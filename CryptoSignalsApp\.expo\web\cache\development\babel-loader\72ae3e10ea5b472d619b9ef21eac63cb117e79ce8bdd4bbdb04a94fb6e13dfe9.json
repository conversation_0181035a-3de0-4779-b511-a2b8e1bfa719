{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nexport var API_CONFIG = {\n  BASE_URL: 'http://localhost:5000/api',\n  TIMEOUT: 10000,\n  RETRY_DELAY: 2000,\n  MAX_RETRIES: 3\n};\nexport var getApiConfig = function getApiConfig() {\n  var isDevelopment = __DEV__;\n  if (isDevelopment) {\n    return _objectSpread(_objectSpread({}, API_CONFIG), {}, {\n      BASE_URL: 'http://localhost:5000/api'\n    });\n  }\n  return _objectSpread(_objectSpread({}, API_CONFIG), {}, {\n    BASE_URL: 'http://localhost:5000/api'\n  });\n};\nexport var ENDPOINTS = {\n  CHANNELS: '/channels',\n  SIGNALS: '/signals',\n  CHANNEL_SIGNALS: function CHANNEL_SIGNALS(channelId) {\n    return `/channels/${channelId}/signals`;\n  },\n  OVERVIEW: '/overview',\n  HEALTH: '/health',\n  PAYMENTS: '/payments',\n  PAYMENT_STATUS: function PAYMENT_STATUS(paymentId) {\n    return `/payments/${paymentId}/status`;\n  },\n  SUBSCRIPTION: '/subscription'\n};\nexport var PAYMENT_CONFIG = {\n  USDT_WALLET: '******************************************',\n  SUPPORTED_NETWORKS: {\n    BSC: {\n      name: 'Binance Smart Chain (BEP20)',\n      chainId: 56,\n      rpcUrl: 'https://bsc-dataseed.binance.org/',\n      explorerUrl: 'https://bscscan.com',\n      usdtContract: '******************************************'\n    },\n    ETH: {\n      name: 'Ethereum (ERC20)',\n      chainId: 1,\n      rpcUrl: 'https://mainnet.infura.io/v3/YOUR_INFURA_KEY',\n      explorerUrl: 'https://etherscan.io',\n      usdtContract: '******************************************'\n    }\n  },\n  PLAN_PRICES: {\n    pro: 29,\n    elite: 79\n  },\n  PAYMENT_TIMEOUT: 30\n};", "map": {"version": 3, "names": ["API_CONFIG", "BASE_URL", "TIMEOUT", "RETRY_DELAY", "MAX_RETRIES", "getApiConfig", "isDevelopment", "__DEV__", "_objectSpread", "ENDPOINTS", "CHANNELS", "SIGNALS", "CHANNEL_SIGNALS", "channelId", "OVERVIEW", "HEALTH", "PAYMENTS", "PAYMENT_STATUS", "paymentId", "SUBSCRIPTION", "PAYMENT_CONFIG", "USDT_WALLET", "SUPPORTED_NETWORKS", "BSC", "name", "chainId", "rpcUrl", "explorerUrl", "usdtContract", "ETH", "PLAN_PRICES", "pro", "elite", "PAYMENT_TIMEOUT"], "sources": ["C:/Users/<USER>/Desktop/Signals/CryptoSignalsApp/src/config/api.js"], "sourcesContent": ["/**\n * Configurações da API\n */\n\n// URL base da API do dashboard backend\nexport const API_CONFIG = {\n  // Para desenvolvimento local\n  BASE_URL: 'http://localhost:5000/api',\n\n  // Para produção (substitua pelo IP/domínio do seu servidor)\n  // BASE_URL: 'http://SEU_IP:5000/api',\n\n  // Timeout para requisições (em ms)\n  TIMEOUT: 10000,\n\n  // Intervalo para retry em caso de erro (em ms)\n  RETRY_DELAY: 2000,\n\n  // Número máximo de tentativas\n  MAX_RETRIES: 3\n};\n\n// Configurações específicas por ambiente\nexport const getApiConfig = () => {\n  // Detectar se está em desenvolvimento ou produção\n  const isDevelopment = __DEV__;\n\n  if (isDevelopment) {\n    return {\n      ...API_CONFIG,\n      BASE_URL: 'http://localhost:5000/api'\n    };\n  }\n\n  // Em produção, você pode configurar diferentes URLs\n  return {\n    ...API_CONFIG,\n    BASE_URL: 'http://localhost:5000/api' // Altere para seu servidor de produção\n  };\n};\n\n// Endpoints disponíveis\nexport const ENDPOINTS = {\n  CHANNELS: '/channels',\n  SIGNALS: '/signals',\n  CHANNEL_SIGNALS: (channelId) => `/channels/${channelId}/signals`,\n  OVERVIEW: '/overview',\n  HEALTH: '/health',\n  PAYMENTS: '/payments',\n  PAYMENT_STATUS: (paymentId) => `/payments/${paymentId}/status`,\n  SUBSCRIPTION: '/subscription'\n};\n\n// Configurações de pagamento\nexport const PAYMENT_CONFIG = {\n  // Wallet para receber pagamentos USDT\n  USDT_WALLET: '******************************************',\n\n  // Redes suportadas\n  SUPPORTED_NETWORKS: {\n    BSC: {\n      name: 'Binance Smart Chain (BEP20)',\n      chainId: 56,\n      rpcUrl: 'https://bsc-dataseed.binance.org/',\n      explorerUrl: 'https://bscscan.com',\n      usdtContract: '******************************************'\n    },\n    ETH: {\n      name: 'Ethereum (ERC20)',\n      chainId: 1,\n      rpcUrl: 'https://mainnet.infura.io/v3/YOUR_INFURA_KEY',\n      explorerUrl: 'https://etherscan.io',\n      usdtContract: '******************************************'\n    }\n  },\n\n  // Preços dos planos em USDT\n  PLAN_PRICES: {\n    pro: 29,\n    elite: 79\n  },\n\n  // Timeout para confirmação de pagamento (em minutos)\n  PAYMENT_TIMEOUT: 30\n};\n"], "mappings": ";;;AAKA,OAAO,IAAMA,UAAU,GAAG;EAExBC,QAAQ,EAAE,2BAA2B;EAMrCC,OAAO,EAAE,KAAK;EAGdC,WAAW,EAAE,IAAI;EAGjBC,WAAW,EAAE;AACf,CAAC;AAGD,OAAO,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;EAEhC,IAAMC,aAAa,GAAGC,OAAO;EAE7B,IAAID,aAAa,EAAE;IACjB,OAAAE,aAAA,CAAAA,aAAA,KACKR,UAAU;MACbC,QAAQ,EAAE;IAA2B;EAEzC;EAGA,OAAAO,aAAA,CAAAA,aAAA,KACKR,UAAU;IACbC,QAAQ,EAAE;EAA2B;AAEzC,CAAC;AAGD,OAAO,IAAMQ,SAAS,GAAG;EACvBC,QAAQ,EAAE,WAAW;EACrBC,OAAO,EAAE,UAAU;EACnBC,eAAe,EAAE,SAAjBA,eAAeA,CAAGC,SAAS;IAAA,OAAK,aAAaA,SAAS,UAAU;EAAA;EAChEC,QAAQ,EAAE,WAAW;EACrBC,MAAM,EAAE,SAAS;EACjBC,QAAQ,EAAE,WAAW;EACrBC,cAAc,EAAE,SAAhBA,cAAcA,CAAGC,SAAS;IAAA,OAAK,aAAaA,SAAS,SAAS;EAAA;EAC9DC,YAAY,EAAE;AAChB,CAAC;AAGD,OAAO,IAAMC,cAAc,GAAG;EAE5BC,WAAW,EAAE,4CAA4C;EAGzDC,kBAAkB,EAAE;IAClBC,GAAG,EAAE;MACHC,IAAI,EAAE,6BAA6B;MACnCC,OAAO,EAAE,EAAE;MACXC,MAAM,EAAE,mCAAmC;MAC3CC,WAAW,EAAE,qBAAqB;MAClCC,YAAY,EAAE;IAChB,CAAC;IACDC,GAAG,EAAE;MACHL,IAAI,EAAE,kBAAkB;MACxBC,OAAO,EAAE,CAAC;MACVC,MAAM,EAAE,8CAA8C;MACtDC,WAAW,EAAE,sBAAsB;MACnCC,YAAY,EAAE;IAChB;EACF,CAAC;EAGDE,WAAW,EAAE;IACXC,GAAG,EAAE,EAAE;IACPC,KAAK,EAAE;EACT,CAAC;EAGDC,eAAe,EAAE;AACnB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}