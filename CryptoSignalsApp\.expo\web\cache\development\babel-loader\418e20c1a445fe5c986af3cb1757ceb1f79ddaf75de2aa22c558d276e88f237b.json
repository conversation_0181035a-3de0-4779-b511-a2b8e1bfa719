{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"children\", \"theme\", \"style\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nimport * as React from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport { useInternalTheme } from \"../../core/theming\";\nimport Text from \"../Typography/Text\";\nimport Title from \"../Typography/v2/Title\";\nvar DialogTitle = function DialogTitle(_ref) {\n  var children = _ref.children,\n    themeOverrides = _ref.theme,\n    style = _ref.style,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var theme = useInternalTheme(themeOverrides);\n  var isV3 = theme.isV3,\n    colors = theme.colors,\n    fonts = theme.fonts;\n  var TextComponent = isV3 ? Text : Title;\n  var headerTextStyle = _objectSpread({\n    color: isV3 ? colors.onSurface : colors === null || colors === void 0 ? void 0 : colors.text\n  }, isV3 ? fonts.headlineSmall : {});\n  return React.createElement(TextComponent, _extends({\n    variant: \"headlineSmall\",\n    accessibilityRole: \"header\",\n    style: [styles.text, isV3 && styles.v3Text, headerTextStyle, style]\n  }, rest), children);\n};\nDialogTitle.displayName = 'Dialog.Title';\nvar styles = StyleSheet.create({\n  text: {\n    marginTop: 22,\n    marginBottom: 18,\n    marginHorizontal: 24\n  },\n  v3Text: {\n    marginTop: 16,\n    marginBottom: 16\n  }\n});\nexport default DialogTitle;\nexport { DialogTitle };", "map": {"version": 3, "names": ["React", "StyleSheet", "useInternalTheme", "Text", "Title", "DialogTitle", "_ref", "children", "themeOverrides", "theme", "style", "rest", "_objectWithoutProperties", "_excluded", "isV3", "colors", "fonts", "TextComponent", "headerTextStyle", "_objectSpread", "color", "onSurface", "text", "headlineSmall", "createElement", "_extends", "variant", "accessibilityRole", "styles", "v3Text", "displayName", "create", "marginTop", "marginBottom", "marginHorizontal"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\Dialog\\DialogTitle.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { StyleProp, StyleSheet, TextStyle } from 'react-native';\n\nimport { useInternalTheme } from '../../core/theming';\nimport type { ThemeProp } from '../../types';\nimport Text from '../Typography/Text';\nimport Title from '../Typography/v2/Title';\n\nexport type Props = React.ComponentPropsWithRef<typeof Title> & {\n  /**\n   * Title text for the `DialogTitle`.\n   */\n  children: React.ReactNode;\n  style?: StyleProp<TextStyle>;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n};\n\n/**\n * A component to show a title in a Dialog.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { Dialog, Portal, Text } from 'react-native-paper';\n *\n * const MyComponent = () => {\n *   const [visible, setVisible] = React.useState(false);\n *\n *   const hideDialog = () => setVisible(false);\n *\n *   return (\n *     <Portal>\n *       <Dialog visible={visible} onDismiss={hideDialog}>\n *         <Dialog.Title>This is a title</Dialog.Title>\n *         <Dialog.Content>\n *           <Text variant=\"bodyMedium\">This is simple dialog</Text>\n *         </Dialog.Content>\n *       </Dialog>\n *     </Portal>\n *   );\n * };\n *\n * export default MyComponent;\n * ```\n */\nconst DialogTitle = ({\n  children,\n  theme: themeOverrides,\n  style,\n  ...rest\n}: Props) => {\n  const theme = useInternalTheme(themeOverrides);\n  const { isV3, colors, fonts } = theme;\n\n  const TextComponent = isV3 ? Text : Title;\n\n  const headerTextStyle = {\n    color: isV3 ? colors.onSurface : colors?.text,\n    ...(isV3 ? fonts.headlineSmall : {}),\n  };\n\n  return (\n    <TextComponent\n      variant=\"headlineSmall\"\n      accessibilityRole=\"header\"\n      style={[styles.text, isV3 && styles.v3Text, headerTextStyle, style]}\n      {...rest}\n    >\n      {children}\n    </TextComponent>\n  );\n};\n\nDialogTitle.displayName = 'Dialog.Title';\n\nconst styles = StyleSheet.create({\n  text: {\n    marginTop: 22,\n    marginBottom: 18,\n    marginHorizontal: 24,\n  },\n  v3Text: {\n    marginTop: 16,\n    marginBottom: 16,\n  },\n});\n\nexport default DialogTitle;\n\n// @component-docs ignore-next-line\nexport { DialogTitle };\n"], "mappings": ";;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,UAAA;AAG9B,SAASC,gBAAgB;AAEzB,OAAOC,IAAI;AACX,OAAOC,KAAK;AA0CZ,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAAC,IAAA,EAKJ;EAAA,IAJXC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;IACDC,cAAc,GAAAF,IAAA,CAArBG,KAAK;IACLC,KAAK,GAAAJ,IAAA,CAALI,KAAK;IACFC,IAAA,GAAAC,wBAAA,CAAAN,IAAA,EAAAO,SAAA;EAEH,IAAMJ,KAAK,GAAGP,gBAAgB,CAACM,cAAc,CAAC;EAC9C,IAAQM,IAAI,GAAoBL,KAAK,CAA7BK,IAAI;IAAEC,MAAM,GAAYN,KAAK,CAAvBM,MAAM;IAAEC,KAAA,GAAUP,KAAK,CAAfO,KAAA;EAEtB,IAAMC,aAAa,GAAGH,IAAI,GAAGX,IAAI,GAAGC,KAAK;EAEzC,IAAMc,eAAe,GAAAC,aAAA;IACnBC,KAAK,EAAEN,IAAI,GAAGC,MAAM,CAACM,SAAS,GAAGN,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEO;EAAI,GACzCR,IAAI,GAAGE,KAAK,CAACO,aAAa,GAAG,CAAC,CAAC,CACpC;EAED,OACEvB,KAAA,CAAAwB,aAAA,CAACP,aAAa,EAAAQ,QAAA;IACZC,OAAO,EAAC,eAAe;IACvBC,iBAAiB,EAAC,QAAQ;IAC1BjB,KAAK,EAAE,CAACkB,MAAM,CAACN,IAAI,EAAER,IAAI,IAAIc,MAAM,CAACC,MAAM,EAAEX,eAAe,EAAER,KAAK;EAAE,GAChEC,IAAI,GAEPJ,QACY,CAAC;AAEpB,CAAC;AAEDF,WAAW,CAACyB,WAAW,GAAG,cAAc;AAExC,IAAMF,MAAM,GAAG3B,UAAU,CAAC8B,MAAM,CAAC;EAC/BT,IAAI,EAAE;IACJU,SAAS,EAAE,EAAE;IACbC,YAAY,EAAE,EAAE;IAChBC,gBAAgB,EAAE;EACpB,CAAC;EACDL,MAAM,EAAE;IACNG,SAAS,EAAE,EAAE;IACbC,YAAY,EAAE;EAChB;AACF,CAAC,CAAC;AAEF,eAAe5B,WAAW;AAG1B,SAASA,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}