{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/toConsumableArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"children\", \"dark\", \"style\", \"mode\", \"elevated\", \"safeAreaInsets\", \"theme\"],\n  _excluded2 = [\"backgroundColor\", \"elevation\"];\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nimport * as React from 'react';\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport color from 'color';\nimport AppbarContent from \"./AppbarContent\";\nimport { DEFAULT_APPBAR_HEIGHT, getAppbarBackgroundColor, modeAppbarHeight, renderAppbarContent, filterAppbarActions } from \"./utils\";\nimport { useInternalTheme } from \"../../core/theming\";\nimport Surface from \"../Surface\";\nvar Appbar = function Appbar(_ref) {\n  var children = _ref.children,\n    dark = _ref.dark,\n    style = _ref.style,\n    _ref$mode = _ref.mode,\n    mode = _ref$mode === void 0 ? 'small' : _ref$mode,\n    elevated = _ref.elevated,\n    safeAreaInsets = _ref.safeAreaInsets,\n    themeOverrides = _ref.theme,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var theme = useInternalTheme(themeOverrides);\n  var isV3 = theme.isV3;\n  var flattenedStyle = StyleSheet.flatten(style);\n  var _ref2 = flattenedStyle || {},\n    customBackground = _ref2.backgroundColor,\n    _ref2$elevation = _ref2.elevation,\n    elevation = _ref2$elevation === void 0 ? isV3 ? elevated ? 2 : 0 : 4 : _ref2$elevation,\n    restStyle = _objectWithoutProperties(_ref2, _excluded2);\n  var backgroundColor = getAppbarBackgroundColor(theme, elevation, customBackground, elevated);\n  var isMode = function isMode(modeToCompare) {\n    return isV3 && mode === modeToCompare;\n  };\n  var isDark = false;\n  if (typeof dark === 'boolean') {\n    isDark = dark;\n  } else if (!isV3) {\n    isDark = backgroundColor === 'transparent' ? false : typeof backgroundColor === 'string' ? !color(backgroundColor).isLight() : true;\n  }\n  var isV3CenterAlignedMode = isV3 && isMode('center-aligned');\n  var shouldCenterContent = false;\n  var shouldAddLeftSpacing = false;\n  var shouldAddRightSpacing = false;\n  if (!isV3 && Platform.OS === 'ios' || isV3CenterAlignedMode) {\n    var hasAppbarContent = false;\n    var leftItemsCount = 0;\n    var rightItemsCount = 0;\n    React.Children.forEach(children, function (child) {\n      if (React.isValidElement(child)) {\n        var isLeading = child.props.isLeading === true;\n        if (child.type === AppbarContent) {\n          hasAppbarContent = true;\n        } else if (isLeading || !hasAppbarContent) {\n          leftItemsCount++;\n        } else {\n          rightItemsCount++;\n        }\n      }\n    });\n    shouldCenterContent = hasAppbarContent && leftItemsCount < 2 && rightItemsCount < (isV3 ? 3 : 2);\n    shouldAddLeftSpacing = shouldCenterContent && leftItemsCount === 0;\n    shouldAddRightSpacing = shouldCenterContent && rightItemsCount === 0;\n  }\n  var spacingStyle = isV3 ? styles.v3Spacing : styles.spacing;\n  var insets = {\n    paddingBottom: safeAreaInsets === null || safeAreaInsets === void 0 ? void 0 : safeAreaInsets.bottom,\n    paddingTop: safeAreaInsets === null || safeAreaInsets === void 0 ? void 0 : safeAreaInsets.top,\n    paddingLeft: safeAreaInsets === null || safeAreaInsets === void 0 ? void 0 : safeAreaInsets.left,\n    paddingRight: safeAreaInsets === null || safeAreaInsets === void 0 ? void 0 : safeAreaInsets.right\n  };\n  return React.createElement(Surface, _extends({\n    style: [{\n      backgroundColor: backgroundColor\n    }, styles.appbar, {\n      height: isV3 ? modeAppbarHeight[mode] : DEFAULT_APPBAR_HEIGHT\n    }, insets, restStyle, !theme.isV3 && {\n      elevation: elevation\n    }],\n    elevation: elevation,\n    container: true\n  }, rest), shouldAddLeftSpacing ? React.createElement(View, {\n    style: spacingStyle\n  }) : null, (!isV3 || isMode('small') || isMode('center-aligned')) && React.createElement(React.Fragment, null, renderAppbarContent({\n    children: children,\n    isDark: isDark,\n    theme: theme,\n    isV3: isV3,\n    renderOnly: ['Appbar.BackAction'],\n    shouldCenterContent: isV3CenterAlignedMode || shouldCenterContent\n  }), renderAppbarContent({\n    children: [].concat(_toConsumableArray(filterAppbarActions(children, true)), _toConsumableArray(filterAppbarActions(children))),\n    isDark: isDark,\n    theme: theme,\n    isV3: isV3,\n    renderExcept: ['Appbar.BackAction'],\n    shouldCenterContent: isV3CenterAlignedMode || shouldCenterContent\n  })), (isMode('medium') || isMode('large')) && React.createElement(View, {\n    style: [styles.columnContainer, isMode('center-aligned') && styles.centerAlignedContainer]\n  }, React.createElement(View, {\n    style: styles.controlsRow\n  }, renderAppbarContent({\n    children: children,\n    isDark: isDark,\n    isV3: isV3,\n    renderOnly: ['Appbar.BackAction'],\n    mode: mode\n  }), renderAppbarContent({\n    children: filterAppbarActions(children, true),\n    isDark: isDark,\n    isV3: isV3,\n    renderOnly: ['Appbar.Action'],\n    mode: mode\n  }), React.createElement(View, {\n    style: styles.rightActionControls\n  }, renderAppbarContent({\n    children: filterAppbarActions(children),\n    isDark: isDark,\n    isV3: isV3,\n    renderExcept: ['Appbar', 'Appbar.BackAction', 'Appbar.Content', 'Appbar.Header'],\n    mode: mode\n  }))), renderAppbarContent({\n    children: children,\n    isDark: isDark,\n    isV3: isV3,\n    renderOnly: ['Appbar.Content'],\n    mode: mode\n  })), shouldAddRightSpacing ? React.createElement(View, {\n    style: spacingStyle\n  }) : null);\n};\nvar styles = StyleSheet.create({\n  appbar: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    paddingHorizontal: 4\n  },\n  spacing: {\n    width: 48\n  },\n  v3Spacing: {\n    width: 52\n  },\n  controlsRow: {\n    flex: 1,\n    flexDirection: 'row',\n    alignItems: 'center',\n    justifyContent: 'space-between'\n  },\n  rightActionControls: {\n    flexDirection: 'row',\n    flex: 1,\n    justifyContent: 'flex-end'\n  },\n  columnContainer: {\n    flexDirection: 'column',\n    flex: 1,\n    paddingTop: 8\n  },\n  centerAlignedContainer: {\n    paddingTop: 0\n  }\n});\nexport default Appbar;\nexport { Appbar };", "map": {"version": 3, "names": ["React", "Platform", "StyleSheet", "View", "color", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DEFAULT_APPBAR_HEIGHT", "getAppbarBackgroundColor", "modeAppbarHeight", "renderAppbarContent", "filterAppbarActions", "useInternalTheme", "Surface", "Appbar", "_ref", "children", "dark", "style", "_ref$mode", "mode", "elevated", "safeAreaInsets", "themeOverrides", "theme", "rest", "_objectWithoutProperties", "_excluded", "isV3", "flattenedStyle", "flatten", "_ref2", "customBackground", "backgroundColor", "_ref2$elevation", "elevation", "restStyle", "_excluded2", "isMode", "modeToCompare", "isDark", "isLight", "isV3CenterAlignedMode", "shouldCenterC<PERSON>nt", "shouldAddLeftSpacing", "shouldAddRightSpacing", "OS", "has<PERSON><PERSON>bar<PERSON><PERSON>nt", "leftItemsCount", "rightItemsCount", "Children", "for<PERSON>ach", "child", "isValidElement", "isLeading", "props", "type", "spacingStyle", "styles", "v3Spacing", "spacing", "insets", "paddingBottom", "bottom", "paddingTop", "top", "paddingLeft", "left", "paddingRight", "right", "createElement", "_extends", "appbar", "height", "container", "Fragment", "renderOnly", "concat", "_toConsumableArray", "renderExcept", "columnContainer", "centerAlignedContainer", "controlsRow", "rightActionControls", "create", "flexDirection", "alignItems", "paddingHorizontal", "width", "flex", "justifyContent"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\Appbar\\Appbar.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  Animated,\n  Platform,\n  StyleProp,\n  StyleSheet,\n  View,\n  ViewStyle,\n  ColorValue,\n} from 'react-native';\n\nimport color from 'color';\n\nimport AppbarContent from './AppbarContent';\nimport {\n  AppbarModes,\n  DEFAULT_APPBAR_HEIGHT,\n  getAppbarBackgroundColor,\n  modeAppbarHeight,\n  renderAppbarContent,\n  filterAppbarActions,\n  AppbarChildProps,\n} from './utils';\nimport { useInternalTheme } from '../../core/theming';\nimport type { MD3Elevation, ThemeProp } from '../../types';\nimport Surface from '../Surface';\n\nexport type Props = Omit<\n  Partial<React.ComponentPropsWithRef<typeof View>>,\n  'style'\n> & {\n  /**\n   * Whether the background color is a dark color. A dark appbar will render light text and vice-versa.\n   */\n  dark?: boolean;\n  /**\n   * Content of the `Appbar`.\n   */\n  children: React.ReactNode;\n  /**\n   * @supported Available in v5.x with theme version 3\n   *\n   * Mode of the Appbar.\n   * - `small` - Appbar with default height (64).\n   * - `medium` - Appbar with medium height (112).\n   * - `large` - Appbar with large height (152).\n   * - `center-aligned` - Appbar with default height and center-aligned title.\n   */\n  mode?: 'small' | 'medium' | 'large' | 'center-aligned';\n  /**\n   * @supported Available in v5.x with theme version 3\n   * Whether Appbar background should have the elevation along with primary color pigment.\n   */\n  elevated?: boolean;\n  /**\n   * Safe area insets for the Appbar. This can be used to avoid elements like the navigation bar on Android and bottom safe area on iOS.\n   */\n  safeAreaInsets?: {\n    bottom?: number;\n    top?: number;\n    left?: number;\n    right?: number;\n  };\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n  style?: Animated.WithAnimatedValue<StyleProp<ViewStyle>>;\n};\n\n/**\n * A component to display action items in a bar. It can be placed at the top or bottom.\n * The top bar usually contains the screen title, controls such as navigation buttons, menu button etc.\n * The bottom bar usually provides access to a drawer and up to four actions.\n *\n * By default Appbar uses primary color as a background, in dark theme with `adaptive` mode it will use surface colour instead.\n * See [Dark Theme](https://callstack.github.io/react-native-paper/docs/guides/theming#dark-theme) for more informations\n *\n * ## Usage\n * ### Top bar\n * ```js\n * import * as React from 'react';\n * import { Appbar } from 'react-native-paper';\n *\n * const MyComponent = () => (\n *   <Appbar.Header>\n *     <Appbar.BackAction onPress={() => {}} />\n *     <Appbar.Content title=\"Title\" />\n *     <Appbar.Action icon=\"calendar\" onPress={() => {}} />\n *     <Appbar.Action icon=\"magnify\" onPress={() => {}} />\n *   </Appbar.Header>\n * );\n *\n * export default MyComponent;\n * ```\n *\n * ### Bottom bar\n * ```js\n * import * as React from 'react';\n * import { StyleSheet } from 'react-native';\n * import { Appbar, FAB, useTheme } from 'react-native-paper';\n * import { useSafeAreaInsets } from 'react-native-safe-area-context';\n *\n * const BOTTOM_APPBAR_HEIGHT = 80;\n * const MEDIUM_FAB_HEIGHT = 56;\n *\n * const MyComponent = () => {\n *   const { bottom } = useSafeAreaInsets();\n *   const theme = useTheme();\n *\n *   return (\n *     <Appbar\n *       style={[\n *         styles.bottom,\n *         {\n *           height: BOTTOM_APPBAR_HEIGHT + bottom,\n *           backgroundColor: theme.colors.elevation.level2,\n *         },\n *       ]}\n *       safeAreaInsets={{ bottom }}\n *     >\n *       <Appbar.Action icon=\"archive\" onPress={() => {}} />\n *       <Appbar.Action icon=\"email\" onPress={() => {}} />\n *       <Appbar.Action icon=\"label\" onPress={() => {}} />\n *       <Appbar.Action icon=\"delete\" onPress={() => {}} />\n *       <FAB\n *         mode=\"flat\"\n *         size=\"medium\"\n *         icon=\"plus\"\n *         onPress={() => {}}\n *         style={[\n *           styles.fab,\n *           { top: (BOTTOM_APPBAR_HEIGHT - MEDIUM_FAB_HEIGHT) / 2 },\n *         ]}\n *       />\n *     </Appbar>\n *   );\n * };\n *\n * const styles = StyleSheet.create({\n *   bottom: {\n *     backgroundColor: 'aquamarine',\n *     position: 'absolute',\n *     left: 0,\n *     right: 0,\n *     bottom: 0,\n *   },\n *   fab: {\n *     position: 'absolute',\n *     right: 16,\n *   },\n * });\n *\n * export default MyComponent;\n * ```\n */\nconst Appbar = ({\n  children,\n  dark,\n  style,\n  mode = 'small',\n  elevated,\n  safeAreaInsets,\n  theme: themeOverrides,\n  ...rest\n}: Props) => {\n  const theme = useInternalTheme(themeOverrides);\n  const { isV3 } = theme;\n  const flattenedStyle = StyleSheet.flatten(style);\n  const {\n    backgroundColor: customBackground,\n    elevation = isV3 ? (elevated ? 2 : 0) : 4,\n    ...restStyle\n  } = (flattenedStyle || {}) as Exclude<typeof flattenedStyle, number> & {\n    elevation?: number;\n    backgroundColor?: ColorValue;\n  };\n\n  const backgroundColor = getAppbarBackgroundColor(\n    theme,\n    elevation,\n    customBackground,\n    elevated\n  );\n\n  const isMode = (modeToCompare: AppbarModes) => {\n    return isV3 && mode === modeToCompare;\n  };\n\n  let isDark = false;\n\n  if (typeof dark === 'boolean') {\n    isDark = dark;\n  } else if (!isV3) {\n    isDark =\n      backgroundColor === 'transparent'\n        ? false\n        : typeof backgroundColor === 'string'\n        ? !color(backgroundColor).isLight()\n        : true;\n  }\n\n  const isV3CenterAlignedMode = isV3 && isMode('center-aligned');\n\n  let shouldCenterContent = false;\n  let shouldAddLeftSpacing = false;\n  let shouldAddRightSpacing = false;\n  if ((!isV3 && Platform.OS === 'ios') || isV3CenterAlignedMode) {\n    let hasAppbarContent = false;\n    let leftItemsCount = 0;\n    let rightItemsCount = 0;\n\n    React.Children.forEach(children, (child) => {\n      if (React.isValidElement<AppbarChildProps>(child)) {\n        const isLeading = child.props.isLeading === true;\n\n        if (child.type === AppbarContent) {\n          hasAppbarContent = true;\n        } else if (isLeading || !hasAppbarContent) {\n          leftItemsCount++;\n        } else {\n          rightItemsCount++;\n        }\n      }\n    });\n\n    shouldCenterContent =\n      hasAppbarContent &&\n      leftItemsCount < 2 &&\n      rightItemsCount < (isV3 ? 3 : 2);\n    shouldAddLeftSpacing = shouldCenterContent && leftItemsCount === 0;\n    shouldAddRightSpacing = shouldCenterContent && rightItemsCount === 0;\n  }\n\n  const spacingStyle = isV3 ? styles.v3Spacing : styles.spacing;\n\n  const insets = {\n    paddingBottom: safeAreaInsets?.bottom,\n    paddingTop: safeAreaInsets?.top,\n    paddingLeft: safeAreaInsets?.left,\n    paddingRight: safeAreaInsets?.right,\n  };\n\n  return (\n    <Surface\n      style={[\n        { backgroundColor },\n        styles.appbar,\n        {\n          height: isV3 ? modeAppbarHeight[mode] : DEFAULT_APPBAR_HEIGHT,\n        },\n        insets,\n        restStyle,\n        !theme.isV3 && { elevation },\n      ]}\n      elevation={elevation as MD3Elevation}\n      container\n      {...rest}\n    >\n      {shouldAddLeftSpacing ? <View style={spacingStyle} /> : null}\n      {(!isV3 || isMode('small') || isMode('center-aligned')) && (\n        <>\n          {/* Render only the back action at first place  */}\n          {renderAppbarContent({\n            children,\n            isDark,\n            theme,\n            isV3,\n            renderOnly: ['Appbar.BackAction'],\n            shouldCenterContent: isV3CenterAlignedMode || shouldCenterContent,\n          })}\n          {/* Render the rest of the content except the back action */}\n          {renderAppbarContent({\n            // Filter appbar actions - first leading icons, then trailing icons\n            children: [\n              ...filterAppbarActions(children, true),\n              ...filterAppbarActions(children),\n            ],\n            isDark,\n            theme,\n            isV3,\n            renderExcept: ['Appbar.BackAction'],\n            shouldCenterContent: isV3CenterAlignedMode || shouldCenterContent,\n          })}\n        </>\n      )}\n      {(isMode('medium') || isMode('large')) && (\n        <View\n          style={[\n            styles.columnContainer,\n            isMode('center-aligned') && styles.centerAlignedContainer,\n          ]}\n        >\n          {/* Appbar top row with controls */}\n          <View style={styles.controlsRow}>\n            {/* Left side of row container, can contain AppbarBackAction or AppbarAction if it's leading icon  */}\n            {renderAppbarContent({\n              children,\n              isDark,\n              isV3,\n              renderOnly: ['Appbar.BackAction'],\n              mode,\n            })}\n            {renderAppbarContent({\n              children: filterAppbarActions(children, true),\n              isDark,\n              isV3,\n              renderOnly: ['Appbar.Action'],\n              mode,\n            })}\n            {/* Right side of row container, can contain other AppbarAction if they are not leading icons */}\n            <View style={styles.rightActionControls}>\n              {renderAppbarContent({\n                children: filterAppbarActions(children),\n                isDark,\n                isV3,\n                renderExcept: [\n                  'Appbar',\n                  'Appbar.BackAction',\n                  'Appbar.Content',\n                  'Appbar.Header',\n                ],\n                mode,\n              })}\n            </View>\n          </View>\n          {renderAppbarContent({\n            children,\n            isDark,\n            isV3,\n            renderOnly: ['Appbar.Content'],\n            mode,\n          })}\n        </View>\n      )}\n      {shouldAddRightSpacing ? <View style={spacingStyle} /> : null}\n    </Surface>\n  );\n};\n\nconst styles = StyleSheet.create({\n  appbar: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    paddingHorizontal: 4,\n  },\n  spacing: {\n    width: 48,\n  },\n  v3Spacing: {\n    width: 52,\n  },\n  controlsRow: {\n    flex: 1,\n    flexDirection: 'row',\n    alignItems: 'center',\n    justifyContent: 'space-between',\n  },\n  rightActionControls: {\n    flexDirection: 'row',\n    flex: 1,\n    justifyContent: 'flex-end',\n  },\n  columnContainer: {\n    flexDirection: 'column',\n    flex: 1,\n    paddingTop: 8,\n  },\n  centerAlignedContainer: {\n    paddingTop: 0,\n  },\n});\n\nexport default Appbar;\n\n// @component-docs ignore-next-line\nexport { Appbar };\n"], "mappings": ";;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAW9B,OAAOC,KAAK,MAAM,OAAO;AAEzB,OAAOC,aAAa;AACpB,SAEEC,qBAAqB,EACrBC,wBAAwB,EACxBC,gBAAgB,EAChBC,mBAAmB,EACnBC,mBAAmB;AAGrB,SAASC,gBAAgB;AAEzB,OAAOC,OAAO;AAmId,IAAMC,MAAM,GAAG,SAATA,MAAMA,CAAAC,IAAA,EASC;EAAA,IARXC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;IACRC,IAAI,GAAAF,IAAA,CAAJE,IAAI;IACJC,KAAK,GAAAH,IAAA,CAALG,KAAK;IAAAC,SAAA,GAAAJ,IAAA,CACLK,IAAI;IAAJA,IAAI,GAAAD,SAAA,cAAG,OAAO,GAAAA,SAAA;IACdE,QAAQ,GAAAN,IAAA,CAARM,QAAQ;IACRC,cAAc,GAAAP,IAAA,CAAdO,cAAc;IACPC,cAAc,GAAAR,IAAA,CAArBS,KAAK;IACFC,IAAA,GAAAC,wBAAA,CAAAX,IAAA,EAAAY,SAAA;EAEH,IAAMH,KAAK,GAAGZ,gBAAgB,CAACW,cAAc,CAAC;EAC9C,IAAQK,IAAA,GAASJ,KAAK,CAAdI,IAAA;EACR,IAAMC,cAAc,GAAG1B,UAAU,CAAC2B,OAAO,CAACZ,KAAK,CAAC;EAChD,IAAAa,KAAA,GAIKF,cAAc,IAAI,CAAC,CAGvB;IANkBG,gBAAgB,GAAAD,KAAA,CAAjCE,eAAe;IAAAC,eAAA,GAAAH,KAAA,CACfI,SAAS;IAATA,SAAS,GAAAD,eAAA,cAAGN,IAAI,GAAIP,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAI,CAAC,GAAAa,eAAA;IACtCE,SAAA,GAAAV,wBAAA,CAAAK,KAAA,EAAAM,UAAA;EAML,IAAMJ,eAAe,GAAGzB,wBAAwB,CAC9CgB,KAAK,EACLW,SAAS,EACTH,gBAAgB,EAChBX,QACF,CAAC;EAED,IAAMiB,MAAM,GAAI,SAAVA,MAAMA,CAAIC,aAA0B,EAAK;IAC7C,OAAOX,IAAI,IAAIR,IAAI,KAAKmB,aAAa;EACvC,CAAC;EAED,IAAIC,MAAM,GAAG,KAAK;EAElB,IAAI,OAAOvB,IAAI,KAAK,SAAS,EAAE;IAC7BuB,MAAM,GAAGvB,IAAI;EACf,CAAC,MAAM,IAAI,CAACW,IAAI,EAAE;IAChBY,MAAM,GACJP,eAAe,KAAK,aAAa,GAC7B,KAAK,GACL,OAAOA,eAAe,KAAK,QAAQ,GACnC,CAAC5B,KAAK,CAAC4B,eAAe,CAAC,CAACQ,OAAO,CAAC,CAAC,GACjC,IAAI;EACZ;EAEA,IAAMC,qBAAqB,GAAGd,IAAI,IAAIU,MAAM,CAAC,gBAAgB,CAAC;EAE9D,IAAIK,mBAAmB,GAAG,KAAK;EAC/B,IAAIC,oBAAoB,GAAG,KAAK;EAChC,IAAIC,qBAAqB,GAAG,KAAK;EACjC,IAAK,CAACjB,IAAI,IAAI1B,QAAQ,CAAC4C,EAAE,KAAK,KAAK,IAAKJ,qBAAqB,EAAE;IAC7D,IAAIK,gBAAgB,GAAG,KAAK;IAC5B,IAAIC,cAAc,GAAG,CAAC;IACtB,IAAIC,eAAe,GAAG,CAAC;IAEvBhD,KAAK,CAACiD,QAAQ,CAACC,OAAO,CAACnC,QAAQ,EAAG,UAAAoC,KAAK,EAAK;MAC1C,IAAInD,KAAK,CAACoD,cAAc,CAAmBD,KAAK,CAAC,EAAE;QACjD,IAAME,SAAS,GAAGF,KAAK,CAACG,KAAK,CAACD,SAAS,KAAK,IAAI;QAEhD,IAAIF,KAAK,CAACI,IAAI,KAAKlD,aAAa,EAAE;UAChCyC,gBAAgB,GAAG,IAAI;QACzB,CAAC,MAAM,IAAIO,SAAS,IAAI,CAACP,gBAAgB,EAAE;UACzCC,cAAc,EAAE;QAClB,CAAC,MAAM;UACLC,eAAe,EAAE;QACnB;MACF;IACF,CAAC,CAAC;IAEFN,mBAAmB,GACjBI,gBAAgB,IAChBC,cAAc,GAAG,CAAC,IAClBC,eAAe,IAAIrB,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;IAClCgB,oBAAoB,GAAGD,mBAAmB,IAAIK,cAAc,KAAK,CAAC;IAClEH,qBAAqB,GAAGF,mBAAmB,IAAIM,eAAe,KAAK,CAAC;EACtE;EAEA,IAAMQ,YAAY,GAAG7B,IAAI,GAAG8B,MAAM,CAACC,SAAS,GAAGD,MAAM,CAACE,OAAO;EAE7D,IAAMC,MAAM,GAAG;IACbC,aAAa,EAAExC,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEyC,MAAM;IACrCC,UAAU,EAAE1C,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE2C,GAAG;IAC/BC,WAAW,EAAE5C,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE6C,IAAI;IACjCC,YAAY,EAAE9C,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE+C;EAChC,CAAC;EAED,OACEpE,KAAA,CAAAqE,aAAA,CAACzD,OAAO,EAAA0D,QAAA;IACNrD,KAAK,EAAE,CACL;MAAEe,eAAA,EAAAA;IAAgB,CAAC,EACnByB,MAAM,CAACc,MAAM,EACb;MACEC,MAAM,EAAE7C,IAAI,GAAGnB,gBAAgB,CAACW,IAAI,CAAC,GAAGb;IAC1C,CAAC,EACDsD,MAAM,EACNzB,SAAS,EACT,CAACZ,KAAK,CAACI,IAAI,IAAI;MAAEO,SAAA,EAAAA;IAAU,CAAC,CAC5B;IACFA,SAAS,EAAEA,SAA0B;IACrCuC,SAAS;EAAA,GACLjD,IAAI,GAEPmB,oBAAoB,GAAG3C,KAAA,CAAAqE,aAAA,CAAClE,IAAI;IAACc,KAAK,EAAEuC;EAAa,CAAE,CAAC,GAAG,IAAI,EAC3D,CAAC,CAAC7B,IAAI,IAAIU,MAAM,CAAC,OAAO,CAAC,IAAIA,MAAM,CAAC,gBAAgB,CAAC,KACpDrC,KAAA,CAAAqE,aAAA,CAAArE,KAAA,CAAA0E,QAAA,QAEGjE,mBAAmB,CAAC;IACnBM,QAAQ,EAARA,QAAQ;IACRwB,MAAM,EAANA,MAAM;IACNhB,KAAK,EAALA,KAAK;IACLI,IAAI,EAAJA,IAAI;IACJgD,UAAU,EAAE,CAAC,mBAAmB,CAAC;IACjCjC,mBAAmB,EAAED,qBAAqB,IAAIC;EAChD,CAAC,CAAC,EAEDjC,mBAAmB,CAAC;IAEnBM,QAAQ,KAAA6D,MAAA,CAAAC,kBAAA,CACHnE,mBAAmB,CAACK,QAAQ,EAAE,IAAI,CAAC,GAAA8D,kBAAA,CACnCnE,mBAAmB,CAACK,QAAQ,CAAC,EACjC;IACDwB,MAAM,EAANA,MAAM;IACNhB,KAAK,EAALA,KAAK;IACLI,IAAI,EAAJA,IAAI;IACJmD,YAAY,EAAE,CAAC,mBAAmB,CAAC;IACnCpC,mBAAmB,EAAED,qBAAqB,IAAIC;EAChD,CAAC,CACD,CACH,EACA,CAACL,MAAM,CAAC,QAAQ,CAAC,IAAIA,MAAM,CAAC,OAAO,CAAC,KACnCrC,KAAA,CAAAqE,aAAA,CAAClE,IAAI;IACHc,KAAK,EAAE,CACLwC,MAAM,CAACsB,eAAe,EACtB1C,MAAM,CAAC,gBAAgB,CAAC,IAAIoB,MAAM,CAACuB,sBAAsB;EACzD,GAGFhF,KAAA,CAAAqE,aAAA,CAAClE,IAAI;IAACc,KAAK,EAAEwC,MAAM,CAACwB;EAAY,GAE7BxE,mBAAmB,CAAC;IACnBM,QAAQ,EAARA,QAAQ;IACRwB,MAAM,EAANA,MAAM;IACNZ,IAAI,EAAJA,IAAI;IACJgD,UAAU,EAAE,CAAC,mBAAmB,CAAC;IACjCxD,IAAA,EAAAA;EACF,CAAC,CAAC,EACDV,mBAAmB,CAAC;IACnBM,QAAQ,EAAEL,mBAAmB,CAACK,QAAQ,EAAE,IAAI,CAAC;IAC7CwB,MAAM,EAANA,MAAM;IACNZ,IAAI,EAAJA,IAAI;IACJgD,UAAU,EAAE,CAAC,eAAe,CAAC;IAC7BxD,IAAA,EAAAA;EACF,CAAC,CAAC,EAEFnB,KAAA,CAAAqE,aAAA,CAAClE,IAAI;IAACc,KAAK,EAAEwC,MAAM,CAACyB;EAAoB,GACrCzE,mBAAmB,CAAC;IACnBM,QAAQ,EAAEL,mBAAmB,CAACK,QAAQ,CAAC;IACvCwB,MAAM,EAANA,MAAM;IACNZ,IAAI,EAAJA,IAAI;IACJmD,YAAY,EAAE,CACZ,QAAQ,EACR,mBAAmB,EACnB,gBAAgB,EAChB,eAAe,CAChB;IACD3D,IAAA,EAAAA;EACF,CAAC,CACG,CACF,CAAC,EACNV,mBAAmB,CAAC;IACnBM,QAAQ,EAARA,QAAQ;IACRwB,MAAM,EAANA,MAAM;IACNZ,IAAI,EAAJA,IAAI;IACJgD,UAAU,EAAE,CAAC,gBAAgB,CAAC;IAC9BxD,IAAA,EAAAA;EACF,CAAC,CACG,CACP,EACAyB,qBAAqB,GAAG5C,KAAA,CAAAqE,aAAA,CAAClE,IAAI;IAACc,KAAK,EAAEuC;EAAa,CAAE,CAAC,GAAG,IAClD,CAAC;AAEd,CAAC;AAED,IAAMC,MAAM,GAAGvD,UAAU,CAACiF,MAAM,CAAC;EAC/BZ,MAAM,EAAE;IACNa,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,QAAQ;IACpBC,iBAAiB,EAAE;EACrB,CAAC;EACD3B,OAAO,EAAE;IACP4B,KAAK,EAAE;EACT,CAAC;EACD7B,SAAS,EAAE;IACT6B,KAAK,EAAE;EACT,CAAC;EACDN,WAAW,EAAE;IACXO,IAAI,EAAE,CAAC;IACPJ,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,QAAQ;IACpBI,cAAc,EAAE;EAClB,CAAC;EACDP,mBAAmB,EAAE;IACnBE,aAAa,EAAE,KAAK;IACpBI,IAAI,EAAE,CAAC;IACPC,cAAc,EAAE;EAClB,CAAC;EACDV,eAAe,EAAE;IACfK,aAAa,EAAE,QAAQ;IACvBI,IAAI,EAAE,CAAC;IACPzB,UAAU,EAAE;EACd,CAAC;EACDiB,sBAAsB,EAAE;IACtBjB,UAAU,EAAE;EACd;AACF,CAAC,CAAC;AAEF,eAAelD,MAAM;AAGrB,SAASA,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}