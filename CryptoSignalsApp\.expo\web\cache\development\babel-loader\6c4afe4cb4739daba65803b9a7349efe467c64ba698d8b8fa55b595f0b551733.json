{"ast": null, "code": "import StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nvar styles = StyleSheet.create({\n  container: {\n    alignItems: 'center',\n    flexDirection: 'row',\n    position: 'relative',\n    marginBottom: 4\n  },\n  btn: {\n    color: '#fff',\n    fontSize: 13,\n    borderStyle: 'solid',\n    borderColor: '#0D0D0D',\n    borderWidth: 1,\n    borderRadius: 4,\n    paddingVertical: 6,\n    paddingHorizontal: 8,\n    marginHorizontal: 4,\n    flexGrow: 1,\n    textAlign: 'center',\n    fontFamily: 'Poppins_500Medium'\n  },\n  btnChecked: {\n    backgroundColor: '#0D0D0D',\n    color: '#FECB37',\n    fontWeight: '500'\n  },\n  firstBtn: {\n    marginLeft: 0\n  },\n  lastBtn: {\n    marginRight: 0\n  }\n});\nexport default styles;", "map": {"version": 3, "names": ["styles", "StyleSheet", "create", "container", "alignItems", "flexDirection", "position", "marginBottom", "btn", "color", "fontSize", "borderStyle", "borderColor", "borderWidth", "borderRadius", "paddingVertical", "paddingHorizontal", "marginHorizontal", "flexGrow", "textAlign", "fontFamily", "btnChecked", "backgroundColor", "fontWeight", "firstBtn", "marginLeft", "lastBtn", "marginRight"], "sources": ["E:/CryptoSignalsApp/src/components/CheckButtons/styles.js"], "sourcesContent": ["import { StyleSheet } from 'react-native';\r\n\r\nconst styles = StyleSheet.create({\r\n  container: {\r\n    alignItems: 'center',\r\n    flexDirection: 'row',\r\n    position: 'relative',\r\n    marginBottom: 4,\r\n  },\r\n  btn: {\r\n    color: '#fff',\r\n    fontSize: 13,\r\n    borderStyle: 'solid',\r\n    borderColor: '#0D0D0D',\r\n    borderWidth: 1,\r\n    borderRadius: 4,\r\n    paddingVertical: 6,\r\n    paddingHorizontal: 8,\r\n    marginHorizontal: 4,\r\n    flexGrow: 1,\r\n    textAlign: 'center',\r\n    fontFamily: 'Poppins_500Medium'\r\n  },\r\n  btnChecked: {\r\n    backgroundColor: '#0D0D0D',\r\n    color: '#FECB37',\r\n    fontWeight: '500'\r\n  },\r\n  firstBtn: {\r\n    marginLeft: 0\r\n  },\r\n  lastBtn: {\r\n    marginRight:0\r\n  }\r\n});\r\n\r\nexport default styles\r\n"], "mappings": ";AAEA,IAAMA,MAAM,GAAGC,UAAU,CAACC,MAAM,CAAC;EAC/BC,SAAS,EAAE;IACTC,UAAU,EAAE,QAAQ;IACpBC,aAAa,EAAE,KAAK;IACpBC,QAAQ,EAAE,UAAU;IACpBC,YAAY,EAAE;EAChB,CAAC;EACDC,GAAG,EAAE;IACHC,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,OAAO;IACpBC,WAAW,EAAE,SAAS;IACtBC,WAAW,EAAE,CAAC;IACdC,YAAY,EAAE,CAAC;IACfC,eAAe,EAAE,CAAC;IAClBC,iBAAiB,EAAE,CAAC;IACpBC,gBAAgB,EAAE,CAAC;IACnBC,QAAQ,EAAE,CAAC;IACXC,SAAS,EAAE,QAAQ;IACnBC,UAAU,EAAE;EACd,CAAC;EACDC,UAAU,EAAE;IACVC,eAAe,EAAE,SAAS;IAC1Bb,KAAK,EAAE,SAAS;IAChBc,UAAU,EAAE;EACd,CAAC;EACDC,QAAQ,EAAE;IACRC,UAAU,EAAE;EACd,CAAC;EACDC,OAAO,EAAE;IACPC,WAAW,EAAC;EACd;AACF,CAAC,CAAC;AAEF,eAAe3B,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}