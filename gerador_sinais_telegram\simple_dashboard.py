#!/usr/bin/env python3
"""
Dashboard Web Simples para CryptoSignals
Versão estável sem dependências complexas
"""

from flask import Flask, render_template, jsonify
import datetime
import threading
import time
import random

app = Flask(__name__)

# Dados simulados para demonstração
dashboard_data = {
    'services': {
        'gerador_sinais': {
            'name': 'Gerador de Sinais Telegram',
            'status': 'active',
            'uptime': '2d 14h 32m',
            'cpu_usage': 15.2,
            'memory_usage': 45.8,
            'restart_count': 0
        },
        'sistema-clientes': {
            'name': 'Sistema de Clientes Streamlit',
            'status': 'inactive',
            'uptime': '0m',
            'cpu_usage': 0.0,
            'memory_usage': 0.0,
            'restart_count': 3
        }
    },
    'server_metrics': {
        'cpu_percent': 23.5,
        'memory_percent': 67.2,
        'disk_percent': 45.8,
        'uptime': '15d 8h 42m'
    },
    'alerts': [
        {
            'level': 'WARNING',
            'service': 'sistema-clientes',
            'message': 'Serviço inativo há mais de 1 hora',
            'timestamp': '2024-01-15 13:30:00'
        }
    ],
    'logs': [
        {'timestamp': '2024-01-15 14:30:15', 'level': 'INFO', 'message': 'Sistema iniciado com sucesso'},
        {'timestamp': '2024-01-15 14:30:20', 'level': 'INFO', 'message': 'Conectado ao Telegram'},
        {'timestamp': '2024-01-15 14:30:25', 'level': 'INFO', 'message': 'Monitoramento de mercado ativo'},
        {'timestamp': '2024-01-15 14:31:00', 'level': 'WARNING', 'message': 'Alta volatilidade detectada em BTCUSDT'},
        {'timestamp': '2024-01-15 14:31:30', 'level': 'INFO', 'message': 'Sinal enviado: ETHUSDT LONG'},
    ],
    'timestamp': datetime.datetime.now().isoformat()
}

def update_simulated_data():
    """Atualiza dados simulados para demonstração"""
    global dashboard_data
    
    # Simular variações nas métricas
    dashboard_data['server_metrics']['cpu_percent'] = max(5, min(95, 
        dashboard_data['server_metrics']['cpu_percent'] + random.uniform(-5, 5)))
    
    dashboard_data['server_metrics']['memory_percent'] = max(10, min(90, 
        dashboard_data['server_metrics']['memory_percent'] + random.uniform(-3, 3)))
    
    dashboard_data['server_metrics']['disk_percent'] = max(20, min(80, 
        dashboard_data['server_metrics']['disk_percent'] + random.uniform(-1, 1)))
    
    # Atualizar timestamp
    dashboard_data['timestamp'] = datetime.datetime.now().isoformat()
    
    # Adicionar log ocasional
    if random.random() < 0.3:  # 30% de chance
        new_log = {
            'timestamp': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'level': random.choice(['INFO', 'WARNING']),
            'message': random.choice([
                'Monitoramento ativo',
                'Verificando conexões',
                'Sistema funcionando normalmente',
                'Análise de mercado em andamento'
            ])
        }
        dashboard_data['logs'].append(new_log)
        # Manter apenas os últimos 10 logs
        dashboard_data['logs'] = dashboard_data['logs'][-10:]

def background_updater():
    """Thread para atualizar dados em background"""
    while True:
        try:
            update_simulated_data()
            time.sleep(10)  # Atualizar a cada 10 segundos
        except Exception as e:
            print(f"Erro na atualização: {e}")
            time.sleep(30)

@app.route('/')
def dashboard():
    """Página principal do dashboard"""
    return render_template('dashboard.html')

@app.route('/api/data')
def get_data():
    """API para obter dados atuais"""
    return jsonify(dashboard_data)

@app.route('/api/refresh')
def refresh_data():
    """API para forçar atualização dos dados"""
    update_simulated_data()
    return jsonify({'status': 'success', 'data': dashboard_data})

def main():
    """Função principal"""
    print("🚀 Iniciando Dashboard Web Simples do CryptoSignals")
    print("=" * 55)
    
    # Iniciar thread de atualização em background
    update_thread = threading.Thread(target=background_updater, daemon=True)
    update_thread.start()
    
    print("✅ Dashboard inicializado")
    print("✅ Thread de atualização iniciada")
    print("\n📊 Dashboard disponível em:")
    print("   http://localhost:5000")
    print("   http://127.0.0.1:5000")
    print("\n🔄 Atualizações automáticas ativas")
    print("⏹️  Pressione Ctrl+C para parar")
    print("=" * 55)
    
    try:
        # Iniciar servidor Flask
        app.run(host='0.0.0.0', port=5000, debug=False)
    except KeyboardInterrupt:
        print("\n🛑 Parando dashboard...")
        print("✅ Dashboard parado")

if __name__ == '__main__':
    main()
