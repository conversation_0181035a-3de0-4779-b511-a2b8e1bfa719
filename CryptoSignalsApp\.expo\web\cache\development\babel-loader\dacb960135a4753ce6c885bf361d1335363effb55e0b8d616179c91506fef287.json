{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport React, { useEffect, useContext } from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport Alert from \"react-native-web/dist/exports/Alert\";\nimport BackHandler from \"react-native-web/dist/exports/BackHandler\";\nimport Linking from \"react-native-web/dist/exports/Linking\";\nimport VersionCheck from 'react-native-version-check';\nimport styles from \"./styles\";\nimport WebSocketWrapper from \"./WebSocket\";\nimport Subscription from \"./Subscription\";\nimport { AxiosContext } from \"../../store/axios\";\nimport { jsx as _jsx, Fragment as _Fragment, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar Wrapper = function Wrapper(_ref) {\n  var children = _ref.children;\n  var _useContext = useContext(AxiosContext),\n    _useContext2 = _slicedToArray(_useContext, 1),\n    api = _useContext2[0];\n  useEffect(function () {\n    checkMaintenance();\n  }, []);\n  var checkMaintenance = function () {\n    var _ref2 = _asyncToGenerator(function* () {\n      var response = yield api.get('/maintenance');\n      if (!response.data) {\n        return;\n      }\n      var _response$data = response.data,\n        title = _response$data.title,\n        message = _response$data.message,\n        cancelable = _response$data.cancelable,\n        upgrade = _response$data.upgrade;\n      if (!title || !message) {\n        return;\n      }\n      if (upgrade) {\n        yield checkUpgradeNeeded(title, message, cancelable);\n        return;\n      }\n      yield showMaintenance(title, message, cancelable);\n    });\n    return function checkMaintenance() {\n      return _ref2.apply(this, arguments);\n    };\n  }();\n  var showMaintenance = function () {\n    var _ref3 = _asyncToGenerator(function* (title, message, cancelable) {\n      Alert.alert(title, message, [], {\n        cancelable: cancelable\n      });\n    });\n    return function showMaintenance(_x, _x2, _x3) {\n      return _ref3.apply(this, arguments);\n    };\n  }();\n  var checkUpgradeNeeded = function () {\n    var _ref4 = _asyncToGenerator(function* (title, message, cancelable) {\n      var updateNeeded = yield VersionCheck.needUpdate();\n      if (updateNeeded && updateNeeded.isNeeded) {\n        Alert.alert(title, message, [{\n          text: 'Update',\n          onPress: function onPress() {\n            BackHandler.exitApp();\n            Linking.openURL(updateNeeded.storeUrl);\n          }\n        }], {\n          cancelable: cancelable\n        });\n      }\n    });\n    return function checkUpgradeNeeded(_x4, _x5, _x6) {\n      return _ref4.apply(this, arguments);\n    };\n  }();\n  return _jsxs(_Fragment, {\n    children: [_jsx(WebSocketWrapper, {}), _jsx(Subscription, {\n      children: _jsx(View, {\n        style: styles.container,\n        children: children\n      })\n    })]\n  });\n};\nexport default Wrapper;", "map": {"version": 3, "names": ["React", "useEffect", "useContext", "View", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Linking", "VersionCheck", "styles", "WebSocketWrapper", "Subscription", "AxiosContext", "jsx", "_jsx", "Fragment", "_Fragment", "jsxs", "_jsxs", "Wrapper", "_ref", "children", "_useContext", "_useContext2", "_slicedToArray", "api", "checkMaintenance", "_ref2", "_asyncToGenerator", "response", "get", "data", "_response$data", "title", "message", "cancelable", "upgrade", "checkUpgradeNeeded", "showMaintenance", "apply", "arguments", "_ref3", "alert", "_x", "_x2", "_x3", "_ref4", "updateNeeded", "needUpdate", "isNeeded", "text", "onPress", "exitApp", "openURL", "storeUrl", "_x4", "_x5", "_x6", "style", "container"], "sources": ["E:/CryptoSignalsApp/src/components/Wrapper/index.js"], "sourcesContent": ["import React, { useEffect, useContext } from 'react';\r\nimport { View, Alert, BackHandler, Linking } from 'react-native';\r\nimport VersionCheck from 'react-native-version-check';\r\nimport styles from './styles'\r\nimport WebSocketWrapper from './WebSocket';\r\nimport Subscription from './Subscription';\r\nimport { AxiosContext } from '../../store/axios';\r\n\r\nconst Wrapper = ({ children }) => {\r\n  const [api] = useContext(AxiosContext);\r\n\r\n  useEffect(() => {\r\n    checkMaintenance();\r\n  }, []);\r\n\r\n  const checkMaintenance = async () => {\r\n    const response = await api.get('/maintenance');\r\n\r\n    if (!response.data) {\r\n      return\r\n    }\r\n\r\n    const { title, message, cancelable, upgrade } = response.data;\r\n\r\n    if (!title || !message) {\r\n      return\r\n    }\r\n\r\n    if (upgrade) {\r\n      await checkUpgradeNeeded(title, message, cancelable);\r\n      return\r\n    }\r\n    \r\n    await showMaintenance(title, message, cancelable);\r\n  }\r\n\r\n  const showMaintenance = async (title, message, cancelable) => {\r\n    Alert.alert(title, message, [], { cancelable })\r\n  }\r\n  \r\n  const checkUpgradeNeeded = async (title, message, cancelable) => {\r\n    let updateNeeded = await VersionCheck.needUpdate();\r\n    \r\n    if (updateNeeded && updateNeeded.isNeeded) {\r\n      Alert.alert(\r\n        title,\r\n        message,\r\n        [\r\n          {\r\n            text: 'Update',\r\n            onPress: () => {\r\n              BackHandler.exitApp();\r\n              Linking.openURL(updateNeeded.storeUrl);\r\n            }    \r\n          }\r\n        ],\r\n        { cancelable }\r\n      )\r\n    }\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <WebSocketWrapper></WebSocketWrapper>\r\n\r\n      <Subscription>\r\n        <View style={styles.container}>\r\n          {children}\r\n        </View>\r\n      </Subscription>\r\n    </>\r\n  )\r\n}\r\n\r\nexport default Wrapper\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,UAAU,QAAQ,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,KAAA;AAAA,OAAAC,WAAA;AAAA,OAAAC,OAAA;AAErD,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,MAAM;AACb,OAAOC,gBAAgB;AACvB,OAAOC,YAAY;AACnB,SAASC,YAAY;AAA4B,SAAAC,GAAA,IAAAC,IAAA,EAAAC,QAAA,IAAAC,SAAA,EAAAC,IAAA,IAAAC,KAAA;AAEjD,IAAMC,OAAO,GAAG,SAAVA,OAAOA,CAAAC,IAAA,EAAqB;EAAA,IAAfC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;EACzB,IAAAC,WAAA,GAAcnB,UAAU,CAACS,YAAY,CAAC;IAAAW,YAAA,GAAAC,cAAA,CAAAF,WAAA;IAA/BG,GAAG,GAAAF,YAAA;EAEVrB,SAAS,CAAC,YAAM;IACdwB,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAEN,IAAMA,gBAAgB;IAAA,IAAAC,KAAA,GAAAC,iBAAA,CAAG,aAAY;MACnC,IAAMC,QAAQ,SAASJ,GAAG,CAACK,GAAG,CAAC,cAAc,CAAC;MAE9C,IAAI,CAACD,QAAQ,CAACE,IAAI,EAAE;QAClB;MACF;MAEA,IAAAC,cAAA,GAAgDH,QAAQ,CAACE,IAAI;QAArDE,KAAK,GAAAD,cAAA,CAALC,KAAK;QAAEC,OAAO,GAAAF,cAAA,CAAPE,OAAO;QAAEC,UAAU,GAAAH,cAAA,CAAVG,UAAU;QAAEC,OAAO,GAAAJ,cAAA,CAAPI,OAAO;MAE3C,IAAI,CAACH,KAAK,IAAI,CAACC,OAAO,EAAE;QACtB;MACF;MAEA,IAAIE,OAAO,EAAE;QACX,MAAMC,kBAAkB,CAACJ,KAAK,EAAEC,OAAO,EAAEC,UAAU,CAAC;QACpD;MACF;MAEA,MAAMG,eAAe,CAACL,KAAK,EAAEC,OAAO,EAAEC,UAAU,CAAC;IACnD,CAAC;IAAA,gBAnBKT,gBAAgBA,CAAA;MAAA,OAAAC,KAAA,CAAAY,KAAA,OAAAC,SAAA;IAAA;EAAA,GAmBrB;EAED,IAAMF,eAAe;IAAA,IAAAG,KAAA,GAAAb,iBAAA,CAAG,WAAOK,KAAK,EAAEC,OAAO,EAAEC,UAAU,EAAK;MAC5D9B,KAAK,CAACqC,KAAK,CAACT,KAAK,EAAEC,OAAO,EAAE,EAAE,EAAE;QAAEC,UAAU,EAAVA;MAAW,CAAC,CAAC;IACjD,CAAC;IAAA,gBAFKG,eAAeA,CAAAK,EAAA,EAAAC,GAAA,EAAAC,GAAA;MAAA,OAAAJ,KAAA,CAAAF,KAAA,OAAAC,SAAA;IAAA;EAAA,GAEpB;EAED,IAAMH,kBAAkB;IAAA,IAAAS,KAAA,GAAAlB,iBAAA,CAAG,WAAOK,KAAK,EAAEC,OAAO,EAAEC,UAAU,EAAK;MAC/D,IAAIY,YAAY,SAASvC,YAAY,CAACwC,UAAU,CAAC,CAAC;MAElD,IAAID,YAAY,IAAIA,YAAY,CAACE,QAAQ,EAAE;QACzC5C,KAAK,CAACqC,KAAK,CACTT,KAAK,EACLC,OAAO,EACP,CACE;UACEgB,IAAI,EAAE,QAAQ;UACdC,OAAO,EAAE,SAATA,OAAOA,CAAA,EAAQ;YACb7C,WAAW,CAAC8C,OAAO,CAAC,CAAC;YACrB7C,OAAO,CAAC8C,OAAO,CAACN,YAAY,CAACO,QAAQ,CAAC;UACxC;QACF,CAAC,CACF,EACD;UAAEnB,UAAU,EAAVA;QAAW,CACf,CAAC;MACH;IACF,CAAC;IAAA,gBAnBKE,kBAAkBA,CAAAkB,GAAA,EAAAC,GAAA,EAAAC,GAAA;MAAA,OAAAX,KAAA,CAAAP,KAAA,OAAAC,SAAA;IAAA;EAAA,GAmBvB;EAED,OACEtB,KAAA,CAAAF,SAAA;IAAAK,QAAA,GACEP,IAAA,CAACJ,gBAAgB,IAAmB,CAAC,EAErCI,IAAA,CAACH,YAAY;MAAAU,QAAA,EACXP,IAAA,CAACV,IAAI;QAACsD,KAAK,EAAEjD,MAAM,CAACkD,SAAU;QAAAtC,QAAA,EAC3BA;MAAQ,CACL;IAAC,CACK,CAAC;EAAA,CACf,CAAC;AAEP,CAAC;AAED,eAAeF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}