<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CryptoSignals Professional</title>

    <!-- Material-UI CSS -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons" />

    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #0f1419 0%, #1a1f2e 100%);
            color: #ffffff;
            overflow-x: hidden;
            margin: 0;
            padding: 0;
        }

        .app-container {
            max-width: 420px;
            margin: 0 auto;
            min-height: 100vh;
            background: linear-gradient(135deg, #0f1419 0%, #1a1f2e 100%);
            position: relative;
            box-shadow: 0 0 50px rgba(0, 0, 0, 0.5);
        }

        .header {
            padding: 24px 20px;
            background: linear-gradient(135deg, #1e2328 0%, #2b2f36 100%);
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .header-title {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .header-icon {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .header h1 {
            font-size: 20px;
            font-weight: 500;
            color: #ffffff;
            margin: 0;
        }

        .status-badge {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: #fff;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .status-badge.premium {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
            box-shadow: 0 4px 12px rgba(0, 184, 148, 0.3);
        }

        .content {
            padding: 16px;
            padding-bottom: 80px;
        }

        .premium-notice {
            background: linear-gradient(135deg, rgba(255, 107, 107, 0.1) 0%, rgba(238, 90, 36, 0.1) 100%);
            border: 1px solid rgba(255, 107, 107, 0.3);
            border-radius: 16px;
            padding: 24px;
            margin: 20px;
            margin-bottom: 20px;
            text-align: center;
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
        }

        .premium-notice::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #ff6b6b, #ee5a24, #ff6b6b);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .premium-notice-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
            color: white;
            font-size: 20px;
        }

        .premium-notice h3 {
            color: #ff6b6b;
            margin-bottom: 12px;
            font-size: 18px;
            font-weight: 600;
        }

        .premium-notice p {
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
            margin-bottom: 20px;
            line-height: 1.5;
        }

        .premium-btn {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: #fff;
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
        }

        .premium-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.6);
        }

        .card {
            background: linear-gradient(135deg, rgba(30, 35, 40, 0.8) 0%, rgba(43, 47, 54, 0.8) 100%);
            border-radius: 16px;
            padding: 20px;
            margin: 0 20px 16px;
            opacity: 0.6;
            border: 1px solid rgba(255, 107, 107, 0.3);
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 107, 107, 0.05) 0%, rgba(238, 90, 36, 0.05) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .card:hover::before {
            opacity: 1;
        }

        .card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
            border-color: rgba(255, 107, 107, 0.6);
        }

        .card.premium {
            opacity: 1;
            border: 1px solid rgba(0, 184, 148, 0.4);
            background: linear-gradient(135deg, rgba(30, 35, 40, 0.9) 0%, rgba(43, 47, 54, 0.9) 100%);
        }

        .card.premium::before {
            background: linear-gradient(135deg, rgba(0, 184, 148, 0.05) 0%, rgba(0, 160, 133, 0.05) 100%);
        }

        .card.premium:hover {
            border-color: rgba(0, 184, 148, 0.8);
            box-shadow: 0 12px 40px rgba(0, 184, 148, 0.2);
        }

        .channel-header {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }

        .channel-avatar {
            background: #333;
            border-radius: 12px;
            padding: 12px;
            margin-right: 12px;
            font-size: 24px;
        }

        .channel-info {
            flex: 1;
        }

        .channel-name {
            display: flex;
            align-items: center;
            margin-bottom: 4px;
        }

        .channel-name h3 {
            font-size: 16px;
            margin-right: 8px;
        }

        .premium-tag {
            background: #FF6B35;
            color: #fff;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 8px;
            font-weight: bold;
        }

        .channel-desc {
            color: #ccc;
            font-size: 13px;
            margin-bottom: 8px;
        }

        .channel-stats {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .stat-badge {
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: bold;
        }

        .stat-badge.type {
            background: #4CAF50;
            color: #fff;
        }

        .stat-badge.signals {
            background: #2196F3;
            color: #fff;
        }

        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 400px;
            background: #202020;
            border-top: 1px solid #5d5d5d;
            display: flex;
            height: 70px;
        }

        .nav-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .nav-item.active {
            color: #FECB37;
        }

        .nav-item:not(.active) {
            color: #8a8a8a;
        }

        .nav-icon {
            font-size: 18px;
            margin-bottom: 4px;
        }

        .nav-label {
            font-size: 10px;
            font-weight: 500;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 1000;
        }

        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: #2a2a2a;
            border-radius: 12px;
            padding: 20px;
            max-width: 350px;
            width: 90%;
            text-align: center;
        }

        .modal h3 {
            color: #FF6B35;
            margin-bottom: 12px;
        }

        .modal p {
            color: #ccc;
            margin-bottom: 20px;
        }

        .modal-buttons {
            display: flex;
            gap: 12px;
        }

        .modal-btn {
            flex: 1;
            padding: 10px;
            border: none;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
        }

        .modal-btn.cancel {
            background: #666;
            color: #fff;
        }

        .modal-btn.premium {
            background: #FF6B35;
            color: #fff;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <div class="header">
            <div class="header-title">
                <div class="header-icon">
                    <span class="material-icons">trending_up</span>
                </div>
                <h1>Signal Channels</h1>
            </div>
            <div class="status-badge" id="statusBadge">
                <span class="material-icons" style="font-size: 14px;">lock</span>
                FREE
            </div>
        </div>

        <!-- Content -->
        <div class="content" id="content">
            <!-- Premium Notice -->
            <div class="premium-notice" id="premiumNotice">
                <div class="premium-notice-icon">
                    <span class="material-icons">lock</span>
                </div>
                <h3>Acesso Premium Necessário</h3>
                <p>Para acessar os sinais de trading profissionais, você precisa de uma assinatura premium.</p>
                <button class="premium-btn" onclick="showPremiumModal()">Ver Planos Premium</button>
            </div>

            <!-- Refresh Button -->
            <div class="card" onclick="refreshChannels()">
                <div style="text-align: center; color: #00b894; font-weight: 600; display: flex; align-items: center; justify-content: center; gap: 8px;">
                    <span class="material-icons">refresh</span>
                    Atualizar Canais
                </div>
            </div>

            <!-- Channels -->
            <div class="card" onclick="accessChannel('Scalp')">
                <div class="channel-header">
                    <div class="channel-avatar">
                        <span class="material-icons">flash_on</span>
                    </div>
                    <div class="channel-info">
                        <div class="channel-name">
                            <h3>CryptoSignals Scalp</h3>
                            <div class="premium-tag" id="scalpTag">PREMIUM</div>
                        </div>
                        <div class="channel-desc">Sinais de scalping de alta frequência</div>
                        <div class="channel-stats">
                            <div class="stat-badge type">FUTURES</div>
                            <div class="stat-badge signals">0 sinais</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card" onclick="accessChannel('Swing')">
                <div class="channel-header">
                    <div class="channel-avatar">
                        <span class="material-icons">trending_up</span>
                    </div>
                    <div class="channel-info">
                        <div class="channel-name">
                            <h3>CryptoSignals Swing</h3>
                            <div class="premium-tag" id="swingTag">PREMIUM</div>
                        </div>
                        <div class="channel-desc">Sinais de swing trading</div>
                        <div class="channel-stats">
                            <div class="stat-badge type">SPOT</div>
                            <div class="stat-badge signals">0 sinais</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card" onclick="accessChannel('Breakout')">
                <div class="channel-header">
                    <div class="channel-avatar">
                        <span class="material-icons">rocket_launch</span>
                    </div>
                    <div class="channel-info">
                        <div class="channel-name">
                            <h3>CryptoSignals Breakout</h3>
                            <div class="premium-tag" id="breakoutTag">PREMIUM</div>
                        </div>
                        <div class="channel-desc">Sinais de rompimento de resistência</div>
                        <div class="channel-stats">
                            <div class="stat-badge type">FUTURES</div>
                            <div class="stat-badge signals">0 sinais</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bottom Navigation -->
        <div class="bottom-nav">
            <div class="nav-item active" onclick="showTab('signals')">
                <div class="nav-icon">
                    <span class="material-icons">trending_up</span>
                </div>
                <div class="nav-label">Signals</div>
            </div>
            <div class="nav-item" onclick="showTab('premium')">
                <div class="nav-icon">
                    <span class="material-icons">diamond</span>
                </div>
                <div class="nav-label">Premium</div>
            </div>
            <div class="nav-item" onclick="showTab('profile')">
                <div class="nav-icon">
                    <span class="material-icons">person</span>
                </div>
                <div class="nav-label">Profile</div>
            </div>
        </div>
    </div>

    <!-- Modal -->
    <div class="modal" id="accessModal">
        <div class="modal-content">
            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 16px;">
                <span class="material-icons" style="color: #ff6b6b;">diamond</span>
                <h3 style="margin: 0;">Acesso Premium Necessário</h3>
            </div>
            <p>Para acessar os sinais de trading, você precisa de uma assinatura premium.</p>
            <div class="modal-buttons">
                <button class="modal-btn cancel" onclick="closeModal()">Cancelar</button>
                <button class="modal-btn premium" onclick="showPremiumModal()">Ver Planos</button>
            </div>
        </div>
    </div>

    <script>
        let isPremium = false;

        function updateUI() {
            const statusBadge = document.getElementById('statusBadge');
            const premiumNotice = document.getElementById('premiumNotice');
            const cards = document.querySelectorAll('.card:not(:first-child)');
            const premiumTags = document.querySelectorAll('.premium-tag');

            if (isPremium) {
                statusBadge.innerHTML = '<span class="material-icons" style="font-size: 14px;">diamond</span> PREMIUM';
                statusBadge.classList.add('premium');
                premiumNotice.style.display = 'none';
                cards.forEach(card => {
                    card.classList.add('premium');
                    card.style.opacity = '1';
                    card.style.borderColor = '#00b894';
                });
                premiumTags.forEach(tag => tag.style.display = 'none');
            } else {
                statusBadge.innerHTML = '<span class="material-icons" style="font-size: 14px;">lock</span> FREE';
                statusBadge.classList.remove('premium');
                premiumNotice.style.display = 'block';
                cards.forEach(card => {
                    card.classList.remove('premium');
                    card.style.opacity = '0.6';
                    card.style.borderColor = 'rgba(255, 107, 107, 0.3)';
                });
                premiumTags.forEach(tag => tag.style.display = 'block');
            }
        }

        function accessChannel(channelName) {
            if (!isPremium) {
                document.getElementById('accessModal').classList.add('show');
            } else {
                alert(`Acessando sinais do canal ${channelName}...`);
            }
        }

        function closeModal() {
            document.getElementById('accessModal').classList.remove('show');
        }

        function showPremiumModal() {
            closeModal();
            alert('Página Premium:\n\n◆ Plano Pro - 29 USDT/mês\n◆ Plano Elite - 79 USDT/mês\n\nPagamento via USDT (BEP20/ERC20)\nWallet: ******************************************');
        }

        function refreshChannels() {
            alert('Atualizando canais...');
        }

        function showTab(tab) {
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => item.classList.remove('active'));
            event.target.closest('.nav-item').classList.add('active');

            if (tab === 'premium') {
                showPremiumModal();
            } else if (tab === 'profile') {
                alert('Página Profile:\n\n● Status: ' + (isPremium ? 'Premium Ativo' : 'Plano Gratuito') + '\n● Wallet USDT: 0xFf83fE...7f78424\n● Configurações básicas\n● Suporte');
            }
        }

        // Simular ativação premium após 5 segundos (para demonstração)
        setTimeout(() => {
            if (confirm('◆ Demonstração: Ativar Premium?\n\n(Isso simula um pagamento confirmado)')) {
                isPremium = true;
                updateUI();
                alert('✓ Premium ativado! Agora você pode acessar todos os sinais.');
            }
        }, 3000);

        // Inicializar UI
        updateUI();
    </script>
</body>
</html>
