<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CryptoSignals Professional</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1a1a1a;
            color: #fff;
            overflow-x: hidden;
        }

        .app-container {
            max-width: 400px;
            margin: 0 auto;
            min-height: 100vh;
            background: #1a1a1a;
            position: relative;
        }

        .header {
            padding: 20px 16px;
            background: #2a2a2a;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 24px;
            font-weight: bold;
        }

        .status-badge {
            background: #FF6B35;
            color: #fff;
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 10px;
            font-weight: bold;
        }

        .status-badge.premium {
            background: #4CAF50;
        }

        .content {
            padding: 16px;
            padding-bottom: 80px;
        }

        .premium-notice {
            background: rgba(255, 107, 53, 0.1);
            border: 1px solid #FF6B35;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            text-align: center;
        }

        .premium-notice h3 {
            color: #FF6B35;
            margin-bottom: 8px;
        }

        .premium-notice p {
            color: #FFB74D;
            font-size: 14px;
            margin-bottom: 12px;
        }

        .premium-btn {
            background: #FF6B35;
            color: #fff;
            padding: 8px 20px;
            border: none;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
        }

        .card {
            background: #2a2a2a;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            opacity: 0.6;
            border: 1px solid #FF6B35;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
        }

        .card.premium {
            opacity: 1;
            border: 1px solid #4CAF50;
        }

        .channel-header {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }

        .channel-avatar {
            background: #333;
            border-radius: 12px;
            padding: 12px;
            margin-right: 12px;
            font-size: 24px;
        }

        .channel-info {
            flex: 1;
        }

        .channel-name {
            display: flex;
            align-items: center;
            margin-bottom: 4px;
        }

        .channel-name h3 {
            font-size: 16px;
            margin-right: 8px;
        }

        .premium-tag {
            background: #FF6B35;
            color: #fff;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 8px;
            font-weight: bold;
        }

        .channel-desc {
            color: #ccc;
            font-size: 13px;
            margin-bottom: 8px;
        }

        .channel-stats {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .stat-badge {
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: bold;
        }

        .stat-badge.type {
            background: #4CAF50;
            color: #fff;
        }

        .stat-badge.signals {
            background: #2196F3;
            color: #fff;
        }

        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 400px;
            background: #202020;
            border-top: 1px solid #5d5d5d;
            display: flex;
            height: 70px;
        }

        .nav-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .nav-item.active {
            color: #FECB37;
        }

        .nav-item:not(.active) {
            color: #8a8a8a;
        }

        .nav-icon {
            font-size: 18px;
            margin-bottom: 4px;
        }

        .nav-label {
            font-size: 10px;
            font-weight: 500;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 1000;
        }

        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: #2a2a2a;
            border-radius: 12px;
            padding: 20px;
            max-width: 350px;
            width: 90%;
            text-align: center;
        }

        .modal h3 {
            color: #FF6B35;
            margin-bottom: 12px;
        }

        .modal p {
            color: #ccc;
            margin-bottom: 20px;
        }

        .modal-buttons {
            display: flex;
            gap: 12px;
        }

        .modal-btn {
            flex: 1;
            padding: 10px;
            border: none;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
        }

        .modal-btn.cancel {
            background: #666;
            color: #fff;
        }

        .modal-btn.premium {
            background: #FF6B35;
            color: #fff;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <div class="header">
            <h1>Signal Channels 📺</h1>
            <div class="status-badge" id="statusBadge">🔒 FREE</div>
        </div>

        <!-- Content -->
        <div class="content" id="content">
            <!-- Premium Notice -->
            <div class="premium-notice" id="premiumNotice">
                <h3>🔒 Acesso Premium Necessário</h3>
                <p>Para acessar os sinais de trading profissionais, você precisa de uma assinatura premium.</p>
                <button class="premium-btn" onclick="showPremiumModal()">Ver Planos Premium</button>
            </div>

            <!-- Refresh Button -->
            <div class="card" onclick="refreshChannels()">
                <div style="text-align: center; color: #4CAF50; font-weight: bold;">
                    🔄 Atualizar Canais
                </div>
            </div>

            <!-- Channels -->
            <div class="card" onclick="accessChannel('Scalp')">
                <div class="channel-header">
                    <div class="channel-avatar">⚡</div>
                    <div class="channel-info">
                        <div class="channel-name">
                            <h3>CryptoSignals Scalp</h3>
                            <div class="premium-tag" id="scalpTag">PREMIUM</div>
                        </div>
                        <div class="channel-desc">Sinais de scalping de alta frequência</div>
                        <div class="channel-stats">
                            <div class="stat-badge type">FUTURES</div>
                            <div class="stat-badge signals">0 sinais</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card" onclick="accessChannel('Swing')">
                <div class="channel-header">
                    <div class="channel-avatar">📈</div>
                    <div class="channel-info">
                        <div class="channel-name">
                            <h3>CryptoSignals Swing</h3>
                            <div class="premium-tag" id="swingTag">PREMIUM</div>
                        </div>
                        <div class="channel-desc">Sinais de swing trading</div>
                        <div class="channel-stats">
                            <div class="stat-badge type">SPOT</div>
                            <div class="stat-badge signals">0 sinais</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card" onclick="accessChannel('Breakout')">
                <div class="channel-header">
                    <div class="channel-avatar">🚀</div>
                    <div class="channel-info">
                        <div class="channel-name">
                            <h3>CryptoSignals Breakout</h3>
                            <div class="premium-tag" id="breakoutTag">PREMIUM</div>
                        </div>
                        <div class="channel-desc">Sinais de rompimento de resistência</div>
                        <div class="channel-stats">
                            <div class="stat-badge type">FUTURES</div>
                            <div class="stat-badge signals">0 sinais</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bottom Navigation -->
        <div class="bottom-nav">
            <div class="nav-item active" onclick="showTab('signals')">
                <div class="nav-icon">📺</div>
                <div class="nav-label">Signals</div>
            </div>
            <div class="nav-item" onclick="showTab('premium')">
                <div class="nav-icon">💎</div>
                <div class="nav-label">Premium</div>
            </div>
            <div class="nav-item" onclick="showTab('profile')">
                <div class="nav-icon">👤</div>
                <div class="nav-label">Profile</div>
            </div>
        </div>
    </div>

    <!-- Modal -->
    <div class="modal" id="accessModal">
        <div class="modal-content">
            <h3>Acesso Premium Necessário 💎</h3>
            <p>Para acessar os sinais de trading, você precisa de uma assinatura premium.</p>
            <div class="modal-buttons">
                <button class="modal-btn cancel" onclick="closeModal()">Cancelar</button>
                <button class="modal-btn premium" onclick="showPremiumModal()">Ver Planos</button>
            </div>
        </div>
    </div>

    <script>
        let isPremium = false;

        function updateUI() {
            const statusBadge = document.getElementById('statusBadge');
            const premiumNotice = document.getElementById('premiumNotice');
            const cards = document.querySelectorAll('.card:not(:first-child)');
            const premiumTags = document.querySelectorAll('.premium-tag');

            if (isPremium) {
                statusBadge.textContent = '💎 PREMIUM';
                statusBadge.classList.add('premium');
                premiumNotice.style.display = 'none';
                cards.forEach(card => {
                    card.classList.add('premium');
                    card.style.opacity = '1';
                    card.style.borderColor = '#4CAF50';
                });
                premiumTags.forEach(tag => tag.style.display = 'none');
            } else {
                statusBadge.textContent = '🔒 FREE';
                statusBadge.classList.remove('premium');
                premiumNotice.style.display = 'block';
                cards.forEach(card => {
                    card.classList.remove('premium');
                    card.style.opacity = '0.6';
                    card.style.borderColor = '#FF6B35';
                });
                premiumTags.forEach(tag => tag.style.display = 'block');
            }
        }

        function accessChannel(channelName) {
            if (!isPremium) {
                document.getElementById('accessModal').classList.add('show');
            } else {
                alert(`Acessando sinais do canal ${channelName}...`);
            }
        }

        function closeModal() {
            document.getElementById('accessModal').classList.remove('show');
        }

        function showPremiumModal() {
            closeModal();
            alert('Página Premium:\n\n💎 Plano Pro - 29 USDT/mês\n💎 Plano Elite - 79 USDT/mês\n\nPagamento via USDT (BEP20/ERC20)\nWallet: ******************************************');
        }

        function refreshChannels() {
            alert('Atualizando canais...');
        }

        function showTab(tab) {
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => item.classList.remove('active'));
            event.target.closest('.nav-item').classList.add('active');

            if (tab === 'premium') {
                showPremiumModal();
            } else if (tab === 'profile') {
                alert('Página Profile:\n\n👤 Status: ' + (isPremium ? 'Premium Ativo' : 'Plano Gratuito') + '\n💰 Wallet USDT: 0xFf83fE...7f78424\n⚙️ Configurações básicas\n📞 Suporte');
            }
        }

        // Simular ativação premium após 5 segundos (para demonstração)
        setTimeout(() => {
            if (confirm('🎉 Demonstração: Ativar Premium?\n\n(Isso simula um pagamento confirmado)')) {
                isPremium = true;
                updateUI();
                alert('✅ Premium ativado! Agora você pode acessar todos os sinais.');
            }
        }, 3000);

        // Inicializar UI
        updateUI();
    </script>
</body>
</html>
