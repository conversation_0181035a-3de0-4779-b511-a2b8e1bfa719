{"ast": null, "code": "import Animated from \"react-native-web/dist/exports/Animated\";\nimport color from 'color';\nimport { MD2DarkTheme } from \"./themes/v2/DarkTheme\";\nexport var isAnimatedValue = function isAnimatedValue(it) {\n  return it instanceof Animated.Value;\n};\nexport default function overlay(elevation) {\n  var surfaceColor = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : function (_MD2DarkTheme$colors) {\n    return (_MD2DarkTheme$colors = MD2DarkTheme.colors) === null || _MD2DarkTheme$colors === void 0 ? void 0 : _MD2DarkTheme$colors.surface;\n  }();\n  if (isAnimatedValue(elevation)) {\n    var inputRange = [0, 1, 2, 3, 8, 24];\n    return elevation.interpolate({\n      inputRange: inputRange,\n      outputRange: inputRange.map(function (elevation) {\n        return calculateColor(surfaceColor, elevation);\n      })\n    });\n  }\n  return calculateColor(surfaceColor, elevation);\n}\nfunction calculateColor(surfaceColor) {\n  var elevation = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;\n  var overlayTransparency;\n  if (elevation >= 1 && elevation <= 24) {\n    overlayTransparency = elevationOverlayTransparency[elevation];\n  } else if (elevation > 24) {\n    overlayTransparency = elevationOverlayTransparency[24];\n  } else {\n    overlayTransparency = elevationOverlayTransparency[1];\n  }\n  return color(surfaceColor).mix(color('white'), overlayTransparency * 0.01).hex();\n}\nvar elevationOverlayTransparency = {\n  1: 5,\n  2: 7,\n  3: 8,\n  4: 9,\n  5: 10,\n  6: 11,\n  7: 11.5,\n  8: 12,\n  9: 12.5,\n  10: 13,\n  11: 13.5,\n  12: 14,\n  13: 14.25,\n  14: 14.5,\n  15: 14.75,\n  16: 15,\n  17: 15.12,\n  18: 15.24,\n  19: 15.36,\n  20: 15.48,\n  21: 15.6,\n  22: 15.72,\n  23: 15.84,\n  24: 16\n};", "map": {"version": 3, "names": ["color", "MD2DarkTheme", "isAnimatedValue", "it", "Animated", "Value", "overlay", "elevation", "surfaceColor", "arguments", "length", "undefined", "_MD2DarkTheme$colors", "colors", "surface", "inputRange", "interpolate", "outputRange", "map", "calculateColor", "overlayTransparency", "elevationOverlayTransparency", "mix", "hex"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\styles\\overlay.tsx"], "sourcesContent": ["import { Animated } from 'react-native';\n\nimport color from 'color';\n\nimport { MD2DarkTheme } from './themes/v2/DarkTheme';\n\nexport const isAnimatedValue = (\n  it: number | string | Animated.AnimatedInterpolation<number | string>\n): it is Animated.Value => it instanceof Animated.Value;\n\nexport default function overlay<T extends Animated.Value | number>(\n  elevation: T,\n  surfaceColor: string = MD2DarkTheme.colors?.surface\n): T extends number ? string : Animated.AnimatedInterpolation<number | string> {\n  if (isAnimatedValue(elevation)) {\n    const inputRange = [0, 1, 2, 3, 8, 24];\n\n    // @ts-expect-error: TS doesn't seem to refine the type correctly\n    return elevation.interpolate({\n      inputRange,\n      outputRange: inputRange.map((elevation) => {\n        return calculateColor(surfaceColor, elevation);\n      }),\n    });\n  }\n\n  // @ts-expect-error: TS doesn't seem to refine the type correctly\n  return calculateColor(surfaceColor, elevation);\n}\n\nfunction calculateColor(surfaceColor: string, elevation: number = 1) {\n  let overlayTransparency: number;\n  if (elevation >= 1 && elevation <= 24) {\n    overlayTransparency = elevationOverlayTransparency[elevation];\n  } else if (elevation > 24) {\n    overlayTransparency = elevationOverlayTransparency[24];\n  } else {\n    overlayTransparency = elevationOverlayTransparency[1];\n  }\n  return color(surfaceColor)\n    .mix(color('white'), overlayTransparency * 0.01)\n    .hex();\n}\n\nconst elevationOverlayTransparency: Record<string, number> = {\n  1: 5,\n  2: 7,\n  3: 8,\n  4: 9,\n  5: 10,\n  6: 11,\n  7: 11.5,\n  8: 12,\n  9: 12.5,\n  10: 13,\n  11: 13.5,\n  12: 14,\n  13: 14.25,\n  14: 14.5,\n  15: 14.75,\n  16: 15,\n  17: 15.12,\n  18: 15.24,\n  19: 15.36,\n  20: 15.48,\n  21: 15.6,\n  22: 15.72,\n  23: 15.84,\n  24: 16,\n};\n"], "mappings": ";AAEA,OAAOA,KAAK,MAAM,OAAO;AAEzB,SAASC,YAAY;AAErB,OAAO,IAAMC,eAAe,GAC1B,SADWA,eAAeA,CAC1BC,EAAqE;EAAA,OAC5CA,EAAE,YAAYC,QAAQ,CAACC,KAAK;AAAA;AAEvD,eAAe,SAASC,OAAOA,CAC7BC,SAAY,EAEiE;EAAA,IAD7EC,YAAoB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,gBAAAG,oBAAA;IAAA,QAAAA,oBAAA,GAAGX,YAAY,CAACY,MAAM,cAAAD,oBAAA,uBAAnBA,oBAAA,CAAqBE,OAAO;EAAA;EAEnD,IAAIZ,eAAe,CAACK,SAAS,CAAC,EAAE;IAC9B,IAAMQ,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;IAGtC,OAAOR,SAAS,CAACS,WAAW,CAAC;MAC3BD,UAAU,EAAVA,UAAU;MACVE,WAAW,EAAEF,UAAU,CAACG,GAAG,CAAE,UAAAX,SAAS,EAAK;QACzC,OAAOY,cAAc,CAACX,YAAY,EAAED,SAAS,CAAC;MAChD,CAAC;IACH,CAAC,CAAC;EACJ;EAGA,OAAOY,cAAc,CAACX,YAAY,EAAED,SAAS,CAAC;AAChD;AAEA,SAASY,cAAcA,CAACX,YAAoB,EAAyB;EAAA,IAAvBD,SAAiB,GAAAE,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EACjE,IAAIW,mBAA2B;EAC/B,IAAIb,SAAS,IAAI,CAAC,IAAIA,SAAS,IAAI,EAAE,EAAE;IACrCa,mBAAmB,GAAGC,4BAA4B,CAACd,SAAS,CAAC;EAC/D,CAAC,MAAM,IAAIA,SAAS,GAAG,EAAE,EAAE;IACzBa,mBAAmB,GAAGC,4BAA4B,CAAC,EAAE,CAAC;EACxD,CAAC,MAAM;IACLD,mBAAmB,GAAGC,4BAA4B,CAAC,CAAC,CAAC;EACvD;EACA,OAAOrB,KAAK,CAACQ,YAAY,CAAC,CACvBc,GAAG,CAACtB,KAAK,CAAC,OAAO,CAAC,EAAEoB,mBAAmB,GAAG,IAAI,CAAC,CAC/CG,GAAG,CAAC,CAAC;AACV;AAEA,IAAMF,4BAAoD,GAAG;EAC3D,CAAC,EAAE,CAAC;EACJ,CAAC,EAAE,CAAC;EACJ,CAAC,EAAE,CAAC;EACJ,CAAC,EAAE,CAAC;EACJ,CAAC,EAAE,EAAE;EACL,CAAC,EAAE,EAAE;EACL,CAAC,EAAE,IAAI;EACP,CAAC,EAAE,EAAE;EACL,CAAC,EAAE,IAAI;EACP,EAAE,EAAE,EAAE;EACN,EAAE,EAAE,IAAI;EACR,EAAE,EAAE,EAAE;EACN,EAAE,EAAE,KAAK;EACT,EAAE,EAAE,IAAI;EACR,EAAE,EAAE,KAAK;EACT,EAAE,EAAE,EAAE;EACN,EAAE,EAAE,KAAK;EACT,EAAE,EAAE,KAAK;EACT,EAAE,EAAE,KAAK;EACT,EAAE,EAAE,KAAK;EACT,EAAE,EAAE,IAAI;EACR,EAAE,EAAE,KAAK;EACT,EAAE,EAAE,KAAK;EACT,EAAE,EAAE;AACN,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}