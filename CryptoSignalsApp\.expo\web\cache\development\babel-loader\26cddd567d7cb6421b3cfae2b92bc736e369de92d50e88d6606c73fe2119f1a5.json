{"ast": null, "code": "import React from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport { format } from 'date-fns';\nimport enUSLocale from 'date-fns/locale/en-US';\nimport styles from \"./styles\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar SignalsCard = function SignalsCard(_ref) {\n  var createdAt = _ref.createdAt,\n    message = _ref.message;\n  var formattedCreatedAt = createdAt;\n  try {\n    formattedCreatedAt = format(new Date(createdAt), 'LLL dd, h:mm aa', {\n      locale: enUSLocale\n    });\n  } catch (_unused) {}\n  return _jsxs(View, {\n    style: styles.container,\n    children: [_jsx(View, {\n      style: styles.header,\n      children: _jsx(Text, {\n        style: styles.textDate,\n        children: formattedCreatedAt\n      })\n    }), _jsx(View, {\n      style: styles.body,\n      children: _jsx(Text, {\n        style: styles.textMessage,\n        selectable: true,\n        children: message\n      })\n    })]\n  });\n};\nexport default SignalsCard;", "map": {"version": 3, "names": ["React", "View", "Text", "format", "enUSLocale", "styles", "jsx", "_jsx", "jsxs", "_jsxs", "SignalsCard", "_ref", "createdAt", "message", "formattedCreatedAt", "Date", "locale", "_unused", "style", "container", "children", "header", "textDate", "body", "textMessage", "selectable"], "sources": ["E:/CryptoSignalsApp/src/pages/Signals/Card/index.js"], "sourcesContent": ["import React from 'react';\r\nimport { View, Text } from 'react-native';\r\nimport { format } from 'date-fns';\r\nimport enUSLocale from 'date-fns/locale/en-US';\r\nimport styles from './styles';\r\n\r\nconst SignalsCard = ({ createdAt, message }) => {\r\n  let formattedCreatedAt = createdAt\r\n\r\n  try {\r\n    formattedCreatedAt = format(\r\n      new Date(createdAt),\r\n      'LLL dd, h:mm aa',\r\n      { locale: enUSLocale }\r\n    );\r\n  } catch {\r\n  }\r\n\r\n  return (\r\n    <View style={styles.container}>\r\n      <View style={styles.header}>\r\n        <Text style={styles.textDate}>{formattedCreatedAt}</Text>\r\n      </View>\r\n\r\n      <View style={styles.body}>\r\n        <Text style={styles.textMessage} selectable>{message}</Text>\r\n      </View>\r\n    </View>\r\n  )\r\n}\r\n\r\nexport default SignalsCard\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,IAAA;AAE1B,SAASC,MAAM,QAAQ,UAAU;AACjC,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,OAAOC,MAAM;AAAiB,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAE9B,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAAC,IAAA,EAA+B;EAAA,IAAzBC,SAAS,GAAAD,IAAA,CAATC,SAAS;IAAEC,OAAO,GAAAF,IAAA,CAAPE,OAAO;EACvC,IAAIC,kBAAkB,GAAGF,SAAS;EAElC,IAAI;IACFE,kBAAkB,GAAGX,MAAM,CACzB,IAAIY,IAAI,CAACH,SAAS,CAAC,EACnB,iBAAiB,EACjB;MAAEI,MAAM,EAAEZ;IAAW,CACvB,CAAC;EACH,CAAC,CAAC,OAAAa,OAAA,EAAM,CACR;EAEA,OACER,KAAA,CAACR,IAAI;IAACiB,KAAK,EAAEb,MAAM,CAACc,SAAU;IAAAC,QAAA,GAC5Bb,IAAA,CAACN,IAAI;MAACiB,KAAK,EAAEb,MAAM,CAACgB,MAAO;MAAAD,QAAA,EACzBb,IAAA,CAACL,IAAI;QAACgB,KAAK,EAAEb,MAAM,CAACiB,QAAS;QAAAF,QAAA,EAAEN;MAAkB,CAAO;IAAC,CACrD,CAAC,EAEPP,IAAA,CAACN,IAAI;MAACiB,KAAK,EAAEb,MAAM,CAACkB,IAAK;MAAAH,QAAA,EACvBb,IAAA,CAACL,IAAI;QAACgB,KAAK,EAAEb,MAAM,CAACmB,WAAY;QAACC,UAAU;QAAAL,QAAA,EAAEP;MAAO,CAAO;IAAC,CACxD,CAAC;EAAA,CACH,CAAC;AAEX,CAAC;AAED,eAAeH,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}