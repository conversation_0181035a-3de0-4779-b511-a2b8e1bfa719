{"ast": null, "code": "export function goBack() {\n  return {\n    type: 'GO_BACK'\n  };\n}\nexport function navigate() {\n  if (typeof (arguments.length <= 0 ? undefined : arguments[0]) === 'string') {\n    return {\n      type: 'NAVIGATE',\n      payload: {\n        name: arguments.length <= 0 ? undefined : arguments[0],\n        params: arguments.length <= 1 ? undefined : arguments[1]\n      }\n    };\n  } else {\n    var payload = (arguments.length <= 0 ? undefined : arguments[0]) || {};\n    if (!payload.hasOwnProperty('key') && !payload.hasOwnProperty('name')) {\n      throw new Error('You need to specify name or key when calling navigate with an object as the argument. See https://reactnavigation.org/docs/navigation-actions#navigate for usage.');\n    }\n    return {\n      type: 'NAVIGATE',\n      payload: payload\n    };\n  }\n}\nexport function reset(state) {\n  return {\n    type: 'RESET',\n    payload: state\n  };\n}\nexport function setParams(params) {\n  return {\n    type: 'SET_PARAMS',\n    payload: {\n      params: params\n    }\n  };\n}", "map": {"version": 3, "names": ["goBack", "type", "navigate", "arguments", "length", "undefined", "payload", "name", "params", "hasOwnProperty", "Error", "reset", "state", "setParams"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\@react-navigation\\routers\\src\\CommonActions.tsx"], "sourcesContent": ["import type { NavigationState, PartialState, Route } from './types';\n\ntype ResetState =\n  | PartialState<NavigationState>\n  | NavigationState\n  | (Omit<NavigationState, 'routes'> & {\n      routes: Omit<Route<string>, 'key'>[];\n    });\n\nexport type Action =\n  | {\n      type: 'GO_BACK';\n      source?: string;\n      target?: string;\n    }\n  | {\n      type: 'NAVIGATE';\n      payload:\n        | {\n            key: string;\n            name?: undefined;\n            params?: object;\n            path?: string;\n            merge?: boolean;\n          }\n        | {\n            name: string;\n            key?: string;\n            params?: object;\n            path?: string;\n            merge?: boolean;\n          };\n      source?: string;\n      target?: string;\n    }\n  | {\n      type: 'RESET';\n      payload: ResetState | undefined;\n      source?: string;\n      target?: string;\n    }\n  | {\n      type: 'SET_PARAMS';\n      payload: { params?: object };\n      source?: string;\n      target?: string;\n    };\n\nexport function goBack(): Action {\n  return { type: 'GO_BACK' };\n}\n\nexport function navigate(\n  options:\n    | { key: string; params?: object; path?: string; merge?: boolean }\n    | {\n        name: string;\n        key?: string;\n        params?: object;\n        path?: string;\n        merge?: boolean;\n      }\n): Action;\n// eslint-disable-next-line no-redeclare\nexport function navigate(name: string, params?: object): Action;\n// eslint-disable-next-line no-redeclare\nexport function navigate(...args: any): Action {\n  if (typeof args[0] === 'string') {\n    return { type: 'NAVIGATE', payload: { name: args[0], params: args[1] } };\n  } else {\n    const payload = args[0] || {};\n\n    if (!payload.hasOwnProperty('key') && !payload.hasOwnProperty('name')) {\n      throw new Error(\n        'You need to specify name or key when calling navigate with an object as the argument. See https://reactnavigation.org/docs/navigation-actions#navigate for usage.'\n      );\n    }\n\n    return { type: 'NAVIGATE', payload };\n  }\n}\n\nexport function reset(state: ResetState | undefined): Action {\n  return { type: 'RESET', payload: state };\n}\n\nexport function setParams(params: object): Action {\n  return { type: 'SET_PARAMS', payload: { params } };\n}\n"], "mappings": "AAgDA,OAAO,SAASA,MAAMA,CAAA,EAAW;EAC/B,OAAO;IAAEC,IAAI,EAAE;EAAU,CAAC;AAC5B;AAgBA,OAAO,SAASC,QAAQA,CAAA,EAAuB;EAC7C,IAAI,QAAAC,SAAA,CAAAC,MAAA,QAAAC,SAAA,GAAAF,SAAA,IAAc,KAAK,QAAQ,EAAE;IAC/B,OAAO;MAAEF,IAAI,EAAE,UAAU;MAAEK,OAAO,EAAE;QAAEC,IAAI,EAAAJ,SAAA,CAAAC,MAAA,QAAAC,SAAA,GAAAF,SAAA,GAAS;QAAEK,MAAM,EAAAL,SAAA,CAAAC,MAAA,QAAAC,SAAA,GAAAF,SAAA;MAAU;IAAE,CAAC;EAC1E,CAAC,MAAM;IACL,IAAMG,OAAO,GAAG,CAAAH,SAAA,CAAAC,MAAA,QAAAC,SAAA,GAAAF,SAAA,QAAW,CAAC,CAAC;IAE7B,IAAI,CAACG,OAAO,CAACG,cAAc,CAAC,KAAK,CAAC,IAAI,CAACH,OAAO,CAACG,cAAc,CAAC,MAAM,CAAC,EAAE;MACrE,MAAM,IAAIC,KAAK,CACb,mKAAmK,CACpK;IACH;IAEA,OAAO;MAAET,IAAI,EAAE,UAAU;MAAEK,OAAA,EAAAA;IAAQ,CAAC;EACtC;AACF;AAEA,OAAO,SAASK,KAAKA,CAACC,KAA6B,EAAU;EAC3D,OAAO;IAAEX,IAAI,EAAE,OAAO;IAAEK,OAAO,EAAEM;EAAM,CAAC;AAC1C;AAEA,OAAO,SAASC,SAASA,CAACL,MAAc,EAAU;EAChD,OAAO;IAAEP,IAAI,EAAE,YAAY;IAAEK,OAAO,EAAE;MAAEE,MAAA,EAAAA;IAAO;EAAE,CAAC;AACpD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}