import React, { useState, useEffect } from 'react';
import { Appearance } from 'react-native';
import { Provider as PaperProvider, MD3LightTheme as PaperDefaultTheme, MD3DarkTheme as PaperDarkTheme } from 'react-native-paper';
import { NavigationContainer, DefaultTheme as NavigationDefaultTheme, DarkTheme as NavigationDarkTheme } from '@react-navigation/native';
import { I18nextProvider } from 'react-i18next';
import StoreProvider from './src/store';
import AxiosProvider from './src/store/axios';
import { useFonts, Poppins_400Regular, Poppins_500Medium, Poppins_600SemiBold, Poppins_700Bold } from '@expo-google-fonts/poppins';
import Routes from './src/routes';
import Loading from './src/components/Loading';
import i18n from './i18n';





const CustomDefaultTheme = {
  ...NavigationDefaultTheme,
  ...PaperDefaultTheme,
  colors: {
    ...NavigationDefaultTheme.colors,
    ...PaperDefaultTheme.colors,
    background: '#202020',
    primary: '#FECB37',
    grey: '#0D0D0D',
  }
};

const CustomDarkTheme = {
  ...NavigationDarkTheme,
  ...PaperDarkTheme,
  colors: {
    ...NavigationDarkTheme.colors,
    ...PaperDarkTheme.colors,
    primary: '#FECB37',
    grey: '#0D0D0D',
  }
};

export default function App() {
  const [isDarkMode, setIsDarkMode] = useState(Appearance.getColorScheme() === 'dark');

  let [fontsLoaded] = useFonts({
    Poppins_400Regular,
    Poppins_500Medium,
    Poppins_600SemiBold,
    Poppins_700Bold,
  });

  useEffect(() => {
    const subscription = Appearance.addChangeListener(({ colorScheme }) => {
      setIsDarkMode(colorScheme === 'dark');
    });

    return () => subscription.remove();
  }, []);

  // Add scroll fix for web
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const style = document.createElement('style');
      style.textContent = `
        html, body {
          height: 100%;
          overflow: auto !important;
          -webkit-overflow-scrolling: touch;
        }
        #root {
          height: 100vh;
          overflow: auto !important;
        }
        div[data-reactroot] {
          height: 100vh !important;
          overflow: auto !important;
        }
        ::-webkit-scrollbar {
          width: 8px;
          height: 8px;
        }
        ::-webkit-scrollbar-track {
          background: #2a2a2a;
          border-radius: 4px;
        }
        ::-webkit-scrollbar-thumb {
          background: #FECB37;
          border-radius: 4px;
        }
        ::-webkit-scrollbar-thumb:hover {
          background: #e6b632;
        }
        html {
          scrollbar-width: thin;
          scrollbar-color: #FECB37 #2a2a2a;
        }
      `;
      document.head.appendChild(style);
    }
  }, []);

  if (!fontsLoaded) {
    return <Loading />;
  }

  const theme = isDarkMode ? CustomDarkTheme : CustomDefaultTheme;

  return (
    <I18nextProvider i18n={i18n}>
      <StoreProvider>
        <AxiosProvider>
          <PaperProvider theme={theme}>
            <NavigationContainer theme={theme}>
              <Routes />
            </NavigationContainer>
          </PaperProvider>
        </AxiosProvider>
      </StoreProvider>
    </I18nextProvider>
  );
}