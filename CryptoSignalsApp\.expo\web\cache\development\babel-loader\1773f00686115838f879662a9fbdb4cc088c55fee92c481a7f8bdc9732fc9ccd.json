{"ast": null, "code": "import * as React from 'react';\nexport default React.createContext(undefined);", "map": {"version": 3, "names": ["React", "createContext", "undefined"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\@react-navigation\\bottom-tabs\\src\\utils\\BottomTabBarHeightContext.tsx"], "sourcesContent": ["import * as React from 'react';\n\nexport default React.createContext<number | undefined>(undefined);\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,eAAeA,KAAK,CAACC,aAAa,CAAqBC,SAAS,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}