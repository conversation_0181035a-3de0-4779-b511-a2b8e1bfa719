{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"enabled\"],\n  _excluded2 = [\"visible\", \"children\"];\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport { ResourceSavingView } from '@react-navigation/elements';\nimport * as React from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nvar Screens;\ntry {\n  Screens = require('react-native-screens');\n} catch (e) {}\nexport var MaybeScreenContainer = function MaybeScreenContainer(_ref) {\n  var _Screens, _Screens$screensEnabl;\n  var enabled = _ref.enabled,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  if ((_Screens = Screens) !== null && _Screens !== void 0 && (_Screens$screensEnabl = _Screens.screensEnabled) !== null && _Screens$screensEnabl !== void 0 && _Screens$screensEnabl.call(_Screens)) {\n    return React.createElement(Screens.ScreenContainer, _extends({\n      enabled: enabled\n    }, rest));\n  }\n  return React.createElement(View, rest);\n};\nexport function MaybeScreen(_ref2) {\n  var _Screens2, _Screens2$screensEnab;\n  var visible = _ref2.visible,\n    children = _ref2.children,\n    rest = _objectWithoutProperties(_ref2, _excluded2);\n  if ((_Screens2 = Screens) !== null && _Screens2 !== void 0 && (_Screens2$screensEnab = _Screens2.screensEnabled) !== null && _Screens2$screensEnab !== void 0 && _Screens2$screensEnab.call(_Screens2)) {\n    return React.createElement(Screens.Screen, _extends({\n      activityState: visible ? 2 : 0\n    }, rest), children);\n  }\n  return React.createElement(ResourceSavingView, _extends({\n    visible: visible\n  }, rest), children);\n}", "map": {"version": 3, "names": ["ResourceSavingView", "React", "View", "Screens", "require", "e", "MaybeScreenContainer", "_ref", "_Screens", "_Screens$screensEnabl", "enabled", "rest", "_objectWithoutProperties", "_excluded", "screensEnabled", "call", "createElement", "ScreenContainer", "_extends", "MaybeScreen", "_ref2", "_Screens2", "_Screens2$screensEnab", "visible", "children", "_excluded2", "Screen", "activityState"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\@react-navigation\\bottom-tabs\\src\\views\\ScreenFallback.tsx"], "sourcesContent": ["import { ResourceSavingView } from '@react-navigation/elements';\nimport * as React from 'react';\nimport { StyleProp, View, ViewProps, ViewStyle } from 'react-native';\n\ntype Props = {\n  visible: boolean;\n  children: React.ReactNode;\n  enabled: boolean;\n  freezeOnBlur?: boolean;\n  style?: StyleProp<ViewStyle>;\n};\n\nlet Screens: typeof import('react-native-screens') | undefined;\n\ntry {\n  Screens = require('react-native-screens');\n} catch (e) {\n  // Ignore\n}\n\nexport const MaybeScreenContainer = ({\n  enabled,\n  ...rest\n}: ViewProps & {\n  enabled: boolean;\n  hasTwoStates: boolean;\n  children: React.ReactNode;\n}) => {\n  if (Screens?.screensEnabled?.()) {\n    return <Screens.ScreenContainer enabled={enabled} {...rest} />;\n  }\n\n  return <View {...rest} />;\n};\n\nexport function MaybeScreen({ visible, children, ...rest }: Props) {\n  if (Screens?.screensEnabled?.()) {\n    return (\n      <Screens.Screen activityState={visible ? 2 : 0} {...rest}>\n        {children}\n      </Screens.Screen>\n    );\n  }\n\n  return (\n    <ResourceSavingView visible={visible} {...rest}>\n      {children}\n    </ResourceSavingView>\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;AAAA,SAASA,kBAAkB,QAAQ,4BAA4B;AAC/D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAAA,OAAAC,IAAA;AAW9B,IAAIC,OAA0D;AAE9D,IAAI;EACFA,OAAO,GAAGC,OAAO,CAAC,sBAAsB,CAAC;AAC3C,CAAC,CAAC,OAAOC,CAAC,EAAE,CACV;AAGF,OAAO,IAAMC,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAGC,IAAA,EAO9B;EAAA,IAAAC,QAAA,EAAAC,qBAAA;EAAA,IANJC,OAAO,GAMRH,IAAA,CANCG,OAAO;IACJC,IAAA,GAAAC,wBAAA,CAKJL,IAAA,EAAAM,SAAA;EACC,KAAAL,QAAA,GAAIL,OAAO,cAAAK,QAAA,gBAAAC,qBAAA,GAAPD,QAAA,CAASM,cAAc,cAAAL,qBAAA,eAAvBA,qBAAA,CAAAM,IAAA,CAAAP,QAAA,CAA2B,EAAE;IAC/B,OAAOP,KAAA,CAAAe,aAAA,CAACb,OAAO,CAACc,eAAe,EAAAC,QAAA;MAACR,OAAO,EAAEA;IAAQ,GAAKC,IAAI,EAAI;EAChE;EAEA,OAAOV,KAAA,CAAAe,aAAA,CAACd,IAAI,EAAKS,IAAI,CAAI;AAC3B,CAAC;AAED,OAAO,SAASQ,WAAWA,CAAAC,KAAA,EAAwC;EAAA,IAAAC,SAAA,EAAAC,qBAAA;EAAA,IAArCC,OAAO,GAA4BH,KAAA,CAAnCG,OAAO;IAAEC,QAAQ,GAAkBJ,KAAA,CAA1BI,QAAQ;IAAKb,IAAA,GAAAC,wBAAA,CAAaQ,KAAA,EAAAK,UAAA;EAC/D,KAAAJ,SAAA,GAAIlB,OAAO,cAAAkB,SAAA,gBAAAC,qBAAA,GAAPD,SAAA,CAASP,cAAc,cAAAQ,qBAAA,eAAvBA,qBAAA,CAAAP,IAAA,CAAAM,SAAA,CAA2B,EAAE;IAC/B,OACEpB,KAAA,CAAAe,aAAA,CAACb,OAAO,CAACuB,MAAM,EAAAR,QAAA;MAACS,aAAa,EAAEJ,OAAO,GAAG,CAAC,GAAG;IAAE,GAAKZ,IAAI,GACrDa,QAAQ,CACM;EAErB;EAEA,OACEvB,KAAA,CAAAe,aAAA,CAAChB,kBAAkB,EAAAkB,QAAA;IAACK,OAAO,EAAEA;EAAQ,GAAKZ,IAAI,GAC3Ca,QAAQ,CACU;AAEzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}