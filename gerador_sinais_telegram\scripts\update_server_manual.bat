@echo off
chcp 65001 >nul
echo.
echo ========================================
echo   CRYPTOSIGNALS - ATUALIZAÇÃO MANUAL
echo ========================================
echo.
echo 📋 INSTRUÇÕES PARA ATUALIZAÇÃO MANUAL:
echo.
echo 1️⃣ CONECTAR AO SERVIDOR:
echo    ssh root@38.102.126.144
echo    Senha: h4*ls:FtJw0e
echo.
echo 2️⃣ PARAR O SERVIÇO:
echo    systemctl stop gerador_sinais.service
echo.
echo 3️⃣ NAVEGAR PARA O PROJETO:
echo    cd /opt/gerador_sinais_telegram
echo.
echo 4️⃣ FAZER BACKUP (OPCIONAL):
echo    cp .env .env.backup.$(date +%%Y%%m%%d_%%H%%M%%S)
echo.
echo 5️⃣ ATUALIZAR CÓDIGO:
echo    git stash
echo    git pull origin main
echo.
echo 6️⃣ ATIVAR AMBIENTE VIRTUAL:
echo    source venv/bin/activate
echo.
echo 7️⃣ ATUALIZAR DEPENDÊNCIAS:
echo    pip install --upgrade pip
echo    pip install pandas-ta finta --upgrade
echo.
echo 8️⃣ REINICIAR SERVIÇO:
echo    systemctl daemon-reload
echo    systemctl restart gerador_sinais.service
echo.
echo 9️⃣ VERIFICAR STATUS:
echo    systemctl status gerador_sinais.service
echo.
echo 🔟 VER LOGS:
echo    journalctl -u gerador_sinais.service -f
echo.
echo ========================================
echo   COMANDOS RÁPIDOS PARA COPIAR:
echo ========================================
echo.
echo # Sequência completa de atualização:
echo systemctl stop gerador_sinais.service
echo cd /opt/gerador_sinais_telegram
echo git stash ^&^& git pull origin main
echo source venv/bin/activate
echo pip install --upgrade pip
echo pip install pandas-ta finta --upgrade
echo systemctl daemon-reload
echo systemctl restart gerador_sinais.service
echo systemctl status gerador_sinais.service
echo.
echo ========================================
echo   MONITORAMENTO:
echo ========================================
echo.
echo 📊 Ver status do serviço:
echo    systemctl status gerador_sinais.service
echo.
echo 📋 Ver logs em tempo real:
echo    journalctl -u gerador_sinais.service -f
echo.
echo 📋 Ver últimas 50 linhas de log:
echo    journalctl -u gerador_sinais.service -n 50
echo.
echo 🔄 Reiniciar serviço:
echo    systemctl restart gerador_sinais.service
echo.
echo 🛑 Parar serviço:
echo    systemctl stop gerador_sinais.service
echo.
echo ▶️ Iniciar serviço:
echo    systemctl start gerador_sinais.service
echo.
echo ========================================
echo   TROUBLESHOOTING:
echo ========================================
echo.
echo ❌ Se o serviço não iniciar:
echo    1. Verificar logs: journalctl -u gerador_sinais.service -n 50
echo    2. Verificar arquivo .env: ls -la .env
echo    3. Testar manualmente: source venv/bin/activate ^&^& python main.py
echo.
echo ❌ Se git pull falhar:
echo    1. Verificar mudanças locais: git status
echo    2. Fazer stash: git stash
echo    3. Tentar novamente: git pull origin main
echo.
echo ❌ Se dependências falharem:
echo    1. Atualizar pip: pip install --upgrade pip
echo    2. Instalar individualmente: pip install pandas-ta
echo    3. Verificar ambiente virtual: which python
echo.
echo ========================================
echo.
pause
