{"ast": null, "code": "import * as React from 'react';\nvar LinkingContext = React.createContext({\n  options: undefined\n});\nLinkingContext.displayName = 'LinkingContext';\nexport default LinkingContext;", "map": {"version": 3, "names": ["React", "LinkingContext", "createContext", "options", "undefined", "displayName"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\@react-navigation\\native\\src\\LinkingContext.tsx"], "sourcesContent": ["import type { ParamListBase } from '@react-navigation/core';\nimport * as React from 'react';\n\nimport type { LinkingOptions } from './types';\n\nconst LinkingContext = React.createContext<{\n  options: LinkingOptions<ParamListBase> | undefined;\n}>({ options: undefined });\n\nLinkingContext.displayName = 'LinkingContext';\n\nexport default LinkingContext;\n"], "mappings": "AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAI9B,IAAMC,cAAc,GAAGD,KAAK,CAACE,aAAa,CAEvC;EAAEC,OAAO,EAAEC;AAAU,CAAC,CAAC;AAE1BH,cAAc,CAACI,WAAW,GAAG,gBAAgB;AAE7C,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}