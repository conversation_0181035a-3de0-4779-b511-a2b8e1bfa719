{
  "expo": {
    "name": "Crypto Signals",
    "slug": "crypto-signals",
    "version": "2.0.20",
    "orientation": "portrait",
    "icon": "./assets/icon.png",
    "splash": {
      "image": "./assets/splash.png",
      "resizeMode": "contain",
      "backgroundColor": "#202020"
    },
    "updates": {
      "fallbackToCacheTimeout": 0
    },
    "assetBundlePatterns": ["**/*"],
    "ios": {
      "supportsTablet": true,
      "bundleIdentifier": "com.rafaelsantanna.crypto-signals"
    },
    "android": {
      "package": "com.expoapps.sinais_premium",
      "versionCode": 53,
      "config": {
        "googleMobileAdsAppId": "ca-app-pub-7771385250367915~2608676182"
      },
      "permissions": [],
      "softwareKeyboardLayoutMode": "pan",
      "adaptiveIcon": {
        "foregroundImage": "./assets/icon.png",
        "backgroundColor": "#202020"
      },
      "googleServicesFile": "./google-services.json"
    },
    "web": {
      "favicon": "./assets/favicon.png",
      "bundler": "webpack",
      "build": {
        "babel": {
          "include": ["@expo/vector-icons"]
        }
      }
    },
    "notification": {
      "icon": "./assets/favicon.png"
    },
    "extra": {
      "eas": {
        "projectId": "b9bb6d2d-293d-47a8-a81e-4d0e0b8166b8"
      }
    },
    "androidStatusBar": {
      "backgroundColor": "#202020",
      "translucent": false,
      "barStyle": "light-content"
    },

  }
}
