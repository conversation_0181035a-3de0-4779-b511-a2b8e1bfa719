# Gerador de Sinais Telegram

Este bot gera e monitora sinais de trading para criptomoedas, enviando atualizações via Telegram.

## Funcionalidades

- Geração automática de sinais de trading
- Monitoramento de profits em tempo real
- Prevenção de sinais duplicados
- Tempo limite para validade dos sinais
- Múltiplas estratégias de trading (Scalp, Breakout, Inside Bar, MFI, Swing)
- Respostas de profit diretamente na mensagem original do sinal

## Configurações

O bot utiliza um arquivo `.env` para configuração. Copie o arquivo `.env.example` e renomeie para `.env`:

```bash
cp .env.example .env
```

### Principais Configurações

- `SIGNAL_VALIDITY_MINUTES`: Tempo máximo de validade do sinal (padrão: 30 minutos)
- `MAX_SIGNALS_PER_DAY`: Limite máximo de sinais por dia
- `SIGNAL_INTERVAL_SECONDS`: Intervalo entre verificações de sinais
- `TRADING_START_HOUR` e `TRADING_END_HOUR`: Horário de operação do bot

## Monitoramento de Profits

O bot monitora automaticamente os seguintes níveis de profit para sinais de scalp:
- 40%
- 60%
- 80%
- 100%

Características do monitoramento:
- Tempo limite de 30 minutos para cada sinal
- Notificação automática quando um nível de profit é atingido
- Aviso quando o tempo limite é atingido sem alcançar todos os profits
- Prevenção de sinais duplicados em um intervalo de 5 minutos
- Todas as notificações de profit são enviadas como respostas à mensagem original do sinal

## Respostas de Profit

O bot responde diretamente à mensagem original do sinal quando um nível de profit é atingido, facilitando o acompanhamento dos resultados pelos usuários. Características:

- Todas as atualizações de profit são enviadas como respostas à mensagem original
- Funciona para todos os tipos de sinais (Scalp, Breakout, Inside Bar, MFI, Swing)
- Notificações de Stop Loss também são enviadas como respostas
- Formato de texto simples e direto para melhor legibilidade

## Instalação

1. Clone o repositório
2. Instale as dependências:
```bash
pip install -r requirements.txt
```

3. Configure o arquivo `.env` com suas credenciais
4. Execute o bot:
```bash
python main.py
```

## Logs

O bot mantém logs detalhados em `gerador_sinais.log`, incluindo:
- Sinais gerados
- Profits atingidos
- Tempo limite de sinais
- Erros e avisos
- Envio de mensagens e respostas

## Contribuição

Sinta-se à vontade para contribuir com o projeto através de pull requests ou reportando issues.

## Licença

Este projeto está sob a licença MIT.
