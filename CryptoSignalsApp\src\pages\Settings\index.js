import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Switch, ScrollView, Alert } from 'react-native';
import { Button } from 'react-native-paper';
import Wrapper from '../../components/Wrapper';
import Card from '../../components/Card';
import StatCard from '../../components/StatCard';

const Settings = () => {
  // Theme & Display
  const [isDarkMode, setIsDarkMode] = useState(true);
  const [language, setLanguage] = useState('English');
  const [currency, setCurrency] = useState('USD');

  // Notifications
  const [pushNotifications, setPushNotifications] = useState(true);
  const [emailNotifications, setEmailNotifications] = useState(true);
  const [signalAlerts, setSignalAlerts] = useState(true);
  const [priceAlerts, setPriceAlerts] = useState(false);

  // Security
  const [twoFactorAuth, setTwoFactorAuth] = useState(false);
  const [biometricAuth, setBiometricAuth] = useState(true);
  const [autoLock, setAutoLock] = useState(true);

  // Trading
  const [riskLevel, setRiskLevel] = useState('Medium');
  const [autoTrade, setAutoTrade] = useState(false);
  const [paperTrading, setPaperTrading] = useState(true);

  // User profile data
  const userProfile = {
    name: 'John Trader',
    email: '<EMAIL>',
    memberSince: 'January 2024',
    subscription: 'Premium',
    avatar: '👤'
  };

  // App info
  const appInfo = {
    version: '2.1.0',
    buildNumber: '2024.01.15',
    lastUpdate: 'January 15, 2024'
  };

  // Toggle functions
  const toggleTheme = () => setIsDarkMode(prev => !prev);
  const togglePushNotifications = () => setPushNotifications(prev => !prev);
  const toggleEmailNotifications = () => setEmailNotifications(prev => !prev);
  const toggleSignalAlerts = () => setSignalAlerts(prev => !prev);
  const togglePriceAlerts = () => setPriceAlerts(prev => !prev);
  const toggleTwoFactorAuth = () => setTwoFactorAuth(prev => !prev);
  const toggleBiometricAuth = () => setBiometricAuth(prev => !prev);
  const toggleAutoLock = () => setAutoLock(prev => !prev);
  const toggleAutoTrade = () => setAutoTrade(prev => !prev);
  const togglePaperTrading = () => setPaperTrading(prev => !prev);

  // Action handlers
  const handleLanguageChange = () => {
    Alert.alert("Language", "Language selection coming soon!");
  };

  const handleCurrencyChange = () => {
    Alert.alert("Currency", "Currency selection coming soon!");
  };

  const handleRiskLevelChange = () => {
    Alert.alert("Risk Level", "Risk level configuration coming soon!");
  };

  const handleExportData = () => {
    Alert.alert("Export Data", "Data export feature coming soon!");
  };

  const handleDeleteAccount = () => {
    Alert.alert(
      "Delete Account",
      "Are you sure you want to delete your account? This action cannot be undone.",
      [
        { text: "Cancel", style: "cancel" },
        { text: "Delete", style: "destructive", onPress: () => Alert.alert("Account Deleted", "This is a demo - account not actually deleted.") }
      ]
    );
  };

  const handleCancelSubscription = () => {
    Alert.alert("Cancel Subscription", "Subscription management coming soon!");
  };

  const handleContactSupport = () => {
    Alert.alert("Support", "Contact support feature coming soon!");
  };

  const handlePrivacyPolicy = () => {
    Alert.alert("Privacy Policy", "Privacy policy coming soon!");
  };

  const handleTermsOfService = () => {
    Alert.alert("Terms of Service", "Terms of service coming soon!");
  };

  return (
    <Wrapper>
      <ScrollView style={{ flex: 1 }}>
        {/* Header */}
        <View style={{ padding: 16, paddingBottom: 8 }}>
          <Text style={{
            color: '#fff',
            fontSize: 28,
            fontFamily: 'Poppins_700Bold',
            marginBottom: 4
          }}>
            Settings ⚙️
          </Text>
          <Text style={{
            color: '#8a8a8a',
            fontSize: 14,
            fontFamily: 'Poppins_400Regular'
          }}>
            Customize your trading experience
          </Text>
        </View>

        {/* User Profile Section */}
        <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>
          <Card>
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <View style={{
                backgroundColor: '#FECB37',
                borderRadius: 25,
                width: 50,
                height: 50,
                alignItems: 'center',
                justifyContent: 'center',
                marginRight: 16
              }}>
                <Text style={{ fontSize: 24 }}>{userProfile.avatar}</Text>
              </View>
              <View style={{ flex: 1 }}>
                <Text style={{
                  color: '#fff',
                  fontSize: 18,
                  fontFamily: 'Poppins_600SemiBold',
                  marginBottom: 2
                }}>
                  {userProfile.name}
                </Text>
                <Text style={{
                  color: '#8a8a8a',
                  fontSize: 14,
                  fontFamily: 'Poppins_400Regular',
                  marginBottom: 2
                }}>
                  {userProfile.email}
                </Text>
                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                  <View style={{
                    backgroundColor: '#FECB37',
                    paddingHorizontal: 8,
                    paddingVertical: 2,
                    borderRadius: 4,
                    marginRight: 8
                  }}>
                    <Text style={{
                      color: '#000',
                      fontSize: 10,
                      fontFamily: 'Poppins_600SemiBold'
                    }}>
                      {userProfile.subscription}
                    </Text>
                  </View>
                  <Text style={{
                    color: '#8a8a8a',
                    fontSize: 12,
                    fontFamily: 'Poppins_400Regular'
                  }}>
                    Member since {userProfile.memberSince}
                  </Text>
                </View>
              </View>
              <TouchableOpacity>
                <Text style={{ color: '#FECB37', fontSize: 12, fontFamily: 'Poppins_500Medium' }}>
                  Edit
                </Text>
              </TouchableOpacity>
            </View>
          </Card>
        </View>

        {/* Account Stats */}
        <View style={{ paddingHorizontal: 8, marginBottom: 16 }}>
          <Text style={{
            color: '#fff',
            fontSize: 18,
            fontFamily: 'Poppins_600SemiBold',
            marginBottom: 12,
            paddingHorizontal: 8
          }}>
            Account Overview
          </Text>
          <View style={{ flexDirection: 'row', flexWrap: 'wrap' }}>
            <StatCard
              title="Active Subscriptions"
              value="3"
              subtitle="Channels"
              icon="📺"
            />
            <StatCard
              title="Total Signals"
              value="1,247"
              subtitle="Received"
              icon="📡"
            />
            <StatCard
              title="Success Rate"
              value="78.5%"
              change="+2.1%"
              changeType="positive"
              icon="🎯"
            />
            <StatCard
              title="Account Level"
              value="Premium"
              subtitle="Active"
              icon="💎"
            />
          </View>
        </View>

        {/* Display & Preferences */}
        <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>
          <Text style={{
            color: '#fff',
            fontSize: 18,
            fontFamily: 'Poppins_600SemiBold',
            marginBottom: 12
          }}>
            Display & Preferences 🎨
          </Text>

          <Card style={{ marginBottom: 12 }}>
            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
              <View>
                <Text style={{ color: '#fff', fontSize: 16, fontFamily: 'Poppins_500Medium' }}>Dark Mode</Text>
                <Text style={{ color: '#8a8a8a', fontSize: 12, fontFamily: 'Poppins_400Regular' }}>Use dark theme</Text>
              </View>
              <Switch
                value={isDarkMode}
                onValueChange={toggleTheme}
                thumbColor={isDarkMode ? '#FECB37' : '#ccc'}
                trackColor={{ false: '#333', true: '#FECB37' }}
              />
            </View>

            <TouchableOpacity
              style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}
              onPress={handleLanguageChange}
            >
              <View>
                <Text style={{ color: '#fff', fontSize: 16, fontFamily: 'Poppins_500Medium' }}>Language</Text>
                <Text style={{ color: '#8a8a8a', fontSize: 12, fontFamily: 'Poppins_400Regular' }}>App language</Text>
              </View>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <Text style={{ color: '#FECB37', fontSize: 14, fontFamily: 'Poppins_500Medium', marginRight: 8 }}>
                  {language}
                </Text>
                <Text style={{ color: '#8a8a8a', fontSize: 16 }}>›</Text>
              </View>
            </TouchableOpacity>

            <TouchableOpacity
              style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}
              onPress={handleCurrencyChange}
            >
              <View>
                <Text style={{ color: '#fff', fontSize: 16, fontFamily: 'Poppins_500Medium' }}>Currency</Text>
                <Text style={{ color: '#8a8a8a', fontSize: 12, fontFamily: 'Poppins_400Regular' }}>Display currency</Text>
              </View>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <Text style={{ color: '#FECB37', fontSize: 14, fontFamily: 'Poppins_500Medium', marginRight: 8 }}>
                  {currency}
                </Text>
                <Text style={{ color: '#8a8a8a', fontSize: 16 }}>›</Text>
              </View>
            </TouchableOpacity>
          </Card>
        </View>

        {/* Notifications */}
        <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>
          <Text style={{
            color: '#fff',
            fontSize: 18,
            fontFamily: 'Poppins_600SemiBold',
            marginBottom: 12
          }}>
            Notifications 🔔
          </Text>

          <Card>
            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
              <View>
                <Text style={{ color: '#fff', fontSize: 16, fontFamily: 'Poppins_500Medium' }}>Push Notifications</Text>
                <Text style={{ color: '#8a8a8a', fontSize: 12, fontFamily: 'Poppins_400Regular' }}>Receive push notifications</Text>
              </View>
              <Switch
                value={pushNotifications}
                onValueChange={togglePushNotifications}
                thumbColor={pushNotifications ? '#FECB37' : '#ccc'}
                trackColor={{ false: '#333', true: '#FECB37' }}
              />
            </View>

            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
              <View>
                <Text style={{ color: '#fff', fontSize: 16, fontFamily: 'Poppins_500Medium' }}>Email Notifications</Text>
                <Text style={{ color: '#8a8a8a', fontSize: 12, fontFamily: 'Poppins_400Regular' }}>Receive email updates</Text>
              </View>
              <Switch
                value={emailNotifications}
                onValueChange={toggleEmailNotifications}
                thumbColor={emailNotifications ? '#FECB37' : '#ccc'}
                trackColor={{ false: '#333', true: '#FECB37' }}
              />
            </View>

            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
              <View>
                <Text style={{ color: '#fff', fontSize: 16, fontFamily: 'Poppins_500Medium' }}>Signal Alerts</Text>
                <Text style={{ color: '#8a8a8a', fontSize: 12, fontFamily: 'Poppins_400Regular' }}>New trading signals</Text>
              </View>
              <Switch
                value={signalAlerts}
                onValueChange={toggleSignalAlerts}
                thumbColor={signalAlerts ? '#FECB37' : '#ccc'}
                trackColor={{ false: '#333', true: '#FECB37' }}
              />
            </View>

            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
              <View>
                <Text style={{ color: '#fff', fontSize: 16, fontFamily: 'Poppins_500Medium' }}>Price Alerts</Text>
                <Text style={{ color: '#8a8a8a', fontSize: 12, fontFamily: 'Poppins_400Regular' }}>Price movement alerts</Text>
              </View>
              <Switch
                value={priceAlerts}
                onValueChange={togglePriceAlerts}
                thumbColor={priceAlerts ? '#FECB37' : '#ccc'}
                trackColor={{ false: '#333', true: '#FECB37' }}
              />
            </View>
          </Card>
        </View>

        {/* Security */}
        <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>
          <Text style={{
            color: '#fff',
            fontSize: 18,
            fontFamily: 'Poppins_600SemiBold',
            marginBottom: 12
          }}>
            Security & Privacy 🔒
          </Text>

          <Card>
            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
              <View>
                <Text style={{ color: '#fff', fontSize: 16, fontFamily: 'Poppins_500Medium' }}>Two-Factor Authentication</Text>
                <Text style={{ color: '#8a8a8a', fontSize: 12, fontFamily: 'Poppins_400Regular' }}>Extra security layer</Text>
              </View>
              <Switch
                value={twoFactorAuth}
                onValueChange={toggleTwoFactorAuth}
                thumbColor={twoFactorAuth ? '#FECB37' : '#ccc'}
                trackColor={{ false: '#333', true: '#FECB37' }}
              />
            </View>

            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
              <View>
                <Text style={{ color: '#fff', fontSize: 16, fontFamily: 'Poppins_500Medium' }}>Biometric Authentication</Text>
                <Text style={{ color: '#8a8a8a', fontSize: 12, fontFamily: 'Poppins_400Regular' }}>Fingerprint/Face ID</Text>
              </View>
              <Switch
                value={biometricAuth}
                onValueChange={toggleBiometricAuth}
                thumbColor={biometricAuth ? '#FECB37' : '#ccc'}
                trackColor={{ false: '#333', true: '#FECB37' }}
              />
            </View>

            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
              <View>
                <Text style={{ color: '#fff', fontSize: 16, fontFamily: 'Poppins_500Medium' }}>Auto Lock</Text>
                <Text style={{ color: '#8a8a8a', fontSize: 12, fontFamily: 'Poppins_400Regular' }}>Lock app when inactive</Text>
              </View>
              <Switch
                value={autoLock}
                onValueChange={toggleAutoLock}
                thumbColor={autoLock ? '#FECB37' : '#ccc'}
                trackColor={{ false: '#333', true: '#FECB37' }}
              />
            </View>
          </Card>
        </View>

        {/* Trading Settings */}
        <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>
          <Text style={{
            color: '#fff',
            fontSize: 18,
            fontFamily: 'Poppins_600SemiBold',
            marginBottom: 12
          }}>
            Trading Settings 📈
          </Text>

          <Card>
            <TouchableOpacity
              style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}
              onPress={handleRiskLevelChange}
            >
              <View>
                <Text style={{ color: '#fff', fontSize: 16, fontFamily: 'Poppins_500Medium' }}>Risk Level</Text>
                <Text style={{ color: '#8a8a8a', fontSize: 12, fontFamily: 'Poppins_400Regular' }}>Trading risk tolerance</Text>
              </View>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <Text style={{ color: '#FECB37', fontSize: 14, fontFamily: 'Poppins_500Medium', marginRight: 8 }}>
                  {riskLevel}
                </Text>
                <Text style={{ color: '#8a8a8a', fontSize: 16 }}>›</Text>
              </View>
            </TouchableOpacity>

            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
              <View>
                <Text style={{ color: '#fff', fontSize: 16, fontFamily: 'Poppins_500Medium' }}>Auto Trading</Text>
                <Text style={{ color: '#8a8a8a', fontSize: 12, fontFamily: 'Poppins_400Regular' }}>Automatic signal execution</Text>
              </View>
              <Switch
                value={autoTrade}
                onValueChange={toggleAutoTrade}
                thumbColor={autoTrade ? '#FECB37' : '#ccc'}
                trackColor={{ false: '#333', true: '#FECB37' }}
              />
            </View>

            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
              <View>
                <Text style={{ color: '#fff', fontSize: 16, fontFamily: 'Poppins_500Medium' }}>Paper Trading</Text>
                <Text style={{ color: '#8a8a8a', fontSize: 12, fontFamily: 'Poppins_400Regular' }}>Practice mode</Text>
              </View>
              <Switch
                value={paperTrading}
                onValueChange={togglePaperTrading}
                thumbColor={paperTrading ? '#FECB37' : '#ccc'}
                trackColor={{ false: '#333', true: '#FECB37' }}
              />
            </View>
          </Card>
        </View>

        {/* Support & Legal */}
        <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>
          <Text style={{
            color: '#fff',
            fontSize: 18,
            fontFamily: 'Poppins_600SemiBold',
            marginBottom: 12
          }}>
            Support & Legal 📋
          </Text>

          <Card>
            <TouchableOpacity
              style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}
              onPress={handleContactSupport}
            >
              <View>
                <Text style={{ color: '#fff', fontSize: 16, fontFamily: 'Poppins_500Medium' }}>Contact Support</Text>
                <Text style={{ color: '#8a8a8a', fontSize: 12, fontFamily: 'Poppins_400Regular' }}>Get help and support</Text>
              </View>
              <Text style={{ color: '#8a8a8a', fontSize: 16 }}>›</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}
              onPress={handleExportData}
            >
              <View>
                <Text style={{ color: '#fff', fontSize: 16, fontFamily: 'Poppins_500Medium' }}>Export Data</Text>
                <Text style={{ color: '#8a8a8a', fontSize: 12, fontFamily: 'Poppins_400Regular' }}>Download your data</Text>
              </View>
              <Text style={{ color: '#8a8a8a', fontSize: 16 }}>›</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}
              onPress={handlePrivacyPolicy}
            >
              <View>
                <Text style={{ color: '#fff', fontSize: 16, fontFamily: 'Poppins_500Medium' }}>Privacy Policy</Text>
                <Text style={{ color: '#8a8a8a', fontSize: 12, fontFamily: 'Poppins_400Regular' }}>How we protect your data</Text>
              </View>
              <Text style={{ color: '#8a8a8a', fontSize: 16 }}>›</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}
              onPress={handleTermsOfService}
            >
              <View>
                <Text style={{ color: '#fff', fontSize: 16, fontFamily: 'Poppins_500Medium' }}>Terms of Service</Text>
                <Text style={{ color: '#8a8a8a', fontSize: 12, fontFamily: 'Poppins_400Regular' }}>App usage terms</Text>
              </View>
              <Text style={{ color: '#8a8a8a', fontSize: 16 }}>›</Text>
            </TouchableOpacity>
          </Card>
        </View>

        {/* App Information */}
        <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>
          <Text style={{
            color: '#fff',
            fontSize: 18,
            fontFamily: 'Poppins_600SemiBold',
            marginBottom: 12
          }}>
            App Information ℹ️
          </Text>

          <Card>
            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 12 }}>
              <Text style={{ color: '#8a8a8a', fontSize: 14, fontFamily: 'Poppins_400Regular' }}>Version</Text>
              <Text style={{ color: '#fff', fontSize: 14, fontFamily: 'Poppins_500Medium' }}>{appInfo.version}</Text>
            </View>
            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 12 }}>
              <Text style={{ color: '#8a8a8a', fontSize: 14, fontFamily: 'Poppins_400Regular' }}>Build</Text>
              <Text style={{ color: '#fff', fontSize: 14, fontFamily: 'Poppins_500Medium' }}>{appInfo.buildNumber}</Text>
            </View>
            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
              <Text style={{ color: '#8a8a8a', fontSize: 14, fontFamily: 'Poppins_400Regular' }}>Last Update</Text>
              <Text style={{ color: '#fff', fontSize: 14, fontFamily: 'Poppins_500Medium' }}>{appInfo.lastUpdate}</Text>
            </View>
          </Card>
        </View>

        {/* Danger Zone */}
        <View style={{ paddingHorizontal: 16, marginBottom: 20 }}>
          <Text style={{
            color: '#F44336',
            fontSize: 18,
            fontFamily: 'Poppins_600SemiBold',
            marginBottom: 12
          }}>
            Danger Zone ⚠️
          </Text>

          <Card style={{ borderColor: '#F44336', borderWidth: 1 }}>
            <Button
              mode="outlined"
              onPress={handleCancelSubscription}
              style={{
                borderColor: '#FF9800',
                marginBottom: 12
              }}
              labelStyle={{
                color: '#FF9800',
                fontFamily: 'Poppins_500Medium'
              }}
            >
              Cancel Subscription
            </Button>

            <Button
              mode="outlined"
              onPress={handleDeleteAccount}
              style={{
                borderColor: '#F44336'
              }}
              labelStyle={{
                color: '#F44336',
                fontFamily: 'Poppins_500Medium'
              }}
            >
              Delete Account
            </Button>
          </Card>
        </View>
      </ScrollView>
    </Wrapper>
  );
};

export default Settings;