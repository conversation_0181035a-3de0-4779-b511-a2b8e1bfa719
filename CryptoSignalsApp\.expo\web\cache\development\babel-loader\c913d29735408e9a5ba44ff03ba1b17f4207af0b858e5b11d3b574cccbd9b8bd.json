{"ast": null, "code": "import React from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport styles from \"./styles\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nvar Wrapper = function Wrapper(_ref) {\n  var children = _ref.children;\n  var webStyle = Platform.OS === 'web' ? {\n    height: '100vh',\n    overflow: 'auto',\n    WebkitOverflowScrolling: 'touch'\n  } : {};\n  return _jsx(View, {\n    style: [styles.container, webStyle],\n    children: children\n  });\n};\nexport default Wrapper;", "map": {"version": 3, "names": ["React", "View", "Platform", "styles", "jsx", "_jsx", "Wrapper", "_ref", "children", "webStyle", "OS", "height", "overflow", "WebkitOverflowScrolling", "style", "container"], "sources": ["E:/CryptoSignalsApp/src/components/Wrapper/index.js"], "sourcesContent": ["import React from 'react';\r\nimport { View, Platform } from 'react-native';\r\nimport styles from './styles';\r\n\r\nconst Wrapper = ({ children }) => {\r\n  const webStyle = Platform.OS === 'web' ? {\r\n    height: '100vh',\r\n    overflow: 'auto',\r\n    WebkitOverflowScrolling: 'touch'\r\n  } : {};\r\n\r\n  return (\r\n    <View style={[styles.container, webStyle]}>\r\n      {children}\r\n    </View>\r\n  )\r\n}\r\n\r\nexport default Wrapper\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,QAAA;AAE1B,OAAOC,MAAM;AAAiB,SAAAC,GAAA,IAAAC,IAAA;AAE9B,IAAMC,OAAO,GAAG,SAAVA,OAAOA,CAAAC,IAAA,EAAqB;EAAA,IAAfC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;EACzB,IAAMC,QAAQ,GAAGP,QAAQ,CAACQ,EAAE,KAAK,KAAK,GAAG;IACvCC,MAAM,EAAE,OAAO;IACfC,QAAQ,EAAE,MAAM;IAChBC,uBAAuB,EAAE;EAC3B,CAAC,GAAG,CAAC,CAAC;EAEN,OACER,IAAA,CAACJ,IAAI;IAACa,KAAK,EAAE,CAACX,MAAM,CAACY,SAAS,EAAEN,QAAQ,CAAE;IAAAD,QAAA,EACvCA;EAAQ,CACL,CAAC;AAEX,CAAC;AAED,eAAeF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}