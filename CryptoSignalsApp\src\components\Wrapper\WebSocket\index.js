import React, { useContext, useRef } from 'react';
import { StoreContext } from '../../../store/index'
import WS from 'react-native-websocket';
import { setSignalFromWebsocket } from '../../../store/actions'

const WebSocketWrapper = () => {
  const [ _, dispatch ] = useContext(StoreContext);
  const ref = useRef();

  return (
    <WS
      ref={ref}
      url="ws://mycryptosignals.com:7000"
      onOpen={() => {
        ref.current.send(JSON.stringify({"type": 'client_connected'}))
      }}
      onMessage={(data) => {
        const signal = JSON.parse(data.data);
        if(signal.type === 'signals_created') {
          setSignalFromWebsocket(dispatch, signal.content)
        }
      }}
      onError={err => console.error('WS ERR:', err)}
      onClose={res => console.info('WS CLOSE:', res)}
      reconnect // Will try to reconnect onClose
    />
  )
}

export default WebSocketWrapper
