import asyncio
import logging
import os
import random
from datetime import datetime, timedelta
from utils.signal_formatter import SignalFormatter
from utils.signal_controller import SignalController

# Configuração de logging com encoding UTF-8
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_signal_formatter.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class SignalTester:
    def __init__(self):
        self.formatter = SignalFormatter()
        self.signal_controller = SignalController()
        self.test_results = []
        
        # Lista de pares para teste
        self.test_pairs = [
            "BTCUSDT", "ETHUSDT", "BNBUSDT", "ADAUSDT", "SOLUSDT", 
            "XRPUSDT", "DOGEUSDT", "DOTUSDT", "AVAXUSDT", "LINKUSDT",
            "MATICUSDT", "LTCUSDT", "UNIUSDT", "ATOMUSDT", "ETCUSDT",
            "BCHUSDT", "XLMUSDT", "VETUSDT", "ICPUSDT", "FILUSDT",
            "THETAUSDT", "XMRUSDT", "AXSUSDT", "AAVEUSDT", "EGLDUSDT",
            "NEARUSDT", "KSMUSDT", "FTMUSDT", "HBARUSDT", "XTZUSDT"
        ]

    async def test_duplicate_scalp_signals(self):
        """Testa cenário de sinais duplicados de Scalp"""
        base_signal = {
            'symbol': 'BTCUSDT',
            'type': 'LONG',
            'entry_price': 50000.00,
            'take_profits': {
                40: 50100.00,
                60: 50200.00,
                100: 50300.00
            },
            'leverage': 20
        }
        
        # Primeiro sinal
        timestamp1 = datetime.now()
        is_duplicate = self.signal_controller.is_duplicate_signal(
            base_signal['symbol'],
            base_signal['type'],
            base_signal['entry_price'],
            timestamp1
        )
        logger.info(f"Primeiro sinal BTCUSDT - Duplicado: {is_duplicate}")
        
        # Segundo sinal (mesmo preço, 1 minuto depois)
        await asyncio.sleep(1)
        timestamp2 = datetime.now()
        is_duplicate = self.signal_controller.is_duplicate_signal(
            base_signal['symbol'],
            base_signal['type'],
            base_signal['entry_price'],
            timestamp2
        )
        logger.info(f"Segundo sinal BTCUSDT (mesmo preço) - Duplicado: {is_duplicate}")
        
        # Terceiro sinal (preço ligeiramente diferente)
        base_signal['entry_price'] = 50010.00  # 0.02% diferença
        timestamp3 = datetime.now()
        is_duplicate = self.signal_controller.is_duplicate_signal(
            base_signal['symbol'],
            base_signal['type'],
            base_signal['entry_price'],
            timestamp3
        )
        logger.info(f"Terceiro sinal BTCUSDT (preço próximo) - Duplicado: {is_duplicate}")
        
        # Quarto sinal (preço significativamente diferente)
        base_signal['entry_price'] = 51000.00  # 2% diferença
        timestamp4 = datetime.now()
        is_duplicate = self.signal_controller.is_duplicate_signal(
            base_signal['symbol'],
            base_signal['type'],
            base_signal['entry_price'],
            timestamp4
        )
        logger.info(f"Quarto sinal BTCUSDT (preço diferente) - Duplicado: {is_duplicate}")

    async def test_profit_update_signals(self):
        """Testa cenário de atualizações de profit"""
        symbol = "ETHUSDT"
        entry_price = 3000.00
        profits = [20, 40, 60, 80, 100]
        
        # Teste 1: Atualizações rápidas (devem ser filtradas)
        logger.info("\nTeste 1: Atualizações rápidas de profit")
        for profit in profits:
            can_send = self.signal_controller.can_send_profit_update(symbol, profit)
            logger.info(f"Profit {profit}% para {symbol} - Pode enviar: {can_send}")
            await asyncio.sleep(1)
            
        # Teste 2: Atualizações com intervalo (algumas devem passar)
        logger.info("\nTeste 2: Atualizações com intervalo")
        await asyncio.sleep(5)  # Simula passagem de tempo
        
        test_cases = [
            {"profit": 25, "desc": "Pequena mudança após intervalo"},
            {"profit": 50, "desc": "Grande mudança após intervalo"},
            {"profit": 55, "desc": "Pequena mudança logo em seguida"},
            {"profit": 90, "desc": "Grande mudança logo em seguida"}
        ]
        
        for case in test_cases:
            can_send = self.signal_controller.can_send_profit_update(symbol, case["profit"])
            logger.info(f"{case['desc']} ({case['profit']}%) - Pode enviar: {can_send}")
            await asyncio.sleep(1)

    async def test_cleanup(self):
        """Testa limpeza de sinais antigos"""
        logger.info("\nTeste de limpeza de sinais antigos")
        
        # Adiciona alguns sinais
        symbols = ["BTCUSDT", "ETHUSDT", "BNBUSDT"]
        for symbol in symbols:
            self.signal_controller.is_duplicate_signal(symbol, "LONG", 1000.0)
            self.signal_controller.can_send_profit_update(symbol, 50.0)
        
        # Verifica quantidade inicial
        initial_signals = len(self.signal_controller.recent_signals)
        initial_profits = len(self.signal_controller.profit_updates)
        logger.info(f"Sinais iniciais: {initial_signals}, Profits iniciais: {initial_profits}")
        
        # Limpa sinais antigos (com tempo reduzido para teste)
        self.signal_controller.cleanup_old_signals(max_age_minutes=0)
        
        # Verifica quantidade final
        final_signals = len(self.signal_controller.recent_signals)
        final_profits = len(self.signal_controller.profit_updates)
        logger.info(f"Sinais após limpeza: {final_signals}, Profits após limpeza: {final_profits}")

    async def run_all_tests(self):
        """Executa todos os testes de duplicação"""
        logger.info("Iniciando testes de duplicação de sinais...")
        
        await self.test_duplicate_scalp_signals()
        await asyncio.sleep(2)
        
        await self.test_profit_update_signals()
        await asyncio.sleep(2)
        
        await self.test_cleanup()
        
        logger.info("Testes de duplicação concluídos")

    async def test_scalp_signal(self):
        """Testa a formatação de um sinal de Scalp"""
        test_signal = {
            'symbol': 'BTCUSDT',
            'type': 'LONG',
            'entry_price': 50000.00,
            'take_profits': {
                40: 50100.00,
                60: 50200.00,
                80: 50250.00,
                100: 50300.00
            },
            'leverage': 20
        }
        
        formatted_message = self.formatter.format_scalp_signal(
            symbol=test_signal['symbol'],
            signal_type=test_signal['type'],
            entry_price=test_signal['entry_price'],
            take_profits=test_signal['take_profits'],
            leverage=test_signal['leverage']
        )
        
        logger.info("Testando sinal de Scalp:")
        logger.info(formatted_message)
        
        self.test_results.append({
            'type': 'SCALP',
            'signal': test_signal,
            'formatted_message': formatted_message,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        })

    async def test_breakout_signal(self):
        """Testa a formatação de um sinal de Breakout"""
        test_signal = {
            'symbol': 'ETHUSDT',
            'entry_price': 3000.00,
            'stop_loss': 2950.00,
            'take_profit': 3100.00,
            'leverage': 10
        }
        
        formatted_message = self.formatter.format_breakout_signal(
            symbol=test_signal['symbol'],
            entry_price=test_signal['entry_price'],
            stop_loss=test_signal['stop_loss'],
            take_profit=test_signal['take_profit'],
            leverage=test_signal['leverage']
        )
        
        logger.info("Testando sinal de Breakout:")
        logger.info(formatted_message)
        
        self.test_results.append({
            'type': 'BREAKOUT',
            'signal': test_signal,
            'formatted_message': formatted_message,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        })

    async def test_inside_bar_signal(self):
        """Testa a formatação de um sinal de Inside Bar"""
        test_signal = {
            'symbol': 'BNBUSDT',
            'entry_price': 400.00,
            'stop_loss': 405.00,
            'take_profit': 390.00,
            'leverage': 10
        }
        
        formatted_message = self.formatter.format_inside_bar_signal(
            symbol=test_signal['symbol'],
            entry_price=test_signal['entry_price'],
            stop_loss=test_signal['stop_loss'],
            take_profit=test_signal['take_profit'],
            leverage=test_signal['leverage']
        )
        
        logger.info("Testando sinal de Inside Bar:")
        logger.info(formatted_message)
        
        self.test_results.append({
            'type': 'INSIDE_BAR',
            'signal': test_signal,
            'formatted_message': formatted_message,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        })
        
    async def test_multiple_signals(self):
        """Testa a geração de múltiplos sinais (pelo menos 30)"""
        logger.info("Iniciando teste de múltiplos sinais...")
        
        signal_types = ["SCALP", "BREAKOUT", "INSIDE_BAR"]
        direction_types = ["LONG", "SHORT"]
        leverage_options = [5, 10, 20, 50, 100]
        
        # Gerar pelo menos 30 sinais diferentes
        for i in range(30):
            # Escolher um par aleatório
            symbol = random.choice(self.test_pairs)
            
            # Escolher um tipo de sinal aleatório
            signal_type = random.choice(signal_types)
            
            # Gerar preço base aleatório entre 10 e 50000
            base_price = round(random.uniform(10, 50000), 2)
            
            # Escolher direção aleatória (LONG/SHORT)
            direction = random.choice(direction_types)
            
            # Escolher alavancagem aleatória
            leverage = random.choice(leverage_options)
            
            # Gerar sinal com base no tipo
            try:
                if signal_type == "SCALP":
                    # Calcular take profits
                    take_profits = {}
                    profit_levels = [40, 60, 80, 100]
                    
                    for level in profit_levels:
                        if direction == "LONG":
                            take_profits[level] = round(base_price * (1 + level/1000), 2)
                        else:
                            take_profits[level] = round(base_price * (1 - level/1000), 2)
                    
                    # Formatar sinal
                    formatted_message = self.formatter.format_scalp_signal(
                        symbol=symbol,
                        signal_type=direction,
                        entry_price=base_price,
                        take_profits=take_profits,
                        leverage=leverage
                    )
                    
                    # Verificar duplicação
                    is_duplicate = self.signal_controller.is_duplicate_signal(
                        symbol, direction, base_price
                    )
                    
                    logger.info(f"Sinal {i+1} - {symbol} SCALP {direction} - Duplicado: {is_duplicate}")
                    if not is_duplicate:
                        logger.info(f"Mensagem formatada:\n{formatted_message}")
                
                elif signal_type == "BREAKOUT":
                    # Calcular stop loss e take profit
                    if direction == "LONG":
                        stop_loss = round(base_price * 0.98, 2)  # 2% abaixo
                        take_profit = round(base_price * 1.05, 2)  # 5% acima
                    else:
                        stop_loss = round(base_price * 1.02, 2)  # 2% acima
                        take_profit = round(base_price * 0.95, 2)  # 5% abaixo
                    
                    # Formatar sinal
                    formatted_message = self.formatter.format_breakout_signal(
                        symbol=symbol,
                        entry_price=base_price,
                        stop_loss=stop_loss,
                        take_profit=take_profit,
                        leverage=leverage,
                        signal_type=direction
                    )
                    
                    # Verificar duplicação
                    is_duplicate = self.signal_controller.is_duplicate_signal(
                        symbol, direction, base_price
                    )
                    
                    logger.info(f"Sinal {i+1} - {symbol} BREAKOUT {direction} - Duplicado: {is_duplicate}")
                    if not is_duplicate:
                        logger.info(f"Mensagem formatada:\n{formatted_message}")
                
                else:  # INSIDE_BAR
                    # Calcular stop loss e take profit
                    if direction == "LONG":
                        stop_loss = round(base_price * 0.98, 2)  # 2% abaixo
                        take_profit = round(base_price * 1.05, 2)  # 5% acima
                    else:
                        stop_loss = round(base_price * 1.02, 2)  # 2% acima
                        take_profit = round(base_price * 0.95, 2)  # 5% abaixo
                    
                    # Formatar sinal
                    formatted_message = self.formatter.format_inside_bar_signal(
                        symbol=symbol,
                        entry_price=base_price,
                        stop_loss=stop_loss,
                        take_profit=take_profit,
                        leverage=leverage,
                        signal_type=direction
                    )
                    
                    # Verificar duplicação
                    is_duplicate = self.signal_controller.is_duplicate_signal(
                        symbol, direction, base_price
                    )
                    
                    logger.info(f"Sinal {i+1} - {symbol} INSIDE_BAR {direction} - Duplicado: {is_duplicate}")
                    if not is_duplicate:
                        logger.info(f"Mensagem formatada:\n{formatted_message}")
                
                # Adicionar pequeno delay para não sobrecarregar o log
                await asyncio.sleep(0.1)
                
            except Exception as e:
                logger.error(f"Erro ao gerar sinal {i+1} ({symbol} {signal_type}): {str(e)}")
        
        logger.info("Teste de múltiplos sinais concluído!")

    def save_results_to_markdown(self):
        """Salva os resultados dos testes em um arquivo Markdown"""
        markdown_content = f"""# Resultados dos Testes de Formatação de Sinais
Data e Hora: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## Sinais Testados

"""
        
        for result in self.test_results:
            markdown_content += f"""### Sinal de {result['type']}
**Timestamp:** {result['timestamp']}

**Dados do Sinal:**
```json
{result['signal']}
```

**Mensagem Formatada:**
```
{result['formatted_message']}
```

---
"""
        
        with open('test_results.md', 'w', encoding='utf-8') as f:
            f.write(markdown_content)
        
        logger.info("Resultados salvos em test_results.md")

async def main():
    tester = SignalTester()
    
    try:
        logger.info("Iniciando testes de múltiplos sinais...")
        
        # Testa geração de múltiplos sinais
        await tester.test_multiple_signals()
        
        logger.info("Testes concluídos com sucesso!")
        
    except Exception as e:
        logger.error(f"Erro durante os testes: {str(e)}")

if __name__ == "__main__":
    asyncio.run(main()) 