{"ast": null, "code": "import * as React from 'react';\nexport default React.createContext(null);", "map": {"version": 3, "names": ["React", "createContext"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\@react-navigation\\stack\\src\\utils\\GestureHandlerRefContext.tsx"], "sourcesContent": ["import * as React from 'react';\n\nexport default React.createContext<React.Ref<\n  import('react-native-gesture-handler').PanGestureHandler\n> | null>(null);\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,eAAeA,KAAK,CAACC,aAAa,CAExB,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}