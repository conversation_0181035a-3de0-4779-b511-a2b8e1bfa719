{"ast": null, "code": "import getNamedContext from \"../getNamedContext\";\nvar HeaderBackContext = getNamedContext('HeaderBackContext', undefined);\nexport default HeaderBackContext;", "map": {"version": 3, "names": ["getNamedContext", "HeaderBackContext", "undefined"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\@react-navigation\\elements\\src\\Header\\HeaderBackContext.tsx"], "sourcesContent": ["import getNamedContext from '../getNamedContext';\n\nconst HeaderBackContext = getNamedContext<{ title: string } | undefined>(\n  'HeaderBackContext',\n  undefined\n);\n\nexport default HeaderBackContext;\n"], "mappings": "AAAA,OAAOA,eAAe;AAEtB,IAAMC,iBAAiB,GAAGD,eAAe,CACvC,mBAAmB,EACnBE,SAAS,CACV;AAED,eAAeD,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}