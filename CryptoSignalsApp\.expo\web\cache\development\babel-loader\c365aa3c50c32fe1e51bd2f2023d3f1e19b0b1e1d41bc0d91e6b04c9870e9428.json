{"ast": null, "code": "'use client';\n\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nvar _excluded = [\"active\", \"activityState\", \"style\", \"enabled\"];\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(<PERSON><PERSON><PERSON>, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nimport Animated from \"react-native-web/dist/exports/Animated\";\nimport View from \"react-native-web/dist/exports/View\";\nimport React from 'react';\nimport { screensEnabled } from \"../core\";\nexport var InnerScreen = View;\nexport var NativeScreen = function (_React$Component) {\n  function NativeScreen() {\n    _classCallCheck(this, NativeScreen);\n    return _callSuper(this, NativeScreen, arguments);\n  }\n  _inherits(NativeScreen, _React$Component);\n  return _createClass(NativeScreen, [{\n    key: \"render\",\n    value: function render() {\n      var _this$props = this.props,\n        active = _this$props.active,\n        activityState = _this$props.activityState,\n        style = _this$props.style,\n        _this$props$enabled = _this$props.enabled,\n        enabled = _this$props$enabled === void 0 ? screensEnabled() : _this$props$enabled,\n        rest = _objectWithoutProperties(_this$props, _excluded);\n      if (enabled) {\n        if (active !== undefined && activityState === undefined) {\n          activityState = active !== 0 ? 2 : 0;\n        }\n        return React.createElement(View, _extends({\n          hidden: activityState === 0,\n          style: [style, {\n            display: activityState !== 0 ? 'flex' : 'none'\n          }]\n        }, rest));\n      }\n      return React.createElement(View, rest);\n    }\n  }]);\n}(React.Component);\nvar Screen = Animated.createAnimatedComponent(NativeScreen);\nexport var ScreenContext = React.createContext(Screen);\nexport default Screen;", "map": {"version": 3, "names": ["_objectWithoutProperties", "_classCallCheck", "_createClass", "_possibleConstructorReturn", "_getPrototypeOf", "_inherits", "_excluded", "_callSuper", "t", "o", "e", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_extends", "Object", "assign", "bind", "n", "arguments", "length", "r", "hasOwnProperty", "Animated", "View", "React", "screensEnabled", "InnerScreen", "NativeScreen", "_React$Component", "key", "value", "render", "_this$props", "props", "active", "activityState", "style", "_this$props$enabled", "enabled", "rest", "undefined", "createElement", "hidden", "display", "Component", "Screen", "createAnimatedComponent", "ScreenContext", "createContext"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\react-native-screens\\src\\components\\Screen.web.tsx"], "sourcesContent": ["'use client';\n\nimport { ScreenProps } from '../types';\nimport { Animated, View } from 'react-native';\nimport React from 'react';\n\nimport { screensEnabled } from '../core';\n\nexport const InnerScreen = View;\n\n// We're using class component here because of the error from reanimated:\n// createAnimatedComponent` does not support stateless functional components; use a class component instead.\n// NOTE: React Server Components do not support class components.\nexport class NativeScreen extends React.Component<ScreenProps> {\n  render(): JSX.Element {\n    let {\n      active,\n      activityState,\n      style,\n      enabled = screensEnabled(),\n      ...rest\n    } = this.props;\n\n    if (enabled) {\n      if (active !== undefined && activityState === undefined) {\n        activityState = active !== 0 ? 2 : 0; // change taken from index.native.tsx\n      }\n      return (\n        <View\n          // @ts-expect-error: hidden exists on web, but not in React Native\n          hidden={activityState === 0}\n          style={[style, { display: activityState !== 0 ? 'flex' : 'none' }]}\n          {...rest}\n        />\n      );\n    }\n\n    return <View {...rest} />;\n  }\n}\n\nconst Screen = Animated.createAnimatedComponent(NativeScreen);\n\nexport const ScreenContext = React.createContext(Screen);\n\nexport default Screen;\n"], "mappings": "AAAA,YAAY;;AAAA,OAAAA,wBAAA;AAAA,OAAAC,eAAA;AAAA,OAAAC,YAAA;AAAA,OAAAC,0BAAA;AAAA,OAAAC,eAAA;AAAA,OAAAC,SAAA;AAAA,IAAAC,SAAA;AAAA,SAAAC,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,GAAAL,eAAA,CAAAK,CAAA,GAAAN,0BAAA,CAAAK,CAAA,EAAAG,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAJ,CAAA,EAAAC,CAAA,QAAAN,eAAA,CAAAI,CAAA,EAAAM,WAAA,IAAAL,CAAA,CAAAM,KAAA,CAAAP,CAAA,EAAAE,CAAA;AAAA,SAAAC,0BAAA,cAAAH,CAAA,IAAAQ,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAR,CAAA,aAAAG,yBAAA,YAAAA,0BAAA,aAAAH,CAAA;AAAC,SAAAY,SAAA;EAAA,OAAAA,QAAA,GAAAC,MAAA,CAAAC,MAAA,GAAAD,MAAA,CAAAC,MAAA,CAAAC,IAAA,eAAAC,CAAA;IAAA,SAAAd,CAAA,MAAAA,CAAA,GAAAe,SAAA,CAAAC,MAAA,EAAAhB,CAAA;MAAA,IAAAF,CAAA,GAAAiB,SAAA,CAAAf,CAAA;MAAA,SAAAiB,CAAA,IAAAnB,CAAA,OAAAoB,cAAA,CAAAT,IAAA,CAAAX,CAAA,EAAAmB,CAAA,MAAAH,CAAA,CAAAG,CAAA,IAAAnB,CAAA,CAAAmB,CAAA;IAAA;IAAA,OAAAH,CAAA;EAAA,GAAAJ,QAAA,CAAAL,KAAA,OAAAU,SAAA;AAAA;AAAA,OAAAI,QAAA;AAAA,OAAAC,IAAA;AAIb,OAAOC,KAAK,MAAM,OAAO;AAEzB,SAASC,cAAc;AAEvB,OAAO,IAAMC,WAAW,GAAGH,IAAI;AAK/B,WAAaI,YAAY,aAAAC,gBAAA;EAAA,SAAAD,aAAA;IAAAjC,eAAA,OAAAiC,YAAA;IAAA,OAAA3B,UAAA,OAAA2B,YAAA,EAAAT,SAAA;EAAA;EAAApB,SAAA,CAAA6B,YAAA,EAAAC,gBAAA;EAAA,OAAAjC,YAAA,CAAAgC,YAAA;IAAAE,GAAA;IAAAC,KAAA,EACvB,SAAAC,MAAMA,CAAA,EAAgB;MACpB,IAAAC,WAAA,GAMI,IAAI,CAACC,KAAK;QALZC,MAAM,GAAAF,WAAA,CAANE,MAAM;QACNC,aAAa,GAAAH,WAAA,CAAbG,aAAa;QACbC,KAAK,GAAAJ,WAAA,CAALI,KAAK;QAAAC,mBAAA,GAAAL,WAAA,CACLM,OAAO;QAAPA,OAAO,GAAAD,mBAAA,cAAGZ,cAAc,CAAC,CAAC,GAAAY,mBAAA;QACvBE,IAAA,GAAA9C,wBAAA,CAAAuC,WAAA,EAAAjC,SAAA;MAGL,IAAIuC,OAAO,EAAE;QACX,IAAIJ,MAAM,KAAKM,SAAS,IAAIL,aAAa,KAAKK,SAAS,EAAE;UACvDL,aAAa,GAAGD,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;QACtC;QACA,OACEV,KAAA,CAAAiB,aAAA,CAAClB,IAAA,EACCV,QAAA;UACA6B,MAAM,EAAEP,aAAa,KAAK,CAAE;UAC5BC,KAAK,EAAE,CAACA,KAAK,EAAE;YAAEO,OAAO,EAAER,aAAa,KAAK,CAAC,GAAG,MAAM,GAAG;UAAO,CAAC;QAAE,GAC/DI,IAAI,CACT,CAAC;MAEN;MAEA,OAAOf,KAAA,CAAAiB,aAAA,CAAClB,IAAI,EAAKgB,IAAO,CAAC;IAC3B;EAAA;AAAA,EAzBgCf,KAAK,CAACoB,SAAS;AA4BjD,IAAMC,MAAM,GAAGvB,QAAQ,CAACwB,uBAAuB,CAACnB,YAAY,CAAC;AAE7D,OAAO,IAAMoB,aAAa,GAAGvB,KAAK,CAACwB,aAAa,CAACH,MAAM,CAAC;AAExD,eAAeA,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}