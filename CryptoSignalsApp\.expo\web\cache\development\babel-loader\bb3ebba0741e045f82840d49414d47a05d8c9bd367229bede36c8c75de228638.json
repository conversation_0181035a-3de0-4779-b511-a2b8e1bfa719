{"ast": null, "code": "export { default as Item } from \"./DrawerItem\";\nexport { default as CollapsedItem } from \"./DrawerCollapsedItem\";\nexport { default as Section } from \"./DrawerSection\";", "map": {"version": 3, "names": ["default", "<PERSON><PERSON>", "CollapsedItem", "Section"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\Drawer\\Drawer.tsx"], "sourcesContent": ["// @component ./DrawerItem.tsx\nexport { default as Item } from './DrawerItem';\n\n// @component ./DrawerCollapsedItem.tsx\nexport { default as CollapsedItem } from './DrawerCollapsedItem';\n\n// @component ./DrawerSection.tsx\nexport { default as Section } from './DrawerSection';\n"], "mappings": "AACA,SAASA,OAAO,IAAIC,IAAI;AAGxB,SAASD,OAAO,IAAIE,aAAa;AAGjC,SAASF,OAAO,IAAIG,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}