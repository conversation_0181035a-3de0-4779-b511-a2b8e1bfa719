# 🚀 Scripts de Atualização do Servidor CryptoSignals

Este diretório contém scripts facilitados para atualizar o sistema CryptoSignals no servidor de produção.

## 📋 **Informações do Servidor**

- **IP:** **************
- **Usuário:** root
- **Senha:** h4*ls:FtJw0e
- **Diretório:** /opt/gerador_sinais_telegram

## 🛠️ **Scripts Disponíveis**

### **1. update_server.py** (Recomendado)
**Script Python universal que funciona em qualquer sistema**

```bash
python scripts/update_server.py
```

**Características:**
- ✅ Funciona em Windows, Linux e macOS
- ✅ Instala dependências automaticamente
- ✅ Interface colorida e amigável
- ✅ Tratamento de erros robusto
- ✅ Backup automático das configurações

### **2. update_server.sh** (Linux/macOS)
**Script Bash para sistemas Unix**

```bash
chmod +x scripts/update_server.sh
./scripts/update_server.sh
```

**Requisitos:**
- `sshpass` (instalado automaticamente)
- Sistema Unix (Linux/macOS)

### **3. update_server.ps1** (Windows PowerShell)
**Script PowerShell para Windows**

```powershell
PowerShell -ExecutionPolicy Bypass -File scripts/update_server.ps1
```

**Requisitos:**
- PuTTY (plink.exe)
- Windows PowerShell

### **4. update_server_manual.bat** (Windows Manual)
**Instruções passo-a-passo para atualização manual**

```cmd
scripts/update_server_manual.bat
```

**Características:**
- 📋 Instruções detalhadas
- 📋 Comandos prontos para copiar
- 📋 Troubleshooting incluído

## 🚀 **Processo de Atualização Automática**

Todos os scripts executam os seguintes passos:

1. **🔗 Conectar ao servidor**
2. **🛑 Parar o serviço** CryptoSignals
3. **💾 Fazer backup** das configurações (.env)
4. **📥 Atualizar código** (git pull)
5. **📦 Atualizar dependências** (pip install)
6. **⚙️ Verificar configurações**
7. **🚀 Reiniciar serviço**
8. **📊 Verificar status**
9. **📋 Mostrar logs** recentes

## 📱 **Uso Rápido**

### **Método Mais Simples (Python):**
```bash
python scripts/update_server.py
```

### **Método Manual (SSH direto):**
```bash
ssh root@**************
# Senha: h4*ls:FtJw0e

cd /opt/gerador_sinais_telegram
systemctl stop gerador_sinais.service
git pull origin main
source venv/bin/activate
pip install --upgrade pip pandas-ta finta
systemctl restart gerador_sinais.service
systemctl status gerador_sinais.service
```

## 🔧 **Comandos Úteis de Monitoramento**

### **Ver logs em tempo real:**
```bash
ssh root@**************
journalctl -u gerador_sinais.service -f
```

### **Ver status do serviço:**
```bash
ssh root@**************
systemctl status gerador_sinais.service
```

### **Reiniciar serviço:**
```bash
ssh root@**************
systemctl restart gerador_sinais.service
```

### **Ver últimas 50 linhas de log:**
```bash
ssh root@**************
journalctl -u gerador_sinais.service -n 50
```

## 🚨 **Troubleshooting**

### **Problema: Conexão SSH falha**
**Solução:**
- Verificar conexão com internet
- Confirmar IP do servidor: **************
- Verificar credenciais de acesso

### **Problema: Git pull falha**
**Solução:**
```bash
cd /opt/gerador_sinais_telegram
git status
git stash
git pull origin main
```

### **Problema: Serviço não inicia**
**Solução:**
```bash
# Ver logs de erro
journalctl -u gerador_sinais.service -n 50

# Verificar arquivo .env
ls -la .env

# Testar manualmente
source venv/bin/activate
python main.py
```

### **Problema: Dependências faltando**
**Solução:**
```bash
cd /opt/gerador_sinais_telegram
source venv/bin/activate
pip install --upgrade pip
pip install pandas-ta finta
pip install -r requirements.txt
```

## 📊 **Verificação de Saúde do Sistema**

### **Verificar se o sistema está funcionando:**
```bash
# Status do serviço
systemctl status gerador_sinais.service

# Logs recentes
journalctl -u gerador_sinais.service -n 20

# Verificar processo
ps aux | grep python

# Verificar portas (se aplicável)
netstat -tlnp | grep python
```

## 🔄 **Frequência de Atualização Recomendada**

- **Atualizações críticas:** Imediatamente
- **Novas funcionalidades:** Semanalmente
- **Manutenção preventiva:** Mensalmente

## 📞 **Suporte**

Em caso de problemas:

1. **Verificar logs:** `journalctl -u gerador_sinais.service -f`
2. **Testar conexão:** `ssh root@**************`
3. **Verificar status:** `systemctl status gerador_sinais.service`
4. **Reiniciar serviço:** `systemctl restart gerador_sinais.service`

## 🎯 **Resumo de Comandos Rápidos**

```bash
# Atualização completa em uma linha
ssh root@************** "cd /opt/gerador_sinais_telegram && systemctl stop gerador_sinais.service && git pull && source venv/bin/activate && pip install --upgrade pandas-ta finta && systemctl restart gerador_sinais.service && systemctl status gerador_sinais.service"

# Ver logs
ssh root@************** "journalctl -u gerador_sinais.service -f"

# Status rápido
ssh root@************** "systemctl status gerador_sinais.service"
```

---

**Última atualização:** 2025-01-13
**Versão dos scripts:** 1.0.0
