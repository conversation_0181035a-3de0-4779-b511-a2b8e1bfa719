{"ast": null, "code": "import * as React from 'react';\nvar MISSING_CONTEXT_ERROR = \"Couldn't find a navigation context. Have you wrapped your app with 'NavigationContainer'? See https://reactnavigation.org/docs/getting-started for setup instructions.\";\nexport default React.createContext({\n  isDefault: true,\n  get getKey() {\n    throw new Error(MISSING_CONTEXT_ERROR);\n  },\n  get setKey() {\n    throw new Error(MISSING_CONTEXT_ERROR);\n  },\n  get getState() {\n    throw new Error(MISSING_CONTEXT_ERROR);\n  },\n  get setState() {\n    throw new Error(MISSING_CONTEXT_ERROR);\n  },\n  get getIsInitial() {\n    throw new Error(MISSING_CONTEXT_ERROR);\n  }\n});", "map": {"version": 3, "names": ["React", "MISSING_CONTEXT_ERROR", "createContext", "isDefault", "<PERSON><PERSON><PERSON>", "Error", "<PERSON><PERSON><PERSON>", "getState", "setState", "getIsInitial"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\@react-navigation\\core\\src\\NavigationStateContext.tsx"], "sourcesContent": ["import type { NavigationState, PartialState } from '@react-navigation/routers';\nimport * as React from 'react';\n\nconst MISSING_CONTEXT_ERROR =\n  \"Couldn't find a navigation context. Have you wrapped your app with 'NavigationContainer'? See https://reactnavigation.org/docs/getting-started for setup instructions.\";\n\nexport default React.createContext<{\n  isDefault?: true;\n  state?: NavigationState | PartialState<NavigationState>;\n  getKey: () => string | undefined;\n  setKey: (key: string) => void;\n  getState: () => NavigationState | PartialState<NavigationState> | undefined;\n  setState: (\n    state: NavigationState | PartialState<NavigationState> | undefined\n  ) => void;\n  getIsInitial: () => boolean;\n  addOptionsGetter?: (\n    key: string,\n    getter: () => object | undefined | null\n  ) => void;\n}>({\n  isDefault: true,\n\n  get getKey(): any {\n    throw new Error(MISSING_CONTEXT_ERROR);\n  },\n  get setKey(): any {\n    throw new Error(MISSING_CONTEXT_ERROR);\n  },\n  get getState(): any {\n    throw new Error(MISSING_CONTEXT_ERROR);\n  },\n  get setState(): any {\n    throw new Error(MISSING_CONTEXT_ERROR);\n  },\n  get getIsInitial(): any {\n    throw new Error(MISSING_CONTEXT_ERROR);\n  },\n});\n"], "mappings": "AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,IAAMC,qBAAqB,GACzB,wKAAwK;AAE1K,eAAeD,KAAK,CAACE,aAAa,CAc/B;EACDC,SAAS,EAAE,IAAI;EAEf,IAAIC,MAAMA,CAAA,EAAQ;IAChB,MAAM,IAAIC,KAAK,CAACJ,qBAAqB,CAAC;EACxC,CAAC;EACD,IAAIK,MAAMA,CAAA,EAAQ;IAChB,MAAM,IAAID,KAAK,CAACJ,qBAAqB,CAAC;EACxC,CAAC;EACD,IAAIM,QAAQA,CAAA,EAAQ;IAClB,MAAM,IAAIF,KAAK,CAACJ,qBAAqB,CAAC;EACxC,CAAC;EACD,IAAIO,QAAQA,CAAA,EAAQ;IAClB,MAAM,IAAIH,KAAK,CAACJ,qBAAqB,CAAC;EACxC,CAAC;EACD,IAAIQ,YAAYA,CAAA,EAAQ;IACtB,MAAM,IAAIJ,KAAK,CAACJ,qBAAqB,CAAC;EACxC;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}