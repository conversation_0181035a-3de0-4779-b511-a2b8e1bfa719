{"ast": null, "code": "import StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nvar styles = StyleSheet.create({\n  container: {\n    justifyContent: 'center',\n    alignItems: 'center',\n    flexDirection: 'row',\n    position: 'relative',\n    marginBottom: 12\n  },\n  search: {\n    backgroundColor: '#0D0D0D',\n    borderRadius: 4,\n    color: '#fff',\n    fontSize: 16,\n    paddingHorizontal: 16,\n    paddingTop: 2,\n    flex: 1,\n    height: 50,\n    fontFamily: 'Poppins_500Medium',\n    alignItems: 'center'\n  },\n  searchButton: {\n    width: 22,\n    height: 22,\n    position: 'absolute',\n    right: 16,\n    display: 'flex',\n    justifyContent: 'center',\n    alignItems: 'center'\n  }\n});\nexport default styles;", "map": {"version": 3, "names": ["styles", "StyleSheet", "create", "container", "justifyContent", "alignItems", "flexDirection", "position", "marginBottom", "search", "backgroundColor", "borderRadius", "color", "fontSize", "paddingHorizontal", "paddingTop", "flex", "height", "fontFamily", "searchButton", "width", "right", "display"], "sources": ["C:/Users/<USER>/Desktop/Signals/CryptoSignalsApp/src/components/InputSearch/styles.js"], "sourcesContent": ["import { StyleSheet } from 'react-native';\r\n\r\nconst styles = StyleSheet.create({\r\n  container: {\r\n    justifyContent: 'center',\r\n    alignItems: 'center',\r\n    flexDirection: 'row',\r\n    position: 'relative',\r\n    marginBottom: 12\r\n  },\r\n  search: {\r\n    backgroundColor: '#0D0D0D',\r\n    borderRadius: 4,\r\n    color: '#fff',\r\n    fontSize: 16,\r\n    paddingHorizontal: 16,\r\n    paddingTop: 2,\r\n    flex: 1,\r\n    height: 50,\r\n    fontFamily: 'Poppins_500Medium',\r\n    alignItems: 'center',\r\n  },\r\n  searchButton: {\r\n    width: 22,\r\n    height: 22,\r\n    position: 'absolute',\r\n    right: 16,\r\n    display: 'flex',\r\n    justifyContent: 'center',\r\n    alignItems: 'center',\r\n  }\r\n});\r\n\r\nexport default styles\r\n"], "mappings": ";AAEA,IAAMA,MAAM,GAAGC,UAAU,CAACC,MAAM,CAAC;EAC/BC,SAAS,EAAE;IACTC,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE,QAAQ;IACpBC,aAAa,EAAE,KAAK;IACpBC,QAAQ,EAAE,UAAU;IACpBC,YAAY,EAAE;EAChB,CAAC;EACDC,MAAM,EAAE;IACNC,eAAe,EAAE,SAAS;IAC1BC,YAAY,EAAE,CAAC;IACfC,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,EAAE;IACZC,iBAAiB,EAAE,EAAE;IACrBC,UAAU,EAAE,CAAC;IACbC,IAAI,EAAE,CAAC;IACPC,MAAM,EAAE,EAAE;IACVC,UAAU,EAAE,mBAAmB;IAC/Bb,UAAU,EAAE;EACd,CAAC;EACDc,YAAY,EAAE;IACZC,KAAK,EAAE,EAAE;IACTH,MAAM,EAAE,EAAE;IACVV,QAAQ,EAAE,UAAU;IACpBc,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,MAAM;IACflB,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE;EACd;AACF,CAAC,CAAC;AAEF,eAAeL,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}