{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"children\", \"title\", \"titleStyle\", \"style\", \"theme\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nimport * as React from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport ListSubheader from \"./ListSubheader\";\nimport { useInternalTheme } from \"../../core/theming\";\nvar ListSection = function ListSection(_ref) {\n  var children = _ref.children,\n    title = _ref.title,\n    titleStyle = _ref.titleStyle,\n    style = _ref.style,\n    themeOverrides = _ref.theme,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var theme = useInternalTheme(themeOverrides);\n  var viewProps = _objectSpread(_objectSpread({}, rest), {}, {\n    theme: theme\n  });\n  return React.createElement(View, _extends({}, viewProps, {\n    style: [styles.container, style]\n  }), title ? React.createElement(ListSubheader, {\n    style: titleStyle,\n    theme: theme\n  }, title) : null, children);\n};\nListSection.displayName = 'List.Section';\nvar styles = StyleSheet.create({\n  container: {\n    marginVertical: 8\n  }\n});\nexport default ListSection;", "map": {"version": 3, "names": ["React", "StyleSheet", "View", "ListSubheader", "useInternalTheme", "ListSection", "_ref", "children", "title", "titleStyle", "style", "themeOverrides", "theme", "rest", "_objectWithoutProperties", "_excluded", "viewProps", "_objectSpread", "createElement", "_extends", "styles", "container", "displayName", "create", "marginVertical"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\List\\ListSection.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  StyleProp,\n  StyleSheet,\n  TextStyle,\n  View,\n  ViewStyle,\n} from 'react-native';\n\nimport ListSubheader from './ListSubheader';\nimport { useInternalTheme } from '../../core/theming';\nimport type { ThemeProp } from '../../types';\n\nexport type Props = React.ComponentPropsWithRef<typeof View> & {\n  /**\n   * Title text for the section.\n   */\n  title?: string;\n  /**\n   * Content of the section.\n   */\n  children: React.ReactNode;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n  /**\n   * Style that is passed to Title element.\n   */\n  titleStyle?: StyleProp<TextStyle>;\n  style?: StyleProp<ViewStyle>;\n};\n\n/**\n * A component used to group list items.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { List, MD3Colors } from 'react-native-paper';\n *\n * const MyComponent = () => (\n *   <List.Section>\n *     <List.Subheader>Some title</List.Subheader>\n *     <List.Item title=\"First Item\" left={() => <List.Icon icon=\"folder\" />} />\n *     <List.Item\n *       title=\"Second Item\"\n *       left={() => <List.Icon color={MD3Colors.tertiary70} icon=\"folder\" />}\n *     />\n *   </List.Section>\n * );\n *\n * export default MyComponent;\n * ```\n */\nconst ListSection = ({\n  children,\n  title,\n  titleStyle,\n  style,\n  theme: themeOverrides,\n  ...rest\n}: Props) => {\n  const theme = useInternalTheme(themeOverrides);\n  const viewProps = { ...rest, theme };\n\n  return (\n    <View {...viewProps} style={[styles.container, style]}>\n      {title ? (\n        <ListSubheader style={titleStyle} theme={theme}>\n          {title}\n        </ListSubheader>\n      ) : null}\n      {children}\n    </View>\n  );\n};\n\nListSection.displayName = 'List.Section';\n\nconst styles = StyleSheet.create({\n  container: {\n    marginVertical: 8,\n  },\n});\n\nexport default ListSection;\n"], "mappings": ";;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAS9B,OAAOC,aAAa;AACpB,SAASC,gBAAgB;AA6CzB,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAAC,IAAA,EAOJ;EAAA,IANXC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;IACRC,KAAK,GAAAF,IAAA,CAALE,KAAK;IACLC,UAAU,GAAAH,IAAA,CAAVG,UAAU;IACVC,KAAK,GAAAJ,IAAA,CAALI,KAAK;IACEC,cAAc,GAAAL,IAAA,CAArBM,KAAK;IACFC,IAAA,GAAAC,wBAAA,CAAAR,IAAA,EAAAS,SAAA;EAEH,IAAMH,KAAK,GAAGR,gBAAgB,CAACO,cAAc,CAAC;EAC9C,IAAMK,SAAS,GAAAC,aAAA,CAAAA,aAAA,KAAQJ,IAAI;IAAED,KAAA,EAAAA;EAAA,EAAO;EAEpC,OACEZ,KAAA,CAAAkB,aAAA,CAAChB,IAAI,EAAAiB,QAAA,KAAKH,SAAS;IAAEN,KAAK,EAAE,CAACU,MAAM,CAACC,SAAS,EAAEX,KAAK;EAAE,IACnDF,KAAK,GACJR,KAAA,CAAAkB,aAAA,CAACf,aAAa;IAACO,KAAK,EAAED,UAAW;IAACG,KAAK,EAAEA;EAAM,GAC5CJ,KACY,CAAC,GACd,IAAI,EACPD,QACG,CAAC;AAEX,CAAC;AAEDF,WAAW,CAACiB,WAAW,GAAG,cAAc;AAExC,IAAMF,MAAM,GAAGnB,UAAU,CAACsB,MAAM,CAAC;EAC/BF,SAAS,EAAE;IACTG,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;AAEF,eAAenB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}