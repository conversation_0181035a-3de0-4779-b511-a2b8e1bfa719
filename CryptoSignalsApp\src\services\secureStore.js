// Mock SecureStore for web compatibility

/**
 * Armazena um valor associado a uma chave no armazenamento seguro.
 * @param {string} key - A chave sob a qual o valor será armazenado.
 * @param {any} value - O valor a ser armazenado.
 * @returns {Promise<void>}
 */
async function setSecureStoreItem(key, value) {
  try {
    localStorage.setItem(key, JSON.stringify(value));
  } catch (e) {
    console.error('SET SECURE STORE ITEM', e);
    throw e;
  }
}

/**
 * Recupera um valor associado a uma chave do armazenamento seguro.
 * @param {string} key - A chave do valor a ser recuperado.
 * @returns {Promise<any | null>} - O valor recuperado ou null se não existir.
 */
async function getSecureStoreItem(key) {
  try {
    const item = localStorage.getItem(key);
    return item ? JSON.parse(item) : null;
  } catch (e) {
    console.error('GET SECURE STORE ITEM', e);
    throw e;
  }
}

/**
 * Remove um valor associado a uma chave do armazenamento seguro.
 * @param {string} key - A chave do valor a ser removido.
 * @returns {Promise<void>}
 */
async function removeSecureStoreItem(key) {
  try {
    localStorage.removeItem(key);
  } catch (e) {
    console.error('REMOVE SECURE STORE ITEM', e);
    throw e;
  }
}

export { setSecureStoreItem, getSecureStoreItem, removeSecureStoreItem };
