{"ast": null, "code": "export { default as createBottomTabNavigator } from \"./navigators/createBottomTabNavigator\";\nexport { default as BottomTabBar } from \"./views/BottomTabBar\";\nexport { default as BottomTabView } from \"./views/BottomTabView\";\nexport { default as BottomTabBarHeightCallbackContext } from \"./utils/BottomTabBarHeightCallbackContext\";\nexport { default as BottomTabBarHeightContext } from \"./utils/BottomTabBarHeightContext\";\nexport { default as useBottomTabBarHeight } from \"./utils/useBottomTabBarHeight\";", "map": {"version": 3, "names": ["default", "createBottomTabNavigator", "BottomTabBar", "BottomTabView", "BottomTabBarHeightCallbackContext", "BottomTabBarHeightContext", "useBottomTabBarHeight"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\@react-navigation\\bottom-tabs\\src\\index.tsx"], "sourcesContent": ["/**\n * Navigators\n */\nexport { default as createBottomTabNavigator } from './navigators/createBottomTabNavigator';\n\n/**\n * Views\n */\nexport { default as BottomTabBar } from './views/BottomTabBar';\nexport { default as BottomTabView } from './views/BottomTabView';\n\n/**\n * Utilities\n */\nexport { default as BottomTabBarHeightCallbackContext } from './utils/BottomTabBarHeightCallbackContext';\nexport { default as BottomTabBarHeightContext } from './utils/BottomTabBarHeightContext';\nexport { default as useBottomTabBarHeight } from './utils/useBottomTabBarHeight';\n\n/**\n * Types\n */\nexport type {\n  BottomTabBarButtonProps,\n  BottomTabBarProps,\n  BottomTabHeaderProps,\n  BottomTabNavigationEventMap,\n  BottomTabNavigationOptions,\n  BottomTabNavigationProp,\n  BottomTabScreenProps,\n} from './types';\n"], "mappings": "AAGA,SAASA,OAAO,IAAIC,wBAAwB;AAK5C,SAASD,OAAO,IAAIE,YAAY;AAChC,SAASF,OAAO,IAAIG,aAAa;AAKjC,SAASH,OAAO,IAAII,iCAAiC;AACrD,SAASJ,OAAO,IAAIK,yBAAyB;AAC7C,SAASL,OAAO,IAAIM,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}