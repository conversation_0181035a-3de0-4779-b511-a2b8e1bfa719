{"ast": null, "code": "import Platform from \"react-native-web/dist/exports/Platform\";\nimport { forBottomSheetAndroid, forFadeFromBottomAndroid, forFadeFromCenter as forFadeCard, forHorizontalIOS, forModalPresentationIOS, forRevealFromBottomAndroid, forScaleFromCenterAndroid, forVerticalIOS } from \"./CardStyleInterpolators\";\nimport { forFade } from \"./HeaderStyleInterpolators\";\nimport { BottomSheetSlideInSpec, BottomSheetSlideOutSpec, FadeInFromBottomAndroidSpec, FadeOutToBottomAndroidSpec, RevealFromBottomAndroidSpec, ScaleFromCenterAndroidSpec, TransitionIOSSpec } from \"./TransitionSpecs\";\nvar ANDROID_VERSION_PIE = 28;\nvar ANDROID_VERSION_10 = 29;\nexport var SlideFromRightIOS = {\n  gestureDirection: 'horizontal',\n  transitionSpec: {\n    open: TransitionIOSSpec,\n    close: TransitionIOSSpec\n  },\n  cardStyleInterpolator: forHorizontalIOS,\n  headerStyleInterpolator: forFade\n};\nexport var ModalSlideFromBottomIOS = {\n  gestureDirection: 'vertical',\n  transitionSpec: {\n    open: TransitionIOSSpec,\n    close: TransitionIOSSpec\n  },\n  cardStyleInterpolator: forVerticalIOS,\n  headerStyleInterpolator: forFade\n};\nexport var ModalPresentationIOS = {\n  gestureDirection: 'vertical',\n  transitionSpec: {\n    open: TransitionIOSSpec,\n    close: TransitionIOSSpec\n  },\n  cardStyleInterpolator: forModalPresentationIOS,\n  headerStyleInterpolator: forFade\n};\nexport var FadeFromBottomAndroid = {\n  gestureDirection: 'vertical',\n  transitionSpec: {\n    open: FadeInFromBottomAndroidSpec,\n    close: FadeOutToBottomAndroidSpec\n  },\n  cardStyleInterpolator: forFadeFromBottomAndroid,\n  headerStyleInterpolator: forFade\n};\nexport var RevealFromBottomAndroid = {\n  gestureDirection: 'vertical',\n  transitionSpec: {\n    open: RevealFromBottomAndroidSpec,\n    close: RevealFromBottomAndroidSpec\n  },\n  cardStyleInterpolator: forRevealFromBottomAndroid,\n  headerStyleInterpolator: forFade\n};\nexport var ScaleFromCenterAndroid = {\n  gestureDirection: 'horizontal',\n  transitionSpec: {\n    open: ScaleFromCenterAndroidSpec,\n    close: ScaleFromCenterAndroidSpec\n  },\n  cardStyleInterpolator: forScaleFromCenterAndroid,\n  headerStyleInterpolator: forFade\n};\nexport var BottomSheetAndroid = {\n  gestureDirection: 'vertical',\n  transitionSpec: {\n    open: BottomSheetSlideInSpec,\n    close: BottomSheetSlideOutSpec\n  },\n  cardStyleInterpolator: forBottomSheetAndroid,\n  headerStyleInterpolator: forFade\n};\nexport var ModalFadeTransition = {\n  gestureDirection: 'vertical',\n  transitionSpec: {\n    open: BottomSheetSlideInSpec,\n    close: BottomSheetSlideOutSpec\n  },\n  cardStyleInterpolator: forFadeCard,\n  headerStyleInterpolator: forFade\n};\nexport var DefaultTransition = Platform.select({\n  ios: SlideFromRightIOS,\n  android: Platform.Version >= ANDROID_VERSION_10 ? ScaleFromCenterAndroid : Platform.Version >= ANDROID_VERSION_PIE ? RevealFromBottomAndroid : FadeFromBottomAndroid,\n  default: ScaleFromCenterAndroid\n});\nexport var ModalTransition = Platform.select({\n  ios: ModalPresentationIOS,\n  default: BottomSheetAndroid\n});", "map": {"version": 3, "names": ["forBottomSheetAndroid", "forFadeFromBottomAndroid", "forFadeFromCenter", "forFadeCard", "forHorizontalIOS", "forModalPresentationIOS", "forRevealFromBottomAndroid", "forScaleFromCenterAndroid", "forVerticalIOS", "forFade", "BottomSheetSlideInSpec", "BottomSheetSlideOutSpec", "FadeInFromBottomAndroidSpec", "FadeOutToBottomAndroidSpec", "RevealFromBottomAndroidSpec", "ScaleFromCenterAndroidSpec", "TransitionIOSSpec", "ANDROID_VERSION_PIE", "ANDROID_VERSION_10", "SlideFromRightIOS", "gestureDirection", "transitionSpec", "open", "close", "cardStyleInterpolator", "headerStyleInterpolator", "ModalSlideFromBottomIOS", "ModalPresentationIOS", "FadeFromBottomAndroid", "RevealFromBottomAndroid", "ScaleFromCenterAndroid", "BottomSheetAndroid", "ModalFadeTransition", "DefaultTransition", "Platform", "select", "ios", "android", "Version", "default", "ModalTransition"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\@react-navigation\\stack\\src\\TransitionConfigs\\TransitionPresets.tsx"], "sourcesContent": ["import { Platform } from 'react-native';\n\nimport type { TransitionPreset } from '../types';\nimport {\n  forBottomSheetAndroid,\n  forFadeFromBottomAndroid,\n  forFadeFromCenter as forFadeCard,\n  forHorizontalIOS,\n  forModalPresentationIOS,\n  forRevealFromBottomAndroid,\n  forScaleFromCenterAndroid,\n  forVerticalIOS,\n} from './CardStyleInterpolators';\nimport { forFade } from './HeaderStyleInterpolators';\nimport {\n  BottomSheetSlideInSpec,\n  BottomSheetSlideOutSpec,\n  FadeInFromBottomAndroidSpec,\n  FadeOutToBottomAndroidSpec,\n  RevealFromBottomAndroidSpec,\n  ScaleFromCenterAndroidSpec,\n  TransitionIOSSpec,\n} from './TransitionSpecs';\n\nconst ANDROID_VERSION_PIE = 28;\nconst ANDROID_VERSION_10 = 29;\n\n/**\n * Standard iOS navigation transition.\n */\nexport const SlideFromRightIOS: TransitionPreset = {\n  gestureDirection: 'horizontal',\n  transitionSpec: {\n    open: TransitionIOSSpec,\n    close: TransitionIOSSpec,\n  },\n  cardStyleInterpolator: forHorizontalIOS,\n  headerStyleInterpolator: forFade,\n};\n\n/**\n * Standard iOS navigation transition for modals.\n */\nexport const ModalSlideFromBottomIOS: TransitionPreset = {\n  gestureDirection: 'vertical',\n  transitionSpec: {\n    open: TransitionIOSSpec,\n    close: TransitionIOSSpec,\n  },\n  cardStyleInterpolator: forVerticalIOS,\n  headerStyleInterpolator: forFade,\n};\n\n/**\n * Standard iOS modal presentation style (introduced in iOS 13).\n */\nexport const ModalPresentationIOS: TransitionPreset = {\n  gestureDirection: 'vertical',\n  transitionSpec: {\n    open: TransitionIOSSpec,\n    close: TransitionIOSSpec,\n  },\n  cardStyleInterpolator: forModalPresentationIOS,\n  headerStyleInterpolator: forFade,\n};\n\n/**\n * Standard Android navigation transition when opening or closing an Activity on Android < 9 (Oreo).\n */\nexport const FadeFromBottomAndroid: TransitionPreset = {\n  gestureDirection: 'vertical',\n  transitionSpec: {\n    open: FadeInFromBottomAndroidSpec,\n    close: FadeOutToBottomAndroidSpec,\n  },\n  cardStyleInterpolator: forFadeFromBottomAndroid,\n  headerStyleInterpolator: forFade,\n};\n\n/**\n * Standard Android navigation transition when opening or closing an Activity on Android 9 (Pie).\n */\nexport const RevealFromBottomAndroid: TransitionPreset = {\n  gestureDirection: 'vertical',\n  transitionSpec: {\n    open: RevealFromBottomAndroidSpec,\n    close: RevealFromBottomAndroidSpec,\n  },\n  cardStyleInterpolator: forRevealFromBottomAndroid,\n  headerStyleInterpolator: forFade,\n};\n\n/**\n * Standard Android navigation transition when opening or closing an Activity on Android 10 (Q).\n */\nexport const ScaleFromCenterAndroid: TransitionPreset = {\n  gestureDirection: 'horizontal',\n  transitionSpec: {\n    open: ScaleFromCenterAndroidSpec,\n    close: ScaleFromCenterAndroidSpec,\n  },\n  cardStyleInterpolator: forScaleFromCenterAndroid,\n  headerStyleInterpolator: forFade,\n};\n\n/**\n * Standard bottom sheet slide transition for Android 10.\n */\nexport const BottomSheetAndroid: TransitionPreset = {\n  gestureDirection: 'vertical',\n  transitionSpec: {\n    open: BottomSheetSlideInSpec,\n    close: BottomSheetSlideOutSpec,\n  },\n  cardStyleInterpolator: forBottomSheetAndroid,\n  headerStyleInterpolator: forFade,\n};\n\n/**\n * Fade transition for transparent modals.\n */\nexport const ModalFadeTransition: TransitionPreset = {\n  gestureDirection: 'vertical',\n  transitionSpec: {\n    open: BottomSheetSlideInSpec,\n    close: BottomSheetSlideOutSpec,\n  },\n  cardStyleInterpolator: forFadeCard,\n  headerStyleInterpolator: forFade,\n};\n\n/**\n * Default navigation transition for the current platform.\n */\nexport const DefaultTransition = Platform.select({\n  ios: SlideFromRightIOS,\n  android:\n    Platform.Version >= ANDROID_VERSION_10\n      ? ScaleFromCenterAndroid\n      : Platform.Version >= ANDROID_VERSION_PIE\n      ? RevealFromBottomAndroid\n      : FadeFromBottomAndroid,\n  default: ScaleFromCenterAndroid,\n});\n\n/**\n * Default modal transition for the current platform.\n */\nexport const ModalTransition = Platform.select({\n  ios: ModalPresentationIOS,\n  default: BottomSheetAndroid,\n});\n"], "mappings": ";AAGA,SACEA,qBAAqB,EACrBC,wBAAwB,EACxBC,iBAAiB,IAAIC,WAAW,EAChCC,gBAAgB,EAChBC,uBAAuB,EACvBC,0BAA0B,EAC1BC,yBAAyB,EACzBC,cAAc;AAEhB,SAASC,OAAO;AAChB,SACEC,sBAAsB,EACtBC,uBAAuB,EACvBC,2BAA2B,EAC3BC,0BAA0B,EAC1BC,2BAA2B,EAC3BC,0BAA0B,EAC1BC,iBAAiB;AAGnB,IAAMC,mBAAmB,GAAG,EAAE;AAC9B,IAAMC,kBAAkB,GAAG,EAAE;AAK7B,OAAO,IAAMC,iBAAmC,GAAG;EACjDC,gBAAgB,EAAE,YAAY;EAC9BC,cAAc,EAAE;IACdC,IAAI,EAAEN,iBAAiB;IACvBO,KAAK,EAAEP;EACT,CAAC;EACDQ,qBAAqB,EAAEpB,gBAAgB;EACvCqB,uBAAuB,EAAEhB;AAC3B,CAAC;AAKD,OAAO,IAAMiB,uBAAyC,GAAG;EACvDN,gBAAgB,EAAE,UAAU;EAC5BC,cAAc,EAAE;IACdC,IAAI,EAAEN,iBAAiB;IACvBO,KAAK,EAAEP;EACT,CAAC;EACDQ,qBAAqB,EAAEhB,cAAc;EACrCiB,uBAAuB,EAAEhB;AAC3B,CAAC;AAKD,OAAO,IAAMkB,oBAAsC,GAAG;EACpDP,gBAAgB,EAAE,UAAU;EAC5BC,cAAc,EAAE;IACdC,IAAI,EAAEN,iBAAiB;IACvBO,KAAK,EAAEP;EACT,CAAC;EACDQ,qBAAqB,EAAEnB,uBAAuB;EAC9CoB,uBAAuB,EAAEhB;AAC3B,CAAC;AAKD,OAAO,IAAMmB,qBAAuC,GAAG;EACrDR,gBAAgB,EAAE,UAAU;EAC5BC,cAAc,EAAE;IACdC,IAAI,EAAEV,2BAA2B;IACjCW,KAAK,EAAEV;EACT,CAAC;EACDW,qBAAqB,EAAEvB,wBAAwB;EAC/CwB,uBAAuB,EAAEhB;AAC3B,CAAC;AAKD,OAAO,IAAMoB,uBAAyC,GAAG;EACvDT,gBAAgB,EAAE,UAAU;EAC5BC,cAAc,EAAE;IACdC,IAAI,EAAER,2BAA2B;IACjCS,KAAK,EAAET;EACT,CAAC;EACDU,qBAAqB,EAAElB,0BAA0B;EACjDmB,uBAAuB,EAAEhB;AAC3B,CAAC;AAKD,OAAO,IAAMqB,sBAAwC,GAAG;EACtDV,gBAAgB,EAAE,YAAY;EAC9BC,cAAc,EAAE;IACdC,IAAI,EAAEP,0BAA0B;IAChCQ,KAAK,EAAER;EACT,CAAC;EACDS,qBAAqB,EAAEjB,yBAAyB;EAChDkB,uBAAuB,EAAEhB;AAC3B,CAAC;AAKD,OAAO,IAAMsB,kBAAoC,GAAG;EAClDX,gBAAgB,EAAE,UAAU;EAC5BC,cAAc,EAAE;IACdC,IAAI,EAAEZ,sBAAsB;IAC5Ba,KAAK,EAAEZ;EACT,CAAC;EACDa,qBAAqB,EAAExB,qBAAqB;EAC5CyB,uBAAuB,EAAEhB;AAC3B,CAAC;AAKD,OAAO,IAAMuB,mBAAqC,GAAG;EACnDZ,gBAAgB,EAAE,UAAU;EAC5BC,cAAc,EAAE;IACdC,IAAI,EAAEZ,sBAAsB;IAC5Ba,KAAK,EAAEZ;EACT,CAAC;EACDa,qBAAqB,EAAErB,WAAW;EAClCsB,uBAAuB,EAAEhB;AAC3B,CAAC;AAKD,OAAO,IAAMwB,iBAAiB,GAAGC,QAAQ,CAACC,MAAM,CAAC;EAC/CC,GAAG,EAAEjB,iBAAiB;EACtBkB,OAAO,EACLH,QAAQ,CAACI,OAAO,IAAIpB,kBAAkB,GAClCY,sBAAsB,GACtBI,QAAQ,CAACI,OAAO,IAAIrB,mBAAmB,GACvCY,uBAAuB,GACvBD,qBAAqB;EAC3BW,OAAO,EAAET;AACX,CAAC,CAAC;AAKF,OAAO,IAAMU,eAAe,GAAGN,QAAQ,CAACC,MAAM,CAAC;EAC7CC,GAAG,EAAET,oBAAoB;EACzBY,OAAO,EAAER;AACX,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}