{"ast": null, "code": "import * as React from 'react';\nimport ThemeContext from \"./ThemeContext\";\nexport default function ThemeProvider(_ref) {\n  var value = _ref.value,\n    children = _ref.children;\n  return React.createElement(ThemeContext.Provider, {\n    value: value\n  }, children);\n}", "map": {"version": 3, "names": ["React", "ThemeContext", "ThemeProvider", "_ref", "value", "children", "createElement", "Provider"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\@react-navigation\\native\\src\\theming\\ThemeProvider.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport type { Theme } from '../types';\nimport ThemeContext from './ThemeContext';\n\ntype Props = {\n  value: Theme;\n  children: React.ReactNode;\n};\n\nexport default function ThemeProvider({ value, children }: Props) {\n  return (\n    <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>\n  );\n}\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAG9B,OAAOC,YAAY;AAOnB,eAAe,SAASC,aAAaA,CAAAC,IAAA,EAA6B;EAAA,IAA1BC,KAAK,GAAmBD,IAAA,CAAxBC,KAAK;IAAEC,QAAA,GAAiBF,IAAA,CAAjBE,QAAA;EAC7C,OACEL,KAAA,CAAAM,aAAA,CAACL,YAAY,CAACM,QAAQ;IAACH,KAAK,EAAEA;EAAM,GAAEC,QAAQ,CAAyB;AAE3E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}