{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"children\", \"enterTouchDelay\", \"leaveTouchDelay\", \"title\", \"theme\", \"titleMaxFontSizeMultiplier\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nimport * as React from 'react';\nimport Dimensions from \"react-native-web/dist/exports/Dimensions\";\nimport View from \"react-native-web/dist/exports/View\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport Pressable from \"react-native-web/dist/exports/Pressable\";\nimport { getTooltipPosition } from \"./utils\";\nimport { useInternalTheme } from \"../../core/theming\";\nimport { addEventListener } from \"../../utils/addEventListener\";\nimport Portal from \"../Portal/Portal\";\nimport Text from \"../Typography/Text\";\nvar Tooltip = function Tooltip(_ref3) {\n  var children = _ref3.children,\n    _ref3$enterTouchDelay = _ref3.enterTouchDelay,\n    enterTouchDelay = _ref3$enterTouchDelay === void 0 ? 500 : _ref3$enterTouchDelay,\n    _ref3$leaveTouchDelay = _ref3.leaveTouchDelay,\n    leaveTouchDelay = _ref3$leaveTouchDelay === void 0 ? 1500 : _ref3$leaveTouchDelay,\n    title = _ref3.title,\n    themeOverrides = _ref3.theme,\n    titleMaxFontSizeMultiplier = _ref3.titleMaxFontSizeMultiplier,\n    rest = _objectWithoutProperties(_ref3, _excluded);\n  var isWeb = Platform.OS === 'web';\n  var theme = useInternalTheme(themeOverrides);\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    visible = _React$useState2[0],\n    setVisible = _React$useState2[1];\n  var _React$useState3 = React.useState({\n      children: {},\n      tooltip: {},\n      measured: false\n    }),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    measurement = _React$useState4[0],\n    setMeasurement = _React$useState4[1];\n  var showTooltipTimer = React.useRef([]);\n  var hideTooltipTimer = React.useRef([]);\n  var childrenWrapperRef = React.useRef(null);\n  var touched = React.useRef(false);\n  var isValidChild = React.useMemo(function () {\n    return React.isValidElement(children);\n  }, [children]);\n  React.useEffect(function () {\n    return function () {\n      if (showTooltipTimer.current.length) {\n        showTooltipTimer.current.forEach(function (t) {\n          return clearTimeout(t);\n        });\n        showTooltipTimer.current = [];\n      }\n      if (hideTooltipTimer.current.length) {\n        hideTooltipTimer.current.forEach(function (t) {\n          return clearTimeout(t);\n        });\n        hideTooltipTimer.current = [];\n      }\n    };\n  }, []);\n  React.useEffect(function () {\n    var subscription = addEventListener(Dimensions, 'change', function () {\n      return setVisible(false);\n    });\n    return function () {\n      return subscription.remove();\n    };\n  }, []);\n  var handleTouchStart = React.useCallback(function () {\n    if (hideTooltipTimer.current.length) {\n      hideTooltipTimer.current.forEach(function (t) {\n        return clearTimeout(t);\n      });\n      hideTooltipTimer.current = [];\n    }\n    if (isWeb) {\n      var id = setTimeout(function () {\n        touched.current = true;\n        setVisible(true);\n      }, enterTouchDelay);\n      showTooltipTimer.current.push(id);\n    } else {\n      touched.current = true;\n      setVisible(true);\n    }\n  }, [isWeb, enterTouchDelay]);\n  var handleTouchEnd = React.useCallback(function () {\n    touched.current = false;\n    if (showTooltipTimer.current.length) {\n      showTooltipTimer.current.forEach(function (t) {\n        return clearTimeout(t);\n      });\n      showTooltipTimer.current = [];\n    }\n    var id = setTimeout(function () {\n      setVisible(false);\n      setMeasurement({\n        children: {},\n        tooltip: {},\n        measured: false\n      });\n    }, leaveTouchDelay);\n    hideTooltipTimer.current.push(id);\n  }, [leaveTouchDelay]);\n  var handlePress = React.useCallback(function () {\n    var _props$onPress;\n    if (touched.current) {\n      return null;\n    }\n    if (!isValidChild) return null;\n    var props = children.props;\n    if (props.disabled) return null;\n    return (_props$onPress = props.onPress) === null || _props$onPress === void 0 ? void 0 : _props$onPress.call(props);\n  }, [children.props, isValidChild]);\n  var handleHoverIn = React.useCallback(function () {\n    handleTouchStart();\n    if (isValidChild) {\n      var _onHoverIn, _ref;\n      (_onHoverIn = (_ref = children.props).onHoverIn) === null || _onHoverIn === void 0 || _onHoverIn.call(_ref);\n    }\n  }, [children.props, handleTouchStart, isValidChild]);\n  var handleHoverOut = React.useCallback(function () {\n    handleTouchEnd();\n    if (isValidChild) {\n      var _onHoverOut, _ref2;\n      (_onHoverOut = (_ref2 = children.props).onHoverOut) === null || _onHoverOut === void 0 || _onHoverOut.call(_ref2);\n    }\n  }, [children.props, handleTouchEnd, isValidChild]);\n  var handleOnLayout = function handleOnLayout(_ref4) {\n    var layout = _ref4.nativeEvent.layout;\n    var _childrenWrapperRef$c;\n    (_childrenWrapperRef$c = childrenWrapperRef.current) === null || _childrenWrapperRef$c === void 0 || _childrenWrapperRef$c.measure(function (_x, _y, width, height, pageX, pageY) {\n      setMeasurement({\n        children: {\n          pageX: pageX,\n          pageY: pageY,\n          height: height,\n          width: width\n        },\n        tooltip: _objectSpread({}, layout),\n        measured: true\n      });\n    });\n  };\n  var mobilePressProps = {\n    onPress: handlePress,\n    onLongPress: function onLongPress() {\n      return handleTouchStart();\n    },\n    onPressOut: function onPressOut() {\n      return handleTouchEnd();\n    },\n    delayLongPress: enterTouchDelay\n  };\n  var webPressProps = {\n    onHoverIn: handleHoverIn,\n    onHoverOut: handleHoverOut\n  };\n  return React.createElement(React.Fragment, null, visible && React.createElement(Portal, null, React.createElement(View, {\n    onLayout: handleOnLayout,\n    style: [styles.tooltip, _objectSpread(_objectSpread({\n      backgroundColor: theme.isV3 ? theme.colors.onSurface : theme.colors.tooltip\n    }, getTooltipPosition(measurement, children)), {}, {\n      borderRadius: theme.roundness\n    }, measurement.measured ? styles.visible : styles.hidden)],\n    testID: \"tooltip-container\"\n  }, React.createElement(Text, {\n    accessibilityLiveRegion: \"polite\",\n    numberOfLines: 1,\n    selectable: false,\n    variant: \"labelLarge\",\n    style: {\n      color: theme.colors.surface\n    },\n    maxFontSizeMultiplier: titleMaxFontSizeMultiplier\n  }, title))), React.createElement(Pressable, _extends({\n    ref: childrenWrapperRef,\n    style: styles.pressContainer\n  }, isWeb ? webPressProps : mobilePressProps), React.cloneElement(children, _objectSpread(_objectSpread({}, rest), isWeb ? webPressProps : mobilePressProps))));\n};\nTooltip.displayName = 'Tooltip';\nvar styles = StyleSheet.create({\n  tooltip: {\n    alignSelf: 'flex-start',\n    justifyContent: 'center',\n    paddingHorizontal: 16,\n    height: 32,\n    maxHeight: 32\n  },\n  visible: {\n    opacity: 1\n  },\n  hidden: {\n    opacity: 0\n  },\n  pressContainer: _objectSpread({}, Platform.OS === 'web' && {\n    cursor: 'default'\n  })\n});\nexport default Tooltip;", "map": {"version": 3, "names": ["React", "Dimensions", "View", "StyleSheet", "Platform", "Pressable", "getTooltipPosition", "useInternalTheme", "addEventListener", "Portal", "Text", "<PERSON><PERSON><PERSON>", "_ref3", "children", "_ref3$enterTouchDelay", "enterTouchDelay", "_ref3$leaveTouchDelay", "leaveTouchDelay", "title", "themeOverrides", "theme", "titleMaxFontSizeMultiplier", "rest", "_objectWithoutProperties", "_excluded", "isWeb", "OS", "_React$useState", "useState", "_React$useState2", "_slicedToArray", "visible", "setVisible", "_React$useState3", "tooltip", "measured", "_React$useState4", "measurement", "setMeasurement", "showTooltipTimer", "useRef", "hideTooltipTimer", "childrenWrapperRef", "touched", "is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useMemo", "isValidElement", "useEffect", "current", "length", "for<PERSON>ach", "t", "clearTimeout", "subscription", "remove", "handleTouchStart", "useCallback", "id", "setTimeout", "push", "handleTouchEnd", "handlePress", "_props$onPress", "props", "disabled", "onPress", "call", "handleHoverIn", "_onHoverIn", "_ref", "onHoverIn", "handleHoverOut", "_onHoverOut", "_ref2", "onHoverOut", "handleOnLayout", "_ref4", "layout", "nativeEvent", "_childrenWrapperRef$c", "measure", "_x", "_y", "width", "height", "pageX", "pageY", "_objectSpread", "mobilePressProps", "onLongPress", "onPressOut", "delayLongPress", "webPressProps", "createElement", "Fragment", "onLayout", "style", "styles", "backgroundColor", "isV3", "colors", "onSurface", "borderRadius", "roundness", "hidden", "testID", "accessibilityLiveRegion", "numberOfLines", "selectable", "variant", "color", "surface", "maxFontSizeMultiplier", "_extends", "ref", "pressContainer", "cloneElement", "displayName", "create", "alignSelf", "justifyContent", "paddingHorizontal", "maxHeight", "opacity", "cursor"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\Tooltip\\Tooltip.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  Dimensions,\n  View,\n  LayoutChangeEvent,\n  StyleSheet,\n  Platform,\n  Pressable,\n  ViewStyle,\n} from 'react-native';\n\nimport type { ThemeProp } from 'src/types';\n\nimport { getTooltipPosition, Measurement, TooltipChildProps } from './utils';\nimport { useInternalTheme } from '../../core/theming';\nimport { addEventListener } from '../../utils/addEventListener';\nimport Portal from '../Portal/Portal';\nimport Text from '../Typography/Text';\n\nexport type Props = {\n  /**\n   * Tooltip reference element. Needs to be able to hold a ref.\n   */\n  children: React.ReactElement;\n  /**\n   * The number of milliseconds a user must touch the element before showing the tooltip.\n   */\n  enterTouchDelay?: number;\n  /**\n   * The number of milliseconds after the user stops touching an element before hiding the tooltip.\n   */\n  leaveTouchDelay?: number;\n  /**\n   * Tooltip title\n   */\n  title: string;\n  /**\n   * Specifies the largest possible scale a title font can reach.\n   */\n  titleMaxFontSizeMultiplier?: number;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n};\n\n/**\n * Tooltips display informative text when users hover over, focus on, or tap an element.\n *\n * Plain tooltips, when activated, display a text label identifying an element, such as a description of its function. Tooltips should include only short, descriptive text and avoid restating visible UI text.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { IconButton, Tooltip } from 'react-native-paper';\n *\n * const MyComponent = () => (\n *   <Tooltip title=\"Selected Camera\">\n *     <IconButton icon=\"camera\" selected size={24} onPress={() => {}} />\n *   </Tooltip>\n * );\n *\n * export default MyComponent;\n * ```\n */\nconst Tooltip = ({\n  children,\n  enterTouchDelay = 500,\n  leaveTouchDelay = 1500,\n  title,\n  theme: themeOverrides,\n  titleMaxFontSizeMultiplier,\n  ...rest\n}: Props) => {\n  const isWeb = Platform.OS === 'web';\n\n  const theme = useInternalTheme(themeOverrides);\n  const [visible, setVisible] = React.useState(false);\n\n  const [measurement, setMeasurement] = React.useState({\n    children: {},\n    tooltip: {},\n    measured: false,\n  });\n  const showTooltipTimer = React.useRef<NodeJS.Timeout[]>([]);\n  const hideTooltipTimer = React.useRef<NodeJS.Timeout[]>([]);\n\n  const childrenWrapperRef = React.useRef<View>(null);\n  const touched = React.useRef(false);\n\n  const isValidChild = React.useMemo(\n    () => React.isValidElement<TooltipChildProps>(children),\n    [children]\n  );\n\n  React.useEffect(() => {\n    return () => {\n      if (showTooltipTimer.current.length) {\n        showTooltipTimer.current.forEach((t) => clearTimeout(t));\n        showTooltipTimer.current = [];\n      }\n\n      if (hideTooltipTimer.current.length) {\n        hideTooltipTimer.current.forEach((t) => clearTimeout(t));\n        hideTooltipTimer.current = [];\n      }\n    };\n  }, []);\n\n  React.useEffect(() => {\n    const subscription = addEventListener(Dimensions, 'change', () =>\n      setVisible(false)\n    );\n\n    return () => subscription.remove();\n  }, []);\n\n  const handleTouchStart = React.useCallback(() => {\n    if (hideTooltipTimer.current.length) {\n      hideTooltipTimer.current.forEach((t) => clearTimeout(t));\n      hideTooltipTimer.current = [];\n    }\n\n    if (isWeb) {\n      let id = setTimeout(() => {\n        touched.current = true;\n        setVisible(true);\n      }, enterTouchDelay) as unknown as NodeJS.Timeout;\n      showTooltipTimer.current.push(id);\n    } else {\n      touched.current = true;\n      setVisible(true);\n    }\n  }, [isWeb, enterTouchDelay]);\n\n  const handleTouchEnd = React.useCallback(() => {\n    touched.current = false;\n    if (showTooltipTimer.current.length) {\n      showTooltipTimer.current.forEach((t) => clearTimeout(t));\n      showTooltipTimer.current = [];\n    }\n\n    let id = setTimeout(() => {\n      setVisible(false);\n      setMeasurement({ children: {}, tooltip: {}, measured: false });\n    }, leaveTouchDelay) as unknown as NodeJS.Timeout;\n    hideTooltipTimer.current.push(id);\n  }, [leaveTouchDelay]);\n\n  const handlePress = React.useCallback(() => {\n    if (touched.current) {\n      return null;\n    }\n    if (!isValidChild) return null;\n    const props = children.props as TooltipChildProps;\n    if (props.disabled) return null;\n    return props.onPress?.();\n  }, [children.props, isValidChild]);\n\n  const handleHoverIn = React.useCallback(() => {\n    handleTouchStart();\n    if (isValidChild) {\n      (children.props as TooltipChildProps).onHoverIn?.();\n    }\n  }, [children.props, handleTouchStart, isValidChild]);\n\n  const handleHoverOut = React.useCallback(() => {\n    handleTouchEnd();\n    if (isValidChild) {\n      (children.props as TooltipChildProps).onHoverOut?.();\n    }\n  }, [children.props, handleTouchEnd, isValidChild]);\n\n  const handleOnLayout = ({ nativeEvent: { layout } }: LayoutChangeEvent) => {\n    childrenWrapperRef.current?.measure(\n      (_x, _y, width, height, pageX, pageY) => {\n        setMeasurement({\n          children: { pageX, pageY, height, width },\n          tooltip: { ...layout },\n          measured: true,\n        });\n      }\n    );\n  };\n\n  const mobilePressProps = {\n    onPress: handlePress,\n    onLongPress: () => handleTouchStart(),\n    onPressOut: () => handleTouchEnd(),\n    delayLongPress: enterTouchDelay,\n  };\n\n  const webPressProps = {\n    onHoverIn: handleHoverIn,\n    onHoverOut: handleHoverOut,\n  };\n\n  return (\n    <>\n      {visible && (\n        <Portal>\n          <View\n            onLayout={handleOnLayout}\n            style={[\n              styles.tooltip,\n              {\n                backgroundColor: theme.isV3\n                  ? theme.colors.onSurface\n                  : theme.colors.tooltip,\n                ...getTooltipPosition(\n                  measurement as Measurement,\n                  children as React.ReactElement<TooltipChildProps>\n                ),\n                borderRadius: theme.roundness,\n                ...(measurement.measured ? styles.visible : styles.hidden),\n              },\n            ]}\n            testID=\"tooltip-container\"\n          >\n            <Text\n              accessibilityLiveRegion=\"polite\"\n              numberOfLines={1}\n              selectable={false}\n              variant=\"labelLarge\"\n              style={{ color: theme.colors.surface }}\n              maxFontSizeMultiplier={titleMaxFontSizeMultiplier}\n            >\n              {title}\n            </Text>\n          </View>\n        </Portal>\n      )}\n      <Pressable\n        ref={childrenWrapperRef}\n        style={styles.pressContainer}\n        {...(isWeb ? webPressProps : mobilePressProps)}\n      >\n        {React.cloneElement(children, {\n          ...rest,\n          ...(isWeb ? webPressProps : mobilePressProps),\n        })}\n      </Pressable>\n    </>\n  );\n};\n\nTooltip.displayName = 'Tooltip';\n\nconst styles = StyleSheet.create({\n  tooltip: {\n    alignSelf: 'flex-start',\n    justifyContent: 'center',\n    paddingHorizontal: 16,\n    height: 32,\n    maxHeight: 32,\n  },\n  visible: {\n    opacity: 1,\n  },\n  hidden: {\n    opacity: 0,\n  },\n  pressContainer: {\n    ...(Platform.OS === 'web' && { cursor: 'default' }),\n  } as ViewStyle,\n});\n\nexport default Tooltip;\n"], "mappings": ";;;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,QAAA;AAAA,OAAAC,SAAA;AAa9B,SAASC,kBAAkB;AAC3B,SAASC,gBAAgB;AACzB,SAASC,gBAAgB;AACzB,OAAOC,MAAM;AACb,OAAOC,IAAI;AAgDX,IAAMC,OAAO,GAAG,SAAVA,OAAOA,CAAAC,KAAA,EAQA;EAAA,IAPXC,QAAQ,GAAAD,KAAA,CAARC,QAAQ;IAAAC,qBAAA,GAAAF,KAAA,CACRG,eAAe;IAAfA,eAAe,GAAAD,qBAAA,cAAG,GAAG,GAAAA,qBAAA;IAAAE,qBAAA,GAAAJ,KAAA,CACrBK,eAAe;IAAfA,eAAe,GAAAD,qBAAA,cAAG,IAAI,GAAAA,qBAAA;IACtBE,KAAK,GAAAN,KAAA,CAALM,KAAK;IACEC,cAAc,GAAAP,KAAA,CAArBQ,KAAK;IACLC,0BAA0B,GAAAT,KAAA,CAA1BS,0BAA0B;IACvBC,IAAA,GAAAC,wBAAA,CAAAX,KAAA,EAAAY,SAAA;EAEH,IAAMC,KAAK,GAAGrB,QAAQ,CAACsB,EAAE,KAAK,KAAK;EAEnC,IAAMN,KAAK,GAAGb,gBAAgB,CAACY,cAAc,CAAC;EAC9C,IAAAQ,eAAA,GAA8B3B,KAAK,CAAC4B,QAAQ,CAAC,KAAK,CAAC;IAAAC,gBAAA,GAAAC,cAAA,CAAAH,eAAA;IAA5CI,OAAO,GAAAF,gBAAA;IAAEG,UAAU,GAAAH,gBAAA;EAE1B,IAAAI,gBAAA,GAAsCjC,KAAK,CAAC4B,QAAQ,CAAC;MACnDf,QAAQ,EAAE,CAAC,CAAC;MACZqB,OAAO,EAAE,CAAC,CAAC;MACXC,QAAQ,EAAE;IACZ,CAAC,CAAC;IAAAC,gBAAA,GAAAN,cAAA,CAAAG,gBAAA;IAJKI,WAAW,GAAAD,gBAAA;IAAEE,cAAc,GAAAF,gBAAA;EAKlC,IAAMG,gBAAgB,GAAGvC,KAAK,CAACwC,MAAM,CAAmB,EAAE,CAAC;EAC3D,IAAMC,gBAAgB,GAAGzC,KAAK,CAACwC,MAAM,CAAmB,EAAE,CAAC;EAE3D,IAAME,kBAAkB,GAAG1C,KAAK,CAACwC,MAAM,CAAO,IAAI,CAAC;EACnD,IAAMG,OAAO,GAAG3C,KAAK,CAACwC,MAAM,CAAC,KAAK,CAAC;EAEnC,IAAMI,YAAY,GAAG5C,KAAK,CAAC6C,OAAO,CAChC;IAAA,OAAM7C,KAAK,CAAC8C,cAAc,CAAoBjC,QAAQ,CAAC;EAAA,GACvD,CAACA,QAAQ,CACX,CAAC;EAEDb,KAAK,CAAC+C,SAAS,CAAC,YAAM;IACpB,OAAO,YAAM;MACX,IAAIR,gBAAgB,CAACS,OAAO,CAACC,MAAM,EAAE;QACnCV,gBAAgB,CAACS,OAAO,CAACE,OAAO,CAAE,UAAAC,CAAC;UAAA,OAAKC,YAAY,CAACD,CAAC,CAAC;QAAA,EAAC;QACxDZ,gBAAgB,CAACS,OAAO,GAAG,EAAE;MAC/B;MAEA,IAAIP,gBAAgB,CAACO,OAAO,CAACC,MAAM,EAAE;QACnCR,gBAAgB,CAACO,OAAO,CAACE,OAAO,CAAE,UAAAC,CAAC;UAAA,OAAKC,YAAY,CAACD,CAAC,CAAC;QAAA,EAAC;QACxDV,gBAAgB,CAACO,OAAO,GAAG,EAAE;MAC/B;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAENhD,KAAK,CAAC+C,SAAS,CAAC,YAAM;IACpB,IAAMM,YAAY,GAAG7C,gBAAgB,CAACP,UAAU,EAAE,QAAQ,EAAE;MAAA,OAC1D+B,UAAU,CAAC,KAAK,CAClB;IAAA,EAAC;IAED,OAAO;MAAA,OAAMqB,YAAY,CAACC,MAAM,CAAC,CAAC;IAAA;EACpC,CAAC,EAAE,EAAE,CAAC;EAEN,IAAMC,gBAAgB,GAAGvD,KAAK,CAACwD,WAAW,CAAC,YAAM;IAC/C,IAAIf,gBAAgB,CAACO,OAAO,CAACC,MAAM,EAAE;MACnCR,gBAAgB,CAACO,OAAO,CAACE,OAAO,CAAE,UAAAC,CAAC;QAAA,OAAKC,YAAY,CAACD,CAAC,CAAC;MAAA,EAAC;MACxDV,gBAAgB,CAACO,OAAO,GAAG,EAAE;IAC/B;IAEA,IAAIvB,KAAK,EAAE;MACT,IAAIgC,EAAE,GAAGC,UAAU,CAAC,YAAM;QACxBf,OAAO,CAACK,OAAO,GAAG,IAAI;QACtBhB,UAAU,CAAC,IAAI,CAAC;MAClB,CAAC,EAAEjB,eAAe,CAA8B;MAChDwB,gBAAgB,CAACS,OAAO,CAACW,IAAI,CAACF,EAAE,CAAC;IACnC,CAAC,MAAM;MACLd,OAAO,CAACK,OAAO,GAAG,IAAI;MACtBhB,UAAU,CAAC,IAAI,CAAC;IAClB;EACF,CAAC,EAAE,CAACP,KAAK,EAAEV,eAAe,CAAC,CAAC;EAE5B,IAAM6C,cAAc,GAAG5D,KAAK,CAACwD,WAAW,CAAC,YAAM;IAC7Cb,OAAO,CAACK,OAAO,GAAG,KAAK;IACvB,IAAIT,gBAAgB,CAACS,OAAO,CAACC,MAAM,EAAE;MACnCV,gBAAgB,CAACS,OAAO,CAACE,OAAO,CAAE,UAAAC,CAAC;QAAA,OAAKC,YAAY,CAACD,CAAC,CAAC;MAAA,EAAC;MACxDZ,gBAAgB,CAACS,OAAO,GAAG,EAAE;IAC/B;IAEA,IAAIS,EAAE,GAAGC,UAAU,CAAC,YAAM;MACxB1B,UAAU,CAAC,KAAK,CAAC;MACjBM,cAAc,CAAC;QAAEzB,QAAQ,EAAE,CAAC,CAAC;QAAEqB,OAAO,EAAE,CAAC,CAAC;QAAEC,QAAQ,EAAE;MAAM,CAAC,CAAC;IAChE,CAAC,EAAElB,eAAe,CAA8B;IAChDwB,gBAAgB,CAACO,OAAO,CAACW,IAAI,CAACF,EAAE,CAAC;EACnC,CAAC,EAAE,CAACxC,eAAe,CAAC,CAAC;EAErB,IAAM4C,WAAW,GAAG7D,KAAK,CAACwD,WAAW,CAAC,YAAM;IAAA,IAAAM,cAAA;IAC1C,IAAInB,OAAO,CAACK,OAAO,EAAE;MACnB,OAAO,IAAI;IACb;IACA,IAAI,CAACJ,YAAY,EAAE,OAAO,IAAI;IAC9B,IAAMmB,KAAK,GAAGlD,QAAQ,CAACkD,KAA0B;IACjD,IAAIA,KAAK,CAACC,QAAQ,EAAE,OAAO,IAAI;IAC/B,QAAAF,cAAA,GAAOC,KAAK,CAACE,OAAO,cAAAH,cAAA,uBAAbA,cAAA,CAAAI,IAAA,CAAAH,KAAgB,CAAC;EAC1B,CAAC,EAAE,CAAClD,QAAQ,CAACkD,KAAK,EAAEnB,YAAY,CAAC,CAAC;EAElC,IAAMuB,aAAa,GAAGnE,KAAK,CAACwD,WAAW,CAAC,YAAM;IAC5CD,gBAAgB,CAAC,CAAC;IAClB,IAAIX,YAAY,EAAE;MAAA,IAAAwB,UAAA,EAAAC,IAAA;MAChB,CAAAD,UAAA,IAAAC,IAAA,GAACxD,QAAQ,CAACkD,KAAK,EAAuBO,SAAS,cAAAF,UAAA,eAA/CA,UAAA,CAAAF,IAAA,CAAAG,IAAkD,CAAC;IACrD;EACF,CAAC,EAAE,CAACxD,QAAQ,CAACkD,KAAK,EAAER,gBAAgB,EAAEX,YAAY,CAAC,CAAC;EAEpD,IAAM2B,cAAc,GAAGvE,KAAK,CAACwD,WAAW,CAAC,YAAM;IAC7CI,cAAc,CAAC,CAAC;IAChB,IAAIhB,YAAY,EAAE;MAAA,IAAA4B,WAAA,EAAAC,KAAA;MAChB,CAAAD,WAAA,IAAAC,KAAA,GAAC5D,QAAQ,CAACkD,KAAK,EAAuBW,UAAU,cAAAF,WAAA,eAAhDA,WAAA,CAAAN,IAAA,CAAAO,KAAmD,CAAC;IACtD;EACF,CAAC,EAAE,CAAC5D,QAAQ,CAACkD,KAAK,EAAEH,cAAc,EAAEhB,YAAY,CAAC,CAAC;EAElD,IAAM+B,cAAc,GAAG,SAAjBA,cAAcA,CAAAC,KAAA,EAAuD;IAAA,IAAlCC,MAAA,GAAAD,KAAA,CAAfE,WAAW,CAAID,MAAA;IAAkC,IAAAE,qBAAA;IACzE,CAAAA,qBAAA,GAAArC,kBAAkB,CAACM,OAAO,cAAA+B,qBAAA,eAA1BA,qBAAA,CAA4BC,OAAO,CACjC,UAACC,EAAE,EAAEC,EAAE,EAAEC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAK;MACvChD,cAAc,CAAC;QACbzB,QAAQ,EAAE;UAAEwE,KAAK,EAALA,KAAK;UAAEC,KAAK,EAALA,KAAK;UAAEF,MAAM,EAANA,MAAM;UAAED,KAAA,EAAAA;QAAM,CAAC;QACzCjD,OAAO,EAAAqD,aAAA,KAAOV,MAAA,CAAQ;QACtB1C,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CACF,CAAC;EACH,CAAC;EAED,IAAMqD,gBAAgB,GAAG;IACvBvB,OAAO,EAAEJ,WAAW;IACpB4B,WAAW,EAAE,SAAbA,WAAWA,CAAA;MAAA,OAAQlC,gBAAgB,CAAC,CAAC;IAAA;IACrCmC,UAAU,EAAE,SAAZA,UAAUA,CAAA;MAAA,OAAQ9B,cAAc,CAAC,CAAC;IAAA;IAClC+B,cAAc,EAAE5E;EAClB,CAAC;EAED,IAAM6E,aAAa,GAAG;IACpBtB,SAAS,EAAEH,aAAa;IACxBO,UAAU,EAAEH;EACd,CAAC;EAED,OACEvE,KAAA,CAAA6F,aAAA,CAAA7F,KAAA,CAAA8F,QAAA,QACG/D,OAAO,IACN/B,KAAA,CAAA6F,aAAA,CAACpF,MAAM,QACLT,KAAA,CAAA6F,aAAA,CAAC3F,IAAI;IACH6F,QAAQ,EAAEpB,cAAe;IACzBqB,KAAK,EAAE,CACLC,MAAM,CAAC/D,OAAO,EAAAqD,aAAA,CAAAA,aAAA;MAEZW,eAAe,EAAE9E,KAAK,CAAC+E,IAAI,GACvB/E,KAAK,CAACgF,MAAM,CAACC,SAAS,GACtBjF,KAAK,CAACgF,MAAM,CAAClE;IAAO,GACrB5B,kBAAkB,CACnB+B,WAAW,EACXxB,QACF,CAAC;MACDyF,YAAY,EAAElF,KAAK,CAACmF;IAAS,GACzBlE,WAAW,CAACF,QAAQ,GAAG8D,MAAM,CAAClE,OAAO,GAAGkE,MAAM,CAACO,MAAM,EAE3D;IACFC,MAAM,EAAC;EAAmB,GAE1BzG,KAAA,CAAA6F,aAAA,CAACnF,IAAI;IACHgG,uBAAuB,EAAC,QAAQ;IAChCC,aAAa,EAAE,CAAE;IACjBC,UAAU,EAAE,KAAM;IAClBC,OAAO,EAAC,YAAY;IACpBb,KAAK,EAAE;MAAEc,KAAK,EAAE1F,KAAK,CAACgF,MAAM,CAACW;IAAQ,CAAE;IACvCC,qBAAqB,EAAE3F;EAA2B,GAEjDH,KACG,CACF,CACA,CACT,EACDlB,KAAA,CAAA6F,aAAA,CAACxF,SAAS,EAAA4G,QAAA;IACRC,GAAG,EAAExE,kBAAmB;IACxBsD,KAAK,EAAEC,MAAM,CAACkB;EAAe,GACxB1F,KAAK,GAAGmE,aAAa,GAAGJ,gBAAgB,GAE5CxF,KAAK,CAACoH,YAAY,CAACvG,QAAQ,EAAA0E,aAAA,CAAAA,aAAA,KACvBjE,IAAI,GACHG,KAAK,GAAGmE,aAAa,GAAGJ,gBAAgB,CAC7C,CACQ,CACX,CAAC;AAEP,CAAC;AAED7E,OAAO,CAAC0G,WAAW,GAAG,SAAS;AAE/B,IAAMpB,MAAM,GAAG9F,UAAU,CAACmH,MAAM,CAAC;EAC/BpF,OAAO,EAAE;IACPqF,SAAS,EAAE,YAAY;IACvBC,cAAc,EAAE,QAAQ;IACxBC,iBAAiB,EAAE,EAAE;IACrBrC,MAAM,EAAE,EAAE;IACVsC,SAAS,EAAE;EACb,CAAC;EACD3F,OAAO,EAAE;IACP4F,OAAO,EAAE;EACX,CAAC;EACDnB,MAAM,EAAE;IACNmB,OAAO,EAAE;EACX,CAAC;EACDR,cAAc,EAAA5B,aAAA,KACRnF,QAAQ,CAACsB,EAAE,KAAK,KAAK,IAAI;IAAEkG,MAAM,EAAE;EAAU,CAAC;AAEtD,CAAC,CAAC;AAEF,eAAejH,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}