{"ast": null, "code": "import React from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport Card from \"../Card\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar StatCard = function StatCard(_ref) {\n  var title = _ref.title,\n    value = _ref.value,\n    change = _ref.change,\n    _ref$changeType = _ref.changeType,\n    changeType = _ref$changeType === void 0 ? 'positive' : _ref$changeType,\n    icon = _ref.icon,\n    subtitle = _ref.subtitle;\n  var getChangeColor = function getChangeColor() {\n    switch (changeType) {\n      case 'positive':\n        return '#4CAF50';\n      case 'negative':\n        return '#F44336';\n      default:\n        return '#8a8a8a';\n    }\n  };\n  return _jsx(Card, {\n    style: {\n      flex: 1,\n      minHeight: 100\n    },\n    children: _jsxs(View, {\n      style: {\n        flexDirection: 'row',\n        justifyContent: 'space-between',\n        alignItems: 'flex-start'\n      },\n      children: [_jsxs(View, {\n        style: {\n          flex: 1\n        },\n        children: [_jsx(Text, {\n          style: {\n            color: '#8a8a8a',\n            fontSize: 12,\n            fontFamily: 'Poppins_400Regular',\n            marginBottom: 4\n          },\n          children: title\n        }), _jsx(Text, {\n          style: {\n            color: '#fff',\n            fontSize: 20,\n            fontFamily: 'Poppins_600SemiBold',\n            marginBottom: 2\n          },\n          children: value\n        }), change && _jsx(Text, {\n          style: {\n            color: getChangeColor(),\n            fontSize: 12,\n            fontFamily: 'Poppins_500Medium'\n          },\n          children: change\n        }), subtitle && _jsx(Text, {\n          style: {\n            color: '#8a8a8a',\n            fontSize: 10,\n            fontFamily: 'Poppins_400Regular',\n            marginTop: 2\n          },\n          children: subtitle\n        })]\n      }), icon && _jsx(View, {\n        style: {\n          backgroundColor: '#FECB37',\n          borderRadius: 8,\n          padding: 8,\n          alignItems: 'center',\n          justifyContent: 'center'\n        },\n        children: _jsx(Text, {\n          style: {\n            fontSize: 16\n          },\n          children: icon\n        })\n      })]\n    })\n  });\n};\nexport default StatCard;", "map": {"version": 3, "names": ["React", "View", "Text", "Card", "jsx", "_jsx", "jsxs", "_jsxs", "StatCard", "_ref", "title", "value", "change", "_ref$changeType", "changeType", "icon", "subtitle", "getChangeColor", "style", "flex", "minHeight", "children", "flexDirection", "justifyContent", "alignItems", "color", "fontSize", "fontFamily", "marginBottom", "marginTop", "backgroundColor", "borderRadius", "padding"], "sources": ["E:/CryptoSignalsApp/src/components/StatCard/index.js"], "sourcesContent": ["import React from 'react';\nimport { View, Text } from 'react-native';\nimport Card from '../Card';\n\nconst StatCard = ({ \n  title, \n  value, \n  change, \n  changeType = 'positive', // 'positive', 'negative', 'neutral'\n  icon,\n  subtitle\n}) => {\n  const getChangeColor = () => {\n    switch (changeType) {\n      case 'positive': return '#4CAF50';\n      case 'negative': return '#F44336';\n      default: return '#8a8a8a';\n    }\n  };\n\n  return (\n    <Card style={{ flex: 1, minHeight: 100 }}>\n      <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-start' }}>\n        <View style={{ flex: 1 }}>\n          <Text style={{ \n            color: '#8a8a8a', \n            fontSize: 12, \n            fontFamily: 'Poppins_400Regular',\n            marginBottom: 4\n          }}>\n            {title}\n          </Text>\n          <Text style={{ \n            color: '#fff', \n            fontSize: 20, \n            fontFamily: 'Poppins_600SemiBold',\n            marginBottom: 2\n          }}>\n            {value}\n          </Text>\n          {change && (\n            <Text style={{ \n              color: getChangeColor(), \n              fontSize: 12, \n              fontFamily: 'Poppins_500Medium'\n            }}>\n              {change}\n            </Text>\n          )}\n          {subtitle && (\n            <Text style={{ \n              color: '#8a8a8a', \n              fontSize: 10, \n              fontFamily: 'Poppins_400Regular',\n              marginTop: 2\n            }}>\n              {subtitle}\n            </Text>\n          )}\n        </View>\n        {icon && (\n          <View style={{ \n            backgroundColor: '#FECB37', \n            borderRadius: 8, \n            padding: 8,\n            alignItems: 'center',\n            justifyContent: 'center'\n          }}>\n            <Text style={{ fontSize: 16 }}>{icon}</Text>\n          </View>\n        )}\n      </View>\n    </Card>\n  );\n};\n\nexport default StatCard;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,IAAA;AAE1B,OAAOC,IAAI;AAAgB,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAE3B,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAAC,IAAA,EAOR;EAAA,IANJC,KAAK,GAAAD,IAAA,CAALC,KAAK;IACLC,KAAK,GAAAF,IAAA,CAALE,KAAK;IACLC,MAAM,GAAAH,IAAA,CAANG,MAAM;IAAAC,eAAA,GAAAJ,IAAA,CACNK,UAAU;IAAVA,UAAU,GAAAD,eAAA,cAAG,UAAU,GAAAA,eAAA;IACvBE,IAAI,GAAAN,IAAA,CAAJM,IAAI;IACJC,QAAQ,GAAAP,IAAA,CAARO,QAAQ;EAER,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;IAC3B,QAAQH,UAAU;MAChB,KAAK,UAAU;QAAE,OAAO,SAAS;MACjC,KAAK,UAAU;QAAE,OAAO,SAAS;MACjC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,OACET,IAAA,CAACF,IAAI;IAACe,KAAK,EAAE;MAAEC,IAAI,EAAE,CAAC;MAAEC,SAAS,EAAE;IAAI,CAAE;IAAAC,QAAA,EACvCd,KAAA,CAACN,IAAI;MAACiB,KAAK,EAAE;QAAEI,aAAa,EAAE,KAAK;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE;MAAa,CAAE;MAAAH,QAAA,GAC/Fd,KAAA,CAACN,IAAI;QAACiB,KAAK,EAAE;UAAEC,IAAI,EAAE;QAAE,CAAE;QAAAE,QAAA,GACvBhB,IAAA,CAACH,IAAI;UAACgB,KAAK,EAAE;YACXO,KAAK,EAAE,SAAS;YAChBC,QAAQ,EAAE,EAAE;YACZC,UAAU,EAAE,oBAAoB;YAChCC,YAAY,EAAE;UAChB,CAAE;UAAAP,QAAA,EACCX;QAAK,CACF,CAAC,EACPL,IAAA,CAACH,IAAI;UAACgB,KAAK,EAAE;YACXO,KAAK,EAAE,MAAM;YACbC,QAAQ,EAAE,EAAE;YACZC,UAAU,EAAE,qBAAqB;YACjCC,YAAY,EAAE;UAChB,CAAE;UAAAP,QAAA,EACCV;QAAK,CACF,CAAC,EACNC,MAAM,IACLP,IAAA,CAACH,IAAI;UAACgB,KAAK,EAAE;YACXO,KAAK,EAAER,cAAc,CAAC,CAAC;YACvBS,QAAQ,EAAE,EAAE;YACZC,UAAU,EAAE;UACd,CAAE;UAAAN,QAAA,EACCT;QAAM,CACH,CACP,EACAI,QAAQ,IACPX,IAAA,CAACH,IAAI;UAACgB,KAAK,EAAE;YACXO,KAAK,EAAE,SAAS;YAChBC,QAAQ,EAAE,EAAE;YACZC,UAAU,EAAE,oBAAoB;YAChCE,SAAS,EAAE;UACb,CAAE;UAAAR,QAAA,EACCL;QAAQ,CACL,CACP;MAAA,CACG,CAAC,EACND,IAAI,IACHV,IAAA,CAACJ,IAAI;QAACiB,KAAK,EAAE;UACXY,eAAe,EAAE,SAAS;UAC1BC,YAAY,EAAE,CAAC;UACfC,OAAO,EAAE,CAAC;UACVR,UAAU,EAAE,QAAQ;UACpBD,cAAc,EAAE;QAClB,CAAE;QAAAF,QAAA,EACAhB,IAAA,CAACH,IAAI;UAACgB,KAAK,EAAE;YAAEQ,QAAQ,EAAE;UAAG,CAAE;UAAAL,QAAA,EAAEN;QAAI,CAAO;MAAC,CACxC,CACP;IAAA,CACG;EAAC,CACH,CAAC;AAEX,CAAC;AAED,eAAeP,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}