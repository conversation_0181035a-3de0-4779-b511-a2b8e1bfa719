{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport * as React from 'react';\nimport NavigationBuilderContext from \"./NavigationBuilderContext\";\nimport NavigationRouteContext from \"./NavigationRouteContext\";\nvar VISITED_ROUTE_KEYS = Symbol('VISITED_ROUTE_KEYS');\nexport var shouldPreventRemove = function shouldPreventRemove(emitter, beforeRemoveListeners, currentRoutes, nextRoutes, action) {\n  var _action$VISITED_ROUTE;\n  var nextRouteKeys = nextRoutes.map(function (route) {\n    return route.key;\n  });\n  var removedRoutes = currentRoutes.filter(function (route) {\n    return !nextRouteKeys.includes(route.key);\n  }).reverse();\n  var visitedRouteKeys = (_action$VISITED_ROUTE = action[VISITED_ROUTE_KEYS]) != null ? _action$VISITED_ROUTE : new Set();\n  var beforeRemoveAction = _objectSpread(_objectSpread({}, action), {}, _defineProperty({}, VISITED_ROUTE_KEYS, visitedRouteKeys));\n  for (var route of removedRoutes) {\n    var _beforeRemoveListener;\n    if (visitedRouteKeys.has(route.key)) {\n      continue;\n    }\n    var isPrevented = (_beforeRemoveListener = beforeRemoveListeners[route.key]) === null || _beforeRemoveListener === void 0 ? void 0 : _beforeRemoveListener.call(beforeRemoveListeners, beforeRemoveAction);\n    if (isPrevented) {\n      return true;\n    }\n    visitedRouteKeys.add(route.key);\n    var event = emitter.emit({\n      type: 'beforeRemove',\n      target: route.key,\n      data: {\n        action: beforeRemoveAction\n      },\n      canPreventDefault: true\n    });\n    if (event.defaultPrevented) {\n      return true;\n    }\n  }\n  return false;\n};\nexport default function useOnPreventRemove(_ref) {\n  var getState = _ref.getState,\n    emitter = _ref.emitter,\n    beforeRemoveListeners = _ref.beforeRemoveListeners;\n  var _React$useContext = React.useContext(NavigationBuilderContext),\n    addKeyedListener = _React$useContext.addKeyedListener;\n  var route = React.useContext(NavigationRouteContext);\n  var routeKey = route === null || route === void 0 ? void 0 : route.key;\n  React.useEffect(function () {\n    if (routeKey) {\n      return addKeyedListener === null || addKeyedListener === void 0 ? void 0 : addKeyedListener('beforeRemove', routeKey, function (action) {\n        var state = getState();\n        return shouldPreventRemove(emitter, beforeRemoveListeners, state.routes, [], action);\n      });\n    }\n  }, [addKeyedListener, beforeRemoveListeners, emitter, getState, routeKey]);\n}", "map": {"version": 3, "names": ["React", "NavigationBuilderContext", "NavigationRouteContext", "VISITED_ROUTE_KEYS", "Symbol", "shouldPreventRemove", "emitter", "beforeRemoveListeners", "currentRoutes", "nextRoutes", "action", "_action$VISITED_ROUTE", "nextR<PERSON>e<PERSON>eys", "map", "route", "key", "removedRoutes", "filter", "includes", "reverse", "visitedRouteKeys", "Set", "beforeRemoveAction", "_objectSpread", "_defineProperty", "_beforeRemoveListener", "has", "isPrevented", "call", "add", "event", "emit", "type", "target", "data", "canPreventDefault", "defaultPrevented", "useOnPreventRemove", "_ref", "getState", "_React$useContext", "useContext", "addKeyedListener", "routeKey", "useEffect", "state", "routes"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\@react-navigation\\core\\src\\useOnPreventRemove.tsx"], "sourcesContent": ["import type {\n  NavigationAction,\n  NavigationState,\n} from '@react-navigation/routers';\nimport * as React from 'react';\n\nimport NavigationBuilderContext, {\n  ChildBeforeRemoveListener,\n} from './NavigationBuilderContext';\nimport NavigationRouteContext from './NavigationRouteContext';\nimport type { EventMapCore } from './types';\nimport type { NavigationEventEmitter } from './useEventEmitter';\n\ntype Options = {\n  getState: () => NavigationState;\n  emitter: NavigationEventEmitter<EventMapCore<any>>;\n  beforeRemoveListeners: Record<string, ChildBeforeRemoveListener | undefined>;\n};\n\nconst VISITED_ROUTE_KEYS = Symbol('VISITED_ROUTE_KEYS');\n\nexport const shouldPreventRemove = (\n  emitter: NavigationEventEmitter<EventMapCore<any>>,\n  beforeRemoveListeners: Record<string, ChildBeforeRemoveListener | undefined>,\n  currentRoutes: { key: string }[],\n  nextRoutes: { key?: string | undefined }[],\n  action: NavigationAction\n) => {\n  const nextRouteKeys = nextRoutes.map((route) => route.key);\n\n  // Call these in reverse order so last screens handle the event first\n  const removedRoutes = currentRoutes\n    .filter((route) => !nextRouteKeys.includes(route.key))\n    .reverse();\n\n  const visitedRouteKeys: Set<string> =\n    // @ts-expect-error: add this property to mark that we've already emitted this action\n    action[VISITED_ROUTE_KEYS] ?? new Set<string>();\n\n  const beforeRemoveAction = {\n    ...action,\n    [VISITED_ROUTE_KEYS]: visitedRouteKeys,\n  };\n\n  for (const route of removedRoutes) {\n    if (visitedRouteKeys.has(route.key)) {\n      // Skip if we've already emitted this action for this screen\n      continue;\n    }\n\n    // First, we need to check if any child screens want to prevent it\n    const isPrevented = beforeRemoveListeners[route.key]?.(beforeRemoveAction);\n\n    if (isPrevented) {\n      return true;\n    }\n\n    visitedRouteKeys.add(route.key);\n\n    const event = emitter.emit({\n      type: 'beforeRemove',\n      target: route.key,\n      data: { action: beforeRemoveAction },\n      canPreventDefault: true,\n    });\n\n    if (event.defaultPrevented) {\n      return true;\n    }\n  }\n\n  return false;\n};\n\nexport default function useOnPreventRemove({\n  getState,\n  emitter,\n  beforeRemoveListeners,\n}: Options) {\n  const { addKeyedListener } = React.useContext(NavigationBuilderContext);\n  const route = React.useContext(NavigationRouteContext);\n  const routeKey = route?.key;\n\n  React.useEffect(() => {\n    if (routeKey) {\n      return addKeyedListener?.('beforeRemove', routeKey, (action) => {\n        const state = getState();\n\n        return shouldPreventRemove(\n          emitter,\n          beforeRemoveListeners,\n          state.routes,\n          [],\n          action\n        );\n      });\n    }\n  }, [addKeyedListener, beforeRemoveListeners, emitter, getState, routeKey]);\n}\n"], "mappings": ";;;AAIA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,OAAOC,wBAAwB;AAG/B,OAAOC,sBAAsB;AAU7B,IAAMC,kBAAkB,GAAGC,MAAM,CAAC,oBAAoB,CAAC;AAEvD,OAAO,IAAMC,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAC9BC,OAAkD,EAClDC,qBAA4E,EAC5EC,aAAgC,EAChCC,UAA0C,EAC1CC,MAAwB,EACrB;EAAA,IAAAC,qBAAA;EACH,IAAMC,aAAa,GAAGH,UAAU,CAACI,GAAG,CAAE,UAAAC,KAAK;IAAA,OAAKA,KAAK,CAACC,GAAG;EAAA,EAAC;EAG1D,IAAMC,aAAa,GAAGR,aAAa,CAChCS,MAAM,CAAE,UAAAH,KAAK;IAAA,OAAK,CAACF,aAAa,CAACM,QAAQ,CAACJ,KAAK,CAACC,GAAG,CAAC;EAAA,EAAC,CACrDI,OAAO,EAAE;EAEZ,IAAMC,gBAA6B,IAAAT,qBAAA,GAEjCD,MAAM,CAACP,kBAAkB,CAAC,YAAAQ,qBAAA,GAAI,IAAIU,GAAG,EAAU;EAEjD,IAAMC,kBAAkB,GAAAC,aAAA,CAAAA,aAAA,KACnBb,MAAM,OAAAc,eAAA,KACRrB,kBAAkB,EAAGiB,gBAAA,EACvB;EAED,KAAK,IAAMN,KAAK,IAAIE,aAAa,EAAE;IAAA,IAAAS,qBAAA;IACjC,IAAIL,gBAAgB,CAACM,GAAG,CAACZ,KAAK,CAACC,GAAG,CAAC,EAAE;MAEnC;IACF;IAGA,IAAMY,WAAW,IAAAF,qBAAA,GAAGlB,qBAAqB,CAACO,KAAK,CAACC,GAAG,CAAC,cAAAU,qBAAA,uBAAhCA,qBAAA,CAAAG,IAAA,CAAArB,qBAAqB,EAAce,kBAAkB,CAAC;IAE1E,IAAIK,WAAW,EAAE;MACf,OAAO,IAAI;IACb;IAEAP,gBAAgB,CAACS,GAAG,CAACf,KAAK,CAACC,GAAG,CAAC;IAE/B,IAAMe,KAAK,GAAGxB,OAAO,CAACyB,IAAI,CAAC;MACzBC,IAAI,EAAE,cAAc;MACpBC,MAAM,EAAEnB,KAAK,CAACC,GAAG;MACjBmB,IAAI,EAAE;QAAExB,MAAM,EAAEY;MAAmB,CAAC;MACpCa,iBAAiB,EAAE;IACrB,CAAC,CAAC;IAEF,IAAIL,KAAK,CAACM,gBAAgB,EAAE;MAC1B,OAAO,IAAI;IACb;EACF;EAEA,OAAO,KAAK;AACd,CAAC;AAED,eAAe,SAASC,kBAAkBA,CAAAC,IAAA,EAI9B;EAAA,IAHVC,QAAQ,GAGAD,IAAA,CAHRC,QAAQ;IACRjC,OAAO,GAECgC,IAAA,CAFRhC,OAAO;IACPC,qBAAA,GACQ+B,IAAA,CADR/B,qBAAA;EAEA,IAAAiC,iBAAA,GAA6BxC,KAAK,CAACyC,UAAU,CAACxC,wBAAwB,CAAC;IAA/DyC,gBAAA,GAAAF,iBAAA,CAAAE,gBAAA;EACR,IAAM5B,KAAK,GAAGd,KAAK,CAACyC,UAAU,CAACvC,sBAAsB,CAAC;EACtD,IAAMyC,QAAQ,GAAG7B,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEC,GAAG;EAE3Bf,KAAK,CAAC4C,SAAS,CAAC,YAAM;IACpB,IAAID,QAAQ,EAAE;MACZ,OAAOD,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAG,cAAc,EAAEC,QAAQ,EAAG,UAAAjC,MAAM,EAAK;QAC9D,IAAMmC,KAAK,GAAGN,QAAQ,EAAE;QAExB,OAAOlC,mBAAmB,CACxBC,OAAO,EACPC,qBAAqB,EACrBsC,KAAK,CAACC,MAAM,EACZ,EAAE,EACFpC,MAAM,CACP;MACH,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACgC,gBAAgB,EAAEnC,qBAAqB,EAAED,OAAO,EAAEiC,QAAQ,EAAEI,QAAQ,CAAC,CAAC;AAC5E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}