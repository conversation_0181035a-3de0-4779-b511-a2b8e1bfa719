{"ast": null, "code": "function _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nimport * as React from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport StyledText from \"./StyledText\";\nvar Title = function Title(props) {\n  return React.createElement(StyledText, _extends({}, props, {\n    alpha: 0.87,\n    family: \"medium\",\n    style: [styles.text, props.style]\n  }));\n};\nexport default Title;\nvar styles = StyleSheet.create({\n  text: {\n    fontSize: 20,\n    lineHeight: 30,\n    marginVertical: 2,\n    letterSpacing: 0.15\n  }\n});", "map": {"version": 3, "names": ["React", "StyleSheet", "StyledText", "Title", "props", "createElement", "_extends", "alpha", "family", "style", "styles", "text", "create", "fontSize", "lineHeight", "marginVertical", "letterSpacing"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\Typography\\v2\\Title.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Text, StyleSheet } from 'react-native';\n\nimport StyledText from './StyledText';\n\nexport type Props = React.ComponentProps<typeof Text> & {\n  children: React.ReactNode;\n};\n\n// @component-group Typography\n\n/**\n * @deprecated Deprecated in v5.x - use `<Text variant=\"titleLarge\" />` instead.\n * Typography component for showing a title.\n *\n * <div class=\"screenshots\">\n *   <img src=\"screenshots/title.png\" />\n * </div>\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { Title } from 'react-native-paper';\n *\n * const MyComponent = () => (\n *   <Title>Title</Title>\n * );\n *\n * export default MyComponent;\n * ```\n */\nconst Title = (props: Props) => (\n  <StyledText\n    {...props}\n    alpha={0.87}\n    family=\"medium\"\n    style={[styles.text, props.style]}\n  />\n);\n\nexport default Title;\n\nconst styles = StyleSheet.create({\n  text: {\n    fontSize: 20,\n    lineHeight: 30,\n    marginVertical: 2,\n    letterSpacing: 0.15,\n  },\n});\n"], "mappings": ";;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,UAAA;AAG9B,OAAOC,UAAU;AA4BjB,IAAMC,KAAK,GAAI,SAATA,KAAKA,CAAIC,KAAY;EAAA,OACzBJ,KAAA,CAAAK,aAAA,CAACH,UAAU,EAAAI,QAAA,KACLF,KAAK;IACTG,KAAK,EAAE,IAAK;IACZC,MAAM,EAAC,QAAQ;IACfC,KAAK,EAAE,CAACC,MAAM,CAACC,IAAI,EAAEP,KAAK,CAACK,KAAK;EAAE,EACnC,CACF;AAAA;AAED,eAAeN,KAAK;AAEpB,IAAMO,MAAM,GAAGT,UAAU,CAACW,MAAM,CAAC;EAC/BD,IAAI,EAAE;IACJE,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,EAAE;IACdC,cAAc,EAAE,CAAC;IACjBC,aAAa,EAAE;EACjB;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}