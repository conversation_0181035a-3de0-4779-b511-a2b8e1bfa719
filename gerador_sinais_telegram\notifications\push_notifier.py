#!/usr/bin/env python3
"""
Sistema de Notificações Push
Envia notificações push para navegadores e dispositivos móveis
"""

import json
import logging
from typing import List, Dict, Optional
from datetime import datetime
import requests
from pywebpush import webpush, WebPushException
import base64
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import ec

logger = logging.getLogger(__name__)

class PushNotifier:
    """Sistema de notificações push"""
    
    def __init__(self, config: Dict):
        self.vapid_private_key = config.get('vapid_private_key')
        self.vapid_public_key = config.get('vapid_public_key')
        self.vapid_claims = config.get('vapid_claims', {
            "sub": "mailto:<EMAIL>"
        })
        self.enabled = config.get('enabled', False)
        self.subscriptions = []  # Lista de assinantes
        
        if not self.vapid_private_key or not self.vapid_public_key:
            logger.warning("VAPID keys not configured for push notifications")
            self.enabled = False
    
    def generate_vapid_keys(self) -> Dict[str, str]:
        """Gera chaves VAPID para push notifications"""
        try:
            # Gerar chave privada
            private_key = ec.generate_private_key(ec.SECP256R1())
            
            # Converter para formato PEM
            private_pem = private_key.private_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PrivateFormat.PKCS8,
                encryption_algorithm=serialization.NoEncryption()
            )
            
            # Gerar chave pública
            public_key = private_key.public_key()
            public_pem = public_key.public_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PublicFormat.SubjectPublicKeyInfo
            )
            
            return {
                'private_key': private_pem.decode('utf-8'),
                'public_key': public_pem.decode('utf-8')
            }
            
        except Exception as e:
            logger.error(f"Erro ao gerar chaves VAPID: {e}")
            return {}
    
    def add_subscription(self, subscription_data: Dict) -> bool:
        """Adiciona nova assinatura push"""
        try:
            # Validar dados da assinatura
            required_fields = ['endpoint', 'keys']
            if not all(field in subscription_data for field in required_fields):
                logger.error("Dados de assinatura inválidos")
                return False
            
            # Verificar se já existe
            for sub in self.subscriptions:
                if sub['endpoint'] == subscription_data['endpoint']:
                    logger.info("Assinatura já existe, atualizando...")
                    sub.update(subscription_data)
                    return True
            
            # Adicionar nova assinatura
            subscription_data['created_at'] = datetime.now().isoformat()
            self.subscriptions.append(subscription_data)
            
            logger.info(f"Nova assinatura push adicionada: {subscription_data['endpoint'][:50]}...")
            return True
            
        except Exception as e:
            logger.error(f"Erro ao adicionar assinatura: {e}")
            return False
    
    def remove_subscription(self, endpoint: str) -> bool:
        """Remove assinatura push"""
        try:
            initial_count = len(self.subscriptions)
            self.subscriptions = [sub for sub in self.subscriptions if sub['endpoint'] != endpoint]
            
            removed = initial_count - len(self.subscriptions)
            if removed > 0:
                logger.info(f"Assinatura removida: {endpoint[:50]}...")
                return True
            else:
                logger.warning(f"Assinatura não encontrada: {endpoint[:50]}...")
                return False
                
        except Exception as e:
            logger.error(f"Erro ao remover assinatura: {e}")
            return False
    
    def send_signal_notification(self, signal_data: Dict) -> Dict[str, int]:
        """Envia notificação de novo sinal"""
        if not self.enabled or not self.subscriptions:
            return {'sent': 0, 'failed': 0}
        
        try:
            # Preparar payload da notificação
            notification_payload = {
                'title': f"🎯 New {signal_data['signal_type']} Signal",
                'body': f"{signal_data['symbol']} - {signal_data['strategy']}",
                'icon': '/icons/signal-icon.png',
                'badge': '/icons/badge-icon.png',
                'data': {
                    'signal_id': signal_data.get('id'),
                    'symbol': signal_data['symbol'],
                    'signal_type': signal_data['signal_type'],
                    'entry_price': signal_data['entry_price'],
                    'url': '/signals'
                },
                'actions': [
                    {
                        'action': 'view',
                        'title': 'View Signal',
                        'icon': '/icons/view-icon.png'
                    },
                    {
                        'action': 'dismiss',
                        'title': 'Dismiss',
                        'icon': '/icons/dismiss-icon.png'
                    }
                ],
                'requireInteraction': True,
                'timestamp': datetime.now().timestamp() * 1000
            }
            
            return self._send_to_all_subscriptions(notification_payload)
            
        except Exception as e:
            logger.error(f"Erro ao enviar notificação de sinal: {e}")
            return {'sent': 0, 'failed': 1}
    
    def send_performance_notification(self, performance_data: Dict) -> Dict[str, int]:
        """Envia notificação de performance"""
        if not self.enabled or not self.subscriptions:
            return {'sent': 0, 'failed': 0}
        
        try:
            win_rate = performance_data.get('win_rate', 0)
            total_profit = performance_data.get('total_profit', 0)
            
            # Escolher emoji baseado na performance
            if win_rate >= 70 and total_profit > 0:
                emoji = "🚀"
            elif win_rate >= 50:
                emoji = "📈"
            else:
                emoji = "📊"
            
            notification_payload = {
                'title': f"{emoji} Performance Update",
                'body': f"Win Rate: {win_rate}% | Profit: {total_profit:.2f}%",
                'icon': '/icons/performance-icon.png',
                'badge': '/icons/badge-icon.png',
                'data': {
                    'win_rate': win_rate,
                    'total_profit': total_profit,
                    'url': '/analytics'
                },
                'actions': [
                    {
                        'action': 'view_analytics',
                        'title': 'View Analytics',
                        'icon': '/icons/analytics-icon.png'
                    }
                ],
                'timestamp': datetime.now().timestamp() * 1000
            }
            
            return self._send_to_all_subscriptions(notification_payload)
            
        except Exception as e:
            logger.error(f"Erro ao enviar notificação de performance: {e}")
            return {'sent': 0, 'failed': 1}
    
    def send_system_notification(self, message: str, notification_type: str = 'info') -> Dict[str, int]:
        """Envia notificação do sistema"""
        if not self.enabled or not self.subscriptions:
            return {'sent': 0, 'failed': 0}
        
        try:
            # Escolher emoji e cor baseado no tipo
            type_config = {
                'info': {'emoji': 'ℹ️', 'icon': '/icons/info-icon.png'},
                'warning': {'emoji': '⚠️', 'icon': '/icons/warning-icon.png'},
                'error': {'emoji': '❌', 'icon': '/icons/error-icon.png'},
                'success': {'emoji': '✅', 'icon': '/icons/success-icon.png'}
            }
            
            config = type_config.get(notification_type, type_config['info'])
            
            notification_payload = {
                'title': f"{config['emoji']} System Notification",
                'body': message,
                'icon': config['icon'],
                'badge': '/icons/badge-icon.png',
                'data': {
                    'type': notification_type,
                    'message': message,
                    'url': '/settings'
                },
                'timestamp': datetime.now().timestamp() * 1000
            }
            
            return self._send_to_all_subscriptions(notification_payload)
            
        except Exception as e:
            logger.error(f"Erro ao enviar notificação do sistema: {e}")
            return {'sent': 0, 'failed': 1}
    
    def _send_to_all_subscriptions(self, payload: Dict) -> Dict[str, int]:
        """Envia notificação para todas as assinaturas"""
        sent_count = 0
        failed_count = 0
        invalid_subscriptions = []
        
        payload_json = json.dumps(payload)
        
        for subscription in self.subscriptions:
            try:
                webpush(
                    subscription_info=subscription,
                    data=payload_json,
                    vapid_private_key=self.vapid_private_key,
                    vapid_claims=self.vapid_claims
                )
                sent_count += 1
                
            except WebPushException as e:
                logger.error(f"Erro ao enviar push para {subscription['endpoint'][:50]}...: {e}")
                
                # Se a assinatura é inválida (410 Gone), marcar para remoção
                if e.response and e.response.status_code == 410:
                    invalid_subscriptions.append(subscription)
                
                failed_count += 1
                
            except Exception as e:
                logger.error(f"Erro inesperado ao enviar push: {e}")
                failed_count += 1
        
        # Remover assinaturas inválidas
        for invalid_sub in invalid_subscriptions:
            self.remove_subscription(invalid_sub['endpoint'])
        
        logger.info(f"Push notifications enviadas: {sent_count} sucesso, {failed_count} falhas")
        
        return {
            'sent': sent_count,
            'failed': failed_count,
            'removed_invalid': len(invalid_subscriptions)
        }
    
    def get_subscription_count(self) -> int:
        """Retorna número de assinaturas ativas"""
        return len(self.subscriptions)
    
    def test_notification(self) -> Dict[str, int]:
        """Envia notificação de teste"""
        test_payload = {
            'title': '🧪 Test Notification',
            'body': 'CryptoSignals push notifications are working!',
            'icon': '/icons/test-icon.png',
            'badge': '/icons/badge-icon.png',
            'data': {
                'test': True,
                'timestamp': datetime.now().isoformat()
            }
        }
        
        return self._send_to_all_subscriptions(test_payload)
