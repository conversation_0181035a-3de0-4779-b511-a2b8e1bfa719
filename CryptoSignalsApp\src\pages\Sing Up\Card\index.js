import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity } from 'react-native';
import styles from './styles';

const SignUpCard = () => {
  const [nickname, setNickname] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  const handleCreateAccount = () => {
    // Implemente a lógica para criar a conta aqui.
  };

  return (
    <View style={styles.container}>
      <Text style={styles.welcomeText}>Welcome to our Community!</Text>
      <Text style={styles.infoText}>Espero que você aproveite, mas primeiro precisamos de algumas informações sobre você.</Text>

      <TextInput
        placeholder="Apelido"
        style={styles.input}
        value={nickname}
        onChangeText={(text) => setNickname(text)}
      />

      <TextInput
        placeholder="Email"
        style={styles.input}
        value={email}
        onChangeText={(text) => setEmail(text)}
        keyboardType="email-address"
      />

      <TextInput
        placeholder="Criar senha"
        style={styles.input}
        value={password}
        onChangeText={(text) => setPassword(text)}
        secureTextEntry={true}
      />

      <TouchableOpacity style={styles.createAccountButton} onPress={handleCreateAccount}>
        <Text style={styles.createAccountButtonText}>Create Account</Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.goBackButton}>
        <Text style={styles.goBackButtonText}>Voltar</Text>
      </TouchableOpacity>
    </View>
  );
};

export default SignUpCard;
