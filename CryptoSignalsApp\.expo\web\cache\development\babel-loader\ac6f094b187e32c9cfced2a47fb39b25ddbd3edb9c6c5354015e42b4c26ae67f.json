{"ast": null, "code": "import Animated from \"react-native-web/dist/exports/Animated\";\nimport I18nManager from \"react-native-web/dist/exports/I18nManager\";\nvar add = Animated.add;\nexport function forUIKit(_ref) {\n  var current = _ref.current,\n    next = _ref.next,\n    layouts = _ref.layouts;\n  var defaultOffset = 100;\n  var leftSpacing = 27;\n  var leftLabelOffset = layouts.leftLabel ? (layouts.screen.width - layouts.leftLabel.width) / 2 - leftSpacing : defaultOffset;\n  var titleLeftOffset = layouts.title ? (layouts.screen.width - layouts.title.width) / 2 - leftSpacing : defaultOffset;\n  var rightOffset = layouts.screen.width / 4;\n  var progress = add(current.progress.interpolate({\n    inputRange: [0, 1],\n    outputRange: [0, 1],\n    extrapolate: 'clamp'\n  }), next ? next.progress.interpolate({\n    inputRange: [0, 1],\n    outputRange: [0, 1],\n    extrapolate: 'clamp'\n  }) : 0);\n  return {\n    leftButtonStyle: {\n      opacity: progress.interpolate({\n        inputRange: [0.3, 1, 1.5],\n        outputRange: [0, 1, 0]\n      })\n    },\n    leftLabelStyle: {\n      transform: [{\n        translateX: progress.interpolate({\n          inputRange: [0, 1, 2],\n          outputRange: I18nManager.getConstants().isRTL ? [-rightOffset, 0, leftLabelOffset] : [leftLabelOffset, 0, -rightOffset]\n        })\n      }]\n    },\n    rightButtonStyle: {\n      opacity: progress.interpolate({\n        inputRange: [0.3, 1, 1.5],\n        outputRange: [0, 1, 0]\n      })\n    },\n    titleStyle: {\n      opacity: progress.interpolate({\n        inputRange: [0, 0.4, 1, 1.5],\n        outputRange: [0, 0.1, 1, 0]\n      }),\n      transform: [{\n        translateX: progress.interpolate({\n          inputRange: [0.5, 1, 2],\n          outputRange: I18nManager.getConstants().isRTL ? [-titleLeftOffset, 0, rightOffset] : [rightOffset, 0, -titleLeftOffset]\n        })\n      }]\n    },\n    backgroundStyle: {\n      transform: [{\n        translateX: progress.interpolate({\n          inputRange: [0, 1, 2],\n          outputRange: I18nManager.getConstants().isRTL ? [-layouts.screen.width, 0, layouts.screen.width] : [layouts.screen.width, 0, -layouts.screen.width]\n        })\n      }]\n    }\n  };\n}\nexport function forFade(_ref2) {\n  var current = _ref2.current,\n    next = _ref2.next;\n  var progress = add(current.progress.interpolate({\n    inputRange: [0, 1],\n    outputRange: [0, 1],\n    extrapolate: 'clamp'\n  }), next ? next.progress.interpolate({\n    inputRange: [0, 1],\n    outputRange: [0, 1],\n    extrapolate: 'clamp'\n  }) : 0);\n  var opacity = progress.interpolate({\n    inputRange: [0, 1, 2],\n    outputRange: [0, 1, 0]\n  });\n  return {\n    leftButtonStyle: {\n      opacity: opacity\n    },\n    rightButtonStyle: {\n      opacity: opacity\n    },\n    titleStyle: {\n      opacity: opacity\n    },\n    backgroundStyle: {\n      opacity: progress.interpolate({\n        inputRange: [0, 1, 1.9, 2],\n        outputRange: [0, 1, 1, 0]\n      })\n    }\n  };\n}\nexport function forSlideLeft(_ref3) {\n  var current = _ref3.current,\n    next = _ref3.next,\n    screen = _ref3.layouts.screen;\n  var progress = add(current.progress.interpolate({\n    inputRange: [0, 1],\n    outputRange: [0, 1],\n    extrapolate: 'clamp'\n  }), next ? next.progress.interpolate({\n    inputRange: [0, 1],\n    outputRange: [0, 1],\n    extrapolate: 'clamp'\n  }) : 0);\n  var translateX = progress.interpolate({\n    inputRange: [0, 1, 2],\n    outputRange: I18nManager.getConstants().isRTL ? [-screen.width, 0, screen.width] : [screen.width, 0, -screen.width]\n  });\n  var transform = [{\n    translateX: translateX\n  }];\n  return {\n    leftButtonStyle: {\n      transform: transform\n    },\n    rightButtonStyle: {\n      transform: transform\n    },\n    titleStyle: {\n      transform: transform\n    },\n    backgroundStyle: {\n      transform: transform\n    }\n  };\n}\nexport function forSlideRight(_ref4) {\n  var current = _ref4.current,\n    next = _ref4.next,\n    screen = _ref4.layouts.screen;\n  var progress = add(current.progress.interpolate({\n    inputRange: [0, 1],\n    outputRange: [0, 1],\n    extrapolate: 'clamp'\n  }), next ? next.progress.interpolate({\n    inputRange: [0, 1],\n    outputRange: [0, 1],\n    extrapolate: 'clamp'\n  }) : 0);\n  var translateX = progress.interpolate({\n    inputRange: [0, 1, 2],\n    outputRange: I18nManager.getConstants().isRTL ? [screen.width, 0, -screen.width] : [-screen.width, 0, screen.width]\n  });\n  var transform = [{\n    translateX: translateX\n  }];\n  return {\n    leftButtonStyle: {\n      transform: transform\n    },\n    rightButtonStyle: {\n      transform: transform\n    },\n    titleStyle: {\n      transform: transform\n    },\n    backgroundStyle: {\n      transform: transform\n    }\n  };\n}\nexport function forSlideUp(_ref5) {\n  var current = _ref5.current,\n    next = _ref5.next,\n    header = _ref5.layouts.header;\n  var progress = add(current.progress.interpolate({\n    inputRange: [0, 1],\n    outputRange: [0, 1],\n    extrapolate: 'clamp'\n  }), next ? next.progress.interpolate({\n    inputRange: [0, 1],\n    outputRange: [0, 1],\n    extrapolate: 'clamp'\n  }) : 0);\n  var translateY = progress.interpolate({\n    inputRange: [0, 1, 2],\n    outputRange: [-header.height, 0, -header.height]\n  });\n  var transform = [{\n    translateY: translateY\n  }];\n  return {\n    leftButtonStyle: {\n      transform: transform\n    },\n    rightButtonStyle: {\n      transform: transform\n    },\n    titleStyle: {\n      transform: transform\n    },\n    backgroundStyle: {\n      transform: transform\n    }\n  };\n}\nexport function forNoAnimation() {\n  return {};\n}", "map": {"version": 3, "names": ["add", "Animated", "forUIKit", "_ref", "current", "next", "layouts", "defaultOffset", "leftSpacing", "leftLabelOffset", "leftLabel", "screen", "width", "titleLeftOffset", "title", "rightOffset", "progress", "interpolate", "inputRange", "outputRange", "extrapolate", "leftButtonStyle", "opacity", "leftLabelStyle", "transform", "translateX", "I18nManager", "getConstants", "isRTL", "rightButtonStyle", "titleStyle", "backgroundStyle", "forFade", "_ref2", "forSlideLeft", "_ref3", "forSlideRight", "_ref4", "forSlideUp", "_ref5", "header", "translateY", "height", "forNoAnimation"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\@react-navigation\\stack\\src\\TransitionConfigs\\HeaderStyleInterpolators.tsx"], "sourcesContent": ["import { Animated, I18nManager } from 'react-native';\n\nimport type {\n  StackHeaderInterpolatedStyle,\n  StackHeaderInterpolationProps,\n} from '../types';\n\nconst { add } = Animated;\n\n/**\n * Standard UIKit style animation for the header where the title fades into the back button label.\n */\nexport function forUIKit({\n  current,\n  next,\n  layouts,\n}: StackHeaderInterpolationProps): StackHeaderInterpolatedStyle {\n  const defaultOffset = 100;\n  const leftSpacing = 27;\n\n  // The title and back button title should cross-fade to each other\n  // When screen is fully open, the title should be in center, and back title should be on left\n  // When screen is closing, the previous title will animate to back title's position\n  // And back title will animate to title's position\n  // We achieve this by calculating the offsets needed to translate title to back title's position and vice-versa\n  const leftLabelOffset = layouts.leftLabel\n    ? (layouts.screen.width - layouts.leftLabel.width) / 2 - leftSpacing\n    : defaultOffset;\n  const titleLeftOffset = layouts.title\n    ? (layouts.screen.width - layouts.title.width) / 2 - leftSpacing\n    : defaultOffset;\n\n  // When the current title is animating to right, it is centered in the right half of screen in middle of transition\n  // The back title also animates in from this position\n  const rightOffset = layouts.screen.width / 4;\n\n  const progress = add(\n    current.progress.interpolate({\n      inputRange: [0, 1],\n      outputRange: [0, 1],\n      extrapolate: 'clamp',\n    }),\n    next\n      ? next.progress.interpolate({\n          inputRange: [0, 1],\n          outputRange: [0, 1],\n          extrapolate: 'clamp',\n        })\n      : 0\n  );\n\n  return {\n    leftButtonStyle: {\n      opacity: progress.interpolate({\n        inputRange: [0.3, 1, 1.5],\n        outputRange: [0, 1, 0],\n      }),\n    },\n    leftLabelStyle: {\n      transform: [\n        {\n          translateX: progress.interpolate({\n            inputRange: [0, 1, 2],\n            outputRange: I18nManager.getConstants().isRTL\n              ? [-rightOffset, 0, leftLabelOffset]\n              : [leftLabelOffset, 0, -rightOffset],\n          }),\n        },\n      ],\n    },\n    rightButtonStyle: {\n      opacity: progress.interpolate({\n        inputRange: [0.3, 1, 1.5],\n        outputRange: [0, 1, 0],\n      }),\n    },\n    titleStyle: {\n      opacity: progress.interpolate({\n        inputRange: [0, 0.4, 1, 1.5],\n        outputRange: [0, 0.1, 1, 0],\n      }),\n      transform: [\n        {\n          translateX: progress.interpolate({\n            inputRange: [0.5, 1, 2],\n            outputRange: I18nManager.getConstants().isRTL\n              ? [-titleLeftOffset, 0, rightOffset]\n              : [rightOffset, 0, -titleLeftOffset],\n          }),\n        },\n      ],\n    },\n    backgroundStyle: {\n      transform: [\n        {\n          translateX: progress.interpolate({\n            inputRange: [0, 1, 2],\n            outputRange: I18nManager.getConstants().isRTL\n              ? [-layouts.screen.width, 0, layouts.screen.width]\n              : [layouts.screen.width, 0, -layouts.screen.width],\n          }),\n        },\n      ],\n    },\n  };\n}\n\n/**\n * Simple fade animation for the header elements.\n */\nexport function forFade({\n  current,\n  next,\n}: StackHeaderInterpolationProps): StackHeaderInterpolatedStyle {\n  const progress = add(\n    current.progress.interpolate({\n      inputRange: [0, 1],\n      outputRange: [0, 1],\n      extrapolate: 'clamp',\n    }),\n    next\n      ? next.progress.interpolate({\n          inputRange: [0, 1],\n          outputRange: [0, 1],\n          extrapolate: 'clamp',\n        })\n      : 0\n  );\n\n  const opacity = progress.interpolate({\n    inputRange: [0, 1, 2],\n    outputRange: [0, 1, 0],\n  });\n\n  return {\n    leftButtonStyle: { opacity },\n    rightButtonStyle: { opacity },\n    titleStyle: { opacity },\n    backgroundStyle: {\n      opacity: progress.interpolate({\n        inputRange: [0, 1, 1.9, 2],\n        outputRange: [0, 1, 1, 0],\n      }),\n    },\n  };\n}\n\n/**\n * Simple translate animation to translate the header to left.\n */\nexport function forSlideLeft({\n  current,\n  next,\n  layouts: { screen },\n}: StackHeaderInterpolationProps): StackHeaderInterpolatedStyle {\n  const progress = add(\n    current.progress.interpolate({\n      inputRange: [0, 1],\n      outputRange: [0, 1],\n      extrapolate: 'clamp',\n    }),\n    next\n      ? next.progress.interpolate({\n          inputRange: [0, 1],\n          outputRange: [0, 1],\n          extrapolate: 'clamp',\n        })\n      : 0\n  );\n\n  const translateX = progress.interpolate({\n    inputRange: [0, 1, 2],\n    outputRange: I18nManager.getConstants().isRTL\n      ? [-screen.width, 0, screen.width]\n      : [screen.width, 0, -screen.width],\n  });\n\n  const transform = [{ translateX }];\n\n  return {\n    leftButtonStyle: { transform },\n    rightButtonStyle: { transform },\n    titleStyle: { transform },\n    backgroundStyle: { transform },\n  };\n}\n\n/**\n * Simple translate animation to translate the header to right.\n */\nexport function forSlideRight({\n  current,\n  next,\n  layouts: { screen },\n}: StackHeaderInterpolationProps): StackHeaderInterpolatedStyle {\n  const progress = add(\n    current.progress.interpolate({\n      inputRange: [0, 1],\n      outputRange: [0, 1],\n      extrapolate: 'clamp',\n    }),\n    next\n      ? next.progress.interpolate({\n          inputRange: [0, 1],\n          outputRange: [0, 1],\n          extrapolate: 'clamp',\n        })\n      : 0\n  );\n\n  const translateX = progress.interpolate({\n    inputRange: [0, 1, 2],\n    outputRange: I18nManager.getConstants().isRTL\n      ? [screen.width, 0, -screen.width]\n      : [-screen.width, 0, screen.width],\n  });\n\n  const transform = [{ translateX }];\n\n  return {\n    leftButtonStyle: { transform },\n    rightButtonStyle: { transform },\n    titleStyle: { transform },\n    backgroundStyle: { transform },\n  };\n}\n\n/**\n * Simple translate animation to translate the header to slide up.\n */\nexport function forSlideUp({\n  current,\n  next,\n  layouts: { header },\n}: StackHeaderInterpolationProps): StackHeaderInterpolatedStyle {\n  const progress = add(\n    current.progress.interpolate({\n      inputRange: [0, 1],\n      outputRange: [0, 1],\n      extrapolate: 'clamp',\n    }),\n    next\n      ? next.progress.interpolate({\n          inputRange: [0, 1],\n          outputRange: [0, 1],\n          extrapolate: 'clamp',\n        })\n      : 0\n  );\n\n  const translateY = progress.interpolate({\n    inputRange: [0, 1, 2],\n    outputRange: [-header.height, 0, -header.height],\n  });\n\n  const transform = [{ translateY }];\n\n  return {\n    leftButtonStyle: { transform },\n    rightButtonStyle: { transform },\n    titleStyle: { transform },\n    backgroundStyle: { transform },\n  };\n}\n\nexport function forNoAnimation(): StackHeaderInterpolatedStyle {\n  return {};\n}\n"], "mappings": ";;AAOA,IAAQA,GAAA,GAAQC,QAAQ,CAAhBD,GAAA;AAKR,OAAO,SAASE,QAAQA,CAAAC,IAAA,EAIwC;EAAA,IAH9DC,OAAO,GAGuBD,IAAA,CAH9BC,OAAO;IACPC,IAAI,GAE0BF,IAAA,CAF9BE,IAAI;IACJC,OAAA,GAC8BH,IAAA,CAD9BG,OAAA;EAEA,IAAMC,aAAa,GAAG,GAAG;EACzB,IAAMC,WAAW,GAAG,EAAE;EAOtB,IAAMC,eAAe,GAAGH,OAAO,CAACI,SAAS,GACrC,CAACJ,OAAO,CAACK,MAAM,CAACC,KAAK,GAAGN,OAAO,CAACI,SAAS,CAACE,KAAK,IAAI,CAAC,GAAGJ,WAAW,GAClED,aAAa;EACjB,IAAMM,eAAe,GAAGP,OAAO,CAACQ,KAAK,GACjC,CAACR,OAAO,CAACK,MAAM,CAACC,KAAK,GAAGN,OAAO,CAACQ,KAAK,CAACF,KAAK,IAAI,CAAC,GAAGJ,WAAW,GAC9DD,aAAa;EAIjB,IAAMQ,WAAW,GAAGT,OAAO,CAACK,MAAM,CAACC,KAAK,GAAG,CAAC;EAE5C,IAAMI,QAAQ,GAAGhB,GAAG,CAClBI,OAAO,CAACY,QAAQ,CAACC,WAAW,CAAC;IAC3BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACnBC,WAAW,EAAE;EACf,CAAC,CAAC,EACFf,IAAI,GACAA,IAAI,CAACW,QAAQ,CAACC,WAAW,CAAC;IACxBC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACnBC,WAAW,EAAE;EACf,CAAC,CAAC,GACF,CAAC,CACN;EAED,OAAO;IACLC,eAAe,EAAE;MACfC,OAAO,EAAEN,QAAQ,CAACC,WAAW,CAAC;QAC5BC,UAAU,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;QACzBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;MACvB,CAAC;IACH,CAAC;IACDI,cAAc,EAAE;MACdC,SAAS,EAAE,CACT;QACEC,UAAU,EAAET,QAAQ,CAACC,WAAW,CAAC;UAC/BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UACrBC,WAAW,EAAEO,WAAW,CAACC,YAAY,EAAE,CAACC,KAAK,GACzC,CAAC,CAACb,WAAW,EAAE,CAAC,EAAEN,eAAe,CAAC,GAClC,CAACA,eAAe,EAAE,CAAC,EAAE,CAACM,WAAW;QACvC,CAAC;MACH,CAAC;IAEL,CAAC;IACDc,gBAAgB,EAAE;MAChBP,OAAO,EAAEN,QAAQ,CAACC,WAAW,CAAC;QAC5BC,UAAU,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;QACzBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;MACvB,CAAC;IACH,CAAC;IACDW,UAAU,EAAE;MACVR,OAAO,EAAEN,QAAQ,CAACC,WAAW,CAAC;QAC5BC,UAAU,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;QAC5BC,WAAW,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;MAC5B,CAAC,CAAC;MACFK,SAAS,EAAE,CACT;QACEC,UAAU,EAAET,QAAQ,CAACC,WAAW,CAAC;UAC/BC,UAAU,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;UACvBC,WAAW,EAAEO,WAAW,CAACC,YAAY,EAAE,CAACC,KAAK,GACzC,CAAC,CAACf,eAAe,EAAE,CAAC,EAAEE,WAAW,CAAC,GAClC,CAACA,WAAW,EAAE,CAAC,EAAE,CAACF,eAAe;QACvC,CAAC;MACH,CAAC;IAEL,CAAC;IACDkB,eAAe,EAAE;MACfP,SAAS,EAAE,CACT;QACEC,UAAU,EAAET,QAAQ,CAACC,WAAW,CAAC;UAC/BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UACrBC,WAAW,EAAEO,WAAW,CAACC,YAAY,EAAE,CAACC,KAAK,GACzC,CAAC,CAACtB,OAAO,CAACK,MAAM,CAACC,KAAK,EAAE,CAAC,EAAEN,OAAO,CAACK,MAAM,CAACC,KAAK,CAAC,GAChD,CAACN,OAAO,CAACK,MAAM,CAACC,KAAK,EAAE,CAAC,EAAE,CAACN,OAAO,CAACK,MAAM,CAACC,KAAK;QACrD,CAAC;MACH,CAAC;IAEL;EACF,CAAC;AACH;AAKA,OAAO,SAASoB,OAAOA,CAAAC,KAAA,EAGyC;EAAA,IAF9D7B,OAAO,GAEuB6B,KAAA,CAF9B7B,OAAO;IACPC,IAAA,GAC8B4B,KAAA,CAD9B5B,IAAA;EAEA,IAAMW,QAAQ,GAAGhB,GAAG,CAClBI,OAAO,CAACY,QAAQ,CAACC,WAAW,CAAC;IAC3BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACnBC,WAAW,EAAE;EACf,CAAC,CAAC,EACFf,IAAI,GACAA,IAAI,CAACW,QAAQ,CAACC,WAAW,CAAC;IACxBC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACnBC,WAAW,EAAE;EACf,CAAC,CAAC,GACF,CAAC,CACN;EAED,IAAME,OAAO,GAAGN,QAAQ,CAACC,WAAW,CAAC;IACnCC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACrBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;EACvB,CAAC,CAAC;EAEF,OAAO;IACLE,eAAe,EAAE;MAAEC,OAAA,EAAAA;IAAQ,CAAC;IAC5BO,gBAAgB,EAAE;MAAEP,OAAA,EAAAA;IAAQ,CAAC;IAC7BQ,UAAU,EAAE;MAAER,OAAA,EAAAA;IAAQ,CAAC;IACvBS,eAAe,EAAE;MACfT,OAAO,EAAEN,QAAQ,CAACC,WAAW,CAAC;QAC5BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QAC1BC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;MAC1B,CAAC;IACH;EACF,CAAC;AACH;AAKA,OAAO,SAASe,YAAYA,CAAAC,KAAA,EAIoC;EAAA,IAH9D/B,OAAO,GAGuB+B,KAAA,CAH9B/B,OAAO;IACPC,IAAI,GAE0B8B,KAAA,CAF9B9B,IAAI;IACOM,MAAA,GACmBwB,KAAA,CAD9B7B,OAAO,CAAIK,MAAA;EAEX,IAAMK,QAAQ,GAAGhB,GAAG,CAClBI,OAAO,CAACY,QAAQ,CAACC,WAAW,CAAC;IAC3BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACnBC,WAAW,EAAE;EACf,CAAC,CAAC,EACFf,IAAI,GACAA,IAAI,CAACW,QAAQ,CAACC,WAAW,CAAC;IACxBC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACnBC,WAAW,EAAE;EACf,CAAC,CAAC,GACF,CAAC,CACN;EAED,IAAMK,UAAU,GAAGT,QAAQ,CAACC,WAAW,CAAC;IACtCC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACrBC,WAAW,EAAEO,WAAW,CAACC,YAAY,EAAE,CAACC,KAAK,GACzC,CAAC,CAACjB,MAAM,CAACC,KAAK,EAAE,CAAC,EAAED,MAAM,CAACC,KAAK,CAAC,GAChC,CAACD,MAAM,CAACC,KAAK,EAAE,CAAC,EAAE,CAACD,MAAM,CAACC,KAAK;EACrC,CAAC,CAAC;EAEF,IAAMY,SAAS,GAAG,CAAC;IAAEC,UAAA,EAAAA;EAAW,CAAC,CAAC;EAElC,OAAO;IACLJ,eAAe,EAAE;MAAEG,SAAA,EAAAA;IAAU,CAAC;IAC9BK,gBAAgB,EAAE;MAAEL,SAAA,EAAAA;IAAU,CAAC;IAC/BM,UAAU,EAAE;MAAEN,SAAA,EAAAA;IAAU,CAAC;IACzBO,eAAe,EAAE;MAAEP,SAAA,EAAAA;IAAU;EAC/B,CAAC;AACH;AAKA,OAAO,SAASY,aAAaA,CAAAC,KAAA,EAImC;EAAA,IAH9DjC,OAAO,GAGuBiC,KAAA,CAH9BjC,OAAO;IACPC,IAAI,GAE0BgC,KAAA,CAF9BhC,IAAI;IACOM,MAAA,GACmB0B,KAAA,CAD9B/B,OAAO,CAAIK,MAAA;EAEX,IAAMK,QAAQ,GAAGhB,GAAG,CAClBI,OAAO,CAACY,QAAQ,CAACC,WAAW,CAAC;IAC3BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACnBC,WAAW,EAAE;EACf,CAAC,CAAC,EACFf,IAAI,GACAA,IAAI,CAACW,QAAQ,CAACC,WAAW,CAAC;IACxBC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACnBC,WAAW,EAAE;EACf,CAAC,CAAC,GACF,CAAC,CACN;EAED,IAAMK,UAAU,GAAGT,QAAQ,CAACC,WAAW,CAAC;IACtCC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACrBC,WAAW,EAAEO,WAAW,CAACC,YAAY,EAAE,CAACC,KAAK,GACzC,CAACjB,MAAM,CAACC,KAAK,EAAE,CAAC,EAAE,CAACD,MAAM,CAACC,KAAK,CAAC,GAChC,CAAC,CAACD,MAAM,CAACC,KAAK,EAAE,CAAC,EAAED,MAAM,CAACC,KAAK;EACrC,CAAC,CAAC;EAEF,IAAMY,SAAS,GAAG,CAAC;IAAEC,UAAA,EAAAA;EAAW,CAAC,CAAC;EAElC,OAAO;IACLJ,eAAe,EAAE;MAAEG,SAAA,EAAAA;IAAU,CAAC;IAC9BK,gBAAgB,EAAE;MAAEL,SAAA,EAAAA;IAAU,CAAC;IAC/BM,UAAU,EAAE;MAAEN,SAAA,EAAAA;IAAU,CAAC;IACzBO,eAAe,EAAE;MAAEP,SAAA,EAAAA;IAAU;EAC/B,CAAC;AACH;AAKA,OAAO,SAASc,UAAUA,CAAAC,KAAA,EAIsC;EAAA,IAH9DnC,OAAO,GAGuBmC,KAAA,CAH9BnC,OAAO;IACPC,IAAI,GAE0BkC,KAAA,CAF9BlC,IAAI;IACOmC,MAAA,GACmBD,KAAA,CAD9BjC,OAAO,CAAIkC,MAAA;EAEX,IAAMxB,QAAQ,GAAGhB,GAAG,CAClBI,OAAO,CAACY,QAAQ,CAACC,WAAW,CAAC;IAC3BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACnBC,WAAW,EAAE;EACf,CAAC,CAAC,EACFf,IAAI,GACAA,IAAI,CAACW,QAAQ,CAACC,WAAW,CAAC;IACxBC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACnBC,WAAW,EAAE;EACf,CAAC,CAAC,GACF,CAAC,CACN;EAED,IAAMqB,UAAU,GAAGzB,QAAQ,CAACC,WAAW,CAAC;IACtCC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACrBC,WAAW,EAAE,CAAC,CAACqB,MAAM,CAACE,MAAM,EAAE,CAAC,EAAE,CAACF,MAAM,CAACE,MAAM;EACjD,CAAC,CAAC;EAEF,IAAMlB,SAAS,GAAG,CAAC;IAAEiB,UAAA,EAAAA;EAAW,CAAC,CAAC;EAElC,OAAO;IACLpB,eAAe,EAAE;MAAEG,SAAA,EAAAA;IAAU,CAAC;IAC9BK,gBAAgB,EAAE;MAAEL,SAAA,EAAAA;IAAU,CAAC;IAC/BM,UAAU,EAAE;MAAEN,SAAA,EAAAA;IAAU,CAAC;IACzBO,eAAe,EAAE;MAAEP,SAAA,EAAAA;IAAU;EAC/B,CAAC;AACH;AAEA,OAAO,SAASmB,cAAcA,CAAA,EAAiC;EAC7D,OAAO,CAAC,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}