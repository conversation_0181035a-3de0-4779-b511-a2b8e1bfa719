## Parte do código responsável pelos sinais

O repositório possui uma classe principal `SignalGenerator` (em **main.py**) que coordena a geração e o monitoramento dos sinais. Os sinais são gerados pelos métodos como `check_scalp_signals`, `check_breakout_signals`, etc., os quais invocam as funções de análise de cada estratégia. Por exemplo, em `check_scalp_signals` chama-se `self.scalp_strategy.analyze_symbol(symbol)` para avaliar um par e determinar um sinal de **Scalp**. Se um sinal válido é retornado (e não for duplicado), ele é formatado e enviado via o objeto `TelegramSender`. No caso de sucesso no envio, o código adiciona o sinal a uma lista `open_signals` para acompanhamento futuro. Esse dicionário inclui o par, o tipo de sinal (LONG/SHORT), preço de entrada, níveis de take profit previstos (40%, 60%, 80%, 100% para scalp) inicialmente marcados como não atingidos, alavancagem, timestamp de início e flags de notificação/fechamento.

Cada estratégia tem seu código em `estrategias/*.py`. Por exemplo, a **estratégia de Scalping** (arquivo *scalp\_strategy.py*) utiliza RSI e EMAs em dados de 1 minuto para determinar entradas LONG/SHORT. Se as condições são satisfeitas (e.g. RSI < 40 e EMA5 > EMA20 para LONG), o método retorna `'LONG'` ou `'SHORT'` com o preço de entrada atual e um conjunto de quatro preços alvo (calculados a partir de \~5% de movimentação do preço). Já a **estratégia de Breakout com Volume** (*breakout\_volume.py*) verifica se houve um volume anômalo nas últimas horas (volume das últimas 6h maior que o maior volume diário dos 30 dias anteriores) e identifica um nível de resistência recente. Se o preço atual rompeu acima dessa resistência com volume anômalo, o código retorna um sinal de entrada (LONG) com preço atual, **stop-loss** logo abaixo do suporte recente, e **take-profit** calculado como três vezes o risco (RR=1:3) acima da entrada. Estrategias como Inside Bar, MFI e Swing possuem lógica semelhante (buscam padrões específicos e definem um único alvo de lucro e stop).

A formatação das mensagens de sinal é feita pela classe `SignalFormatter` (*utils/signal\_formatter.py*). Ela monta as strings enviadas ao Telegram incluindo par, tipo (📈 LONG ou 📉 SHORT), preços de entrada, stop e alvos, alavancagem sugerida e prazo de validade do sinal. Nota-se que o formato segue um padrão consistente conforme descrito (por exemplo, o scalp mostra quatro níveis de take profit e orienta stop de 2-3% abaixo da entrada). Importante: cada mensagem inclui uma linha “⏰ Signal valid until...” com um horário calculado (e.g. scalp adiciona 4 horas à hora atual, breakout 6h, etc.), indicando até quando o sinal é válido.

## Lógica de monitoramento e possíveis falhas

O acompanhamento dos sinais (interpretação de resultados) é feito principalmente pelo método `monitor_open_signals` dentro de **main.py**. Ele é executado periodicamente no loop principal após a geração dos sinais. A lógica percorre cada sinal em `self.open_signals` e verifica: (1) se já foi marcado como fechado; (2) se o tempo de validade expirou; (3) caso contrário, obtém o preço atual e calcula o percentual de variação em relação ao preço de entrada. Para sinais **Scalp**, o código então compara esse % de lucro (ou perda) com cada nível de profit não notificado (40%, 60%, 80%, 100%). Quando o preço atinge ou excede um nível (ex: +40% do movimento planejado), ele envia automaticamente uma notificação desse nível atingido – no caso, gerando e postando uma imagem de **profit** atingido com os detalhes (par, tipo, preço atual etc.). Cada nível atingido é marcado em `signal['notified_levels']`. Assim que todos os níveis forem atingidos, o sinal é marcado como **fechado** e removido do monitoramento.

Quando o tempo máximo de validade é atingido sem que todos os alvos tenham sido alcançados, o sistema também lida com isso. O código checa se o tempo decorrido excede `SIGNAL_VALIDITY_MINUTES` (configurado no .env, padrão 30 minutos). Caso positivo, ele identifica quais níveis de profit não foram atingidos e envia uma mensagem de aviso no Telegram indicando **tempo limite atingido** e listando os profit targets não alcançados. Essa é precisamente a origem das mensagens como *"⚠️ Tempo limite atingido para XYZ... Profits não atingidos: 40%, 60%, 80%, 100%"* que o usuário está observando. Em seguida, o sinal é marcado como fechado e retirado da lista de monitoramento.

Duas possíveis falhas nessa lógica explicam por que muitos sinais “expiram” sem bater alvos:

* **Janela de monitoramento insuficiente:** O monitoramento, por padrão, roda a cada 5 minutos (definido por `SIGNAL_INTERVAL_SECONDS`, tipicamente 300s). Isso pode fazer com que picos rápidos no preço passem despercebidos. O código verifica apenas o preço atual no momento do loop – ele não armazena o *high* atingido nem escuta continuamente. Portanto, se o preço tocou brevemente um nível de take profit (por exemplo 2% acima da entrada, equivalente a 40%) e recuou antes da próxima iteração, o sistema **não reconhecerá** que o alvo foi atingido. Isso leva a falsos negativos onde um target foi atingido no mercado, mas o bot não notificou e eventualmente declarou o sinal expirado. Essa limitação de amostragem pode explicar parte dos “sinais expirados sem atingir alvo”.

* **Monitoramento apenas para sinais de Scalp:** Note que o código só adiciona sinais de scalp a `open_signals` para acompanhamento contínuo. No trecho de breakout, por exemplo, não há código adicionando o sinal de breakout na lista de monitoramento (não há um append similar ao do scalp). Ou seja, sinais de breakout, Inside Bar, etc., não têm sua execução verificada no método `monitor_open_signals` (o qual por padrão monitora níveis de 40-100% destinados ao scalp). Assim, se um sinal de breakout atinge ou não seu alvo, o sistema não envia notificações nem “expira” esses sinais explicitamente – o usuário pode ficar sem feedback a menos que monitore manualmente. Isso pode dar a impressão de que o bot “ignora” esses sinais após enviá-los. Idealmente, o código deveria acompanhar também sinais de alvo único (por exemplo, marcar quando o TP único foi atingido ou se expirou). A ausência desse acompanhamento específico não impacta os *logs* mencionados pelo usuário (que citam os 4 níveis), mas é uma limitação importante a ser corrigida.

## Estratégia de Breakout com Volume (avaliação da aplicação)

O código da estratégia de breakout com volume (**BreakoutVolumeStrategy**) corresponde, em termos gerais, ao que foi proposto: identifica rompimentos de resistência com volume anômalo. A implementação verifica se o volume somado das últimas 6 horas supera o maior volume diário dos últimos 30 dias – isso assegura que apenas movimentos realmente fora do comum gerem sinais. Em seguida, ele calcula níveis de **suporte** e **resistência** recentes usando um intervalo de consolidação (últimos 4 candles de 15min). Se o preço atual ultrapassou a resistência identificada, considera-se que houve um breakout, e então define-se: **preço de entrada** = preço atual, **Stop Loss** = pouco abaixo do suporte (suporte \* 0.998) e **Take Profit** com base em razão de risco\:retorno 1:3. Ou seja, a meta de lucro fica aproximadamente a três vezes a distância entre entrada e stop, acima do preço de entrada.

Essa lógica está **correta em termos conceituais**, mas alguns pontos merecem atenção: (1) **Direção única** – o código aparentemente só considera breakouts para cima (LONG). Não há verificação de rompimento de suporte para acionar um SHORT (o parâmetro `signal_type` nem é retornado, diferente do scalp). Assim, oportunidades de short em quebra de suporte com volume não geram sinal, podendo limitar os resultados. (2) **Timing do sinal** – exigir que o preço *já* esteja acima da resistência pode fazer o sinal chegar “atrasado”, após boa parte do movimento inicial ter ocorrido. O trade pode começar já próximo de uma exaustão de curto prazo. Isso, combinado ao alvo distante (3x o risco), pode explicar porque muitos desses sinais não atingem o take profit antes de reverter ou expirar. (3) **Volume anômalo muito raro** – a condição de volume é bastante restritiva. Embora selecione apenas movimentos significativos (o que em tese melhora a assertividade), pode gerar poucos sinais e possivelmente em momentos de volatilidade extrema. Se o sinal for gerado já no topo de um candle de alta explosiva, a probabilidade de uma correção antes de alcançar 3\*risco é alta, fazendo o trade falhar no TP. Em resumo, a estratégia em si está implementada conforme descrito, mas a **eficácia** pode estar prejudicada pela escolha dos critérios (volume e RR=3) e pelo curto tempo de monitoramento efetivo no bot.

## Calibragem de parâmetros (TP, SL, validade dos sinais)

Analisando os parâmetros usados nas estratégias e no sistema, nota-se possíveis desalinhamentos que contribuem para baixo desempenho:

* **Take Profits (alvos)** – No scalp, define-se por padrão uma movimentação alvo de \~5% (profit\_percentage = 0.05) a partir do preço de entrada, distribuída em quatro níveis incrementais. Isso equivale a \~+2% (40% de 5%) no primeiro alvo, até +5% no último. Em contextos de scalp (timeframe 1min) e criptoativos de grande capitalização, uma variação de +2% a +5% em poucos minutos pode ser pouco frequente. Assim, muitos sinais não chegam nem ao primeiro alvo antes de reverter. Por outro lado, a mensagem sugere stop de 2-3% abaixo – ou seja, arrisca-se \~2% para tentar ganhar até 5%. Esse risco\:retorno é bom, porém se o movimento de 5% raramente se concretiza em 30 minutos, o resultado prático é que a maioria dos trades ou fica em zero (expira) ou atinge o stop (caso o usuário tenha colocado). **Sugestão:** reduzir os alvos percentuais para scalping (por exemplo 1-3%) ou tornar adaptativo conforme volatilidade do ativo, aumentando a chance de pelo menos o primeiro target ser atingido rapidamente.

* **Stop Loss** – Observa-se que o código do scalp não retorna um valor de stop loss explícito, apenas recomenda na mensagem “stop 2-3% abaixo da entrada”. Essa falta de definição programática pode levar a decisões inconsistentes do usuário. Já nas outras estratégias, o stop é calculado conforme estrutura da estratégia (e.g. breakout usa suporte\*0.998, inside bar abaixo da mínima da inside bar, etc., o que é adequado). Para melhorar, o bot poderia **calcular e apresentar um stop loss concreto para scalp** com base em volatilidade recente (por ex, um múltiplo do ATR curto) ou porcentagem fixa mesmo (2%) aplicada ao preço de entrada, em vez de apenas recomendar genericamente.

* **Validade do sinal (duration)** – Há um aparente descompasso entre o que as mensagens de sinal indicam e o que o código realmente impõe. As mensagens formatadas definem validade de 4h para scalp, 6h breakout, 8h inside bar etc., coerente com a ideia de cada estratégia ter um horizonte diferente. No entanto, o parâmetro global `SIGNAL_VALIDITY_MINUTES` padrão é 30 minutos, e é ele que o monitor utiliza para expirar sinais. **Se este valor não foi ajustado**, o bot está fechando todos os sinais após 30 min, independentemente da estratégia – contradizendo a validade informada ao usuário. Isso certamente resultaria em muitos scalps abortados antes do movimento de 5% completar (lembrando que no design original havia até 4h) e virtualmente todos breakouts/inside bar expirando muito antes do prazo esperado (p.ex. breakout deveria durar até 6h, mas fecharia em 0,5h). É crucial alinhar essa configuração: ou definir validade diferenciada por tipo de sinal (ideal) ou pelo menos aumentar `SIGNAL_VALIDITY_MINUTES` para cobrir o maior prazo desejado (p.ex. 480 min se swing trade 48h for incluído). Caso contrário, os sinais de médio prazo nunca terão chance de sucesso dentro do tempo imposto.

* **Frequência de monitoramento** – conforme mencionado, 5 minutos entre verificações pode ser longo para scalps. Se viável, reduzir esse intervalo (ou implementar monitoramento contínuo em uma *thread* separada) ajudaria a captar *hits* nos alvos com mais precisão. Todavia, deve-se balancear para não sobrecarregar a API da Binance.

* **Seleção de ativos e critérios** – O código escolhe pares aleatoriamente de listas predefinidas para analisar a cada iteração. É importante garantir que essas listas (SCALP\_SYMBOLS, etc.) contenham ativos com liquidez e volatilidade condizentes com cada estratégia. Se os ativos não forem adequados (por exemplo, aplicar scalp em moedas muito pouco voláteis), os alvos de profit dificilmente serão atingidos. Verifique também os limiares dos indicadores: no scalp, RSI < 40 pode ser um critério conservador (poderia perder ralis iniciais); talvez calibrar RSI/EMAs ou adicionar filtros (volume, tendência maior) pudesse melhorar a taxa de acerto.

## Recomendações de correção e melhorias

**1. Ajustar a duração de validade dos sinais:** Para resolver o principal problema de sinais expirando precocemente, alinhe o tempo de monitoramento com o indicado nas mensagens. Uma melhoria seria tornar `SIGNAL_VALIDITY_MINUTES` variável por estratégia – por exemplo, armazenar junto com cada sinal aberto o seu prazo (4h para scalp, 6h breakout etc.) e usar isso na checagem em vez de um único valor fixo. Se quiser simplificar, pode-se ao menos elevar o padrão (ex.: 240 min) para dar mais fôlego aos trades. Isso deve reduzir significativamente o número de *“sinal expirado”* sem hits, especialmente para estratégias de prazo maior.

**2. Melhorar a lógica de monitoramento de preços:** Implementar formas de não perder detecções de alvos atingidos. Uma opção é consultar não só o último preço mas também a máxima/mínima ocorrida desde a última verificação. A API da Binance permite obter candles ou preços high/low em intervalos curtos – assim, mesmo que o pico tenha sido momentâneo, o sistema marcaria o nível como atingido. Outra alternativa é rodar a verificação com intervalo menor (por ex., a cada 30s ou 1min para scalp) durante a validade do sinal. Garantir também que, se o usuário configurar um número menor de níveis de take profit (ou apenas um TP nas estratégias comuns), o código trate apropriadamente – atualmente está fixo nos 4 níveis do scalp para notificação. Isso requer tornar a lista de níveis dinâmica por tipo de sinal.

**3. Incluir acompanhamento para **todos** os tipos de sinal:** Conforme analisado, apenas os scalps estão sendo rastreados ativamente. Recomenda-se estender o `open_signals` para incluir sinais de breakout, inside bar, etc., possivelmente com uma estrutura ligeiramente diferente (já que eles têm um TP único). Por exemplo, poderia adicionar no `open_signals` entradas com `'take_profit': valor` e `'stop_loss': valor` para essas estratégias, e no monitoramento verificar: se o preço atual atingiu o take\_profit (ou excedeu, no caso LONG), notificar (e fechar o sinal); se atingiu stop (se houver intenção de notificar stop hits), ou se expirou o tempo, avisar que expirou sem alcançar o alvo. Essa unificação torna o comportamento do bot mais consistente e informativo. Atualmente, a ausência de feedback pós-sinal pode prejudicar o usuário na avaliação de desempenho.

**4. Refinar os parâmetros da estratégia:** Avaliar historicamente se 5% de alvo em 4h (scalp) ou RR3 em 6h (breakout) têm sido realistas. Caso a maioria dos sinais não atinja nem o primeiro nível, pode ser necessário reduzir metas ou tornar condicional: por exemplo, scalp poderia ter níveis 20%, 40%, 60%, 80% de um movimento de 2-3%, em vez de 5%. No breakout, talvez usar RR 1:2 ou 1:2.5 aumente a taxa de sucesso, ou então permitir saídas parciais (realizar parte do lucro em 1x ou 2x o risco e mover stop para zero). Além disso, incorporando detecção de **breakdown** (rompimento de suporte para operações SHORT) aproveita mais cenários, já que o mercado também cai com volume. Garantir que a lista de símbolos e horários operacionais estejam ajustados – o código tem janela de operação configurável (6h–21h por padrão), então verificar se não está perdendo movimentos fora desse horário (se for aceitável operar 24h, por exemplo, pode-se estender).

**5. Revisão de logs e depuração:** Como parte da melhoria, use os *logs* detalhados gerados (`gerador_sinais.log`) para identificar padrões. Verifique entradas do tipo “Sinal de X enviado” vs “Profit Y% atingido” vs “Tempo limite atingido” para quantificar onde está o gargalo. Pode-se adicionar no log quanto do movimento cada sinal fez no máximo (peak) antes de expirar – isso ajudaria a ajustar os níveis de TP. Se muitos sinais estão expirando a, digamos, +30% ou +35% sem chegar a 40%, talvez reduzir os steps para 30/50/70/90% ou similar. A ideia é iterar nesses parâmetros com base nos dados reais coletados.

