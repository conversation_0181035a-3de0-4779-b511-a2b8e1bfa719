{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"children\", \"size\", \"style\", \"theme\", \"visible\"],\n  _excluded2 = [\"backgroundColor\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nimport * as React from 'react';\nimport Animated from \"react-native-web/dist/exports/Animated\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport useWindowDimensions from \"react-native-web/dist/exports/useWindowDimensions\";\nimport { useInternalTheme } from \"../core/theming\";\nimport { black, white } from \"../styles/themes/v2/colors\";\nimport getContrastingColor from \"../utils/getContrastingColor\";\nvar defaultSize = 20;\nvar Badge = function Badge(_ref) {\n  var children = _ref.children,\n    _ref$size = _ref.size,\n    size = _ref$size === void 0 ? defaultSize : _ref$size,\n    style = _ref.style,\n    themeOverrides = _ref.theme,\n    _ref$visible = _ref.visible,\n    visible = _ref$visible === void 0 ? true : _ref$visible,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var _theme$colors;\n  var theme = useInternalTheme(themeOverrides);\n  var _React$useRef = React.useRef(new Animated.Value(visible ? 1 : 0)),\n    opacity = _React$useRef.current;\n  var _useWindowDimensions = useWindowDimensions(),\n    fontScale = _useWindowDimensions.fontScale;\n  var isFirstRendering = React.useRef(true);\n  var scale = theme.animation.scale;\n  React.useEffect(function () {\n    if (isFirstRendering.current) {\n      isFirstRendering.current = false;\n      return;\n    }\n    Animated.timing(opacity, {\n      toValue: visible ? 1 : 0,\n      duration: 150 * scale,\n      useNativeDriver: true\n    }).start();\n  }, [visible, opacity, scale]);\n  var _ref2 = StyleSheet.flatten(style) || {},\n    _ref2$backgroundColor = _ref2.backgroundColor,\n    backgroundColor = _ref2$backgroundColor === void 0 ? theme.isV3 ? theme.colors.error : (_theme$colors = theme.colors) === null || _theme$colors === void 0 ? void 0 : _theme$colors.notification : _ref2$backgroundColor,\n    restStyle = _objectWithoutProperties(_ref2, _excluded2);\n  var textColor = theme.isV3 ? theme.colors.onError : getContrastingColor(backgroundColor, white, black);\n  var borderRadius = size / 2;\n  var paddingHorizontal = theme.isV3 ? 3 : 4;\n  return React.createElement(Animated.Text, _extends({\n    numberOfLines: 1,\n    style: [_objectSpread(_objectSpread({\n      opacity: opacity,\n      backgroundColor: backgroundColor,\n      color: textColor,\n      fontSize: size * 0.5\n    }, !theme.isV3 && theme.fonts.regular), {}, {\n      lineHeight: size / fontScale,\n      height: size,\n      minWidth: size,\n      borderRadius: borderRadius,\n      paddingHorizontal: paddingHorizontal\n    }), styles.container, restStyle]\n  }, rest), children);\n};\nexport default Badge;\nvar styles = StyleSheet.create({\n  container: {\n    alignSelf: 'flex-end',\n    textAlign: 'center',\n    textAlignVertical: 'center',\n    overflow: 'hidden'\n  }\n});", "map": {"version": 3, "names": ["React", "Animated", "StyleSheet", "useWindowDimensions", "useInternalTheme", "black", "white", "getContrastingColor", "defaultSize", "Badge", "_ref", "children", "_ref$size", "size", "style", "themeOverrides", "theme", "_ref$visible", "visible", "rest", "_objectWithoutProperties", "_excluded", "_theme$colors", "_React$useRef", "useRef", "Value", "opacity", "current", "_useWindowDimensions", "fontScale", "isFirstRendering", "scale", "animation", "useEffect", "timing", "toValue", "duration", "useNativeDriver", "start", "_ref2", "flatten", "_ref2$backgroundColor", "backgroundColor", "isV3", "colors", "error", "notification", "restStyle", "_excluded2", "textColor", "onError", "borderRadius", "paddingHorizontal", "createElement", "Text", "_extends", "numberOfLines", "_objectSpread", "color", "fontSize", "fonts", "regular", "lineHeight", "height", "min<PERSON><PERSON><PERSON>", "styles", "container", "create", "alignSelf", "textAlign", "textAlignVertical", "overflow"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\Badge.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  Animated,\n  StyleProp,\n  StyleSheet,\n  TextStyle,\n  useWindowDimensions,\n} from 'react-native';\n\nimport { useInternalTheme } from '../core/theming';\nimport { black, white } from '../styles/themes/v2/colors';\nimport type { ThemeProp } from '../types';\nimport getContrastingColor from '../utils/getContrastingColor';\n\nconst defaultSize = 20;\n\nexport type Props = React.ComponentProps<typeof Animated.Text> & {\n  /**\n   * Whether the badge is visible\n   */\n  visible?: boolean;\n  /**\n   * Content of the `Badge`.\n   */\n  children?: string | number;\n  /**\n   * Size of the `Badge`.\n   */\n  size?: number;\n  style?: StyleProp<TextStyle>;\n  ref?: React.RefObject<typeof Animated.Text>;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n};\n\n/**\n * Badges are small status descriptors for UI elements.\n * A badge consists of a small circle, typically containing a number or other short set of characters, that appears in proximity to another object.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { Badge } from 'react-native-paper';\n *\n * const MyComponent = () => (\n *   <Badge>3</Badge>\n * );\n *\n * export default MyComponent;\n * ```\n */\nconst Badge = ({\n  children,\n  size = defaultSize,\n  style,\n  theme: themeOverrides,\n  visible = true,\n  ...rest\n}: Props) => {\n  const theme = useInternalTheme(themeOverrides);\n  const { current: opacity } = React.useRef<Animated.Value>(\n    new Animated.Value(visible ? 1 : 0)\n  );\n  const { fontScale } = useWindowDimensions();\n\n  const isFirstRendering = React.useRef<boolean>(true);\n\n  const {\n    animation: { scale },\n  } = theme;\n\n  React.useEffect(() => {\n    // Do not run animation on very first rendering\n    if (isFirstRendering.current) {\n      isFirstRendering.current = false;\n      return;\n    }\n\n    Animated.timing(opacity, {\n      toValue: visible ? 1 : 0,\n      duration: 150 * scale,\n      useNativeDriver: true,\n    }).start();\n  }, [visible, opacity, scale]);\n\n  const {\n    backgroundColor = theme.isV3\n      ? theme.colors.error\n      : theme.colors?.notification,\n    ...restStyle\n  } = (StyleSheet.flatten(style) || {}) as TextStyle;\n\n  const textColor = theme.isV3\n    ? theme.colors.onError\n    : getContrastingColor(backgroundColor, white, black);\n\n  const borderRadius = size / 2;\n\n  const paddingHorizontal = theme.isV3 ? 3 : 4;\n\n  return (\n    <Animated.Text\n      numberOfLines={1}\n      style={[\n        {\n          opacity,\n          backgroundColor,\n          color: textColor,\n          fontSize: size * 0.5,\n          ...(!theme.isV3 && theme.fonts.regular),\n          lineHeight: size / fontScale,\n          height: size,\n          minWidth: size,\n          borderRadius,\n          paddingHorizontal,\n        },\n        styles.container,\n        restStyle,\n      ]}\n      {...rest}\n    >\n      {children}\n    </Animated.Text>\n  );\n};\n\nexport default Badge;\n\nconst styles = StyleSheet.create({\n  container: {\n    alignSelf: 'flex-end',\n    textAlign: 'center',\n    textAlignVertical: 'center',\n    overflow: 'hidden',\n  },\n});\n"], "mappings": ";;;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,mBAAA;AAS9B,SAASC,gBAAgB;AACzB,SAASC,KAAK,EAAEC,KAAK;AAErB,OAAOC,mBAAmB;AAE1B,IAAMC,WAAW,GAAG,EAAE;AAuCtB,IAAMC,KAAK,GAAG,SAARA,KAAKA,CAAAC,IAAA,EAOE;EAAA,IANXC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;IAAAC,SAAA,GAAAF,IAAA,CACRG,IAAI;IAAJA,IAAI,GAAAD,SAAA,cAAGJ,WAAW,GAAAI,SAAA;IAClBE,KAAK,GAAAJ,IAAA,CAALI,KAAK;IACEC,cAAc,GAAAL,IAAA,CAArBM,KAAK;IAAAC,YAAA,GAAAP,IAAA,CACLQ,OAAO;IAAPA,OAAO,GAAAD,YAAA,cAAG,IAAI,GAAAA,YAAA;IACXE,IAAA,GAAAC,wBAAA,CAAAV,IAAA,EAAAW,SAAA;EACQ,IAAAC,aAAA;EACX,IAAMN,KAAK,GAAGZ,gBAAgB,CAACW,cAAc,CAAC;EAC9C,IAAAQ,aAAA,GAA6BvB,KAAK,CAACwB,MAAM,CACvC,IAAIvB,QAAQ,CAACwB,KAAK,CAACP,OAAO,GAAG,CAAC,GAAG,CAAC,CACpC,CAAC;IAFgBQ,OAAA,GAAAH,aAAA,CAATI,OAAO;EAGf,IAAAC,oBAAA,GAAsBzB,mBAAmB,CAAC,CAAC;IAAnC0B,SAAA,GAAAD,oBAAA,CAAAC,SAAA;EAER,IAAMC,gBAAgB,GAAG9B,KAAK,CAACwB,MAAM,CAAU,IAAI,CAAC;EAEpD,IACeO,KAAA,GACXf,KAAK,CADPgB,SAAS,CAAID,KAAA;EAGf/B,KAAK,CAACiC,SAAS,CAAC,YAAM;IAEpB,IAAIH,gBAAgB,CAACH,OAAO,EAAE;MAC5BG,gBAAgB,CAACH,OAAO,GAAG,KAAK;MAChC;IACF;IAEA1B,QAAQ,CAACiC,MAAM,CAACR,OAAO,EAAE;MACvBS,OAAO,EAAEjB,OAAO,GAAG,CAAC,GAAG,CAAC;MACxBkB,QAAQ,EAAE,GAAG,GAAGL,KAAK;MACrBM,eAAe,EAAE;IACnB,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;EACZ,CAAC,EAAE,CAACpB,OAAO,EAAEQ,OAAO,EAAEK,KAAK,CAAC,CAAC;EAE7B,IAAAQ,KAAA,GAKKrC,UAAU,CAACsC,OAAO,CAAC1B,KAAK,CAAC,IAAI,CAAC,CAAe;IAAA2B,qBAAA,GAAAF,KAAA,CAJhDG,eAAe;IAAfA,eAAe,GAAAD,qBAAA,cAAGzB,KAAK,CAAC2B,IAAI,GACxB3B,KAAK,CAAC4B,MAAM,CAACC,KAAK,IAAAvB,aAAA,GAClBN,KAAK,CAAC4B,MAAM,cAAAtB,aAAA,uBAAZA,aAAA,CAAcwB,YAAY,GAAAL,qBAAA;IAC3BM,SAAA,GAAA3B,wBAAA,CAAAmB,KAAA,EAAAS,UAAA;EAGL,IAAMC,SAAS,GAAGjC,KAAK,CAAC2B,IAAI,GACxB3B,KAAK,CAAC4B,MAAM,CAACM,OAAO,GACpB3C,mBAAmB,CAACmC,eAAe,EAAEpC,KAAK,EAAED,KAAK,CAAC;EAEtD,IAAM8C,YAAY,GAAGtC,IAAI,GAAG,CAAC;EAE7B,IAAMuC,iBAAiB,GAAGpC,KAAK,CAAC2B,IAAI,GAAG,CAAC,GAAG,CAAC;EAE5C,OACE3C,KAAA,CAAAqD,aAAA,CAACpD,QAAQ,CAACqD,IAAI,EAAAC,QAAA;IACZC,aAAa,EAAE,CAAE;IACjB1C,KAAK,EAAE,CAAA2C,aAAA,CAAAA,aAAA;MAEH/B,OAAO,EAAPA,OAAO;MACPgB,eAAe,EAAfA,eAAe;MACfgB,KAAK,EAAET,SAAS;MAChBU,QAAQ,EAAE9C,IAAI,GAAG;IAAG,GAChB,CAACG,KAAK,CAAC2B,IAAI,IAAI3B,KAAK,CAAC4C,KAAK,CAACC,OAAO;MACtCC,UAAU,EAAEjD,IAAI,GAAGgB,SAAS;MAC5BkC,MAAM,EAAElD,IAAI;MACZmD,QAAQ,EAAEnD,IAAI;MACdsC,YAAY,EAAZA,YAAY;MACZC,iBAAA,EAAAA;IAAA,IAEFa,MAAM,CAACC,SAAS,EAChBnB,SAAS;EACT,GACE5B,IAAI,GAEPR,QACY,CAAC;AAEpB,CAAC;AAED,eAAeF,KAAK;AAEpB,IAAMwD,MAAM,GAAG/D,UAAU,CAACiE,MAAM,CAAC;EAC/BD,SAAS,EAAE;IACTE,SAAS,EAAE,UAAU;IACrBC,SAAS,EAAE,QAAQ;IACnBC,iBAAiB,EAAE,QAAQ;IAC3BC,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}