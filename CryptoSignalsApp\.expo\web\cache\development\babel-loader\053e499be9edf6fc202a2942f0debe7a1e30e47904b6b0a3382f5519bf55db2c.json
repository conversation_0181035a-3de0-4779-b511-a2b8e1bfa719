{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"children\", \"headerConfig\", \"activityState\", \"shouldFreeze\", \"stackPresentation\", \"sheetAllowedDetents\", \"contentStyle\", \"style\", \"screenId\", \"unstable_sheetFooter\"];\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nimport * as React from 'react';\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport warnOnce from 'warn-once';\nimport DebugContainer from \"./DebugContainer\";\nimport { ScreenStackHeaderConfig } from \"./ScreenStackHeaderConfig\";\nimport Screen from \"./Screen\";\nimport ScreenStack from \"./ScreenStack\";\nimport { RNSScreensRefContext } from \"../contexts\";\nimport { FooterComponent } from \"./ScreenFooter\";\nfunction ScreenStackItem(_ref, ref) {\n  var _headerConfig$largeTi, _headerConfig$largeTi2;\n  var children = _ref.children,\n    headerConfig = _ref.headerConfig,\n    activityState = _ref.activityState,\n    shouldFreeze = _ref.shouldFreeze,\n    stackPresentation = _ref.stackPresentation,\n    sheetAllowedDetents = _ref.sheetAllowedDetents,\n    contentStyle = _ref.contentStyle,\n    style = _ref.style,\n    screenId = _ref.screenId,\n    unstable_sheetFooter = _ref.unstable_sheetFooter,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var currentScreenRef = React.useRef(null);\n  var screenRefs = React.useContext(RNSScreensRefContext);\n  React.useImperativeHandle(ref, function () {\n    return currentScreenRef.current;\n  });\n  var isHeaderInModal = Platform.OS === 'android' ? false : stackPresentation !== 'push' && (headerConfig == null ? void 0 : headerConfig.hidden) === false;\n  var headerHiddenPreviousRef = React.useRef(headerConfig == null ? void 0 : headerConfig.hidden);\n  React.useEffect(function () {\n    warnOnce(Platform.OS !== 'android' && stackPresentation !== 'push' && headerHiddenPreviousRef.current !== (headerConfig == null ? void 0 : headerConfig.hidden), `Dynamically changing header's visibility in modals will result in remounting the screen and losing all local state.`);\n    headerHiddenPreviousRef.current = headerConfig == null ? void 0 : headerConfig.hidden;\n  }, [headerConfig == null ? void 0 : headerConfig.hidden, stackPresentation]);\n  var content = React.createElement(React.Fragment, null, React.createElement(DebugContainer, {\n    style: [stackPresentation === 'formSheet' ? Platform.OS === 'ios' ? styles.absolute : sheetAllowedDetents === 'fitToContents' ? null : styles.container : styles.container, contentStyle],\n    stackPresentation: stackPresentation != null ? stackPresentation : 'push'\n  }, children), React.createElement(ScreenStackHeaderConfig, headerConfig), stackPresentation === 'formSheet' && unstable_sheetFooter && React.createElement(FooterComponent, null, unstable_sheetFooter()));\n  var internalScreenStyle;\n  if (stackPresentation === 'formSheet' && contentStyle) {\n    var flattenContentStyles = StyleSheet.flatten(contentStyle);\n    internalScreenStyle = {\n      backgroundColor: flattenContentStyles == null ? void 0 : flattenContentStyles.backgroundColor\n    };\n  }\n  return React.createElement(Screen, _extends({\n    ref: function ref(node) {\n      currentScreenRef.current = node;\n      if (screenRefs === null) {\n        console.warn('Looks like RNSScreensRefContext is missing. Make sure the ScreenStack component is wrapped in it');\n        return;\n      }\n      var currentRefs = screenRefs.current;\n      if (node === null) {\n        delete currentRefs[screenId];\n      } else {\n        currentRefs[screenId] = {\n          current: node\n        };\n      }\n    },\n    enabled: true,\n    isNativeStack: true,\n    activityState: activityState,\n    shouldFreeze: shouldFreeze,\n    stackPresentation: stackPresentation,\n    hasLargeHeader: (_headerConfig$largeTi = headerConfig == null ? void 0 : headerConfig.largeTitle) != null ? _headerConfig$largeTi : false,\n    sheetAllowedDetents: sheetAllowedDetents,\n    style: [style, internalScreenStyle]\n  }, rest), isHeaderInModal ? React.createElement(ScreenStack, {\n    style: styles.container\n  }, React.createElement(Screen, {\n    enabled: true,\n    isNativeStack: true,\n    activityState: activityState,\n    shouldFreeze: shouldFreeze,\n    hasLargeHeader: (_headerConfig$largeTi2 = headerConfig == null ? void 0 : headerConfig.largeTitle) != null ? _headerConfig$largeTi2 : false,\n    style: StyleSheet.absoluteFill\n  }, content)) : content);\n}\nexport default React.forwardRef(ScreenStackItem);\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1\n  },\n  absolute: {\n    position: 'absolute',\n    top: 0,\n    start: 0,\n    end: 0\n  }\n});", "map": {"version": 3, "names": ["React", "Platform", "StyleSheet", "warnOnce", "DebugContainer", "ScreenStackHeaderConfig", "Screen", "ScreenStack", "RNSScreensRefContext", "FooterComponent", "ScreenStackItem", "_ref", "ref", "_headerConfig$largeTi", "_headerConfig$largeTi2", "children", "headerConfig", "activityState", "shouldFreeze", "stackPresentation", "sheetAllowedDetents", "contentStyle", "style", "screenId", "unstable_sheetFooter", "rest", "_objectWithoutProperties", "_excluded", "currentScreenRef", "useRef", "screenRefs", "useContext", "useImperativeHandle", "current", "isHeaderInModal", "OS", "hidden", "headerHiddenPreviousRef", "useEffect", "content", "createElement", "Fragment", "styles", "absolute", "container", "internalScreenStyle", "flattenContentStyles", "flatten", "backgroundColor", "_extends", "node", "console", "warn", "currentRefs", "enabled", "isNativeStack", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "largeTitle", "absoluteFill", "forwardRef", "create", "flex", "position", "top", "start", "end"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\react-native-screens\\src\\components\\ScreenStackItem.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  Platform,\n  type StyleProp,\n  StyleSheet,\n  type ViewStyle,\n  View,\n} from 'react-native';\nimport warnOnce from 'warn-once';\n\nimport DebugContainer from './DebugContainer';\nimport { ScreenProps, ScreenStackHeaderConfigProps } from '../types';\nimport { ScreenStackHeaderConfig } from './ScreenStackHeaderConfig';\nimport Screen from './Screen';\nimport ScreenStack from './ScreenStack';\nimport { RNSScreensRefContext } from '../contexts';\nimport { FooterComponent } from './ScreenFooter';\n\ntype Props = Omit<\n  ScreenProps,\n  'enabled' | 'isNativeStack' | 'hasLargeHeader'\n> & {\n  screenId: string;\n  headerConfig?: ScreenStackHeaderConfigProps;\n  contentStyle?: StyleProp<ViewStyle>;\n};\n\nfunction ScreenStackItem(\n  {\n    children,\n    headerConfig,\n    activityState,\n    shouldFreeze,\n    stackPresentation,\n    sheetAllowedDetents,\n    contentStyle,\n    style,\n    screenId,\n    // eslint-disable-next-line camelcase\n    unstable_sheetFooter,\n    ...rest\n  }: Props,\n  ref: React.ForwardedRef<View>,\n) {\n  const currentScreenRef = React.useRef<View | null>(null);\n  const screenRefs = React.useContext(RNSScreensRefContext);\n\n  React.useImperativeHandle(ref, () => currentScreenRef.current!);\n\n  const isHeaderInModal =\n    Platform.OS === 'android'\n      ? false\n      : stackPresentation !== 'push' && headerConfig?.hidden === false;\n\n  const headerHiddenPreviousRef = React.useRef(headerConfig?.hidden);\n\n  React.useEffect(() => {\n    warnOnce(\n      Platform.OS !== 'android' &&\n        stackPresentation !== 'push' &&\n        headerHiddenPreviousRef.current !== headerConfig?.hidden,\n      `Dynamically changing header's visibility in modals will result in remounting the screen and losing all local state.`,\n    );\n\n    headerHiddenPreviousRef.current = headerConfig?.hidden;\n  }, [headerConfig?.hidden, stackPresentation]);\n\n  const content = (\n    <>\n      <DebugContainer\n        style={[\n          stackPresentation === 'formSheet'\n            ? Platform.OS === 'ios'\n              ? styles.absolute\n              : sheetAllowedDetents === 'fitToContents'\n              ? null\n              : styles.container\n            : styles.container,\n          contentStyle,\n        ]}\n        stackPresentation={stackPresentation ?? 'push'}>\n        {children}\n      </DebugContainer>\n      {/**\n       * `HeaderConfig` needs to be the direct child of `Screen` without any intermediate `View`\n       * We don't render it conditionally based on visibility to make it possible to dynamically render a custom `header`\n       * Otherwise dynamically rendering a custom `header` leaves the native header visible\n       *\n       * https://github.com/software-mansion/react-native-screens/blob/main/guides/GUIDE_FOR_LIBRARY_AUTHORS.md#screenstackheaderconfig\n       *\n       * HeaderConfig must not be first child of a Screen.\n       * See https://github.com/software-mansion/react-native-screens/pull/1825\n       * for detailed explanation.\n       */}\n      <ScreenStackHeaderConfig {...headerConfig} />\n      {/* eslint-disable-next-line camelcase */}\n      {stackPresentation === 'formSheet' && unstable_sheetFooter && (\n        <FooterComponent>{unstable_sheetFooter()}</FooterComponent>\n      )}\n    </>\n  );\n\n  // We take backgroundColor from contentStyle and apply it on Screen.\n  // This allows to workaround one issue with truncated\n  // content with formSheet presentation.\n  let internalScreenStyle;\n\n  if (stackPresentation === 'formSheet' && contentStyle) {\n    const flattenContentStyles = StyleSheet.flatten(contentStyle);\n    internalScreenStyle = {\n      backgroundColor: flattenContentStyles?.backgroundColor,\n    };\n  }\n\n  return (\n    <Screen\n      ref={node => {\n        currentScreenRef.current = node;\n\n        if (screenRefs === null) {\n          console.warn(\n            'Looks like RNSScreensRefContext is missing. Make sure the ScreenStack component is wrapped in it',\n          );\n          return;\n        }\n\n        const currentRefs = screenRefs.current;\n\n        if (node === null) {\n          // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n          delete currentRefs[screenId];\n        } else {\n          currentRefs[screenId] = { current: node };\n        }\n      }}\n      enabled\n      isNativeStack\n      activityState={activityState}\n      shouldFreeze={shouldFreeze}\n      stackPresentation={stackPresentation}\n      hasLargeHeader={headerConfig?.largeTitle ?? false}\n      sheetAllowedDetents={sheetAllowedDetents}\n      style={[style, internalScreenStyle]}\n      {...rest}>\n      {isHeaderInModal ? (\n        <ScreenStack style={styles.container}>\n          <Screen\n            enabled\n            isNativeStack\n            activityState={activityState}\n            shouldFreeze={shouldFreeze}\n            hasLargeHeader={headerConfig?.largeTitle ?? false}\n            style={StyleSheet.absoluteFill}>\n            {content}\n          </Screen>\n        </ScreenStack>\n      ) : (\n        content\n      )}\n    </Screen>\n  );\n}\n\nexport default React.forwardRef(ScreenStackItem);\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n  },\n  absolute: {\n    position: 'absolute',\n    top: 0,\n    start: 0,\n    end: 0,\n  },\n});\n"], "mappings": ";;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAAA,OAAAC,UAAA;AAQ9B,OAAOC,QAAQ,MAAM,WAAW;AAEhC,OAAOC,cAAc;AAErB,SAASC,uBAAuB;AAChC,OAAOC,MAAM;AACb,OAAOC,WAAW;AAClB,SAASC,oBAAoB;AAC7B,SAASC,eAAe;AAWxB,SAASC,eAAeA,CAAAC,IAAA,EAetBC,GAA6B,EAC7B;EAAA,IAAAC,qBAAA,EAAAC,sBAAA;EAAA,IAdEC,QAAQ,GAAAJ,IAAA,CAARI,QAAQ;IACRC,YAAY,GAAAL,IAAA,CAAZK,YAAY;IACZC,aAAa,GAAAN,IAAA,CAAbM,aAAa;IACbC,YAAY,GAAAP,IAAA,CAAZO,YAAY;IACZC,iBAAiB,GAAAR,IAAA,CAAjBQ,iBAAiB;IACjBC,mBAAmB,GAAAT,IAAA,CAAnBS,mBAAmB;IACnBC,YAAY,GAAAV,IAAA,CAAZU,YAAY;IACZC,KAAK,GAAAX,IAAA,CAALW,KAAK;IACLC,QAAQ,GAAAZ,IAAA,CAARY,QAAQ;IAERC,oBAAoB,GAAAb,IAAA,CAApBa,oBAAoB;IACjBC,IAAA,GAAAC,wBAAA,CAAAf,IAAA,EAAAgB,SAAA;EAIL,IAAMC,gBAAgB,GAAG5B,KAAK,CAAC6B,MAAM,CAAc,IAAI,CAAC;EACxD,IAAMC,UAAU,GAAG9B,KAAK,CAAC+B,UAAU,CAACvB,oBAAoB,CAAC;EAEzDR,KAAK,CAACgC,mBAAmB,CAACpB,GAAG,EAAE;IAAA,OAAMgB,gBAAgB,CAACK,OAAQ;EAAA,EAAC;EAE/D,IAAMC,eAAe,GACnBjC,QAAQ,CAACkC,EAAE,KAAK,SAAS,GACrB,KAAK,GACLhB,iBAAiB,KAAK,MAAM,IAAI,CAAAH,YAAY,oBAAZA,YAAY,CAAEoB,MAAM,MAAK,KAAK;EAEpE,IAAMC,uBAAuB,GAAGrC,KAAK,CAAC6B,MAAM,CAACb,YAAY,oBAAZA,YAAY,CAAEoB,MAAM,CAAC;EAElEpC,KAAK,CAACsC,SAAS,CAAC,YAAM;IACpBnC,QAAQ,CACNF,QAAQ,CAACkC,EAAE,KAAK,SAAS,IACvBhB,iBAAiB,KAAK,MAAM,IAC5BkB,uBAAuB,CAACJ,OAAO,MAAKjB,YAAY,oBAAZA,YAAY,CAAEoB,MAAM,GAC1D,qHACF,CAAC;IAEDC,uBAAuB,CAACJ,OAAO,GAAGjB,YAAY,oBAAZA,YAAY,CAAEoB,MAAM;EACxD,CAAC,EAAE,CAACpB,YAAY,oBAAZA,YAAY,CAAEoB,MAAM,EAAEjB,iBAAiB,CAAC,CAAC;EAE7C,IAAMoB,OAAO,GACXvC,KAAA,CAAAwC,aAAA,CAAAxC,KAAA,CAAAyC,QAAA,QACEzC,KAAA,CAAAwC,aAAA,CAACpC,cAAc;IACbkB,KAAK,EAAE,CACLH,iBAAiB,KAAK,WAAW,GAC7BlB,QAAQ,CAACkC,EAAE,KAAK,KAAK,GACnBO,MAAM,CAACC,QAAQ,GACfvB,mBAAmB,KAAK,eAAe,GACvC,IAAI,GACJsB,MAAM,CAACE,SAAS,GAClBF,MAAM,CAACE,SAAS,EACpBvB,YAAY,CACZ;IACFF,iBAAiB,EAAEA,iBAAiB,WAAjBA,iBAAiB,GAAI;EAAO,GAC9CJ,QACa,CAAC,EAYjBf,KAAA,CAAAwC,aAAA,CAACnC,uBAAuB,EAAKW,YAAe,CAAC,EAE5CG,iBAAiB,KAAK,WAAW,IAAIK,oBAAoB,IACxDxB,KAAA,CAAAwC,aAAA,CAAC/B,eAAe,QAAEe,oBAAoB,CAAC,CAAmB,CAE5D,CACH;EAKD,IAAIqB,mBAAmB;EAEvB,IAAI1B,iBAAiB,KAAK,WAAW,IAAIE,YAAY,EAAE;IACrD,IAAMyB,oBAAoB,GAAG5C,UAAU,CAAC6C,OAAO,CAAC1B,YAAY,CAAC;IAC7DwB,mBAAmB,GAAG;MACpBG,eAAe,EAAEF,oBAAoB,oBAApBA,oBAAoB,CAAEE;IACzC,CAAC;EACH;EAEA,OACEhD,KAAA,CAAAwC,aAAA,CAAClC,MAAM,EAAA2C,QAAA;IACLrC,GAAG,EAAE,SAALA,GAAGA,CAAEsC,IAAI,EAAI;MACXtB,gBAAgB,CAACK,OAAO,GAAGiB,IAAI;MAE/B,IAAIpB,UAAU,KAAK,IAAI,EAAE;QACvBqB,OAAO,CAACC,IAAI,CACV,kGACF,CAAC;QACD;MACF;MAEA,IAAMC,WAAW,GAAGvB,UAAU,CAACG,OAAO;MAEtC,IAAIiB,IAAI,KAAK,IAAI,EAAE;QAEjB,OAAOG,WAAW,CAAC9B,QAAQ,CAAC;MAC9B,CAAC,MAAM;QACL8B,WAAW,CAAC9B,QAAQ,CAAC,GAAG;UAAEU,OAAO,EAAEiB;QAAK,CAAC;MAC3C;IACF,CAAE;IACFI,OAAO;IACPC,aAAa;IACbtC,aAAa,EAAEA,aAAc;IAC7BC,YAAY,EAAEA,YAAa;IAC3BC,iBAAiB,EAAEA,iBAAkB;IACrCqC,cAAc,GAAA3C,qBAAA,GAAEG,YAAY,oBAAZA,YAAY,CAAEyC,UAAU,YAAA5C,qBAAA,GAAI,KAAM;IAClDO,mBAAmB,EAAEA,mBAAoB;IACzCE,KAAK,EAAE,CAACA,KAAK,EAAEuB,mBAAmB;EAAE,GAChCpB,IAAI,GACPS,eAAe,GACdlC,KAAA,CAAAwC,aAAA,CAACjC,WAAW;IAACe,KAAK,EAAEoB,MAAM,CAACE;EAAU,GACnC5C,KAAA,CAAAwC,aAAA,CAAClC,MAAM;IACLgD,OAAO;IACPC,aAAa;IACbtC,aAAa,EAAEA,aAAc;IAC7BC,YAAY,EAAEA,YAAa;IAC3BsC,cAAc,GAAA1C,sBAAA,GAAEE,YAAY,oBAAZA,YAAY,CAAEyC,UAAU,YAAA3C,sBAAA,GAAI,KAAM;IAClDQ,KAAK,EAAEpB,UAAU,CAACwD;EAAa,GAC9BnB,OACK,CACG,CAAC,GAEdA,OAEI,CAAC;AAEb;AAEA,eAAevC,KAAK,CAAC2D,UAAU,CAACjD,eAAe,CAAC;AAEhD,IAAMgC,MAAM,GAAGxC,UAAU,CAAC0D,MAAM,CAAC;EAC/BhB,SAAS,EAAE;IACTiB,IAAI,EAAE;EACR,CAAC;EACDlB,QAAQ,EAAE;IACRmB,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,CAAC;IACNC,KAAK,EAAE,CAAC;IACRC,GAAG,EAAE;EACP;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}