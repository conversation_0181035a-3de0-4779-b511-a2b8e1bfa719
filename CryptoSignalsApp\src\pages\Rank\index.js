import React, { useState, useEffect } from 'react';
import { View, Text, FlatList, Image } from 'react-native';
import styles from './styles';

const Rank = () => {
  const [rankData, setRankData] = useState([]);

  useEffect(() => {
    // Simule a obtenção dos dados de classificação dos usuários
    const fetchData = async () => {
      try {
        // Faça uma chamada à API ou acesse seu banco de dados para obter os dados de classificação
        // Aqui, estou usando dados fictícios para ilustração
        const response = await fetch('https://api.example.com/rank');
        const data = await response.json();
        setRankData(data);
      } catch (error) {
        console.error('Erro ao obter dados de classificação:', error);
      }
    };

    fetchData();
  }, []);

  const renderRankItem = ({ item, index }) => (
    <View style={styles.rankItem}>
      <Text style={styles.rank}>{index + 1}</Text>
      <Image source={{ uri: item.profilePicture }} style={styles.profilePicture} />
      <View style={styles.userInfo}>
        <Text style={styles.username}>{item.username}</Text>
        <Text style={styles.badgeTitle}>{item.badgeTitle}</Text>
      </View>
      <Text style={styles.points}>{item.points} pontos</Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Classificação dos 50 Melhores</Text>
      <FlatList
        data={rankData}
        keyExtractor={(item, index) => index.toString()}
        renderItem={renderRankItem}
      />
    </View>
  );
};

export default Rank;
