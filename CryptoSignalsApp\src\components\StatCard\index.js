import React from 'react';
import { View, Text } from 'react-native';
import Card from '../Card';

const StatCard = ({ 
  title, 
  value, 
  change, 
  changeType = 'positive', // 'positive', 'negative', 'neutral'
  icon,
  subtitle
}) => {
  const getChangeColor = () => {
    switch (changeType) {
      case 'positive': return '#4CAF50';
      case 'negative': return '#F44336';
      default: return '#8a8a8a';
    }
  };

  return (
    <Card style={{ flex: 1, minHeight: 100 }}>
      <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-start' }}>
        <View style={{ flex: 1 }}>
          <Text style={{ 
            color: '#8a8a8a', 
            fontSize: 12, 
            fontFamily: 'Poppins_400Regular',
            marginBottom: 4
          }}>
            {title}
          </Text>
          <Text style={{ 
            color: '#fff', 
            fontSize: 20, 
            fontFamily: 'Poppins_600SemiBold',
            marginBottom: 2
          }}>
            {value}
          </Text>
          {change && (
            <Text style={{ 
              color: getChangeColor(), 
              fontSize: 12, 
              fontFamily: 'Poppins_500Medium'
            }}>
              {change}
            </Text>
          )}
          {subtitle && (
            <Text style={{ 
              color: '#8a8a8a', 
              fontSize: 10, 
              fontFamily: 'Poppins_400Regular',
              marginTop: 2
            }}>
              {subtitle}
            </Text>
          )}
        </View>
        {icon && (
          <View style={{ 
            backgroundColor: '#FECB37', 
            borderRadius: 8, 
            padding: 8,
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <Text style={{ fontSize: 16 }}>{icon}</Text>
          </View>
        )}
      </View>
    </Card>
  );
};

export default StatCard;
