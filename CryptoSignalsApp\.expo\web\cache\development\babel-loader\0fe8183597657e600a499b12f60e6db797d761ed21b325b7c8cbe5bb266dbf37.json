{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport React, { useState, useEffect } from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport FlatList from \"react-native-web/dist/exports/FlatList\";\nimport TouchableOpacity from \"react-native-web/dist/exports/TouchableOpacity\";\nimport TextInput from \"react-native-web/dist/exports/TextInput\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar ForumPage = function ForumPage() {\n  var _useState = useState([]),\n    _useState2 = _slicedToArray(_useState, 2),\n    posts = _useState2[0],\n    setPosts = _useState2[1];\n  var _useState3 = useState(''),\n    _useState4 = _slicedToArray(_useState3, 2),\n    newPostText = _useState4[0],\n    setNewPostText = _useState4[1];\n  useEffect(function () {}, []);\n  var handleAddPost = function handleAddPost() {};\n  return _jsxs(View, {\n    style: styles.container,\n    children: [_jsx(Text, {\n      style: styles.pageTitle,\n      children: \"F\\xF3rum\"\n    }), _jsx(TextInput, {\n      style: styles.input,\n      placeholder: \"Digite sua postagem...\",\n      value: newPostText,\n      onChangeText: function onChangeText(text) {\n        return setNewPostText(text);\n      }\n    }), _jsx(TouchableOpacity, {\n      style: styles.addButton,\n      onPress: handleAddPost,\n      children: _jsx(Text, {\n        style: styles.addButtonText,\n        children: \"Adicionar Postagem\"\n      })\n    }), _jsx(FlatList, {\n      data: posts,\n      keyExtractor: function keyExtractor(item) {\n        return item.id.toString();\n      },\n      renderItem: function renderItem(_ref) {\n        var item = _ref.item;\n        return _jsxs(View, {\n          style: styles.postContainer,\n          children: [_jsx(Text, {\n            style: styles.postText,\n            children: item.text\n          }), _jsxs(Text, {\n            style: styles.postAuthor,\n            children: [\"Autor: \", item.author]\n          })]\n        });\n      }\n    })]\n  });\n};\nexport default ForumPage;\nvar styles = {\n  container: {\n    flex: 1,\n    padding: 16,\n    backgroundColor: '#FFFFFF'\n  },\n  pageTitle: {\n    fontSize: 24,\n    fontWeight: 'bold',\n    marginBottom: 16\n  },\n  input: {\n    borderWidth: 1,\n    borderColor: '#CCCCCC',\n    borderRadius: 4,\n    padding: 8,\n    marginBottom: 16\n  },\n  addButton: {\n    backgroundColor: '#3176c4',\n    borderRadius: 4,\n    padding: 12,\n    alignItems: 'center'\n  },\n  addButtonText: {\n    color: '#FFFFFF',\n    fontWeight: 'bold'\n  },\n  postContainer: {\n    borderWidth: 1,\n    borderColor: '#CCCCCC',\n    borderRadius: 4,\n    padding: 16,\n    marginBottom: 16\n  },\n  postText: {\n    fontSize: 16,\n    marginBottom: 8\n  },\n  postAuthor: {\n    fontSize: 12,\n    color: '#888888'\n  }\n};", "map": {"version": 3, "names": ["React", "useState", "useEffect", "View", "Text", "FlatList", "TouchableOpacity", "TextInput", "jsx", "_jsx", "jsxs", "_jsxs", "ForumPage", "_useState", "_useState2", "_slicedToArray", "posts", "setPosts", "_useState3", "_useState4", "newPostText", "setNewPostText", "handleAddPost", "style", "styles", "container", "children", "pageTitle", "input", "placeholder", "value", "onChangeText", "text", "addButton", "onPress", "addButtonText", "data", "keyExtractor", "item", "id", "toString", "renderItem", "_ref", "postC<PERSON><PERSON>", "postText", "<PERSON><PERSON><PERSON><PERSON>", "author", "flex", "padding", "backgroundColor", "fontSize", "fontWeight", "marginBottom", "borderWidth", "borderColor", "borderRadius", "alignItems", "color"], "sources": ["E:/CryptoSignalsApp/src/pages/Forum/index.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { View, Text, FlatList, TouchableOpacity, TextInput } from 'react-native';\r\n\r\n// Importe seus estilos aqui\r\n\r\nconst ForumPage = () => {\r\n  const [posts, setPosts] = useState([]);\r\n  const [newPostText, setNewPostText] = useState('');\r\n\r\n  useEffect(() => {\r\n    // Carregue os posts do fórum da sua fonte de dados (API, banco de dados, etc.)\r\n    // Atualize o estado \"posts\" com os dados carregados.\r\n  }, []);\r\n\r\n  const handleAddPost = () => {\r\n    // Envie o novo post para o servidor (ou sua fonte de dados) e atualize a lista de posts.\r\n    // Limpe o campo de entrada de texto.\r\n  };\r\n\r\n  return (\r\n    <View style={styles.container}>\r\n      <Text style={styles.pageTitle}>Fórum</Text>\r\n\r\n      <TextInput\r\n        style={styles.input}\r\n        placeholder=\"Digite sua postagem...\"\r\n        value={newPostText}\r\n        onChangeText={(text) => setNewPostText(text)}\r\n      />\r\n\r\n      <TouchableOpacity style={styles.addButton} onPress={handleAddPost}>\r\n        <Text style={styles.addButtonText}>Adicionar Postagem</Text>\r\n      </TouchableOpacity>\r\n\r\n      <FlatList\r\n        data={posts}\r\n        keyExtractor={(item) => item.id.toString()}\r\n        renderItem={({ item }) => (\r\n          <View style={styles.postContainer}>\r\n            <Text style={styles.postText}>{item.text}</Text>\r\n            <Text style={styles.postAuthor}>Autor: {item.author}</Text>\r\n          </View>\r\n        )}\r\n      />\r\n    </View>\r\n  );\r\n};\r\n\r\nexport default ForumPage;\r\n\r\n// Estilos (use seus próprios estilos aqui)\r\nconst styles = {\r\n  container: {\r\n    flex: 1,\r\n    padding: 16,\r\n    backgroundColor: '#FFFFFF',\r\n  },\r\n  pageTitle: {\r\n    fontSize: 24,\r\n    fontWeight: 'bold',\r\n    marginBottom: 16,\r\n  },\r\n  input: {\r\n    borderWidth: 1,\r\n    borderColor: '#CCCCCC',\r\n    borderRadius: 4,\r\n    padding: 8,\r\n    marginBottom: 16,\r\n  },\r\n  addButton: {\r\n    backgroundColor: '#3176c4',\r\n    borderRadius: 4,\r\n    padding: 12,\r\n    alignItems: 'center',\r\n  },\r\n  addButtonText: {\r\n    color: '#FFFFFF',\r\n    fontWeight: 'bold',\r\n  },\r\n  postContainer: {\r\n    borderWidth: 1,\r\n    borderColor: '#CCCCCC',\r\n    borderRadius: 4,\r\n    padding: 16,\r\n    marginBottom: 16,\r\n  },\r\n  postText: {\r\n    fontSize: 16,\r\n    marginBottom: 8,\r\n  },\r\n  postAuthor: {\r\n    fontSize: 12,\r\n    color: '#888888',\r\n  },\r\n};\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,QAAA;AAAA,OAAAC,gBAAA;AAAA,OAAAC,SAAA;AAAA,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAKnD,IAAMC,SAAS,GAAG,SAAZA,SAASA,CAAA,EAAS;EACtB,IAAAC,SAAA,GAA0BZ,QAAQ,CAAC,EAAE,CAAC;IAAAa,UAAA,GAAAC,cAAA,CAAAF,SAAA;IAA/BG,KAAK,GAAAF,UAAA;IAAEG,QAAQ,GAAAH,UAAA;EACtB,IAAAI,UAAA,GAAsCjB,QAAQ,CAAC,EAAE,CAAC;IAAAkB,UAAA,GAAAJ,cAAA,CAAAG,UAAA;IAA3CE,WAAW,GAAAD,UAAA;IAAEE,cAAc,GAAAF,UAAA;EAElCjB,SAAS,CAAC,YAAM,CAGhB,CAAC,EAAE,EAAE,CAAC;EAEN,IAAMoB,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS,CAG5B,CAAC;EAED,OACEX,KAAA,CAACR,IAAI;IAACoB,KAAK,EAAEC,MAAM,CAACC,SAAU;IAAAC,QAAA,GAC5BjB,IAAA,CAACL,IAAI;MAACmB,KAAK,EAAEC,MAAM,CAACG,SAAU;MAAAD,QAAA,EAAC;IAAK,CAAM,CAAC,EAE3CjB,IAAA,CAACF,SAAS;MACRgB,KAAK,EAAEC,MAAM,CAACI,KAAM;MACpBC,WAAW,EAAC,wBAAwB;MACpCC,KAAK,EAAEV,WAAY;MACnBW,YAAY,EAAE,SAAdA,YAAYA,CAAGC,IAAI;QAAA,OAAKX,cAAc,CAACW,IAAI,CAAC;MAAA;IAAC,CAC9C,CAAC,EAEFvB,IAAA,CAACH,gBAAgB;MAACiB,KAAK,EAAEC,MAAM,CAACS,SAAU;MAACC,OAAO,EAAEZ,aAAc;MAAAI,QAAA,EAChEjB,IAAA,CAACL,IAAI;QAACmB,KAAK,EAAEC,MAAM,CAACW,aAAc;QAAAT,QAAA,EAAC;MAAkB,CAAM;IAAC,CAC5C,CAAC,EAEnBjB,IAAA,CAACJ,QAAQ;MACP+B,IAAI,EAAEpB,KAAM;MACZqB,YAAY,EAAE,SAAdA,YAAYA,CAAGC,IAAI;QAAA,OAAKA,IAAI,CAACC,EAAE,CAACC,QAAQ,CAAC,CAAC;MAAA,CAAC;MAC3CC,UAAU,EAAE,SAAZA,UAAUA,CAAAC,IAAA;QAAA,IAAKJ,IAAI,GAAAI,IAAA,CAAJJ,IAAI;QAAA,OACjB3B,KAAA,CAACR,IAAI;UAACoB,KAAK,EAAEC,MAAM,CAACmB,aAAc;UAAAjB,QAAA,GAChCjB,IAAA,CAACL,IAAI;YAACmB,KAAK,EAAEC,MAAM,CAACoB,QAAS;YAAAlB,QAAA,EAAEY,IAAI,CAACN;UAAI,CAAO,CAAC,EAChDrB,KAAA,CAACP,IAAI;YAACmB,KAAK,EAAEC,MAAM,CAACqB,UAAW;YAAAnB,QAAA,GAAC,SAAO,EAACY,IAAI,CAACQ,MAAM;UAAA,CAAO,CAAC;QAAA,CACvD,CAAC;MAAA;IACP,CACH,CAAC;EAAA,CACE,CAAC;AAEX,CAAC;AAED,eAAelC,SAAS;AAGxB,IAAMY,MAAM,GAAG;EACbC,SAAS,EAAE;IACTsB,IAAI,EAAE,CAAC;IACPC,OAAO,EAAE,EAAE;IACXC,eAAe,EAAE;EACnB,CAAC;EACDtB,SAAS,EAAE;IACTuB,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBC,YAAY,EAAE;EAChB,CAAC;EACDxB,KAAK,EAAE;IACLyB,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE,SAAS;IACtBC,YAAY,EAAE,CAAC;IACfP,OAAO,EAAE,CAAC;IACVI,YAAY,EAAE;EAChB,CAAC;EACDnB,SAAS,EAAE;IACTgB,eAAe,EAAE,SAAS;IAC1BM,YAAY,EAAE,CAAC;IACfP,OAAO,EAAE,EAAE;IACXQ,UAAU,EAAE;EACd,CAAC;EACDrB,aAAa,EAAE;IACbsB,KAAK,EAAE,SAAS;IAChBN,UAAU,EAAE;EACd,CAAC;EACDR,aAAa,EAAE;IACbU,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE,SAAS;IACtBC,YAAY,EAAE,CAAC;IACfP,OAAO,EAAE,EAAE;IACXI,YAAY,EAAE;EAChB,CAAC;EACDR,QAAQ,EAAE;IACRM,QAAQ,EAAE,EAAE;IACZE,YAAY,EAAE;EAChB,CAAC;EACDP,UAAU,EAAE;IACVK,QAAQ,EAAE,EAAE;IACZO,KAAK,EAAE;EACT;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}