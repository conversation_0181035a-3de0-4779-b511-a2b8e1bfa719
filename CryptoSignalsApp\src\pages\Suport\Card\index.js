import React from 'react';
import { View, Text, TouchableOpacity, Image, ToastAndroid} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import styles from './styles';
import { format } from 'date-fns';
import enUSLocale from 'date-fns/locale/en-US';
import IconLocked from '../../../components/Icon/Locked';
import { IconButton,  } from 'react-native-paper';

const Card = ({ channel, paidPremium, isLoadedNotification, notificationPermission, returnNotificationPermission }) => {
  const navigation = useNavigation();
  const formattedCreatedAt = format(
    new Date(channel.lastSignalAt),
    'LLL dd, h:mm aa',
    { locale: enUSLocale }
  );

  const handleShowSignals = () => {
    if (!paidPremium && channel.isPremium) {
      navigation.navigate('Premium');
      return;
    }
  
    navigation.navigate('Signals', { channelName: channel.name, channelId: channel.externalId });
  }

  const handleToggleNotification = async (toggleActive) => {
    const params = {
      channelId: channel.externalId,
      isActive: toggleActive,
    };
    
    returnNotificationPermission(params);
  }
  
  return (
    <>
      <TouchableOpacity
        style={styles.container}
        onPress={() => handleShowSignals()}
      >
        <View style={[styles.dFlex, styles.row]}>
          <Image style={styles.photo} source={{ uri: channel.photo }} />

          <View>
            <Text style={styles.title}>{channel.name}</Text>
            <View style={styles.typeContainer}>
              <Text style={styles.type}>{channel.type}</Text>
              <Text style={styles.dot}></Text>
              {channel.isPremium ? (
                <Text style={styles.type}>Premium</Text>
              ) : (
                <Text style={styles.type}>Free</Text>
              )}
            </View>
          </View>
        </View>

        <View style={styles.icon}>
          {channel.isPremium && !paidPremium && <IconLocked />}
        </View>

        {(!channel.isPremium || paidPremium) && (
          <>
            <Text style={styles.time}>{formattedCreatedAt}</Text>

            {isLoadedNotification && !notificationPermission.isActive && (
              <TouchableOpacity
                style={{
                  position: "absolute",
                  right: 0,
                  top: 0,
                  borderRadius: 0,
                  width: 70,
                  height: 70,
                  display: "flex",
                  alignItems: "flex-end",
                }}
                onPress={(e) => {
                  e.stopPropagation();
                  handleToggleNotification(true);
                }}
              >
                <IconButton
                  icon="bell-off-outline"
                  iconColor="#868686"
                  size={26}
                ></IconButton>
              </TouchableOpacity>
            )}

            {isLoadedNotification && notificationPermission.isActive && (
              <TouchableOpacity
                style={{
                  position: "absolute",
                  right: 0,
                  top: 0,
                  borderRadius: 0,
                  width: 70,
                  height: 70,
                  display: "flex",
                  alignItems: "flex-end",
                }}
                onPress={(e) => {
                  e.stopPropagation();
                  handleToggleNotification(false);
                }}
              >
                <IconButton
                  icon="bell-ring-outline"
                  iconColor="#A5E1BF"
                  size={26}
                ></IconButton>
              </TouchableOpacity>
            )}
          </>
        )}
      </TouchableOpacity>
    </>
  );
}

export default Card;
