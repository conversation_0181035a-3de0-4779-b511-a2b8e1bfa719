{"ast": null, "code": "'use client';\n\nimport canUseDOM from \"../../modules/canUseDom\";\nfunction isScreenReaderEnabled() {\n  return new Promise(function (resolve, reject) {\n    resolve(true);\n  });\n}\nvar prefersReducedMotionMedia = canUseDOM && typeof window.matchMedia === 'function' ? window.matchMedia('(prefers-reduced-motion: reduce)') : null;\nfunction isReduceMotionEnabled() {\n  return new Promise(function (resolve, reject) {\n    resolve(prefersReducedMotionMedia ? prefersReducedMotionMedia.matches : true);\n  });\n}\nfunction addChangeListener(fn) {\n  if (prefersReducedMotionMedia != null) {\n    prefersReducedMotionMedia.addEventListener != null ? prefersReducedMotionMedia.addEventListener('change', fn) : prefersReducedMotionMedia.addListener(fn);\n  }\n}\nfunction removeChangeListener(fn) {\n  if (prefersReducedMotionMedia != null) {\n    prefersReducedMotionMedia.removeEventListener != null ? prefersReducedMotionMedia.removeEventListener('change', fn) : prefersReducedMotionMedia.removeListener(fn);\n  }\n}\nvar handlers = {};\nvar AccessibilityInfo = {\n  isScreenReaderEnabled: isScreenReaderEnabled,\n  isReduceMotionEnabled: isReduceMotionEnabled,\n  fetch: isScreenReaderEnabled,\n  addEventListener: function addEventListener(eventName, handler) {\n    if (eventName === 'reduceMotionChanged') {\n      if (!prefersReducedMotionMedia) {\n        return;\n      }\n      var listener = function listener(event) {\n        handler(event.matches);\n      };\n      addChangeListener(listener);\n      handlers[handler] = listener;\n    }\n    return {\n      remove: function remove() {\n        return AccessibilityInfo.removeEventListener(eventName, handler);\n      }\n    };\n  },\n  setAccessibilityFocus: function setAccessibilityFocus(reactTag) {},\n  announceForAccessibility: function announceForAccessibility(announcement) {},\n  removeEventListener: function removeEventListener(eventName, handler) {\n    if (eventName === 'reduceMotionChanged') {\n      var listener = handlers[handler];\n      if (!listener || !prefersReducedMotionMedia) {\n        return;\n      }\n      removeChangeListener(listener);\n    }\n    return;\n  }\n};\nexport default AccessibilityInfo;", "map": {"version": 3, "names": ["canUseDOM", "isScreenReaderEnabled", "Promise", "resolve", "reject", "prefersReducedMotionMedia", "window", "matchMedia", "isReduceMotionEnabled", "matches", "addChangeListener", "fn", "addEventListener", "addListener", "removeChangeListener", "removeEventListener", "removeListener", "handlers", "AccessibilityInfo", "fetch", "eventName", "handler", "listener", "event", "remove", "setAccessibilityFocus", "reactTag", "announceForAccessibility", "announcement"], "sources": ["C:/Users/<USER>/Desktop/Signals/CryptoSignalsApp/node_modules/react-native-web/dist/exports/AccessibilityInfo/index.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n'use client';\n\nimport canUseDOM from '../../modules/canUseDom';\nfunction isScreenReaderEnabled() {\n  return new Promise((resolve, reject) => {\n    resolve(true);\n  });\n}\nvar prefersReducedMotionMedia = canUseDOM && typeof window.matchMedia === 'function' ? window.matchMedia('(prefers-reduced-motion: reduce)') : null;\nfunction isReduceMotionEnabled() {\n  return new Promise((resolve, reject) => {\n    resolve(prefersReducedMotionMedia ? prefersReducedMotionMedia.matches : true);\n  });\n}\nfunction addChangeListener(fn) {\n  if (prefersReducedMotionMedia != null) {\n    prefersReducedMotionMedia.addEventListener != null ? prefersReducedMotionMedia.addEventListener('change', fn) : prefersReducedMotionMedia.addListener(fn);\n  }\n}\nfunction removeChangeListener(fn) {\n  if (prefersReducedMotionMedia != null) {\n    prefersReducedMotionMedia.removeEventListener != null ? prefersReducedMotionMedia.removeEventListener('change', fn) : prefersReducedMotionMedia.removeListener(fn);\n  }\n}\nvar handlers = {};\nvar AccessibilityInfo = {\n  /**\n   * Query whether a screen reader is currently enabled.\n   *\n   * Returns a promise which resolves to a boolean.\n   * The result is `true` when a screen reader is enabled and `false` otherwise.\n   */\n  isScreenReaderEnabled,\n  /**\n   * Query whether the user prefers reduced motion.\n   *\n   * Returns a promise which resolves to a boolean.\n   * The result is `true` when a screen reader is enabled and `false` otherwise.\n   */\n  isReduceMotionEnabled,\n  /**\n   * Deprecated\n   */\n  fetch: isScreenReaderEnabled,\n  /**\n   * Add an event handler. Supported events: reduceMotionChanged\n   */\n  addEventListener: function addEventListener(eventName, handler) {\n    if (eventName === 'reduceMotionChanged') {\n      if (!prefersReducedMotionMedia) {\n        return;\n      }\n      var listener = event => {\n        handler(event.matches);\n      };\n      addChangeListener(listener);\n      handlers[handler] = listener;\n    }\n    return {\n      remove: () => AccessibilityInfo.removeEventListener(eventName, handler)\n    };\n  },\n  /**\n   * Set accessibility focus to a react component.\n   */\n  setAccessibilityFocus: function setAccessibilityFocus(reactTag) {},\n  /**\n   * Post a string to be announced by the screen reader.\n   */\n  announceForAccessibility: function announceForAccessibility(announcement) {},\n  /**\n   * Remove an event handler.\n   */\n  removeEventListener: function removeEventListener(eventName, handler) {\n    if (eventName === 'reduceMotionChanged') {\n      var listener = handlers[handler];\n      if (!listener || !prefersReducedMotionMedia) {\n        return;\n      }\n      removeChangeListener(listener);\n    }\n    return;\n  }\n};\nexport default AccessibilityInfo;"], "mappings": "AASA,YAAY;;AAEZ,OAAOA,SAAS;AAChB,SAASC,qBAAqBA,CAAA,EAAG;EAC/B,OAAO,IAAIC,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;IACtCD,OAAO,CAAC,IAAI,CAAC;EACf,CAAC,CAAC;AACJ;AACA,IAAIE,yBAAyB,GAAGL,SAAS,IAAI,OAAOM,MAAM,CAACC,UAAU,KAAK,UAAU,GAAGD,MAAM,CAACC,UAAU,CAAC,kCAAkC,CAAC,GAAG,IAAI;AACnJ,SAASC,qBAAqBA,CAAA,EAAG;EAC/B,OAAO,IAAIN,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;IACtCD,OAAO,CAACE,yBAAyB,GAAGA,yBAAyB,CAACI,OAAO,GAAG,IAAI,CAAC;EAC/E,CAAC,CAAC;AACJ;AACA,SAASC,iBAAiBA,CAACC,EAAE,EAAE;EAC7B,IAAIN,yBAAyB,IAAI,IAAI,EAAE;IACrCA,yBAAyB,CAACO,gBAAgB,IAAI,IAAI,GAAGP,yBAAyB,CAACO,gBAAgB,CAAC,QAAQ,EAAED,EAAE,CAAC,GAAGN,yBAAyB,CAACQ,WAAW,CAACF,EAAE,CAAC;EAC3J;AACF;AACA,SAASG,oBAAoBA,CAACH,EAAE,EAAE;EAChC,IAAIN,yBAAyB,IAAI,IAAI,EAAE;IACrCA,yBAAyB,CAACU,mBAAmB,IAAI,IAAI,GAAGV,yBAAyB,CAACU,mBAAmB,CAAC,QAAQ,EAAEJ,EAAE,CAAC,GAAGN,yBAAyB,CAACW,cAAc,CAACL,EAAE,CAAC;EACpK;AACF;AACA,IAAIM,QAAQ,GAAG,CAAC,CAAC;AACjB,IAAIC,iBAAiB,GAAG;EAOtBjB,qBAAqB,EAArBA,qBAAqB;EAOrBO,qBAAqB,EAArBA,qBAAqB;EAIrBW,KAAK,EAAElB,qBAAqB;EAI5BW,gBAAgB,EAAE,SAASA,gBAAgBA,CAACQ,SAAS,EAAEC,OAAO,EAAE;IAC9D,IAAID,SAAS,KAAK,qBAAqB,EAAE;MACvC,IAAI,CAACf,yBAAyB,EAAE;QAC9B;MACF;MACA,IAAIiB,QAAQ,GAAG,SAAXA,QAAQA,CAAGC,KAAK,EAAI;QACtBF,OAAO,CAACE,KAAK,CAACd,OAAO,CAAC;MACxB,CAAC;MACDC,iBAAiB,CAACY,QAAQ,CAAC;MAC3BL,QAAQ,CAACI,OAAO,CAAC,GAAGC,QAAQ;IAC9B;IACA,OAAO;MACLE,MAAM,EAAE,SAARA,MAAMA,CAAA;QAAA,OAAQN,iBAAiB,CAACH,mBAAmB,CAACK,SAAS,EAAEC,OAAO,CAAC;MAAA;IACzE,CAAC;EACH,CAAC;EAIDI,qBAAqB,EAAE,SAASA,qBAAqBA,CAACC,QAAQ,EAAE,CAAC,CAAC;EAIlEC,wBAAwB,EAAE,SAASA,wBAAwBA,CAACC,YAAY,EAAE,CAAC,CAAC;EAI5Eb,mBAAmB,EAAE,SAASA,mBAAmBA,CAACK,SAAS,EAAEC,OAAO,EAAE;IACpE,IAAID,SAAS,KAAK,qBAAqB,EAAE;MACvC,IAAIE,QAAQ,GAAGL,QAAQ,CAACI,OAAO,CAAC;MAChC,IAAI,CAACC,QAAQ,IAAI,CAACjB,yBAAyB,EAAE;QAC3C;MACF;MACAS,oBAAoB,CAACQ,QAAQ,CAAC;IAChC;IACA;EACF;AACF,CAAC;AACD,eAAeJ,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}