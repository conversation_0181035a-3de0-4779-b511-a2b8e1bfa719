{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport React from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport TouchableOpacity from \"react-native-web/dist/exports/TouchableOpacity\";\nimport Image from \"react-native-web/dist/exports/Image\";\nimport { useNavigation } from '@react-navigation/native';\nimport styles from \"./styles\";\nimport { format } from 'date-fns';\nimport enUSLocale from 'date-fns/locale/en-US';\nimport IconLocked from \"../../../components/Icon/Locked\";\nimport { IconButton } from 'react-native-paper';\nimport { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from \"react/jsx-runtime\";\nvar Card = function Card(_ref) {\n  var channel = _ref.channel,\n    paidPremium = _ref.paidPremium,\n    isLoadedNotification = _ref.isLoadedNotification,\n    notificationPermission = _ref.notificationPermission,\n    returnNotificationPermission = _ref.returnNotificationPermission;\n  var navigation = useNavigation();\n  var formattedCreatedAt = format(new Date(channel.lastSignalAt), 'LLL dd, h:mm aa', {\n    locale: enUSLocale\n  });\n  var handleShowSignals = function handleShowSignals() {\n    if (!paidPremium && channel.isPremium) {\n      navigation.navigate('Premium');\n      return;\n    }\n    navigation.navigate('Signals', {\n      channelName: channel.name,\n      channelId: channel.externalId\n    });\n  };\n  var handleToggleNotification = function () {\n    var _ref2 = _asyncToGenerator(function* (toggleActive) {\n      var params = {\n        channelId: channel.externalId,\n        isActive: toggleActive\n      };\n      returnNotificationPermission(params);\n    });\n    return function handleToggleNotification(_x) {\n      return _ref2.apply(this, arguments);\n    };\n  }();\n  return _jsx(_Fragment, {\n    children: _jsxs(TouchableOpacity, {\n      style: styles.container,\n      onPress: function onPress() {\n        return handleShowSignals();\n      },\n      children: [_jsxs(View, {\n        style: [styles.dFlex, styles.row],\n        children: [_jsx(Image, {\n          style: styles.photo,\n          source: {\n            uri: channel.photo\n          }\n        }), _jsxs(View, {\n          children: [_jsx(Text, {\n            style: styles.title,\n            children: channel.name\n          }), _jsxs(View, {\n            style: styles.typeContainer,\n            children: [_jsx(Text, {\n              style: styles.type,\n              children: channel.type\n            }), _jsx(Text, {\n              style: styles.dot\n            }), channel.isPremium ? _jsx(Text, {\n              style: styles.type,\n              children: \"Premium\"\n            }) : _jsx(Text, {\n              style: styles.type,\n              children: \"Free\"\n            })]\n          })]\n        })]\n      }), _jsx(View, {\n        style: styles.icon,\n        children: channel.isPremium && !paidPremium && _jsx(IconLocked, {})\n      }), (!channel.isPremium || paidPremium) && _jsxs(_Fragment, {\n        children: [_jsx(Text, {\n          style: styles.time,\n          children: formattedCreatedAt\n        }), isLoadedNotification && !notificationPermission.isActive && _jsx(TouchableOpacity, {\n          style: {\n            position: \"absolute\",\n            right: 0,\n            top: 0,\n            borderRadius: 0,\n            width: 70,\n            height: 70,\n            display: \"flex\",\n            alignItems: \"flex-end\"\n          },\n          onPress: function onPress(e) {\n            e.stopPropagation();\n            handleToggleNotification(true);\n          },\n          children: _jsx(IconButton, {\n            icon: \"bell-off-outline\",\n            iconColor: \"#868686\",\n            size: 26\n          })\n        }), isLoadedNotification && notificationPermission.isActive && _jsx(TouchableOpacity, {\n          style: {\n            position: \"absolute\",\n            right: 0,\n            top: 0,\n            borderRadius: 0,\n            width: 70,\n            height: 70,\n            display: \"flex\",\n            alignItems: \"flex-end\"\n          },\n          onPress: function onPress(e) {\n            e.stopPropagation();\n            handleToggleNotification(false);\n          },\n          children: _jsx(IconButton, {\n            icon: \"bell-ring-outline\",\n            iconColor: \"#A5E1BF\",\n            size: 26\n          })\n        })]\n      })]\n    })\n  });\n};\nexport default Card;", "map": {"version": 3, "names": ["React", "View", "Text", "TouchableOpacity", "Image", "useNavigation", "styles", "format", "enUSLocale", "IconLocked", "IconButton", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "Card", "_ref", "channel", "paidPremium", "isLoadedNotification", "notificationPermission", "returnNotificationPermission", "navigation", "formattedCreatedAt", "Date", "lastSignalAt", "locale", "handleShowSignals", "isPremium", "navigate", "channelName", "name", "channelId", "externalId", "handleToggleNotification", "_ref2", "_asyncToGenerator", "toggleActive", "params", "isActive", "_x", "apply", "arguments", "children", "style", "container", "onPress", "dFlex", "row", "photo", "source", "uri", "title", "typeContainer", "type", "dot", "icon", "time", "position", "right", "top", "borderRadius", "width", "height", "display", "alignItems", "e", "stopPropagation", "iconColor", "size"], "sources": ["E:/CryptoSignalsApp/src/pages/Channels/Card/index.js"], "sourcesContent": ["import React from 'react';\r\nimport { View, Text, TouchableOpacity, Image } from 'react-native';\r\nimport { useNavigation } from '@react-navigation/native';\r\nimport styles from './styles';\r\nimport { format } from 'date-fns';\r\nimport enUSLocale from 'date-fns/locale/en-US';\r\nimport IconLocked from '../../../components/Icon/Locked';\r\nimport { IconButton,  } from 'react-native-paper';\r\n\r\nconst Card = ({ channel, paidPremium, isLoadedNotification, notificationPermission, returnNotificationPermission }) => {\r\n  const navigation = useNavigation();\r\n  const formattedCreatedAt = format(\r\n    new Date(channel.lastSignalAt),\r\n    'LLL dd, h:mm aa',\r\n    { locale: enUSLocale }\r\n  );\r\n\r\n  const handleShowSignals = () => {\r\n    if (!paidPremium && channel.isPremium) {\r\n      navigation.navigate('Premium');\r\n      return;\r\n    }\r\n\r\n    navigation.navigate('Signals', { channelName: channel.name, channelId: channel.externalId });\r\n  }\r\n\r\n  const handleToggleNotification = async (toggleActive) => {\r\n    const params = {\r\n      channelId: channel.externalId,\r\n      isActive: toggleActive,\r\n    };\r\n\r\n    returnNotificationPermission(params);\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <TouchableOpacity\r\n        style={styles.container}\r\n        onPress={() => handleShowSignals()}\r\n      >\r\n        <View style={[styles.dFlex, styles.row]}>\r\n          <Image style={styles.photo} source={{ uri: channel.photo }} />\r\n\r\n          <View>\r\n            <Text style={styles.title}>{channel.name}</Text>\r\n            <View style={styles.typeContainer}>\r\n              <Text style={styles.type}>{channel.type}</Text>\r\n              <Text style={styles.dot}></Text>\r\n              {channel.isPremium ? (\r\n                <Text style={styles.type}>Premium</Text>\r\n              ) : (\r\n                <Text style={styles.type}>Free</Text>\r\n              )}\r\n            </View>\r\n          </View>\r\n        </View>\r\n\r\n        <View style={styles.icon}>\r\n          {channel.isPremium && !paidPremium && <IconLocked />}\r\n        </View>\r\n\r\n        {(!channel.isPremium || paidPremium) && (\r\n          <>\r\n            <Text style={styles.time}>{formattedCreatedAt}</Text>\r\n\r\n            {isLoadedNotification && !notificationPermission.isActive && (\r\n              <TouchableOpacity\r\n                style={{\r\n                  position: \"absolute\",\r\n                  right: 0,\r\n                  top: 0,\r\n                  borderRadius: 0,\r\n                  width: 70,\r\n                  height: 70,\r\n                  display: \"flex\",\r\n                  alignItems: \"flex-end\",\r\n                }}\r\n                onPress={(e) => {\r\n                  e.stopPropagation();\r\n                  handleToggleNotification(true);\r\n                }}\r\n              >\r\n                <IconButton\r\n                  icon=\"bell-off-outline\"\r\n                  iconColor=\"#868686\"\r\n                  size={26}\r\n                ></IconButton>\r\n              </TouchableOpacity>\r\n            )}\r\n\r\n            {isLoadedNotification && notificationPermission.isActive && (\r\n              <TouchableOpacity\r\n                style={{\r\n                  position: \"absolute\",\r\n                  right: 0,\r\n                  top: 0,\r\n                  borderRadius: 0,\r\n                  width: 70,\r\n                  height: 70,\r\n                  display: \"flex\",\r\n                  alignItems: \"flex-end\",\r\n                }}\r\n                onPress={(e) => {\r\n                  e.stopPropagation();\r\n                  handleToggleNotification(false);\r\n                }}\r\n              >\r\n                <IconButton\r\n                  icon=\"bell-ring-outline\"\r\n                  iconColor=\"#A5E1BF\"\r\n                  size={26}\r\n                ></IconButton>\r\n              </TouchableOpacity>\r\n            )}\r\n          </>\r\n        )}\r\n      </TouchableOpacity>\r\n    </>\r\n  );\r\n}\r\n\r\nexport default Card;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,gBAAA;AAAA,OAAAC,KAAA;AAE1B,SAASC,aAAa,QAAQ,0BAA0B;AACxD,OAAOC,MAAM;AACb,SAASC,MAAM,QAAQ,UAAU;AACjC,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,OAAOC,UAAU;AACjB,SAASC,UAAU,QAAU,oBAAoB;AAAC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA,EAAAC,QAAA,IAAAC,SAAA;AAElD,IAAMC,IAAI,GAAG,SAAPA,IAAIA,CAAAC,IAAA,EAA6G;EAAA,IAAvGC,OAAO,GAAAD,IAAA,CAAPC,OAAO;IAAEC,WAAW,GAAAF,IAAA,CAAXE,WAAW;IAAEC,oBAAoB,GAAAH,IAAA,CAApBG,oBAAoB;IAAEC,sBAAsB,GAAAJ,IAAA,CAAtBI,sBAAsB;IAAEC,4BAA4B,GAAAL,IAAA,CAA5BK,4BAA4B;EAC9G,IAAMC,UAAU,GAAGnB,aAAa,CAAC,CAAC;EAClC,IAAMoB,kBAAkB,GAAGlB,MAAM,CAC/B,IAAImB,IAAI,CAACP,OAAO,CAACQ,YAAY,CAAC,EAC9B,iBAAiB,EACjB;IAAEC,MAAM,EAAEpB;EAAW,CACvB,CAAC;EAED,IAAMqB,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAA,EAAS;IAC9B,IAAI,CAACT,WAAW,IAAID,OAAO,CAACW,SAAS,EAAE;MACrCN,UAAU,CAACO,QAAQ,CAAC,SAAS,CAAC;MAC9B;IACF;IAEAP,UAAU,CAACO,QAAQ,CAAC,SAAS,EAAE;MAAEC,WAAW,EAAEb,OAAO,CAACc,IAAI;MAAEC,SAAS,EAAEf,OAAO,CAACgB;IAAW,CAAC,CAAC;EAC9F,CAAC;EAED,IAAMC,wBAAwB;IAAA,IAAAC,KAAA,GAAAC,iBAAA,CAAG,WAAOC,YAAY,EAAK;MACvD,IAAMC,MAAM,GAAG;QACbN,SAAS,EAAEf,OAAO,CAACgB,UAAU;QAC7BM,QAAQ,EAAEF;MACZ,CAAC;MAEDhB,4BAA4B,CAACiB,MAAM,CAAC;IACtC,CAAC;IAAA,gBAPKJ,wBAAwBA,CAAAM,EAAA;MAAA,OAAAL,KAAA,CAAAM,KAAA,OAAAC,SAAA;IAAA;EAAA,GAO7B;EAED,OACEhC,IAAA,CAAAI,SAAA;IAAA6B,QAAA,EACE/B,KAAA,CAACX,gBAAgB;MACf2C,KAAK,EAAExC,MAAM,CAACyC,SAAU;MACxBC,OAAO,EAAE,SAATA,OAAOA,CAAA;QAAA,OAAQnB,iBAAiB,CAAC,CAAC;MAAA,CAAC;MAAAgB,QAAA,GAEnC/B,KAAA,CAACb,IAAI;QAAC6C,KAAK,EAAE,CAACxC,MAAM,CAAC2C,KAAK,EAAE3C,MAAM,CAAC4C,GAAG,CAAE;QAAAL,QAAA,GACtCjC,IAAA,CAACR,KAAK;UAAC0C,KAAK,EAAExC,MAAM,CAAC6C,KAAM;UAACC,MAAM,EAAE;YAAEC,GAAG,EAAElC,OAAO,CAACgC;UAAM;QAAE,CAAE,CAAC,EAE9DrC,KAAA,CAACb,IAAI;UAAA4C,QAAA,GACHjC,IAAA,CAACV,IAAI;YAAC4C,KAAK,EAAExC,MAAM,CAACgD,KAAM;YAAAT,QAAA,EAAE1B,OAAO,CAACc;UAAI,CAAO,CAAC,EAChDnB,KAAA,CAACb,IAAI;YAAC6C,KAAK,EAAExC,MAAM,CAACiD,aAAc;YAAAV,QAAA,GAChCjC,IAAA,CAACV,IAAI;cAAC4C,KAAK,EAAExC,MAAM,CAACkD,IAAK;cAAAX,QAAA,EAAE1B,OAAO,CAACqC;YAAI,CAAO,CAAC,EAC/C5C,IAAA,CAACV,IAAI;cAAC4C,KAAK,EAAExC,MAAM,CAACmD;YAAI,CAAO,CAAC,EAC/BtC,OAAO,CAACW,SAAS,GAChBlB,IAAA,CAACV,IAAI;cAAC4C,KAAK,EAAExC,MAAM,CAACkD,IAAK;cAAAX,QAAA,EAAC;YAAO,CAAM,CAAC,GAExCjC,IAAA,CAACV,IAAI;cAAC4C,KAAK,EAAExC,MAAM,CAACkD,IAAK;cAAAX,QAAA,EAAC;YAAI,CAAM,CACrC;UAAA,CACG,CAAC;QAAA,CACH,CAAC;MAAA,CACH,CAAC,EAEPjC,IAAA,CAACX,IAAI;QAAC6C,KAAK,EAAExC,MAAM,CAACoD,IAAK;QAAAb,QAAA,EACtB1B,OAAO,CAACW,SAAS,IAAI,CAACV,WAAW,IAAIR,IAAA,CAACH,UAAU,IAAE;MAAC,CAChD,CAAC,EAEN,CAAC,CAACU,OAAO,CAACW,SAAS,IAAIV,WAAW,KACjCN,KAAA,CAAAE,SAAA;QAAA6B,QAAA,GACEjC,IAAA,CAACV,IAAI;UAAC4C,KAAK,EAAExC,MAAM,CAACqD,IAAK;UAAAd,QAAA,EAAEpB;QAAkB,CAAO,CAAC,EAEpDJ,oBAAoB,IAAI,CAACC,sBAAsB,CAACmB,QAAQ,IACvD7B,IAAA,CAACT,gBAAgB;UACf2C,KAAK,EAAE;YACLc,QAAQ,EAAE,UAAU;YACpBC,KAAK,EAAE,CAAC;YACRC,GAAG,EAAE,CAAC;YACNC,YAAY,EAAE,CAAC;YACfC,KAAK,EAAE,EAAE;YACTC,MAAM,EAAE,EAAE;YACVC,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE;UACd,CAAE;UACFnB,OAAO,EAAE,SAATA,OAAOA,CAAGoB,CAAC,EAAK;YACdA,CAAC,CAACC,eAAe,CAAC,CAAC;YACnBjC,wBAAwB,CAAC,IAAI,CAAC;UAChC,CAAE;UAAAS,QAAA,EAEFjC,IAAA,CAACF,UAAU;YACTgD,IAAI,EAAC,kBAAkB;YACvBY,SAAS,EAAC,SAAS;YACnBC,IAAI,EAAE;UAAG,CACE;QAAC,CACE,CACnB,EAEAlD,oBAAoB,IAAIC,sBAAsB,CAACmB,QAAQ,IACtD7B,IAAA,CAACT,gBAAgB;UACf2C,KAAK,EAAE;YACLc,QAAQ,EAAE,UAAU;YACpBC,KAAK,EAAE,CAAC;YACRC,GAAG,EAAE,CAAC;YACNC,YAAY,EAAE,CAAC;YACfC,KAAK,EAAE,EAAE;YACTC,MAAM,EAAE,EAAE;YACVC,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE;UACd,CAAE;UACFnB,OAAO,EAAE,SAATA,OAAOA,CAAGoB,CAAC,EAAK;YACdA,CAAC,CAACC,eAAe,CAAC,CAAC;YACnBjC,wBAAwB,CAAC,KAAK,CAAC;UACjC,CAAE;UAAAS,QAAA,EAEFjC,IAAA,CAACF,UAAU;YACTgD,IAAI,EAAC,mBAAmB;YACxBY,SAAS,EAAC,SAAS;YACnBC,IAAI,EAAE;UAAG,CACE;QAAC,CACE,CACnB;MAAA,CACD,CACH;IAAA,CACe;EAAC,CACnB,CAAC;AAEP,CAAC;AAED,eAAetD,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}