{"ast": null, "code": "var pick = function pick(obj) {\n  for (var _len = arguments.length, keys = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    keys[_key - 1] = arguments[_key];\n  }\n  return keys.flat().filter(function (key) {\n    return Object.prototype.hasOwnProperty.call(obj, key);\n  }).reduce(function (acc, key) {\n    acc[key] = obj[key];\n    return acc;\n  }, {});\n};\nvar omit = function omit(obj) {\n  for (var _len2 = arguments.length, keysToOmit = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n    keysToOmit[_key2 - 1] = arguments[_key2];\n  }\n  var keysToOmitSet = new Set(keysToOmit.flat());\n  return Object.getOwnPropertyNames(obj).filter(function (key) {\n    return !keysToOmitSet.has(key);\n  }).reduce(function (acc, key) {\n    acc[key] = obj[key];\n    return acc;\n  }, {});\n};\nmodule.exports = {\n  pick: pick,\n  omit: omit\n};", "map": {"version": 3, "names": ["pick", "obj", "_len", "arguments", "length", "keys", "Array", "_key", "flat", "filter", "key", "Object", "prototype", "hasOwnProperty", "call", "reduce", "acc", "omit", "_len2", "keysToOmit", "_key2", "keysToOmitSet", "Set", "getOwnPropertyNames", "has", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/Signals/CryptoSignalsApp/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/lib/object-utils.js"], "sourcesContent": ["const pick = (obj, ...keys) =>\n  keys\n    .flat()\n    .filter(key => Object.prototype.hasOwnProperty.call(obj, key))\n    .reduce((acc, key) => {\n      acc[key] = obj[key];\n      return acc;\n    }, {});\n\nconst omit = (obj, ...keysToOmit) => {\n  const keysToOmitSet = new Set(keysToOmit.flat());\n  return Object.getOwnPropertyNames(obj)\n    .filter(key => !keysToOmitSet.has(key))\n    .reduce((acc, key) => {\n      acc[key] = obj[key];\n      return acc;\n    }, {});\n};\n\nmodule.exports = { pick, omit };\n"], "mappings": "AAAA,IAAMA,IAAI,GAAG,SAAPA,IAAIA,CAAIC,GAAG;EAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAKC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,OAAAA,IAAA,WAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAAJF,IAAI,CAAAE,IAAA,QAAAJ,SAAA,CAAAI,IAAA;EAAA;EAAA,OACxBF,IAAI,CACDG,IAAI,CAAC,CAAC,CACNC,MAAM,CAAC,UAAAC,GAAG;IAAA,OAAIC,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACb,GAAG,EAAES,GAAG,CAAC;EAAA,EAAC,CAC7DK,MAAM,CAAC,UAACC,GAAG,EAAEN,GAAG,EAAK;IACpBM,GAAG,CAACN,GAAG,CAAC,GAAGT,GAAG,CAACS,GAAG,CAAC;IACnB,OAAOM,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;AAAA;AAEV,IAAMC,IAAI,GAAG,SAAPA,IAAIA,CAAIhB,GAAG,EAAoB;EAAA,SAAAiB,KAAA,GAAAf,SAAA,CAAAC,MAAA,EAAfe,UAAU,OAAAb,KAAA,CAAAY,KAAA,OAAAA,KAAA,WAAAE,KAAA,MAAAA,KAAA,GAAAF,KAAA,EAAAE,KAAA;IAAVD,UAAU,CAAAC,KAAA,QAAAjB,SAAA,CAAAiB,KAAA;EAAA;EAC9B,IAAMC,aAAa,GAAG,IAAIC,GAAG,CAACH,UAAU,CAACX,IAAI,CAAC,CAAC,CAAC;EAChD,OAAOG,MAAM,CAACY,mBAAmB,CAACtB,GAAG,CAAC,CACnCQ,MAAM,CAAC,UAAAC,GAAG;IAAA,OAAI,CAACW,aAAa,CAACG,GAAG,CAACd,GAAG,CAAC;EAAA,EAAC,CACtCK,MAAM,CAAC,UAACC,GAAG,EAAEN,GAAG,EAAK;IACpBM,GAAG,CAACN,GAAG,CAAC,GAAGT,GAAG,CAACS,GAAG,CAAC;IACnB,OAAOM,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;AACV,CAAC;AAEDS,MAAM,CAACC,OAAO,GAAG;EAAE1B,IAAI,EAAJA,IAAI;EAAEiB,IAAI,EAAJA;AAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}