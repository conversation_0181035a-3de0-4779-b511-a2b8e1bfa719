{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport React, { useContext, useEffect, useState } from \"react\";\nimport View from \"react-native-web/dist/exports/View\";\nimport Alert from \"react-native-web/dist/exports/Alert\";\nimport ScrollView from \"react-native-web/dist/exports/ScrollView\";\nimport TouchableOpacity from \"react-native-web/dist/exports/TouchableOpacity\";\nimport Modal from \"react-native-web/dist/exports/Modal\";\nimport { Button, Text, ProgressBar } from 'react-native-paper';\nimport Wrapper from \"../../components/Wrapper\";\nimport Card from \"../../components/Card\";\nimport StatCard from \"../../components/StatCard\";\nimport USDTPayment from \"../../components/USDTPayment\";\nimport { StoreContext } from \"../../store\";\nimport { PAYMENT_CONFIG } from \"../../config/api\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport default function Premium(_ref) {\n  var _ref$route = _ref.route,\n    route = _ref$route === void 0 ? {} : _ref$route;\n  var _useContext = useContext(StoreContext),\n    _useContext2 = _slicedToArray(_useContext, 2),\n    state = _useContext2[0],\n    dispatch = _useContext2[1];\n  var _useState = useState('pro'),\n    _useState2 = _slicedToArray(_useState, 2),\n    selectedPlan = _useState2[0],\n    setSelectedPlan = _useState2[1];\n  var _useState3 = useState(false),\n    _useState4 = _slicedToArray(_useState3, 2),\n    showPaymentModal = _useState4[0],\n    setShowPaymentModal = _useState4[1];\n  var _useState5 = useState(null),\n    _useState6 = _slicedToArray(_useState5, 2),\n    paymentPlan = _useState6[0],\n    setPaymentPlan = _useState6[1];\n  var _ref2 = state || {\n      subscription: {\n        subscriptionStatus: false\n      }\n    },\n    subscriptionStatus = _ref2.subscription.subscriptionStatus;\n  var _ref3 = route.params || {\n      accountNotRecovered: false\n    },\n    accountNotRecovered = _ref3.accountNotRecovered;\n  var premiumFeatures = [{\n    icon: '🚀',\n    title: 'Priority Signals',\n    description: 'Get signals 30 seconds before free users',\n    free: false,\n    pro: true,\n    elite: true\n  }, {\n    icon: '📊',\n    title: 'Advanced Analytics',\n    description: 'Detailed performance metrics and insights',\n    free: false,\n    pro: true,\n    elite: true\n  }, {\n    icon: '🎯',\n    title: 'Custom Risk Management',\n    description: 'Personalized risk settings and alerts',\n    free: false,\n    pro: true,\n    elite: true\n  }, {\n    icon: '📱',\n    title: 'Mobile Notifications',\n    description: 'Real-time push notifications',\n    free: true,\n    pro: true,\n    elite: true\n  }, {\n    icon: '💬',\n    title: 'Premium Support',\n    description: '24/7 priority customer support',\n    free: false,\n    pro: false,\n    elite: true\n  }, {\n    icon: '🤖',\n    title: 'Auto Trading',\n    description: 'Automated signal execution',\n    free: false,\n    pro: false,\n    elite: true\n  }, {\n    icon: '📈',\n    title: 'Portfolio Tracking',\n    description: 'Advanced portfolio management tools',\n    free: false,\n    pro: true,\n    elite: true\n  }, {\n    icon: '🔔',\n    title: 'Signal Channels',\n    description: 'Access to premium signal channels',\n    free: '3 channels',\n    pro: '15 channels',\n    elite: 'Unlimited'\n  }];\n  var pricingPlans = [{\n    id: 'free',\n    name: 'Free',\n    price: '$0',\n    period: 'forever',\n    description: 'Perfect for beginners',\n    features: ['3 Signal Channels', 'Basic Notifications', 'Community Support'],\n    color: '#4CAF50',\n    popular: false\n  }, {\n    id: 'pro',\n    name: 'Pro',\n    price: `$${PAYMENT_CONFIG.PLAN_PRICES.pro}`,\n    priceUSDT: `${PAYMENT_CONFIG.PLAN_PRICES.pro} USDT`,\n    period: 'month',\n    description: 'Most popular choice',\n    features: ['15 Signal Channels', 'Priority Signals', 'Advanced Analytics', 'Custom Risk Management'],\n    color: '#FECB37',\n    popular: true,\n    discount: '50% OFF'\n  }, {\n    id: 'elite',\n    name: 'Elite',\n    price: `$${PAYMENT_CONFIG.PLAN_PRICES.elite}`,\n    priceUSDT: `${PAYMENT_CONFIG.PLAN_PRICES.elite} USDT`,\n    period: 'month',\n    description: 'For serious traders',\n    features: ['Unlimited Channels', 'Auto Trading', 'Premium Support', 'All Pro Features'],\n    color: '#9C27B0',\n    popular: false\n  }];\n  var testimonials = [{\n    name: 'Sarah Chen',\n    role: 'Day Trader',\n    avatar: '👩‍💼',\n    rating: 5,\n    text: 'Premium signals helped me increase my portfolio by 340% in 6 months!'\n  }, {\n    name: 'Mike Rodriguez',\n    role: 'Crypto Investor',\n    avatar: '👨‍💻',\n    rating: 5,\n    text: 'The auto-trading feature is a game changer. I make money while I sleep!'\n  }, {\n    name: 'Alex Thompson',\n    role: 'Professional Trader',\n    avatar: '👨‍💼',\n    rating: 5,\n    text: 'Best ROI I\\'ve ever seen from a trading service. Highly recommended!'\n  }];\n  useEffect(function () {\n    if (accountNotRecovered) {\n      Alert.alert(\"Info\", \"No active subscription for this account.\");\n    }\n  }, [accountNotRecovered]);\n  var handleSubscribe = function handleSubscribe(planId) {\n    var plan = pricingPlans.find(function (p) {\n      return p.id === planId;\n    });\n    if (!plan) return;\n    Alert.alert(\"Escolha o Método de Pagamento\", `Plano ${plan.name} - ${plan.priceUSDT}`, [{\n      text: \"Cancelar\",\n      style: \"cancel\"\n    }, {\n      text: \"Pagar com USDT\",\n      onPress: function onPress() {\n        setPaymentPlan(planId);\n        setShowPaymentModal(true);\n      }\n    }, {\n      text: \"Pagar com Cartão\",\n      onPress: function onPress() {\n        Alert.alert(\"Em Breve\", \"Pagamento com cartão será disponibilizado em breve!\");\n      }\n    }]);\n  };\n  var handlePaymentSuccess = function handlePaymentSuccess(paymentSession) {\n    setShowPaymentModal(false);\n    setPaymentPlan(null);\n    Alert.alert(\"Pagamento Confirmado! 🎉\", \"Seu plano premium foi ativado com sucesso!\", [{\n      text: \"OK\",\n      onPress: function onPress() {}\n    }]);\n  };\n  var handlePaymentCancel = function handlePaymentCancel() {\n    setShowPaymentModal(false);\n    setPaymentPlan(null);\n  };\n  var handleManageSubscription = function handleManageSubscription() {\n    Alert.alert(\"Manage Subscription\", \"Subscription management coming soon!\");\n  };\n  if (subscriptionStatus) {\n    return _jsx(Wrapper, {\n      children: _jsxs(ScrollView, {\n        style: {\n          flex: 1\n        },\n        children: [_jsxs(View, {\n          style: {\n            padding: 16,\n            alignItems: 'center'\n          },\n          children: [_jsx(Text, {\n            style: {\n              fontSize: 60,\n              marginBottom: 16\n            },\n            children: \"\\uD83D\\uDC8E\"\n          }), _jsx(Text, {\n            style: {\n              color: '#FECB37',\n              fontSize: 24,\n              fontFamily: 'Poppins_700Bold',\n              textAlign: 'center',\n              marginBottom: 8\n            },\n            children: \"Premium Active\"\n          }), _jsx(Text, {\n            style: {\n              color: '#8a8a8a',\n              fontSize: 14,\n              fontFamily: 'Poppins_400Regular',\n              textAlign: 'center'\n            },\n            children: \"You have access to all premium features\"\n          })]\n        }), _jsxs(View, {\n          style: {\n            paddingHorizontal: 8,\n            marginBottom: 16\n          },\n          children: [_jsx(Text, {\n            style: {\n              color: '#fff',\n              fontSize: 18,\n              fontFamily: 'Poppins_600SemiBold',\n              marginBottom: 12,\n              paddingHorizontal: 8\n            },\n            children: \"Your Premium Benefits\"\n          }), _jsxs(View, {\n            style: {\n              flexDirection: 'row',\n              flexWrap: 'wrap'\n            },\n            children: [_jsx(StatCard, {\n              title: \"Premium Channels\",\n              value: \"15\",\n              subtitle: \"Unlocked\",\n              icon: \"\\uD83D\\uDCFA\"\n            }), _jsx(StatCard, {\n              title: \"Priority Signals\",\n              value: \"847\",\n              subtitle: \"Received\",\n              icon: \"\\uD83D\\uDE80\"\n            }), _jsx(StatCard, {\n              title: \"Success Rate\",\n              value: \"84.2%\",\n              change: \"+5.1%\",\n              changeType: \"positive\",\n              icon: \"\\uD83C\\uDFAF\"\n            }), _jsx(StatCard, {\n              title: \"Savings\",\n              value: \"$2,340\",\n              subtitle: \"This month\",\n              icon: \"\\uD83D\\uDCB0\"\n            })]\n          })]\n        }), _jsxs(View, {\n          style: {\n            paddingHorizontal: 16,\n            marginBottom: 20\n          },\n          children: [_jsx(Text, {\n            style: {\n              color: '#fff',\n              fontSize: 18,\n              fontFamily: 'Poppins_600SemiBold',\n              marginBottom: 12\n            },\n            children: \"Subscription Management\"\n          }), _jsxs(Card, {\n            children: [_jsxs(View, {\n              style: {\n                flexDirection: 'row',\n                justifyContent: 'space-between',\n                alignItems: 'center',\n                marginBottom: 16\n              },\n              children: [_jsxs(View, {\n                children: [_jsx(Text, {\n                  style: {\n                    color: '#fff',\n                    fontSize: 16,\n                    fontFamily: 'Poppins_600SemiBold'\n                  },\n                  children: \"Pro Plan\"\n                }), _jsx(Text, {\n                  style: {\n                    color: '#8a8a8a',\n                    fontSize: 12,\n                    fontFamily: 'Poppins_400Regular'\n                  },\n                  children: \"Next billing: February 15, 2024\"\n                })]\n              }), _jsx(View, {\n                style: {\n                  backgroundColor: '#FECB37',\n                  paddingHorizontal: 8,\n                  paddingVertical: 4,\n                  borderRadius: 4\n                },\n                children: _jsx(Text, {\n                  style: {\n                    color: '#000',\n                    fontSize: 12,\n                    fontFamily: 'Poppins_600SemiBold'\n                  },\n                  children: \"ACTIVE\"\n                })\n              })]\n            }), _jsx(Button, {\n              mode: \"outlined\",\n              onPress: handleManageSubscription,\n              style: {\n                borderColor: '#FECB37'\n              },\n              labelStyle: {\n                color: '#FECB37',\n                fontFamily: 'Poppins_500Medium'\n              },\n              children: \"Manage Subscription\"\n            })]\n          })]\n        })]\n      })\n    });\n  }\n  return _jsxs(Wrapper, {\n    children: [_jsxs(ScrollView, {\n      style: {\n        flex: 1\n      },\n      children: [_jsxs(View, {\n        style: {\n          padding: 16,\n          alignItems: 'center',\n          backgroundColor: 'linear-gradient(135deg, #FECB37, #FF9800)'\n        },\n        children: [_jsx(Text, {\n          style: {\n            fontSize: 60,\n            marginBottom: 16\n          },\n          children: \"\\uD83D\\uDC8E\"\n        }), _jsx(Text, {\n          style: {\n            color: '#fff',\n            fontSize: 28,\n            fontFamily: 'Poppins_700Bold',\n            textAlign: 'center',\n            marginBottom: 8\n          },\n          children: \"Unlock Premium Trading\"\n        }), _jsx(Text, {\n          style: {\n            color: '#fff',\n            fontSize: 16,\n            fontFamily: 'Poppins_400Regular',\n            textAlign: 'center',\n            marginBottom: 16,\n            opacity: 0.9\n          },\n          children: \"Get priority signals and advanced features\"\n        }), _jsx(View, {\n          style: {\n            backgroundColor: '#F44336',\n            paddingHorizontal: 16,\n            paddingVertical: 8,\n            borderRadius: 20,\n            marginBottom: 16\n          },\n          children: _jsx(Text, {\n            style: {\n              color: '#fff',\n              fontSize: 12,\n              fontFamily: 'Poppins_600SemiBold',\n              textAlign: 'center'\n            },\n            children: \"\\uD83D\\uDD25 LIMITED TIME: 50% OFF FIRST MONTH\"\n          })\n        })]\n      }), _jsxs(View, {\n        style: {\n          paddingHorizontal: 8,\n          marginTop: 16,\n          marginBottom: 16\n        },\n        children: [_jsx(Text, {\n          style: {\n            color: '#fff',\n            fontSize: 18,\n            fontFamily: 'Poppins_600SemiBold',\n            marginBottom: 12,\n            paddingHorizontal: 8,\n            textAlign: 'center'\n          },\n          children: \"Join 50,000+ Successful Traders\"\n        }), _jsxs(View, {\n          style: {\n            flexDirection: 'row',\n            flexWrap: 'wrap'\n          },\n          children: [_jsx(StatCard, {\n            title: \"Average ROI\",\n            value: \"340%\",\n            subtitle: \"Per year\",\n            icon: \"\\uD83D\\uDCC8\"\n          }), _jsx(StatCard, {\n            title: \"Success Rate\",\n            value: \"87.3%\",\n            subtitle: \"Signal accuracy\",\n            icon: \"\\uD83C\\uDFAF\"\n          }), _jsx(StatCard, {\n            title: \"Active Users\",\n            value: \"50K+\",\n            subtitle: \"Worldwide\",\n            icon: \"\\uD83D\\uDC65\"\n          }), _jsx(StatCard, {\n            title: \"Signals Daily\",\n            value: \"150+\",\n            subtitle: \"Premium signals\",\n            icon: \"\\uD83D\\uDCE1\"\n          })]\n        })]\n      }), _jsxs(View, {\n        style: {\n          paddingHorizontal: 16,\n          marginBottom: 16\n        },\n        children: [_jsx(Text, {\n          style: {\n            color: '#fff',\n            fontSize: 20,\n            fontFamily: 'Poppins_700Bold',\n            marginBottom: 16,\n            textAlign: 'center'\n          },\n          children: \"Choose Your Plan\"\n        }), pricingPlans.map(function (plan) {\n          return _jsx(TouchableOpacity, {\n            onPress: function onPress() {\n              return setSelectedPlan(plan.id);\n            },\n            style: {\n              marginBottom: 12\n            },\n            children: _jsxs(Card, {\n              style: {\n                borderColor: selectedPlan === plan.id ? plan.color : 'transparent',\n                borderWidth: selectedPlan === plan.id ? 2 : 0,\n                position: 'relative'\n              },\n              children: [plan.popular && _jsx(View, {\n                style: {\n                  position: 'absolute',\n                  top: -8,\n                  right: 16,\n                  backgroundColor: '#F44336',\n                  paddingHorizontal: 12,\n                  paddingVertical: 4,\n                  borderRadius: 12,\n                  zIndex: 1\n                },\n                children: _jsx(Text, {\n                  style: {\n                    color: '#fff',\n                    fontSize: 10,\n                    fontFamily: 'Poppins_600SemiBold'\n                  },\n                  children: \"MOST POPULAR\"\n                })\n              }), plan.discount && _jsx(View, {\n                style: {\n                  position: 'absolute',\n                  top: -8,\n                  left: 16,\n                  backgroundColor: '#4CAF50',\n                  paddingHorizontal: 12,\n                  paddingVertical: 4,\n                  borderRadius: 12,\n                  zIndex: 1\n                },\n                children: _jsx(Text, {\n                  style: {\n                    color: '#fff',\n                    fontSize: 10,\n                    fontFamily: 'Poppins_600SemiBold'\n                  },\n                  children: plan.discount\n                })\n              }), _jsxs(View, {\n                style: {\n                  flexDirection: 'row',\n                  alignItems: 'center',\n                  marginBottom: 12\n                },\n                children: [_jsxs(View, {\n                  style: {\n                    flex: 1\n                  },\n                  children: [_jsx(Text, {\n                    style: {\n                      color: plan.color,\n                      fontSize: 20,\n                      fontFamily: 'Poppins_700Bold'\n                    },\n                    children: plan.name\n                  }), _jsx(Text, {\n                    style: {\n                      color: '#8a8a8a',\n                      fontSize: 12,\n                      fontFamily: 'Poppins_400Regular'\n                    },\n                    children: plan.description\n                  })]\n                }), _jsxs(View, {\n                  style: {\n                    alignItems: 'flex-end'\n                  },\n                  children: [_jsxs(View, {\n                    style: {\n                      flexDirection: 'row',\n                      alignItems: 'baseline'\n                    },\n                    children: [_jsx(Text, {\n                      style: {\n                        color: '#fff',\n                        fontSize: 24,\n                        fontFamily: 'Poppins_700Bold'\n                      },\n                      children: plan.price\n                    }), _jsxs(Text, {\n                      style: {\n                        color: '#8a8a8a',\n                        fontSize: 12,\n                        fontFamily: 'Poppins_400Regular',\n                        marginLeft: 4\n                      },\n                      children: [\"/\", plan.period]\n                    })]\n                  }), plan.priceUSDT && _jsx(Text, {\n                    style: {\n                      color: '#4CAF50',\n                      fontSize: 12,\n                      fontFamily: 'Poppins_500Medium',\n                      marginTop: 2\n                    },\n                    children: plan.priceUSDT\n                  })]\n                })]\n              }), _jsx(View, {\n                style: {\n                  marginBottom: 16\n                },\n                children: plan.features.map(function (feature, index) {\n                  return _jsxs(View, {\n                    style: {\n                      flexDirection: 'row',\n                      alignItems: 'center',\n                      marginBottom: 4\n                    },\n                    children: [_jsx(Text, {\n                      style: {\n                        color: '#4CAF50',\n                        marginRight: 8\n                      },\n                      children: \"\\u2713\"\n                    }), _jsx(Text, {\n                      style: {\n                        color: '#ccc',\n                        fontSize: 14,\n                        fontFamily: 'Poppins_400Regular'\n                      },\n                      children: feature\n                    })]\n                  }, index);\n                })\n              }), plan.id !== 'free' && _jsx(Button, {\n                mode: selectedPlan === plan.id ? \"contained\" : \"outlined\",\n                onPress: function onPress() {\n                  return handleSubscribe(plan.id);\n                },\n                style: {\n                  backgroundColor: selectedPlan === plan.id ? plan.color : 'transparent',\n                  borderColor: plan.color\n                },\n                labelStyle: {\n                  color: selectedPlan === plan.id ? '#000' : plan.color,\n                  fontFamily: 'Poppins_600SemiBold'\n                },\n                children: selectedPlan === plan.id ? 'Selected Plan' : `Choose ${plan.name}`\n              })]\n            })\n          }, plan.id);\n        })]\n      }), _jsxs(View, {\n        style: {\n          paddingHorizontal: 16,\n          marginBottom: 16\n        },\n        children: [_jsx(Text, {\n          style: {\n            color: '#fff',\n            fontSize: 20,\n            fontFamily: 'Poppins_700Bold',\n            marginBottom: 16,\n            textAlign: 'center'\n          },\n          children: \"Feature Comparison\"\n        }), _jsxs(Card, {\n          children: [_jsxs(View, {\n            style: {\n              flexDirection: 'row',\n              marginBottom: 12\n            },\n            children: [_jsx(View, {\n              style: {\n                flex: 2\n              },\n              children: _jsx(Text, {\n                style: {\n                  color: '#8a8a8a',\n                  fontSize: 12,\n                  fontFamily: 'Poppins_600SemiBold'\n                },\n                children: \"FEATURES\"\n              })\n            }), _jsx(View, {\n              style: {\n                flex: 1,\n                alignItems: 'center'\n              },\n              children: _jsx(Text, {\n                style: {\n                  color: '#4CAF50',\n                  fontSize: 12,\n                  fontFamily: 'Poppins_600SemiBold'\n                },\n                children: \"FREE\"\n              })\n            }), _jsx(View, {\n              style: {\n                flex: 1,\n                alignItems: 'center'\n              },\n              children: _jsx(Text, {\n                style: {\n                  color: '#FECB37',\n                  fontSize: 12,\n                  fontFamily: 'Poppins_600SemiBold'\n                },\n                children: \"PRO\"\n              })\n            }), _jsx(View, {\n              style: {\n                flex: 1,\n                alignItems: 'center'\n              },\n              children: _jsx(Text, {\n                style: {\n                  color: '#9C27B0',\n                  fontSize: 12,\n                  fontFamily: 'Poppins_600SemiBold'\n                },\n                children: \"ELITE\"\n              })\n            })]\n          }), premiumFeatures.map(function (feature, index) {\n            return _jsxs(View, {\n              style: {\n                flexDirection: 'row',\n                alignItems: 'center',\n                paddingVertical: 8,\n                borderTopWidth: index > 0 ? 1 : 0,\n                borderTopColor: '#333'\n              },\n              children: [_jsxs(View, {\n                style: {\n                  flex: 2,\n                  flexDirection: 'row',\n                  alignItems: 'center'\n                },\n                children: [_jsx(Text, {\n                  style: {\n                    fontSize: 16,\n                    marginRight: 8\n                  },\n                  children: feature.icon\n                }), _jsxs(View, {\n                  children: [_jsx(Text, {\n                    style: {\n                      color: '#fff',\n                      fontSize: 14,\n                      fontFamily: 'Poppins_500Medium'\n                    },\n                    children: feature.title\n                  }), _jsx(Text, {\n                    style: {\n                      color: '#8a8a8a',\n                      fontSize: 11,\n                      fontFamily: 'Poppins_400Regular'\n                    },\n                    children: feature.description\n                  })]\n                })]\n              }), _jsx(View, {\n                style: {\n                  flex: 1,\n                  alignItems: 'center'\n                },\n                children: _jsx(Text, {\n                  style: {\n                    color: typeof feature.free === 'boolean' ? feature.free ? '#4CAF50' : '#F44336' : '#8a8a8a',\n                    fontSize: 12,\n                    fontFamily: 'Poppins_500Medium'\n                  },\n                  children: typeof feature.free === 'boolean' ? feature.free ? '✓' : '✗' : feature.free\n                })\n              }), _jsx(View, {\n                style: {\n                  flex: 1,\n                  alignItems: 'center'\n                },\n                children: _jsx(Text, {\n                  style: {\n                    color: typeof feature.pro === 'boolean' ? feature.pro ? '#4CAF50' : '#F44336' : '#8a8a8a',\n                    fontSize: 12,\n                    fontFamily: 'Poppins_500Medium'\n                  },\n                  children: typeof feature.pro === 'boolean' ? feature.pro ? '✓' : '✗' : feature.pro\n                })\n              }), _jsx(View, {\n                style: {\n                  flex: 1,\n                  alignItems: 'center'\n                },\n                children: _jsx(Text, {\n                  style: {\n                    color: typeof feature.elite === 'boolean' ? feature.elite ? '#4CAF50' : '#F44336' : '#8a8a8a',\n                    fontSize: 12,\n                    fontFamily: 'Poppins_500Medium'\n                  },\n                  children: typeof feature.elite === 'boolean' ? feature.elite ? '✓' : '✗' : feature.elite\n                })\n              })]\n            }, index);\n          })]\n        })]\n      }), _jsxs(View, {\n        style: {\n          paddingHorizontal: 16,\n          marginBottom: 16\n        },\n        children: [_jsx(Text, {\n          style: {\n            color: '#fff',\n            fontSize: 20,\n            fontFamily: 'Poppins_700Bold',\n            marginBottom: 16,\n            textAlign: 'center'\n          },\n          children: \"What Our Users Say\"\n        }), _jsx(ScrollView, {\n          horizontal: true,\n          showsHorizontalScrollIndicator: false,\n          children: testimonials.map(function (testimonial, index) {\n            return _jsxs(Card, {\n              style: {\n                marginRight: 12,\n                width: 280\n              },\n              children: [_jsxs(View, {\n                style: {\n                  flexDirection: 'row',\n                  alignItems: 'center',\n                  marginBottom: 12\n                },\n                children: [_jsx(View, {\n                  style: {\n                    backgroundColor: '#333',\n                    borderRadius: 20,\n                    width: 40,\n                    height: 40,\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    marginRight: 12\n                  },\n                  children: _jsx(Text, {\n                    style: {\n                      fontSize: 20\n                    },\n                    children: testimonial.avatar\n                  })\n                }), _jsxs(View, {\n                  style: {\n                    flex: 1\n                  },\n                  children: [_jsx(Text, {\n                    style: {\n                      color: '#fff',\n                      fontSize: 14,\n                      fontFamily: 'Poppins_600SemiBold'\n                    },\n                    children: testimonial.name\n                  }), _jsx(Text, {\n                    style: {\n                      color: '#8a8a8a',\n                      fontSize: 12,\n                      fontFamily: 'Poppins_400Regular'\n                    },\n                    children: testimonial.role\n                  })]\n                }), _jsx(View, {\n                  style: {\n                    flexDirection: 'row'\n                  },\n                  children: _toConsumableArray(Array(testimonial.rating)).map(function (_, i) {\n                    return _jsx(Text, {\n                      style: {\n                        color: '#FECB37',\n                        fontSize: 12\n                      },\n                      children: \"\\u2B50\"\n                    }, i);\n                  })\n                })]\n              }), _jsxs(Text, {\n                style: {\n                  color: '#ccc',\n                  fontSize: 13,\n                  fontFamily: 'Poppins_400Regular',\n                  lineHeight: 18,\n                  fontStyle: 'italic'\n                },\n                children: [\"\\\"\", testimonial.text, \"\\\"\"]\n              })]\n            }, index);\n          })\n        })]\n      }), _jsx(View, {\n        style: {\n          paddingHorizontal: 16,\n          marginBottom: 20\n        },\n        children: _jsxs(Card, {\n          style: {\n            backgroundColor: '#FECB37',\n            padding: 20,\n            alignItems: 'center'\n          },\n          children: [_jsx(Text, {\n            style: {\n              color: '#000',\n              fontSize: 20,\n              fontFamily: 'Poppins_700Bold',\n              textAlign: 'center',\n              marginBottom: 8\n            },\n            children: \"Ready to Start Earning?\"\n          }), _jsx(Text, {\n            style: {\n              color: '#000',\n              fontSize: 14,\n              fontFamily: 'Poppins_400Regular',\n              textAlign: 'center',\n              marginBottom: 16,\n              opacity: 0.8\n            },\n            children: \"Join thousands of successful traders today\"\n          }), _jsx(Button, {\n            mode: \"contained\",\n            onPress: function onPress() {\n              return handleSubscribe(selectedPlan);\n            },\n            style: {\n              backgroundColor: '#000',\n              paddingHorizontal: 20\n            },\n            labelStyle: {\n              color: '#FECB37',\n              fontFamily: 'Poppins_600SemiBold',\n              fontSize: 16\n            },\n            children: \"Start Premium Trial\"\n          }), _jsx(Text, {\n            style: {\n              color: '#000',\n              fontSize: 11,\n              fontFamily: 'Poppins_400Regular',\n              textAlign: 'center',\n              marginTop: 8,\n              opacity: 0.7\n            },\n            children: \"Cancel anytime \\u2022 No hidden fees\"\n          })]\n        })\n      })]\n    }), _jsx(Modal, {\n      visible: showPaymentModal,\n      animationType: \"slide\",\n      presentationStyle: \"pageSheet\",\n      onRequestClose: handlePaymentCancel,\n      children: _jsx(USDTPayment, {\n        planId: paymentPlan,\n        onPaymentSuccess: handlePaymentSuccess,\n        onPaymentCancel: handlePaymentCancel,\n        visible: showPaymentModal\n      })\n    })]\n  });\n}", "map": {"version": 3, "names": ["React", "useContext", "useEffect", "useState", "View", "<PERSON><PERSON>", "ScrollView", "TouchableOpacity", "Modal", "<PERSON><PERSON>", "Text", "ProgressBar", "Wrapper", "Card", "StatCard", "USDTPayment", "StoreContext", "PAYMENT_CONFIG", "jsx", "_jsx", "jsxs", "_jsxs", "Premium", "_ref", "_ref$route", "route", "_useContext", "_useContext2", "_slicedToArray", "state", "dispatch", "_useState", "_useState2", "<PERSON><PERSON><PERSON>", "setSelectedPlan", "_useState3", "_useState4", "showPaymentModal", "setShowPaymentModal", "_useState5", "_useState6", "paymentPlan", "setPaymentPlan", "_ref2", "subscription", "subscriptionStatus", "_ref3", "params", "accountNotRecovered", "premiumFeatures", "icon", "title", "description", "free", "pro", "elite", "pricingPlans", "id", "name", "price", "period", "features", "color", "popular", "PLAN_PRICES", "priceUSDT", "discount", "testimonials", "role", "avatar", "rating", "text", "alert", "handleSubscribe", "planId", "plan", "find", "p", "style", "onPress", "handlePaymentSuccess", "paymentSession", "handlePaymentCancel", "handleManageSubscription", "children", "flex", "padding", "alignItems", "fontSize", "marginBottom", "fontFamily", "textAlign", "paddingHorizontal", "flexDirection", "flexWrap", "value", "subtitle", "change", "changeType", "justifyContent", "backgroundColor", "paddingVertical", "borderRadius", "mode", "borderColor", "labelStyle", "opacity", "marginTop", "map", "borderWidth", "position", "top", "right", "zIndex", "left", "marginLeft", "feature", "index", "marginRight", "borderTopWidth", "borderTopColor", "horizontal", "showsHorizontalScrollIndicator", "testimonial", "width", "height", "_toConsumableArray", "Array", "_", "i", "lineHeight", "fontStyle", "visible", "animationType", "presentationStyle", "onRequestClose", "onPaymentSuccess", "onPaymentCancel"], "sources": ["C:/Users/<USER>/Desktop/Signals/CryptoSignalsApp/src/pages/Premium/index.js"], "sourcesContent": ["import React, { useContext, useEffect, useState } from \"react\";\r\nimport { View, Alert, ScrollView, TouchableOpacity, Modal } from \"react-native\";\r\nimport { But<PERSON>, Text, ProgressBar } from 'react-native-paper';\r\nimport Wrapper from \"../../components/Wrapper\";\r\nimport Card from \"../../components/Card\";\r\nimport StatCard from \"../../components/StatCard\";\r\nimport USDTPayment from \"../../components/USDTPayment\";\r\nimport { StoreContext } from '../../store';\r\nimport { PAYMENT_CONFIG } from '../../config/api';\r\n\r\nexport default function Premium({ route = {} }) {\r\n  const [state, dispatch] = useContext(StoreContext);\r\n  const [selectedPlan, setSelectedPlan] = useState('pro');\r\n  const [showPaymentModal, setShowPaymentModal] = useState(false);\r\n  const [paymentPlan, setPaymentPlan] = useState(null);\r\n\r\n  const { subscription: { subscriptionStatus } } = state || { subscription: { subscriptionStatus: false } };\r\n  let { accountNotRecovered } = route.params || { accountNotRecovered: false };\r\n\r\n  // Premium features data\r\n  const premiumFeatures = [\r\n    {\r\n      icon: '🚀',\r\n      title: 'Priority Signals',\r\n      description: 'Get signals 30 seconds before free users',\r\n      free: false,\r\n      pro: true,\r\n      elite: true\r\n    },\r\n    {\r\n      icon: '📊',\r\n      title: 'Advanced Analytics',\r\n      description: 'Detailed performance metrics and insights',\r\n      free: false,\r\n      pro: true,\r\n      elite: true\r\n    },\r\n    {\r\n      icon: '🎯',\r\n      title: 'Custom Risk Management',\r\n      description: 'Personalized risk settings and alerts',\r\n      free: false,\r\n      pro: true,\r\n      elite: true\r\n    },\r\n    {\r\n      icon: '📱',\r\n      title: 'Mobile Notifications',\r\n      description: 'Real-time push notifications',\r\n      free: true,\r\n      pro: true,\r\n      elite: true\r\n    },\r\n    {\r\n      icon: '💬',\r\n      title: 'Premium Support',\r\n      description: '24/7 priority customer support',\r\n      free: false,\r\n      pro: false,\r\n      elite: true\r\n    },\r\n    {\r\n      icon: '🤖',\r\n      title: 'Auto Trading',\r\n      description: 'Automated signal execution',\r\n      free: false,\r\n      pro: false,\r\n      elite: true\r\n    },\r\n    {\r\n      icon: '📈',\r\n      title: 'Portfolio Tracking',\r\n      description: 'Advanced portfolio management tools',\r\n      free: false,\r\n      pro: true,\r\n      elite: true\r\n    },\r\n    {\r\n      icon: '🔔',\r\n      title: 'Signal Channels',\r\n      description: 'Access to premium signal channels',\r\n      free: '3 channels',\r\n      pro: '15 channels',\r\n      elite: 'Unlimited'\r\n    }\r\n  ];\r\n\r\n  // Pricing plans\r\n  const pricingPlans = [\r\n    {\r\n      id: 'free',\r\n      name: 'Free',\r\n      price: '$0',\r\n      period: 'forever',\r\n      description: 'Perfect for beginners',\r\n      features: ['3 Signal Channels', 'Basic Notifications', 'Community Support'],\r\n      color: '#4CAF50',\r\n      popular: false\r\n    },\r\n    {\r\n      id: 'pro',\r\n      name: 'Pro',\r\n      price: `$${PAYMENT_CONFIG.PLAN_PRICES.pro}`,\r\n      priceUSDT: `${PAYMENT_CONFIG.PLAN_PRICES.pro} USDT`,\r\n      period: 'month',\r\n      description: 'Most popular choice',\r\n      features: ['15 Signal Channels', 'Priority Signals', 'Advanced Analytics', 'Custom Risk Management'],\r\n      color: '#FECB37',\r\n      popular: true,\r\n      discount: '50% OFF'\r\n    },\r\n    {\r\n      id: 'elite',\r\n      name: 'Elite',\r\n      price: `$${PAYMENT_CONFIG.PLAN_PRICES.elite}`,\r\n      priceUSDT: `${PAYMENT_CONFIG.PLAN_PRICES.elite} USDT`,\r\n      period: 'month',\r\n      description: 'For serious traders',\r\n      features: ['Unlimited Channels', 'Auto Trading', 'Premium Support', 'All Pro Features'],\r\n      color: '#9C27B0',\r\n      popular: false\r\n    }\r\n  ];\r\n\r\n  // Testimonials\r\n  const testimonials = [\r\n    {\r\n      name: 'Sarah Chen',\r\n      role: 'Day Trader',\r\n      avatar: '👩‍💼',\r\n      rating: 5,\r\n      text: 'Premium signals helped me increase my portfolio by 340% in 6 months!'\r\n    },\r\n    {\r\n      name: 'Mike Rodriguez',\r\n      role: 'Crypto Investor',\r\n      avatar: '👨‍💻',\r\n      rating: 5,\r\n      text: 'The auto-trading feature is a game changer. I make money while I sleep!'\r\n    },\r\n    {\r\n      name: 'Alex Thompson',\r\n      role: 'Professional Trader',\r\n      avatar: '👨‍💼',\r\n      rating: 5,\r\n      text: 'Best ROI I\\'ve ever seen from a trading service. Highly recommended!'\r\n    }\r\n  ];\r\n\r\n  useEffect(() => {\r\n    if (accountNotRecovered) {\r\n      Alert.alert(\"Info\", \"No active subscription for this account.\");\r\n    }\r\n  }, [accountNotRecovered]);\r\n\r\n  const handleSubscribe = (planId) => {\r\n    const plan = pricingPlans.find(p => p.id === planId);\r\n    if (!plan) return;\r\n\r\n    Alert.alert(\r\n      \"Escolha o Método de Pagamento\",\r\n      `Plano ${plan.name} - ${plan.priceUSDT}`,\r\n      [\r\n        {\r\n          text: \"Cancelar\",\r\n          style: \"cancel\"\r\n        },\r\n        {\r\n          text: \"Pagar com USDT\",\r\n          onPress: () => {\r\n            setPaymentPlan(planId);\r\n            setShowPaymentModal(true);\r\n          }\r\n        },\r\n        {\r\n          text: \"Pagar com Cartão\",\r\n          onPress: () => {\r\n            Alert.alert(\"Em Breve\", \"Pagamento com cartão será disponibilizado em breve!\");\r\n          }\r\n        }\r\n      ]\r\n    );\r\n  };\r\n\r\n  const handlePaymentSuccess = (paymentSession) => {\r\n    setShowPaymentModal(false);\r\n    setPaymentPlan(null);\r\n\r\n    // Aqui você pode atualizar o estado da assinatura\r\n    Alert.alert(\r\n      \"Pagamento Confirmado! 🎉\",\r\n      \"Seu plano premium foi ativado com sucesso!\",\r\n      [\r\n        {\r\n          text: \"OK\",\r\n          onPress: () => {\r\n            // Atualizar estado da assinatura no contexto\r\n            // dispatch({ type: 'SET_SUBSCRIPTION', payload: { subscriptionStatus: true } });\r\n          }\r\n        }\r\n      ]\r\n    );\r\n  };\r\n\r\n  const handlePaymentCancel = () => {\r\n    setShowPaymentModal(false);\r\n    setPaymentPlan(null);\r\n  };\r\n\r\n  const handleManageSubscription = () => {\r\n    Alert.alert(\"Manage Subscription\", \"Subscription management coming soon!\");\r\n  };\r\n\r\n  if (subscriptionStatus) {\r\n    return (\r\n      <Wrapper>\r\n        <ScrollView style={{ flex: 1 }}>\r\n          {/* Premium Active Header */}\r\n          <View style={{ padding: 16, alignItems: 'center' }}>\r\n            <Text style={{ fontSize: 60, marginBottom: 16 }}>💎</Text>\r\n            <Text style={{\r\n              color: '#FECB37',\r\n              fontSize: 24,\r\n              fontFamily: 'Poppins_700Bold',\r\n              textAlign: 'center',\r\n              marginBottom: 8\r\n            }}>\r\n              Premium Active\r\n            </Text>\r\n            <Text style={{\r\n              color: '#8a8a8a',\r\n              fontSize: 14,\r\n              fontFamily: 'Poppins_400Regular',\r\n              textAlign: 'center'\r\n            }}>\r\n              You have access to all premium features\r\n            </Text>\r\n          </View>\r\n\r\n          {/* Premium Stats */}\r\n          <View style={{ paddingHorizontal: 8, marginBottom: 16 }}>\r\n            <Text style={{\r\n              color: '#fff',\r\n              fontSize: 18,\r\n              fontFamily: 'Poppins_600SemiBold',\r\n              marginBottom: 12,\r\n              paddingHorizontal: 8\r\n            }}>\r\n              Your Premium Benefits\r\n            </Text>\r\n            <View style={{ flexDirection: 'row', flexWrap: 'wrap' }}>\r\n              <StatCard\r\n                title=\"Premium Channels\"\r\n                value=\"15\"\r\n                subtitle=\"Unlocked\"\r\n                icon=\"📺\"\r\n              />\r\n              <StatCard\r\n                title=\"Priority Signals\"\r\n                value=\"847\"\r\n                subtitle=\"Received\"\r\n                icon=\"🚀\"\r\n              />\r\n              <StatCard\r\n                title=\"Success Rate\"\r\n                value=\"84.2%\"\r\n                change=\"+5.1%\"\r\n                changeType=\"positive\"\r\n                icon=\"🎯\"\r\n              />\r\n              <StatCard\r\n                title=\"Savings\"\r\n                value=\"$2,340\"\r\n                subtitle=\"This month\"\r\n                icon=\"💰\"\r\n              />\r\n            </View>\r\n          </View>\r\n\r\n          {/* Subscription Management */}\r\n          <View style={{ paddingHorizontal: 16, marginBottom: 20 }}>\r\n            <Text style={{\r\n              color: '#fff',\r\n              fontSize: 18,\r\n              fontFamily: 'Poppins_600SemiBold',\r\n              marginBottom: 12\r\n            }}>\r\n              Subscription Management\r\n            </Text>\r\n\r\n            <Card>\r\n              <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>\r\n                <View>\r\n                  <Text style={{ color: '#fff', fontSize: 16, fontFamily: 'Poppins_600SemiBold' }}>Pro Plan</Text>\r\n                  <Text style={{ color: '#8a8a8a', fontSize: 12, fontFamily: 'Poppins_400Regular' }}>Next billing: February 15, 2024</Text>\r\n                </View>\r\n                <View style={{\r\n                  backgroundColor: '#FECB37',\r\n                  paddingHorizontal: 8,\r\n                  paddingVertical: 4,\r\n                  borderRadius: 4\r\n                }}>\r\n                  <Text style={{ color: '#000', fontSize: 12, fontFamily: 'Poppins_600SemiBold' }}>ACTIVE</Text>\r\n                </View>\r\n              </View>\r\n\r\n              <Button\r\n                mode=\"outlined\"\r\n                onPress={handleManageSubscription}\r\n                style={{ borderColor: '#FECB37' }}\r\n                labelStyle={{ color: '#FECB37', fontFamily: 'Poppins_500Medium' }}\r\n              >\r\n                Manage Subscription\r\n              </Button>\r\n            </Card>\r\n          </View>\r\n        </ScrollView>\r\n      </Wrapper>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Wrapper>\r\n      <ScrollView style={{ flex: 1 }}>\r\n        {/* Hero Section */}\r\n        <View style={{ padding: 16, alignItems: 'center', backgroundColor: 'linear-gradient(135deg, #FECB37, #FF9800)' }}>\r\n          <Text style={{ fontSize: 60, marginBottom: 16 }}>💎</Text>\r\n          <Text style={{\r\n            color: '#fff',\r\n            fontSize: 28,\r\n            fontFamily: 'Poppins_700Bold',\r\n            textAlign: 'center',\r\n            marginBottom: 8\r\n          }}>\r\n            Unlock Premium Trading\r\n          </Text>\r\n          <Text style={{\r\n            color: '#fff',\r\n            fontSize: 16,\r\n            fontFamily: 'Poppins_400Regular',\r\n            textAlign: 'center',\r\n            marginBottom: 16,\r\n            opacity: 0.9\r\n          }}>\r\n            Get priority signals and advanced features\r\n          </Text>\r\n\r\n          {/* Limited Time Offer */}\r\n          <View style={{\r\n            backgroundColor: '#F44336',\r\n            paddingHorizontal: 16,\r\n            paddingVertical: 8,\r\n            borderRadius: 20,\r\n            marginBottom: 16\r\n          }}>\r\n            <Text style={{\r\n              color: '#fff',\r\n              fontSize: 12,\r\n              fontFamily: 'Poppins_600SemiBold',\r\n              textAlign: 'center'\r\n            }}>\r\n              🔥 LIMITED TIME: 50% OFF FIRST MONTH\r\n            </Text>\r\n          </View>\r\n        </View>\r\n\r\n        {/* Success Stats */}\r\n        <View style={{ paddingHorizontal: 8, marginTop: 16, marginBottom: 16 }}>\r\n          <Text style={{\r\n            color: '#fff',\r\n            fontSize: 18,\r\n            fontFamily: 'Poppins_600SemiBold',\r\n            marginBottom: 12,\r\n            paddingHorizontal: 8,\r\n            textAlign: 'center'\r\n          }}>\r\n            Join 50,000+ Successful Traders\r\n          </Text>\r\n          <View style={{ flexDirection: 'row', flexWrap: 'wrap' }}>\r\n            <StatCard\r\n              title=\"Average ROI\"\r\n              value=\"340%\"\r\n              subtitle=\"Per year\"\r\n              icon=\"📈\"\r\n            />\r\n            <StatCard\r\n              title=\"Success Rate\"\r\n              value=\"87.3%\"\r\n              subtitle=\"Signal accuracy\"\r\n              icon=\"🎯\"\r\n            />\r\n            <StatCard\r\n              title=\"Active Users\"\r\n              value=\"50K+\"\r\n              subtitle=\"Worldwide\"\r\n              icon=\"👥\"\r\n            />\r\n            <StatCard\r\n              title=\"Signals Daily\"\r\n              value=\"150+\"\r\n              subtitle=\"Premium signals\"\r\n              icon=\"📡\"\r\n            />\r\n          </View>\r\n        </View>\r\n\r\n        {/* Pricing Plans */}\r\n        <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>\r\n          <Text style={{\r\n            color: '#fff',\r\n            fontSize: 20,\r\n            fontFamily: 'Poppins_700Bold',\r\n            marginBottom: 16,\r\n            textAlign: 'center'\r\n          }}>\r\n            Choose Your Plan\r\n          </Text>\r\n\r\n          {pricingPlans.map((plan) => (\r\n            <TouchableOpacity\r\n              key={plan.id}\r\n              onPress={() => setSelectedPlan(plan.id)}\r\n              style={{ marginBottom: 12 }}\r\n            >\r\n              <Card style={{\r\n                borderColor: selectedPlan === plan.id ? plan.color : 'transparent',\r\n                borderWidth: selectedPlan === plan.id ? 2 : 0,\r\n                position: 'relative'\r\n              }}>\r\n                {plan.popular && (\r\n                  <View style={{\r\n                    position: 'absolute',\r\n                    top: -8,\r\n                    right: 16,\r\n                    backgroundColor: '#F44336',\r\n                    paddingHorizontal: 12,\r\n                    paddingVertical: 4,\r\n                    borderRadius: 12,\r\n                    zIndex: 1\r\n                  }}>\r\n                    <Text style={{\r\n                      color: '#fff',\r\n                      fontSize: 10,\r\n                      fontFamily: 'Poppins_600SemiBold'\r\n                    }}>\r\n                      MOST POPULAR\r\n                    </Text>\r\n                  </View>\r\n                )}\r\n\r\n                {plan.discount && (\r\n                  <View style={{\r\n                    position: 'absolute',\r\n                    top: -8,\r\n                    left: 16,\r\n                    backgroundColor: '#4CAF50',\r\n                    paddingHorizontal: 12,\r\n                    paddingVertical: 4,\r\n                    borderRadius: 12,\r\n                    zIndex: 1\r\n                  }}>\r\n                    <Text style={{\r\n                      color: '#fff',\r\n                      fontSize: 10,\r\n                      fontFamily: 'Poppins_600SemiBold'\r\n                    }}>\r\n                      {plan.discount}\r\n                    </Text>\r\n                  </View>\r\n                )}\r\n\r\n                <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>\r\n                  <View style={{ flex: 1 }}>\r\n                    <Text style={{\r\n                      color: plan.color,\r\n                      fontSize: 20,\r\n                      fontFamily: 'Poppins_700Bold'\r\n                    }}>\r\n                      {plan.name}\r\n                    </Text>\r\n                    <Text style={{\r\n                      color: '#8a8a8a',\r\n                      fontSize: 12,\r\n                      fontFamily: 'Poppins_400Regular'\r\n                    }}>\r\n                      {plan.description}\r\n                    </Text>\r\n                  </View>\r\n                  <View style={{ alignItems: 'flex-end' }}>\r\n                    <View style={{ flexDirection: 'row', alignItems: 'baseline' }}>\r\n                      <Text style={{\r\n                        color: '#fff',\r\n                        fontSize: 24,\r\n                        fontFamily: 'Poppins_700Bold'\r\n                      }}>\r\n                        {plan.price}\r\n                      </Text>\r\n                      <Text style={{\r\n                        color: '#8a8a8a',\r\n                        fontSize: 12,\r\n                        fontFamily: 'Poppins_400Regular',\r\n                        marginLeft: 4\r\n                      }}>\r\n                        /{plan.period}\r\n                      </Text>\r\n                    </View>\r\n                    {plan.priceUSDT && (\r\n                      <Text style={{\r\n                        color: '#4CAF50',\r\n                        fontSize: 12,\r\n                        fontFamily: 'Poppins_500Medium',\r\n                        marginTop: 2\r\n                      }}>\r\n                        {plan.priceUSDT}\r\n                      </Text>\r\n                    )}\r\n                  </View>\r\n                </View>\r\n\r\n                <View style={{ marginBottom: 16 }}>\r\n                  {plan.features.map((feature, index) => (\r\n                    <View key={index} style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 4 }}>\r\n                      <Text style={{ color: '#4CAF50', marginRight: 8 }}>✓</Text>\r\n                      <Text style={{\r\n                        color: '#ccc',\r\n                        fontSize: 14,\r\n                        fontFamily: 'Poppins_400Regular'\r\n                      }}>\r\n                        {feature}\r\n                      </Text>\r\n                    </View>\r\n                  ))}\r\n                </View>\r\n\r\n                {plan.id !== 'free' && (\r\n                  <Button\r\n                    mode={selectedPlan === plan.id ? \"contained\" : \"outlined\"}\r\n                    onPress={() => handleSubscribe(plan.id)}\r\n                    style={{\r\n                      backgroundColor: selectedPlan === plan.id ? plan.color : 'transparent',\r\n                      borderColor: plan.color\r\n                    }}\r\n                    labelStyle={{\r\n                      color: selectedPlan === plan.id ? '#000' : plan.color,\r\n                      fontFamily: 'Poppins_600SemiBold'\r\n                    }}\r\n                  >\r\n                    {selectedPlan === plan.id ? 'Selected Plan' : `Choose ${plan.name}`}\r\n                  </Button>\r\n                )}\r\n              </Card>\r\n            </TouchableOpacity>\r\n          ))}\r\n        </View>\r\n\r\n        {/* Feature Comparison */}\r\n        <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>\r\n          <Text style={{\r\n            color: '#fff',\r\n            fontSize: 20,\r\n            fontFamily: 'Poppins_700Bold',\r\n            marginBottom: 16,\r\n            textAlign: 'center'\r\n          }}>\r\n            Feature Comparison\r\n          </Text>\r\n\r\n          <Card>\r\n            <View style={{ flexDirection: 'row', marginBottom: 12 }}>\r\n              <View style={{ flex: 2 }}>\r\n                <Text style={{ color: '#8a8a8a', fontSize: 12, fontFamily: 'Poppins_600SemiBold' }}>FEATURES</Text>\r\n              </View>\r\n              <View style={{ flex: 1, alignItems: 'center' }}>\r\n                <Text style={{ color: '#4CAF50', fontSize: 12, fontFamily: 'Poppins_600SemiBold' }}>FREE</Text>\r\n              </View>\r\n              <View style={{ flex: 1, alignItems: 'center' }}>\r\n                <Text style={{ color: '#FECB37', fontSize: 12, fontFamily: 'Poppins_600SemiBold' }}>PRO</Text>\r\n              </View>\r\n              <View style={{ flex: 1, alignItems: 'center' }}>\r\n                <Text style={{ color: '#9C27B0', fontSize: 12, fontFamily: 'Poppins_600SemiBold' }}>ELITE</Text>\r\n              </View>\r\n            </View>\r\n\r\n            {premiumFeatures.map((feature, index) => (\r\n              <View key={index} style={{\r\n                flexDirection: 'row',\r\n                alignItems: 'center',\r\n                paddingVertical: 8,\r\n                borderTopWidth: index > 0 ? 1 : 0,\r\n                borderTopColor: '#333'\r\n              }}>\r\n                <View style={{ flex: 2, flexDirection: 'row', alignItems: 'center' }}>\r\n                  <Text style={{ fontSize: 16, marginRight: 8 }}>{feature.icon}</Text>\r\n                  <View>\r\n                    <Text style={{ color: '#fff', fontSize: 14, fontFamily: 'Poppins_500Medium' }}>\r\n                      {feature.title}\r\n                    </Text>\r\n                    <Text style={{ color: '#8a8a8a', fontSize: 11, fontFamily: 'Poppins_400Regular' }}>\r\n                      {feature.description}\r\n                    </Text>\r\n                  </View>\r\n                </View>\r\n                <View style={{ flex: 1, alignItems: 'center' }}>\r\n                  <Text style={{\r\n                    color: typeof feature.free === 'boolean' ? (feature.free ? '#4CAF50' : '#F44336') : '#8a8a8a',\r\n                    fontSize: 12,\r\n                    fontFamily: 'Poppins_500Medium'\r\n                  }}>\r\n                    {typeof feature.free === 'boolean' ? (feature.free ? '✓' : '✗') : feature.free}\r\n                  </Text>\r\n                </View>\r\n                <View style={{ flex: 1, alignItems: 'center' }}>\r\n                  <Text style={{\r\n                    color: typeof feature.pro === 'boolean' ? (feature.pro ? '#4CAF50' : '#F44336') : '#8a8a8a',\r\n                    fontSize: 12,\r\n                    fontFamily: 'Poppins_500Medium'\r\n                  }}>\r\n                    {typeof feature.pro === 'boolean' ? (feature.pro ? '✓' : '✗') : feature.pro}\r\n                  </Text>\r\n                </View>\r\n                <View style={{ flex: 1, alignItems: 'center' }}>\r\n                  <Text style={{\r\n                    color: typeof feature.elite === 'boolean' ? (feature.elite ? '#4CAF50' : '#F44336') : '#8a8a8a',\r\n                    fontSize: 12,\r\n                    fontFamily: 'Poppins_500Medium'\r\n                  }}>\r\n                    {typeof feature.elite === 'boolean' ? (feature.elite ? '✓' : '✗') : feature.elite}\r\n                  </Text>\r\n                </View>\r\n              </View>\r\n            ))}\r\n          </Card>\r\n        </View>\r\n\r\n        {/* Testimonials */}\r\n        <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>\r\n          <Text style={{\r\n            color: '#fff',\r\n            fontSize: 20,\r\n            fontFamily: 'Poppins_700Bold',\r\n            marginBottom: 16,\r\n            textAlign: 'center'\r\n          }}>\r\n            What Our Users Say\r\n          </Text>\r\n\r\n          <ScrollView horizontal showsHorizontalScrollIndicator={false}>\r\n            {testimonials.map((testimonial, index) => (\r\n              <Card key={index} style={{ marginRight: 12, width: 280 }}>\r\n                <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>\r\n                  <View style={{\r\n                    backgroundColor: '#333',\r\n                    borderRadius: 20,\r\n                    width: 40,\r\n                    height: 40,\r\n                    alignItems: 'center',\r\n                    justifyContent: 'center',\r\n                    marginRight: 12\r\n                  }}>\r\n                    <Text style={{ fontSize: 20 }}>{testimonial.avatar}</Text>\r\n                  </View>\r\n                  <View style={{ flex: 1 }}>\r\n                    <Text style={{\r\n                      color: '#fff',\r\n                      fontSize: 14,\r\n                      fontFamily: 'Poppins_600SemiBold'\r\n                    }}>\r\n                      {testimonial.name}\r\n                    </Text>\r\n                    <Text style={{\r\n                      color: '#8a8a8a',\r\n                      fontSize: 12,\r\n                      fontFamily: 'Poppins_400Regular'\r\n                    }}>\r\n                      {testimonial.role}\r\n                    </Text>\r\n                  </View>\r\n                  <View style={{ flexDirection: 'row' }}>\r\n                    {[...Array(testimonial.rating)].map((_, i) => (\r\n                      <Text key={i} style={{ color: '#FECB37', fontSize: 12 }}>⭐</Text>\r\n                    ))}\r\n                  </View>\r\n                </View>\r\n                <Text style={{\r\n                  color: '#ccc',\r\n                  fontSize: 13,\r\n                  fontFamily: 'Poppins_400Regular',\r\n                  lineHeight: 18,\r\n                  fontStyle: 'italic'\r\n                }}>\r\n                  \"{testimonial.text}\"\r\n                </Text>\r\n              </Card>\r\n            ))}\r\n          </ScrollView>\r\n        </View>\r\n\r\n        {/* Call to Action */}\r\n        <View style={{ paddingHorizontal: 16, marginBottom: 20 }}>\r\n          <Card style={{ backgroundColor: '#FECB37', padding: 20, alignItems: 'center' }}>\r\n            <Text style={{\r\n              color: '#000',\r\n              fontSize: 20,\r\n              fontFamily: 'Poppins_700Bold',\r\n              textAlign: 'center',\r\n              marginBottom: 8\r\n            }}>\r\n              Ready to Start Earning?\r\n            </Text>\r\n            <Text style={{\r\n              color: '#000',\r\n              fontSize: 14,\r\n              fontFamily: 'Poppins_400Regular',\r\n              textAlign: 'center',\r\n              marginBottom: 16,\r\n              opacity: 0.8\r\n            }}>\r\n              Join thousands of successful traders today\r\n            </Text>\r\n            <Button\r\n              mode=\"contained\"\r\n              onPress={() => handleSubscribe(selectedPlan)}\r\n              style={{\r\n                backgroundColor: '#000',\r\n                paddingHorizontal: 20\r\n              }}\r\n              labelStyle={{\r\n                color: '#FECB37',\r\n                fontFamily: 'Poppins_600SemiBold',\r\n                fontSize: 16\r\n              }}\r\n            >\r\n              Start Premium Trial\r\n            </Button>\r\n            <Text style={{\r\n              color: '#000',\r\n              fontSize: 11,\r\n              fontFamily: 'Poppins_400Regular',\r\n              textAlign: 'center',\r\n              marginTop: 8,\r\n              opacity: 0.7\r\n            }}>\r\n              Cancel anytime • No hidden fees\r\n            </Text>\r\n          </Card>\r\n        </View>\r\n      </ScrollView>\r\n\r\n      {/* Modal de Pagamento USDT */}\r\n      <Modal\r\n        visible={showPaymentModal}\r\n        animationType=\"slide\"\r\n        presentationStyle=\"pageSheet\"\r\n        onRequestClose={handlePaymentCancel}\r\n      >\r\n        <USDTPayment\r\n          planId={paymentPlan}\r\n          onPaymentSuccess={handlePaymentSuccess}\r\n          onPaymentCancel={handlePaymentCancel}\r\n          visible={showPaymentModal}\r\n        />\r\n      </Modal>\r\n    </Wrapper>\r\n  );\r\n}"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,KAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,gBAAA;AAAA,OAAAC,KAAA;AAE/D,SAASC,MAAM,EAAEC,IAAI,EAAEC,WAAW,QAAQ,oBAAoB;AAC9D,OAAOC,OAAO;AACd,OAAOC,IAAI;AACX,OAAOC,QAAQ;AACf,OAAOC,WAAW;AAClB,SAASC,YAAY;AACrB,SAASC,cAAc;AAA2B,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAElD,eAAe,SAASC,OAAOA,CAAAC,IAAA,EAAiB;EAAA,IAAAC,UAAA,GAAAD,IAAA,CAAdE,KAAK;IAALA,KAAK,GAAAD,UAAA,cAAG,CAAC,CAAC,GAAAA,UAAA;EAC1C,IAAAE,WAAA,GAA0BzB,UAAU,CAACe,YAAY,CAAC;IAAAW,YAAA,GAAAC,cAAA,CAAAF,WAAA;IAA3CG,KAAK,GAAAF,YAAA;IAAEG,QAAQ,GAAAH,YAAA;EACtB,IAAAI,SAAA,GAAwC5B,QAAQ,CAAC,KAAK,CAAC;IAAA6B,UAAA,GAAAJ,cAAA,CAAAG,SAAA;IAAhDE,YAAY,GAAAD,UAAA;IAAEE,eAAe,GAAAF,UAAA;EACpC,IAAAG,UAAA,GAAgDhC,QAAQ,CAAC,KAAK,CAAC;IAAAiC,UAAA,GAAAR,cAAA,CAAAO,UAAA;IAAxDE,gBAAgB,GAAAD,UAAA;IAAEE,mBAAmB,GAAAF,UAAA;EAC5C,IAAAG,UAAA,GAAsCpC,QAAQ,CAAC,IAAI,CAAC;IAAAqC,UAAA,GAAAZ,cAAA,CAAAW,UAAA;IAA7CE,WAAW,GAAAD,UAAA;IAAEE,cAAc,GAAAF,UAAA;EAElC,IAAAG,KAAA,GAAiDd,KAAK,IAAI;MAAEe,YAAY,EAAE;QAAEC,kBAAkB,EAAE;MAAM;IAAE,CAAC;IAAjFA,kBAAkB,GAAAF,KAAA,CAAlCC,YAAY,CAAIC,kBAAkB;EAC1C,IAAAC,KAAA,GAA8BrB,KAAK,CAACsB,MAAM,IAAI;MAAEC,mBAAmB,EAAE;IAAM,CAAC;IAAtEA,mBAAmB,GAAAF,KAAA,CAAnBE,mBAAmB;EAGzB,IAAMC,eAAe,GAAG,CACtB;IACEC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,kBAAkB;IACzBC,WAAW,EAAE,0CAA0C;IACvDC,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE;EACT,CAAC,EACD;IACEL,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE,2CAA2C;IACxDC,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE;EACT,CAAC,EACD;IACEL,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,wBAAwB;IAC/BC,WAAW,EAAE,uCAAuC;IACpDC,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE;EACT,CAAC,EACD;IACEL,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,sBAAsB;IAC7BC,WAAW,EAAE,8BAA8B;IAC3CC,IAAI,EAAE,IAAI;IACVC,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE;EACT,CAAC,EACD;IACEL,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,gCAAgC;IAC7CC,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE;EACT,CAAC,EACD;IACEL,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE,4BAA4B;IACzCC,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE;EACT,CAAC,EACD;IACEL,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE,qCAAqC;IAClDC,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE;EACT,CAAC,EACD;IACEL,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,mCAAmC;IAChDC,IAAI,EAAE,YAAY;IAClBC,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC,CACF;EAGD,IAAMC,YAAY,GAAG,CACnB;IACEC,EAAE,EAAE,MAAM;IACVC,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE,IAAI;IACXC,MAAM,EAAE,SAAS;IACjBR,WAAW,EAAE,uBAAuB;IACpCS,QAAQ,EAAE,CAAC,mBAAmB,EAAE,qBAAqB,EAAE,mBAAmB,CAAC;IAC3EC,KAAK,EAAE,SAAS;IAChBC,OAAO,EAAE;EACX,CAAC,EACD;IACEN,EAAE,EAAE,KAAK;IACTC,IAAI,EAAE,KAAK;IACXC,KAAK,EAAE,IAAI1C,cAAc,CAAC+C,WAAW,CAACV,GAAG,EAAE;IAC3CW,SAAS,EAAE,GAAGhD,cAAc,CAAC+C,WAAW,CAACV,GAAG,OAAO;IACnDM,MAAM,EAAE,OAAO;IACfR,WAAW,EAAE,qBAAqB;IAClCS,QAAQ,EAAE,CAAC,oBAAoB,EAAE,kBAAkB,EAAE,oBAAoB,EAAE,wBAAwB,CAAC;IACpGC,KAAK,EAAE,SAAS;IAChBC,OAAO,EAAE,IAAI;IACbG,QAAQ,EAAE;EACZ,CAAC,EACD;IACET,EAAE,EAAE,OAAO;IACXC,IAAI,EAAE,OAAO;IACbC,KAAK,EAAE,IAAI1C,cAAc,CAAC+C,WAAW,CAACT,KAAK,EAAE;IAC7CU,SAAS,EAAE,GAAGhD,cAAc,CAAC+C,WAAW,CAACT,KAAK,OAAO;IACrDK,MAAM,EAAE,OAAO;IACfR,WAAW,EAAE,qBAAqB;IAClCS,QAAQ,EAAE,CAAC,oBAAoB,EAAE,cAAc,EAAE,iBAAiB,EAAE,kBAAkB,CAAC;IACvFC,KAAK,EAAE,SAAS;IAChBC,OAAO,EAAE;EACX,CAAC,CACF;EAGD,IAAMI,YAAY,GAAG,CACnB;IACET,IAAI,EAAE,YAAY;IAClBU,IAAI,EAAE,YAAY;IAClBC,MAAM,EAAE,OAAO;IACfC,MAAM,EAAE,CAAC;IACTC,IAAI,EAAE;EACR,CAAC,EACD;IACEb,IAAI,EAAE,gBAAgB;IACtBU,IAAI,EAAE,iBAAiB;IACvBC,MAAM,EAAE,OAAO;IACfC,MAAM,EAAE,CAAC;IACTC,IAAI,EAAE;EACR,CAAC,EACD;IACEb,IAAI,EAAE,eAAe;IACrBU,IAAI,EAAE,qBAAqB;IAC3BC,MAAM,EAAE,OAAO;IACfC,MAAM,EAAE,CAAC;IACTC,IAAI,EAAE;EACR,CAAC,CACF;EAEDrE,SAAS,CAAC,YAAM;IACd,IAAI8C,mBAAmB,EAAE;MACvB3C,KAAK,CAACmE,KAAK,CAAC,MAAM,EAAE,0CAA0C,CAAC;IACjE;EACF,CAAC,EAAE,CAACxB,mBAAmB,CAAC,CAAC;EAEzB,IAAMyB,eAAe,GAAG,SAAlBA,eAAeA,CAAIC,MAAM,EAAK;IAClC,IAAMC,IAAI,GAAGnB,YAAY,CAACoB,IAAI,CAAC,UAAAC,CAAC;MAAA,OAAIA,CAAC,CAACpB,EAAE,KAAKiB,MAAM;IAAA,EAAC;IACpD,IAAI,CAACC,IAAI,EAAE;IAEXtE,KAAK,CAACmE,KAAK,CACT,+BAA+B,EAC/B,SAASG,IAAI,CAACjB,IAAI,MAAMiB,IAAI,CAACV,SAAS,EAAE,EACxC,CACE;MACEM,IAAI,EAAE,UAAU;MAChBO,KAAK,EAAE;IACT,CAAC,EACD;MACEP,IAAI,EAAE,gBAAgB;MACtBQ,OAAO,EAAE,SAATA,OAAOA,CAAA,EAAQ;QACbrC,cAAc,CAACgC,MAAM,CAAC;QACtBpC,mBAAmB,CAAC,IAAI,CAAC;MAC3B;IACF,CAAC,EACD;MACEiC,IAAI,EAAE,kBAAkB;MACxBQ,OAAO,EAAE,SAATA,OAAOA,CAAA,EAAQ;QACb1E,KAAK,CAACmE,KAAK,CAAC,UAAU,EAAE,qDAAqD,CAAC;MAChF;IACF,CAAC,CAEL,CAAC;EACH,CAAC;EAED,IAAMQ,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAIC,cAAc,EAAK;IAC/C3C,mBAAmB,CAAC,KAAK,CAAC;IAC1BI,cAAc,CAAC,IAAI,CAAC;IAGpBrC,KAAK,CAACmE,KAAK,CACT,0BAA0B,EAC1B,4CAA4C,EAC5C,CACE;MACED,IAAI,EAAE,IAAI;MACVQ,OAAO,EAAE,SAATA,OAAOA,CAAA,EAAQ,CAGf;IACF,CAAC,CAEL,CAAC;EACH,CAAC;EAED,IAAMG,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAA,EAAS;IAChC5C,mBAAmB,CAAC,KAAK,CAAC;IAC1BI,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,IAAMyC,wBAAwB,GAAG,SAA3BA,wBAAwBA,CAAA,EAAS;IACrC9E,KAAK,CAACmE,KAAK,CAAC,qBAAqB,EAAE,sCAAsC,CAAC;EAC5E,CAAC;EAED,IAAI3B,kBAAkB,EAAE;IACtB,OACE1B,IAAA,CAACP,OAAO;MAAAwE,QAAA,EACN/D,KAAA,CAACf,UAAU;QAACwE,KAAK,EAAE;UAAEO,IAAI,EAAE;QAAE,CAAE;QAAAD,QAAA,GAE7B/D,KAAA,CAACjB,IAAI;UAAC0E,KAAK,EAAE;YAAEQ,OAAO,EAAE,EAAE;YAAEC,UAAU,EAAE;UAAS,CAAE;UAAAH,QAAA,GACjDjE,IAAA,CAACT,IAAI;YAACoE,KAAK,EAAE;cAAEU,QAAQ,EAAE,EAAE;cAAEC,YAAY,EAAE;YAAG,CAAE;YAAAL,QAAA,EAAC;UAAE,CAAM,CAAC,EAC1DjE,IAAA,CAACT,IAAI;YAACoE,KAAK,EAAE;cACXhB,KAAK,EAAE,SAAS;cAChB0B,QAAQ,EAAE,EAAE;cACZE,UAAU,EAAE,iBAAiB;cAC7BC,SAAS,EAAE,QAAQ;cACnBF,YAAY,EAAE;YAChB,CAAE;YAAAL,QAAA,EAAC;UAEH,CAAM,CAAC,EACPjE,IAAA,CAACT,IAAI;YAACoE,KAAK,EAAE;cACXhB,KAAK,EAAE,SAAS;cAChB0B,QAAQ,EAAE,EAAE;cACZE,UAAU,EAAE,oBAAoB;cAChCC,SAAS,EAAE;YACb,CAAE;YAAAP,QAAA,EAAC;UAEH,CAAM,CAAC;QAAA,CACH,CAAC,EAGP/D,KAAA,CAACjB,IAAI;UAAC0E,KAAK,EAAE;YAAEc,iBAAiB,EAAE,CAAC;YAAEH,YAAY,EAAE;UAAG,CAAE;UAAAL,QAAA,GACtDjE,IAAA,CAACT,IAAI;YAACoE,KAAK,EAAE;cACXhB,KAAK,EAAE,MAAM;cACb0B,QAAQ,EAAE,EAAE;cACZE,UAAU,EAAE,qBAAqB;cACjCD,YAAY,EAAE,EAAE;cAChBG,iBAAiB,EAAE;YACrB,CAAE;YAAAR,QAAA,EAAC;UAEH,CAAM,CAAC,EACP/D,KAAA,CAACjB,IAAI;YAAC0E,KAAK,EAAE;cAAEe,aAAa,EAAE,KAAK;cAAEC,QAAQ,EAAE;YAAO,CAAE;YAAAV,QAAA,GACtDjE,IAAA,CAACL,QAAQ;cACPqC,KAAK,EAAC,kBAAkB;cACxB4C,KAAK,EAAC,IAAI;cACVC,QAAQ,EAAC,UAAU;cACnB9C,IAAI,EAAC;YAAI,CACV,CAAC,EACF/B,IAAA,CAACL,QAAQ;cACPqC,KAAK,EAAC,kBAAkB;cACxB4C,KAAK,EAAC,KAAK;cACXC,QAAQ,EAAC,UAAU;cACnB9C,IAAI,EAAC;YAAI,CACV,CAAC,EACF/B,IAAA,CAACL,QAAQ;cACPqC,KAAK,EAAC,cAAc;cACpB4C,KAAK,EAAC,OAAO;cACbE,MAAM,EAAC,OAAO;cACdC,UAAU,EAAC,UAAU;cACrBhD,IAAI,EAAC;YAAI,CACV,CAAC,EACF/B,IAAA,CAACL,QAAQ;cACPqC,KAAK,EAAC,SAAS;cACf4C,KAAK,EAAC,QAAQ;cACdC,QAAQ,EAAC,YAAY;cACrB9C,IAAI,EAAC;YAAI,CACV,CAAC;UAAA,CACE,CAAC;QAAA,CACH,CAAC,EAGP7B,KAAA,CAACjB,IAAI;UAAC0E,KAAK,EAAE;YAAEc,iBAAiB,EAAE,EAAE;YAAEH,YAAY,EAAE;UAAG,CAAE;UAAAL,QAAA,GACvDjE,IAAA,CAACT,IAAI;YAACoE,KAAK,EAAE;cACXhB,KAAK,EAAE,MAAM;cACb0B,QAAQ,EAAE,EAAE;cACZE,UAAU,EAAE,qBAAqB;cACjCD,YAAY,EAAE;YAChB,CAAE;YAAAL,QAAA,EAAC;UAEH,CAAM,CAAC,EAEP/D,KAAA,CAACR,IAAI;YAAAuE,QAAA,GACH/D,KAAA,CAACjB,IAAI;cAAC0E,KAAK,EAAE;gBAAEe,aAAa,EAAE,KAAK;gBAAEM,cAAc,EAAE,eAAe;gBAAEZ,UAAU,EAAE,QAAQ;gBAAEE,YAAY,EAAE;cAAG,CAAE;cAAAL,QAAA,GAC7G/D,KAAA,CAACjB,IAAI;gBAAAgF,QAAA,GACHjE,IAAA,CAACT,IAAI;kBAACoE,KAAK,EAAE;oBAAEhB,KAAK,EAAE,MAAM;oBAAE0B,QAAQ,EAAE,EAAE;oBAAEE,UAAU,EAAE;kBAAsB,CAAE;kBAAAN,QAAA,EAAC;gBAAQ,CAAM,CAAC,EAChGjE,IAAA,CAACT,IAAI;kBAACoE,KAAK,EAAE;oBAAEhB,KAAK,EAAE,SAAS;oBAAE0B,QAAQ,EAAE,EAAE;oBAAEE,UAAU,EAAE;kBAAqB,CAAE;kBAAAN,QAAA,EAAC;gBAA+B,CAAM,CAAC;cAAA,CACrH,CAAC,EACPjE,IAAA,CAACf,IAAI;gBAAC0E,KAAK,EAAE;kBACXsB,eAAe,EAAE,SAAS;kBAC1BR,iBAAiB,EAAE,CAAC;kBACpBS,eAAe,EAAE,CAAC;kBAClBC,YAAY,EAAE;gBAChB,CAAE;gBAAAlB,QAAA,EACAjE,IAAA,CAACT,IAAI;kBAACoE,KAAK,EAAE;oBAAEhB,KAAK,EAAE,MAAM;oBAAE0B,QAAQ,EAAE,EAAE;oBAAEE,UAAU,EAAE;kBAAsB,CAAE;kBAAAN,QAAA,EAAC;gBAAM,CAAM;cAAC,CAC1F,CAAC;YAAA,CACH,CAAC,EAEPjE,IAAA,CAACV,MAAM;cACL8F,IAAI,EAAC,UAAU;cACfxB,OAAO,EAAEI,wBAAyB;cAClCL,KAAK,EAAE;gBAAE0B,WAAW,EAAE;cAAU,CAAE;cAClCC,UAAU,EAAE;gBAAE3C,KAAK,EAAE,SAAS;gBAAE4B,UAAU,EAAE;cAAoB,CAAE;cAAAN,QAAA,EACnE;YAED,CAAQ,CAAC;UAAA,CACL,CAAC;QAAA,CACH,CAAC;MAAA,CACG;IAAC,CACN,CAAC;EAEd;EAEA,OACE/D,KAAA,CAACT,OAAO;IAAAwE,QAAA,GACN/D,KAAA,CAACf,UAAU;MAACwE,KAAK,EAAE;QAAEO,IAAI,EAAE;MAAE,CAAE;MAAAD,QAAA,GAE7B/D,KAAA,CAACjB,IAAI;QAAC0E,KAAK,EAAE;UAAEQ,OAAO,EAAE,EAAE;UAAEC,UAAU,EAAE,QAAQ;UAAEa,eAAe,EAAE;QAA4C,CAAE;QAAAhB,QAAA,GAC/GjE,IAAA,CAACT,IAAI;UAACoE,KAAK,EAAE;YAAEU,QAAQ,EAAE,EAAE;YAAEC,YAAY,EAAE;UAAG,CAAE;UAAAL,QAAA,EAAC;QAAE,CAAM,CAAC,EAC1DjE,IAAA,CAACT,IAAI;UAACoE,KAAK,EAAE;YACXhB,KAAK,EAAE,MAAM;YACb0B,QAAQ,EAAE,EAAE;YACZE,UAAU,EAAE,iBAAiB;YAC7BC,SAAS,EAAE,QAAQ;YACnBF,YAAY,EAAE;UAChB,CAAE;UAAAL,QAAA,EAAC;QAEH,CAAM,CAAC,EACPjE,IAAA,CAACT,IAAI;UAACoE,KAAK,EAAE;YACXhB,KAAK,EAAE,MAAM;YACb0B,QAAQ,EAAE,EAAE;YACZE,UAAU,EAAE,oBAAoB;YAChCC,SAAS,EAAE,QAAQ;YACnBF,YAAY,EAAE,EAAE;YAChBiB,OAAO,EAAE;UACX,CAAE;UAAAtB,QAAA,EAAC;QAEH,CAAM,CAAC,EAGPjE,IAAA,CAACf,IAAI;UAAC0E,KAAK,EAAE;YACXsB,eAAe,EAAE,SAAS;YAC1BR,iBAAiB,EAAE,EAAE;YACrBS,eAAe,EAAE,CAAC;YAClBC,YAAY,EAAE,EAAE;YAChBb,YAAY,EAAE;UAChB,CAAE;UAAAL,QAAA,EACAjE,IAAA,CAACT,IAAI;YAACoE,KAAK,EAAE;cACXhB,KAAK,EAAE,MAAM;cACb0B,QAAQ,EAAE,EAAE;cACZE,UAAU,EAAE,qBAAqB;cACjCC,SAAS,EAAE;YACb,CAAE;YAAAP,QAAA,EAAC;UAEH,CAAM;QAAC,CACH,CAAC;MAAA,CACH,CAAC,EAGP/D,KAAA,CAACjB,IAAI;QAAC0E,KAAK,EAAE;UAAEc,iBAAiB,EAAE,CAAC;UAAEe,SAAS,EAAE,EAAE;UAAElB,YAAY,EAAE;QAAG,CAAE;QAAAL,QAAA,GACrEjE,IAAA,CAACT,IAAI;UAACoE,KAAK,EAAE;YACXhB,KAAK,EAAE,MAAM;YACb0B,QAAQ,EAAE,EAAE;YACZE,UAAU,EAAE,qBAAqB;YACjCD,YAAY,EAAE,EAAE;YAChBG,iBAAiB,EAAE,CAAC;YACpBD,SAAS,EAAE;UACb,CAAE;UAAAP,QAAA,EAAC;QAEH,CAAM,CAAC,EACP/D,KAAA,CAACjB,IAAI;UAAC0E,KAAK,EAAE;YAAEe,aAAa,EAAE,KAAK;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAAV,QAAA,GACtDjE,IAAA,CAACL,QAAQ;YACPqC,KAAK,EAAC,aAAa;YACnB4C,KAAK,EAAC,MAAM;YACZC,QAAQ,EAAC,UAAU;YACnB9C,IAAI,EAAC;UAAI,CACV,CAAC,EACF/B,IAAA,CAACL,QAAQ;YACPqC,KAAK,EAAC,cAAc;YACpB4C,KAAK,EAAC,OAAO;YACbC,QAAQ,EAAC,iBAAiB;YAC1B9C,IAAI,EAAC;UAAI,CACV,CAAC,EACF/B,IAAA,CAACL,QAAQ;YACPqC,KAAK,EAAC,cAAc;YACpB4C,KAAK,EAAC,MAAM;YACZC,QAAQ,EAAC,WAAW;YACpB9C,IAAI,EAAC;UAAI,CACV,CAAC,EACF/B,IAAA,CAACL,QAAQ;YACPqC,KAAK,EAAC,eAAe;YACrB4C,KAAK,EAAC,MAAM;YACZC,QAAQ,EAAC,iBAAiB;YAC1B9C,IAAI,EAAC;UAAI,CACV,CAAC;QAAA,CACE,CAAC;MAAA,CACH,CAAC,EAGP7B,KAAA,CAACjB,IAAI;QAAC0E,KAAK,EAAE;UAAEc,iBAAiB,EAAE,EAAE;UAAEH,YAAY,EAAE;QAAG,CAAE;QAAAL,QAAA,GACvDjE,IAAA,CAACT,IAAI;UAACoE,KAAK,EAAE;YACXhB,KAAK,EAAE,MAAM;YACb0B,QAAQ,EAAE,EAAE;YACZE,UAAU,EAAE,iBAAiB;YAC7BD,YAAY,EAAE,EAAE;YAChBE,SAAS,EAAE;UACb,CAAE;UAAAP,QAAA,EAAC;QAEH,CAAM,CAAC,EAEN5B,YAAY,CAACoD,GAAG,CAAC,UAACjC,IAAI;UAAA,OACrBxD,IAAA,CAACZ,gBAAgB;YAEfwE,OAAO,EAAE,SAATA,OAAOA,CAAA;cAAA,OAAQ7C,eAAe,CAACyC,IAAI,CAAClB,EAAE,CAAC;YAAA,CAAC;YACxCqB,KAAK,EAAE;cAAEW,YAAY,EAAE;YAAG,CAAE;YAAAL,QAAA,EAE5B/D,KAAA,CAACR,IAAI;cAACiE,KAAK,EAAE;gBACX0B,WAAW,EAAEvE,YAAY,KAAK0C,IAAI,CAAClB,EAAE,GAAGkB,IAAI,CAACb,KAAK,GAAG,aAAa;gBAClE+C,WAAW,EAAE5E,YAAY,KAAK0C,IAAI,CAAClB,EAAE,GAAG,CAAC,GAAG,CAAC;gBAC7CqD,QAAQ,EAAE;cACZ,CAAE;cAAA1B,QAAA,GACCT,IAAI,CAACZ,OAAO,IACX5C,IAAA,CAACf,IAAI;gBAAC0E,KAAK,EAAE;kBACXgC,QAAQ,EAAE,UAAU;kBACpBC,GAAG,EAAE,CAAC,CAAC;kBACPC,KAAK,EAAE,EAAE;kBACTZ,eAAe,EAAE,SAAS;kBAC1BR,iBAAiB,EAAE,EAAE;kBACrBS,eAAe,EAAE,CAAC;kBAClBC,YAAY,EAAE,EAAE;kBAChBW,MAAM,EAAE;gBACV,CAAE;gBAAA7B,QAAA,EACAjE,IAAA,CAACT,IAAI;kBAACoE,KAAK,EAAE;oBACXhB,KAAK,EAAE,MAAM;oBACb0B,QAAQ,EAAE,EAAE;oBACZE,UAAU,EAAE;kBACd,CAAE;kBAAAN,QAAA,EAAC;gBAEH,CAAM;cAAC,CACH,CACP,EAEAT,IAAI,CAACT,QAAQ,IACZ/C,IAAA,CAACf,IAAI;gBAAC0E,KAAK,EAAE;kBACXgC,QAAQ,EAAE,UAAU;kBACpBC,GAAG,EAAE,CAAC,CAAC;kBACPG,IAAI,EAAE,EAAE;kBACRd,eAAe,EAAE,SAAS;kBAC1BR,iBAAiB,EAAE,EAAE;kBACrBS,eAAe,EAAE,CAAC;kBAClBC,YAAY,EAAE,EAAE;kBAChBW,MAAM,EAAE;gBACV,CAAE;gBAAA7B,QAAA,EACAjE,IAAA,CAACT,IAAI;kBAACoE,KAAK,EAAE;oBACXhB,KAAK,EAAE,MAAM;oBACb0B,QAAQ,EAAE,EAAE;oBACZE,UAAU,EAAE;kBACd,CAAE;kBAAAN,QAAA,EACCT,IAAI,CAACT;gBAAQ,CACV;cAAC,CACH,CACP,EAED7C,KAAA,CAACjB,IAAI;gBAAC0E,KAAK,EAAE;kBAAEe,aAAa,EAAE,KAAK;kBAAEN,UAAU,EAAE,QAAQ;kBAAEE,YAAY,EAAE;gBAAG,CAAE;gBAAAL,QAAA,GAC5E/D,KAAA,CAACjB,IAAI;kBAAC0E,KAAK,EAAE;oBAAEO,IAAI,EAAE;kBAAE,CAAE;kBAAAD,QAAA,GACvBjE,IAAA,CAACT,IAAI;oBAACoE,KAAK,EAAE;sBACXhB,KAAK,EAAEa,IAAI,CAACb,KAAK;sBACjB0B,QAAQ,EAAE,EAAE;sBACZE,UAAU,EAAE;oBACd,CAAE;oBAAAN,QAAA,EACCT,IAAI,CAACjB;kBAAI,CACN,CAAC,EACPvC,IAAA,CAACT,IAAI;oBAACoE,KAAK,EAAE;sBACXhB,KAAK,EAAE,SAAS;sBAChB0B,QAAQ,EAAE,EAAE;sBACZE,UAAU,EAAE;oBACd,CAAE;oBAAAN,QAAA,EACCT,IAAI,CAACvB;kBAAW,CACb,CAAC;gBAAA,CACH,CAAC,EACP/B,KAAA,CAACjB,IAAI;kBAAC0E,KAAK,EAAE;oBAAES,UAAU,EAAE;kBAAW,CAAE;kBAAAH,QAAA,GACtC/D,KAAA,CAACjB,IAAI;oBAAC0E,KAAK,EAAE;sBAAEe,aAAa,EAAE,KAAK;sBAAEN,UAAU,EAAE;oBAAW,CAAE;oBAAAH,QAAA,GAC5DjE,IAAA,CAACT,IAAI;sBAACoE,KAAK,EAAE;wBACXhB,KAAK,EAAE,MAAM;wBACb0B,QAAQ,EAAE,EAAE;wBACZE,UAAU,EAAE;sBACd,CAAE;sBAAAN,QAAA,EACCT,IAAI,CAAChB;oBAAK,CACP,CAAC,EACPtC,KAAA,CAACX,IAAI;sBAACoE,KAAK,EAAE;wBACXhB,KAAK,EAAE,SAAS;wBAChB0B,QAAQ,EAAE,EAAE;wBACZE,UAAU,EAAE,oBAAoB;wBAChCyB,UAAU,EAAE;sBACd,CAAE;sBAAA/B,QAAA,GAAC,GACA,EAACT,IAAI,CAACf,MAAM;oBAAA,CACT,CAAC;kBAAA,CACH,CAAC,EACNe,IAAI,CAACV,SAAS,IACb9C,IAAA,CAACT,IAAI;oBAACoE,KAAK,EAAE;sBACXhB,KAAK,EAAE,SAAS;sBAChB0B,QAAQ,EAAE,EAAE;sBACZE,UAAU,EAAE,mBAAmB;sBAC/BiB,SAAS,EAAE;oBACb,CAAE;oBAAAvB,QAAA,EACCT,IAAI,CAACV;kBAAS,CACX,CACP;gBAAA,CACG,CAAC;cAAA,CACH,CAAC,EAEP9C,IAAA,CAACf,IAAI;gBAAC0E,KAAK,EAAE;kBAAEW,YAAY,EAAE;gBAAG,CAAE;gBAAAL,QAAA,EAC/BT,IAAI,CAACd,QAAQ,CAAC+C,GAAG,CAAC,UAACQ,OAAO,EAAEC,KAAK;kBAAA,OAChChG,KAAA,CAACjB,IAAI;oBAAa0E,KAAK,EAAE;sBAAEe,aAAa,EAAE,KAAK;sBAAEN,UAAU,EAAE,QAAQ;sBAAEE,YAAY,EAAE;oBAAE,CAAE;oBAAAL,QAAA,GACvFjE,IAAA,CAACT,IAAI;sBAACoE,KAAK,EAAE;wBAAEhB,KAAK,EAAE,SAAS;wBAAEwD,WAAW,EAAE;sBAAE,CAAE;sBAAAlC,QAAA,EAAC;oBAAC,CAAM,CAAC,EAC3DjE,IAAA,CAACT,IAAI;sBAACoE,KAAK,EAAE;wBACXhB,KAAK,EAAE,MAAM;wBACb0B,QAAQ,EAAE,EAAE;wBACZE,UAAU,EAAE;sBACd,CAAE;sBAAAN,QAAA,EACCgC;oBAAO,CACJ,CAAC;kBAAA,GAREC,KASL,CAAC;gBAAA,CACR;cAAC,CACE,CAAC,EAEN1C,IAAI,CAAClB,EAAE,KAAK,MAAM,IACjBtC,IAAA,CAACV,MAAM;gBACL8F,IAAI,EAAEtE,YAAY,KAAK0C,IAAI,CAAClB,EAAE,GAAG,WAAW,GAAG,UAAW;gBAC1DsB,OAAO,EAAE,SAATA,OAAOA,CAAA;kBAAA,OAAQN,eAAe,CAACE,IAAI,CAAClB,EAAE,CAAC;gBAAA,CAAC;gBACxCqB,KAAK,EAAE;kBACLsB,eAAe,EAAEnE,YAAY,KAAK0C,IAAI,CAAClB,EAAE,GAAGkB,IAAI,CAACb,KAAK,GAAG,aAAa;kBACtE0C,WAAW,EAAE7B,IAAI,CAACb;gBACpB,CAAE;gBACF2C,UAAU,EAAE;kBACV3C,KAAK,EAAE7B,YAAY,KAAK0C,IAAI,CAAClB,EAAE,GAAG,MAAM,GAAGkB,IAAI,CAACb,KAAK;kBACrD4B,UAAU,EAAE;gBACd,CAAE;gBAAAN,QAAA,EAEDnD,YAAY,KAAK0C,IAAI,CAAClB,EAAE,GAAG,eAAe,GAAG,UAAUkB,IAAI,CAACjB,IAAI;cAAE,CAC7D,CACT;YAAA,CACG;UAAC,GAlIFiB,IAAI,CAAClB,EAmIM,CAAC;QAAA,CACpB,CAAC;MAAA,CACE,CAAC,EAGPpC,KAAA,CAACjB,IAAI;QAAC0E,KAAK,EAAE;UAAEc,iBAAiB,EAAE,EAAE;UAAEH,YAAY,EAAE;QAAG,CAAE;QAAAL,QAAA,GACvDjE,IAAA,CAACT,IAAI;UAACoE,KAAK,EAAE;YACXhB,KAAK,EAAE,MAAM;YACb0B,QAAQ,EAAE,EAAE;YACZE,UAAU,EAAE,iBAAiB;YAC7BD,YAAY,EAAE,EAAE;YAChBE,SAAS,EAAE;UACb,CAAE;UAAAP,QAAA,EAAC;QAEH,CAAM,CAAC,EAEP/D,KAAA,CAACR,IAAI;UAAAuE,QAAA,GACH/D,KAAA,CAACjB,IAAI;YAAC0E,KAAK,EAAE;cAAEe,aAAa,EAAE,KAAK;cAAEJ,YAAY,EAAE;YAAG,CAAE;YAAAL,QAAA,GACtDjE,IAAA,CAACf,IAAI;cAAC0E,KAAK,EAAE;gBAAEO,IAAI,EAAE;cAAE,CAAE;cAAAD,QAAA,EACvBjE,IAAA,CAACT,IAAI;gBAACoE,KAAK,EAAE;kBAAEhB,KAAK,EAAE,SAAS;kBAAE0B,QAAQ,EAAE,EAAE;kBAAEE,UAAU,EAAE;gBAAsB,CAAE;gBAAAN,QAAA,EAAC;cAAQ,CAAM;YAAC,CAC/F,CAAC,EACPjE,IAAA,CAACf,IAAI;cAAC0E,KAAK,EAAE;gBAAEO,IAAI,EAAE,CAAC;gBAAEE,UAAU,EAAE;cAAS,CAAE;cAAAH,QAAA,EAC7CjE,IAAA,CAACT,IAAI;gBAACoE,KAAK,EAAE;kBAAEhB,KAAK,EAAE,SAAS;kBAAE0B,QAAQ,EAAE,EAAE;kBAAEE,UAAU,EAAE;gBAAsB,CAAE;gBAAAN,QAAA,EAAC;cAAI,CAAM;YAAC,CAC3F,CAAC,EACPjE,IAAA,CAACf,IAAI;cAAC0E,KAAK,EAAE;gBAAEO,IAAI,EAAE,CAAC;gBAAEE,UAAU,EAAE;cAAS,CAAE;cAAAH,QAAA,EAC7CjE,IAAA,CAACT,IAAI;gBAACoE,KAAK,EAAE;kBAAEhB,KAAK,EAAE,SAAS;kBAAE0B,QAAQ,EAAE,EAAE;kBAAEE,UAAU,EAAE;gBAAsB,CAAE;gBAAAN,QAAA,EAAC;cAAG,CAAM;YAAC,CAC1F,CAAC,EACPjE,IAAA,CAACf,IAAI;cAAC0E,KAAK,EAAE;gBAAEO,IAAI,EAAE,CAAC;gBAAEE,UAAU,EAAE;cAAS,CAAE;cAAAH,QAAA,EAC7CjE,IAAA,CAACT,IAAI;gBAACoE,KAAK,EAAE;kBAAEhB,KAAK,EAAE,SAAS;kBAAE0B,QAAQ,EAAE,EAAE;kBAAEE,UAAU,EAAE;gBAAsB,CAAE;gBAAAN,QAAA,EAAC;cAAK,CAAM;YAAC,CAC5F,CAAC;UAAA,CACH,CAAC,EAENnC,eAAe,CAAC2D,GAAG,CAAC,UAACQ,OAAO,EAAEC,KAAK;YAAA,OAClChG,KAAA,CAACjB,IAAI;cAAa0E,KAAK,EAAE;gBACvBe,aAAa,EAAE,KAAK;gBACpBN,UAAU,EAAE,QAAQ;gBACpBc,eAAe,EAAE,CAAC;gBAClBkB,cAAc,EAAEF,KAAK,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;gBACjCG,cAAc,EAAE;cAClB,CAAE;cAAApC,QAAA,GACA/D,KAAA,CAACjB,IAAI;gBAAC0E,KAAK,EAAE;kBAAEO,IAAI,EAAE,CAAC;kBAAEQ,aAAa,EAAE,KAAK;kBAAEN,UAAU,EAAE;gBAAS,CAAE;gBAAAH,QAAA,GACnEjE,IAAA,CAACT,IAAI;kBAACoE,KAAK,EAAE;oBAAEU,QAAQ,EAAE,EAAE;oBAAE8B,WAAW,EAAE;kBAAE,CAAE;kBAAAlC,QAAA,EAAEgC,OAAO,CAAClE;gBAAI,CAAO,CAAC,EACpE7B,KAAA,CAACjB,IAAI;kBAAAgF,QAAA,GACHjE,IAAA,CAACT,IAAI;oBAACoE,KAAK,EAAE;sBAAEhB,KAAK,EAAE,MAAM;sBAAE0B,QAAQ,EAAE,EAAE;sBAAEE,UAAU,EAAE;oBAAoB,CAAE;oBAAAN,QAAA,EAC3EgC,OAAO,CAACjE;kBAAK,CACV,CAAC,EACPhC,IAAA,CAACT,IAAI;oBAACoE,KAAK,EAAE;sBAAEhB,KAAK,EAAE,SAAS;sBAAE0B,QAAQ,EAAE,EAAE;sBAAEE,UAAU,EAAE;oBAAqB,CAAE;oBAAAN,QAAA,EAC/EgC,OAAO,CAAChE;kBAAW,CAChB,CAAC;gBAAA,CACH,CAAC;cAAA,CACH,CAAC,EACPjC,IAAA,CAACf,IAAI;gBAAC0E,KAAK,EAAE;kBAAEO,IAAI,EAAE,CAAC;kBAAEE,UAAU,EAAE;gBAAS,CAAE;gBAAAH,QAAA,EAC7CjE,IAAA,CAACT,IAAI;kBAACoE,KAAK,EAAE;oBACXhB,KAAK,EAAE,OAAOsD,OAAO,CAAC/D,IAAI,KAAK,SAAS,GAAI+D,OAAO,CAAC/D,IAAI,GAAG,SAAS,GAAG,SAAS,GAAI,SAAS;oBAC7FmC,QAAQ,EAAE,EAAE;oBACZE,UAAU,EAAE;kBACd,CAAE;kBAAAN,QAAA,EACC,OAAOgC,OAAO,CAAC/D,IAAI,KAAK,SAAS,GAAI+D,OAAO,CAAC/D,IAAI,GAAG,GAAG,GAAG,GAAG,GAAI+D,OAAO,CAAC/D;gBAAI,CAC1E;cAAC,CACH,CAAC,EACPlC,IAAA,CAACf,IAAI;gBAAC0E,KAAK,EAAE;kBAAEO,IAAI,EAAE,CAAC;kBAAEE,UAAU,EAAE;gBAAS,CAAE;gBAAAH,QAAA,EAC7CjE,IAAA,CAACT,IAAI;kBAACoE,KAAK,EAAE;oBACXhB,KAAK,EAAE,OAAOsD,OAAO,CAAC9D,GAAG,KAAK,SAAS,GAAI8D,OAAO,CAAC9D,GAAG,GAAG,SAAS,GAAG,SAAS,GAAI,SAAS;oBAC3FkC,QAAQ,EAAE,EAAE;oBACZE,UAAU,EAAE;kBACd,CAAE;kBAAAN,QAAA,EACC,OAAOgC,OAAO,CAAC9D,GAAG,KAAK,SAAS,GAAI8D,OAAO,CAAC9D,GAAG,GAAG,GAAG,GAAG,GAAG,GAAI8D,OAAO,CAAC9D;gBAAG,CACvE;cAAC,CACH,CAAC,EACPnC,IAAA,CAACf,IAAI;gBAAC0E,KAAK,EAAE;kBAAEO,IAAI,EAAE,CAAC;kBAAEE,UAAU,EAAE;gBAAS,CAAE;gBAAAH,QAAA,EAC7CjE,IAAA,CAACT,IAAI;kBAACoE,KAAK,EAAE;oBACXhB,KAAK,EAAE,OAAOsD,OAAO,CAAC7D,KAAK,KAAK,SAAS,GAAI6D,OAAO,CAAC7D,KAAK,GAAG,SAAS,GAAG,SAAS,GAAI,SAAS;oBAC/FiC,QAAQ,EAAE,EAAE;oBACZE,UAAU,EAAE;kBACd,CAAE;kBAAAN,QAAA,EACC,OAAOgC,OAAO,CAAC7D,KAAK,KAAK,SAAS,GAAI6D,OAAO,CAAC7D,KAAK,GAAG,GAAG,GAAG,GAAG,GAAI6D,OAAO,CAAC7D;gBAAK,CAC7E;cAAC,CACH,CAAC;YAAA,GA5CE8D,KA6CL,CAAC;UAAA,CACR,CAAC;QAAA,CACE,CAAC;MAAA,CACH,CAAC,EAGPhG,KAAA,CAACjB,IAAI;QAAC0E,KAAK,EAAE;UAAEc,iBAAiB,EAAE,EAAE;UAAEH,YAAY,EAAE;QAAG,CAAE;QAAAL,QAAA,GACvDjE,IAAA,CAACT,IAAI;UAACoE,KAAK,EAAE;YACXhB,KAAK,EAAE,MAAM;YACb0B,QAAQ,EAAE,EAAE;YACZE,UAAU,EAAE,iBAAiB;YAC7BD,YAAY,EAAE,EAAE;YAChBE,SAAS,EAAE;UACb,CAAE;UAAAP,QAAA,EAAC;QAEH,CAAM,CAAC,EAEPjE,IAAA,CAACb,UAAU;UAACmH,UAAU;UAACC,8BAA8B,EAAE,KAAM;UAAAtC,QAAA,EAC1DjB,YAAY,CAACyC,GAAG,CAAC,UAACe,WAAW,EAAEN,KAAK;YAAA,OACnChG,KAAA,CAACR,IAAI;cAAaiE,KAAK,EAAE;gBAAEwC,WAAW,EAAE,EAAE;gBAAEM,KAAK,EAAE;cAAI,CAAE;cAAAxC,QAAA,GACvD/D,KAAA,CAACjB,IAAI;gBAAC0E,KAAK,EAAE;kBAAEe,aAAa,EAAE,KAAK;kBAAEN,UAAU,EAAE,QAAQ;kBAAEE,YAAY,EAAE;gBAAG,CAAE;gBAAAL,QAAA,GAC5EjE,IAAA,CAACf,IAAI;kBAAC0E,KAAK,EAAE;oBACXsB,eAAe,EAAE,MAAM;oBACvBE,YAAY,EAAE,EAAE;oBAChBsB,KAAK,EAAE,EAAE;oBACTC,MAAM,EAAE,EAAE;oBACVtC,UAAU,EAAE,QAAQ;oBACpBY,cAAc,EAAE,QAAQ;oBACxBmB,WAAW,EAAE;kBACf,CAAE;kBAAAlC,QAAA,EACAjE,IAAA,CAACT,IAAI;oBAACoE,KAAK,EAAE;sBAAEU,QAAQ,EAAE;oBAAG,CAAE;oBAAAJ,QAAA,EAAEuC,WAAW,CAACtD;kBAAM,CAAO;gBAAC,CACtD,CAAC,EACPhD,KAAA,CAACjB,IAAI;kBAAC0E,KAAK,EAAE;oBAAEO,IAAI,EAAE;kBAAE,CAAE;kBAAAD,QAAA,GACvBjE,IAAA,CAACT,IAAI;oBAACoE,KAAK,EAAE;sBACXhB,KAAK,EAAE,MAAM;sBACb0B,QAAQ,EAAE,EAAE;sBACZE,UAAU,EAAE;oBACd,CAAE;oBAAAN,QAAA,EACCuC,WAAW,CAACjE;kBAAI,CACb,CAAC,EACPvC,IAAA,CAACT,IAAI;oBAACoE,KAAK,EAAE;sBACXhB,KAAK,EAAE,SAAS;sBAChB0B,QAAQ,EAAE,EAAE;sBACZE,UAAU,EAAE;oBACd,CAAE;oBAAAN,QAAA,EACCuC,WAAW,CAACvD;kBAAI,CACb,CAAC;gBAAA,CACH,CAAC,EACPjD,IAAA,CAACf,IAAI;kBAAC0E,KAAK,EAAE;oBAAEe,aAAa,EAAE;kBAAM,CAAE;kBAAAT,QAAA,EACnC0C,kBAAA,CAAIC,KAAK,CAACJ,WAAW,CAACrD,MAAM,CAAC,EAAEsC,GAAG,CAAC,UAACoB,CAAC,EAAEC,CAAC;oBAAA,OACvC9G,IAAA,CAACT,IAAI;sBAASoE,KAAK,EAAE;wBAAEhB,KAAK,EAAE,SAAS;wBAAE0B,QAAQ,EAAE;sBAAG,CAAE;sBAAAJ,QAAA,EAAC;oBAAC,GAA/C6C,CAAqD,CAAC;kBAAA,CAClE;gBAAC,CACE,CAAC;cAAA,CACH,CAAC,EACP5G,KAAA,CAACX,IAAI;gBAACoE,KAAK,EAAE;kBACXhB,KAAK,EAAE,MAAM;kBACb0B,QAAQ,EAAE,EAAE;kBACZE,UAAU,EAAE,oBAAoB;kBAChCwC,UAAU,EAAE,EAAE;kBACdC,SAAS,EAAE;gBACb,CAAE;gBAAA/C,QAAA,GAAC,IACA,EAACuC,WAAW,CAACpD,IAAI,EAAC,IACrB;cAAA,CAAM,CAAC;YAAA,GA3CE8C,KA4CL,CAAC;UAAA,CACR;QAAC,CACQ,CAAC;MAAA,CACT,CAAC,EAGPlG,IAAA,CAACf,IAAI;QAAC0E,KAAK,EAAE;UAAEc,iBAAiB,EAAE,EAAE;UAAEH,YAAY,EAAE;QAAG,CAAE;QAAAL,QAAA,EACvD/D,KAAA,CAACR,IAAI;UAACiE,KAAK,EAAE;YAAEsB,eAAe,EAAE,SAAS;YAAEd,OAAO,EAAE,EAAE;YAAEC,UAAU,EAAE;UAAS,CAAE;UAAAH,QAAA,GAC7EjE,IAAA,CAACT,IAAI;YAACoE,KAAK,EAAE;cACXhB,KAAK,EAAE,MAAM;cACb0B,QAAQ,EAAE,EAAE;cACZE,UAAU,EAAE,iBAAiB;cAC7BC,SAAS,EAAE,QAAQ;cACnBF,YAAY,EAAE;YAChB,CAAE;YAAAL,QAAA,EAAC;UAEH,CAAM,CAAC,EACPjE,IAAA,CAACT,IAAI;YAACoE,KAAK,EAAE;cACXhB,KAAK,EAAE,MAAM;cACb0B,QAAQ,EAAE,EAAE;cACZE,UAAU,EAAE,oBAAoB;cAChCC,SAAS,EAAE,QAAQ;cACnBF,YAAY,EAAE,EAAE;cAChBiB,OAAO,EAAE;YACX,CAAE;YAAAtB,QAAA,EAAC;UAEH,CAAM,CAAC,EACPjE,IAAA,CAACV,MAAM;YACL8F,IAAI,EAAC,WAAW;YAChBxB,OAAO,EAAE,SAATA,OAAOA,CAAA;cAAA,OAAQN,eAAe,CAACxC,YAAY,CAAC;YAAA,CAAC;YAC7C6C,KAAK,EAAE;cACLsB,eAAe,EAAE,MAAM;cACvBR,iBAAiB,EAAE;YACrB,CAAE;YACFa,UAAU,EAAE;cACV3C,KAAK,EAAE,SAAS;cAChB4B,UAAU,EAAE,qBAAqB;cACjCF,QAAQ,EAAE;YACZ,CAAE;YAAAJ,QAAA,EACH;UAED,CAAQ,CAAC,EACTjE,IAAA,CAACT,IAAI;YAACoE,KAAK,EAAE;cACXhB,KAAK,EAAE,MAAM;cACb0B,QAAQ,EAAE,EAAE;cACZE,UAAU,EAAE,oBAAoB;cAChCC,SAAS,EAAE,QAAQ;cACnBgB,SAAS,EAAE,CAAC;cACZD,OAAO,EAAE;YACX,CAAE;YAAAtB,QAAA,EAAC;UAEH,CAAM,CAAC;QAAA,CACH;MAAC,CACH,CAAC;IAAA,CACG,CAAC,EAGbjE,IAAA,CAACX,KAAK;MACJ4H,OAAO,EAAE/F,gBAAiB;MAC1BgG,aAAa,EAAC,OAAO;MACrBC,iBAAiB,EAAC,WAAW;MAC7BC,cAAc,EAAErD,mBAAoB;MAAAE,QAAA,EAEpCjE,IAAA,CAACJ,WAAW;QACV2D,MAAM,EAAEjC,WAAY;QACpB+F,gBAAgB,EAAExD,oBAAqB;QACvCyD,eAAe,EAAEvD,mBAAoB;QACrCkD,OAAO,EAAE/F;MAAiB,CAC3B;IAAC,CACG,CAAC;EAAA,CACD,CAAC;AAEd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}