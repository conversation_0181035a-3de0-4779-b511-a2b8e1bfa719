<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CryptoSignals - Dashboard de Monitoramento</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Axios para requisições HTTP -->
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>

    <style>
        :root {
            --primary-color: #F7B500;
            --secondary-color: #FFC107;
            --success-color: #28a745;
            --warning-color: #F7B500;
            --danger-color: #dc3545;
            --dark-bg: #0D1117;
            --darker-bg: #161B22;
            --card-bg: #21262D;
            --border-color: #30363D;
            --text-light: #F0F6FC;
            --text-muted: #8B949E;
            --accent-gold: #F7B500;
            --accent-orange: #FF8C00;
        }

        body {
            background: var(--dark-bg);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: var(--text-light);
        }

        .dashboard-header {
            background: var(--darker-bg);
            border-bottom: 2px solid var(--accent-gold);
            padding: 1.5rem 0;
            margin-bottom: 2rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .card {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease, border-color 0.3s ease;
        }

        .card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 30px rgba(247, 181, 0, 0.1);
            border-color: var(--accent-gold);
        }

        .card-header {
            background: var(--darker-bg);
            border-bottom: 1px solid var(--border-color);
            border-radius: 12px 12px 0 0 !important;
            color: var(--accent-gold);
            font-weight: 600;
        }

        .metric-card {
            text-align: center;
            padding: 1.5rem;
            border-radius: 12px;
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .metric-card:hover {
            transform: scale(1.02);
            border-color: var(--accent-gold);
            box-shadow: 0 8px 25px rgba(247, 181, 0, 0.15);
        }

        .metric-value {
            font-size: 2.5rem;
            font-weight: bold;
            margin: 0.5rem 0;
            color: var(--accent-gold);
        }

        .metric-label {
            font-size: 0.9rem;
            color: var(--text-muted);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }

        .status-active { background-color: var(--success-color); }
        .status-inactive { background-color: var(--warning-color); }
        .status-error { background-color: var(--danger-color); }

        .log-entry {
            padding: 0.5rem;
            margin: 0.25rem 0;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
        }

        .log-info {
            background: rgba(40, 167, 69, 0.1);
            border-left: 3px solid var(--success-color);
            color: var(--text-light);
        }
        .log-warning {
            background: rgba(247, 181, 0, 0.1);
            border-left: 3px solid var(--warning-color);
            color: var(--text-light);
        }
        .log-error {
            background: rgba(220, 53, 69, 0.1);
            border-left: 3px solid var(--danger-color);
            color: var(--text-light);
        }

        .alert-item {
            padding: 0.75rem;
            margin: 0.5rem 0;
            border-radius: 8px;
            border-left: 4px solid;
            background: var(--card-bg);
            border: 1px solid var(--border-color);
        }

        .alert-warning {
            background: rgba(247, 181, 0, 0.1);
            border-left-color: var(--warning-color);
        }

        .alert-error {
            background: rgba(220, 53, 69, 0.1);
            border-left-color: var(--danger-color);
        }

        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: bold;
        }

        .connected {
            background: rgba(39, 174, 96, 0.9);
            color: white;
        }

        .disconnected {
            background: rgba(231, 76, 60, 0.9);
            color: white;
        }

        .chart-container {
            position: relative;
            height: 300px;
            margin: 1rem 0;
        }

        .refresh-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--accent-gold) 0%, var(--accent-orange) 100%);
            border: none;
            color: var(--dark-bg);
            font-size: 1.5rem;
            box-shadow: 0 4px 20px rgba(247, 181, 0, 0.3);
            transition: all 0.3s ease;
        }

        .refresh-btn:hover {
            background: linear-gradient(135deg, var(--accent-orange) 0%, var(--accent-gold) 100%);
            transform: scale(1.1);
            box-shadow: 0 6px 25px rgba(247, 181, 0, 0.5);
        }

        .refresh-btn.spinning {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .last-update {
            font-size: 0.8rem;
            opacity: 0.7;
            text-align: center;
            margin-top: 1rem;
        }

        @media (max-width: 768px) {
            .metric-value {
                font-size: 2rem;
            }

            .card {
                margin-bottom: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Status de Conexão -->
    <div id="connectionStatus" class="connection-status disconnected">
        <i class="fas fa-wifi"></i> Desconectado
    </div>

    <!-- Botão de Refresh -->
    <button id="refreshBtn" class="refresh-btn" onclick="requestUpdate()">
        <i class="fas fa-sync-alt"></i>
    </button>

    <!-- Header -->
    <div class="dashboard-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="mb-0" style="color: var(--accent-gold);">
                        <i class="fas fa-chart-line me-2" style="color: var(--accent-gold);"></i>
                        CryptoSignals Dashboard
                    </h1>
                    <p class="mb-0" style="color: var(--text-muted);">Monitoramento em Tempo Real</p>
                </div>
                <div class="col-md-6 text-end">
                    <div id="lastUpdate" class="last-update">
                        Última atualização: --
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <!-- Métricas do Servidor -->
        <div class="row mb-4">
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card metric-card">
                    <div class="metric-label">
                        <i class="fas fa-microchip me-1"></i> CPU
                    </div>
                    <div id="cpuUsage" class="metric-value">--</div>
                    <div class="progress" style="height: 8px; background-color: var(--border-color);">
                        <div id="cpuProgress" class="progress-bar" style="width: 0%; background-color: var(--accent-gold);"></div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card metric-card">
                    <div class="metric-label">
                        <i class="fas fa-memory me-1"></i> Memória
                    </div>
                    <div id="memoryUsage" class="metric-value">--</div>
                    <div class="progress" style="height: 8px; background-color: var(--border-color);">
                        <div id="memoryProgress" class="progress-bar" style="width: 0%; background-color: var(--accent-orange);"></div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card metric-card">
                    <div class="metric-label">
                        <i class="fas fa-hdd me-1"></i> Disco
                    </div>
                    <div id="diskUsage" class="metric-value">--</div>
                    <div class="progress" style="height: 8px; background-color: var(--border-color);">
                        <div id="diskProgress" class="progress-bar" style="width: 0%; background-color: var(--success-color);"></div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card metric-card">
                    <div class="metric-label">
                        <i class="fas fa-clock me-1"></i> Uptime
                    </div>
                    <div id="serverUptime" class="metric-value" style="font-size: 1.5rem;">--</div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Status dos Serviços -->
            <div class="col-lg-6 mb-4">
                <div class="card h-100">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-cogs me-2"></i>
                            Status dos Serviços
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="servicesStatus">
                            <div class="text-center py-4">
                                <i class="fas fa-spinner fa-spin fa-2x"></i>
                                <p class="mt-2">Carregando status dos serviços...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Alertas -->
            <div class="col-lg-6 mb-4">
                <div class="card h-100">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Alertas Ativos
                            <span id="alertsCount" class="badge bg-danger ms-2">0</span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="alertsList">
                            <div class="text-center py-4">
                                <i class="fas fa-check-circle fa-2x text-success"></i>
                                <p class="mt-2">Nenhum alerta ativo</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Gráficos e Logs -->
        <div class="row">
            <!-- Gráfico de Performance -->
            <div class="col-lg-8 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-area me-2"></i>
                            Performance do Servidor
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="performanceChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Logs Recentes -->
            <div class="col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-file-alt me-2"></i>
                            Logs Recentes
                        </h5>
                        <button class="btn btn-sm" style="border: 1px solid var(--accent-gold); color: var(--accent-gold);" onclick="loadMoreLogs()">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                    <div class="card-body p-2" style="max-height: 400px; overflow-y: auto;">
                        <div id="logsList">
                            <div class="text-center py-4">
                                <i class="fas fa-spinner fa-spin"></i>
                                <p class="mt-2">Carregando logs...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Variáveis globais
        let performanceChart;
        let performanceData = {
            labels: [],
            cpu: [],
            memory: [],
            disk: []
        };
        let updateInterval;

        // Inicialização
        document.addEventListener('DOMContentLoaded', function() {
            initializeChart();
            updateConnectionStatus(true);
            requestUpdate();

            // Iniciar polling automático a cada 10 segundos
            updateInterval = setInterval(requestUpdate, 10000);
        });

        // Funções principais
        function updateConnectionStatus(connected) {
            const statusEl = document.getElementById('connectionStatus');
            if (connected) {
                statusEl.className = 'connection-status connected';
                statusEl.innerHTML = '<i class="fas fa-wifi"></i> Conectado';
            } else {
                statusEl.className = 'connection-status disconnected';
                statusEl.innerHTML = '<i class="fas fa-wifi"></i> Desconectado';
            }
        }

        function requestUpdate() {
            const btn = document.getElementById('refreshBtn');
            btn.classList.add('spinning');

            // Fazer requisição HTTP para obter dados
            axios.get('/api/data')
                .then(response => {
                    updateDashboard(response.data);
                    updateConnectionStatus(true);
                })
                .catch(error => {
                    console.error('Erro ao obter dados:', error);
                    updateConnectionStatus(false);
                })
                .finally(() => {
                    setTimeout(() => {
                        btn.classList.remove('spinning');
                    }, 500);
                });
        }

        function updateDashboard(data) {
            updateServerMetrics(data.server_metrics);
            updateServicesStatus(data.services);
            updateAlerts(data.alerts);
            updateLogs(data.logs);
            updateLastUpdateTime(data.timestamp);
            updatePerformanceChart(data.server_metrics);
        }

        function updateServerMetrics(metrics) {
            if (!metrics) return;

            // CPU
            const cpuUsage = metrics.cpu_percent || 0;
            document.getElementById('cpuUsage').textContent = cpuUsage.toFixed(1) + '%';
            document.getElementById('cpuProgress').style.width = cpuUsage + '%';

            // Memória
            const memoryUsage = metrics.memory_percent || 0;
            document.getElementById('memoryUsage').textContent = memoryUsage.toFixed(1) + '%';
            document.getElementById('memoryProgress').style.width = memoryUsage + '%';

            // Disco
            const diskUsage = metrics.disk_percent || 0;
            document.getElementById('diskUsage').textContent = diskUsage.toFixed(1) + '%';
            document.getElementById('diskProgress').style.width = diskUsage + '%';

            // Uptime
            document.getElementById('serverUptime').textContent = metrics.uptime || '--';
        }

        function updateServicesStatus(services) {
            const container = document.getElementById('servicesStatus');
            if (!services || Object.keys(services).length === 0) {
                container.innerHTML = '<p class="text-center">Nenhum serviço encontrado</p>';
                return;
            }

            let html = '';
            for (const [serviceId, service] of Object.entries(services)) {
                const statusClass = service.status === 'active' ? 'status-active' :
                                  service.status === 'inactive' ? 'status-inactive' : 'status-error';

                html += `
                    <div class="d-flex justify-content-between align-items-center mb-3 p-3 rounded" style="background: rgba(255,255,255,0.05);">
                        <div>
                            <div class="d-flex align-items-center mb-1">
                                <span class="status-indicator ${statusClass}"></span>
                                <strong>${service.name}</strong>
                            </div>
                            <small class="text-muted">
                                CPU: ${service.cpu_usage?.toFixed(1) || 0}% |
                                RAM: ${service.memory_usage?.toFixed(1) || 0}% |
                                Restarts: ${service.restart_count || 0}
                            </small>
                        </div>
                        <div class="text-end">
                            <div class="badge ${service.status === 'active' ? 'bg-success' : 'bg-warning'} mb-1">
                                ${service.status}
                            </div>
                            <div class="small text-muted">
                                ${service.uptime || 'N/A'}
                            </div>
                        </div>
                    </div>
                `;
            }
            container.innerHTML = html;
        }

        function updateAlerts(alerts) {
            const container = document.getElementById('alertsList');
            const countBadge = document.getElementById('alertsCount');

            if (!alerts || alerts.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-4">
                        <i class="fas fa-check-circle fa-2x text-success"></i>
                        <p class="mt-2">Nenhum alerta ativo</p>
                    </div>
                `;
                countBadge.textContent = '0';
                return;
            }

            countBadge.textContent = alerts.length;

            let html = '';
            alerts.forEach(alert => {
                const alertClass = alert.level === 'ERROR' || alert.level === 'CRITICAL' ? 'alert-error' : 'alert-warning';
                const icon = alert.level === 'ERROR' || alert.level === 'CRITICAL' ? 'fa-exclamation-circle' : 'fa-exclamation-triangle';

                html += `
                    <div class="alert-item ${alertClass}">
                        <div class="d-flex align-items-start">
                            <i class="fas ${icon} me-2 mt-1"></i>
                            <div class="flex-grow-1">
                                <div class="fw-bold">${alert.service}</div>
                                <div class="small">${alert.message}</div>
                                <div class="small text-muted mt-1">
                                    ${new Date(alert.timestamp).toLocaleString()}
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        function updateLogs(logs) {
            const container = document.getElementById('logsList');
            if (!logs || logs.length === 0) {
                container.innerHTML = '<p class="text-center">Nenhum log disponível</p>';
                return;
            }

            let html = '';
            logs.slice(-10).forEach(log => {
                const logClass = `log-${log.level.toLowerCase()}`;
                html += `
                    <div class="log-entry ${logClass}">
                        <div class="small">
                            <span class="text-muted">${log.timestamp}</span>
                            <span class="badge badge-sm ms-2 ${log.level === 'ERROR' ? 'bg-danger' : log.level === 'WARNING' ? 'bg-warning' : 'bg-success'}">${log.level}</span>
                        </div>
                        <div class="mt-1">${log.message}</div>
                    </div>
                `;
            });

            container.innerHTML = html;
            container.scrollTop = container.scrollHeight;
        }

        function updateLastUpdateTime(timestamp) {
            const date = new Date(timestamp);
            document.getElementById('lastUpdate').textContent =
                `Última atualização: ${date.toLocaleString()}`;
        }

        function initializeChart() {
            const ctx = document.getElementById('performanceChart').getContext('2d');
            performanceChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [
                        {
                            label: 'CPU %',
                            data: [],
                            borderColor: '#F7B500',
                            backgroundColor: 'rgba(247, 181, 0, 0.1)',
                            tension: 0.4
                        },
                        {
                            label: 'Memória %',
                            data: [],
                            borderColor: '#FF8C00',
                            backgroundColor: 'rgba(255, 140, 0, 0.1)',
                            tension: 0.4
                        },
                        {
                            label: 'Disco %',
                            data: [],
                            borderColor: '#28a745',
                            backgroundColor: 'rgba(40, 167, 69, 0.1)',
                            tension: 0.4
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            labels: {
                                color: '#F0F6FC'
                            }
                        }
                    },
                    scales: {
                        x: {
                            ticks: { color: '#8B949E' },
                            grid: { color: '#30363D' }
                        },
                        y: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                color: '#8B949E',
                                callback: function(value) {
                                    return value + '%';
                                }
                            },
                            grid: { color: '#30363D' }
                        }
                    }
                }
            });
        }

        function updatePerformanceChart(metrics) {
            if (!metrics || !performanceChart) return;

            const now = new Date().toLocaleTimeString();

            // Manter apenas os últimos 20 pontos
            if (performanceData.labels.length >= 20) {
                performanceData.labels.shift();
                performanceData.cpu.shift();
                performanceData.memory.shift();
                performanceData.disk.shift();
            }

            performanceData.labels.push(now);
            performanceData.cpu.push(metrics.cpu_percent || 0);
            performanceData.memory.push(metrics.memory_percent || 0);
            performanceData.disk.push(metrics.disk_percent || 0);

            performanceChart.data.labels = performanceData.labels;
            performanceChart.data.datasets[0].data = performanceData.cpu;
            performanceChart.data.datasets[1].data = performanceData.memory;
            performanceChart.data.datasets[2].data = performanceData.disk;

            performanceChart.update('none');
        }

        function loadMoreLogs() {
            console.log('Carregando mais logs...');
            requestUpdate();
        }
    </script>
</body>
</html>
