import os
import sys
import asyncio
import logging
import time
import datetime
from decimal import Decimal, ROUND_DOWN
import pandas as pd
import numpy as np
import nest_asyncio
from binance.client import Client
from binance.exceptions import BinanceAPIException
from binance.enums import *

# Importando configurações e utilitários do projeto principal
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.settings import (
    BINANCE_API_KEY,
    BINANCE_API_SECRET,
    BINANCE_TESTNET,
    LEVERAGE
)
from utils.binance_client import BinanceHandler
from utils.signal_formatter import SignalFormatter

# Aplicar nest_asyncio para permitir loops aninhados
nest_asyncio.apply()

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("trading_automatizado/auto_trader.log"),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

class AutoTrader:
    def __init__(self, capital_por_operacao=20, modo_simulacao=True, estrategia="scalp", capital_total=None, max_posicoes=4):
        """
        Inicializa o trader automático

        Args:
            capital_por_operacao: Valor em dólares para cada operação
            modo_simulacao: Se True, apenas simula as operações sem executá-las
            estrategia: Tipo de estratégia ('scalp' ou 'swing')
            capital_total: Capital total disponível para trading
            max_posicoes: Número máximo de posições abertas simultaneamente
        """
        self.capital_total = capital_total
        self.max_posicoes = max_posicoes

        # Se capital_total for fornecido, calcula o capital por operação como 10%
        if capital_total:
            self.capital_por_operacao = capital_total * 0.1  # 10% do capital total
        else:
            self.capital_por_operacao = capital_por_operacao

        self.modo_simulacao = modo_simulacao
        self.estrategia = estrategia.lower()
        self.binance_handler = BinanceHandler(use_mock_data=False)
        self.alavancagem = LEVERAGE  # Alavancagem padrão das configurações

        # Inicializa o cliente da Binance para operações
        self.client = Client(BINANCE_API_KEY, BINANCE_API_SECRET)
        if BINANCE_TESTNET:
            self.client.API_URL = 'https://testnet.binancefuture.com/fapi'
            self.client.FUTURES_API_URL = 'https://testnet.binancefuture.com/fapi'

        # Dicionário para rastrear operações abertas
        self.operacoes_abertas = {}

        # Formatter para formatação de mensagens
        self.formatter = SignalFormatter()

        # Configurações específicas por estratégia com base no backtesting
        self.configurar_estrategia()

        logger.info(f"AutoTrader inicializado com capital total de ${capital_total if capital_total else 'não definido'}")
        logger.info(f"Capital por operação: ${self.capital_por_operacao} (10% do capital total)")
        logger.info(f"Máximo de posições simultâneas: {self.max_posicoes}")
        logger.info(f"Modo de simulação: {'ATIVADO' if modo_simulacao else 'DESATIVADO'}")
        logger.info(f"Estratégia: {self.estrategia}")

    def configurar_estrategia(self):
        """Configura parâmetros específicos para cada estratégia com base no backtesting"""
        if self.estrategia == "scalp":
            # Configurações para Scalping baseadas no backtesting
            self.stop_loss_percentual = 0.005  # 0.5% - Ajustado para reduzir perdas
            self.take_profit_percentuais = [0.004, 0.006, 0.008, 0.01]  # 0.4%, 0.6%, 0.8%, 1.0% - Níveis de TP otimizados
            self.quantidade_por_tp = [0.25, 0.25, 0.25, 0.25]  # 25% da posição para cada nível de TP
            self.timeframe_preferido = "1m"  # Melhor timeframe conforme backtesting
            logger.info(f"Configuração de Scalping: SL={self.stop_loss_percentual*100}%, TPs={[tp*100 for tp in self.take_profit_percentuais]}%")
        else:  # swing
            # Configurações para Swing Trading baseadas no backtesting
            self.stop_loss_percentual = 0.01  # 1.0% - Maior tolerância para swing
            self.take_profit_percentuais = [0.01, 0.015, 0.02, 0.025]  # 1.0%, 1.5%, 2.0%, 2.5% - Níveis de TP otimizados
            self.quantidade_por_tp = [0.2, 0.3, 0.3, 0.2]  # Distribuição otimizada
            self.timeframe_preferido = "4h"  # Melhor timeframe conforme backtesting
            logger.info(f"Configuração de Swing: SL={self.stop_loss_percentual*100}%, TPs={[tp*100 for tp in self.take_profit_percentuais]}%")

    def is_trading_hours(self):
        """Verifica se está dentro do horário de trading - OPERAÇÃO 24/7"""
        # Criptomoedas operam 24h, sempre retorna True
        # Mantemos a função para compatibilidade, mas sempre permite trading
        return True

    def verificar_volatilidade(self, symbol):
        """
        Verifica se a volatilidade atual é adequada para a estratégia

        Args:
            symbol: Par de trading (ex: 'BTCUSDT')

        Returns:
            bool: True se a volatilidade for adequada, False caso contrário
        """
        try:
            # Obtém dados históricos
            if self.estrategia == "scalp":
                df = self.binance_handler.get_historical_klines(symbol=symbol, interval="1h", lookback_days=1)
            else:  # swing
                df = self.binance_handler.get_historical_klines(symbol=symbol, interval="1d", lookback_days=7)

            if df.empty:
                return False

            # Calcula a volatilidade (ATR simplificado)
            df['high_low'] = df['high'] - df['low']
            df['high_close'] = np.abs(df['high'] - df['close'].shift())
            df['low_close'] = np.abs(df['low'] - df['close'].shift())
            df['tr'] = df[['high_low', 'high_close', 'low_close']].max(axis=1)
            atr = df['tr'].mean()

            # Calcula a volatilidade percentual
            preco_atual = df['close'].iloc[-1]
            volatilidade_percentual = (atr / preco_atual) * 100

            logger.info(f"Volatilidade para {symbol}: {volatilidade_percentual:.2f}%")

            # Verifica se a volatilidade é adequada para a estratégia
            if self.estrategia == "scalp":
                # Para scalping, queremos volatilidade moderada (nem muito alta nem muito baixa)
                return 0.5 <= volatilidade_percentual <= 3.0
            else:  # swing
                # Para swing, aceitamos volatilidade mais alta
                return 0.8 <= volatilidade_percentual <= 5.0

        except Exception as e:
            logger.error(f"Erro ao verificar volatilidade para {symbol}: {e}")
            return True  # Em caso de erro, permitimos a operação

    def verificar_tendencia(self, symbol, tipo_sinal):
        """
        Verifica se a tendência atual está alinhada com o tipo de sinal

        Args:
            symbol: Par de trading
            tipo_sinal: Tipo de sinal (LONG ou SHORT)

        Returns:
            bool: True se a tendência estiver alinhada, False caso contrário
        """
        try:
            # Obtém dados históricos
            df = self.binance_handler.get_historical_klines(symbol=symbol, interval='1h', lookback_days=1)

            if df is None or df.empty:
                logger.warning(f"Sem dados históricos para verificar tendência de {symbol}")
                return True  # Assume tendência favorável na ausência de dados

            # Calcula médias móveis
            df['ma20'] = df['close'].rolling(window=20).mean()
            df['ma50'] = df['close'].rolling(window=50).mean()

            # Verifica a tendência
            ultimo_candle = df.iloc[-1]

            if tipo_sinal == 'LONG':
                return ultimo_candle['ma20'] > ultimo_candle['ma50']
            else:  # SHORT
                return ultimo_candle['ma20'] < ultimo_candle['ma50']

        except Exception as e:
            logger.error(f"Erro ao verificar tendência: {e}")
            return True  # Assume tendência favorável em caso de erro

    def calcular_quantidade(self, symbol, preco_entrada):
        """
        Calcula a quantidade a ser comprada/vendida com base no capital por operação

        Args:
            symbol: Par de trading (ex: 'BTCUSDT')
            preco_entrada: Preço de entrada da operação

        Returns:
            float: Quantidade a ser comprada/vendida
        """
        try:
            # Obtém informações do símbolo para determinar a precisão da quantidade
            info = self.client.futures_exchange_info()
            symbol_info = next((s for s in info['symbols'] if s['symbol'] == symbol), None)

            if not symbol_info:
                logger.error(f"Símbolo {symbol} não encontrado")
                return None

            # Encontra a precisão da quantidade
            quantity_precision = 0
            for filter in symbol_info['filters']:
                if filter['filterType'] == 'LOT_SIZE':
                    step_size = float(filter['stepSize'])
                    quantity_precision = len(str(step_size).rstrip('0').split('.')[-1])
                    break

            # Calcula a quantidade com base no capital e alavancagem
            quantidade = (self.capital_por_operacao * self.alavancagem) / float(preco_entrada)

            # Arredonda para a precisão correta
            quantidade_decimal = Decimal(str(quantidade))
            quantidade_arredondada = quantidade_decimal.quantize(
                Decimal('0.' + '0' * quantity_precision),
                rounding=ROUND_DOWN
            )

            return float(quantidade_arredondada)

        except Exception as e:
            logger.error(f"Erro ao calcular quantidade para {symbol}: {e}")
            return None

    def calcular_stop_loss(self, preco_entrada, tipo_sinal):
        """
        Calcula o stop loss baseado no tipo de sinal e estratégia

        Args:
            preco_entrada: Preço de entrada
            tipo_sinal: 'LONG' ou 'SHORT'

        Returns:
            float: Preço do stop loss
        """
        if tipo_sinal == 'LONG':
            return preco_entrada * (1 - self.stop_loss_percentual)
        else:  # SHORT
            return preco_entrada * (1 + self.stop_loss_percentual)

    def calcular_take_profits(self, preco_entrada, tipo_sinal):
        """
        Calcula múltiplos níveis de take profit baseados no tipo de sinal e estratégia

        Args:
            preco_entrada: Preço de entrada
            tipo_sinal: 'LONG' ou 'SHORT'

        Returns:
            list: Lista de preços de take profit
        """
        take_profits = []

        for percentual in self.take_profit_percentuais:
            if tipo_sinal == 'LONG':
                take_profits.append(preco_entrada * (1 + percentual))
            else:  # SHORT
                take_profits.append(preco_entrada * (1 - percentual))

        return take_profits

    async def executar_ordem(self, symbol, tipo_sinal, preco_entrada, stop_loss, take_profits):
        """
        Executa uma ordem na Binance

        Args:
            symbol: Par de trading (ex: 'BTCUSDT')
            tipo_sinal: 'LONG' ou 'SHORT'
            preco_entrada: Preço de entrada
            stop_loss: Preço do stop loss
            take_profits: Lista de preços de take profit

        Returns:
            dict: Informações da ordem ou None se falhar
        """
        # Verifica se já atingiu o limite de posições abertas
        if len(self.operacoes_abertas) >= self.max_posicoes:
            logger.warning(f"Limite de {self.max_posicoes} posições abertas atingido. Ordem para {symbol} não executada.")
            return None

        if not self.is_trading_hours():
            logger.warning(f"Fora do horário de trading. Ordem para {symbol} não executada.")
            return None

        # Verifica volatilidade e tendência
        if not self.verificar_volatilidade(symbol):
            logger.warning(f"Volatilidade inadequada para {symbol}. Ordem não executada.")
            return None

        if not self.verificar_tendencia(symbol, tipo_sinal):
            logger.warning(f"Tendência desfavorável para {symbol} ({tipo_sinal}). Ordem não executada.")
            return None

        try:
            # Calcula a quantidade
            quantidade_total = self.calcular_quantidade(symbol, preco_entrada)
            if not quantidade_total:
                return None

            # Define o lado da ordem
            side = SIDE_BUY if tipo_sinal == 'LONG' else SIDE_SELL

            # Configura a alavancagem
            if not self.modo_simulacao:
                self.client.futures_change_leverage(symbol=symbol, leverage=self.alavancagem)

            logger.info(f"Preparando ordem para {symbol}: {tipo_sinal}, Preço: {preco_entrada}, Quantidade: {quantidade_total}")

            if self.modo_simulacao:
                # Simula a ordem
                ordem_id = f"sim_{int(time.time())}"
                ordem = {
                    'orderId': ordem_id,
                    'symbol': symbol,
                    'side': side,
                    'price': preco_entrada,
                    'origQty': quantidade_total,
                    'status': 'FILLED',
                    'tipo': tipo_sinal,
                    'stop_loss': stop_loss,
                    'take_profits': take_profits,
                    'quantidades_tp': [quantidade_total * perc for perc in self.quantidade_por_tp]
                }

                # Registra a operação simulada
                self.operacoes_abertas[ordem_id] = ordem

                logger.info(f"[SIMULAÇÃO] Ordem executada para {symbol}: {tipo_sinal}, ID: {ordem_id}")
                return ordem
            else:
                # Executa a ordem real
                ordem = self.client.futures_create_order(
                    symbol=symbol,
                    side=side,
                    type=ORDER_TYPE_MARKET,
                    quantity=quantidade_total
                )

                # Configura stop loss
                sl_ordem = self.client.futures_create_order(
                    symbol=symbol,
                    side=SIDE_SELL if tipo_sinal == 'LONG' else SIDE_BUY,
                    type=ORDER_TYPE_STOP_MARKET,
                    quantity=quantidade_total,
                    stopPrice=stop_loss,
                    closePosition=True
                )

                # Configura take profits em múltiplos níveis
                tp_ordens = []
                for i, (tp, quantidade_percentual) in enumerate(zip(take_profits, self.quantidade_por_tp)):
                    quantidade_tp = quantidade_total * quantidade_percentual

                    # Arredonda a quantidade para a precisão correta
                    info = self.client.futures_exchange_info()
                    symbol_info = next((s for s in info['symbols'] if s['symbol'] == symbol), None)
                    quantity_precision = 0
                    for filter in symbol_info['filters']:
                        if filter['filterType'] == 'LOT_SIZE':
                            step_size = float(filter['stepSize'])
                            quantity_precision = len(str(step_size).rstrip('0').split('.')[-1])
                            break

                    quantidade_decimal = Decimal(str(quantidade_tp))
                    quantidade_arredondada = quantidade_decimal.quantize(
                        Decimal('0.' + '0' * quantity_precision),
                        rounding=ROUND_DOWN
                    )
                    quantidade_tp = float(quantidade_arredondada)

                    if quantidade_tp > 0:
                        tp_ordem = self.client.futures_create_order(
                            symbol=symbol,
                            side=SIDE_SELL if tipo_sinal == 'LONG' else SIDE_BUY,
                            type=ORDER_TYPE_TAKE_PROFIT_MARKET,
                            quantity=quantidade_tp,
                            stopPrice=tp,
                            reduceOnly=True
                        )
                        tp_ordens.append(tp_ordem)

                # Registra a operação
                ordem_completa = {
                    'orderId': ordem['orderId'],
                    'symbol': symbol,
                    'side': side,
                    'price': preco_entrada,
                    'origQty': quantidade_total,
                    'status': 'FILLED',
                    'tipo': tipo_sinal,
                    'stop_loss_order': sl_ordem,
                    'take_profit_orders': tp_ordens,
                    'take_profits': take_profits,
                    'quantidades_tp': [quantidade_total * perc for perc in self.quantidade_por_tp]
                }

                self.operacoes_abertas[ordem['orderId']] = ordem_completa

                logger.info(f"Ordem executada para {symbol}: {tipo_sinal}, ID: {ordem['orderId']}")
                return ordem_completa

        except BinanceAPIException as e:
            logger.error(f"Erro da API Binance ao executar ordem para {symbol}: {e}")
            return None
        except Exception as e:
            logger.error(f"Erro ao executar ordem para {symbol}: {e}")
            return None

    async def processar_sinal(self, sinal):
        """
        Processa um sinal recebido e executa a ordem correspondente

        Args:
            sinal: Dicionário contendo informações do sinal

        Returns:
            dict: Informações da ordem ou None se falhar
        """
        try:
            symbol = sinal['symbol']
            tipo_sinal = sinal['signal_type']
            preco_entrada = sinal['entry_price']

            # Usa o stop loss fornecido ou calcula com base na estratégia
            stop_loss = sinal.get('stop_loss') or self.calcular_stop_loss(preco_entrada, tipo_sinal)

            # Usa o take profit fornecido ou calcula múltiplos níveis com base na estratégia
            if 'take_profit' in sinal:
                take_profits = [sinal['take_profit']]
            else:
                take_profits = self.calcular_take_profits(preco_entrada, tipo_sinal)

            logger.info(f"Processando sinal para {symbol}: {tipo_sinal}")
            logger.info(f"Stop Loss: {stop_loss}, Take Profits: {take_profits}")

            # Executa a ordem
            resultado = await self.executar_ordem(
                symbol=symbol,
                tipo_sinal=tipo_sinal,
                preco_entrada=preco_entrada,
                stop_loss=stop_loss,
                take_profits=take_profits
            )

            if resultado:
                logger.info(f"Sinal processado com sucesso para {symbol}")
                return resultado
            else:
                logger.warning(f"Falha ao processar sinal para {symbol}")
                return None

        except Exception as e:
            logger.error(f"Erro ao processar sinal: {e}")
            return None

    async def monitorar_operacoes(self):
        """Monitora as operações abertas para verificar status"""
        while True:
            try:
                if not self.operacoes_abertas:
                    await asyncio.sleep(10)
                    continue

                logger.info(f"Monitorando {len(self.operacoes_abertas)} operações abertas")

                for ordem_id, ordem in list(self.operacoes_abertas.items()):
                    symbol = ordem['symbol']

                    if self.modo_simulacao:
                        # Em modo de simulação, verificamos o preço atual para simular execução de SL/TP
                        preco_atual = self.binance_handler.get_current_price(symbol)

                        if ordem['tipo'] == 'LONG':
                            if preco_atual <= ordem['stop_loss']:
                                logger.info(f"[SIMULAÇÃO] Stop Loss atingido para {symbol} em {preco_atual}")
                                del self.operacoes_abertas[ordem_id]
                            else:
                                # Verifica se algum take profit foi atingido
                                for i, (tp, qtd) in enumerate(zip(ordem['take_profits'], ordem['quantidades_tp'])):
                                    if preco_atual >= tp:
                                        logger.info(f"[SIMULAÇÃO] Take Profit {i+1} atingido para {symbol} em {preco_atual}")
                                        # Remove este TP da lista
                                        ordem['take_profits'].pop(i)
                                        ordem['quantidades_tp'].pop(i)
                                        # Se não houver mais TPs, remove a operação
                                        if not ordem['take_profits']:
                                            del self.operacoes_abertas[ordem_id]
                                        break
                        else:  # SHORT
                            if preco_atual >= ordem['stop_loss']:
                                logger.info(f"[SIMULAÇÃO] Stop Loss atingido para {symbol} em {preco_atual}")
                                del self.operacoes_abertas[ordem_id]
                            else:
                                # Verifica se algum take profit foi atingido
                                for i, (tp, qtd) in enumerate(zip(ordem['take_profits'], ordem['quantidades_tp'])):
                                    if preco_atual <= tp:
                                        logger.info(f"[SIMULAÇÃO] Take Profit {i+1} atingido para {symbol} em {preco_atual}")
                                        # Remove este TP da lista
                                        ordem['take_profits'].pop(i)
                                        ordem['quantidades_tp'].pop(i)
                                        # Se não houver mais TPs, remove a operação
                                        if not ordem['take_profits']:
                                            del self.operacoes_abertas[ordem_id]
                                        break
                    else:
                        # Em modo real, verificamos o status da posição
                        posicao = self.client.futures_position_information(symbol=symbol)

                        # Se a quantidade for 0, a posição foi fechada
                        if float(posicao[0]['positionAmt']) == 0:
                            logger.info(f"Posição fechada para {symbol}")
                            del self.operacoes_abertas[ordem_id]

                await asyncio.sleep(10)  # Verifica a cada 10 segundos

            except Exception as e:
                logger.error(f"Erro ao monitorar operações: {e}")
                await asyncio.sleep(30)  # Espera mais tempo em caso de erro

    async def iniciar(self):
        """Inicia o monitoramento de operações"""
        logger.info("Iniciando monitoramento de operações")
        await self.monitorar_operacoes()

    def get_position_info(self, symbol):
        """
        Obtém informações da posição atual

        Args:
            symbol: Par de trading

        Returns:
            dict: Informações da posição ou None se não houver posição aberta
        """
        try:
            if self.modo_simulacao:
                # Em modo de simulação, verifica nas operações abertas
                for ordem_id, ordem in self.operacoes_abertas.items():
                    if ordem['symbol'] == symbol:
                        return {
                            'symbol': symbol,
                            'positionAmt': ordem.get('quantidade_total', 0.0),
                            'entryPrice': ordem.get('preco_entrada', 0.0),
                            'leverage': self.alavancagem,
                            'unrealizedProfit': 0.0,  # Simulado
                            'marginType': 'isolated',
                            'positionSide': 'BOTH',
                            'stopLoss': ordem.get('stop_loss', None),
                            'takeProfit': ordem.get('take_profits', [None])[0] if ordem.get('take_profits') else None
                        }

                # Se não encontrou, retorna posição vazia
                return {
                    'symbol': symbol,
                    'positionAmt': 0.0,
                    'entryPrice': 0.0,
                    'leverage': self.alavancagem,
                    'unrealizedProfit': 0.0,
                    'marginType': 'isolated',
                    'positionSide': 'BOTH',
                    'stopLoss': None,
                    'takeProfit': None
                }
            else:
                # Em modo real, consulta a API da Binance
                posicao = self.client.futures_position_information(symbol=symbol)

                if posicao and float(posicao[0]['positionAmt']) != 0:
                    # Adiciona informações de stop loss e take profit
                    ordens_abertas = self.client.futures_get_open_orders(symbol=symbol)
                    stop_loss = None
                    take_profit = None

                    for ordem in ordens_abertas:
                        if ordem['type'] == 'STOP_MARKET':
                            stop_loss = float(ordem['stopPrice'])
                        elif ordem['type'] == 'TAKE_PROFIT_MARKET':
                            take_profit = float(ordem['stopPrice'])

                    # Adiciona as informações ao dicionário da posição
                    posicao[0]['stopLoss'] = stop_loss
                    posicao[0]['takeProfit'] = take_profit

                    return posicao[0]
                else:
                    return {
                        'symbol': symbol,
                        'positionAmt': 0.0,
                        'entryPrice': 0.0,
                        'leverage': self.alavancagem,
                        'unrealizedProfit': 0.0,
                        'marginType': 'isolated',
                        'positionSide': 'BOTH',
                        'stopLoss': None,
                        'takeProfit': None
                    }

        except Exception as e:
            logger.error(f"Erro ao obter informações da posição: {e}")
            return {
                'symbol': symbol,
                'positionAmt': 0.0,
                'entryPrice': 0.0,
                'leverage': self.alavancagem,
                'unrealizedProfit': 0.0,
                'marginType': 'isolated',
                'positionSide': 'BOTH',
                'stopLoss': None,
                'takeProfit': None,
                'error': str(e)
            }

    def close_position(self, symbol):
        """
        Fecha uma posição aberta

        Args:
            symbol: Par de trading

        Returns:
            dict: Informações da ordem de fechamento ou None se falhar
        """
        try:
            # Obtém informações da posição
            posicao = self.get_position_info(symbol)

            if float(posicao['positionAmt']) == 0:
                logger.warning(f"Não há posição aberta para {symbol}")
                return None

            quantidade = abs(float(posicao['positionAmt']))
            tipo_posicao = 'LONG' if float(posicao['positionAmt']) > 0 else 'SHORT'

            # Determina o lado da ordem para fechar a posição
            lado = 'SELL' if tipo_posicao == 'LONG' else 'BUY'

            if self.modo_simulacao:
                # Em modo de simulação, remove das operações abertas
                for ordem_id in list(self.operacoes_abertas.keys()):
                    if self.operacoes_abertas[ordem_id]['symbol'] == symbol:
                        preco_atual = self.binance_handler.get_current_price(symbol)
                        preco_entrada = self.operacoes_abertas[ordem_id]['preco_entrada']

                        # Calcula o lucro/prejuízo
                        if tipo_posicao == 'LONG':
                            profit_pct = (preco_atual - preco_entrada) / preco_entrada * 100
                        else:
                            profit_pct = (preco_entrada - preco_atual) / preco_entrada * 100

                        logger.info(f"[SIMULAÇÃO] Fechando posição {tipo_posicao} para {symbol} com {profit_pct:.2f}% de lucro/prejuízo")

                        # Remove a operação
                        ordem_fechamento = {
                            'symbol': symbol,
                            'orderId': 999999,
                            'side': lado,
                            'type': 'MARKET',
                            'status': 'FILLED',
                            'price': preco_atual,
                            'origQty': quantidade,
                            'executedQty': quantidade,
                            'profit_pct': profit_pct
                        }

                        del self.operacoes_abertas[ordem_id]
                        return ordem_fechamento

                return None
            else:
                # Em modo real, cancela todas as ordens abertas primeiro
                self.client.futures_cancel_all_open_orders(symbol=symbol)

                # Executa a ordem de fechamento
                ordem = self.client.futures_create_order(
                    symbol=symbol,
                    side=lado,
                    type='MARKET',
                    quantity=quantidade,
                    reduceOnly=True
                )

                logger.info(f"Posição fechada para {symbol}: {ordem}")
                return ordem

        except Exception as e:
            logger.error(f"Erro ao fechar posição para {symbol}: {e}")
            return None

async def main():
    """Função principal para testes"""
    trader = AutoTrader(capital_por_operacao=20, modo_simulacao=True, estrategia="swing")

    # Exemplo de sinal para teste
    sinal_teste = {
        'symbol': 'BTCUSDT',
        'signal_type': 'LONG',
        'entry_price': 65000,
        'stop_loss': 64500,
        'take_profit': 66000
    }

    # Processa o sinal de teste
    await trader.processar_sinal(sinal_teste)

    # Inicia o monitoramento
    await trader.iniciar()

if __name__ == "__main__":
    asyncio.run(main())