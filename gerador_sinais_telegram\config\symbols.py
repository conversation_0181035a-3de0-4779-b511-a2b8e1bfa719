# Lista expandida de símbolos para monitorar - <PERSON>ÚLTIPLAS FONTES DE SINAIS

# === TOKENS PRINCIPAIS (Alta Liquidez) ===
MAJOR_TOKENS = [
    "BTCUSDT", "ETHUSDT", "BNBUSDT", "XRPUSDT", "ADAUSDT",
    "SOLUSDT", "DOGEUSDT", "AVAXUSDT", "DOTUSDT", "MATICUSDT",
    "LINKUSDT", "LTCUSDT", "BCHUSDT", "ETCUSDT", "XLMUSDT"
]

# === DEFI TOKENS (Finanças Descentralizadas) ===
DEFI_TOKENS = [
    "UNIUSDT", "AAVEUSDT", "SUSHIUSDT", "CAKEUSDT", "1INCHUSDT",
    "COMPUSDT", "MKRUSDT", "YFIUSDT", "CRVUSDT", "BALANCERUSDT",
    "SNXUSDT", "RENUSDT", "KNCUSDT", "LRCUSDT", "BANDUSDT"
]

# === LAYER 1 BLOCKCHAINS ===
LAYER1_TOKENS = [
    "NEARUSDT", "FTMUSDT", "ATOMUSDT", "ALGOUSDT", "EGLDUSDT",
    "ZILUSDT", "ICXUSDT", "ONTUSDT", "QTMUSDT", "WAVESUSDT",
    "HBARUSDT", "VETUSDT", "IOTAUSDT", "NEOUSDT", "EOSUSDT"
]

# === GAMING & METAVERSE ===
GAMING_TOKENS = [
    "SANDUSDT", "MANAUSDT", "AXSUSDT", "ENJUSDT", "CHZUSDT",
    "GALAUSDT", "FLOWUSDT", "IMXUSDT", "GMTUSDT", "APECOINUSDT",
    "YGGUSDT", "SLPUSDT", "TLMUSDT", "ALICEUSDT", "STARUSDT"
]

# === MEME COINS (Alta Volatilidade) ===
MEME_TOKENS = [
    "DOGEUSDT", "SHIBUSDT", "PEPEUSDT", "FLOKIUSDT", "BONKUSDT",
    "WIFUSDT", "BOMEUSDT", "MEMEUSDT", "BABYDOGEUSDT", "AKITAUSDT"
]

# === AI & TECH TOKENS ===
AI_TECH_TOKENS = [
    "FETUSDT", "AGIXUSDT", "OCEANUSDT", "RNDRУСDT", "THETAUSDT",
    "GRTUSDT", "FILUSDT", "ARUSDT", "CTSIUSDT", "PHBUSDT"
]

# === NOVOS TOKENS & TRENDING ===
NEW_TRENDING_TOKENS = [
    "USUALUSDT", "PIXELUSDT", "NOTUSDT", "WUSDT", "JUPUSDT",
    "PYTHUSDT", "STRKUSDT", "MANTAUSDT", "ALTUSDT", "AIUSDT",
    "ACEUSDT", "NFPUSDT", "XAIUSDT", "PORTALUSDT", "PDAUSDT"
]

# === TOKENS DE BAIXO VALOR (Potencial Alto) ===
LOW_CAP_TOKENS = [
    "HOTUSDT", "DENTUSDT", "WINUSDT", "BTTCUSDT", "CELRUSDT",
    "CKBUSDT", "SCUSDT", "DGBUSDT", "RVNUSDT", "VTHOУСDT",
    "NPXSUSDT", "KEYUSDT", "STMXUSDT", "OGNUSDT", "FUNUSDT"
]

# === SÍMBOLOS PARA ESTRATÉGIA DE SCALPING (Expandido) ===
SCALP_SYMBOLS = MAJOR_TOKENS + DEFI_TOKENS[:10] + GAMING_TOKENS[:10] + MEME_TOKENS[:5] + [
    "ZRXUSDT", "STORJUSDT", "SKLUSDT", "ANKRUSDT", "CELOUSDT",
    "BATUSDT", "ENJUSDT", "CHZUSDT", "HOTUSDT", "DENTUSDT"
]

# === SÍMBOLOS PARA ESTRATÉGIA DE BREAKOUT (Expandido) ===
BREAKOUT_SYMBOLS = MAJOR_TOKENS + LAYER1_TOKENS[:10] + DEFI_TOKENS[:8] + [
    "GALAUSDT", "IMXUSDT", "GMTUSDT", "FETUSDT", "GRTUSDT",
    "THETAUSDT", "FILUSDT", "OCEANUSDT", "ENJUSDT", "CHZUSDT"
]

# === SÍMBOLOS PARA ESTRATÉGIA INSIDE BAR (Expandido) ===
INSIDE_BAR_SYMBOLS = MAJOR_TOKENS + DEFI_TOKENS[:8] + LAYER1_TOKENS[:7] + [
    "SANDUSDT", "MANAUSDT", "AXSUSDT", "GALAUSDT", "FLOWUSDT",
    "FETUSDT", "AGIXUSDT", "OCEANUSDT", "GRTUSDT", "THETAUSDT"
]

# === SÍMBOLOS PARA ESTRATÉGIA MFI (Expandido) ===
MFI_SYMBOLS = MAJOR_TOKENS + DEFI_TOKENS[:12] + LAYER1_TOKENS[:8] + [
    "SANDUSDT", "MANAUSDT", "AXSUSDT", "ENJUSDT", "CHZUSDT",
    "GALAUSDT", "FLOWUSDT", "IMXUSDT", "FETUSDT", "AGIXUSDT"
]

# === SÍMBOLOS PARA ESTRATÉGIA SWING (Expandido) ===
SWING_SYMBOLS = MAJOR_TOKENS + DEFI_TOKENS + LAYER1_TOKENS + AI_TECH_TOKENS + [
    "SANDUSDT", "MANAUSDT", "AXSUSDT", "ENJUSDT", "CHZUSDT",
    "GALAUSDT", "FLOWUSDT", "IMXUSDT", "GMTUSDT", "APECOINUSDT"
]

# === POOLS DE TOKENS POR CATEGORIA ===
VOLATILITY_POOLS = {
    "HIGH_VOLATILITY": MEME_TOKENS + NEW_TRENDING_TOKENS[:10] + LOW_CAP_TOKENS[:10],
    "MEDIUM_VOLATILITY": GAMING_TOKENS + AI_TECH_TOKENS + DEFI_TOKENS[:10],
    "LOW_VOLATILITY": MAJOR_TOKENS + LAYER1_TOKENS[:8],
    "TRENDING": NEW_TRENDING_TOKENS + ["JUPUSDT", "PYTHUSDT", "STRKUSDT", "MANTAUSDT"]
}

# === SÍMBOLOS POR TIMEFRAME OTIMIZADO ===
TIMEFRAME_SYMBOLS = {
    "1m": MAJOR_TOKENS[:8] + MEME_TOKENS[:5],  # Tokens com alta liquidez para 1min
    "5m": MAJOR_TOKENS + DEFI_TOKENS[:8] + GAMING_TOKENS[:5],
    "15m": MAJOR_TOKENS + DEFI_TOKENS + LAYER1_TOKENS[:10],
    "1h": MAJOR_TOKENS + DEFI_TOKENS + LAYER1_TOKENS + AI_TECH_TOKENS[:8],
    "4h": MAJOR_TOKENS + DEFI_TOKENS + LAYER1_TOKENS + AI_TECH_TOKENS + GAMING_TOKENS[:10],
    "1d": MAJOR_TOKENS + DEFI_TOKENS + LAYER1_TOKENS + AI_TECH_TOKENS + GAMING_TOKENS
}

# === TODOS OS SÍMBOLOS ÚNICOS ===
ALL_SYMBOLS = list(set(
    MAJOR_TOKENS + DEFI_TOKENS + LAYER1_TOKENS + GAMING_TOKENS +
    MEME_TOKENS + AI_TECH_TOKENS + NEW_TRENDING_TOKENS + LOW_CAP_TOKENS
))