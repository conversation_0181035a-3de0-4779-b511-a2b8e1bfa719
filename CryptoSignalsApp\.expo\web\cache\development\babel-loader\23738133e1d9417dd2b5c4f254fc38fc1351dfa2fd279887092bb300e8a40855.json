{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"style\", \"theme\", \"maxFontSizeMultiplier\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nimport * as React from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport color from 'color';\nimport { useInternalTheme } from \"../../core/theming\";\nimport Text from \"../Typography/Text\";\nvar ListSubheader = function ListSubheader(_ref) {\n  var style = _ref.style,\n    overrideTheme = _ref.theme,\n    maxFontSizeMultiplier = _ref.maxFontSizeMultiplier,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var theme = useInternalTheme(overrideTheme);\n  var textColor = theme.isV3 ? theme.colors.onSurfaceVariant : color(theme.colors.text).alpha(0.54).rgb().string();\n  var font = theme.isV3 ? theme.fonts.bodyMedium : theme.fonts.medium;\n  return React.createElement(Text, _extends({\n    variant: \"bodyMedium\",\n    numberOfLines: 1,\n    maxFontSizeMultiplier: maxFontSizeMultiplier\n  }, rest, {\n    style: [styles.container, _objectSpread({\n      color: textColor\n    }, font), style]\n  }));\n};\nListSubheader.displayName = 'List.Subheader';\nvar styles = StyleSheet.create({\n  container: {\n    paddingHorizontal: 16,\n    paddingVertical: 13\n  }\n});\nexport default ListSubheader;", "map": {"version": 3, "names": ["React", "StyleSheet", "color", "useInternalTheme", "Text", "ListSubheader", "_ref", "style", "overrideTheme", "theme", "maxFontSizeMultiplier", "rest", "_objectWithoutProperties", "_excluded", "textColor", "isV3", "colors", "onSurfaceVariant", "text", "alpha", "rgb", "string", "font", "fonts", "bodyMedium", "medium", "createElement", "_extends", "variant", "numberOfLines", "styles", "container", "_objectSpread", "displayName", "create", "paddingHorizontal", "paddingVertical"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\List\\ListSubheader.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { StyleProp, StyleSheet, TextStyle } from 'react-native';\n\nimport color from 'color';\nimport type { ThemeProp } from 'src/types';\n\nimport { useInternalTheme } from '../../core/theming';\nimport Text from '../Typography/Text';\n\nexport type Props = React.ComponentProps<typeof Text> & {\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n  /**\n   * Style that is passed to Text element.\n   */\n  style?: StyleProp<TextStyle>;\n  /**\n   * Specifies the largest possible scale a text font can reach.\n   */\n  maxFontSizeMultiplier?: number;\n};\n\n/**\n * A component used to display a header in lists.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { List } from 'react-native-paper';\n *\n * const MyComponent = () => <List.Subheader>My List Title</List.Subheader>;\n *\n * export default MyComponent;\n * ```\n */\nconst ListSubheader = ({\n  style,\n  theme: overrideTheme,\n  maxFontSizeMultiplier,\n  ...rest\n}: Props) => {\n  const theme = useInternalTheme(overrideTheme);\n\n  const textColor = theme.isV3\n    ? theme.colors.onSurfaceVariant\n    : color(theme.colors.text).alpha(0.54).rgb().string();\n\n  const font = theme.isV3 ? theme.fonts.bodyMedium : theme.fonts.medium;\n\n  return (\n    <Text\n      variant=\"bodyMedium\"\n      numberOfLines={1}\n      maxFontSizeMultiplier={maxFontSizeMultiplier}\n      {...rest}\n      style={[\n        styles.container,\n        {\n          color: textColor,\n          ...font,\n        },\n        style,\n      ]}\n    />\n  );\n};\n\nListSubheader.displayName = 'List.Subheader';\n\nconst styles = StyleSheet.create({\n  container: {\n    paddingHorizontal: 16,\n    paddingVertical: 13,\n  },\n});\n\nexport default ListSubheader;\n"], "mappings": ";;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,UAAA;AAG9B,OAAOC,KAAK,MAAM,OAAO;AAGzB,SAASC,gBAAgB;AACzB,OAAOC,IAAI;AA8BX,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAAC,IAAA,EAKN;EAAA,IAJXC,KAAK,GAAAD,IAAA,CAALC,KAAK;IACEC,aAAa,GAAAF,IAAA,CAApBG,KAAK;IACLC,qBAAqB,GAAAJ,IAAA,CAArBI,qBAAqB;IAClBC,IAAA,GAAAC,wBAAA,CAAAN,IAAA,EAAAO,SAAA;EAEH,IAAMJ,KAAK,GAAGN,gBAAgB,CAACK,aAAa,CAAC;EAE7C,IAAMM,SAAS,GAAGL,KAAK,CAACM,IAAI,GACxBN,KAAK,CAACO,MAAM,CAACC,gBAAgB,GAC7Bf,KAAK,CAACO,KAAK,CAACO,MAAM,CAACE,IAAI,CAAC,CAACC,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EAEvD,IAAMC,IAAI,GAAGb,KAAK,CAACM,IAAI,GAAGN,KAAK,CAACc,KAAK,CAACC,UAAU,GAAGf,KAAK,CAACc,KAAK,CAACE,MAAM;EAErE,OACEzB,KAAA,CAAA0B,aAAA,CAACtB,IAAI,EAAAuB,QAAA;IACHC,OAAO,EAAC,YAAY;IACpBC,aAAa,EAAE,CAAE;IACjBnB,qBAAqB,EAAEA;EAAsB,GACzCC,IAAI;IACRJ,KAAK,EAAE,CACLuB,MAAM,CAACC,SAAS,EAAAC,aAAA;MAEd9B,KAAK,EAAEY;IAAS,GACbQ,IAAA,GAELf,KAAK;EACL,EACH,CAAC;AAEN,CAAC;AAEDF,aAAa,CAAC4B,WAAW,GAAG,gBAAgB;AAE5C,IAAMH,MAAM,GAAG7B,UAAU,CAACiC,MAAM,CAAC;EAC/BH,SAAS,EAAE;IACTI,iBAAiB,EAAE,EAAE;IACrBC,eAAe,EAAE;EACnB;AACF,CAAC,CAAC;AAEF,eAAe/B,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}