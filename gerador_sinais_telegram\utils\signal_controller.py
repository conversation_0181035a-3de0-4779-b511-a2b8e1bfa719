import logging
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class SignalController:
    def __init__(self):
        self.recent_signals = {}
        self.profit_updates = {}
        
    def is_duplicate_signal(self, symbol, signal_type, entry_price, timestamp=None):
        """
        Verifica se um sinal é duplicado
        
        Args:
            symbol (str): Símbolo do par (ex: BTCUSDT)
            signal_type (str): Tipo do sinal (LONG, SHORT, etc)
            entry_price (float): Preço de entrada
            timestamp (datetime): Timestamp do sinal (opcional)
            
        Returns:
            bool: True se for duplicado, False caso contrário
        """
        if timestamp is None:
            timestamp = datetime.now()
            
        key = f"{symbol}_{signal_type}"
        
        if key in self.recent_signals:
            last_signal = self.recent_signals[key]
            time_diff = timestamp - last_signal['timestamp']
            price_diff = abs(entry_price - last_signal['entry_price']) / last_signal['entry_price']
            
            # Considera duplicado se dentro de 5 minutos e variação de preço menor que 0.5%
            if time_diff.total_seconds() < 300 and price_diff < 0.005:
                logger.warning(f"Sinal duplicado detectado para {symbol}")
                return True
        
        # Registra o novo sinal
        self.recent_signals[key] = {
            'timestamp': timestamp,
            'entry_price': entry_price
        }
        return False
        
    def can_send_profit_update(self, symbol, profit_percentage, timestamp=None):
        """
        Verifica se pode enviar uma atualização de profit
        
        Args:
            symbol (str): Símbolo do par
            profit_percentage (float): Percentual de lucro
            timestamp (datetime): Timestamp do sinal (opcional)
            
        Returns:
            bool: True se pode enviar, False caso contrário
        """
        if timestamp is None:
            timestamp = datetime.now()
            
        if symbol not in self.profit_updates:
            self.profit_updates[symbol] = {
                'last_update': timestamp,
                'last_profit': profit_percentage
            }
            return True
            
        last_update = self.profit_updates[symbol]
        time_diff = timestamp - last_update['last_update']
        profit_diff = abs(profit_percentage - last_update['last_profit'])
        
        # Só atualiza se:
        # 1. Passou pelo menos 5 minutos desde a última atualização
        # 2. A diferença de profit é de pelo menos 20%
        if time_diff.total_seconds() >= 300 and profit_diff >= 20:
            self.profit_updates[symbol] = {
                'last_update': timestamp,
                'last_profit': profit_percentage
            }
            return True
            
        return False
        
    def cleanup_old_signals(self, max_age_minutes=60):
        """Remove sinais antigos da memória"""
        current_time = datetime.now()
        max_age = timedelta(minutes=max_age_minutes)
        
        # Limpa sinais de entrada
        for key in list(self.recent_signals.keys()):
            if current_time - self.recent_signals[key]['timestamp'] > max_age:
                del self.recent_signals[key]
                
        # Limpa atualizações de profit
        for symbol in list(self.profit_updates.keys()):
            if current_time - self.profit_updates[symbol]['last_update'] > max_age:
                del self.profit_updates[symbol] 