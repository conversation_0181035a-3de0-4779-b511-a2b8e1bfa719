{"ast": null, "code": "import Animated from \"react-native-web/dist/exports/Animated\";\nvar add = Animated.add,\n  multiply = Animated.multiply;\nexport default function conditional(condition, main, fallback) {\n  return add(multiply(condition, main), multiply(condition.interpolate({\n    inputRange: [0, 1],\n    outputRange: [1, 0]\n  }), fallback));\n}", "map": {"version": 3, "names": ["add", "Animated", "multiply", "conditional", "condition", "main", "fallback", "interpolate", "inputRange", "outputRange"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\@react-navigation\\stack\\src\\utils\\conditional.tsx"], "sourcesContent": ["import { Animated } from 'react-native';\n\nconst { add, multiply } = Animated;\n\n/**\n * Use an Animated Node based on a condition. Similar to Reanimated's `cond`.\n *\n * @param condition Animated Node representing the condition, must be 0 or 1, 1 means `true`, 0 means `false`\n * @param main Animated Node to use if the condition is `true`\n * @param fallback Animated Node to use if the condition is `false`\n */\nexport default function conditional(\n  condition: Animated.AnimatedInterpolation<0 | 1>,\n  main: Animated.AnimatedInterpolation<number>,\n  fallback: Animated.AnimatedInterpolation<number>\n) {\n  // To implement this behavior, we multiply the main node with the condition.\n  // So if condition is 0, result will be 0, and if condition is 1, result will be main node.\n  // Then we multiple reverse of the condition (0 if condition is 1) with the fallback.\n  // So if condition is 0, result will be fallback node, and if condition is 1, result will be 0,\n  // This way, one of them will always be 0, and other one will be the value we need.\n  // In the end we add them both together, 0 + value we need = value we need\n  return add(\n    multiply(condition, main),\n    multiply(\n      condition.interpolate({\n        inputRange: [0, 1],\n        outputRange: [1, 0],\n      }),\n      fallback\n    )\n  );\n}\n"], "mappings": ";AAEA,IAAQA,GAAG,GAAeC,QAAQ,CAA1BD,GAAG;EAAEE,QAAA,GAAaD,QAAQ,CAArBC,QAAA;AASb,eAAe,SAASC,WAAWA,CACjCC,SAAgD,EAChDC,IAA4C,EAC5CC,QAAgD,EAChD;EAOA,OAAON,GAAG,CACRE,QAAQ,CAACE,SAAS,EAAEC,IAAI,CAAC,EACzBH,QAAQ,CACNE,SAAS,CAACG,WAAW,CAAC;IACpBC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC;EACpB,CAAC,CAAC,EACFH,QAAQ,CACT,CACF;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}