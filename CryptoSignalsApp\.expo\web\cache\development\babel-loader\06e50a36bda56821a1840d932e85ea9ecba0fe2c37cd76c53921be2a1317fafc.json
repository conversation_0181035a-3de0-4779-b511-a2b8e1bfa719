{"ast": null, "code": "import PooledClass from \"../../vendor/react-native/PooledClass\";\nvar twoArgumentPooler = PooledClass.twoArgumentPooler;\nfunction Position(left, top) {\n  this.left = left;\n  this.top = top;\n}\nPosition.prototype.destructor = function () {\n  this.left = null;\n  this.top = null;\n};\nPooledClass.addPoolingTo(Position, twoArgumentPooler);\nexport default Position;", "map": {"version": 3, "names": ["PooledClass", "twoArgumentPooler", "Position", "left", "top", "prototype", "destructor", "addPoolingTo"], "sources": ["C:/Users/<USER>/Desktop/Signals/CryptoSignalsApp/node_modules/react-native-web/dist/exports/Touchable/Position.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport PooledClass from '../../vendor/react-native/PooledClass';\nvar twoArgumentPooler = PooledClass.twoArgumentPooler;\nfunction Position(left, top) {\n  this.left = left;\n  this.top = top;\n}\nPosition.prototype.destructor = function () {\n  this.left = null;\n  this.top = null;\n};\nPooledClass.addPoolingTo(Position, twoArgumentPooler);\nexport default Position;"], "mappings": "AASA,OAAOA,WAAW;AAClB,IAAIC,iBAAiB,GAAGD,WAAW,CAACC,iBAAiB;AACrD,SAASC,QAAQA,CAACC,IAAI,EAAEC,GAAG,EAAE;EAC3B,IAAI,CAACD,IAAI,GAAGA,IAAI;EAChB,IAAI,CAACC,GAAG,GAAGA,GAAG;AAChB;AACAF,QAAQ,CAACG,SAAS,CAACC,UAAU,GAAG,YAAY;EAC1C,IAAI,CAACH,IAAI,GAAG,IAAI;EAChB,IAAI,CAACC,GAAG,GAAG,IAAI;AACjB,CAAC;AACDJ,WAAW,CAACO,YAAY,CAACL,QAAQ,EAAED,iBAAiB,CAAC;AACrD,eAAeC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}