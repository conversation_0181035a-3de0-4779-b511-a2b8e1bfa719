{"ast": null, "code": "import * as React from 'react';\nvar UnhandledActionContext = React.createContext(undefined);\nexport default UnhandledActionContext;", "map": {"version": 3, "names": ["React", "UnhandledActionContext", "createContext", "undefined"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\@react-navigation\\core\\src\\UnhandledActionContext.tsx"], "sourcesContent": ["import type { NavigationAction } from '@react-navigation/routers';\nimport * as React from 'react';\n\nconst UnhandledActionContext = React.createContext<\n  ((action: NavigationAction) => void) | undefined\n>(undefined);\n\nexport default UnhandledActionContext;\n"], "mappings": "AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,IAAMC,sBAAsB,GAAGD,KAAK,CAACE,aAAa,CAEhDC,SAAS,CAAC;AAEZ,eAAeF,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}