{"ast": null, "code": "import StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nvar styles = StyleSheet.create({\n  container: {\n    display: 'flex',\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n    marginBottom: 3,\n    height: 48\n  },\n  title: {\n    color: '#fff',\n    fontSize: 24,\n    fontWeight: '500',\n    lineHeight: 32,\n    fontFamily: 'Poppins_500Medium'\n  },\n  backButton: {\n    width: 60,\n    height: 48,\n    display: 'flex',\n    flexDirection: 'row',\n    alignItems: 'center'\n  }\n});\nexport default styles;", "map": {"version": 3, "names": ["styles", "StyleSheet", "create", "container", "display", "flexDirection", "justifyContent", "alignItems", "marginBottom", "height", "title", "color", "fontSize", "fontWeight", "lineHeight", "fontFamily", "backButton", "width"], "sources": ["E:/CryptoSignalsApp/src/components/PageTitle/styles.js"], "sourcesContent": ["import { StyleSheet } from 'react-native';\r\n\r\nconst styles = StyleSheet.create({\r\n  container: {\r\n    display: 'flex',\r\n    flexDirection: 'row',\r\n    justifyContent: 'space-between',\r\n    alignItems: 'center',\r\n    marginBottom: 3,\r\n    height: 48,\r\n  },\r\n  title: {\r\n    color: '#fff',\r\n    fontSize: 24,\r\n    fontWeight: '500',\r\n    lineHeight: 32,\r\n    fontFamily: 'Poppins_500Medium'\r\n  },\r\n  backButton: {\r\n    width: 60,\r\n    height: 48,\r\n    display: 'flex',\r\n    flexDirection: 'row',\r\n    alignItems: 'center'\r\n  }\r\n});\r\n\r\nexport default styles\r\n"], "mappings": ";AAEA,IAAMA,MAAM,GAAGC,UAAU,CAACC,MAAM,CAAC;EAC/BC,SAAS,EAAE;IACTC,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE,eAAe;IAC/BC,UAAU,EAAE,QAAQ;IACpBC,YAAY,EAAE,CAAC;IACfC,MAAM,EAAE;EACV,CAAC;EACDC,KAAK,EAAE;IACLC,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,KAAK;IACjBC,UAAU,EAAE,EAAE;IACdC,UAAU,EAAE;EACd,CAAC;EACDC,UAAU,EAAE;IACVC,KAAK,EAAE,EAAE;IACTR,MAAM,EAAE,EAAE;IACVL,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,KAAK;IACpBE,UAAU,EAAE;EACd;AACF,CAAC,CAAC;AAEF,eAAeP,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}