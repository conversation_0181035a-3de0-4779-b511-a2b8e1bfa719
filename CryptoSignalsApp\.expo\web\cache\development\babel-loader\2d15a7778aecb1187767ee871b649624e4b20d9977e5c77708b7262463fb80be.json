{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"style\", \"theme\"],\n  _excluded2 = [\"elevation\", \"style\", \"backgroundColor\", \"testID\", \"children\", \"mode\", \"container\"],\n  _excluded3 = [\"elevation\", \"children\", \"theme\", \"style\", \"testID\", \"mode\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nimport * as React from 'react';\nimport Animated from \"react-native-web/dist/exports/Animated\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport { useInternalTheme } from \"../core/theming\";\nimport overlay, { isAnimatedValue } from \"../styles/overlay\";\nimport shadow from \"../styles/shadow\";\nimport { forwardRef } from \"../utils/forwardRef\";\nimport { splitStyles } from \"../utils/splitStyles\";\nvar MD2Surface = forwardRef(function (_ref, ref) {\n  var style = _ref.style,\n    overrideTheme = _ref.theme,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var _ref2 = StyleSheet.flatten(style) || {},\n    _ref2$elevation = _ref2.elevation,\n    elevation = _ref2$elevation === void 0 ? 4 : _ref2$elevation;\n  var _useInternalTheme = useInternalTheme(overrideTheme),\n    isDarkTheme = _useInternalTheme.dark,\n    mode = _useInternalTheme.mode,\n    colors = _useInternalTheme.colors;\n  return React.createElement(Animated.View, _extends({\n    ref: ref\n  }, rest, {\n    style: [{\n      backgroundColor: isDarkTheme && mode === 'adaptive' ? overlay(elevation, colors === null || colors === void 0 ? void 0 : colors.surface) : colors === null || colors === void 0 ? void 0 : colors.surface\n    }, elevation ? shadow(elevation) : null, style]\n  }));\n});\nvar outerLayerStyleProperties = ['position', 'alignSelf', 'top', 'right', 'bottom', 'left', 'start', 'end', 'flex', 'flexShrink', 'flexGrow', 'width', 'height', 'transform', 'opacity'];\nvar shadowColor = '#000';\nvar iOSShadowOutputRanges = [{\n  shadowOpacity: 0.15,\n  height: [0, 1, 2, 4, 6, 8],\n  shadowRadius: [0, 3, 6, 8, 10, 12]\n}, {\n  shadowOpacity: 0.3,\n  height: [0, 1, 1, 1, 2, 4],\n  shadowRadius: [0, 1, 2, 3, 3, 4]\n}];\nvar inputRange = [0, 1, 2, 3, 4, 5];\nfunction getStyleForShadowLayer(elevation, layer) {\n  if (isAnimatedValue(elevation)) {\n    return {\n      shadowColor: shadowColor,\n      shadowOpacity: elevation.interpolate({\n        inputRange: [0, 1],\n        outputRange: [0, iOSShadowOutputRanges[layer].shadowOpacity],\n        extrapolate: 'clamp'\n      }),\n      shadowOffset: {\n        width: 0,\n        height: elevation.interpolate({\n          inputRange: inputRange,\n          outputRange: iOSShadowOutputRanges[layer].height\n        })\n      },\n      shadowRadius: elevation.interpolate({\n        inputRange: inputRange,\n        outputRange: iOSShadowOutputRanges[layer].shadowRadius\n      })\n    };\n  }\n  return {\n    shadowColor: shadowColor,\n    shadowOpacity: elevation ? iOSShadowOutputRanges[layer].shadowOpacity : 0,\n    shadowOffset: {\n      width: 0,\n      height: iOSShadowOutputRanges[layer].height[elevation]\n    },\n    shadowRadius: iOSShadowOutputRanges[layer].shadowRadius[elevation]\n  };\n}\nvar SurfaceIOS = forwardRef(function (_ref3, ref) {\n  var elevation = _ref3.elevation,\n    style = _ref3.style,\n    backgroundColor = _ref3.backgroundColor,\n    testID = _ref3.testID,\n    children = _ref3.children,\n    _ref3$mode = _ref3.mode,\n    mode = _ref3$mode === void 0 ? 'elevated' : _ref3$mode,\n    container = _ref3.container,\n    props = _objectWithoutProperties(_ref3, _excluded2);\n  var _React$useMemo = React.useMemo(function () {\n      var flattenedStyles = StyleSheet.flatten(style) || {};\n      var _splitStyles = splitStyles(flattenedStyles, function (style) {\n          return outerLayerStyleProperties.includes(style) || style.startsWith('margin');\n        }, function (style) {\n          return style.startsWith('border') && style.endsWith('Radius');\n        }),\n        _splitStyles2 = _slicedToArray(_splitStyles, 3),\n        filteredStyles = _splitStyles2[0],\n        outerLayerStyles = _splitStyles2[1],\n        borderRadiusStyles = _splitStyles2[2];\n      if (process.env.NODE_ENV !== 'production' && filteredStyles.overflow === 'hidden' && elevation !== 0) {\n        console.warn('When setting overflow to hidden on Surface the shadow will not be displayed correctly. Wrap the content of your component in a separate View with the overflow style.');\n      }\n      var bgColor = flattenedStyles.backgroundColor || backgroundColor;\n      var isElevated = mode === 'elevated';\n      var outerLayerViewStyles = _objectSpread(_objectSpread(_objectSpread(_objectSpread({}, isElevated && getStyleForShadowLayer(elevation, 0)), outerLayerStyles), borderRadiusStyles), {}, {\n        backgroundColor: bgColor\n      });\n      var innerLayerViewStyles = _objectSpread(_objectSpread(_objectSpread(_objectSpread({}, isElevated && getStyleForShadowLayer(elevation, 1)), filteredStyles), borderRadiusStyles), {}, {\n        flex: flattenedStyles.height || !container && flattenedStyles.flex ? 1 : undefined,\n        backgroundColor: bgColor\n      });\n      return [outerLayerViewStyles, innerLayerViewStyles];\n    }, [style, elevation, backgroundColor, mode, container]),\n    _React$useMemo2 = _slicedToArray(_React$useMemo, 2),\n    outerLayerViewStyles = _React$useMemo2[0],\n    innerLayerViewStyles = _React$useMemo2[1];\n  return React.createElement(Animated.View, {\n    ref: ref,\n    style: outerLayerViewStyles,\n    testID: `${testID}-outer-layer`\n  }, React.createElement(Animated.View, _extends({}, props, {\n    style: innerLayerViewStyles,\n    testID: testID\n  }), children));\n});\nvar Surface = forwardRef(function (_ref4, ref) {\n  var _ref4$elevation = _ref4.elevation,\n    elevation = _ref4$elevation === void 0 ? 1 : _ref4$elevation,\n    children = _ref4.children,\n    overridenTheme = _ref4.theme,\n    style = _ref4.style,\n    _ref4$testID = _ref4.testID,\n    testID = _ref4$testID === void 0 ? 'surface' : _ref4$testID,\n    _ref4$mode = _ref4.mode,\n    mode = _ref4$mode === void 0 ? 'elevated' : _ref4$mode,\n    props = _objectWithoutProperties(_ref4, _excluded3);\n  var theme = useInternalTheme(overridenTheme);\n  if (!theme.isV3) return React.createElement(MD2Surface, _extends({}, props, {\n    theme: theme,\n    style: style,\n    ref: ref\n  }), children);\n  var colors = theme.colors;\n  var inputRange = [0, 1, 2, 3, 4, 5];\n  var backgroundColor = function (_colors$elevation2) {\n    if (isAnimatedValue(elevation)) {\n      return elevation.interpolate({\n        inputRange: inputRange,\n        outputRange: inputRange.map(function (elevation) {\n          var _colors$elevation;\n          return (_colors$elevation = colors.elevation) === null || _colors$elevation === void 0 ? void 0 : _colors$elevation[`level${elevation}`];\n        })\n      });\n    }\n    return (_colors$elevation2 = colors.elevation) === null || _colors$elevation2 === void 0 ? void 0 : _colors$elevation2[`level${elevation}`];\n  }();\n  var isElevated = mode === 'elevated';\n  if (Platform.OS === 'web') {\n    var _props$pointerEvents = props.pointerEvents,\n      pointerEvents = _props$pointerEvents === void 0 ? 'auto' : _props$pointerEvents;\n    return React.createElement(Animated.View, _extends({}, props, {\n      pointerEvents: pointerEvents,\n      ref: ref,\n      testID: testID,\n      style: [{\n        backgroundColor: backgroundColor\n      }, elevation && isElevated ? shadow(elevation, theme.isV3) : null, style]\n    }), children);\n  }\n  if (Platform.OS === 'android') {\n    var elevationLevel = [0, 3, 6, 9, 12, 15];\n    var getElevationAndroid = function getElevationAndroid() {\n      if (isAnimatedValue(elevation)) {\n        return elevation.interpolate({\n          inputRange: inputRange,\n          outputRange: elevationLevel\n        });\n      }\n      return elevationLevel[elevation];\n    };\n    var _ref5 = StyleSheet.flatten(style) || {},\n      margin = _ref5.margin,\n      padding = _ref5.padding,\n      transform = _ref5.transform,\n      borderRadius = _ref5.borderRadius;\n    var outerLayerStyles = {\n      margin: margin,\n      padding: padding,\n      transform: transform,\n      borderRadius: borderRadius\n    };\n    var sharedStyle = [{\n      backgroundColor: backgroundColor\n    }, style];\n    return React.createElement(Animated.View, _extends({}, props, {\n      testID: testID,\n      ref: ref,\n      style: [{\n        backgroundColor: backgroundColor,\n        transform: transform\n      }, outerLayerStyles, sharedStyle, isElevated && {\n        elevation: getElevationAndroid()\n      }]\n    }), children);\n  }\n  return React.createElement(SurfaceIOS, _extends({}, props, {\n    ref: ref,\n    elevation: elevation,\n    backgroundColor: backgroundColor,\n    style: style,\n    testID: testID,\n    mode: mode\n  }), children);\n});\nexport default Surface;", "map": {"version": 3, "names": ["React", "Animated", "Platform", "StyleSheet", "useInternalTheme", "overlay", "isAnimatedValue", "shadow", "forwardRef", "splitStyles", "MD2Surface", "_ref", "ref", "style", "overrideTheme", "theme", "rest", "_objectWithoutProperties", "_excluded", "_ref2", "flatten", "_ref2$elevation", "elevation", "_useInternalTheme", "isDarkTheme", "dark", "mode", "colors", "createElement", "View", "_extends", "backgroundColor", "surface", "outerLayerStyleProperties", "shadowColor", "iOSShadowOutputRanges", "shadowOpacity", "height", "shadowRadius", "inputRange", "getStyleForShadowLayer", "layer", "interpolate", "outputRange", "extrapolate", "shadowOffset", "width", "SurfaceIOS", "_ref3", "testID", "children", "_ref3$mode", "container", "props", "_excluded2", "_React$useMemo", "useMemo", "flattenedStyles", "_splitStyles", "includes", "startsWith", "endsWith", "_splitStyles2", "_slicedToArray", "filteredStyles", "outerLayerStyles", "borderRadiusStyles", "process", "env", "NODE_ENV", "overflow", "console", "warn", "bgColor", "isElevated", "outerLayerViewStyles", "_objectSpread", "innerLayerViewStyles", "flex", "undefined", "_React$useMemo2", "Surface", "_ref4", "_ref4$elevation", "overridenTheme", "_ref4$testID", "_ref4$mode", "_excluded3", "isV3", "_colors$elevation2", "map", "_colors$elevation", "OS", "_props$pointerEvents", "pointerEvents", "elevationLevel", "getElevationAndroid", "_ref5", "margin", "padding", "transform", "borderRadius", "sharedStyle"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\Surface.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  Animated,\n  Platform,\n  ShadowStyleIOS,\n  StyleProp,\n  StyleSheet,\n  View,\n  ViewStyle,\n} from 'react-native';\n\nimport { useInternalTheme } from '../core/theming';\nimport overlay, { isAnimatedValue } from '../styles/overlay';\nimport shadow from '../styles/shadow';\nimport type { ThemeProp, MD3Elevation } from '../types';\nimport { forwardRef } from '../utils/forwardRef';\nimport { splitStyles } from '../utils/splitStyles';\n\ntype Elevation = 0 | 1 | 2 | 3 | 4 | 5 | Animated.Value;\n\nexport type Props = Omit<React.ComponentPropsWithRef<typeof View>, 'style'> & {\n  /**\n   * Content of the `Surface`.\n   */\n  children: React.ReactNode;\n  style?: Animated.WithAnimatedValue<StyleProp<ViewStyle>>;\n  /**\n   * @supported Available in v5.x with theme version 3\n   * Changes shadows and background on iOS and Android.\n   * Used to create UI hierarchy between components.\n   *\n   * Note: If `mode` is set to `flat`, Surface doesn't have a shadow.\n   *\n   * Note: In version 2 the `elevation` prop was accepted via `style` prop i.e. `style={{ elevation: 4 }}`.\n   * It's no longer supported with theme version 3 and you should use `elevation` property instead.\n   */\n  elevation?: Elevation;\n  /**\n   * @supported Available in v5.x with theme version 3\n   * Mode of the Surface.\n   * - `elevated` - Surface with a shadow and background color corresponding to set `elevation` value.\n   * - `flat` - Surface without a shadow, with the background color corresponding to set `elevation` value.\n   */\n  mode?: 'flat' | 'elevated';\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n  /**\n   * TestID used for testing purposes\n   */\n  testID?: string;\n  ref?: React.RefObject<View>;\n  /**\n   * @internal\n   */\n  container?: boolean;\n};\n\nconst MD2Surface = forwardRef<View, Props>(\n  ({ style, theme: overrideTheme, ...rest }: Omit<Props, 'elevation'>, ref) => {\n    const { elevation = 4 } = (StyleSheet.flatten(style) || {}) as ViewStyle;\n    const { dark: isDarkTheme, mode, colors } = useInternalTheme(overrideTheme);\n\n    return (\n      <Animated.View\n        ref={ref}\n        {...rest}\n        style={[\n          {\n            backgroundColor:\n              isDarkTheme && mode === 'adaptive'\n                ? overlay(elevation, colors?.surface)\n                : colors?.surface,\n          },\n          elevation ? shadow(elevation) : null,\n          style,\n        ]}\n      />\n    );\n  }\n);\n\nconst outerLayerStyleProperties: (keyof ViewStyle)[] = [\n  'position',\n  'alignSelf',\n  'top',\n  'right',\n  'bottom',\n  'left',\n  'start',\n  'end',\n  'flex',\n  'flexShrink',\n  'flexGrow',\n  'width',\n  'height',\n  'transform',\n  'opacity',\n];\n\nconst shadowColor = '#000';\nconst iOSShadowOutputRanges = [\n  {\n    shadowOpacity: 0.15,\n    height: [0, 1, 2, 4, 6, 8],\n    shadowRadius: [0, 3, 6, 8, 10, 12],\n  },\n  {\n    shadowOpacity: 0.3,\n    height: [0, 1, 1, 1, 2, 4],\n    shadowRadius: [0, 1, 2, 3, 3, 4],\n  },\n];\nconst inputRange = [0, 1, 2, 3, 4, 5];\nfunction getStyleForShadowLayer(\n  elevation: Elevation,\n  layer: 0 | 1\n): Animated.WithAnimatedValue<ShadowStyleIOS> {\n  if (isAnimatedValue(elevation)) {\n    return {\n      shadowColor,\n      shadowOpacity: elevation.interpolate({\n        inputRange: [0, 1],\n        outputRange: [0, iOSShadowOutputRanges[layer].shadowOpacity],\n        extrapolate: 'clamp',\n      }),\n      shadowOffset: {\n        width: 0,\n        height: elevation.interpolate({\n          inputRange,\n          outputRange: iOSShadowOutputRanges[layer].height,\n        }),\n      },\n      shadowRadius: elevation.interpolate({\n        inputRange,\n        outputRange: iOSShadowOutputRanges[layer].shadowRadius,\n      }),\n    };\n  }\n\n  return {\n    shadowColor,\n    shadowOpacity: elevation ? iOSShadowOutputRanges[layer].shadowOpacity : 0,\n    shadowOffset: {\n      width: 0,\n      height: iOSShadowOutputRanges[layer].height[elevation],\n    },\n    shadowRadius: iOSShadowOutputRanges[layer].shadowRadius[elevation],\n  };\n}\n\nconst SurfaceIOS = forwardRef<\n  View,\n  Omit<Props, 'elevation'> & {\n    elevation: Elevation;\n    backgroundColor?: string | Animated.AnimatedInterpolation<string | number>;\n  }\n>(\n  (\n    {\n      elevation,\n      style,\n      backgroundColor,\n      testID,\n      children,\n      mode = 'elevated',\n      container,\n      ...props\n    },\n    ref\n  ) => {\n    const [outerLayerViewStyles, innerLayerViewStyles] = React.useMemo(() => {\n      const flattenedStyles = (StyleSheet.flatten(style) || {}) as ViewStyle;\n\n      const [filteredStyles, outerLayerStyles, borderRadiusStyles] =\n        splitStyles(\n          flattenedStyles,\n          (style) =>\n            outerLayerStyleProperties.includes(style) ||\n            style.startsWith('margin'),\n          (style) => style.startsWith('border') && style.endsWith('Radius')\n        );\n\n      if (\n        process.env.NODE_ENV !== 'production' &&\n        filteredStyles.overflow === 'hidden' &&\n        elevation !== 0\n      ) {\n        console.warn(\n          'When setting overflow to hidden on Surface the shadow will not be displayed correctly. Wrap the content of your component in a separate View with the overflow style.'\n        );\n      }\n\n      const bgColor = flattenedStyles.backgroundColor || backgroundColor;\n\n      const isElevated = mode === 'elevated';\n\n      const outerLayerViewStyles = {\n        ...(isElevated && getStyleForShadowLayer(elevation, 0)),\n        ...outerLayerStyles,\n        ...borderRadiusStyles,\n        backgroundColor: bgColor,\n      };\n\n      const innerLayerViewStyles = {\n        ...(isElevated && getStyleForShadowLayer(elevation, 1)),\n        ...filteredStyles,\n        ...borderRadiusStyles,\n        flex:\n          flattenedStyles.height || (!container && flattenedStyles.flex)\n            ? 1\n            : undefined,\n        backgroundColor: bgColor,\n      };\n\n      return [outerLayerViewStyles, innerLayerViewStyles];\n    }, [style, elevation, backgroundColor, mode, container]);\n\n    return (\n      <Animated.View\n        ref={ref}\n        style={outerLayerViewStyles}\n        testID={`${testID}-outer-layer`}\n      >\n        <Animated.View {...props} style={innerLayerViewStyles} testID={testID}>\n          {children}\n        </Animated.View>\n      </Animated.View>\n    );\n  }\n);\n\n/**\n * Surface is a basic container that can give depth to an element with elevation shadow.\n * On dark theme with `adaptive` mode, surface is constructed by also placing a semi-transparent white overlay over a component surface.\n * See [Dark Theme](https://callstack.github.io/react-native-paper/docs/guides/theming#dark-theme) for more information.\n * Overlay and shadow can be applied by specifying the `elevation` property both on Android and iOS.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { Surface, Text } from 'react-native-paper';\n * import { StyleSheet } from 'react-native';\n *\n * const MyComponent = () => (\n *   <Surface style={styles.surface} elevation={4}>\n *      <Text>Surface</Text>\n *   </Surface>\n * );\n *\n * export default MyComponent;\n *\n * const styles = StyleSheet.create({\n *   surface: {\n *     padding: 8,\n *     height: 80,\n *     width: 80,\n *     alignItems: 'center',\n *     justifyContent: 'center',\n *   },\n * });\n * ```\n */\nconst Surface = forwardRef<View, Props>(\n  (\n    {\n      elevation = 1,\n      children,\n      theme: overridenTheme,\n      style,\n      testID = 'surface',\n      mode = 'elevated',\n      ...props\n    }: Props,\n    ref\n  ) => {\n    const theme = useInternalTheme(overridenTheme);\n\n    if (!theme.isV3)\n      return (\n        <MD2Surface {...props} theme={theme} style={style} ref={ref}>\n          {children}\n        </MD2Surface>\n      );\n\n    const { colors } = theme;\n\n    const inputRange = [0, 1, 2, 3, 4, 5];\n\n    const backgroundColor = (() => {\n      if (isAnimatedValue(elevation)) {\n        return elevation.interpolate({\n          inputRange,\n          outputRange: inputRange.map((elevation) => {\n            return colors.elevation?.[`level${elevation as MD3Elevation}`];\n          }),\n        });\n      }\n\n      return colors.elevation?.[`level${elevation}`];\n    })();\n\n    const isElevated = mode === 'elevated';\n\n    if (Platform.OS === 'web') {\n      const { pointerEvents = 'auto' } = props;\n      return (\n        <Animated.View\n          {...props}\n          pointerEvents={pointerEvents}\n          ref={ref}\n          testID={testID}\n          style={[\n            { backgroundColor },\n            elevation && isElevated ? shadow(elevation, theme.isV3) : null,\n            style,\n          ]}\n        >\n          {children}\n        </Animated.View>\n      );\n    }\n\n    if (Platform.OS === 'android') {\n      const elevationLevel = [0, 3, 6, 9, 12, 15];\n\n      const getElevationAndroid = () => {\n        if (isAnimatedValue(elevation)) {\n          return elevation.interpolate({\n            inputRange,\n            outputRange: elevationLevel,\n          });\n        }\n\n        return elevationLevel[elevation];\n      };\n\n      const { margin, padding, transform, borderRadius } = (StyleSheet.flatten(\n        style\n      ) || {}) as ViewStyle;\n\n      const outerLayerStyles = { margin, padding, transform, borderRadius };\n      const sharedStyle = [{ backgroundColor }, style];\n\n      return (\n        <Animated.View\n          {...props}\n          testID={testID}\n          ref={ref}\n          style={[\n            {\n              backgroundColor,\n              transform,\n            },\n            outerLayerStyles,\n            sharedStyle,\n            isElevated && {\n              elevation: getElevationAndroid(),\n            },\n          ]}\n        >\n          {children}\n        </Animated.View>\n      );\n    }\n\n    return (\n      <SurfaceIOS\n        {...props}\n        ref={ref}\n        elevation={elevation}\n        backgroundColor={backgroundColor}\n        style={style}\n        testID={testID}\n        mode={mode}\n      >\n        {children}\n      </SurfaceIOS>\n    );\n  }\n);\n\nexport default Surface;\n"], "mappings": ";;;;;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAAA,OAAAC,QAAA;AAAA,OAAAC,UAAA;AAW9B,SAASC,gBAAgB;AACzB,OAAOC,OAAO,IAAIC,eAAe;AACjC,OAAOC,MAAM;AAEb,SAASC,UAAU;AACnB,SAASC,WAAW;AA2CpB,IAAMC,UAAU,GAAGF,UAAU,CAC3B,UAAAG,IAAA,EAAqEC,GAAG,EAAK;EAAA,IAA1EC,KAAK,GAAAF,IAAA,CAALE,KAAK;IAASC,aAAa,GAAAH,IAAA,CAApBI,KAAK;IAAoBC,IAAA,GAAAC,wBAAA,CAAAN,IAAA,EAAAO,SAAA;EACjC,IAAAC,KAAA,GAA2BhB,UAAU,CAACiB,OAAO,CAACP,KAAK,CAAC,IAAI,CAAC,CAAe;IAAAQ,eAAA,GAAAF,KAAA,CAAhEG,SAAS;IAATA,SAAS,GAAAD,eAAA,cAAG,IAAAA,eAAA;EACpB,IAAAE,iBAAA,GAA4CnB,gBAAgB,CAACU,aAAa,CAAC;IAA7DU,WAAW,GAAAD,iBAAA,CAAjBE,IAAI;IAAeC,IAAI,GAAAH,iBAAA,CAAJG,IAAI;IAAEC,MAAA,GAAAJ,iBAAA,CAAAI,MAAA;EAEjC,OACE3B,KAAA,CAAA4B,aAAA,CAAC3B,QAAQ,CAAC4B,IAAI,EAAAC,QAAA;IACZlB,GAAG,EAAEA;EAAI,GACLI,IAAI;IACRH,KAAK,EAAE,CACL;MACEkB,eAAe,EACbP,WAAW,IAAIE,IAAI,KAAK,UAAU,GAC9BrB,OAAO,CAACiB,SAAS,EAAEK,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEK,OAAO,CAAC,GACnCL,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEK;IAChB,CAAC,EACDV,SAAS,GAAGf,MAAM,CAACe,SAAS,CAAC,GAAG,IAAI,EACpCT,KAAK;EACL,EACH,CAAC;AAEN,CACF,CAAC;AAED,IAAMoB,yBAA8C,GAAG,CACrD,UAAU,EACV,WAAW,EACX,KAAK,EACL,OAAO,EACP,QAAQ,EACR,MAAM,EACN,OAAO,EACP,KAAK,EACL,MAAM,EACN,YAAY,EACZ,UAAU,EACV,OAAO,EACP,QAAQ,EACR,WAAW,EACX,SAAS,CACV;AAED,IAAMC,WAAW,GAAG,MAAM;AAC1B,IAAMC,qBAAqB,GAAG,CAC5B;EACEC,aAAa,EAAE,IAAI;EACnBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC1BC,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE;AACnC,CAAC,EACD;EACEF,aAAa,EAAE,GAAG;EAClBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC1BC,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AACjC,CAAC,CACF;AACD,IAAMC,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACrC,SAASC,sBAAsBA,CAC7BlB,SAAoB,EACpBmB,KAAY,EACgC;EAC5C,IAAInC,eAAe,CAACgB,SAAS,CAAC,EAAE;IAC9B,OAAO;MACLY,WAAW,EAAXA,WAAW;MACXE,aAAa,EAAEd,SAAS,CAACoB,WAAW,CAAC;QACnCH,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QAClBI,WAAW,EAAE,CAAC,CAAC,EAAER,qBAAqB,CAACM,KAAK,CAAC,CAACL,aAAa,CAAC;QAC5DQ,WAAW,EAAE;MACf,CAAC,CAAC;MACFC,YAAY,EAAE;QACZC,KAAK,EAAE,CAAC;QACRT,MAAM,EAAEf,SAAS,CAACoB,WAAW,CAAC;UAC5BH,UAAU,EAAVA,UAAU;UACVI,WAAW,EAAER,qBAAqB,CAACM,KAAK,CAAC,CAACJ;QAC5C,CAAC;MACH,CAAC;MACDC,YAAY,EAAEhB,SAAS,CAACoB,WAAW,CAAC;QAClCH,UAAU,EAAVA,UAAU;QACVI,WAAW,EAAER,qBAAqB,CAACM,KAAK,CAAC,CAACH;MAC5C,CAAC;IACH,CAAC;EACH;EAEA,OAAO;IACLJ,WAAW,EAAXA,WAAW;IACXE,aAAa,EAAEd,SAAS,GAAGa,qBAAqB,CAACM,KAAK,CAAC,CAACL,aAAa,GAAG,CAAC;IACzES,YAAY,EAAE;MACZC,KAAK,EAAE,CAAC;MACRT,MAAM,EAAEF,qBAAqB,CAACM,KAAK,CAAC,CAACJ,MAAM,CAACf,SAAS;IACvD,CAAC;IACDgB,YAAY,EAAEH,qBAAqB,CAACM,KAAK,CAAC,CAACH,YAAY,CAAChB,SAAS;EACnE,CAAC;AACH;AAEA,IAAMyB,UAAU,GAAGvC,UAAU,CAO3B,UAAAwC,KAAA,EAWEpC,GAAG,EACA;EAAA,IAVDU,SAAS,GAAA0B,KAAA,CAAT1B,SAAS;IACTT,KAAK,GAAAmC,KAAA,CAALnC,KAAK;IACLkB,eAAe,GAAAiB,KAAA,CAAfjB,eAAe;IACfkB,MAAM,GAAAD,KAAA,CAANC,MAAM;IACNC,QAAQ,GAAAF,KAAA,CAARE,QAAQ;IAAAC,UAAA,GAAAH,KAAA,CACRtB,IAAI;IAAJA,IAAI,GAAAyB,UAAA,cAAG,UAAU,GAAAA,UAAA;IACjBC,SAAS,GAAAJ,KAAA,CAATI,SAAS;IACNC,KAAA,GAAApC,wBAAA,CAAA+B,KAAA,EAAAM,UAAA;EAIL,IAAAC,cAAA,GAAqDvD,KAAK,CAACwD,OAAO,CAAC,YAAM;MACvE,IAAMC,eAAe,GAAItD,UAAU,CAACiB,OAAO,CAACP,KAAK,CAAC,IAAI,CAAC,CAAe;MAEtE,IAAA6C,YAAA,GACEjD,WAAW,CACTgD,eAAe,EACd,UAAA5C,KAAK;UAAA,OACJoB,yBAAyB,CAAC0B,QAAQ,CAAC9C,KAAK,CAAC,IACzCA,KAAK,CAAC+C,UAAU,CAAC,QAAQ,CAAC;QAAA,GAC3B,UAAA/C,KAAK;UAAA,OAAKA,KAAK,CAAC+C,UAAU,CAAC,QAAQ,CAAC,IAAI/C,KAAK,CAACgD,QAAQ,CAAC,QAAQ,CAClE;QAAA,EAAC;QAAAC,aAAA,GAAAC,cAAA,CAAAL,YAAA;QAPIM,cAAc,GAAAF,aAAA;QAAEG,gBAAgB,GAAAH,aAAA;QAAEI,kBAAkB,GAAAJ,aAAA;MAS3D,IACEK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACrCL,cAAc,CAACM,QAAQ,KAAK,QAAQ,IACpChD,SAAS,KAAK,CAAC,EACf;QACAiD,OAAO,CAACC,IAAI,CACV,uKACF,CAAC;MACH;MAEA,IAAMC,OAAO,GAAGhB,eAAe,CAAC1B,eAAe,IAAIA,eAAe;MAElE,IAAM2C,UAAU,GAAGhD,IAAI,KAAK,UAAU;MAEtC,IAAMiD,oBAAoB,GAAAC,aAAA,CAAAA,aAAA,CAAAA,aAAA,CAAAA,aAAA,KACpBF,UAAU,IAAIlC,sBAAsB,CAAClB,SAAS,EAAE,CAAC,CAAC,GACnD2C,gBAAgB,GAChBC,kBAAkB;QACrBnC,eAAe,EAAE0C;MAAA,EAClB;MAED,IAAMI,oBAAoB,GAAAD,aAAA,CAAAA,aAAA,CAAAA,aAAA,CAAAA,aAAA,KACpBF,UAAU,IAAIlC,sBAAsB,CAAClB,SAAS,EAAE,CAAC,CAAC,GACnD0C,cAAc,GACdE,kBAAkB;QACrBY,IAAI,EACFrB,eAAe,CAACpB,MAAM,IAAK,CAACe,SAAS,IAAIK,eAAe,CAACqB,IAAK,GAC1D,CAAC,GACDC,SAAS;QACfhD,eAAe,EAAE0C;MAAA,EAClB;MAED,OAAO,CAACE,oBAAoB,EAAEE,oBAAoB,CAAC;IACrD,CAAC,EAAE,CAAChE,KAAK,EAAES,SAAS,EAAES,eAAe,EAAEL,IAAI,EAAE0B,SAAS,CAAC,CAAC;IAAA4B,eAAA,GAAAjB,cAAA,CAAAR,cAAA;IA7CjDoB,oBAAoB,GAAAK,eAAA;IAAEH,oBAAoB,GAAAG,eAAA;EA+CjD,OACEhF,KAAA,CAAA4B,aAAA,CAAC3B,QAAQ,CAAC4B,IAAI;IACZjB,GAAG,EAAEA,GAAI;IACTC,KAAK,EAAE8D,oBAAqB;IAC5B1B,MAAM,EAAE,GAAGA,MAAM;EAAe,GAEhCjD,KAAA,CAAA4B,aAAA,CAAC3B,QAAQ,CAAC4B,IAAI,EAAAC,QAAA,KAAKuB,KAAK;IAAExC,KAAK,EAAEgE,oBAAqB;IAAC5B,MAAM,EAAEA;EAAO,IACnEC,QACY,CACF,CAAC;AAEpB,CACF,CAAC;AAiCD,IAAM+B,OAAO,GAAGzE,UAAU,CACxB,UAAA0E,KAAA,EAUEtE,GAAG,EACA;EAAA,IAAAuE,eAAA,GAAAD,KAAA,CATD5D,SAAS;IAATA,SAAS,GAAA6D,eAAA,cAAG,CAAC,GAAAA,eAAA;IACbjC,QAAQ,GAAAgC,KAAA,CAARhC,QAAQ;IACDkC,cAAc,GAAAF,KAAA,CAArBnE,KAAK;IACLF,KAAK,GAAAqE,KAAA,CAALrE,KAAK;IAAAwE,YAAA,GAAAH,KAAA,CACLjC,MAAM;IAANA,MAAM,GAAAoC,YAAA,cAAG,SAAS,GAAAA,YAAA;IAAAC,UAAA,GAAAJ,KAAA,CAClBxD,IAAI;IAAJA,IAAI,GAAA4D,UAAA,cAAG,UAAU,GAAAA,UAAA;IACdjC,KAAA,GAAApC,wBAAA,CAAAiE,KAAA,EAAAK,UAAA;EAIL,IAAMxE,KAAK,GAAGX,gBAAgB,CAACgF,cAAc,CAAC;EAE9C,IAAI,CAACrE,KAAK,CAACyE,IAAI,EACb,OACExF,KAAA,CAAA4B,aAAA,CAAClB,UAAU,EAAAoB,QAAA,KAAKuB,KAAK;IAAEtC,KAAK,EAAEA,KAAM;IAACF,KAAK,EAAEA,KAAM;IAACD,GAAG,EAAEA;EAAI,IACzDsC,QACS,CAAC;EAGjB,IAAQvB,MAAA,GAAWZ,KAAK,CAAhBY,MAAA;EAER,IAAMY,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAErC,IAAMR,eAAe,GAAI,UAAA0D,kBAAA,EAAM;IAC7B,IAAInF,eAAe,CAACgB,SAAS,CAAC,EAAE;MAC9B,OAAOA,SAAS,CAACoB,WAAW,CAAC;QAC3BH,UAAU,EAAVA,UAAU;QACVI,WAAW,EAAEJ,UAAU,CAACmD,GAAG,CAAE,UAAApE,SAAS,EAAK;UAAA,IAAAqE,iBAAA;UACzC,QAAAA,iBAAA,GAAOhE,MAAM,CAACL,SAAS,cAAAqE,iBAAA,uBAAhBA,iBAAA,CAAmB,QAAQrE,SAAS,EAAkB,CAAC;QAChE,CAAC;MACH,CAAC,CAAC;IACJ;IAEA,QAAAmE,kBAAA,GAAO9D,MAAM,CAACL,SAAS,cAAAmE,kBAAA,uBAAhBA,kBAAA,CAAmB,QAAQnE,SAAS,EAAE,CAAC;EAChD,CAAC,CAAE,CAAC;EAEJ,IAAMoD,UAAU,GAAGhD,IAAI,KAAK,UAAU;EAEtC,IAAIxB,QAAQ,CAAC0F,EAAE,KAAK,KAAK,EAAE;IACzB,IAAAC,oBAAA,GAAmCxC,KAAK,CAAhCyC,aAAa;MAAbA,aAAa,GAAAD,oBAAA,cAAG,SAAAA,oBAAA;IACxB,OACE7F,KAAA,CAAA4B,aAAA,CAAC3B,QAAQ,CAAC4B,IAAI,EAAAC,QAAA,KACRuB,KAAK;MACTyC,aAAa,EAAEA,aAAc;MAC7BlF,GAAG,EAAEA,GAAI;MACTqC,MAAM,EAAEA,MAAO;MACfpC,KAAK,EAAE,CACL;QAAEkB,eAAA,EAAAA;MAAgB,CAAC,EACnBT,SAAS,IAAIoD,UAAU,GAAGnE,MAAM,CAACe,SAAS,EAAEP,KAAK,CAACyE,IAAI,CAAC,GAAG,IAAI,EAC9D3E,KAAK;IACL,IAEDqC,QACY,CAAC;EAEpB;EAEA,IAAIhD,QAAQ,CAAC0F,EAAE,KAAK,SAAS,EAAE;IAC7B,IAAMG,cAAc,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;IAE3C,IAAMC,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAA,EAAS;MAChC,IAAI1F,eAAe,CAACgB,SAAS,CAAC,EAAE;QAC9B,OAAOA,SAAS,CAACoB,WAAW,CAAC;UAC3BH,UAAU,EAAVA,UAAU;UACVI,WAAW,EAAEoD;QACf,CAAC,CAAC;MACJ;MAEA,OAAOA,cAAc,CAACzE,SAAS,CAAC;IAClC,CAAC;IAED,IAAA2E,KAAA,GAAsD9F,UAAU,CAACiB,OAAO,CACtEP,KACF,CAAC,IAAI,CAAC,CAAe;MAFbqF,MAAM,GAAAD,KAAA,CAANC,MAAM;MAAEC,OAAO,GAAAF,KAAA,CAAPE,OAAO;MAAEC,SAAS,GAAAH,KAAA,CAATG,SAAS;MAAEC,YAAA,GAAAJ,KAAA,CAAAI,YAAA;IAIpC,IAAMpC,gBAAgB,GAAG;MAAEiC,MAAM,EAANA,MAAM;MAAEC,OAAO,EAAPA,OAAO;MAAEC,SAAS,EAATA,SAAS;MAAEC,YAAA,EAAAA;IAAa,CAAC;IACrE,IAAMC,WAAW,GAAG,CAAC;MAAEvE,eAAA,EAAAA;IAAgB,CAAC,EAAElB,KAAK,CAAC;IAEhD,OACEb,KAAA,CAAA4B,aAAA,CAAC3B,QAAQ,CAAC4B,IAAI,EAAAC,QAAA,KACRuB,KAAK;MACTJ,MAAM,EAAEA,MAAO;MACfrC,GAAG,EAAEA,GAAI;MACTC,KAAK,EAAE,CACL;QACEkB,eAAe,EAAfA,eAAe;QACfqE,SAAA,EAAAA;MACF,CAAC,EACDnC,gBAAgB,EAChBqC,WAAW,EACX5B,UAAU,IAAI;QACZpD,SAAS,EAAE0E,mBAAmB,CAAC;MACjC,CAAC;IACD,IAED9C,QACY,CAAC;EAEpB;EAEA,OACElD,KAAA,CAAA4B,aAAA,CAACmB,UAAU,EAAAjB,QAAA,KACLuB,KAAK;IACTzC,GAAG,EAAEA,GAAI;IACTU,SAAS,EAAEA,SAAU;IACrBS,eAAe,EAAEA,eAAgB;IACjClB,KAAK,EAAEA,KAAM;IACboC,MAAM,EAAEA,MAAO;IACfvB,IAAI,EAAEA;EAAK,IAEVwB,QACS,CAAC;AAEjB,CACF,CAAC;AAED,eAAe+B,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}