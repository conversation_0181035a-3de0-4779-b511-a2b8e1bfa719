{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport React, { useState, useEffect } from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport TouchableOpacity from \"react-native-web/dist/exports/TouchableOpacity\";\nimport Alert from \"react-native-web/dist/exports/Alert\";\nimport Clipboard from \"react-native-web/dist/exports/Clipboard\";\nimport Linking from \"react-native-web/dist/exports/Linking\";\nimport { But<PERSON>, Card, Chip, ProgressBar } from 'react-native-paper';\nimport QRCode from 'react-native-qrcode-svg';\nimport paymentService from \"../../services/PaymentService\";\nimport { PAYMENT_CONFIG } from \"../../config/api\";\nimport styles from \"./styles\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar USDTPayment = function USDTPayment(_ref) {\n  var planId = _ref.planId,\n    onPaymentSuccess = _ref.onPaymentSuccess,\n    onPaymentCancel = _ref.onPaymentCancel,\n    _ref$visible = _ref.visible,\n    visible = _ref$visible === void 0 ? true : _ref$visible;\n  var _useState = useState(null),\n    _useState2 = _slicedToArray(_useState, 2),\n    paymentSession = _useState2[0],\n    setPaymentSession = _useState2[1];\n  var _useState3 = useState('BSC'),\n    _useState4 = _slicedToArray(_useState3, 2),\n    selectedNetwork = _useState4[0],\n    setSelectedNetwork = _useState4[1];\n  var _useState5 = useState(false),\n    _useState6 = _slicedToArray(_useState5, 2),\n    isLoading = _useState6[0],\n    setIsLoading = _useState6[1];\n  var _useState7 = useState(0),\n    _useState8 = _slicedToArray(_useState7, 2),\n    timeRemaining = _useState8[0],\n    setTimeRemaining = _useState8[1];\n  var _useState9 = useState('pending'),\n    _useState0 = _slicedToArray(_useState9, 2),\n    paymentStatus = _useState0[0],\n    setPaymentStatus = _useState0[1];\n  useEffect(function () {\n    if (visible && planId) {\n      initializePayment();\n    }\n  }, [visible, planId]);\n  useEffect(function () {\n    var interval;\n    if (paymentSession && paymentStatus === 'pending') {\n      interval = setInterval(checkPaymentStatus, 10000);\n    }\n    return function () {\n      if (interval) clearInterval(interval);\n    };\n  }, [paymentSession, paymentStatus]);\n  useEffect(function () {\n    var timer;\n    if (paymentSession && timeRemaining > 0) {\n      timer = setTimeout(function () {\n        setTimeRemaining(function (prev) {\n          return prev - 1;\n        });\n      }, 1000);\n    } else if (timeRemaining === 0 && paymentSession) {\n      setPaymentStatus('expired');\n    }\n    return function () {\n      if (timer) clearTimeout(timer);\n    };\n  }, [timeRemaining, paymentSession]);\n  var initializePayment = function () {\n    var _ref2 = _asyncToGenerator(function* () {\n      try {\n        setIsLoading(true);\n        var session = yield paymentService.createPaymentSession(planId);\n        setPaymentSession(session);\n        var expiresAt = new Date(session.expiresAt);\n        var now = new Date();\n        var remaining = Math.max(0, Math.floor((expiresAt - now) / 1000));\n        setTimeRemaining(remaining);\n      } catch (error) {\n        console.error('Erro ao inicializar pagamento:', error);\n        Alert.alert('Erro', 'Não foi possível inicializar o pagamento. Tente novamente.');\n      } finally {\n        setIsLoading(false);\n      }\n    });\n    return function initializePayment() {\n      return _ref2.apply(this, arguments);\n    };\n  }();\n  var checkPaymentStatus = function () {\n    var _ref3 = _asyncToGenerator(function* () {\n      if (!paymentSession) return;\n      try {\n        var updatedSession = yield paymentService.checkPaymentStatus(paymentSession.id);\n        setPaymentSession(updatedSession);\n        setPaymentStatus(updatedSession.status);\n        if (updatedSession.status === 'confirmed') {\n          Alert.alert('Pagamento Confirmado! 🎉', 'Seu plano foi ativado com sucesso!', [{\n            text: 'OK',\n            onPress: function onPress() {\n              return onPaymentSuccess(updatedSession);\n            }\n          }]);\n        } else if (updatedSession.status === 'expired') {\n          Alert.alert('Pagamento Expirado', 'O tempo para pagamento expirou. Inicie um novo pagamento.', [{\n            text: 'OK',\n            onPress: onPaymentCancel\n          }]);\n        }\n      } catch (error) {\n        console.error('Erro ao verificar status:', error);\n      }\n    });\n    return function checkPaymentStatus() {\n      return _ref3.apply(this, arguments);\n    };\n  }();\n  var copyToClipboard = function copyToClipboard(text, label) {\n    Clipboard.setString(text);\n    Alert.alert('Copiado!', `${label} copiado para a área de transferência.`);\n  };\n  var openExplorer = function openExplorer(network, address) {\n    var networkConfig = PAYMENT_CONFIG.SUPPORTED_NETWORKS[network];\n    var url = `${networkConfig.explorerUrl}/address/${address}`;\n    Linking.openURL(url);\n  };\n  var formatTime = function formatTime(seconds) {\n    var minutes = Math.floor(seconds / 60);\n    var secs = seconds % 60;\n    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n  };\n  var getStatusColor = function getStatusColor() {\n    switch (paymentStatus) {\n      case 'pending':\n        return '#FF9800';\n      case 'confirming':\n        return '#2196F3';\n      case 'confirmed':\n        return '#4CAF50';\n      case 'expired':\n        return '#F44336';\n      default:\n        return '#9E9E9E';\n    }\n  };\n  var getStatusText = function getStatusText() {\n    switch (paymentStatus) {\n      case 'pending':\n        return 'Aguardando Pagamento';\n      case 'confirming':\n        return `Confirmando (${(paymentSession == null ? void 0 : paymentSession.confirmations) || 0}/3)`;\n      case 'confirmed':\n        return 'Pagamento Confirmado';\n      case 'expired':\n        return 'Pagamento Expirado';\n      default:\n        return 'Processando...';\n    }\n  };\n  if (!visible || !paymentSession) {\n    return null;\n  }\n  var plan = paymentService.getPlanDetails(planId);\n  var qrInfo = paymentService.generatePaymentQR(paymentSession, selectedNetwork);\n  var networkConfig = PAYMENT_CONFIG.SUPPORTED_NETWORKS[selectedNetwork];\n  return _jsxs(View, {\n    style: styles.container,\n    children: [_jsx(Card, {\n      style: styles.headerCard,\n      children: _jsxs(View, {\n        style: styles.header,\n        children: [_jsx(Text, {\n          style: styles.title,\n          children: \"Pagamento USDT\"\n        }), _jsxs(Text, {\n          style: styles.planName,\n          children: [\"Plano \", plan.name, \" - $\", plan.price, \" USDT\"]\n        }), _jsxs(View, {\n          style: styles.statusContainer,\n          children: [_jsx(Chip, {\n            mode: \"outlined\",\n            style: [styles.statusChip, {\n              borderColor: getStatusColor()\n            }],\n            textStyle: {\n              color: getStatusColor()\n            },\n            children: getStatusText()\n          }), timeRemaining > 0 && paymentStatus === 'pending' && _jsxs(Text, {\n            style: styles.timer,\n            children: [\"\\u23F0 \", formatTime(timeRemaining)]\n          })]\n        })]\n      })\n    }), _jsxs(Card, {\n      style: styles.networkCard,\n      children: [_jsx(Text, {\n        style: styles.sectionTitle,\n        children: \"Selecione a Rede\"\n      }), _jsx(View, {\n        style: styles.networkButtons,\n        children: Object.entries(PAYMENT_CONFIG.SUPPORTED_NETWORKS).map(function (_ref4) {\n          var _ref5 = _slicedToArray(_ref4, 2),\n            key = _ref5[0],\n            network = _ref5[1];\n          return _jsx(TouchableOpacity, {\n            style: [styles.networkButton, selectedNetwork === key && styles.networkButtonSelected],\n            onPress: function onPress() {\n              return setSelectedNetwork(key);\n            },\n            children: _jsx(Text, {\n              style: [styles.networkButtonText, selectedNetwork === key && styles.networkButtonTextSelected],\n              children: network.name\n            })\n          }, key);\n        })\n      })]\n    }), _jsxs(Card, {\n      style: styles.qrCard,\n      children: [_jsx(Text, {\n        style: styles.sectionTitle,\n        children: \"Escaneie o QR Code\"\n      }), _jsx(View, {\n        style: styles.qrContainer,\n        children: _jsx(QRCode, {\n          value: qrInfo.qrData,\n          size: 200,\n          backgroundColor: \"white\",\n          color: \"black\"\n        })\n      }), _jsx(Text, {\n        style: styles.qrInstructions,\n        children: \"Escaneie com sua carteira crypto ou copie o endere\\xE7o abaixo\"\n      })]\n    }), _jsxs(Card, {\n      style: styles.detailsCard,\n      children: [_jsx(Text, {\n        style: styles.sectionTitle,\n        children: \"Detalhes do Pagamento\"\n      }), _jsxs(View, {\n        style: styles.detailRow,\n        children: [_jsx(Text, {\n          style: styles.detailLabel,\n          children: \"Endere\\xE7o:\"\n        }), _jsxs(TouchableOpacity, {\n          style: styles.copyButton,\n          onPress: function onPress() {\n            return copyToClipboard(paymentSession.walletAddress, 'Endereço');\n          },\n          children: [_jsx(Text, {\n            style: styles.addressText,\n            numberOfLines: 1,\n            children: paymentSession.walletAddress\n          }), _jsx(Text, {\n            style: styles.copyIcon,\n            children: \"\\uD83D\\uDCCB\"\n          })]\n        })]\n      }), _jsxs(View, {\n        style: styles.detailRow,\n        children: [_jsx(Text, {\n          style: styles.detailLabel,\n          children: \"Valor:\"\n        }), _jsxs(Text, {\n          style: styles.detailValue,\n          children: [paymentSession.amount, \" USDT\"]\n        })]\n      }), _jsxs(View, {\n        style: styles.detailRow,\n        children: [_jsx(Text, {\n          style: styles.detailLabel,\n          children: \"Rede:\"\n        }), _jsx(Text, {\n          style: styles.detailValue,\n          children: networkConfig.name\n        })]\n      }), _jsxs(View, {\n        style: styles.detailRow,\n        children: [_jsx(Text, {\n          style: styles.detailLabel,\n          children: \"ID do Pagamento:\"\n        }), _jsx(TouchableOpacity, {\n          onPress: function onPress() {\n            return copyToClipboard(paymentSession.id, 'ID do Pagamento');\n          },\n          children: _jsx(Text, {\n            style: styles.paymentId,\n            children: paymentSession.id\n          })\n        })]\n      })]\n    }), paymentStatus === 'confirming' && _jsxs(Card, {\n      style: styles.progressCard,\n      children: [_jsx(Text, {\n        style: styles.sectionTitle,\n        children: \"Confirma\\xE7\\xF5es da Blockchain\"\n      }), _jsx(ProgressBar, {\n        progress: (paymentSession.confirmations || 0) / 3,\n        color: \"#4CAF50\",\n        style: styles.progressBar\n      }), _jsxs(Text, {\n        style: styles.progressText,\n        children: [paymentSession.confirmations || 0, \" de 3 confirma\\xE7\\xF5es\"]\n      })]\n    }), _jsxs(View, {\n      style: styles.actionButtons,\n      children: [_jsx(Button, {\n        mode: \"outlined\",\n        onPress: onPaymentCancel,\n        style: styles.cancelButton,\n        labelStyle: styles.cancelButtonText,\n        children: \"Cancelar\"\n      }), _jsx(Button, {\n        mode: \"contained\",\n        onPress: function onPress() {\n          return openExplorer(selectedNetwork, paymentSession.walletAddress);\n        },\n        style: styles.explorerButton,\n        labelStyle: styles.explorerButtonText,\n        children: \"Ver no Explorer\"\n      })]\n    }), _jsxs(Card, {\n      style: styles.instructionsCard,\n      children: [_jsx(Text, {\n        style: styles.sectionTitle,\n        children: \"Instru\\xE7\\xF5es\"\n      }), qrInfo.instructions.steps.map(function (step, index) {\n        return _jsxs(Text, {\n          style: styles.instructionStep,\n          children: [index + 1, \". \", step]\n        }, index);\n      }), _jsxs(View, {\n        style: styles.warningsContainer,\n        children: [_jsx(Text, {\n          style: styles.warningsTitle,\n          children: \"\\u26A0\\uFE0F Importante:\"\n        }), qrInfo.instructions.warnings.map(function (warning, index) {\n          return _jsxs(Text, {\n            style: styles.warningText,\n            children: [\"\\u2022 \", warning]\n          }, index);\n        })]\n      })]\n    })]\n  });\n};\nexport default USDTPayment;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "View", "Text", "TouchableOpacity", "<PERSON><PERSON>", "Clipboard", "Linking", "<PERSON><PERSON>", "Card", "Chip", "ProgressBar", "QRCode", "paymentService", "PAYMENT_CONFIG", "styles", "jsx", "_jsx", "jsxs", "_jsxs", "USDTPayment", "_ref", "planId", "onPaymentSuccess", "onPaymentCancel", "_ref$visible", "visible", "_useState", "_useState2", "_slicedToArray", "paymentSession", "setPaymentSession", "_useState3", "_useState4", "selectedNetwork", "setSelectedNetwork", "_useState5", "_useState6", "isLoading", "setIsLoading", "_useState7", "_useState8", "timeRemaining", "setTimeRemaining", "_useState9", "_useState0", "paymentStatus", "setPaymentStatus", "initializePayment", "interval", "setInterval", "checkPaymentStatus", "clearInterval", "timer", "setTimeout", "prev", "clearTimeout", "_ref2", "_asyncToGenerator", "session", "createPaymentSession", "expiresAt", "Date", "now", "remaining", "Math", "max", "floor", "error", "console", "alert", "apply", "arguments", "_ref3", "updatedSession", "id", "status", "text", "onPress", "copyToClipboard", "label", "setString", "openExplorer", "network", "address", "networkConfig", "SUPPORTED_NETWORKS", "url", "explorerUrl", "openURL", "formatTime", "seconds", "minutes", "secs", "toString", "padStart", "getStatusColor", "getStatusText", "confirmations", "plan", "getPlanDetails", "qrInfo", "generatePaymentQR", "style", "container", "children", "headerCard", "header", "title", "planName", "name", "price", "statusContainer", "mode", "statusChip", "borderColor", "textStyle", "color", "networkCard", "sectionTitle", "networkButtons", "Object", "entries", "map", "_ref4", "_ref5", "key", "networkButton", "networkButtonSelected", "networkButtonText", "networkButtonTextSelected", "qrCard", "qr<PERSON><PERSON><PERSON>", "value", "qrData", "size", "backgroundColor", "qrInstructions", "detailsCard", "detailRow", "detail<PERSON><PERSON><PERSON>", "copyButton", "wallet<PERSON>ddress", "addressText", "numberOfLines", "copyIcon", "detailValue", "amount", "paymentId", "progressCard", "progress", "progressBar", "progressText", "actionButtons", "cancelButton", "labelStyle", "cancelButtonText", "<PERSON><PERSON><PERSON><PERSON>", "explorerButtonText", "instructionsCard", "instructions", "steps", "step", "index", "instructionStep", "warningsContainer", "warningsTitle", "warnings", "warning", "warningText"], "sources": ["C:/Users/<USER>/Desktop/Signals/CryptoSignalsApp/src/components/USDTPayment/index.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { View, Text, TouchableOpacity, Alert, Clipboard, Linking } from 'react-native';\nimport { Button, Card, Chip, ProgressBar } from 'react-native-paper';\nimport QRCode from 'react-native-qrcode-svg';\nimport paymentService from '../../services/PaymentService';\nimport { PAYMENT_CONFIG } from '../../config/api';\nimport styles from './styles';\n\nconst USDTPayment = ({ \n  planId, \n  onPaymentSuccess, \n  onPaymentCancel, \n  visible = true \n}) => {\n  const [paymentSession, setPaymentSession] = useState(null);\n  const [selectedNetwork, setSelectedNetwork] = useState('BSC');\n  const [isLoading, setIsLoading] = useState(false);\n  const [timeRemaining, setTimeRemaining] = useState(0);\n  const [paymentStatus, setPaymentStatus] = useState('pending');\n\n  useEffect(() => {\n    if (visible && planId) {\n      initializePayment();\n    }\n  }, [visible, planId]);\n\n  useEffect(() => {\n    let interval;\n    if (paymentSession && paymentStatus === 'pending') {\n      // Verificar status a cada 10 segundos\n      interval = setInterval(checkPaymentStatus, 10000);\n    }\n    return () => {\n      if (interval) clearInterval(interval);\n    };\n  }, [paymentSession, paymentStatus]);\n\n  useEffect(() => {\n    let timer;\n    if (paymentSession && timeRemaining > 0) {\n      timer = setTimeout(() => {\n        setTimeRemaining(prev => prev - 1);\n      }, 1000);\n    } else if (timeRemaining === 0 && paymentSession) {\n      setPaymentStatus('expired');\n    }\n    return () => {\n      if (timer) clearTimeout(timer);\n    };\n  }, [timeRemaining, paymentSession]);\n\n  const initializePayment = async () => {\n    try {\n      setIsLoading(true);\n      const session = await paymentService.createPaymentSession(planId);\n      setPaymentSession(session);\n      \n      // Calcular tempo restante\n      const expiresAt = new Date(session.expiresAt);\n      const now = new Date();\n      const remaining = Math.max(0, Math.floor((expiresAt - now) / 1000));\n      setTimeRemaining(remaining);\n      \n    } catch (error) {\n      console.error('Erro ao inicializar pagamento:', error);\n      Alert.alert('Erro', 'Não foi possível inicializar o pagamento. Tente novamente.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const checkPaymentStatus = async () => {\n    if (!paymentSession) return;\n\n    try {\n      const updatedSession = await paymentService.checkPaymentStatus(paymentSession.id);\n      setPaymentSession(updatedSession);\n      setPaymentStatus(updatedSession.status);\n\n      if (updatedSession.status === 'confirmed') {\n        Alert.alert(\n          'Pagamento Confirmado! 🎉',\n          'Seu plano foi ativado com sucesso!',\n          [{ text: 'OK', onPress: () => onPaymentSuccess(updatedSession) }]\n        );\n      } else if (updatedSession.status === 'expired') {\n        Alert.alert(\n          'Pagamento Expirado',\n          'O tempo para pagamento expirou. Inicie um novo pagamento.',\n          [{ text: 'OK', onPress: onPaymentCancel }]\n        );\n      }\n    } catch (error) {\n      console.error('Erro ao verificar status:', error);\n    }\n  };\n\n  const copyToClipboard = (text, label) => {\n    Clipboard.setString(text);\n    Alert.alert('Copiado!', `${label} copiado para a área de transferência.`);\n  };\n\n  const openExplorer = (network, address) => {\n    const networkConfig = PAYMENT_CONFIG.SUPPORTED_NETWORKS[network];\n    const url = `${networkConfig.explorerUrl}/address/${address}`;\n    Linking.openURL(url);\n  };\n\n  const formatTime = (seconds) => {\n    const minutes = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  const getStatusColor = () => {\n    switch (paymentStatus) {\n      case 'pending': return '#FF9800';\n      case 'confirming': return '#2196F3';\n      case 'confirmed': return '#4CAF50';\n      case 'expired': return '#F44336';\n      default: return '#9E9E9E';\n    }\n  };\n\n  const getStatusText = () => {\n    switch (paymentStatus) {\n      case 'pending': return 'Aguardando Pagamento';\n      case 'confirming': return `Confirmando (${paymentSession?.confirmations || 0}/3)`;\n      case 'confirmed': return 'Pagamento Confirmado';\n      case 'expired': return 'Pagamento Expirado';\n      default: return 'Processando...';\n    }\n  };\n\n  if (!visible || !paymentSession) {\n    return null;\n  }\n\n  const plan = paymentService.getPlanDetails(planId);\n  const qrInfo = paymentService.generatePaymentQR(paymentSession, selectedNetwork);\n  const networkConfig = PAYMENT_CONFIG.SUPPORTED_NETWORKS[selectedNetwork];\n\n  return (\n    <View style={styles.container}>\n      {/* Header */}\n      <Card style={styles.headerCard}>\n        <View style={styles.header}>\n          <Text style={styles.title}>Pagamento USDT</Text>\n          <Text style={styles.planName}>Plano {plan.name} - ${plan.price} USDT</Text>\n          \n          <View style={styles.statusContainer}>\n            <Chip \n              mode=\"outlined\" \n              style={[styles.statusChip, { borderColor: getStatusColor() }]}\n              textStyle={{ color: getStatusColor() }}\n            >\n              {getStatusText()}\n            </Chip>\n            \n            {timeRemaining > 0 && paymentStatus === 'pending' && (\n              <Text style={styles.timer}>\n                ⏰ {formatTime(timeRemaining)}\n              </Text>\n            )}\n          </View>\n        </View>\n      </Card>\n\n      {/* Network Selection */}\n      <Card style={styles.networkCard}>\n        <Text style={styles.sectionTitle}>Selecione a Rede</Text>\n        <View style={styles.networkButtons}>\n          {Object.entries(PAYMENT_CONFIG.SUPPORTED_NETWORKS).map(([key, network]) => (\n            <TouchableOpacity\n              key={key}\n              style={[\n                styles.networkButton,\n                selectedNetwork === key && styles.networkButtonSelected\n              ]}\n              onPress={() => setSelectedNetwork(key)}\n            >\n              <Text style={[\n                styles.networkButtonText,\n                selectedNetwork === key && styles.networkButtonTextSelected\n              ]}>\n                {network.name}\n              </Text>\n            </TouchableOpacity>\n          ))}\n        </View>\n      </Card>\n\n      {/* QR Code */}\n      <Card style={styles.qrCard}>\n        <Text style={styles.sectionTitle}>Escaneie o QR Code</Text>\n        <View style={styles.qrContainer}>\n          <QRCode\n            value={qrInfo.qrData}\n            size={200}\n            backgroundColor=\"white\"\n            color=\"black\"\n          />\n        </View>\n        <Text style={styles.qrInstructions}>\n          Escaneie com sua carteira crypto ou copie o endereço abaixo\n        </Text>\n      </Card>\n\n      {/* Payment Details */}\n      <Card style={styles.detailsCard}>\n        <Text style={styles.sectionTitle}>Detalhes do Pagamento</Text>\n        \n        <View style={styles.detailRow}>\n          <Text style={styles.detailLabel}>Endereço:</Text>\n          <TouchableOpacity \n            style={styles.copyButton}\n            onPress={() => copyToClipboard(paymentSession.walletAddress, 'Endereço')}\n          >\n            <Text style={styles.addressText} numberOfLines={1}>\n              {paymentSession.walletAddress}\n            </Text>\n            <Text style={styles.copyIcon}>📋</Text>\n          </TouchableOpacity>\n        </View>\n\n        <View style={styles.detailRow}>\n          <Text style={styles.detailLabel}>Valor:</Text>\n          <Text style={styles.detailValue}>{paymentSession.amount} USDT</Text>\n        </View>\n\n        <View style={styles.detailRow}>\n          <Text style={styles.detailLabel}>Rede:</Text>\n          <Text style={styles.detailValue}>{networkConfig.name}</Text>\n        </View>\n\n        <View style={styles.detailRow}>\n          <Text style={styles.detailLabel}>ID do Pagamento:</Text>\n          <TouchableOpacity \n            onPress={() => copyToClipboard(paymentSession.id, 'ID do Pagamento')}\n          >\n            <Text style={styles.paymentId}>{paymentSession.id}</Text>\n          </TouchableOpacity>\n        </View>\n      </Card>\n\n      {/* Progress Bar for Confirmations */}\n      {paymentStatus === 'confirming' && (\n        <Card style={styles.progressCard}>\n          <Text style={styles.sectionTitle}>Confirmações da Blockchain</Text>\n          <ProgressBar \n            progress={(paymentSession.confirmations || 0) / 3} \n            color=\"#4CAF50\"\n            style={styles.progressBar}\n          />\n          <Text style={styles.progressText}>\n            {paymentSession.confirmations || 0} de 3 confirmações\n          </Text>\n        </Card>\n      )}\n\n      {/* Action Buttons */}\n      <View style={styles.actionButtons}>\n        <Button\n          mode=\"outlined\"\n          onPress={onPaymentCancel}\n          style={styles.cancelButton}\n          labelStyle={styles.cancelButtonText}\n        >\n          Cancelar\n        </Button>\n        \n        <Button\n          mode=\"contained\"\n          onPress={() => openExplorer(selectedNetwork, paymentSession.walletAddress)}\n          style={styles.explorerButton}\n          labelStyle={styles.explorerButtonText}\n        >\n          Ver no Explorer\n        </Button>\n      </View>\n\n      {/* Instructions */}\n      <Card style={styles.instructionsCard}>\n        <Text style={styles.sectionTitle}>Instruções</Text>\n        {qrInfo.instructions.steps.map((step, index) => (\n          <Text key={index} style={styles.instructionStep}>\n            {index + 1}. {step}\n          </Text>\n        ))}\n        \n        <View style={styles.warningsContainer}>\n          <Text style={styles.warningsTitle}>⚠️ Importante:</Text>\n          {qrInfo.instructions.warnings.map((warning, index) => (\n            <Text key={index} style={styles.warningText}>\n              • {warning}\n            </Text>\n          ))}\n        </View>\n      </Card>\n    </View>\n  );\n};\n\nexport default USDTPayment;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,gBAAA;AAAA,OAAAC,KAAA;AAAA,OAAAC,SAAA;AAAA,OAAAC,OAAA;AAEnD,SAASC,MAAM,EAAEC,IAAI,EAAEC,IAAI,EAAEC,WAAW,QAAQ,oBAAoB;AACpE,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,cAAc;AACrB,SAASC,cAAc;AACvB,OAAOC,MAAM;AAAiB,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAE9B,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAAC,IAAA,EAKX;EAAA,IAJJC,MAAM,GAAAD,IAAA,CAANC,MAAM;IACNC,gBAAgB,GAAAF,IAAA,CAAhBE,gBAAgB;IAChBC,eAAe,GAAAH,IAAA,CAAfG,eAAe;IAAAC,YAAA,GAAAJ,IAAA,CACfK,OAAO;IAAPA,OAAO,GAAAD,YAAA,cAAG,IAAI,GAAAA,YAAA;EAEd,IAAAE,SAAA,GAA4C3B,QAAQ,CAAC,IAAI,CAAC;IAAA4B,UAAA,GAAAC,cAAA,CAAAF,SAAA;IAAnDG,cAAc,GAAAF,UAAA;IAAEG,iBAAiB,GAAAH,UAAA;EACxC,IAAAI,UAAA,GAA8ChC,QAAQ,CAAC,KAAK,CAAC;IAAAiC,UAAA,GAAAJ,cAAA,CAAAG,UAAA;IAAtDE,eAAe,GAAAD,UAAA;IAAEE,kBAAkB,GAAAF,UAAA;EAC1C,IAAAG,UAAA,GAAkCpC,QAAQ,CAAC,KAAK,CAAC;IAAAqC,UAAA,GAAAR,cAAA,CAAAO,UAAA;IAA1CE,SAAS,GAAAD,UAAA;IAAEE,YAAY,GAAAF,UAAA;EAC9B,IAAAG,UAAA,GAA0CxC,QAAQ,CAAC,CAAC,CAAC;IAAAyC,UAAA,GAAAZ,cAAA,CAAAW,UAAA;IAA9CE,aAAa,GAAAD,UAAA;IAAEE,gBAAgB,GAAAF,UAAA;EACtC,IAAAG,UAAA,GAA0C5C,QAAQ,CAAC,SAAS,CAAC;IAAA6C,UAAA,GAAAhB,cAAA,CAAAe,UAAA;IAAtDE,aAAa,GAAAD,UAAA;IAAEE,gBAAgB,GAAAF,UAAA;EAEtC5C,SAAS,CAAC,YAAM;IACd,IAAIyB,OAAO,IAAIJ,MAAM,EAAE;MACrB0B,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC,EAAE,CAACtB,OAAO,EAAEJ,MAAM,CAAC,CAAC;EAErBrB,SAAS,CAAC,YAAM;IACd,IAAIgD,QAAQ;IACZ,IAAInB,cAAc,IAAIgB,aAAa,KAAK,SAAS,EAAE;MAEjDG,QAAQ,GAAGC,WAAW,CAACC,kBAAkB,EAAE,KAAK,CAAC;IACnD;IACA,OAAO,YAAM;MACX,IAAIF,QAAQ,EAAEG,aAAa,CAACH,QAAQ,CAAC;IACvC,CAAC;EACH,CAAC,EAAE,CAACnB,cAAc,EAAEgB,aAAa,CAAC,CAAC;EAEnC7C,SAAS,CAAC,YAAM;IACd,IAAIoD,KAAK;IACT,IAAIvB,cAAc,IAAIY,aAAa,GAAG,CAAC,EAAE;MACvCW,KAAK,GAAGC,UAAU,CAAC,YAAM;QACvBX,gBAAgB,CAAC,UAAAY,IAAI;UAAA,OAAIA,IAAI,GAAG,CAAC;QAAA,EAAC;MACpC,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,MAAM,IAAIb,aAAa,KAAK,CAAC,IAAIZ,cAAc,EAAE;MAChDiB,gBAAgB,CAAC,SAAS,CAAC;IAC7B;IACA,OAAO,YAAM;MACX,IAAIM,KAAK,EAAEG,YAAY,CAACH,KAAK,CAAC;IAChC,CAAC;EACH,CAAC,EAAE,CAACX,aAAa,EAAEZ,cAAc,CAAC,CAAC;EAEnC,IAAMkB,iBAAiB;IAAA,IAAAS,KAAA,GAAAC,iBAAA,CAAG,aAAY;MACpC,IAAI;QACFnB,YAAY,CAAC,IAAI,CAAC;QAClB,IAAMoB,OAAO,SAAS9C,cAAc,CAAC+C,oBAAoB,CAACtC,MAAM,CAAC;QACjES,iBAAiB,CAAC4B,OAAO,CAAC;QAG1B,IAAME,SAAS,GAAG,IAAIC,IAAI,CAACH,OAAO,CAACE,SAAS,CAAC;QAC7C,IAAME,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;QACtB,IAAME,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,KAAK,CAAC,CAACN,SAAS,GAAGE,GAAG,IAAI,IAAI,CAAC,CAAC;QACnEpB,gBAAgB,CAACqB,SAAS,CAAC;MAE7B,CAAC,CAAC,OAAOI,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD/D,KAAK,CAACiE,KAAK,CAAC,MAAM,EAAE,4DAA4D,CAAC;MACnF,CAAC,SAAS;QACR/B,YAAY,CAAC,KAAK,CAAC;MACrB;IACF,CAAC;IAAA,gBAlBKS,iBAAiBA,CAAA;MAAA,OAAAS,KAAA,CAAAc,KAAA,OAAAC,SAAA;IAAA;EAAA,GAkBtB;EAED,IAAMrB,kBAAkB;IAAA,IAAAsB,KAAA,GAAAf,iBAAA,CAAG,aAAY;MACrC,IAAI,CAAC5B,cAAc,EAAE;MAErB,IAAI;QACF,IAAM4C,cAAc,SAAS7D,cAAc,CAACsC,kBAAkB,CAACrB,cAAc,CAAC6C,EAAE,CAAC;QACjF5C,iBAAiB,CAAC2C,cAAc,CAAC;QACjC3B,gBAAgB,CAAC2B,cAAc,CAACE,MAAM,CAAC;QAEvC,IAAIF,cAAc,CAACE,MAAM,KAAK,WAAW,EAAE;UACzCvE,KAAK,CAACiE,KAAK,CACT,0BAA0B,EAC1B,oCAAoC,EACpC,CAAC;YAAEO,IAAI,EAAE,IAAI;YAAEC,OAAO,EAAE,SAATA,OAAOA,CAAA;cAAA,OAAQvD,gBAAgB,CAACmD,cAAc,CAAC;YAAA;UAAC,CAAC,CAClE,CAAC;QACH,CAAC,MAAM,IAAIA,cAAc,CAACE,MAAM,KAAK,SAAS,EAAE;UAC9CvE,KAAK,CAACiE,KAAK,CACT,oBAAoB,EACpB,2DAA2D,EAC3D,CAAC;YAAEO,IAAI,EAAE,IAAI;YAAEC,OAAO,EAAEtD;UAAgB,CAAC,CAC3C,CAAC;QACH;MACF,CAAC,CAAC,OAAO4C,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD;IACF,CAAC;IAAA,gBAxBKjB,kBAAkBA,CAAA;MAAA,OAAAsB,KAAA,CAAAF,KAAA,OAAAC,SAAA;IAAA;EAAA,GAwBvB;EAED,IAAMO,eAAe,GAAG,SAAlBA,eAAeA,CAAIF,IAAI,EAAEG,KAAK,EAAK;IACvC1E,SAAS,CAAC2E,SAAS,CAACJ,IAAI,CAAC;IACzBxE,KAAK,CAACiE,KAAK,CAAC,UAAU,EAAE,GAAGU,KAAK,wCAAwC,CAAC;EAC3E,CAAC;EAED,IAAME,YAAY,GAAG,SAAfA,YAAYA,CAAIC,OAAO,EAAEC,OAAO,EAAK;IACzC,IAAMC,aAAa,GAAGvE,cAAc,CAACwE,kBAAkB,CAACH,OAAO,CAAC;IAChE,IAAMI,GAAG,GAAG,GAAGF,aAAa,CAACG,WAAW,YAAYJ,OAAO,EAAE;IAC7D7E,OAAO,CAACkF,OAAO,CAACF,GAAG,CAAC;EACtB,CAAC;EAED,IAAMG,UAAU,GAAG,SAAbA,UAAUA,CAAIC,OAAO,EAAK;IAC9B,IAAMC,OAAO,GAAG3B,IAAI,CAACE,KAAK,CAACwB,OAAO,GAAG,EAAE,CAAC;IACxC,IAAME,IAAI,GAAGF,OAAO,GAAG,EAAE;IACzB,OAAO,GAAGC,OAAO,CAACE,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACrF,CAAC;EAED,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;IAC3B,QAAQlD,aAAa;MACnB,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC,KAAK,YAAY;QAAE,OAAO,SAAS;MACnC,KAAK,WAAW;QAAE,OAAO,SAAS;MAClC,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,IAAMmD,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;IAC1B,QAAQnD,aAAa;MACnB,KAAK,SAAS;QAAE,OAAO,sBAAsB;MAC7C,KAAK,YAAY;QAAE,OAAO,gBAAgB,CAAAhB,cAAc,oBAAdA,cAAc,CAAEoE,aAAa,KAAI,CAAC,KAAK;MACjF,KAAK,WAAW;QAAE,OAAO,sBAAsB;MAC/C,KAAK,SAAS;QAAE,OAAO,oBAAoB;MAC3C;QAAS,OAAO,gBAAgB;IAClC;EACF,CAAC;EAED,IAAI,CAACxE,OAAO,IAAI,CAACI,cAAc,EAAE;IAC/B,OAAO,IAAI;EACb;EAEA,IAAMqE,IAAI,GAAGtF,cAAc,CAACuF,cAAc,CAAC9E,MAAM,CAAC;EAClD,IAAM+E,MAAM,GAAGxF,cAAc,CAACyF,iBAAiB,CAACxE,cAAc,EAAEI,eAAe,CAAC;EAChF,IAAMmD,aAAa,GAAGvE,cAAc,CAACwE,kBAAkB,CAACpD,eAAe,CAAC;EAExE,OACEf,KAAA,CAACjB,IAAI;IAACqG,KAAK,EAAExF,MAAM,CAACyF,SAAU;IAAAC,QAAA,GAE5BxF,IAAA,CAACR,IAAI;MAAC8F,KAAK,EAAExF,MAAM,CAAC2F,UAAW;MAAAD,QAAA,EAC7BtF,KAAA,CAACjB,IAAI;QAACqG,KAAK,EAAExF,MAAM,CAAC4F,MAAO;QAAAF,QAAA,GACzBxF,IAAA,CAACd,IAAI;UAACoG,KAAK,EAAExF,MAAM,CAAC6F,KAAM;UAAAH,QAAA,EAAC;QAAc,CAAM,CAAC,EAChDtF,KAAA,CAAChB,IAAI;UAACoG,KAAK,EAAExF,MAAM,CAAC8F,QAAS;UAAAJ,QAAA,GAAC,QAAM,EAACN,IAAI,CAACW,IAAI,EAAC,MAAI,EAACX,IAAI,CAACY,KAAK,EAAC,OAAK;QAAA,CAAM,CAAC,EAE3E5F,KAAA,CAACjB,IAAI;UAACqG,KAAK,EAAExF,MAAM,CAACiG,eAAgB;UAAAP,QAAA,GAClCxF,IAAA,CAACP,IAAI;YACHuG,IAAI,EAAC,UAAU;YACfV,KAAK,EAAE,CAACxF,MAAM,CAACmG,UAAU,EAAE;cAAEC,WAAW,EAAEnB,cAAc,CAAC;YAAE,CAAC,CAAE;YAC9DoB,SAAS,EAAE;cAAEC,KAAK,EAAErB,cAAc,CAAC;YAAE,CAAE;YAAAS,QAAA,EAEtCR,aAAa,CAAC;UAAC,CACZ,CAAC,EAENvD,aAAa,GAAG,CAAC,IAAII,aAAa,KAAK,SAAS,IAC/C3B,KAAA,CAAChB,IAAI;YAACoG,KAAK,EAAExF,MAAM,CAACsC,KAAM;YAAAoD,QAAA,GAAC,SACvB,EAACf,UAAU,CAAChD,aAAa,CAAC;UAAA,CACxB,CACP;QAAA,CACG,CAAC;MAAA,CACH;IAAC,CACH,CAAC,EAGPvB,KAAA,CAACV,IAAI;MAAC8F,KAAK,EAAExF,MAAM,CAACuG,WAAY;MAAAb,QAAA,GAC9BxF,IAAA,CAACd,IAAI;QAACoG,KAAK,EAAExF,MAAM,CAACwG,YAAa;QAAAd,QAAA,EAAC;MAAgB,CAAM,CAAC,EACzDxF,IAAA,CAACf,IAAI;QAACqG,KAAK,EAAExF,MAAM,CAACyG,cAAe;QAAAf,QAAA,EAChCgB,MAAM,CAACC,OAAO,CAAC5G,cAAc,CAACwE,kBAAkB,CAAC,CAACqC,GAAG,CAAC,UAAAC,KAAA;UAAA,IAAAC,KAAA,GAAAhG,cAAA,CAAA+F,KAAA;YAAEE,GAAG,GAAAD,KAAA;YAAE1C,OAAO,GAAA0C,KAAA;UAAA,OACnE5G,IAAA,CAACb,gBAAgB;YAEfmG,KAAK,EAAE,CACLxF,MAAM,CAACgH,aAAa,EACpB7F,eAAe,KAAK4F,GAAG,IAAI/G,MAAM,CAACiH,qBAAqB,CACvD;YACFlD,OAAO,EAAE,SAATA,OAAOA,CAAA;cAAA,OAAQ3C,kBAAkB,CAAC2F,GAAG,CAAC;YAAA,CAAC;YAAArB,QAAA,EAEvCxF,IAAA,CAACd,IAAI;cAACoG,KAAK,EAAE,CACXxF,MAAM,CAACkH,iBAAiB,EACxB/F,eAAe,KAAK4F,GAAG,IAAI/G,MAAM,CAACmH,yBAAyB,CAC3D;cAAAzB,QAAA,EACCtB,OAAO,CAAC2B;YAAI,CACT;UAAC,GAZFgB,GAaW,CAAC;QAAA,CACpB;MAAC,CACE,CAAC;IAAA,CACH,CAAC,EAGP3G,KAAA,CAACV,IAAI;MAAC8F,KAAK,EAAExF,MAAM,CAACoH,MAAO;MAAA1B,QAAA,GACzBxF,IAAA,CAACd,IAAI;QAACoG,KAAK,EAAExF,MAAM,CAACwG,YAAa;QAAAd,QAAA,EAAC;MAAkB,CAAM,CAAC,EAC3DxF,IAAA,CAACf,IAAI;QAACqG,KAAK,EAAExF,MAAM,CAACqH,WAAY;QAAA3B,QAAA,EAC9BxF,IAAA,CAACL,MAAM;UACLyH,KAAK,EAAEhC,MAAM,CAACiC,MAAO;UACrBC,IAAI,EAAE,GAAI;UACVC,eAAe,EAAC,OAAO;UACvBnB,KAAK,EAAC;QAAO,CACd;MAAC,CACE,CAAC,EACPpG,IAAA,CAACd,IAAI;QAACoG,KAAK,EAAExF,MAAM,CAAC0H,cAAe;QAAAhC,QAAA,EAAC;MAEpC,CAAM,CAAC;IAAA,CACH,CAAC,EAGPtF,KAAA,CAACV,IAAI;MAAC8F,KAAK,EAAExF,MAAM,CAAC2H,WAAY;MAAAjC,QAAA,GAC9BxF,IAAA,CAACd,IAAI;QAACoG,KAAK,EAAExF,MAAM,CAACwG,YAAa;QAAAd,QAAA,EAAC;MAAqB,CAAM,CAAC,EAE9DtF,KAAA,CAACjB,IAAI;QAACqG,KAAK,EAAExF,MAAM,CAAC4H,SAAU;QAAAlC,QAAA,GAC5BxF,IAAA,CAACd,IAAI;UAACoG,KAAK,EAAExF,MAAM,CAAC6H,WAAY;UAAAnC,QAAA,EAAC;QAAS,CAAM,CAAC,EACjDtF,KAAA,CAACf,gBAAgB;UACfmG,KAAK,EAAExF,MAAM,CAAC8H,UAAW;UACzB/D,OAAO,EAAE,SAATA,OAAOA,CAAA;YAAA,OAAQC,eAAe,CAACjD,cAAc,CAACgH,aAAa,EAAE,UAAU,CAAC;UAAA,CAAC;UAAArC,QAAA,GAEzExF,IAAA,CAACd,IAAI;YAACoG,KAAK,EAAExF,MAAM,CAACgI,WAAY;YAACC,aAAa,EAAE,CAAE;YAAAvC,QAAA,EAC/C3E,cAAc,CAACgH;UAAa,CACzB,CAAC,EACP7H,IAAA,CAACd,IAAI;YAACoG,KAAK,EAAExF,MAAM,CAACkI,QAAS;YAAAxC,QAAA,EAAC;UAAE,CAAM,CAAC;QAAA,CACvB,CAAC;MAAA,CACf,CAAC,EAEPtF,KAAA,CAACjB,IAAI;QAACqG,KAAK,EAAExF,MAAM,CAAC4H,SAAU;QAAAlC,QAAA,GAC5BxF,IAAA,CAACd,IAAI;UAACoG,KAAK,EAAExF,MAAM,CAAC6H,WAAY;UAAAnC,QAAA,EAAC;QAAM,CAAM,CAAC,EAC9CtF,KAAA,CAAChB,IAAI;UAACoG,KAAK,EAAExF,MAAM,CAACmI,WAAY;UAAAzC,QAAA,GAAE3E,cAAc,CAACqH,MAAM,EAAC,OAAK;QAAA,CAAM,CAAC;MAAA,CAChE,CAAC,EAEPhI,KAAA,CAACjB,IAAI;QAACqG,KAAK,EAAExF,MAAM,CAAC4H,SAAU;QAAAlC,QAAA,GAC5BxF,IAAA,CAACd,IAAI;UAACoG,KAAK,EAAExF,MAAM,CAAC6H,WAAY;UAAAnC,QAAA,EAAC;QAAK,CAAM,CAAC,EAC7CxF,IAAA,CAACd,IAAI;UAACoG,KAAK,EAAExF,MAAM,CAACmI,WAAY;UAAAzC,QAAA,EAAEpB,aAAa,CAACyB;QAAI,CAAO,CAAC;MAAA,CACxD,CAAC,EAEP3F,KAAA,CAACjB,IAAI;QAACqG,KAAK,EAAExF,MAAM,CAAC4H,SAAU;QAAAlC,QAAA,GAC5BxF,IAAA,CAACd,IAAI;UAACoG,KAAK,EAAExF,MAAM,CAAC6H,WAAY;UAAAnC,QAAA,EAAC;QAAgB,CAAM,CAAC,EACxDxF,IAAA,CAACb,gBAAgB;UACf0E,OAAO,EAAE,SAATA,OAAOA,CAAA;YAAA,OAAQC,eAAe,CAACjD,cAAc,CAAC6C,EAAE,EAAE,iBAAiB,CAAC;UAAA,CAAC;UAAA8B,QAAA,EAErExF,IAAA,CAACd,IAAI;YAACoG,KAAK,EAAExF,MAAM,CAACqI,SAAU;YAAA3C,QAAA,EAAE3E,cAAc,CAAC6C;UAAE,CAAO;QAAC,CACzC,CAAC;MAAA,CACf,CAAC;IAAA,CACH,CAAC,EAGN7B,aAAa,KAAK,YAAY,IAC7B3B,KAAA,CAACV,IAAI;MAAC8F,KAAK,EAAExF,MAAM,CAACsI,YAAa;MAAA5C,QAAA,GAC/BxF,IAAA,CAACd,IAAI;QAACoG,KAAK,EAAExF,MAAM,CAACwG,YAAa;QAAAd,QAAA,EAAC;MAA0B,CAAM,CAAC,EACnExF,IAAA,CAACN,WAAW;QACV2I,QAAQ,EAAE,CAACxH,cAAc,CAACoE,aAAa,IAAI,CAAC,IAAI,CAAE;QAClDmB,KAAK,EAAC,SAAS;QACfd,KAAK,EAAExF,MAAM,CAACwI;MAAY,CAC3B,CAAC,EACFpI,KAAA,CAAChB,IAAI;QAACoG,KAAK,EAAExF,MAAM,CAACyI,YAAa;QAAA/C,QAAA,GAC9B3E,cAAc,CAACoE,aAAa,IAAI,CAAC,EAAC,0BACrC;MAAA,CAAM,CAAC;IAAA,CACH,CACP,EAGD/E,KAAA,CAACjB,IAAI;MAACqG,KAAK,EAAExF,MAAM,CAAC0I,aAAc;MAAAhD,QAAA,GAChCxF,IAAA,CAACT,MAAM;QACLyG,IAAI,EAAC,UAAU;QACfnC,OAAO,EAAEtD,eAAgB;QACzB+E,KAAK,EAAExF,MAAM,CAAC2I,YAAa;QAC3BC,UAAU,EAAE5I,MAAM,CAAC6I,gBAAiB;QAAAnD,QAAA,EACrC;MAED,CAAQ,CAAC,EAETxF,IAAA,CAACT,MAAM;QACLyG,IAAI,EAAC,WAAW;QAChBnC,OAAO,EAAE,SAATA,OAAOA,CAAA;UAAA,OAAQI,YAAY,CAAChD,eAAe,EAAEJ,cAAc,CAACgH,aAAa,CAAC;QAAA,CAAC;QAC3EvC,KAAK,EAAExF,MAAM,CAAC8I,cAAe;QAC7BF,UAAU,EAAE5I,MAAM,CAAC+I,kBAAmB;QAAArD,QAAA,EACvC;MAED,CAAQ,CAAC;IAAA,CACL,CAAC,EAGPtF,KAAA,CAACV,IAAI;MAAC8F,KAAK,EAAExF,MAAM,CAACgJ,gBAAiB;MAAAtD,QAAA,GACnCxF,IAAA,CAACd,IAAI;QAACoG,KAAK,EAAExF,MAAM,CAACwG,YAAa;QAAAd,QAAA,EAAC;MAAU,CAAM,CAAC,EAClDJ,MAAM,CAAC2D,YAAY,CAACC,KAAK,CAACtC,GAAG,CAAC,UAACuC,IAAI,EAAEC,KAAK;QAAA,OACzChJ,KAAA,CAAChB,IAAI;UAAaoG,KAAK,EAAExF,MAAM,CAACqJ,eAAgB;UAAA3D,QAAA,GAC7C0D,KAAK,GAAG,CAAC,EAAC,IAAE,EAACD,IAAI;QAAA,GADTC,KAEL,CAAC;MAAA,CACR,CAAC,EAEFhJ,KAAA,CAACjB,IAAI;QAACqG,KAAK,EAAExF,MAAM,CAACsJ,iBAAkB;QAAA5D,QAAA,GACpCxF,IAAA,CAACd,IAAI;UAACoG,KAAK,EAAExF,MAAM,CAACuJ,aAAc;UAAA7D,QAAA,EAAC;QAAc,CAAM,CAAC,EACvDJ,MAAM,CAAC2D,YAAY,CAACO,QAAQ,CAAC5C,GAAG,CAAC,UAAC6C,OAAO,EAAEL,KAAK;UAAA,OAC/ChJ,KAAA,CAAChB,IAAI;YAAaoG,KAAK,EAAExF,MAAM,CAAC0J,WAAY;YAAAhE,QAAA,GAAC,SACzC,EAAC+D,OAAO;UAAA,GADDL,KAEL,CAAC;QAAA,CACR,CAAC;MAAA,CACE,CAAC;IAAA,CACH,CAAC;EAAA,CACH,CAAC;AAEX,CAAC;AAED,eAAe/I,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}