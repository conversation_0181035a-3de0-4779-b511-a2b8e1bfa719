import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Tuple, Optional
import logging

logger = logging.getLogger(__name__)

class Backtester:
    def __init__(self, binance_client, strategy, initial_capital: float = 1000.0):
        """
        Inicializa o backtester
        
        Args:
            binance_client: Cliente da Binance para obter dados históricos
            strategy: Estratégia a ser testada
            initial_capital: Capital inicial para simulação
        """
        self.binance_client = binance_client
        self.strategy = strategy
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.positions: List[Dict] = []
        self.trades_history: List[Dict] = []
        self.equity_curve: List[Dict] = []
        
    def get_historical_data(self, symbol: str, interval: str, start_date: datetime, end_date: datetime) -> pd.DataFrame:
        """
        Obtém dados históricos da Binance
        
        Args:
            symbol: Par de trading (ex: 'BTCUSDT')
            interval: Timeframe (ex: '1h', '4h', '1d')
            start_date: Data inicial
            end_date: Data final
            
        Returns:
            DataFrame com os dados históricos
        """
        try:
            # Converter datas para timestamp em milissegundos
            start_ts = int(start_date.timestamp() * 1000)
            end_ts = int(end_date.timestamp() * 1000)
            
            df = self.binance_client.get_historical_klines(
                symbol=symbol,
                interval=interval,
                start_ts=start_ts,
                end_ts=end_ts
            )
            
            if df.empty:
                raise ValueError("Nenhum dado histórico encontrado")
            
            # Renomear colunas para manter consistência
            df = df.rename(columns={
                'OpenTime': 'timestamp',
                'CloseTime': 'close_time',
                'QuoteAssetVolume': 'quote_volume',
                'NumberOfTrades': 'trades',
                'TakerBuyBaseAssetVolume': 'taker_buy_base',
                'TakerBuyQuoteAssetVolume': 'taker_buy_quote',
                'Ignore': 'ignore'
            })
            
            # Resetar o índice para ter a coluna timestamp
            if 'timestamp' not in df.columns:
                df = df.reset_index()
                df = df.rename(columns={'OpenTime': 'timestamp'})
            
            # Converter tipos de dados
            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = df[col].astype(float)
            
            return df
            
        except Exception as e:
            raise Exception(f"Erro ao obter dados históricos: {str(e)}")
    
    def calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Calcula indicadores técnicos necessários para a estratégia
        
        Args:
            df: DataFrame com dados históricos
            
        Returns:
            DataFrame com indicadores calculados
        """
        # Implementar cálculo de indicadores específicos da estratégia
        return df
    
    def run_backtest(self, symbol: str, interval: str, start_date: datetime, end_date: datetime) -> Dict:
        """
        Executa o backtest da estratégia
        
        Args:
            symbol: Par de trading
            interval: Intervalo de tempo
            start_date: Data inicial
            end_date: Data final
            
        Returns:
            Dicionário com resultados do backtest
        """
        try:
            # Obter dados históricos
            df = self.get_historical_data(symbol, interval, start_date, end_date)
            
            # Resetar estado
            self.positions = []
            self.trades_history = []
            self.equity_curve = [{'timestamp': start_date, 'equity': self.initial_capital}]
            self.current_capital = self.initial_capital
            
            # Iterar sobre os dados
            for i in range(len(df)):
                current_candle = df.iloc[i]
                current_time = pd.to_datetime(current_candle['timestamp'])
                current_price = float(current_candle['close'])
                
                # Atualizar posições existentes
                self.update_positions(current_price, current_time)
                
                # Preparar dados para análise
                historical_data = df.iloc[:i+1].copy()
                
                try:
                    # Verificar sinais da estratégia
                    signal = self.strategy.analyze_symbol(symbol)
                    
                    if signal and isinstance(signal, tuple):
                        if len(signal) == 3:  # (entry_price, stop_loss, take_profit)
                            entry_price, stop_loss, take_profit = signal
                            signal_type = 'LONG'  # Assumir LONG como padrão
                        elif len(signal) == 4:  # (signal_type, entry_price, stop_loss, take_profit)
                            signal_type, entry_price, stop_loss, take_profit = signal
                        
                        # Executar trade apenas se tivermos todos os dados necessários
                        if all(x is not None for x in [entry_price, stop_loss, take_profit]):
                            self.execute_trade(
                                symbol=symbol,
                                signal_type=signal_type,
                                entry_price=entry_price,
                                stop_loss=stop_loss,
                                take_profit=take_profit,
                                timestamp=current_time
                            )
                except Exception as strategy_error:
                    logger.error(f"Erro ao analisar sinal: {strategy_error}")
                    continue
                
                # Atualizar curva de equity
                self.equity_curve.append({
                    'timestamp': current_time,
                    'equity': self.current_capital
                })
            
            # Fechar posições abertas no final do período
            for position in self.positions:
                if position['status'] == 'open':
                    self.close_position(position, current_price, end_date, 'end_of_period')
            
            # Calcular métricas de performance
            metrics = self.calculate_performance_metrics()
            
            return {
                'total_trades': len(self.trades_history),
                'win_rate': metrics['win_rate'],
                'profit_factor': metrics['profit_factor'],
                'max_drawdown': metrics['max_drawdown'],
                'sharpe_ratio': metrics['sharpe_ratio'],
                'total_return': metrics['total_return'],
                'equity_curve': self.equity_curve,
                'trades_history': self.trades_history
            }
            
        except Exception as e:
            raise Exception(f"Erro ao executar backtest: {str(e)}")
    
    def execute_trade(self, symbol: str, signal_type: str, entry_price: float,
                     stop_loss: float, take_profit: float, timestamp: datetime):
        """
        Simula a execução de um trade
        
        Args:
            symbol: Par de trading
            signal_type: Tipo do sinal (LONG/SHORT)
            entry_price: Preço de entrada
            stop_loss: Preço de stop loss
            take_profit: Preço de take profit
            timestamp: Momento da execução
        """
        position_size = self.current_capital * 0.01
        
        self.positions.append({
            'symbol': symbol,
            'type': signal_type,
            'entry_price': entry_price,
            'stop_loss': stop_loss,
            'take_profit': take_profit,
            'position_size': position_size,
            'entry_time': timestamp,
            'status': 'open'
        })
    
    def update_positions(self, current_price: float, current_time: datetime):
        """
        Atualiza posições abertas
        
        Args:
            current_price: Preço atual
            current_time: Momento atual
        """
        for position in self.positions:
            if position['status'] != 'open':
                continue
                
            if position['type'] == 'LONG':
                if current_price <= position['stop_loss']:
                    self.close_position(position, current_price, current_time, 'stop_loss')
                elif current_price >= position['take_profit']:
                    self.close_position(position, current_price, current_time, 'take_profit')
            else:  # SHORT
                if current_price >= position['stop_loss']:
                    self.close_position(position, current_price, current_time, 'stop_loss')
                elif current_price <= position['take_profit']:
                    self.close_position(position, current_price, current_time, 'take_profit')
    
    def close_position(self, position: Dict, exit_price: float, exit_time: datetime, reason: str):
        """
        Fecha uma posição
        
        Args:
            position: Dicionário com dados da posição
            exit_price: Preço de saída
            exit_time: Momento da saída
            reason: Razão do fechamento
        """
        if position['type'] == 'LONG':
            profit_pct = (exit_price - position['entry_price']) / position['entry_price']
        else:  # SHORT
            profit_pct = (position['entry_price'] - exit_price) / position['entry_price']
        
        profit_amount = position['position_size'] * profit_pct
        
        self.trades_history.append({
            'symbol': position['symbol'],
            'type': position['type'],
            'entry_price': position['entry_price'],
            'exit_price': exit_price,
            'profit_pct': profit_pct,
            'profit_amount': profit_amount,
            'entry_time': position['entry_time'],
            'exit_time': exit_time,
            'reason': reason
        })
        
        self.current_capital += profit_amount
        position['status'] = 'closed'
    
    def calculate_performance_metrics(self) -> Dict:
        """
        Calcula métricas de performance do backtest
        
        Returns:
            Dicionário com métricas calculadas
        """
        if not self.trades_history:
            return {
                'total_trades': 0,
                'win_rate': 0.0,
                'profit_factor': 0.0,
                'max_drawdown': 0.0,
                'sharpe_ratio': 0.0,
                'total_return': 0.0
            }
        
        # Calcular win rate
        winning_trades = len([t for t in self.trades_history if t['profit_pct'] > 0])
        win_rate = winning_trades / len(self.trades_history)
        
        # Calcular profit factor
        gross_profit = sum(t['profit_amount'] for t in self.trades_history if t['profit_pct'] > 0)
        gross_loss = abs(sum(t['profit_amount'] for t in self.trades_history if t['profit_pct'] < 0))
        profit_factor = gross_profit / gross_loss if gross_loss != 0 else float('inf')
        
        # Calcular drawdown máximo
        equity_values = [point['equity'] for point in self.equity_curve]
        max_drawdown = self.calculate_max_drawdown(equity_values)
        
        # Calcular Sharpe ratio
        returns = [t['profit_pct'] for t in self.trades_history]
        if len(returns) > 1:
            sharpe_ratio = np.mean(returns) / np.std(returns) if np.std(returns) != 0 else 0
        else:
            sharpe_ratio = 0
        
        # Calcular retorno total
        total_return = ((self.current_capital - self.initial_capital) / self.initial_capital) * 100
        
        return {
            'total_trades': len(self.trades_history),
            'win_rate': win_rate,
            'profit_factor': profit_factor,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'total_return': total_return
        }
    
    def calculate_max_drawdown(self, equity_values: List[float]) -> float:
        """
        Calcula o drawdown máximo
        
        Args:
            equity_values: Lista de valores de equity
            
        Returns:
            Drawdown máximo em porcentagem
        """
        if not equity_values:
            return 0.0
            
        peak = equity_values[0]
        max_dd = 0.0
        
        for value in equity_values:
            if value > peak:
                peak = value
            dd = (peak - value) / peak
            max_dd = max(max_dd, dd)
            
        return max_dd * 100 