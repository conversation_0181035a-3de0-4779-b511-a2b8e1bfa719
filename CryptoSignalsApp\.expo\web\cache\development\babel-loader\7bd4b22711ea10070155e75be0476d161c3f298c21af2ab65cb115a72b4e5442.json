{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport React, { useReducer } from 'react';\nimport globalReducer from \"./reducers\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nvar initialArg = {\n  subscription: {\n    subscriptionStatus: false,\n    lastSubscriptionStatusDate: null,\n    customerId: null,\n    subscriptionPeriodEnd: null,\n    accessToken: null\n  },\n  signalFromWebsocket: {},\n  channelOrderedByNewSignal: null,\n  pushNotification: {\n    token: null,\n    lastLoadTokenDate: null\n  }\n};\nexport var StoreContext = React.createContext(initialArg);\nexport default function StoreProvider(props) {\n  var _useReducer = useReducer(globalReducer, initialArg),\n    _useReducer2 = _slicedToArray(_useReducer, 2),\n    state = _useReducer2[0],\n    dispatch = _useReducer2[1];\n  return _jsx(StoreContext.Provider, {\n    value: [state, dispatch],\n    children: props.children\n  });\n}", "map": {"version": 3, "names": ["React", "useReducer", "globalReducer", "jsx", "_jsx", "initialArg", "subscription", "subscriptionStatus", "lastSubscriptionStatusDate", "customerId", "subscriptionPeriodEnd", "accessToken", "signalFromWebsocket", "channelOrderedByNewSignal", "pushNotification", "token", "lastLoadTokenDate", "StoreContext", "createContext", "StoreProvider", "props", "_useReducer", "_useReducer2", "_slicedToArray", "state", "dispatch", "Provider", "value", "children"], "sources": ["C:/Users/<USER>/Desktop/Signals/CryptoSignalsApp/src/store/index.js"], "sourcesContent": ["import React, { useReducer } from 'react';\r\nimport globalReducer from './reducers';\r\n\r\nconst initialArg = {\r\n  subscription: {\r\n    subscriptionStatus: false,\r\n    lastSubscriptionStatusDate: null,\r\n    customerId: null,\r\n    subscriptionPeriodEnd: null,\r\n    accessToken: null\r\n  },\r\n  signalFromWebsocket: {},\r\n  channelOrderedByNewSignal: null,\r\n  pushNotification: {\r\n    token: null,\r\n    lastLoadTokenDate: null\r\n  }\r\n}\r\n\r\nexport const StoreContext = React.createContext(initialArg);\r\n\r\nexport default function StoreProvider(props) {\r\n  const [state, dispatch] = useReducer(globalReducer, initialArg);\r\n\r\n  return (\r\n    <StoreContext.Provider value={[ state, dispatch ]}>\r\n      {props.children}\r\n    </StoreContext.Provider>\r\n  );\r\n}\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,UAAU,QAAQ,OAAO;AACzC,OAAOC,aAAa;AAAmB,SAAAC,GAAA,IAAAC,IAAA;AAEvC,IAAMC,UAAU,GAAG;EACjBC,YAAY,EAAE;IACZC,kBAAkB,EAAE,KAAK;IACzBC,0BAA0B,EAAE,IAAI;IAChCC,UAAU,EAAE,IAAI;IAChBC,qBAAqB,EAAE,IAAI;IAC3BC,WAAW,EAAE;EACf,CAAC;EACDC,mBAAmB,EAAE,CAAC,CAAC;EACvBC,yBAAyB,EAAE,IAAI;EAC/BC,gBAAgB,EAAE;IAChBC,KAAK,EAAE,IAAI;IACXC,iBAAiB,EAAE;EACrB;AACF,CAAC;AAED,OAAO,IAAMC,YAAY,GAAGjB,KAAK,CAACkB,aAAa,CAACb,UAAU,CAAC;AAE3D,eAAe,SAASc,aAAaA,CAACC,KAAK,EAAE;EAC3C,IAAAC,WAAA,GAA0BpB,UAAU,CAACC,aAAa,EAAEG,UAAU,CAAC;IAAAiB,YAAA,GAAAC,cAAA,CAAAF,WAAA;IAAxDG,KAAK,GAAAF,YAAA;IAAEG,QAAQ,GAAAH,YAAA;EAEtB,OACElB,IAAA,CAACa,YAAY,CAACS,QAAQ;IAACC,KAAK,EAAE,CAAEH,KAAK,EAAEC,QAAQ,CAAG;IAAAG,QAAA,EAC/CR,KAAK,CAACQ;EAAQ,CACM,CAAC;AAE5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}