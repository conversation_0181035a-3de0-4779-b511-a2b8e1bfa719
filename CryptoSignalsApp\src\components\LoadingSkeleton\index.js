import React, { useEffect, useRef } from 'react';
import { View, Animated } from 'react-native';

const LoadingSkeleton = ({ 
  width = '100%', 
  height = 20, 
  borderRadius = 4,
  style 
}) => {
  const animatedValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: false,
        }),
        Animated.timing(animatedValue, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: false,
        }),
      ])
    );
    animation.start();
    return () => animation.stop();
  }, []);

  const backgroundColor = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['#2a2a2a', '#3a3a3a'],
  });

  return (
    <Animated.View
      style={[
        {
          width,
          height,
          borderRadius,
          backgroundColor,
        },
        style,
      ]}
    />
  );
};

const SkeletonCard = () => (
  <View style={{ 
    backgroundColor: '#2a2a2a', 
    borderRadius: 12, 
    padding: 16, 
    margin: 8,
    borderWidth: 1,
    borderColor: '#333'
  }}>
    <LoadingSkeleton width="60%" height={16} style={{ marginBottom: 8 }} />
    <LoadingSkeleton width="100%" height={12} style={{ marginBottom: 4 }} />
    <LoadingSkeleton width="80%" height={12} style={{ marginBottom: 8 }} />
    <LoadingSkeleton width="40%" height={10} />
  </View>
);

export { LoadingSkeleton, SkeletonCard };
