{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport * as React from 'react';\nimport NavigationBuilderContext from \"./NavigationBuilderContext\";\nimport NavigationContext from \"./NavigationContext\";\nimport NavigationRouteContext from \"./NavigationRouteContext\";\nimport SceneView from \"./SceneView\";\nimport useNavigationCache from \"./useNavigationCache\";\nimport useRouteCache from \"./useRouteCache\";\nexport default function useDescriptors(_ref) {\n  var state = _ref.state,\n    screens = _ref.screens,\n    navigation = _ref.navigation,\n    screenOptions = _ref.screenOptions,\n    defaultScreenOptions = _ref.defaultScreenOptions,\n    onAction = _ref.onAction,\n    getState = _ref.getState,\n    setState = _ref.setState,\n    addListener = _ref.addListener,\n    addKeyedListener = _ref.addKeyedListener,\n    onRouteFocus = _ref.onRouteFocus,\n    router = _ref.router,\n    emitter = _ref.emitter;\n  var _React$useState = React.useState({}),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    options = _React$useState2[0],\n    setOptions = _React$useState2[1];\n  var _React$useContext = React.useContext(NavigationBuilderContext),\n    onDispatchAction = _React$useContext.onDispatchAction,\n    onOptionsChange = _React$useContext.onOptionsChange,\n    stackRef = _React$useContext.stackRef;\n  var context = React.useMemo(function () {\n    return {\n      navigation: navigation,\n      onAction: onAction,\n      addListener: addListener,\n      addKeyedListener: addKeyedListener,\n      onRouteFocus: onRouteFocus,\n      onDispatchAction: onDispatchAction,\n      onOptionsChange: onOptionsChange,\n      stackRef: stackRef\n    };\n  }, [navigation, onAction, addListener, addKeyedListener, onRouteFocus, onDispatchAction, onOptionsChange, stackRef]);\n  var navigations = useNavigationCache({\n    state: state,\n    getState: getState,\n    navigation: navigation,\n    setOptions: setOptions,\n    router: router,\n    emitter: emitter\n  });\n  var routes = useRouteCache(state.routes);\n  return routes.reduce(function (acc, route, i) {\n    var config = screens[route.name];\n    var screen = config.props;\n    var navigation = navigations[route.key];\n    var optionsList = [screenOptions].concat(_toConsumableArray(config.options ? config.options.filter(Boolean) : []), [screen.options, options[route.key]]);\n    var customOptions = optionsList.reduce(function (acc, curr) {\n      return Object.assign(acc, typeof curr !== 'function' ? curr : curr({\n        route: route,\n        navigation: navigation\n      }));\n    }, {});\n    var mergedOptions = _objectSpread(_objectSpread({}, typeof defaultScreenOptions === 'function' ? defaultScreenOptions({\n      route: route,\n      navigation: navigation,\n      options: customOptions\n    }) : defaultScreenOptions), customOptions);\n    var clearOptions = function clearOptions() {\n      return setOptions(function (o) {\n        if (route.key in o) {\n          var _route$key = route.key,\n            _ = o[_route$key],\n            rest = _objectWithoutProperties(o, [_route$key].map(_toPropertyKey));\n          return rest;\n        }\n        return o;\n      });\n    };\n    acc[route.key] = {\n      route: route,\n      navigation: navigation,\n      render: function render() {\n        return React.createElement(NavigationBuilderContext.Provider, {\n          key: route.key,\n          value: context\n        }, React.createElement(NavigationContext.Provider, {\n          value: navigation\n        }, React.createElement(NavigationRouteContext.Provider, {\n          value: route\n        }, React.createElement(SceneView, {\n          navigation: navigation,\n          route: route,\n          screen: screen,\n          routeState: state.routes[i].state,\n          getState: getState,\n          setState: setState,\n          options: mergedOptions,\n          clearOptions: clearOptions\n        }))));\n      },\n      options: mergedOptions\n    };\n    return acc;\n  }, {});\n}", "map": {"version": 3, "names": ["React", "NavigationBuilderContext", "NavigationContext", "NavigationRouteContext", "SceneView", "useNavigationCache", "useRouteCache", "useDescriptors", "_ref", "state", "screens", "navigation", "screenOptions", "defaultScreenOptions", "onAction", "getState", "setState", "addListener", "addKeyedListener", "onRouteFocus", "router", "emitter", "_React$useState", "useState", "_React$useState2", "_slicedToArray", "options", "setOptions", "_React$useContext", "useContext", "onDispatchAction", "onOptionsChange", "stackRef", "context", "useMemo", "navigations", "routes", "reduce", "acc", "route", "i", "config", "name", "screen", "props", "key", "optionsList", "concat", "_toConsumableArray", "filter", "Boolean", "customOptions", "curr", "Object", "assign", "mergedOptions", "_objectSpread", "clearOptions", "o", "_route$key", "_", "rest", "_objectWithoutProperties", "map", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "render", "createElement", "Provider", "value", "routeState"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\@react-navigation\\core\\src\\useDescriptors.tsx"], "sourcesContent": ["import type {\n  NavigationAction,\n  NavigationState,\n  ParamListBase,\n  Router,\n} from '@react-navigation/routers';\nimport * as React from 'react';\n\nimport NavigationBuilderContext, {\n  AddKeyedListener,\n  AddListener,\n} from './NavigationBuilderContext';\nimport NavigationContext from './NavigationContext';\nimport NavigationRouteContext from './NavigationRouteContext';\nimport SceneView from './SceneView';\nimport type {\n  Descriptor,\n  EventMapBase,\n  NavigationHelpers,\n  NavigationProp,\n  RouteConfig,\n  RouteProp,\n} from './types';\nimport type { NavigationEventEmitter } from './useEventEmitter';\nimport useNavigationCache from './useNavigationCache';\nimport useRouteCache from './useRouteCache';\n\nexport type ScreenConfigWithParent<\n  State extends NavigationState,\n  ScreenOptions extends {},\n  EventMap extends EventMapBase\n> = {\n  keys: (string | undefined)[];\n  options: (ScreenOptionsOrCallback<ScreenOptions> | undefined)[] | undefined;\n  props: RouteConfig<ParamListBase, string, State, ScreenOptions, EventMap>;\n};\n\ntype ScreenOptionsOrCallback<ScreenOptions extends {}> =\n  | ScreenOptions\n  | ((props: {\n      route: RouteProp<ParamListBase, string>;\n      navigation: any;\n    }) => ScreenOptions);\n\ntype Options<\n  State extends NavigationState,\n  ScreenOptions extends {},\n  EventMap extends EventMapBase\n> = {\n  state: State;\n  screens: Record<\n    string,\n    ScreenConfigWithParent<State, ScreenOptions, EventMap>\n  >;\n  navigation: NavigationHelpers<ParamListBase>;\n  screenOptions?: ScreenOptionsOrCallback<ScreenOptions>;\n  defaultScreenOptions?:\n    | ScreenOptions\n    | ((props: {\n        route: RouteProp<ParamListBase>;\n        navigation: any;\n        options: ScreenOptions;\n      }) => ScreenOptions);\n  onAction: (action: NavigationAction) => boolean;\n  getState: () => State;\n  setState: (state: State) => void;\n  addListener: AddListener;\n  addKeyedListener: AddKeyedListener;\n  onRouteFocus: (key: string) => void;\n  router: Router<State, NavigationAction>;\n  emitter: NavigationEventEmitter<EventMap>;\n};\n\n/**\n * Hook to create descriptor objects for the child routes.\n *\n * A descriptor object provides 3 things:\n * - Helper method to render a screen\n * - Options specified by the screen for the navigator\n * - Navigation object intended for the route\n */\nexport default function useDescriptors<\n  State extends NavigationState,\n  ActionHelpers extends Record<string, () => void>,\n  ScreenOptions extends {},\n  EventMap extends EventMapBase\n>({\n  state,\n  screens,\n  navigation,\n  screenOptions,\n  defaultScreenOptions,\n  onAction,\n  getState,\n  setState,\n  addListener,\n  addKeyedListener,\n  onRouteFocus,\n  router,\n  emitter,\n}: Options<State, ScreenOptions, EventMap>) {\n  const [options, setOptions] = React.useState<Record<string, object>>({});\n  const { onDispatchAction, onOptionsChange, stackRef } = React.useContext(\n    NavigationBuilderContext\n  );\n\n  const context = React.useMemo(\n    () => ({\n      navigation,\n      onAction,\n      addListener,\n      addKeyedListener,\n      onRouteFocus,\n      onDispatchAction,\n      onOptionsChange,\n      stackRef,\n    }),\n    [\n      navigation,\n      onAction,\n      addListener,\n      addKeyedListener,\n      onRouteFocus,\n      onDispatchAction,\n      onOptionsChange,\n      stackRef,\n    ]\n  );\n\n  const navigations = useNavigationCache<State, ScreenOptions, EventMap>({\n    state,\n    getState,\n    navigation,\n    setOptions,\n    router,\n    emitter,\n  });\n\n  const routes = useRouteCache(state.routes);\n\n  return routes.reduce<\n    Record<\n      string,\n      Descriptor<\n        ScreenOptions,\n        NavigationProp<\n          ParamListBase,\n          string,\n          string | undefined,\n          State,\n          ScreenOptions,\n          EventMap\n        > &\n          ActionHelpers,\n        RouteProp<ParamListBase>\n      >\n    >\n  >((acc, route, i) => {\n    const config = screens[route.name];\n    const screen = config.props;\n    const navigation = navigations[route.key];\n\n    const optionsList = [\n      // The default `screenOptions` passed to the navigator\n      screenOptions,\n      // The `screenOptions` props passed to `Group` elements\n      ...((config.options\n        ? config.options.filter(Boolean)\n        : []) as ScreenOptionsOrCallback<ScreenOptions>[]),\n      // The `options` prop passed to `Screen` elements,\n      screen.options,\n      // The options set via `navigation.setOptions`\n      options[route.key],\n    ];\n\n    const customOptions = optionsList.reduce<ScreenOptions>(\n      (acc, curr) =>\n        Object.assign(\n          acc,\n          // @ts-expect-error: we check for function but TS still complains\n          typeof curr !== 'function' ? curr : curr({ route, navigation })\n        ),\n      {} as ScreenOptions\n    );\n\n    const mergedOptions = {\n      ...(typeof defaultScreenOptions === 'function'\n        ? // @ts-expect-error: ts gives incorrect error here\n          defaultScreenOptions({\n            route,\n            navigation,\n            options: customOptions,\n          })\n        : defaultScreenOptions),\n      ...customOptions,\n    };\n\n    const clearOptions = () =>\n      setOptions((o) => {\n        if (route.key in o) {\n          // eslint-disable-next-line @typescript-eslint/no-unused-vars\n          const { [route.key]: _, ...rest } = o;\n          return rest;\n        }\n\n        return o;\n      });\n\n    acc[route.key] = {\n      route,\n      // @ts-expect-error: it's missing action helpers, fix later\n      navigation,\n      render() {\n        return (\n          <NavigationBuilderContext.Provider key={route.key} value={context}>\n            <NavigationContext.Provider value={navigation}>\n              <NavigationRouteContext.Provider value={route}>\n                <SceneView\n                  navigation={navigation}\n                  route={route}\n                  screen={screen}\n                  routeState={state.routes[i].state}\n                  getState={getState}\n                  setState={setState}\n                  options={mergedOptions}\n                  clearOptions={clearOptions}\n                />\n              </NavigationRouteContext.Provider>\n            </NavigationContext.Provider>\n          </NavigationBuilderContext.Provider>\n        );\n      },\n      options: mergedOptions as ScreenOptions,\n    };\n\n    return acc;\n  }, {});\n}\n"], "mappings": ";;;;;;;;AAMA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,OAAOC,wBAAwB;AAI/B,OAAOC,iBAAiB;AACxB,OAAOC,sBAAsB;AAC7B,OAAOC,SAAS;AAUhB,OAAOC,kBAAkB;AACzB,OAAOC,aAAa;AAwDpB,eAAe,SAASC,cAAcA,CAAAC,IAAA,EAmBM;EAAA,IAb1CC,KAAK,GAamCD,IAAA,CAbxCC,KAAK;IACLC,OAAO,GAYiCF,IAAA,CAZxCE,OAAO;IACPC,UAAU,GAW8BH,IAAA,CAXxCG,UAAU;IACVC,aAAa,GAU2BJ,IAAA,CAVxCI,aAAa;IACbC,oBAAoB,GASoBL,IAAA,CATxCK,oBAAoB;IACpBC,QAAQ,GAQgCN,IAAA,CARxCM,QAAQ;IACRC,QAAQ,GAOgCP,IAAA,CAPxCO,QAAQ;IACRC,QAAQ,GAMgCR,IAAA,CANxCQ,QAAQ;IACRC,WAAW,GAK6BT,IAAA,CALxCS,WAAW;IACXC,gBAAgB,GAIwBV,IAAA,CAJxCU,gBAAgB;IAChBC,YAAY,GAG4BX,IAAA,CAHxCW,YAAY;IACZC,MAAM,GAEkCZ,IAAA,CAFxCY,MAAM;IACNC,OAAA,GACwCb,IAAA,CADxCa,OAAA;EAEA,IAAAC,eAAA,GAA8BtB,KAAK,CAACuB,QAAQ,CAAyB,CAAC,CAAC,CAAC;IAAAC,gBAAA,GAAAC,cAAA,CAAAH,eAAA;IAAjEI,OAAO,GAAAF,gBAAA;IAAEG,UAAU,GAAAH,gBAAA;EAC1B,IAAAI,iBAAA,GAAwD5B,KAAK,CAAC6B,UAAU,CACtE5B,wBAAwB,CACzB;IAFO6B,gBAAgB,GAAAF,iBAAA,CAAhBE,gBAAgB;IAAEC,eAAe,GAAAH,iBAAA,CAAfG,eAAe;IAAEC,QAAA,GAAAJ,iBAAA,CAAAI,QAAA;EAI3C,IAAMC,OAAO,GAAGjC,KAAK,CAACkC,OAAO,CAC3B;IAAA,OAAO;MACLvB,UAAU,EAAVA,UAAU;MACVG,QAAQ,EAARA,QAAQ;MACRG,WAAW,EAAXA,WAAW;MACXC,gBAAgB,EAAhBA,gBAAgB;MAChBC,YAAY,EAAZA,YAAY;MACZW,gBAAgB,EAAhBA,gBAAgB;MAChBC,eAAe,EAAfA,eAAe;MACfC,QAAA,EAAAA;IACF,CAAC;EAAA,CAAC,EACF,CACErB,UAAU,EACVG,QAAQ,EACRG,WAAW,EACXC,gBAAgB,EAChBC,YAAY,EACZW,gBAAgB,EAChBC,eAAe,EACfC,QAAQ,CACT,CACF;EAED,IAAMG,WAAW,GAAG9B,kBAAkB,CAAiC;IACrEI,KAAK,EAALA,KAAK;IACLM,QAAQ,EAARA,QAAQ;IACRJ,UAAU,EAAVA,UAAU;IACVgB,UAAU,EAAVA,UAAU;IACVP,MAAM,EAANA,MAAM;IACNC,OAAA,EAAAA;EACF,CAAC,CAAC;EAEF,IAAMe,MAAM,GAAG9B,aAAa,CAACG,KAAK,CAAC2B,MAAM,CAAC;EAE1C,OAAOA,MAAM,CAACC,MAAM,CAiBlB,UAACC,GAAG,EAAEC,KAAK,EAAEC,CAAC,EAAK;IACnB,IAAMC,MAAM,GAAG/B,OAAO,CAAC6B,KAAK,CAACG,IAAI,CAAC;IAClC,IAAMC,MAAM,GAAGF,MAAM,CAACG,KAAK;IAC3B,IAAMjC,UAAU,GAAGwB,WAAW,CAACI,KAAK,CAACM,GAAG,CAAC;IAEzC,IAAMC,WAAW,IAEflC,aAAa,EAAAmC,MAAA,CAAAC,kBAAA,CAERP,MAAM,CAACf,OAAO,GACfe,MAAM,CAACf,OAAO,CAACuB,MAAM,CAACC,OAAO,CAAC,GAC9B,EAAE,IAENP,MAAM,CAACjB,OAAO,EAEdA,OAAO,CAACa,KAAK,CAACM,GAAG,CAAC,EACnB;IAED,IAAMM,aAAa,GAAGL,WAAW,CAACT,MAAM,CACtC,UAACC,GAAG,EAAEc,IAAI;MAAA,OACRC,MAAM,CAACC,MAAM,CACXhB,GAAG,EAEH,OAAOc,IAAI,KAAK,UAAU,GAAGA,IAAI,GAAGA,IAAI,CAAC;QAAEb,KAAK,EAALA,KAAK;QAAE5B,UAAA,EAAAA;MAAW,CAAC,CAAC,CAChE;IAAA,GACH,CAAC,CAAC,CACH;IAED,IAAM4C,aAAa,GAAAC,aAAA,CAAAA,aAAA,KACb,OAAO3C,oBAAoB,KAAK,UAAU,GAE1CA,oBAAoB,CAAC;MACnB0B,KAAK,EAALA,KAAK;MACL5B,UAAU,EAAVA,UAAU;MACVe,OAAO,EAAEyB;IACX,CAAC,CAAC,GACFtC,oBAAoB,GACrBsC,aAAA,CACJ;IAED,IAAMM,YAAY,GAAG,SAAfA,YAAYA,CAAA;MAAA,OAChB9B,UAAU,CAAE,UAAA+B,CAAC,EAAK;QAChB,IAAInB,KAAK,CAACM,GAAG,IAAIa,CAAC,EAAE;UAElB,IAAAC,UAAA,GAASpB,KAAK,CAACM,GAAG;YAAGe,CAAC,GAAcF,CAAC,CAAAC,UAAA;YAAVE,IAAA,GAAAC,wBAAA,CAASJ,CAAC,GAAAC,UAAA,EAAAI,GAAA,CAAAC,cAAA;UACrC,OAAOH,IAAI;QACb;QAEA,OAAOH,CAAC;MACV,CAAC,CAAC;IAAA;IAEJpB,GAAG,CAACC,KAAK,CAACM,GAAG,CAAC,GAAG;MACfN,KAAK,EAALA,KAAK;MAEL5B,UAAU,EAAVA,UAAU;MACVsD,MAAM,WAANA,MAAMA,CAAA,EAAG;QACP,OACEjE,KAAA,CAAAkE,aAAA,CAACjE,wBAAwB,CAACkE,QAAQ;UAACtB,GAAG,EAAEN,KAAK,CAACM,GAAI;UAACuB,KAAK,EAAEnC;QAAQ,GAChEjC,KAAA,CAAAkE,aAAA,CAAChE,iBAAiB,CAACiE,QAAQ;UAACC,KAAK,EAAEzD;QAAW,GAC5CX,KAAA,CAAAkE,aAAA,CAAC/D,sBAAsB,CAACgE,QAAQ;UAACC,KAAK,EAAE7B;QAAM,GAC5CvC,KAAA,CAAAkE,aAAA,CAAC9D,SAAS;UACRO,UAAU,EAAEA,UAAW;UACvB4B,KAAK,EAAEA,KAAM;UACbI,MAAM,EAAEA,MAAO;UACf0B,UAAU,EAAE5D,KAAK,CAAC2B,MAAM,CAACI,CAAC,CAAC,CAAC/B,KAAM;UAClCM,QAAQ,EAAEA,QAAS;UACnBC,QAAQ,EAAEA,QAAS;UACnBU,OAAO,EAAE6B,aAAc;UACvBE,YAAY,EAAEA;QAAa,EAC3B,CAC8B,CACP,CACK;MAExC,CAAC;MACD/B,OAAO,EAAE6B;IACX,CAAC;IAED,OAAOjB,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;AACR", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}