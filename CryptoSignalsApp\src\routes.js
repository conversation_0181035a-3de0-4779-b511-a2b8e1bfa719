import React from "react";
import { createStackNavigator } from "@react-navigation/stack";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { Text } from "react-native";

// Import essential pages only
import Channels from "./pages/Channels";
import Signals from "./pages/Signals";
import Premium from "./pages/Premium";
import Profile from "./pages/Profile";

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();

// Simplified Tab Navigator with essential features only
function MainTabNavigator() {
  const labelStyle = {
    fontFamily: 'Poppins_500Medium',
    fontSize: 10,
  }

  const getTabBarIcon = (routeName, focused) => {
    let iconName;
    switch (routeName) {
      case 'Channels':
        iconName = focused ? '📺' : '📺';
        break;
      case 'Premium':
        iconName = focused ? '💎' : '💎';
        break;
      case 'Profile':
        iconName = focused ? '👤' : '👤';
        break;
      default:
        iconName = '📱';
    }
    return iconName;
  };

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        headerShown: false,
        tabBarIcon: ({ focused }) => (
          <Text style={{
            fontSize: focused ? 18 : 16,
            opacity: focused ? 1 : 0.7,
            marginBottom: 2
          }}>
            {getTabBarIcon(route.name, focused)}
          </Text>
        ),
        tabBarLabelPosition: 'below-icon',
        tabBarStyle: [
          {
            backgroundColor: "#202020",
            height: 70,
            borderTopColor: "#5d5d5d",
            paddingBottom: 8,
            paddingTop: 8,
          },
        ],
        tabBarLabel: ({ focused }) => {
          return (
            <Text style={{
              ...labelStyle,
              color: focused ? "#FECB37" : "#8a8a8a",
              marginTop: 2
            }}>
              {route.name}
            </Text>
          );
        },
      })}
    >
      <Tab.Screen
        name="Channels"
        component={Channels}
        options={{
          tabBarLabel: 'Signals'
        }}
      />
      <Tab.Screen
        name="Premium"
        component={Premium}
        options={{
          tabBarLabel: 'Premium'
        }}
      />
      <Tab.Screen
        name="Profile"
        component={Profile}
        options={{
          tabBarLabel: 'Profile'
        }}
      />
    </Tab.Navigator>
  );
}

// Stack Navigator for modal screens and navigation
function AppNavigator() {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="Main" component={MainTabNavigator} />
      <Stack.Screen name="Signals" component={Signals} />
    </Stack.Navigator>
  );
}

export default function Routes() {
  return (
    <AppNavigator />
  );
}

/*
Aqui estão as mudanças que fiz:

Integrei o Drawer.Navigator diretamente no Routes para evitar redundância.
Removi a função AppNavigator que não estava sendo utilizada.
A estrutura agora é que temos um Stack.Navigator principal com uma combinação de Drawer.Navigator e
 Tab.Navigator para uma melhor organização e navegação.
*/