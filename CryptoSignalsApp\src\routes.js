import React from "react";
import { createStackNavigator } from "@react-navigation/stack";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { Text } from "react-native";

// Import pages
import Home from "./pages/Home";
import Channels from "./pages/Channels";
import Signals from "./pages/Signals";
import Premium from "./pages/Premium";
import Academy from "./pages/Academy";
import News from "./pages/News";
import Settings from "./pages/Settings";
import User from "./pages/User";
import Forum from "./pages/Forum";
import CompareCripto from "./pages/CompareCripto";
import Rank from "./pages/Rank";

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();

// Main Tab Navigator with all features
function ExtendedTabNavigator() {
  const labelStyle = {
    fontFamily: 'Poppins_500Medium',
    fontSize: 10,
  }

  const getTabBarIcon = (routeName, focused) => {
    let iconName;
    switch (routeName) {
      case 'Home':
        iconName = focused ? '🏠' : '🏠';
        break;
      case 'Channels':
        iconName = focused ? '📺' : '📺';
        break;
      case 'News':
        iconName = focused ? '📰' : '📰';
        break;
      case 'Academy':
        iconName = focused ? '🎓' : '🎓';
        break;
      case 'Settings':
        iconName = focused ? '⚙️' : '⚙️';
        break;
      case 'Premium':
        iconName = focused ? '💎' : '💎';
        break;
      default:
        iconName = '📱';
    }
    return iconName;
  };

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        headerShown: false,
        tabBarIcon: ({ focused }) => (
          <Text style={{
            fontSize: focused ? 18 : 16,
            opacity: focused ? 1 : 0.7,
            marginBottom: 2
          }}>
            {getTabBarIcon(route.name, focused)}
          </Text>
        ),
        tabBarLabelPosition: 'below-icon',
        tabBarStyle: [
          {
            backgroundColor: "#202020",
            height: 70,
            borderTopColor: "#5d5d5d",
            paddingBottom: 8,
            paddingTop: 8,
          },
        ],
        tabBarLabel: ({ focused }) => {
          return (
            <Text style={{
              ...labelStyle,
              color: focused ? "#FECB37" : "#8a8a8a",
              marginTop: 2
            }}>
              {route.name}
            </Text>
          );
        },
      })}
    >
      <Tab.Screen name="Home" component={Home} />
      <Tab.Screen name="Channels" component={Channels} />
      <Tab.Screen name="News" component={News} />
      <Tab.Screen name="Academy" component={Academy} />
      <Tab.Screen name="Settings" component={Settings} />
      <Tab.Screen name="Premium" component={Premium} />
    </Tab.Navigator>
  );
}

// Stack Navigator for modal screens and navigation
function AppNavigator() {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="Main" component={ExtendedTabNavigator} />
      <Stack.Screen name="Signals" component={Signals} />
      <Stack.Screen name="User" component={User} />
      <Stack.Screen name="Forum" component={Forum} />
      <Stack.Screen name="CompareCripto" component={CompareCripto} />
      <Stack.Screen name="Rank" component={Rank} />
    </Stack.Navigator>
  );
}

export default function Routes() {
  return (
    <AppNavigator />
  );
}

/*
Aqui estão as mudanças que fiz:

Integrei o Drawer.Navigator diretamente no Routes para evitar redundância.
Removi a função AppNavigator que não estava sendo utilizada.
A estrutura agora é que temos um Stack.Navigator principal com uma combinação de Drawer.Navigator e
 Tab.Navigator para uma melhor organização e navegação.
*/