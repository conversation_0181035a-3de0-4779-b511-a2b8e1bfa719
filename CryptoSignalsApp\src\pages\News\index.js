import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, TouchableOpacity, FlatList, RefreshControl } from 'react-native';
import { Searchbar, Button, Chip } from 'react-native-paper';
import PageTitle from '../../components/PageTitle';
import Wrapper from '../../components/Wrapper';
import Card from '../../components/Card';
import { SkeletonCard } from '../../components/LoadingSkeleton';
import styles from './styles';

const News = () => {
  const [newsData, setNewsData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');

  const categories = ['All', 'Bitcoin', 'Ethereum', 'DeFi', 'NFTs', 'Regulation', 'Market Analysis'];

  useEffect(() => {
    // Simulate fetching news
    const fetchedNews = [
      {
        id: '1',
        title: 'Bitcoin Reaches New All-Time High Above $65,000',
        category: 'Bitcoin',
        date: '2024-01-15',
        time: '2 hours ago',
        content: 'Bitcoin has surged to a new all-time high, breaking through the $65,000 resistance level amid institutional adoption and positive market sentiment. The cryptocurrency has gained over 15% in the past week.',
        author: 'CryptoNews Team',
        readTime: '3 min read',
        trending: true,
        image: '📈'
      },
      {
        id: '2',
        title: 'Ethereum 2.0 Staking Rewards Hit Record Levels',
        category: 'Ethereum',
        date: '2024-01-15',
        time: '4 hours ago',
        content: 'Ethereum staking rewards have reached unprecedented levels as more validators join the network, strengthening the blockchain\'s security and decentralization.',
        author: 'DeFi Analyst',
        readTime: '5 min read',
        trending: false,
        image: '⚡'
      },
      {
        id: '3',
        title: 'Major DeFi Protocol Launches Cross-Chain Bridge',
        category: 'DeFi',
        date: '2024-01-14',
        time: '6 hours ago',
        content: 'A leading DeFi protocol has announced the launch of its cross-chain bridge, enabling seamless asset transfers between multiple blockchains.',
        author: 'Blockchain Reporter',
        readTime: '4 min read',
        trending: true,
        image: '🌉'
      },
      {
        id: '4',
        title: 'NFT Market Shows Signs of Recovery',
        category: 'NFTs',
        date: '2024-01-14',
        time: '8 hours ago',
        content: 'The NFT market is showing positive signs with increased trading volume and new collections gaining traction among collectors.',
        author: 'NFT Insider',
        readTime: '3 min read',
        trending: false,
        image: '🎨'
      },
      {
        id: '5',
        title: 'New Crypto Regulations Proposed in Europe',
        category: 'Regulation',
        date: '2024-01-13',
        time: '1 day ago',
        content: 'European regulators have proposed new comprehensive cryptocurrency regulations aimed at protecting investors while fostering innovation.',
        author: 'Regulatory Watch',
        readTime: '6 min read',
        trending: false,
        image: '⚖️'
      }
    ];

    setTimeout(() => {
      setNewsData(fetchedNews);
      setLoading(false);
    }, 1000);
  }, []);

  const onRefresh = () => {
    setRefreshing(true);
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };

  const filteredNews = newsData.filter(news => {
    const matchesSearch = news.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         news.content.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'All' || news.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  if (loading) {
    return (
      <Wrapper>
        <PageTitle text="Crypto News" />
        <ScrollView style={{ padding: 16 }}>
          <SkeletonCard />
          <SkeletonCard />
          <SkeletonCard />
        </ScrollView>
      </Wrapper>
    );
  }

  return (
    <Wrapper>
      <ScrollView
        style={{ flex: 1 }}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
      >
        {/* Header */}
        <View style={{ padding: 16, paddingBottom: 8 }}>
          <Text style={{
            color: '#fff',
            fontSize: 28,
            fontFamily: 'Poppins_700Bold',
            marginBottom: 4
          }}>
            Crypto News 📰
          </Text>
          <Text style={{
            color: '#8a8a8a',
            fontSize: 14,
            fontFamily: 'Poppins_400Regular'
          }}>
            Stay updated with the latest cryptocurrency news
          </Text>
        </View>

        {/* Search Bar */}
        <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>
          <Searchbar
            placeholder="Search news articles..."
            onChangeText={setSearchQuery}
            value={searchQuery}
            style={{ backgroundColor: '#2a2a2a', elevation: 0 }}
            inputStyle={{ color: '#fff' }}
            iconColor="#8a8a8a"
            placeholderTextColor="#8a8a8a"
          />
        </View>

        {/* Categories */}
        <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {categories.map((category) => (
              <Chip
                key={category}
                selected={selectedCategory === category}
                onPress={() => setSelectedCategory(category)}
                style={{
                  marginRight: 8,
                  backgroundColor: selectedCategory === category ? '#FECB37' : '#2a2a2a'
                }}
                textStyle={{
                  color: selectedCategory === category ? '#000' : '#fff',
                  fontFamily: 'Poppins_500Medium'
                }}
              >
                {category}
              </Chip>
            ))}
          </ScrollView>
        </View>

        {/* Trending News */}
        <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>
          <Text style={{
            color: '#fff',
            fontSize: 18,
            fontFamily: 'Poppins_600SemiBold',
            marginBottom: 12
          }}>
            Trending Now 🔥
          </Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {filteredNews.filter(news => news.trending).map((news) => (
              <Card key={news.id} style={{ marginRight: 12, width: 280 }}>
                <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
                  <Text style={{ fontSize: 20, marginRight: 8 }}>{news.image}</Text>
                  <View style={{
                    backgroundColor: '#FECB37',
                    paddingHorizontal: 6,
                    paddingVertical: 2,
                    borderRadius: 4
                  }}>
                    <Text style={{
                      color: '#000',
                      fontSize: 10,
                      fontFamily: 'Poppins_600SemiBold'
                    }}>
                      {news.category}
                    </Text>
                  </View>
                </View>
                <Text style={{
                  color: '#fff',
                  fontSize: 14,
                  fontFamily: 'Poppins_600SemiBold',
                  marginBottom: 8,
                  lineHeight: 20
                }}>
                  {news.title}
                </Text>
                <Text style={{
                  color: '#8a8a8a',
                  fontSize: 12,
                  fontFamily: 'Poppins_400Regular'
                }}>
                  {news.time} • {news.readTime}
                </Text>
              </Card>
            ))}
          </ScrollView>
        </View>

        {/* All News */}
        <View style={{ paddingHorizontal: 16, marginBottom: 20 }}>
          <Text style={{
            color: '#fff',
            fontSize: 18,
            fontFamily: 'Poppins_600SemiBold',
            marginBottom: 12
          }}>
            Latest Articles
          </Text>

          {filteredNews.length === 0 ? (
            <Card>
              <Text style={{
                color: '#8a8a8a',
                fontSize: 14,
                fontFamily: 'Poppins_400Regular',
                textAlign: 'center',
                padding: 20
              }}>
                No articles found matching your search criteria.
              </Text>
            </Card>
          ) : (
            filteredNews.map((news) => (
              <Card key={news.id} style={{ marginBottom: 12 }}>
                <View style={{ flexDirection: 'row', alignItems: 'flex-start' }}>
                  <View style={{
                    backgroundColor: '#333',
                    borderRadius: 8,
                    padding: 12,
                    marginRight: 12,
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <Text style={{ fontSize: 24 }}>{news.image}</Text>
                  </View>

                  <View style={{ flex: 1 }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 4 }}>
                      <View style={{
                        backgroundColor: news.trending ? '#FECB37' : '#333',
                        paddingHorizontal: 6,
                        paddingVertical: 2,
                        borderRadius: 4,
                        marginRight: 8
                      }}>
                        <Text style={{
                          color: news.trending ? '#000' : '#fff',
                          fontSize: 10,
                          fontFamily: 'Poppins_600SemiBold'
                        }}>
                          {news.category}
                        </Text>
                      </View>
                      {news.trending && (
                        <Text style={{ fontSize: 12 }}>🔥</Text>
                      )}
                    </View>

                    <Text style={{
                      color: '#fff',
                      fontSize: 16,
                      fontFamily: 'Poppins_600SemiBold',
                      marginBottom: 6,
                      lineHeight: 22
                    }}>
                      {news.title}
                    </Text>

                    <Text style={{
                      color: '#ccc',
                      fontSize: 13,
                      fontFamily: 'Poppins_400Regular',
                      marginBottom: 8,
                      lineHeight: 18
                    }}>
                      {news.content.substring(0, 120)}...
                    </Text>

                    <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
                      <View>
                        <Text style={{
                          color: '#8a8a8a',
                          fontSize: 11,
                          fontFamily: 'Poppins_400Regular'
                        }}>
                          By {news.author}
                        </Text>
                        <Text style={{
                          color: '#8a8a8a',
                          fontSize: 11,
                          fontFamily: 'Poppins_400Regular'
                        }}>
                          {news.time} • {news.readTime}
                        </Text>
                      </View>
                      <TouchableOpacity>
                        <Text style={{
                          color: '#FECB37',
                          fontSize: 12,
                          fontFamily: 'Poppins_500Medium'
                        }}>
                          Read More
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                </View>
              </Card>
            ))
          )}
        </View>
      </ScrollView>
    </Wrapper>
  );
};

export default News;
