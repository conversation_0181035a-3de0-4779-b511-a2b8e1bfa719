{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport React, { useState, useEffect } from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport ScrollView from \"react-native-web/dist/exports/ScrollView\";\nimport TouchableOpacity from \"react-native-web/dist/exports/TouchableOpacity\";\nimport DropDownPicker from 'react-native-dropdown-picker';\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar CryptoComparison = function CryptoComparison() {\n  var _useState = useState(null),\n    _useState2 = _slicedToArray(_useState, 2),\n    crypto1 = _useState2[0],\n    setCrypto1 = _useState2[1];\n  var _useState3 = useState(null),\n    _useState4 = _slicedToArray(_useState3, 2),\n    crypto2 = _useState4[0],\n    setCrypto2 = _useState4[1];\n  var _useState5 = useState({}),\n    _useState6 = _slicedToArray(_useState5, 2),\n    comparisonData = _useState6[0],\n    setComparisonData = _useState6[1];\n  var fetchComparisonData = function fetchComparisonData() {};\n  useEffect(function () {\n    if (crypto1 && crypto2) {\n      fetchComparisonData();\n    }\n  }, [crypto1, crypto2]);\n  return _jsx(ScrollView, {\n    children: _jsxs(View, {\n      children: [_jsx(Text, {\n        children: \"Selecione duas criptomoedas para comparar:\"\n      }), _jsx(DropDownPicker, {\n        items: [{\n          label: 'Bitcoin',\n          value: 'btc'\n        }, {\n          label: 'Ethereum',\n          value: 'eth'\n        }],\n        placeholder: \"Selecione a primeira criptomoeda\",\n        onChangeItem: function onChangeItem(item) {\n          return setCrypto1(item.value);\n        }\n      }), _jsx(DropDownPicker, {\n        items: [{\n          label: 'Bitcoin',\n          value: 'btc'\n        }, {\n          label: 'Ethereum',\n          value: 'eth'\n        }],\n        placeholder: \"Selecione a segunda criptomoeda\",\n        onChangeItem: function onChangeItem(item) {\n          return setCrypto2(item.value);\n        }\n      }), _jsx(TouchableOpacity, {\n        onPress: fetchComparisonData,\n        children: _jsx(Text, {\n          children: \"Comparar\"\n        })\n      })]\n    })\n  });\n};\nexport default CryptoComparison;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "View", "Text", "ScrollView", "TouchableOpacity", "DropDownPicker", "jsx", "_jsx", "jsxs", "_jsxs", "CryptoComparison", "_useState", "_useState2", "_slicedToArray", "crypto1", "setCrypto1", "_useState3", "_useState4", "crypto2", "setCrypto2", "_useState5", "_useState6", "comparisonData", "setComparisonData", "fetchComparisonData", "children", "items", "label", "value", "placeholder", "onChangeItem", "item", "onPress"], "sources": ["E:/CryptoSignalsApp/src/pages/CompareCripto/index.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { View, Text, ScrollView, TouchableOpacity } from 'react-native';\r\nimport DropDownPicker from 'react-native-dropdown-picker'; // Exemplo de biblioteca de lista suspensa\r\n\r\nconst CryptoComparison = () => {\r\n  const [crypto1, setCrypto1] = useState(null);\r\n  const [crypto2, setCrypto2] = useState(null);\r\n  const [comparisonData, setComparisonData] = useState({}); // Dados de comparação\r\n\r\n  // Função para buscar dados das criptomoedas selecionadas\r\n  const fetchComparisonData = () => {\r\n    // Lógica para buscar os dados relevantes das criptomoedas\r\n    // e atualizar o estado de comparisonData\r\n  };\r\n\r\n  // Efeito para buscar dados quando as criptomoedas são selecionadas\r\n  useEffect(() => {\r\n    if (crypto1 && crypto2) {\r\n      fetchComparisonData();\r\n    }\r\n  }, [crypto1, crypto2]);\r\n\r\n  return (\r\n    <ScrollView>\r\n      <View>\r\n        <Text>Selecione duas criptomoedas para comparar:</Text>\r\n        <DropDownPicker\r\n          items={[\r\n            { label: 'Bitcoin', value: 'btc' },\r\n            { label: 'Ethereum', value: 'eth' },\r\n            // Adicione mais criptomoedas aqui\r\n          ]}\r\n          placeholder=\"Selecione a primeira criptomoeda\"\r\n          onChangeItem={(item) => setCrypto1(item.value)}\r\n        />\r\n        <DropDownPicker\r\n          items={[\r\n            { label: 'Bitcoin', value: 'btc' },\r\n            { label: 'Ethereum', value: 'eth' },\r\n            // Adicione mais criptomoedas aqui\r\n          ]}\r\n          placeholder=\"Selecione a segunda criptomoeda\"\r\n          onChangeItem={(item) => setCrypto2(item.value)}\r\n        />\r\n        <TouchableOpacity onPress={fetchComparisonData}>\r\n          <Text>Comparar</Text>\r\n        </TouchableOpacity>\r\n      </View>\r\n      {/* Exibir informações de comparação aqui */}\r\n    </ScrollView>\r\n  );\r\n};\r\n\r\nexport default CryptoComparison;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,gBAAA;AAEnD,OAAOC,cAAc,MAAM,8BAA8B;AAAC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAE1D,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA,EAAS;EAC7B,IAAAC,SAAA,GAA8BZ,QAAQ,CAAC,IAAI,CAAC;IAAAa,UAAA,GAAAC,cAAA,CAAAF,SAAA;IAArCG,OAAO,GAAAF,UAAA;IAAEG,UAAU,GAAAH,UAAA;EAC1B,IAAAI,UAAA,GAA8BjB,QAAQ,CAAC,IAAI,CAAC;IAAAkB,UAAA,GAAAJ,cAAA,CAAAG,UAAA;IAArCE,OAAO,GAAAD,UAAA;IAAEE,UAAU,GAAAF,UAAA;EAC1B,IAAAG,UAAA,GAA4CrB,QAAQ,CAAC,CAAC,CAAC,CAAC;IAAAsB,UAAA,GAAAR,cAAA,CAAAO,UAAA;IAAjDE,cAAc,GAAAD,UAAA;IAAEE,iBAAiB,GAAAF,UAAA;EAGxC,IAAMG,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAA,EAAS,CAGlC,CAAC;EAGDxB,SAAS,CAAC,YAAM;IACd,IAAIc,OAAO,IAAII,OAAO,EAAE;MACtBM,mBAAmB,CAAC,CAAC;IACvB;EACF,CAAC,EAAE,CAACV,OAAO,EAAEI,OAAO,CAAC,CAAC;EAEtB,OACEX,IAAA,CAACJ,UAAU;IAAAsB,QAAA,EACThB,KAAA,CAACR,IAAI;MAAAwB,QAAA,GACHlB,IAAA,CAACL,IAAI;QAAAuB,QAAA,EAAC;MAA0C,CAAM,CAAC,EACvDlB,IAAA,CAACF,cAAc;QACbqB,KAAK,EAAE,CACL;UAAEC,KAAK,EAAE,SAAS;UAAEC,KAAK,EAAE;QAAM,CAAC,EAClC;UAAED,KAAK,EAAE,UAAU;UAAEC,KAAK,EAAE;QAAM,CAAC,CAEnC;QACFC,WAAW,EAAC,kCAAkC;QAC9CC,YAAY,EAAE,SAAdA,YAAYA,CAAGC,IAAI;UAAA,OAAKhB,UAAU,CAACgB,IAAI,CAACH,KAAK,CAAC;QAAA;MAAC,CAChD,CAAC,EACFrB,IAAA,CAACF,cAAc;QACbqB,KAAK,EAAE,CACL;UAAEC,KAAK,EAAE,SAAS;UAAEC,KAAK,EAAE;QAAM,CAAC,EAClC;UAAED,KAAK,EAAE,UAAU;UAAEC,KAAK,EAAE;QAAM,CAAC,CAEnC;QACFC,WAAW,EAAC,iCAAiC;QAC7CC,YAAY,EAAE,SAAdA,YAAYA,CAAGC,IAAI;UAAA,OAAKZ,UAAU,CAACY,IAAI,CAACH,KAAK,CAAC;QAAA;MAAC,CAChD,CAAC,EACFrB,IAAA,CAACH,gBAAgB;QAAC4B,OAAO,EAAER,mBAAoB;QAAAC,QAAA,EAC7ClB,IAAA,CAACL,IAAI;UAAAuB,QAAA,EAAC;QAAQ,CAAM;MAAC,CACL,CAAC;IAAA,CACf;EAAC,CAEG,CAAC;AAEjB,CAAC;AAED,eAAef,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}