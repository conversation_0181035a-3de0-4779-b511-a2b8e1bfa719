{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport React, { useState, useEffect } from 'react';\nimport Appearance from \"react-native-web/dist/exports/Appearance\";\nimport { Provider as PaperProvider, MD3LightTheme as PaperDefaultTheme, MD3DarkTheme as PaperDarkTheme } from 'react-native-paper';\nimport { NavigationContainer, DefaultTheme as NavigationDefaultTheme, DarkTheme as NavigationDarkTheme } from '@react-navigation/native';\nimport { I18nextProvider } from 'react-i18next';\nimport StoreProvider from \"./src/store\";\nimport AxiosProvider from \"./src/store/axios\";\nimport { useFonts, Poppins_400Regular, Poppins_500Medium, Poppins_600SemiBold, Poppins_700Bold } from '@expo-google-fonts/poppins';\nimport Routes from \"./src/routes\";\nimport Loading from \"./src/components/Loading\";\nimport i18n from \"./i18n\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nvar CustomDefaultTheme = _objectSpread(_objectSpread(_objectSpread({}, NavigationDefaultTheme), PaperDefaultTheme), {}, {\n  colors: _objectSpread(_objectSpread(_objectSpread({}, NavigationDefaultTheme.colors), PaperDefaultTheme.colors), {}, {\n    background: '#202020',\n    primary: '#FECB37',\n    grey: '#0D0D0D'\n  })\n});\nvar CustomDarkTheme = _objectSpread(_objectSpread(_objectSpread({}, NavigationDarkTheme), PaperDarkTheme), {}, {\n  colors: _objectSpread(_objectSpread(_objectSpread({}, NavigationDarkTheme.colors), PaperDarkTheme.colors), {}, {\n    primary: '#FECB37',\n    grey: '#0D0D0D'\n  })\n});\nexport default function App() {\n  var _useState = useState(Appearance.getColorScheme() === 'dark'),\n    _useState2 = _slicedToArray(_useState, 2),\n    isDarkMode = _useState2[0],\n    setIsDarkMode = _useState2[1];\n  var _useFonts = useFonts({\n      Poppins_400Regular: Poppins_400Regular,\n      Poppins_500Medium: Poppins_500Medium,\n      Poppins_600SemiBold: Poppins_600SemiBold,\n      Poppins_700Bold: Poppins_700Bold\n    }),\n    _useFonts2 = _slicedToArray(_useFonts, 1),\n    fontsLoaded = _useFonts2[0];\n  useEffect(function () {\n    var subscription = Appearance.addChangeListener(function (_ref) {\n      var colorScheme = _ref.colorScheme;\n      setIsDarkMode(colorScheme === 'dark');\n    });\n    return function () {\n      return subscription.remove();\n    };\n  }, []);\n  useEffect(function () {\n    if (typeof window !== 'undefined') {\n      var style = document.createElement('style');\n      style.textContent = `\n        html, body {\n          height: 100%;\n          overflow: auto !important;\n          -webkit-overflow-scrolling: touch;\n        }\n        #root {\n          height: 100vh;\n          overflow: auto !important;\n        }\n        div[data-reactroot] {\n          height: 100vh !important;\n          overflow: auto !important;\n        }\n        ::-webkit-scrollbar {\n          width: 8px;\n          height: 8px;\n        }\n        ::-webkit-scrollbar-track {\n          background: #2a2a2a;\n          border-radius: 4px;\n        }\n        ::-webkit-scrollbar-thumb {\n          background: #FECB37;\n          border-radius: 4px;\n        }\n        ::-webkit-scrollbar-thumb:hover {\n          background: #e6b632;\n        }\n        html {\n          scrollbar-width: thin;\n          scrollbar-color: #FECB37 #2a2a2a;\n        }\n      `;\n      document.head.appendChild(style);\n    }\n  }, []);\n  if (!fontsLoaded) {\n    return _jsx(Loading, {});\n  }\n  var theme = isDarkMode ? CustomDarkTheme : CustomDefaultTheme;\n  return _jsx(I18nextProvider, {\n    i18n: i18n,\n    children: _jsx(StoreProvider, {\n      children: _jsx(AxiosProvider, {\n        children: _jsx(PaperProvider, {\n          theme: theme,\n          children: _jsx(NavigationContainer, {\n            theme: theme,\n            children: _jsx(Routes, {})\n          })\n        })\n      })\n    })\n  });\n}", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Appearance", "Provider", "PaperProvider", "MD3LightTheme", "PaperDefaultTheme", "MD3DarkTheme", "PaperDarkTheme", "NavigationContainer", "DefaultTheme", "NavigationDefaultTheme", "DarkTheme", "NavigationDarkTheme", "I18nextProvider", "StoreProvider", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useFonts", "Poppins_400Regular", "Poppins_500Medium", "Poppins_600SemiBold", "Poppins_700Bold", "Routes", "Loading", "i18n", "jsx", "_jsx", "CustomDefaultTheme", "_objectSpread", "colors", "background", "primary", "grey", "CustomDarkTheme", "App", "_useState", "getColorScheme", "_useState2", "_slicedToArray", "isDarkMode", "setIsDarkMode", "_useFonts", "_useFonts2", "fontsLoaded", "subscription", "addChangeListener", "_ref", "colorScheme", "remove", "window", "style", "document", "createElement", "textContent", "head", "append<PERSON><PERSON><PERSON>", "theme", "children"], "sources": ["E:/CryptoSignalsApp/App.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { Appearance } from 'react-native';\r\nimport { Provider as PaperProvider, MD3LightTheme as PaperDefaultTheme, MD3DarkTheme as PaperDarkTheme } from 'react-native-paper';\r\nimport { NavigationContainer, DefaultTheme as NavigationDefaultTheme, DarkTheme as NavigationDarkTheme } from '@react-navigation/native';\r\nimport { I18nextProvider } from 'react-i18next';\r\nimport StoreProvider from './src/store';\r\nimport AxiosProvider from './src/store/axios';\r\nimport { useFonts, Poppins_400Regular, Poppins_500Medium, Poppins_600SemiBold, Poppins_700Bold } from '@expo-google-fonts/poppins';\r\nimport Routes from './src/routes';\r\nimport Loading from './src/components/Loading';\r\nimport i18n from './i18n';\r\n\r\n\r\n\r\n\r\n\r\nconst CustomDefaultTheme = {\r\n  ...NavigationDefaultTheme,\r\n  ...PaperDefaultTheme,\r\n  colors: {\r\n    ...NavigationDefaultTheme.colors,\r\n    ...PaperDefaultTheme.colors,\r\n    background: '#202020',\r\n    primary: '#FECB37',\r\n    grey: '#0D0D0D',\r\n  }\r\n};\r\n\r\nconst CustomDarkTheme = {\r\n  ...NavigationDarkTheme,\r\n  ...PaperDarkTheme,\r\n  colors: {\r\n    ...NavigationDarkTheme.colors,\r\n    ...PaperDarkTheme.colors,\r\n    primary: '#FECB37',\r\n    grey: '#0D0D0D',\r\n  }\r\n};\r\n\r\nexport default function App() {\r\n  const [isDarkMode, setIsDarkMode] = useState(Appearance.getColorScheme() === 'dark');\r\n\r\n  let [fontsLoaded] = useFonts({\r\n    Poppins_400Regular,\r\n    Poppins_500Medium,\r\n    Poppins_600SemiBold,\r\n    Poppins_700Bold,\r\n  });\r\n\r\n  useEffect(() => {\r\n    const subscription = Appearance.addChangeListener(({ colorScheme }) => {\r\n      setIsDarkMode(colorScheme === 'dark');\r\n    });\r\n\r\n    return () => subscription.remove();\r\n  }, []);\r\n\r\n  // Add scroll fix for web\r\n  useEffect(() => {\r\n    if (typeof window !== 'undefined') {\r\n      const style = document.createElement('style');\r\n      style.textContent = `\r\n        html, body {\r\n          height: 100%;\r\n          overflow: auto !important;\r\n          -webkit-overflow-scrolling: touch;\r\n        }\r\n        #root {\r\n          height: 100vh;\r\n          overflow: auto !important;\r\n        }\r\n        div[data-reactroot] {\r\n          height: 100vh !important;\r\n          overflow: auto !important;\r\n        }\r\n        ::-webkit-scrollbar {\r\n          width: 8px;\r\n          height: 8px;\r\n        }\r\n        ::-webkit-scrollbar-track {\r\n          background: #2a2a2a;\r\n          border-radius: 4px;\r\n        }\r\n        ::-webkit-scrollbar-thumb {\r\n          background: #FECB37;\r\n          border-radius: 4px;\r\n        }\r\n        ::-webkit-scrollbar-thumb:hover {\r\n          background: #e6b632;\r\n        }\r\n        html {\r\n          scrollbar-width: thin;\r\n          scrollbar-color: #FECB37 #2a2a2a;\r\n        }\r\n      `;\r\n      document.head.appendChild(style);\r\n    }\r\n  }, []);\r\n\r\n  if (!fontsLoaded) {\r\n    return <Loading />;\r\n  }\r\n\r\n  const theme = isDarkMode ? CustomDarkTheme : CustomDefaultTheme;\r\n\r\n  return (\r\n    <I18nextProvider i18n={i18n}>\r\n      <StoreProvider>\r\n        <AxiosProvider>\r\n          <PaperProvider theme={theme}>\r\n            <NavigationContainer theme={theme}>\r\n              <Routes />\r\n            </NavigationContainer>\r\n          </PaperProvider>\r\n        </AxiosProvider>\r\n      </StoreProvider>\r\n    </I18nextProvider>\r\n  );\r\n}"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,OAAAC,UAAA;AAEnD,SAASC,QAAQ,IAAIC,aAAa,EAAEC,aAAa,IAAIC,iBAAiB,EAAEC,YAAY,IAAIC,cAAc,QAAQ,oBAAoB;AAClI,SAASC,mBAAmB,EAAEC,YAAY,IAAIC,sBAAsB,EAAEC,SAAS,IAAIC,mBAAmB,QAAQ,0BAA0B;AACxI,SAASC,eAAe,QAAQ,eAAe;AAC/C,OAAOC,aAAa;AACpB,OAAOC,aAAa;AACpB,SAASC,QAAQ,EAAEC,kBAAkB,EAAEC,iBAAiB,EAAEC,mBAAmB,EAAEC,eAAe,QAAQ,4BAA4B;AAClI,OAAOC,MAAM;AACb,OAAOC,OAAO;AACd,OAAOC,IAAI;AAAe,SAAAC,GAAA,IAAAC,IAAA;AAM1B,IAAMC,kBAAkB,GAAAC,aAAA,CAAAA,aAAA,CAAAA,aAAA,KACnBjB,sBAAsB,GACtBL,iBAAiB;EACpBuB,MAAM,EAAAD,aAAA,CAAAA,aAAA,CAAAA,aAAA,KACDjB,sBAAsB,CAACkB,MAAM,GAC7BvB,iBAAiB,CAACuB,MAAM;IAC3BC,UAAU,EAAE,SAAS;IACrBC,OAAO,EAAE,SAAS;IAClBC,IAAI,EAAE;EAAS;AAChB,EACF;AAED,IAAMC,eAAe,GAAAL,aAAA,CAAAA,aAAA,CAAAA,aAAA,KAChBf,mBAAmB,GACnBL,cAAc;EACjBqB,MAAM,EAAAD,aAAA,CAAAA,aAAA,CAAAA,aAAA,KACDf,mBAAmB,CAACgB,MAAM,GAC1BrB,cAAc,CAACqB,MAAM;IACxBE,OAAO,EAAE,SAAS;IAClBC,IAAI,EAAE;EAAS;AAChB,EACF;AAED,eAAe,SAASE,GAAGA,CAAA,EAAG;EAC5B,IAAAC,SAAA,GAAoCnC,QAAQ,CAACE,UAAU,CAACkC,cAAc,CAAC,CAAC,KAAK,MAAM,CAAC;IAAAC,UAAA,GAAAC,cAAA,CAAAH,SAAA;IAA7EI,UAAU,GAAAF,UAAA;IAAEG,aAAa,GAAAH,UAAA;EAEhC,IAAAI,SAAA,GAAoBxB,QAAQ,CAAC;MAC3BC,kBAAkB,EAAlBA,kBAAkB;MAClBC,iBAAiB,EAAjBA,iBAAiB;MACjBC,mBAAmB,EAAnBA,mBAAmB;MACnBC,eAAe,EAAfA;IACF,CAAC,CAAC;IAAAqB,UAAA,GAAAJ,cAAA,CAAAG,SAAA;IALGE,WAAW,GAAAD,UAAA;EAOhBzC,SAAS,CAAC,YAAM;IACd,IAAM2C,YAAY,GAAG1C,UAAU,CAAC2C,iBAAiB,CAAC,UAAAC,IAAA,EAAqB;MAAA,IAAlBC,WAAW,GAAAD,IAAA,CAAXC,WAAW;MAC9DP,aAAa,CAACO,WAAW,KAAK,MAAM,CAAC;IACvC,CAAC,CAAC;IAEF,OAAO;MAAA,OAAMH,YAAY,CAACI,MAAM,CAAC,CAAC;IAAA;EACpC,CAAC,EAAE,EAAE,CAAC;EAGN/C,SAAS,CAAC,YAAM;IACd,IAAI,OAAOgD,MAAM,KAAK,WAAW,EAAE;MACjC,IAAMC,KAAK,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;MAC7CF,KAAK,CAACG,WAAW,GAAG;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;MACDF,QAAQ,CAACG,IAAI,CAACC,WAAW,CAACL,KAAK,CAAC;IAClC;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,IAAI,CAACP,WAAW,EAAE;IAChB,OAAOjB,IAAA,CAACH,OAAO,IAAE,CAAC;EACpB;EAEA,IAAMiC,KAAK,GAAGjB,UAAU,GAAGN,eAAe,GAAGN,kBAAkB;EAE/D,OACED,IAAA,CAACZ,eAAe;IAACU,IAAI,EAAEA,IAAK;IAAAiC,QAAA,EAC1B/B,IAAA,CAACX,aAAa;MAAA0C,QAAA,EACZ/B,IAAA,CAACV,aAAa;QAAAyC,QAAA,EACZ/B,IAAA,CAACtB,aAAa;UAACoD,KAAK,EAAEA,KAAM;UAAAC,QAAA,EAC1B/B,IAAA,CAACjB,mBAAmB;YAAC+C,KAAK,EAAEA,KAAM;YAAAC,QAAA,EAChC/B,IAAA,CAACJ,MAAM,IAAE;UAAC,CACS;QAAC,CACT;MAAC,CACH;IAAC,CACH;EAAC,CACD,CAAC;AAEtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}