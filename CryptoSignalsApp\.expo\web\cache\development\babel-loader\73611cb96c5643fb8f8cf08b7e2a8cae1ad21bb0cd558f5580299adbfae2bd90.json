{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport React from 'react';\nimport Animated from \"react-native-web/dist/exports/Animated\";\nimport Pressable from \"react-native-web/dist/exports/Pressable\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport { AdornmentSide } from \"./enums\";\nimport { getTextColor } from \"./utils\";\nimport { useInternalTheme } from \"../../../core/theming\";\nimport { getConstants } from \"../helpers\";\nvar AffixContext = React.createContext({\n  textStyle: {\n    fontFamily: '',\n    color: ''\n  },\n  topPosition: null,\n  side: AdornmentSide.Left\n});\nvar AffixAdornment = function AffixAdornment(_ref) {\n  var affix = _ref.affix,\n    side = _ref.side,\n    textStyle = _ref.textStyle,\n    topPosition = _ref.topPosition,\n    onLayout = _ref.onLayout,\n    visible = _ref.visible,\n    paddingHorizontal = _ref.paddingHorizontal,\n    maxFontSizeMultiplier = _ref.maxFontSizeMultiplier,\n    testID = _ref.testID,\n    disabled = _ref.disabled;\n  return React.createElement(AffixContext.Provider, {\n    value: {\n      side: side,\n      textStyle: textStyle,\n      topPosition: topPosition,\n      onLayout: onLayout,\n      visible: visible,\n      paddingHorizontal: paddingHorizontal,\n      maxFontSizeMultiplier: maxFontSizeMultiplier,\n      testID: testID,\n      disabled: disabled\n    }\n  }, affix);\n};\nvar TextInputAffix = function TextInputAffix(_ref2) {\n  var text = _ref2.text,\n    labelStyle = _ref2.textStyle,\n    themeOverrides = _ref2.theme,\n    onTextLayout = _ref2.onLayout,\n    onPress = _ref2.onPress,\n    _ref2$accessibilityLa = _ref2.accessibilityLabel,\n    accessibilityLabel = _ref2$accessibilityLa === void 0 ? text : _ref2$accessibilityLa;\n  var theme = useInternalTheme(themeOverrides);\n  var _getConstants = getConstants(theme.isV3),\n    AFFIX_OFFSET = _getConstants.AFFIX_OFFSET;\n  var _React$useContext = React.useContext(AffixContext),\n    textStyle = _React$useContext.textStyle,\n    onLayout = _React$useContext.onLayout,\n    topPosition = _React$useContext.topPosition,\n    side = _React$useContext.side,\n    visible = _React$useContext.visible,\n    paddingHorizontal = _React$useContext.paddingHorizontal,\n    maxFontSizeMultiplier = _React$useContext.maxFontSizeMultiplier,\n    testID = _React$useContext.testID,\n    disabled = _React$useContext.disabled;\n  var offset = typeof paddingHorizontal === 'number' ? paddingHorizontal : AFFIX_OFFSET;\n  var style = _defineProperty({\n    top: topPosition\n  }, side, offset);\n  var textColor = getTextColor({\n    theme: theme,\n    disabled: disabled\n  });\n  var content = React.createElement(Text, {\n    maxFontSizeMultiplier: maxFontSizeMultiplier,\n    style: [{\n      color: textColor\n    }, textStyle, labelStyle],\n    onLayout: onTextLayout,\n    testID: `${testID}-text`\n  }, text);\n  return React.createElement(Animated.View, {\n    style: [styles.container, style, {\n      opacity: (visible === null || visible === void 0 ? void 0 : visible.interpolate({\n        inputRange: [0, 1],\n        outputRange: [1, 0]\n      })) || 1\n    }],\n    onLayout: onLayout,\n    testID: testID\n  }, onPress ? React.createElement(Pressable, {\n    onPress: onPress,\n    accessibilityRole: \"button\",\n    accessibilityLabel: accessibilityLabel\n  }, content) : content);\n};\nTextInputAffix.displayName = 'TextInput.Affix';\nvar styles = StyleSheet.create({\n  container: {\n    position: 'absolute',\n    justifyContent: 'center',\n    alignItems: 'center'\n  }\n});\nexport default TextInputAffix;\nexport { TextInputAffix, AffixAdornment };", "map": {"version": 3, "names": ["React", "Animated", "Pressable", "StyleSheet", "Text", "AdornmentSide", "getTextColor", "useInternalTheme", "getConstants", "AffixContext", "createContext", "textStyle", "fontFamily", "color", "topPosition", "side", "Left", "AffixAdornment", "_ref", "affix", "onLayout", "visible", "paddingHorizontal", "maxFontSizeMultiplier", "testID", "disabled", "createElement", "Provider", "value", "TextInputAffix", "_ref2", "text", "labelStyle", "themeOverrides", "theme", "onTextLayout", "onPress", "_ref2$accessibilityLa", "accessibilityLabel", "_getConstants", "isV3", "AFFIX_OFFSET", "_React$useContext", "useContext", "offset", "style", "_defineProperty", "top", "textColor", "content", "View", "styles", "container", "opacity", "interpolate", "inputRange", "outputRange", "accessibilityRole", "displayName", "create", "position", "justifyContent", "alignItems"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\TextInput\\Adornment\\TextInputAffix.tsx"], "sourcesContent": ["import React from 'react';\nimport {\n  Animated,\n  DimensionValue,\n  GestureResponderEvent,\n  LayoutChangeEvent,\n  Pressable,\n  StyleProp,\n  StyleSheet,\n  Text,\n  TextStyle,\n  ViewStyle,\n} from 'react-native';\n\nimport { AdornmentSide } from './enums';\nimport { getTextColor } from './utils';\nimport { useInternalTheme } from '../../../core/theming';\nimport type { ThemeProp } from '../../../types';\nimport { getConstants } from '../helpers';\n\nexport type Props = {\n  /**\n   * Text to show.\n   */\n  text: string;\n  onLayout?: (event: LayoutChangeEvent) => void;\n  /**\n   * Function to execute on press.\n   */\n  onPress?: (e: GestureResponderEvent) => void;\n  /**\n   * Accessibility label for the affix. This is read by the screen reader when the user taps the affix.\n   */\n  accessibilityLabel?: string;\n  /**\n   * Style that is passed to the Text element.\n   */\n  textStyle?: StyleProp<TextStyle>;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n};\n\ntype ContextState = {\n  topPosition: number | null;\n  onLayout?: (event: LayoutChangeEvent) => void;\n  visible?: Animated.Value;\n  textStyle?: StyleProp<TextStyle>;\n  side: AdornmentSide;\n  paddingHorizontal?: DimensionValue;\n  maxFontSizeMultiplier?: number | undefined | null;\n  testID?: string;\n  disabled?: boolean;\n};\n\nconst AffixContext = React.createContext<ContextState>({\n  textStyle: { fontFamily: '', color: '' },\n  topPosition: null,\n  side: AdornmentSide.Left,\n});\n\nconst AffixAdornment: React.FunctionComponent<\n  {\n    affix: React.ReactNode;\n    testID: string;\n  } & ContextState\n> = ({\n  affix,\n  side,\n  textStyle,\n  topPosition,\n  onLayout,\n  visible,\n  paddingHorizontal,\n  maxFontSizeMultiplier,\n  testID,\n  disabled,\n}) => {\n  return (\n    <AffixContext.Provider\n      value={{\n        side,\n        textStyle,\n        topPosition,\n        onLayout,\n        visible,\n        paddingHorizontal,\n        maxFontSizeMultiplier,\n        testID,\n        disabled,\n      }}\n    >\n      {affix}\n    </AffixContext.Provider>\n  );\n};\n\n/**\n * A component to render a leading / trailing text in the TextInput\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { TextInput } from 'react-native-paper';\n *\n * const MyComponent = () => {\n *   const [text, setText] = React.useState('');\n *\n *   return (\n *     <TextInput\n *       mode=\"outlined\"\n *       label=\"Outlined input\"\n *       placeholder=\"Type something\"\n *       right={<TextInput.Affix text=\"/100\" />}\n *     />\n *   );\n * };\n *\n * export default MyComponent;\n * ```\n */\n\nconst TextInputAffix = ({\n  text,\n  textStyle: labelStyle,\n  theme: themeOverrides,\n  onLayout: onTextLayout,\n  onPress,\n  accessibilityLabel = text,\n}: Props) => {\n  const theme = useInternalTheme(themeOverrides);\n  const { AFFIX_OFFSET } = getConstants(theme.isV3);\n\n  const {\n    textStyle,\n    onLayout,\n    topPosition,\n    side,\n    visible,\n    paddingHorizontal,\n    maxFontSizeMultiplier,\n    testID,\n    disabled,\n  } = React.useContext(AffixContext);\n\n  const offset =\n    typeof paddingHorizontal === 'number' ? paddingHorizontal : AFFIX_OFFSET;\n\n  const style = {\n    top: topPosition,\n    [side]: offset,\n  } as ViewStyle;\n\n  const textColor = getTextColor({ theme, disabled });\n\n  const content = (\n    <Text\n      maxFontSizeMultiplier={maxFontSizeMultiplier}\n      style={[{ color: textColor }, textStyle, labelStyle]}\n      onLayout={onTextLayout}\n      testID={`${testID}-text`}\n    >\n      {text}\n    </Text>\n  );\n\n  return (\n    <Animated.View\n      style={[\n        styles.container,\n        style,\n        {\n          opacity:\n            visible?.interpolate({\n              inputRange: [0, 1],\n              outputRange: [1, 0],\n            }) || 1,\n        },\n      ]}\n      onLayout={onLayout}\n      testID={testID}\n    >\n      {onPress ? (\n        <Pressable\n          onPress={onPress}\n          accessibilityRole=\"button\"\n          accessibilityLabel={accessibilityLabel}\n        >\n          {content}\n        </Pressable>\n      ) : (\n        content\n      )}\n    </Animated.View>\n  );\n};\n\nTextInputAffix.displayName = 'TextInput.Affix';\n\nconst styles = StyleSheet.create({\n  container: {\n    position: 'absolute',\n    justifyContent: 'center',\n    alignItems: 'center',\n  },\n});\n\nexport default TextInputAffix;\n\n// @component-docs ignore-next-line\nexport { TextInputAffix, AffixAdornment };\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAAA,OAAAC,SAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAczB,SAASC,aAAa;AACtB,SAASC,YAAY;AACrB,SAASC,gBAAgB;AAEzB,SAASC,YAAY;AAsCrB,IAAMC,YAAY,GAAGT,KAAK,CAACU,aAAa,CAAe;EACrDC,SAAS,EAAE;IAAEC,UAAU,EAAE,EAAE;IAAEC,KAAK,EAAE;EAAG,CAAC;EACxCC,WAAW,EAAE,IAAI;EACjBC,IAAI,EAAEV,aAAa,CAACW;AACtB,CAAC,CAAC;AAEF,IAAMC,cAKL,GAAG,SALEA,cAKLA,CAAAC,IAAA,EAWK;EAAA,IAVJC,KAAK,GAAAD,IAAA,CAALC,KAAK;IACLJ,IAAI,GAAAG,IAAA,CAAJH,IAAI;IACJJ,SAAS,GAAAO,IAAA,CAATP,SAAS;IACTG,WAAW,GAAAI,IAAA,CAAXJ,WAAW;IACXM,QAAQ,GAAAF,IAAA,CAARE,QAAQ;IACRC,OAAO,GAAAH,IAAA,CAAPG,OAAO;IACPC,iBAAiB,GAAAJ,IAAA,CAAjBI,iBAAiB;IACjBC,qBAAqB,GAAAL,IAAA,CAArBK,qBAAqB;IACrBC,MAAM,GAAAN,IAAA,CAANM,MAAM;IACNC,QAAA,GAAAP,IAAA,CAAAO,QAAA;EAEA,OACEzB,KAAA,CAAA0B,aAAA,CAACjB,YAAY,CAACkB,QAAQ;IACpBC,KAAK,EAAE;MACLb,IAAI,EAAJA,IAAI;MACJJ,SAAS,EAATA,SAAS;MACTG,WAAW,EAAXA,WAAW;MACXM,QAAQ,EAARA,QAAQ;MACRC,OAAO,EAAPA,OAAO;MACPC,iBAAiB,EAAjBA,iBAAiB;MACjBC,qBAAqB,EAArBA,qBAAqB;MACrBC,MAAM,EAANA,MAAM;MACNC,QAAA,EAAAA;IACF;EAAE,GAEDN,KACoB,CAAC;AAE5B,CAAC;AA2BD,IAAMU,cAAc,GAAG,SAAjBA,cAAcA,CAAAC,KAAA,EAOP;EAAA,IANXC,IAAI,GAAAD,KAAA,CAAJC,IAAI;IACOC,UAAU,GAAAF,KAAA,CAArBnB,SAAS;IACFsB,cAAc,GAAAH,KAAA,CAArBI,KAAK;IACKC,YAAY,GAAAL,KAAA,CAAtBV,QAAQ;IACRgB,OAAO,GAAAN,KAAA,CAAPM,OAAO;IAAAC,qBAAA,GAAAP,KAAA,CACPQ,kBAAkB;IAAlBA,kBAAkB,GAAAD,qBAAA,cAAGN,IAAA,GAAAM,qBAAA;EAErB,IAAMH,KAAK,GAAG3B,gBAAgB,CAAC0B,cAAc,CAAC;EAC9C,IAAAM,aAAA,GAAyB/B,YAAY,CAAC0B,KAAK,CAACM,IAAI,CAAC;IAAzCC,YAAA,GAAAF,aAAA,CAAAE,YAAA;EAER,IAAAC,iBAAA,GAUI1C,KAAK,CAAC2C,UAAU,CAAClC,YAAY,CAAC;IAThCE,SAAS,GAAA+B,iBAAA,CAAT/B,SAAS;IACTS,QAAQ,GAAAsB,iBAAA,CAARtB,QAAQ;IACRN,WAAW,GAAA4B,iBAAA,CAAX5B,WAAW;IACXC,IAAI,GAAA2B,iBAAA,CAAJ3B,IAAI;IACJM,OAAO,GAAAqB,iBAAA,CAAPrB,OAAO;IACPC,iBAAiB,GAAAoB,iBAAA,CAAjBpB,iBAAiB;IACjBC,qBAAqB,GAAAmB,iBAAA,CAArBnB,qBAAqB;IACrBC,MAAM,GAAAkB,iBAAA,CAANlB,MAAM;IACNC,QAAA,GAAAiB,iBAAA,CAAAjB,QAAA;EAGF,IAAMmB,MAAM,GACV,OAAOtB,iBAAiB,KAAK,QAAQ,GAAGA,iBAAiB,GAAGmB,YAAY;EAE1E,IAAMI,KAAK,GAAAC,eAAA;IACTC,GAAG,EAAEjC;EAAW,GACfC,IAAI,EAAG6B,MAAA,CACI;EAEd,IAAMI,SAAS,GAAG1C,YAAY,CAAC;IAAE4B,KAAK,EAALA,KAAK;IAAET,QAAA,EAAAA;EAAS,CAAC,CAAC;EAEnD,IAAMwB,OAAO,GACXjD,KAAA,CAAA0B,aAAA,CAACtB,IAAI;IACHmB,qBAAqB,EAAEA,qBAAsB;IAC7CsB,KAAK,EAAE,CAAC;MAAEhC,KAAK,EAAEmC;IAAU,CAAC,EAAErC,SAAS,EAAEqB,UAAU,CAAE;IACrDZ,QAAQ,EAAEe,YAAa;IACvBX,MAAM,EAAE,GAAGA,MAAM;EAAQ,GAExBO,IACG,CACP;EAED,OACE/B,KAAA,CAAA0B,aAAA,CAACzB,QAAQ,CAACiD,IAAI;IACZL,KAAK,EAAE,CACLM,MAAM,CAACC,SAAS,EAChBP,KAAK,EACL;MACEQ,OAAO,EACL,CAAAhC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEiC,WAAW,CAAC;QACnBC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QAClBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC;MACpB,CAAC,CAAC,KAAI;IACV,CAAC,CACD;IACFpC,QAAQ,EAAEA,QAAS;IACnBI,MAAM,EAAEA;EAAO,GAEdY,OAAO,GACNpC,KAAA,CAAA0B,aAAA,CAACxB,SAAS;IACRkC,OAAO,EAAEA,OAAQ;IACjBqB,iBAAiB,EAAC,QAAQ;IAC1BnB,kBAAkB,EAAEA;EAAmB,GAEtCW,OACQ,CAAC,GAEZA,OAEW,CAAC;AAEpB,CAAC;AAEDpB,cAAc,CAAC6B,WAAW,GAAG,iBAAiB;AAE9C,IAAMP,MAAM,GAAGhD,UAAU,CAACwD,MAAM,CAAC;EAC/BP,SAAS,EAAE;IACTQ,QAAQ,EAAE,UAAU;IACpBC,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE;EACd;AACF,CAAC,CAAC;AAEF,eAAejC,cAAc;AAG7B,SAASA,cAAc,EAAEZ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}