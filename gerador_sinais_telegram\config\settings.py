# Configurações gerais do aplicativo
import os
from dotenv import load_dotenv

# Carrega variáveis de ambiente do arquivo .env (se existir)
load_dotenv()

# Configurações do Telegram
TELEGRAM_API_ID = int(os.getenv("TELEGRAM_API_ID", "26052926"))
TELEGRAM_API_HASH = os.getenv("TELEGRAM_API_HASH", "f2701dc0efb918fda05ebac97eaeb31e")
TELEGRAM_GROUP_ID = int(os.getenv("TELEGRAM_GROUP_ID", "-4794516218"))
TELEGRAM_SESSION_FILE = os.getenv("TELEGRAM_SESSION_FILE", "session.session")
TELEGRAM_BOT_TOKEN = os.getenv("TELEGRAM_BOT_TOKEN", "seu_token_aqui")
TELEGRAM_PHONE = os.getenv("TELEGRAM_PHONE", "")

# Configurações da Binance
BINANCE_API_KEY = os.getenv("BINANCE_API_KEY", "your_testnet_api_key")
BINANCE_API_SECRET = os.getenv("BINANCE_API_SECRET", "your_testnet_api_secret")
BINANCE_TESTNET = os.getenv("BINANCE_TESTNET", "True").lower() == "true"

# Configurações de execução - OTIMIZADO PARA 24/7
MAX_SIGNALS_PER_DAY = int(os.getenv("MAX_SIGNALS_PER_DAY", "50"))  # Aumentado para 50 sinais/dia
SIGNAL_INTERVAL_SECONDS = int(os.getenv("SIGNAL_INTERVAL_SECONDS", "180"))  # 3 minutos entre sinais (mais frequente)
LEVERAGE = int(os.getenv("LEVERAGE", "20"))
LOOKBACK_DAYS = int(os.getenv("LOOKBACK_DAYS", "1"))

# Configurações de logging
LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
LOG_FILE = os.getenv("LOG_FILE", "gerador_sinais.log")

# Horário de operação - OPERAÇÃO 24/7 ATIVADA
# Criptomoedas operam 24h, então removemos restrições de horário
TRADING_START_HOUR = int(os.getenv("TRADING_START_HOUR", "0"))   # 00:00 - Meia-noite
TRADING_END_HOUR = int(os.getenv("TRADING_END_HOUR", "23"))     # 23:00 - 11 PM (praticamente 24h)

# Configurações de tempo limite para sinais
SIGNAL_VALIDITY_MINUTES = int(os.getenv("SIGNAL_VALIDITY_MINUTES", "30"))  # Tempo máximo de validade do sinal

# Configurações de níveis de profit para scalp
SCALP_PROFIT_LEVELS = [40, 60, 80, 100]  # Níveis de profit em porcentagem