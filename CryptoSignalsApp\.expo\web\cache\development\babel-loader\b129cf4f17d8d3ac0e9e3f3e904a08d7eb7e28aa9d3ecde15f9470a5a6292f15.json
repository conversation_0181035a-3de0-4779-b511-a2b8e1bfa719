{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport * as React from 'react';\nimport AccessibilityInfo from \"react-native-web/dist/exports/AccessibilityInfo\";\nimport Appearance from \"react-native-web/dist/exports/Appearance\";\nimport SafeAreaProviderCompat from \"./SafeAreaProviderCompat\";\nimport { Provider as SettingsProvider } from \"./settings\";\nimport { defaultThemesByVersion, ThemeProvider } from \"./theming\";\nimport MaterialCommunityIcon from \"../components/MaterialCommunityIcon\";\nimport PortalHost from \"../components/Portal/PortalHost\";\nimport { addEventListener } from \"../utils/addEventListener\";\nvar PaperProvider = function PaperProvider(props) {\n  var isOnlyVersionInTheme = props.theme && Object.keys(props.theme).length === 1 && props.theme.version;\n  var colorSchemeName = (!props.theme || isOnlyVersionInTheme) && (Appearance === null || Appearance === void 0 ? void 0 : Appearance.getColorScheme()) || 'light';\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    reduceMotionEnabled = _React$useState2[0],\n    setReduceMotionEnabled = _React$useState2[1];\n  var _React$useState3 = React.useState(colorSchemeName),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    colorScheme = _React$useState4[0],\n    setColorScheme = _React$useState4[1];\n  var handleAppearanceChange = function handleAppearanceChange(preferences) {\n    var colorScheme = preferences.colorScheme;\n    setColorScheme(colorScheme);\n  };\n  React.useEffect(function () {\n    var subscription;\n    if (!props.theme) {\n      subscription = addEventListener(AccessibilityInfo, 'reduceMotionChanged', setReduceMotionEnabled);\n    }\n    return function () {\n      if (!props.theme) {\n        var _subscription;\n        (_subscription = subscription) === null || _subscription === void 0 || _subscription.remove();\n      }\n    };\n  }, [props.theme]);\n  React.useEffect(function () {\n    var appearanceSubscription;\n    if (!props.theme || isOnlyVersionInTheme) {\n      appearanceSubscription = Appearance === null || Appearance === void 0 ? void 0 : Appearance.addChangeListener(handleAppearanceChange);\n    }\n    return function () {\n      if (!props.theme || isOnlyVersionInTheme) {\n        if (appearanceSubscription) {\n          appearanceSubscription.remove();\n        } else {\n          Appearance === null || Appearance === void 0 || Appearance.removeChangeListener(handleAppearanceChange);\n        }\n      }\n    };\n  }, [props.theme, isOnlyVersionInTheme]);\n  var theme = React.useMemo(function () {\n    var _props$theme, _props$theme2;\n    var themeVersion = ((_props$theme = props.theme) === null || _props$theme === void 0 ? void 0 : _props$theme.version) || 3;\n    var scheme = colorScheme || 'light';\n    var defaultThemeBase = defaultThemesByVersion[themeVersion][scheme];\n    var extendedThemeBase = _objectSpread(_objectSpread(_objectSpread({}, defaultThemeBase), props.theme), {}, {\n      version: themeVersion,\n      animation: _objectSpread(_objectSpread({}, (_props$theme2 = props.theme) === null || _props$theme2 === void 0 ? void 0 : _props$theme2.animation), {}, {\n        scale: reduceMotionEnabled ? 0 : 1\n      })\n    });\n    return _objectSpread(_objectSpread({}, extendedThemeBase), {}, {\n      isV3: extendedThemeBase.version === 3\n    });\n  }, [colorScheme, props.theme, reduceMotionEnabled]);\n  var children = props.children,\n    settings = props.settings;\n  var settingsValue = React.useMemo(function () {\n    return _objectSpread({\n      icon: MaterialCommunityIcon,\n      rippleEffectEnabled: true\n    }, settings);\n  }, [settings]);\n  return React.createElement(SafeAreaProviderCompat, null, React.createElement(PortalHost, null, React.createElement(SettingsProvider, {\n    value: settingsValue\n  }, React.createElement(ThemeProvider, {\n    theme: theme\n  }, children))));\n};\nexport default PaperProvider;", "map": {"version": 3, "names": ["React", "AccessibilityInfo", "Appearance", "SafeAreaProviderCompat", "Provider", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultThemesByVersion", "ThemeProvider", "MaterialCommunityIcon", "PortalHost", "addEventListener", "PaperProvider", "props", "isOnlyVersionInTheme", "theme", "Object", "keys", "length", "version", "colorSchemeName", "getColorScheme", "_React$useState", "useState", "_React$useState2", "_slicedToArray", "reduceMotionEnabled", "setReduceMotionEnabled", "_React$useState3", "_React$useState4", "colorScheme", "setColorScheme", "handleAppearanceChange", "preferences", "useEffect", "subscription", "_subscription", "remove", "appearanceSubscription", "addChangeListener", "removeChangeListener", "useMemo", "_props$theme", "_props$theme2", "themeVersion", "scheme", "defaultThemeBase", "extendedThemeBase", "_objectSpread", "animation", "scale", "isV3", "children", "settings", "settingsValue", "icon", "rippleEffectEnabled", "createElement", "value"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\core\\PaperProvider.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  AccessibilityInfo,\n  Appearance,\n  ColorSchemeName,\n  NativeEventSubscription,\n} from 'react-native';\n\nimport SafeAreaProviderCompat from './SafeAreaProviderCompat';\nimport { Provider as SettingsProvider, Settings } from './settings';\nimport { defaultThemesByVersion, ThemeProvider } from './theming';\nimport MaterialCommunityIcon from '../components/MaterialCommunityIcon';\nimport PortalHost from '../components/Portal/PortalHost';\nimport type { ThemeProp } from '../types';\nimport { addEventListener } from '../utils/addEventListener';\n\nexport type Props = {\n  children: React.ReactNode;\n  theme?: ThemeProp;\n  settings?: Settings;\n};\n\nconst PaperProvider = (props: Props) => {\n  const isOnlyVersionInTheme =\n    props.theme && Object.keys(props.theme).length === 1 && props.theme.version;\n\n  const colorSchemeName =\n    ((!props.theme || isOnlyVersionInTheme) && Appearance?.getColorScheme()) ||\n    'light';\n\n  const [reduceMotionEnabled, setReduceMotionEnabled] =\n    React.useState<boolean>(false);\n  const [colorScheme, setColorScheme] =\n    React.useState<ColorSchemeName>(colorSchemeName);\n\n  const handleAppearanceChange = (\n    preferences: Appearance.AppearancePreferences\n  ) => {\n    const { colorScheme } = preferences;\n    setColorScheme(colorScheme);\n  };\n\n  React.useEffect(() => {\n    let subscription: NativeEventSubscription | undefined;\n\n    if (!props.theme) {\n      subscription = addEventListener(\n        AccessibilityInfo,\n        'reduceMotionChanged',\n        setReduceMotionEnabled\n      );\n    }\n    return () => {\n      if (!props.theme) {\n        subscription?.remove();\n      }\n    };\n  }, [props.theme]);\n\n  React.useEffect(() => {\n    let appearanceSubscription: NativeEventSubscription | undefined;\n    if (!props.theme || isOnlyVersionInTheme) {\n      appearanceSubscription = Appearance?.addChangeListener(\n        handleAppearanceChange\n      ) as NativeEventSubscription | undefined;\n    }\n    return () => {\n      if (!props.theme || isOnlyVersionInTheme) {\n        if (appearanceSubscription) {\n          appearanceSubscription.remove();\n        } else {\n          // @ts-expect-error: We keep deprecated listener remove method for backwards compat with old RN versions\n          Appearance?.removeChangeListener(handleAppearanceChange);\n        }\n      }\n    };\n  }, [props.theme, isOnlyVersionInTheme]);\n\n  const theme = React.useMemo(() => {\n    const themeVersion = props.theme?.version || 3;\n    const scheme = colorScheme || 'light';\n    const defaultThemeBase = defaultThemesByVersion[themeVersion][scheme];\n\n    const extendedThemeBase = {\n      ...defaultThemeBase,\n      ...props.theme,\n      version: themeVersion,\n      animation: {\n        ...props.theme?.animation,\n        scale: reduceMotionEnabled ? 0 : 1,\n      },\n    };\n\n    return {\n      ...extendedThemeBase,\n      isV3: extendedThemeBase.version === 3,\n    };\n  }, [colorScheme, props.theme, reduceMotionEnabled]);\n\n  const { children, settings } = props;\n\n  const settingsValue = React.useMemo(\n    () => ({\n      icon: MaterialCommunityIcon,\n      rippleEffectEnabled: true,\n      ...settings,\n    }),\n    [settings]\n  );\n\n  return (\n    <SafeAreaProviderCompat>\n      <PortalHost>\n        <SettingsProvider value={settingsValue}>\n          <ThemeProvider theme={theme}>{children}</ThemeProvider>\n        </SettingsProvider>\n      </PortalHost>\n    </SafeAreaProviderCompat>\n  );\n};\n\nexport default PaperProvider;\n"], "mappings": ";;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,iBAAA;AAAA,OAAAC,UAAA;AAQ9B,OAAOC,sBAAsB;AAC7B,SAASC,QAAQ,IAAIC,gBAAgB;AACrC,SAASC,sBAAsB,EAAEC,aAAa;AAC9C,OAAOC,qBAAqB;AAC5B,OAAOC,UAAU;AAEjB,SAASC,gBAAgB;AAQzB,IAAMC,aAAa,GAAI,SAAjBA,aAAaA,CAAIC,KAAY,EAAK;EACtC,IAAMC,oBAAoB,GACxBD,KAAK,CAACE,KAAK,IAAIC,MAAM,CAACC,IAAI,CAACJ,KAAK,CAACE,KAAK,CAAC,CAACG,MAAM,KAAK,CAAC,IAAIL,KAAK,CAACE,KAAK,CAACI,OAAO;EAE7E,IAAMC,eAAe,GAClB,CAAC,CAACP,KAAK,CAACE,KAAK,IAAID,oBAAoB,MAAKX,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEkB,cAAc,CAAC,CAAC,KACvE,OAAO;EAET,IAAAC,eAAA,GACErB,KAAK,CAACsB,QAAQ,CAAU,KAAK,CAAC;IAAAC,gBAAA,GAAAC,cAAA,CAAAH,eAAA;IADzBI,mBAAmB,GAAAF,gBAAA;IAAEG,sBAAsB,GAAAH,gBAAA;EAElD,IAAAI,gBAAA,GACE3B,KAAK,CAACsB,QAAQ,CAAkBH,eAAe,CAAC;IAAAS,gBAAA,GAAAJ,cAAA,CAAAG,gBAAA;IAD3CE,WAAW,GAAAD,gBAAA;IAAEE,cAAc,GAAAF,gBAAA;EAGlC,IAAMG,sBAAsB,GAC1B,SADIA,sBAAsBA,CAC1BC,WAA6C,EAC1C;IACH,IAAQH,WAAA,GAAgBG,WAAW,CAA3BH,WAAA;IACRC,cAAc,CAACD,WAAW,CAAC;EAC7B,CAAC;EAED7B,KAAK,CAACiC,SAAS,CAAC,YAAM;IACpB,IAAIC,YAAiD;IAErD,IAAI,CAACtB,KAAK,CAACE,KAAK,EAAE;MAChBoB,YAAY,GAAGxB,gBAAgB,CAC7BT,iBAAiB,EACjB,qBAAqB,EACrByB,sBACF,CAAC;IACH;IACA,OAAO,YAAM;MACX,IAAI,CAACd,KAAK,CAACE,KAAK,EAAE;QAAA,IAAAqB,aAAA;QAChB,CAAAA,aAAA,GAAAD,YAAY,cAAAC,aAAA,eAAZA,aAAA,CAAcC,MAAM,CAAC,CAAC;MACxB;IACF,CAAC;EACH,CAAC,EAAE,CAACxB,KAAK,CAACE,KAAK,CAAC,CAAC;EAEjBd,KAAK,CAACiC,SAAS,CAAC,YAAM;IACpB,IAAII,sBAA2D;IAC/D,IAAI,CAACzB,KAAK,CAACE,KAAK,IAAID,oBAAoB,EAAE;MACxCwB,sBAAsB,GAAGnC,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEoC,iBAAiB,CACpDP,sBACF,CAAwC;IAC1C;IACA,OAAO,YAAM;MACX,IAAI,CAACnB,KAAK,CAACE,KAAK,IAAID,oBAAoB,EAAE;QACxC,IAAIwB,sBAAsB,EAAE;UAC1BA,sBAAsB,CAACD,MAAM,CAAC,CAAC;QACjC,CAAC,MAAM;UAELlC,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAEqC,oBAAoB,CAACR,sBAAsB,CAAC;QAC1D;MACF;IACF,CAAC;EACH,CAAC,EAAE,CAACnB,KAAK,CAACE,KAAK,EAAED,oBAAoB,CAAC,CAAC;EAEvC,IAAMC,KAAK,GAAGd,KAAK,CAACwC,OAAO,CAAC,YAAM;IAAA,IAAAC,YAAA,EAAAC,aAAA;IAChC,IAAMC,YAAY,GAAG,EAAAF,YAAA,GAAA7B,KAAK,CAACE,KAAK,cAAA2B,YAAA,uBAAXA,YAAA,CAAavB,OAAO,KAAI,CAAC;IAC9C,IAAM0B,MAAM,GAAGf,WAAW,IAAI,OAAO;IACrC,IAAMgB,gBAAgB,GAAGvC,sBAAsB,CAACqC,YAAY,CAAC,CAACC,MAAM,CAAC;IAErE,IAAME,iBAAiB,GAAAC,aAAA,CAAAA,aAAA,CAAAA,aAAA,KAClBF,gBAAgB,GAChBjC,KAAK,CAACE,KAAK;MACdI,OAAO,EAAEyB,YAAY;MACrBK,SAAS,EAAAD,aAAA,CAAAA,aAAA,KACP,CAAAL,aAAA,GAAG9B,KAAK,CAACE,KAAK,cAAA4B,aAAA,uBAAXA,aAAA,CAAaM,SAAS;QACzBC,KAAK,EAAExB,mBAAmB,GAAG,CAAC,GAAG;MAAA;IACnC,EACD;IAED,OAAAsB,aAAA,CAAAA,aAAA,KACKD,iBAAiB;MACpBI,IAAI,EAAEJ,iBAAiB,CAAC5B,OAAO,KAAK;IAAA;EAExC,CAAC,EAAE,CAACW,WAAW,EAAEjB,KAAK,CAACE,KAAK,EAAEW,mBAAmB,CAAC,CAAC;EAEnD,IAAQ0B,QAAQ,GAAevC,KAAK,CAA5BuC,QAAQ;IAAEC,QAAA,GAAaxC,KAAK,CAAlBwC,QAAA;EAElB,IAAMC,aAAa,GAAGrD,KAAK,CAACwC,OAAO,CACjC;IAAA,OAAAO,aAAA;MACEO,IAAI,EAAE9C,qBAAqB;MAC3B+C,mBAAmB,EAAE;IAAI,GACtBH,QAAA;EAAA,CACH,EACF,CAACA,QAAQ,CACX,CAAC;EAED,OACEpD,KAAA,CAAAwD,aAAA,CAACrD,sBAAsB,QACrBH,KAAA,CAAAwD,aAAA,CAAC/C,UAAU,QACTT,KAAA,CAAAwD,aAAA,CAACnD,gBAAgB;IAACoD,KAAK,EAAEJ;EAAc,GACrCrD,KAAA,CAAAwD,aAAA,CAACjD,aAAa;IAACO,KAAK,EAAEA;EAAM,GAAEqC,QAAwB,CACtC,CACR,CACU,CAAC;AAE7B,CAAC;AAED,eAAexC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}