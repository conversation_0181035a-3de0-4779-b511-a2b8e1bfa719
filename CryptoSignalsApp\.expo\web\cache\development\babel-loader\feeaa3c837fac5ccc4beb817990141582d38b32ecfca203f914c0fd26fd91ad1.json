{"ast": null, "code": "import StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: '#fff'\n  },\n  profileHeader: {\n    padding: 20,\n    alignItems: 'center',\n    borderBottomWidth: 1,\n    borderBottomColor: '#eee'\n  },\n  profilePicture: {\n    width: 100,\n    height: 100,\n    borderRadius: 50,\n    marginBottom: 10\n  },\n  username: {\n    fontSize: 20,\n    fontWeight: 'bold',\n    marginBottom: 5\n  },\n  fullName: {\n    fontSize: 16,\n    color: '#555',\n    marginBottom: 5\n  },\n  bio: {\n    fontSize: 14,\n    color: '#777',\n    marginTop: 10\n  },\n  privacySwitch: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    marginTop: 20\n  },\n  privacyLabel: {\n    fontSize: 16,\n    marginRight: 10\n  },\n  metricsSection: {\n    padding: 20,\n    borderBottomWidth: 1,\n    borderBottomColor: '#eee'\n  },\n  sectionTitle: {\n    fontSize: 20,\n    fontWeight: 'bold',\n    marginBottom: 10\n  },\n  badgesSection: {\n    padding: 20\n  },\n  cancelSubscriptionButton: {\n    backgroundColor: '#dc3545',\n    padding: 12,\n    borderRadius: 4,\n    alignItems: 'center',\n    margin: 20\n  },\n  cancelSubscriptionText: {\n    color: '#fff',\n    fontSize: 16\n  }\n});\nexport default styles;", "map": {"version": 3, "names": ["styles", "StyleSheet", "create", "container", "flex", "backgroundColor", "<PERSON><PERSON><PERSON><PERSON>", "padding", "alignItems", "borderBottomWidth", "borderBottomColor", "profilePicture", "width", "height", "borderRadius", "marginBottom", "username", "fontSize", "fontWeight", "fullName", "color", "bio", "marginTop", "privacySwitch", "flexDirection", "privacyLabel", "marginRight", "metricsSection", "sectionTitle", "badgesSection", "cancelSubscriptionButton", "margin", "cancelSubscriptionText"], "sources": ["E:/CryptoSignalsApp/src/pages/User/styles.js"], "sourcesContent": ["import { StyleSheet } from 'react-native';\r\n\r\nconst styles = StyleSheet.create({\r\n  container: {\r\n    flex: 1,\r\n    backgroundColor: '#fff',\r\n  },\r\n  profileHeader: {\r\n    padding: 20,\r\n    alignItems: 'center',\r\n    borderBottomWidth: 1,\r\n    borderBottomColor: '#eee',\r\n  },\r\n  profilePicture: {\r\n    width: 100,\r\n    height: 100,\r\n    borderRadius: 50,\r\n    marginBottom: 10,\r\n  },\r\n  username: {\r\n    fontSize: 20,\r\n    fontWeight: 'bold',\r\n    marginBottom: 5,\r\n  },\r\n  fullName: {\r\n    fontSize: 16,\r\n    color: '#555',\r\n    marginBottom: 5,\r\n  },\r\n  bio: {\r\n    fontSize: 14,\r\n    color: '#777',\r\n    marginTop: 10,\r\n  },\r\n  privacySwitch: {\r\n    flexDirection: 'row',\r\n    alignItems: 'center',\r\n    marginTop: 20,\r\n  },\r\n  privacyLabel: {\r\n    fontSize: 16,\r\n    marginRight: 10,\r\n  },\r\n  metricsSection: {\r\n    padding: 20,\r\n    borderBottomWidth: 1,\r\n    borderBottomColor: '#eee',\r\n  },\r\n  sectionTitle: {\r\n    fontSize: 20,\r\n    fontWeight: 'bold',\r\n    marginBottom: 10,\r\n  },\r\n  badgesSection: {\r\n    padding: 20,\r\n  },\r\n  cancelSubscriptionButton: {\r\n    backgroundColor: '#dc3545',\r\n    padding: 12,\r\n    borderRadius: 4,\r\n    alignItems: 'center',\r\n    margin: 20,\r\n  },\r\n  cancelSubscriptionText: {\r\n    color: '#fff',\r\n    fontSize: 16,\r\n  },\r\n});\r\n\r\nexport default styles;\r\n"], "mappings": ";AAEA,IAAMA,MAAM,GAAGC,UAAU,CAACC,MAAM,CAAC;EAC/BC,SAAS,EAAE;IACTC,IAAI,EAAE,CAAC;IACPC,eAAe,EAAE;EACnB,CAAC;EACDC,aAAa,EAAE;IACbC,OAAO,EAAE,EAAE;IACXC,UAAU,EAAE,QAAQ;IACpBC,iBAAiB,EAAE,CAAC;IACpBC,iBAAiB,EAAE;EACrB,CAAC;EACDC,cAAc,EAAE;IACdC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAE,GAAG;IACXC,YAAY,EAAE,EAAE;IAChBC,YAAY,EAAE;EAChB,CAAC;EACDC,QAAQ,EAAE;IACRC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBH,YAAY,EAAE;EAChB,CAAC;EACDI,QAAQ,EAAE;IACRF,QAAQ,EAAE,EAAE;IACZG,KAAK,EAAE,MAAM;IACbL,YAAY,EAAE;EAChB,CAAC;EACDM,GAAG,EAAE;IACHJ,QAAQ,EAAE,EAAE;IACZG,KAAK,EAAE,MAAM;IACbE,SAAS,EAAE;EACb,CAAC;EACDC,aAAa,EAAE;IACbC,aAAa,EAAE,KAAK;IACpBhB,UAAU,EAAE,QAAQ;IACpBc,SAAS,EAAE;EACb,CAAC;EACDG,YAAY,EAAE;IACZR,QAAQ,EAAE,EAAE;IACZS,WAAW,EAAE;EACf,CAAC;EACDC,cAAc,EAAE;IACdpB,OAAO,EAAE,EAAE;IACXE,iBAAiB,EAAE,CAAC;IACpBC,iBAAiB,EAAE;EACrB,CAAC;EACDkB,YAAY,EAAE;IACZX,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBH,YAAY,EAAE;EAChB,CAAC;EACDc,aAAa,EAAE;IACbtB,OAAO,EAAE;EACX,CAAC;EACDuB,wBAAwB,EAAE;IACxBzB,eAAe,EAAE,SAAS;IAC1BE,OAAO,EAAE,EAAE;IACXO,YAAY,EAAE,CAAC;IACfN,UAAU,EAAE,QAAQ;IACpBuB,MAAM,EAAE;EACV,CAAC;EACDC,sBAAsB,EAAE;IACtBZ,KAAK,EAAE,MAAM;IACbH,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC;AAEF,eAAejB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}