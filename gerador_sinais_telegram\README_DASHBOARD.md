# 🖥️ CryptoSignals - Dashboard Web

Dashboard web moderno e interativo para monitoramento em tempo real do servidor CryptoSignals.

## 🌟 Funcionalidades

### 📊 **Monitoramento em Tempo Real**
- **Métricas do Servidor**: CPU, Memória, Disco, Uptime
- **Status dos Serviços**: Gerador de Sinais, Sistema de Clientes
- **Gráficos Interativos**: Performance histórica com Chart.js
- **Atualizações Automáticas**: WebSocket para dados em tempo real

### 🚨 **Sistema de Alertas**
- **Alertas Visuais**: Notificações de problemas em tempo real
- **Níveis de Severidade**: INFO, WARNING, ERROR, CRITICAL
- **Histórico de Alertas**: Rastreamento de problemas resolvidos

### 📋 **Visualização de Logs**
- **Logs em Tempo Real**: Acompanhamento ao vivo
- **Filtragem por Nível**: INFO, WARNING, ERROR
- **Busca Avançada**: Localizar eventos específicos
- **Interface Colorida**: Logs categorizados por cores

### 📱 **Interface Responsiva**
- **Design Moderno**: Interface glassmorphism com gradientes
- **Mobile-Friendly**: Adaptável a dispositivos móveis
- **Tema Escuro**: Otimizado para uso prolongado
- **Animações Suaves**: Transições e efeitos visuais

## 🚀 Instalação e Uso

### **Método 1: Inicialização Automática (Recomendado)**

```bash
# Executar o script de inicialização
python start_dashboard.py
```

O script irá:
1. ✅ Verificar a versão do Python
2. 📦 Instalar dependências automaticamente
3. 🔧 Verificar arquivos necessários
4. 🚀 Iniciar o dashboard

### **Método 2: Instalação Manual**

```bash
# 1. Instalar dependências
pip install -r requirements_dashboard.txt

# 2. Iniciar dashboard
python web_dashboard.py
```

## 🌐 Acesso ao Dashboard

Após iniciar, o dashboard estará disponível em:

- **Local**: http://localhost:5000
- **Rede Local**: http://SEU_IP:5000

### **Exemplo de URLs:**
```
http://localhost:5000
http://127.0.0.1:5000
http://*************:5000  # Substitua pelo seu IP
```

## 📋 Dependências

```
Flask==2.3.3              # Framework web
Flask-SocketIO==5.3.6     # WebSocket para tempo real
python-socketio==5.8.0    # Cliente Socket.IO
python-engineio==4.7.1    # Engine.IO
eventlet==0.33.3          # Servidor assíncrono
paramiko==3.3.1           # Conexão SSH
psutil==5.9.5             # Métricas do sistema
requests==2.31.0          # Requisições HTTP
python-dotenv==1.0.0      # Variáveis de ambiente
```

## 🏗️ Estrutura do Projeto

```
📁 projeto/
├── 🐍 web_dashboard.py           # Servidor Flask principal
├── 🚀 start_dashboard.py         # Script de inicialização
├── 📋 requirements_dashboard.txt # Dependências
├── 📖 README_DASHBOARD.md        # Esta documentação
├── 📁 templates/
│   └── 🌐 dashboard.html         # Interface web
├── 📁 dashboard_data/            # Dados do dashboard (criado automaticamente)
└── 📁 core/
    └── 🔧 monitor.py             # Monitor avançado
```

## 🎨 Interface do Dashboard

### **Seções Principais:**

1. **📊 Métricas do Servidor**
   - CPU, Memória, Disco em tempo real
   - Barras de progresso coloridas
   - Uptime do servidor

2. **🔧 Status dos Serviços**
   - Gerador de Sinais Telegram
   - Sistema de Clientes Streamlit
   - Indicadores visuais de status

3. **🚨 Alertas Ativos**
   - Lista de problemas atuais
   - Contador de alertas
   - Classificação por severidade

4. **📈 Gráfico de Performance**
   - Histórico de CPU, Memória e Disco
   - Atualização em tempo real
   - Últimos 20 pontos de dados

5. **📋 Logs Recentes**
   - Últimas entradas de log
   - Colorização por nível
   - Scroll automático

### **Recursos Visuais:**

- 🎨 **Tema Glassmorphism**: Efeitos de vidro e transparência
- 🌈 **Gradientes**: Fundo com gradiente azul/roxo
- 📱 **Responsivo**: Adaptável a qualquer tela
- ⚡ **Animações**: Transições suaves e efeitos hover
- 🔄 **Indicadores**: Status de conexão e botão de refresh

## 🔧 Configuração Avançada

### **Personalizar Porta:**
```python
# No arquivo web_dashboard.py, linha final:
socketio.run(app, host='0.0.0.0', port=8080, debug=False)  # Porta 8080
```

### **Configurar Intervalo de Atualização:**
```python
# No arquivo web_dashboard.py, função background_monitoring():
time.sleep(5)  # Atualizar a cada 5 segundos
```

### **Modo Debug:**
```python
# Para desenvolvimento:
socketio.run(app, host='0.0.0.0', port=5000, debug=True)
```

## 🛠️ Solução de Problemas

### **Problema: "Módulo não encontrado"**
```bash
# Instalar dependências:
pip install -r requirements_dashboard.txt
```

### **Problema: "Porta já em uso"**
```bash
# Verificar processos na porta 5000:
netstat -ano | findstr :5000  # Windows
lsof -i :5000                 # Linux/Mac

# Matar processo:
taskkill /PID <PID> /F        # Windows
kill -9 <PID>                # Linux/Mac
```

### **Problema: "Erro de conexão SSH"**
- Verificar credenciais do servidor
- Confirmar conectividade de rede
- Verificar firewall

### **Problema: "Dashboard não carrega"**
- Verificar se o arquivo `templates/dashboard.html` existe
- Confirmar que o servidor Flask está rodando
- Verificar console do navegador para erros

## 📊 Métricas Monitoradas

### **Servidor:**
- 🖥️ **CPU**: Percentual de uso
- 💾 **Memória**: Percentual de uso
- 💿 **Disco**: Percentual de uso
- ⏰ **Uptime**: Tempo online
- 🔗 **Conexões**: Conexões ativas

### **Serviços:**
- ✅ **Status**: active/inactive/error
- 🔄 **Restarts**: Número de reinicializações
- 📊 **Recursos**: CPU e memória por serviço
- ⏱️ **Uptime**: Tempo de execução

### **Logs:**
- 📝 **Entradas Recentes**: Últimas 50 linhas
- 🏷️ **Níveis**: INFO, WARNING, ERROR
- 🕐 **Timestamps**: Data e hora
- 🔍 **Busca**: Filtros por palavra-chave

## 🔒 Segurança

### **Recomendações:**
- 🔐 Use HTTPS em produção
- 🛡️ Configure firewall adequadamente
- 🔑 Altere credenciais padrão
- 📝 Monitore logs de acesso
- 🚫 Restrinja acesso por IP se necessário

### **Configuração HTTPS:**
```python
# Para produção com SSL:
socketio.run(app, 
    host='0.0.0.0', 
    port=443, 
    certfile='cert.pem', 
    keyfile='key.pem'
)
```

## 🆘 Suporte

### **Logs do Dashboard:**
```bash
# Verificar logs do Python:
python web_dashboard.py

# Logs detalhados:
python web_dashboard.py --verbose
```

### **Contato:**
- 📧 Suporte técnico via issues do projeto
- 📖 Documentação completa no README principal
- 🔧 Configurações avançadas no código fonte

---

## 🎯 Próximas Funcionalidades

- 📧 **Notificações por Email**: Alertas automáticos
- 📱 **App Mobile**: Aplicativo nativo
- 🔔 **Webhooks**: Integração com Discord/Slack
- 📈 **Relatórios**: Exportação de dados
- 🎨 **Temas**: Múltiplos temas visuais
- 🌍 **Multi-idioma**: Suporte a outros idiomas

---

**🚀 Dashboard criado para o CryptoSignals - Monitoramento Profissional em Tempo Real!**
