import React from 'react';
import { View, Text } from 'react-native';
import { format } from 'date-fns';
import enUSLocale from 'date-fns/locale/en-US';
import styles from './styles';

const SignalsCard = ({ createdAt, message }) => {
  let formattedCreatedAt = createdAt

  try {
    formattedCreatedAt = format(
      new Date(createdAt),
      'LLL dd, h:mm aa',
      { locale: enUSLocale }
    );
  } catch {
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.textDate}>{formattedCreatedAt}</Text>
      </View>

      <View style={styles.body}>
        <Text style={styles.textMessage} selectable>{message}</Text>
      </View>
    </View>
  )
}

export default SignalsCard
