{"ast": null, "code": "function _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nimport * as React from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nvar DialogContent = function DialogContent(props) {\n  return React.createElement(View, _extends({}, props, {\n    style: [styles.container, props.style]\n  }), props.children);\n};\nDialogContent.displayName = 'Dialog.Content';\nvar styles = StyleSheet.create({\n  container: {\n    paddingBottom: 24,\n    paddingHorizontal: 24\n  }\n});\nexport default DialogContent;", "map": {"version": 3, "names": ["React", "View", "StyleSheet", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "props", "createElement", "_extends", "style", "styles", "container", "children", "displayName", "create", "paddingBottom", "paddingHorizontal"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\Dialog\\DialogContent.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { View, ViewStyle, StyleSheet, StyleProp } from 'react-native';\n\nexport type Props = React.ComponentPropsWithRef<typeof View> & {\n  /**\n   * Content of the `DialogContent`.\n   */\n  children: React.ReactNode;\n  style?: StyleProp<ViewStyle>;\n};\n\n/**\n * A component to show content in a Dialog.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { Dialog, Portal, Text } from 'react-native-paper';\n *\n * const MyComponent = () => {\n *   const [visible, setVisible] = React.useState(false);\n *\n *   const hideDialog = () => setVisible(false);\n *\n *   return (\n *     <Portal>\n *       <Dialog visible={visible} onDismiss={hideDialog}>\n *         <Dialog.Content>\n *           <Text variant=\"bodyMedium\">This is simple dialog</Text>\n *         </Dialog.Content>\n *       </Dialog>\n *     </Portal>\n *   );\n * };\n *\n * export default MyComponent;\n * ```\n */\nconst DialogContent = (props: Props) => (\n  <View {...props} style={[styles.container, props.style]}>\n    {props.children}\n  </View>\n);\n\nDialogContent.displayName = 'Dialog.Content';\n\nconst styles = StyleSheet.create({\n  container: {\n    paddingBottom: 24,\n    paddingHorizontal: 24,\n  },\n});\n\nexport default DialogContent;\n"], "mappings": ";;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,IAAA;AAAA,OAAAC,UAAA;AAsC9B,IAAMC,aAAa,GAAI,SAAjBA,aAAaA,CAAIC,KAAY;EAAA,OACjCJ,KAAA,CAAAK,aAAA,CAACJ,IAAI,EAAAK,QAAA,KAAKF,KAAK;IAAEG,KAAK,EAAE,CAACC,MAAM,CAACC,SAAS,EAAEL,KAAK,CAACG,KAAK;EAAE,IACrDH,KAAK,CAACM,QACH,CACP;AAAA;AAEDP,aAAa,CAACQ,WAAW,GAAG,gBAAgB;AAE5C,IAAMH,MAAM,GAAGN,UAAU,CAACU,MAAM,CAAC;EAC/BH,SAAS,EAAE;IACTI,aAAa,EAAE,EAAE;IACjBC,iBAAiB,EAAE;EACrB;AACF,CAAC,CAAC;AAEF,eAAeX,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}