{"ast": null, "code": "import getBoundingClientRect from \"../../modules/getBoundingClientRect\";\nvar emptyFunction = function emptyFunction() {};\nvar emptyObject = {};\nvar emptyArray = [];\nfunction normalizeIdentifier(identifier) {\n  return identifier > 20 ? identifier % 20 : identifier;\n}\nexport default function createResponderEvent(domEvent, responderTouchHistoryStore) {\n  var rect;\n  var propagationWasStopped = false;\n  var changedTouches;\n  var touches;\n  var domEventChangedTouches = domEvent.changedTouches;\n  var domEventType = domEvent.type;\n  var metaKey = domEvent.metaKey === true;\n  var shiftKey = domEvent.shiftKey === true;\n  var force = domEventChangedTouches && domEventChangedTouches[0].force || 0;\n  var identifier = normalizeIdentifier(domEventChangedTouches && domEventChangedTouches[0].identifier || 0);\n  var clientX = domEventChangedTouches && domEventChangedTouches[0].clientX || domEvent.clientX;\n  var clientY = domEventChangedTouches && domEventChangedTouches[0].clientY || domEvent.clientY;\n  var pageX = domEventChangedTouches && domEventChangedTouches[0].pageX || domEvent.pageX;\n  var pageY = domEventChangedTouches && domEventChangedTouches[0].pageY || domEvent.pageY;\n  var preventDefault = typeof domEvent.preventDefault === 'function' ? domEvent.preventDefault.bind(domEvent) : emptyFunction;\n  var timestamp = domEvent.timeStamp;\n  function normalizeTouches(touches) {\n    return Array.prototype.slice.call(touches).map(function (touch) {\n      return {\n        force: touch.force,\n        identifier: normalizeIdentifier(touch.identifier),\n        get locationX() {\n          return locationX(touch.clientX);\n        },\n        get locationY() {\n          return locationY(touch.clientY);\n        },\n        pageX: touch.pageX,\n        pageY: touch.pageY,\n        target: touch.target,\n        timestamp: timestamp\n      };\n    });\n  }\n  if (domEventChangedTouches != null) {\n    changedTouches = normalizeTouches(domEventChangedTouches);\n    touches = normalizeTouches(domEvent.touches);\n  } else {\n    var emulatedTouches = [{\n      force: force,\n      identifier: identifier,\n      get locationX() {\n        return locationX(clientX);\n      },\n      get locationY() {\n        return locationY(clientY);\n      },\n      pageX: pageX,\n      pageY: pageY,\n      target: domEvent.target,\n      timestamp: timestamp\n    }];\n    changedTouches = emulatedTouches;\n    touches = domEventType === 'mouseup' || domEventType === 'dragstart' ? emptyArray : emulatedTouches;\n  }\n  var responderEvent = {\n    bubbles: true,\n    cancelable: true,\n    currentTarget: null,\n    defaultPrevented: domEvent.defaultPrevented,\n    dispatchConfig: emptyObject,\n    eventPhase: domEvent.eventPhase,\n    isDefaultPrevented: function isDefaultPrevented() {\n      return domEvent.defaultPrevented;\n    },\n    isPropagationStopped: function isPropagationStopped() {\n      return propagationWasStopped;\n    },\n    isTrusted: domEvent.isTrusted,\n    nativeEvent: {\n      altKey: false,\n      ctrlKey: false,\n      metaKey: metaKey,\n      shiftKey: shiftKey,\n      changedTouches: changedTouches,\n      force: force,\n      identifier: identifier,\n      get locationX() {\n        return locationX(clientX);\n      },\n      get locationY() {\n        return locationY(clientY);\n      },\n      pageX: pageX,\n      pageY: pageY,\n      target: domEvent.target,\n      timestamp: timestamp,\n      touches: touches,\n      type: domEventType\n    },\n    persist: emptyFunction,\n    preventDefault: preventDefault,\n    stopPropagation: function stopPropagation() {\n      propagationWasStopped = true;\n    },\n    target: domEvent.target,\n    timeStamp: timestamp,\n    touchHistory: responderTouchHistoryStore.touchHistory\n  };\n  function locationX(x) {\n    rect = rect || getBoundingClientRect(responderEvent.currentTarget);\n    if (rect) {\n      return x - rect.left;\n    }\n  }\n  function locationY(y) {\n    rect = rect || getBoundingClientRect(responderEvent.currentTarget);\n    if (rect) {\n      return y - rect.top;\n    }\n  }\n  return responderEvent;\n}", "map": {"version": 3, "names": ["getBoundingClientRect", "emptyFunction", "emptyObject", "emptyArray", "normalizeIdentifier", "identifier", "createResponderEvent", "domEvent", "responderTouchHistoryStore", "rect", "propagationWasStopped", "changedTouches", "touches", "domEventChangedTouches", "domEventType", "type", "metaKey", "shift<PERSON>ey", "force", "clientX", "clientY", "pageX", "pageY", "preventDefault", "bind", "timestamp", "timeStamp", "normalizeTouches", "Array", "prototype", "slice", "call", "map", "touch", "locationX", "locationY", "target", "emulatedTouches", "responderEvent", "bubbles", "cancelable", "currentTarget", "defaultPrevented", "dispatchConfig", "eventPhase", "isDefaultPrevented", "isPropagationStopped", "isTrusted", "nativeEvent", "altKey", "ctrl<PERSON>ey", "persist", "stopPropagation", "touchHistory", "x", "left", "y", "top"], "sources": ["C:/Users/<USER>/Desktop/Signals/CryptoSignalsApp/node_modules/react-native-web/dist/modules/useResponderEvents/createResponderEvent.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport getBoundingClientRect from '../../modules/getBoundingClientRect';\nvar emptyFunction = () => {};\nvar emptyObject = {};\nvar emptyArray = [];\n\n/**\n * <PERSON><PERSON> produces very large identifiers that would cause the `touchBank` array\n * length to be so large as to crash the browser, if not normalized like this.\n * In the future the `touchBank` should use an object/map instead.\n */\nfunction normalizeIdentifier(identifier) {\n  return identifier > 20 ? identifier % 20 : identifier;\n}\n\n/**\n * Converts a native DOM event to a ResponderEvent.\n * Mouse events are transformed into fake touch events.\n */\nexport default function createResponderEvent(domEvent, responderTouchHistoryStore) {\n  var rect;\n  var propagationWasStopped = false;\n  var changedTouches;\n  var touches;\n  var domEventChangedTouches = domEvent.changedTouches;\n  var domEventType = domEvent.type;\n  var metaKey = domEvent.metaKey === true;\n  var shiftKey = domEvent.shiftKey === true;\n  var force = domEventChangedTouches && domEventChangedTouches[0].force || 0;\n  var identifier = normalizeIdentifier(domEventChangedTouches && domEventChangedTouches[0].identifier || 0);\n  var clientX = domEventChangedTouches && domEventChangedTouches[0].clientX || domEvent.clientX;\n  var clientY = domEventChangedTouches && domEventChangedTouches[0].clientY || domEvent.clientY;\n  var pageX = domEventChangedTouches && domEventChangedTouches[0].pageX || domEvent.pageX;\n  var pageY = domEventChangedTouches && domEventChangedTouches[0].pageY || domEvent.pageY;\n  var preventDefault = typeof domEvent.preventDefault === 'function' ? domEvent.preventDefault.bind(domEvent) : emptyFunction;\n  var timestamp = domEvent.timeStamp;\n  function normalizeTouches(touches) {\n    return Array.prototype.slice.call(touches).map(touch => {\n      return {\n        force: touch.force,\n        identifier: normalizeIdentifier(touch.identifier),\n        get locationX() {\n          return locationX(touch.clientX);\n        },\n        get locationY() {\n          return locationY(touch.clientY);\n        },\n        pageX: touch.pageX,\n        pageY: touch.pageY,\n        target: touch.target,\n        timestamp\n      };\n    });\n  }\n  if (domEventChangedTouches != null) {\n    changedTouches = normalizeTouches(domEventChangedTouches);\n    touches = normalizeTouches(domEvent.touches);\n  } else {\n    var emulatedTouches = [{\n      force,\n      identifier,\n      get locationX() {\n        return locationX(clientX);\n      },\n      get locationY() {\n        return locationY(clientY);\n      },\n      pageX,\n      pageY,\n      target: domEvent.target,\n      timestamp\n    }];\n    changedTouches = emulatedTouches;\n    touches = domEventType === 'mouseup' || domEventType === 'dragstart' ? emptyArray : emulatedTouches;\n  }\n  var responderEvent = {\n    bubbles: true,\n    cancelable: true,\n    // `currentTarget` is set before dispatch\n    currentTarget: null,\n    defaultPrevented: domEvent.defaultPrevented,\n    dispatchConfig: emptyObject,\n    eventPhase: domEvent.eventPhase,\n    isDefaultPrevented() {\n      return domEvent.defaultPrevented;\n    },\n    isPropagationStopped() {\n      return propagationWasStopped;\n    },\n    isTrusted: domEvent.isTrusted,\n    nativeEvent: {\n      altKey: false,\n      ctrlKey: false,\n      metaKey,\n      shiftKey,\n      changedTouches,\n      force,\n      identifier,\n      get locationX() {\n        return locationX(clientX);\n      },\n      get locationY() {\n        return locationY(clientY);\n      },\n      pageX,\n      pageY,\n      target: domEvent.target,\n      timestamp,\n      touches,\n      type: domEventType\n    },\n    persist: emptyFunction,\n    preventDefault,\n    stopPropagation() {\n      propagationWasStopped = true;\n    },\n    target: domEvent.target,\n    timeStamp: timestamp,\n    touchHistory: responderTouchHistoryStore.touchHistory\n  };\n\n  // Using getters and functions serves two purposes:\n  // 1) The value of `currentTarget` is not initially available.\n  // 2) Measuring the clientRect may cause layout jank and should only be done on-demand.\n  function locationX(x) {\n    rect = rect || getBoundingClientRect(responderEvent.currentTarget);\n    if (rect) {\n      return x - rect.left;\n    }\n  }\n  function locationY(y) {\n    rect = rect || getBoundingClientRect(responderEvent.currentTarget);\n    if (rect) {\n      return y - rect.top;\n    }\n  }\n  return responderEvent;\n}"], "mappings": "AASA,OAAOA,qBAAqB;AAC5B,IAAIC,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS,CAAC,CAAC;AAC5B,IAAIC,WAAW,GAAG,CAAC,CAAC;AACpB,IAAIC,UAAU,GAAG,EAAE;AAOnB,SAASC,mBAAmBA,CAACC,UAAU,EAAE;EACvC,OAAOA,UAAU,GAAG,EAAE,GAAGA,UAAU,GAAG,EAAE,GAAGA,UAAU;AACvD;AAMA,eAAe,SAASC,oBAAoBA,CAACC,QAAQ,EAAEC,0BAA0B,EAAE;EACjF,IAAIC,IAAI;EACR,IAAIC,qBAAqB,GAAG,KAAK;EACjC,IAAIC,cAAc;EAClB,IAAIC,OAAO;EACX,IAAIC,sBAAsB,GAAGN,QAAQ,CAACI,cAAc;EACpD,IAAIG,YAAY,GAAGP,QAAQ,CAACQ,IAAI;EAChC,IAAIC,OAAO,GAAGT,QAAQ,CAACS,OAAO,KAAK,IAAI;EACvC,IAAIC,QAAQ,GAAGV,QAAQ,CAACU,QAAQ,KAAK,IAAI;EACzC,IAAIC,KAAK,GAAGL,sBAAsB,IAAIA,sBAAsB,CAAC,CAAC,CAAC,CAACK,KAAK,IAAI,CAAC;EAC1E,IAAIb,UAAU,GAAGD,mBAAmB,CAACS,sBAAsB,IAAIA,sBAAsB,CAAC,CAAC,CAAC,CAACR,UAAU,IAAI,CAAC,CAAC;EACzG,IAAIc,OAAO,GAAGN,sBAAsB,IAAIA,sBAAsB,CAAC,CAAC,CAAC,CAACM,OAAO,IAAIZ,QAAQ,CAACY,OAAO;EAC7F,IAAIC,OAAO,GAAGP,sBAAsB,IAAIA,sBAAsB,CAAC,CAAC,CAAC,CAACO,OAAO,IAAIb,QAAQ,CAACa,OAAO;EAC7F,IAAIC,KAAK,GAAGR,sBAAsB,IAAIA,sBAAsB,CAAC,CAAC,CAAC,CAACQ,KAAK,IAAId,QAAQ,CAACc,KAAK;EACvF,IAAIC,KAAK,GAAGT,sBAAsB,IAAIA,sBAAsB,CAAC,CAAC,CAAC,CAACS,KAAK,IAAIf,QAAQ,CAACe,KAAK;EACvF,IAAIC,cAAc,GAAG,OAAOhB,QAAQ,CAACgB,cAAc,KAAK,UAAU,GAAGhB,QAAQ,CAACgB,cAAc,CAACC,IAAI,CAACjB,QAAQ,CAAC,GAAGN,aAAa;EAC3H,IAAIwB,SAAS,GAAGlB,QAAQ,CAACmB,SAAS;EAClC,SAASC,gBAAgBA,CAACf,OAAO,EAAE;IACjC,OAAOgB,KAAK,CAACC,SAAS,CAACC,KAAK,CAACC,IAAI,CAACnB,OAAO,CAAC,CAACoB,GAAG,CAAC,UAAAC,KAAK,EAAI;MACtD,OAAO;QACLf,KAAK,EAAEe,KAAK,CAACf,KAAK;QAClBb,UAAU,EAAED,mBAAmB,CAAC6B,KAAK,CAAC5B,UAAU,CAAC;QACjD,IAAI6B,SAASA,CAAA,EAAG;UACd,OAAOA,SAAS,CAACD,KAAK,CAACd,OAAO,CAAC;QACjC,CAAC;QACD,IAAIgB,SAASA,CAAA,EAAG;UACd,OAAOA,SAAS,CAACF,KAAK,CAACb,OAAO,CAAC;QACjC,CAAC;QACDC,KAAK,EAAEY,KAAK,CAACZ,KAAK;QAClBC,KAAK,EAAEW,KAAK,CAACX,KAAK;QAClBc,MAAM,EAAEH,KAAK,CAACG,MAAM;QACpBX,SAAS,EAATA;MACF,CAAC;IACH,CAAC,CAAC;EACJ;EACA,IAAIZ,sBAAsB,IAAI,IAAI,EAAE;IAClCF,cAAc,GAAGgB,gBAAgB,CAACd,sBAAsB,CAAC;IACzDD,OAAO,GAAGe,gBAAgB,CAACpB,QAAQ,CAACK,OAAO,CAAC;EAC9C,CAAC,MAAM;IACL,IAAIyB,eAAe,GAAG,CAAC;MACrBnB,KAAK,EAALA,KAAK;MACLb,UAAU,EAAVA,UAAU;MACV,IAAI6B,SAASA,CAAA,EAAG;QACd,OAAOA,SAAS,CAACf,OAAO,CAAC;MAC3B,CAAC;MACD,IAAIgB,SAASA,CAAA,EAAG;QACd,OAAOA,SAAS,CAACf,OAAO,CAAC;MAC3B,CAAC;MACDC,KAAK,EAALA,KAAK;MACLC,KAAK,EAALA,KAAK;MACLc,MAAM,EAAE7B,QAAQ,CAAC6B,MAAM;MACvBX,SAAS,EAATA;IACF,CAAC,CAAC;IACFd,cAAc,GAAG0B,eAAe;IAChCzB,OAAO,GAAGE,YAAY,KAAK,SAAS,IAAIA,YAAY,KAAK,WAAW,GAAGX,UAAU,GAAGkC,eAAe;EACrG;EACA,IAAIC,cAAc,GAAG;IACnBC,OAAO,EAAE,IAAI;IACbC,UAAU,EAAE,IAAI;IAEhBC,aAAa,EAAE,IAAI;IACnBC,gBAAgB,EAAEnC,QAAQ,CAACmC,gBAAgB;IAC3CC,cAAc,EAAEzC,WAAW;IAC3B0C,UAAU,EAAErC,QAAQ,CAACqC,UAAU;IAC/BC,kBAAkB,WAAlBA,kBAAkBA,CAAA,EAAG;MACnB,OAAOtC,QAAQ,CAACmC,gBAAgB;IAClC,CAAC;IACDI,oBAAoB,WAApBA,oBAAoBA,CAAA,EAAG;MACrB,OAAOpC,qBAAqB;IAC9B,CAAC;IACDqC,SAAS,EAAExC,QAAQ,CAACwC,SAAS;IAC7BC,WAAW,EAAE;MACXC,MAAM,EAAE,KAAK;MACbC,OAAO,EAAE,KAAK;MACdlC,OAAO,EAAPA,OAAO;MACPC,QAAQ,EAARA,QAAQ;MACRN,cAAc,EAAdA,cAAc;MACdO,KAAK,EAALA,KAAK;MACLb,UAAU,EAAVA,UAAU;MACV,IAAI6B,SAASA,CAAA,EAAG;QACd,OAAOA,SAAS,CAACf,OAAO,CAAC;MAC3B,CAAC;MACD,IAAIgB,SAASA,CAAA,EAAG;QACd,OAAOA,SAAS,CAACf,OAAO,CAAC;MAC3B,CAAC;MACDC,KAAK,EAALA,KAAK;MACLC,KAAK,EAALA,KAAK;MACLc,MAAM,EAAE7B,QAAQ,CAAC6B,MAAM;MACvBX,SAAS,EAATA,SAAS;MACTb,OAAO,EAAPA,OAAO;MACPG,IAAI,EAAED;IACR,CAAC;IACDqC,OAAO,EAAElD,aAAa;IACtBsB,cAAc,EAAdA,cAAc;IACd6B,eAAe,WAAfA,eAAeA,CAAA,EAAG;MAChB1C,qBAAqB,GAAG,IAAI;IAC9B,CAAC;IACD0B,MAAM,EAAE7B,QAAQ,CAAC6B,MAAM;IACvBV,SAAS,EAAED,SAAS;IACpB4B,YAAY,EAAE7C,0BAA0B,CAAC6C;EAC3C,CAAC;EAKD,SAASnB,SAASA,CAACoB,CAAC,EAAE;IACpB7C,IAAI,GAAGA,IAAI,IAAIT,qBAAqB,CAACsC,cAAc,CAACG,aAAa,CAAC;IAClE,IAAIhC,IAAI,EAAE;MACR,OAAO6C,CAAC,GAAG7C,IAAI,CAAC8C,IAAI;IACtB;EACF;EACA,SAASpB,SAASA,CAACqB,CAAC,EAAE;IACpB/C,IAAI,GAAGA,IAAI,IAAIT,qBAAqB,CAACsC,cAAc,CAACG,aAAa,CAAC;IAClE,IAAIhC,IAAI,EAAE;MACR,OAAO+C,CAAC,GAAG/C,IAAI,CAACgD,GAAG;IACrB;EACF;EACA,OAAOnB,cAAc;AACvB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}