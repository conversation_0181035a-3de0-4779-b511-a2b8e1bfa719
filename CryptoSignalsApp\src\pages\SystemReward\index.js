import React from 'react';
import { View, Text, ScrollView } from 'react-native';
import styles from './styles'; // Certifique-se de criar seu arquivo de estilos

const Rewards = () => {
  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Sistema de Recompensas</Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Pontuação e Rank</Text>
        <Text style={styles.paragraph}>
          O sistema de recompensas é baseado na pontuação que os usuários ganham por interagir no aplicativo.
          Quanto mais você interage, mais pontos você ganha, e seu rank na comunidade aumenta.
        </Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Badges</Text>
        <Text style={styles.paragraph}>
          Ao atingir determinadas metas de pontuação, você será recompensado com distintivos especiais.
          Essas badges são exibidas em seu perfil e representam suas conquistas na comunidade.
        </Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Recompensas</Text>
        <Text style={styles.paragraph}>
          As recompensas são desbloqueadas à medida que você alcança certos níveis de pontuação e rank.
          Essas recompensas podem incluir acesso a recursos premium, descontos exclusivos e muito mais.
        </Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Privacidade</Text>
        <Text style={styles.paragraph}>
          Você pode optar por tornar seu perfil público ou privado. Se for público, outros membros da comunidade
          poderão ver suas conquistas e badges. Se for privado, apenas você terá acesso às informações do seu perfil.
        </Text>
      </View>
    </ScrollView>
  );
};

export default Rewards;
