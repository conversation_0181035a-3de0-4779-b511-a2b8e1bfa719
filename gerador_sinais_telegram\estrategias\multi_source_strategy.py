#!/usr/bin/env python3
"""
Estratégia Multi-Source - Combina múltiplas fontes de sinais
"""

import logging
import pandas as pd
import numpy as np
from typing import Tuple, Optional, List, Dict
import sys
import os

# Adicionar o diretório raiz ao path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from utils.technical_indicators import talib

logger = logging.getLogger(__name__)

class MultiSourceStrategy:
    def __init__(self, binance_handler):
        self.binance = binance_handler
        self.name = "Multi-Source"

        # Configurações da estratégia
        self.min_volume_threshold = 1000000  # Volume mínimo em USDT
        self.rsi_oversold = 30
        self.rsi_overbought = 70
        self.bb_period = 20
        self.macd_fast = 12
        self.macd_slow = 26
        self.macd_signal = 9

        # Pesos para diferentes fontes de sinais
        self.signal_weights = {
            'rsi': 0.25,
            'bollinger': 0.20,
            'macd': 0.20,
            'volume': 0.15,
            'momentum': 0.10,
            'support_resistance': 0.10
        }

        logger.info(f"Estratégia {self.name} inicializada com múltiplas fontes de sinais")

    def analyze_symbol(self, symbol: str) -> Tuple[Optional[str], Optional[float], Optional[float], Optional[float]]:
        """
        Analisa um símbolo usando múltiplas fontes de sinais

        Returns:
            Tuple: (signal_type, entry_price, stop_loss, take_profit)
        """
        try:
            # Obter dados de diferentes timeframes
            data_1m = self.binance.get_historical_klines(symbol, "1m", 1)
            data_5m = self.binance.get_historical_klines(symbol, "5m", 1)
            data_15m = self.binance.get_historical_klines(symbol, "15m", 1)

            if data_1m.empty or data_5m.empty or data_15m.empty:
                return None, None, None, None

            # Analisar cada fonte de sinal
            signals = {}
            signals['rsi'] = self._analyze_rsi(data_15m)
            signals['bollinger'] = self._analyze_bollinger_bands(data_15m)
            signals['macd'] = self._analyze_macd(data_15m)
            signals['volume'] = self._analyze_volume(data_5m)
            signals['momentum'] = self._analyze_momentum(data_15m)
            signals['support_resistance'] = self._analyze_support_resistance(data_15m)

            # Combinar sinais com pesos
            final_signal = self._combine_signals(signals)

            if final_signal['strength'] < 0.6:  # Threshold mínimo de 60%
                return None, None, None, None

            # Obter preço atual
            current_price = self.binance.get_current_price(symbol)
            if not current_price or not self._validate_price_range(symbol, current_price):
                return None, None, None, None

            # Calcular stop loss e take profit
            signal_type = final_signal['direction']
            stop_loss, take_profit = self._calculate_levels(current_price, signal_type, final_signal['strength'])

            # Validar se os cálculos fazem sentido
            if not self._validate_signal_values(symbol, signal_type, current_price, stop_loss, take_profit):
                return None, None, None, None

            logger.info(f"{symbol} - Sinal Multi-Source {signal_type} (força: {final_signal['strength']:.2f})")
            return signal_type, float(current_price), float(stop_loss), float(take_profit)

        except Exception as e:
            logger.error(f"Erro ao analisar {symbol} com Multi-Source: {e}")
            return None, None, None, None

    def _analyze_rsi(self, data: pd.DataFrame) -> Dict:
        """Analisa RSI para sinais de sobrecompra/sobrevenda"""
        try:
            rsi = talib.RSI(data['close'].values, timeperiod=14)
            current_rsi = rsi[-1]

            if current_rsi < self.rsi_oversold:
                return {'signal': 'LONG', 'strength': (self.rsi_oversold - current_rsi) / self.rsi_oversold}
            elif current_rsi > self.rsi_overbought:
                return {'signal': 'SHORT', 'strength': (current_rsi - self.rsi_overbought) / (100 - self.rsi_overbought)}
            else:
                return {'signal': 'NEUTRAL', 'strength': 0}
        except:
            return {'signal': 'NEUTRAL', 'strength': 0}

    def _analyze_bollinger_bands(self, data: pd.DataFrame) -> Dict:
        """Analisa Bandas de Bollinger"""
        try:
            upper, middle, lower = talib.BBANDS(data['close'].values, timeperiod=self.bb_period)
            current_price = data['close'].iloc[-1]

            if current_price <= lower[-1]:
                strength = (lower[-1] - current_price) / lower[-1]
                return {'signal': 'LONG', 'strength': min(strength * 2, 1.0)}
            elif current_price >= upper[-1]:
                strength = (current_price - upper[-1]) / upper[-1]
                return {'signal': 'SHORT', 'strength': min(strength * 2, 1.0)}
            else:
                return {'signal': 'NEUTRAL', 'strength': 0}
        except:
            return {'signal': 'NEUTRAL', 'strength': 0}

    def _analyze_macd(self, data: pd.DataFrame) -> Dict:
        """Analisa MACD para sinais de momentum"""
        try:
            macd, signal, hist = talib.MACD(data['close'].values,
                                          fastperiod=self.macd_fast,
                                          slowperiod=self.macd_slow,
                                          signalperiod=self.macd_signal)

            # Verificar cruzamento
            if len(hist) >= 2:
                if hist[-2] <= 0 and hist[-1] > 0:  # Cruzamento para cima
                    return {'signal': 'LONG', 'strength': min(abs(hist[-1]) * 10, 1.0)}
                elif hist[-2] >= 0 and hist[-1] < 0:  # Cruzamento para baixo
                    return {'signal': 'SHORT', 'strength': min(abs(hist[-1]) * 10, 1.0)}

            return {'signal': 'NEUTRAL', 'strength': 0}
        except:
            return {'signal': 'NEUTRAL', 'strength': 0}

    def _analyze_volume(self, data: pd.DataFrame) -> Dict:
        """Analisa volume para confirmação"""
        try:
            volume_sma = talib.SMA(data['volume'].values, timeperiod=20)
            current_volume = data['volume'].iloc[-1]
            avg_volume = volume_sma[-1]

            if current_volume > avg_volume * 1.5:  # Volume 50% acima da média
                # Determinar direção baseada no preço
                price_change = (data['close'].iloc[-1] - data['close'].iloc[-2]) / data['close'].iloc[-2]
                strength = min((current_volume / avg_volume - 1), 1.0)

                if price_change > 0:
                    return {'signal': 'LONG', 'strength': strength}
                else:
                    return {'signal': 'SHORT', 'strength': strength}

            return {'signal': 'NEUTRAL', 'strength': 0}
        except:
            return {'signal': 'NEUTRAL', 'strength': 0}

    def _analyze_momentum(self, data: pd.DataFrame) -> Dict:
        """Analisa momentum de preço"""
        try:
            momentum = talib.MOM(data['close'].values, timeperiod=10)
            current_momentum = momentum[-1]

            # Normalizar momentum
            momentum_strength = min(abs(current_momentum) / data['close'].iloc[-1] * 100, 1.0)

            if current_momentum > 0:
                return {'signal': 'LONG', 'strength': momentum_strength}
            elif current_momentum < 0:
                return {'signal': 'SHORT', 'strength': momentum_strength}
            else:
                return {'signal': 'NEUTRAL', 'strength': 0}
        except:
            return {'signal': 'NEUTRAL', 'strength': 0}

    def _analyze_support_resistance(self, data: pd.DataFrame) -> Dict:
        """Analisa níveis de suporte e resistência"""
        try:
            # Calcular níveis de suporte e resistência simples
            highs = data['high'].rolling(window=10).max()
            lows = data['low'].rolling(window=10).min()
            current_price = data['close'].iloc[-1]

            resistance = highs.iloc[-1]
            support = lows.iloc[-1]

            # Verificar proximidade aos níveis
            resistance_distance = (resistance - current_price) / current_price
            support_distance = (current_price - support) / current_price

            if support_distance < 0.01:  # Próximo ao suporte (1%)
                return {'signal': 'LONG', 'strength': 0.7}
            elif resistance_distance < 0.01:  # Próximo à resistência (1%)
                return {'signal': 'SHORT', 'strength': 0.7}
            else:
                return {'signal': 'NEUTRAL', 'strength': 0}
        except:
            return {'signal': 'NEUTRAL', 'strength': 0}

    def _combine_signals(self, signals: Dict) -> Dict:
        """Combina todos os sinais com pesos"""
        long_score = 0
        short_score = 0

        for source, signal_data in signals.items():
            weight = self.signal_weights.get(source, 0)
            strength = signal_data.get('strength', 0)
            signal = signal_data.get('signal', 'NEUTRAL')

            if signal == 'LONG':
                long_score += weight * strength
            elif signal == 'SHORT':
                short_score += weight * strength

        # Determinar sinal final
        if long_score > short_score and long_score > 0.6:
            return {'direction': 'LONG', 'strength': long_score}
        elif short_score > long_score and short_score > 0.6:
            return {'direction': 'SHORT', 'strength': short_score}
        else:
            return {'direction': 'NEUTRAL', 'strength': max(long_score, short_score)}

    def _calculate_levels(self, entry_price: float, signal_type: str, strength: float) -> Tuple[float, float]:
        """Calcula stop loss e take profit baseado na força do sinal"""
        # Ajustar níveis baseado na força do sinal
        base_sl_percent = 0.02  # 2% base
        base_tp_percent = 0.04  # 4% base

        # Quanto maior a força, menor o stop loss e maior o take profit
        sl_percent = base_sl_percent * (2 - strength)  # Reduz SL com força alta
        tp_percent = base_tp_percent * (1 + strength)  # Aumenta TP com força alta

        if signal_type == 'LONG':
            stop_loss = entry_price * (1 - sl_percent)
            take_profit = entry_price * (1 + tp_percent)
        else:  # SHORT
            stop_loss = entry_price * (1 + sl_percent)
            take_profit = entry_price * (1 - tp_percent)

        return round(stop_loss, 6), round(take_profit, 6)

    def _validate_price_range(self, symbol: str, price: float) -> bool:
        """Valida se um preço está dentro de uma faixa realista"""
        if price <= 0:
            return False

        price_ranges = {
            'BTC': (10000, 150000), 'ETH': (500, 10000), 'BNB': (50, 1000),
            'ADA': (0.1, 5), 'SOL': (10, 1000), 'DOT': (1, 50),
            'LINK': (1, 100), 'MATIC': (0.1, 10), 'AVAX': (5, 200)
        }

        for coin, (min_price, max_price) in price_ranges.items():
            if coin in symbol:
                return min_price <= price <= max_price

        return 0.001 <= price <= 100000

    def _validate_signal_values(self, symbol: str, signal_type: str, entry_price: float,
                               stop_loss: float, take_profit: float) -> bool:
        """Valida se os valores do sinal fazem sentido"""
        try:
            if entry_price <= 0 or stop_loss <= 0 or take_profit <= 0:
                return False

            if signal_type == 'LONG':
                if stop_loss >= entry_price or take_profit <= entry_price:
                    return False
                max_tp = entry_price * 1.10  # Máximo 10%
                if take_profit > max_tp:
                    return False
            else:  # SHORT
                if stop_loss <= entry_price or take_profit >= entry_price:
                    return False
                min_tp = entry_price * 0.90  # Mínimo 10%
                if take_profit < min_tp:
                    return False

            return True
        except:
            return False
