{"ast": null, "code": "var CSS_UNIT_RE = /^[+-]?\\d*(?:\\.\\d+)?(?:[Ee][+-]?\\d+)?(%|\\w*)/;\nvar getUnit = function getUnit(str) {\n  return str.match(CSS_UNIT_RE)[1];\n};\nvar isNumeric = function isNumeric(n) {\n  return !isNaN(parseFloat(n)) && isFinite(n);\n};\nvar multiplyStyleLengthValue = function multiplyStyleLengthValue(value, multiple) {\n  if (typeof value === 'string') {\n    var number = parseFloat(value) * multiple;\n    var unit = getUnit(value);\n    return \"\" + number + unit;\n  } else if (isNumeric(value)) {\n    return value * multiple;\n  }\n};\nexport default multiplyStyleLengthValue;", "map": {"version": 3, "names": ["CSS_UNIT_RE", "getUnit", "str", "match", "isNumeric", "n", "isNaN", "parseFloat", "isFinite", "multiplyStyleLengthValue", "value", "multiple", "number", "unit"], "sources": ["E:/CryptoSignalsApp/node_modules/react-native-web/dist/modules/multiplyStyleLengthValue/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nvar CSS_UNIT_RE = /^[+-]?\\d*(?:\\.\\d+)?(?:[Ee][+-]?\\d+)?(%|\\w*)/;\nvar getUnit = str => str.match(CSS_UNIT_RE)[1];\nvar isNumeric = n => {\n  return !isNaN(parseFloat(n)) && isFinite(n);\n};\nvar multiplyStyleLengthValue = (value, multiple) => {\n  if (typeof value === 'string') {\n    var number = parseFloat(value) * multiple;\n    var unit = getUnit(value);\n    return \"\" + number + unit;\n  } else if (isNumeric(value)) {\n    return value * multiple;\n  }\n};\nexport default multiplyStyleLengthValue;"], "mappings": "AASA,IAAIA,WAAW,GAAG,6CAA6C;AAC/D,IAAIC,OAAO,GAAG,SAAVA,OAAOA,CAAGC,GAAG;EAAA,OAAIA,GAAG,CAACC,KAAK,CAACH,WAAW,CAAC,CAAC,CAAC,CAAC;AAAA;AAC9C,IAAII,SAAS,GAAG,SAAZA,SAASA,CAAGC,CAAC,EAAI;EACnB,OAAO,CAACC,KAAK,CAACC,UAAU,CAACF,CAAC,CAAC,CAAC,IAAIG,QAAQ,CAACH,CAAC,CAAC;AAC7C,CAAC;AACD,IAAII,wBAAwB,GAAG,SAA3BA,wBAAwBA,CAAIC,KAAK,EAAEC,QAAQ,EAAK;EAClD,IAAI,OAAOD,KAAK,KAAK,QAAQ,EAAE;IAC7B,IAAIE,MAAM,GAAGL,UAAU,CAACG,KAAK,CAAC,GAAGC,QAAQ;IACzC,IAAIE,IAAI,GAAGZ,OAAO,CAACS,KAAK,CAAC;IACzB,OAAO,EAAE,GAAGE,MAAM,GAAGC,IAAI;EAC3B,CAAC,MAAM,IAAIT,SAAS,CAACM,KAAK,CAAC,EAAE;IAC3B,OAAOA,KAAK,GAAGC,QAAQ;EACzB;AACF,CAAC;AACD,eAAeF,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}