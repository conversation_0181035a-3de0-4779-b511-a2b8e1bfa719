import { useState } from "react";

export const useEmail = (defaultValue) => {
  let [email, setEmail] = useState(defaultValue ?? '');

  email = email.replace(/\s/g, '');

  const emailValidate = (text) => {
    let reg = /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w\w+)+$/;

    if (reg.test(text) === false) {
      return false;
    }  

    return true;
  }

  return [email, setEmail, emailValidate(email)]
}

export default useEmail;

