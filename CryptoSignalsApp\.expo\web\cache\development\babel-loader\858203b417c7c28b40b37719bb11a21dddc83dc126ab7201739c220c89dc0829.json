{"ast": null, "code": "import * as React from 'react';\nvar NavigationBuilderContext = React.createContext({\n  onDispatchAction: function onDispatchAction() {\n    return undefined;\n  },\n  onOptionsChange: function onOptionsChange() {\n    return undefined;\n  }\n});\nexport default NavigationBuilderContext;", "map": {"version": 3, "names": ["React", "NavigationBuilderContext", "createContext", "onDispatchAction", "undefined", "onOptionsChange"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\@react-navigation\\core\\src\\NavigationBuilderContext.tsx"], "sourcesContent": ["import type {\n  NavigationAction,\n  NavigationState,\n  ParamListBase,\n} from '@react-navigation/routers';\nimport * as React from 'react';\n\nimport type { NavigationHelpers } from './types';\n\nexport type ListenerMap = {\n  action: ChildActionListener;\n  focus: FocusedNavigationListener;\n};\n\nexport type KeyedListenerMap = {\n  getState: GetStateListener;\n  beforeRemove: ChildBeforeRemoveListener;\n};\n\nexport type AddListener = <T extends keyof ListenerMap>(\n  type: T,\n  listener: ListenerMap[T]\n) => void;\n\nexport type AddKeyedListener = <T extends keyof KeyedListenerMap>(\n  type: T,\n  key: string,\n  listener: KeyedListenerMap[T]\n) => void;\n\nexport type ChildActionListener = (\n  action: NavigationAction,\n  visitedNavigators?: Set<string>\n) => boolean;\n\nexport type FocusedNavigationCallback<T> = (\n  navigation: NavigationHelpers<ParamListBase>\n) => T;\n\nexport type FocusedNavigationListener = <T>(\n  callback: FocusedNavigationCallback<T>\n) => {\n  handled: boolean;\n  result: T;\n};\n\nexport type GetStateListener = () => NavigationState;\n\nexport type ChildBeforeRemoveListener = (action: NavigationAction) => boolean;\n\n/**\n * Context which holds the required helpers needed to build nested navigators.\n */\nconst NavigationBuilderContext = React.createContext<{\n  onAction?: (\n    action: NavigationAction,\n    visitedNavigators?: Set<string>\n  ) => boolean;\n  addListener?: AddListener;\n  addKeyedListener?: AddKeyedListener;\n  onRouteFocus?: (key: string) => void;\n  onDispatchAction: (action: NavigationAction, noop: boolean) => void;\n  onOptionsChange: (options: object) => void;\n  stackRef?: React.MutableRefObject<string | undefined>;\n}>({\n  onDispatchAction: () => undefined,\n  onOptionsChange: () => undefined,\n});\n\nexport default NavigationBuilderContext;\n"], "mappings": "AAKA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAgD9B,IAAMC,wBAAwB,GAAGD,KAAK,CAACE,aAAa,CAWjD;EACDC,gBAAgB,EAAE,SAAlBA,gBAAgBA,CAAA;IAAA,OAAQC,SAAS;EAAA;EACjCC,eAAe,EAAE,SAAjBA,eAAeA,CAAA;IAAA,OAAQD,SAAA;EAAA;AACzB,CAAC,CAAC;AAEF,eAAeH,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}