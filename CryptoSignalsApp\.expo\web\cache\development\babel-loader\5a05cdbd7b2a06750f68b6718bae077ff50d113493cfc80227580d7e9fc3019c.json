{"ast": null, "code": "export default function isArrayEqual(a, b) {\n  if (a === b) {\n    return true;\n  }\n  if (a.length !== b.length) {\n    return false;\n  }\n  return a.every(function (it, index) {\n    return it === b[index];\n  });\n}", "map": {"version": 3, "names": ["isArrayEqual", "a", "b", "length", "every", "it", "index"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\@react-navigation\\core\\src\\isArrayEqual.tsx"], "sourcesContent": ["/**\n * Compare two arrays with primitive values as the content.\n * We need to make sure that both values and order match.\n */\nexport default function isArrayEqual(a: any[], b: any[]) {\n  if (a === b) {\n    return true;\n  }\n\n  if (a.length !== b.length) {\n    return false;\n  }\n\n  return a.every((it, index) => it === b[index]);\n}\n"], "mappings": "AAIA,eAAe,SAASA,YAAYA,CAACC,CAAQ,EAAEC,CAAQ,EAAE;EACvD,IAAID,CAAC,KAAKC,CAAC,EAAE;IACX,OAAO,IAAI;EACb;EAEA,IAAID,CAAC,CAACE,MAAM,KAAKD,CAAC,CAACC,MAAM,EAAE;IACzB,OAAO,KAAK;EACd;EAEA,OAAOF,CAAC,CAACG,KAAK,CAAC,UAACC,EAAE,EAAEC,KAAK;IAAA,OAAKD,EAAE,KAAKH,CAAC,CAACI,KAAK,CAAC;EAAA,EAAC;AAChD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}