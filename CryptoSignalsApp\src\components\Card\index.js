import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Surface } from 'react-native-paper';

const Card = ({ 
  children, 
  style, 
  onPress, 
  elevation = 2,
  padding = 16,
  margin = 8,
  backgroundColor = '#2a2a2a'
}) => {
  const cardStyle = {
    backgroundColor,
    borderRadius: 12,
    padding,
    margin,
    borderWidth: 1,
    borderColor: '#333',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation,
  };

  if (onPress) {
    return (
      <TouchableOpacity style={cardStyle} onPress={onPress} activeOpacity={0.8}>
        {children}
      </TouchableOpacity>
    );
  }

  return (
    <View style={[cardStyle, style]}>
      {children}
    </View>
  );
};

export default Card;
