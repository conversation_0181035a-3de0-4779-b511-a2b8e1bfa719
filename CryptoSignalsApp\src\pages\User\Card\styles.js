import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  container: {
    backgroundColor: "#0D0D0D",
    borderRadius: 4,
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginTop: 12,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  photo: {
    backgroundColor: "#fff",
    borderRadius: 23,
    width: 46,
    height: 46,
    marginRight: 16,
  },
  title: {
    color: "#fff",
    fontSize: 15,
    lineHeight: 22,
    marginBottom: 4,
    fontFamily: "Poppins_700Bold",
  },
  time: {
    color: "#ccc",
    fontSize: 10,
    position: 'absolute',
    bottom: 8,
    right: 8,
  },
  typeContainer: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
  },
  type: {
    color: "#fff",
    fontSize: 10,
    textTransform: "uppercase",
    lineHeight: 14,
    fontFamily: "Poppins_500Medium",
  },
  dot: {
    width: 4,
    height: 4,
    backgroundColor: "#fff",
    borderRadius: 2,
    marginHorizontal: 5,
  },
  icon: {
    color: "#fff",
  },
  dFlex: {
    display: "flex",
  },
  row: {
    flexDirection: "row",
  },
});

export default styles;