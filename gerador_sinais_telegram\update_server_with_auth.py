#!/usr/bin/env python3
"""
Script de atualização do servidor CryptoSignals com suporte à autenticação do Telegram
Uso: python update_server_with_auth.py
"""

import subprocess
import sys
import time
import os

# Configurações do servidor
SERVER_IP = "**************"
SERVER_USER = "root"
SERVER_PASSWORD = "h4*ls:FtJw0e"
PROJECT_PATH = "/opt/gerador_sinais_telegram"
PHONE_NUMBER = "+5521982301476"

class Colors:
    GREEN = '\033[92m'
    BLUE = '\033[94m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    NC = '\033[0m'  # No Color

def print_colored(text, color):
    """Imprime texto colorido"""
    print(f"{color}{text}{Colors.NC}")

def run_ssh_command(command, interactive=False):
    """Executa comando SSH no servidor"""
    if interactive:
        # Para comandos interativos, usar SSH direto
        full_command = f'ssh -o StrictHostKeyChecking=no {SERVER_USER}@{SERVER_IP} "{command}"'
    else:
        # Para comandos não-interativos, usar sshpass
        full_command = f'sshpass -p "{SERVER_PASSWORD}" ssh -o StrictHostKeyChecking=no {SERVER_USER}@{SERVER_IP} "{command}"'
    
    print_colored(f"🔄 Executando: {command}", Colors.YELLOW)
    
    try:
        if interactive:
            # Para comandos interativos, não capturar output
            result = subprocess.run(full_command, shell=True)
            return result.returncode == 0, "", ""
        else:
            result = subprocess.run(full_command, shell=True, capture_output=True, text=True, timeout=30)
            return result.returncode == 0, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        print_colored("⚠️ Comando expirou (timeout)", Colors.YELLOW)
        return False, "", "Timeout"
    except Exception as e:
        print_colored(f"❌ Erro: {e}", Colors.RED)
        return False, "", str(e)

def check_telegram_auth_needed():
    """Verifica se é necessária autenticação do Telegram"""
    print_colored("🔍 Verificando se precisa de autenticação do Telegram...", Colors.BLUE)
    
    # Verificar logs recentes
    success, output, error = run_ssh_command(f"journalctl -u gerador_sinais.service --no-pager -l -n 20")
    
    if success and output:
        output_lower = output.lower()
        if any(keyword in output_lower for keyword in ["phone number", "código", "authentication", "login", "enter your phone"]):
            return True
    
    return False

def perform_telegram_auth():
    """Realiza autenticação do Telegram de forma interativa"""
    print_colored("📱 INICIANDO AUTENTICAÇÃO DO TELEGRAM", Colors.BLUE)
    print("=" * 60)
    print()
    print_colored("📋 INSTRUÇÕES IMPORTANTES:", Colors.GREEN)
    print("1. O sistema vai abrir uma sessão SSH interativa")
    print("2. Quando pedir o número de telefone, digite: +5521982301476")
    print("3. Aguarde receber o código no seu Telegram")
    print("4. Digite o código quando solicitado")
    print("5. Após a autenticação bem-sucedida, pressione Ctrl+C")
    print("6. O script continuará automaticamente")
    print()
    print_colored(f"📱 SEU NÚMERO: {PHONE_NUMBER}", Colors.YELLOW)
    print()
    
    input("Pressione ENTER para iniciar a autenticação interativa...")
    
    print_colored("🚀 Abrindo sessão SSH interativa...", Colors.BLUE)
    print_colored("DICA: Use Ctrl+C para sair após a autenticação", Colors.YELLOW)
    print("=" * 60)
    
    # Parar o serviço primeiro
    run_ssh_command("systemctl stop gerador_sinais.service")
    
    # Comando para autenticação interativa
    auth_command = f"cd {PROJECT_PATH} && source venv/bin/activate && python main.py"
    
    # Executar comando interativo
    try:
        print_colored("Conectando ao servidor para autenticação...", Colors.BLUE)
        print_colored("Digite seu número quando solicitado: +5521982301476", Colors.YELLOW)
        print()
        
        # Usar SSH interativo
        ssh_command = f'ssh -o StrictHostKeyChecking=no {SERVER_USER}@{SERVER_IP} -t "{auth_command}"'
        os.system(ssh_command)
        
        print()
        print_colored("✅ Sessão de autenticação finalizada!", Colors.GREEN)
        
    except KeyboardInterrupt:
        print()
        print_colored("✅ Autenticação interrompida pelo usuário", Colors.GREEN)
    except Exception as e:
        print_colored(f"⚠️ Erro na autenticação: {e}", Colors.YELLOW)

def update_server():
    """Atualiza o servidor com suporte à autenticação do Telegram"""
    print_colored("🚀 INICIANDO ATUALIZAÇÃO DO CRYPTOSIGNALS", Colors.GREEN)
    print("=" * 60)
    
    try:
        # 1. Parar o serviço
        print_colored("🛑 Parando serviço CryptoSignals...", Colors.BLUE)
        run_ssh_command("systemctl stop gerador_sinais.service")
        
        # 2. Fazer backup das configurações
        print_colored("💾 Fazendo backup das configurações...", Colors.BLUE)
        backup_date = time.strftime("%Y%m%d_%H%M%S")
        run_ssh_command(f"cd {PROJECT_PATH} && cp .env .env.backup.{backup_date} 2>/dev/null || echo 'Arquivo .env não encontrado'")
        
        # 3. Resolver problemas de merge e atualizar código
        print_colored("📥 Resolvendo merge e atualizando código...", Colors.BLUE)
        run_ssh_command(f"cd {PROJECT_PATH} && git config pull.rebase false")
        run_ssh_command(f"cd {PROJECT_PATH} && git stash")
        
        success, output, error = run_ssh_command(f"cd {PROJECT_PATH} && git pull origin main")
        
        if not success:
            print_colored("⚠️ Erro no git pull, forçando atualização...", Colors.YELLOW)
            run_ssh_command(f"cd {PROJECT_PATH} && git fetch origin main")
            run_ssh_command(f"cd {PROJECT_PATH} && git reset --hard origin/main")
            print_colored("✅ Código atualizado forçadamente", Colors.GREEN)
        
        # 4. Atualizar dependências
        print_colored("📦 Atualizando dependências...", Colors.BLUE)
        run_ssh_command(f"cd {PROJECT_PATH} && source venv/bin/activate && pip install --upgrade pip")
        run_ssh_command(f"cd {PROJECT_PATH} && source venv/bin/activate && pip install pandas-ta finta --upgrade")
        
        # 5. Verificar se precisa de autenticação
        print_colored("🔐 Verificando necessidade de autenticação...", Colors.BLUE)
        
        # Tentar iniciar o serviço
        run_ssh_command("systemctl daemon-reload")
        run_ssh_command("systemctl restart gerador_sinais.service")
        
        # Aguardar e verificar
        time.sleep(3)
        
        if check_telegram_auth_needed():
            print_colored("📱 AUTENTICAÇÃO DO TELEGRAM NECESSÁRIA!", Colors.YELLOW)
            perform_telegram_auth()
        else:
            print_colored("✅ Nenhuma autenticação necessária", Colors.GREEN)
        
        # 6. Reiniciar o serviço final
        print_colored("🚀 Reiniciando serviço final...", Colors.BLUE)
        run_ssh_command("systemctl restart gerador_sinais.service")
        
        # 7. Aguardar inicialização
        print_colored("⏳ Aguardando inicialização...", Colors.YELLOW)
        time.sleep(5)
        
        # 8. Verificar status
        print_colored("📊 Verificando status do serviço...", Colors.BLUE)
        success, output, error = run_ssh_command("systemctl status gerador_sinais.service --no-pager -l")
        
        if success and "active (running)" in output:
            print_colored("✅ Serviço está rodando!", Colors.GREEN)
        else:
            print_colored("⚠️ Serviço pode não estar rodando corretamente", Colors.YELLOW)
        
        # 9. Verificar logs recentes
        print_colored("📋 Verificando logs recentes...", Colors.BLUE)
        run_ssh_command("journalctl -u gerador_sinais.service --no-pager -l -n 15")
        
        print()
        print_colored("=" * 60, Colors.GREEN)
        print_colored("  ✅ ATUALIZAÇÃO CONCLUÍDA!", Colors.GREEN)
        print_colored("=" * 60, Colors.GREEN)
        
        return True
        
    except Exception as e:
        print_colored(f"❌ Erro durante atualização: {e}", Colors.RED)
        return False

def show_monitoring_commands():
    """Mostra comandos úteis para monitoramento"""
    print()
    print_colored("📋 COMANDOS ÚTEIS PARA MONITORAMENTO:", Colors.BLUE)
    print()
    print_colored("Ver logs em tempo real:", Colors.GREEN)
    print(f"  ssh {SERVER_USER}@{SERVER_IP}")
    print("  journalctl -u gerador_sinais.service -f")
    print()
    print_colored("Ver status do serviço:", Colors.GREEN)
    print(f"  ssh {SERVER_USER}@{SERVER_IP}")
    print("  systemctl status gerador_sinais.service")
    print()
    print_colored("Reiniciar serviço:", Colors.GREEN)
    print(f"  ssh {SERVER_USER}@{SERVER_IP}")
    print("  systemctl restart gerador_sinais.service")
    print()

def main():
    """Função principal"""
    try:
        # Verificar se sshpass está disponível
        try:
            subprocess.run(["sshpass", "-V"], capture_output=True)
        except FileNotFoundError:
            print_colored("❌ sshpass não encontrado. Instale com:", Colors.RED)
            print("  sudo apt-get install sshpass  # Ubuntu/Debian")
            print("  brew install hudochenkov/sshpass/sshpass  # macOS")
            sys.exit(1)
        
        success = update_server()
        
        if success:
            show_monitoring_commands()
            
            # Perguntar se quer ver logs
            try:
                response = input("\nDeseja ver os logs em tempo real? (y/n): ").strip().lower()
                if response in ['y', 'yes', 's', 'sim']:
                    print_colored("📋 Abrindo logs em tempo real...", Colors.BLUE)
                    os.system(f'ssh {SERVER_USER}@{SERVER_IP} "journalctl -u gerador_sinais.service -f"')
            except KeyboardInterrupt:
                print()
            
            print_colored("🎉 Script finalizado!", Colors.GREEN)
        else:
            print_colored("❌ Atualização falhou!", Colors.RED)
            sys.exit(1)
            
    except KeyboardInterrupt:
        print()
        print_colored("⚠️ Script interrompido pelo usuário", Colors.YELLOW)
    except Exception as e:
        print_colored(f"❌ Erro inesperado: {e}", Colors.RED)
        sys.exit(1)

if __name__ == "__main__":
    main()
