{"ast": null, "code": "import { useCallback, useRef } from 'react';\nexport default function useRefEffect(effect) {\n  var cleanupRef = useRef(undefined);\n  return useCallback(function (instance) {\n    if (cleanupRef.current) {\n      cleanupRef.current();\n      cleanupRef.current = undefined;\n    }\n    if (instance != null) {\n      cleanupRef.current = effect(instance);\n    }\n  }, [effect]);\n}", "map": {"version": 3, "names": ["useCallback", "useRef", "useRefEffect", "effect", "cleanupRef", "undefined", "instance", "current"], "sources": ["C:/Users/<USER>/Desktop/Signals/CryptoSignalsApp/node_modules/react-native-web/dist/vendor/react-native/Utilities/useRefEffect.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\nimport { useCallback, useRef } from 'react';\n/**\n * Constructs a callback ref that provides similar semantics as `useEffect`. The\n * supplied `effect` callback will be called with non-null component instances.\n * The `effect` callback can also optionally return a cleanup function.\n *\n * When a component is updated or unmounted, the cleanup function is called. The\n * `effect` callback will then be called again, if applicable.\n *\n * When a new `effect` callback is supplied, the previously returned cleanup\n * function will be called before the new `effect` callback is called with the\n * same instance.\n *\n * WARNING: The `effect` callback should be stable (e.g. using `useCallback`).\n */\nexport default function useRefEffect(effect) {\n  var cleanupRef = useRef(undefined);\n  return useCallback(instance => {\n    if (cleanupRef.current) {\n      cleanupRef.current();\n      cleanupRef.current = undefined;\n    }\n    if (instance != null) {\n      cleanupRef.current = effect(instance);\n    }\n  }, [effect]);\n}"], "mappings": "AAUA,SAASA,WAAW,EAAEC,MAAM,QAAQ,OAAO;AAe3C,eAAe,SAASC,YAAYA,CAACC,MAAM,EAAE;EAC3C,IAAIC,UAAU,GAAGH,MAAM,CAACI,SAAS,CAAC;EAClC,OAAOL,WAAW,CAAC,UAAAM,QAAQ,EAAI;IAC7B,IAAIF,UAAU,CAACG,OAAO,EAAE;MACtBH,UAAU,CAACG,OAAO,CAAC,CAAC;MACpBH,UAAU,CAACG,OAAO,GAAGF,SAAS;IAChC;IACA,IAAIC,QAAQ,IAAI,IAAI,EAAE;MACpBF,UAAU,CAACG,OAAO,GAAGJ,MAAM,CAACG,QAAQ,CAAC;IACvC;EACF,CAAC,EAAE,CAACH,MAAM,CAAC,CAAC;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}