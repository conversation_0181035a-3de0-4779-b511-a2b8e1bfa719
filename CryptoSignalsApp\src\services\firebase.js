import firebase from 'firebase/app';
require('firebase/database');

// Configuração do Firebase
const firebaseConfig = {
  apiKey: "AIzaSyAdVaEfEEzZke9PufjZE0_kse7M7xGG3Zs",
  authDomain: "sinais-premium.firebaseapp.com",
  databaseURL: "https://sinais-premium-default-rtdb.firebaseio.com",
  projectId: "sinais-premium",
  storageBucket: "sinais-premium.appspot.com",
  messagingSenderId: "511238105025",
  appId: "1:511238105025:web:7317c1fe9c13b25d273140",
  measurementId: "G-7BNF9TVTYM"
};

// Verifique se o Firebase já foi inicializado para evitar problemas de re-inicialização
if (!firebase.apps.length) {
  firebase.initializeApp(firebaseConfig);
}

// Referência para o banco de dados do Firebase
const database = firebase.database();

// Funções para leitura e escrita de dados no Firebase

function saveData(path, data) {
  return new Promise((resolve, reject) => {
    database.ref(path).set(data, error => {
      if (error) {
        reject(error);
      } else {
        resolve(true);
      }
    });
  });
}

function readData(path) {
  return new Promise((resolve, reject) => {
    database.ref(path).once('value', snapshot => {
      resolve(snapshot.val());
    }, error => {
      reject(error);
    });
  });
}

function removeData(path) {
  return new Promise((resolve, reject) => {
    database.ref(path).remove(error => {
      if (error) {
        reject(error);
      } else {
        resolve(true);
      }
    });
  });
}

module.exports = {
  saveData,
  readData,
  removeData
};
