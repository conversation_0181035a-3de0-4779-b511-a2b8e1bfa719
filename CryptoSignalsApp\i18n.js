import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

// Arquivos de tradução
import enTranslation from './idiomas/en.json';
import esTranslation from './idiomas/es.json';
import frTranslation from './idiomas/fr.json';
import deTranslation from './idiomas/de.json';
import itTranslation from './idiomas/it.json';
import koTranslation from './idiomas/ko.json';
import ptBRTranslation from './idiomas/pt-BR.json';
import zhTranslation from './idiomas/zh.json';
import ruTranslation from './idiomas/ru.json';

// Mapeamento de traduções
const translations = {
  en: enTranslation,
  es: esTranslation,
  fr: frTranslation,
  de: deTranslation,
  it: itTranslation,
  ko: koTranslation,
  ptBR: ptBRTranslation,
  zh: zhTranslation,
  ru: ruTranslation,
};

// Configuração do i18next
i18n
  .use(initReactI18next)
  .init({
    resources: Object.keys(translations).reduce((acc, key) => {
      acc[key] = { translation: translations[key] };
      return acc;
    }, {}),
    lng: 'en', // Idioma padrão
    fallbackLng: 'en', // Idioma de fallback se a tradução não estiver disponível
    interpolation: {
      escapeValue: false, // Não escapar HTML ou caracteres especiais nas traduções
    },
  });

export default i18n;

/*
Corrigi os caminhos dos arquivos de tradução removendo o ponto extra (de .src para ./src).
Criei um objeto translations para mapear as traduções por idioma e usei um loop .reduce para construir
dinamicamente os recursos.
*/