#!/usr/bin/env python3
"""
Estratégia de Análise de Volume Avançada
"""

import logging
import pandas as pd
import numpy as np
from typing import Tuple, Optional
import sys
import os

# Adicionar o diretório raiz ao path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from utils.technical_indicators import talib

logger = logging.getLogger(__name__)

class VolumeAnalysisStrategy:
    def __init__(self, binance_handler):
        self.binance = binance_handler
        self.name = "Volume Analysis"

        # Configurações da estratégia
        self.volume_spike_threshold = 2.0  # Volume 2x acima da média
        self.price_change_threshold = 0.02  # 2% de mudança de preço
        self.vwap_period = 20
        self.obv_period = 14

        logger.info(f"Estratégia {self.name} inicializada")

    def analyze_symbol(self, symbol: str) -> Tuple[Optional[str], Optional[float], Optional[float], Optional[float]]:
        """
        Analisa um símbolo baseado em padrões de volume
        """
        try:
            # Obter dados de 5 minutos para análise de volume
            data = self.binance.get_historical_klines(symbol, "5m", 1)

            if data.empty or len(data) < 50:
                return None, None, None, None

            # Calcular indicadores de volume
            volume_signal = self._analyze_volume_spike(data)
            vwap_signal = self._analyze_vwap(data)
            obv_signal = self._analyze_obv(data)
            accumulation_signal = self._analyze_accumulation_distribution(data)

            # Combinar sinais
            signals = [volume_signal, vwap_signal, obv_signal, accumulation_signal]
            long_signals = sum(1 for s in signals if s == 'LONG')
            short_signals = sum(1 for s in signals if s == 'SHORT')

            # Precisamos de pelo menos 3 sinais na mesma direção
            if long_signals >= 3:
                signal_type = 'LONG'
            elif short_signals >= 3:
                signal_type = 'SHORT'
            else:
                return None, None, None, None

            # Obter preço atual
            current_price = self.binance.get_current_price(symbol)
            if not current_price or not self._validate_price_range(symbol, current_price):
                return None, None, None, None

            # Calcular stop loss e take profit
            stop_loss, take_profit = self._calculate_levels(current_price, signal_type, data)

            # Validar se os cálculos fazem sentido
            if not self._validate_signal_values(symbol, signal_type, current_price, stop_loss, take_profit):
                return None, None, None, None

            logger.info(f"{symbol} - Sinal Volume Analysis {signal_type} (L:{long_signals}, S:{short_signals})")
            return signal_type, float(current_price), float(stop_loss), float(take_profit)

        except Exception as e:
            logger.error(f"Erro ao analisar {symbol} com Volume Analysis: {e}")
            return None, None, None, None

    def _analyze_volume_spike(self, data: pd.DataFrame) -> str:
        """Analisa picos de volume"""
        try:
            # Calcular média móvel do volume
            volume_ma = talib.SMA(data['volume'].values, timeperiod=20)
            current_volume = data['volume'].iloc[-1]
            avg_volume = volume_ma[-1]

            # Verificar se há pico de volume
            if current_volume > avg_volume * self.volume_spike_threshold:
                # Verificar direção do preço durante o pico
                price_change = (data['close'].iloc[-1] - data['close'].iloc[-5]) / data['close'].iloc[-5]

                if price_change > self.price_change_threshold:
                    return 'LONG'
                elif price_change < -self.price_change_threshold:
                    return 'SHORT'

            return 'NEUTRAL'
        except:
            return 'NEUTRAL'

    def _analyze_vwap(self, data: pd.DataFrame) -> str:
        """Analisa VWAP (Volume Weighted Average Price)"""
        try:
            # Calcular VWAP
            typical_price = (data['high'] + data['low'] + data['close']) / 3
            vwap = (typical_price * data['volume']).cumsum() / data['volume'].cumsum()

            current_price = data['close'].iloc[-1]
            current_vwap = vwap.iloc[-1]

            # Verificar posição em relação ao VWAP
            if current_price > current_vwap * 1.001:  # 0.1% acima do VWAP
                return 'LONG'
            elif current_price < current_vwap * 0.999:  # 0.1% abaixo do VWAP
                return 'SHORT'
            else:
                return 'NEUTRAL'
        except:
            return 'NEUTRAL'

    def _analyze_obv(self, data: pd.DataFrame) -> str:
        """Analisa On-Balance Volume"""
        try:
            obv = talib.OBV(data['close'].values, data['volume'].values)
            obv_ma = talib.SMA(obv, timeperiod=self.obv_period)

            if len(obv) >= 2 and len(obv_ma) >= 2:
                # Verificar tendência do OBV
                obv_trend = obv[-1] - obv[-2]
                obv_ma_trend = obv_ma[-1] - obv_ma[-2]

                if obv_trend > 0 and obv_ma_trend > 0:
                    return 'LONG'
                elif obv_trend < 0 and obv_ma_trend < 0:
                    return 'SHORT'

            return 'NEUTRAL'
        except:
            return 'NEUTRAL'

    def _analyze_accumulation_distribution(self, data: pd.DataFrame) -> str:
        """Analisa Accumulation/Distribution Line"""
        try:
            ad = talib.AD(data['high'].values, data['low'].values,
                         data['close'].values, data['volume'].values)

            if len(ad) >= 5:
                # Verificar tendência da linha A/D
                recent_trend = ad[-1] - ad[-5]

                if recent_trend > 0:
                    return 'LONG'
                elif recent_trend < 0:
                    return 'SHORT'

            return 'NEUTRAL'
        except:
            return 'NEUTRAL'

    def _calculate_levels(self, entry_price: float, signal_type: str, data: pd.DataFrame) -> Tuple[float, float]:
        """Calcula stop loss e take profit baseado na volatilidade"""
        try:
            # Calcular ATR para volatilidade
            atr = talib.ATR(data['high'].values, data['low'].values, data['close'].values, timeperiod=14)
            current_atr = atr[-1]
            atr_percent = current_atr / entry_price

            # Ajustar níveis baseado na volatilidade
            sl_multiplier = 1.5  # 1.5x ATR para stop loss
            tp_multiplier = 2.5  # 2.5x ATR para take profit

            if signal_type == 'LONG':
                stop_loss = entry_price - (current_atr * sl_multiplier)
                take_profit = entry_price + (current_atr * tp_multiplier)
            else:  # SHORT
                stop_loss = entry_price + (current_atr * sl_multiplier)
                take_profit = entry_price - (current_atr * tp_multiplier)

            return round(stop_loss, 6), round(take_profit, 6)
        except:
            # Fallback para percentuais fixos
            if signal_type == 'LONG':
                stop_loss = entry_price * 0.98  # 2% stop loss
                take_profit = entry_price * 1.04  # 4% take profit
            else:
                stop_loss = entry_price * 1.02
                take_profit = entry_price * 0.96

            return round(stop_loss, 6), round(take_profit, 6)

    def _validate_price_range(self, symbol: str, price: float) -> bool:
        """Valida se um preço está dentro de uma faixa realista"""
        if price <= 0:
            return False

        price_ranges = {
            'BTC': (10000, 150000), 'ETH': (500, 10000), 'BNB': (50, 1000),
            'ADA': (0.1, 5), 'SOL': (10, 1000), 'DOT': (1, 50),
            'LINK': (1, 100), 'MATIC': (0.1, 10), 'AVAX': (5, 200)
        }

        for coin, (min_price, max_price) in price_ranges.items():
            if coin in symbol:
                return min_price <= price <= max_price

        return 0.001 <= price <= 100000

    def _validate_signal_values(self, symbol: str, signal_type: str, entry_price: float,
                               stop_loss: float, take_profit: float) -> bool:
        """Valida se os valores do sinal fazem sentido"""
        try:
            if entry_price <= 0 or stop_loss <= 0 or take_profit <= 0:
                return False

            if signal_type == 'LONG':
                if stop_loss >= entry_price or take_profit <= entry_price:
                    return False
                max_tp = entry_price * 1.08  # Máximo 8%
                if take_profit > max_tp:
                    return False
            else:  # SHORT
                if stop_loss <= entry_price or take_profit >= entry_price:
                    return False
                min_tp = entry_price * 0.92  # Mínimo 8%
                if take_profit < min_tp:
                    return False

            return True
        except:
            return False
