{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nvar _excluded = [\"styleInterpolator\", \"interpolationIndex\", \"current\", \"gesture\", \"next\", \"layout\", \"insets\", \"overlay\", \"overlayEnabled\", \"shadowEnabled\", \"gestureEnabled\", \"gestureDirection\", \"pageOverflowEnabled\", \"headerDarkContent\", \"children\", \"containerStyle\", \"contentStyle\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport Color from 'color';\nimport * as React from 'react';\nimport Animated from \"react-native-web/dist/exports/Animated\";\nimport InteractionManager from \"react-native-web/dist/exports/InteractionManager\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport { forModalPresentationIOS } from \"../../TransitionConfigs/CardStyleInterpolators\";\nimport CardAnimationContext from \"../../utils/CardAnimationContext\";\nimport getDistanceForDirection from \"../../utils/getDistanceForDirection\";\nimport getInvertedMultiplier from \"../../utils/getInvertedMultiplier\";\nimport memoize from \"../../utils/memoize\";\nimport { GestureState, PanGestureHandler } from \"../GestureHandler\";\nimport ModalStatusBarManager from \"../ModalStatusBarManager\";\nimport CardSheet from \"./CardSheet\";\nvar GESTURE_VELOCITY_IMPACT = 0.3;\nvar TRUE = 1;\nvar FALSE = 0;\nvar GESTURE_RESPONSE_DISTANCE_HORIZONTAL = 50;\nvar GESTURE_RESPONSE_DISTANCE_VERTICAL = 135;\nvar useNativeDriver = Platform.OS !== 'web';\nvar hasOpacityStyle = function hasOpacityStyle(style) {\n  if (style) {\n    var flattenedStyle = StyleSheet.flatten(style);\n    return flattenedStyle.opacity != null;\n  }\n  return false;\n};\nvar Card = function (_React$Component) {\n  function Card() {\n    var _this;\n    _classCallCheck(this, Card);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _callSuper(this, Card, [].concat(args));\n    _this.isCurrentlyMounted = false;\n    _this.isClosing = new Animated.Value(FALSE);\n    _this.inverted = new Animated.Value(getInvertedMultiplier(_this.props.gestureDirection));\n    _this.layout = {\n      width: new Animated.Value(_this.props.layout.width),\n      height: new Animated.Value(_this.props.layout.height)\n    };\n    _this.isSwiping = new Animated.Value(FALSE);\n    _this.animate = function (_ref2) {\n      var closing = _ref2.closing,\n        velocity = _ref2.velocity;\n      var _this$props = _this.props,\n        gesture = _this$props.gesture,\n        transitionSpec = _this$props.transitionSpec,\n        onOpen = _this$props.onOpen,\n        onClose = _this$props.onClose,\n        onTransition = _this$props.onTransition;\n      var toValue = _this.getAnimateToValue(_objectSpread(_objectSpread({}, _this.props), {}, {\n        closing: closing\n      }));\n      _this.lastToValue = toValue;\n      _this.isClosing.setValue(closing ? TRUE : FALSE);\n      var spec = closing ? transitionSpec.close : transitionSpec.open;\n      var animation = spec.animation === 'spring' ? Animated.spring : Animated.timing;\n      _this.setPointerEventsEnabled(!closing);\n      _this.handleStartInteraction();\n      clearTimeout(_this.pendingGestureCallback);\n      onTransition === null || onTransition === void 0 ? void 0 : onTransition({\n        closing: closing,\n        gesture: velocity !== undefined\n      });\n      animation(gesture, _objectSpread(_objectSpread({}, spec.config), {}, {\n        velocity: velocity,\n        toValue: toValue,\n        useNativeDriver: useNativeDriver,\n        isInteraction: false\n      })).start(function (_ref3) {\n        var finished = _ref3.finished;\n        _this.handleEndInteraction();\n        clearTimeout(_this.pendingGestureCallback);\n        if (finished) {\n          if (closing) {\n            onClose();\n          } else {\n            onOpen();\n          }\n          if (_this.isCurrentlyMounted) {\n            _this.forceUpdate();\n          }\n        }\n      });\n    };\n    _this.getAnimateToValue = function (_ref4) {\n      var closing = _ref4.closing,\n        layout = _ref4.layout,\n        gestureDirection = _ref4.gestureDirection;\n      if (!closing) {\n        return 0;\n      }\n      return getDistanceForDirection(layout, gestureDirection);\n    };\n    _this.setPointerEventsEnabled = function (enabled) {\n      var _this$ref$current;\n      var pointerEvents = enabled ? 'box-none' : 'none';\n      (_this$ref$current = _this.ref.current) === null || _this$ref$current === void 0 ? void 0 : _this$ref$current.setPointerEvents(pointerEvents);\n    };\n    _this.handleStartInteraction = function () {\n      if (_this.interactionHandle === undefined) {\n        _this.interactionHandle = InteractionManager.createInteractionHandle();\n      }\n    };\n    _this.handleEndInteraction = function () {\n      if (_this.interactionHandle !== undefined) {\n        InteractionManager.clearInteractionHandle(_this.interactionHandle);\n        _this.interactionHandle = undefined;\n      }\n    };\n    _this.handleGestureStateChange = function (_ref5) {\n      var nativeEvent = _ref5.nativeEvent;\n      var _this$props2 = _this.props,\n        layout = _this$props2.layout,\n        onClose = _this$props2.onClose,\n        onGestureBegin = _this$props2.onGestureBegin,\n        onGestureCanceled = _this$props2.onGestureCanceled,\n        onGestureEnd = _this$props2.onGestureEnd,\n        gestureDirection = _this$props2.gestureDirection,\n        gestureVelocityImpact = _this$props2.gestureVelocityImpact;\n      switch (nativeEvent.state) {\n        case GestureState.ACTIVE:\n          _this.isSwiping.setValue(TRUE);\n          _this.handleStartInteraction();\n          onGestureBegin === null || onGestureBegin === void 0 ? void 0 : onGestureBegin();\n          break;\n        case GestureState.CANCELLED:\n          {\n            _this.isSwiping.setValue(FALSE);\n            _this.handleEndInteraction();\n            var velocity = gestureDirection === 'vertical' || gestureDirection === 'vertical-inverted' ? nativeEvent.velocityY : nativeEvent.velocityX;\n            _this.animate({\n              closing: _this.props.closing,\n              velocity: velocity\n            });\n            onGestureCanceled === null || onGestureCanceled === void 0 ? void 0 : onGestureCanceled();\n            break;\n          }\n        case GestureState.END:\n          {\n            _this.isSwiping.setValue(FALSE);\n            var distance;\n            var translation;\n            var _velocity;\n            if (gestureDirection === 'vertical' || gestureDirection === 'vertical-inverted') {\n              distance = layout.height;\n              translation = nativeEvent.translationY;\n              _velocity = nativeEvent.velocityY;\n            } else {\n              distance = layout.width;\n              translation = nativeEvent.translationX;\n              _velocity = nativeEvent.velocityX;\n            }\n            var closing = (translation + _velocity * gestureVelocityImpact) * getInvertedMultiplier(gestureDirection) > distance / 2 ? _velocity !== 0 || translation !== 0 : _this.props.closing;\n            _this.animate({\n              closing: closing,\n              velocity: _velocity\n            });\n            if (closing) {\n              _this.pendingGestureCallback = setTimeout(function () {\n                onClose();\n                _this.forceUpdate();\n              }, 32);\n            }\n            onGestureEnd === null || onGestureEnd === void 0 ? void 0 : onGestureEnd();\n            break;\n          }\n      }\n    };\n    _this.getInterpolatedStyle = memoize(function (styleInterpolator, animation) {\n      return styleInterpolator(animation);\n    });\n    _this.getCardAnimation = memoize(function (interpolationIndex, current, next, layout, insetTop, insetRight, insetBottom, insetLeft) {\n      return {\n        index: interpolationIndex,\n        current: {\n          progress: current\n        },\n        next: next && {\n          progress: next\n        },\n        closing: _this.isClosing,\n        swiping: _this.isSwiping,\n        inverted: _this.inverted,\n        layouts: {\n          screen: layout\n        },\n        insets: {\n          top: insetTop,\n          right: insetRight,\n          bottom: insetBottom,\n          left: insetLeft\n        }\n      };\n    });\n    _this.ref = React.createRef();\n    return _this;\n  }\n  _inherits(Card, _React$Component);\n  return _createClass(Card, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this.animate({\n        closing: this.props.closing\n      });\n      this.isCurrentlyMounted = true;\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps) {\n      var _this$props3 = this.props,\n        layout = _this$props3.layout,\n        gestureDirection = _this$props3.gestureDirection,\n        closing = _this$props3.closing;\n      var width = layout.width,\n        height = layout.height;\n      if (width !== prevProps.layout.width) {\n        this.layout.width.setValue(width);\n      }\n      if (height !== prevProps.layout.height) {\n        this.layout.height.setValue(height);\n      }\n      if (gestureDirection !== prevProps.gestureDirection) {\n        this.inverted.setValue(getInvertedMultiplier(gestureDirection));\n      }\n      var toValue = this.getAnimateToValue(this.props);\n      if (this.getAnimateToValue(prevProps) !== toValue || this.lastToValue !== toValue) {\n        this.animate({\n          closing: closing\n        });\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      this.props.gesture.stopAnimation();\n      this.isCurrentlyMounted = false;\n      this.handleEndInteraction();\n    }\n  }, {\n    key: \"gestureActivationCriteria\",\n    value: function gestureActivationCriteria() {\n      var _this$props4 = this.props,\n        layout = _this$props4.layout,\n        gestureDirection = _this$props4.gestureDirection,\n        gestureResponseDistance = _this$props4.gestureResponseDistance;\n      var enableTrackpadTwoFingerGesture = true;\n      var distance = gestureResponseDistance !== undefined ? gestureResponseDistance : gestureDirection === 'vertical' || gestureDirection === 'vertical-inverted' ? GESTURE_RESPONSE_DISTANCE_VERTICAL : GESTURE_RESPONSE_DISTANCE_HORIZONTAL;\n      if (gestureDirection === 'vertical') {\n        return {\n          maxDeltaX: 15,\n          minOffsetY: 5,\n          hitSlop: {\n            bottom: -layout.height + distance\n          },\n          enableTrackpadTwoFingerGesture: enableTrackpadTwoFingerGesture\n        };\n      } else if (gestureDirection === 'vertical-inverted') {\n        return {\n          maxDeltaX: 15,\n          minOffsetY: -5,\n          hitSlop: {\n            top: -layout.height + distance\n          },\n          enableTrackpadTwoFingerGesture: enableTrackpadTwoFingerGesture\n        };\n      } else {\n        var hitSlop = -layout.width + distance;\n        var invertedMultiplier = getInvertedMultiplier(gestureDirection);\n        if (invertedMultiplier === 1) {\n          return {\n            minOffsetX: 5,\n            maxDeltaY: 20,\n            hitSlop: {\n              right: hitSlop\n            },\n            enableTrackpadTwoFingerGesture: enableTrackpadTwoFingerGesture\n          };\n        } else {\n          return {\n            minOffsetX: -5,\n            maxDeltaY: 20,\n            hitSlop: {\n              left: hitSlop\n            },\n            enableTrackpadTwoFingerGesture: enableTrackpadTwoFingerGesture\n          };\n        }\n      }\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props5 = this.props,\n        styleInterpolator = _this$props5.styleInterpolator,\n        interpolationIndex = _this$props5.interpolationIndex,\n        current = _this$props5.current,\n        gesture = _this$props5.gesture,\n        next = _this$props5.next,\n        layout = _this$props5.layout,\n        insets = _this$props5.insets,\n        overlay = _this$props5.overlay,\n        overlayEnabled = _this$props5.overlayEnabled,\n        shadowEnabled = _this$props5.shadowEnabled,\n        gestureEnabled = _this$props5.gestureEnabled,\n        gestureDirection = _this$props5.gestureDirection,\n        pageOverflowEnabled = _this$props5.pageOverflowEnabled,\n        headerDarkContent = _this$props5.headerDarkContent,\n        children = _this$props5.children,\n        customContainerStyle = _this$props5.containerStyle,\n        contentStyle = _this$props5.contentStyle,\n        rest = _objectWithoutProperties(_this$props5, _excluded);\n      var interpolationProps = this.getCardAnimation(interpolationIndex, current, next, layout, insets.top, insets.right, insets.bottom, insets.left);\n      var interpolatedStyle = this.getInterpolatedStyle(styleInterpolator, interpolationProps);\n      var containerStyle = interpolatedStyle.containerStyle,\n        cardStyle = interpolatedStyle.cardStyle,\n        overlayStyle = interpolatedStyle.overlayStyle,\n        shadowStyle = interpolatedStyle.shadowStyle;\n      var handleGestureEvent = gestureEnabled ? Animated.event([{\n        nativeEvent: gestureDirection === 'vertical' || gestureDirection === 'vertical-inverted' ? {\n          translationY: gesture\n        } : {\n          translationX: gesture\n        }\n      }], {\n        useNativeDriver: useNativeDriver\n      }) : undefined;\n      var _StyleSheet$flatten = StyleSheet.flatten(contentStyle || {}),\n        backgroundColor = _StyleSheet$flatten.backgroundColor;\n      var isTransparent = typeof backgroundColor === 'string' ? Color(backgroundColor).alpha() === 0 : false;\n      return React.createElement(CardAnimationContext.Provider, {\n        value: interpolationProps\n      }, Platform.OS === 'ios' && overlayEnabled && next && getIsModalPresentation(styleInterpolator) ? React.createElement(ModalStatusBarManager, {\n        dark: headerDarkContent,\n        layout: layout,\n        insets: insets,\n        style: cardStyle\n      }) : null, React.createElement(Animated.View, {\n        style: {\n          opacity: current\n        },\n        collapsable: false\n      }), React.createElement(View, _extends({\n        pointerEvents: \"box-none\",\n        collapsable: false\n      }, rest), overlayEnabled ? React.createElement(View, {\n        pointerEvents: \"box-none\",\n        style: StyleSheet.absoluteFill\n      }, overlay({\n        style: overlayStyle\n      })) : null, React.createElement(Animated.View, {\n        style: [styles.container, containerStyle, customContainerStyle],\n        pointerEvents: \"box-none\"\n      }, React.createElement(PanGestureHandler, _extends({\n        enabled: layout.width !== 0 && gestureEnabled,\n        onGestureEvent: handleGestureEvent,\n        onHandlerStateChange: this.handleGestureStateChange\n      }, this.gestureActivationCriteria()), React.createElement(Animated.View, {\n        needsOffscreenAlphaCompositing: hasOpacityStyle(cardStyle),\n        style: [styles.container, cardStyle]\n      }, shadowEnabled && shadowStyle && !isTransparent ? React.createElement(Animated.View, {\n        style: [styles.shadow, gestureDirection === 'horizontal' ? [styles.shadowHorizontal, styles.shadowLeft] : gestureDirection === 'horizontal-inverted' ? [styles.shadowHorizontal, styles.shadowRight] : gestureDirection === 'vertical' ? [styles.shadowVertical, styles.shadowTop] : [styles.shadowVertical, styles.shadowBottom], {\n          backgroundColor: backgroundColor\n        }, shadowStyle],\n        pointerEvents: \"none\"\n      }) : null, React.createElement(CardSheet, {\n        ref: this.ref,\n        enabled: pageOverflowEnabled,\n        layout: layout,\n        style: contentStyle\n      }, children))))));\n    }\n  }]);\n}(React.Component);\nCard.defaultProps = {\n  shadowEnabled: false,\n  gestureEnabled: true,\n  gestureVelocityImpact: GESTURE_VELOCITY_IMPACT,\n  overlay: function overlay(_ref) {\n    var style = _ref.style;\n    return style ? React.createElement(Animated.View, {\n      pointerEvents: \"none\",\n      style: [styles.overlay, style]\n    }) : null;\n  }\n};\nexport { Card as default };\nexport var getIsModalPresentation = function getIsModalPresentation(cardStyleInterpolator) {\n  return cardStyleInterpolator === forModalPresentationIOS || cardStyleInterpolator.name === 'forModalPresentationIOS';\n};\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1\n  },\n  overlay: {\n    flex: 1,\n    backgroundColor: '#000'\n  },\n  shadow: {\n    position: 'absolute',\n    shadowRadius: 5,\n    shadowColor: '#000',\n    shadowOpacity: 0.3\n  },\n  shadowHorizontal: {\n    top: 0,\n    bottom: 0,\n    width: 3,\n    shadowOffset: {\n      width: -1,\n      height: 1\n    }\n  },\n  shadowLeft: {\n    left: 0\n  },\n  shadowRight: {\n    right: 0\n  },\n  shadowVertical: {\n    left: 0,\n    right: 0,\n    height: 3,\n    shadowOffset: {\n      width: 1,\n      height: -1\n    }\n  },\n  shadowTop: {\n    top: 0\n  },\n  shadowBottom: {\n    bottom: 0\n  }\n});", "map": {"version": 3, "names": ["Color", "React", "Animated", "InteractionManager", "Platform", "StyleSheet", "View", "forModalPresentationIOS", "CardAnimationContext", "getDistanceForDirection", "getInvertedMultiplier", "memoize", "GestureState", "PanGestureHandler", "ModalStatusBarManager", "CardSheet", "GESTURE_VELOCITY_IMPACT", "TRUE", "FALSE", "GESTURE_RESPONSE_DISTANCE_HORIZONTAL", "GESTURE_RESPONSE_DISTANCE_VERTICAL", "useNativeDriver", "OS", "hasOpacityStyle", "style", "flattenedStyle", "flatten", "opacity", "Card", "_React$Component", "_this", "_classCallCheck", "_len", "arguments", "length", "args", "Array", "_key", "_callSuper", "concat", "isCurrentlyMounted", "isClosing", "Value", "inverted", "props", "gestureDirection", "layout", "width", "height", "isSwiping", "animate", "_ref2", "closing", "velocity", "_this$props", "gesture", "transitionSpec", "onOpen", "onClose", "onTransition", "toValue", "getAnimateToValue", "_objectSpread", "lastToValue", "setValue", "spec", "close", "open", "animation", "spring", "timing", "setPointerEventsEnabled", "handleStartInteraction", "clearTimeout", "pendingGestureCallback", "undefined", "config", "isInteraction", "start", "_ref3", "finished", "handleEndInteraction", "forceUpdate", "_ref4", "enabled", "_this$ref$current", "pointerEvents", "ref", "current", "setPointerEvents", "interactionHandle", "createInteractionHandle", "clearInteractionHandle", "handleGestureStateChange", "_ref5", "nativeEvent", "_this$props2", "onGestureBegin", "onGestureCanceled", "onGestureEnd", "gestureVelocityImpact", "state", "ACTIVE", "CANCELLED", "velocityY", "velocityX", "END", "distance", "translation", "translationY", "translationX", "setTimeout", "getInterpolatedStyle", "styleInterpolator", "getCardAnimation", "interpolationIndex", "next", "insetTop", "insetRight", "insetBottom", "insetLeft", "index", "progress", "swiping", "layouts", "screen", "insets", "top", "right", "bottom", "left", "createRef", "_inherits", "_createClass", "key", "value", "componentDidMount", "componentDidUpdate", "prevProps", "_this$props3", "componentWillUnmount", "stopAnimation", "gestureActivationCriteria", "_this$props4", "gestureResponseDistance", "enableTrackpadTwoFingerGesture", "maxDeltaX", "minOffsetY", "hitSlop", "invertedMultiplier", "minOffsetX", "maxDeltaY", "render", "_this$props5", "overlay", "overlayEnabled", "shadowEnabled", "gestureEnabled", "pageOverflowEnabled", "headerDarkContent", "children", "customContainerStyle", "containerStyle", "contentStyle", "rest", "_objectWithoutProperties", "_excluded", "interpolationProps", "interpolatedStyle", "cardStyle", "overlayStyle", "shadowStyle", "handleGestureEvent", "event", "_StyleSheet$flatten", "backgroundColor", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alpha", "createElement", "Provider", "getIsModalPresentation", "dark", "collapsable", "_extends", "absoluteFill", "styles", "container", "onGestureEvent", "onHandlerStateChange", "needsOffscreenAlphaCompositing", "shadow", "shadowHorizontal", "shadowLeft", "shadowRight", "shadowVertical", "shadowTop", "shadowBottom", "Component", "defaultProps", "_ref", "default", "cardStyleInterpolator", "name", "create", "flex", "position", "shadowRadius", "shadowColor", "shadowOpacity", "shadowOffset"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\@react-navigation\\stack\\src\\views\\Stack\\Card.tsx"], "sourcesContent": ["import Color from 'color';\nimport * as React from 'react';\nimport {\n  Animated,\n  InteractionManager,\n  Platform,\n  StyleProp,\n  StyleSheet,\n  View,\n  ViewProps,\n  ViewStyle,\n} from 'react-native';\nimport type { EdgeInsets } from 'react-native-safe-area-context';\n\nimport { forModalPresentationIOS } from '../../TransitionConfigs/CardStyleInterpolators';\nimport type {\n  GestureDirection,\n  Layout,\n  StackCardInterpolationProps,\n  StackCardStyleInterpolator,\n  TransitionSpec,\n} from '../../types';\nimport CardAnimationContext from '../../utils/CardAnimationContext';\nimport getDistanceForDirection from '../../utils/getDistanceForDirection';\nimport getInvertedMultiplier from '../../utils/getInvertedMultiplier';\nimport memoize from '../../utils/memoize';\nimport {\n  GestureState,\n  PanGestureHandler,\n  PanGestureHandlerGestureEvent,\n} from '../GestureHandler';\nimport ModalStatusBarManager from '../ModalStatusBarManager';\nimport CardSheet, { CardSheetRef } from './CardSheet';\n\ntype Props = ViewProps & {\n  interpolationIndex: number;\n  closing: boolean;\n  next?: Animated.AnimatedInterpolation<number>;\n  current: Animated.AnimatedInterpolation<number>;\n  gesture: Animated.Value;\n  layout: Layout;\n  insets: EdgeInsets;\n  headerDarkContent: boolean | undefined;\n  pageOverflowEnabled: boolean;\n  gestureDirection: GestureDirection;\n  onOpen: () => void;\n  onClose: () => void;\n  onTransition: (props: { closing: boolean; gesture: boolean }) => void;\n  onGestureBegin: () => void;\n  onGestureCanceled: () => void;\n  onGestureEnd: () => void;\n  children: React.ReactNode;\n  overlay: (props: {\n    style: Animated.WithAnimatedValue<StyleProp<ViewStyle>>;\n  }) => React.ReactNode;\n  overlayEnabled: boolean;\n  shadowEnabled: boolean;\n  gestureEnabled: boolean;\n  gestureResponseDistance?: number;\n  gestureVelocityImpact: number;\n  transitionSpec: {\n    open: TransitionSpec;\n    close: TransitionSpec;\n  };\n  styleInterpolator: StackCardStyleInterpolator;\n  containerStyle?: StyleProp<ViewStyle>;\n  contentStyle?: StyleProp<ViewStyle>;\n};\n\nconst GESTURE_VELOCITY_IMPACT = 0.3;\n\nconst TRUE = 1;\nconst FALSE = 0;\n\n/**\n * The distance of touch start from the edge of the screen where the gesture will be recognized\n */\nconst GESTURE_RESPONSE_DISTANCE_HORIZONTAL = 50;\nconst GESTURE_RESPONSE_DISTANCE_VERTICAL = 135;\n\nconst useNativeDriver = Platform.OS !== 'web';\n\nconst hasOpacityStyle = (style: any) => {\n  if (style) {\n    const flattenedStyle = StyleSheet.flatten(style);\n    return flattenedStyle.opacity != null;\n  }\n\n  return false;\n};\n\nexport default class Card extends React.Component<Props> {\n  static defaultProps = {\n    shadowEnabled: false,\n    gestureEnabled: true,\n    gestureVelocityImpact: GESTURE_VELOCITY_IMPACT,\n    overlay: ({\n      style,\n    }: {\n      style: Animated.WithAnimatedValue<StyleProp<ViewStyle>>;\n    }) =>\n      style ? (\n        <Animated.View pointerEvents=\"none\" style={[styles.overlay, style]} />\n      ) : null,\n  };\n\n  componentDidMount() {\n    this.animate({ closing: this.props.closing });\n    this.isCurrentlyMounted = true;\n  }\n\n  componentDidUpdate(prevProps: Props) {\n    const { layout, gestureDirection, closing } = this.props;\n    const { width, height } = layout;\n\n    if (width !== prevProps.layout.width) {\n      this.layout.width.setValue(width);\n    }\n\n    if (height !== prevProps.layout.height) {\n      this.layout.height.setValue(height);\n    }\n\n    if (gestureDirection !== prevProps.gestureDirection) {\n      this.inverted.setValue(getInvertedMultiplier(gestureDirection));\n    }\n\n    const toValue = this.getAnimateToValue(this.props);\n\n    if (\n      this.getAnimateToValue(prevProps) !== toValue ||\n      this.lastToValue !== toValue\n    ) {\n      // We need to trigger the animation when route was closed\n      // Thr route might have been closed by a `POP` action or by a gesture\n      // When route was closed due to a gesture, the animation would've happened already\n      // It's still important to trigger the animation so that `onClose` is called\n      // If `onClose` is not called, cleanup step won't be performed for gestures\n      this.animate({ closing });\n    }\n  }\n\n  componentWillUnmount() {\n    this.props.gesture.stopAnimation();\n    this.isCurrentlyMounted = false;\n    this.handleEndInteraction();\n  }\n\n  private isCurrentlyMounted = false;\n\n  private isClosing = new Animated.Value(FALSE);\n\n  private inverted = new Animated.Value(\n    getInvertedMultiplier(this.props.gestureDirection)\n  );\n\n  private layout = {\n    width: new Animated.Value(this.props.layout.width),\n    height: new Animated.Value(this.props.layout.height),\n  };\n\n  private isSwiping = new Animated.Value(FALSE);\n\n  private interactionHandle: number | undefined;\n\n  private pendingGestureCallback: number | undefined;\n\n  private lastToValue: number | undefined;\n\n  private animate = ({\n    closing,\n    velocity,\n  }: {\n    closing: boolean;\n    velocity?: number;\n  }) => {\n    const { gesture, transitionSpec, onOpen, onClose, onTransition } =\n      this.props;\n\n    const toValue = this.getAnimateToValue({\n      ...this.props,\n      closing,\n    });\n\n    this.lastToValue = toValue;\n\n    this.isClosing.setValue(closing ? TRUE : FALSE);\n\n    const spec = closing ? transitionSpec.close : transitionSpec.open;\n\n    const animation =\n      spec.animation === 'spring' ? Animated.spring : Animated.timing;\n\n    this.setPointerEventsEnabled(!closing);\n    this.handleStartInteraction();\n\n    clearTimeout(this.pendingGestureCallback);\n\n    onTransition?.({ closing, gesture: velocity !== undefined });\n    animation(gesture, {\n      ...spec.config,\n      velocity,\n      toValue,\n      useNativeDriver,\n      isInteraction: false,\n    }).start(({ finished }) => {\n      this.handleEndInteraction();\n\n      clearTimeout(this.pendingGestureCallback);\n\n      if (finished) {\n        if (closing) {\n          onClose();\n        } else {\n          onOpen();\n        }\n\n        if (this.isCurrentlyMounted) {\n          // Make sure to re-open screen if it wasn't removed\n          this.forceUpdate();\n        }\n      }\n    });\n  };\n\n  private getAnimateToValue = ({\n    closing,\n    layout,\n    gestureDirection,\n  }: {\n    closing?: boolean;\n    layout: Layout;\n    gestureDirection: GestureDirection;\n  }) => {\n    if (!closing) {\n      return 0;\n    }\n\n    return getDistanceForDirection(layout, gestureDirection);\n  };\n\n  private setPointerEventsEnabled = (enabled: boolean) => {\n    const pointerEvents = enabled ? 'box-none' : 'none';\n\n    this.ref.current?.setPointerEvents(pointerEvents);\n  };\n\n  private handleStartInteraction = () => {\n    if (this.interactionHandle === undefined) {\n      this.interactionHandle = InteractionManager.createInteractionHandle();\n    }\n  };\n\n  private handleEndInteraction = () => {\n    if (this.interactionHandle !== undefined) {\n      InteractionManager.clearInteractionHandle(this.interactionHandle);\n      this.interactionHandle = undefined;\n    }\n  };\n\n  private handleGestureStateChange = ({\n    nativeEvent,\n  }: PanGestureHandlerGestureEvent) => {\n    const {\n      layout,\n      onClose,\n      onGestureBegin,\n      onGestureCanceled,\n      onGestureEnd,\n      gestureDirection,\n      gestureVelocityImpact,\n    } = this.props;\n\n    switch (nativeEvent.state) {\n      case GestureState.ACTIVE:\n        this.isSwiping.setValue(TRUE);\n        this.handleStartInteraction();\n        onGestureBegin?.();\n        break;\n      case GestureState.CANCELLED: {\n        this.isSwiping.setValue(FALSE);\n        this.handleEndInteraction();\n\n        const velocity =\n          gestureDirection === 'vertical' ||\n          gestureDirection === 'vertical-inverted'\n            ? nativeEvent.velocityY\n            : nativeEvent.velocityX;\n\n        this.animate({ closing: this.props.closing, velocity });\n\n        onGestureCanceled?.();\n        break;\n      }\n      case GestureState.END: {\n        this.isSwiping.setValue(FALSE);\n\n        let distance;\n        let translation;\n        let velocity;\n\n        if (\n          gestureDirection === 'vertical' ||\n          gestureDirection === 'vertical-inverted'\n        ) {\n          distance = layout.height;\n          translation = nativeEvent.translationY;\n          velocity = nativeEvent.velocityY;\n        } else {\n          distance = layout.width;\n          translation = nativeEvent.translationX;\n          velocity = nativeEvent.velocityX;\n        }\n\n        const closing =\n          (translation + velocity * gestureVelocityImpact) *\n            getInvertedMultiplier(gestureDirection) >\n          distance / 2\n            ? velocity !== 0 || translation !== 0\n            : this.props.closing;\n\n        this.animate({ closing, velocity });\n\n        if (closing) {\n          // We call onClose with a delay to make sure that the animation has already started\n          // This will make sure that the state update caused by this doesn't affect start of animation\n          this.pendingGestureCallback = setTimeout(() => {\n            onClose();\n\n            // Trigger an update after we dispatch the action to remove the screen\n            // This will make sure that we check if the screen didn't get removed so we can cancel the animation\n            this.forceUpdate();\n          }, 32) as any as number;\n        }\n\n        onGestureEnd?.();\n        break;\n      }\n    }\n  };\n\n  // Memoize this to avoid extra work on re-render\n  private getInterpolatedStyle = memoize(\n    (\n      styleInterpolator: StackCardStyleInterpolator,\n      animation: StackCardInterpolationProps\n    ) => styleInterpolator(animation)\n  );\n\n  // Keep track of the animation context when deps changes.\n  private getCardAnimation = memoize(\n    (\n      interpolationIndex: number,\n      current: Animated.AnimatedInterpolation<number>,\n      next: Animated.AnimatedInterpolation<number> | undefined,\n      layout: Layout,\n      insetTop: number,\n      insetRight: number,\n      insetBottom: number,\n      insetLeft: number\n    ) => ({\n      index: interpolationIndex,\n      current: { progress: current },\n      next: next && { progress: next },\n      closing: this.isClosing,\n      swiping: this.isSwiping,\n      inverted: this.inverted,\n      layouts: {\n        screen: layout,\n      },\n      insets: {\n        top: insetTop,\n        right: insetRight,\n        bottom: insetBottom,\n        left: insetLeft,\n      },\n    })\n  );\n\n  private gestureActivationCriteria() {\n    const { layout, gestureDirection, gestureResponseDistance } = this.props;\n    const enableTrackpadTwoFingerGesture = true;\n\n    const distance =\n      gestureResponseDistance !== undefined\n        ? gestureResponseDistance\n        : gestureDirection === 'vertical' ||\n          gestureDirection === 'vertical-inverted'\n        ? GESTURE_RESPONSE_DISTANCE_VERTICAL\n        : GESTURE_RESPONSE_DISTANCE_HORIZONTAL;\n\n    if (gestureDirection === 'vertical') {\n      return {\n        maxDeltaX: 15,\n        minOffsetY: 5,\n        hitSlop: { bottom: -layout.height + distance },\n        enableTrackpadTwoFingerGesture,\n      };\n    } else if (gestureDirection === 'vertical-inverted') {\n      return {\n        maxDeltaX: 15,\n        minOffsetY: -5,\n        hitSlop: { top: -layout.height + distance },\n        enableTrackpadTwoFingerGesture,\n      };\n    } else {\n      const hitSlop = -layout.width + distance;\n      const invertedMultiplier = getInvertedMultiplier(gestureDirection);\n\n      if (invertedMultiplier === 1) {\n        return {\n          minOffsetX: 5,\n          maxDeltaY: 20,\n          hitSlop: { right: hitSlop },\n          enableTrackpadTwoFingerGesture,\n        };\n      } else {\n        return {\n          minOffsetX: -5,\n          maxDeltaY: 20,\n          hitSlop: { left: hitSlop },\n          enableTrackpadTwoFingerGesture,\n        };\n      }\n    }\n  }\n\n  private ref = React.createRef<CardSheetRef>();\n\n  render() {\n    const {\n      styleInterpolator,\n      interpolationIndex,\n      current,\n      gesture,\n      next,\n      layout,\n      insets,\n      overlay,\n      overlayEnabled,\n      shadowEnabled,\n      gestureEnabled,\n      gestureDirection,\n      pageOverflowEnabled,\n      headerDarkContent,\n      children,\n      containerStyle: customContainerStyle,\n      contentStyle,\n      ...rest\n    } = this.props;\n\n    const interpolationProps = this.getCardAnimation(\n      interpolationIndex,\n      current,\n      next,\n      layout,\n      insets.top,\n      insets.right,\n      insets.bottom,\n      insets.left\n    );\n\n    const interpolatedStyle = this.getInterpolatedStyle(\n      styleInterpolator,\n      interpolationProps\n    );\n\n    const { containerStyle, cardStyle, overlayStyle, shadowStyle } =\n      interpolatedStyle;\n\n    const handleGestureEvent = gestureEnabled\n      ? Animated.event(\n          [\n            {\n              nativeEvent:\n                gestureDirection === 'vertical' ||\n                gestureDirection === 'vertical-inverted'\n                  ? { translationY: gesture }\n                  : { translationX: gesture },\n            },\n          ],\n          { useNativeDriver }\n        )\n      : undefined;\n\n    const { backgroundColor } = StyleSheet.flatten(contentStyle || {});\n    const isTransparent =\n      typeof backgroundColor === 'string'\n        ? Color(backgroundColor).alpha() === 0\n        : false;\n\n    return (\n      <CardAnimationContext.Provider value={interpolationProps}>\n        {\n          // StatusBar messes with translucent status bar on Android\n          // So we should only enable it on iOS\n          Platform.OS === 'ios' &&\n          overlayEnabled &&\n          next &&\n          getIsModalPresentation(styleInterpolator) ? (\n            <ModalStatusBarManager\n              dark={headerDarkContent}\n              layout={layout}\n              insets={insets}\n              style={cardStyle}\n            />\n          ) : null\n        }\n        <Animated.View\n          style={{\n            // This is a dummy style that doesn't actually change anything visually.\n            // Animated needs the animated value to be used somewhere, otherwise things don't update properly.\n            // If we disable animations and hide header, it could end up making the value unused.\n            // So we have this dummy style that will always be used regardless of what else changed.\n            opacity: current,\n          }}\n          // Make sure that this view isn't removed. If this view is removed, our style with animated value won't apply\n          collapsable={false}\n        />\n        <View\n          pointerEvents=\"box-none\"\n          // Make sure this view is not removed on the new architecture, as it causes focus loss during navigation on Android.\n          // This can happen when the view flattening results in different trees - due to `overflow` style changing in a parent.\n          collapsable={false}\n          {...rest}\n        >\n          {overlayEnabled ? (\n            <View pointerEvents=\"box-none\" style={StyleSheet.absoluteFill}>\n              {overlay({ style: overlayStyle })}\n            </View>\n          ) : null}\n          <Animated.View\n            style={[styles.container, containerStyle, customContainerStyle]}\n            pointerEvents=\"box-none\"\n          >\n            <PanGestureHandler\n              enabled={layout.width !== 0 && gestureEnabled}\n              onGestureEvent={handleGestureEvent}\n              onHandlerStateChange={this.handleGestureStateChange}\n              {...this.gestureActivationCriteria()}\n            >\n              <Animated.View\n                needsOffscreenAlphaCompositing={hasOpacityStyle(cardStyle)}\n                style={[styles.container, cardStyle]}\n              >\n                {shadowEnabled && shadowStyle && !isTransparent ? (\n                  <Animated.View\n                    style={[\n                      styles.shadow,\n                      gestureDirection === 'horizontal'\n                        ? [styles.shadowHorizontal, styles.shadowLeft]\n                        : gestureDirection === 'horizontal-inverted'\n                        ? [styles.shadowHorizontal, styles.shadowRight]\n                        : gestureDirection === 'vertical'\n                        ? [styles.shadowVertical, styles.shadowTop]\n                        : [styles.shadowVertical, styles.shadowBottom],\n                      { backgroundColor },\n                      shadowStyle,\n                    ]}\n                    pointerEvents=\"none\"\n                  />\n                ) : null}\n                <CardSheet\n                  ref={this.ref}\n                  enabled={pageOverflowEnabled}\n                  layout={layout}\n                  style={contentStyle}\n                >\n                  {children}\n                </CardSheet>\n              </Animated.View>\n            </PanGestureHandler>\n          </Animated.View>\n        </View>\n      </CardAnimationContext.Provider>\n    );\n  }\n}\n\nexport const getIsModalPresentation = (\n  cardStyleInterpolator: StackCardStyleInterpolator\n) => {\n  return (\n    cardStyleInterpolator === forModalPresentationIOS ||\n    // Handle custom modal presentation interpolators as well\n    cardStyleInterpolator.name === 'forModalPresentationIOS'\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n  },\n  overlay: {\n    flex: 1,\n    backgroundColor: '#000',\n  },\n  shadow: {\n    position: 'absolute',\n    shadowRadius: 5,\n    shadowColor: '#000',\n    shadowOpacity: 0.3,\n  },\n  shadowHorizontal: {\n    top: 0,\n    bottom: 0,\n    width: 3,\n    shadowOffset: { width: -1, height: 1 },\n  },\n  shadowLeft: {\n    left: 0,\n  },\n  shadowRight: {\n    right: 0,\n  },\n  shadowVertical: {\n    left: 0,\n    right: 0,\n    height: 3,\n    shadowOffset: { width: 1, height: -1 },\n  },\n  shadowTop: {\n    top: 0,\n  },\n  shadowBottom: {\n    bottom: 0,\n  },\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,KAAKC,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAAA,OAAAC,kBAAA;AAAA,OAAAC,QAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAa9B,SAASC,uBAAuB;AAQhC,OAAOC,oBAAoB;AAC3B,OAAOC,uBAAuB;AAC9B,OAAOC,qBAAqB;AAC5B,OAAOC,OAAO;AACd,SACEC,YAAY,EACZC,iBAAiB;AAGnB,OAAOC,qBAAqB;AAC5B,OAAOC,SAAS;AAqChB,IAAMC,uBAAuB,GAAG,GAAG;AAEnC,IAAMC,IAAI,GAAG,CAAC;AACd,IAAMC,KAAK,GAAG,CAAC;AAKf,IAAMC,oCAAoC,GAAG,EAAE;AAC/C,IAAMC,kCAAkC,GAAG,GAAG;AAE9C,IAAMC,eAAe,GAAGjB,QAAQ,CAACkB,EAAE,KAAK,KAAK;AAE7C,IAAMC,eAAe,GAAI,SAAnBA,eAAeA,CAAIC,KAAU,EAAK;EACtC,IAAIA,KAAK,EAAE;IACT,IAAMC,cAAc,GAAGpB,UAAU,CAACqB,OAAO,CAACF,KAAK,CAAC;IAChD,OAAOC,cAAc,CAACE,OAAO,IAAI,IAAI;EACvC;EAEA,OAAO,KAAK;AACd,CAAC;AAAA,IAEoBC,IAAI,aAAAC,gBAAA;EAAA,SAAAD,KAAA;IAAA,IAAAE,KAAA;IAAAC,eAAA,OAAAH,IAAA;IAAA,SAAAI,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAC,IAAA,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;MAAAF,IAAA,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;IAAA;IAAAP,KAAA,GAAAQ,UAAA,OAAAV,IAAA,KAAAW,MAAA,CAAAJ,IAAA;IAAAL,KAAA,CAyDfU,kBAAkB,GAAG,KAAK;IAAAV,KAAA,CAE1BW,SAAS,GAAG,IAAIvC,QAAQ,CAACwC,KAAK,CAACxB,KAAK,CAAC;IAAAY,KAAA,CAErCa,QAAQ,GAAG,IAAIzC,QAAQ,CAACwC,KAAK,CACnChC,qBAAqB,CAACoB,KAAA,CAAKc,KAAK,CAACC,gBAAgB,CAAC,CACnD;IAAAf,KAAA,CAEOgB,MAAM,GAAG;MACfC,KAAK,EAAE,IAAI7C,QAAQ,CAACwC,KAAK,CAACZ,KAAA,CAAKc,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC;MAClDC,MAAM,EAAE,IAAI9C,QAAQ,CAACwC,KAAK,CAACZ,KAAA,CAAKc,KAAK,CAACE,MAAM,CAACE,MAAM;IACrD,CAAC;IAAAlB,KAAA,CAEOmB,SAAS,GAAG,IAAI/C,QAAQ,CAACwC,KAAK,CAACxB,KAAK,CAAC;IAAAY,KAAA,CAQrCoB,OAAO,GAAG,UAAAC,KAAA,EAMZ;MAAA,IALJC,OAAO,GAKRD,KAAA,CALCC,OAAO;QACPC,QAAA,GAIDF,KAAA,CAJCE,QAAA;MAKA,IAAAC,WAAA,GACExB,KAAA,CAAKc,KAAK;QADJW,OAAO,GAAAD,WAAA,CAAPC,OAAO;QAAEC,cAAc,GAAAF,WAAA,CAAdE,cAAc;QAAEC,MAAM,GAAAH,WAAA,CAANG,MAAM;QAAEC,OAAO,GAAAJ,WAAA,CAAPI,OAAO;QAAEC,YAAA,GAAAL,WAAA,CAAAK,YAAA;MAGlD,IAAMC,OAAO,GAAG9B,KAAA,CAAK+B,iBAAiB,CAAAC,aAAA,CAAAA,aAAA,KACjChC,KAAA,CAAKc,KAAK;QACbQ,OAAA,EAAAA;MAAA,EACD,CAAC;MAEFtB,KAAA,CAAKiC,WAAW,GAAGH,OAAO;MAE1B9B,KAAA,CAAKW,SAAS,CAACuB,QAAQ,CAACZ,OAAO,GAAGnC,IAAI,GAAGC,KAAK,CAAC;MAE/C,IAAM+C,IAAI,GAAGb,OAAO,GAAGI,cAAc,CAACU,KAAK,GAAGV,cAAc,CAACW,IAAI;MAEjE,IAAMC,SAAS,GACbH,IAAI,CAACG,SAAS,KAAK,QAAQ,GAAGlE,QAAQ,CAACmE,MAAM,GAAGnE,QAAQ,CAACoE,MAAM;MAEjExC,KAAA,CAAKyC,uBAAuB,CAAC,CAACnB,OAAO,CAAC;MACtCtB,KAAA,CAAK0C,sBAAsB,EAAE;MAE7BC,YAAY,CAAC3C,KAAA,CAAK4C,sBAAsB,CAAC;MAEzCf,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAG;QAAEP,OAAO,EAAPA,OAAO;QAAEG,OAAO,EAAEF,QAAQ,KAAKsB;MAAU,CAAC,CAAC;MAC5DP,SAAS,CAACb,OAAO,EAAAO,aAAA,CAAAA,aAAA,KACZG,IAAI,CAACW,MAAM;QACdvB,QAAQ,EAARA,QAAQ;QACRO,OAAO,EAAPA,OAAO;QACPvC,eAAe,EAAfA,eAAe;QACfwD,aAAa,EAAE;MAAA,EAChB,CAAC,CAACC,KAAK,CAAC,UAAAC,KAAA,EAAkB;QAAA,IAAfC,QAAA,GAAUD,KAAA,CAAVC,QAAA;QACVlD,KAAA,CAAKmD,oBAAoB,EAAE;QAE3BR,YAAY,CAAC3C,KAAA,CAAK4C,sBAAsB,CAAC;QAEzC,IAAIM,QAAQ,EAAE;UACZ,IAAI5B,OAAO,EAAE;YACXM,OAAO,EAAE;UACX,CAAC,MAAM;YACLD,MAAM,EAAE;UACV;UAEA,IAAI3B,KAAA,CAAKU,kBAAkB,EAAE;YAE3BV,KAAA,CAAKoD,WAAW,EAAE;UACpB;QACF;MACF,CAAC,CAAC;IACJ,CAAC;IAAApD,KAAA,CAEO+B,iBAAiB,GAAG,UAAAsB,KAAA,EAQtB;MAAA,IAPJ/B,OAAO,GAOR+B,KAAA,CAPC/B,OAAO;QACPN,MAAM,GAMPqC,KAAA,CANCrC,MAAM;QACND,gBAAA,GAKDsC,KAAA,CALCtC,gBAAA;MAMA,IAAI,CAACO,OAAO,EAAE;QACZ,OAAO,CAAC;MACV;MAEA,OAAO3C,uBAAuB,CAACqC,MAAM,EAAED,gBAAgB,CAAC;IAC1D,CAAC;IAAAf,KAAA,CAEOyC,uBAAuB,GAAI,UAAAa,OAAgB,EAAK;MAAA,IAAAC,iBAAA;MACtD,IAAMC,aAAa,GAAGF,OAAO,GAAG,UAAU,GAAG,MAAM;MAEnD,CAAAC,iBAAA,GAAAvD,KAAA,CAAKyD,GAAG,CAACC,OAAO,cAAAH,iBAAA,uBAAhBA,iBAAA,CAAkBI,gBAAgB,CAACH,aAAa,CAAC;IACnD,CAAC;IAAAxD,KAAA,CAEO0C,sBAAsB,GAAG,YAAM;MACrC,IAAI1C,KAAA,CAAK4D,iBAAiB,KAAKf,SAAS,EAAE;QACxC7C,KAAA,CAAK4D,iBAAiB,GAAGvF,kBAAkB,CAACwF,uBAAuB,EAAE;MACvE;IACF,CAAC;IAAA7D,KAAA,CAEOmD,oBAAoB,GAAG,YAAM;MACnC,IAAInD,KAAA,CAAK4D,iBAAiB,KAAKf,SAAS,EAAE;QACxCxE,kBAAkB,CAACyF,sBAAsB,CAAC9D,KAAA,CAAK4D,iBAAiB,CAAC;QACjE5D,KAAA,CAAK4D,iBAAiB,GAAGf,SAAS;MACpC;IACF,CAAC;IAAA7C,KAAA,CAEO+D,wBAAwB,GAAG,UAAAC,KAAA,EAEE;MAAA,IADnCC,WAAA,GAC8BD,KAAA,CAD9BC,WAAA;MAEA,IAAAC,YAAA,GAQIlE,KAAA,CAAKc,KAAK;QAPZE,MAAM,GAAAkD,YAAA,CAANlD,MAAM;QACNY,OAAO,GAAAsC,YAAA,CAAPtC,OAAO;QACPuC,cAAc,GAAAD,YAAA,CAAdC,cAAc;QACdC,iBAAiB,GAAAF,YAAA,CAAjBE,iBAAiB;QACjBC,YAAY,GAAAH,YAAA,CAAZG,YAAY;QACZtD,gBAAgB,GAAAmD,YAAA,CAAhBnD,gBAAgB;QAChBuD,qBAAA,GAAAJ,YAAA,CAAAI,qBAAA;MAGF,QAAQL,WAAW,CAACM,KAAK;QACvB,KAAKzF,YAAY,CAAC0F,MAAM;UACtBxE,KAAA,CAAKmB,SAAS,CAACe,QAAQ,CAAC/C,IAAI,CAAC;UAC7Ba,KAAA,CAAK0C,sBAAsB,EAAE;UAC7ByB,cAAc,aAAdA,cAAc,uBAAdA,cAAc,EAAI;UAClB;QACF,KAAKrF,YAAY,CAAC2F,SAAS;UAAE;YAC3BzE,KAAA,CAAKmB,SAAS,CAACe,QAAQ,CAAC9C,KAAK,CAAC;YAC9BY,KAAA,CAAKmD,oBAAoB,EAAE;YAE3B,IAAM5B,QAAQ,GACZR,gBAAgB,KAAK,UAAU,IAC/BA,gBAAgB,KAAK,mBAAmB,GACpCkD,WAAW,CAACS,SAAS,GACrBT,WAAW,CAACU,SAAS;YAE3B3E,KAAA,CAAKoB,OAAO,CAAC;cAAEE,OAAO,EAAEtB,KAAA,CAAKc,KAAK,CAACQ,OAAO;cAAEC,QAAA,EAAAA;YAAS,CAAC,CAAC;YAEvD6C,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,EAAI;YACrB;UACF;QACA,KAAKtF,YAAY,CAAC8F,GAAG;UAAE;YACrB5E,KAAA,CAAKmB,SAAS,CAACe,QAAQ,CAAC9C,KAAK,CAAC;YAE9B,IAAIyF,QAAQ;YACZ,IAAIC,WAAW;YACf,IAAIvD,SAAQ;YAEZ,IACER,gBAAgB,KAAK,UAAU,IAC/BA,gBAAgB,KAAK,mBAAmB,EACxC;cACA8D,QAAQ,GAAG7D,MAAM,CAACE,MAAM;cACxB4D,WAAW,GAAGb,WAAW,CAACc,YAAY;cACtCxD,SAAQ,GAAG0C,WAAW,CAACS,SAAS;YAClC,CAAC,MAAM;cACLG,QAAQ,GAAG7D,MAAM,CAACC,KAAK;cACvB6D,WAAW,GAAGb,WAAW,CAACe,YAAY;cACtCzD,SAAQ,GAAG0C,WAAW,CAACU,SAAS;YAClC;YAEA,IAAMrD,OAAO,GACX,CAACwD,WAAW,GAAGvD,SAAQ,GAAG+C,qBAAqB,IAC7C1F,qBAAqB,CAACmC,gBAAgB,CAAC,GACzC8D,QAAQ,GAAG,CAAC,GACRtD,SAAQ,KAAK,CAAC,IAAIuD,WAAW,KAAK,CAAC,GACnC9E,KAAA,CAAKc,KAAK,CAACQ,OAAO;YAExBtB,KAAA,CAAKoB,OAAO,CAAC;cAAEE,OAAO,EAAPA,OAAO;cAAEC,QAAA,EAAAA;YAAS,CAAC,CAAC;YAEnC,IAAID,OAAO,EAAE;cAGXtB,KAAA,CAAK4C,sBAAsB,GAAGqC,UAAU,CAAC,YAAM;gBAC7CrD,OAAO,EAAE;gBAIT5B,KAAA,CAAKoD,WAAW,EAAE;cACpB,CAAC,EAAE,EAAE,CAAkB;YACzB;YAEAiB,YAAY,aAAZA,YAAY,uBAAZA,YAAY,EAAI;YAChB;UACF;MAAC;IAEL,CAAC;IAAArE,KAAA,CAGOkF,oBAAoB,GAAGrG,OAAO,CACpC,UACEsG,iBAA6C,EAC7C7C,SAAsC;MAAA,OACnC6C,iBAAiB,CAAC7C,SAAS,CAAC;IAAA,EAClC;IAAAtC,KAAA,CAGOoF,gBAAgB,GAAGvG,OAAO,CAChC,UACEwG,kBAA0B,EAC1B3B,OAA+C,EAC/C4B,IAAwD,EACxDtE,MAAc,EACduE,QAAgB,EAChBC,UAAkB,EAClBC,WAAmB,EACnBC,SAAiB;MAAA,OACb;QACJC,KAAK,EAAEN,kBAAkB;QACzB3B,OAAO,EAAE;UAAEkC,QAAQ,EAAElC;QAAQ,CAAC;QAC9B4B,IAAI,EAAEA,IAAI,IAAI;UAAEM,QAAQ,EAAEN;QAAK,CAAC;QAChChE,OAAO,EAAEtB,KAAA,CAAKW,SAAS;QACvBkF,OAAO,EAAE7F,KAAA,CAAKmB,SAAS;QACvBN,QAAQ,EAAEb,KAAA,CAAKa,QAAQ;QACvBiF,OAAO,EAAE;UACPC,MAAM,EAAE/E;QACV,CAAC;QACDgF,MAAM,EAAE;UACNC,GAAG,EAAEV,QAAQ;UACbW,KAAK,EAAEV,UAAU;UACjBW,MAAM,EAAEV,WAAW;UACnBW,IAAI,EAAEV;QACR;MACF,CAAC;IAAA,CAAC,CACH;IAAA1F,KAAA,CAkDOyD,GAAG,GAAGtF,KAAK,CAACkI,SAAS,EAAgB;IAAA,OAAArG,KAAA;EAAA;EAAAsG,SAAA,CAAAxG,IAAA,EAAAC,gBAAA;EAAA,OAAAwG,YAAA,CAAAzG,IAAA;IAAA0G,GAAA;IAAAC,KAAA,EAjU7C,SAAAC,iBAAiBA,CAAA,EAAG;MAClB,IAAI,CAACtF,OAAO,CAAC;QAAEE,OAAO,EAAE,IAAI,CAACR,KAAK,CAACQ;MAAQ,CAAC,CAAC;MAC7C,IAAI,CAACZ,kBAAkB,GAAG,IAAI;IAChC;EAAA;IAAA8F,GAAA;IAAAC,KAAA,EAEA,SAAAE,kBAAkBA,CAACC,SAAgB,EAAE;MACnC,IAAAC,YAAA,GAA8C,IAAI,CAAC/F,KAAK;QAAhDE,MAAM,GAAA6F,YAAA,CAAN7F,MAAM;QAAED,gBAAgB,GAAA8F,YAAA,CAAhB9F,gBAAgB;QAAEO,OAAA,GAAAuF,YAAA,CAAAvF,OAAA;MAClC,IAAQL,KAAK,GAAaD,MAAM,CAAxBC,KAAK;QAAEC,MAAA,GAAWF,MAAM,CAAjBE,MAAA;MAEf,IAAID,KAAK,KAAK2F,SAAS,CAAC5F,MAAM,CAACC,KAAK,EAAE;QACpC,IAAI,CAACD,MAAM,CAACC,KAAK,CAACiB,QAAQ,CAACjB,KAAK,CAAC;MACnC;MAEA,IAAIC,MAAM,KAAK0F,SAAS,CAAC5F,MAAM,CAACE,MAAM,EAAE;QACtC,IAAI,CAACF,MAAM,CAACE,MAAM,CAACgB,QAAQ,CAAChB,MAAM,CAAC;MACrC;MAEA,IAAIH,gBAAgB,KAAK6F,SAAS,CAAC7F,gBAAgB,EAAE;QACnD,IAAI,CAACF,QAAQ,CAACqB,QAAQ,CAACtD,qBAAqB,CAACmC,gBAAgB,CAAC,CAAC;MACjE;MAEA,IAAMe,OAAO,GAAG,IAAI,CAACC,iBAAiB,CAAC,IAAI,CAACjB,KAAK,CAAC;MAElD,IACE,IAAI,CAACiB,iBAAiB,CAAC6E,SAAS,CAAC,KAAK9E,OAAO,IAC7C,IAAI,CAACG,WAAW,KAAKH,OAAO,EAC5B;QAMA,IAAI,CAACV,OAAO,CAAC;UAAEE,OAAA,EAAAA;QAAQ,CAAC,CAAC;MAC3B;IACF;EAAA;IAAAkF,GAAA;IAAAC,KAAA,EAEA,SAAAK,oBAAoBA,CAAA,EAAG;MACrB,IAAI,CAAChG,KAAK,CAACW,OAAO,CAACsF,aAAa,EAAE;MAClC,IAAI,CAACrG,kBAAkB,GAAG,KAAK;MAC/B,IAAI,CAACyC,oBAAoB,EAAE;IAC7B;EAAA;IAAAqD,GAAA;IAAAC,KAAA,EAyOQ,SAAAO,yBAAyBA,CAAA,EAAG;MAClC,IAAAC,YAAA,GAA8D,IAAI,CAACnG,KAAK;QAAhEE,MAAM,GAAAiG,YAAA,CAANjG,MAAM;QAAED,gBAAgB,GAAAkG,YAAA,CAAhBlG,gBAAgB;QAAEmG,uBAAA,GAAAD,YAAA,CAAAC,uBAAA;MAClC,IAAMC,8BAA8B,GAAG,IAAI;MAE3C,IAAMtC,QAAQ,GACZqC,uBAAuB,KAAKrE,SAAS,GACjCqE,uBAAuB,GACvBnG,gBAAgB,KAAK,UAAU,IAC/BA,gBAAgB,KAAK,mBAAmB,GACxCzB,kCAAkC,GAClCD,oCAAoC;MAE1C,IAAI0B,gBAAgB,KAAK,UAAU,EAAE;QACnC,OAAO;UACLqG,SAAS,EAAE,EAAE;UACbC,UAAU,EAAE,CAAC;UACbC,OAAO,EAAE;YAAEnB,MAAM,EAAE,CAACnF,MAAM,CAACE,MAAM,GAAG2D;UAAS,CAAC;UAC9CsC,8BAAA,EAAAA;QACF,CAAC;MACH,CAAC,MAAM,IAAIpG,gBAAgB,KAAK,mBAAmB,EAAE;QACnD,OAAO;UACLqG,SAAS,EAAE,EAAE;UACbC,UAAU,EAAE,CAAC,CAAC;UACdC,OAAO,EAAE;YAAErB,GAAG,EAAE,CAACjF,MAAM,CAACE,MAAM,GAAG2D;UAAS,CAAC;UAC3CsC,8BAAA,EAAAA;QACF,CAAC;MACH,CAAC,MAAM;QACL,IAAMG,OAAO,GAAG,CAACtG,MAAM,CAACC,KAAK,GAAG4D,QAAQ;QACxC,IAAM0C,kBAAkB,GAAG3I,qBAAqB,CAACmC,gBAAgB,CAAC;QAElE,IAAIwG,kBAAkB,KAAK,CAAC,EAAE;UAC5B,OAAO;YACLC,UAAU,EAAE,CAAC;YACbC,SAAS,EAAE,EAAE;YACbH,OAAO,EAAE;cAAEpB,KAAK,EAAEoB;YAAQ,CAAC;YAC3BH,8BAAA,EAAAA;UACF,CAAC;QACH,CAAC,MAAM;UACL,OAAO;YACLK,UAAU,EAAE,CAAC,CAAC;YACdC,SAAS,EAAE,EAAE;YACbH,OAAO,EAAE;cAAElB,IAAI,EAAEkB;YAAQ,CAAC;YAC1BH,8BAAA,EAAAA;UACF,CAAC;QACH;MACF;IACF;EAAA;IAAAX,GAAA;IAAAC,KAAA,EAIA,SAAAiB,MAAMA,CAAA,EAAG;MACP,IAAAC,YAAA,GAmBI,IAAI,CAAC7G,KAAK;QAlBZqE,iBAAiB,GAAAwC,YAAA,CAAjBxC,iBAAiB;QACjBE,kBAAkB,GAAAsC,YAAA,CAAlBtC,kBAAkB;QAClB3B,OAAO,GAAAiE,YAAA,CAAPjE,OAAO;QACPjC,OAAO,GAAAkG,YAAA,CAAPlG,OAAO;QACP6D,IAAI,GAAAqC,YAAA,CAAJrC,IAAI;QACJtE,MAAM,GAAA2G,YAAA,CAAN3G,MAAM;QACNgF,MAAM,GAAA2B,YAAA,CAAN3B,MAAM;QACN4B,OAAO,GAAAD,YAAA,CAAPC,OAAO;QACPC,cAAc,GAAAF,YAAA,CAAdE,cAAc;QACdC,aAAa,GAAAH,YAAA,CAAbG,aAAa;QACbC,cAAc,GAAAJ,YAAA,CAAdI,cAAc;QACdhH,gBAAgB,GAAA4G,YAAA,CAAhB5G,gBAAgB;QAChBiH,mBAAmB,GAAAL,YAAA,CAAnBK,mBAAmB;QACnBC,iBAAiB,GAAAN,YAAA,CAAjBM,iBAAiB;QACjBC,QAAQ,GAAAP,YAAA,CAARO,QAAQ;QACQC,oBAAoB,GAAAR,YAAA,CAApCS,cAAc;QACdC,YAAY,GAAAV,YAAA,CAAZU,YAAY;QACTC,IAAA,GAAAC,wBAAA,CAAAZ,YAAA,EAAAa,SAAA;MAGL,IAAMC,kBAAkB,GAAG,IAAI,CAACrD,gBAAgB,CAC9CC,kBAAkB,EAClB3B,OAAO,EACP4B,IAAI,EACJtE,MAAM,EACNgF,MAAM,CAACC,GAAG,EACVD,MAAM,CAACE,KAAK,EACZF,MAAM,CAACG,MAAM,EACbH,MAAM,CAACI,IAAI,CACZ;MAED,IAAMsC,iBAAiB,GAAG,IAAI,CAACxD,oBAAoB,CACjDC,iBAAiB,EACjBsD,kBAAkB,CACnB;MAED,IAAQL,cAAc,GACpBM,iBAAiB,CADXN,cAAc;QAAEO,SAAS,GAC/BD,iBAAiB,CADKC,SAAS;QAAEC,YAAY,GAC7CF,iBAAiB,CADgBE,YAAY;QAAEC,WAAA,GAC/CH,iBAAiB,CAD8BG,WAAA;MAGjD,IAAMC,kBAAkB,GAAGf,cAAc,GACrC3J,QAAQ,CAAC2K,KAAK,CACZ,CACE;QACE9E,WAAW,EACTlD,gBAAgB,KAAK,UAAU,IAC/BA,gBAAgB,KAAK,mBAAmB,GACpC;UAAEgE,YAAY,EAAEtD;QAAQ,CAAC,GACzB;UAAEuD,YAAY,EAAEvD;QAAQ;MAChC,CAAC,CACF,EACD;QAAElC,eAAA,EAAAA;MAAgB,CAAC,CACpB,GACDsD,SAAS;MAEb,IAAAmG,mBAAA,GAA4BzK,UAAU,CAACqB,OAAO,CAACyI,YAAY,IAAI,CAAC,CAAC,CAAC;QAA1DY,eAAA,GAAAD,mBAAA,CAAAC,eAAA;MACR,IAAMC,aAAa,GACjB,OAAOD,eAAe,KAAK,QAAQ,GAC/B/K,KAAK,CAAC+K,eAAe,CAAC,CAACE,KAAK,EAAE,KAAK,CAAC,GACpC,KAAK;MAEX,OACEhL,KAAA,CAAAiL,aAAA,CAAC1K,oBAAoB,CAAC2K,QAAQ;QAAC5C,KAAK,EAAEgC;MAAmB,GAIrDnK,QAAQ,CAACkB,EAAE,KAAK,KAAK,IACrBqI,cAAc,IACdvC,IAAI,IACJgE,sBAAsB,CAACnE,iBAAiB,CAAC,GACvChH,KAAA,CAAAiL,aAAA,CAACpK,qBAAqB;QACpBuK,IAAI,EAAEtB,iBAAkB;QACxBjH,MAAM,EAAEA,MAAO;QACfgF,MAAM,EAAEA,MAAO;QACftG,KAAK,EAAEiJ;MAAU,EACjB,GACA,IAAI,EAEVxK,KAAA,CAAAiL,aAAA,CAAChL,QAAQ,CAACI,IAAI;QACZkB,KAAK,EAAE;UAKLG,OAAO,EAAE6D;QACX;QAEA8F,WAAW,EAAE;MAAM,EACnB,EACFrL,KAAA,CAAAiL,aAAA,CAAC5K,IAAI,EAAAiL,QAAA;QACHjG,aAAa,EAAC;QAGdgG,WAAW,EAAE;MAAM,GACflB,IAAI,GAEPT,cAAc,GACb1J,KAAA,CAAAiL,aAAA,CAAC5K,IAAI;QAACgF,aAAa,EAAC,UAAU;QAAC9D,KAAK,EAAEnB,UAAU,CAACmL;MAAa,GAC3D9B,OAAO,CAAC;QAAElI,KAAK,EAAEkJ;MAAa,CAAC,CAAC,CAC5B,GACL,IAAI,EACRzK,KAAA,CAAAiL,aAAA,CAAChL,QAAQ,CAACI,IAAI;QACZkB,KAAK,EAAE,CAACiK,MAAM,CAACC,SAAS,EAAExB,cAAc,EAAED,oBAAoB,CAAE;QAChE3E,aAAa,EAAC;MAAU,GAExBrF,KAAA,CAAAiL,aAAA,CAACrK,iBAAiB,EAAA0K,QAAA;QAChBnG,OAAO,EAAEtC,MAAM,CAACC,KAAK,KAAK,CAAC,IAAI8G,cAAe;QAC9C8B,cAAc,EAAEf,kBAAmB;QACnCgB,oBAAoB,EAAE,IAAI,CAAC/F;MAAyB,GAChD,IAAI,CAACiD,yBAAyB,EAAE,GAEpC7I,KAAA,CAAAiL,aAAA,CAAChL,QAAQ,CAACI,IAAI;QACZuL,8BAA8B,EAAEtK,eAAe,CAACkJ,SAAS,CAAE;QAC3DjJ,KAAK,EAAE,CAACiK,MAAM,CAACC,SAAS,EAAEjB,SAAS;MAAE,GAEpCb,aAAa,IAAIe,WAAW,IAAI,CAACK,aAAa,GAC7C/K,KAAA,CAAAiL,aAAA,CAAChL,QAAQ,CAACI,IAAI;QACZkB,KAAK,EAAE,CACLiK,MAAM,CAACK,MAAM,EACbjJ,gBAAgB,KAAK,YAAY,GAC7B,CAAC4I,MAAM,CAACM,gBAAgB,EAAEN,MAAM,CAACO,UAAU,CAAC,GAC5CnJ,gBAAgB,KAAK,qBAAqB,GAC1C,CAAC4I,MAAM,CAACM,gBAAgB,EAAEN,MAAM,CAACQ,WAAW,CAAC,GAC7CpJ,gBAAgB,KAAK,UAAU,GAC/B,CAAC4I,MAAM,CAACS,cAAc,EAAET,MAAM,CAACU,SAAS,CAAC,GACzC,CAACV,MAAM,CAACS,cAAc,EAAET,MAAM,CAACW,YAAY,CAAC,EAChD;UAAErB,eAAA,EAAAA;QAAgB,CAAC,EACnBJ,WAAW,CACX;QACFrF,aAAa,EAAC;MAAM,EACpB,GACA,IAAI,EACRrF,KAAA,CAAAiL,aAAA,CAACnK,SAAS;QACRwE,GAAG,EAAE,IAAI,CAACA,GAAI;QACdH,OAAO,EAAE0E,mBAAoB;QAC7BhH,MAAM,EAAEA,MAAO;QACftB,KAAK,EAAE2I;MAAa,GAEnBH,QAAQ,CACC,CACE,CACE,CACN,CACX,CACuB;IAEpC;EAAA;AAAA,EAregC/J,KAAK,CAACoM,SAAS;AAA5BzK,IAAI,CAChB0K,YAAY,GAAG;EACpB1C,aAAa,EAAE,KAAK;EACpBC,cAAc,EAAE,IAAI;EACpBzD,qBAAqB,EAAEpF,uBAAuB;EAC9C0I,OAAO,EAAE,SAATA,OAAOA,CAAE6C,IAAA;IAAA,IACP/K,KAAA,GAGD+K,IAAA,CAHC/K,KAAA;IAGD,OACCA,KAAK,GACHvB,KAAA,CAAAiL,aAAA,CAAChL,QAAQ,CAACI,IAAI;MAACgF,aAAa,EAAC,MAAM;MAAC9D,KAAK,EAAE,CAACiK,MAAM,CAAC/B,OAAO,EAAElI,KAAK;IAAE,EAAG,GACpE,IAAI;EAAA;AACZ,CAAC;AAAA,SAbkBI,IAAI,IAAA4K,OAAA;AAwezB,OAAO,IAAMpB,sBAAsB,GACjC,SADWA,sBAAsBA,CACjCqB,qBAAiD,EAC9C;EACH,OACEA,qBAAqB,KAAKlM,uBAAuB,IAEjDkM,qBAAqB,CAACC,IAAI,KAAK,yBAAyB;AAE5D,CAAC;AAED,IAAMjB,MAAM,GAAGpL,UAAU,CAACsM,MAAM,CAAC;EAC/BjB,SAAS,EAAE;IACTkB,IAAI,EAAE;EACR,CAAC;EACDlD,OAAO,EAAE;IACPkD,IAAI,EAAE,CAAC;IACP7B,eAAe,EAAE;EACnB,CAAC;EACDe,MAAM,EAAE;IACNe,QAAQ,EAAE,UAAU;IACpBC,YAAY,EAAE,CAAC;IACfC,WAAW,EAAE,MAAM;IACnBC,aAAa,EAAE;EACjB,CAAC;EACDjB,gBAAgB,EAAE;IAChBhE,GAAG,EAAE,CAAC;IACNE,MAAM,EAAE,CAAC;IACTlF,KAAK,EAAE,CAAC;IACRkK,YAAY,EAAE;MAAElK,KAAK,EAAE,CAAC,CAAC;MAAEC,MAAM,EAAE;IAAE;EACvC,CAAC;EACDgJ,UAAU,EAAE;IACV9D,IAAI,EAAE;EACR,CAAC;EACD+D,WAAW,EAAE;IACXjE,KAAK,EAAE;EACT,CAAC;EACDkE,cAAc,EAAE;IACdhE,IAAI,EAAE,CAAC;IACPF,KAAK,EAAE,CAAC;IACRhF,MAAM,EAAE,CAAC;IACTiK,YAAY,EAAE;MAAElK,KAAK,EAAE,CAAC;MAAEC,MAAM,EAAE,CAAC;IAAE;EACvC,CAAC;EACDmJ,SAAS,EAAE;IACTpE,GAAG,EAAE;EACP,CAAC;EACDqE,YAAY,EAAE;IACZnE,MAAM,EAAE;EACV;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}