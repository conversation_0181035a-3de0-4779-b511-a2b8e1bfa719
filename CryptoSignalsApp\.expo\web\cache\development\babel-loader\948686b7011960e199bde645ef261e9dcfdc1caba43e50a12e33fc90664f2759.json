{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport { useEffect, useState } from 'react';\nimport { Asset } from \"./Asset\";\nexport function useAssets(moduleIds) {\n  var _useState = useState(),\n    _useState2 = _slicedToArray(_useState, 2),\n    assets = _useState2[0],\n    setAssets = _useState2[1];\n  var _useState3 = useState(),\n    _useState4 = _slicedToArray(_useState3, 2),\n    error = _useState4[0],\n    setError = _useState4[1];\n  useEffect(function () {\n    Asset.loadAsync(moduleIds).then(setAssets).catch(setError);\n  }, []);\n  return [assets, error];\n}", "map": {"version": 3, "names": ["useEffect", "useState", "<PERSON><PERSON>", "useAssets", "moduleIds", "_useState", "_useState2", "_slicedToArray", "assets", "setAssets", "_useState3", "_useState4", "error", "setError", "loadAsync", "then", "catch"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\expo-asset\\src\\AssetHooks.ts"], "sourcesContent": ["import { useEffect, useState } from 'react';\n\nimport { Asset } from './Asset';\n\n// @needsAudit\n/**\n * Downloads and stores one or more assets locally.\n * After the assets are loaded, this hook returns a list of asset instances.\n * If something went wrong when loading the assets, an error is returned.\n *\n * > Note, the assets are not \"reloaded\" when you dynamically change the asset list.\n *\n * @return Returns an array containing:\n * - on the first position, a list of all loaded assets. If they aren't loaded yet, this value is\n *   `undefined`.\n * - on the second position, an error which encountered when loading the assets. If there was no\n *   error, this value is `undefined`.\n *\n * @example\n * ```tsx\n * const [assets, error] = useAssets([require('path/to/asset.jpg'), require('path/to/other.png')]);\n *\n * return assets ? <Image source={assets[0]} /> : null;\n * ```\n */\nexport function useAssets(moduleIds: number | number[]): [Asset[] | undefined, Error | undefined] {\n  const [assets, setAssets] = useState<Asset[]>();\n  const [error, setError] = useState<Error>();\n\n  useEffect(() => {\n    Asset.loadAsync(moduleIds).then(setAssets).catch(setError);\n  }, []);\n\n  return [assets, error];\n}\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAE3C,SAASC,KAAK;AAuBd,OAAM,SAAUC,SAASA,CAACC,SAA4B;EACpD,IAAAC,SAAA,GAA4BJ,QAAQ,EAAW;IAAAK,UAAA,GAAAC,cAAA,CAAAF,SAAA;IAAxCG,MAAM,GAAAF,UAAA;IAAEG,SAAS,GAAAH,UAAA;EACxB,IAAAI,UAAA,GAA0BT,QAAQ,EAAS;IAAAU,UAAA,GAAAJ,cAAA,CAAAG,UAAA;IAApCE,KAAK,GAAAD,UAAA;IAAEE,QAAQ,GAAAF,UAAA;EAEtBX,SAAS,CAAC,YAAK;IACbE,KAAK,CAACY,SAAS,CAACV,SAAS,CAAC,CAACW,IAAI,CAACN,SAAS,CAAC,CAACO,KAAK,CAACH,QAAQ,CAAC;EAC5D,CAAC,EAAE,EAAE,CAAC;EAEN,OAAO,CAACL,MAAM,EAAEI,KAAK,CAAC;AACxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}