{"ast": null, "code": "import StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nexport default StyleSheet.create({\n  container: {\n    flex: 1,\n    padding: 16,\n    backgroundColor: '#1a1a1a'\n  },\n  headerCard: {\n    backgroundColor: '#2a2a2a',\n    marginBottom: 16,\n    borderRadius: 12\n  },\n  header: {\n    padding: 20,\n    alignItems: 'center'\n  },\n  title: {\n    fontSize: 24,\n    fontWeight: 'bold',\n    color: '#fff',\n    marginBottom: 8\n  },\n  planName: {\n    fontSize: 18,\n    color: '#FECB37',\n    marginBottom: 16,\n    fontWeight: '600'\n  },\n  statusContainer: {\n    alignItems: 'center'\n  },\n  statusChip: {\n    backgroundColor: 'transparent',\n    marginBottom: 8\n  },\n  timer: {\n    fontSize: 16,\n    color: '#FF9800',\n    fontWeight: 'bold'\n  },\n  networkCard: {\n    backgroundColor: '#2a2a2a',\n    marginBottom: 16,\n    borderRadius: 12,\n    padding: 16\n  },\n  sectionTitle: {\n    fontSize: 18,\n    fontWeight: 'bold',\n    color: '#fff',\n    marginBottom: 12\n  },\n  networkButtons: {\n    flexDirection: 'row',\n    gap: 12\n  },\n  networkButton: {\n    flex: 1,\n    padding: 12,\n    borderRadius: 8,\n    borderWidth: 1,\n    borderColor: '#444',\n    alignItems: 'center'\n  },\n  networkButtonSelected: {\n    borderColor: '#4CAF50',\n    backgroundColor: '#4CAF5020'\n  },\n  networkButtonText: {\n    color: '#ccc',\n    fontSize: 12,\n    textAlign: 'center'\n  },\n  networkButtonTextSelected: {\n    color: '#4CAF50',\n    fontWeight: 'bold'\n  },\n  qrCard: {\n    backgroundColor: '#2a2a2a',\n    marginBottom: 16,\n    borderRadius: 12,\n    padding: 16\n  },\n  qrContainer: {\n    alignItems: 'center',\n    padding: 20,\n    backgroundColor: '#fff',\n    borderRadius: 12,\n    marginBottom: 12\n  },\n  qrInstructions: {\n    color: '#ccc',\n    fontSize: 14,\n    textAlign: 'center'\n  },\n  detailsCard: {\n    backgroundColor: '#2a2a2a',\n    marginBottom: 16,\n    borderRadius: 12,\n    padding: 16\n  },\n  detailRow: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n    marginBottom: 12,\n    paddingVertical: 4\n  },\n  detailLabel: {\n    color: '#ccc',\n    fontSize: 14,\n    flex: 1\n  },\n  detailValue: {\n    color: '#fff',\n    fontSize: 14,\n    fontWeight: 'bold',\n    flex: 1,\n    textAlign: 'right'\n  },\n  copyButton: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    backgroundColor: '#333',\n    padding: 8,\n    borderRadius: 6,\n    flex: 2,\n    marginLeft: 8\n  },\n  addressText: {\n    color: '#fff',\n    fontSize: 12,\n    flex: 1,\n    fontFamily: 'monospace'\n  },\n  copyIcon: {\n    marginLeft: 8,\n    fontSize: 16\n  },\n  paymentId: {\n    color: '#4CAF50',\n    fontSize: 12,\n    fontFamily: 'monospace',\n    textDecorationLine: 'underline'\n  },\n  progressCard: {\n    backgroundColor: '#2a2a2a',\n    marginBottom: 16,\n    borderRadius: 12,\n    padding: 16\n  },\n  progressBar: {\n    height: 8,\n    borderRadius: 4,\n    marginBottom: 8\n  },\n  progressText: {\n    color: '#ccc',\n    fontSize: 14,\n    textAlign: 'center'\n  },\n  actionButtons: {\n    flexDirection: 'row',\n    gap: 12,\n    marginBottom: 16\n  },\n  cancelButton: {\n    flex: 1,\n    borderColor: '#F44336'\n  },\n  cancelButtonText: {\n    color: '#F44336'\n  },\n  explorerButton: {\n    flex: 1,\n    backgroundColor: '#2196F3'\n  },\n  explorerButtonText: {\n    color: '#fff'\n  },\n  instructionsCard: {\n    backgroundColor: '#2a2a2a',\n    borderRadius: 12,\n    padding: 16\n  },\n  instructionStep: {\n    color: '#ccc',\n    fontSize: 14,\n    marginBottom: 8,\n    lineHeight: 20\n  },\n  warningsContainer: {\n    marginTop: 16,\n    padding: 12,\n    backgroundColor: '#FF980020',\n    borderRadius: 8,\n    borderLeftWidth: 4,\n    borderLeftColor: '#FF9800'\n  },\n  warningsTitle: {\n    color: '#FF9800',\n    fontSize: 14,\n    fontWeight: 'bold',\n    marginBottom: 8\n  },\n  warningText: {\n    color: '#FFB74D',\n    fontSize: 12,\n    marginBottom: 4,\n    lineHeight: 16\n  }\n});", "map": {"version": 3, "names": ["StyleSheet", "create", "container", "flex", "padding", "backgroundColor", "headerCard", "marginBottom", "borderRadius", "header", "alignItems", "title", "fontSize", "fontWeight", "color", "planName", "statusContainer", "statusChip", "timer", "networkCard", "sectionTitle", "networkButtons", "flexDirection", "gap", "networkButton", "borderWidth", "borderColor", "networkButtonSelected", "networkButtonText", "textAlign", "networkButtonTextSelected", "qrCard", "qr<PERSON><PERSON><PERSON>", "qrInstructions", "detailsCard", "detailRow", "justifyContent", "paddingVertical", "detail<PERSON><PERSON><PERSON>", "detailValue", "copyButton", "marginLeft", "addressText", "fontFamily", "copyIcon", "paymentId", "textDecorationLine", "progressCard", "progressBar", "height", "progressText", "actionButtons", "cancelButton", "cancelButtonText", "<PERSON><PERSON><PERSON><PERSON>", "explorerButtonText", "instructionsCard", "instructionStep", "lineHeight", "warningsContainer", "marginTop", "borderLeftWidth", "borderLeftColor", "warningsTitle", "warningText"], "sources": ["C:/Users/<USER>/Desktop/Signals/CryptoSignalsApp/src/components/USDTPayment/styles.js"], "sourcesContent": ["import { StyleSheet } from 'react-native';\n\nexport default StyleSheet.create({\n  container: {\n    flex: 1,\n    padding: 16,\n    backgroundColor: '#1a1a1a',\n  },\n\n  headerCard: {\n    backgroundColor: '#2a2a2a',\n    marginBottom: 16,\n    borderRadius: 12,\n  },\n\n  header: {\n    padding: 20,\n    alignItems: 'center',\n  },\n\n  title: {\n    fontSize: 24,\n    fontWeight: 'bold',\n    color: '#fff',\n    marginBottom: 8,\n  },\n\n  planName: {\n    fontSize: 18,\n    color: '#FECB37',\n    marginBottom: 16,\n    fontWeight: '600',\n  },\n\n  statusContainer: {\n    alignItems: 'center',\n  },\n\n  statusChip: {\n    backgroundColor: 'transparent',\n    marginBottom: 8,\n  },\n\n  timer: {\n    fontSize: 16,\n    color: '#FF9800',\n    fontWeight: 'bold',\n  },\n\n  networkCard: {\n    backgroundColor: '#2a2a2a',\n    marginBottom: 16,\n    borderRadius: 12,\n    padding: 16,\n  },\n\n  sectionTitle: {\n    fontSize: 18,\n    fontWeight: 'bold',\n    color: '#fff',\n    marginBottom: 12,\n  },\n\n  networkButtons: {\n    flexDirection: 'row',\n    gap: 12,\n  },\n\n  networkButton: {\n    flex: 1,\n    padding: 12,\n    borderRadius: 8,\n    borderWidth: 1,\n    borderColor: '#444',\n    alignItems: 'center',\n  },\n\n  networkButtonSelected: {\n    borderColor: '#4CAF50',\n    backgroundColor: '#4CAF5020',\n  },\n\n  networkButtonText: {\n    color: '#ccc',\n    fontSize: 12,\n    textAlign: 'center',\n  },\n\n  networkButtonTextSelected: {\n    color: '#4CAF50',\n    fontWeight: 'bold',\n  },\n\n  qrCard: {\n    backgroundColor: '#2a2a2a',\n    marginBottom: 16,\n    borderRadius: 12,\n    padding: 16,\n  },\n\n  qrContainer: {\n    alignItems: 'center',\n    padding: 20,\n    backgroundColor: '#fff',\n    borderRadius: 12,\n    marginBottom: 12,\n  },\n\n  qrInstructions: {\n    color: '#ccc',\n    fontSize: 14,\n    textAlign: 'center',\n  },\n\n  detailsCard: {\n    backgroundColor: '#2a2a2a',\n    marginBottom: 16,\n    borderRadius: 12,\n    padding: 16,\n  },\n\n  detailRow: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n    marginBottom: 12,\n    paddingVertical: 4,\n  },\n\n  detailLabel: {\n    color: '#ccc',\n    fontSize: 14,\n    flex: 1,\n  },\n\n  detailValue: {\n    color: '#fff',\n    fontSize: 14,\n    fontWeight: 'bold',\n    flex: 1,\n    textAlign: 'right',\n  },\n\n  copyButton: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    backgroundColor: '#333',\n    padding: 8,\n    borderRadius: 6,\n    flex: 2,\n    marginLeft: 8,\n  },\n\n  addressText: {\n    color: '#fff',\n    fontSize: 12,\n    flex: 1,\n    fontFamily: 'monospace',\n  },\n\n  copyIcon: {\n    marginLeft: 8,\n    fontSize: 16,\n  },\n\n  paymentId: {\n    color: '#4CAF50',\n    fontSize: 12,\n    fontFamily: 'monospace',\n    textDecorationLine: 'underline',\n  },\n\n  progressCard: {\n    backgroundColor: '#2a2a2a',\n    marginBottom: 16,\n    borderRadius: 12,\n    padding: 16,\n  },\n\n  progressBar: {\n    height: 8,\n    borderRadius: 4,\n    marginBottom: 8,\n  },\n\n  progressText: {\n    color: '#ccc',\n    fontSize: 14,\n    textAlign: 'center',\n  },\n\n  actionButtons: {\n    flexDirection: 'row',\n    gap: 12,\n    marginBottom: 16,\n  },\n\n  cancelButton: {\n    flex: 1,\n    borderColor: '#F44336',\n  },\n\n  cancelButtonText: {\n    color: '#F44336',\n  },\n\n  explorerButton: {\n    flex: 1,\n    backgroundColor: '#2196F3',\n  },\n\n  explorerButtonText: {\n    color: '#fff',\n  },\n\n  instructionsCard: {\n    backgroundColor: '#2a2a2a',\n    borderRadius: 12,\n    padding: 16,\n  },\n\n  instructionStep: {\n    color: '#ccc',\n    fontSize: 14,\n    marginBottom: 8,\n    lineHeight: 20,\n  },\n\n  warningsContainer: {\n    marginTop: 16,\n    padding: 12,\n    backgroundColor: '#FF980020',\n    borderRadius: 8,\n    borderLeftWidth: 4,\n    borderLeftColor: '#FF9800',\n  },\n\n  warningsTitle: {\n    color: '#FF9800',\n    fontSize: 14,\n    fontWeight: 'bold',\n    marginBottom: 8,\n  },\n\n  warningText: {\n    color: '#FFB74D',\n    fontSize: 12,\n    marginBottom: 4,\n    lineHeight: 16,\n  },\n});\n"], "mappings": ";AAEA,eAAeA,UAAU,CAACC,MAAM,CAAC;EAC/BC,SAAS,EAAE;IACTC,IAAI,EAAE,CAAC;IACPC,OAAO,EAAE,EAAE;IACXC,eAAe,EAAE;EACnB,CAAC;EAEDC,UAAU,EAAE;IACVD,eAAe,EAAE,SAAS;IAC1BE,YAAY,EAAE,EAAE;IAChBC,YAAY,EAAE;EAChB,CAAC;EAEDC,MAAM,EAAE;IACNL,OAAO,EAAE,EAAE;IACXM,UAAU,EAAE;EACd,CAAC;EAEDC,KAAK,EAAE;IACLC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBC,KAAK,EAAE,MAAM;IACbP,YAAY,EAAE;EAChB,CAAC;EAEDQ,QAAQ,EAAE;IACRH,QAAQ,EAAE,EAAE;IACZE,KAAK,EAAE,SAAS;IAChBP,YAAY,EAAE,EAAE;IAChBM,UAAU,EAAE;EACd,CAAC;EAEDG,eAAe,EAAE;IACfN,UAAU,EAAE;EACd,CAAC;EAEDO,UAAU,EAAE;IACVZ,eAAe,EAAE,aAAa;IAC9BE,YAAY,EAAE;EAChB,CAAC;EAEDW,KAAK,EAAE;IACLN,QAAQ,EAAE,EAAE;IACZE,KAAK,EAAE,SAAS;IAChBD,UAAU,EAAE;EACd,CAAC;EAEDM,WAAW,EAAE;IACXd,eAAe,EAAE,SAAS;IAC1BE,YAAY,EAAE,EAAE;IAChBC,YAAY,EAAE,EAAE;IAChBJ,OAAO,EAAE;EACX,CAAC;EAEDgB,YAAY,EAAE;IACZR,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBC,KAAK,EAAE,MAAM;IACbP,YAAY,EAAE;EAChB,CAAC;EAEDc,cAAc,EAAE;IACdC,aAAa,EAAE,KAAK;IACpBC,GAAG,EAAE;EACP,CAAC;EAEDC,aAAa,EAAE;IACbrB,IAAI,EAAE,CAAC;IACPC,OAAO,EAAE,EAAE;IACXI,YAAY,EAAE,CAAC;IACfiB,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE,MAAM;IACnBhB,UAAU,EAAE;EACd,CAAC;EAEDiB,qBAAqB,EAAE;IACrBD,WAAW,EAAE,SAAS;IACtBrB,eAAe,EAAE;EACnB,CAAC;EAEDuB,iBAAiB,EAAE;IACjBd,KAAK,EAAE,MAAM;IACbF,QAAQ,EAAE,EAAE;IACZiB,SAAS,EAAE;EACb,CAAC;EAEDC,yBAAyB,EAAE;IACzBhB,KAAK,EAAE,SAAS;IAChBD,UAAU,EAAE;EACd,CAAC;EAEDkB,MAAM,EAAE;IACN1B,eAAe,EAAE,SAAS;IAC1BE,YAAY,EAAE,EAAE;IAChBC,YAAY,EAAE,EAAE;IAChBJ,OAAO,EAAE;EACX,CAAC;EAED4B,WAAW,EAAE;IACXtB,UAAU,EAAE,QAAQ;IACpBN,OAAO,EAAE,EAAE;IACXC,eAAe,EAAE,MAAM;IACvBG,YAAY,EAAE,EAAE;IAChBD,YAAY,EAAE;EAChB,CAAC;EAED0B,cAAc,EAAE;IACdnB,KAAK,EAAE,MAAM;IACbF,QAAQ,EAAE,EAAE;IACZiB,SAAS,EAAE;EACb,CAAC;EAEDK,WAAW,EAAE;IACX7B,eAAe,EAAE,SAAS;IAC1BE,YAAY,EAAE,EAAE;IAChBC,YAAY,EAAE,EAAE;IAChBJ,OAAO,EAAE;EACX,CAAC;EAED+B,SAAS,EAAE;IACTb,aAAa,EAAE,KAAK;IACpBc,cAAc,EAAE,eAAe;IAC/B1B,UAAU,EAAE,QAAQ;IACpBH,YAAY,EAAE,EAAE;IAChB8B,eAAe,EAAE;EACnB,CAAC;EAEDC,WAAW,EAAE;IACXxB,KAAK,EAAE,MAAM;IACbF,QAAQ,EAAE,EAAE;IACZT,IAAI,EAAE;EACR,CAAC;EAEDoC,WAAW,EAAE;IACXzB,KAAK,EAAE,MAAM;IACbF,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBV,IAAI,EAAE,CAAC;IACP0B,SAAS,EAAE;EACb,CAAC;EAEDW,UAAU,EAAE;IACVlB,aAAa,EAAE,KAAK;IACpBZ,UAAU,EAAE,QAAQ;IACpBL,eAAe,EAAE,MAAM;IACvBD,OAAO,EAAE,CAAC;IACVI,YAAY,EAAE,CAAC;IACfL,IAAI,EAAE,CAAC;IACPsC,UAAU,EAAE;EACd,CAAC;EAEDC,WAAW,EAAE;IACX5B,KAAK,EAAE,MAAM;IACbF,QAAQ,EAAE,EAAE;IACZT,IAAI,EAAE,CAAC;IACPwC,UAAU,EAAE;EACd,CAAC;EAEDC,QAAQ,EAAE;IACRH,UAAU,EAAE,CAAC;IACb7B,QAAQ,EAAE;EACZ,CAAC;EAEDiC,SAAS,EAAE;IACT/B,KAAK,EAAE,SAAS;IAChBF,QAAQ,EAAE,EAAE;IACZ+B,UAAU,EAAE,WAAW;IACvBG,kBAAkB,EAAE;EACtB,CAAC;EAEDC,YAAY,EAAE;IACZ1C,eAAe,EAAE,SAAS;IAC1BE,YAAY,EAAE,EAAE;IAChBC,YAAY,EAAE,EAAE;IAChBJ,OAAO,EAAE;EACX,CAAC;EAED4C,WAAW,EAAE;IACXC,MAAM,EAAE,CAAC;IACTzC,YAAY,EAAE,CAAC;IACfD,YAAY,EAAE;EAChB,CAAC;EAED2C,YAAY,EAAE;IACZpC,KAAK,EAAE,MAAM;IACbF,QAAQ,EAAE,EAAE;IACZiB,SAAS,EAAE;EACb,CAAC;EAEDsB,aAAa,EAAE;IACb7B,aAAa,EAAE,KAAK;IACpBC,GAAG,EAAE,EAAE;IACPhB,YAAY,EAAE;EAChB,CAAC;EAED6C,YAAY,EAAE;IACZjD,IAAI,EAAE,CAAC;IACPuB,WAAW,EAAE;EACf,CAAC;EAED2B,gBAAgB,EAAE;IAChBvC,KAAK,EAAE;EACT,CAAC;EAEDwC,cAAc,EAAE;IACdnD,IAAI,EAAE,CAAC;IACPE,eAAe,EAAE;EACnB,CAAC;EAEDkD,kBAAkB,EAAE;IAClBzC,KAAK,EAAE;EACT,CAAC;EAED0C,gBAAgB,EAAE;IAChBnD,eAAe,EAAE,SAAS;IAC1BG,YAAY,EAAE,EAAE;IAChBJ,OAAO,EAAE;EACX,CAAC;EAEDqD,eAAe,EAAE;IACf3C,KAAK,EAAE,MAAM;IACbF,QAAQ,EAAE,EAAE;IACZL,YAAY,EAAE,CAAC;IACfmD,UAAU,EAAE;EACd,CAAC;EAEDC,iBAAiB,EAAE;IACjBC,SAAS,EAAE,EAAE;IACbxD,OAAO,EAAE,EAAE;IACXC,eAAe,EAAE,WAAW;IAC5BG,YAAY,EAAE,CAAC;IACfqD,eAAe,EAAE,CAAC;IAClBC,eAAe,EAAE;EACnB,CAAC;EAEDC,aAAa,EAAE;IACbjD,KAAK,EAAE,SAAS;IAChBF,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBN,YAAY,EAAE;EAChB,CAAC;EAEDyD,WAAW,EAAE;IACXlD,KAAK,EAAE,SAAS;IAChBF,QAAQ,EAAE,EAAE;IACZL,YAAY,EAAE,CAAC;IACfmD,UAAU,EAAE;EACd;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}