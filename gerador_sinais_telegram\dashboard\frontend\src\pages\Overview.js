import React, { useState, useEffect } from 'react';
import {
  TrendingUp,
  TrendingDown,
  Activity,
  Target,
  RefreshCw
} from 'lucide-react';
import axios from 'axios';

const Overview = () => {
  const [metrics, setMetrics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchMetrics = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/overview');
      setMetrics(response.data);
      setError(null);
    } catch (err) {
      setError('Erro ao carregar métricas');
      console.error('Erro:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMetrics();
    // Atualizar a cada 30 segundos
    const interval = setInterval(fetchMetrics, 30000);
    return () => clearInterval(interval);
  }, []);

  const MetricCard = ({ title, value, icon: Icon, color, subtitle }) => (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center">
        <div className={`p-3 rounded-full ${color}`}>
          <Icon className="h-6 w-6 text-white" />
        </div>
        <div className="ml-4">
          <h3 className="text-sm font-medium text-gray-500">{title}</h3>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
          {subtitle && (
            <p className="text-sm text-gray-600">{subtitle}</p>
          )}
        </div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin text-blue-500" />
        <span className="ml-2 text-gray-600">Carregando métricas...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <TrendingDown className="h-5 w-5 text-red-400" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Erro</h3>
            <div className="mt-2 text-sm text-red-700">
              <p>{error}</p>
            </div>
            <div className="mt-4">
              <button
                onClick={fetchMetrics}
                className="bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200"
              >
                Tentar novamente
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-gray-900">Overview</h1>
        <button
          onClick={fetchMetrics}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          Atualizar
        </button>
      </div>

      {/* Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Sinais Ativos"
          value={metrics?.active_signals || 0}
          icon={Activity}
          color="bg-blue-500"
          subtitle="Últimas 24h"
        />

        <MetricCard
          title="Total de Sinais"
          value={metrics?.total_signals || 0}
          icon={Target}
          color="bg-green-500"
          subtitle="Histórico completo"
        />

        <MetricCard
          title="Win Rate"
          value={`${metrics?.win_rate || 0}%`}
          icon={TrendingUp}
          color="bg-purple-500"
          subtitle="Últimos 30 dias"
        />

        <MetricCard
          title="Profit Médio"
          value={`${metrics?.avg_profit || 0}%`}
          icon={TrendingUp}
          color="bg-yellow-500"
          subtitle="Últimos 30 dias"
        />
      </div>

      {/* Status Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Sistema Status */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Status do Sistema</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Sistema Principal</span>
              <span className="flex items-center text-sm text-green-600">
                <div className="h-2 w-2 bg-green-400 rounded-full mr-2"></div>
                Online
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Banco de Dados</span>
              <span className="flex items-center text-sm text-green-600">
                <div className="h-2 w-2 bg-green-400 rounded-full mr-2"></div>
                Conectado
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Telegram Bot</span>
              <span className="flex items-center text-sm text-green-600">
                <div className="h-2 w-2 bg-green-400 rounded-full mr-2"></div>
                Ativo
              </span>
            </div>
          </div>
        </div>

        {/* Informações Gerais */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Informações</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Versão</span>
              <span className="text-sm text-gray-900 font-medium">2.0.0</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Última Atualização</span>
              <span className="text-sm text-gray-900">
                {metrics?.last_updated ?
                  new Date(metrics.last_updated).toLocaleString('pt-BR') :
                  'Agora'
                }
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Estratégias Ativas</span>
              <span className="text-sm text-green-600 font-medium">8</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Tokens Monitorados</span>
              <span className="text-sm text-blue-600 font-medium">109</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Operação</span>
              <span className="text-sm text-purple-600 font-medium">24/7</span>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Ações Rápidas</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <Activity className="h-6 w-6 text-blue-500 mb-2" />
            <div className="text-sm font-medium text-gray-900">Ver Sinais Ativos</div>
            <div className="text-xs text-gray-500">Monitorar sinais em tempo real</div>
          </button>

          <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <TrendingUp className="h-6 w-6 text-green-500 mb-2" />
            <div className="text-sm font-medium text-gray-900">Analytics</div>
            <div className="text-xs text-gray-500">Ver relatórios detalhados</div>
          </button>

          <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <Target className="h-6 w-6 text-purple-500 mb-2" />
            <div className="text-sm font-medium text-gray-900">Configurações</div>
            <div className="text-xs text-gray-500">Ajustar parâmetros</div>
          </button>
        </div>
      </div>
    </div>
  );
};

export default Overview;
