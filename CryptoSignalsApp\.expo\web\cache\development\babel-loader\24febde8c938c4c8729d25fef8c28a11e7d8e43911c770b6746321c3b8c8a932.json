{"ast": null, "code": "import NativeEventEmitter from \"../../vendor/react-native/EventEmitter/NativeEventEmitter\";\nexport default NativeEventEmitter;", "map": {"version": 3, "names": ["NativeEventEmitter"], "sources": ["C:/Users/<USER>/Desktop/Signals/CryptoSignalsApp/node_modules/react-native-web/dist/exports/NativeEventEmitter/index.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport NativeEventEmitter from '../../vendor/react-native/EventEmitter/NativeEventEmitter';\nexport default NativeEventEmitter;"], "mappings": "AASA,OAAOA,kBAAkB;AACzB,eAAeA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}