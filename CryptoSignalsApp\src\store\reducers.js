import { setSecureStoreItem, removeSecureStoreItem } from '../services/secureStore';

const globalReducer = (state, action) => {
  if (action.type === 'SET_SUBSCRIPTION') {
    state.subscription = action.payload;
    setSecureStoreItem('subscription', action.payload);

    return { ...state };
  }

  if (action.type === 'REMOVE_SUBSCRIPTION') {
    const _payload = {
      subscriptionStatus: false,
      lastSubscriptionStatusDate: null,
      customerId: null,
      subscriptionPeriodEnd: null,
      accessToken: null
    }
    state.subscription = _payload
    removeSecureStoreItem('subscription');

    return { ...state };
  }

  if (action.type === 'SET_SIGNAL_FROM_WEBSOCKET') {
    state.signalFromWebsocket[action.payload.channel.externalId] = action.payload;
    state.channelOrderedByNewSignal = action.payload;
    return { ...state };
  }

  return state;
}

export default globalReducer;
