{"ast": null, "code": "import { CurrentRenderContext } from '@react-navigation/core';\nimport * as React from 'react';\nimport Server<PERSON>ontext from \"./ServerContext\";\nexport default React.forwardRef(function ServerContainer(_ref, ref) {\n  var children = _ref.children,\n    location = _ref.location;\n  React.useEffect(function () {\n    console.error(\"'ServerContainer' should only be used on the server with 'react-dom/server' for SSR.\");\n  }, []);\n  var current = {};\n  if (ref) {\n    var value = {\n      getCurrentOptions: function getCurrentOptions() {\n        return current.options;\n      }\n    };\n    if (typeof ref === 'function') {\n      ref(value);\n    } else {\n      ref.current = value;\n    }\n  }\n  return React.createElement(ServerContext.Provider, {\n    value: {\n      location: location\n    }\n  }, React.createElement(CurrentRenderContext.Provider, {\n    value: current\n  }, children));\n});", "map": {"version": 3, "names": ["CurrentRenderContext", "React", "ServerContext", "forwardRef", "ServerContainer", "_ref", "ref", "children", "location", "useEffect", "console", "error", "current", "value", "getCurrentOptions", "options", "createElement", "Provider"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\@react-navigation\\native\\src\\ServerContainer.tsx"], "sourcesContent": ["import { CurrentRenderContext } from '@react-navigation/core';\nimport * as React from 'react';\n\nimport ServerContext, { ServerContextType } from './ServerContext';\nimport type { ServerContainerRef } from './types';\n\ntype Props = ServerContextType & {\n  children: React.ReactNode;\n};\n\n/**\n * Container component for server rendering.\n *\n * @param props.location Location object to base the initial URL for SSR.\n * @param props.children Child elements to render the content.\n * @param props.ref Ref object which contains helper methods.\n */\nexport default React.forwardRef(function ServerContainer(\n  { children, location }: Props,\n  ref: React.Ref<ServerContainerRef>\n) {\n  React.useEffect(() => {\n    console.error(\n      \"'ServerContainer' should only be used on the server with 'react-dom/server' for SSR.\"\n    );\n  }, []);\n\n  const current: { options?: object } = {};\n\n  if (ref) {\n    const value = {\n      getCurrentOptions() {\n        return current.options;\n      },\n    };\n\n    // We write to the `ref` during render instead of `React.useImperativeHandle`\n    // This is because `useImperative<PERSON><PERSON><PERSON>` will update the ref after 'commit',\n    // and there's no 'commit' phase during SSR.\n    // Mutating ref during render is unsafe in concurrent mode, but we don't care about it for SSR.\n    if (typeof ref === 'function') {\n      ref(value);\n    } else {\n      // @ts-expect-error: the TS types are incorrect and say that ref.current is readonly\n      ref.current = value;\n    }\n  }\n\n  return (\n    <ServerContext.Provider value={{ location }}>\n      <CurrentRenderContext.Provider value={current}>\n        {children}\n      </CurrentRenderContext.Provider>\n    </ServerContext.Provider>\n  );\n});\n"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,wBAAwB;AAC7D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAE9B,OAAOC,aAAa;AAcpB,eAAeD,KAAK,CAACE,UAAU,CAAC,SAASC,eAAeA,CAAAC,IAAA,EAEtDC,GAAkC,EAClC;EAAA,IAFEC,QAAQ,GAAmBF,IAAA,CAA3BE,QAAQ;IAAEC,QAAA,GAAiBH,IAAA,CAAjBG,QAAA;EAGZP,KAAK,CAACQ,SAAS,CAAC,YAAM;IACpBC,OAAO,CAACC,KAAK,CACX,sFAAsF,CACvF;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,IAAMC,OAA6B,GAAG,CAAC,CAAC;EAExC,IAAIN,GAAG,EAAE;IACP,IAAMO,KAAK,GAAG;MACZC,iBAAiB,WAAjBA,iBAAiBA,CAAA,EAAG;QAClB,OAAOF,OAAO,CAACG,OAAO;MACxB;IACF,CAAC;IAMD,IAAI,OAAOT,GAAG,KAAK,UAAU,EAAE;MAC7BA,GAAG,CAACO,KAAK,CAAC;IACZ,CAAC,MAAM;MAELP,GAAG,CAACM,OAAO,GAAGC,KAAK;IACrB;EACF;EAEA,OACEZ,KAAA,CAAAe,aAAA,CAACd,aAAa,CAACe,QAAQ;IAACJ,KAAK,EAAE;MAAEL,QAAA,EAAAA;IAAS;EAAE,GAC1CP,KAAA,CAAAe,aAAA,CAAChB,oBAAoB,CAACiB,QAAQ;IAACJ,KAAK,EAAED;EAAQ,GAC3CL,QAAQ,CACqB,CACT;AAE7B,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}