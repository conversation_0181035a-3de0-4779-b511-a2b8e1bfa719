{"ast": null, "code": "import React from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport overlay from \"../../styles/overlay\";\nimport { black, white } from \"../../styles/themes/v2/colors\";\nvar borderStyleProperties = ['borderRadius', 'borderTopLeftRadius', 'borderTopRightRadius', 'borderBottomRightRadius', 'borderBottomLeftRadius'];\nexport var getAppbarBackgroundColor = function getAppbarBackgroundColor(theme, elevation, customBackground, elevated) {\n  var isV3 = theme.isV3,\n    isDarkTheme = theme.dark,\n    mode = theme.mode,\n    colors = theme.colors;\n  var isAdaptiveMode = mode === 'adaptive';\n  if (customBackground) {\n    return customBackground;\n  }\n  if (!isV3) {\n    if (isDarkTheme && isAdaptiveMode) {\n      return overlay(elevation, colors === null || colors === void 0 ? void 0 : colors.surface);\n    }\n    return colors.primary;\n  }\n  if (elevated) {\n    return theme.colors.elevation.level2;\n  }\n  return colors.surface;\n};\nexport var getAppbarColor = function getAppbarColor(_ref) {\n  var color = _ref.color,\n    isDark = _ref.isDark,\n    isV3 = _ref.isV3;\n  if (typeof color !== 'undefined') {\n    return color;\n  }\n  if (isDark) {\n    return white;\n  }\n  if (isV3) {\n    return undefined;\n  }\n  return black;\n};\nexport var getAppbarBorders = function getAppbarBorders(style) {\n  var borders = {};\n  for (var property of borderStyleProperties) {\n    var value = style[property];\n    if (value) {\n      borders[property] = value;\n    }\n  }\n  return borders;\n};\nexport var DEFAULT_APPBAR_HEIGHT = 56;\nvar MD3_DEFAULT_APPBAR_HEIGHT = 64;\nexport var modeAppbarHeight = {\n  small: MD3_DEFAULT_APPBAR_HEIGHT,\n  medium: 112,\n  large: 152,\n  'center-aligned': MD3_DEFAULT_APPBAR_HEIGHT\n};\nexport var modeTextVariant = {\n  small: 'titleLarge',\n  medium: 'headlineSmall',\n  large: 'headlineMedium',\n  'center-aligned': 'titleLarge'\n};\nexport var filterAppbarActions = function filterAppbarActions(children) {\n  var isLeading = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  return React.Children.toArray(children).filter(function (child) {\n    if (!React.isValidElement(child)) return false;\n    return isLeading ? child.props.isLeading : !child.props.isLeading;\n  });\n};\nexport var renderAppbarContent = function renderAppbarContent(_ref2) {\n  var children = _ref2.children,\n    isDark = _ref2.isDark,\n    _ref2$shouldCenterCon = _ref2.shouldCenterContent,\n    shouldCenterContent = _ref2$shouldCenterCon === void 0 ? false : _ref2$shouldCenterCon,\n    isV3 = _ref2.isV3,\n    renderOnly = _ref2.renderOnly,\n    renderExcept = _ref2.renderExcept,\n    _ref2$mode = _ref2.mode,\n    mode = _ref2$mode === void 0 ? 'small' : _ref2$mode,\n    theme = _ref2.theme;\n  return React.Children.toArray(children).filter(function (child) {\n    return child != null && typeof child !== 'boolean';\n  }).filter(function (child) {\n    return (renderExcept ? !renderExcept.includes(child.type.displayName) : child\n    );\n  }).filter(function (child) {\n    return (renderOnly ? renderOnly.includes(child.type.displayName) : child\n    );\n  }).map(function (child, i) {\n    if (!React.isValidElement(child) || !['Appbar.Content', 'Appbar.Action', 'Appbar.BackAction', 'Tooltip'].includes(child.type.displayName)) {\n      return child;\n    }\n    var props = {\n      theme: theme,\n      color: getAppbarColor({\n        color: child.props.color,\n        isDark: isDark,\n        isV3: isV3\n      })\n    };\n    if (child.type.displayName === 'Appbar.Content') {\n      props.mode = mode;\n      props.style = [isV3 ? i === 0 && !shouldCenterContent && styles.v3Spacing : i !== 0 && styles.v2Spacing, shouldCenterContent && styles.centerAlignedContent, child.props.style];\n      props.color;\n    }\n    return React.cloneElement(child, props);\n  });\n};\nvar styles = StyleSheet.create({\n  centerAlignedContent: {\n    alignItems: 'center'\n  },\n  v2Spacing: {\n    marginLeft: 8\n  },\n  v3Spacing: {\n    marginLeft: 12\n  }\n});", "map": {"version": 3, "names": ["React", "StyleSheet", "overlay", "black", "white", "borderStyleProperties", "getAppbarBackgroundColor", "theme", "elevation", "customBackground", "elevated", "isV3", "isDarkTheme", "dark", "mode", "colors", "isAdaptiveMode", "surface", "primary", "level2", "getAppbarColor", "_ref", "color", "isDark", "undefined", "getAppbarBorders", "style", "borders", "property", "value", "DEFAULT_APPBAR_HEIGHT", "MD3_DEFAULT_APPBAR_HEIGHT", "modeAppbarHeight", "small", "medium", "large", "modeTextVariant", "filterAppbarActions", "children", "isLeading", "arguments", "length", "Children", "toArray", "filter", "child", "isValidElement", "props", "renderAppbarContent", "_ref2", "_ref2$shouldCenterCon", "shouldCenterC<PERSON>nt", "renderOnly", "renderExcept", "_ref2$mode", "includes", "type", "displayName", "map", "i", "styles", "v3Spacing", "v2Spacing", "centerAlignedContent", "cloneElement", "create", "alignItems", "marginLeft"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\Appbar\\utils.ts"], "sourcesContent": ["import React from 'react';\nimport type { ColorValue, StyleProp, ViewStyle } from 'react-native';\nimport { StyleSheet, Animated } from 'react-native';\n\nimport overlay from '../../styles/overlay';\nimport { black, white } from '../../styles/themes/v2/colors';\nimport type { InternalTheme, ThemeProp } from '../../types';\n\nexport type AppbarModes = 'small' | 'medium' | 'large' | 'center-aligned';\n\nexport type AppbarChildProps = {\n  isLeading?: boolean;\n  color: string;\n  style?: StyleProp<ViewStyle>;\n};\n\nconst borderStyleProperties = [\n  'borderRadius',\n  'borderTopLeftRadius',\n  'borderTopRightRadius',\n  'borderBottomRightRadius',\n  'borderBottomLeftRadius',\n];\n\nexport const getAppbarBackgroundColor = (\n  theme: InternalTheme,\n  elevation: number,\n  customBackground?: ColorValue,\n  elevated?: boolean\n) => {\n  const { isV3, dark: isDarkTheme, mode, colors } = theme;\n  const isAdaptiveMode = mode === 'adaptive';\n  if (customBackground) {\n    return customBackground;\n  }\n\n  if (!isV3) {\n    if (isDarkTheme && isAdaptiveMode) {\n      return overlay(elevation, colors?.surface);\n    }\n\n    return colors.primary;\n  }\n\n  if (elevated) {\n    return theme.colors.elevation.level2;\n  }\n\n  return colors.surface;\n};\n\nexport const getAppbarColor = ({\n  color,\n  isDark,\n  isV3,\n}: BaseProps & { color: string }) => {\n  if (typeof color !== 'undefined') {\n    return color;\n  }\n\n  if (isDark) {\n    return white;\n  }\n\n  if (isV3) {\n    return undefined;\n  }\n\n  return black;\n};\n\nexport const getAppbarBorders = (\n  style:\n    | Animated.Value\n    | Animated.AnimatedInterpolation<string | number>\n    | Animated.WithAnimatedObject<ViewStyle>\n) => {\n  const borders: Record<string, number> = {};\n\n  for (const property of borderStyleProperties) {\n    const value = style[property as keyof typeof style];\n    if (value) {\n      borders[property] = value;\n    }\n  }\n\n  return borders;\n};\n\ntype BaseProps = {\n  isDark: boolean;\n  isV3: boolean;\n};\n\ntype RenderAppbarContentProps = BaseProps & {\n  children: React.ReactNode;\n  shouldCenterContent?: boolean;\n  isV3: boolean;\n  renderOnly?: (string | boolean)[];\n  renderExcept?: string[];\n  mode?: AppbarModes;\n  theme?: ThemeProp;\n};\n\nexport const DEFAULT_APPBAR_HEIGHT = 56;\nconst MD3_DEFAULT_APPBAR_HEIGHT = 64;\n\nexport const modeAppbarHeight = {\n  small: MD3_DEFAULT_APPBAR_HEIGHT,\n  medium: 112,\n  large: 152,\n  'center-aligned': MD3_DEFAULT_APPBAR_HEIGHT,\n};\n\nexport const modeTextVariant = {\n  small: 'titleLarge',\n  medium: 'headlineSmall',\n  large: 'headlineMedium',\n  'center-aligned': 'titleLarge',\n} as const;\n\nexport const filterAppbarActions = (\n  children: React.ReactNode,\n  isLeading = false\n) => {\n  return React.Children.toArray(children).filter((child) => {\n    if (!React.isValidElement<AppbarChildProps>(child)) return false;\n    return isLeading ? child.props.isLeading : !child.props.isLeading;\n  });\n};\n\nexport const renderAppbarContent = ({\n  children,\n  isDark,\n  shouldCenterContent = false,\n  isV3,\n  renderOnly,\n  renderExcept,\n  mode = 'small',\n  theme,\n}: RenderAppbarContentProps) => {\n  return React.Children.toArray(children as React.ReactNode | React.ReactNode[])\n    .filter((child) => child != null && typeof child !== 'boolean')\n    .filter((child) =>\n      // @ts-expect-error: TypeScript complains about the type of type but it doesn't matter\n      renderExcept ? !renderExcept.includes(child.type.displayName) : child\n    )\n    .filter((child) =>\n      // @ts-expect-error: TypeScript complains about the type of type but it doesn't matter\n      renderOnly ? renderOnly.includes(child.type.displayName) : child\n    )\n    .map((child, i) => {\n      if (\n        !React.isValidElement<AppbarChildProps>(child) ||\n        ![\n          'Appbar.Content',\n          'Appbar.Action',\n          'Appbar.BackAction',\n          'Tooltip',\n        ].includes(\n          // @ts-expect-error: TypeScript complains about the type of type but it doesn't matter\n          child.type.displayName\n        )\n      ) {\n        return child;\n      }\n\n      const props: {\n        color?: string;\n        style?: StyleProp<ViewStyle>;\n        mode?: AppbarModes;\n        theme?: ThemeProp;\n      } = {\n        theme,\n        color: getAppbarColor({ color: child.props.color, isDark, isV3 }),\n      };\n\n      // @ts-expect-error: TypeScript complains about the type of type but it doesn't matter\n      if (child.type.displayName === 'Appbar.Content') {\n        props.mode = mode;\n        props.style = [\n          isV3\n            ? i === 0 && !shouldCenterContent && styles.v3Spacing\n            : i !== 0 && styles.v2Spacing,\n          shouldCenterContent && styles.centerAlignedContent,\n          child.props.style,\n        ];\n        props.color;\n      }\n      return React.cloneElement(child, props);\n    });\n};\n\nconst styles = StyleSheet.create({\n  centerAlignedContent: {\n    alignItems: 'center',\n  },\n  v2Spacing: {\n    marginLeft: 8,\n  },\n  v3Spacing: {\n    marginLeft: 12,\n  },\n});\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAAA,OAAAC,UAAA;AAIzB,OAAOC,OAAO;AACd,SAASC,KAAK,EAAEC,KAAK;AAWrB,IAAMC,qBAAqB,GAAG,CAC5B,cAAc,EACd,qBAAqB,EACrB,sBAAsB,EACtB,yBAAyB,EACzB,wBAAwB,CACzB;AAED,OAAO,IAAMC,wBAAwB,GAAG,SAA3BA,wBAAwBA,CACnCC,KAAoB,EACpBC,SAAiB,EACjBC,gBAA6B,EAC7BC,QAAkB,EACf;EACH,IAAQC,IAAI,GAAsCJ,KAAK,CAA/CI,IAAI;IAAQC,WAAW,GAAmBL,KAAK,CAAzCM,IAAI;IAAeC,IAAI,GAAaP,KAAK,CAAtBO,IAAI;IAAEC,MAAA,GAAWR,KAAK,CAAhBQ,MAAA;EACvC,IAAMC,cAAc,GAAGF,IAAI,KAAK,UAAU;EAC1C,IAAIL,gBAAgB,EAAE;IACpB,OAAOA,gBAAgB;EACzB;EAEA,IAAI,CAACE,IAAI,EAAE;IACT,IAAIC,WAAW,IAAII,cAAc,EAAE;MACjC,OAAOd,OAAO,CAACM,SAAS,EAAEO,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEE,OAAO,CAAC;IAC5C;IAEA,OAAOF,MAAM,CAACG,OAAO;EACvB;EAEA,IAAIR,QAAQ,EAAE;IACZ,OAAOH,KAAK,CAACQ,MAAM,CAACP,SAAS,CAACW,MAAM;EACtC;EAEA,OAAOJ,MAAM,CAACE,OAAO;AACvB,CAAC;AAED,OAAO,IAAMG,cAAc,GAAG,SAAjBA,cAAcA,CAAAC,IAAA,EAIU;EAAA,IAHnCC,KAAK,GAAAD,IAAA,CAALC,KAAK;IACLC,MAAM,GAAAF,IAAA,CAANE,MAAM;IACNZ,IAAA,GAAAU,IAAA,CAAAV,IAAA;EAEA,IAAI,OAAOW,KAAK,KAAK,WAAW,EAAE;IAChC,OAAOA,KAAK;EACd;EAEA,IAAIC,MAAM,EAAE;IACV,OAAOnB,KAAK;EACd;EAEA,IAAIO,IAAI,EAAE;IACR,OAAOa,SAAS;EAClB;EAEA,OAAOrB,KAAK;AACd,CAAC;AAED,OAAO,IAAMsB,gBAAgB,GAC3B,SADWA,gBAAgBA,CAC3BC,KAG0C,EACvC;EACH,IAAMC,OAA+B,GAAG,CAAC,CAAC;EAE1C,KAAK,IAAMC,QAAQ,IAAIvB,qBAAqB,EAAE;IAC5C,IAAMwB,KAAK,GAAGH,KAAK,CAACE,QAAQ,CAAuB;IACnD,IAAIC,KAAK,EAAE;MACTF,OAAO,CAACC,QAAQ,CAAC,GAAGC,KAAK;IAC3B;EACF;EAEA,OAAOF,OAAO;AAChB,CAAC;AAiBD,OAAO,IAAMG,qBAAqB,GAAG,EAAE;AACvC,IAAMC,yBAAyB,GAAG,EAAE;AAEpC,OAAO,IAAMC,gBAAgB,GAAG;EAC9BC,KAAK,EAAEF,yBAAyB;EAChCG,MAAM,EAAE,GAAG;EACXC,KAAK,EAAE,GAAG;EACV,gBAAgB,EAAEJ;AACpB,CAAC;AAED,OAAO,IAAMK,eAAe,GAAG;EAC7BH,KAAK,EAAE,YAAY;EACnBC,MAAM,EAAE,eAAe;EACvBC,KAAK,EAAE,gBAAgB;EACvB,gBAAgB,EAAE;AACpB,CAAU;AAEV,OAAO,IAAME,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAC9BC,QAAyB,EAEtB;EAAA,IADHC,SAAS,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAhB,SAAA,GAAAgB,SAAA,MAAG,KAAK;EAEjB,OAAOxC,KAAK,CAAC0C,QAAQ,CAACC,OAAO,CAACL,QAAQ,CAAC,CAACM,MAAM,CAAE,UAAAC,KAAK,EAAK;IACxD,IAAI,CAAC7C,KAAK,CAAC8C,cAAc,CAAmBD,KAAK,CAAC,EAAE,OAAO,KAAK;IAChE,OAAON,SAAS,GAAGM,KAAK,CAACE,KAAK,CAACR,SAAS,GAAG,CAACM,KAAK,CAACE,KAAK,CAACR,SAAS;EACnE,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,IAAMS,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAAC,KAAA,EASA;EAAA,IAR9BX,QAAQ,GAAAW,KAAA,CAARX,QAAQ;IACRf,MAAM,GAAA0B,KAAA,CAAN1B,MAAM;IAAA2B,qBAAA,GAAAD,KAAA,CACNE,mBAAmB;IAAnBA,mBAAmB,GAAAD,qBAAA,cAAG,KAAK,GAAAA,qBAAA;IAC3BvC,IAAI,GAAAsC,KAAA,CAAJtC,IAAI;IACJyC,UAAU,GAAAH,KAAA,CAAVG,UAAU;IACVC,YAAY,GAAAJ,KAAA,CAAZI,YAAY;IAAAC,UAAA,GAAAL,KAAA,CACZnC,IAAI;IAAJA,IAAI,GAAAwC,UAAA,cAAG,OAAO,GAAAA,UAAA;IACd/C,KAAA,GAAA0C,KAAA,CAAA1C,KAAA;EAEA,OAAOP,KAAK,CAAC0C,QAAQ,CAACC,OAAO,CAACL,QAA+C,CAAC,CAC3EM,MAAM,CAAE,UAAAC,KAAK;IAAA,OAAKA,KAAK,IAAI,IAAI,IAAI,OAAOA,KAAK,KAAK,SAAS;EAAA,EAAC,CAC9DD,MAAM,CAAE,UAAAC,KAAK;IAAA,QAEZQ,YAAY,GAAG,CAACA,YAAY,CAACE,QAAQ,CAACV,KAAK,CAACW,IAAI,CAACC,WAAW,CAAC,GAAGZ;IAClE;EAAA,EAAC,CACAD,MAAM,CAAE,UAAAC,KAAK;IAAA,QAEZO,UAAU,GAAGA,UAAU,CAACG,QAAQ,CAACV,KAAK,CAACW,IAAI,CAACC,WAAW,CAAC,GAAGZ;IAC7D;EAAA,EAAC,CACAa,GAAG,CAAC,UAACb,KAAK,EAAEc,CAAC,EAAK;IACjB,IACE,CAAC3D,KAAK,CAAC8C,cAAc,CAAmBD,KAAK,CAAC,IAC9C,CAAC,CACC,gBAAgB,EAChB,eAAe,EACf,mBAAmB,EACnB,SAAS,CACV,CAACU,QAAQ,CAERV,KAAK,CAACW,IAAI,CAACC,WACb,CAAC,EACD;MACA,OAAOZ,KAAK;IACd;IAEA,IAAME,KAKL,GAAG;MACFxC,KAAK,EAALA,KAAK;MACLe,KAAK,EAAEF,cAAc,CAAC;QAAEE,KAAK,EAAEuB,KAAK,CAACE,KAAK,CAACzB,KAAK;QAAEC,MAAM,EAANA,MAAM;QAAEZ,IAAA,EAAAA;MAAK,CAAC;IAClE,CAAC;IAGD,IAAIkC,KAAK,CAACW,IAAI,CAACC,WAAW,KAAK,gBAAgB,EAAE;MAC/CV,KAAK,CAACjC,IAAI,GAAGA,IAAI;MACjBiC,KAAK,CAACrB,KAAK,GAAG,CACZf,IAAI,GACAgD,CAAC,KAAK,CAAC,IAAI,CAACR,mBAAmB,IAAIS,MAAM,CAACC,SAAS,GACnDF,CAAC,KAAK,CAAC,IAAIC,MAAM,CAACE,SAAS,EAC/BX,mBAAmB,IAAIS,MAAM,CAACG,oBAAoB,EAClDlB,KAAK,CAACE,KAAK,CAACrB,KAAK,CAClB;MACDqB,KAAK,CAACzB,KAAK;IACb;IACA,OAAOtB,KAAK,CAACgE,YAAY,CAACnB,KAAK,EAAEE,KAAK,CAAC;EACzC,CAAC,CAAC;AACN,CAAC;AAED,IAAMa,MAAM,GAAG3D,UAAU,CAACgE,MAAM,CAAC;EAC/BF,oBAAoB,EAAE;IACpBG,UAAU,EAAE;EACd,CAAC;EACDJ,SAAS,EAAE;IACTK,UAAU,EAAE;EACd,CAAC;EACDN,SAAS,EAAE;IACTM,UAAU,EAAE;EACd;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}