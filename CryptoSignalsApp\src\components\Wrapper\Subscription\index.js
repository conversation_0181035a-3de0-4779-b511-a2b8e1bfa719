import React, { useContext, useEffect } from 'react';
import { StoreContext } from '../../../store/index';
import { setSubscription } from '../../../store/actions';
import { getSecureStoreItem } from '../../../services/secureStore';
import { AxiosContext } from '../../../store/axios';

const Subscription = ({ children }) => {
  const [state, dispatch] = useContext(StoreContext);
  const [api] = useContext(AxiosContext);
  const { subscription: { customerId, subscriptionPeriodEnd } } = state;

  useEffect(() => {
    loadSubscription();
  }, []);

  const loadSubscription = async () => {
    if (!customerId) {
      let subscription = await getSecureStoreItem('subscription');

      if (!subscription) {
        return;
      }

      if (isExpiredDay(subscription.subscriptionPeriodEnd)) {
        await getSubscriptionStatus(subscription);
        return;
      }

      setSubscription(dispatch, subscription);
    }

    if (customerId && !subscriptionPeriodEnd) {
      let subscription = await getSecureStoreItem('subscription');
      await getSubscriptionStatus(subscription);
    }
  }

  const isExpiredDay = (endDate) => {
    let dateNow = new Date();
    let subscriptionDateEnd = new Date(endDate);

    return dateNow >= subscriptionDateEnd;
  }

  const getSubscriptionStatus = async (subscription) => {
    const {
      data: {
        subscriptionStatus,
        subscriptionPeriodEnd,
        accessToken
      }
    } = await api.get('/stripe-get-subscription-status/' + subscription.customerId);

    subscription.subscriptionStatus = subscriptionStatus;
    subscription.subscriptionPeriodEnd = subscriptionPeriodEnd;
    subscription.accessToken = accessToken;

    setSubscription(dispatch, subscription);
  }

  return (
    <>
      {children}
    </>
  )
}

export default Subscription;
