{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport React from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport TextInput from \"react-native-web/dist/exports/TextInput\";\nimport TouchableOpacity from \"react-native-web/dist/exports/TouchableOpacity\";\nimport IconSearch from \"../Icon/Search\";\nimport styles from \"./styles\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar InputSearch = function InputSearch(props) {\n  return _jsxs(View, {\n    style: styles.container,\n    children: [_jsx(TextInput, _objectSpread(_objectSpread({}, props), {}, {\n      style: styles.search,\n      placeholderTextColor: \"#7A7A7A\"\n    })), _jsx(TouchableOpacity, {\n      style: styles.searchButton,\n      children: _jsx(IconSearch, {})\n    })]\n  });\n};\nexport default InputSearch;", "map": {"version": 3, "names": ["React", "View", "TextInput", "TouchableOpacity", "IconSearch", "styles", "jsx", "_jsx", "jsxs", "_jsxs", "InputSearch", "props", "style", "container", "children", "_objectSpread", "search", "placeholderTextColor", "searchButton"], "sources": ["C:/Users/<USER>/Desktop/Signals/CryptoSignalsApp/src/components/InputSearch/index.js"], "sourcesContent": ["import React from 'react';\r\nimport { View, TextInput, TouchableOpacity } from 'react-native';\r\nimport IconSearch from '../Icon/Search';\r\nimport styles from './styles';\r\n\r\nconst InputSearch = (props) => {\r\n  return (\r\n    <View style={styles.container}>\r\n      <TextInput\r\n        {...props}\r\n        style={styles.search}\r\n        placeholderTextColor=\"#7A7A7A\"\r\n      />\r\n\r\n      <TouchableOpacity style={styles.searchButton}>\r\n        <IconSearch />\r\n      </TouchableOpacity>\r\n    </View>\r\n  )\r\n}\r\n\r\nexport default InputSearch\r\n"], "mappings": ";;;AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,SAAA;AAAA,OAAAC,gBAAA;AAE1B,OAAOC,UAAU;AACjB,OAAOC,MAAM;AAAiB,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAE9B,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAIC,KAAK,EAAK;EAC7B,OACEF,KAAA,CAACR,IAAI;IAACW,KAAK,EAAEP,MAAM,CAACQ,SAAU;IAAAC,QAAA,GAC5BP,IAAA,CAACL,SAAS,EAAAa,aAAA,CAAAA,aAAA,KACJJ,KAAK;MACTC,KAAK,EAAEP,MAAM,CAACW,MAAO;MACrBC,oBAAoB,EAAC;IAAS,EAC/B,CAAC,EAEFV,IAAA,CAACJ,gBAAgB;MAACS,KAAK,EAAEP,MAAM,CAACa,YAAa;MAAAJ,QAAA,EAC3CP,IAAA,CAACH,UAAU,IAAE;IAAC,CACE,CAAC;EAAA,CACf,CAAC;AAEX,CAAC;AAED,eAAeM,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}