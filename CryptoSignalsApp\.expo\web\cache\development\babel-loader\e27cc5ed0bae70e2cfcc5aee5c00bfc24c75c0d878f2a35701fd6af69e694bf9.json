{"ast": null, "code": "import color from 'color';\nimport { MD3Colors, tokens } from \"./tokens\";\nimport configureFonts from \"../../fonts\";\nvar _tokens$md$ref = tokens.md.ref,\n  palette = _tokens$md$ref.palette,\n  opacity = _tokens$md$ref.opacity;\nexport var MD3LightTheme = {\n  dark: false,\n  roundness: 4,\n  version: 3,\n  isV3: true,\n  colors: {\n    primary: palette.primary40,\n    primaryContainer: palette.primary90,\n    secondary: palette.secondary40,\n    secondaryContainer: palette.secondary90,\n    tertiary: palette.tertiary40,\n    tertiaryContainer: palette.tertiary90,\n    surface: palette.neutral99,\n    surfaceVariant: palette.neutralVariant90,\n    surfaceDisabled: color(palette.neutral10).alpha(opacity.level2).rgb().string(),\n    background: palette.neutral99,\n    error: palette.error40,\n    errorContainer: palette.error90,\n    onPrimary: palette.primary100,\n    onPrimaryContainer: palette.primary10,\n    onSecondary: palette.secondary100,\n    onSecondaryContainer: palette.secondary10,\n    onTertiary: palette.tertiary100,\n    onTertiaryContainer: palette.tertiary10,\n    onSurface: palette.neutral10,\n    onSurfaceVariant: palette.neutralVariant30,\n    onSurfaceDisabled: color(palette.neutral10).alpha(opacity.level4).rgb().string(),\n    onError: palette.error100,\n    onErrorContainer: palette.error10,\n    onBackground: palette.neutral10,\n    outline: palette.neutralVariant50,\n    outlineVariant: palette.neutralVariant80,\n    inverseSurface: palette.neutral20,\n    inverseOnSurface: palette.neutral95,\n    inversePrimary: palette.primary80,\n    shadow: palette.neutral0,\n    scrim: palette.neutral0,\n    backdrop: color(MD3Colors.neutralVariant20).alpha(0.4).rgb().string(),\n    elevation: {\n      level0: 'transparent',\n      level1: 'rgb(247, 243, 249)',\n      level2: 'rgb(243, 237, 246)',\n      level3: 'rgb(238, 232, 244)',\n      level4: 'rgb(236, 230, 243)',\n      level5: 'rgb(233, 227, 241)'\n    }\n  },\n  fonts: configureFonts(),\n  animation: {\n    scale: 1.0\n  }\n};", "map": {"version": 3, "names": ["color", "MD3Colors", "tokens", "configure<PERSON>onts", "_tokens$md$ref", "md", "ref", "palette", "opacity", "MD3LightTheme", "dark", "roundness", "version", "isV3", "colors", "primary", "primary40", "primaryContainer", "primary90", "secondary", "secondary40", "secondaryContainer", "secondary90", "tertiary", "tertiary40", "tertiaryContainer", "tertiary90", "surface", "neutral99", "surfaceVariant", "neutralVariant90", "surfaceDisabled", "neutral10", "alpha", "level2", "rgb", "string", "background", "error", "error40", "<PERSON><PERSON><PERSON><PERSON>", "error90", "onPrimary", "primary100", "onPrimaryContainer", "primary10", "onSecondary", "secondary100", "onSecondaryContainer", "secondary10", "onTertiary", "tertiary100", "onTertiaryContainer", "tertiary10", "onSurface", "onSurfaceVariant", "neutralVariant30", "onSurfaceDisabled", "level4", "onError", "error100", "onError<PERSON><PERSON>r", "error10", "onBackground", "outline", "neutralVariant50", "outlineVariant", "neutralVariant80", "inverseSurface", "neutral20", "inverseOnSurface", "neutral95", "inversePrimary", "primary80", "shadow", "neutral0", "scrim", "backdrop", "neutralVariant20", "elevation", "level0", "level1", "level3", "level5", "fonts", "animation", "scale"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\styles\\themes\\v3\\LightTheme.tsx"], "sourcesContent": ["import color from 'color';\n\nimport { MD3Colors, tokens } from './tokens';\nimport type { MD3Theme } from '../../../types';\nimport configureFonts from '../../fonts';\n\nconst { palette, opacity } = tokens.md.ref;\n\nexport const MD3LightTheme: MD3Theme = {\n  dark: false,\n  roundness: 4,\n  version: 3,\n  isV3: true,\n  colors: {\n    primary: palette.primary40,\n    primaryContainer: palette.primary90,\n    secondary: palette.secondary40,\n    secondaryContainer: palette.secondary90,\n    tertiary: palette.tertiary40,\n    tertiaryContainer: palette.tertiary90,\n    surface: palette.neutral99,\n    surfaceVariant: palette.neutralVariant90,\n    surfaceDisabled: color(palette.neutral10)\n      .alpha(opacity.level2)\n      .rgb()\n      .string(),\n    background: palette.neutral99,\n    error: palette.error40,\n    errorContainer: palette.error90,\n    onPrimary: palette.primary100,\n    onPrimaryContainer: palette.primary10,\n    onSecondary: palette.secondary100,\n    onSecondaryContainer: palette.secondary10,\n    onTertiary: palette.tertiary100,\n    onTertiaryContainer: palette.tertiary10,\n    onSurface: palette.neutral10,\n    onSurfaceVariant: palette.neutralVariant30,\n    onSurfaceDisabled: color(palette.neutral10)\n      .alpha(opacity.level4)\n      .rgb()\n      .string(),\n    onError: palette.error100,\n    onErrorContainer: palette.error10,\n    onBackground: palette.neutral10,\n    outline: palette.neutralVariant50,\n    outlineVariant: palette.neutralVariant80,\n    inverseSurface: palette.neutral20,\n    inverseOnSurface: palette.neutral95,\n    inversePrimary: palette.primary80,\n    shadow: palette.neutral0,\n    scrim: palette.neutral0,\n    backdrop: color(MD3Colors.neutralVariant20).alpha(0.4).rgb().string(),\n    elevation: {\n      level0: 'transparent',\n      // Note: Color values with transparency cause RN to transfer shadows to children nodes\n      // instead of View component in Surface. Providing solid background fixes the issue.\n      // Opaque color values generated with `palette.primary99` used as background\n      level1: 'rgb(247, 243, 249)', // palette.primary40, alpha 0.05\n      level2: 'rgb(243, 237, 246)', // palette.primary40, alpha 0.08\n      level3: 'rgb(238, 232, 244)', // palette.primary40, alpha 0.11\n      level4: 'rgb(236, 230, 243)', // palette.primary40, alpha 0.12\n      level5: 'rgb(233, 227, 241)', // palette.primary40, alpha 0.14\n    },\n  },\n  fonts: configureFonts(),\n  animation: {\n    scale: 1.0,\n  },\n};\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,SAASC,SAAS,EAAEC,MAAM;AAE1B,OAAOC,cAAc;AAErB,IAAAC,cAAA,GAA6BF,MAAM,CAACG,EAAE,CAACC,GAAG;EAAlCC,OAAO,GAAAH,cAAA,CAAPG,OAAO;EAAEC,OAAA,GAAAJ,cAAA,CAAAI,OAAA;AAEjB,OAAO,IAAMC,aAAuB,GAAG;EACrCC,IAAI,EAAE,KAAK;EACXC,SAAS,EAAE,CAAC;EACZC,OAAO,EAAE,CAAC;EACVC,IAAI,EAAE,IAAI;EACVC,MAAM,EAAE;IACNC,OAAO,EAAER,OAAO,CAACS,SAAS;IAC1BC,gBAAgB,EAAEV,OAAO,CAACW,SAAS;IACnCC,SAAS,EAAEZ,OAAO,CAACa,WAAW;IAC9BC,kBAAkB,EAAEd,OAAO,CAACe,WAAW;IACvCC,QAAQ,EAAEhB,OAAO,CAACiB,UAAU;IAC5BC,iBAAiB,EAAElB,OAAO,CAACmB,UAAU;IACrCC,OAAO,EAAEpB,OAAO,CAACqB,SAAS;IAC1BC,cAAc,EAAEtB,OAAO,CAACuB,gBAAgB;IACxCC,eAAe,EAAE/B,KAAK,CAACO,OAAO,CAACyB,SAAS,CAAC,CACtCC,KAAK,CAACzB,OAAO,CAAC0B,MAAM,CAAC,CACrBC,GAAG,CAAC,CAAC,CACLC,MAAM,CAAC,CAAC;IACXC,UAAU,EAAE9B,OAAO,CAACqB,SAAS;IAC7BU,KAAK,EAAE/B,OAAO,CAACgC,OAAO;IACtBC,cAAc,EAAEjC,OAAO,CAACkC,OAAO;IAC/BC,SAAS,EAAEnC,OAAO,CAACoC,UAAU;IAC7BC,kBAAkB,EAAErC,OAAO,CAACsC,SAAS;IACrCC,WAAW,EAAEvC,OAAO,CAACwC,YAAY;IACjCC,oBAAoB,EAAEzC,OAAO,CAAC0C,WAAW;IACzCC,UAAU,EAAE3C,OAAO,CAAC4C,WAAW;IAC/BC,mBAAmB,EAAE7C,OAAO,CAAC8C,UAAU;IACvCC,SAAS,EAAE/C,OAAO,CAACyB,SAAS;IAC5BuB,gBAAgB,EAAEhD,OAAO,CAACiD,gBAAgB;IAC1CC,iBAAiB,EAAEzD,KAAK,CAACO,OAAO,CAACyB,SAAS,CAAC,CACxCC,KAAK,CAACzB,OAAO,CAACkD,MAAM,CAAC,CACrBvB,GAAG,CAAC,CAAC,CACLC,MAAM,CAAC,CAAC;IACXuB,OAAO,EAAEpD,OAAO,CAACqD,QAAQ;IACzBC,gBAAgB,EAAEtD,OAAO,CAACuD,OAAO;IACjCC,YAAY,EAAExD,OAAO,CAACyB,SAAS;IAC/BgC,OAAO,EAAEzD,OAAO,CAAC0D,gBAAgB;IACjCC,cAAc,EAAE3D,OAAO,CAAC4D,gBAAgB;IACxCC,cAAc,EAAE7D,OAAO,CAAC8D,SAAS;IACjCC,gBAAgB,EAAE/D,OAAO,CAACgE,SAAS;IACnCC,cAAc,EAAEjE,OAAO,CAACkE,SAAS;IACjCC,MAAM,EAAEnE,OAAO,CAACoE,QAAQ;IACxBC,KAAK,EAAErE,OAAO,CAACoE,QAAQ;IACvBE,QAAQ,EAAE7E,KAAK,CAACC,SAAS,CAAC6E,gBAAgB,CAAC,CAAC7C,KAAK,CAAC,GAAG,CAAC,CAACE,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;IACrE2C,SAAS,EAAE;MACTC,MAAM,EAAE,aAAa;MAIrBC,MAAM,EAAE,oBAAoB;MAC5B/C,MAAM,EAAE,oBAAoB;MAC5BgD,MAAM,EAAE,oBAAoB;MAC5BxB,MAAM,EAAE,oBAAoB;MAC5ByB,MAAM,EAAE;IACV;EACF,CAAC;EACDC,KAAK,EAAEjF,cAAc,CAAC,CAAC;EACvBkF,SAAS,EAAE;IACTC,KAAK,EAAE;EACT;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}