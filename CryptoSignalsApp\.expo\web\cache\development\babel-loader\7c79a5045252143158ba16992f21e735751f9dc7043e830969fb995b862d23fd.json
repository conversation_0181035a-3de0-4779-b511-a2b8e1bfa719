{"ast": null, "code": "import StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nvar styles = StyleSheet.create({\n  row: {\n    marginBottom: 16\n  },\n  pageTitle: {\n    marginBottom: 24\n  },\n  h1: {\n    color: '#fff',\n    fontSize: 24,\n    textAlign: 'center',\n    fontFamily: 'Poppins_500Medium'\n  },\n  paragraph: {\n    color: '#fff',\n    fontSize: 14,\n    textAlign: 'center',\n    fontFamily: 'Poppins_400Regular'\n  },\n  text: {\n    color: '#fff',\n    fontSize: 12,\n    textAlign: 'center',\n    fontFamily: 'Poppins_400Regular'\n  },\n  inputEmail: {\n    backgroundColor: '#fff',\n    borderRadius: 4,\n    color: '#0D0D0D',\n    fontSize: 14,\n    padding: 12,\n    height: 48,\n    fontFamily: 'Poppins_400Regular',\n    alignItems: 'center',\n    marginBottom: 15\n  },\n  premiumDates: {\n    color: '#fff',\n    fontSize: 14,\n    fontFamily: 'Poppins_500Medium',\n    textAlign: 'center'\n  },\n  buttonPay: {\n    backgroundColor: '#3176c4',\n    borderRadius: 4,\n    paddingVertical: 6,\n    height: 48,\n    display: 'flex',\n    flexDirection: 'row',\n    alignItems: 'center',\n    justifyContent: 'center'\n  },\n  textButtonPay: {\n    color: '#fff',\n    fontSize: 14,\n    textAlign: 'center',\n    fontFamily: 'Poppins_600SemiBold',\n    lineHeight: 22\n  }\n});\nexport default styles;", "map": {"version": 3, "names": ["styles", "StyleSheet", "create", "row", "marginBottom", "pageTitle", "h1", "color", "fontSize", "textAlign", "fontFamily", "paragraph", "text", "inputEmail", "backgroundColor", "borderRadius", "padding", "height", "alignItems", "premiumDates", "buttonPay", "paddingVertical", "display", "flexDirection", "justifyContent", "textButtonPay", "lineHeight"], "sources": ["E:/CryptoSignalsApp/src/pages/Premium/styles.js"], "sourcesContent": ["import { StyleSheet } from 'react-native';\r\n\r\nconst styles = StyleSheet.create({\r\n  row: {\r\n    marginBottom: 16,\r\n  },\r\n  pageTitle: {\r\n    marginBottom: 24\r\n  },\r\n  h1: {\r\n    color: '#fff',\r\n    fontSize: 24,\r\n    textAlign: 'center',\r\n    fontFamily: 'Poppins_500Medium'\r\n  },\r\n  paragraph: {\r\n    color: '#fff',\r\n    fontSize: 14,\r\n    textAlign: 'center',\r\n    fontFamily: 'Poppins_400Regular'\r\n  },\r\n  text: {\r\n    color: '#fff',\r\n    fontSize: 12,\r\n    textAlign: 'center',\r\n    fontFamily: 'Poppins_400Regular'\r\n  },\r\n  inputEmail: {\r\n    backgroundColor: '#fff',\r\n    borderRadius: 4,\r\n    color: '#0D0D0D',\r\n    fontSize: 14,\r\n    padding: 12,\r\n    height: 48,\r\n    fontFamily: 'Poppins_400Regular',\r\n    alignItems: 'center',\r\n    marginBottom: 15\r\n  },\r\n  premiumDates: {\r\n    color: '#fff',\r\n    fontSize: 14,\r\n    fontFamily: 'Poppins_500Medium',\r\n    textAlign: 'center',\r\n  },\r\n  buttonPay: {\r\n    backgroundColor: '#3176c4',\r\n    borderRadius: 4,\r\n    paddingVertical: 6,\r\n    height: 48,\r\n    display: 'flex',\r\n    flexDirection: 'row',\r\n    alignItems: 'center',\r\n    justifyContent: 'center'\r\n  },\r\n  textButtonPay: {\r\n    color: '#fff',\r\n    fontSize: 14,\r\n    textAlign: 'center',\r\n    fontFamily: 'Poppins_600SemiBold',\r\n    lineHeight: 22,\r\n  }\r\n});\r\n\r\nexport default styles;\r\n"], "mappings": ";AAEA,IAAMA,MAAM,GAAGC,UAAU,CAACC,MAAM,CAAC;EAC/BC,GAAG,EAAE;IACHC,YAAY,EAAE;EAChB,CAAC;EACDC,SAAS,EAAE;IACTD,YAAY,EAAE;EAChB,CAAC;EACDE,EAAE,EAAE;IACFC,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,QAAQ;IACnBC,UAAU,EAAE;EACd,CAAC;EACDC,SAAS,EAAE;IACTJ,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,QAAQ;IACnBC,UAAU,EAAE;EACd,CAAC;EACDE,IAAI,EAAE;IACJL,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,QAAQ;IACnBC,UAAU,EAAE;EACd,CAAC;EACDG,UAAU,EAAE;IACVC,eAAe,EAAE,MAAM;IACvBC,YAAY,EAAE,CAAC;IACfR,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,EAAE;IACZQ,OAAO,EAAE,EAAE;IACXC,MAAM,EAAE,EAAE;IACVP,UAAU,EAAE,oBAAoB;IAChCQ,UAAU,EAAE,QAAQ;IACpBd,YAAY,EAAE;EAChB,CAAC;EACDe,YAAY,EAAE;IACZZ,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,EAAE;IACZE,UAAU,EAAE,mBAAmB;IAC/BD,SAAS,EAAE;EACb,CAAC;EACDW,SAAS,EAAE;IACTN,eAAe,EAAE,SAAS;IAC1BC,YAAY,EAAE,CAAC;IACfM,eAAe,EAAE,CAAC;IAClBJ,MAAM,EAAE,EAAE;IACVK,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,KAAK;IACpBL,UAAU,EAAE,QAAQ;IACpBM,cAAc,EAAE;EAClB,CAAC;EACDC,aAAa,EAAE;IACblB,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,QAAQ;IACnBC,UAAU,EAAE,qBAAqB;IACjCgB,UAAU,EAAE;EACd;AACF,CAAC,CAAC;AAEF,eAAe1B,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}