{"ast": null, "code": "export * from \"./Font\";\nexport { useFonts } from \"./FontHooks\";", "map": {"version": 3, "names": ["useFonts"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\expo-font\\src\\index.ts"], "sourcesContent": ["export * from './Font';\nexport { useFonts } from './FontHooks';\n"], "mappings": "AAAA;AACA,SAASA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}