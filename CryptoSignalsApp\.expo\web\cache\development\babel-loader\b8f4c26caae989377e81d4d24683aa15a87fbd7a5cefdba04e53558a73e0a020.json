{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"size\", \"color\", \"icon\", \"disabled\", \"onPress\", \"accessibilityLabel\", \"isLeading\", \"theme\", \"rippleColor\"];\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nimport * as React from 'react';\nimport color from 'color';\nimport { useInternalTheme } from \"../../core/theming\";\nimport { black } from \"../../styles/themes/v2/colors\";\nimport { forwardRef } from \"../../utils/forwardRef\";\nimport IconButton from \"../IconButton/IconButton\";\nvar AppbarAction = forwardRef(function (_ref, ref) {\n  var _ref$size = _ref.size,\n    size = _ref$size === void 0 ? 24 : _ref$size,\n    iconColor = _ref.color,\n    icon = _ref.icon,\n    disabled = _ref.disabled,\n    onPress = _ref.onPress,\n    accessibilityLabel = _ref.accessibilityLabel,\n    isLeading = _ref.isLeading,\n    themeOverrides = _ref.theme,\n    rippleColor = _ref.rippleColor,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var theme = useInternalTheme(themeOverrides);\n  var actionIconColor = iconColor ? iconColor : theme.isV3 ? isLeading ? theme.colors.onSurface : theme.colors.onSurfaceVariant : color(black).alpha(0.54).rgb().string();\n  return React.createElement(IconButton, _extends({\n    size: size,\n    onPress: onPress,\n    iconColor: actionIconColor,\n    icon: icon,\n    disabled: disabled,\n    accessibilityLabel: accessibilityLabel,\n    animated: true,\n    ref: ref,\n    rippleColor: rippleColor\n  }, rest));\n});\nAppbarAction.displayName = 'Appbar.Action';\nexport default AppbarAction;\nexport { AppbarAction };", "map": {"version": 3, "names": ["React", "color", "useInternalTheme", "black", "forwardRef", "IconButton", "AppbarAction", "_ref", "ref", "_ref$size", "size", "iconColor", "icon", "disabled", "onPress", "accessibilityLabel", "isLeading", "themeOverrides", "theme", "rippleColor", "rest", "_objectWithoutProperties", "_excluded", "actionIconColor", "isV3", "colors", "onSurface", "onSurfaceVariant", "alpha", "rgb", "string", "createElement", "_extends", "animated", "displayName"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\Appbar\\AppbarAction.tsx"], "sourcesContent": ["import * as React from 'react';\nimport type {\n  Animated,\n  ColorValue,\n  StyleProp,\n  View,\n  ViewStyle,\n} from 'react-native';\n\nimport color from 'color';\nimport type { ThemeProp } from 'src/types';\n\nimport { useInternalTheme } from '../../core/theming';\nimport { black } from '../../styles/themes/v2/colors';\nimport { forwardRef } from '../../utils/forwardRef';\nimport type { IconSource } from '../Icon';\nimport IconButton from '../IconButton/IconButton';\n\nexport type Props = React.ComponentPropsWithoutRef<typeof IconButton> & {\n  /**\n   *  Custom color for action icon.\n   */\n  color?: string;\n  /**\n   * Color of the ripple effect.\n   */\n  rippleColor?: ColorValue;\n  /**\n   * Name of the icon to show.\n   */\n  icon: IconSource;\n  /**\n   * Optional icon size.\n   */\n  size?: number;\n  /**\n   * Whether the button is disabled. A disabled button is greyed out and `onPress` is not called on touch.\n   */\n  disabled?: boolean;\n  /**\n   * Accessibility label for the button. This is read by the screen reader when the user taps the button.\n   */\n  accessibilityLabel?: string;\n  /**\n   * Function to execute on press.\n   */\n  onPress?: () => void;\n  /**\n   * @supported Available in v5.x with theme version 3\n   *\n   * Whether it's the leading button. Note: If `Appbar.BackAction` is present, it will be rendered before any `isLeading` icons.\n   */\n  isLeading?: boolean;\n  style?: Animated.WithAnimatedValue<StyleProp<ViewStyle>>;\n  ref?: React.RefObject<View>;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n};\n\n/**\n * A component used to display an action item in the appbar.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { Appbar } from 'react-native-paper';\n * import { Platform } from 'react-native';\n *\n * const MORE_ICON = Platform.OS === 'ios' ? 'dots-horizontal' : 'dots-vertical';\n *\n * const MyComponent = () => (\n *     <Appbar.Header>\n *        <Appbar.Content title=\"Title\" subtitle={'Subtitle'} />\n *         <Appbar.Action icon=\"magnify\" onPress={() => {}} />\n *         <Appbar.Action icon={MORE_ICON} onPress={() => {}} />\n *     </Appbar.Header>\n * );\n *\n * export default MyComponent;\n * ```\n */\nconst AppbarAction = forwardRef<View, Props>(\n  (\n    {\n      size = 24,\n      color: iconColor,\n      icon,\n      disabled,\n      onPress,\n      accessibilityLabel,\n      isLeading,\n      theme: themeOverrides,\n      rippleColor,\n      ...rest\n    }: Props,\n    ref\n  ) => {\n    const theme = useInternalTheme(themeOverrides);\n\n    const actionIconColor = iconColor\n      ? iconColor\n      : theme.isV3\n      ? isLeading\n        ? theme.colors.onSurface\n        : theme.colors.onSurfaceVariant\n      : color(black).alpha(0.54).rgb().string();\n\n    return (\n      <IconButton\n        size={size}\n        onPress={onPress}\n        iconColor={actionIconColor}\n        icon={icon}\n        disabled={disabled}\n        accessibilityLabel={accessibilityLabel}\n        animated\n        ref={ref}\n        rippleColor={rippleColor}\n        {...rest}\n      />\n    );\n  }\n);\n\nAppbarAction.displayName = 'Appbar.Action';\n\nexport default AppbarAction;\n\n// @component-docs ignore-next-line\nexport { AppbarAction };\n"], "mappings": ";;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAS9B,OAAOC,KAAK,MAAM,OAAO;AAGzB,SAASC,gBAAgB;AACzB,SAASC,KAAK;AACd,SAASC,UAAU;AAEnB,OAAOC,UAAU;AAmEjB,IAAMC,YAAY,GAAGF,UAAU,CAC7B,UAAAG,IAAA,EAaEC,GAAG,EACA;EAAA,IAAAC,SAAA,GAAAF,IAAA,CAZDG,IAAI;IAAJA,IAAI,GAAAD,SAAA,cAAG,EAAE,GAAAA,SAAA;IACFE,SAAS,GAAAJ,IAAA,CAAhBN,KAAK;IACLW,IAAI,GAAAL,IAAA,CAAJK,IAAI;IACJC,QAAQ,GAAAN,IAAA,CAARM,QAAQ;IACRC,OAAO,GAAAP,IAAA,CAAPO,OAAO;IACPC,kBAAkB,GAAAR,IAAA,CAAlBQ,kBAAkB;IAClBC,SAAS,GAAAT,IAAA,CAATS,SAAS;IACFC,cAAc,GAAAV,IAAA,CAArBW,KAAK;IACLC,WAAW,GAAAZ,IAAA,CAAXY,WAAW;IACRC,IAAA,GAAAC,wBAAA,CAAAd,IAAA,EAAAe,SAAA;EAIL,IAAMJ,KAAK,GAAGhB,gBAAgB,CAACe,cAAc,CAAC;EAE9C,IAAMM,eAAe,GAAGZ,SAAS,GAC7BA,SAAS,GACTO,KAAK,CAACM,IAAI,GACVR,SAAS,GACPE,KAAK,CAACO,MAAM,CAACC,SAAS,GACtBR,KAAK,CAACO,MAAM,CAACE,gBAAgB,GAC/B1B,KAAK,CAACE,KAAK,CAAC,CAACyB,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EAE3C,OACE9B,KAAA,CAAA+B,aAAA,CAAC1B,UAAU,EAAA2B,QAAA;IACTtB,IAAI,EAAEA,IAAK;IACXI,OAAO,EAAEA,OAAQ;IACjBH,SAAS,EAAEY,eAAgB;IAC3BX,IAAI,EAAEA,IAAK;IACXC,QAAQ,EAAEA,QAAS;IACnBE,kBAAkB,EAAEA,kBAAmB;IACvCkB,QAAQ;IACRzB,GAAG,EAAEA,GAAI;IACTW,WAAW,EAAEA;EAAY,GACrBC,IAAI,CACT,CAAC;AAEN,CACF,CAAC;AAEDd,YAAY,CAAC4B,WAAW,GAAG,eAAe;AAE1C,eAAe5B,YAAY;AAG3B,SAASA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}