import React, { useState } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Alert } from 'react-native';
import { Button } from 'react-native-paper';
import PageTitle from '../../components/PageTitle';
import Wrapper from '../../components/Wrapper';

const CryptoComparison = () => {
  const [crypto1, setCrypto1] = useState('Bitcoin');
  const [crypto2, setCrypto2] = useState('Ethereum');

  const cryptoData = {
    Bitcoin: { price: '$45,000', change: '+2.5%', marketCap: '$850B' },
    Ethereum: { price: '$3,200', change: '+1.8%', marketCap: '$380B' },
    Cardano: { price: '$0.45', change: '-0.5%', marketCap: '$15B' },
    Solana: { price: '$95', change: '+5.2%', marketCap: '$40B' },
  };

  const cryptoOptions = ['Bitcoin', 'Ethereum', 'Cardano', 'Solana'];

  const handleCompare = () => {
    Alert.alert("Comparison", `Comparing ${crypto1} vs ${crypto2}`);
  };

  return (
    <Wrapper>
      <PageTitle text="Compare Cryptocurrencies" />
      <ScrollView style={{ padding: 16 }}>
        <Text style={{ color: '#fff', fontSize: 18, marginBottom: 20, textAlign: 'center' }}>
          Select two cryptocurrencies to compare:
        </Text>

        {/* Crypto 1 Selection */}
        <View style={{ marginBottom: 20 }}>
          <Text style={{ color: '#FECB37', fontSize: 16, marginBottom: 10 }}>First Cryptocurrency:</Text>
          <View style={{ flexDirection: 'row', flexWrap: 'wrap' }}>
            {cryptoOptions.map((crypto) => (
              <TouchableOpacity
                key={crypto}
                onPress={() => setCrypto1(crypto)}
                style={{
                  backgroundColor: crypto1 === crypto ? '#FECB37' : '#2a2a2a',
                  padding: 10,
                  margin: 5,
                  borderRadius: 8,
                }}
              >
                <Text style={{ color: crypto1 === crypto ? '#000' : '#fff' }}>{crypto}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Crypto 2 Selection */}
        <View style={{ marginBottom: 20 }}>
          <Text style={{ color: '#FECB37', fontSize: 16, marginBottom: 10 }}>Second Cryptocurrency:</Text>
          <View style={{ flexDirection: 'row', flexWrap: 'wrap' }}>
            {cryptoOptions.map((crypto) => (
              <TouchableOpacity
                key={crypto}
                onPress={() => setCrypto2(crypto)}
                style={{
                  backgroundColor: crypto2 === crypto ? '#FECB37' : '#2a2a2a',
                  padding: 10,
                  margin: 5,
                  borderRadius: 8,
                }}
              >
                <Text style={{ color: crypto2 === crypto ? '#000' : '#fff' }}>{crypto}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Comparison Results */}
        <View style={{ marginTop: 20 }}>
          <Text style={{ color: '#fff', fontSize: 18, marginBottom: 16, textAlign: 'center' }}>
            Comparison Results
          </Text>

          <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
            {/* Crypto 1 Data */}
            <View style={{ flex: 1, backgroundColor: '#2a2a2a', padding: 16, marginRight: 8, borderRadius: 8 }}>
              <Text style={{ color: '#FECB37', fontSize: 16, fontWeight: 'bold', textAlign: 'center' }}>{crypto1}</Text>
              <Text style={{ color: '#fff', marginTop: 8 }}>Price: {cryptoData[crypto1]?.price}</Text>
              <Text style={{ color: '#fff' }}>Change: {cryptoData[crypto1]?.change}</Text>
              <Text style={{ color: '#fff' }}>Market Cap: {cryptoData[crypto1]?.marketCap}</Text>
            </View>

            {/* Crypto 2 Data */}
            <View style={{ flex: 1, backgroundColor: '#2a2a2a', padding: 16, marginLeft: 8, borderRadius: 8 }}>
              <Text style={{ color: '#FECB37', fontSize: 16, fontWeight: 'bold', textAlign: 'center' }}>{crypto2}</Text>
              <Text style={{ color: '#fff', marginTop: 8 }}>Price: {cryptoData[crypto2]?.price}</Text>
              <Text style={{ color: '#fff' }}>Change: {cryptoData[crypto2]?.change}</Text>
              <Text style={{ color: '#fff' }}>Market Cap: {cryptoData[crypto2]?.marketCap}</Text>
            </View>
          </View>
        </View>

        <Button mode="contained" onPress={handleCompare} style={{ marginTop: 20 }}>
          Detailed Comparison
        </Button>
      </ScrollView>
    </Wrapper>
  );
};

export default CryptoComparison;
