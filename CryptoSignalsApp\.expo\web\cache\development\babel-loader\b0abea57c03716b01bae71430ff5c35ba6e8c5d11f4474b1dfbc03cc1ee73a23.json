{"ast": null, "code": "import StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: '#fff'\n  },\n  themeToggle: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n    padding: 20,\n    borderBottomWidth: 1,\n    borderBottomColor: '#eee'\n  },\n  themeToggleText: {\n    fontSize: 16,\n    fontWeight: 'bold'\n  },\n  privacySwitch: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n    padding: 20,\n    borderBottomWidth: 1,\n    borderBottomColor: '#eee'\n  },\n  privacyLabel: {\n    fontSize: 16\n  },\n  notificationSwitch: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n    padding: 20,\n    borderBottomWidth: 1,\n    borderBottomColor: '#eee'\n  },\n  notificationLabel: {\n    fontSize: 16\n  },\n  securitySection: {\n    marginTop: 10\n  },\n  linkOption: {\n    padding: 20,\n    borderBottomWidth: 1,\n    borderBottomColor: '#eee'\n  },\n  linkOptionText: {\n    fontSize: 16,\n    color: '#007BFF'\n  },\n  cancelSubscriptionButton: {\n    backgroundColor: '#dc3545',\n    padding: 12,\n    borderRadius: 4,\n    alignItems: 'center',\n    marginTop: 16,\n    marginHorizontal: 20\n  },\n  cancelSubscriptionText: {\n    color: '#fff',\n    fontSize: 16\n  }\n});\nexport default styles;", "map": {"version": 3, "names": ["styles", "StyleSheet", "create", "container", "flex", "backgroundColor", "themeToggle", "flexDirection", "justifyContent", "alignItems", "padding", "borderBottomWidth", "borderBottomColor", "themeToggleText", "fontSize", "fontWeight", "privacySwitch", "privacyLabel", "notificationSwitch", "notificationLabel", "securitySection", "marginTop", "linkOption", "linkOptionText", "color", "cancelSubscriptionButton", "borderRadius", "marginHorizontal", "cancelSubscriptionText"], "sources": ["E:/CryptoSignalsApp/src/pages/Settings/styles.js"], "sourcesContent": ["import { StyleSheet } from 'react-native';\r\n\r\nconst styles = StyleSheet.create({\r\n  container: {\r\n    flex: 1,\r\n    backgroundColor: '#fff',\r\n  },\r\n  themeToggle: {\r\n    flexDirection: 'row',\r\n    justifyContent: 'space-between',\r\n    alignItems: 'center',\r\n    padding: 20,\r\n    borderBottomWidth: 1,\r\n    borderBottomColor: '#eee',\r\n  },\r\n  themeToggleText: {\r\n    fontSize: 16,\r\n    fontWeight: 'bold',  // adicionado negrito para títulos de seções\r\n  },\r\n  privacySwitch: {\r\n    flexDirection: 'row',\r\n    justifyContent: 'space-between',\r\n    alignItems: 'center',\r\n    padding: 20,\r\n    borderBottomWidth: 1,\r\n    borderBottomColor: '#eee',\r\n  },\r\n  privacyLabel: {\r\n    fontSize: 16,\r\n  },\r\n  notificationSwitch: {\r\n    flexDirection: 'row',\r\n    justifyContent: 'space-between',\r\n    alignItems: 'center',\r\n    padding: 20,\r\n    borderBottomWidth: 1,\r\n    borderBottomColor: '#eee',\r\n  },\r\n  notificationLabel: {\r\n    fontSize: 16,\r\n  },\r\n  securitySection: {  // Estilo para a seção \"Segurança e Privacidade\"\r\n    marginTop: 10,\r\n  },\r\n  linkOption: {  // Estilo para os links \"Política de privacidade\" e \"Termos de serviço\"\r\n    padding: 20,\r\n    borderBottomWidth: 1,\r\n    borderBottomColor: '#eee',\r\n  },\r\n  linkOptionText: {\r\n    fontSize: 16,\r\n    color: '#007BFF',  // azul padrão de link\r\n  },\r\n  cancelSubscriptionButton: {\r\n    backgroundColor: '#dc3545',\r\n    padding: 12,\r\n    borderRadius: 4,\r\n    alignItems: 'center',\r\n    marginTop: 16,\r\n    marginHorizontal: 20,\r\n  },\r\n  cancelSubscriptionText: {\r\n    color: '#fff',\r\n    fontSize: 16,\r\n  },\r\n});\r\n\r\nexport default styles;\r\n"], "mappings": ";AAEA,IAAMA,MAAM,GAAGC,UAAU,CAACC,MAAM,CAAC;EAC/BC,SAAS,EAAE;IACTC,IAAI,EAAE,CAAC;IACPC,eAAe,EAAE;EACnB,CAAC;EACDC,WAAW,EAAE;IACXC,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE,eAAe;IAC/BC,UAAU,EAAE,QAAQ;IACpBC,OAAO,EAAE,EAAE;IACXC,iBAAiB,EAAE,CAAC;IACpBC,iBAAiB,EAAE;EACrB,CAAC;EACDC,eAAe,EAAE;IACfC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE;EACd,CAAC;EACDC,aAAa,EAAE;IACbT,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE,eAAe;IAC/BC,UAAU,EAAE,QAAQ;IACpBC,OAAO,EAAE,EAAE;IACXC,iBAAiB,EAAE,CAAC;IACpBC,iBAAiB,EAAE;EACrB,CAAC;EACDK,YAAY,EAAE;IACZH,QAAQ,EAAE;EACZ,CAAC;EACDI,kBAAkB,EAAE;IAClBX,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE,eAAe;IAC/BC,UAAU,EAAE,QAAQ;IACpBC,OAAO,EAAE,EAAE;IACXC,iBAAiB,EAAE,CAAC;IACpBC,iBAAiB,EAAE;EACrB,CAAC;EACDO,iBAAiB,EAAE;IACjBL,QAAQ,EAAE;EACZ,CAAC;EACDM,eAAe,EAAE;IACfC,SAAS,EAAE;EACb,CAAC;EACDC,UAAU,EAAE;IACVZ,OAAO,EAAE,EAAE;IACXC,iBAAiB,EAAE,CAAC;IACpBC,iBAAiB,EAAE;EACrB,CAAC;EACDW,cAAc,EAAE;IACdT,QAAQ,EAAE,EAAE;IACZU,KAAK,EAAE;EACT,CAAC;EACDC,wBAAwB,EAAE;IACxBpB,eAAe,EAAE,SAAS;IAC1BK,OAAO,EAAE,EAAE;IACXgB,YAAY,EAAE,CAAC;IACfjB,UAAU,EAAE,QAAQ;IACpBY,SAAS,EAAE,EAAE;IACbM,gBAAgB,EAAE;EACpB,CAAC;EACDC,sBAAsB,EAAE;IACtBJ,KAAK,EAAE,MAAM;IACbV,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC;AAEF,eAAed,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}