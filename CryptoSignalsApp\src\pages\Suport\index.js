import React, { useState } from 'react';
import { View, Text, TextInput, Button, Alert, ScrollView, TouchableOpacity } from 'react-native';
import styles from './styles';

const FAQs = [
  { question: 'Como redefino minha senha?', answer: 'Vá para a página de configurações e clique em "Redefinir senha".' },
  { question: 'Onde posso encontrar meus recibos?', answer: 'Todos os seus recibos estão na seção "Histórico de Pagamento".' },
  // ... você pode adicionar mais FAQs aqui
];

const Support = () => {
  const [message, setMessage] = useState('');

  const handleSubmit = () => {
    if (message.trim() === '') {
      Alert.alert('Erro', 'Por favor, digite sua mensagem de suporte.');
      return;
    }

    Alert.alert('Sucesso', 'Sua mensagem de suporte foi enviada com sucesso.');
    setMessage('');
  };

  const handleDirectContact = () => {
    // Aqui, você pode adicionar funcionalidade para abrir um chat ao vivo, ligação, etc.
    Alert.alert('Contato Direto', 'Você iniciou um contato direto com o suporte.');
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Suporte ao Cliente</Text>
      
      <Text style={styles.sectionTitle}>FAQs</Text>
      {FAQs.map((faq, index) => (
        <View key={index} style={styles.faqContainer}>
          <Text style={styles.faqQuestion}>{faq.question}</Text>
          <Text style={styles.faqAnswer}>{faq.answer}</Text>
        </View>
      ))}

      <Text style={styles.sectionTitle}>Contato com o suporte</Text>
      <Text style={styles.subtitle}>Envie-nos uma mensagem e entraremos em contato com você em breve.</Text>

      <TextInput
        style={styles.input}
        placeholder="Digite sua mensagem aqui"
        multiline
        numberOfLines={5}
        value={message}
        onChangeText={(text) => setMessage(text)}
      />

      <Button title="Enviar Mensagem" onPress={handleSubmit} />

      <TouchableOpacity style={styles.directContactButton} onPress={handleDirectContact}>
        <Text style={styles.directContactText}>Contato Direto com o Suporte</Text>
      </TouchableOpacity>

    </ScrollView>
  );
};

export default Support;
