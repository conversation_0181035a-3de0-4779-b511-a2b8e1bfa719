{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport React, { useContext, useEffect } from 'react';\nimport { StoreContext } from \"../../../store/index\";\nimport { setSubscription } from \"../../../store/actions\";\nimport { getSecureStoreItem } from \"../../../services/secureStore\";\nimport { AxiosContext } from \"../../../store/axios\";\nimport { Fragment as _Fragment, jsx as _jsx } from \"react/jsx-runtime\";\nvar Subscription = function Subscription(_ref) {\n  var children = _ref.children;\n  var _useContext = useContext(StoreContext),\n    _useContext2 = _slicedToArray(_useContext, 2),\n    state = _useContext2[0],\n    dispatch = _useContext2[1];\n  var _useContext3 = useContext(AxiosContext),\n    _useContext4 = _slicedToArray(_useContext3, 1),\n    api = _useContext4[0];\n  var _state$subscription = state.subscription,\n    customerId = _state$subscription.customerId,\n    subscriptionPeriodEnd = _state$subscription.subscriptionPeriodEnd;\n  useEffect(function () {\n    loadSubscription();\n  }, []);\n  var loadSubscription = function () {\n    var _ref2 = _asyncToGenerator(function* () {\n      if (!customerId) {\n        var subscription = yield getSecureStoreItem('subscription');\n        if (!subscription) {\n          return;\n        }\n        if (isExpiredDay(subscription.subscriptionPeriodEnd)) {\n          yield getSubscriptionStatus(subscription);\n          return;\n        }\n        setSubscription(dispatch, subscription);\n      }\n      if (customerId && !subscriptionPeriodEnd) {\n        var _subscription = yield getSecureStoreItem('subscription');\n        yield getSubscriptionStatus(_subscription);\n      }\n    });\n    return function loadSubscription() {\n      return _ref2.apply(this, arguments);\n    };\n  }();\n  var isExpiredDay = function isExpiredDay(endDate) {\n    var dateNow = new Date();\n    var subscriptionDateEnd = new Date(endDate);\n    return dateNow >= subscriptionDateEnd;\n  };\n  var getSubscriptionStatus = function () {\n    var _ref3 = _asyncToGenerator(function* (subscription) {\n      var _yield$api$get = yield api.get('/stripe-get-subscription-status/' + subscription.customerId),\n        _yield$api$get$data = _yield$api$get.data,\n        subscriptionStatus = _yield$api$get$data.subscriptionStatus,\n        subscriptionPeriodEnd = _yield$api$get$data.subscriptionPeriodEnd,\n        accessToken = _yield$api$get$data.accessToken;\n      subscription.subscriptionStatus = subscriptionStatus;\n      subscription.subscriptionPeriodEnd = subscriptionPeriodEnd;\n      subscription.accessToken = accessToken;\n      setSubscription(dispatch, subscription);\n    });\n    return function getSubscriptionStatus(_x) {\n      return _ref3.apply(this, arguments);\n    };\n  }();\n  return _jsx(_Fragment, {\n    children: children\n  });\n};\nexport default Subscription;", "map": {"version": 3, "names": ["React", "useContext", "useEffect", "StoreContext", "setSubscription", "getSecureStoreItem", "AxiosContext", "Fragment", "_Fragment", "jsx", "_jsx", "Subscription", "_ref", "children", "_useContext", "_useContext2", "_slicedToArray", "state", "dispatch", "_useContext3", "_useContext4", "api", "_state$subscription", "subscription", "customerId", "subscriptionPeriodEnd", "loadSubscription", "_ref2", "_asyncToGenerator", "isExpiredDay", "getSubscriptionStatus", "apply", "arguments", "endDate", "dateNow", "Date", "subscriptionDateEnd", "_ref3", "_yield$api$get", "get", "_yield$api$get$data", "data", "subscriptionStatus", "accessToken", "_x"], "sources": ["E:/CryptoSignalsApp/src/components/Wrapper/Subscription/index.js"], "sourcesContent": ["import React, { useContext, useEffect } from 'react';\r\nimport { StoreContext } from '../../../store/index';\r\nimport { setSubscription } from '../../../store/actions';\r\nimport { getSecureStoreItem } from '../../../services/secureStore';\r\nimport { AxiosContext } from '../../../store/axios';\r\n\r\nconst Subscription = ({ children }) => {\r\n  const [state, dispatch] = useContext(StoreContext);\r\n  const [api] = useContext(AxiosContext);\r\n  const { subscription: { customerId, subscriptionPeriodEnd } } = state;\r\n\r\n  useEffect(() => {\r\n    loadSubscription();\r\n  }, []);\r\n\r\n  const loadSubscription = async () => {\r\n    if (!customerId) {\r\n      let subscription = await getSecureStoreItem('subscription');\r\n\r\n      if (!subscription) {\r\n        return;\r\n      }\r\n\r\n      if (isExpiredDay(subscription.subscriptionPeriodEnd)) {\r\n        await getSubscriptionStatus(subscription);\r\n        return;\r\n      }\r\n\r\n      setSubscription(dispatch, subscription);\r\n    }\r\n\r\n    if (customerId && !subscriptionPeriodEnd) {\r\n      let subscription = await getSecureStoreItem('subscription');\r\n      await getSubscriptionStatus(subscription);\r\n    }\r\n  }\r\n\r\n  const isExpiredDay = (endDate) => {\r\n    let dateNow = new Date();\r\n    let subscriptionDateEnd = new Date(endDate);\r\n\r\n    return dateNow >= subscriptionDateEnd;\r\n  }\r\n\r\n  const getSubscriptionStatus = async (subscription) => {\r\n    const {\r\n      data: {\r\n        subscriptionStatus,\r\n        subscriptionPeriodEnd,\r\n        accessToken\r\n      }\r\n    } = await api.get('/stripe-get-subscription-status/' + subscription.customerId);\r\n\r\n    subscription.subscriptionStatus = subscriptionStatus;\r\n    subscription.subscriptionPeriodEnd = subscriptionPeriodEnd;\r\n    subscription.accessToken = accessToken;\r\n\r\n    setSubscription(dispatch, subscription);\r\n  }\r\n\r\n  return (\r\n    <>\r\n      {children}\r\n    </>\r\n  )\r\n}\r\n\r\nexport default Subscription;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AACpD,SAASC,YAAY;AACrB,SAASC,eAAe;AACxB,SAASC,kBAAkB;AAC3B,SAASC,YAAY;AAA+B,SAAAC,QAAA,IAAAC,SAAA,EAAAC,GAAA,IAAAC,IAAA;AAEpD,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAAC,IAAA,EAAqB;EAAA,IAAfC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;EAC9B,IAAAC,WAAA,GAA0Bb,UAAU,CAACE,YAAY,CAAC;IAAAY,YAAA,GAAAC,cAAA,CAAAF,WAAA;IAA3CG,KAAK,GAAAF,YAAA;IAAEG,QAAQ,GAAAH,YAAA;EACtB,IAAAI,YAAA,GAAclB,UAAU,CAACK,YAAY,CAAC;IAAAc,YAAA,GAAAJ,cAAA,CAAAG,YAAA;IAA/BE,GAAG,GAAAD,YAAA;EACV,IAAAE,mBAAA,GAAgEL,KAAK,CAA7DM,YAAY;IAAIC,UAAU,GAAAF,mBAAA,CAAVE,UAAU;IAAEC,qBAAqB,GAAAH,mBAAA,CAArBG,qBAAqB;EAEzDvB,SAAS,CAAC,YAAM;IACdwB,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAEN,IAAMA,gBAAgB;IAAA,IAAAC,KAAA,GAAAC,iBAAA,CAAG,aAAY;MACnC,IAAI,CAACJ,UAAU,EAAE;QACf,IAAID,YAAY,SAASlB,kBAAkB,CAAC,cAAc,CAAC;QAE3D,IAAI,CAACkB,YAAY,EAAE;UACjB;QACF;QAEA,IAAIM,YAAY,CAACN,YAAY,CAACE,qBAAqB,CAAC,EAAE;UACpD,MAAMK,qBAAqB,CAACP,YAAY,CAAC;UACzC;QACF;QAEAnB,eAAe,CAACc,QAAQ,EAAEK,YAAY,CAAC;MACzC;MAEA,IAAIC,UAAU,IAAI,CAACC,qBAAqB,EAAE;QACxC,IAAIF,aAAY,SAASlB,kBAAkB,CAAC,cAAc,CAAC;QAC3D,MAAMyB,qBAAqB,CAACP,aAAY,CAAC;MAC3C;IACF,CAAC;IAAA,gBApBKG,gBAAgBA,CAAA;MAAA,OAAAC,KAAA,CAAAI,KAAA,OAAAC,SAAA;IAAA;EAAA,GAoBrB;EAED,IAAMH,YAAY,GAAG,SAAfA,YAAYA,CAAII,OAAO,EAAK;IAChC,IAAIC,OAAO,GAAG,IAAIC,IAAI,CAAC,CAAC;IACxB,IAAIC,mBAAmB,GAAG,IAAID,IAAI,CAACF,OAAO,CAAC;IAE3C,OAAOC,OAAO,IAAIE,mBAAmB;EACvC,CAAC;EAED,IAAMN,qBAAqB;IAAA,IAAAO,KAAA,GAAAT,iBAAA,CAAG,WAAOL,YAAY,EAAK;MACpD,IAAAe,cAAA,SAMUjB,GAAG,CAACkB,GAAG,CAAC,kCAAkC,GAAGhB,YAAY,CAACC,UAAU,CAAC;QAAAgB,mBAAA,GAAAF,cAAA,CAL7EG,IAAI;QACFC,kBAAkB,GAAAF,mBAAA,CAAlBE,kBAAkB;QAClBjB,qBAAqB,GAAAe,mBAAA,CAArBf,qBAAqB;QACrBkB,WAAW,GAAAH,mBAAA,CAAXG,WAAW;MAIfpB,YAAY,CAACmB,kBAAkB,GAAGA,kBAAkB;MACpDnB,YAAY,CAACE,qBAAqB,GAAGA,qBAAqB;MAC1DF,YAAY,CAACoB,WAAW,GAAGA,WAAW;MAEtCvC,eAAe,CAACc,QAAQ,EAAEK,YAAY,CAAC;IACzC,CAAC;IAAA,gBAdKO,qBAAqBA,CAAAc,EAAA;MAAA,OAAAP,KAAA,CAAAN,KAAA,OAAAC,SAAA;IAAA;EAAA,GAc1B;EAED,OACEtB,IAAA,CAAAF,SAAA;IAAAK,QAAA,EACGA;EAAQ,CACT,CAAC;AAEP,CAAC;AAED,eAAeF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}