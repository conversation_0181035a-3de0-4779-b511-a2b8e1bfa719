{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport color from 'color';\nimport { black, white } from \"../../styles/themes/v2/colors\";\nexport var getCardCoverStyle = function getCardCoverStyle(_ref) {\n  var theme = _ref.theme,\n    index = _ref.index,\n    total = _ref.total,\n    borderRadiusStyles = _ref.borderRadiusStyles;\n  var isV3 = theme.isV3,\n    roundness = theme.roundness;\n  if (Object.keys(borderRadiusStyles).length > 0) {\n    return _objectSpread({\n      borderRadius: 3 * roundness\n    }, borderRadiusStyles);\n  }\n  if (isV3) {\n    return {\n      borderRadius: 3 * roundness\n    };\n  }\n  if (index === 0) {\n    if (total === 1) {\n      return {\n        borderRadius: roundness\n      };\n    }\n    return {\n      borderTopLeftRadius: roundness,\n      borderTopRightRadius: roundness\n    };\n  }\n  if (typeof total === 'number' && index === total - 1) {\n    return {\n      borderBottomLeftRadius: roundness\n    };\n  }\n  return undefined;\n};\nvar getBorderColor = function getBorderColor(_ref2) {\n  var theme = _ref2.theme;\n  if (theme.isV3) {\n    return theme.colors.outline;\n  }\n  if (theme.dark) {\n    return color(white).alpha(0.12).rgb().string();\n  }\n  return color(black).alpha(0.12).rgb().string();\n};\nvar getBackgroundColor = function getBackgroundColor(_ref3) {\n  var theme = _ref3.theme,\n    isMode = _ref3.isMode;\n  if (theme.isV3) {\n    if (isMode('contained')) {\n      return theme.colors.surfaceVariant;\n    }\n    if (isMode('outlined')) {\n      return theme.colors.surface;\n    }\n  }\n  return undefined;\n};\nexport var getCardColors = function getCardColors(_ref4) {\n  var theme = _ref4.theme,\n    mode = _ref4.mode;\n  var isMode = function isMode(modeToCompare) {\n    return mode === modeToCompare;\n  };\n  return {\n    backgroundColor: getBackgroundColor({\n      theme: theme,\n      isMode: isMode\n    }),\n    borderColor: getBorderColor({\n      theme: theme\n    })\n  };\n};", "map": {"version": 3, "names": ["color", "black", "white", "getCardCoverStyle", "_ref", "theme", "index", "total", "borderRadiusStyles", "isV3", "roundness", "Object", "keys", "length", "_objectSpread", "borderRadius", "borderTopLeftRadius", "borderTopRightRadius", "borderBottomLeftRadius", "undefined", "getBorderColor", "_ref2", "colors", "outline", "dark", "alpha", "rgb", "string", "getBackgroundColor", "_ref3", "isMode", "surfaceVariant", "surface", "getCardColors", "_ref4", "mode", "modeToCompare", "backgroundColor", "borderColor"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\Card\\utils.tsx"], "sourcesContent": ["import type { StyleProp, ViewStyle } from 'react-native';\n\nimport color from 'color';\n\nimport { black, white } from '../../styles/themes/v2/colors';\nimport type { InternalTheme } from '../../types';\n\ntype CardMode = 'elevated' | 'outlined' | 'contained';\n\ntype BorderRadiusStyles = Pick<\n  ViewStyle,\n  Extract<keyof ViewStyle, `border${string}Radius`>\n>;\n\nexport type CardActionChildProps = {\n  compact?: boolean;\n  mode?: string;\n  style?: StyleProp<ViewStyle>;\n};\n\nexport const getCardCoverStyle = ({\n  theme,\n  index,\n  total,\n  borderRadiusStyles,\n}: {\n  theme: InternalTheme;\n  borderRadiusStyles: BorderRadiusStyles;\n  index?: number;\n  total?: number;\n}) => {\n  const { isV3, roundness } = theme;\n\n  if (Object.keys(borderRadiusStyles).length > 0) {\n    return {\n      borderRadius: 3 * roundness,\n      ...borderRadiusStyles,\n    };\n  }\n\n  if (isV3) {\n    return {\n      borderRadius: 3 * roundness,\n    };\n  }\n\n  if (index === 0) {\n    if (total === 1) {\n      return {\n        borderRadius: roundness,\n      };\n    }\n\n    return {\n      borderTopLeftRadius: roundness,\n      borderTopRightRadius: roundness,\n    };\n  }\n\n  if (typeof total === 'number' && index === total - 1) {\n    return {\n      borderBottomLeftRadius: roundness,\n    };\n  }\n\n  return undefined;\n};\n\nconst getBorderColor = ({ theme }: { theme: InternalTheme }) => {\n  if (theme.isV3) {\n    return theme.colors.outline;\n  }\n\n  if (theme.dark) {\n    return color(white).alpha(0.12).rgb().string();\n  }\n  return color(black).alpha(0.12).rgb().string();\n};\n\nconst getBackgroundColor = ({\n  theme,\n  isMode,\n}: {\n  theme: InternalTheme;\n  isMode: (mode: CardMode) => boolean;\n}) => {\n  if (theme.isV3) {\n    if (isMode('contained')) {\n      return theme.colors.surfaceVariant;\n    }\n    if (isMode('outlined')) {\n      return theme.colors.surface;\n    }\n  }\n  return undefined;\n};\n\nexport const getCardColors = ({\n  theme,\n  mode,\n}: {\n  theme: InternalTheme;\n  mode: CardMode;\n}) => {\n  const isMode = (modeToCompare: CardMode) => {\n    return mode === modeToCompare;\n  };\n\n  return {\n    backgroundColor: getBackgroundColor({\n      theme,\n      isMode,\n    }),\n    borderColor: getBorderColor({ theme }),\n  };\n};\n"], "mappings": ";;;AAEA,OAAOA,KAAK,MAAM,OAAO;AAEzB,SAASC,KAAK,EAAEC,KAAK;AAgBrB,OAAO,IAAMC,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAAC,IAAA,EAUxB;EAAA,IATJC,KAAK,GAAAD,IAAA,CAALC,KAAK;IACLC,KAAK,GAAAF,IAAA,CAALE,KAAK;IACLC,KAAK,GAAAH,IAAA,CAALG,KAAK;IACLC,kBAAA,GAAAJ,IAAA,CAAAI,kBAAA;EAOA,IAAQC,IAAI,GAAgBJ,KAAK,CAAzBI,IAAI;IAAEC,SAAA,GAAcL,KAAK,CAAnBK,SAAA;EAEd,IAAIC,MAAM,CAACC,IAAI,CAACJ,kBAAkB,CAAC,CAACK,MAAM,GAAG,CAAC,EAAE;IAC9C,OAAAC,aAAA;MACEC,YAAY,EAAE,CAAC,GAAGL;IAAS,GACxBF,kBAAA;EAEP;EAEA,IAAIC,IAAI,EAAE;IACR,OAAO;MACLM,YAAY,EAAE,CAAC,GAAGL;IACpB,CAAC;EACH;EAEA,IAAIJ,KAAK,KAAK,CAAC,EAAE;IACf,IAAIC,KAAK,KAAK,CAAC,EAAE;MACf,OAAO;QACLQ,YAAY,EAAEL;MAChB,CAAC;IACH;IAEA,OAAO;MACLM,mBAAmB,EAAEN,SAAS;MAC9BO,oBAAoB,EAAEP;IACxB,CAAC;EACH;EAEA,IAAI,OAAOH,KAAK,KAAK,QAAQ,IAAID,KAAK,KAAKC,KAAK,GAAG,CAAC,EAAE;IACpD,OAAO;MACLW,sBAAsB,EAAER;IAC1B,CAAC;EACH;EAEA,OAAOS,SAAS;AAClB,CAAC;AAED,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAAAC,KAAA,EAA4C;EAAA,IAAtChB,KAAA,GAAAgB,KAAA,CAAAhB,KAAA;EACxB,IAAIA,KAAK,CAACI,IAAI,EAAE;IACd,OAAOJ,KAAK,CAACiB,MAAM,CAACC,OAAO;EAC7B;EAEA,IAAIlB,KAAK,CAACmB,IAAI,EAAE;IACd,OAAOxB,KAAK,CAACE,KAAK,CAAC,CAACuB,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EAChD;EACA,OAAO3B,KAAK,CAACC,KAAK,CAAC,CAACwB,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;AAChD,CAAC;AAED,IAAMC,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAAC,KAAA,EAMlB;EAAA,IALJxB,KAAK,GAAAwB,KAAA,CAALxB,KAAK;IACLyB,MAAA,GAAAD,KAAA,CAAAC,MAAA;EAKA,IAAIzB,KAAK,CAACI,IAAI,EAAE;IACd,IAAIqB,MAAM,CAAC,WAAW,CAAC,EAAE;MACvB,OAAOzB,KAAK,CAACiB,MAAM,CAACS,cAAc;IACpC;IACA,IAAID,MAAM,CAAC,UAAU,CAAC,EAAE;MACtB,OAAOzB,KAAK,CAACiB,MAAM,CAACU,OAAO;IAC7B;EACF;EACA,OAAOb,SAAS;AAClB,CAAC;AAED,OAAO,IAAMc,aAAa,GAAG,SAAhBA,aAAaA,CAAAC,KAAA,EAMpB;EAAA,IALJ7B,KAAK,GAAA6B,KAAA,CAAL7B,KAAK;IACL8B,IAAA,GAAAD,KAAA,CAAAC,IAAA;EAKA,IAAML,MAAM,GAAI,SAAVA,MAAMA,CAAIM,aAAuB,EAAK;IAC1C,OAAOD,IAAI,KAAKC,aAAa;EAC/B,CAAC;EAED,OAAO;IACLC,eAAe,EAAET,kBAAkB,CAAC;MAClCvB,KAAK,EAALA,KAAK;MACLyB,MAAA,EAAAA;IACF,CAAC,CAAC;IACFQ,WAAW,EAAElB,cAAc,CAAC;MAAEf,KAAA,EAAAA;IAAM,CAAC;EACvC,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}