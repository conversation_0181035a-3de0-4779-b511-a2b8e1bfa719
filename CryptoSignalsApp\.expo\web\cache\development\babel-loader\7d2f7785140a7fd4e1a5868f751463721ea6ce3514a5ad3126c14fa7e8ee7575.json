{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"to\", \"action\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport * as React from 'react';\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport useLinkProps from \"./useLinkProps\";\nexport default function Link(_ref) {\n  var to = _ref.to,\n    action = _ref.action,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var props = useLinkProps({\n    to: to,\n    action: action\n  });\n  var onPress = function onPress(e) {\n    if ('onPress' in rest) {\n      var _rest$onPress;\n      (_rest$onPress = rest.onPress) === null || _rest$onPress === void 0 ? void 0 : _rest$onPress.call(rest, e);\n    }\n    props.onPress(e);\n  };\n  return React.createElement(Text, _objectSpread(_objectSpread(_objectSpread({}, props), rest), Platform.select({\n    web: {\n      onClick: onPress\n    },\n    default: {\n      onPress: onPress\n    }\n  })));\n}", "map": {"version": 3, "names": ["React", "Platform", "Text", "useLinkProps", "Link", "_ref", "to", "action", "rest", "_objectWithoutProperties", "_excluded", "props", "onPress", "e", "_rest$onPress", "call", "createElement", "_objectSpread", "select", "web", "onClick", "default"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\@react-navigation\\native\\src\\Link.tsx"], "sourcesContent": ["import type { NavigationAction } from '@react-navigation/core';\nimport * as React from 'react';\nimport { GestureResponderEvent, Platform, Text, TextProps } from 'react-native';\n\nimport useLinkProps from './useLinkProps';\nimport type { To } from './useLinkTo';\n\ntype Props<ParamList extends ReactNavigation.RootParamList> = {\n  to: To<ParamList>;\n  action?: NavigationAction;\n  target?: string;\n  onPress?: (\n    e: React.MouseEvent<HTMLAnchorElement, MouseEvent> | GestureResponderEvent\n  ) => void;\n} & (Omit<TextProps, 'disabled'> & {\n  disabled?: boolean | null;\n  children: React.ReactNode;\n});\n\n/**\n * Component to render link to another screen using a path.\n * Uses an anchor tag on the web.\n *\n * @param props.to Absolute path to screen (e.g. `/feeds/hot`).\n * @param props.action Optional action to use for in-page navigation. By default, the path is parsed to an action based on linking config.\n * @param props.children Child elements to render the content.\n */\nexport default function Link<ParamList extends ReactNavigation.RootParamList>({\n  to,\n  action,\n  ...rest\n}: Props<ParamList>) {\n  const props = useLinkProps<ParamList>({ to, action });\n\n  const onPress = (\n    e: React.MouseEvent<HTMLAnchorElement, MouseEvent> | GestureResponderEvent\n  ) => {\n    if ('onPress' in rest) {\n      rest.onPress?.(e);\n    }\n\n    props.onPress(e);\n  };\n\n  return React.createElement(Text, {\n    ...props,\n    ...rest,\n    ...Platform.select({\n      web: { onClick: onPress } as any,\n      default: { onPress },\n    }),\n  });\n}\n"], "mappings": ";;;;;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAAA,OAAAC,IAAA;AAG9B,OAAOC,YAAY;AAuBnB,eAAe,SAASC,IAAIA,CAAAC,IAAA,EAIP;EAAA,IAHnBC,EAAE,GAGeD,IAAA,CAHjBC,EAAE;IACFC,MAAM,GAEWF,IAAA,CAFjBE,MAAM;IACHC,IAAA,GAAAC,wBAAA,CACcJ,IAAA,EAAAK,SAAA;EACjB,IAAMC,KAAK,GAAGR,YAAY,CAAY;IAAEG,EAAE,EAAFA,EAAE;IAAEC,MAAA,EAAAA;EAAO,CAAC,CAAC;EAErD,IAAMK,OAAO,GACX,SADIA,OAAOA,CACXC,CAA0E,EACvE;IACH,IAAI,SAAS,IAAIL,IAAI,EAAE;MAAA,IAAAM,aAAA;MACrB,CAAAA,aAAA,GAAAN,IAAI,CAACI,OAAO,cAAAE,aAAA,uBAAZA,aAAA,CAAAC,IAAA,CAAAP,IAAI,EAAWK,CAAC,CAAC;IACnB;IAEAF,KAAK,CAACC,OAAO,CAACC,CAAC,CAAC;EAClB,CAAC;EAED,OAAOb,KAAK,CAACgB,aAAa,CAACd,IAAI,EAAAe,aAAA,CAAAA,aAAA,CAAAA,aAAA,KAC1BN,KAAK,GACLH,IAAI,GACJP,QAAQ,CAACiB,MAAM,CAAC;IACjBC,GAAG,EAAE;MAAEC,OAAO,EAAER;IAAQ,CAAQ;IAChCS,OAAO,EAAE;MAAET,OAAA,EAAAA;IAAQ;EACrB,CAAC,EACF,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}