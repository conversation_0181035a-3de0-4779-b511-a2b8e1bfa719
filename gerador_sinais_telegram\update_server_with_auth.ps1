# Script de atualização do servidor CryptoSignals com suporte à autenticação do Telegram
# Uso: .\update_server_with_auth.ps1

# Configurações do servidor
$SERVER_IP = "**************"
$SERVER_USER = "root"
$SERVER_PASSWORD = "h4*ls:FtJw0e"
$PROJECT_PATH = "/opt/gerador_sinais_telegram"
$PHONE_NUMBER = "+5521982301476"

# Função para verificar se plink está disponível
function Test-Plink {
    try {
        $null = & plink -V 2>$null
        return $true
    }
    catch {
        Write-Host "❌ PuTTY plink não encontrado!" -ForegroundColor Red
        Write-Host "Baixe e instale o PuTTY de: https://www.putty.org/" -ForegroundColor Yellow
        return $false
    }
}

# Função para executar comandos no servidor
function Invoke-RemoteCommand {
    param(
        [string]$Command,
        [switch]$Interactive
    )
    
    Write-Host "🔄 Executando: $Command" -ForegroundColor Yellow
    
    if ($Interactive) {
        # Para comandos interativos
        & plink -ssh -pw $SERVER_PASSWORD "$SERVER_USER@$SERVER_IP" -t $Command
    } else {
        # Para comandos normais
        $result = & plink -ssh -batch -pw $SERVER_PASSWORD "$SERVER_USER@$SERVER_IP" $Command
        return $result
    }
}

# Função para verificar se precisa de autenticação do Telegram
function Test-TelegramAuthNeeded {
    Write-Host "🔍 Verificando se precisa de autenticação do Telegram..." -ForegroundColor Blue
    
    $logs = Invoke-RemoteCommand "journalctl -u gerador_sinais.service --no-pager -l -n 20"
    
    if ($logs -and ($logs -match "phone number|código|authentication|login|enter your phone")) {
        return $true
    }
    
    return $false
}

# Função para realizar autenticação do Telegram
function Start-TelegramAuth {
    Write-Host ""
    Write-Host "📱 INICIANDO AUTENTICAÇÃO DO TELEGRAM" -ForegroundColor Blue
    Write-Host "=" * 60
    Write-Host ""
    Write-Host "📋 INSTRUÇÕES IMPORTANTES:" -ForegroundColor Green
    Write-Host "1. O sistema vai abrir uma sessão SSH interativa"
    Write-Host "2. Quando pedir o número de telefone, digite: $PHONE_NUMBER"
    Write-Host "3. Aguarde receber o código no seu Telegram"
    Write-Host "4. Digite o código quando solicitado"
    Write-Host "5. Após a autenticação bem-sucedida, pressione Ctrl+C"
    Write-Host "6. O script continuará automaticamente"
    Write-Host ""
    Write-Host "📱 SEU NÚMERO: $PHONE_NUMBER" -ForegroundColor Yellow
    Write-Host ""
    
    Read-Host "Pressione ENTER para iniciar a autenticação interativa"
    
    Write-Host "🚀 Abrindo sessão SSH interativa..." -ForegroundColor Blue
    Write-Host "DICA: Use Ctrl+C para sair após a autenticação" -ForegroundColor Yellow
    Write-Host "=" * 60
    
    # Parar o serviço primeiro
    Invoke-RemoteCommand "systemctl stop gerador_sinais.service"
    
    # Comando para autenticação interativa
    $authCommand = "cd $PROJECT_PATH && source venv/bin/activate && python main.py"
    
    try {
        Write-Host "Conectando ao servidor para autenticação..." -ForegroundColor Blue
        Write-Host "Digite seu número quando solicitado: $PHONE_NUMBER" -ForegroundColor Yellow
        Write-Host ""
        
        # Executar comando interativo
        Invoke-RemoteCommand $authCommand -Interactive
        
        Write-Host ""
        Write-Host "✅ Sessão de autenticação finalizada!" -ForegroundColor Green
        
    }
    catch {
        Write-Host ""
        Write-Host "✅ Autenticação interrompida" -ForegroundColor Green
    }
}

# Função principal de atualização
function Update-Server {
    Write-Host "🚀 INICIANDO ATUALIZAÇÃO DO CRYPTOSIGNALS" -ForegroundColor Green
    Write-Host "=" * 60
    
    try {
        # 1. Parar o serviço
        Write-Host "🛑 Parando serviço CryptoSignals..." -ForegroundColor Blue
        Invoke-RemoteCommand "systemctl stop gerador_sinais.service"
        
        # 2. Fazer backup das configurações
        Write-Host "💾 Fazendo backup das configurações..." -ForegroundColor Blue
        $backupDate = Get-Date -Format "yyyyMMdd_HHmmss"
        Invoke-RemoteCommand "cd $PROJECT_PATH && cp .env .env.backup.$backupDate 2>/dev/null || echo 'Arquivo .env não encontrado'"
        
        # 3. Resolver problemas de merge e atualizar código
        Write-Host "📥 Resolvendo merge e atualizando código..." -ForegroundColor Blue
        Invoke-RemoteCommand "cd $PROJECT_PATH && git config pull.rebase false"
        Invoke-RemoteCommand "cd $PROJECT_PATH && git stash"
        
        $pullResult = Invoke-RemoteCommand "cd $PROJECT_PATH && git pull origin main"
        
        if ($LASTEXITCODE -ne 0) {
            Write-Host "⚠️ Erro no git pull, forçando atualização..." -ForegroundColor Yellow
            Invoke-RemoteCommand "cd $PROJECT_PATH && git fetch origin main"
            Invoke-RemoteCommand "cd $PROJECT_PATH && git reset --hard origin/main"
            Write-Host "✅ Código atualizado forçadamente" -ForegroundColor Green
        }
        
        # 4. Atualizar dependências
        Write-Host "📦 Atualizando dependências..." -ForegroundColor Blue
        Invoke-RemoteCommand "cd $PROJECT_PATH && source venv/bin/activate && pip install --upgrade pip"
        Invoke-RemoteCommand "cd $PROJECT_PATH && source venv/bin/activate && pip install pandas-ta finta --upgrade"
        
        # 5. Verificar se precisa de autenticação
        Write-Host "🔐 Verificando necessidade de autenticação..." -ForegroundColor Blue
        
        # Tentar iniciar o serviço
        Invoke-RemoteCommand "systemctl daemon-reload"
        Invoke-RemoteCommand "systemctl restart gerador_sinais.service"
        
        # Aguardar e verificar
        Start-Sleep -Seconds 3
        
        if (Test-TelegramAuthNeeded) {
            Write-Host "📱 AUTENTICAÇÃO DO TELEGRAM NECESSÁRIA!" -ForegroundColor Yellow
            Start-TelegramAuth
        } else {
            Write-Host "✅ Nenhuma autenticação necessária" -ForegroundColor Green
        }
        
        # 6. Reiniciar o serviço final
        Write-Host "🚀 Reiniciando serviço final..." -ForegroundColor Blue
        Invoke-RemoteCommand "systemctl restart gerador_sinais.service"
        
        # 7. Aguardar inicialização
        Write-Host "⏳ Aguardando inicialização..." -ForegroundColor Yellow
        Start-Sleep -Seconds 5
        
        # 8. Verificar status
        Write-Host "📊 Verificando status do serviço..." -ForegroundColor Blue
        $status = Invoke-RemoteCommand "systemctl status gerador_sinais.service --no-pager -l"
        
        if ($status -match "active \(running\)") {
            Write-Host "✅ Serviço está rodando!" -ForegroundColor Green
        } else {
            Write-Host "⚠️ Serviço pode não estar rodando corretamente" -ForegroundColor Yellow
        }
        
        # 9. Verificar logs recentes
        Write-Host "📋 Verificando logs recentes..." -ForegroundColor Blue
        Invoke-RemoteCommand "journalctl -u gerador_sinais.service --no-pager -l -n 15"
        
        Write-Host ""
        Write-Host "=" * 60 -ForegroundColor Green
        Write-Host "  ✅ ATUALIZAÇÃO CONCLUÍDA!" -ForegroundColor Green
        Write-Host "=" * 60 -ForegroundColor Green
        
        return $true
        
    }
    catch {
        Write-Host "❌ Erro durante atualização: $_" -ForegroundColor Red
        return $false
    }
}

# Função para mostrar comandos de monitoramento
function Show-MonitoringCommands {
    Write-Host ""
    Write-Host "📋 COMANDOS ÚTEIS PARA MONITORAMENTO:" -ForegroundColor Blue
    Write-Host ""
    Write-Host "Ver logs em tempo real:" -ForegroundColor Green
    Write-Host "  plink -ssh -pw $SERVER_PASSWORD $SERVER_USER@$SERVER_IP"
    Write-Host "  journalctl -u gerador_sinais.service -f"
    Write-Host ""
    Write-Host "Ver status do serviço:" -ForegroundColor Green
    Write-Host "  plink -ssh -pw $SERVER_PASSWORD $SERVER_USER@$SERVER_IP"
    Write-Host "  systemctl status gerador_sinais.service"
    Write-Host ""
    Write-Host "Reiniciar serviço:" -ForegroundColor Green
    Write-Host "  plink -ssh -pw $SERVER_PASSWORD $SERVER_USER@$SERVER_IP"
    Write-Host "  systemctl restart gerador_sinais.service"
    Write-Host ""
}

# Função principal
function Main {
    try {
        # Verificar dependências
        if (-not (Test-Plink)) {
            exit 1
        }
        
        $success = Update-Server
        
        if ($success) {
            Show-MonitoringCommands
            
            # Perguntar se quer ver logs
            $response = Read-Host "Deseja ver os logs em tempo real? (y/n)"
            if ($response -eq "y" -or $response -eq "Y") {
                Write-Host "📋 Abrindo logs em tempo real..." -ForegroundColor Blue
                & plink -ssh -pw $SERVER_PASSWORD "$SERVER_USER@$SERVER_IP" "journalctl -u gerador_sinais.service -f"
            }
            
            Write-Host "🎉 Script finalizado!" -ForegroundColor Green
        } else {
            Write-Host "❌ Atualização falhou!" -ForegroundColor Red
            exit 1
        }
        
    }
    catch {
        Write-Host ""
        Write-Host "⚠️ Script interrompido: $_" -ForegroundColor Yellow
    }
}

# Executar função principal
Main
