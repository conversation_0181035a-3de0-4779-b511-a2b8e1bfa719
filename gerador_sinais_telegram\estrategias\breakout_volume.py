import logging
import pandas as pd
import numpy as np
from config.settings import LEVERAGE

logger = logging.getLogger(__name__)

class BreakoutVolumeStrategy:
    def __init__(self, binance_handler):
        self.binance = binance_handler
        self.leverage = LEVERAGE

    def analyze_symbol(self, symbol, anomaly_hours=6):
        """
        Analisa um símbolo para verificar se há sinal de breakout com volume

        Returns:
            tuple: (signal_type, entry_price, stop_loss, take_profit) ou (None, None, None, None) se não houver sinal
        """
        try:
            # Verificar anomalia de volume
            if not self._check_volume_anomaly(symbol, anomaly_hours):
                return None, None, None, None

            # Verificar níveis de consolidação
            support, resistance = self._get_consolidation_levels(symbol)
            if support is None or resistance is None:
                return None, None, None, None

            # Verificar se o preço atual está acima da resistência (breakout)
            current_price = self.binance.get_current_price(symbol)
            if current_price is None:
                logger.warning(f"Não foi possível obter preço atual para {symbol}")
                return None, None, None, None

            # Validar se o preço atual é realista
            if not self._validate_price_range(symbol, current_price):
                logger.error(f"Preço atual irrealista para {symbol}: {current_price}")
                return None, None, None, None

            # Verificar se realmente há breakout (preço deve estar pelo menos 0.1% acima da resistência)
            breakout_threshold = resistance * 1.001  # 0.1% acima da resistência
            if current_price <= breakout_threshold:
                return None, None, None, None

            # Calcular stop loss e take profit com validação
            stop_loss = round(support * 0.998, 6)  # Ligeiramente abaixo do suporte

            # Validar se o stop loss faz sentido
            if stop_loss >= current_price:
                logger.error(f"Stop loss inválido para {symbol}: {stop_loss} >= {current_price}")
                return None, None, None, None

            # Calcular take profit com base em risco:recompensa de 1:2 (mais conservador)
            risk = current_price - stop_loss
            if risk <= 0:
                logger.error(f"Risco inválido para {symbol}: {risk}")
                return None, None, None, None

            take_profit = round(current_price + 2 * risk, 6)  # Mudado de 3x para 2x (mais realista)

            # Validar se o take profit é realista (não pode ser mais que 20% acima do preço atual)
            max_tp = current_price * 1.20
            if take_profit > max_tp:
                take_profit = round(max_tp, 6)
                logger.warning(f"Take profit ajustado para {symbol}: {take_profit} (máximo 20%)")

            # Como é um breakout de alta, o sinal é LONG
            signal_type = "LONG"
            logger.info(f"{symbol} - Sinal de breakout com volume gerado. Entrada: {current_price}, SL: {stop_loss}, TP: {take_profit}")

            # Garantir que todos os valores são float e válidos
            try:
                return signal_type, float(current_price), float(stop_loss), float(take_profit)
            except (ValueError, TypeError) as e:
                logger.error(f"Erro ao converter valores para float em {symbol}: {e}")
                return None, None, None, None

        except Exception as e:
            logger.error(f"Erro ao analisar {symbol} para breakout com volume: {e}")
            return None, None, None, None

    def _check_volume_anomaly(self, symbol, anomaly_hours=6):
        """
        Verifica se o volume acumulado nas últimas horas é superior
        ao maior volume diário registrado nos últimos 30 dias.
        """
        try:
            # Obter candles de 1h para as últimas horas
            klines_recent = self.binance.get_historical_klines(
                symbol=symbol,
                interval='1h',
                lookback_days=1
            )

            if klines_recent.empty or len(klines_recent) < anomaly_hours:
                return False

            # Calcular volume recente (últimas anomaly_hours)
            recent_volume = klines_recent['volume'].tail(anomaly_hours).sum()

            # Obter candles diários dos últimos 30 dias
            klines_daily = self.binance.get_historical_klines(
                symbol=symbol,
                interval='1d',
                lookback_days=30
            )

            if klines_daily.empty:
                return False

            # Encontrar o maior volume diário
            max_daily_volume = klines_daily['volume'].max()

            # Verificar se o volume recente é maior que o máximo diário
            is_anomaly = recent_volume > max_daily_volume

            if is_anomaly:
                logger.info(f"{symbol} - Anomalia de volume detectada! Volume recente: {recent_volume}, Máximo diário: {max_daily_volume}")

            return is_anomaly

        except Exception as e:
            logger.error(f"Erro ao verificar anomalia de volume para {symbol}: {e}")
            return False

    def _get_consolidation_levels(self, symbol, interval="15m", lookback=4):
        """
        Obtém níveis de suporte e resistência a partir dos candles de consolidação.
        """
        try:
            klines = self.binance.get_historical_klines(
                symbol=symbol,
                interval=interval,
                lookback_days=1
            )

            if klines.empty or len(klines) < lookback + 1:
                return None, None

            # Usar os últimos "lookback" candles para definir a consolidação
            candles = klines.iloc[-(lookback+1):-1]

            resistance = candles['high'].max()
            support = candles['low'].min()

            logger.info(f"{symbol} - Consolidação detectada: Suporte = {support}, Resistência = {resistance}")
            return support, resistance

        except Exception as e:
            logger.error(f"Erro ao obter níveis de consolidação para {symbol}: {e}")
            return None, None

    def _validate_price_range(self, symbol, price):
        """Valida se um preço está dentro de uma faixa realista para o símbolo"""
        if price <= 0:
            return False

        # Faixas de preços realistas (com margem de segurança)
        price_ranges = {
            'BTC': (10000, 150000),
            'ETH': (500, 10000),
            'BNB': (50, 1000),
            'ADA': (0.1, 5),
            'SOL': (10, 1000),
            'DOT': (1, 50),
            'LINK': (1, 100),
            'MATIC': (0.1, 10),
            'AVAX': (5, 200),
            'ATOM': (1, 100)
        }

        for coin, (min_price, max_price) in price_ranges.items():
            if coin in symbol:
                return min_price <= price <= max_price

        # Para moedas não mapeadas, aceitar uma faixa ampla
        return 0.001 <= price <= 100000