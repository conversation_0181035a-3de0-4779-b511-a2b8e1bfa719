import React, { useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { TextInput, Button, Text } from 'react-native-paper';
import { AntDesign } from '@expo/vector-icons';

const SignUp = () => {
  const [showPassword, setShowPassword] = useState(false);

  return (
    <View style={styles.container}>
      {/* Logo do Aplicativo */}
      {/* Você pode adicionar a imagem da logo aqui. */}

      {/* Campo de Nome */}
      <TextInput
        label="Nome"
        mode="outlined"
        style={styles.input}
      />

      {/* Campo de Email */}
      <TextInput
        label="Email"
        mode="outlined"
        style={styles.input}
      />

      {/* Campo de Senha */}
      <TextInput
        label="Senha"
        mode="outlined"
        secureTextEntry={!showPassword}
        style={styles.input}
        right={
          <TextInput.Icon 
            name={showPassword ? 'eye' : 'eye-off'}
            onPress={() => setShowPassword(!showPassword)}
          />
        }
      />

      {/* Botão de Cadastro */}
      <Button mode="contained" onPress={() => {}} style={styles.button}>
        Cadastrar
      </Button>

      {/* <PERSON><PERSON> tem uma conta? */}
      <Text style={styles.text}>Já tem uma conta?</Text>

      {/* Botão "Login" */}
      <Button mode="text" onPress={() => {}} style={styles.button}>
        Login
      </Button>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    justifyContent: 'center',
  },
  input: {
    marginBottom: 16,
  },
  button: {
    marginBottom: 16,
  },
  text: {
    textAlign: 'center',
    marginBottom: 16,
  },
});

export default SignUp;

/*
Mudanças realizadas:

Importei os componentes necessários do React Native Paper.
Substituí os componentes padrão do React Native pelos componentes do React Native Paper.
Usei TextInput.Icon para adicionar o ícone de mostrar/ocultar senha diretamente no TextInput.
Utilizei os botões Button do React Native Paper, que vêm com estilos predefinidos.
*/
