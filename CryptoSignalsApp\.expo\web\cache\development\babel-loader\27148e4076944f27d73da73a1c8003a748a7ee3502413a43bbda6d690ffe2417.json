{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nimport { Background, getDefaultHeaderHeight, SafeAreaProviderCompat } from '@react-navigation/elements';\nimport Color from 'color';\nimport * as React from 'react';\nimport Animated from \"react-native-web/dist/exports/Animated\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport { forModalPresentationIOS, forNoAnimation as forNoAnimationCard } from \"../../TransitionConfigs/CardStyleInterpolators\";\nimport { DefaultTransition, ModalFadeTransition, ModalTransition } from \"../../TransitionConfigs/TransitionPresets\";\nimport findLastIndex from \"../../utils/findLastIndex\";\nimport getDistanceForDirection from \"../../utils/getDistanceForDirection\";\nimport { MaybeScreen, MaybeScreenContainer } from \"../Screens\";\nimport { getIsModalPresentation } from \"./Card\";\nimport CardContainer from \"./CardContainer\";\nvar EPSILON = 1e-5;\nvar STATE_INACTIVE = 0;\nvar STATE_TRANSITIONING_OR_BELOW_TOP = 1;\nvar STATE_ON_TOP = 2;\nvar FALLBACK_DESCRIPTOR = Object.freeze({\n  options: {}\n});\nvar getInterpolationIndex = function getInterpolationIndex(scenes, index) {\n  var cardStyleInterpolator = scenes[index].descriptor.options.cardStyleInterpolator;\n  var interpolationIndex = 0;\n  for (var i = index - 1; i >= 0; i--) {\n    var _scenes$i;\n    var cardStyleInterpolatorCurrent = (_scenes$i = scenes[i]) === null || _scenes$i === void 0 ? void 0 : _scenes$i.descriptor.options.cardStyleInterpolator;\n    if (cardStyleInterpolatorCurrent !== cardStyleInterpolator) {\n      break;\n    }\n    interpolationIndex++;\n  }\n  return interpolationIndex;\n};\nvar getIsModal = function getIsModal(scene, interpolationIndex, isParentModal) {\n  if (isParentModal) {\n    return true;\n  }\n  var cardStyleInterpolator = scene.descriptor.options.cardStyleInterpolator;\n  var isModalPresentation = getIsModalPresentation(cardStyleInterpolator);\n  var isModal = isModalPresentation && interpolationIndex !== 0;\n  return isModal;\n};\nvar getHeaderHeights = function getHeaderHeights(scenes, insets, isParentHeaderShown, isParentModal, layout, previous) {\n  return scenes.reduce(function (acc, curr, index) {\n    var _curr$descriptor$opti = curr.descriptor.options,\n      _curr$descriptor$opti2 = _curr$descriptor$opti.headerStatusBarHeight,\n      headerStatusBarHeight = _curr$descriptor$opti2 === void 0 ? isParentHeaderShown ? 0 : insets.top : _curr$descriptor$opti2,\n      headerStyle = _curr$descriptor$opti.headerStyle;\n    var style = StyleSheet.flatten(headerStyle || {});\n    var height = 'height' in style && typeof style.height === 'number' ? style.height : previous[curr.route.key];\n    var interpolationIndex = getInterpolationIndex(scenes, index);\n    var isModal = getIsModal(curr, interpolationIndex, isParentModal);\n    acc[curr.route.key] = typeof height === 'number' ? height : getDefaultHeaderHeight(layout, isModal, headerStatusBarHeight);\n    return acc;\n  }, {});\n};\nvar getDistanceFromOptions = function getDistanceFromOptions(layout, descriptor) {\n  var _ref4 = (descriptor === null || descriptor === void 0 ? void 0 : descriptor.options) || {},\n    presentation = _ref4.presentation,\n    _ref4$gestureDirectio = _ref4.gestureDirection,\n    gestureDirection = _ref4$gestureDirectio === void 0 ? presentation === 'modal' ? ModalTransition.gestureDirection : DefaultTransition.gestureDirection : _ref4$gestureDirectio;\n  return getDistanceForDirection(layout, gestureDirection);\n};\nvar getProgressFromGesture = function getProgressFromGesture(gesture, layout, descriptor) {\n  var distance = getDistanceFromOptions({\n    width: Math.max(1, layout.width),\n    height: Math.max(1, layout.height)\n  }, descriptor);\n  if (distance > 0) {\n    return gesture.interpolate({\n      inputRange: [0, distance],\n      outputRange: [1, 0]\n    });\n  }\n  return gesture.interpolate({\n    inputRange: [distance, 0],\n    outputRange: [0, 1]\n  });\n};\nvar CardStack = function (_React$Component) {\n  function CardStack(_props) {\n    var _this;\n    _classCallCheck(this, CardStack);\n    _this = _callSuper(this, CardStack, [_props]);\n    _this.handleLayout = function (e) {\n      var _e$nativeEvent$layout = e.nativeEvent.layout,\n        height = _e$nativeEvent$layout.height,\n        width = _e$nativeEvent$layout.width;\n      var layout = {\n        width: width,\n        height: height\n      };\n      _this.setState(function (state, props) {\n        if (height === state.layout.height && width === state.layout.width) {\n          return null;\n        }\n        return {\n          layout: layout,\n          headerHeights: getHeaderHeights(state.scenes, props.insets, props.isParentHeaderShown, props.isParentModal, layout, state.headerHeights)\n        };\n      });\n    };\n    _this.handleHeaderLayout = function (_ref) {\n      var route = _ref.route,\n        height = _ref.height;\n      _this.setState(function (_ref2) {\n        var headerHeights = _ref2.headerHeights;\n        var previousHeight = headerHeights[route.key];\n        if (previousHeight === height) {\n          return null;\n        }\n        return {\n          headerHeights: _objectSpread(_objectSpread({}, headerHeights), {}, _defineProperty({}, route.key, height))\n        };\n      });\n    };\n    _this.getFocusedRoute = function () {\n      var state = _this.props.state;\n      return state.routes[state.index];\n    };\n    _this.getPreviousScene = function (_ref3) {\n      var route = _ref3.route;\n      var getPreviousRoute = _this.props.getPreviousRoute;\n      var scenes = _this.state.scenes;\n      var previousRoute = getPreviousRoute({\n        route: route\n      });\n      if (previousRoute) {\n        var previousScene = scenes.find(function (scene) {\n          return scene.descriptor.route.key === previousRoute.key;\n        });\n        return previousScene;\n      }\n      return undefined;\n    };\n    _this.state = {\n      routes: [],\n      scenes: [],\n      gestures: {},\n      layout: SafeAreaProviderCompat.initialMetrics.frame,\n      descriptors: _this.props.descriptors,\n      headerHeights: {}\n    };\n    return _this;\n  }\n  _inherits(CardStack, _React$Component);\n  return _createClass(CardStack, [{\n    key: \"render\",\n    value: function render() {\n      var _this2 = this;\n      var _this$props = this.props,\n        insets = _this$props.insets,\n        state = _this$props.state,\n        routes = _this$props.routes,\n        closingRouteKeys = _this$props.closingRouteKeys,\n        onOpenRoute = _this$props.onOpenRoute,\n        onCloseRoute = _this$props.onCloseRoute,\n        renderHeader = _this$props.renderHeader,\n        renderScene = _this$props.renderScene,\n        isParentHeaderShown = _this$props.isParentHeaderShown,\n        isParentModal = _this$props.isParentModal,\n        onTransitionStart = _this$props.onTransitionStart,\n        onTransitionEnd = _this$props.onTransitionEnd,\n        onGestureStart = _this$props.onGestureStart,\n        onGestureEnd = _this$props.onGestureEnd,\n        onGestureCancel = _this$props.onGestureCancel,\n        _this$props$detachIna = _this$props.detachInactiveScreens,\n        detachInactiveScreens = _this$props$detachIna === void 0 ? Platform.OS === 'web' || Platform.OS === 'android' || Platform.OS === 'ios' : _this$props$detachIna;\n      var _this$state = this.state,\n        scenes = _this$state.scenes,\n        layout = _this$state.layout,\n        gestures = _this$state.gestures,\n        headerHeights = _this$state.headerHeights;\n      var focusedRoute = state.routes[state.index];\n      var focusedHeaderHeight = headerHeights[focusedRoute.key];\n      var isFloatHeaderAbsolute = this.state.scenes.slice(-2).some(function (scene) {\n        var _scene$descriptor$opt;\n        var options = (_scene$descriptor$opt = scene.descriptor.options) != null ? _scene$descriptor$opt : {};\n        var headerMode = options.headerMode,\n          headerTransparent = options.headerTransparent,\n          _options$headerShown = options.headerShown,\n          headerShown = _options$headerShown === void 0 ? true : _options$headerShown;\n        if (headerTransparent || headerShown === false || headerMode === 'screen') {\n          return true;\n        }\n        return false;\n      });\n      var activeScreensLimit = 1;\n      for (var i = scenes.length - 1; i >= 0; i--) {\n        var options = scenes[i].descriptor.options;\n        var _options$detachPrevio = options.detachPreviousScreen,\n          detachPreviousScreen = _options$detachPrevio === void 0 ? options.presentation === 'transparentModal' ? false : getIsModalPresentation(options.cardStyleInterpolator) ? i !== findLastIndex(scenes, function (scene) {\n            var cardStyleInterpolator = scene.descriptor.options.cardStyleInterpolator;\n            return cardStyleInterpolator === forModalPresentationIOS || (cardStyleInterpolator === null || cardStyleInterpolator === void 0 ? void 0 : cardStyleInterpolator.name) === 'forModalPresentationIOS';\n          }) : true : _options$detachPrevio;\n        if (detachPreviousScreen === false) {\n          activeScreensLimit++;\n        } else {\n          if (i <= scenes.length - 2) {\n            break;\n          }\n        }\n      }\n      var floatingHeader = React.createElement(React.Fragment, {\n        key: \"header\"\n      }, renderHeader({\n        mode: 'float',\n        layout: layout,\n        scenes: scenes,\n        getPreviousScene: this.getPreviousScene,\n        getFocusedRoute: this.getFocusedRoute,\n        onContentHeightChange: this.handleHeaderLayout,\n        style: [styles.floating, isFloatHeaderAbsolute && [{\n          height: focusedHeaderHeight\n        }, styles.absolute]]\n      }));\n      return React.createElement(Background, null, isFloatHeaderAbsolute ? null : floatingHeader, React.createElement(MaybeScreenContainer, {\n        enabled: detachInactiveScreens,\n        style: styles.container,\n        onLayout: this.handleLayout\n      }, routes.map(function (route, index, self) {\n        var _scenes, _scenes2;\n        var focused = focusedRoute.key === route.key;\n        var gesture = gestures[route.key];\n        var scene = scenes[index];\n        var isScreenActive = 1;\n        if (index < self.length - activeScreensLimit - 1) {\n          isScreenActive = STATE_INACTIVE;\n        } else {\n          var sceneForActivity = scenes[self.length - 1];\n          var outputValue = index === self.length - 1 ? STATE_ON_TOP : index >= self.length - activeScreensLimit ? STATE_TRANSITIONING_OR_BELOW_TOP : STATE_INACTIVE;\n          isScreenActive = sceneForActivity ? sceneForActivity.progress.current.interpolate({\n            inputRange: [0, 1 - EPSILON, 1],\n            outputRange: [1, 1, outputValue],\n            extrapolate: 'clamp'\n          }) : STATE_TRANSITIONING_OR_BELOW_TOP;\n        }\n        var _scene$descriptor$opt2 = scene.descriptor.options,\n          _scene$descriptor$opt3 = _scene$descriptor$opt2.headerShown,\n          headerShown = _scene$descriptor$opt3 === void 0 ? true : _scene$descriptor$opt3,\n          headerTransparent = _scene$descriptor$opt2.headerTransparent,\n          headerStyle = _scene$descriptor$opt2.headerStyle,\n          headerTintColor = _scene$descriptor$opt2.headerTintColor,\n          freezeOnBlur = _scene$descriptor$opt2.freezeOnBlur;\n        var safeAreaInsetTop = insets.top;\n        var safeAreaInsetRight = insets.right;\n        var safeAreaInsetBottom = insets.bottom;\n        var safeAreaInsetLeft = insets.left;\n        var headerHeight = headerShown !== false ? headerHeights[route.key] : 0;\n        var headerDarkContent;\n        if (headerShown) {\n          if (typeof headerTintColor === 'string') {\n            headerDarkContent = Color(headerTintColor).isDark();\n          } else {\n            var flattenedHeaderStyle = StyleSheet.flatten(headerStyle);\n            if (flattenedHeaderStyle && 'backgroundColor' in flattenedHeaderStyle && typeof flattenedHeaderStyle.backgroundColor === 'string') {\n              headerDarkContent = !Color(flattenedHeaderStyle.backgroundColor).isDark();\n            }\n          }\n        }\n        var interpolationIndex = getInterpolationIndex(scenes, index);\n        var isModal = getIsModal(scene, interpolationIndex, isParentModal);\n        var isNextScreenTransparent = ((_scenes = scenes[index + 1]) === null || _scenes === void 0 ? void 0 : _scenes.descriptor.options.presentation) === 'transparentModal';\n        var detachCurrentScreen = ((_scenes2 = scenes[index + 1]) === null || _scenes2 === void 0 ? void 0 : _scenes2.descriptor.options.detachPreviousScreen) !== false;\n        return React.createElement(MaybeScreen, {\n          key: route.key,\n          style: StyleSheet.absoluteFill,\n          enabled: detachInactiveScreens,\n          active: isScreenActive,\n          freezeOnBlur: freezeOnBlur,\n          pointerEvents: \"box-none\"\n        }, React.createElement(CardContainer, {\n          index: index,\n          interpolationIndex: interpolationIndex,\n          modal: isModal,\n          active: index === self.length - 1,\n          focused: focused,\n          closing: closingRouteKeys.includes(route.key),\n          layout: layout,\n          gesture: gesture,\n          scene: scene,\n          safeAreaInsetTop: safeAreaInsetTop,\n          safeAreaInsetRight: safeAreaInsetRight,\n          safeAreaInsetBottom: safeAreaInsetBottom,\n          safeAreaInsetLeft: safeAreaInsetLeft,\n          onGestureStart: onGestureStart,\n          onGestureCancel: onGestureCancel,\n          onGestureEnd: onGestureEnd,\n          headerHeight: headerHeight,\n          isParentHeaderShown: isParentHeaderShown,\n          onHeaderHeightChange: _this2.handleHeaderLayout,\n          getPreviousScene: _this2.getPreviousScene,\n          getFocusedRoute: _this2.getFocusedRoute,\n          headerDarkContent: headerDarkContent,\n          hasAbsoluteFloatHeader: isFloatHeaderAbsolute && !headerTransparent,\n          renderHeader: renderHeader,\n          renderScene: renderScene,\n          onOpenRoute: onOpenRoute,\n          onCloseRoute: onCloseRoute,\n          onTransitionStart: onTransitionStart,\n          onTransitionEnd: onTransitionEnd,\n          isNextScreenTransparent: isNextScreenTransparent,\n          detachCurrentScreen: detachCurrentScreen\n        }));\n      })), isFloatHeaderAbsolute ? floatingHeader : null);\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(props, state) {\n      if (props.routes === state.routes && props.descriptors === state.descriptors) {\n        return null;\n      }\n      var gestures = props.routes.reduce(function (acc, curr) {\n        var descriptor = props.descriptors[curr.key];\n        var _ref5 = (descriptor === null || descriptor === void 0 ? void 0 : descriptor.options) || {},\n          animationEnabled = _ref5.animationEnabled;\n        acc[curr.key] = state.gestures[curr.key] || new Animated.Value(props.openingRouteKeys.includes(curr.key) && animationEnabled !== false ? getDistanceFromOptions(state.layout, descriptor) : 0);\n        return acc;\n      }, {});\n      var scenes = props.routes.map(function (route, index, self) {\n        var _descriptor$options$h;\n        var previousRoute = self[index - 1];\n        var nextRoute = self[index + 1];\n        var oldScene = state.scenes[index];\n        var currentGesture = gestures[route.key];\n        var previousGesture = previousRoute ? gestures[previousRoute.key] : undefined;\n        var nextGesture = nextRoute ? gestures[nextRoute.key] : undefined;\n        var descriptor = props.descriptors[route.key] || state.descriptors[route.key] || (oldScene ? oldScene.descriptor : FALLBACK_DESCRIPTOR);\n        var nextDescriptor = props.descriptors[nextRoute === null || nextRoute === void 0 ? void 0 : nextRoute.key] || state.descriptors[nextRoute === null || nextRoute === void 0 ? void 0 : nextRoute.key];\n        var previousDescriptor = props.descriptors[previousRoute === null || previousRoute === void 0 ? void 0 : previousRoute.key] || state.descriptors[previousRoute === null || previousRoute === void 0 ? void 0 : previousRoute.key];\n        var optionsForTransitionConfig = index !== self.length - 1 && nextDescriptor && nextDescriptor.options.presentation !== 'transparentModal' ? nextDescriptor.options : descriptor.options;\n        var defaultTransitionPreset = optionsForTransitionConfig.presentation === 'modal' ? ModalTransition : optionsForTransitionConfig.presentation === 'transparentModal' ? ModalFadeTransition : DefaultTransition;\n        var _optionsForTransition = optionsForTransitionConfig.animationEnabled,\n          animationEnabled = _optionsForTransition === void 0 ? Platform.OS !== 'web' && Platform.OS !== 'windows' && Platform.OS !== 'macos' : _optionsForTransition,\n          _optionsForTransition2 = optionsForTransitionConfig.gestureEnabled,\n          gestureEnabled = _optionsForTransition2 === void 0 ? Platform.OS === 'ios' && animationEnabled : _optionsForTransition2,\n          _optionsForTransition3 = optionsForTransitionConfig.gestureDirection,\n          gestureDirection = _optionsForTransition3 === void 0 ? defaultTransitionPreset.gestureDirection : _optionsForTransition3,\n          _optionsForTransition4 = optionsForTransitionConfig.transitionSpec,\n          transitionSpec = _optionsForTransition4 === void 0 ? defaultTransitionPreset.transitionSpec : _optionsForTransition4,\n          _optionsForTransition5 = optionsForTransitionConfig.cardStyleInterpolator,\n          cardStyleInterpolator = _optionsForTransition5 === void 0 ? animationEnabled === false ? forNoAnimationCard : defaultTransitionPreset.cardStyleInterpolator : _optionsForTransition5,\n          _optionsForTransition6 = optionsForTransitionConfig.headerStyleInterpolator,\n          headerStyleInterpolator = _optionsForTransition6 === void 0 ? defaultTransitionPreset.headerStyleInterpolator : _optionsForTransition6,\n          _optionsForTransition7 = optionsForTransitionConfig.cardOverlayEnabled,\n          cardOverlayEnabled = _optionsForTransition7 === void 0 ? Platform.OS !== 'ios' && optionsForTransitionConfig.presentation !== 'transparentModal' || getIsModalPresentation(cardStyleInterpolator) : _optionsForTransition7;\n        var headerMode = (_descriptor$options$h = descriptor.options.headerMode) != null ? _descriptor$options$h : !(optionsForTransitionConfig.presentation === 'modal' || optionsForTransitionConfig.presentation === 'transparentModal' || (nextDescriptor === null || nextDescriptor === void 0 ? void 0 : nextDescriptor.options.presentation) === 'modal' || (nextDescriptor === null || nextDescriptor === void 0 ? void 0 : nextDescriptor.options.presentation) === 'transparentModal' || getIsModalPresentation(cardStyleInterpolator)) && Platform.OS === 'ios' && descriptor.options.header === undefined ? 'float' : 'screen';\n        var scene = {\n          route: route,\n          descriptor: _objectSpread(_objectSpread({}, descriptor), {}, {\n            options: _objectSpread(_objectSpread({}, descriptor.options), {}, {\n              animationEnabled: animationEnabled,\n              cardOverlayEnabled: cardOverlayEnabled,\n              cardStyleInterpolator: cardStyleInterpolator,\n              gestureDirection: gestureDirection,\n              gestureEnabled: gestureEnabled,\n              headerStyleInterpolator: headerStyleInterpolator,\n              transitionSpec: transitionSpec,\n              headerMode: headerMode\n            })\n          }),\n          progress: {\n            current: getProgressFromGesture(currentGesture, state.layout, descriptor),\n            next: nextGesture && (nextDescriptor === null || nextDescriptor === void 0 ? void 0 : nextDescriptor.options.presentation) !== 'transparentModal' ? getProgressFromGesture(nextGesture, state.layout, nextDescriptor) : undefined,\n            previous: previousGesture ? getProgressFromGesture(previousGesture, state.layout, previousDescriptor) : undefined\n          },\n          __memo: [state.layout, descriptor, nextDescriptor, previousDescriptor, currentGesture, nextGesture, previousGesture]\n        };\n        if (oldScene && scene.__memo.every(function (it, i) {\n          return oldScene.__memo[i] === it;\n        })) {\n          return oldScene;\n        }\n        return scene;\n      });\n      return {\n        routes: props.routes,\n        scenes: scenes,\n        gestures: gestures,\n        descriptors: props.descriptors,\n        headerHeights: getHeaderHeights(scenes, props.insets, props.isParentHeaderShown, props.isParentModal, state.layout, state.headerHeights)\n      };\n    }\n  }]);\n}(React.Component);\nexport { CardStack as default };\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1\n  },\n  absolute: {\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    right: 0\n  },\n  floating: {\n    zIndex: 1\n  }\n});", "map": {"version": 3, "names": ["Background", "getDefaultHeaderHeight", "SafeAreaProviderCompat", "Color", "React", "Animated", "Platform", "StyleSheet", "forModalPresentationIOS", "forNoAnimation", "forNoAnimationCard", "DefaultTransition", "ModalFadeTransition", "ModalTransition", "findLastIndex", "getDistanceForDirection", "MaybeScreen", "MaybeScreenContainer", "getIsModalPresentation", "CardContainer", "EPSILON", "STATE_INACTIVE", "STATE_TRANSITIONING_OR_BELOW_TOP", "STATE_ON_TOP", "FALLBACK_DESCRIPTOR", "Object", "freeze", "options", "getInterpolationIndex", "scenes", "index", "cardStyleInterpolator", "descriptor", "interpolationIndex", "i", "_scenes$i", "cardStyleInterpolatorCurrent", "getIsModal", "scene", "isParentModal", "isModalPresentation", "isModal", "getHeaderHeights", "insets", "isParentHeaderShown", "layout", "previous", "reduce", "acc", "curr", "_curr$descriptor$opti", "_curr$descriptor$opti2", "headerStatusBarHeight", "top", "headerStyle", "style", "flatten", "height", "route", "key", "getDistanceFromOptions", "_ref4", "presentation", "_ref4$gestureDirectio", "gestureDirection", "getProgressFromGesture", "gesture", "distance", "width", "Math", "max", "interpolate", "inputRange", "outputRange", "CardStack", "_React$Component", "props", "_this", "_classCallCheck", "_callSuper", "handleLayout", "e", "_e$nativeEvent$layout", "nativeEvent", "setState", "state", "headerHeights", "handleHeaderLayout", "_ref", "_ref2", "previousHeight", "_objectSpread", "_defineProperty", "getFocusedRoute", "routes", "getPreviousScene", "_ref3", "getPreviousRoute", "previousRoute", "previousScene", "find", "undefined", "gestures", "initialMetrics", "frame", "descriptors", "_inherits", "_createClass", "value", "render", "_this2", "_this$props", "closingRouteKeys", "onOpenRoute", "onCloseRoute", "renderHeader", "renderScene", "onTransitionStart", "onTransitionEnd", "onGestureStart", "onGestureEnd", "onGestureCancel", "_this$props$detachIna", "detachInactiveScreens", "OS", "_this$state", "focusedRoute", "focusedHeaderHeight", "isFloatHeaderAbsolute", "slice", "some", "_scene$descriptor$opt", "headerMode", "headerTransparent", "_options$headerShown", "headerShown", "activeScreensLimit", "length", "_options$detachPrevio", "detachPreviousScreen", "name", "floatingHeader", "createElement", "Fragment", "mode", "onContentHeightChange", "styles", "floating", "absolute", "enabled", "container", "onLayout", "map", "self", "_scenes", "_scenes2", "focused", "isScreenActive", "sceneForActivity", "outputValue", "progress", "current", "extrapolate", "_scene$descriptor$opt2", "_scene$descriptor$opt3", "headerTintColor", "freezeOnBlur", "safeAreaInsetTop", "safeAreaInsetRight", "right", "safeAreaInsetBottom", "bottom", "safeAreaInsetLeft", "left", "headerHeight", "headerDarkContent", "isDark", "flattenedHeaderStyle", "backgroundColor", "isNextScreenTransparent", "detachCurrentScreen", "absoluteFill", "active", "pointerEvents", "modal", "closing", "includes", "onHeaderHeightChange", "hasAbsoluteFloatHeader", "getDerivedStateFromProps", "_ref5", "animationEnabled", "Value", "openingRouteKeys", "_descriptor$options$h", "nextRoute", "oldScene", "currentGesture", "previousGesture", "nextGesture", "nextDescriptor", "previousDescriptor", "optionsForTransitionConfig", "defaultTransitionPreset", "_optionsForTransition", "_optionsForTransition2", "gestureEnabled", "_optionsForTransition3", "_optionsForTransition4", "transitionSpec", "_optionsForTransition5", "_optionsForTransition6", "headerStyleInterpolator", "_optionsForTransition7", "cardOverlayEnabled", "header", "next", "__memo", "every", "it", "Component", "default", "create", "flex", "position", "zIndex"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\@react-navigation\\stack\\src\\views\\Stack\\CardStack.tsx"], "sourcesContent": ["import {\n  Background,\n  getDefaultHeaderHeight,\n  SafeAreaProviderCompat,\n} from '@react-navigation/elements';\nimport type {\n  ParamListBase,\n  Route,\n  StackNavigationState,\n} from '@react-navigation/native';\nimport Color from 'color';\nimport * as React from 'react';\nimport {\n  Animated,\n  LayoutChangeEvent,\n  Platform,\n  StyleSheet,\n} from 'react-native';\nimport type { EdgeInsets } from 'react-native-safe-area-context';\n\nimport {\n  forModalPresentationIOS,\n  forNoAnimation as forNoAnimationCard,\n} from '../../TransitionConfigs/CardStyleInterpolators';\nimport {\n  DefaultTransition,\n  ModalFadeTransition,\n  ModalTransition,\n} from '../../TransitionConfigs/TransitionPresets';\nimport type {\n  Layout,\n  Scene,\n  StackDescriptor,\n  StackDescriptorMap,\n  StackHeaderMode,\n  StackNavigationOptions,\n} from '../../types';\nimport findLastIndex from '../../utils/findLastIndex';\nimport getDistanceForDirection from '../../utils/getDistanceForDirection';\nimport type { Props as HeaderContainerProps } from '../Header/HeaderContainer';\nimport { MaybeScreen, MaybeScreenContainer } from '../Screens';\nimport { getIsModalPresentation } from './Card';\nimport CardContainer from './CardContainer';\n\ntype GestureValues = {\n  [key: string]: Animated.Value;\n};\n\ntype Props = {\n  insets: EdgeInsets;\n  state: StackNavigationState<ParamListBase>;\n  descriptors: StackDescriptorMap;\n  routes: Route<string>[];\n  openingRouteKeys: string[];\n  closingRouteKeys: string[];\n  onOpenRoute: (props: { route: Route<string> }) => void;\n  onCloseRoute: (props: { route: Route<string> }) => void;\n  getPreviousRoute: (props: {\n    route: Route<string>;\n  }) => Route<string> | undefined;\n  renderHeader: (props: HeaderContainerProps) => React.ReactNode;\n  renderScene: (props: { route: Route<string> }) => React.ReactNode;\n  isParentHeaderShown: boolean;\n  isParentModal: boolean;\n  onTransitionStart: (\n    props: { route: Route<string> },\n    closing: boolean\n  ) => void;\n  onTransitionEnd: (props: { route: Route<string> }, closing: boolean) => void;\n  onGestureStart: (props: { route: Route<string> }) => void;\n  onGestureEnd: (props: { route: Route<string> }) => void;\n  onGestureCancel: (props: { route: Route<string> }) => void;\n  detachInactiveScreens?: boolean;\n};\n\ntype State = {\n  routes: Route<string>[];\n  descriptors: StackDescriptorMap;\n  scenes: Scene[];\n  gestures: GestureValues;\n  layout: Layout;\n  headerHeights: Record<string, number>;\n};\n\nconst EPSILON = 1e-5;\n\nconst STATE_INACTIVE = 0;\nconst STATE_TRANSITIONING_OR_BELOW_TOP = 1;\nconst STATE_ON_TOP = 2;\n\nconst FALLBACK_DESCRIPTOR = Object.freeze({ options: {} });\n\nconst getInterpolationIndex = (scenes: Scene[], index: number) => {\n  const { cardStyleInterpolator } = scenes[index].descriptor.options;\n\n  // Start from current card and count backwards the number of cards with same interpolation\n  let interpolationIndex = 0;\n\n  for (let i = index - 1; i >= 0; i--) {\n    const cardStyleInterpolatorCurrent =\n      scenes[i]?.descriptor.options.cardStyleInterpolator;\n\n    if (cardStyleInterpolatorCurrent !== cardStyleInterpolator) {\n      break;\n    }\n\n    interpolationIndex++;\n  }\n\n  return interpolationIndex;\n};\n\nconst getIsModal = (\n  scene: Scene,\n  interpolationIndex: number,\n  isParentModal: boolean\n) => {\n  if (isParentModal) {\n    return true;\n  }\n\n  const { cardStyleInterpolator } = scene.descriptor.options;\n  const isModalPresentation = getIsModalPresentation(cardStyleInterpolator);\n  const isModal = isModalPresentation && interpolationIndex !== 0;\n\n  return isModal;\n};\n\nconst getHeaderHeights = (\n  scenes: Scene[],\n  insets: EdgeInsets,\n  isParentHeaderShown: boolean,\n  isParentModal: boolean,\n  layout: Layout,\n  previous: Record<string, number>\n) => {\n  return scenes.reduce<Record<string, number>>((acc, curr, index) => {\n    const {\n      headerStatusBarHeight = isParentHeaderShown ? 0 : insets.top,\n      headerStyle,\n    } = curr.descriptor.options;\n\n    const style = StyleSheet.flatten(headerStyle || {});\n\n    const height =\n      'height' in style && typeof style.height === 'number'\n        ? style.height\n        : previous[curr.route.key];\n\n    const interpolationIndex = getInterpolationIndex(scenes, index);\n    const isModal = getIsModal(curr, interpolationIndex, isParentModal);\n\n    acc[curr.route.key] =\n      typeof height === 'number'\n        ? height\n        : getDefaultHeaderHeight(layout, isModal, headerStatusBarHeight);\n\n    return acc;\n  }, {});\n};\n\nconst getDistanceFromOptions = (\n  layout: Layout,\n  descriptor?: StackDescriptor\n) => {\n  const {\n    presentation,\n    gestureDirection = presentation === 'modal'\n      ? ModalTransition.gestureDirection\n      : DefaultTransition.gestureDirection,\n  } = (descriptor?.options || {}) as StackNavigationOptions;\n\n  return getDistanceForDirection(layout, gestureDirection);\n};\n\nconst getProgressFromGesture = (\n  gesture: Animated.Value,\n  layout: Layout,\n  descriptor?: StackDescriptor\n) => {\n  const distance = getDistanceFromOptions(\n    {\n      // Make sure that we have a non-zero distance, otherwise there will be incorrect progress\n      // This causes blank screen on web if it was previously inside container with display: none\n      width: Math.max(1, layout.width),\n      height: Math.max(1, layout.height),\n    },\n    descriptor\n  );\n\n  if (distance > 0) {\n    return gesture.interpolate({\n      inputRange: [0, distance],\n      outputRange: [1, 0],\n    });\n  }\n\n  return gesture.interpolate({\n    inputRange: [distance, 0],\n    outputRange: [0, 1],\n  });\n};\n\nexport default class CardStack extends React.Component<Props, State> {\n  static getDerivedStateFromProps(\n    props: Props,\n    state: State\n  ): Partial<State> | null {\n    if (\n      props.routes === state.routes &&\n      props.descriptors === state.descriptors\n    ) {\n      return null;\n    }\n\n    const gestures = props.routes.reduce<GestureValues>((acc, curr) => {\n      const descriptor = props.descriptors[curr.key];\n      const { animationEnabled } = descriptor?.options || {};\n\n      acc[curr.key] =\n        state.gestures[curr.key] ||\n        new Animated.Value(\n          props.openingRouteKeys.includes(curr.key) &&\n          animationEnabled !== false\n            ? getDistanceFromOptions(state.layout, descriptor)\n            : 0\n        );\n\n      return acc;\n    }, {});\n\n    const scenes = props.routes.map((route, index, self) => {\n      const previousRoute = self[index - 1];\n      const nextRoute = self[index + 1];\n\n      const oldScene = state.scenes[index];\n\n      const currentGesture = gestures[route.key];\n      const previousGesture = previousRoute\n        ? gestures[previousRoute.key]\n        : undefined;\n      const nextGesture = nextRoute ? gestures[nextRoute.key] : undefined;\n\n      const descriptor =\n        props.descriptors[route.key] ||\n        state.descriptors[route.key] ||\n        (oldScene ? oldScene.descriptor : FALLBACK_DESCRIPTOR);\n\n      const nextDescriptor =\n        props.descriptors[nextRoute?.key] || state.descriptors[nextRoute?.key];\n\n      const previousDescriptor =\n        props.descriptors[previousRoute?.key] ||\n        state.descriptors[previousRoute?.key];\n\n      // When a screen is not the last, it should use next screen's transition config\n      // Many transitions also animate the previous screen, so using 2 different transitions doesn't look right\n      // For example combining a slide and a modal transition would look wrong otherwise\n      // With this approach, combining different transition styles in the same navigator mostly looks right\n      // This will still be broken when 2 transitions have different idle state (e.g. modal presentation),\n      // but majority of the transitions look alright\n      const optionsForTransitionConfig =\n        index !== self.length - 1 &&\n        nextDescriptor &&\n        nextDescriptor.options.presentation !== 'transparentModal'\n          ? nextDescriptor.options\n          : descriptor.options;\n\n      let defaultTransitionPreset =\n        optionsForTransitionConfig.presentation === 'modal'\n          ? ModalTransition\n          : optionsForTransitionConfig.presentation === 'transparentModal'\n          ? ModalFadeTransition\n          : DefaultTransition;\n\n      const {\n        animationEnabled = Platform.OS !== 'web' &&\n          Platform.OS !== 'windows' &&\n          Platform.OS !== 'macos',\n        gestureEnabled = Platform.OS === 'ios' && animationEnabled,\n        gestureDirection = defaultTransitionPreset.gestureDirection,\n        transitionSpec = defaultTransitionPreset.transitionSpec,\n        cardStyleInterpolator = animationEnabled === false\n          ? forNoAnimationCard\n          : defaultTransitionPreset.cardStyleInterpolator,\n        headerStyleInterpolator = defaultTransitionPreset.headerStyleInterpolator,\n        cardOverlayEnabled = (Platform.OS !== 'ios' &&\n          optionsForTransitionConfig.presentation !== 'transparentModal') ||\n          getIsModalPresentation(cardStyleInterpolator),\n      } = optionsForTransitionConfig;\n\n      const headerMode: StackHeaderMode =\n        descriptor.options.headerMode ??\n        (!(\n          optionsForTransitionConfig.presentation === 'modal' ||\n          optionsForTransitionConfig.presentation === 'transparentModal' ||\n          nextDescriptor?.options.presentation === 'modal' ||\n          nextDescriptor?.options.presentation === 'transparentModal' ||\n          getIsModalPresentation(cardStyleInterpolator)\n        ) &&\n        Platform.OS === 'ios' &&\n        descriptor.options.header === undefined\n          ? 'float'\n          : 'screen');\n\n      const scene = {\n        route,\n        descriptor: {\n          ...descriptor,\n          options: {\n            ...descriptor.options,\n            animationEnabled,\n            cardOverlayEnabled,\n            cardStyleInterpolator,\n            gestureDirection,\n            gestureEnabled,\n            headerStyleInterpolator,\n            transitionSpec,\n            headerMode,\n          },\n        },\n        progress: {\n          current: getProgressFromGesture(\n            currentGesture,\n            state.layout,\n            descriptor\n          ),\n          next:\n            nextGesture &&\n            nextDescriptor?.options.presentation !== 'transparentModal'\n              ? getProgressFromGesture(\n                  nextGesture,\n                  state.layout,\n                  nextDescriptor\n                )\n              : undefined,\n          previous: previousGesture\n            ? getProgressFromGesture(\n                previousGesture,\n                state.layout,\n                previousDescriptor\n              )\n            : undefined,\n        },\n        __memo: [\n          state.layout,\n          descriptor,\n          nextDescriptor,\n          previousDescriptor,\n          currentGesture,\n          nextGesture,\n          previousGesture,\n        ],\n      };\n\n      if (\n        oldScene &&\n        scene.__memo.every((it, i) => {\n          // @ts-expect-error: we haven't added __memo to the annotation to prevent usage elsewhere\n          return oldScene.__memo[i] === it;\n        })\n      ) {\n        return oldScene;\n      }\n\n      return scene;\n    });\n\n    return {\n      routes: props.routes,\n      scenes,\n      gestures,\n      descriptors: props.descriptors,\n      headerHeights: getHeaderHeights(\n        scenes,\n        props.insets,\n        props.isParentHeaderShown,\n        props.isParentModal,\n        state.layout,\n        state.headerHeights\n      ),\n    };\n  }\n\n  constructor(props: Props) {\n    super(props);\n\n    this.state = {\n      routes: [],\n      scenes: [],\n      gestures: {},\n      layout: SafeAreaProviderCompat.initialMetrics.frame,\n      descriptors: this.props.descriptors,\n      // Used when card's header is null and mode is float to make transition\n      // between screens with headers and those without headers smooth.\n      // This is not a great heuristic here. We don't know synchronously\n      // on mount what the header height is so we have just used the most\n      // common cases here.\n      headerHeights: {},\n    };\n  }\n\n  private handleLayout = (e: LayoutChangeEvent) => {\n    const { height, width } = e.nativeEvent.layout;\n\n    const layout = { width, height };\n\n    this.setState((state, props) => {\n      if (height === state.layout.height && width === state.layout.width) {\n        return null;\n      }\n\n      return {\n        layout,\n        headerHeights: getHeaderHeights(\n          state.scenes,\n          props.insets,\n          props.isParentHeaderShown,\n          props.isParentModal,\n          layout,\n          state.headerHeights\n        ),\n      };\n    });\n  };\n\n  private handleHeaderLayout = ({\n    route,\n    height,\n  }: {\n    route: Route<string>;\n    height: number;\n  }) => {\n    this.setState(({ headerHeights }) => {\n      const previousHeight = headerHeights[route.key];\n\n      if (previousHeight === height) {\n        return null;\n      }\n\n      return {\n        headerHeights: {\n          ...headerHeights,\n          [route.key]: height,\n        },\n      };\n    });\n  };\n\n  private getFocusedRoute = () => {\n    const { state } = this.props;\n\n    return state.routes[state.index];\n  };\n\n  private getPreviousScene = ({ route }: { route: Route<string> }) => {\n    const { getPreviousRoute } = this.props;\n    const { scenes } = this.state;\n\n    const previousRoute = getPreviousRoute({ route });\n\n    if (previousRoute) {\n      const previousScene = scenes.find(\n        (scene) => scene.descriptor.route.key === previousRoute.key\n      );\n\n      return previousScene;\n    }\n\n    return undefined;\n  };\n\n  render() {\n    const {\n      insets,\n      state,\n      routes,\n      closingRouteKeys,\n      onOpenRoute,\n      onCloseRoute,\n      renderHeader,\n      renderScene,\n      isParentHeaderShown,\n      isParentModal,\n      onTransitionStart,\n      onTransitionEnd,\n      onGestureStart,\n      onGestureEnd,\n      onGestureCancel,\n      detachInactiveScreens = Platform.OS === 'web' ||\n        Platform.OS === 'android' ||\n        Platform.OS === 'ios',\n    } = this.props;\n\n    const { scenes, layout, gestures, headerHeights } = this.state;\n\n    const focusedRoute = state.routes[state.index];\n    const focusedHeaderHeight = headerHeights[focusedRoute.key];\n\n    const isFloatHeaderAbsolute = this.state.scenes.slice(-2).some((scene) => {\n      const options = scene.descriptor.options ?? {};\n      const { headerMode, headerTransparent, headerShown = true } = options;\n\n      if (\n        headerTransparent ||\n        headerShown === false ||\n        headerMode === 'screen'\n      ) {\n        return true;\n      }\n\n      return false;\n    });\n\n    let activeScreensLimit = 1;\n\n    for (let i = scenes.length - 1; i >= 0; i--) {\n      const { options } = scenes[i].descriptor;\n      const {\n        // By default, we don't want to detach the previous screen of the active one for modals\n        detachPreviousScreen = options.presentation === 'transparentModal'\n          ? false\n          : getIsModalPresentation(options.cardStyleInterpolator)\n          ? i !==\n            findLastIndex(scenes, (scene) => {\n              const { cardStyleInterpolator } = scene.descriptor.options;\n\n              return (\n                cardStyleInterpolator === forModalPresentationIOS ||\n                cardStyleInterpolator?.name === 'forModalPresentationIOS'\n              );\n            })\n          : true,\n      } = options;\n\n      if (detachPreviousScreen === false) {\n        activeScreensLimit++;\n      } else {\n        // Check at least last 2 screens before stopping\n        // This will make sure that screen isn't detached when another screen is animating on top of the transparent one\n        // For example, (Opaque -> Transparent -> Opaque)\n        if (i <= scenes.length - 2) {\n          break;\n        }\n      }\n    }\n\n    const floatingHeader = (\n      <React.Fragment key=\"header\">\n        {renderHeader({\n          mode: 'float',\n          layout,\n          scenes,\n          getPreviousScene: this.getPreviousScene,\n          getFocusedRoute: this.getFocusedRoute,\n          onContentHeightChange: this.handleHeaderLayout,\n          style: [\n            styles.floating,\n            isFloatHeaderAbsolute && [\n              // Without this, the header buttons won't be touchable on Android when headerTransparent: true\n              { height: focusedHeaderHeight },\n              styles.absolute,\n            ],\n          ],\n        })}\n      </React.Fragment>\n    );\n\n    return (\n      <Background>\n        {isFloatHeaderAbsolute ? null : floatingHeader}\n        <MaybeScreenContainer\n          enabled={detachInactiveScreens}\n          style={styles.container}\n          onLayout={this.handleLayout}\n        >\n          {routes.map((route, index, self) => {\n            const focused = focusedRoute.key === route.key;\n            const gesture = gestures[route.key];\n            const scene = scenes[index];\n\n            // For the screens that shouldn't be active, the value is 0\n            // For those that should be active, but are not the top screen, the value is 1\n            // For those on top of the stack and with interaction enabled, the value is 2\n            // For the old implementation, it stays the same it was\n            let isScreenActive:\n              | Animated.AnimatedInterpolation<0 | 1 | 2>\n              | 2\n              | 1\n              | 0 = 1;\n\n            if (index < self.length - activeScreensLimit - 1) {\n              // screen should be inactive because it is too deep in the stack\n              isScreenActive = STATE_INACTIVE;\n            } else {\n              const sceneForActivity = scenes[self.length - 1];\n              const outputValue =\n                index === self.length - 1\n                  ? STATE_ON_TOP // the screen is on top after the transition\n                  : index >= self.length - activeScreensLimit\n                  ? STATE_TRANSITIONING_OR_BELOW_TOP // the screen should stay active after the transition, it is not on top but is in activeLimit\n                  : STATE_INACTIVE; // the screen should be active only during the transition, it is at the edge of activeLimit\n              isScreenActive = sceneForActivity\n                ? sceneForActivity.progress.current.interpolate({\n                    inputRange: [0, 1 - EPSILON, 1],\n                    outputRange: [1, 1, outputValue],\n                    extrapolate: 'clamp',\n                  })\n                : STATE_TRANSITIONING_OR_BELOW_TOP;\n            }\n\n            const {\n              headerShown = true,\n              headerTransparent,\n              headerStyle,\n              headerTintColor,\n              freezeOnBlur,\n            } = scene.descriptor.options;\n\n            const safeAreaInsetTop = insets.top;\n            const safeAreaInsetRight = insets.right;\n            const safeAreaInsetBottom = insets.bottom;\n            const safeAreaInsetLeft = insets.left;\n\n            const headerHeight =\n              headerShown !== false ? headerHeights[route.key] : 0;\n\n            let headerDarkContent: boolean | undefined;\n\n            if (headerShown) {\n              if (typeof headerTintColor === 'string') {\n                headerDarkContent = Color(headerTintColor).isDark();\n              } else {\n                const flattenedHeaderStyle = StyleSheet.flatten(headerStyle);\n\n                if (\n                  flattenedHeaderStyle &&\n                  'backgroundColor' in flattenedHeaderStyle &&\n                  typeof flattenedHeaderStyle.backgroundColor === 'string'\n                ) {\n                  headerDarkContent = !Color(\n                    flattenedHeaderStyle.backgroundColor\n                  ).isDark();\n                }\n              }\n            }\n\n            // Start from current card and count backwards the number of cards with same interpolation\n            const interpolationIndex = getInterpolationIndex(scenes, index);\n            const isModal = getIsModal(\n              scene,\n              interpolationIndex,\n              isParentModal\n            );\n\n            const isNextScreenTransparent =\n              scenes[index + 1]?.descriptor.options.presentation ===\n              'transparentModal';\n\n            const detachCurrentScreen =\n              scenes[index + 1]?.descriptor.options.detachPreviousScreen !==\n              false;\n\n            return (\n              <MaybeScreen\n                key={route.key}\n                style={StyleSheet.absoluteFill}\n                enabled={detachInactiveScreens}\n                active={isScreenActive}\n                freezeOnBlur={freezeOnBlur}\n                pointerEvents=\"box-none\"\n              >\n                <CardContainer\n                  index={index}\n                  interpolationIndex={interpolationIndex}\n                  modal={isModal}\n                  active={index === self.length - 1}\n                  focused={focused}\n                  closing={closingRouteKeys.includes(route.key)}\n                  layout={layout}\n                  gesture={gesture}\n                  scene={scene}\n                  safeAreaInsetTop={safeAreaInsetTop}\n                  safeAreaInsetRight={safeAreaInsetRight}\n                  safeAreaInsetBottom={safeAreaInsetBottom}\n                  safeAreaInsetLeft={safeAreaInsetLeft}\n                  onGestureStart={onGestureStart}\n                  onGestureCancel={onGestureCancel}\n                  onGestureEnd={onGestureEnd}\n                  headerHeight={headerHeight}\n                  isParentHeaderShown={isParentHeaderShown}\n                  onHeaderHeightChange={this.handleHeaderLayout}\n                  getPreviousScene={this.getPreviousScene}\n                  getFocusedRoute={this.getFocusedRoute}\n                  headerDarkContent={headerDarkContent}\n                  hasAbsoluteFloatHeader={\n                    isFloatHeaderAbsolute && !headerTransparent\n                  }\n                  renderHeader={renderHeader}\n                  renderScene={renderScene}\n                  onOpenRoute={onOpenRoute}\n                  onCloseRoute={onCloseRoute}\n                  onTransitionStart={onTransitionStart}\n                  onTransitionEnd={onTransitionEnd}\n                  isNextScreenTransparent={isNextScreenTransparent}\n                  detachCurrentScreen={detachCurrentScreen}\n                />\n              </MaybeScreen>\n            );\n          })}\n        </MaybeScreenContainer>\n        {isFloatHeaderAbsolute ? floatingHeader : null}\n      </Background>\n    );\n  }\n}\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n  },\n  absolute: {\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    right: 0,\n  },\n  floating: {\n    zIndex: 1,\n  },\n});\n"], "mappings": ";;;;;;;;;;AAAA,SACEA,UAAU,EACVC,sBAAsB,EACtBC,sBAAsB,QACjB,4BAA4B;AAMnC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,KAAKC,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAAA,OAAAC,QAAA;AAAA,OAAAC,UAAA;AAS9B,SACEC,uBAAuB,EACvBC,cAAc,IAAIC,kBAAkB;AAEtC,SACEC,iBAAiB,EACjBC,mBAAmB,EACnBC,eAAe;AAUjB,OAAOC,aAAa;AACpB,OAAOC,uBAAuB;AAE9B,SAASC,WAAW,EAAEC,oBAAoB;AAC1C,SAASC,sBAAsB;AAC/B,OAAOC,aAAa;AA0CpB,IAAMC,OAAO,GAAG,IAAI;AAEpB,IAAMC,cAAc,GAAG,CAAC;AACxB,IAAMC,gCAAgC,GAAG,CAAC;AAC1C,IAAMC,YAAY,GAAG,CAAC;AAEtB,IAAMC,mBAAmB,GAAGC,MAAM,CAACC,MAAM,CAAC;EAAEC,OAAO,EAAE,CAAC;AAAE,CAAC,CAAC;AAE1D,IAAMC,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAIC,MAAe,EAAEC,KAAa,EAAK;EAChE,IAAQC,qBAAA,GAA0BF,MAAM,CAACC,KAAK,CAAC,CAACE,UAAU,CAACL,OAAO,CAA1DI,qBAAA;EAGR,IAAIE,kBAAkB,GAAG,CAAC;EAE1B,KAAK,IAAIC,CAAC,GAAGJ,KAAK,GAAG,CAAC,EAAEI,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAAA,IAAAC,SAAA;IACnC,IAAMC,4BAA4B,IAAAD,SAAA,GAChCN,MAAM,CAACK,CAAC,CAAC,cAAAC,SAAA,uBAATA,SAAA,CAAWH,UAAU,CAACL,OAAO,CAACI,qBAAqB;IAErD,IAAIK,4BAA4B,KAAKL,qBAAqB,EAAE;MAC1D;IACF;IAEAE,kBAAkB,EAAE;EACtB;EAEA,OAAOA,kBAAkB;AAC3B,CAAC;AAED,IAAMI,UAAU,GAAG,SAAbA,UAAUA,CACdC,KAAY,EACZL,kBAA0B,EAC1BM,aAAsB,EACnB;EACH,IAAIA,aAAa,EAAE;IACjB,OAAO,IAAI;EACb;EAEA,IAAQR,qBAAA,GAA0BO,KAAK,CAACN,UAAU,CAACL,OAAO,CAAlDI,qBAAA;EACR,IAAMS,mBAAmB,GAAGtB,sBAAsB,CAACa,qBAAqB,CAAC;EACzE,IAAMU,OAAO,GAAGD,mBAAmB,IAAIP,kBAAkB,KAAK,CAAC;EAE/D,OAAOQ,OAAO;AAChB,CAAC;AAED,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CACpBb,MAAe,EACfc,MAAkB,EAClBC,mBAA4B,EAC5BL,aAAsB,EACtBM,MAAc,EACdC,QAAgC,EAC7B;EACH,OAAOjB,MAAM,CAACkB,MAAM,CAAyB,UAACC,GAAG,EAAEC,IAAI,EAAEnB,KAAK,EAAK;IACjE,IAAAoB,qBAAA,GAGID,IAAI,CAACjB,UAAU,CAACL,OAAO;MAAAwB,sBAAA,GAAAD,qBAAA,CAFzBE,qBAAqB;MAArBA,qBAAqB,GAAAD,sBAAA,cAAGP,mBAAmB,GAAG,CAAC,GAAGD,MAAM,CAACU,GAAG,GAAAF,sBAAA;MAC5DG,WAAA,GAAAJ,qBAAA,CAAAI,WAAA;IAGF,IAAMC,KAAK,GAAGhD,UAAU,CAACiD,OAAO,CAACF,WAAW,IAAI,CAAC,CAAC,CAAC;IAEnD,IAAMG,MAAM,GACV,QAAQ,IAAIF,KAAK,IAAI,OAAOA,KAAK,CAACE,MAAM,KAAK,QAAQ,GACjDF,KAAK,CAACE,MAAM,GACZX,QAAQ,CAACG,IAAI,CAACS,KAAK,CAACC,GAAG,CAAC;IAE9B,IAAM1B,kBAAkB,GAAGL,qBAAqB,CAACC,MAAM,EAAEC,KAAK,CAAC;IAC/D,IAAMW,OAAO,GAAGJ,UAAU,CAACY,IAAI,EAAEhB,kBAAkB,EAAEM,aAAa,CAAC;IAEnES,GAAG,CAACC,IAAI,CAACS,KAAK,CAACC,GAAG,CAAC,GACjB,OAAOF,MAAM,KAAK,QAAQ,GACtBA,MAAM,GACNxD,sBAAsB,CAAC4C,MAAM,EAAEJ,OAAO,EAAEW,qBAAqB,CAAC;IAEpE,OAAOJ,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;AACR,CAAC;AAED,IAAMY,sBAAsB,GAAG,SAAzBA,sBAAsBA,CAC1Bf,MAAc,EACdb,UAA4B,EACzB;EACH,IAAA6B,KAAA,GAKK,CAAA7B,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEL,OAAO,KAAI,CAAC,CAA4B;IAJvDmC,YAAY,GAAAD,KAAA,CAAZC,YAAY;IAAAC,qBAAA,GAAAF,KAAA,CACZG,gBAAgB;IAAhBA,gBAAgB,GAAAD,qBAAA,cAAGD,YAAY,KAAK,OAAO,GACvCjD,eAAe,CAACmD,gBAAgB,GAChCrD,iBAAiB,CAACqD,gBAAA,GAAAD,qBAAA;EAGxB,OAAOhD,uBAAuB,CAAC8B,MAAM,EAAEmB,gBAAgB,CAAC;AAC1D,CAAC;AAED,IAAMC,sBAAsB,GAAG,SAAzBA,sBAAsBA,CAC1BC,OAAuB,EACvBrB,MAAc,EACdb,UAA4B,EACzB;EACH,IAAMmC,QAAQ,GAAGP,sBAAsB,CACrC;IAGEQ,KAAK,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEzB,MAAM,CAACuB,KAAK,CAAC;IAChCX,MAAM,EAAEY,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEzB,MAAM,CAACY,MAAM;EACnC,CAAC,EACDzB,UAAU,CACX;EAED,IAAImC,QAAQ,GAAG,CAAC,EAAE;IAChB,OAAOD,OAAO,CAACK,WAAW,CAAC;MACzBC,UAAU,EAAE,CAAC,CAAC,EAAEL,QAAQ,CAAC;MACzBM,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC;IACpB,CAAC,CAAC;EACJ;EAEA,OAAOP,OAAO,CAACK,WAAW,CAAC;IACzBC,UAAU,EAAE,CAACL,QAAQ,EAAE,CAAC,CAAC;IACzBM,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC;EACpB,CAAC,CAAC;AACJ,CAAC;AAAA,IAEoBC,SAAS,aAAAC,gBAAA;EAqL5B,SAAAD,UAAYE,MAAY,EAAE;IAAA,IAAAC,KAAA;IAAAC,eAAA,OAAAJ,SAAA;IACxBG,KAAA,GAAAE,UAAA,OAAAL,SAAA,GAAME,MAAK;IAACC,KAAA,CAiBNG,YAAY,GAAI,UAAAC,CAAoB,EAAK;MAC/C,IAAAC,qBAAA,GAA0BD,CAAC,CAACE,WAAW,CAACtC,MAAM;QAAtCY,MAAM,GAAAyB,qBAAA,CAANzB,MAAM;QAAEW,KAAA,GAAAc,qBAAA,CAAAd,KAAA;MAEhB,IAAMvB,MAAM,GAAG;QAAEuB,KAAK,EAALA,KAAK;QAAEX,MAAA,EAAAA;MAAO,CAAC;MAEhCoB,KAAA,CAAKO,QAAQ,CAAC,UAACC,KAAK,EAAET,KAAK,EAAK;QAC9B,IAAInB,MAAM,KAAK4B,KAAK,CAACxC,MAAM,CAACY,MAAM,IAAIW,KAAK,KAAKiB,KAAK,CAACxC,MAAM,CAACuB,KAAK,EAAE;UAClE,OAAO,IAAI;QACb;QAEA,OAAO;UACLvB,MAAM,EAANA,MAAM;UACNyC,aAAa,EAAE5C,gBAAgB,CAC7B2C,KAAK,CAACxD,MAAM,EACZ+C,KAAK,CAACjC,MAAM,EACZiC,KAAK,CAAChC,mBAAmB,EACzBgC,KAAK,CAACrC,aAAa,EACnBM,MAAM,EACNwC,KAAK,CAACC,aAAa;QAEvB,CAAC;MACH,CAAC,CAAC;IACJ,CAAC;IAAAT,KAAA,CAEOU,kBAAkB,GAAG,UAAAC,IAAA,EAMvB;MAAA,IALJ9B,KAAK,GAKN8B,IAAA,CALC9B,KAAK;QACLD,MAAA,GAID+B,IAAA,CAJC/B,MAAA;MAKAoB,KAAA,CAAKO,QAAQ,CAAC,UAAAK,KAAA,EAAuB;QAAA,IAApBH,aAAA,GAAeG,KAAA,CAAfH,aAAA;QACf,IAAMI,cAAc,GAAGJ,aAAa,CAAC5B,KAAK,CAACC,GAAG,CAAC;QAE/C,IAAI+B,cAAc,KAAKjC,MAAM,EAAE;UAC7B,OAAO,IAAI;QACb;QAEA,OAAO;UACL6B,aAAa,EAAAK,aAAA,CAAAA,aAAA,KACRL,aAAa,OAAAM,eAAA,KACflC,KAAK,CAACC,GAAG,EAAGF,MAAA;QAEjB,CAAC;MACH,CAAC,CAAC;IACJ,CAAC;IAAAoB,KAAA,CAEOgB,eAAe,GAAG,YAAM;MAC9B,IAAQR,KAAA,GAAUR,KAAA,CAAKD,KAAK,CAApBS,KAAA;MAER,OAAOA,KAAK,CAACS,MAAM,CAACT,KAAK,CAACvD,KAAK,CAAC;IAClC,CAAC;IAAA+C,KAAA,CAEOkB,gBAAgB,GAAG,UAAAC,KAAA,EAAyC;MAAA,IAAtCtC,KAAA,GAAiCsC,KAAA,CAAjCtC,KAAA;MAC5B,IAAQuC,gBAAA,GAAqBpB,KAAA,CAAKD,KAAK,CAA/BqB,gBAAA;MACR,IAAQpE,MAAA,GAAWgD,KAAA,CAAKQ,KAAK,CAArBxD,MAAA;MAER,IAAMqE,aAAa,GAAGD,gBAAgB,CAAC;QAAEvC,KAAA,EAAAA;MAAM,CAAC,CAAC;MAEjD,IAAIwC,aAAa,EAAE;QACjB,IAAMC,aAAa,GAAGtE,MAAM,CAACuE,IAAI,CAC9B,UAAA9D,KAAK;UAAA,OAAKA,KAAK,CAACN,UAAU,CAAC0B,KAAK,CAACC,GAAG,KAAKuC,aAAa,CAACvC,GAAG;QAAA,EAC5D;QAED,OAAOwC,aAAa;MACtB;MAEA,OAAOE,SAAS;IAClB,CAAC;IAnFCxB,KAAA,CAAKQ,KAAK,GAAG;MACXS,MAAM,EAAE,EAAE;MACVjE,MAAM,EAAE,EAAE;MACVyE,QAAQ,EAAE,CAAC,CAAC;MACZzD,MAAM,EAAE3C,sBAAsB,CAACqG,cAAc,CAACC,KAAK;MACnDC,WAAW,EAAE5B,KAAA,CAAKD,KAAK,CAAC6B,WAAW;MAMnCnB,aAAa,EAAE,CAAC;IAClB,CAAC;IAAA,OAAAT,KAAA;EACH;EAAA6B,SAAA,CAAAhC,SAAA,EAAAC,gBAAA;EAAA,OAAAgC,YAAA,CAAAjC,SAAA;IAAAf,GAAA;IAAAiD,KAAA,EAwEA,SAAAC,MAAMA,CAAA,EAAG;MAAA,IAAAC,MAAA;MACP,IAAAC,WAAA,GAmBI,IAAI,CAACnC,KAAK;QAlBZjC,MAAM,GAAAoE,WAAA,CAANpE,MAAM;QACN0C,KAAK,GAAA0B,WAAA,CAAL1B,KAAK;QACLS,MAAM,GAAAiB,WAAA,CAANjB,MAAM;QACNkB,gBAAgB,GAAAD,WAAA,CAAhBC,gBAAgB;QAChBC,WAAW,GAAAF,WAAA,CAAXE,WAAW;QACXC,YAAY,GAAAH,WAAA,CAAZG,YAAY;QACZC,YAAY,GAAAJ,WAAA,CAAZI,YAAY;QACZC,WAAW,GAAAL,WAAA,CAAXK,WAAW;QACXxE,mBAAmB,GAAAmE,WAAA,CAAnBnE,mBAAmB;QACnBL,aAAa,GAAAwE,WAAA,CAAbxE,aAAa;QACb8E,iBAAiB,GAAAN,WAAA,CAAjBM,iBAAiB;QACjBC,eAAe,GAAAP,WAAA,CAAfO,eAAe;QACfC,cAAc,GAAAR,WAAA,CAAdQ,cAAc;QACdC,YAAY,GAAAT,WAAA,CAAZS,YAAY;QACZC,eAAe,GAAAV,WAAA,CAAfU,eAAe;QAAAC,qBAAA,GAAAX,WAAA,CACfY,qBAAqB;QAArBA,qBAAqB,GAAAD,qBAAA,cAAGpH,QAAQ,CAACsH,EAAE,KAAK,KAAK,IAC3CtH,QAAQ,CAACsH,EAAE,KAAK,SAAS,IACzBtH,QAAQ,CAACsH,EAAE,KAAK,QAAAF,qBAAA;MAGpB,IAAAG,WAAA,GAAoD,IAAI,CAACxC,KAAK;QAAtDxD,MAAM,GAAAgG,WAAA,CAANhG,MAAM;QAAEgB,MAAM,GAAAgF,WAAA,CAANhF,MAAM;QAAEyD,QAAQ,GAAAuB,WAAA,CAARvB,QAAQ;QAAEhB,aAAA,GAAAuC,WAAA,CAAAvC,aAAA;MAElC,IAAMwC,YAAY,GAAGzC,KAAK,CAACS,MAAM,CAACT,KAAK,CAACvD,KAAK,CAAC;MAC9C,IAAMiG,mBAAmB,GAAGzC,aAAa,CAACwC,YAAY,CAACnE,GAAG,CAAC;MAE3D,IAAMqE,qBAAqB,GAAG,IAAI,CAAC3C,KAAK,CAACxD,MAAM,CAACoG,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAE,UAAA5F,KAAK,EAAK;QAAA,IAAA6F,qBAAA;QACxE,IAAMxG,OAAO,IAAAwG,qBAAA,GAAG7F,KAAK,CAACN,UAAU,CAACL,OAAO,YAAAwG,qBAAA,GAAI,CAAC,CAAC;QAC9C,IAAQC,UAAU,GAA4CzG,OAAO,CAA7DyG,UAAU;UAAEC,iBAAiB,GAAyB1G,OAAO,CAAjD0G,iBAAiB;UAAAC,oBAAA,GAAyB3G,OAAO,CAA9B4G,WAAW;UAAXA,WAAW,GAAAD,oBAAA,cAAG,OAAAA,oBAAA;QAErD,IACED,iBAAiB,IACjBE,WAAW,KAAK,KAAK,IACrBH,UAAU,KAAK,QAAQ,EACvB;UACA,OAAO,IAAI;QACb;QAEA,OAAO,KAAK;MACd,CAAC,CAAC;MAEF,IAAII,kBAAkB,GAAG,CAAC;MAE1B,KAAK,IAAItG,CAAC,GAAGL,MAAM,CAAC4G,MAAM,GAAG,CAAC,EAAEvG,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC3C,IAAQP,OAAA,GAAYE,MAAM,CAACK,CAAC,CAAC,CAACF,UAAU,CAAhCL,OAAA;QACR,IAAA+G,qBAAA,GAeI/G,OAAO,CAbTgH,oBAAoB;UAApBA,oBAAoB,GAAAD,qBAAA,cAAG/G,OAAO,CAACmC,YAAY,KAAK,kBAAkB,GAC9D,KAAK,GACL5C,sBAAsB,CAACS,OAAO,CAACI,qBAAqB,CAAC,GACrDG,CAAC,KACDpB,aAAa,CAACe,MAAM,EAAG,UAAAS,KAAK,EAAK;YAC/B,IAAQP,qBAAA,GAA0BO,KAAK,CAACN,UAAU,CAACL,OAAO,CAAlDI,qBAAA;YAER,OACEA,qBAAqB,KAAKvB,uBAAuB,IACjD,CAAAuB,qBAAqB,aAArBA,qBAAqB,uBAArBA,qBAAqB,CAAE6G,IAAI,MAAK,yBAAyB;UAE7D,CAAC,CAAC,GACF,OAAAF,qBAAA;QAGN,IAAIC,oBAAoB,KAAK,KAAK,EAAE;UAClCH,kBAAkB,EAAE;QACtB,CAAC,MAAM;UAIL,IAAItG,CAAC,IAAIL,MAAM,CAAC4G,MAAM,GAAG,CAAC,EAAE;YAC1B;UACF;QACF;MACF;MAEA,IAAMI,cAAc,GAClBzI,KAAA,CAAA0I,aAAA,CAAC1I,KAAK,CAAC2I,QAAQ;QAACpF,GAAG,EAAC;MAAQ,GACzBwD,YAAY,CAAC;QACZ6B,IAAI,EAAE,OAAO;QACbnG,MAAM,EAANA,MAAM;QACNhB,MAAM,EAANA,MAAM;QACNkE,gBAAgB,EAAE,IAAI,CAACA,gBAAgB;QACvCF,eAAe,EAAE,IAAI,CAACA,eAAe;QACrCoD,qBAAqB,EAAE,IAAI,CAAC1D,kBAAkB;QAC9ChC,KAAK,EAAE,CACL2F,MAAM,CAACC,QAAQ,EACfnB,qBAAqB,IAAI,CAEvB;UAAEvE,MAAM,EAAEsE;QAAoB,CAAC,EAC/BmB,MAAM,CAACE,QAAQ,CAChB;MAEL,CAAC,CAAC,CAEL;MAED,OACEhJ,KAAA,CAAA0I,aAAA,CAAC9I,UAAU,QACRgI,qBAAqB,GAAG,IAAI,GAAGa,cAAc,EAC9CzI,KAAA,CAAA0I,aAAA,CAAC7H,oBAAoB;QACnBoI,OAAO,EAAE1B,qBAAsB;QAC/BpE,KAAK,EAAE2F,MAAM,CAACI,SAAU;QACxBC,QAAQ,EAAE,IAAI,CAACvE;MAAa,GAE3Bc,MAAM,CAAC0D,GAAG,CAAC,UAAC9F,KAAK,EAAE5B,KAAK,EAAE2H,IAAI,EAAK;QAAA,IAAAC,OAAA,EAAAC,QAAA;QAClC,IAAMC,OAAO,GAAG9B,YAAY,CAACnE,GAAG,KAAKD,KAAK,CAACC,GAAG;QAC9C,IAAMO,OAAO,GAAGoC,QAAQ,CAAC5C,KAAK,CAACC,GAAG,CAAC;QACnC,IAAMrB,KAAK,GAAGT,MAAM,CAACC,KAAK,CAAC;QAM3B,IAAI+H,cAIC,GAAG,CAAC;QAET,IAAI/H,KAAK,GAAG2H,IAAI,CAAChB,MAAM,GAAGD,kBAAkB,GAAG,CAAC,EAAE;UAEhDqB,cAAc,GAAGxI,cAAc;QACjC,CAAC,MAAM;UACL,IAAMyI,gBAAgB,GAAGjI,MAAM,CAAC4H,IAAI,CAAChB,MAAM,GAAG,CAAC,CAAC;UAChD,IAAMsB,WAAW,GACfjI,KAAK,KAAK2H,IAAI,CAAChB,MAAM,GAAG,CAAC,GACrBlH,YAAY,GACZO,KAAK,IAAI2H,IAAI,CAAChB,MAAM,GAAGD,kBAAkB,GACzClH,gCAAgC,GAChCD,cAAc;UACpBwI,cAAc,GAAGC,gBAAgB,GAC7BA,gBAAgB,CAACE,QAAQ,CAACC,OAAO,CAAC1F,WAAW,CAAC;YAC5CC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,GAAGpD,OAAO,EAAE,CAAC,CAAC;YAC/BqD,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,EAAEsF,WAAW,CAAC;YAChCG,WAAW,EAAE;UACf,CAAC,CAAC,GACF5I,gCAAgC;QACtC;QAEA,IAAA6I,sBAAA,GAMI7H,KAAK,CAACN,UAAU,CAACL,OAAO;UAAAyI,sBAAA,GAAAD,sBAAA,CAL1B5B,WAAW;UAAXA,WAAW,GAAA6B,sBAAA,cAAG,IAAI,GAAAA,sBAAA;UAClB/B,iBAAiB,GAAA8B,sBAAA,CAAjB9B,iBAAiB;UACjB/E,WAAW,GAAA6G,sBAAA,CAAX7G,WAAW;UACX+G,eAAe,GAAAF,sBAAA,CAAfE,eAAe;UACfC,YAAA,GAAAH,sBAAA,CAAAG,YAAA;QAGF,IAAMC,gBAAgB,GAAG5H,MAAM,CAACU,GAAG;QACnC,IAAMmH,kBAAkB,GAAG7H,MAAM,CAAC8H,KAAK;QACvC,IAAMC,mBAAmB,GAAG/H,MAAM,CAACgI,MAAM;QACzC,IAAMC,iBAAiB,GAAGjI,MAAM,CAACkI,IAAI;QAErC,IAAMC,YAAY,GAChBvC,WAAW,KAAK,KAAK,GAAGjD,aAAa,CAAC5B,KAAK,CAACC,GAAG,CAAC,GAAG,CAAC;QAEtD,IAAIoH,iBAAsC;QAE1C,IAAIxC,WAAW,EAAE;UACf,IAAI,OAAO8B,eAAe,KAAK,QAAQ,EAAE;YACvCU,iBAAiB,GAAG5K,KAAK,CAACkK,eAAe,CAAC,CAACW,MAAM,EAAE;UACrD,CAAC,MAAM;YACL,IAAMC,oBAAoB,GAAG1K,UAAU,CAACiD,OAAO,CAACF,WAAW,CAAC;YAE5D,IACE2H,oBAAoB,IACpB,iBAAiB,IAAIA,oBAAoB,IACzC,OAAOA,oBAAoB,CAACC,eAAe,KAAK,QAAQ,EACxD;cACAH,iBAAiB,GAAG,CAAC5K,KAAK,CACxB8K,oBAAoB,CAACC,eAAe,CACrC,CAACF,MAAM,EAAE;YACZ;UACF;QACF;QAGA,IAAM/I,kBAAkB,GAAGL,qBAAqB,CAACC,MAAM,EAAEC,KAAK,CAAC;QAC/D,IAAMW,OAAO,GAAGJ,UAAU,CACxBC,KAAK,EACLL,kBAAkB,EAClBM,aAAa,CACd;QAED,IAAM4I,uBAAuB,GAC3B,EAAAzB,OAAA,GAAA7H,MAAM,CAACC,KAAK,GAAG,CAAC,CAAC,cAAA4H,OAAA,uBAAjBA,OAAA,CAAmB1H,UAAU,CAACL,OAAO,CAACmC,YAAY,MAClD,kBAAkB;QAEpB,IAAMsH,mBAAmB,GACvB,EAAAzB,QAAA,GAAA9H,MAAM,CAACC,KAAK,GAAG,CAAC,CAAC,cAAA6H,QAAA,uBAAjBA,QAAA,CAAmB3H,UAAU,CAACL,OAAO,CAACgH,oBAAoB,MAC1D,KAAK;QAEP,OACEvI,KAAA,CAAA0I,aAAA,CAAC9H,WAAW;UACV2C,GAAG,EAAED,KAAK,CAACC,GAAI;UACfJ,KAAK,EAAEhD,UAAU,CAAC8K,YAAa;UAC/BhC,OAAO,EAAE1B,qBAAsB;UAC/B2D,MAAM,EAAEzB,cAAe;UACvBS,YAAY,EAAEA,YAAa;UAC3BiB,aAAa,EAAC;QAAU,GAExBnL,KAAA,CAAA0I,aAAA,CAAC3H,aAAa;UACZW,KAAK,EAAEA,KAAM;UACbG,kBAAkB,EAAEA,kBAAmB;UACvCuJ,KAAK,EAAE/I,OAAQ;UACf6I,MAAM,EAAExJ,KAAK,KAAK2H,IAAI,CAAChB,MAAM,GAAG,CAAE;UAClCmB,OAAO,EAAEA,OAAQ;UACjB6B,OAAO,EAAEzE,gBAAgB,CAAC0E,QAAQ,CAAChI,KAAK,CAACC,GAAG,CAAE;UAC9Cd,MAAM,EAAEA,MAAO;UACfqB,OAAO,EAAEA,OAAQ;UACjB5B,KAAK,EAAEA,KAAM;UACbiI,gBAAgB,EAAEA,gBAAiB;UACnCC,kBAAkB,EAAEA,kBAAmB;UACvCE,mBAAmB,EAAEA,mBAAoB;UACzCE,iBAAiB,EAAEA,iBAAkB;UACrCrD,cAAc,EAAEA,cAAe;UAC/BE,eAAe,EAAEA,eAAgB;UACjCD,YAAY,EAAEA,YAAa;UAC3BsD,YAAY,EAAEA,YAAa;UAC3BlI,mBAAmB,EAAEA,mBAAoB;UACzC+I,oBAAoB,EAAE7E,MAAI,CAACvB,kBAAmB;UAC9CQ,gBAAgB,EAAEe,MAAI,CAACf,gBAAiB;UACxCF,eAAe,EAAEiB,MAAI,CAACjB,eAAgB;UACtCkF,iBAAiB,EAAEA,iBAAkB;UACrCa,sBAAsB,EACpB5D,qBAAqB,IAAI,CAACK,iBAC3B;UACDlB,YAAY,EAAEA,YAAa;UAC3BC,WAAW,EAAEA,WAAY;UACzBH,WAAW,EAAEA,WAAY;UACzBC,YAAY,EAAEA,YAAa;UAC3BG,iBAAiB,EAAEA,iBAAkB;UACrCC,eAAe,EAAEA,eAAgB;UACjC6D,uBAAuB,EAAEA,uBAAwB;UACjDC,mBAAmB,EAAEA;QAAoB,EACzC,CACU;MAElB,CAAC,CAAC,CACmB,EACtBpD,qBAAqB,GAAGa,cAAc,GAAG,IAAI,CACnC;IAEjB;EAAA;IAAAlF,GAAA;IAAAiD,KAAA,EA9fA,SAAOiF,wBAAwBA,CAC7BjH,KAAY,EACZS,KAAY,EACW;MACvB,IACET,KAAK,CAACkB,MAAM,KAAKT,KAAK,CAACS,MAAM,IAC7BlB,KAAK,CAAC6B,WAAW,KAAKpB,KAAK,CAACoB,WAAW,EACvC;QACA,OAAO,IAAI;MACb;MAEA,IAAMH,QAAQ,GAAG1B,KAAK,CAACkB,MAAM,CAAC/C,MAAM,CAAgB,UAACC,GAAG,EAAEC,IAAI,EAAK;QACjE,IAAMjB,UAAU,GAAG4C,KAAK,CAAC6B,WAAW,CAACxD,IAAI,CAACU,GAAG,CAAC;QAC9C,IAAAmI,KAAA,GAA6B,CAAA9J,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEL,OAAO,KAAI,CAAC,CAAC;UAA9CoK,gBAAA,GAAAD,KAAA,CAAAC,gBAAA;QAER/I,GAAG,CAACC,IAAI,CAACU,GAAG,CAAC,GACX0B,KAAK,CAACiB,QAAQ,CAACrD,IAAI,CAACU,GAAG,CAAC,IACxB,IAAItD,QAAQ,CAAC2L,KAAK,CAChBpH,KAAK,CAACqH,gBAAgB,CAACP,QAAQ,CAACzI,IAAI,CAACU,GAAG,CAAC,IACzCoI,gBAAgB,KAAK,KAAK,GACtBnI,sBAAsB,CAACyB,KAAK,CAACxC,MAAM,EAAEb,UAAU,CAAC,GAChD,CAAC,CACN;QAEH,OAAOgB,GAAG;MACZ,CAAC,EAAE,CAAC,CAAC,CAAC;MAEN,IAAMnB,MAAM,GAAG+C,KAAK,CAACkB,MAAM,CAAC0D,GAAG,CAAC,UAAC9F,KAAK,EAAE5B,KAAK,EAAE2H,IAAI,EAAK;QAAA,IAAAyC,qBAAA;QACtD,IAAMhG,aAAa,GAAGuD,IAAI,CAAC3H,KAAK,GAAG,CAAC,CAAC;QACrC,IAAMqK,SAAS,GAAG1C,IAAI,CAAC3H,KAAK,GAAG,CAAC,CAAC;QAEjC,IAAMsK,QAAQ,GAAG/G,KAAK,CAACxD,MAAM,CAACC,KAAK,CAAC;QAEpC,IAAMuK,cAAc,GAAG/F,QAAQ,CAAC5C,KAAK,CAACC,GAAG,CAAC;QAC1C,IAAM2I,eAAe,GAAGpG,aAAa,GACjCI,QAAQ,CAACJ,aAAa,CAACvC,GAAG,CAAC,GAC3B0C,SAAS;QACb,IAAMkG,WAAW,GAAGJ,SAAS,GAAG7F,QAAQ,CAAC6F,SAAS,CAACxI,GAAG,CAAC,GAAG0C,SAAS;QAEnE,IAAMrE,UAAU,GACd4C,KAAK,CAAC6B,WAAW,CAAC/C,KAAK,CAACC,GAAG,CAAC,IAC5B0B,KAAK,CAACoB,WAAW,CAAC/C,KAAK,CAACC,GAAG,CAAC,KAC3ByI,QAAQ,GAAGA,QAAQ,CAACpK,UAAU,GAAGR,mBAAmB,CAAC;QAExD,IAAMgL,cAAc,GAClB5H,KAAK,CAAC6B,WAAW,CAAC0F,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAExI,GAAG,CAAC,IAAI0B,KAAK,CAACoB,WAAW,CAAC0F,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAExI,GAAG,CAAC;QAExE,IAAM8I,kBAAkB,GACtB7H,KAAK,CAAC6B,WAAW,CAACP,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEvC,GAAG,CAAC,IACrC0B,KAAK,CAACoB,WAAW,CAACP,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEvC,GAAG,CAAC;QAQvC,IAAM+I,0BAA0B,GAC9B5K,KAAK,KAAK2H,IAAI,CAAChB,MAAM,GAAG,CAAC,IACzB+D,cAAc,IACdA,cAAc,CAAC7K,OAAO,CAACmC,YAAY,KAAK,kBAAkB,GACtD0I,cAAc,CAAC7K,OAAO,GACtBK,UAAU,CAACL,OAAO;QAExB,IAAIgL,uBAAuB,GACzBD,0BAA0B,CAAC5I,YAAY,KAAK,OAAO,GAC/CjD,eAAe,GACf6L,0BAA0B,CAAC5I,YAAY,KAAK,kBAAkB,GAC9DlD,mBAAmB,GACnBD,iBAAiB;QAEvB,IAAAiM,qBAAA,GAcIF,0BAA0B,CAb5BX,gBAAgB;UAAhBA,gBAAgB,GAAAa,qBAAA,cAAGtM,QAAQ,CAACsH,EAAE,KAAK,KAAK,IACtCtH,QAAQ,CAACsH,EAAE,KAAK,SAAS,IACzBtH,QAAQ,CAACsH,EAAE,KAAK,OAAO,GAAAgF,qBAAA;UAAAC,sBAAA,GAWvBH,0BAA0B,CAV5BI,cAAc;UAAdA,cAAc,GAAAD,sBAAA,cAAGvM,QAAQ,CAACsH,EAAE,KAAK,KAAK,IAAImE,gBAAgB,GAAAc,sBAAA;UAAAE,sBAAA,GAUxDL,0BAA0B,CAT5B1I,gBAAgB;UAAhBA,gBAAgB,GAAA+I,sBAAA,cAAGJ,uBAAuB,CAAC3I,gBAAgB,GAAA+I,sBAAA;UAAAC,sBAAA,GASzDN,0BAA0B,CAR5BO,cAAc;UAAdA,cAAc,GAAAD,sBAAA,cAAGL,uBAAuB,CAACM,cAAc,GAAAD,sBAAA;UAAAE,sBAAA,GAQrDR,0BAA0B,CAP5B3K,qBAAqB;UAArBA,qBAAqB,GAAAmL,sBAAA,cAAGnB,gBAAgB,KAAK,KAAK,GAC9CrL,kBAAkB,GAClBiM,uBAAuB,CAAC5K,qBAAqB,GAAAmL,sBAAA;UAAAC,sBAAA,GAK/CT,0BAA0B,CAJ5BU,uBAAuB;UAAvBA,uBAAuB,GAAAD,sBAAA,cAAGR,uBAAuB,CAACS,uBAAuB,GAAAD,sBAAA;UAAAE,sBAAA,GAIvEX,0BAA0B,CAH5BY,kBAAkB;UAAlBA,kBAAkB,GAAAD,sBAAA,cAAI/M,QAAQ,CAACsH,EAAE,KAAK,KAAK,IACzC8E,0BAA0B,CAAC5I,YAAY,KAAK,kBAAkB,IAC9D5C,sBAAsB,CAACa,qBAAqB,IAAAsL,sBAAA;QAGhD,IAAMjF,UAA2B,IAAA8D,qBAAA,GAC/BlK,UAAU,CAACL,OAAO,CAACyG,UAAU,YAAA8D,qBAAA,GAC5B,EACCQ,0BAA0B,CAAC5I,YAAY,KAAK,OAAO,IACnD4I,0BAA0B,CAAC5I,YAAY,KAAK,kBAAkB,IAC9D,CAAA0I,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE7K,OAAO,CAACmC,YAAY,MAAK,OAAO,IAChD,CAAA0I,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE7K,OAAO,CAACmC,YAAY,MAAK,kBAAkB,IAC3D5C,sBAAsB,CAACa,qBAAqB,CAAC,CAC9C,IACDzB,QAAQ,CAACsH,EAAE,KAAK,KAAK,IACrB5F,UAAU,CAACL,OAAO,CAAC4L,MAAM,KAAKlH,SAAS,GACnC,OAAO,GACP,QAAS;QAEf,IAAM/D,KAAK,GAAG;UACZoB,KAAK,EAALA,KAAK;UACL1B,UAAU,EAAA2D,aAAA,CAAAA,aAAA,KACL3D,UAAU;YACbL,OAAO,EAAAgE,aAAA,CAAAA,aAAA,KACF3D,UAAU,CAACL,OAAO;cACrBoK,gBAAgB,EAAhBA,gBAAgB;cAChBuB,kBAAkB,EAAlBA,kBAAkB;cAClBvL,qBAAqB,EAArBA,qBAAqB;cACrBiC,gBAAgB,EAAhBA,gBAAgB;cAChB8I,cAAc,EAAdA,cAAc;cACdM,uBAAuB,EAAvBA,uBAAuB;cACvBH,cAAc,EAAdA,cAAc;cACd7E,UAAA,EAAAA;YAAA;UACF,EACD;UACD4B,QAAQ,EAAE;YACRC,OAAO,EAAEhG,sBAAsB,CAC7BoI,cAAc,EACdhH,KAAK,CAACxC,MAAM,EACZb,UAAU,CACX;YACDwL,IAAI,EACFjB,WAAW,IACX,CAAAC,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE7K,OAAO,CAACmC,YAAY,MAAK,kBAAkB,GACvDG,sBAAsB,CACpBsI,WAAW,EACXlH,KAAK,CAACxC,MAAM,EACZ2J,cAAc,CACf,GACDnG,SAAS;YACfvD,QAAQ,EAAEwJ,eAAe,GACrBrI,sBAAsB,CACpBqI,eAAe,EACfjH,KAAK,CAACxC,MAAM,EACZ4J,kBAAkB,CACnB,GACDpG;UACN,CAAC;UACDoH,MAAM,EAAE,CACNpI,KAAK,CAACxC,MAAM,EACZb,UAAU,EACVwK,cAAc,EACdC,kBAAkB,EAClBJ,cAAc,EACdE,WAAW,EACXD,eAAe;QAEnB,CAAC;QAED,IACEF,QAAQ,IACR9J,KAAK,CAACmL,MAAM,CAACC,KAAK,CAAC,UAACC,EAAE,EAAEzL,CAAC,EAAK;UAE5B,OAAOkK,QAAQ,CAACqB,MAAM,CAACvL,CAAC,CAAC,KAAKyL,EAAE;QAClC,CAAC,CAAC,EACF;UACA,OAAOvB,QAAQ;QACjB;QAEA,OAAO9J,KAAK;MACd,CAAC,CAAC;MAEF,OAAO;QACLwD,MAAM,EAAElB,KAAK,CAACkB,MAAM;QACpBjE,MAAM,EAANA,MAAM;QACNyE,QAAQ,EAARA,QAAQ;QACRG,WAAW,EAAE7B,KAAK,CAAC6B,WAAW;QAC9BnB,aAAa,EAAE5C,gBAAgB,CAC7Bb,MAAM,EACN+C,KAAK,CAACjC,MAAM,EACZiC,KAAK,CAAChC,mBAAmB,EACzBgC,KAAK,CAACrC,aAAa,EACnB8C,KAAK,CAACxC,MAAM,EACZwC,KAAK,CAACC,aAAa;MAEvB,CAAC;IACH;EAAA;AAAA,EAnLqClF,KAAK,CAACwN,SAAS;AAAA,SAAjClJ,SAAS,IAAAmJ,OAAA;AAkgB9B,IAAM3E,MAAM,GAAG3I,UAAU,CAACuN,MAAM,CAAC;EAC/BxE,SAAS,EAAE;IACTyE,IAAI,EAAE;EACR,CAAC;EACD3E,QAAQ,EAAE;IACR4E,QAAQ,EAAE,UAAU;IACpB3K,GAAG,EAAE,CAAC;IACNwH,IAAI,EAAE,CAAC;IACPJ,KAAK,EAAE;EACT,CAAC;EACDtB,QAAQ,EAAE;IACR8E,MAAM,EAAE;EACV;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}