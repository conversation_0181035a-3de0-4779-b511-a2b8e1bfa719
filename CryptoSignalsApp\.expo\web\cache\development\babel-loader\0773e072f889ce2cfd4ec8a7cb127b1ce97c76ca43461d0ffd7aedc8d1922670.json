{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport color from 'color';\nimport { AdornmentSide, AdornmentType } from \"./Adornment/enums\";\nimport { MIN_WIDTH, ADORNMENT_SIZE, MD2_ADORNMENT_OFFSET, MD2_AFFIX_OFFSET, MD2_FLAT_INPUT_OFFSET, MD2_ICON_OFFSET, MD2_INPUT_PADDING_HORIZONTAL, MD2_LABEL_PADDING_HORIZONTAL, MD2_LABEL_PADDING_TOP, MD2_MIN_HEIGHT, MD2_OUTLINED_INPUT_OFFSET, MD3_ADORNMENT_OFFSET, MD3_AFFIX_OFFSET, MD3_FLAT_INPUT_OFFSET, MD3_ICON_OFFSET, MD3_INPUT_PADDING_HORIZONTAL, MD3_LABEL_PADDING_HORIZONTAL, MD3_LABEL_PADDING_TOP, MD3_MIN_HEIGHT, MD3_OUTLINED_INPUT_OFFSET } from \"./constants\";\nexport var calculateLabelTopPosition = function calculateLabelTopPosition(labelHeight) {\n  var height = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n  var optionalPadding = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;\n  var customHeight = height > 0 ? height : 0;\n  return Math.floor((customHeight - labelHeight) / 2 + optionalPadding);\n};\nexport var calculateInputHeight = function calculateInputHeight(labelHeight) {\n  var height = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n  var minHeight = arguments.length > 2 ? arguments[2] : undefined;\n  var finalHeight = height > 0 ? height : labelHeight;\n  if (height > 0) return height;\n  return finalHeight < minHeight ? minHeight : finalHeight;\n};\nexport var calculatePadding = function calculatePadding(props) {\n  var height = props.height,\n    _props$multiline = props.multiline,\n    multiline = _props$multiline === void 0 ? false : _props$multiline;\n  var result = 0;\n  if (multiline) {\n    if (height && multiline) {\n      result = calculateTextAreaPadding(props);\n    } else {\n      result = calculateInputPadding(props);\n    }\n  }\n  return Math.max(0, result);\n};\nvar calculateTextAreaPadding = function calculateTextAreaPadding(props) {\n  var dense = props.dense;\n  return dense ? 10 : 20;\n};\nvar calculateInputPadding = function calculateInputPadding(_ref) {\n  var topPosition = _ref.topPosition,\n    fontSize = _ref.fontSize,\n    multiline = _ref.multiline,\n    scale = _ref.scale,\n    dense = _ref.dense,\n    offset = _ref.offset,\n    isAndroid = _ref.isAndroid;\n  var refFontSize = scale * fontSize;\n  var result = Math.floor(topPosition / 2);\n  result = result + Math.floor((refFontSize - fontSize) / 2) - (scale < 1 ? offset / 2 : 0);\n  if (multiline && isAndroid) result = Math.min(dense ? offset / 2 : offset, result);\n  return result;\n};\nexport var adjustPaddingOut = function adjustPaddingOut(_ref2) {\n  var pad = _ref2.pad,\n    multiline = _ref2.multiline,\n    label = _ref2.label,\n    scale = _ref2.scale,\n    height = _ref2.height,\n    fontSize = _ref2.fontSize,\n    lineHeight = _ref2.lineHeight,\n    dense = _ref2.dense,\n    offset = _ref2.offset,\n    isAndroid = _ref2.isAndroid;\n  var fontHeight = lineHeight != null ? lineHeight : fontSize;\n  var refFontHeight = scale * fontSize;\n  var result = pad;\n  if (!isAndroid && height && !multiline) {\n    return {\n      paddingTop: Math.max(0, (height - fontHeight) / 2),\n      paddingBottom: Math.max(0, (height - fontHeight) / 2)\n    };\n  }\n  if (!isAndroid && multiline) {\n    if (dense) {\n      if (label) {\n        result += scale < 1 ? Math.min(offset, refFontHeight / 2 * scale) : 0;\n      } else {\n        result += 0;\n      }\n    }\n    if (!dense) {\n      if (label) {\n        result += scale < 1 ? Math.min(offset, refFontHeight * scale) : Math.min(offset / 2, refFontHeight * scale);\n      } else {\n        result += scale < 1 ? Math.min(offset / 2, refFontHeight * scale) : 0;\n      }\n    }\n    result = Math.floor(result);\n  }\n  return {\n    paddingTop: result,\n    paddingBottom: result\n  };\n};\nexport var adjustPaddingFlat = function adjustPaddingFlat(_ref3) {\n  var pad = _ref3.pad,\n    scale = _ref3.scale,\n    multiline = _ref3.multiline,\n    label = _ref3.label,\n    height = _ref3.height,\n    offset = _ref3.offset,\n    dense = _ref3.dense,\n    fontSize = _ref3.fontSize,\n    isAndroid = _ref3.isAndroid,\n    styles = _ref3.styles;\n  var result = pad;\n  var topResult = result;\n  var bottomResult = result;\n  var paddingTop = styles.paddingTop,\n    paddingBottom = styles.paddingBottom;\n  var refFontSize = scale * fontSize;\n  if (!multiline) {\n    if (label) {\n      return {\n        paddingTop: paddingTop,\n        paddingBottom: paddingBottom\n      };\n    }\n    return {\n      paddingTop: result,\n      paddingBottom: result\n    };\n  }\n  if (label) {\n    topResult = paddingTop;\n    bottomResult = paddingBottom;\n    if (!isAndroid) {\n      if (dense) {\n        topResult += scale < 1 ? Math.min(result, refFontSize * scale) - result / 2 : Math.min(result, refFontSize * scale) - result / 2;\n      }\n      if (!dense) {\n        topResult += scale < 1 ? Math.min(offset / 2, refFontSize * scale) : Math.min(result, refFontSize * scale) - offset / 2;\n      }\n    }\n    topResult = Math.floor(topResult);\n  } else {\n    if (height) {\n      return {\n        paddingTop: Math.max(0, (height - fontSize) / 2),\n        paddingBottom: Math.max(0, (height - fontSize) / 2)\n      };\n    }\n    if (!isAndroid) {\n      if (dense) {\n        result += scale < 1 ? Math.min(offset / 2, fontSize / 2 * scale) : Math.min(offset / 2, scale);\n      }\n      if (!dense) {\n        result += scale < 1 ? Math.min(offset, fontSize * scale) : Math.min(fontSize, offset / 2 * scale);\n      }\n      result = Math.floor(result);\n      topResult = result;\n      bottomResult = result;\n    }\n  }\n  return {\n    paddingTop: Math.max(0, topResult),\n    paddingBottom: Math.max(0, bottomResult)\n  };\n};\nexport function calculateFlatAffixTopPosition(_ref4) {\n  var height = _ref4.height,\n    paddingTop = _ref4.paddingTop,\n    paddingBottom = _ref4.paddingBottom,\n    affixHeight = _ref4.affixHeight;\n  var inputHeightWithoutPadding = height - paddingTop - paddingBottom;\n  var halfOfTheInputHeightDecreasedByAffixHeight = (inputHeightWithoutPadding - affixHeight) / 2;\n  return paddingTop + halfOfTheInputHeightDecreasedByAffixHeight;\n}\nexport function calculateOutlinedIconAndAffixTopPosition(_ref5) {\n  var height = _ref5.height,\n    affixHeight = _ref5.affixHeight,\n    labelYOffset = _ref5.labelYOffset;\n  return (height - affixHeight + labelYOffset) / 2;\n}\nexport var calculateFlatInputHorizontalPadding = function calculateFlatInputHorizontalPadding(_ref6) {\n  var adornmentConfig = _ref6.adornmentConfig,\n    isV3 = _ref6.isV3;\n  var _getConstants = getConstants(isV3),\n    LABEL_PADDING_HORIZONTAL = _getConstants.LABEL_PADDING_HORIZONTAL,\n    ADORNMENT_OFFSET = _getConstants.ADORNMENT_OFFSET,\n    FLAT_INPUT_OFFSET = _getConstants.FLAT_INPUT_OFFSET;\n  var paddingLeft = LABEL_PADDING_HORIZONTAL;\n  var paddingRight = LABEL_PADDING_HORIZONTAL;\n  adornmentConfig.forEach(function (_ref7) {\n    var type = _ref7.type,\n      side = _ref7.side;\n    if (type === AdornmentType.Icon && side === AdornmentSide.Left) {\n      paddingLeft = ADORNMENT_SIZE + ADORNMENT_OFFSET + FLAT_INPUT_OFFSET;\n    } else if (side === AdornmentSide.Right) {\n      if (type === AdornmentType.Affix) {\n        paddingRight = ADORNMENT_SIZE + ADORNMENT_OFFSET + FLAT_INPUT_OFFSET;\n      } else if (type === AdornmentType.Icon) {\n        paddingRight = ADORNMENT_SIZE + ADORNMENT_OFFSET + FLAT_INPUT_OFFSET;\n      }\n    }\n  });\n  return {\n    paddingLeft: paddingLeft,\n    paddingRight: paddingRight\n  };\n};\nvar getInputTextColor = function getInputTextColor(_ref8) {\n  var theme = _ref8.theme,\n    textColor = _ref8.textColor,\n    disabled = _ref8.disabled;\n  if (textColor) {\n    return textColor;\n  }\n  if (theme.isV3) {\n    if (disabled) {\n      return theme.colors.onSurfaceDisabled;\n    }\n    return theme.colors.onSurface;\n  }\n  if (disabled) {\n    return color(theme.colors.text).alpha(0.54).rgb().string();\n  }\n  return theme.colors.text;\n};\nvar getActiveColor = function getActiveColor(_ref9) {\n  var theme = _ref9.theme,\n    disabled = _ref9.disabled,\n    error = _ref9.error,\n    activeUnderlineColor = _ref9.activeUnderlineColor,\n    activeOutlineColor = _ref9.activeOutlineColor,\n    mode = _ref9.mode;\n  var isFlat = mode === 'flat';\n  var modeColor = isFlat ? activeUnderlineColor : activeOutlineColor;\n  if (error) {\n    return theme.colors.error;\n  }\n  if (modeColor) {\n    return modeColor;\n  }\n  if (disabled) {\n    if (theme.isV3) {\n      return theme.colors.onSurfaceDisabled;\n    }\n    return color(theme.colors.text).alpha(0.54).rgb().string();\n  }\n  return theme.colors.primary;\n};\nvar getPlaceholderColor = function getPlaceholderColor(_ref0) {\n  var theme = _ref0.theme,\n    disabled = _ref0.disabled;\n  if (theme.isV3) {\n    if (disabled) {\n      return theme.colors.onSurfaceDisabled;\n    }\n    return theme.colors.onSurfaceVariant;\n  }\n  if (disabled) {\n    return theme.colors.disabled;\n  }\n  return theme.colors.placeholder;\n};\nvar getSelectionColor = function getSelectionColor(_ref1) {\n  var activeColor = _ref1.activeColor,\n    customSelectionColor = _ref1.customSelectionColor;\n  if (typeof customSelectionColor !== 'undefined') {\n    return customSelectionColor;\n  }\n  if (Platform.OS === 'android') {\n    return color(activeColor).alpha(0.54).rgb().string();\n  }\n  return activeColor;\n};\nvar getFlatBackgroundColor = function getFlatBackgroundColor(_ref10) {\n  var theme = _ref10.theme,\n    disabled = _ref10.disabled;\n  var _theme$colors, _theme$colors2;\n  if (theme.isV3) {\n    if (disabled) {\n      return color(theme.colors.onSurface).alpha(0.04).rgb().string();\n    } else {\n      return theme.colors.surfaceVariant;\n    }\n  }\n  if (disabled) {\n    return undefined;\n  }\n  return theme.dark ? color((_theme$colors = theme.colors) === null || _theme$colors === void 0 ? void 0 : _theme$colors.background).lighten(0.24).rgb().string() : color((_theme$colors2 = theme.colors) === null || _theme$colors2 === void 0 ? void 0 : _theme$colors2.background).darken(0.06).rgb().string();\n};\nvar getFlatUnderlineColor = function getFlatUnderlineColor(_ref11) {\n  var theme = _ref11.theme,\n    disabled = _ref11.disabled,\n    underlineColor = _ref11.underlineColor;\n  if (!disabled && underlineColor) {\n    return underlineColor;\n  }\n  if (theme.isV3) {\n    if (disabled) {\n      return theme.colors.onSurfaceDisabled;\n    }\n    return theme.colors.onSurfaceVariant;\n  }\n  if (disabled) {\n    return 'transparent';\n  }\n  return theme.colors.disabled;\n};\nvar getOutlinedOutlineInputColor = function getOutlinedOutlineInputColor(_ref12) {\n  var theme = _ref12.theme,\n    disabled = _ref12.disabled,\n    customOutlineColor = _ref12.customOutlineColor;\n  var isTransparent = color(customOutlineColor).alpha() === 0;\n  if (!disabled && customOutlineColor) {\n    return customOutlineColor;\n  }\n  if (theme.isV3) {\n    if (disabled) {\n      if (theme.dark) {\n        return 'transparent';\n      }\n      return theme.colors.surfaceDisabled;\n    }\n    return theme.colors.outline;\n  }\n  if (disabled) {\n    if (isTransparent) {\n      return customOutlineColor;\n    }\n    return theme.colors.disabled;\n  }\n  return theme.colors.placeholder;\n};\nexport var getFlatInputColors = function getFlatInputColors(_ref13) {\n  var underlineColor = _ref13.underlineColor,\n    activeUnderlineColor = _ref13.activeUnderlineColor,\n    customSelectionColor = _ref13.customSelectionColor,\n    textColor = _ref13.textColor,\n    disabled = _ref13.disabled,\n    error = _ref13.error,\n    theme = _ref13.theme;\n  var baseFlatColorProps = {\n    theme: theme,\n    disabled: disabled\n  };\n  var activeColor = getActiveColor(_objectSpread(_objectSpread({}, baseFlatColorProps), {}, {\n    error: error,\n    activeUnderlineColor: activeUnderlineColor,\n    mode: 'flat'\n  }));\n  return {\n    inputTextColor: getInputTextColor(_objectSpread(_objectSpread({}, baseFlatColorProps), {}, {\n      textColor: textColor\n    })),\n    activeColor: activeColor,\n    underlineColorCustom: getFlatUnderlineColor(_objectSpread(_objectSpread({}, baseFlatColorProps), {}, {\n      underlineColor: underlineColor\n    })),\n    placeholderColor: getPlaceholderColor(baseFlatColorProps),\n    selectionColor: getSelectionColor({\n      activeColor: activeColor,\n      customSelectionColor: customSelectionColor\n    }),\n    errorColor: theme.colors.error,\n    backgroundColor: getFlatBackgroundColor(baseFlatColorProps)\n  };\n};\nexport var getOutlinedInputColors = function getOutlinedInputColors(_ref14) {\n  var activeOutlineColor = _ref14.activeOutlineColor,\n    customOutlineColor = _ref14.customOutlineColor,\n    customSelectionColor = _ref14.customSelectionColor,\n    textColor = _ref14.textColor,\n    disabled = _ref14.disabled,\n    error = _ref14.error,\n    theme = _ref14.theme;\n  var baseOutlinedColorProps = {\n    theme: theme,\n    disabled: disabled\n  };\n  var activeColor = getActiveColor(_objectSpread(_objectSpread({}, baseOutlinedColorProps), {}, {\n    error: error,\n    activeOutlineColor: activeOutlineColor,\n    mode: 'outlined'\n  }));\n  return {\n    inputTextColor: getInputTextColor(_objectSpread(_objectSpread({}, baseOutlinedColorProps), {}, {\n      textColor: textColor\n    })),\n    activeColor: activeColor,\n    outlineColor: getOutlinedOutlineInputColor(_objectSpread(_objectSpread({}, baseOutlinedColorProps), {}, {\n      customOutlineColor: customOutlineColor\n    })),\n    placeholderColor: getPlaceholderColor(baseOutlinedColorProps),\n    selectionColor: getSelectionColor({\n      activeColor: activeColor,\n      customSelectionColor: customSelectionColor\n    }),\n    errorColor: theme.colors.error\n  };\n};\nexport var getConstants = function getConstants(isV3) {\n  var AFFIX_OFFSET;\n  var ICON_OFFSET;\n  var LABEL_PADDING_TOP;\n  var LABEL_PADDING_HORIZONTAL;\n  var FLAT_INPUT_OFFSET;\n  var MIN_HEIGHT;\n  var INPUT_PADDING_HORIZONTAL;\n  var ADORNMENT_OFFSET;\n  var OUTLINED_INPUT_OFFSET;\n  if (isV3) {\n    AFFIX_OFFSET = MD3_AFFIX_OFFSET;\n    ICON_OFFSET = MD3_ICON_OFFSET;\n    LABEL_PADDING_TOP = MD3_LABEL_PADDING_TOP;\n    LABEL_PADDING_HORIZONTAL = MD3_LABEL_PADDING_HORIZONTAL;\n    FLAT_INPUT_OFFSET = MD3_FLAT_INPUT_OFFSET;\n    MIN_HEIGHT = MD3_MIN_HEIGHT;\n    INPUT_PADDING_HORIZONTAL = MD3_INPUT_PADDING_HORIZONTAL;\n    ADORNMENT_OFFSET = MD3_ADORNMENT_OFFSET;\n    OUTLINED_INPUT_OFFSET = MD3_OUTLINED_INPUT_OFFSET;\n  } else {\n    AFFIX_OFFSET = MD2_AFFIX_OFFSET;\n    ICON_OFFSET = MD2_ICON_OFFSET;\n    LABEL_PADDING_TOP = MD2_LABEL_PADDING_TOP;\n    LABEL_PADDING_HORIZONTAL = MD2_LABEL_PADDING_HORIZONTAL;\n    FLAT_INPUT_OFFSET = MD2_FLAT_INPUT_OFFSET;\n    MIN_HEIGHT = MD2_MIN_HEIGHT;\n    INPUT_PADDING_HORIZONTAL = MD2_INPUT_PADDING_HORIZONTAL;\n    ADORNMENT_OFFSET = MD2_ADORNMENT_OFFSET;\n    OUTLINED_INPUT_OFFSET = MD2_OUTLINED_INPUT_OFFSET;\n  }\n  return {\n    AFFIX_OFFSET: AFFIX_OFFSET,\n    ICON_OFFSET: ICON_OFFSET,\n    LABEL_PADDING_TOP: LABEL_PADDING_TOP,\n    LABEL_PADDING_HORIZONTAL: LABEL_PADDING_HORIZONTAL,\n    FLAT_INPUT_OFFSET: FLAT_INPUT_OFFSET,\n    MIN_HEIGHT: MIN_HEIGHT,\n    INPUT_PADDING_HORIZONTAL: INPUT_PADDING_HORIZONTAL,\n    ADORNMENT_OFFSET: ADORNMENT_OFFSET,\n    OUTLINED_INPUT_OFFSET: OUTLINED_INPUT_OFFSET,\n    MIN_WIDTH: MIN_WIDTH\n  };\n};", "map": {"version": 3, "names": ["color", "AdornmentSide", "AdornmentType", "MIN_WIDTH", "ADORNMENT_SIZE", "MD2_ADORNMENT_OFFSET", "MD2_AFFIX_OFFSET", "MD2_FLAT_INPUT_OFFSET", "MD2_ICON_OFFSET", "MD2_INPUT_PADDING_HORIZONTAL", "MD2_LABEL_PADDING_HORIZONTAL", "MD2_LABEL_PADDING_TOP", "MD2_MIN_HEIGHT", "MD2_OUTLINED_INPUT_OFFSET", "MD3_ADORNMENT_OFFSET", "MD3_AFFIX_OFFSET", "MD3_FLAT_INPUT_OFFSET", "MD3_ICON_OFFSET", "MD3_INPUT_PADDING_HORIZONTAL", "MD3_LABEL_PADDING_HORIZONTAL", "MD3_LABEL_PADDING_TOP", "MD3_MIN_HEIGHT", "MD3_OUTLINED_INPUT_OFFSET", "calculateLabelTopPosition", "labelHeight", "height", "arguments", "length", "undefined", "optionalPadding", "customHeight", "Math", "floor", "calculateInputHeight", "minHeight", "finalHeight", "calculatePadding", "props", "_props$multiline", "multiline", "result", "calculateTextAreaPadding", "calculateInputPadding", "max", "dense", "_ref", "topPosition", "fontSize", "scale", "offset", "isAndroid", "refFontSize", "min", "adjustPaddingOut", "_ref2", "pad", "label", "lineHeight", "fontHeight", "refFontHeight", "paddingTop", "paddingBottom", "adjustPaddingFlat", "_ref3", "styles", "topResult", "bottomResult", "calculateFlatAffixTopPosition", "_ref4", "affixHeight", "inputHeightWithoutPadding", "halfOfTheInputHeightDecreasedByAffixHeight", "calculateOutlinedIconAndAffixTopPosition", "_ref5", "labelYOffset", "calculateFlatInputHorizontalPadding", "_ref6", "adornmentConfig", "isV3", "_getConstants", "getConstants", "LABEL_PADDING_HORIZONTAL", "ADORNMENT_OFFSET", "FLAT_INPUT_OFFSET", "paddingLeft", "paddingRight", "for<PERSON>ach", "_ref7", "type", "side", "Icon", "Left", "Right", "Affix", "getInputTextColor", "_ref8", "theme", "textColor", "disabled", "colors", "onSurfaceDisabled", "onSurface", "text", "alpha", "rgb", "string", "getActiveColor", "_ref9", "error", "activeUnderlineColor", "activeOutlineColor", "mode", "is<PERSON><PERSON>", "modeColor", "primary", "getPlaceholderColor", "_ref0", "onSurfaceVariant", "placeholder", "getSelectionColor", "_ref1", "activeColor", "customSelectionColor", "Platform", "OS", "getFlatBackgroundColor", "_ref10", "_theme$colors", "_theme$colors2", "surfaceVariant", "dark", "background", "lighten", "darken", "getFlatUnderlineColor", "_ref11", "underlineColor", "getOutlinedOutlineInputColor", "_ref12", "customOutlineColor", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "surfaceDisabled", "outline", "getFlatInputColors", "_ref13", "baseFlatColorProps", "_objectSpread", "inputTextColor", "underlineColorCustom", "placeholderColor", "selectionColor", "errorColor", "backgroundColor", "getOutlinedInputColors", "_ref14", "baseOutlinedColorProps", "outlineColor", "AFFIX_OFFSET", "ICON_OFFSET", "LABEL_PADDING_TOP", "MIN_HEIGHT", "INPUT_PADDING_HORIZONTAL", "OUTLINED_INPUT_OFFSET"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\TextInput\\helpers.tsx"], "sourcesContent": ["import { Platform } from 'react-native';\n\nimport color from 'color';\n\nimport { AdornmentSide, AdornmentType } from './Adornment/enums';\nimport type { AdornmentConfig } from './Adornment/types';\nimport {\n  MIN_WIDTH,\n  ADORNMENT_SIZE,\n  MD2_ADORNMENT_OFFSET,\n  MD2_AFFIX_OFFSET,\n  MD2_FLAT_INPUT_OFFSET,\n  MD2_ICON_OFFSET,\n  MD2_INPUT_PADDING_HORIZONTAL,\n  MD2_LABEL_PADDING_HORIZONTAL,\n  MD2_LABEL_PADDING_TOP,\n  MD2_MIN_HEIGHT,\n  MD2_OUTLINED_INPUT_OFFSET,\n  MD3_ADORNMENT_OFFSET,\n  MD3_AFFIX_OFFSET,\n  MD3_FLAT_INPUT_OFFSET,\n  MD3_ICON_OFFSET,\n  MD3_INPUT_PADDING_HORIZONTAL,\n  MD3_LABEL_PADDING_HORIZONTAL,\n  MD3_LABEL_PADDING_TOP,\n  MD3_MIN_HEIGHT,\n  MD3_OUTLINED_INPUT_OFFSET,\n} from './constants';\nimport type { TextInputLabelProp } from './types';\nimport type { InternalTheme } from '../../types';\n\ntype PaddingProps = {\n  height: number | null;\n  labelHalfHeight: number;\n  multiline: boolean | null;\n  dense: boolean | null;\n  topPosition: number;\n  fontSize: number;\n  lineHeight?: number;\n  label?: TextInputLabelProp | null;\n  scale: number;\n  offset: number;\n  isAndroid: boolean;\n  styles: { paddingTop: number; paddingBottom: number };\n};\n\ntype AdjProps = PaddingProps & {\n  pad: number;\n};\n\nexport type Padding = { paddingTop: number; paddingBottom: number };\n\nexport const calculateLabelTopPosition = (\n  labelHeight: number,\n  height: number = 0,\n  optionalPadding: number = 0\n): number => {\n  const customHeight = height > 0 ? height : 0;\n\n  return Math.floor((customHeight - labelHeight) / 2 + optionalPadding);\n};\n\nexport const calculateInputHeight = (\n  labelHeight: number,\n  height: any = 0,\n  minHeight: number\n): number => {\n  const finalHeight = height > 0 ? height : labelHeight;\n\n  if (height > 0) return height;\n  return finalHeight < minHeight ? minHeight : finalHeight;\n};\n\nexport const calculatePadding = (props: PaddingProps): number => {\n  const { height, multiline = false } = props;\n\n  let result = 0;\n\n  if (multiline) {\n    if (height && multiline) {\n      result = calculateTextAreaPadding(props);\n    } else {\n      result = calculateInputPadding(props);\n    }\n  }\n\n  return Math.max(0, result);\n};\n\nconst calculateTextAreaPadding = (props: PaddingProps) => {\n  const { dense } = props;\n\n  return dense ? 10 : 20;\n};\n\nconst calculateInputPadding = ({\n  topPosition,\n  fontSize,\n  multiline,\n  scale,\n  dense,\n  offset,\n  isAndroid,\n}: PaddingProps): number => {\n  const refFontSize = scale * fontSize;\n  let result = Math.floor(topPosition / 2);\n\n  result =\n    result +\n    Math.floor((refFontSize - fontSize) / 2) -\n    (scale < 1 ? offset / 2 : 0);\n\n  if (multiline && isAndroid)\n    result = Math.min(dense ? offset / 2 : offset, result);\n\n  return result;\n};\n\nexport const adjustPaddingOut = ({\n  pad,\n  multiline,\n  label,\n  scale,\n  height,\n  fontSize,\n  lineHeight,\n  dense,\n  offset,\n  isAndroid,\n}: AdjProps): Padding => {\n  const fontHeight = lineHeight ?? fontSize;\n  const refFontHeight = scale * fontSize;\n  let result = pad;\n\n  if (!isAndroid && height && !multiline) {\n    return {\n      paddingTop: Math.max(0, (height - fontHeight) / 2),\n      paddingBottom: Math.max(0, (height - fontHeight) / 2),\n    };\n  }\n  if (!isAndroid && multiline) {\n    if (dense) {\n      if (label) {\n        result += scale < 1 ? Math.min(offset, (refFontHeight / 2) * scale) : 0;\n      } else {\n        result += 0;\n      }\n    }\n    if (!dense) {\n      if (label) {\n        result +=\n          scale < 1\n            ? Math.min(offset, refFontHeight * scale)\n            : Math.min(offset / 2, refFontHeight * scale);\n      } else {\n        result += scale < 1 ? Math.min(offset / 2, refFontHeight * scale) : 0;\n      }\n    }\n    result = Math.floor(result);\n  }\n  return { paddingTop: result, paddingBottom: result };\n};\n\nexport const adjustPaddingFlat = ({\n  pad,\n  scale,\n  multiline,\n  label,\n  height,\n  offset,\n  dense,\n  fontSize,\n  isAndroid,\n  styles,\n}: AdjProps): Padding => {\n  let result = pad;\n  let topResult = result;\n  let bottomResult = result;\n  const { paddingTop, paddingBottom } = styles;\n  const refFontSize = scale * fontSize;\n\n  if (!multiline) {\n    // do not modify padding if input is not multiline\n    if (label) {\n      // return const style for flat input with label\n      return { paddingTop, paddingBottom };\n    }\n    // return pad for flat input without label\n    return { paddingTop: result, paddingBottom: result };\n  }\n\n  if (label) {\n    // add paddings passed from styles\n    topResult = paddingTop;\n    bottomResult = paddingBottom;\n\n    // adjust top padding for iOS\n    if (!isAndroid) {\n      if (dense) {\n        topResult +=\n          scale < 1\n            ? Math.min(result, refFontSize * scale) - result / 2\n            : Math.min(result, refFontSize * scale) - result / 2;\n      }\n      if (!dense) {\n        topResult +=\n          scale < 1\n            ? Math.min(offset / 2, refFontSize * scale)\n            : Math.min(result, refFontSize * scale) - offset / 2;\n      }\n    }\n    topResult = Math.floor(topResult);\n  } else {\n    if (height) {\n      // center text when height is passed\n      return {\n        paddingTop: Math.max(0, (height - fontSize) / 2),\n        paddingBottom: Math.max(0, (height - fontSize) / 2),\n      };\n    }\n    // adjust paddings for iOS if no label\n    if (!isAndroid) {\n      if (dense) {\n        result +=\n          scale < 1\n            ? Math.min(offset / 2, (fontSize / 2) * scale)\n            : Math.min(offset / 2, scale);\n      }\n      if (!dense) {\n        result +=\n          scale < 1\n            ? Math.min(offset, fontSize * scale)\n            : Math.min(fontSize, (offset / 2) * scale);\n      }\n\n      result = Math.floor(result);\n      topResult = result;\n      bottomResult = result;\n    }\n  }\n\n  return {\n    paddingTop: Math.max(0, topResult),\n    paddingBottom: Math.max(0, bottomResult),\n  };\n};\n\nexport function calculateFlatAffixTopPosition({\n  height,\n  paddingTop,\n  paddingBottom,\n  affixHeight,\n}: {\n  height: number;\n  paddingTop: number;\n  paddingBottom: number;\n  affixHeight: number;\n}): number {\n  const inputHeightWithoutPadding = height - paddingTop - paddingBottom;\n\n  const halfOfTheInputHeightDecreasedByAffixHeight =\n    (inputHeightWithoutPadding - affixHeight) / 2;\n\n  return paddingTop + halfOfTheInputHeightDecreasedByAffixHeight;\n}\n\nexport function calculateOutlinedIconAndAffixTopPosition({\n  height,\n  affixHeight,\n  labelYOffset,\n}: {\n  height: number;\n  affixHeight: number;\n  labelYOffset: number;\n}): number {\n  return (height - affixHeight + labelYOffset) / 2;\n}\n\nexport const calculateFlatInputHorizontalPadding = ({\n  adornmentConfig,\n  isV3,\n}: {\n  adornmentConfig: AdornmentConfig[];\n  isV3?: boolean;\n}) => {\n  const { LABEL_PADDING_HORIZONTAL, ADORNMENT_OFFSET, FLAT_INPUT_OFFSET } =\n    getConstants(isV3);\n\n  let paddingLeft = LABEL_PADDING_HORIZONTAL;\n  let paddingRight = LABEL_PADDING_HORIZONTAL;\n\n  adornmentConfig.forEach(({ type, side }) => {\n    if (type === AdornmentType.Icon && side === AdornmentSide.Left) {\n      paddingLeft = ADORNMENT_SIZE + ADORNMENT_OFFSET + FLAT_INPUT_OFFSET;\n    } else if (side === AdornmentSide.Right) {\n      if (type === AdornmentType.Affix) {\n        paddingRight = ADORNMENT_SIZE + ADORNMENT_OFFSET + FLAT_INPUT_OFFSET;\n      } else if (type === AdornmentType.Icon) {\n        paddingRight = ADORNMENT_SIZE + ADORNMENT_OFFSET + FLAT_INPUT_OFFSET;\n      }\n    }\n  });\n\n  return { paddingLeft, paddingRight };\n};\n\ntype BaseProps = {\n  theme: InternalTheme;\n  disabled?: boolean;\n};\n\ntype Mode = 'flat' | 'outlined';\n\nconst getInputTextColor = ({\n  theme,\n  textColor,\n  disabled,\n}: BaseProps & { textColor?: string }) => {\n  if (textColor) {\n    return textColor;\n  }\n\n  if (theme.isV3) {\n    if (disabled) {\n      return theme.colors.onSurfaceDisabled;\n    }\n\n    return theme.colors.onSurface;\n  }\n\n  if (disabled) {\n    return color(theme.colors.text).alpha(0.54).rgb().string();\n  }\n\n  return theme.colors.text;\n};\n\nconst getActiveColor = ({\n  theme,\n  disabled,\n  error,\n  activeUnderlineColor,\n  activeOutlineColor,\n  mode,\n}: BaseProps & {\n  error?: boolean;\n  activeUnderlineColor?: string;\n  activeOutlineColor?: string;\n  mode?: Mode;\n}) => {\n  const isFlat = mode === 'flat';\n  const modeColor = isFlat ? activeUnderlineColor : activeOutlineColor;\n\n  if (error) {\n    return theme.colors.error;\n  }\n\n  if (modeColor) {\n    return modeColor;\n  }\n\n  if (disabled) {\n    if (theme.isV3) {\n      return theme.colors.onSurfaceDisabled;\n    }\n\n    return color(theme.colors.text).alpha(0.54).rgb().string();\n  }\n\n  return theme.colors.primary;\n};\n\nconst getPlaceholderColor = ({ theme, disabled }: BaseProps) => {\n  if (theme.isV3) {\n    if (disabled) {\n      return theme.colors.onSurfaceDisabled;\n    }\n\n    return theme.colors.onSurfaceVariant;\n  }\n\n  if (disabled) {\n    return theme.colors.disabled;\n  }\n\n  return theme.colors.placeholder;\n};\n\nconst getSelectionColor = ({\n  activeColor,\n  customSelectionColor,\n}: {\n  activeColor: string;\n  customSelectionColor?: string;\n}) => {\n  if (typeof customSelectionColor !== 'undefined') {\n    return customSelectionColor;\n  }\n\n  if (Platform.OS === 'android') {\n    return color(activeColor).alpha(0.54).rgb().string();\n  }\n\n  return activeColor;\n};\n\nconst getFlatBackgroundColor = ({ theme, disabled }: BaseProps) => {\n  if (theme.isV3) {\n    if (disabled) {\n      return color(theme.colors.onSurface).alpha(0.04).rgb().string();\n    } else {\n      return theme.colors.surfaceVariant;\n    }\n  }\n\n  if (disabled) {\n    return undefined;\n  }\n\n  return theme.dark\n    ? color(theme.colors?.background).lighten(0.24).rgb().string()\n    : color(theme.colors?.background).darken(0.06).rgb().string();\n};\n\nconst getFlatUnderlineColor = ({\n  theme,\n  disabled,\n  underlineColor,\n}: BaseProps & { underlineColor?: string }) => {\n  if (!disabled && underlineColor) {\n    return underlineColor;\n  }\n\n  if (theme.isV3) {\n    if (disabled) {\n      return theme.colors.onSurfaceDisabled;\n    }\n\n    return theme.colors.onSurfaceVariant;\n  }\n\n  if (disabled) {\n    return 'transparent';\n  }\n\n  return theme.colors.disabled;\n};\n\nconst getOutlinedOutlineInputColor = ({\n  theme,\n  disabled,\n  customOutlineColor,\n}: BaseProps & { customOutlineColor?: string }) => {\n  const isTransparent = color(customOutlineColor).alpha() === 0;\n\n  if (!disabled && customOutlineColor) {\n    return customOutlineColor;\n  }\n\n  if (theme.isV3) {\n    if (disabled) {\n      if (theme.dark) {\n        return 'transparent';\n      }\n      return theme.colors.surfaceDisabled;\n    }\n\n    return theme.colors.outline;\n  }\n\n  if (disabled) {\n    if (isTransparent) {\n      return customOutlineColor;\n    }\n    return theme.colors.disabled;\n  }\n  return theme.colors.placeholder;\n};\n\nexport const getFlatInputColors = ({\n  underlineColor,\n  activeUnderlineColor,\n  customSelectionColor,\n  textColor,\n  disabled,\n  error,\n  theme,\n}: {\n  underlineColor?: string;\n  activeUnderlineColor?: string;\n  customSelectionColor?: string;\n  textColor?: string;\n  disabled?: boolean;\n  error?: boolean;\n  theme: InternalTheme;\n}) => {\n  const baseFlatColorProps = { theme, disabled };\n  const activeColor = getActiveColor({\n    ...baseFlatColorProps,\n    error,\n    activeUnderlineColor,\n    mode: 'flat',\n  });\n\n  return {\n    inputTextColor: getInputTextColor({\n      ...baseFlatColorProps,\n      textColor,\n    }),\n    activeColor,\n    underlineColorCustom: getFlatUnderlineColor({\n      ...baseFlatColorProps,\n      underlineColor,\n    }),\n    placeholderColor: getPlaceholderColor(baseFlatColorProps),\n    selectionColor: getSelectionColor({ activeColor, customSelectionColor }),\n    errorColor: theme.colors.error,\n    backgroundColor: getFlatBackgroundColor(baseFlatColorProps),\n  };\n};\n\nexport const getOutlinedInputColors = ({\n  activeOutlineColor,\n  customOutlineColor,\n  customSelectionColor,\n  textColor,\n  disabled,\n  error,\n  theme,\n}: {\n  activeOutlineColor?: string;\n  customOutlineColor?: string;\n  customSelectionColor?: string;\n  textColor?: string;\n  disabled?: boolean;\n  error?: boolean;\n  theme: InternalTheme;\n}) => {\n  const baseOutlinedColorProps = { theme, disabled };\n  const activeColor = getActiveColor({\n    ...baseOutlinedColorProps,\n    error,\n    activeOutlineColor,\n    mode: 'outlined',\n  });\n\n  return {\n    inputTextColor: getInputTextColor({\n      ...baseOutlinedColorProps,\n      textColor,\n    }),\n    activeColor,\n    outlineColor: getOutlinedOutlineInputColor({\n      ...baseOutlinedColorProps,\n      customOutlineColor,\n    }),\n    placeholderColor: getPlaceholderColor(baseOutlinedColorProps),\n    selectionColor: getSelectionColor({ activeColor, customSelectionColor }),\n    errorColor: theme.colors.error,\n  };\n};\n\nexport const getConstants = (isV3?: boolean) => {\n  // Text input affix\n  let AFFIX_OFFSET;\n  // Text input icon\n  let ICON_OFFSET;\n  //Text input flat\n  let LABEL_PADDING_TOP;\n  let LABEL_PADDING_HORIZONTAL;\n  let FLAT_INPUT_OFFSET;\n  let MIN_HEIGHT;\n  // Text input outlined;\n  let INPUT_PADDING_HORIZONTAL;\n  let ADORNMENT_OFFSET;\n  let OUTLINED_INPUT_OFFSET;\n\n  if (isV3) {\n    AFFIX_OFFSET = MD3_AFFIX_OFFSET;\n    ICON_OFFSET = MD3_ICON_OFFSET;\n    LABEL_PADDING_TOP = MD3_LABEL_PADDING_TOP;\n    LABEL_PADDING_HORIZONTAL = MD3_LABEL_PADDING_HORIZONTAL;\n    FLAT_INPUT_OFFSET = MD3_FLAT_INPUT_OFFSET;\n    MIN_HEIGHT = MD3_MIN_HEIGHT;\n    INPUT_PADDING_HORIZONTAL = MD3_INPUT_PADDING_HORIZONTAL;\n    ADORNMENT_OFFSET = MD3_ADORNMENT_OFFSET;\n    OUTLINED_INPUT_OFFSET = MD3_OUTLINED_INPUT_OFFSET;\n  } else {\n    AFFIX_OFFSET = MD2_AFFIX_OFFSET;\n    ICON_OFFSET = MD2_ICON_OFFSET;\n    LABEL_PADDING_TOP = MD2_LABEL_PADDING_TOP;\n    LABEL_PADDING_HORIZONTAL = MD2_LABEL_PADDING_HORIZONTAL;\n    FLAT_INPUT_OFFSET = MD2_FLAT_INPUT_OFFSET;\n    MIN_HEIGHT = MD2_MIN_HEIGHT;\n    INPUT_PADDING_HORIZONTAL = MD2_INPUT_PADDING_HORIZONTAL;\n    ADORNMENT_OFFSET = MD2_ADORNMENT_OFFSET;\n    OUTLINED_INPUT_OFFSET = MD2_OUTLINED_INPUT_OFFSET;\n  }\n\n  return {\n    AFFIX_OFFSET,\n    ICON_OFFSET,\n    LABEL_PADDING_TOP,\n    LABEL_PADDING_HORIZONTAL,\n    FLAT_INPUT_OFFSET,\n    MIN_HEIGHT,\n    INPUT_PADDING_HORIZONTAL,\n    ADORNMENT_OFFSET,\n    OUTLINED_INPUT_OFFSET,\n    MIN_WIDTH,\n  };\n};\n"], "mappings": ";;;;AAEA,OAAOA,KAAK,MAAM,OAAO;AAEzB,SAASC,aAAa,EAAEC,aAAa;AAErC,SACEC,SAAS,EACTC,cAAc,EACdC,oBAAoB,EACpBC,gBAAgB,EAChBC,qBAAqB,EACrBC,eAAe,EACfC,4BAA4B,EAC5BC,4BAA4B,EAC5BC,qBAAqB,EACrBC,cAAc,EACdC,yBAAyB,EACzBC,oBAAoB,EACpBC,gBAAgB,EAChBC,qBAAqB,EACrBC,eAAe,EACfC,4BAA4B,EAC5BC,4BAA4B,EAC5BC,qBAAqB,EACrBC,cAAc,EACdC,yBAAyB;AA0B3B,OAAO,IAAMC,yBAAyB,GAAG,SAA5BA,yBAAyBA,CACpCC,WAAmB,EAGR;EAAA,IAFXC,MAAc,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAAA,IAClBG,eAAuB,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAE3B,IAAMI,YAAY,GAAGL,MAAM,GAAG,CAAC,GAAGA,MAAM,GAAG,CAAC;EAE5C,OAAOM,IAAI,CAACC,KAAK,CAAC,CAACF,YAAY,GAAGN,WAAW,IAAI,CAAC,GAAGK,eAAe,CAAC;AACvE,CAAC;AAED,OAAO,IAAMI,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAC/BT,WAAmB,EAGR;EAAA,IAFXC,MAAW,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAAA,IACfQ,SAAiB,GAAAR,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAEjB,IAAMO,WAAW,GAAGV,MAAM,GAAG,CAAC,GAAGA,MAAM,GAAGD,WAAW;EAErD,IAAIC,MAAM,GAAG,CAAC,EAAE,OAAOA,MAAM;EAC7B,OAAOU,WAAW,GAAGD,SAAS,GAAGA,SAAS,GAAGC,WAAW;AAC1D,CAAC;AAED,OAAO,IAAMC,gBAAgB,GAAI,SAApBA,gBAAgBA,CAAIC,KAAmB,EAAa;EAC/D,IAAQZ,MAAM,GAAwBY,KAAK,CAAnCZ,MAAM;IAAAa,gBAAA,GAAwBD,KAAK,CAA3BE,SAAS;IAATA,SAAS,GAAAD,gBAAA,cAAG,QAAAA,gBAAA;EAE5B,IAAIE,MAAM,GAAG,CAAC;EAEd,IAAID,SAAS,EAAE;IACb,IAAId,MAAM,IAAIc,SAAS,EAAE;MACvBC,MAAM,GAAGC,wBAAwB,CAACJ,KAAK,CAAC;IAC1C,CAAC,MAAM;MACLG,MAAM,GAAGE,qBAAqB,CAACL,KAAK,CAAC;IACvC;EACF;EAEA,OAAON,IAAI,CAACY,GAAG,CAAC,CAAC,EAAEH,MAAM,CAAC;AAC5B,CAAC;AAED,IAAMC,wBAAwB,GAAI,SAA5BA,wBAAwBA,CAAIJ,KAAmB,EAAK;EACxD,IAAQO,KAAA,GAAUP,KAAK,CAAfO,KAAA;EAER,OAAOA,KAAK,GAAG,EAAE,GAAG,EAAE;AACxB,CAAC;AAED,IAAMF,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAAG,IAAA,EAQC;EAAA,IAP1BC,WAAW,GAAAD,IAAA,CAAXC,WAAW;IACXC,QAAQ,GAAAF,IAAA,CAARE,QAAQ;IACRR,SAAS,GAAAM,IAAA,CAATN,SAAS;IACTS,KAAK,GAAAH,IAAA,CAALG,KAAK;IACLJ,KAAK,GAAAC,IAAA,CAALD,KAAK;IACLK,MAAM,GAAAJ,IAAA,CAANI,MAAM;IACNC,SAAA,GAAAL,IAAA,CAAAK,SAAA;EAEA,IAAMC,WAAW,GAAGH,KAAK,GAAGD,QAAQ;EACpC,IAAIP,MAAM,GAAGT,IAAI,CAACC,KAAK,CAACc,WAAW,GAAG,CAAC,CAAC;EAExCN,MAAM,GACJA,MAAM,GACNT,IAAI,CAACC,KAAK,CAAC,CAACmB,WAAW,GAAGJ,QAAQ,IAAI,CAAC,CAAC,IACvCC,KAAK,GAAG,CAAC,GAAGC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;EAE9B,IAAIV,SAAS,IAAIW,SAAS,EACxBV,MAAM,GAAGT,IAAI,CAACqB,GAAG,CAACR,KAAK,GAAGK,MAAM,GAAG,CAAC,GAAGA,MAAM,EAAET,MAAM,CAAC;EAExD,OAAOA,MAAM;AACf,CAAC;AAED,OAAO,IAAMa,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAAC,KAAA,EAWJ;EAAA,IAVvBC,GAAG,GAAAD,KAAA,CAAHC,GAAG;IACHhB,SAAS,GAAAe,KAAA,CAATf,SAAS;IACTiB,KAAK,GAAAF,KAAA,CAALE,KAAK;IACLR,KAAK,GAAAM,KAAA,CAALN,KAAK;IACLvB,MAAM,GAAA6B,KAAA,CAAN7B,MAAM;IACNsB,QAAQ,GAAAO,KAAA,CAARP,QAAQ;IACRU,UAAU,GAAAH,KAAA,CAAVG,UAAU;IACVb,KAAK,GAAAU,KAAA,CAALV,KAAK;IACLK,MAAM,GAAAK,KAAA,CAANL,MAAM;IACNC,SAAA,GAAAI,KAAA,CAAAJ,SAAA;EAEA,IAAMQ,UAAU,GAAGD,UAAU,WAAVA,UAAU,GAAIV,QAAQ;EACzC,IAAMY,aAAa,GAAGX,KAAK,GAAGD,QAAQ;EACtC,IAAIP,MAAM,GAAGe,GAAG;EAEhB,IAAI,CAACL,SAAS,IAAIzB,MAAM,IAAI,CAACc,SAAS,EAAE;IACtC,OAAO;MACLqB,UAAU,EAAE7B,IAAI,CAACY,GAAG,CAAC,CAAC,EAAE,CAAClB,MAAM,GAAGiC,UAAU,IAAI,CAAC,CAAC;MAClDG,aAAa,EAAE9B,IAAI,CAACY,GAAG,CAAC,CAAC,EAAE,CAAClB,MAAM,GAAGiC,UAAU,IAAI,CAAC;IACtD,CAAC;EACH;EACA,IAAI,CAACR,SAAS,IAAIX,SAAS,EAAE;IAC3B,IAAIK,KAAK,EAAE;MACT,IAAIY,KAAK,EAAE;QACThB,MAAM,IAAIQ,KAAK,GAAG,CAAC,GAAGjB,IAAI,CAACqB,GAAG,CAACH,MAAM,EAAGU,aAAa,GAAG,CAAC,GAAIX,KAAK,CAAC,GAAG,CAAC;MACzE,CAAC,MAAM;QACLR,MAAM,IAAI,CAAC;MACb;IACF;IACA,IAAI,CAACI,KAAK,EAAE;MACV,IAAIY,KAAK,EAAE;QACThB,MAAM,IACJQ,KAAK,GAAG,CAAC,GACLjB,IAAI,CAACqB,GAAG,CAACH,MAAM,EAAEU,aAAa,GAAGX,KAAK,CAAC,GACvCjB,IAAI,CAACqB,GAAG,CAACH,MAAM,GAAG,CAAC,EAAEU,aAAa,GAAGX,KAAK,CAAC;MACnD,CAAC,MAAM;QACLR,MAAM,IAAIQ,KAAK,GAAG,CAAC,GAAGjB,IAAI,CAACqB,GAAG,CAACH,MAAM,GAAG,CAAC,EAAEU,aAAa,GAAGX,KAAK,CAAC,GAAG,CAAC;MACvE;IACF;IACAR,MAAM,GAAGT,IAAI,CAACC,KAAK,CAACQ,MAAM,CAAC;EAC7B;EACA,OAAO;IAAEoB,UAAU,EAAEpB,MAAM;IAAEqB,aAAa,EAAErB;EAAO,CAAC;AACtD,CAAC;AAED,OAAO,IAAMsB,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAAC,KAAA,EAWL;EAAA,IAVvBR,GAAG,GAAAQ,KAAA,CAAHR,GAAG;IACHP,KAAK,GAAAe,KAAA,CAALf,KAAK;IACLT,SAAS,GAAAwB,KAAA,CAATxB,SAAS;IACTiB,KAAK,GAAAO,KAAA,CAALP,KAAK;IACL/B,MAAM,GAAAsC,KAAA,CAANtC,MAAM;IACNwB,MAAM,GAAAc,KAAA,CAANd,MAAM;IACNL,KAAK,GAAAmB,KAAA,CAALnB,KAAK;IACLG,QAAQ,GAAAgB,KAAA,CAARhB,QAAQ;IACRG,SAAS,GAAAa,KAAA,CAATb,SAAS;IACTc,MAAA,GAAAD,KAAA,CAAAC,MAAA;EAEA,IAAIxB,MAAM,GAAGe,GAAG;EAChB,IAAIU,SAAS,GAAGzB,MAAM;EACtB,IAAI0B,YAAY,GAAG1B,MAAM;EACzB,IAAQoB,UAAU,GAAoBI,MAAM,CAApCJ,UAAU;IAAEC,aAAA,GAAkBG,MAAM,CAAxBH,aAAA;EACpB,IAAMV,WAAW,GAAGH,KAAK,GAAGD,QAAQ;EAEpC,IAAI,CAACR,SAAS,EAAE;IAEd,IAAIiB,KAAK,EAAE;MAET,OAAO;QAAEI,UAAU,EAAVA,UAAU;QAAEC,aAAA,EAAAA;MAAc,CAAC;IACtC;IAEA,OAAO;MAAED,UAAU,EAAEpB,MAAM;MAAEqB,aAAa,EAAErB;IAAO,CAAC;EACtD;EAEA,IAAIgB,KAAK,EAAE;IAETS,SAAS,GAAGL,UAAU;IACtBM,YAAY,GAAGL,aAAa;IAG5B,IAAI,CAACX,SAAS,EAAE;MACd,IAAIN,KAAK,EAAE;QACTqB,SAAS,IACPjB,KAAK,GAAG,CAAC,GACLjB,IAAI,CAACqB,GAAG,CAACZ,MAAM,EAAEW,WAAW,GAAGH,KAAK,CAAC,GAAGR,MAAM,GAAG,CAAC,GAClDT,IAAI,CAACqB,GAAG,CAACZ,MAAM,EAAEW,WAAW,GAAGH,KAAK,CAAC,GAAGR,MAAM,GAAG,CAAC;MAC1D;MACA,IAAI,CAACI,KAAK,EAAE;QACVqB,SAAS,IACPjB,KAAK,GAAG,CAAC,GACLjB,IAAI,CAACqB,GAAG,CAACH,MAAM,GAAG,CAAC,EAAEE,WAAW,GAAGH,KAAK,CAAC,GACzCjB,IAAI,CAACqB,GAAG,CAACZ,MAAM,EAAEW,WAAW,GAAGH,KAAK,CAAC,GAAGC,MAAM,GAAG,CAAC;MAC1D;IACF;IACAgB,SAAS,GAAGlC,IAAI,CAACC,KAAK,CAACiC,SAAS,CAAC;EACnC,CAAC,MAAM;IACL,IAAIxC,MAAM,EAAE;MAEV,OAAO;QACLmC,UAAU,EAAE7B,IAAI,CAACY,GAAG,CAAC,CAAC,EAAE,CAAClB,MAAM,GAAGsB,QAAQ,IAAI,CAAC,CAAC;QAChDc,aAAa,EAAE9B,IAAI,CAACY,GAAG,CAAC,CAAC,EAAE,CAAClB,MAAM,GAAGsB,QAAQ,IAAI,CAAC;MACpD,CAAC;IACH;IAEA,IAAI,CAACG,SAAS,EAAE;MACd,IAAIN,KAAK,EAAE;QACTJ,MAAM,IACJQ,KAAK,GAAG,CAAC,GACLjB,IAAI,CAACqB,GAAG,CAACH,MAAM,GAAG,CAAC,EAAGF,QAAQ,GAAG,CAAC,GAAIC,KAAK,CAAC,GAC5CjB,IAAI,CAACqB,GAAG,CAACH,MAAM,GAAG,CAAC,EAAED,KAAK,CAAC;MACnC;MACA,IAAI,CAACJ,KAAK,EAAE;QACVJ,MAAM,IACJQ,KAAK,GAAG,CAAC,GACLjB,IAAI,CAACqB,GAAG,CAACH,MAAM,EAAEF,QAAQ,GAAGC,KAAK,CAAC,GAClCjB,IAAI,CAACqB,GAAG,CAACL,QAAQ,EAAGE,MAAM,GAAG,CAAC,GAAID,KAAK,CAAC;MAChD;MAEAR,MAAM,GAAGT,IAAI,CAACC,KAAK,CAACQ,MAAM,CAAC;MAC3ByB,SAAS,GAAGzB,MAAM;MAClB0B,YAAY,GAAG1B,MAAM;IACvB;EACF;EAEA,OAAO;IACLoB,UAAU,EAAE7B,IAAI,CAACY,GAAG,CAAC,CAAC,EAAEsB,SAAS,CAAC;IAClCJ,aAAa,EAAE9B,IAAI,CAACY,GAAG,CAAC,CAAC,EAAEuB,YAAY;EACzC,CAAC;AACH,CAAC;AAED,OAAO,SAASC,6BAA6BA,CAAAC,KAAA,EAUlC;EAAA,IATT3C,MAAM,GAAA2C,KAAA,CAAN3C,MAAM;IACNmC,UAAU,GAAAQ,KAAA,CAAVR,UAAU;IACVC,aAAa,GAAAO,KAAA,CAAbP,aAAa;IACbQ,WAAA,GAAAD,KAAA,CAAAC,WAAA;EAOA,IAAMC,yBAAyB,GAAG7C,MAAM,GAAGmC,UAAU,GAAGC,aAAa;EAErE,IAAMU,0CAA0C,GAC9C,CAACD,yBAAyB,GAAGD,WAAW,IAAI,CAAC;EAE/C,OAAOT,UAAU,GAAGW,0CAA0C;AAChE;AAEA,OAAO,SAASC,wCAAwCA,CAAAC,KAAA,EAQ7C;EAAA,IAPThD,MAAM,GAAAgD,KAAA,CAANhD,MAAM;IACN4C,WAAW,GAAAI,KAAA,CAAXJ,WAAW;IACXK,YAAA,GAAAD,KAAA,CAAAC,YAAA;EAMA,OAAO,CAACjD,MAAM,GAAG4C,WAAW,GAAGK,YAAY,IAAI,CAAC;AAClD;AAEA,OAAO,IAAMC,mCAAmC,GAAG,SAAtCA,mCAAmCA,CAAAC,KAAA,EAM1C;EAAA,IALJC,eAAe,GAAAD,KAAA,CAAfC,eAAe;IACfC,IAAA,GAAAF,KAAA,CAAAE,IAAA;EAKA,IAAAC,aAAA,GACEC,YAAY,CAACF,IAAI,CAAC;IADZG,wBAAwB,GAAAF,aAAA,CAAxBE,wBAAwB;IAAEC,gBAAgB,GAAAH,aAAA,CAAhBG,gBAAgB;IAAEC,iBAAA,GAAAJ,aAAA,CAAAI,iBAAA;EAGpD,IAAIC,WAAW,GAAGH,wBAAwB;EAC1C,IAAII,YAAY,GAAGJ,wBAAwB;EAE3CJ,eAAe,CAACS,OAAO,CAAC,UAAAC,KAAA,EAAoB;IAAA,IAAjBC,IAAI,GAAAD,KAAA,CAAJC,IAAI;MAAEC,IAAA,GAAAF,KAAA,CAAAE,IAAA;IAC/B,IAAID,IAAI,KAAKtF,aAAa,CAACwF,IAAI,IAAID,IAAI,KAAKxF,aAAa,CAAC0F,IAAI,EAAE;MAC9DP,WAAW,GAAGhF,cAAc,GAAG8E,gBAAgB,GAAGC,iBAAiB;IACrE,CAAC,MAAM,IAAIM,IAAI,KAAKxF,aAAa,CAAC2F,KAAK,EAAE;MACvC,IAAIJ,IAAI,KAAKtF,aAAa,CAAC2F,KAAK,EAAE;QAChCR,YAAY,GAAGjF,cAAc,GAAG8E,gBAAgB,GAAGC,iBAAiB;MACtE,CAAC,MAAM,IAAIK,IAAI,KAAKtF,aAAa,CAACwF,IAAI,EAAE;QACtCL,YAAY,GAAGjF,cAAc,GAAG8E,gBAAgB,GAAGC,iBAAiB;MACtE;IACF;EACF,CAAC,CAAC;EAEF,OAAO;IAAEC,WAAW,EAAXA,WAAW;IAAEC,YAAA,EAAAA;EAAa,CAAC;AACtC,CAAC;AASD,IAAMS,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAAC,KAAA,EAImB;EAAA,IAHxCC,KAAK,GAAAD,KAAA,CAALC,KAAK;IACLC,SAAS,GAAAF,KAAA,CAATE,SAAS;IACTC,QAAA,GAAAH,KAAA,CAAAG,QAAA;EAEA,IAAID,SAAS,EAAE;IACb,OAAOA,SAAS;EAClB;EAEA,IAAID,KAAK,CAAClB,IAAI,EAAE;IACd,IAAIoB,QAAQ,EAAE;MACZ,OAAOF,KAAK,CAACG,MAAM,CAACC,iBAAiB;IACvC;IAEA,OAAOJ,KAAK,CAACG,MAAM,CAACE,SAAS;EAC/B;EAEA,IAAIH,QAAQ,EAAE;IACZ,OAAOlG,KAAK,CAACgG,KAAK,CAACG,MAAM,CAACG,IAAI,CAAC,CAACC,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EAC5D;EAEA,OAAOT,KAAK,CAACG,MAAM,CAACG,IAAI;AAC1B,CAAC;AAED,IAAMI,cAAc,GAAG,SAAjBA,cAAcA,CAAAC,KAAA,EAYd;EAAA,IAXJX,KAAK,GAAAW,KAAA,CAALX,KAAK;IACLE,QAAQ,GAAAS,KAAA,CAART,QAAQ;IACRU,KAAK,GAAAD,KAAA,CAALC,KAAK;IACLC,oBAAoB,GAAAF,KAAA,CAApBE,oBAAoB;IACpBC,kBAAkB,GAAAH,KAAA,CAAlBG,kBAAkB;IAClBC,IAAA,GAAAJ,KAAA,CAAAI,IAAA;EAOA,IAAMC,MAAM,GAAGD,IAAI,KAAK,MAAM;EAC9B,IAAME,SAAS,GAAGD,MAAM,GAAGH,oBAAoB,GAAGC,kBAAkB;EAEpE,IAAIF,KAAK,EAAE;IACT,OAAOZ,KAAK,CAACG,MAAM,CAACS,KAAK;EAC3B;EAEA,IAAIK,SAAS,EAAE;IACb,OAAOA,SAAS;EAClB;EAEA,IAAIf,QAAQ,EAAE;IACZ,IAAIF,KAAK,CAAClB,IAAI,EAAE;MACd,OAAOkB,KAAK,CAACG,MAAM,CAACC,iBAAiB;IACvC;IAEA,OAAOpG,KAAK,CAACgG,KAAK,CAACG,MAAM,CAACG,IAAI,CAAC,CAACC,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EAC5D;EAEA,OAAOT,KAAK,CAACG,MAAM,CAACe,OAAO;AAC7B,CAAC;AAED,IAAMC,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAAC,KAAA,EAAuC;EAAA,IAAjCpB,KAAK,GAAAoB,KAAA,CAALpB,KAAK;IAAEE,QAAA,GAAAkB,KAAA,CAAAlB,QAAA;EACpC,IAAIF,KAAK,CAAClB,IAAI,EAAE;IACd,IAAIoB,QAAQ,EAAE;MACZ,OAAOF,KAAK,CAACG,MAAM,CAACC,iBAAiB;IACvC;IAEA,OAAOJ,KAAK,CAACG,MAAM,CAACkB,gBAAgB;EACtC;EAEA,IAAInB,QAAQ,EAAE;IACZ,OAAOF,KAAK,CAACG,MAAM,CAACD,QAAQ;EAC9B;EAEA,OAAOF,KAAK,CAACG,MAAM,CAACmB,WAAW;AACjC,CAAC;AAED,IAAMC,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAAC,KAAA,EAMjB;EAAA,IALJC,WAAW,GAAAD,KAAA,CAAXC,WAAW;IACXC,oBAAA,GAAAF,KAAA,CAAAE,oBAAA;EAKA,IAAI,OAAOA,oBAAoB,KAAK,WAAW,EAAE;IAC/C,OAAOA,oBAAoB;EAC7B;EAEA,IAAIC,QAAQ,CAACC,EAAE,KAAK,SAAS,EAAE;IAC7B,OAAO5H,KAAK,CAACyH,WAAW,CAAC,CAAClB,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EACtD;EAEA,OAAOgB,WAAW;AACpB,CAAC;AAED,IAAMI,sBAAsB,GAAG,SAAzBA,sBAAsBA,CAAAC,MAAA,EAAuC;EAAA,IAAjC9B,KAAK,GAAA8B,MAAA,CAAL9B,KAAK;IAAEE,QAAA,GAAA4B,MAAA,CAAA5B,QAAA;EAA0B,IAAA6B,aAAA,EAAAC,cAAA;EACjE,IAAIhC,KAAK,CAAClB,IAAI,EAAE;IACd,IAAIoB,QAAQ,EAAE;MACZ,OAAOlG,KAAK,CAACgG,KAAK,CAACG,MAAM,CAACE,SAAS,CAAC,CAACE,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;IACjE,CAAC,MAAM;MACL,OAAOT,KAAK,CAACG,MAAM,CAAC8B,cAAc;IACpC;EACF;EAEA,IAAI/B,QAAQ,EAAE;IACZ,OAAOtE,SAAS;EAClB;EAEA,OAAOoE,KAAK,CAACkC,IAAI,GACblI,KAAK,EAAA+H,aAAA,GAAC/B,KAAK,CAACG,MAAM,cAAA4B,aAAA,uBAAZA,aAAA,CAAcI,UAAU,CAAC,CAACC,OAAO,CAAC,IAAI,CAAC,CAAC5B,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC,GAC5DzG,KAAK,EAAAgI,cAAA,GAAChC,KAAK,CAACG,MAAM,cAAA6B,cAAA,uBAAZA,cAAA,CAAcG,UAAU,CAAC,CAACE,MAAM,CAAC,IAAI,CAAC,CAAC7B,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;AACjE,CAAC;AAED,IAAM6B,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAAC,MAAA,EAIoB;EAAA,IAH7CvC,KAAK,GAAAuC,MAAA,CAALvC,KAAK;IACLE,QAAQ,GAAAqC,MAAA,CAARrC,QAAQ;IACRsC,cAAA,GAAAD,MAAA,CAAAC,cAAA;EAEA,IAAI,CAACtC,QAAQ,IAAIsC,cAAc,EAAE;IAC/B,OAAOA,cAAc;EACvB;EAEA,IAAIxC,KAAK,CAAClB,IAAI,EAAE;IACd,IAAIoB,QAAQ,EAAE;MACZ,OAAOF,KAAK,CAACG,MAAM,CAACC,iBAAiB;IACvC;IAEA,OAAOJ,KAAK,CAACG,MAAM,CAACkB,gBAAgB;EACtC;EAEA,IAAInB,QAAQ,EAAE;IACZ,OAAO,aAAa;EACtB;EAEA,OAAOF,KAAK,CAACG,MAAM,CAACD,QAAQ;AAC9B,CAAC;AAED,IAAMuC,4BAA4B,GAAG,SAA/BA,4BAA4BA,CAAAC,MAAA,EAIiB;EAAA,IAHjD1C,KAAK,GAAA0C,MAAA,CAAL1C,KAAK;IACLE,QAAQ,GAAAwC,MAAA,CAARxC,QAAQ;IACRyC,kBAAA,GAAAD,MAAA,CAAAC,kBAAA;EAEA,IAAMC,aAAa,GAAG5I,KAAK,CAAC2I,kBAAkB,CAAC,CAACpC,KAAK,CAAC,CAAC,KAAK,CAAC;EAE7D,IAAI,CAACL,QAAQ,IAAIyC,kBAAkB,EAAE;IACnC,OAAOA,kBAAkB;EAC3B;EAEA,IAAI3C,KAAK,CAAClB,IAAI,EAAE;IACd,IAAIoB,QAAQ,EAAE;MACZ,IAAIF,KAAK,CAACkC,IAAI,EAAE;QACd,OAAO,aAAa;MACtB;MACA,OAAOlC,KAAK,CAACG,MAAM,CAAC0C,eAAe;IACrC;IAEA,OAAO7C,KAAK,CAACG,MAAM,CAAC2C,OAAO;EAC7B;EAEA,IAAI5C,QAAQ,EAAE;IACZ,IAAI0C,aAAa,EAAE;MACjB,OAAOD,kBAAkB;IAC3B;IACA,OAAO3C,KAAK,CAACG,MAAM,CAACD,QAAQ;EAC9B;EACA,OAAOF,KAAK,CAACG,MAAM,CAACmB,WAAW;AACjC,CAAC;AAED,OAAO,IAAMyB,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAAC,MAAA,EAgBzB;EAAA,IAfJR,cAAc,GAAAQ,MAAA,CAAdR,cAAc;IACd3B,oBAAoB,GAAAmC,MAAA,CAApBnC,oBAAoB;IACpBa,oBAAoB,GAAAsB,MAAA,CAApBtB,oBAAoB;IACpBzB,SAAS,GAAA+C,MAAA,CAAT/C,SAAS;IACTC,QAAQ,GAAA8C,MAAA,CAAR9C,QAAQ;IACRU,KAAK,GAAAoC,MAAA,CAALpC,KAAK;IACLZ,KAAA,GAAAgD,MAAA,CAAAhD,KAAA;EAUA,IAAMiD,kBAAkB,GAAG;IAAEjD,KAAK,EAALA,KAAK;IAAEE,QAAA,EAAAA;EAAS,CAAC;EAC9C,IAAMuB,WAAW,GAAGf,cAAc,CAAAwC,aAAA,CAAAA,aAAA,KAC7BD,kBAAkB;IACrBrC,KAAK,EAALA,KAAK;IACLC,oBAAoB,EAApBA,oBAAoB;IACpBE,IAAI,EAAE;EAAA,EACP,CAAC;EAEF,OAAO;IACLoC,cAAc,EAAErD,iBAAiB,CAAAoD,aAAA,CAAAA,aAAA,KAC5BD,kBAAkB;MACrBhD,SAAA,EAAAA;IAAA,EACD,CAAC;IACFwB,WAAW,EAAXA,WAAW;IACX2B,oBAAoB,EAAEd,qBAAqB,CAAAY,aAAA,CAAAA,aAAA,KACtCD,kBAAkB;MACrBT,cAAA,EAAAA;IAAA,EACD,CAAC;IACFa,gBAAgB,EAAElC,mBAAmB,CAAC8B,kBAAkB,CAAC;IACzDK,cAAc,EAAE/B,iBAAiB,CAAC;MAAEE,WAAW,EAAXA,WAAW;MAAEC,oBAAA,EAAAA;IAAqB,CAAC,CAAC;IACxE6B,UAAU,EAAEvD,KAAK,CAACG,MAAM,CAACS,KAAK;IAC9B4C,eAAe,EAAE3B,sBAAsB,CAACoB,kBAAkB;EAC5D,CAAC;AACH,CAAC;AAED,OAAO,IAAMQ,sBAAsB,GAAG,SAAzBA,sBAAsBA,CAAAC,MAAA,EAgB7B;EAAA,IAfJ5C,kBAAkB,GAAA4C,MAAA,CAAlB5C,kBAAkB;IAClB6B,kBAAkB,GAAAe,MAAA,CAAlBf,kBAAkB;IAClBjB,oBAAoB,GAAAgC,MAAA,CAApBhC,oBAAoB;IACpBzB,SAAS,GAAAyD,MAAA,CAATzD,SAAS;IACTC,QAAQ,GAAAwD,MAAA,CAARxD,QAAQ;IACRU,KAAK,GAAA8C,MAAA,CAAL9C,KAAK;IACLZ,KAAA,GAAA0D,MAAA,CAAA1D,KAAA;EAUA,IAAM2D,sBAAsB,GAAG;IAAE3D,KAAK,EAALA,KAAK;IAAEE,QAAA,EAAAA;EAAS,CAAC;EAClD,IAAMuB,WAAW,GAAGf,cAAc,CAAAwC,aAAA,CAAAA,aAAA,KAC7BS,sBAAsB;IACzB/C,KAAK,EAALA,KAAK;IACLE,kBAAkB,EAAlBA,kBAAkB;IAClBC,IAAI,EAAE;EAAA,EACP,CAAC;EAEF,OAAO;IACLoC,cAAc,EAAErD,iBAAiB,CAAAoD,aAAA,CAAAA,aAAA,KAC5BS,sBAAsB;MACzB1D,SAAA,EAAAA;IAAA,EACD,CAAC;IACFwB,WAAW,EAAXA,WAAW;IACXmC,YAAY,EAAEnB,4BAA4B,CAAAS,aAAA,CAAAA,aAAA,KACrCS,sBAAsB;MACzBhB,kBAAA,EAAAA;IAAA,EACD,CAAC;IACFU,gBAAgB,EAAElC,mBAAmB,CAACwC,sBAAsB,CAAC;IAC7DL,cAAc,EAAE/B,iBAAiB,CAAC;MAAEE,WAAW,EAAXA,WAAW;MAAEC,oBAAA,EAAAA;IAAqB,CAAC,CAAC;IACxE6B,UAAU,EAAEvD,KAAK,CAACG,MAAM,CAACS;EAC3B,CAAC;AACH,CAAC;AAED,OAAO,IAAM5B,YAAY,GAAI,SAAhBA,YAAYA,CAAIF,IAAc,EAAK;EAE9C,IAAI+E,YAAY;EAEhB,IAAIC,WAAW;EAEf,IAAIC,iBAAiB;EACrB,IAAI9E,wBAAwB;EAC5B,IAAIE,iBAAiB;EACrB,IAAI6E,UAAU;EAEd,IAAIC,wBAAwB;EAC5B,IAAI/E,gBAAgB;EACpB,IAAIgF,qBAAqB;EAEzB,IAAIpF,IAAI,EAAE;IACR+E,YAAY,GAAG9I,gBAAgB;IAC/B+I,WAAW,GAAG7I,eAAe;IAC7B8I,iBAAiB,GAAG3I,qBAAqB;IACzC6D,wBAAwB,GAAG9D,4BAA4B;IACvDgE,iBAAiB,GAAGnE,qBAAqB;IACzCgJ,UAAU,GAAG3I,cAAc;IAC3B4I,wBAAwB,GAAG/I,4BAA4B;IACvDgE,gBAAgB,GAAGpE,oBAAoB;IACvCoJ,qBAAqB,GAAG5I,yBAAyB;EACnD,CAAC,MAAM;IACLuI,YAAY,GAAGvJ,gBAAgB;IAC/BwJ,WAAW,GAAGtJ,eAAe;IAC7BuJ,iBAAiB,GAAGpJ,qBAAqB;IACzCsE,wBAAwB,GAAGvE,4BAA4B;IACvDyE,iBAAiB,GAAG5E,qBAAqB;IACzCyJ,UAAU,GAAGpJ,cAAc;IAC3BqJ,wBAAwB,GAAGxJ,4BAA4B;IACvDyE,gBAAgB,GAAG7E,oBAAoB;IACvC6J,qBAAqB,GAAGrJ,yBAAyB;EACnD;EAEA,OAAO;IACLgJ,YAAY,EAAZA,YAAY;IACZC,WAAW,EAAXA,WAAW;IACXC,iBAAiB,EAAjBA,iBAAiB;IACjB9E,wBAAwB,EAAxBA,wBAAwB;IACxBE,iBAAiB,EAAjBA,iBAAiB;IACjB6E,UAAU,EAAVA,UAAU;IACVC,wBAAwB,EAAxBA,wBAAwB;IACxB/E,gBAAgB,EAAhBA,gBAAgB;IAChBgF,qBAAqB,EAArBA,qBAAqB;IACrB/J,SAAA,EAAAA;EACF,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}