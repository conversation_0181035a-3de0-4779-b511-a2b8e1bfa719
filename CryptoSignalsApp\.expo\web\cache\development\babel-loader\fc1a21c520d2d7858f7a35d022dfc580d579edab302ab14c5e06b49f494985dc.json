{"ast": null, "code": "import * as React from 'react';\nexport default function useChildListeners() {\n  var _React$useRef = React.useRef({\n      action: [],\n      focus: []\n    }),\n    listeners = _React$useRef.current;\n  var addListener = React.useCallback(function (type, listener) {\n    listeners[type].push(listener);\n    var removed = false;\n    return function () {\n      var index = listeners[type].indexOf(listener);\n      if (!removed && index > -1) {\n        removed = true;\n        listeners[type].splice(index, 1);\n      }\n    };\n  }, [listeners]);\n  return {\n    listeners: listeners,\n    addListener: addListener\n  };\n}", "map": {"version": 3, "names": ["React", "useChildListeners", "_React$useRef", "useRef", "action", "focus", "listeners", "current", "addListener", "useCallback", "type", "listener", "push", "removed", "index", "indexOf", "splice"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\@react-navigation\\core\\src\\useChildListeners.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport type { ListenerMap } from './NavigationBuilderContext';\n\n/**\n * Hook which lets child navigators add action listeners.\n */\nexport default function useChildListeners() {\n  const { current: listeners } = React.useRef<{\n    [K in keyof ListenerMap]: ListenerMap[K][];\n  }>({\n    action: [],\n    focus: [],\n  });\n\n  const addListener = React.useCallback(\n    <T extends keyof ListenerMap>(type: T, listener: ListenerMap[T]) => {\n      listeners[type].push(listener);\n\n      let removed = false;\n      return () => {\n        const index = listeners[type].indexOf(listener);\n\n        if (!removed && index > -1) {\n          removed = true;\n          listeners[type].splice(index, 1);\n        }\n      };\n    },\n    [listeners]\n  );\n\n  return {\n    listeners,\n    addListener,\n  };\n}\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAO9B,eAAe,SAASC,iBAAiBA,CAAA,EAAG;EAC1C,IAAAC,aAAA,GAA+BF,KAAK,CAACG,MAAM,CAExC;MACDC,MAAM,EAAE,EAAE;MACVC,KAAK,EAAE;IACT,CAAC,CAAC;IALeC,SAAA,GAAAJ,aAAA,CAATK,OAAO;EAOf,IAAMC,WAAW,GAAGR,KAAK,CAACS,WAAW,CACnC,UAA8BC,IAAO,EAAEC,QAAwB,EAAK;IAClEL,SAAS,CAACI,IAAI,CAAC,CAACE,IAAI,CAACD,QAAQ,CAAC;IAE9B,IAAIE,OAAO,GAAG,KAAK;IACnB,OAAO,YAAM;MACX,IAAMC,KAAK,GAAGR,SAAS,CAACI,IAAI,CAAC,CAACK,OAAO,CAACJ,QAAQ,CAAC;MAE/C,IAAI,CAACE,OAAO,IAAIC,KAAK,GAAG,CAAC,CAAC,EAAE;QAC1BD,OAAO,GAAG,IAAI;QACdP,SAAS,CAACI,IAAI,CAAC,CAACM,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MAClC;IACF,CAAC;EACH,CAAC,EACD,CAACR,SAAS,CAAC,CACZ;EAED,OAAO;IACLA,SAAS,EAATA,SAAS;IACTE,WAAA,EAAAA;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}