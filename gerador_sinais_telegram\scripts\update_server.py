#!/usr/bin/env python3
"""
Script de atualização automática para o servidor CryptoSignals
Funciona em Windows, Linux e macOS
"""

import subprocess
import sys
import time
import os
from datetime import datetime

# Configurações do servidor
SERVER_CONFIG = {
    'ip': '**************',
    'user': 'root',
    'password': 'h4*ls:FtJw0e',
    'project_path': '/opt/gerador_sinais_telegram'
}

class Colors:
    """Cores para output no terminal"""
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    PURPLE = '\033[0;35m'
    CYAN = '\033[0;36m'
    WHITE = '\033[1;37m'
    NC = '\033[0m'  # No Color

def print_colored(message, color=Colors.NC):
    """Imprime mensagem colorida"""
    print(f"{color}{message}{Colors.NC}")

def print_header():
    """Imprime cabeçalho do script"""
    print_colored("=" * 50, Colors.BLUE)
    print_colored("  CRYPTOSIGNALS - ATUALIZAÇÃO SERVIDOR", Colors.BLUE)
    print_colored("=" * 50, Colors.BLUE)
    print()

def check_dependencies():
    """Verifica se as dependências estão instaladas"""
    print_colored("🔍 Verificando dependências...", Colors.BLUE)

    # Verificar se paramiko está instalado (para SSH em Python)
    try:
        import paramiko
        print_colored("✅ Paramiko encontrado", Colors.GREEN)
        return True
    except ImportError:
        print_colored("❌ Paramiko não encontrado", Colors.RED)
        print_colored("Instalando paramiko...", Colors.YELLOW)
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'paramiko'])
            import paramiko
            print_colored("✅ Paramiko instalado com sucesso", Colors.GREEN)
            return True
        except Exception as e:
            print_colored(f"❌ Erro ao instalar paramiko: {e}", Colors.RED)
            return False

def execute_ssh_command(ssh_client, command, timeout=30):
    """Executa comando via SSH"""
    print_colored(f"Executando: {command}", Colors.YELLOW)

    try:
        stdin, stdout, stderr = ssh_client.exec_command(command, timeout=timeout)

        # Ler output
        output = stdout.read().decode('utf-8').strip()
        error = stderr.read().decode('utf-8').strip()

        exit_code = stdout.channel.recv_exit_status()

        if output:
            print(output)
        if error and exit_code != 0:
            print_colored(f"Erro: {error}", Colors.RED)

        return exit_code == 0, output, error

    except Exception as e:
        print_colored(f"❌ Erro ao executar comando: {e}", Colors.RED)
        return False, "", str(e)

def connect_to_server():
    """Conecta ao servidor via SSH"""
    print_colored("🔗 Conectando ao servidor...", Colors.BLUE)

    try:
        import paramiko

        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())

        ssh.connect(
            hostname=SERVER_CONFIG['ip'],
            username=SERVER_CONFIG['user'],
            password=SERVER_CONFIG['password'],
            timeout=10
        )

        print_colored("✅ Conexão estabelecida com sucesso!", Colors.GREEN)
        return ssh

    except Exception as e:
        print_colored(f"❌ Falha na conexão: {e}", Colors.RED)
        print_colored("Verifique:", Colors.YELLOW)
        print("  - Conexão com internet")
        print(f"  - IP do servidor: {SERVER_CONFIG['ip']}")
        print("  - Credenciais de acesso")
        return None

def update_server():
    """Executa a atualização do servidor"""
    print_header()

    # Verificar dependências
    if not check_dependencies():
        return False

    # Conectar ao servidor
    ssh = connect_to_server()
    if not ssh:
        return False

    try:
        print()

        # 1. Parar o serviço
        print_colored("🛑 Parando serviço CryptoSignals...", Colors.BLUE)
        execute_ssh_command(ssh, "systemctl stop gerador_sinais.service")

        # 2. Fazer backup das configurações
        print_colored("💾 Fazendo backup das configurações...", Colors.BLUE)
        backup_date = datetime.now().strftime("%Y%m%d_%H%M%S")
        execute_ssh_command(ssh, f"cd {SERVER_CONFIG['project_path']} && cp .env .env.backup.{backup_date} 2>/dev/null || echo 'Arquivo .env não encontrado'")

        # 3. Atualizar código do repositório
        print_colored("📥 Atualizando código do repositório...", Colors.BLUE)
        execute_ssh_command(ssh, f"cd {SERVER_CONFIG['project_path']} && git stash")

        # Configurar git para merge automático
        execute_ssh_command(ssh, f"cd {SERVER_CONFIG['project_path']} && git config pull.rebase false")

        success, output, error = execute_ssh_command(ssh, f"cd {SERVER_CONFIG['project_path']} && git pull origin main")

        if not success:
            print_colored("⚠️ Erro no git pull, forçando atualização...", Colors.YELLOW)
            execute_ssh_command(ssh, f"cd {SERVER_CONFIG['project_path']} && git fetch origin main")
            execute_ssh_command(ssh, f"cd {SERVER_CONFIG['project_path']} && git reset --hard origin/main")
            print_colored("✅ Código atualizado forçadamente", Colors.GREEN)

        # 4. Atualizar dependências
        print_colored("📦 Atualizando dependências...", Colors.BLUE)
        execute_ssh_command(ssh, f"cd {SERVER_CONFIG['project_path']} && source venv/bin/activate && pip install --upgrade pip")
        execute_ssh_command(ssh, f"cd {SERVER_CONFIG['project_path']} && source venv/bin/activate && pip install pandas-ta finta --upgrade")

        # 5. Verificar configurações
        print_colored("⚙️ Verificando configurações...", Colors.BLUE)
        execute_ssh_command(ssh, f"cd {SERVER_CONFIG['project_path']} && ls -la .env || echo 'ATENÇÃO: Arquivo .env não encontrado!'")

        # 6. Verificar se precisa de autenticação do Telegram
        print_colored("🔐 Verificando necessidade de autenticação do Telegram...", Colors.BLUE)

        # Primeiro, tentar iniciar o serviço normalmente
        execute_ssh_command(ssh, "systemctl daemon-reload")
        execute_ssh_command(ssh, "systemctl restart gerador_sinais.service")

        # Aguardar um pouco e verificar se precisa de autenticação
        print_colored("⏳ Aguardando inicialização do serviço...", Colors.YELLOW)
        time.sleep(3)

        # Verificar logs para ver se está pedindo autenticação
        success, output, error = execute_ssh_command(ssh, "journalctl -u gerador_sinais.service --no-pager -l -n 10")

        if "phone number" in output.lower() or "código" in output.lower() or "authentication" in output.lower():
            print_colored("📱 AUTENTICAÇÃO DO TELEGRAM NECESSÁRIA!", Colors.YELLOW)
            print_colored("O sistema está pedindo autenticação do Telegram.", Colors.YELLOW)
            print()

            # Parar o serviço para fazer autenticação manual
            execute_ssh_command(ssh, "systemctl stop gerador_sinais.service")

            print_colored("🔧 INICIANDO PROCESSO DE AUTENTICAÇÃO INTERATIVA...", Colors.BLUE)
            print()
            print_colored("📋 INSTRUÇÕES:", Colors.GREEN)
            print("1. O sistema vai iniciar o main.py em modo interativo")
            print("2. Quando pedir o número de telefone, digite: +5521982301476")
            print("3. Aguarde receber o código no Telegram")
            print("4. Digite o código quando solicitado")
            print("5. Após a autenticação, pressione Ctrl+C para parar")
            print("6. O serviço será reiniciado automaticamente")
            print()

            input("Pressione ENTER para continuar com a autenticação...")

            # Executar autenticação interativa
            print_colored("🚀 Iniciando autenticação interativa...", Colors.BLUE)
            print_colored("IMPORTANTE: Seu número é +5521982301476", Colors.YELLOW)
            print()

            try:
                # Criar uma sessão interativa para autenticação
                import paramiko

                # Abrir canal interativo
                channel = ssh.invoke_shell()
                channel.send(f"cd {SERVER_CONFIG['project_path']}\n")
                time.sleep(1)
                channel.send("source venv/bin/activate\n")
                time.sleep(1)
                channel.send("python main.py\n")
                time.sleep(2)

                print_colored("📱 Sessão interativa iniciada. Siga as instruções na tela:", Colors.GREEN)
                print_colored("Número: +5521982301476", Colors.YELLOW)
                print()

                # Loop para interação
                while True:
                    if channel.recv_ready():
                        output = channel.recv(1024).decode('utf-8')
                        print(output, end='')

                        # Se pedir número de telefone
                        if "phone number" in output.lower() or "número" in output.lower():
                            print_colored("\n📱 Digite seu número: +5521982301476", Colors.YELLOW)
                            user_input = input()
                            if not user_input.strip():
                                user_input = "+5521982301476"
                            channel.send(user_input + "\n")

                        # Se pedir código
                        elif "code" in output.lower() or "código" in output.lower():
                            print_colored("\n🔐 Digite o código recebido no Telegram:", Colors.YELLOW)
                            code = input("Código: ")
                            channel.send(code + "\n")

                        # Se autenticação foi bem-sucedida
                        elif "successfully" in output.lower() or "sucesso" in output.lower():
                            print_colored("\n✅ Autenticação bem-sucedida!", Colors.GREEN)
                            time.sleep(2)
                            break

                    # Permitir input do usuário
                    try:
                        import select
                        import sys
                        if select.select([sys.stdin], [], [], 0.1)[0]:
                            user_input = input()
                            if user_input.lower() in ['exit', 'quit', 'sair']:
                                break
                            channel.send(user_input + "\n")
                    except:
                        pass

                # Parar o processo
                channel.send("\x03")  # Ctrl+C
                time.sleep(1)
                channel.close()

                print_colored("✅ Autenticação concluída!", Colors.GREEN)

            except Exception as e:
                print_colored(f"⚠️ Erro na autenticação interativa: {e}", Colors.YELLOW)
                print_colored("Você pode fazer a autenticação manualmente:", Colors.BLUE)
                print(f"ssh root@{SERVER_CONFIG['ip']}")
                print(f"cd {SERVER_CONFIG['project_path']}")
                print("source venv/bin/activate")
                print("python main.py")
                print("# Digite +5521982301476 quando pedir o número")
                print("# Digite o código do Telegram quando solicitado")
                print("# Pressione Ctrl+C após autenticação")

                input("Pressione ENTER após completar a autenticação manual...")

        # 7. Reiniciar o serviço após autenticação
        print_colored("🚀 Reiniciando serviço após autenticação...", Colors.BLUE)
        execute_ssh_command(ssh, "systemctl restart gerador_sinais.service")

        # 8. Aguardar inicialização final
        print_colored("⏳ Aguardando inicialização final...", Colors.YELLOW)
        time.sleep(5)

        # 9. Verificar status final
        print_colored("📊 Verificando status final do serviço...", Colors.BLUE)
        success, output, error = execute_ssh_command(ssh, "systemctl status gerador_sinais.service --no-pager -l")

        # 10. Verificar logs recentes
        print_colored("📋 Verificando logs recentes...", Colors.BLUE)
        execute_ssh_command(ssh, "journalctl -u gerador_sinais.service --no-pager -l -n 20")

        print()
        print_colored("=" * 50, Colors.GREEN)
        print_colored("  ✅ ATUALIZAÇÃO CONCLUÍDA!", Colors.GREEN)
        print_colored("=" * 50, Colors.GREEN)

        return True

    except Exception as e:
        print_colored(f"❌ Erro durante a atualização: {e}", Colors.RED)
        return False

    finally:
        ssh.close()

def show_monitoring_commands():
    """Mostra comandos úteis para monitoramento"""
    print()
    print_colored("📋 Comandos úteis para monitoramento:", Colors.YELLOW)
    print()
    print_colored("Ver logs em tempo real:", Colors.BLUE)
    print(f"  ssh root@{SERVER_CONFIG['ip']}")
    print("  journalctl -u gerador_sinais.service -f")
    print()
    print_colored("Ver status do serviço:", Colors.BLUE)
    print(f"  ssh root@{SERVER_CONFIG['ip']}")
    print("  systemctl status gerador_sinais.service")
    print()
    print_colored("Reiniciar serviço manualmente:", Colors.BLUE)
    print(f"  ssh root@{SERVER_CONFIG['ip']}")
    print("  systemctl restart gerador_sinais.service")
    print()
    print_colored("Acessar diretório do projeto:", Colors.BLUE)
    print(f"  ssh root@{SERVER_CONFIG['ip']}")
    print(f"  cd {SERVER_CONFIG['project_path']}")
    print()

def main():
    """Função principal"""
    try:
        success = update_server()

        if success:
            show_monitoring_commands()

            # Perguntar se quer ver logs em tempo real
            try:
                response = input("Deseja ver os logs em tempo real? (y/n): ").strip().lower()
                if response in ['y', 'yes', 's', 'sim']:
                    print_colored("📋 Para ver logs em tempo real, execute:", Colors.BLUE)
                    print(f"ssh root@{SERVER_CONFIG['ip']}")
                    print("journalctl -u gerador_sinais.service -f")
            except KeyboardInterrupt:
                print()

            print_colored("🎉 Script de atualização finalizado!", Colors.GREEN)
        else:
            print_colored("❌ Atualização falhou!", Colors.RED)
            sys.exit(1)

    except KeyboardInterrupt:
        print()
        print_colored("⚠️ Atualização cancelada pelo usuário", Colors.YELLOW)
        sys.exit(1)
    except Exception as e:
        print_colored(f"❌ Erro inesperado: {e}", Colors.RED)
        sys.exit(1)

if __name__ == "__main__":
    main()
