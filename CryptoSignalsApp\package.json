{"scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "eject": "expo eject"}, "dependencies": {"@expo-google-fonts/poppins": "^0.2.3", "@expo/webpack-config": "^19.0.0", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/drawer": "^6.6.15", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "date-fns": "^2.29.3", "expo": "~49.0.0", "expo-font": "~11.4.0", "expo-splash-screen": "~0.20.5", "expo-status-bar": "~1.6.0", "i18next": "^23.4.6", "react": "18.2.0", "react-dom": "18.2.0", "react-i18next": "^13.2.2", "react-native": "0.72.6", "react-native-gesture-handler": "~2.12.0", "react-native-paper": "^5.10.6", "react-native-reanimated": "~3.3.0", "react-native-safe-area-context": "4.6.3", "react-native-svg": "13.9.0", "react-native-web": "~0.19.6"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true, "name": "cryptosignals-app", "version": "1.0.0"}