import React from 'react';
import { View } from 'react-native';
import { AntDesign } from '@expo/vector-icons';
import styles from './styles';
import { Card, Button, Text } from 'react-native-paper';

const LoginCard = () => {
  return (
    <Card style={styles.container}>
      <Card.Content>
        {/* Título do Card */}
        <Text style={styles.title}>Bem-vindo de volta!</Text>

        {/* Descrição ou subtítulo */}
        <Text style={{ marginVertical: 10 }}>Entre com sua conta para continuar.</Text>

        {/* Campos de Login e Senha */}
        {/* Aqui você pode adicionar seus campos de TextInput para login e senha. */}

        {/* Botão de Login */}
        <Button style={styles.loginButton} mode="contained">
          Entrar
        </Button>

        {/* Opções adicionais */}
        <View style={styles.row}>
          <Button compact style={styles.option} onPress={() => {}}>
            Esqueceu a senha?
          </Button>
          <Button compact style={styles.option} onPress={() => {}}>
            C<PERSON>r conta
          </Button>
        </View>

        {/* Ou entre com */}
        <Text style={{ marginVertical: 10 }}>Ou entre com:</Text>

        {/* Botões de mídia social */}
        <View style={styles.socialButtons}>
          <Button style={styles.socialButton} icon={() => <AntDesign name="google" size={24} color="red" />}>
            Google
          </Button>
          <Button style={styles.socialButton} icon={() => <AntDesign name="facebook-square" size={24} color="blue" />}>
            Facebook
          </Button>
        </View>
      </Card.Content>
    </Card>
  );
};

export default LoginCard;

/*
Utilizei o componente Card do React Native Paper para envolver todo o conteúdo.
Substituí o TouchableOpacity por Button para todos os botões, incluindo botões sociais. Note que no botão social, eu utilizei uma propriedade icon para inserir o ícone dentro do botão.
Usei o componente Text do React Native Paper em vez do padrão do React Native para todos os textos.
Substituí os estilos internos por estilos do arquivo styles quando aplicável.
*/