/* Fix scroll for web */
html, body {
  height: 100%;
  overflow: auto !important;
  -webkit-overflow-scrolling: touch;
}

#root {
  height: 100vh;
  overflow: auto !important;
}

/* Ensure all React Native Web containers are scrollable */
div[data-reactroot] {
  height: 100vh !important;
  overflow: auto !important;
}

/* Fix for ScrollView components */
div[style*="overflow: hidden"] {
  overflow: auto !important;
}

/* Custom scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #2a2a2a;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #FECB37;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #e6b632;
}

/* Firefox scrollbar */
html {
  scrollbar-width: thin;
  scrollbar-color: #FECB37 #2a2a2a;
}

/* Force scroll on all containers */
* {
  -webkit-overflow-scrolling: touch !important;
}
