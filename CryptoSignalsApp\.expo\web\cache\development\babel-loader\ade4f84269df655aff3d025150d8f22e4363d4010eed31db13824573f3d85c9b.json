{"ast": null, "code": "import ToggleButtonComponent from \"./ToggleButton\";\nimport ToggleButtonGroup from \"./ToggleButtonGroup\";\nimport ToggleButtonRow from \"./ToggleButtonRow\";\nvar ToggleButton = Object.assign(ToggleButtonComponent, {\n  Group: ToggleButtonGroup,\n  Row: ToggleButtonRow\n});\nexport default ToggleButton;", "map": {"version": 3, "names": ["ToggleButtonComponent", "ToggleButtonGroup", "ToggleButtonRow", "ToggleButton", "Object", "assign", "Group", "Row"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\ToggleButton\\index.ts"], "sourcesContent": ["import ToggleButtonComponent from './ToggleButton';\nimport ToggleButtonGroup from './ToggleButtonGroup';\nimport ToggleButtonRow from './ToggleButtonRow';\n\nconst ToggleButton = Object.assign(\n  // @component ./ToggleButton.tsx\n  ToggleButtonComponent,\n  {\n    // @component ./ToggleButtonGroup.tsx\n    Group: ToggleButtonGroup,\n    // @component ./ToggleButtonRow.tsx\n    Row: ToggleButtonRow,\n  }\n);\n\nexport default ToggleButton;\n"], "mappings": "AAAA,OAAOA,qBAAqB;AAC5B,OAAOC,iBAAiB;AACxB,OAAOC,eAAe;AAEtB,IAAMC,YAAY,GAAGC,MAAM,CAACC,MAAM,CAEhCL,qBAAqB,EACrB;EAEEM,KAAK,EAAEL,iBAAiB;EAExBM,GAAG,EAAEL;AACP,CACF,CAAC;AAED,eAAeC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}