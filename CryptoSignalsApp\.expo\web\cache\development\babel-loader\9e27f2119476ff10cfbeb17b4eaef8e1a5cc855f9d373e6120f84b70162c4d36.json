{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"style\", \"theme\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nimport * as React from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport NativeText from \"react-native-web/dist/exports/Text\";\nimport { useInternalTheme } from \"../../../core/theming\";\nimport { forwardRef } from \"../../../utils/forwardRef\";\nvar Text = function Text(_ref, ref) {\n  var style = _ref.style,\n    overrideTheme = _ref.theme,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var _theme$fonts, _theme$colors;\n  var root = React.useRef(null);\n  var theme = useInternalTheme(overrideTheme);\n  React.useImperativeHandle(ref, function () {\n    return {\n      setNativeProps: function setNativeProps(args) {\n        var _root$current;\n        return (_root$current = root.current) === null || _root$current === void 0 ? void 0 : _root$current.setNativeProps(args);\n      }\n    };\n  });\n  return React.createElement(NativeText, _extends({}, rest, {\n    ref: root,\n    style: [_objectSpread(_objectSpread({}, !theme.isV3 && ((_theme$fonts = theme.fonts) === null || _theme$fonts === void 0 ? void 0 : _theme$fonts.regular)), {}, {\n      color: theme.isV3 ? (_theme$colors = theme.colors) === null || _theme$colors === void 0 ? void 0 : _theme$colors.onSurface : theme.colors.text\n    }), styles.text, style]\n  }));\n};\nvar styles = StyleSheet.create({\n  text: {\n    textAlign: 'left'\n  }\n});\nexport default forwardRef(Text);", "map": {"version": 3, "names": ["React", "StyleSheet", "NativeText", "useInternalTheme", "forwardRef", "Text", "_ref", "ref", "style", "overrideTheme", "theme", "rest", "_objectWithoutProperties", "_excluded", "_theme$fonts", "_theme$colors", "root", "useRef", "useImperativeHandle", "setNativeProps", "args", "_root$current", "current", "createElement", "_extends", "_objectSpread", "isV3", "fonts", "regular", "color", "colors", "onSurface", "text", "styles", "create", "textAlign"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\Typography\\v2\\Text.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  StyleProp,\n  StyleSheet,\n  Text as NativeText,\n  TextStyle,\n} from 'react-native';\n\nimport type { MD2Theme } from 'src/types';\n\nimport { useInternalTheme } from '../../../core/theming';\nimport { forwardRef } from '../../../utils/forwardRef';\n\ntype Props = React.ComponentProps<typeof NativeText> & {\n  style?: StyleProp<TextStyle>;\n  /**\n   * @optional\n   */\n  theme?: MD2Theme;\n};\n\n// @component-group Typography\n\n/**\n * Text component which follows styles from the theme.\n *\n * @extends Text props https://reactnative.dev/docs/text#props\n */\nconst Text: React.ForwardRefRenderFunction<{}, Props> = (\n  { style, theme: overrideTheme, ...rest }: Props,\n  ref\n) => {\n  const root = React.useRef<NativeText | null>(null);\n  const theme = useInternalTheme(overrideTheme);\n\n  React.useImperativeHandle(ref, () => ({\n    setNativeProps: (args: Object) => root.current?.setNativeProps(args),\n  }));\n\n  return (\n    <NativeText\n      {...rest}\n      ref={root}\n      style={[\n        {\n          ...(!theme.isV3 && theme.fonts?.regular),\n          color: theme.isV3 ? theme.colors?.onSurface : theme.colors.text,\n        },\n        styles.text,\n        style,\n      ]}\n    />\n  );\n};\n\nconst styles = StyleSheet.create({\n  text: {\n    textAlign: 'left',\n  },\n});\n\nexport default forwardRef(Text);\n"], "mappings": ";;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,UAAA;AAAA,OAAAC,UAAA;AAU9B,SAASC,gBAAgB;AACzB,SAASC,UAAU;AAiBnB,IAAMC,IAA+C,GAAG,SAAlDA,IAA+CA,CAAAC,IAAA,EAEnDC,GAAG,EACA;EAAA,IAFDC,KAAK,GAAAF,IAAA,CAALE,KAAK;IAASC,aAAa,GAAAH,IAAA,CAApBI,KAAK;IAAoBC,IAAA,GAAAC,wBAAA,CAAAN,IAAA,EAAAO,SAAA;EAE/B,IAAAC,YAAA,EAAAC,aAAA;EACH,IAAMC,IAAI,GAAGhB,KAAK,CAACiB,MAAM,CAAoB,IAAI,CAAC;EAClD,IAAMP,KAAK,GAAGP,gBAAgB,CAACM,aAAa,CAAC;EAE7CT,KAAK,CAACkB,mBAAmB,CAACX,GAAG,EAAE;IAAA,OAAO;MACpCY,cAAc,EAAG,SAAjBA,cAAcA,CAAGC,IAAY;QAAA,IAAAC,aAAA;QAAA,QAAAA,aAAA,GAAKL,IAAI,CAACM,OAAO,cAAAD,aAAA,uBAAZA,aAAA,CAAcF,cAAc,CAACC,IAAI,CAAC;MAAA;IACtE,CAAC;EAAA,CAAC,CAAC;EAEH,OACEpB,KAAA,CAAAuB,aAAA,CAACrB,UAAU,EAAAsB,QAAA,KACLb,IAAI;IACRJ,GAAG,EAAES,IAAK;IACVR,KAAK,EAAE,CAAAiB,aAAA,CAAAA,aAAA,KAEC,CAACf,KAAK,CAACgB,IAAI,MAAAZ,YAAA,GAAIJ,KAAK,CAACiB,KAAK,cAAAb,YAAA,uBAAXA,YAAA,CAAac,OAAO;MACvCC,KAAK,EAAEnB,KAAK,CAACgB,IAAI,IAAAX,aAAA,GAAGL,KAAK,CAACoB,MAAM,cAAAf,aAAA,uBAAZA,aAAA,CAAcgB,SAAS,GAAGrB,KAAK,CAACoB,MAAM,CAACE;IAAA,IAE7DC,MAAM,CAACD,IAAI,EACXxB,KAAK;EACL,EACH,CAAC;AAEN,CAAC;AAED,IAAMyB,MAAM,GAAGhC,UAAU,CAACiC,MAAM,CAAC;EAC/BF,IAAI,EAAE;IACJG,SAAS,EAAE;EACb;AACF,CAAC,CAAC;AAEF,eAAe/B,UAAU,CAACC,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}