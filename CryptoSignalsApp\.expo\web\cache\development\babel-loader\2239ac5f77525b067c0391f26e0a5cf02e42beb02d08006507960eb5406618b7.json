{"ast": null, "code": "import color from 'color';\nexport var getActiveTintColor = function getActiveTintColor(_ref) {\n  var activeColor = _ref.activeColor,\n    defaultColor = _ref.defaultColor,\n    theme = _ref.theme;\n  if (typeof activeColor === 'string') {\n    return activeColor;\n  }\n  if (theme.isV3) {\n    return theme.colors.onSecondaryContainer;\n  }\n  return defaultColor;\n};\nexport var getInactiveTintColor = function getInactiveTintColor(_ref2) {\n  var inactiveColor = _ref2.inactiveColor,\n    defaultColor = _ref2.defaultColor,\n    theme = _ref2.theme;\n  if (typeof inactiveColor === 'string') {\n    return inactiveColor;\n  }\n  if (theme.isV3) {\n    return theme.colors.onSurfaceVariant;\n  }\n  return color(defaultColor).alpha(0.5).rgb().string();\n};\nexport var getLabelColor = function getLabelColor(_ref3) {\n  var tintColor = _ref3.tintColor,\n    hasColor = _ref3.hasColor,\n    focused = _ref3.focused,\n    defaultColor = _ref3.defaultColor,\n    theme = _ref3.theme;\n  if (hasColor) {\n    return tintColor;\n  }\n  if (theme.isV3) {\n    if (focused) {\n      return theme.colors.onSurface;\n    }\n    return theme.colors.onSurfaceVariant;\n  }\n  return defaultColor;\n};", "map": {"version": 3, "names": ["color", "getActiveTintColor", "_ref", "activeColor", "defaultColor", "theme", "isV3", "colors", "onSecondaryContainer", "getInactiveTintColor", "_ref2", "inactiveColor", "onSurfaceVariant", "alpha", "rgb", "string", "getLabelColor", "_ref3", "tintColor", "hasColor", "focused", "onSurface"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\BottomNavigation\\utils.ts"], "sourcesContent": ["import color from 'color';\nimport type { InternalTheme } from 'src/types';\n\nimport type { black, white } from '../../styles/themes/v2/colors';\n\ntype BaseProps = {\n  defaultColor: typeof black | typeof white;\n  theme: InternalTheme;\n};\n\nexport const getActiveTintColor = ({\n  activeColor,\n  defaultColor,\n  theme,\n}: BaseProps & {\n  activeColor: string | undefined;\n}) => {\n  if (typeof activeColor === 'string') {\n    return activeColor;\n  }\n\n  if (theme.isV3) {\n    return theme.colors.onSecondaryContainer;\n  }\n\n  return defaultColor;\n};\n\nexport const getInactiveTintColor = ({\n  inactiveColor,\n  defaultColor,\n  theme,\n}: BaseProps & {\n  inactiveColor: string | undefined;\n}) => {\n  if (typeof inactiveColor === 'string') {\n    return inactiveColor;\n  }\n\n  if (theme.isV3) {\n    return theme.colors.onSurfaceVariant;\n  }\n\n  return color(defaultColor).alpha(0.5).rgb().string();\n};\n\nexport const getLabelColor = ({\n  tintColor,\n  hasColor,\n  focused,\n  defaultColor,\n  theme,\n}: BaseProps & {\n  tintColor: string;\n  hasColor: boolean;\n  focused: boolean;\n}) => {\n  if (hasColor) {\n    return tintColor;\n  }\n\n  if (theme.isV3) {\n    if (focused) {\n      return theme.colors.onSurface;\n    }\n    return theme.colors.onSurfaceVariant;\n  }\n\n  return defaultColor;\n};\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAUzB,OAAO,IAAMC,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAAC,IAAA,EAMzB;EAAA,IALJC,WAAW,GAAAD,IAAA,CAAXC,WAAW;IACXC,YAAY,GAAAF,IAAA,CAAZE,YAAY;IACZC,KAAA,GAAAH,IAAA,CAAAG,KAAA;EAIA,IAAI,OAAOF,WAAW,KAAK,QAAQ,EAAE;IACnC,OAAOA,WAAW;EACpB;EAEA,IAAIE,KAAK,CAACC,IAAI,EAAE;IACd,OAAOD,KAAK,CAACE,MAAM,CAACC,oBAAoB;EAC1C;EAEA,OAAOJ,YAAY;AACrB,CAAC;AAED,OAAO,IAAMK,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAAC,KAAA,EAM3B;EAAA,IALJC,aAAa,GAAAD,KAAA,CAAbC,aAAa;IACbP,YAAY,GAAAM,KAAA,CAAZN,YAAY;IACZC,KAAA,GAAAK,KAAA,CAAAL,KAAA;EAIA,IAAI,OAAOM,aAAa,KAAK,QAAQ,EAAE;IACrC,OAAOA,aAAa;EACtB;EAEA,IAAIN,KAAK,CAACC,IAAI,EAAE;IACd,OAAOD,KAAK,CAACE,MAAM,CAACK,gBAAgB;EACtC;EAEA,OAAOZ,KAAK,CAACI,YAAY,CAAC,CAACS,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;AACtD,CAAC;AAED,OAAO,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAAC,KAAA,EAUpB;EAAA,IATJC,SAAS,GAAAD,KAAA,CAATC,SAAS;IACTC,QAAQ,GAAAF,KAAA,CAARE,QAAQ;IACRC,OAAO,GAAAH,KAAA,CAAPG,OAAO;IACPhB,YAAY,GAAAa,KAAA,CAAZb,YAAY;IACZC,KAAA,GAAAY,KAAA,CAAAZ,KAAA;EAMA,IAAIc,QAAQ,EAAE;IACZ,OAAOD,SAAS;EAClB;EAEA,IAAIb,KAAK,CAACC,IAAI,EAAE;IACd,IAAIc,OAAO,EAAE;MACX,OAAOf,KAAK,CAACE,MAAM,CAACc,SAAS;IAC/B;IACA,OAAOhB,KAAK,CAACE,MAAM,CAACK,gBAAgB;EACtC;EAEA,OAAOR,YAAY;AACrB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}