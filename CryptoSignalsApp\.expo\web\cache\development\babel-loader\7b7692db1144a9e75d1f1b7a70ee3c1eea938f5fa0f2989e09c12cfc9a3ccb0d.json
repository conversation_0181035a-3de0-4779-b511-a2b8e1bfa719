{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport React from 'react';\nimport axios from 'axios';\nimport { getApiConfig } from \"../../config/api\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport var AxiosContext = React.createContext();\nvar apiConfig = getApiConfig();\nvar apiClient = axios.create({\n  baseURL: apiConfig.BASE_URL,\n  timeout: apiConfig.TIMEOUT,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\napiClient.interceptors.response.use(function (response) {\n  return response;\n}, function (error) {\n  console.error('API Error:', error);\n  if (error.code === 'ECONNREFUSED' || error.code === 'NETWORK_ERROR') {\n    console.warn('Usando dados mock devido a erro de conexão');\n    return Promise.resolve({\n      data: getMockData(error.config.url)\n    });\n  }\n  return Promise.reject(error);\n});\nfunction getMockData(url) {\n  if (url.includes('/channels')) {\n    return getMockChannels();\n  } else if (url.includes('/signals')) {\n    return getMockSignals();\n  } else if (url.includes('/push-notifications/token/')) {\n    return {\n      permissions: []\n    };\n  }\n  return {};\n}\nfunction getMockChannels() {\n  return [{\n    id: 1,\n    externalId: 'strategy-scalp',\n    name: 'CryptoSignals Scalp',\n    description: 'Sinais da estratégia Scalp',\n    type: 'FUTURES',\n    isPremium: false,\n    lastSignalAt: new Date().toISOString(),\n    totalSignals: 0,\n    recentSignals: 0,\n    photo: 'https://via.placeholder.com/50x50/4CAF50/FFFFFF?text=S'\n  }, {\n    id: 2,\n    externalId: 'strategy-breakout',\n    name: 'CryptoSignals Breakout',\n    description: 'Sinais da estratégia Breakout',\n    type: 'FUTURES',\n    isPremium: false,\n    lastSignalAt: new Date(Date.now() - 3600000).toISOString(),\n    totalSignals: 0,\n    recentSignals: 0,\n    photo: 'https://via.placeholder.com/50x50/4CAF50/FFFFFF?text=B'\n  }, {\n    id: 3,\n    externalId: 'strategy-swing',\n    name: 'CryptoSignals Swing',\n    description: 'Sinais da estratégia Swing',\n    type: 'SPOT',\n    isPremium: false,\n    lastSignalAt: new Date(Date.now() - 7200000).toISOString(),\n    totalSignals: 0,\n    recentSignals: 0,\n    photo: 'https://via.placeholder.com/50x50/4CAF50/FFFFFF?text=S'\n  }];\n}\nfunction getMockSignals() {\n  return [{\n    id: 1,\n    symbol: 'BTCUSDT',\n    signal_type: 'LONG',\n    strategy: 'Scalp',\n    entry_price: 45000,\n    stop_loss: 44000,\n    take_profit_1: 46000,\n    status: 'OPEN',\n    leverage: 10,\n    createdAt: new Date().toISOString(),\n    messageOriginal: 'CRYPTOSIGNALS PROFESSIONAL\\n===================================\\n\\nASSET: BTCUSDT\\nSTRATEGY: SCALP\\nDIRECTION: LONG\\nLEVERAGE: 10x\\nTIMEFRAME: 1-4H\\n\\nENTRY ZONE: 45000.00\\nSTOP LOSS: 44000.00\\n\\nTAKE PROFIT LEVELS:\\nTP1: 46000.00\\n\\nRISK MANAGEMENT:\\n- Position size: 1-2% of portfolio\\n- Risk/Reward: 1:2.5 minimum\\n- Strict stop loss adherence\\n\\n===================================\\nCRYPTOSIGNALS PROFESSIONAL'\n  }];\n}\nvar api = {\n  get: function () {\n    var _get = _asyncToGenerator(function* (url) {\n      var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      try {\n        var response = yield apiClient.get(url, config);\n        return response;\n      } catch (error) {\n        console.warn(`Erro na API GET ${url}, usando dados mock:`, error.message);\n        return {\n          data: getMockData(url)\n        };\n      }\n    });\n    function get(_x) {\n      return _get.apply(this, arguments);\n    }\n    return get;\n  }(),\n  post: function () {\n    var _post = _asyncToGenerator(function* (url) {\n      var data = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      var config = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n      try {\n        var response = yield apiClient.post(url, data, config);\n        return response;\n      } catch (error) {\n        console.warn(`Erro na API POST ${url}, usando resposta mock:`, error.message);\n        return {\n          data: {\n            success: true,\n            message: 'Mock response'\n          }\n        };\n      }\n    });\n    function post(_x2) {\n      return _post.apply(this, arguments);\n    }\n    return post;\n  }(),\n  patch: function () {\n    var _patch = _asyncToGenerator(function* (url) {\n      var data = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      var config = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n      try {\n        var response = yield apiClient.patch(url, data, config);\n        return response;\n      } catch (error) {\n        console.warn(`Erro na API PATCH ${url}, usando resposta mock:`, error.message);\n        return {\n          data: {\n            success: true,\n            message: 'Mock response'\n          }\n        };\n      }\n    });\n    function patch(_x3) {\n      return _patch.apply(this, arguments);\n    }\n    return patch;\n  }(),\n  delete: function () {\n    var _delete2 = _asyncToGenerator(function* (url) {\n      var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      try {\n        var response = yield apiClient.delete(url, config);\n        return response;\n      } catch (error) {\n        console.warn(`Erro na API DELETE ${url}, usando resposta mock:`, error.message);\n        return {\n          data: {\n            success: true,\n            message: 'Mock response'\n          }\n        };\n      }\n    });\n    function _delete(_x4) {\n      return _delete2.apply(this, arguments);\n    }\n    return _delete;\n  }()\n};\nexport default function AxiosProvider(_ref) {\n  var children = _ref.children;\n  return _jsx(AxiosContext.Provider, {\n    value: [api],\n    children: children\n  });\n}", "map": {"version": 3, "names": ["React", "axios", "getApiConfig", "jsx", "_jsx", "AxiosContext", "createContext", "apiConfig", "apiClient", "create", "baseURL", "BASE_URL", "timeout", "TIMEOUT", "headers", "interceptors", "response", "use", "error", "console", "code", "warn", "Promise", "resolve", "data", "getMockData", "config", "url", "reject", "includes", "getMockChannels", "getMockSignals", "permissions", "id", "externalId", "name", "description", "type", "isPremium", "lastSignalAt", "Date", "toISOString", "totalSignals", "recentSignals", "photo", "now", "symbol", "signal_type", "strategy", "entry_price", "stop_loss", "take_profit_1", "status", "leverage", "createdAt", "messageOriginal", "api", "get", "_get", "_asyncToGenerator", "arguments", "length", "undefined", "message", "_x", "apply", "post", "_post", "success", "_x2", "patch", "_patch", "_x3", "delete", "_delete2", "_x4", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref", "children", "Provider", "value"], "sources": ["C:/Users/<USER>/Desktop/Signals/CryptoSignalsApp/src/store/axios/index.js"], "sourcesContent": ["import React from 'react';\r\nimport axios from 'axios';\r\nimport { getApiConfig } from '../../config/api';\r\n\r\nexport const AxiosContext = React.createContext();\r\n\r\n// Obter configuração da API\r\nconst apiConfig = getApiConfig();\r\n\r\n// Criar instância do axios\r\nconst apiClient = axios.create({\r\n  baseURL: apiConfig.BASE_URL,\r\n  timeout: apiConfig.TIMEOUT,\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n  },\r\n});\r\n\r\n// Interceptor para tratamento de erros\r\napiClient.interceptors.response.use(\r\n  (response) => response,\r\n  (error) => {\r\n    console.error('API Error:', error);\r\n\r\n    // Se não conseguir conectar, usar dados mock\r\n    if (error.code === 'ECONNREFUSED' || error.code === 'NETWORK_ERROR') {\r\n      console.warn('Usando dados mock devido a erro de conexão');\r\n      return Promise.resolve({ data: getMockData(error.config.url) });\r\n    }\r\n\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Função para obter dados mock em caso de erro\r\nfunction getMockData(url) {\r\n  if (url.includes('/channels')) {\r\n    return getMockChannels();\r\n  } else if (url.includes('/signals')) {\r\n    return getMockSignals();\r\n  } else if (url.includes('/push-notifications/token/')) {\r\n    return { permissions: [] };\r\n  }\r\n  return {};\r\n}\r\n\r\nfunction getMockChannels() {\r\n  return [\r\n    {\r\n      id: 1,\r\n      externalId: 'strategy-scalp',\r\n      name: 'CryptoSignals Scalp',\r\n      description: 'Sinais da estratégia Scalp',\r\n      type: 'FUTURES',\r\n      isPremium: false,\r\n      lastSignalAt: new Date().toISOString(),\r\n      totalSignals: 0,\r\n      recentSignals: 0,\r\n      photo: 'https://via.placeholder.com/50x50/4CAF50/FFFFFF?text=S'\r\n    },\r\n    {\r\n      id: 2,\r\n      externalId: 'strategy-breakout',\r\n      name: 'CryptoSignals Breakout',\r\n      description: 'Sinais da estratégia Breakout',\r\n      type: 'FUTURES',\r\n      isPremium: false,\r\n      lastSignalAt: new Date(Date.now() - 3600000).toISOString(),\r\n      totalSignals: 0,\r\n      recentSignals: 0,\r\n      photo: 'https://via.placeholder.com/50x50/4CAF50/FFFFFF?text=B'\r\n    },\r\n    {\r\n      id: 3,\r\n      externalId: 'strategy-swing',\r\n      name: 'CryptoSignals Swing',\r\n      description: 'Sinais da estratégia Swing',\r\n      type: 'SPOT',\r\n      isPremium: false,\r\n      lastSignalAt: new Date(Date.now() - 7200000).toISOString(),\r\n      totalSignals: 0,\r\n      recentSignals: 0,\r\n      photo: 'https://via.placeholder.com/50x50/4CAF50/FFFFFF?text=S'\r\n    },\r\n  ];\r\n}\r\n\r\nfunction getMockSignals() {\r\n  return [\r\n    {\r\n      id: 1,\r\n      symbol: 'BTCUSDT',\r\n      signal_type: 'LONG',\r\n      strategy: 'Scalp',\r\n      entry_price: 45000,\r\n      stop_loss: 44000,\r\n      take_profit_1: 46000,\r\n      status: 'OPEN',\r\n      leverage: 10,\r\n      createdAt: new Date().toISOString(),\r\n      messageOriginal: 'CRYPTOSIGNALS PROFESSIONAL\\n===================================\\n\\nASSET: BTCUSDT\\nSTRATEGY: SCALP\\nDIRECTION: LONG\\nLEVERAGE: 10x\\nTIMEFRAME: 1-4H\\n\\nENTRY ZONE: 45000.00\\nSTOP LOSS: 44000.00\\n\\nTAKE PROFIT LEVELS:\\nTP1: 46000.00\\n\\nRISK MANAGEMENT:\\n- Position size: 1-2% of portfolio\\n- Risk/Reward: 1:2.5 minimum\\n- Strict stop loss adherence\\n\\n===================================\\nCRYPTOSIGNALS PROFESSIONAL'\r\n    }\r\n  ];\r\n}\r\n\r\n// API wrapper com fallback para mock\r\nconst api = {\r\n  get: async (url, config = {}) => {\r\n    try {\r\n      const response = await apiClient.get(url, config);\r\n      return response;\r\n    } catch (error) {\r\n      console.warn(`Erro na API GET ${url}, usando dados mock:`, error.message);\r\n      return { data: getMockData(url) };\r\n    }\r\n  },\r\n\r\n  post: async (url, data = {}, config = {}) => {\r\n    try {\r\n      const response = await apiClient.post(url, data, config);\r\n      return response;\r\n    } catch (error) {\r\n      console.warn(`Erro na API POST ${url}, usando resposta mock:`, error.message);\r\n      return { data: { success: true, message: 'Mock response' } };\r\n    }\r\n  },\r\n\r\n  patch: async (url, data = {}, config = {}) => {\r\n    try {\r\n      const response = await apiClient.patch(url, data, config);\r\n      return response;\r\n    } catch (error) {\r\n      console.warn(`Erro na API PATCH ${url}, usando resposta mock:`, error.message);\r\n      return { data: { success: true, message: 'Mock response' } };\r\n    }\r\n  },\r\n\r\n  delete: async (url, config = {}) => {\r\n    try {\r\n      const response = await apiClient.delete(url, config);\r\n      return response;\r\n    } catch (error) {\r\n      console.warn(`Erro na API DELETE ${url}, usando resposta mock:`, error.message);\r\n      return { data: { success: true, message: 'Mock response' } };\r\n    }\r\n  }\r\n};\r\n\r\nexport default function AxiosProvider({ children }) {\r\n  return (\r\n    <AxiosContext.Provider value={[ api ]}>\r\n      {children}\r\n    </AxiosContext.Provider>\r\n  );\r\n}\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,YAAY;AAA2B,SAAAC,GAAA,IAAAC,IAAA;AAEhD,OAAO,IAAMC,YAAY,GAAGL,KAAK,CAACM,aAAa,CAAC,CAAC;AAGjD,IAAMC,SAAS,GAAGL,YAAY,CAAC,CAAC;AAGhC,IAAMM,SAAS,GAAGP,KAAK,CAACQ,MAAM,CAAC;EAC7BC,OAAO,EAAEH,SAAS,CAACI,QAAQ;EAC3BC,OAAO,EAAEL,SAAS,CAACM,OAAO;EAC1BC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;AAGFN,SAAS,CAACO,YAAY,CAACC,QAAQ,CAACC,GAAG,CACjC,UAACD,QAAQ;EAAA,OAAKA,QAAQ;AAAA,GACtB,UAACE,KAAK,EAAK;EACTC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;EAGlC,IAAIA,KAAK,CAACE,IAAI,KAAK,cAAc,IAAIF,KAAK,CAACE,IAAI,KAAK,eAAe,EAAE;IACnED,OAAO,CAACE,IAAI,CAAC,4CAA4C,CAAC;IAC1D,OAAOC,OAAO,CAACC,OAAO,CAAC;MAAEC,IAAI,EAAEC,WAAW,CAACP,KAAK,CAACQ,MAAM,CAACC,GAAG;IAAE,CAAC,CAAC;EACjE;EAEA,OAAOL,OAAO,CAACM,MAAM,CAACV,KAAK,CAAC;AAC9B,CACF,CAAC;AAGD,SAASO,WAAWA,CAACE,GAAG,EAAE;EACxB,IAAIA,GAAG,CAACE,QAAQ,CAAC,WAAW,CAAC,EAAE;IAC7B,OAAOC,eAAe,CAAC,CAAC;EAC1B,CAAC,MAAM,IAAIH,GAAG,CAACE,QAAQ,CAAC,UAAU,CAAC,EAAE;IACnC,OAAOE,cAAc,CAAC,CAAC;EACzB,CAAC,MAAM,IAAIJ,GAAG,CAACE,QAAQ,CAAC,4BAA4B,CAAC,EAAE;IACrD,OAAO;MAAEG,WAAW,EAAE;IAAG,CAAC;EAC5B;EACA,OAAO,CAAC,CAAC;AACX;AAEA,SAASF,eAAeA,CAAA,EAAG;EACzB,OAAO,CACL;IACEG,EAAE,EAAE,CAAC;IACLC,UAAU,EAAE,gBAAgB;IAC5BC,IAAI,EAAE,qBAAqB;IAC3BC,WAAW,EAAE,4BAA4B;IACzCC,IAAI,EAAE,SAAS;IACfC,SAAS,EAAE,KAAK;IAChBC,YAAY,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;IACtCC,YAAY,EAAE,CAAC;IACfC,aAAa,EAAE,CAAC;IAChBC,KAAK,EAAE;EACT,CAAC,EACD;IACEX,EAAE,EAAE,CAAC;IACLC,UAAU,EAAE,mBAAmB;IAC/BC,IAAI,EAAE,wBAAwB;IAC9BC,WAAW,EAAE,+BAA+B;IAC5CC,IAAI,EAAE,SAAS;IACfC,SAAS,EAAE,KAAK;IAChBC,YAAY,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACK,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,CAACJ,WAAW,CAAC,CAAC;IAC1DC,YAAY,EAAE,CAAC;IACfC,aAAa,EAAE,CAAC;IAChBC,KAAK,EAAE;EACT,CAAC,EACD;IACEX,EAAE,EAAE,CAAC;IACLC,UAAU,EAAE,gBAAgB;IAC5BC,IAAI,EAAE,qBAAqB;IAC3BC,WAAW,EAAE,4BAA4B;IACzCC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAE,KAAK;IAChBC,YAAY,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACK,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,CAACJ,WAAW,CAAC,CAAC;IAC1DC,YAAY,EAAE,CAAC;IACfC,aAAa,EAAE,CAAC;IAChBC,KAAK,EAAE;EACT,CAAC,CACF;AACH;AAEA,SAASb,cAAcA,CAAA,EAAG;EACxB,OAAO,CACL;IACEE,EAAE,EAAE,CAAC;IACLa,MAAM,EAAE,SAAS;IACjBC,WAAW,EAAE,MAAM;IACnBC,QAAQ,EAAE,OAAO;IACjBC,WAAW,EAAE,KAAK;IAClBC,SAAS,EAAE,KAAK;IAChBC,aAAa,EAAE,KAAK;IACpBC,MAAM,EAAE,MAAM;IACdC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,IAAId,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;IACnCc,eAAe,EAAE;EACnB,CAAC,CACF;AACH;AAGA,IAAMC,GAAG,GAAG;EACVC,GAAG;IAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAE,WAAOhC,GAAG,EAAkB;MAAA,IAAhBD,MAAM,GAAAkC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MAC1B,IAAI;QACF,IAAM5C,QAAQ,SAASR,SAAS,CAACiD,GAAG,CAAC9B,GAAG,EAAED,MAAM,CAAC;QACjD,OAAOV,QAAQ;MACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;QACdC,OAAO,CAACE,IAAI,CAAC,mBAAmBM,GAAG,sBAAsB,EAAET,KAAK,CAAC6C,OAAO,CAAC;QACzE,OAAO;UAAEvC,IAAI,EAAEC,WAAW,CAACE,GAAG;QAAE,CAAC;MACnC;IACF,CAAC;IAAA,SARD8B,GAAGA,CAAAO,EAAA;MAAA,OAAAN,IAAA,CAAAO,KAAA,OAAAL,SAAA;IAAA;IAAA,OAAHH,GAAG;EAAA,GAQF;EAEDS,IAAI;IAAA,IAAAC,KAAA,GAAAR,iBAAA,CAAE,WAAOhC,GAAG,EAA6B;MAAA,IAA3BH,IAAI,GAAAoC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MAAA,IAAElC,MAAM,GAAAkC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MACtC,IAAI;QACF,IAAM5C,QAAQ,SAASR,SAAS,CAAC0D,IAAI,CAACvC,GAAG,EAAEH,IAAI,EAAEE,MAAM,CAAC;QACxD,OAAOV,QAAQ;MACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;QACdC,OAAO,CAACE,IAAI,CAAC,oBAAoBM,GAAG,yBAAyB,EAAET,KAAK,CAAC6C,OAAO,CAAC;QAC7E,OAAO;UAAEvC,IAAI,EAAE;YAAE4C,OAAO,EAAE,IAAI;YAAEL,OAAO,EAAE;UAAgB;QAAE,CAAC;MAC9D;IACF,CAAC;IAAA,SARDG,IAAIA,CAAAG,GAAA;MAAA,OAAAF,KAAA,CAAAF,KAAA,OAAAL,SAAA;IAAA;IAAA,OAAJM,IAAI;EAAA,GAQH;EAEDI,KAAK;IAAA,IAAAC,MAAA,GAAAZ,iBAAA,CAAE,WAAOhC,GAAG,EAA6B;MAAA,IAA3BH,IAAI,GAAAoC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MAAA,IAAElC,MAAM,GAAAkC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MACvC,IAAI;QACF,IAAM5C,QAAQ,SAASR,SAAS,CAAC8D,KAAK,CAAC3C,GAAG,EAAEH,IAAI,EAAEE,MAAM,CAAC;QACzD,OAAOV,QAAQ;MACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;QACdC,OAAO,CAACE,IAAI,CAAC,qBAAqBM,GAAG,yBAAyB,EAAET,KAAK,CAAC6C,OAAO,CAAC;QAC9E,OAAO;UAAEvC,IAAI,EAAE;YAAE4C,OAAO,EAAE,IAAI;YAAEL,OAAO,EAAE;UAAgB;QAAE,CAAC;MAC9D;IACF,CAAC;IAAA,SARDO,KAAKA,CAAAE,GAAA;MAAA,OAAAD,MAAA,CAAAN,KAAA,OAAAL,SAAA;IAAA;IAAA,OAALU,KAAK;EAAA,GAQJ;EAEDG,MAAM;IAAA,IAAAC,QAAA,GAAAf,iBAAA,CAAE,WAAOhC,GAAG,EAAkB;MAAA,IAAhBD,MAAM,GAAAkC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MAC7B,IAAI;QACF,IAAM5C,QAAQ,SAASR,SAAS,CAACiE,MAAM,CAAC9C,GAAG,EAAED,MAAM,CAAC;QACpD,OAAOV,QAAQ;MACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;QACdC,OAAO,CAACE,IAAI,CAAC,sBAAsBM,GAAG,yBAAyB,EAAET,KAAK,CAAC6C,OAAO,CAAC;QAC/E,OAAO;UAAEvC,IAAI,EAAE;YAAE4C,OAAO,EAAE,IAAI;YAAEL,OAAO,EAAE;UAAgB;QAAE,CAAC;MAC9D;IACF,CAAC;IAAA,SARDU,OAAMA,CAAAE,GAAA;MAAA,OAAAD,QAAA,CAAAT,KAAA,OAAAL,SAAA;IAAA;IAAA,OAANa,OAAM;EAAA;AASR,CAAC;AAED,eAAe,SAASG,aAAaA,CAAAC,IAAA,EAAe;EAAA,IAAZC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;EAC9C,OACE1E,IAAA,CAACC,YAAY,CAAC0E,QAAQ;IAACC,KAAK,EAAE,CAAExB,GAAG,CAAG;IAAAsB,QAAA,EACnCA;EAAQ,CACY,CAAC;AAE5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}