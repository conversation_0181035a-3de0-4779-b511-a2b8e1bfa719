import React, { useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { TextInput, Button, Text, Checkbox, IconButton } from 'react-native-paper';
import { AntDesign } from '@expo/vector-icons';

const Login = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [checked, setChecked] = useState(false); // Para o checkbox "Lembrar Senha"

  return (
    <View style={styles.container}>
      {/* Logo do Aplicativo */}
      {/* Você pode adicionar a imagem da logo aqui. */}

      {/* Campo de Login */}
      <TextInput
        label="Login"
        mode="outlined"
        style={styles.input}
      />

      {/* Campo de Senha */}
      <View style={styles.passwordContainer}>
        <TextInput
          label="Senha"
          mode="outlined"
          secureTextEntry={!showPassword}
          style={styles.input}
          right={
            <TextInput.Icon 
              name={showPassword ? 'eye' : 'eye-off'}
              onPress={() => setShowPassword(!showPassword)}
            />
          }
        />
      </View>

      {/* Opção de Lembrar Senha */}
      <View style={styles.option}>
        <Checkbox
          status={checked ? 'checked' : 'unchecked'}
          onPress={() => setChecked(!checked)}
        />
        <Text>Lembrar Senha</Text>
      </View>

      {/* Opção de Esqueceu a Senha */}
      <Button mode="text" onPress={() => {}}>Esqueceu a Senha?</Button>

      {/* Botão de Login */}
      <Button mode="contained" onPress={() => {}} style={styles.button}>
        Logar
      </Button>

      {/* Não tem uma conta? */}
      <Text style={styles.text}>Não tem uma conta?</Text>

      {/* Botão "Sign Up" */}
      <Button mode="outlined" onPress={() => {}} style={styles.button}>
        Sign Up
      </Button>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    justifyContent: 'center',
  },
  input: {
    marginBottom: 16,
  },
  passwordContainer: {
    marginBottom: 16,
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  button: {
    marginBottom: 16,
  },
  text: {
    textAlign: 'center',
    marginBottom: 16,
  },
});

export default Login;


/*
Importei os componentes necessários do React Native Paper.
Substituí os componentes padrão do React Native pelos componentes do React Native Paper.
Adicionei um Checkbox para a opção "Lembrar Senha".
Usei TextInput.Icon para adicionar o ícone de mostrar/ocultar senha diretamente no TextInput.
Utilizei os botões Button do React Native Paper, que vêm com estilos predefinidos.
*/