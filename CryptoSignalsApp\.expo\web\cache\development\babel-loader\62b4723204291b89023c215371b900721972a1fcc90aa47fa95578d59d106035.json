{"ast": null, "code": "import Animated from \"react-native-web/dist/exports/Animated\";\nimport * as MD2Colors from \"./themes/v2/colors\";\nimport { MD3Colors } from \"./themes/v3/tokens\";\nvar SHADOW_COLOR = MD2Colors.black;\nvar SHADOW_OPACITY = 0.24;\nvar MD3_SHADOW_OPACITY = 0.3;\nvar MD3_SHADOW_COLOR = MD3Colors.primary0;\nexport default function shadow() {\n  var elevation = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n  var isV3 = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  return isV3 ? v3Shadow(elevation) : v2Shadow(elevation);\n}\nfunction v2Shadow() {\n  var elevation = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n  if (elevation instanceof Animated.Value) {\n    var inputRange = [0, 1, 2, 3, 8, 24];\n    return {\n      shadowColor: SHADOW_COLOR,\n      shadowOffset: {\n        width: new Animated.Value(0),\n        height: elevation.interpolate({\n          inputRange: inputRange,\n          outputRange: [0, 0.5, 0.75, 2, 7, 23]\n        })\n      },\n      shadowOpacity: elevation.interpolate({\n        inputRange: [0, 1],\n        outputRange: [0, SHADOW_OPACITY],\n        extrapolate: 'clamp'\n      }),\n      shadowRadius: elevation.interpolate({\n        inputRange: inputRange,\n        outputRange: [0, 0.75, 1.5, 3, 8, 24]\n      })\n    };\n  } else {\n    if (elevation === 0) {\n      return {};\n    }\n    var height, radius;\n    switch (elevation) {\n      case 1:\n        height = 0.5;\n        radius = 0.75;\n        break;\n      case 2:\n        height = 0.75;\n        radius = 1.5;\n        break;\n      default:\n        height = elevation - 1;\n        radius = elevation;\n    }\n    return {\n      shadowColor: SHADOW_COLOR,\n      shadowOffset: {\n        width: 0,\n        height: height\n      },\n      shadowOpacity: SHADOW_OPACITY,\n      shadowRadius: radius\n    };\n  }\n}\nfunction v3Shadow() {\n  var elevation = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n  var inputRange = [0, 1, 2, 3, 4, 5];\n  var shadowHeight = [0, 1, 2, 4, 6, 8];\n  var shadowRadius = [0, 3, 6, 8, 10, 12];\n  if (elevation instanceof Animated.Value) {\n    return {\n      shadowColor: MD3_SHADOW_COLOR,\n      shadowOffset: {\n        width: new Animated.Value(0),\n        height: elevation.interpolate({\n          inputRange: inputRange,\n          outputRange: shadowHeight\n        })\n      },\n      shadowOpacity: elevation.interpolate({\n        inputRange: [0, 1],\n        outputRange: [0, MD3_SHADOW_OPACITY],\n        extrapolate: 'clamp'\n      }),\n      shadowRadius: elevation.interpolate({\n        inputRange: inputRange,\n        outputRange: shadowRadius\n      })\n    };\n  } else {\n    return {\n      shadowColor: MD3_SHADOW_COLOR,\n      shadowOpacity: elevation ? MD3_SHADOW_OPACITY : 0,\n      shadowOffset: {\n        width: 0,\n        height: shadowHeight[elevation]\n      },\n      shadowRadius: shadowRadius[elevation]\n    };\n  }\n}", "map": {"version": 3, "names": ["MD2Colors", "MD3Colors", "SHADOW_COLOR", "black", "SHADOW_OPACITY", "MD3_SHADOW_OPACITY", "MD3_SHADOW_COLOR", "primary0", "shadow", "elevation", "arguments", "length", "undefined", "isV3", "v3Shadow", "v2Shadow", "Animated", "Value", "inputRange", "shadowColor", "shadowOffset", "width", "height", "interpolate", "outputRange", "shadowOpacity", "extrapolate", "shadowRadius", "radius", "shadowHeight"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\styles\\shadow.tsx"], "sourcesContent": ["import { Animated } from 'react-native';\n\nimport * as MD2Colors from './themes/v2/colors';\nimport { MD3Colors } from './themes/v3/tokens';\n\nconst SHADOW_COLOR = MD2Colors.black;\nconst SHADOW_OPACITY = 0.24;\nconst MD3_SHADOW_OPACITY = 0.3;\nconst MD3_SHADOW_COLOR = MD3Colors.primary0;\n\nexport default function shadow(\n  elevation: number | Animated.Value = 0,\n  isV3 = false\n) {\n  return isV3 ? v3Shadow(elevation) : v2Shadow(elevation);\n}\n\nfunction v2Shadow(elevation: number | Animated.Value = 0) {\n  if (elevation instanceof Animated.Value) {\n    const inputRange = [0, 1, 2, 3, 8, 24];\n\n    return {\n      shadowColor: SHADOW_COLOR,\n      shadowOffset: {\n        width: new Animated.Value(0),\n        height: elevation.interpolate({\n          inputRange,\n          outputRange: [0, 0.5, 0.75, 2, 7, 23],\n        }),\n      },\n      shadowOpacity: elevation.interpolate({\n        inputRange: [0, 1],\n        outputRange: [0, SHADOW_OPACITY],\n        extrapolate: 'clamp',\n      }),\n      shadowRadius: elevation.interpolate({\n        inputRange,\n        outputRange: [0, 0.75, 1.5, 3, 8, 24],\n      }),\n    };\n  } else {\n    if (elevation === 0) {\n      return {};\n    }\n\n    let height, radius;\n    switch (elevation) {\n      case 1:\n        height = 0.5;\n        radius = 0.75;\n        break;\n      case 2:\n        height = 0.75;\n        radius = 1.5;\n        break;\n      default:\n        height = elevation - 1;\n        radius = elevation;\n    }\n\n    return {\n      shadowColor: SHADOW_COLOR,\n      shadowOffset: {\n        width: 0,\n        height,\n      },\n      shadowOpacity: SHADOW_OPACITY,\n      shadowRadius: radius,\n    };\n  }\n}\n\nfunction v3Shadow(elevation: number | Animated.Value = 0) {\n  const inputRange = [0, 1, 2, 3, 4, 5];\n  const shadowHeight = [0, 1, 2, 4, 6, 8];\n  const shadowRadius = [0, 3, 6, 8, 10, 12];\n\n  if (elevation instanceof Animated.Value) {\n    return {\n      shadowColor: MD3_SHADOW_COLOR,\n      shadowOffset: {\n        width: new Animated.Value(0),\n        height: elevation.interpolate({\n          inputRange,\n          outputRange: shadowHeight,\n        }),\n      },\n      shadowOpacity: elevation.interpolate({\n        inputRange: [0, 1],\n        outputRange: [0, MD3_SHADOW_OPACITY],\n        extrapolate: 'clamp',\n      }),\n      shadowRadius: elevation.interpolate({\n        inputRange,\n        outputRange: shadowRadius,\n      }),\n    };\n  } else {\n    return {\n      shadowColor: MD3_SHADOW_COLOR,\n      shadowOpacity: elevation ? MD3_SHADOW_OPACITY : 0,\n      shadowOffset: {\n        width: 0,\n        height: shadowHeight[elevation],\n      },\n      shadowRadius: shadowRadius[elevation],\n    };\n  }\n}\n"], "mappings": ";AAEA,OAAO,KAAKA,SAAS;AACrB,SAASC,SAAS;AAElB,IAAMC,YAAY,GAAGF,SAAS,CAACG,KAAK;AACpC,IAAMC,cAAc,GAAG,IAAI;AAC3B,IAAMC,kBAAkB,GAAG,GAAG;AAC9B,IAAMC,gBAAgB,GAAGL,SAAS,CAACM,QAAQ;AAE3C,eAAe,SAASC,MAAMA,CAAA,EAG5B;EAAA,IAFAC,SAAkC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAAA,IACtCG,IAAI,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EAEZ,OAAOG,IAAI,GAAGC,QAAQ,CAACL,SAAS,CAAC,GAAGM,QAAQ,CAACN,SAAS,CAAC;AACzD;AAEA,SAASM,QAAQA,CAAA,EAAyC;EAAA,IAAxCN,SAAkC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EACtD,IAAID,SAAS,YAAYO,QAAQ,CAACC,KAAK,EAAE;IACvC,IAAMC,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;IAEtC,OAAO;MACLC,WAAW,EAAEjB,YAAY;MACzBkB,YAAY,EAAE;QACZC,KAAK,EAAE,IAAIL,QAAQ,CAACC,KAAK,CAAC,CAAC,CAAC;QAC5BK,MAAM,EAAEb,SAAS,CAACc,WAAW,CAAC;UAC5BL,UAAU,EAAVA,UAAU;UACVM,WAAW,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE;QACtC,CAAC;MACH,CAAC;MACDC,aAAa,EAAEhB,SAAS,CAACc,WAAW,CAAC;QACnCL,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QAClBM,WAAW,EAAE,CAAC,CAAC,EAAEpB,cAAc,CAAC;QAChCsB,WAAW,EAAE;MACf,CAAC,CAAC;MACFC,YAAY,EAAElB,SAAS,CAACc,WAAW,CAAC;QAClCL,UAAU,EAAVA,UAAU;QACVM,WAAW,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE;MACtC,CAAC;IACH,CAAC;EACH,CAAC,MAAM;IACL,IAAIf,SAAS,KAAK,CAAC,EAAE;MACnB,OAAO,CAAC,CAAC;IACX;IAEA,IAAIa,MAAM,EAAEM,MAAM;IAClB,QAAQnB,SAAS;MACf,KAAK,CAAC;QACJa,MAAM,GAAG,GAAG;QACZM,MAAM,GAAG,IAAI;QACb;MACF,KAAK,CAAC;QACJN,MAAM,GAAG,IAAI;QACbM,MAAM,GAAG,GAAG;QACZ;MACF;QACEN,MAAM,GAAGb,SAAS,GAAG,CAAC;QACtBmB,MAAM,GAAGnB,SAAS;IACtB;IAEA,OAAO;MACLU,WAAW,EAAEjB,YAAY;MACzBkB,YAAY,EAAE;QACZC,KAAK,EAAE,CAAC;QACRC,MAAA,EAAAA;MACF,CAAC;MACDG,aAAa,EAAErB,cAAc;MAC7BuB,YAAY,EAAEC;IAChB,CAAC;EACH;AACF;AAEA,SAASd,QAAQA,CAAA,EAAyC;EAAA,IAAxCL,SAAkC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EACtD,IAAMQ,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACrC,IAAMW,YAAY,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACvC,IAAMF,YAAY,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;EAEzC,IAAIlB,SAAS,YAAYO,QAAQ,CAACC,KAAK,EAAE;IACvC,OAAO;MACLE,WAAW,EAAEb,gBAAgB;MAC7Bc,YAAY,EAAE;QACZC,KAAK,EAAE,IAAIL,QAAQ,CAACC,KAAK,CAAC,CAAC,CAAC;QAC5BK,MAAM,EAAEb,SAAS,CAACc,WAAW,CAAC;UAC5BL,UAAU,EAAVA,UAAU;UACVM,WAAW,EAAEK;QACf,CAAC;MACH,CAAC;MACDJ,aAAa,EAAEhB,SAAS,CAACc,WAAW,CAAC;QACnCL,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QAClBM,WAAW,EAAE,CAAC,CAAC,EAAEnB,kBAAkB,CAAC;QACpCqB,WAAW,EAAE;MACf,CAAC,CAAC;MACFC,YAAY,EAAElB,SAAS,CAACc,WAAW,CAAC;QAClCL,UAAU,EAAVA,UAAU;QACVM,WAAW,EAAEG;MACf,CAAC;IACH,CAAC;EACH,CAAC,MAAM;IACL,OAAO;MACLR,WAAW,EAAEb,gBAAgB;MAC7BmB,aAAa,EAAEhB,SAAS,GAAGJ,kBAAkB,GAAG,CAAC;MACjDe,YAAY,EAAE;QACZC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAEO,YAAY,CAACpB,SAAS;MAChC,CAAC;MACDkB,YAAY,EAAEA,YAAY,CAAClB,SAAS;IACtC,CAAC;EACH;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}