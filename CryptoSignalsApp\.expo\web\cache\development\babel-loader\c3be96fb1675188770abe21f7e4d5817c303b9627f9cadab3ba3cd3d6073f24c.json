{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"style\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport { useTheme } from '@react-navigation/native';\nimport * as React from 'react';\nimport Animated from \"react-native-web/dist/exports/Animated\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nexport default function HeaderBackground(_ref) {\n  var style = _ref.style,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var _useTheme = useTheme(),\n    colors = _useTheme.colors;\n  return React.createElement(Animated.View, _extends({\n    style: [styles.container, {\n      backgroundColor: colors.card,\n      borderBottomColor: colors.border,\n      shadowColor: colors.border\n    }, style]\n  }, rest));\n}\nvar styles = StyleSheet.create({\n  container: _objectSpread({\n    flex: 1\n  }, Platform.select({\n    android: {\n      elevation: 4\n    },\n    ios: {\n      shadowOpacity: 0.85,\n      shadowRadius: 0,\n      shadowOffset: {\n        width: 0,\n        height: StyleSheet.hairlineWidth\n      }\n    },\n    default: {\n      borderBottomWidth: StyleSheet.hairlineWidth\n    }\n  }))\n});", "map": {"version": 3, "names": ["useTheme", "React", "Animated", "Platform", "StyleSheet", "HeaderBackground", "_ref", "style", "rest", "_objectWithoutProperties", "_excluded", "_useTheme", "colors", "createElement", "View", "_extends", "styles", "container", "backgroundColor", "card", "borderBottomColor", "border", "shadowColor", "create", "_objectSpread", "flex", "select", "android", "elevation", "ios", "shadowOpacity", "shadowRadius", "shadowOffset", "width", "height", "hairlineWidth", "default", "borderBottomWidth"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\@react-navigation\\elements\\src\\Header\\HeaderBackground.tsx"], "sourcesContent": ["import { useTheme } from '@react-navigation/native';\nimport * as React from 'react';\nimport {\n  Animated,\n  Platform,\n  StyleProp,\n  StyleSheet,\n  ViewProps,\n  ViewStyle,\n} from 'react-native';\n\ntype Props = Omit<ViewProps, 'style'> & {\n  style?: Animated.WithAnimatedValue<StyleProp<ViewStyle>>;\n  children?: React.ReactNode;\n};\n\nexport default function HeaderBackground({ style, ...rest }: Props) {\n  const { colors } = useTheme();\n\n  return (\n    <Animated.View\n      style={[\n        styles.container,\n        {\n          backgroundColor: colors.card,\n          borderBottomColor: colors.border,\n          shadowColor: colors.border,\n        },\n        style,\n      ]}\n      {...rest}\n    />\n  );\n}\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    ...Platform.select({\n      android: {\n        elevation: 4,\n      },\n      ios: {\n        shadowOpacity: 0.85,\n        shadowRadius: 0,\n        shadowOffset: {\n          width: 0,\n          height: StyleSheet.hairlineWidth,\n        },\n      },\n      default: {\n        borderBottomWidth: StyleSheet.hairlineWidth,\n      },\n    }),\n  },\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA,SAASA,QAAQ,QAAQ,0BAA0B;AACnD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAAA,OAAAC,QAAA;AAAA,OAAAC,UAAA;AAe9B,eAAe,SAASC,gBAAgBA,CAAAC,IAAA,EAA4B;EAAA,IAAzBC,KAAK,GAAkBD,IAAA,CAAvBC,KAAK;IAAKC,IAAA,GAAAC,wBAAA,CAAaH,IAAA,EAAAI,SAAA;EAChE,IAAAC,SAAA,GAAmBX,QAAQ,EAAE;IAArBY,MAAA,GAAAD,SAAA,CAAAC,MAAA;EAER,OACEX,KAAA,CAAAY,aAAA,CAACX,QAAQ,CAACY,IAAI,EAAAC,QAAA;IACZR,KAAK,EAAE,CACLS,MAAM,CAACC,SAAS,EAChB;MACEC,eAAe,EAAEN,MAAM,CAACO,IAAI;MAC5BC,iBAAiB,EAAER,MAAM,CAACS,MAAM;MAChCC,WAAW,EAAEV,MAAM,CAACS;IACtB,CAAC,EACDd,KAAK;EACL,GACEC,IAAI,EACR;AAEN;AAEA,IAAMQ,MAAM,GAAGZ,UAAU,CAACmB,MAAM,CAAC;EAC/BN,SAAS,EAAAO,aAAA;IACPC,IAAI,EAAE;EAAC,GACJtB,QAAQ,CAACuB,MAAM,CAAC;IACjBC,OAAO,EAAE;MACPC,SAAS,EAAE;IACb,CAAC;IACDC,GAAG,EAAE;MACHC,aAAa,EAAE,IAAI;MACnBC,YAAY,EAAE,CAAC;MACfC,YAAY,EAAE;QACZC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE9B,UAAU,CAAC+B;MACrB;IACF,CAAC;IACDC,OAAO,EAAE;MACPC,iBAAiB,EAAEjC,UAAU,CAAC+B;IAChC;EACF,CAAC;AAEL,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}