{"ast": null, "code": "'use strict';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nimport _objectSpread from \"@babel/runtime/helpers/objectSpread2\";\nimport AccessibilityUtil from \"../../modules/AccessibilityUtil\";\nimport BoundingDimensions from \"./BoundingDimensions\";\nimport normalizeColor from '@react-native/normalize-colors';\nimport Position from \"./Position\";\nimport React from 'react';\nimport UIManager from \"../UIManager\";\nimport View from \"../View\";\nimport { warnOnce } from \"../../modules/warnOnce\";\nvar extractSingleTouch = function extractSingleTouch(nativeEvent) {\n  var touches = nativeEvent.touches;\n  var changedTouches = nativeEvent.changedTouches;\n  var hasTouches = touches && touches.length > 0;\n  var hasChangedTouches = changedTouches && changedTouches.length > 0;\n  return !hasTouches && hasChangedTouches ? changedTouches[0] : hasTouches ? touches[0] : nativeEvent;\n};\nvar States = {\n  NOT_RESPONDER: 'NOT_RESPONDER',\n  RESPONDER_INACTIVE_PRESS_IN: 'RESPONDER_INACTIVE_PRESS_IN',\n  RESPONDER_INACTIVE_PRESS_OUT: 'RESPONDER_INACTIVE_PRESS_OUT',\n  RESPONDER_ACTIVE_PRESS_IN: 'RESPONDER_ACTIVE_PRESS_IN',\n  RESPONDER_ACTIVE_PRESS_OUT: 'RESPONDER_ACTIVE_PRESS_OUT',\n  RESPONDER_ACTIVE_LONG_PRESS_IN: 'RESPONDER_ACTIVE_LONG_PRESS_IN',\n  RESPONDER_ACTIVE_LONG_PRESS_OUT: 'RESPONDER_ACTIVE_LONG_PRESS_OUT',\n  ERROR: 'ERROR'\n};\nvar baseStatesConditions = {\n  NOT_RESPONDER: false,\n  RESPONDER_INACTIVE_PRESS_IN: false,\n  RESPONDER_INACTIVE_PRESS_OUT: false,\n  RESPONDER_ACTIVE_PRESS_IN: false,\n  RESPONDER_ACTIVE_PRESS_OUT: false,\n  RESPONDER_ACTIVE_LONG_PRESS_IN: false,\n  RESPONDER_ACTIVE_LONG_PRESS_OUT: false,\n  ERROR: false\n};\nvar IsActive = _objectSpread(_objectSpread({}, baseStatesConditions), {}, {\n  RESPONDER_ACTIVE_PRESS_OUT: true,\n  RESPONDER_ACTIVE_PRESS_IN: true\n});\nvar IsPressingIn = _objectSpread(_objectSpread({}, baseStatesConditions), {}, {\n  RESPONDER_INACTIVE_PRESS_IN: true,\n  RESPONDER_ACTIVE_PRESS_IN: true,\n  RESPONDER_ACTIVE_LONG_PRESS_IN: true\n});\nvar IsLongPressingIn = _objectSpread(_objectSpread({}, baseStatesConditions), {}, {\n  RESPONDER_ACTIVE_LONG_PRESS_IN: true\n});\nvar Signals = {\n  DELAY: 'DELAY',\n  RESPONDER_GRANT: 'RESPONDER_GRANT',\n  RESPONDER_RELEASE: 'RESPONDER_RELEASE',\n  RESPONDER_TERMINATED: 'RESPONDER_TERMINATED',\n  ENTER_PRESS_RECT: 'ENTER_PRESS_RECT',\n  LEAVE_PRESS_RECT: 'LEAVE_PRESS_RECT',\n  LONG_PRESS_DETECTED: 'LONG_PRESS_DETECTED'\n};\nvar Transitions = {\n  NOT_RESPONDER: {\n    DELAY: States.ERROR,\n    RESPONDER_GRANT: States.RESPONDER_INACTIVE_PRESS_IN,\n    RESPONDER_RELEASE: States.ERROR,\n    RESPONDER_TERMINATED: States.ERROR,\n    ENTER_PRESS_RECT: States.ERROR,\n    LEAVE_PRESS_RECT: States.ERROR,\n    LONG_PRESS_DETECTED: States.ERROR\n  },\n  RESPONDER_INACTIVE_PRESS_IN: {\n    DELAY: States.RESPONDER_ACTIVE_PRESS_IN,\n    RESPONDER_GRANT: States.ERROR,\n    RESPONDER_RELEASE: States.NOT_RESPONDER,\n    RESPONDER_TERMINATED: States.NOT_RESPONDER,\n    ENTER_PRESS_RECT: States.RESPONDER_INACTIVE_PRESS_IN,\n    LEAVE_PRESS_RECT: States.RESPONDER_INACTIVE_PRESS_OUT,\n    LONG_PRESS_DETECTED: States.ERROR\n  },\n  RESPONDER_INACTIVE_PRESS_OUT: {\n    DELAY: States.RESPONDER_ACTIVE_PRESS_OUT,\n    RESPONDER_GRANT: States.ERROR,\n    RESPONDER_RELEASE: States.NOT_RESPONDER,\n    RESPONDER_TERMINATED: States.NOT_RESPONDER,\n    ENTER_PRESS_RECT: States.RESPONDER_INACTIVE_PRESS_IN,\n    LEAVE_PRESS_RECT: States.RESPONDER_INACTIVE_PRESS_OUT,\n    LONG_PRESS_DETECTED: States.ERROR\n  },\n  RESPONDER_ACTIVE_PRESS_IN: {\n    DELAY: States.ERROR,\n    RESPONDER_GRANT: States.ERROR,\n    RESPONDER_RELEASE: States.NOT_RESPONDER,\n    RESPONDER_TERMINATED: States.NOT_RESPONDER,\n    ENTER_PRESS_RECT: States.RESPONDER_ACTIVE_PRESS_IN,\n    LEAVE_PRESS_RECT: States.RESPONDER_ACTIVE_PRESS_OUT,\n    LONG_PRESS_DETECTED: States.RESPONDER_ACTIVE_LONG_PRESS_IN\n  },\n  RESPONDER_ACTIVE_PRESS_OUT: {\n    DELAY: States.ERROR,\n    RESPONDER_GRANT: States.ERROR,\n    RESPONDER_RELEASE: States.NOT_RESPONDER,\n    RESPONDER_TERMINATED: States.NOT_RESPONDER,\n    ENTER_PRESS_RECT: States.RESPONDER_ACTIVE_PRESS_IN,\n    LEAVE_PRESS_RECT: States.RESPONDER_ACTIVE_PRESS_OUT,\n    LONG_PRESS_DETECTED: States.ERROR\n  },\n  RESPONDER_ACTIVE_LONG_PRESS_IN: {\n    DELAY: States.ERROR,\n    RESPONDER_GRANT: States.ERROR,\n    RESPONDER_RELEASE: States.NOT_RESPONDER,\n    RESPONDER_TERMINATED: States.NOT_RESPONDER,\n    ENTER_PRESS_RECT: States.RESPONDER_ACTIVE_LONG_PRESS_IN,\n    LEAVE_PRESS_RECT: States.RESPONDER_ACTIVE_LONG_PRESS_OUT,\n    LONG_PRESS_DETECTED: States.RESPONDER_ACTIVE_LONG_PRESS_IN\n  },\n  RESPONDER_ACTIVE_LONG_PRESS_OUT: {\n    DELAY: States.ERROR,\n    RESPONDER_GRANT: States.ERROR,\n    RESPONDER_RELEASE: States.NOT_RESPONDER,\n    RESPONDER_TERMINATED: States.NOT_RESPONDER,\n    ENTER_PRESS_RECT: States.RESPONDER_ACTIVE_LONG_PRESS_IN,\n    LEAVE_PRESS_RECT: States.RESPONDER_ACTIVE_LONG_PRESS_OUT,\n    LONG_PRESS_DETECTED: States.ERROR\n  },\n  error: {\n    DELAY: States.NOT_RESPONDER,\n    RESPONDER_GRANT: States.RESPONDER_INACTIVE_PRESS_IN,\n    RESPONDER_RELEASE: States.NOT_RESPONDER,\n    RESPONDER_TERMINATED: States.NOT_RESPONDER,\n    ENTER_PRESS_RECT: States.NOT_RESPONDER,\n    LEAVE_PRESS_RECT: States.NOT_RESPONDER,\n    LONG_PRESS_DETECTED: States.NOT_RESPONDER\n  }\n};\nvar HIGHLIGHT_DELAY_MS = 130;\nvar PRESS_EXPAND_PX = 20;\nvar LONG_PRESS_THRESHOLD = 500;\nvar LONG_PRESS_DELAY_MS = LONG_PRESS_THRESHOLD - HIGHLIGHT_DELAY_MS;\nvar LONG_PRESS_ALLOWED_MOVEMENT = 10;\nvar TouchableMixin = {\n  componentDidMount: function componentDidMount() {\n    var _this = this;\n    warnOnce('TouchableMixin', 'TouchableMixin is deprecated. Please use Pressable.');\n    var touchableNode = this.getTouchableNode && this.getTouchableNode();\n    if (touchableNode && touchableNode.addEventListener) {\n      this._touchableBlurListener = function (e) {\n        if (_this._isTouchableKeyboardActive) {\n          if (_this.state.touchable.touchState && _this.state.touchable.touchState !== States.NOT_RESPONDER) {\n            _this.touchableHandleResponderTerminate({\n              nativeEvent: e\n            });\n          }\n          _this._isTouchableKeyboardActive = false;\n        }\n      };\n      touchableNode.addEventListener('blur', this._touchableBlurListener);\n    }\n  },\n  componentWillUnmount: function componentWillUnmount() {\n    var touchableNode = this.getTouchableNode && this.getTouchableNode();\n    if (touchableNode && touchableNode.addEventListener) {\n      touchableNode.removeEventListener('blur', this._touchableBlurListener);\n    }\n    this.touchableDelayTimeout && clearTimeout(this.touchableDelayTimeout);\n    this.longPressDelayTimeout && clearTimeout(this.longPressDelayTimeout);\n    this.pressOutDelayTimeout && clearTimeout(this.pressOutDelayTimeout);\n    this.pressInLocation = null;\n    this.state.touchable.responderID = null;\n  },\n  touchableGetInitialState: function touchableGetInitialState() {\n    return {\n      touchable: {\n        touchState: undefined,\n        responderID: null\n      }\n    };\n  },\n  touchableHandleResponderTerminationRequest: function touchableHandleResponderTerminationRequest() {\n    return !this.props.rejectResponderTermination;\n  },\n  touchableHandleStartShouldSetResponder: function touchableHandleStartShouldSetResponder() {\n    return !this.props.disabled;\n  },\n  touchableLongPressCancelsPress: function touchableLongPressCancelsPress() {\n    return true;\n  },\n  touchableHandleResponderGrant: function touchableHandleResponderGrant(e) {\n    var dispatchID = e.currentTarget;\n    e.persist();\n    this.pressOutDelayTimeout && clearTimeout(this.pressOutDelayTimeout);\n    this.pressOutDelayTimeout = null;\n    this.state.touchable.touchState = States.NOT_RESPONDER;\n    this.state.touchable.responderID = dispatchID;\n    this._receiveSignal(Signals.RESPONDER_GRANT, e);\n    var delayMS = this.touchableGetHighlightDelayMS !== undefined ? Math.max(this.touchableGetHighlightDelayMS(), 0) : HIGHLIGHT_DELAY_MS;\n    delayMS = isNaN(delayMS) ? HIGHLIGHT_DELAY_MS : delayMS;\n    if (delayMS !== 0) {\n      this.touchableDelayTimeout = setTimeout(this._handleDelay.bind(this, e), delayMS);\n    } else {\n      this._handleDelay(e);\n    }\n    var longDelayMS = this.touchableGetLongPressDelayMS !== undefined ? Math.max(this.touchableGetLongPressDelayMS(), 10) : LONG_PRESS_DELAY_MS;\n    longDelayMS = isNaN(longDelayMS) ? LONG_PRESS_DELAY_MS : longDelayMS;\n    this.longPressDelayTimeout = setTimeout(this._handleLongDelay.bind(this, e), longDelayMS + delayMS);\n  },\n  touchableHandleResponderRelease: function touchableHandleResponderRelease(e) {\n    this.pressInLocation = null;\n    this._receiveSignal(Signals.RESPONDER_RELEASE, e);\n  },\n  touchableHandleResponderTerminate: function touchableHandleResponderTerminate(e) {\n    this.pressInLocation = null;\n    this._receiveSignal(Signals.RESPONDER_TERMINATED, e);\n  },\n  touchableHandleResponderMove: function touchableHandleResponderMove(e) {\n    if (!this.state.touchable.positionOnActivate) {\n      return;\n    }\n    var positionOnActivate = this.state.touchable.positionOnActivate;\n    var dimensionsOnActivate = this.state.touchable.dimensionsOnActivate;\n    var pressRectOffset = this.touchableGetPressRectOffset ? this.touchableGetPressRectOffset() : {\n      left: PRESS_EXPAND_PX,\n      right: PRESS_EXPAND_PX,\n      top: PRESS_EXPAND_PX,\n      bottom: PRESS_EXPAND_PX\n    };\n    var pressExpandLeft = pressRectOffset.left;\n    var pressExpandTop = pressRectOffset.top;\n    var pressExpandRight = pressRectOffset.right;\n    var pressExpandBottom = pressRectOffset.bottom;\n    var hitSlop = this.touchableGetHitSlop ? this.touchableGetHitSlop() : null;\n    if (hitSlop) {\n      pressExpandLeft += hitSlop.left || 0;\n      pressExpandTop += hitSlop.top || 0;\n      pressExpandRight += hitSlop.right || 0;\n      pressExpandBottom += hitSlop.bottom || 0;\n    }\n    var touch = extractSingleTouch(e.nativeEvent);\n    var pageX = touch && touch.pageX;\n    var pageY = touch && touch.pageY;\n    if (this.pressInLocation) {\n      var movedDistance = this._getDistanceBetweenPoints(pageX, pageY, this.pressInLocation.pageX, this.pressInLocation.pageY);\n      if (movedDistance > LONG_PRESS_ALLOWED_MOVEMENT) {\n        this._cancelLongPressDelayTimeout();\n      }\n    }\n    var isTouchWithinActive = pageX > positionOnActivate.left - pressExpandLeft && pageY > positionOnActivate.top - pressExpandTop && pageX < positionOnActivate.left + dimensionsOnActivate.width + pressExpandRight && pageY < positionOnActivate.top + dimensionsOnActivate.height + pressExpandBottom;\n    if (isTouchWithinActive) {\n      var prevState = this.state.touchable.touchState;\n      this._receiveSignal(Signals.ENTER_PRESS_RECT, e);\n      var curState = this.state.touchable.touchState;\n      if (curState === States.RESPONDER_INACTIVE_PRESS_IN && prevState !== States.RESPONDER_INACTIVE_PRESS_IN) {\n        this._cancelLongPressDelayTimeout();\n      }\n    } else {\n      this._cancelLongPressDelayTimeout();\n      this._receiveSignal(Signals.LEAVE_PRESS_RECT, e);\n    }\n  },\n  touchableHandleFocus: function touchableHandleFocus(e) {\n    this.props.onFocus && this.props.onFocus(e);\n  },\n  touchableHandleBlur: function touchableHandleBlur(e) {\n    this.props.onBlur && this.props.onBlur(e);\n  },\n  _remeasureMetricsOnActivation: function _remeasureMetricsOnActivation() {\n    var tag = this.state.touchable.responderID;\n    if (tag == null) {\n      return;\n    }\n    UIManager.measure(tag, this._handleQueryLayout);\n  },\n  _handleQueryLayout: function _handleQueryLayout(l, t, w, h, globalX, globalY) {\n    if (!l && !t && !w && !h && !globalX && !globalY) {\n      return;\n    }\n    this.state.touchable.positionOnActivate && Position.release(this.state.touchable.positionOnActivate);\n    this.state.touchable.dimensionsOnActivate && BoundingDimensions.release(this.state.touchable.dimensionsOnActivate);\n    this.state.touchable.positionOnActivate = Position.getPooled(globalX, globalY);\n    this.state.touchable.dimensionsOnActivate = BoundingDimensions.getPooled(w, h);\n  },\n  _handleDelay: function _handleDelay(e) {\n    this.touchableDelayTimeout = null;\n    this._receiveSignal(Signals.DELAY, e);\n  },\n  _handleLongDelay: function _handleLongDelay(e) {\n    this.longPressDelayTimeout = null;\n    var curState = this.state.touchable.touchState;\n    if (curState !== States.RESPONDER_ACTIVE_PRESS_IN && curState !== States.RESPONDER_ACTIVE_LONG_PRESS_IN) {\n      console.error('Attempted to transition from state `' + curState + '` to `' + States.RESPONDER_ACTIVE_LONG_PRESS_IN + '`, which is not supported. This is ' + 'most likely due to `Touchable.longPressDelayTimeout` not being cancelled.');\n    } else {\n      this._receiveSignal(Signals.LONG_PRESS_DETECTED, e);\n    }\n  },\n  _receiveSignal: function _receiveSignal(signal, e) {\n    var responderID = this.state.touchable.responderID;\n    var curState = this.state.touchable.touchState;\n    var nextState = Transitions[curState] && Transitions[curState][signal];\n    if (!responderID && signal === Signals.RESPONDER_RELEASE) {\n      return;\n    }\n    if (!nextState) {\n      throw new Error('Unrecognized signal `' + signal + '` or state `' + curState + '` for Touchable responder `' + responderID + '`');\n    }\n    if (nextState === States.ERROR) {\n      throw new Error('Touchable cannot transition from `' + curState + '` to `' + signal + '` for responder `' + responderID + '`');\n    }\n    if (curState !== nextState) {\n      this._performSideEffectsForTransition(curState, nextState, signal, e);\n      this.state.touchable.touchState = nextState;\n    }\n  },\n  _cancelLongPressDelayTimeout: function _cancelLongPressDelayTimeout() {\n    this.longPressDelayTimeout && clearTimeout(this.longPressDelayTimeout);\n    this.longPressDelayTimeout = null;\n  },\n  _isHighlight: function _isHighlight(state) {\n    return state === States.RESPONDER_ACTIVE_PRESS_IN || state === States.RESPONDER_ACTIVE_LONG_PRESS_IN;\n  },\n  _savePressInLocation: function _savePressInLocation(e) {\n    var touch = extractSingleTouch(e.nativeEvent);\n    var pageX = touch && touch.pageX;\n    var pageY = touch && touch.pageY;\n    var locationX = touch && touch.locationX;\n    var locationY = touch && touch.locationY;\n    this.pressInLocation = {\n      pageX: pageX,\n      pageY: pageY,\n      locationX: locationX,\n      locationY: locationY\n    };\n  },\n  _getDistanceBetweenPoints: function _getDistanceBetweenPoints(aX, aY, bX, bY) {\n    var deltaX = aX - bX;\n    var deltaY = aY - bY;\n    return Math.sqrt(deltaX * deltaX + deltaY * deltaY);\n  },\n  _performSideEffectsForTransition: function _performSideEffectsForTransition(curState, nextState, signal, e) {\n    var curIsHighlight = this._isHighlight(curState);\n    var newIsHighlight = this._isHighlight(nextState);\n    var isFinalSignal = signal === Signals.RESPONDER_TERMINATED || signal === Signals.RESPONDER_RELEASE;\n    if (isFinalSignal) {\n      this._cancelLongPressDelayTimeout();\n    }\n    var isInitialTransition = curState === States.NOT_RESPONDER && nextState === States.RESPONDER_INACTIVE_PRESS_IN;\n    var isActiveTransition = !IsActive[curState] && IsActive[nextState];\n    if (isInitialTransition || isActiveTransition) {\n      this._remeasureMetricsOnActivation();\n    }\n    if (IsPressingIn[curState] && signal === Signals.LONG_PRESS_DETECTED) {\n      this.touchableHandleLongPress && this.touchableHandleLongPress(e);\n    }\n    if (newIsHighlight && !curIsHighlight) {\n      this._startHighlight(e);\n    } else if (!newIsHighlight && curIsHighlight) {\n      this._endHighlight(e);\n    }\n    if (IsPressingIn[curState] && signal === Signals.RESPONDER_RELEASE) {\n      var hasLongPressHandler = !!this.props.onLongPress;\n      var pressIsLongButStillCallOnPress = IsLongPressingIn[curState] && (!hasLongPressHandler || !this.touchableLongPressCancelsPress());\n      var shouldInvokePress = !IsLongPressingIn[curState] || pressIsLongButStillCallOnPress;\n      if (shouldInvokePress && this.touchableHandlePress) {\n        if (!newIsHighlight && !curIsHighlight) {\n          this._startHighlight(e);\n          this._endHighlight(e);\n        }\n        this.touchableHandlePress(e);\n      }\n    }\n    this.touchableDelayTimeout && clearTimeout(this.touchableDelayTimeout);\n    this.touchableDelayTimeout = null;\n  },\n  _playTouchSound: function _playTouchSound() {\n    UIManager.playTouchSound();\n  },\n  _startHighlight: function _startHighlight(e) {\n    this._savePressInLocation(e);\n    this.touchableHandleActivePressIn && this.touchableHandleActivePressIn(e);\n  },\n  _endHighlight: function _endHighlight(e) {\n    var _this2 = this;\n    if (this.touchableHandleActivePressOut) {\n      if (this.touchableGetPressOutDelayMS && this.touchableGetPressOutDelayMS()) {\n        this.pressOutDelayTimeout = setTimeout(function () {\n          _this2.touchableHandleActivePressOut(e);\n        }, this.touchableGetPressOutDelayMS());\n      } else {\n        this.touchableHandleActivePressOut(e);\n      }\n    }\n  },\n  touchableHandleKeyEvent: function touchableHandleKeyEvent(e) {\n    var type = e.type,\n      key = e.key;\n    if (key === 'Enter' || key === ' ') {\n      if (type === 'keydown') {\n        if (!this._isTouchableKeyboardActive) {\n          if (!this.state.touchable.touchState || this.state.touchable.touchState === States.NOT_RESPONDER) {\n            this.touchableHandleResponderGrant(e);\n            this._isTouchableKeyboardActive = true;\n          }\n        }\n      } else if (type === 'keyup') {\n        if (this._isTouchableKeyboardActive) {\n          if (this.state.touchable.touchState && this.state.touchable.touchState !== States.NOT_RESPONDER) {\n            this.touchableHandleResponderRelease(e);\n            this._isTouchableKeyboardActive = false;\n          }\n        }\n      }\n      e.stopPropagation();\n      if (!(key === 'Enter' && AccessibilityUtil.propsToAriaRole(this.props) === 'link')) {\n        e.preventDefault();\n      }\n    }\n  },\n  withoutDefaultFocusAndBlur: {}\n};\nvar touchableHandleFocus = TouchableMixin.touchableHandleFocus,\n  touchableHandleBlur = TouchableMixin.touchableHandleBlur,\n  TouchableMixinWithoutDefaultFocusAndBlur = _objectWithoutPropertiesLoose(TouchableMixin, [\"touchableHandleFocus\", \"touchableHandleBlur\"]);\nTouchableMixin.withoutDefaultFocusAndBlur = TouchableMixinWithoutDefaultFocusAndBlur;\nvar Touchable = {\n  Mixin: TouchableMixin,\n  TOUCH_TARGET_DEBUG: false,\n  renderDebugView: function renderDebugView(_ref) {\n    var color = _ref.color,\n      hitSlop = _ref.hitSlop;\n    if (!Touchable.TOUCH_TARGET_DEBUG) {\n      return null;\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      throw Error('Touchable.TOUCH_TARGET_DEBUG should not be enabled in prod!');\n    }\n    var debugHitSlopStyle = {};\n    hitSlop = hitSlop || {\n      top: 0,\n      bottom: 0,\n      left: 0,\n      right: 0\n    };\n    for (var key in hitSlop) {\n      debugHitSlopStyle[key] = -hitSlop[key];\n    }\n    var normalizedColor = normalizeColor(color);\n    if (typeof normalizedColor !== 'number') {\n      return null;\n    }\n    var hexColor = '#' + ('00000000' + normalizedColor.toString(16)).substr(-8);\n    return React.createElement(View, {\n      pointerEvents: \"none\",\n      style: _objectSpread({\n        position: 'absolute',\n        borderColor: hexColor.slice(0, -2) + '55',\n        borderWidth: 1,\n        borderStyle: 'dashed',\n        backgroundColor: hexColor.slice(0, -2) + '0F'\n      }, debugHitSlopStyle)\n    });\n  }\n};\nexport default Touchable;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_objectSpread", "AccessibilityUtil", "BoundingDimensions", "normalizeColor", "Position", "React", "UIManager", "View", "warnOnce", "extractSingleTouch", "nativeEvent", "touches", "changedTouches", "hasTouches", "length", "hasChangedTouches", "States", "NOT_RESPONDER", "RESPONDER_INACTIVE_PRESS_IN", "RESPONDER_INACTIVE_PRESS_OUT", "RESPONDER_ACTIVE_PRESS_IN", "RESPONDER_ACTIVE_PRESS_OUT", "RESPONDER_ACTIVE_LONG_PRESS_IN", "RESPONDER_ACTIVE_LONG_PRESS_OUT", "ERROR", "baseStatesConditions", "IsActive", "IsPressingIn", "IsLongPressingIn", "Signals", "DELAY", "RESPONDER_GRANT", "RESPONDER_RELEASE", "RESPONDER_TERMINATED", "ENTER_PRESS_RECT", "LEAVE_PRESS_RECT", "LONG_PRESS_DETECTED", "Transitions", "error", "HIGHLIGHT_DELAY_MS", "PRESS_EXPAND_PX", "LONG_PRESS_THRESHOLD", "LONG_PRESS_DELAY_MS", "LONG_PRESS_ALLOWED_MOVEMENT", "TouchableMixin", "componentDidMount", "_this", "touchableNode", "getTouchableNode", "addEventListener", "_touchableBlurListener", "e", "_isTouchableKeyboardActive", "state", "touchable", "touchState", "touchableHandleResponderTerminate", "componentWillUnmount", "removeEventListener", "touchableDelayTimeout", "clearTimeout", "longPressDelayTimeout", "pressOutDelayTimeout", "pressInLocation", "responderID", "touchableGetInitialState", "undefined", "touchableHandleResponderTerminationRequest", "props", "rejectResponderTermination", "touchableHandleStartShouldSetResponder", "disabled", "touchableLongPressCancelsPress", "touchableHandleResponderGrant", "dispatchID", "currentTarget", "persist", "_receiveSignal", "delayMS", "touchableGetHighlightDelayMS", "Math", "max", "isNaN", "setTimeout", "_handleDelay", "bind", "longDelayMS", "touchableGetLongPressDelayMS", "_handleLongDelay", "touchableHandleResponderRelease", "touchableHandleResponderMove", "positionOnActivate", "dimensionsOnActivate", "pressRectOffset", "touchableGetPressRectOffset", "left", "right", "top", "bottom", "pressExpandLeft", "pressExpandTop", "pressExpandRight", "pressExpandBottom", "hitSlop", "touchableGetHitSlop", "touch", "pageX", "pageY", "movedDistance", "_getDistanceBetweenPoints", "_cancelLongPressDelayTimeout", "isTouchWithinActive", "width", "height", "prevState", "curState", "touchableHandleFocus", "onFocus", "touchableHandleBlur", "onBlur", "_remeasureMetricsOnActivation", "tag", "measure", "_handleQueryLayout", "l", "t", "w", "h", "globalX", "globalY", "release", "getPooled", "console", "signal", "nextState", "Error", "_performSideEffectsForTransition", "_isHighlight", "_savePressInLocation", "locationX", "locationY", "aX", "aY", "bX", "bY", "deltaX", "deltaY", "sqrt", "curIs<PERSON>igh<PERSON>", "newIsHighlight", "isFinalSignal", "isInitialTransition", "isActiveTransition", "touchableHandleLongPress", "_startHighlight", "_endHighlight", "hasLongPressHandler", "onLongPress", "pressIsLongButStillCallOnPress", "shouldInvokePress", "touchableHandlePress", "_playTouchSound", "playTouchSound", "touchableHandleActivePressIn", "_this2", "touchableHandleActivePressOut", "touchableGetPressOutDelayMS", "touchableHandleKeyEvent", "type", "key", "stopPropagation", "propsToAriaRole", "preventDefault", "withoutDefaultFocusAndBlur", "TouchableMixinWithoutDefaultFocusAndBlur", "Touchable", "Mixin", "TOUCH_TARGET_DEBUG", "renderDebugView", "_ref", "color", "process", "env", "NODE_ENV", "debugHitSlopStyle", "normalizedColor", "hexColor", "toString", "substr", "createElement", "pointerEvents", "style", "position", "borderColor", "slice", "borderWidth", "borderStyle", "backgroundColor"], "sources": ["E:/CryptoSignalsApp/node_modules/react-native-web/dist/exports/Touchable/index.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use strict';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nimport _objectSpread from \"@babel/runtime/helpers/objectSpread2\";\nimport AccessibilityUtil from '../../modules/AccessibilityUtil';\nimport BoundingDimensions from './BoundingDimensions';\nimport normalizeColor from '@react-native/normalize-colors';\nimport Position from './Position';\nimport React from 'react';\nimport UIManager from '../UIManager';\nimport View from '../View';\nimport { warnOnce } from '../../modules/warnOnce';\nvar extractSingleTouch = nativeEvent => {\n  var touches = nativeEvent.touches;\n  var changedTouches = nativeEvent.changedTouches;\n  var hasTouches = touches && touches.length > 0;\n  var hasChangedTouches = changedTouches && changedTouches.length > 0;\n  return !hasTouches && hasChangedTouches ? changedTouches[0] : hasTouches ? touches[0] : nativeEvent;\n};\n\n/**\n * `Touchable`: Taps done right.\n *\n * You hook your `ResponderEventPlugin` events into `Touchable`. `Touchable`\n * will measure time/geometry and tells you when to give feedback to the user.\n *\n * ====================== Touchable Tutorial ===============================\n * The `Touchable` mixin helps you handle the \"press\" interaction. It analyzes\n * the geometry of elements, and observes when another responder (scroll view\n * etc) has stolen the touch lock. It notifies your component when it should\n * give feedback to the user. (bouncing/highlighting/unhighlighting).\n *\n * - When a touch was activated (typically you highlight)\n * - When a touch was deactivated (typically you unhighlight)\n * - When a touch was \"pressed\" - a touch ended while still within the geometry\n *   of the element, and no other element (like scroller) has \"stolen\" touch\n *   lock (\"responder\") (Typically you bounce the element).\n *\n * A good tap interaction isn't as simple as you might think. There should be a\n * slight delay before showing a highlight when starting a touch. If a\n * subsequent touch move exceeds the boundary of the element, it should\n * unhighlight, but if that same touch is brought back within the boundary, it\n * should rehighlight again. A touch can move in and out of that boundary\n * several times, each time toggling highlighting, but a \"press\" is only\n * triggered if that touch ends while within the element's boundary and no\n * scroller (or anything else) has stolen the lock on touches.\n *\n * To create a new type of component that handles interaction using the\n * `Touchable` mixin, do the following:\n *\n * - Initialize the `Touchable` state.\n *\n *   getInitialState: function() {\n *     return merge(this.touchableGetInitialState(), yourComponentState);\n *   }\n *\n * - Add a method to get your touchable component's node.\n *   getTouchableNode: function() {\n *     return this.touchableRef.current\n *   }\n *\n * - Choose the rendered component who's touches should start the interactive\n *   sequence. On that rendered node, forward all `Touchable` responder\n *   handlers. You can choose any rendered node you like. Choose a node whose\n *   hit target you'd like to instigate the interaction sequence:\n *\n *   // In render function:\n *   return (\n *     <View\n *       ref={this.touchableRef}\n *       onStartShouldSetResponder={this.touchableHandleStartShouldSetResponder}\n *       onResponderTerminationRequest={this.touchableHandleResponderTerminationRequest}\n *       onResponderGrant={this.touchableHandleResponderGrant}\n *       onResponderMove={this.touchableHandleResponderMove}\n *       onResponderRelease={this.touchableHandleResponderRelease}\n *       onResponderTerminate={this.touchableHandleResponderTerminate}>\n *       <View>\n *         Even though the hit detection/interactions are triggered by the\n *         wrapping (typically larger) node, we usually end up implementing\n *         custom logic that highlights this inner one.\n *       </View>\n *     </View>\n *   );\n *\n * - You may set up your own handlers for each of these events, so long as you\n *   also invoke the `touchable*` handlers inside of your custom handler.\n *\n * - Implement the handlers on your component class in order to provide\n *   feedback to the user. See documentation for each of these class methods\n *   that you should implement.\n *\n *   touchableHandlePress: function() {\n *      this.performBounceAnimation();  // or whatever you want to do.\n *   },\n *   touchableHandleActivePressIn: function() {\n *     this.beginHighlighting(...);  // Whatever you like to convey activation\n *   },\n *   touchableHandleActivePressOut: function() {\n *     this.endHighlighting(...);  // Whatever you like to convey deactivation\n *   },\n *\n * - There are more advanced methods you can implement (see documentation below):\n *   touchableGetHighlightDelayMS: function() {\n *     return 20;\n *   }\n *   // In practice, *always* use a predeclared constant (conserve memory).\n *   touchableGetPressRectOffset: function() {\n *     return {top: 20, left: 20, right: 20, bottom: 100};\n *   }\n */\n\n/**\n * Touchable states.\n */\n\nvar States = {\n  NOT_RESPONDER: 'NOT_RESPONDER',\n  // Not the responder\n  RESPONDER_INACTIVE_PRESS_IN: 'RESPONDER_INACTIVE_PRESS_IN',\n  // Responder, inactive, in the `PressRect`\n  RESPONDER_INACTIVE_PRESS_OUT: 'RESPONDER_INACTIVE_PRESS_OUT',\n  // Responder, inactive, out of `PressRect`\n  RESPONDER_ACTIVE_PRESS_IN: 'RESPONDER_ACTIVE_PRESS_IN',\n  // Responder, active, in the `PressRect`\n  RESPONDER_ACTIVE_PRESS_OUT: 'RESPONDER_ACTIVE_PRESS_OUT',\n  // Responder, active, out of `PressRect`\n  RESPONDER_ACTIVE_LONG_PRESS_IN: 'RESPONDER_ACTIVE_LONG_PRESS_IN',\n  // Responder, active, in the `PressRect`, after long press threshold\n  RESPONDER_ACTIVE_LONG_PRESS_OUT: 'RESPONDER_ACTIVE_LONG_PRESS_OUT',\n  // Responder, active, out of `PressRect`, after long press threshold\n  ERROR: 'ERROR'\n};\n/*\n * Quick lookup map for states that are considered to be \"active\"\n */\n\nvar baseStatesConditions = {\n  NOT_RESPONDER: false,\n  RESPONDER_INACTIVE_PRESS_IN: false,\n  RESPONDER_INACTIVE_PRESS_OUT: false,\n  RESPONDER_ACTIVE_PRESS_IN: false,\n  RESPONDER_ACTIVE_PRESS_OUT: false,\n  RESPONDER_ACTIVE_LONG_PRESS_IN: false,\n  RESPONDER_ACTIVE_LONG_PRESS_OUT: false,\n  ERROR: false\n};\nvar IsActive = _objectSpread(_objectSpread({}, baseStatesConditions), {}, {\n  RESPONDER_ACTIVE_PRESS_OUT: true,\n  RESPONDER_ACTIVE_PRESS_IN: true\n});\n\n/**\n * Quick lookup for states that are considered to be \"pressing\" and are\n * therefore eligible to result in a \"selection\" if the press stops.\n */\nvar IsPressingIn = _objectSpread(_objectSpread({}, baseStatesConditions), {}, {\n  RESPONDER_INACTIVE_PRESS_IN: true,\n  RESPONDER_ACTIVE_PRESS_IN: true,\n  RESPONDER_ACTIVE_LONG_PRESS_IN: true\n});\nvar IsLongPressingIn = _objectSpread(_objectSpread({}, baseStatesConditions), {}, {\n  RESPONDER_ACTIVE_LONG_PRESS_IN: true\n});\n\n/**\n * Inputs to the state machine.\n */\nvar Signals = {\n  DELAY: 'DELAY',\n  RESPONDER_GRANT: 'RESPONDER_GRANT',\n  RESPONDER_RELEASE: 'RESPONDER_RELEASE',\n  RESPONDER_TERMINATED: 'RESPONDER_TERMINATED',\n  ENTER_PRESS_RECT: 'ENTER_PRESS_RECT',\n  LEAVE_PRESS_RECT: 'LEAVE_PRESS_RECT',\n  LONG_PRESS_DETECTED: 'LONG_PRESS_DETECTED'\n};\n/**\n * Mapping from States x Signals => States\n */\nvar Transitions = {\n  NOT_RESPONDER: {\n    DELAY: States.ERROR,\n    RESPONDER_GRANT: States.RESPONDER_INACTIVE_PRESS_IN,\n    RESPONDER_RELEASE: States.ERROR,\n    RESPONDER_TERMINATED: States.ERROR,\n    ENTER_PRESS_RECT: States.ERROR,\n    LEAVE_PRESS_RECT: States.ERROR,\n    LONG_PRESS_DETECTED: States.ERROR\n  },\n  RESPONDER_INACTIVE_PRESS_IN: {\n    DELAY: States.RESPONDER_ACTIVE_PRESS_IN,\n    RESPONDER_GRANT: States.ERROR,\n    RESPONDER_RELEASE: States.NOT_RESPONDER,\n    RESPONDER_TERMINATED: States.NOT_RESPONDER,\n    ENTER_PRESS_RECT: States.RESPONDER_INACTIVE_PRESS_IN,\n    LEAVE_PRESS_RECT: States.RESPONDER_INACTIVE_PRESS_OUT,\n    LONG_PRESS_DETECTED: States.ERROR\n  },\n  RESPONDER_INACTIVE_PRESS_OUT: {\n    DELAY: States.RESPONDER_ACTIVE_PRESS_OUT,\n    RESPONDER_GRANT: States.ERROR,\n    RESPONDER_RELEASE: States.NOT_RESPONDER,\n    RESPONDER_TERMINATED: States.NOT_RESPONDER,\n    ENTER_PRESS_RECT: States.RESPONDER_INACTIVE_PRESS_IN,\n    LEAVE_PRESS_RECT: States.RESPONDER_INACTIVE_PRESS_OUT,\n    LONG_PRESS_DETECTED: States.ERROR\n  },\n  RESPONDER_ACTIVE_PRESS_IN: {\n    DELAY: States.ERROR,\n    RESPONDER_GRANT: States.ERROR,\n    RESPONDER_RELEASE: States.NOT_RESPONDER,\n    RESPONDER_TERMINATED: States.NOT_RESPONDER,\n    ENTER_PRESS_RECT: States.RESPONDER_ACTIVE_PRESS_IN,\n    LEAVE_PRESS_RECT: States.RESPONDER_ACTIVE_PRESS_OUT,\n    LONG_PRESS_DETECTED: States.RESPONDER_ACTIVE_LONG_PRESS_IN\n  },\n  RESPONDER_ACTIVE_PRESS_OUT: {\n    DELAY: States.ERROR,\n    RESPONDER_GRANT: States.ERROR,\n    RESPONDER_RELEASE: States.NOT_RESPONDER,\n    RESPONDER_TERMINATED: States.NOT_RESPONDER,\n    ENTER_PRESS_RECT: States.RESPONDER_ACTIVE_PRESS_IN,\n    LEAVE_PRESS_RECT: States.RESPONDER_ACTIVE_PRESS_OUT,\n    LONG_PRESS_DETECTED: States.ERROR\n  },\n  RESPONDER_ACTIVE_LONG_PRESS_IN: {\n    DELAY: States.ERROR,\n    RESPONDER_GRANT: States.ERROR,\n    RESPONDER_RELEASE: States.NOT_RESPONDER,\n    RESPONDER_TERMINATED: States.NOT_RESPONDER,\n    ENTER_PRESS_RECT: States.RESPONDER_ACTIVE_LONG_PRESS_IN,\n    LEAVE_PRESS_RECT: States.RESPONDER_ACTIVE_LONG_PRESS_OUT,\n    LONG_PRESS_DETECTED: States.RESPONDER_ACTIVE_LONG_PRESS_IN\n  },\n  RESPONDER_ACTIVE_LONG_PRESS_OUT: {\n    DELAY: States.ERROR,\n    RESPONDER_GRANT: States.ERROR,\n    RESPONDER_RELEASE: States.NOT_RESPONDER,\n    RESPONDER_TERMINATED: States.NOT_RESPONDER,\n    ENTER_PRESS_RECT: States.RESPONDER_ACTIVE_LONG_PRESS_IN,\n    LEAVE_PRESS_RECT: States.RESPONDER_ACTIVE_LONG_PRESS_OUT,\n    LONG_PRESS_DETECTED: States.ERROR\n  },\n  error: {\n    DELAY: States.NOT_RESPONDER,\n    RESPONDER_GRANT: States.RESPONDER_INACTIVE_PRESS_IN,\n    RESPONDER_RELEASE: States.NOT_RESPONDER,\n    RESPONDER_TERMINATED: States.NOT_RESPONDER,\n    ENTER_PRESS_RECT: States.NOT_RESPONDER,\n    LEAVE_PRESS_RECT: States.NOT_RESPONDER,\n    LONG_PRESS_DETECTED: States.NOT_RESPONDER\n  }\n};\n\n// ==== Typical Constants for integrating into UI components ====\n// var HIT_EXPAND_PX = 20;\n// var HIT_VERT_OFFSET_PX = 10;\nvar HIGHLIGHT_DELAY_MS = 130;\nvar PRESS_EXPAND_PX = 20;\nvar LONG_PRESS_THRESHOLD = 500;\nvar LONG_PRESS_DELAY_MS = LONG_PRESS_THRESHOLD - HIGHLIGHT_DELAY_MS;\nvar LONG_PRESS_ALLOWED_MOVEMENT = 10;\n\n// Default amount \"active\" region protrudes beyond box\n\n/**\n * By convention, methods prefixed with underscores are meant to be @private,\n * and not @protected. Mixers shouldn't access them - not even to provide them\n * as callback handlers.\n *\n *\n * ========== Geometry =========\n * `Touchable` only assumes that there exists a `HitRect` node. The `PressRect`\n * is an abstract box that is extended beyond the `HitRect`.\n *\n *  +--------------------------+\n *  |                          | - \"Start\" events in `HitRect` cause `HitRect`\n *  |  +--------------------+  |   to become the responder.\n *  |  |  +--------------+  |  | - `HitRect` is typically expanded around\n *  |  |  |              |  |  |   the `VisualRect`, but shifted downward.\n *  |  |  |  VisualRect  |  |  | - After pressing down, after some delay,\n *  |  |  |              |  |  |   and before letting up, the Visual React\n *  |  |  +--------------+  |  |   will become \"active\". This makes it eligible\n *  |  |     HitRect        |  |   for being highlighted (so long as the\n *  |  +--------------------+  |   press remains in the `PressRect`).\n *  |        PressRect     o   |\n *  +----------------------|---+\n *           Out Region    |\n *                         +-----+ This gap between the `HitRect` and\n *                                 `PressRect` allows a touch to move far away\n *                                 from the original hit rect, and remain\n *                                 highlighted, and eligible for a \"Press\".\n *                                 Customize this via\n *                                 `touchableGetPressRectOffset()`.\n *\n *\n *\n * ======= State Machine =======\n *\n * +-------------+ <---+ RESPONDER_RELEASE\n * |NOT_RESPONDER|\n * +-------------+ <---+ RESPONDER_TERMINATED\n *     +\n *     | RESPONDER_GRANT (HitRect)\n *     v\n * +---------------------------+  DELAY   +-------------------------+  T + DELAY     +------------------------------+\n * |RESPONDER_INACTIVE_PRESS_IN|+-------->|RESPONDER_ACTIVE_PRESS_IN| +------------> |RESPONDER_ACTIVE_LONG_PRESS_IN|\n * +---------------------------+          +-------------------------+                +------------------------------+\n *     +            ^                         +           ^                                 +           ^\n *     |LEAVE_      |ENTER_                   |LEAVE_     |ENTER_                           |LEAVE_     |ENTER_\n *     |PRESS_RECT  |PRESS_RECT               |PRESS_RECT |PRESS_RECT                       |PRESS_RECT |PRESS_RECT\n *     |            |                         |           |                                 |           |\n *     v            +                         v           +                                 v           +\n * +----------------------------+  DELAY  +--------------------------+               +-------------------------------+\n * |RESPONDER_INACTIVE_PRESS_OUT|+------->|RESPONDER_ACTIVE_PRESS_OUT|               |RESPONDER_ACTIVE_LONG_PRESS_OUT|\n * +----------------------------+         +--------------------------+               +-------------------------------+\n *\n * T + DELAY => LONG_PRESS_DELAY_MS + DELAY\n *\n * Not drawn are the side effects of each transition. The most important side\n * effect is the `touchableHandlePress` abstract method invocation that occurs\n * when a responder is released while in either of the \"Press\" states.\n *\n * The other important side effects are the highlight abstract method\n * invocations (internal callbacks) to be implemented by the mixer.\n *\n *\n * @lends Touchable.prototype\n */\nvar TouchableMixin = {\n  // HACK (part 1): basic support for touchable interactions using a keyboard\n  componentDidMount: function componentDidMount() {\n    warnOnce('TouchableMixin', 'TouchableMixin is deprecated. Please use Pressable.');\n    var touchableNode = this.getTouchableNode && this.getTouchableNode();\n    if (touchableNode && touchableNode.addEventListener) {\n      this._touchableBlurListener = e => {\n        if (this._isTouchableKeyboardActive) {\n          if (this.state.touchable.touchState && this.state.touchable.touchState !== States.NOT_RESPONDER) {\n            this.touchableHandleResponderTerminate({\n              nativeEvent: e\n            });\n          }\n          this._isTouchableKeyboardActive = false;\n        }\n      };\n      touchableNode.addEventListener('blur', this._touchableBlurListener);\n    }\n  },\n  /**\n   * Clear all timeouts on unmount\n   */\n  componentWillUnmount: function componentWillUnmount() {\n    var touchableNode = this.getTouchableNode && this.getTouchableNode();\n    if (touchableNode && touchableNode.addEventListener) {\n      touchableNode.removeEventListener('blur', this._touchableBlurListener);\n    }\n    this.touchableDelayTimeout && clearTimeout(this.touchableDelayTimeout);\n    this.longPressDelayTimeout && clearTimeout(this.longPressDelayTimeout);\n    this.pressOutDelayTimeout && clearTimeout(this.pressOutDelayTimeout);\n    // Clear DOM nodes\n    this.pressInLocation = null;\n    this.state.touchable.responderID = null;\n  },\n  /**\n   * It's prefer that mixins determine state in this way, having the class\n   * explicitly mix the state in the one and only `getInitialState` method.\n   *\n   * @return {object} State object to be placed inside of\n   * `this.state.touchable`.\n   */\n  touchableGetInitialState: function touchableGetInitialState() {\n    return {\n      touchable: {\n        touchState: undefined,\n        responderID: null\n      }\n    };\n  },\n  // ==== Hooks to Gesture Responder system ====\n  /**\n   * Must return true if embedded in a native platform scroll view.\n   */\n  touchableHandleResponderTerminationRequest: function touchableHandleResponderTerminationRequest() {\n    return !this.props.rejectResponderTermination;\n  },\n  /**\n   * Must return true to start the process of `Touchable`.\n   */\n  touchableHandleStartShouldSetResponder: function touchableHandleStartShouldSetResponder() {\n    return !this.props.disabled;\n  },\n  /**\n   * Return true to cancel press on long press.\n   */\n  touchableLongPressCancelsPress: function touchableLongPressCancelsPress() {\n    return true;\n  },\n  /**\n   * Place as callback for a DOM element's `onResponderGrant` event.\n   * @param {SyntheticEvent} e Synthetic event from event system.\n   *\n   */\n  touchableHandleResponderGrant: function touchableHandleResponderGrant(e) {\n    var dispatchID = e.currentTarget;\n    // Since e is used in a callback invoked on another event loop\n    // (as in setTimeout etc), we need to call e.persist() on the\n    // event to make sure it doesn't get reused in the event object pool.\n    e.persist();\n    this.pressOutDelayTimeout && clearTimeout(this.pressOutDelayTimeout);\n    this.pressOutDelayTimeout = null;\n    this.state.touchable.touchState = States.NOT_RESPONDER;\n    this.state.touchable.responderID = dispatchID;\n    this._receiveSignal(Signals.RESPONDER_GRANT, e);\n    var delayMS = this.touchableGetHighlightDelayMS !== undefined ? Math.max(this.touchableGetHighlightDelayMS(), 0) : HIGHLIGHT_DELAY_MS;\n    delayMS = isNaN(delayMS) ? HIGHLIGHT_DELAY_MS : delayMS;\n    if (delayMS !== 0) {\n      this.touchableDelayTimeout = setTimeout(this._handleDelay.bind(this, e), delayMS);\n    } else {\n      this._handleDelay(e);\n    }\n    var longDelayMS = this.touchableGetLongPressDelayMS !== undefined ? Math.max(this.touchableGetLongPressDelayMS(), 10) : LONG_PRESS_DELAY_MS;\n    longDelayMS = isNaN(longDelayMS) ? LONG_PRESS_DELAY_MS : longDelayMS;\n    this.longPressDelayTimeout = setTimeout(this._handleLongDelay.bind(this, e), longDelayMS + delayMS);\n  },\n  /**\n   * Place as callback for a DOM element's `onResponderRelease` event.\n   */\n  touchableHandleResponderRelease: function touchableHandleResponderRelease(e) {\n    this.pressInLocation = null;\n    this._receiveSignal(Signals.RESPONDER_RELEASE, e);\n  },\n  /**\n   * Place as callback for a DOM element's `onResponderTerminate` event.\n   */\n  touchableHandleResponderTerminate: function touchableHandleResponderTerminate(e) {\n    this.pressInLocation = null;\n    this._receiveSignal(Signals.RESPONDER_TERMINATED, e);\n  },\n  /**\n   * Place as callback for a DOM element's `onResponderMove` event.\n   */\n  touchableHandleResponderMove: function touchableHandleResponderMove(e) {\n    // Measurement may not have returned yet.\n    if (!this.state.touchable.positionOnActivate) {\n      return;\n    }\n    var positionOnActivate = this.state.touchable.positionOnActivate;\n    var dimensionsOnActivate = this.state.touchable.dimensionsOnActivate;\n    var pressRectOffset = this.touchableGetPressRectOffset ? this.touchableGetPressRectOffset() : {\n      left: PRESS_EXPAND_PX,\n      right: PRESS_EXPAND_PX,\n      top: PRESS_EXPAND_PX,\n      bottom: PRESS_EXPAND_PX\n    };\n    var pressExpandLeft = pressRectOffset.left;\n    var pressExpandTop = pressRectOffset.top;\n    var pressExpandRight = pressRectOffset.right;\n    var pressExpandBottom = pressRectOffset.bottom;\n    var hitSlop = this.touchableGetHitSlop ? this.touchableGetHitSlop() : null;\n    if (hitSlop) {\n      pressExpandLeft += hitSlop.left || 0;\n      pressExpandTop += hitSlop.top || 0;\n      pressExpandRight += hitSlop.right || 0;\n      pressExpandBottom += hitSlop.bottom || 0;\n    }\n    var touch = extractSingleTouch(e.nativeEvent);\n    var pageX = touch && touch.pageX;\n    var pageY = touch && touch.pageY;\n    if (this.pressInLocation) {\n      var movedDistance = this._getDistanceBetweenPoints(pageX, pageY, this.pressInLocation.pageX, this.pressInLocation.pageY);\n      if (movedDistance > LONG_PRESS_ALLOWED_MOVEMENT) {\n        this._cancelLongPressDelayTimeout();\n      }\n    }\n    var isTouchWithinActive = pageX > positionOnActivate.left - pressExpandLeft && pageY > positionOnActivate.top - pressExpandTop && pageX < positionOnActivate.left + dimensionsOnActivate.width + pressExpandRight && pageY < positionOnActivate.top + dimensionsOnActivate.height + pressExpandBottom;\n    if (isTouchWithinActive) {\n      var prevState = this.state.touchable.touchState;\n      this._receiveSignal(Signals.ENTER_PRESS_RECT, e);\n      var curState = this.state.touchable.touchState;\n      if (curState === States.RESPONDER_INACTIVE_PRESS_IN && prevState !== States.RESPONDER_INACTIVE_PRESS_IN) {\n        // fix for t7967420\n        this._cancelLongPressDelayTimeout();\n      }\n    } else {\n      this._cancelLongPressDelayTimeout();\n      this._receiveSignal(Signals.LEAVE_PRESS_RECT, e);\n    }\n  },\n  /**\n   * Invoked when the item receives focus. Mixers might override this to\n   * visually distinguish the `VisualRect` so that the user knows that it\n   * currently has the focus. Most platforms only support a single element being\n   * focused at a time, in which case there may have been a previously focused\n   * element that was blurred just prior to this. This can be overridden when\n   * using `Touchable.Mixin.withoutDefaultFocusAndBlur`.\n   */\n  touchableHandleFocus: function touchableHandleFocus(e) {\n    this.props.onFocus && this.props.onFocus(e);\n  },\n  /**\n   * Invoked when the item loses focus. Mixers might override this to\n   * visually distinguish the `VisualRect` so that the user knows that it\n   * no longer has focus. Most platforms only support a single element being\n   * focused at a time, in which case the focus may have moved to another.\n   * This can be overridden when using\n   * `Touchable.Mixin.withoutDefaultFocusAndBlur`.\n   */\n  touchableHandleBlur: function touchableHandleBlur(e) {\n    this.props.onBlur && this.props.onBlur(e);\n  },\n  // ==== Abstract Application Callbacks ====\n\n  /**\n   * Invoked when the item should be highlighted. Mixers should implement this\n   * to visually distinguish the `VisualRect` so that the user knows that\n   * releasing a touch will result in a \"selection\" (analog to click).\n   *\n   * @abstract\n   * touchableHandleActivePressIn: function,\n   */\n\n  /**\n   * Invoked when the item is \"active\" (in that it is still eligible to become\n   * a \"select\") but the touch has left the `PressRect`. Usually the mixer will\n   * want to unhighlight the `VisualRect`. If the user (while pressing) moves\n   * back into the `PressRect` `touchableHandleActivePressIn` will be invoked\n   * again and the mixer should probably highlight the `VisualRect` again. This\n   * event will not fire on an `touchEnd/mouseUp` event, only move events while\n   * the user is depressing the mouse/touch.\n   *\n   * @abstract\n   * touchableHandleActivePressOut: function\n   */\n\n  /**\n   * Invoked when the item is \"selected\" - meaning the interaction ended by\n   * letting up while the item was either in the state\n   * `RESPONDER_ACTIVE_PRESS_IN` or `RESPONDER_INACTIVE_PRESS_IN`.\n   *\n   * @abstract\n   * touchableHandlePress: function\n   */\n\n  /**\n   * Invoked when the item is long pressed - meaning the interaction ended by\n   * letting up while the item was in `RESPONDER_ACTIVE_LONG_PRESS_IN`. If\n   * `touchableHandleLongPress` is *not* provided, `touchableHandlePress` will\n   * be called as it normally is. If `touchableHandleLongPress` is provided, by\n   * default any `touchableHandlePress` callback will not be invoked. To\n   * override this default behavior, override `touchableLongPressCancelsPress`\n   * to return false. As a result, `touchableHandlePress` will be called when\n   * lifting up, even if `touchableHandleLongPress` has also been called.\n   *\n   * @abstract\n   * touchableHandleLongPress: function\n   */\n\n  /**\n   * Returns the number of millis to wait before triggering a highlight.\n   *\n   * @abstract\n   * touchableGetHighlightDelayMS: function\n   */\n\n  /**\n   * Returns the amount to extend the `HitRect` into the `PressRect`. Positive\n   * numbers mean the size expands outwards.\n   *\n   * @abstract\n   * touchableGetPressRectOffset: function\n   */\n\n  // ==== Internal Logic ====\n\n  /**\n   * Measures the `HitRect` node on activation. The Bounding rectangle is with\n   * respect to viewport - not page, so adding the `pageXOffset/pageYOffset`\n   * should result in points that are in the same coordinate system as an\n   * event's `globalX/globalY` data values.\n   *\n   * - Consider caching this for the lifetime of the component, or possibly\n   *   being able to share this cache between any `ScrollMap` view.\n   *\n   * @sideeffects\n   * @private\n   */\n  _remeasureMetricsOnActivation: function _remeasureMetricsOnActivation() {\n    var tag = this.state.touchable.responderID;\n    if (tag == null) {\n      return;\n    }\n    UIManager.measure(tag, this._handleQueryLayout);\n  },\n  _handleQueryLayout: function _handleQueryLayout(l, t, w, h, globalX, globalY) {\n    //don't do anything UIManager failed to measure node\n    if (!l && !t && !w && !h && !globalX && !globalY) {\n      return;\n    }\n    this.state.touchable.positionOnActivate && Position.release(this.state.touchable.positionOnActivate);\n    this.state.touchable.dimensionsOnActivate &&\n    // $FlowFixMe\n    BoundingDimensions.release(this.state.touchable.dimensionsOnActivate);\n    this.state.touchable.positionOnActivate = Position.getPooled(globalX, globalY);\n    // $FlowFixMe\n    this.state.touchable.dimensionsOnActivate = BoundingDimensions.getPooled(w, h);\n  },\n  _handleDelay: function _handleDelay(e) {\n    this.touchableDelayTimeout = null;\n    this._receiveSignal(Signals.DELAY, e);\n  },\n  _handleLongDelay: function _handleLongDelay(e) {\n    this.longPressDelayTimeout = null;\n    var curState = this.state.touchable.touchState;\n    if (curState !== States.RESPONDER_ACTIVE_PRESS_IN && curState !== States.RESPONDER_ACTIVE_LONG_PRESS_IN) {\n      console.error('Attempted to transition from state `' + curState + '` to `' + States.RESPONDER_ACTIVE_LONG_PRESS_IN + '`, which is not supported. This is ' + 'most likely due to `Touchable.longPressDelayTimeout` not being cancelled.');\n    } else {\n      this._receiveSignal(Signals.LONG_PRESS_DETECTED, e);\n    }\n  },\n  /**\n   * Receives a state machine signal, performs side effects of the transition\n   * and stores the new state. Validates the transition as well.\n   *\n   * @param {Signals} signal State machine signal.\n   * @throws Error if invalid state transition or unrecognized signal.\n   * @sideeffects\n   */\n  _receiveSignal: function _receiveSignal(signal, e) {\n    var responderID = this.state.touchable.responderID;\n    var curState = this.state.touchable.touchState;\n    var nextState = Transitions[curState] && Transitions[curState][signal];\n    if (!responderID && signal === Signals.RESPONDER_RELEASE) {\n      return;\n    }\n    if (!nextState) {\n      throw new Error('Unrecognized signal `' + signal + '` or state `' + curState + '` for Touchable responder `' + responderID + '`');\n    }\n    if (nextState === States.ERROR) {\n      throw new Error('Touchable cannot transition from `' + curState + '` to `' + signal + '` for responder `' + responderID + '`');\n    }\n    if (curState !== nextState) {\n      this._performSideEffectsForTransition(curState, nextState, signal, e);\n      this.state.touchable.touchState = nextState;\n    }\n  },\n  _cancelLongPressDelayTimeout: function _cancelLongPressDelayTimeout() {\n    this.longPressDelayTimeout && clearTimeout(this.longPressDelayTimeout);\n    this.longPressDelayTimeout = null;\n  },\n  _isHighlight: function _isHighlight(state) {\n    return state === States.RESPONDER_ACTIVE_PRESS_IN || state === States.RESPONDER_ACTIVE_LONG_PRESS_IN;\n  },\n  _savePressInLocation: function _savePressInLocation(e) {\n    var touch = extractSingleTouch(e.nativeEvent);\n    var pageX = touch && touch.pageX;\n    var pageY = touch && touch.pageY;\n    var locationX = touch && touch.locationX;\n    var locationY = touch && touch.locationY;\n    this.pressInLocation = {\n      pageX,\n      pageY,\n      locationX,\n      locationY\n    };\n  },\n  _getDistanceBetweenPoints: function _getDistanceBetweenPoints(aX, aY, bX, bY) {\n    var deltaX = aX - bX;\n    var deltaY = aY - bY;\n    return Math.sqrt(deltaX * deltaX + deltaY * deltaY);\n  },\n  /**\n   * Will perform a transition between touchable states, and identify any\n   * highlighting or unhighlighting that must be performed for this particular\n   * transition.\n   *\n   * @param {States} curState Current Touchable state.\n   * @param {States} nextState Next Touchable state.\n   * @param {Signal} signal Signal that triggered the transition.\n   * @param {Event} e Native event.\n   * @sideeffects\n   */\n  _performSideEffectsForTransition: function _performSideEffectsForTransition(curState, nextState, signal, e) {\n    var curIsHighlight = this._isHighlight(curState);\n    var newIsHighlight = this._isHighlight(nextState);\n    var isFinalSignal = signal === Signals.RESPONDER_TERMINATED || signal === Signals.RESPONDER_RELEASE;\n    if (isFinalSignal) {\n      this._cancelLongPressDelayTimeout();\n    }\n    var isInitialTransition = curState === States.NOT_RESPONDER && nextState === States.RESPONDER_INACTIVE_PRESS_IN;\n    var isActiveTransition = !IsActive[curState] && IsActive[nextState];\n    if (isInitialTransition || isActiveTransition) {\n      this._remeasureMetricsOnActivation();\n    }\n    if (IsPressingIn[curState] && signal === Signals.LONG_PRESS_DETECTED) {\n      this.touchableHandleLongPress && this.touchableHandleLongPress(e);\n    }\n    if (newIsHighlight && !curIsHighlight) {\n      this._startHighlight(e);\n    } else if (!newIsHighlight && curIsHighlight) {\n      this._endHighlight(e);\n    }\n    if (IsPressingIn[curState] && signal === Signals.RESPONDER_RELEASE) {\n      var hasLongPressHandler = !!this.props.onLongPress;\n      var pressIsLongButStillCallOnPress = IsLongPressingIn[curState] && (\n      // We *are* long pressing.. // But either has no long handler\n      !hasLongPressHandler || !this.touchableLongPressCancelsPress()); // or we're told to ignore it.\n\n      var shouldInvokePress = !IsLongPressingIn[curState] || pressIsLongButStillCallOnPress;\n      if (shouldInvokePress && this.touchableHandlePress) {\n        if (!newIsHighlight && !curIsHighlight) {\n          // we never highlighted because of delay, but we should highlight now\n          this._startHighlight(e);\n          this._endHighlight(e);\n        }\n        this.touchableHandlePress(e);\n      }\n    }\n    this.touchableDelayTimeout && clearTimeout(this.touchableDelayTimeout);\n    this.touchableDelayTimeout = null;\n  },\n  _playTouchSound: function _playTouchSound() {\n    UIManager.playTouchSound();\n  },\n  _startHighlight: function _startHighlight(e) {\n    this._savePressInLocation(e);\n    this.touchableHandleActivePressIn && this.touchableHandleActivePressIn(e);\n  },\n  _endHighlight: function _endHighlight(e) {\n    if (this.touchableHandleActivePressOut) {\n      if (this.touchableGetPressOutDelayMS && this.touchableGetPressOutDelayMS()) {\n        this.pressOutDelayTimeout = setTimeout(() => {\n          this.touchableHandleActivePressOut(e);\n        }, this.touchableGetPressOutDelayMS());\n      } else {\n        this.touchableHandleActivePressOut(e);\n      }\n    }\n  },\n  // HACK (part 2): basic support for touchable interactions using a keyboard (including\n  // delays and longPress)\n  touchableHandleKeyEvent: function touchableHandleKeyEvent(e) {\n    var type = e.type,\n      key = e.key;\n    if (key === 'Enter' || key === ' ') {\n      if (type === 'keydown') {\n        if (!this._isTouchableKeyboardActive) {\n          if (!this.state.touchable.touchState || this.state.touchable.touchState === States.NOT_RESPONDER) {\n            this.touchableHandleResponderGrant(e);\n            this._isTouchableKeyboardActive = true;\n          }\n        }\n      } else if (type === 'keyup') {\n        if (this._isTouchableKeyboardActive) {\n          if (this.state.touchable.touchState && this.state.touchable.touchState !== States.NOT_RESPONDER) {\n            this.touchableHandleResponderRelease(e);\n            this._isTouchableKeyboardActive = false;\n          }\n        }\n      }\n      e.stopPropagation();\n      // prevent the default behaviour unless the Touchable functions as a link\n      // and Enter is pressed\n      if (!(key === 'Enter' && AccessibilityUtil.propsToAriaRole(this.props) === 'link')) {\n        e.preventDefault();\n      }\n    }\n  },\n  withoutDefaultFocusAndBlur: {}\n};\n\n/**\n * Provide an optional version of the mixin where `touchableHandleFocus` and\n * `touchableHandleBlur` can be overridden. This allows appropriate defaults to\n * be set on TV platforms, without breaking existing implementations of\n * `Touchable`.\n */\nvar touchableHandleFocus = TouchableMixin.touchableHandleFocus,\n  touchableHandleBlur = TouchableMixin.touchableHandleBlur,\n  TouchableMixinWithoutDefaultFocusAndBlur = _objectWithoutPropertiesLoose(TouchableMixin, [\"touchableHandleFocus\", \"touchableHandleBlur\"]);\nTouchableMixin.withoutDefaultFocusAndBlur = TouchableMixinWithoutDefaultFocusAndBlur;\nvar Touchable = {\n  Mixin: TouchableMixin,\n  TOUCH_TARGET_DEBUG: false,\n  // Highlights all touchable targets. Toggle with Inspector.\n  /**\n   * Renders a debugging overlay to visualize touch target with hitSlop (might not work on Android).\n   */\n  renderDebugView: _ref => {\n    var color = _ref.color,\n      hitSlop = _ref.hitSlop;\n    if (!Touchable.TOUCH_TARGET_DEBUG) {\n      return null;\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      throw Error('Touchable.TOUCH_TARGET_DEBUG should not be enabled in prod!');\n    }\n    var debugHitSlopStyle = {};\n    hitSlop = hitSlop || {\n      top: 0,\n      bottom: 0,\n      left: 0,\n      right: 0\n    };\n    for (var key in hitSlop) {\n      debugHitSlopStyle[key] = -hitSlop[key];\n    }\n    var normalizedColor = normalizeColor(color);\n    if (typeof normalizedColor !== 'number') {\n      return null;\n    }\n    var hexColor = '#' + ('00000000' + normalizedColor.toString(16)).substr(-8);\n    return /*#__PURE__*/React.createElement(View, {\n      pointerEvents: \"none\",\n      style: _objectSpread({\n        position: 'absolute',\n        borderColor: hexColor.slice(0, -2) + '55',\n        // More opaque\n        borderWidth: 1,\n        borderStyle: 'dashed',\n        backgroundColor: hexColor.slice(0, -2) + '0F'\n      }, debugHitSlopStyle)\n    });\n  }\n};\nexport default Touchable;"], "mappings": "AAUA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,qDAAqD;AAC/F,OAAOC,aAAa,MAAM,sCAAsC;AAChE,OAAOC,iBAAiB;AACxB,OAAOC,kBAAkB;AACzB,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,QAAQ;AACf,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS;AAChB,OAAOC,IAAI;AACX,SAASC,QAAQ;AACjB,IAAIC,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAGC,WAAW,EAAI;EACtC,IAAIC,OAAO,GAAGD,WAAW,CAACC,OAAO;EACjC,IAAIC,cAAc,GAAGF,WAAW,CAACE,cAAc;EAC/C,IAAIC,UAAU,GAAGF,OAAO,IAAIA,OAAO,CAACG,MAAM,GAAG,CAAC;EAC9C,IAAIC,iBAAiB,GAAGH,cAAc,IAAIA,cAAc,CAACE,MAAM,GAAG,CAAC;EACnE,OAAO,CAACD,UAAU,IAAIE,iBAAiB,GAAGH,cAAc,CAAC,CAAC,CAAC,GAAGC,UAAU,GAAGF,OAAO,CAAC,CAAC,CAAC,GAAGD,WAAW;AACrG,CAAC;AAiGD,IAAIM,MAAM,GAAG;EACXC,aAAa,EAAE,eAAe;EAE9BC,2BAA2B,EAAE,6BAA6B;EAE1DC,4BAA4B,EAAE,8BAA8B;EAE5DC,yBAAyB,EAAE,2BAA2B;EAEtDC,0BAA0B,EAAE,4BAA4B;EAExDC,8BAA8B,EAAE,gCAAgC;EAEhEC,+BAA+B,EAAE,iCAAiC;EAElEC,KAAK,EAAE;AACT,CAAC;AAKD,IAAIC,oBAAoB,GAAG;EACzBR,aAAa,EAAE,KAAK;EACpBC,2BAA2B,EAAE,KAAK;EAClCC,4BAA4B,EAAE,KAAK;EACnCC,yBAAyB,EAAE,KAAK;EAChCC,0BAA0B,EAAE,KAAK;EACjCC,8BAA8B,EAAE,KAAK;EACrCC,+BAA+B,EAAE,KAAK;EACtCC,KAAK,EAAE;AACT,CAAC;AACD,IAAIE,QAAQ,GAAG1B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEyB,oBAAoB,CAAC,EAAE,CAAC,CAAC,EAAE;EACxEJ,0BAA0B,EAAE,IAAI;EAChCD,yBAAyB,EAAE;AAC7B,CAAC,CAAC;AAMF,IAAIO,YAAY,GAAG3B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEyB,oBAAoB,CAAC,EAAE,CAAC,CAAC,EAAE;EAC5EP,2BAA2B,EAAE,IAAI;EACjCE,yBAAyB,EAAE,IAAI;EAC/BE,8BAA8B,EAAE;AAClC,CAAC,CAAC;AACF,IAAIM,gBAAgB,GAAG5B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEyB,oBAAoB,CAAC,EAAE,CAAC,CAAC,EAAE;EAChFH,8BAA8B,EAAE;AAClC,CAAC,CAAC;AAKF,IAAIO,OAAO,GAAG;EACZC,KAAK,EAAE,OAAO;EACdC,eAAe,EAAE,iBAAiB;EAClCC,iBAAiB,EAAE,mBAAmB;EACtCC,oBAAoB,EAAE,sBAAsB;EAC5CC,gBAAgB,EAAE,kBAAkB;EACpCC,gBAAgB,EAAE,kBAAkB;EACpCC,mBAAmB,EAAE;AACvB,CAAC;AAID,IAAIC,WAAW,GAAG;EAChBpB,aAAa,EAAE;IACba,KAAK,EAAEd,MAAM,CAACQ,KAAK;IACnBO,eAAe,EAAEf,MAAM,CAACE,2BAA2B;IACnDc,iBAAiB,EAAEhB,MAAM,CAACQ,KAAK;IAC/BS,oBAAoB,EAAEjB,MAAM,CAACQ,KAAK;IAClCU,gBAAgB,EAAElB,MAAM,CAACQ,KAAK;IAC9BW,gBAAgB,EAAEnB,MAAM,CAACQ,KAAK;IAC9BY,mBAAmB,EAAEpB,MAAM,CAACQ;EAC9B,CAAC;EACDN,2BAA2B,EAAE;IAC3BY,KAAK,EAAEd,MAAM,CAACI,yBAAyB;IACvCW,eAAe,EAAEf,MAAM,CAACQ,KAAK;IAC7BQ,iBAAiB,EAAEhB,MAAM,CAACC,aAAa;IACvCgB,oBAAoB,EAAEjB,MAAM,CAACC,aAAa;IAC1CiB,gBAAgB,EAAElB,MAAM,CAACE,2BAA2B;IACpDiB,gBAAgB,EAAEnB,MAAM,CAACG,4BAA4B;IACrDiB,mBAAmB,EAAEpB,MAAM,CAACQ;EAC9B,CAAC;EACDL,4BAA4B,EAAE;IAC5BW,KAAK,EAAEd,MAAM,CAACK,0BAA0B;IACxCU,eAAe,EAAEf,MAAM,CAACQ,KAAK;IAC7BQ,iBAAiB,EAAEhB,MAAM,CAACC,aAAa;IACvCgB,oBAAoB,EAAEjB,MAAM,CAACC,aAAa;IAC1CiB,gBAAgB,EAAElB,MAAM,CAACE,2BAA2B;IACpDiB,gBAAgB,EAAEnB,MAAM,CAACG,4BAA4B;IACrDiB,mBAAmB,EAAEpB,MAAM,CAACQ;EAC9B,CAAC;EACDJ,yBAAyB,EAAE;IACzBU,KAAK,EAAEd,MAAM,CAACQ,KAAK;IACnBO,eAAe,EAAEf,MAAM,CAACQ,KAAK;IAC7BQ,iBAAiB,EAAEhB,MAAM,CAACC,aAAa;IACvCgB,oBAAoB,EAAEjB,MAAM,CAACC,aAAa;IAC1CiB,gBAAgB,EAAElB,MAAM,CAACI,yBAAyB;IAClDe,gBAAgB,EAAEnB,MAAM,CAACK,0BAA0B;IACnDe,mBAAmB,EAAEpB,MAAM,CAACM;EAC9B,CAAC;EACDD,0BAA0B,EAAE;IAC1BS,KAAK,EAAEd,MAAM,CAACQ,KAAK;IACnBO,eAAe,EAAEf,MAAM,CAACQ,KAAK;IAC7BQ,iBAAiB,EAAEhB,MAAM,CAACC,aAAa;IACvCgB,oBAAoB,EAAEjB,MAAM,CAACC,aAAa;IAC1CiB,gBAAgB,EAAElB,MAAM,CAACI,yBAAyB;IAClDe,gBAAgB,EAAEnB,MAAM,CAACK,0BAA0B;IACnDe,mBAAmB,EAAEpB,MAAM,CAACQ;EAC9B,CAAC;EACDF,8BAA8B,EAAE;IAC9BQ,KAAK,EAAEd,MAAM,CAACQ,KAAK;IACnBO,eAAe,EAAEf,MAAM,CAACQ,KAAK;IAC7BQ,iBAAiB,EAAEhB,MAAM,CAACC,aAAa;IACvCgB,oBAAoB,EAAEjB,MAAM,CAACC,aAAa;IAC1CiB,gBAAgB,EAAElB,MAAM,CAACM,8BAA8B;IACvDa,gBAAgB,EAAEnB,MAAM,CAACO,+BAA+B;IACxDa,mBAAmB,EAAEpB,MAAM,CAACM;EAC9B,CAAC;EACDC,+BAA+B,EAAE;IAC/BO,KAAK,EAAEd,MAAM,CAACQ,KAAK;IACnBO,eAAe,EAAEf,MAAM,CAACQ,KAAK;IAC7BQ,iBAAiB,EAAEhB,MAAM,CAACC,aAAa;IACvCgB,oBAAoB,EAAEjB,MAAM,CAACC,aAAa;IAC1CiB,gBAAgB,EAAElB,MAAM,CAACM,8BAA8B;IACvDa,gBAAgB,EAAEnB,MAAM,CAACO,+BAA+B;IACxDa,mBAAmB,EAAEpB,MAAM,CAACQ;EAC9B,CAAC;EACDc,KAAK,EAAE;IACLR,KAAK,EAAEd,MAAM,CAACC,aAAa;IAC3Bc,eAAe,EAAEf,MAAM,CAACE,2BAA2B;IACnDc,iBAAiB,EAAEhB,MAAM,CAACC,aAAa;IACvCgB,oBAAoB,EAAEjB,MAAM,CAACC,aAAa;IAC1CiB,gBAAgB,EAAElB,MAAM,CAACC,aAAa;IACtCkB,gBAAgB,EAAEnB,MAAM,CAACC,aAAa;IACtCmB,mBAAmB,EAAEpB,MAAM,CAACC;EAC9B;AACF,CAAC;AAKD,IAAIsB,kBAAkB,GAAG,GAAG;AAC5B,IAAIC,eAAe,GAAG,EAAE;AACxB,IAAIC,oBAAoB,GAAG,GAAG;AAC9B,IAAIC,mBAAmB,GAAGD,oBAAoB,GAAGF,kBAAkB;AACnE,IAAII,2BAA2B,GAAG,EAAE;AAoEpC,IAAIC,cAAc,GAAG;EAEnBC,iBAAiB,EAAE,SAASA,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAC9CtC,QAAQ,CAAC,gBAAgB,EAAE,qDAAqD,CAAC;IACjF,IAAIuC,aAAa,GAAG,IAAI,CAACC,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAAC,CAAC;IACpE,IAAID,aAAa,IAAIA,aAAa,CAACE,gBAAgB,EAAE;MACnD,IAAI,CAACC,sBAAsB,GAAG,UAAAC,CAAC,EAAI;QACjC,IAAIL,KAAI,CAACM,0BAA0B,EAAE;UACnC,IAAIN,KAAI,CAACO,KAAK,CAACC,SAAS,CAACC,UAAU,IAAIT,KAAI,CAACO,KAAK,CAACC,SAAS,CAACC,UAAU,KAAKvC,MAAM,CAACC,aAAa,EAAE;YAC/F6B,KAAI,CAACU,iCAAiC,CAAC;cACrC9C,WAAW,EAAEyC;YACf,CAAC,CAAC;UACJ;UACAL,KAAI,CAACM,0BAA0B,GAAG,KAAK;QACzC;MACF,CAAC;MACDL,aAAa,CAACE,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAACC,sBAAsB,CAAC;IACrE;EACF,CAAC;EAIDO,oBAAoB,EAAE,SAASA,oBAAoBA,CAAA,EAAG;IACpD,IAAIV,aAAa,GAAG,IAAI,CAACC,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAAC,CAAC;IACpE,IAAID,aAAa,IAAIA,aAAa,CAACE,gBAAgB,EAAE;MACnDF,aAAa,CAACW,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAACR,sBAAsB,CAAC;IACxE;IACA,IAAI,CAACS,qBAAqB,IAAIC,YAAY,CAAC,IAAI,CAACD,qBAAqB,CAAC;IACtE,IAAI,CAACE,qBAAqB,IAAID,YAAY,CAAC,IAAI,CAACC,qBAAqB,CAAC;IACtE,IAAI,CAACC,oBAAoB,IAAIF,YAAY,CAAC,IAAI,CAACE,oBAAoB,CAAC;IAEpE,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACV,KAAK,CAACC,SAAS,CAACU,WAAW,GAAG,IAAI;EACzC,CAAC;EAQDC,wBAAwB,EAAE,SAASA,wBAAwBA,CAAA,EAAG;IAC5D,OAAO;MACLX,SAAS,EAAE;QACTC,UAAU,EAAEW,SAAS;QACrBF,WAAW,EAAE;MACf;IACF,CAAC;EACH,CAAC;EAKDG,0CAA0C,EAAE,SAASA,0CAA0CA,CAAA,EAAG;IAChG,OAAO,CAAC,IAAI,CAACC,KAAK,CAACC,0BAA0B;EAC/C,CAAC;EAIDC,sCAAsC,EAAE,SAASA,sCAAsCA,CAAA,EAAG;IACxF,OAAO,CAAC,IAAI,CAACF,KAAK,CAACG,QAAQ;EAC7B,CAAC;EAIDC,8BAA8B,EAAE,SAASA,8BAA8BA,CAAA,EAAG;IACxE,OAAO,IAAI;EACb,CAAC;EAMDC,6BAA6B,EAAE,SAASA,6BAA6BA,CAACtB,CAAC,EAAE;IACvE,IAAIuB,UAAU,GAAGvB,CAAC,CAACwB,aAAa;IAIhCxB,CAAC,CAACyB,OAAO,CAAC,CAAC;IACX,IAAI,CAACd,oBAAoB,IAAIF,YAAY,CAAC,IAAI,CAACE,oBAAoB,CAAC;IACpE,IAAI,CAACA,oBAAoB,GAAG,IAAI;IAChC,IAAI,CAACT,KAAK,CAACC,SAAS,CAACC,UAAU,GAAGvC,MAAM,CAACC,aAAa;IACtD,IAAI,CAACoC,KAAK,CAACC,SAAS,CAACU,WAAW,GAAGU,UAAU;IAC7C,IAAI,CAACG,cAAc,CAAChD,OAAO,CAACE,eAAe,EAAEoB,CAAC,CAAC;IAC/C,IAAI2B,OAAO,GAAG,IAAI,CAACC,4BAA4B,KAAKb,SAAS,GAAGc,IAAI,CAACC,GAAG,CAAC,IAAI,CAACF,4BAA4B,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGxC,kBAAkB;IACrIuC,OAAO,GAAGI,KAAK,CAACJ,OAAO,CAAC,GAAGvC,kBAAkB,GAAGuC,OAAO;IACvD,IAAIA,OAAO,KAAK,CAAC,EAAE;MACjB,IAAI,CAACnB,qBAAqB,GAAGwB,UAAU,CAAC,IAAI,CAACC,YAAY,CAACC,IAAI,CAAC,IAAI,EAAElC,CAAC,CAAC,EAAE2B,OAAO,CAAC;IACnF,CAAC,MAAM;MACL,IAAI,CAACM,YAAY,CAACjC,CAAC,CAAC;IACtB;IACA,IAAImC,WAAW,GAAG,IAAI,CAACC,4BAA4B,KAAKrB,SAAS,GAAGc,IAAI,CAACC,GAAG,CAAC,IAAI,CAACM,4BAA4B,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG7C,mBAAmB;IAC3I4C,WAAW,GAAGJ,KAAK,CAACI,WAAW,CAAC,GAAG5C,mBAAmB,GAAG4C,WAAW;IACpE,IAAI,CAACzB,qBAAqB,GAAGsB,UAAU,CAAC,IAAI,CAACK,gBAAgB,CAACH,IAAI,CAAC,IAAI,EAAElC,CAAC,CAAC,EAAEmC,WAAW,GAAGR,OAAO,CAAC;EACrG,CAAC;EAIDW,+BAA+B,EAAE,SAASA,+BAA+BA,CAACtC,CAAC,EAAE;IAC3E,IAAI,CAACY,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACc,cAAc,CAAChD,OAAO,CAACG,iBAAiB,EAAEmB,CAAC,CAAC;EACnD,CAAC;EAIDK,iCAAiC,EAAE,SAASA,iCAAiCA,CAACL,CAAC,EAAE;IAC/E,IAAI,CAACY,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACc,cAAc,CAAChD,OAAO,CAACI,oBAAoB,EAAEkB,CAAC,CAAC;EACtD,CAAC;EAIDuC,4BAA4B,EAAE,SAASA,4BAA4BA,CAACvC,CAAC,EAAE;IAErE,IAAI,CAAC,IAAI,CAACE,KAAK,CAACC,SAAS,CAACqC,kBAAkB,EAAE;MAC5C;IACF;IACA,IAAIA,kBAAkB,GAAG,IAAI,CAACtC,KAAK,CAACC,SAAS,CAACqC,kBAAkB;IAChE,IAAIC,oBAAoB,GAAG,IAAI,CAACvC,KAAK,CAACC,SAAS,CAACsC,oBAAoB;IACpE,IAAIC,eAAe,GAAG,IAAI,CAACC,2BAA2B,GAAG,IAAI,CAACA,2BAA2B,CAAC,CAAC,GAAG;MAC5FC,IAAI,EAAEvD,eAAe;MACrBwD,KAAK,EAAExD,eAAe;MACtByD,GAAG,EAAEzD,eAAe;MACpB0D,MAAM,EAAE1D;IACV,CAAC;IACD,IAAI2D,eAAe,GAAGN,eAAe,CAACE,IAAI;IAC1C,IAAIK,cAAc,GAAGP,eAAe,CAACI,GAAG;IACxC,IAAII,gBAAgB,GAAGR,eAAe,CAACG,KAAK;IAC5C,IAAIM,iBAAiB,GAAGT,eAAe,CAACK,MAAM;IAC9C,IAAIK,OAAO,GAAG,IAAI,CAACC,mBAAmB,GAAG,IAAI,CAACA,mBAAmB,CAAC,CAAC,GAAG,IAAI;IAC1E,IAAID,OAAO,EAAE;MACXJ,eAAe,IAAII,OAAO,CAACR,IAAI,IAAI,CAAC;MACpCK,cAAc,IAAIG,OAAO,CAACN,GAAG,IAAI,CAAC;MAClCI,gBAAgB,IAAIE,OAAO,CAACP,KAAK,IAAI,CAAC;MACtCM,iBAAiB,IAAIC,OAAO,CAACL,MAAM,IAAI,CAAC;IAC1C;IACA,IAAIO,KAAK,GAAGhG,kBAAkB,CAAC0C,CAAC,CAACzC,WAAW,CAAC;IAC7C,IAAIgG,KAAK,GAAGD,KAAK,IAAIA,KAAK,CAACC,KAAK;IAChC,IAAIC,KAAK,GAAGF,KAAK,IAAIA,KAAK,CAACE,KAAK;IAChC,IAAI,IAAI,CAAC5C,eAAe,EAAE;MACxB,IAAI6C,aAAa,GAAG,IAAI,CAACC,yBAAyB,CAACH,KAAK,EAAEC,KAAK,EAAE,IAAI,CAAC5C,eAAe,CAAC2C,KAAK,EAAE,IAAI,CAAC3C,eAAe,CAAC4C,KAAK,CAAC;MACxH,IAAIC,aAAa,GAAGjE,2BAA2B,EAAE;QAC/C,IAAI,CAACmE,4BAA4B,CAAC,CAAC;MACrC;IACF;IACA,IAAIC,mBAAmB,GAAGL,KAAK,GAAGf,kBAAkB,CAACI,IAAI,GAAGI,eAAe,IAAIQ,KAAK,GAAGhB,kBAAkB,CAACM,GAAG,GAAGG,cAAc,IAAIM,KAAK,GAAGf,kBAAkB,CAACI,IAAI,GAAGH,oBAAoB,CAACoB,KAAK,GAAGX,gBAAgB,IAAIM,KAAK,GAAGhB,kBAAkB,CAACM,GAAG,GAAGL,oBAAoB,CAACqB,MAAM,GAAGX,iBAAiB;IACrS,IAAIS,mBAAmB,EAAE;MACvB,IAAIG,SAAS,GAAG,IAAI,CAAC7D,KAAK,CAACC,SAAS,CAACC,UAAU;MAC/C,IAAI,CAACsB,cAAc,CAAChD,OAAO,CAACK,gBAAgB,EAAEiB,CAAC,CAAC;MAChD,IAAIgE,QAAQ,GAAG,IAAI,CAAC9D,KAAK,CAACC,SAAS,CAACC,UAAU;MAC9C,IAAI4D,QAAQ,KAAKnG,MAAM,CAACE,2BAA2B,IAAIgG,SAAS,KAAKlG,MAAM,CAACE,2BAA2B,EAAE;QAEvG,IAAI,CAAC4F,4BAA4B,CAAC,CAAC;MACrC;IACF,CAAC,MAAM;MACL,IAAI,CAACA,4BAA4B,CAAC,CAAC;MACnC,IAAI,CAACjC,cAAc,CAAChD,OAAO,CAACM,gBAAgB,EAAEgB,CAAC,CAAC;IAClD;EACF,CAAC;EASDiE,oBAAoB,EAAE,SAASA,oBAAoBA,CAACjE,CAAC,EAAE;IACrD,IAAI,CAACiB,KAAK,CAACiD,OAAO,IAAI,IAAI,CAACjD,KAAK,CAACiD,OAAO,CAAClE,CAAC,CAAC;EAC7C,CAAC;EASDmE,mBAAmB,EAAE,SAASA,mBAAmBA,CAACnE,CAAC,EAAE;IACnD,IAAI,CAACiB,KAAK,CAACmD,MAAM,IAAI,IAAI,CAACnD,KAAK,CAACmD,MAAM,CAACpE,CAAC,CAAC;EAC3C,CAAC;EA6EDqE,6BAA6B,EAAE,SAASA,6BAA6BA,CAAA,EAAG;IACtE,IAAIC,GAAG,GAAG,IAAI,CAACpE,KAAK,CAACC,SAAS,CAACU,WAAW;IAC1C,IAAIyD,GAAG,IAAI,IAAI,EAAE;MACf;IACF;IACAnH,SAAS,CAACoH,OAAO,CAACD,GAAG,EAAE,IAAI,CAACE,kBAAkB,CAAC;EACjD,CAAC;EACDA,kBAAkB,EAAE,SAASA,kBAAkBA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,OAAO,EAAEC,OAAO,EAAE;IAE5E,IAAI,CAACL,CAAC,IAAI,CAACC,CAAC,IAAI,CAACC,CAAC,IAAI,CAACC,CAAC,IAAI,CAACC,OAAO,IAAI,CAACC,OAAO,EAAE;MAChD;IACF;IACA,IAAI,CAAC5E,KAAK,CAACC,SAAS,CAACqC,kBAAkB,IAAIvF,QAAQ,CAAC8H,OAAO,CAAC,IAAI,CAAC7E,KAAK,CAACC,SAAS,CAACqC,kBAAkB,CAAC;IACpG,IAAI,CAACtC,KAAK,CAACC,SAAS,CAACsC,oBAAoB,IAEzC1F,kBAAkB,CAACgI,OAAO,CAAC,IAAI,CAAC7E,KAAK,CAACC,SAAS,CAACsC,oBAAoB,CAAC;IACrE,IAAI,CAACvC,KAAK,CAACC,SAAS,CAACqC,kBAAkB,GAAGvF,QAAQ,CAAC+H,SAAS,CAACH,OAAO,EAAEC,OAAO,CAAC;IAE9E,IAAI,CAAC5E,KAAK,CAACC,SAAS,CAACsC,oBAAoB,GAAG1F,kBAAkB,CAACiI,SAAS,CAACL,CAAC,EAAEC,CAAC,CAAC;EAChF,CAAC;EACD3C,YAAY,EAAE,SAASA,YAAYA,CAACjC,CAAC,EAAE;IACrC,IAAI,CAACQ,qBAAqB,GAAG,IAAI;IACjC,IAAI,CAACkB,cAAc,CAAChD,OAAO,CAACC,KAAK,EAAEqB,CAAC,CAAC;EACvC,CAAC;EACDqC,gBAAgB,EAAE,SAASA,gBAAgBA,CAACrC,CAAC,EAAE;IAC7C,IAAI,CAACU,qBAAqB,GAAG,IAAI;IACjC,IAAIsD,QAAQ,GAAG,IAAI,CAAC9D,KAAK,CAACC,SAAS,CAACC,UAAU;IAC9C,IAAI4D,QAAQ,KAAKnG,MAAM,CAACI,yBAAyB,IAAI+F,QAAQ,KAAKnG,MAAM,CAACM,8BAA8B,EAAE;MACvG8G,OAAO,CAAC9F,KAAK,CAAC,sCAAsC,GAAG6E,QAAQ,GAAG,QAAQ,GAAGnG,MAAM,CAACM,8BAA8B,GAAG,qCAAqC,GAAG,2EAA2E,CAAC;IAC3O,CAAC,MAAM;MACL,IAAI,CAACuD,cAAc,CAAChD,OAAO,CAACO,mBAAmB,EAAEe,CAAC,CAAC;IACrD;EACF,CAAC;EASD0B,cAAc,EAAE,SAASA,cAAcA,CAACwD,MAAM,EAAElF,CAAC,EAAE;IACjD,IAAIa,WAAW,GAAG,IAAI,CAACX,KAAK,CAACC,SAAS,CAACU,WAAW;IAClD,IAAImD,QAAQ,GAAG,IAAI,CAAC9D,KAAK,CAACC,SAAS,CAACC,UAAU;IAC9C,IAAI+E,SAAS,GAAGjG,WAAW,CAAC8E,QAAQ,CAAC,IAAI9E,WAAW,CAAC8E,QAAQ,CAAC,CAACkB,MAAM,CAAC;IACtE,IAAI,CAACrE,WAAW,IAAIqE,MAAM,KAAKxG,OAAO,CAACG,iBAAiB,EAAE;MACxD;IACF;IACA,IAAI,CAACsG,SAAS,EAAE;MACd,MAAM,IAAIC,KAAK,CAAC,uBAAuB,GAAGF,MAAM,GAAG,cAAc,GAAGlB,QAAQ,GAAG,6BAA6B,GAAGnD,WAAW,GAAG,GAAG,CAAC;IACnI;IACA,IAAIsE,SAAS,KAAKtH,MAAM,CAACQ,KAAK,EAAE;MAC9B,MAAM,IAAI+G,KAAK,CAAC,oCAAoC,GAAGpB,QAAQ,GAAG,QAAQ,GAAGkB,MAAM,GAAG,mBAAmB,GAAGrE,WAAW,GAAG,GAAG,CAAC;IAChI;IACA,IAAImD,QAAQ,KAAKmB,SAAS,EAAE;MAC1B,IAAI,CAACE,gCAAgC,CAACrB,QAAQ,EAAEmB,SAAS,EAAED,MAAM,EAAElF,CAAC,CAAC;MACrE,IAAI,CAACE,KAAK,CAACC,SAAS,CAACC,UAAU,GAAG+E,SAAS;IAC7C;EACF,CAAC;EACDxB,4BAA4B,EAAE,SAASA,4BAA4BA,CAAA,EAAG;IACpE,IAAI,CAACjD,qBAAqB,IAAID,YAAY,CAAC,IAAI,CAACC,qBAAqB,CAAC;IACtE,IAAI,CAACA,qBAAqB,GAAG,IAAI;EACnC,CAAC;EACD4E,YAAY,EAAE,SAASA,YAAYA,CAACpF,KAAK,EAAE;IACzC,OAAOA,KAAK,KAAKrC,MAAM,CAACI,yBAAyB,IAAIiC,KAAK,KAAKrC,MAAM,CAACM,8BAA8B;EACtG,CAAC;EACDoH,oBAAoB,EAAE,SAASA,oBAAoBA,CAACvF,CAAC,EAAE;IACrD,IAAIsD,KAAK,GAAGhG,kBAAkB,CAAC0C,CAAC,CAACzC,WAAW,CAAC;IAC7C,IAAIgG,KAAK,GAAGD,KAAK,IAAIA,KAAK,CAACC,KAAK;IAChC,IAAIC,KAAK,GAAGF,KAAK,IAAIA,KAAK,CAACE,KAAK;IAChC,IAAIgC,SAAS,GAAGlC,KAAK,IAAIA,KAAK,CAACkC,SAAS;IACxC,IAAIC,SAAS,GAAGnC,KAAK,IAAIA,KAAK,CAACmC,SAAS;IACxC,IAAI,CAAC7E,eAAe,GAAG;MACrB2C,KAAK,EAALA,KAAK;MACLC,KAAK,EAALA,KAAK;MACLgC,SAAS,EAATA,SAAS;MACTC,SAAS,EAATA;IACF,CAAC;EACH,CAAC;EACD/B,yBAAyB,EAAE,SAASA,yBAAyBA,CAACgC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;IAC5E,IAAIC,MAAM,GAAGJ,EAAE,GAAGE,EAAE;IACpB,IAAIG,MAAM,GAAGJ,EAAE,GAAGE,EAAE;IACpB,OAAOhE,IAAI,CAACmE,IAAI,CAACF,MAAM,GAAGA,MAAM,GAAGC,MAAM,GAAGA,MAAM,CAAC;EACrD,CAAC;EAYDV,gCAAgC,EAAE,SAASA,gCAAgCA,CAACrB,QAAQ,EAAEmB,SAAS,EAAED,MAAM,EAAElF,CAAC,EAAE;IAC1G,IAAIiG,cAAc,GAAG,IAAI,CAACX,YAAY,CAACtB,QAAQ,CAAC;IAChD,IAAIkC,cAAc,GAAG,IAAI,CAACZ,YAAY,CAACH,SAAS,CAAC;IACjD,IAAIgB,aAAa,GAAGjB,MAAM,KAAKxG,OAAO,CAACI,oBAAoB,IAAIoG,MAAM,KAAKxG,OAAO,CAACG,iBAAiB;IACnG,IAAIsH,aAAa,EAAE;MACjB,IAAI,CAACxC,4BAA4B,CAAC,CAAC;IACrC;IACA,IAAIyC,mBAAmB,GAAGpC,QAAQ,KAAKnG,MAAM,CAACC,aAAa,IAAIqH,SAAS,KAAKtH,MAAM,CAACE,2BAA2B;IAC/G,IAAIsI,kBAAkB,GAAG,CAAC9H,QAAQ,CAACyF,QAAQ,CAAC,IAAIzF,QAAQ,CAAC4G,SAAS,CAAC;IACnE,IAAIiB,mBAAmB,IAAIC,kBAAkB,EAAE;MAC7C,IAAI,CAAChC,6BAA6B,CAAC,CAAC;IACtC;IACA,IAAI7F,YAAY,CAACwF,QAAQ,CAAC,IAAIkB,MAAM,KAAKxG,OAAO,CAACO,mBAAmB,EAAE;MACpE,IAAI,CAACqH,wBAAwB,IAAI,IAAI,CAACA,wBAAwB,CAACtG,CAAC,CAAC;IACnE;IACA,IAAIkG,cAAc,IAAI,CAACD,cAAc,EAAE;MACrC,IAAI,CAACM,eAAe,CAACvG,CAAC,CAAC;IACzB,CAAC,MAAM,IAAI,CAACkG,cAAc,IAAID,cAAc,EAAE;MAC5C,IAAI,CAACO,aAAa,CAACxG,CAAC,CAAC;IACvB;IACA,IAAIxB,YAAY,CAACwF,QAAQ,CAAC,IAAIkB,MAAM,KAAKxG,OAAO,CAACG,iBAAiB,EAAE;MAClE,IAAI4H,mBAAmB,GAAG,CAAC,CAAC,IAAI,CAACxF,KAAK,CAACyF,WAAW;MAClD,IAAIC,8BAA8B,GAAGlI,gBAAgB,CAACuF,QAAQ,CAAC,KAE/D,CAACyC,mBAAmB,IAAI,CAAC,IAAI,CAACpF,8BAA8B,CAAC,CAAC,CAAC;MAE/D,IAAIuF,iBAAiB,GAAG,CAACnI,gBAAgB,CAACuF,QAAQ,CAAC,IAAI2C,8BAA8B;MACrF,IAAIC,iBAAiB,IAAI,IAAI,CAACC,oBAAoB,EAAE;QAClD,IAAI,CAACX,cAAc,IAAI,CAACD,cAAc,EAAE;UAEtC,IAAI,CAACM,eAAe,CAACvG,CAAC,CAAC;UACvB,IAAI,CAACwG,aAAa,CAACxG,CAAC,CAAC;QACvB;QACA,IAAI,CAAC6G,oBAAoB,CAAC7G,CAAC,CAAC;MAC9B;IACF;IACA,IAAI,CAACQ,qBAAqB,IAAIC,YAAY,CAAC,IAAI,CAACD,qBAAqB,CAAC;IACtE,IAAI,CAACA,qBAAqB,GAAG,IAAI;EACnC,CAAC;EACDsG,eAAe,EAAE,SAASA,eAAeA,CAAA,EAAG;IAC1C3J,SAAS,CAAC4J,cAAc,CAAC,CAAC;EAC5B,CAAC;EACDR,eAAe,EAAE,SAASA,eAAeA,CAACvG,CAAC,EAAE;IAC3C,IAAI,CAACuF,oBAAoB,CAACvF,CAAC,CAAC;IAC5B,IAAI,CAACgH,4BAA4B,IAAI,IAAI,CAACA,4BAA4B,CAAChH,CAAC,CAAC;EAC3E,CAAC;EACDwG,aAAa,EAAE,SAASA,aAAaA,CAACxG,CAAC,EAAE;IAAA,IAAAiH,MAAA;IACvC,IAAI,IAAI,CAACC,6BAA6B,EAAE;MACtC,IAAI,IAAI,CAACC,2BAA2B,IAAI,IAAI,CAACA,2BAA2B,CAAC,CAAC,EAAE;QAC1E,IAAI,CAACxG,oBAAoB,GAAGqB,UAAU,CAAC,YAAM;UAC3CiF,MAAI,CAACC,6BAA6B,CAAClH,CAAC,CAAC;QACvC,CAAC,EAAE,IAAI,CAACmH,2BAA2B,CAAC,CAAC,CAAC;MACxC,CAAC,MAAM;QACL,IAAI,CAACD,6BAA6B,CAAClH,CAAC,CAAC;MACvC;IACF;EACF,CAAC;EAGDoH,uBAAuB,EAAE,SAASA,uBAAuBA,CAACpH,CAAC,EAAE;IAC3D,IAAIqH,IAAI,GAAGrH,CAAC,CAACqH,IAAI;MACfC,GAAG,GAAGtH,CAAC,CAACsH,GAAG;IACb,IAAIA,GAAG,KAAK,OAAO,IAAIA,GAAG,KAAK,GAAG,EAAE;MAClC,IAAID,IAAI,KAAK,SAAS,EAAE;QACtB,IAAI,CAAC,IAAI,CAACpH,0BAA0B,EAAE;UACpC,IAAI,CAAC,IAAI,CAACC,KAAK,CAACC,SAAS,CAACC,UAAU,IAAI,IAAI,CAACF,KAAK,CAACC,SAAS,CAACC,UAAU,KAAKvC,MAAM,CAACC,aAAa,EAAE;YAChG,IAAI,CAACwD,6BAA6B,CAACtB,CAAC,CAAC;YACrC,IAAI,CAACC,0BAA0B,GAAG,IAAI;UACxC;QACF;MACF,CAAC,MAAM,IAAIoH,IAAI,KAAK,OAAO,EAAE;QAC3B,IAAI,IAAI,CAACpH,0BAA0B,EAAE;UACnC,IAAI,IAAI,CAACC,KAAK,CAACC,SAAS,CAACC,UAAU,IAAI,IAAI,CAACF,KAAK,CAACC,SAAS,CAACC,UAAU,KAAKvC,MAAM,CAACC,aAAa,EAAE;YAC/F,IAAI,CAACwE,+BAA+B,CAACtC,CAAC,CAAC;YACvC,IAAI,CAACC,0BAA0B,GAAG,KAAK;UACzC;QACF;MACF;MACAD,CAAC,CAACuH,eAAe,CAAC,CAAC;MAGnB,IAAI,EAAED,GAAG,KAAK,OAAO,IAAIxK,iBAAiB,CAAC0K,eAAe,CAAC,IAAI,CAACvG,KAAK,CAAC,KAAK,MAAM,CAAC,EAAE;QAClFjB,CAAC,CAACyH,cAAc,CAAC,CAAC;MACpB;IACF;EACF,CAAC;EACDC,0BAA0B,EAAE,CAAC;AAC/B,CAAC;AAQD,IAAIzD,oBAAoB,GAAGxE,cAAc,CAACwE,oBAAoB;EAC5DE,mBAAmB,GAAG1E,cAAc,CAAC0E,mBAAmB;EACxDwD,wCAAwC,GAAG/K,6BAA6B,CAAC6C,cAAc,EAAE,CAAC,sBAAsB,EAAE,qBAAqB,CAAC,CAAC;AAC3IA,cAAc,CAACiI,0BAA0B,GAAGC,wCAAwC;AACpF,IAAIC,SAAS,GAAG;EACdC,KAAK,EAAEpI,cAAc;EACrBqI,kBAAkB,EAAE,KAAK;EAKzBC,eAAe,EAAE,SAAjBA,eAAeA,CAAEC,IAAI,EAAI;IACvB,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;MACpB7E,OAAO,GAAG4E,IAAI,CAAC5E,OAAO;IACxB,IAAI,CAACwE,SAAS,CAACE,kBAAkB,EAAE;MACjC,OAAO,IAAI;IACb;IACA,IAAII,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,MAAMhD,KAAK,CAAC,6DAA6D,CAAC;IAC5E;IACA,IAAIiD,iBAAiB,GAAG,CAAC,CAAC;IAC1BjF,OAAO,GAAGA,OAAO,IAAI;MACnBN,GAAG,EAAE,CAAC;MACNC,MAAM,EAAE,CAAC;MACTH,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE;IACT,CAAC;IACD,KAAK,IAAIyE,GAAG,IAAIlE,OAAO,EAAE;MACvBiF,iBAAiB,CAACf,GAAG,CAAC,GAAG,CAAClE,OAAO,CAACkE,GAAG,CAAC;IACxC;IACA,IAAIgB,eAAe,GAAGtL,cAAc,CAACiL,KAAK,CAAC;IAC3C,IAAI,OAAOK,eAAe,KAAK,QAAQ,EAAE;MACvC,OAAO,IAAI;IACb;IACA,IAAIC,QAAQ,GAAG,GAAG,GAAG,CAAC,UAAU,GAAGD,eAAe,CAACE,QAAQ,CAAC,EAAE,CAAC,EAAEC,MAAM,CAAC,CAAC,CAAC,CAAC;IAC3E,OAAoBvL,KAAK,CAACwL,aAAa,CAACtL,IAAI,EAAE;MAC5CuL,aAAa,EAAE,MAAM;MACrBC,KAAK,EAAE/L,aAAa,CAAC;QACnBgM,QAAQ,EAAE,UAAU;QACpBC,WAAW,EAAEP,QAAQ,CAACQ,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI;QAEzCC,WAAW,EAAE,CAAC;QACdC,WAAW,EAAE,QAAQ;QACrBC,eAAe,EAAEX,QAAQ,CAACQ,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG;MAC3C,CAAC,EAAEV,iBAAiB;IACtB,CAAC,CAAC;EACJ;AACF,CAAC;AACD,eAAeT,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}