{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport React, { useState, useEffect } from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport ScrollView from \"react-native-web/dist/exports/ScrollView\";\nimport TouchableOpacity from \"react-native-web/dist/exports/TouchableOpacity\";\nimport FlatList from \"react-native-web/dist/exports/FlatList\";\nimport RefreshControl from \"react-native-web/dist/exports/RefreshControl\";\nimport { Searchbar, Button, Chip } from 'react-native-paper';\nimport PageTitle from \"../../components/PageTitle\";\nimport Wrapper from \"../../components/Wrapper\";\nimport Card from \"../../components/Card\";\nimport { SkeletonCard } from \"../../components/LoadingSkeleton\";\nimport styles from \"./styles\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar News = function News() {\n  var _useState = useState([]),\n    _useState2 = _slicedToArray(_useState, 2),\n    newsData = _useState2[0],\n    setNewsData = _useState2[1];\n  var _useState3 = useState(true),\n    _useState4 = _slicedToArray(_useState3, 2),\n    loading = _useState4[0],\n    setLoading = _useState4[1];\n  var _useState5 = useState(false),\n    _useState6 = _slicedToArray(_useState5, 2),\n    refreshing = _useState6[0],\n    setRefreshing = _useState6[1];\n  var _useState7 = useState(''),\n    _useState8 = _slicedToArray(_useState7, 2),\n    searchQuery = _useState8[0],\n    setSearchQuery = _useState8[1];\n  var _useState9 = useState('All'),\n    _useState0 = _slicedToArray(_useState9, 2),\n    selectedCategory = _useState0[0],\n    setSelectedCategory = _useState0[1];\n  var categories = ['All', 'Bitcoin', 'Ethereum', 'DeFi', 'NFTs', 'Regulation', 'Market Analysis'];\n  useEffect(function () {\n    var fetchedNews = [{\n      id: '1',\n      title: 'Bitcoin Reaches New All-Time High Above $65,000',\n      category: 'Bitcoin',\n      date: '2024-01-15',\n      time: '2 hours ago',\n      content: 'Bitcoin has surged to a new all-time high, breaking through the $65,000 resistance level amid institutional adoption and positive market sentiment. The cryptocurrency has gained over 15% in the past week.',\n      author: 'CryptoNews Team',\n      readTime: '3 min read',\n      trending: true,\n      image: '📈'\n    }, {\n      id: '2',\n      title: 'Ethereum 2.0 Staking Rewards Hit Record Levels',\n      category: 'Ethereum',\n      date: '2024-01-15',\n      time: '4 hours ago',\n      content: 'Ethereum staking rewards have reached unprecedented levels as more validators join the network, strengthening the blockchain\\'s security and decentralization.',\n      author: 'DeFi Analyst',\n      readTime: '5 min read',\n      trending: false,\n      image: '⚡'\n    }, {\n      id: '3',\n      title: 'Major DeFi Protocol Launches Cross-Chain Bridge',\n      category: 'DeFi',\n      date: '2024-01-14',\n      time: '6 hours ago',\n      content: 'A leading DeFi protocol has announced the launch of its cross-chain bridge, enabling seamless asset transfers between multiple blockchains.',\n      author: 'Blockchain Reporter',\n      readTime: '4 min read',\n      trending: true,\n      image: '🌉'\n    }, {\n      id: '4',\n      title: 'NFT Market Shows Signs of Recovery',\n      category: 'NFTs',\n      date: '2024-01-14',\n      time: '8 hours ago',\n      content: 'The NFT market is showing positive signs with increased trading volume and new collections gaining traction among collectors.',\n      author: 'NFT Insider',\n      readTime: '3 min read',\n      trending: false,\n      image: '🎨'\n    }, {\n      id: '5',\n      title: 'New Crypto Regulations Proposed in Europe',\n      category: 'Regulation',\n      date: '2024-01-13',\n      time: '1 day ago',\n      content: 'European regulators have proposed new comprehensive cryptocurrency regulations aimed at protecting investors while fostering innovation.',\n      author: 'Regulatory Watch',\n      readTime: '6 min read',\n      trending: false,\n      image: '⚖️'\n    }];\n    setTimeout(function () {\n      setNewsData(fetchedNews);\n      setLoading(false);\n    }, 1000);\n  }, []);\n  var onRefresh = function onRefresh() {\n    setRefreshing(true);\n    setTimeout(function () {\n      setRefreshing(false);\n    }, 1000);\n  };\n  var filteredNews = newsData.filter(function (news) {\n    var matchesSearch = news.title.toLowerCase().includes(searchQuery.toLowerCase()) || news.content.toLowerCase().includes(searchQuery.toLowerCase());\n    var matchesCategory = selectedCategory === 'All' || news.category === selectedCategory;\n    return matchesSearch && matchesCategory;\n  });\n  if (loading) {\n    return _jsxs(Wrapper, {\n      children: [_jsx(PageTitle, {\n        text: \"Crypto News\"\n      }), _jsxs(ScrollView, {\n        style: {\n          padding: 16\n        },\n        children: [_jsx(SkeletonCard, {}), _jsx(SkeletonCard, {}), _jsx(SkeletonCard, {})]\n      })]\n    });\n  }\n  return _jsx(Wrapper, {\n    children: _jsxs(ScrollView, {\n      style: {\n        flex: 1\n      },\n      refreshControl: _jsx(RefreshControl, {\n        refreshing: refreshing,\n        onRefresh: onRefresh\n      }),\n      children: [_jsxs(View, {\n        style: {\n          padding: 16,\n          paddingBottom: 8\n        },\n        children: [_jsx(Text, {\n          style: {\n            color: '#fff',\n            fontSize: 28,\n            fontFamily: 'Poppins_700Bold',\n            marginBottom: 4\n          },\n          children: \"Crypto News \\uD83D\\uDCF0\"\n        }), _jsx(Text, {\n          style: {\n            color: '#8a8a8a',\n            fontSize: 14,\n            fontFamily: 'Poppins_400Regular'\n          },\n          children: \"Stay updated with the latest cryptocurrency news\"\n        })]\n      }), _jsx(View, {\n        style: {\n          paddingHorizontal: 16,\n          marginBottom: 16\n        },\n        children: _jsx(Searchbar, {\n          placeholder: \"Search news articles...\",\n          onChangeText: setSearchQuery,\n          value: searchQuery,\n          style: {\n            backgroundColor: '#2a2a2a',\n            elevation: 0\n          },\n          inputStyle: {\n            color: '#fff'\n          },\n          iconColor: \"#8a8a8a\",\n          placeholderTextColor: \"#8a8a8a\"\n        })\n      }), _jsx(View, {\n        style: {\n          paddingHorizontal: 16,\n          marginBottom: 16\n        },\n        children: _jsx(ScrollView, {\n          horizontal: true,\n          showsHorizontalScrollIndicator: false,\n          children: categories.map(function (category) {\n            return _jsx(Chip, {\n              selected: selectedCategory === category,\n              onPress: function onPress() {\n                return setSelectedCategory(category);\n              },\n              style: {\n                marginRight: 8,\n                backgroundColor: selectedCategory === category ? '#FECB37' : '#2a2a2a'\n              },\n              textStyle: {\n                color: selectedCategory === category ? '#000' : '#fff',\n                fontFamily: 'Poppins_500Medium'\n              },\n              children: category\n            }, category);\n          })\n        })\n      }), _jsxs(View, {\n        style: {\n          paddingHorizontal: 16,\n          marginBottom: 16\n        },\n        children: [_jsx(Text, {\n          style: {\n            color: '#fff',\n            fontSize: 18,\n            fontFamily: 'Poppins_600SemiBold',\n            marginBottom: 12\n          },\n          children: \"Trending Now \\uD83D\\uDD25\"\n        }), _jsx(ScrollView, {\n          horizontal: true,\n          showsHorizontalScrollIndicator: false,\n          children: filteredNews.filter(function (news) {\n            return news.trending;\n          }).map(function (news) {\n            return _jsxs(Card, {\n              style: {\n                marginRight: 12,\n                width: 280\n              },\n              children: [_jsxs(View, {\n                style: {\n                  flexDirection: 'row',\n                  alignItems: 'center',\n                  marginBottom: 8\n                },\n                children: [_jsx(Text, {\n                  style: {\n                    fontSize: 20,\n                    marginRight: 8\n                  },\n                  children: news.image\n                }), _jsx(View, {\n                  style: {\n                    backgroundColor: '#FECB37',\n                    paddingHorizontal: 6,\n                    paddingVertical: 2,\n                    borderRadius: 4\n                  },\n                  children: _jsx(Text, {\n                    style: {\n                      color: '#000',\n                      fontSize: 10,\n                      fontFamily: 'Poppins_600SemiBold'\n                    },\n                    children: news.category\n                  })\n                })]\n              }), _jsx(Text, {\n                style: {\n                  color: '#fff',\n                  fontSize: 14,\n                  fontFamily: 'Poppins_600SemiBold',\n                  marginBottom: 8,\n                  lineHeight: 20\n                },\n                children: news.title\n              }), _jsxs(Text, {\n                style: {\n                  color: '#8a8a8a',\n                  fontSize: 12,\n                  fontFamily: 'Poppins_400Regular'\n                },\n                children: [news.time, \" \\u2022 \", news.readTime]\n              })]\n            }, news.id);\n          })\n        })]\n      }), _jsxs(View, {\n        style: {\n          paddingHorizontal: 16,\n          marginBottom: 20\n        },\n        children: [_jsx(Text, {\n          style: {\n            color: '#fff',\n            fontSize: 18,\n            fontFamily: 'Poppins_600SemiBold',\n            marginBottom: 12\n          },\n          children: \"Latest Articles\"\n        }), filteredNews.length === 0 ? _jsx(Card, {\n          children: _jsx(Text, {\n            style: {\n              color: '#8a8a8a',\n              fontSize: 14,\n              fontFamily: 'Poppins_400Regular',\n              textAlign: 'center',\n              padding: 20\n            },\n            children: \"No articles found matching your search criteria.\"\n          })\n        }) : filteredNews.map(function (news) {\n          return _jsx(Card, {\n            style: {\n              marginBottom: 12\n            },\n            children: _jsxs(View, {\n              style: {\n                flexDirection: 'row',\n                alignItems: 'flex-start'\n              },\n              children: [_jsx(View, {\n                style: {\n                  backgroundColor: '#333',\n                  borderRadius: 8,\n                  padding: 12,\n                  marginRight: 12,\n                  alignItems: 'center',\n                  justifyContent: 'center'\n                },\n                children: _jsx(Text, {\n                  style: {\n                    fontSize: 24\n                  },\n                  children: news.image\n                })\n              }), _jsxs(View, {\n                style: {\n                  flex: 1\n                },\n                children: [_jsxs(View, {\n                  style: {\n                    flexDirection: 'row',\n                    alignItems: 'center',\n                    marginBottom: 4\n                  },\n                  children: [_jsx(View, {\n                    style: {\n                      backgroundColor: news.trending ? '#FECB37' : '#333',\n                      paddingHorizontal: 6,\n                      paddingVertical: 2,\n                      borderRadius: 4,\n                      marginRight: 8\n                    },\n                    children: _jsx(Text, {\n                      style: {\n                        color: news.trending ? '#000' : '#fff',\n                        fontSize: 10,\n                        fontFamily: 'Poppins_600SemiBold'\n                      },\n                      children: news.category\n                    })\n                  }), news.trending && _jsx(Text, {\n                    style: {\n                      fontSize: 12\n                    },\n                    children: \"\\uD83D\\uDD25\"\n                  })]\n                }), _jsx(Text, {\n                  style: {\n                    color: '#fff',\n                    fontSize: 16,\n                    fontFamily: 'Poppins_600SemiBold',\n                    marginBottom: 6,\n                    lineHeight: 22\n                  },\n                  children: news.title\n                }), _jsxs(Text, {\n                  style: {\n                    color: '#ccc',\n                    fontSize: 13,\n                    fontFamily: 'Poppins_400Regular',\n                    marginBottom: 8,\n                    lineHeight: 18\n                  },\n                  children: [news.content.substring(0, 120), \"...\"]\n                }), _jsxs(View, {\n                  style: {\n                    flexDirection: 'row',\n                    justifyContent: 'space-between',\n                    alignItems: 'center'\n                  },\n                  children: [_jsxs(View, {\n                    children: [_jsxs(Text, {\n                      style: {\n                        color: '#8a8a8a',\n                        fontSize: 11,\n                        fontFamily: 'Poppins_400Regular'\n                      },\n                      children: [\"By \", news.author]\n                    }), _jsxs(Text, {\n                      style: {\n                        color: '#8a8a8a',\n                        fontSize: 11,\n                        fontFamily: 'Poppins_400Regular'\n                      },\n                      children: [news.time, \" \\u2022 \", news.readTime]\n                    })]\n                  }), _jsx(TouchableOpacity, {\n                    children: _jsx(Text, {\n                      style: {\n                        color: '#FECB37',\n                        fontSize: 12,\n                        fontFamily: 'Poppins_500Medium'\n                      },\n                      children: \"Read More\"\n                    })\n                  })]\n                })]\n              })]\n            })\n          }, news.id);\n        })]\n      })]\n    })\n  });\n};\nexport default News;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "View", "Text", "ScrollView", "TouchableOpacity", "FlatList", "RefreshControl", "Searchbar", "<PERSON><PERSON>", "Chip", "Page<PERSON><PERSON>le", "Wrapper", "Card", "SkeletonCard", "styles", "jsx", "_jsx", "jsxs", "_jsxs", "News", "_useState", "_useState2", "_slicedToArray", "newsData", "setNewsData", "_useState3", "_useState4", "loading", "setLoading", "_useState5", "_useState6", "refreshing", "setRefreshing", "_useState7", "_useState8", "searchQuery", "setSearch<PERSON>uery", "_useState9", "_useState0", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "categories", "fetchedNews", "id", "title", "category", "date", "time", "content", "author", "readTime", "trending", "image", "setTimeout", "onRefresh", "filteredNews", "filter", "news", "matchesSearch", "toLowerCase", "includes", "matchesCategory", "children", "text", "style", "padding", "flex", "refreshControl", "paddingBottom", "color", "fontSize", "fontFamily", "marginBottom", "paddingHorizontal", "placeholder", "onChangeText", "value", "backgroundColor", "elevation", "inputStyle", "iconColor", "placeholderTextColor", "horizontal", "showsHorizontalScrollIndicator", "map", "selected", "onPress", "marginRight", "textStyle", "width", "flexDirection", "alignItems", "paddingVertical", "borderRadius", "lineHeight", "length", "textAlign", "justifyContent", "substring"], "sources": ["E:/CryptoSignalsApp/src/pages/News/index.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { View, Text, ScrollView, TouchableOpacity, FlatList, RefreshControl } from 'react-native';\r\nimport { Searchbar, Button, Chip } from 'react-native-paper';\r\nimport PageTitle from '../../components/PageTitle';\r\nimport Wrapper from '../../components/Wrapper';\r\nimport Card from '../../components/Card';\r\nimport { SkeletonCard } from '../../components/LoadingSkeleton';\r\nimport styles from './styles';\r\n\r\nconst News = () => {\r\n  const [newsData, setNewsData] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [refreshing, setRefreshing] = useState(false);\r\n  const [searchQuery, setSearchQuery] = useState('');\r\n  const [selectedCategory, setSelectedCategory] = useState('All');\r\n\r\n  const categories = ['All', 'Bitcoin', 'Ethereum', 'DeFi', 'NFTs', 'Regulation', 'Market Analysis'];\r\n\r\n  useEffect(() => {\r\n    // Simulate fetching news\r\n    const fetchedNews = [\r\n      {\r\n        id: '1',\r\n        title: 'Bitcoin Reaches New All-Time High Above $65,000',\r\n        category: 'Bitcoin',\r\n        date: '2024-01-15',\r\n        time: '2 hours ago',\r\n        content: 'Bitcoin has surged to a new all-time high, breaking through the $65,000 resistance level amid institutional adoption and positive market sentiment. The cryptocurrency has gained over 15% in the past week.',\r\n        author: 'CryptoNews Team',\r\n        readTime: '3 min read',\r\n        trending: true,\r\n        image: '📈'\r\n      },\r\n      {\r\n        id: '2',\r\n        title: 'Ethereum 2.0 Staking Rewards Hit Record Levels',\r\n        category: 'Ethereum',\r\n        date: '2024-01-15',\r\n        time: '4 hours ago',\r\n        content: 'Ethereum staking rewards have reached unprecedented levels as more validators join the network, strengthening the blockchain\\'s security and decentralization.',\r\n        author: 'DeFi Analyst',\r\n        readTime: '5 min read',\r\n        trending: false,\r\n        image: '⚡'\r\n      },\r\n      {\r\n        id: '3',\r\n        title: 'Major DeFi Protocol Launches Cross-Chain Bridge',\r\n        category: 'DeFi',\r\n        date: '2024-01-14',\r\n        time: '6 hours ago',\r\n        content: 'A leading DeFi protocol has announced the launch of its cross-chain bridge, enabling seamless asset transfers between multiple blockchains.',\r\n        author: 'Blockchain Reporter',\r\n        readTime: '4 min read',\r\n        trending: true,\r\n        image: '🌉'\r\n      },\r\n      {\r\n        id: '4',\r\n        title: 'NFT Market Shows Signs of Recovery',\r\n        category: 'NFTs',\r\n        date: '2024-01-14',\r\n        time: '8 hours ago',\r\n        content: 'The NFT market is showing positive signs with increased trading volume and new collections gaining traction among collectors.',\r\n        author: 'NFT Insider',\r\n        readTime: '3 min read',\r\n        trending: false,\r\n        image: '🎨'\r\n      },\r\n      {\r\n        id: '5',\r\n        title: 'New Crypto Regulations Proposed in Europe',\r\n        category: 'Regulation',\r\n        date: '2024-01-13',\r\n        time: '1 day ago',\r\n        content: 'European regulators have proposed new comprehensive cryptocurrency regulations aimed at protecting investors while fostering innovation.',\r\n        author: 'Regulatory Watch',\r\n        readTime: '6 min read',\r\n        trending: false,\r\n        image: '⚖️'\r\n      }\r\n    ];\r\n\r\n    setTimeout(() => {\r\n      setNewsData(fetchedNews);\r\n      setLoading(false);\r\n    }, 1000);\r\n  }, []);\r\n\r\n  const onRefresh = () => {\r\n    setRefreshing(true);\r\n    setTimeout(() => {\r\n      setRefreshing(false);\r\n    }, 1000);\r\n  };\r\n\r\n  const filteredNews = newsData.filter(news => {\r\n    const matchesSearch = news.title.toLowerCase().includes(searchQuery.toLowerCase()) ||\r\n                         news.content.toLowerCase().includes(searchQuery.toLowerCase());\r\n    const matchesCategory = selectedCategory === 'All' || news.category === selectedCategory;\r\n    return matchesSearch && matchesCategory;\r\n  });\r\n\r\n  if (loading) {\r\n    return (\r\n      <Wrapper>\r\n        <PageTitle text=\"Crypto News\" />\r\n        <ScrollView style={{ padding: 16 }}>\r\n          <SkeletonCard />\r\n          <SkeletonCard />\r\n          <SkeletonCard />\r\n        </ScrollView>\r\n      </Wrapper>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Wrapper>\r\n      <ScrollView\r\n        style={{ flex: 1 }}\r\n        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}\r\n      >\r\n        {/* Header */}\r\n        <View style={{ padding: 16, paddingBottom: 8 }}>\r\n          <Text style={{\r\n            color: '#fff',\r\n            fontSize: 28,\r\n            fontFamily: 'Poppins_700Bold',\r\n            marginBottom: 4\r\n          }}>\r\n            Crypto News 📰\r\n          </Text>\r\n          <Text style={{\r\n            color: '#8a8a8a',\r\n            fontSize: 14,\r\n            fontFamily: 'Poppins_400Regular'\r\n          }}>\r\n            Stay updated with the latest cryptocurrency news\r\n          </Text>\r\n        </View>\r\n\r\n        {/* Search Bar */}\r\n        <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>\r\n          <Searchbar\r\n            placeholder=\"Search news articles...\"\r\n            onChangeText={setSearchQuery}\r\n            value={searchQuery}\r\n            style={{ backgroundColor: '#2a2a2a', elevation: 0 }}\r\n            inputStyle={{ color: '#fff' }}\r\n            iconColor=\"#8a8a8a\"\r\n            placeholderTextColor=\"#8a8a8a\"\r\n          />\r\n        </View>\r\n\r\n        {/* Categories */}\r\n        <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>\r\n          <ScrollView horizontal showsHorizontalScrollIndicator={false}>\r\n            {categories.map((category) => (\r\n              <Chip\r\n                key={category}\r\n                selected={selectedCategory === category}\r\n                onPress={() => setSelectedCategory(category)}\r\n                style={{\r\n                  marginRight: 8,\r\n                  backgroundColor: selectedCategory === category ? '#FECB37' : '#2a2a2a'\r\n                }}\r\n                textStyle={{\r\n                  color: selectedCategory === category ? '#000' : '#fff',\r\n                  fontFamily: 'Poppins_500Medium'\r\n                }}\r\n              >\r\n                {category}\r\n              </Chip>\r\n            ))}\r\n          </ScrollView>\r\n        </View>\r\n\r\n        {/* Trending News */}\r\n        <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>\r\n          <Text style={{\r\n            color: '#fff',\r\n            fontSize: 18,\r\n            fontFamily: 'Poppins_600SemiBold',\r\n            marginBottom: 12\r\n          }}>\r\n            Trending Now 🔥\r\n          </Text>\r\n          <ScrollView horizontal showsHorizontalScrollIndicator={false}>\r\n            {filteredNews.filter(news => news.trending).map((news) => (\r\n              <Card key={news.id} style={{ marginRight: 12, width: 280 }}>\r\n                <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>\r\n                  <Text style={{ fontSize: 20, marginRight: 8 }}>{news.image}</Text>\r\n                  <View style={{\r\n                    backgroundColor: '#FECB37',\r\n                    paddingHorizontal: 6,\r\n                    paddingVertical: 2,\r\n                    borderRadius: 4\r\n                  }}>\r\n                    <Text style={{\r\n                      color: '#000',\r\n                      fontSize: 10,\r\n                      fontFamily: 'Poppins_600SemiBold'\r\n                    }}>\r\n                      {news.category}\r\n                    </Text>\r\n                  </View>\r\n                </View>\r\n                <Text style={{\r\n                  color: '#fff',\r\n                  fontSize: 14,\r\n                  fontFamily: 'Poppins_600SemiBold',\r\n                  marginBottom: 8,\r\n                  lineHeight: 20\r\n                }}>\r\n                  {news.title}\r\n                </Text>\r\n                <Text style={{\r\n                  color: '#8a8a8a',\r\n                  fontSize: 12,\r\n                  fontFamily: 'Poppins_400Regular'\r\n                }}>\r\n                  {news.time} • {news.readTime}\r\n                </Text>\r\n              </Card>\r\n            ))}\r\n          </ScrollView>\r\n        </View>\r\n\r\n        {/* All News */}\r\n        <View style={{ paddingHorizontal: 16, marginBottom: 20 }}>\r\n          <Text style={{\r\n            color: '#fff',\r\n            fontSize: 18,\r\n            fontFamily: 'Poppins_600SemiBold',\r\n            marginBottom: 12\r\n          }}>\r\n            Latest Articles\r\n          </Text>\r\n\r\n          {filteredNews.length === 0 ? (\r\n            <Card>\r\n              <Text style={{\r\n                color: '#8a8a8a',\r\n                fontSize: 14,\r\n                fontFamily: 'Poppins_400Regular',\r\n                textAlign: 'center',\r\n                padding: 20\r\n              }}>\r\n                No articles found matching your search criteria.\r\n              </Text>\r\n            </Card>\r\n          ) : (\r\n            filteredNews.map((news) => (\r\n              <Card key={news.id} style={{ marginBottom: 12 }}>\r\n                <View style={{ flexDirection: 'row', alignItems: 'flex-start' }}>\r\n                  <View style={{\r\n                    backgroundColor: '#333',\r\n                    borderRadius: 8,\r\n                    padding: 12,\r\n                    marginRight: 12,\r\n                    alignItems: 'center',\r\n                    justifyContent: 'center'\r\n                  }}>\r\n                    <Text style={{ fontSize: 24 }}>{news.image}</Text>\r\n                  </View>\r\n\r\n                  <View style={{ flex: 1 }}>\r\n                    <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 4 }}>\r\n                      <View style={{\r\n                        backgroundColor: news.trending ? '#FECB37' : '#333',\r\n                        paddingHorizontal: 6,\r\n                        paddingVertical: 2,\r\n                        borderRadius: 4,\r\n                        marginRight: 8\r\n                      }}>\r\n                        <Text style={{\r\n                          color: news.trending ? '#000' : '#fff',\r\n                          fontSize: 10,\r\n                          fontFamily: 'Poppins_600SemiBold'\r\n                        }}>\r\n                          {news.category}\r\n                        </Text>\r\n                      </View>\r\n                      {news.trending && (\r\n                        <Text style={{ fontSize: 12 }}>🔥</Text>\r\n                      )}\r\n                    </View>\r\n\r\n                    <Text style={{\r\n                      color: '#fff',\r\n                      fontSize: 16,\r\n                      fontFamily: 'Poppins_600SemiBold',\r\n                      marginBottom: 6,\r\n                      lineHeight: 22\r\n                    }}>\r\n                      {news.title}\r\n                    </Text>\r\n\r\n                    <Text style={{\r\n                      color: '#ccc',\r\n                      fontSize: 13,\r\n                      fontFamily: 'Poppins_400Regular',\r\n                      marginBottom: 8,\r\n                      lineHeight: 18\r\n                    }}>\r\n                      {news.content.substring(0, 120)}...\r\n                    </Text>\r\n\r\n                    <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>\r\n                      <View>\r\n                        <Text style={{\r\n                          color: '#8a8a8a',\r\n                          fontSize: 11,\r\n                          fontFamily: 'Poppins_400Regular'\r\n                        }}>\r\n                          By {news.author}\r\n                        </Text>\r\n                        <Text style={{\r\n                          color: '#8a8a8a',\r\n                          fontSize: 11,\r\n                          fontFamily: 'Poppins_400Regular'\r\n                        }}>\r\n                          {news.time} • {news.readTime}\r\n                        </Text>\r\n                      </View>\r\n                      <TouchableOpacity>\r\n                        <Text style={{\r\n                          color: '#FECB37',\r\n                          fontSize: 12,\r\n                          fontFamily: 'Poppins_500Medium'\r\n                        }}>\r\n                          Read More\r\n                        </Text>\r\n                      </TouchableOpacity>\r\n                    </View>\r\n                  </View>\r\n                </View>\r\n              </Card>\r\n            ))\r\n          )}\r\n        </View>\r\n      </ScrollView>\r\n    </Wrapper>\r\n  );\r\n};\r\n\r\nexport default News;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,gBAAA;AAAA,OAAAC,QAAA;AAAA,OAAAC,cAAA;AAEnD,SAASC,SAAS,EAAEC,MAAM,EAAEC,IAAI,QAAQ,oBAAoB;AAC5D,OAAOC,SAAS;AAChB,OAAOC,OAAO;AACd,OAAOC,IAAI;AACX,SAASC,YAAY;AACrB,OAAOC,MAAM;AAAiB,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAE9B,IAAMC,IAAI,GAAG,SAAPA,IAAIA,CAAA,EAAS;EACjB,IAAAC,SAAA,GAAgCrB,QAAQ,CAAC,EAAE,CAAC;IAAAsB,UAAA,GAAAC,cAAA,CAAAF,SAAA;IAArCG,QAAQ,GAAAF,UAAA;IAAEG,WAAW,GAAAH,UAAA;EAC5B,IAAAI,UAAA,GAA8B1B,QAAQ,CAAC,IAAI,CAAC;IAAA2B,UAAA,GAAAJ,cAAA,CAAAG,UAAA;IAArCE,OAAO,GAAAD,UAAA;IAAEE,UAAU,GAAAF,UAAA;EAC1B,IAAAG,UAAA,GAAoC9B,QAAQ,CAAC,KAAK,CAAC;IAAA+B,UAAA,GAAAR,cAAA,CAAAO,UAAA;IAA5CE,UAAU,GAAAD,UAAA;IAAEE,aAAa,GAAAF,UAAA;EAChC,IAAAG,UAAA,GAAsClC,QAAQ,CAAC,EAAE,CAAC;IAAAmC,UAAA,GAAAZ,cAAA,CAAAW,UAAA;IAA3CE,WAAW,GAAAD,UAAA;IAAEE,cAAc,GAAAF,UAAA;EAClC,IAAAG,UAAA,GAAgDtC,QAAQ,CAAC,KAAK,CAAC;IAAAuC,UAAA,GAAAhB,cAAA,CAAAe,UAAA;IAAxDE,gBAAgB,GAAAD,UAAA;IAAEE,mBAAmB,GAAAF,UAAA;EAE5C,IAAMG,UAAU,GAAG,CAAC,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,iBAAiB,CAAC;EAElGzC,SAAS,CAAC,YAAM;IAEd,IAAM0C,WAAW,GAAG,CAClB;MACEC,EAAE,EAAE,GAAG;MACPC,KAAK,EAAE,iDAAiD;MACxDC,QAAQ,EAAE,SAAS;MACnBC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,aAAa;MACnBC,OAAO,EAAE,8MAA8M;MACvNC,MAAM,EAAE,iBAAiB;MACzBC,QAAQ,EAAE,YAAY;MACtBC,QAAQ,EAAE,IAAI;MACdC,KAAK,EAAE;IACT,CAAC,EACD;MACET,EAAE,EAAE,GAAG;MACPC,KAAK,EAAE,gDAAgD;MACvDC,QAAQ,EAAE,UAAU;MACpBC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,aAAa;MACnBC,OAAO,EAAE,gKAAgK;MACzKC,MAAM,EAAE,cAAc;MACtBC,QAAQ,EAAE,YAAY;MACtBC,QAAQ,EAAE,KAAK;MACfC,KAAK,EAAE;IACT,CAAC,EACD;MACET,EAAE,EAAE,GAAG;MACPC,KAAK,EAAE,iDAAiD;MACxDC,QAAQ,EAAE,MAAM;MAChBC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,aAAa;MACnBC,OAAO,EAAE,6IAA6I;MACtJC,MAAM,EAAE,qBAAqB;MAC7BC,QAAQ,EAAE,YAAY;MACtBC,QAAQ,EAAE,IAAI;MACdC,KAAK,EAAE;IACT,CAAC,EACD;MACET,EAAE,EAAE,GAAG;MACPC,KAAK,EAAE,oCAAoC;MAC3CC,QAAQ,EAAE,MAAM;MAChBC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,aAAa;MACnBC,OAAO,EAAE,+HAA+H;MACxIC,MAAM,EAAE,aAAa;MACrBC,QAAQ,EAAE,YAAY;MACtBC,QAAQ,EAAE,KAAK;MACfC,KAAK,EAAE;IACT,CAAC,EACD;MACET,EAAE,EAAE,GAAG;MACPC,KAAK,EAAE,2CAA2C;MAClDC,QAAQ,EAAE,YAAY;MACtBC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,WAAW;MACjBC,OAAO,EAAE,0IAA0I;MACnJC,MAAM,EAAE,kBAAkB;MAC1BC,QAAQ,EAAE,YAAY;MACtBC,QAAQ,EAAE,KAAK;MACfC,KAAK,EAAE;IACT,CAAC,CACF;IAEDC,UAAU,CAAC,YAAM;MACf7B,WAAW,CAACkB,WAAW,CAAC;MACxBd,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,EAAE,EAAE,CAAC;EAEN,IAAM0B,SAAS,GAAG,SAAZA,SAASA,CAAA,EAAS;IACtBtB,aAAa,CAAC,IAAI,CAAC;IACnBqB,UAAU,CAAC,YAAM;MACfrB,aAAa,CAAC,KAAK,CAAC;IACtB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,IAAMuB,YAAY,GAAGhC,QAAQ,CAACiC,MAAM,CAAC,UAAAC,IAAI,EAAI;IAC3C,IAAMC,aAAa,GAAGD,IAAI,CAACb,KAAK,CAACe,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzB,WAAW,CAACwB,WAAW,CAAC,CAAC,CAAC,IAC7DF,IAAI,CAACT,OAAO,CAACW,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzB,WAAW,CAACwB,WAAW,CAAC,CAAC,CAAC;IACnF,IAAME,eAAe,GAAGtB,gBAAgB,KAAK,KAAK,IAAIkB,IAAI,CAACZ,QAAQ,KAAKN,gBAAgB;IACxF,OAAOmB,aAAa,IAAIG,eAAe;EACzC,CAAC,CAAC;EAEF,IAAIlC,OAAO,EAAE;IACX,OACET,KAAA,CAACP,OAAO;MAAAmD,QAAA,GACN9C,IAAA,CAACN,SAAS;QAACqD,IAAI,EAAC;MAAa,CAAE,CAAC,EAChC7C,KAAA,CAACf,UAAU;QAAC6D,KAAK,EAAE;UAAEC,OAAO,EAAE;QAAG,CAAE;QAAAH,QAAA,GACjC9C,IAAA,CAACH,YAAY,IAAE,CAAC,EAChBG,IAAA,CAACH,YAAY,IAAE,CAAC,EAChBG,IAAA,CAACH,YAAY,IAAE,CAAC;MAAA,CACN,CAAC;IAAA,CACN,CAAC;EAEd;EAEA,OACEG,IAAA,CAACL,OAAO;IAAAmD,QAAA,EACN5C,KAAA,CAACf,UAAU;MACT6D,KAAK,EAAE;QAAEE,IAAI,EAAE;MAAE,CAAE;MACnBC,cAAc,EAAEnD,IAAA,CAACV,cAAc;QAACyB,UAAU,EAAEA,UAAW;QAACuB,SAAS,EAAEA;MAAU,CAAE,CAAE;MAAAQ,QAAA,GAGjF5C,KAAA,CAACjB,IAAI;QAAC+D,KAAK,EAAE;UAAEC,OAAO,EAAE,EAAE;UAAEG,aAAa,EAAE;QAAE,CAAE;QAAAN,QAAA,GAC7C9C,IAAA,CAACd,IAAI;UAAC8D,KAAK,EAAE;YACXK,KAAK,EAAE,MAAM;YACbC,QAAQ,EAAE,EAAE;YACZC,UAAU,EAAE,iBAAiB;YAC7BC,YAAY,EAAE;UAChB,CAAE;UAAAV,QAAA,EAAC;QAEH,CAAM,CAAC,EACP9C,IAAA,CAACd,IAAI;UAAC8D,KAAK,EAAE;YACXK,KAAK,EAAE,SAAS;YAChBC,QAAQ,EAAE,EAAE;YACZC,UAAU,EAAE;UACd,CAAE;UAAAT,QAAA,EAAC;QAEH,CAAM,CAAC;MAAA,CACH,CAAC,EAGP9C,IAAA,CAACf,IAAI;QAAC+D,KAAK,EAAE;UAAES,iBAAiB,EAAE,EAAE;UAAED,YAAY,EAAE;QAAG,CAAE;QAAAV,QAAA,EACvD9C,IAAA,CAACT,SAAS;UACRmE,WAAW,EAAC,yBAAyB;UACrCC,YAAY,EAAEvC,cAAe;UAC7BwC,KAAK,EAAEzC,WAAY;UACnB6B,KAAK,EAAE;YAAEa,eAAe,EAAE,SAAS;YAAEC,SAAS,EAAE;UAAE,CAAE;UACpDC,UAAU,EAAE;YAAEV,KAAK,EAAE;UAAO,CAAE;UAC9BW,SAAS,EAAC,SAAS;UACnBC,oBAAoB,EAAC;QAAS,CAC/B;MAAC,CACE,CAAC,EAGPjE,IAAA,CAACf,IAAI;QAAC+D,KAAK,EAAE;UAAES,iBAAiB,EAAE,EAAE;UAAED,YAAY,EAAE;QAAG,CAAE;QAAAV,QAAA,EACvD9C,IAAA,CAACb,UAAU;UAAC+E,UAAU;UAACC,8BAA8B,EAAE,KAAM;UAAArB,QAAA,EAC1DrB,UAAU,CAAC2C,GAAG,CAAC,UAACvC,QAAQ;YAAA,OACvB7B,IAAA,CAACP,IAAI;cAEH4E,QAAQ,EAAE9C,gBAAgB,KAAKM,QAAS;cACxCyC,OAAO,EAAE,SAATA,OAAOA,CAAA;gBAAA,OAAQ9C,mBAAmB,CAACK,QAAQ,CAAC;cAAA,CAAC;cAC7CmB,KAAK,EAAE;gBACLuB,WAAW,EAAE,CAAC;gBACdV,eAAe,EAAEtC,gBAAgB,KAAKM,QAAQ,GAAG,SAAS,GAAG;cAC/D,CAAE;cACF2C,SAAS,EAAE;gBACTnB,KAAK,EAAE9B,gBAAgB,KAAKM,QAAQ,GAAG,MAAM,GAAG,MAAM;gBACtD0B,UAAU,EAAE;cACd,CAAE;cAAAT,QAAA,EAEDjB;YAAQ,GAZJA,QAaD,CAAC;UAAA,CACR;QAAC,CACQ;MAAC,CACT,CAAC,EAGP3B,KAAA,CAACjB,IAAI;QAAC+D,KAAK,EAAE;UAAES,iBAAiB,EAAE,EAAE;UAAED,YAAY,EAAE;QAAG,CAAE;QAAAV,QAAA,GACvD9C,IAAA,CAACd,IAAI;UAAC8D,KAAK,EAAE;YACXK,KAAK,EAAE,MAAM;YACbC,QAAQ,EAAE,EAAE;YACZC,UAAU,EAAE,qBAAqB;YACjCC,YAAY,EAAE;UAChB,CAAE;UAAAV,QAAA,EAAC;QAEH,CAAM,CAAC,EACP9C,IAAA,CAACb,UAAU;UAAC+E,UAAU;UAACC,8BAA8B,EAAE,KAAM;UAAArB,QAAA,EAC1DP,YAAY,CAACC,MAAM,CAAC,UAAAC,IAAI;YAAA,OAAIA,IAAI,CAACN,QAAQ;UAAA,EAAC,CAACiC,GAAG,CAAC,UAAC3B,IAAI;YAAA,OACnDvC,KAAA,CAACN,IAAI;cAAeoD,KAAK,EAAE;gBAAEuB,WAAW,EAAE,EAAE;gBAAEE,KAAK,EAAE;cAAI,CAAE;cAAA3B,QAAA,GACzD5C,KAAA,CAACjB,IAAI;gBAAC+D,KAAK,EAAE;kBAAE0B,aAAa,EAAE,KAAK;kBAAEC,UAAU,EAAE,QAAQ;kBAAEnB,YAAY,EAAE;gBAAE,CAAE;gBAAAV,QAAA,GAC3E9C,IAAA,CAACd,IAAI;kBAAC8D,KAAK,EAAE;oBAAEM,QAAQ,EAAE,EAAE;oBAAEiB,WAAW,EAAE;kBAAE,CAAE;kBAAAzB,QAAA,EAAEL,IAAI,CAACL;gBAAK,CAAO,CAAC,EAClEpC,IAAA,CAACf,IAAI;kBAAC+D,KAAK,EAAE;oBACXa,eAAe,EAAE,SAAS;oBAC1BJ,iBAAiB,EAAE,CAAC;oBACpBmB,eAAe,EAAE,CAAC;oBAClBC,YAAY,EAAE;kBAChB,CAAE;kBAAA/B,QAAA,EACA9C,IAAA,CAACd,IAAI;oBAAC8D,KAAK,EAAE;sBACXK,KAAK,EAAE,MAAM;sBACbC,QAAQ,EAAE,EAAE;sBACZC,UAAU,EAAE;oBACd,CAAE;oBAAAT,QAAA,EACCL,IAAI,CAACZ;kBAAQ,CACV;gBAAC,CACH,CAAC;cAAA,CACH,CAAC,EACP7B,IAAA,CAACd,IAAI;gBAAC8D,KAAK,EAAE;kBACXK,KAAK,EAAE,MAAM;kBACbC,QAAQ,EAAE,EAAE;kBACZC,UAAU,EAAE,qBAAqB;kBACjCC,YAAY,EAAE,CAAC;kBACfsB,UAAU,EAAE;gBACd,CAAE;gBAAAhC,QAAA,EACCL,IAAI,CAACb;cAAK,CACP,CAAC,EACP1B,KAAA,CAAChB,IAAI;gBAAC8D,KAAK,EAAE;kBACXK,KAAK,EAAE,SAAS;kBAChBC,QAAQ,EAAE,EAAE;kBACZC,UAAU,EAAE;gBACd,CAAE;gBAAAT,QAAA,GACCL,IAAI,CAACV,IAAI,EAAC,UAAG,EAACU,IAAI,CAACP,QAAQ;cAAA,CACxB,CAAC;YAAA,GAjCEO,IAAI,CAACd,EAkCV,CAAC;UAAA,CACR;QAAC,CACQ,CAAC;MAAA,CACT,CAAC,EAGPzB,KAAA,CAACjB,IAAI;QAAC+D,KAAK,EAAE;UAAES,iBAAiB,EAAE,EAAE;UAAED,YAAY,EAAE;QAAG,CAAE;QAAAV,QAAA,GACvD9C,IAAA,CAACd,IAAI;UAAC8D,KAAK,EAAE;YACXK,KAAK,EAAE,MAAM;YACbC,QAAQ,EAAE,EAAE;YACZC,UAAU,EAAE,qBAAqB;YACjCC,YAAY,EAAE;UAChB,CAAE;UAAAV,QAAA,EAAC;QAEH,CAAM,CAAC,EAENP,YAAY,CAACwC,MAAM,KAAK,CAAC,GACxB/E,IAAA,CAACJ,IAAI;UAAAkD,QAAA,EACH9C,IAAA,CAACd,IAAI;YAAC8D,KAAK,EAAE;cACXK,KAAK,EAAE,SAAS;cAChBC,QAAQ,EAAE,EAAE;cACZC,UAAU,EAAE,oBAAoB;cAChCyB,SAAS,EAAE,QAAQ;cACnB/B,OAAO,EAAE;YACX,CAAE;YAAAH,QAAA,EAAC;UAEH,CAAM;QAAC,CACH,CAAC,GAEPP,YAAY,CAAC6B,GAAG,CAAC,UAAC3B,IAAI;UAAA,OACpBzC,IAAA,CAACJ,IAAI;YAAeoD,KAAK,EAAE;cAAEQ,YAAY,EAAE;YAAG,CAAE;YAAAV,QAAA,EAC9C5C,KAAA,CAACjB,IAAI;cAAC+D,KAAK,EAAE;gBAAE0B,aAAa,EAAE,KAAK;gBAAEC,UAAU,EAAE;cAAa,CAAE;cAAA7B,QAAA,GAC9D9C,IAAA,CAACf,IAAI;gBAAC+D,KAAK,EAAE;kBACXa,eAAe,EAAE,MAAM;kBACvBgB,YAAY,EAAE,CAAC;kBACf5B,OAAO,EAAE,EAAE;kBACXsB,WAAW,EAAE,EAAE;kBACfI,UAAU,EAAE,QAAQ;kBACpBM,cAAc,EAAE;gBAClB,CAAE;gBAAAnC,QAAA,EACA9C,IAAA,CAACd,IAAI;kBAAC8D,KAAK,EAAE;oBAAEM,QAAQ,EAAE;kBAAG,CAAE;kBAAAR,QAAA,EAAEL,IAAI,CAACL;gBAAK,CAAO;cAAC,CAC9C,CAAC,EAEPlC,KAAA,CAACjB,IAAI;gBAAC+D,KAAK,EAAE;kBAAEE,IAAI,EAAE;gBAAE,CAAE;gBAAAJ,QAAA,GACvB5C,KAAA,CAACjB,IAAI;kBAAC+D,KAAK,EAAE;oBAAE0B,aAAa,EAAE,KAAK;oBAAEC,UAAU,EAAE,QAAQ;oBAAEnB,YAAY,EAAE;kBAAE,CAAE;kBAAAV,QAAA,GAC3E9C,IAAA,CAACf,IAAI;oBAAC+D,KAAK,EAAE;sBACXa,eAAe,EAAEpB,IAAI,CAACN,QAAQ,GAAG,SAAS,GAAG,MAAM;sBACnDsB,iBAAiB,EAAE,CAAC;sBACpBmB,eAAe,EAAE,CAAC;sBAClBC,YAAY,EAAE,CAAC;sBACfN,WAAW,EAAE;oBACf,CAAE;oBAAAzB,QAAA,EACA9C,IAAA,CAACd,IAAI;sBAAC8D,KAAK,EAAE;wBACXK,KAAK,EAAEZ,IAAI,CAACN,QAAQ,GAAG,MAAM,GAAG,MAAM;wBACtCmB,QAAQ,EAAE,EAAE;wBACZC,UAAU,EAAE;sBACd,CAAE;sBAAAT,QAAA,EACCL,IAAI,CAACZ;oBAAQ,CACV;kBAAC,CACH,CAAC,EACNY,IAAI,CAACN,QAAQ,IACZnC,IAAA,CAACd,IAAI;oBAAC8D,KAAK,EAAE;sBAAEM,QAAQ,EAAE;oBAAG,CAAE;oBAAAR,QAAA,EAAC;kBAAE,CAAM,CACxC;gBAAA,CACG,CAAC,EAEP9C,IAAA,CAACd,IAAI;kBAAC8D,KAAK,EAAE;oBACXK,KAAK,EAAE,MAAM;oBACbC,QAAQ,EAAE,EAAE;oBACZC,UAAU,EAAE,qBAAqB;oBACjCC,YAAY,EAAE,CAAC;oBACfsB,UAAU,EAAE;kBACd,CAAE;kBAAAhC,QAAA,EACCL,IAAI,CAACb;gBAAK,CACP,CAAC,EAEP1B,KAAA,CAAChB,IAAI;kBAAC8D,KAAK,EAAE;oBACXK,KAAK,EAAE,MAAM;oBACbC,QAAQ,EAAE,EAAE;oBACZC,UAAU,EAAE,oBAAoB;oBAChCC,YAAY,EAAE,CAAC;oBACfsB,UAAU,EAAE;kBACd,CAAE;kBAAAhC,QAAA,GACCL,IAAI,CAACT,OAAO,CAACkD,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,EAAC,KAClC;gBAAA,CAAM,CAAC,EAEPhF,KAAA,CAACjB,IAAI;kBAAC+D,KAAK,EAAE;oBAAE0B,aAAa,EAAE,KAAK;oBAAEO,cAAc,EAAE,eAAe;oBAAEN,UAAU,EAAE;kBAAS,CAAE;kBAAA7B,QAAA,GAC3F5C,KAAA,CAACjB,IAAI;oBAAA6D,QAAA,GACH5C,KAAA,CAAChB,IAAI;sBAAC8D,KAAK,EAAE;wBACXK,KAAK,EAAE,SAAS;wBAChBC,QAAQ,EAAE,EAAE;wBACZC,UAAU,EAAE;sBACd,CAAE;sBAAAT,QAAA,GAAC,KACE,EAACL,IAAI,CAACR,MAAM;oBAAA,CACX,CAAC,EACP/B,KAAA,CAAChB,IAAI;sBAAC8D,KAAK,EAAE;wBACXK,KAAK,EAAE,SAAS;wBAChBC,QAAQ,EAAE,EAAE;wBACZC,UAAU,EAAE;sBACd,CAAE;sBAAAT,QAAA,GACCL,IAAI,CAACV,IAAI,EAAC,UAAG,EAACU,IAAI,CAACP,QAAQ;oBAAA,CACxB,CAAC;kBAAA,CACH,CAAC,EACPlC,IAAA,CAACZ,gBAAgB;oBAAA0D,QAAA,EACf9C,IAAA,CAACd,IAAI;sBAAC8D,KAAK,EAAE;wBACXK,KAAK,EAAE,SAAS;wBAChBC,QAAQ,EAAE,EAAE;wBACZC,UAAU,EAAE;sBACd,CAAE;sBAAAT,QAAA,EAAC;oBAEH,CAAM;kBAAC,CACS,CAAC;gBAAA,CACf,CAAC;cAAA,CACH,CAAC;YAAA,CACH;UAAC,GAnFEL,IAAI,CAACd,EAoFV,CAAC;QAAA,CACR,CACF;MAAA,CACG,CAAC;IAAA,CACG;EAAC,CACN,CAAC;AAEd,CAAC;AAED,eAAexB,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}