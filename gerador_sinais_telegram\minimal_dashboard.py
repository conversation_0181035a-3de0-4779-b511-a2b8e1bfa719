#!/usr/bin/env python3
"""
Dashboard <PERSON>ínimo para CryptoSignals
Versão ultra-simples que funciona garantidamente
"""

from flask import Flask, render_template, jsonify
import datetime
import random

app = Flask(__name__)

@app.route('/')
def dashboard():
    """Página principal do dashboard"""
    return render_template('dashboard.html')

@app.route('/api/data')
def get_data():
    """API para obter dados atuais"""
    # Dados simulados que mudam a cada requisição
    data = {
        'services': {
            'gerador_sinais': {
                'name': 'Gerador de Sinais Telegram',
                'status': 'active',
                'uptime': '2d 14h 32m',
                'cpu_usage': round(random.uniform(10, 30), 1),
                'memory_usage': round(random.uniform(40, 60), 1),
                'restart_count': 0
            },
            'sistema-clientes': {
                'name': 'Sistema de Clientes Streamlit',
                'status': random.choice(['active', 'inactive']),
                'uptime': '1h 15m' if random.random() > 0.5 else '0m',
                'cpu_usage': round(random.uniform(0, 15), 1),
                'memory_usage': round(random.uniform(0, 25), 1),
                'restart_count': random.randint(0, 5)
            }
        },
        'server_metrics': {
            'cpu_percent': round(random.uniform(15, 45), 1),
            'memory_percent': round(random.uniform(50, 80), 1),
            'disk_percent': round(random.uniform(35, 55), 1),
            'uptime': '15d 8h 42m'
        },
        'alerts': [
            {
                'level': 'WARNING',
                'service': 'sistema-clientes',
                'message': 'Serviço com alta utilização de CPU',
                'timestamp': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        ] if random.random() > 0.7 else [],
        'logs': [
            {
                'timestamp': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'level': 'INFO',
                'message': 'Sistema funcionando normalmente'
            },
            {
                'timestamp': (datetime.datetime.now() - datetime.timedelta(minutes=1)).strftime('%Y-%m-%d %H:%M:%S'),
                'level': 'INFO',
                'message': 'Monitoramento ativo'
            },
            {
                'timestamp': (datetime.datetime.now() - datetime.timedelta(minutes=2)).strftime('%Y-%m-%d %H:%M:%S'),
                'level': random.choice(['INFO', 'WARNING']),
                'message': random.choice([
                    'Conectado ao Telegram',
                    'Análise de mercado em andamento',
                    'Verificando conexões',
                    'Alta volatilidade detectada'
                ])
            }
        ],
        'timestamp': datetime.datetime.now().isoformat()
    }
    
    return jsonify(data)

@app.route('/api/refresh')
def refresh_data():
    """API para forçar atualização dos dados"""
    return jsonify({'status': 'success', 'message': 'Dados atualizados'})

if __name__ == '__main__':
    print("🚀 Iniciando Dashboard Mínimo do CryptoSignals")
    print("=" * 50)
    print("📊 Dashboard disponível em:")
    print("   http://localhost:5000")
    print("   http://127.0.0.1:5000")
    print("=" * 50)
    
    try:
        app.run(host='127.0.0.1', port=5000, debug=True)
    except Exception as e:
        print(f"Erro ao iniciar: {e}")
        print("Tentando na porta 5001...")
        app.run(host='127.0.0.1', port=5001, debug=True)
