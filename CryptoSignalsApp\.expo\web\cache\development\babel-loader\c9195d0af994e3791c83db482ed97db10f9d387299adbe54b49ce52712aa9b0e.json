{"ast": null, "code": "import * as React from 'react';\nvar NavigationContainerRefContext = React.createContext(undefined);\nexport default NavigationContainerRefContext;", "map": {"version": 3, "names": ["React", "NavigationContainerRefContext", "createContext", "undefined"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\@react-navigation\\core\\src\\NavigationContainerRefContext.tsx"], "sourcesContent": ["import type { ParamListBase } from '@react-navigation/routers';\nimport * as React from 'react';\n\nimport type { NavigationContainerRef } from './types';\n\n/**\n * Context which holds the route prop for a screen.\n */\nconst NavigationContainerRefContext = React.createContext<\n  NavigationContainerRef<ParamListBase> | undefined\n>(undefined);\n\nexport default NavigationContainerRefContext;\n"], "mappings": "AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAO9B,IAAMC,6BAA6B,GAAGD,KAAK,CAACE,aAAa,CAEvDC,SAAS,CAAC;AAEZ,eAAeF,6BAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}