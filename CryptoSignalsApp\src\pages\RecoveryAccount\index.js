import React, { useContext, useState } from "react";
import { ToastAndroid } from "react-native";
import {
  TextInput,
  Text,
  Button,
  useTheme
} from 'react-native-paper';
import Wrapper from "../../components/Wrapper";
import PageTitle from "../../components/PageTitle";
import styles from "./styles";
import { StoreContext } from '../../store';
import { useNavigation } from '@react-navigation/native';
import useEmail from "../../hooks/useEmail";
import { setSubscription } from "../../store/actions";
import { AxiosContext } from "../../store/axios";

export default function RecoveryAccount() {
  const { colors } = useTheme();
  const [_, dispatch] = useContext(StoreContext);
  const [api] = useContext(AxiosContext);
  const [sendEmail, setSendEmail, validSendEmail] = useEmail('');
  const [isLoading, setIsLoading] = useState(false);
  const [code, setCode] = useState('');
  const [showCodeInput, setShowCodeInput] = useState(false);
  const navigation = useNavigation();

  // const keyboardVisible = useKeyboardVisible();

  const handleSendEmail = async () => {
    setIsLoading(true);

    let _email = sendEmail.toLowerCase();

    try {
      await api.post(`/account-recovery/${_email}/send-email`, {
        params: {
          bypassAccessToken: true
        }
      });

      setIsLoading(false);
      setShowCodeInput(true);
      ToastAndroid.showWithGravity("E-mail sent with success", ToastAndroid.SHORT, ToastAndroid.BOTTOM);
    } catch (err) {
      setIsLoading(false);
      ToastAndroid.showWithGravity("Error sending the e-mail", ToastAndroid.SHORT, ToastAndroid.BOTTOM);
    }
  }

  const getSubscriptionStatus = async (customerId) => {
    const {
      data: {
        subscriptionStatus,
        subscriptionPeriodEnd,
        accessToken
      }
    } = await api.get('/stripe-get-subscription-status/' + customerId, {
      data: {
        params: {
          bypassAccessToken: true
        }
      }
    });

    const subscription = {
      subscriptionStatus,
      subscriptionPeriodEnd,
      accessToken,
      lastSubscriptionStatusDate: new Date().toDateString(),
    }
    
    setSubscription(dispatch, subscription);
  }

  const validateCode = async () => {
    let _email = sendEmail.toLowerCase();
    
    try {
      const response = await api.post(`/account-recovery/${_email}/validate-code/${code}`, {
        params: {
          bypassAccessToken: true
        }
      });

      const { valid, signature, recoveryId } = response.data;

      if (!valid) {
        ToastAndroid.showWithGravity(
          "Invalid code",
          ToastAndroid.LONG,
          ToastAndroid.BOTTOM
        );
        return;
      }

      await getSubscriptionStatus(recoveryId);

      if (signature && !signature.subscriptionStatus) {
        navigation.navigate("Premium", { accountNotRecovered: true });
        return;
      }

      navigation.navigate("Channels", { accountHasBeenRecovered: true });
    } catch (err) {
      ToastAndroid.showWithGravity("Error validation code", ToastAndroid.SHORT, ToastAndroid.BOTTOM);
    }
  }


  return (
    <Wrapper>
        <View style={styles.pageTitle}>
          <PageTitle
            key="TitleRecoveryAccount"
            text={"Recovery Account"}
            goBack="Premium"
          />
        </View>

        {!showCodeInput &&
          <View style={styles.row}>
            <Text style={{ ...styles.h1, marginBottom: 24 }}>
              Please enter your registered email address in this field
            </Text>

            <TextInput
              mode="outlined"
              placeholder="Insert an email"
              style={{ ...styles.input, marginBottom: 16 }} 
              onChangeText={(value) => setSendEmail(value)}
              value={sendEmail}
            />

            <Button
              loading={isLoading}
              mode="contained"
              onPress={handleSendEmail}
              disabled={!validSendEmail}
            >
              Send e-mail
            </Button>
          </View>
        }

        {showCodeInput &&
          <View>
            <Text style={{ ...styles.text, marginBottom: 16 }}>
              We need to verify your email address. We have sent an email to <Text style={{color: '#FECB37'}}>{sendEmail.toLowerCase()}</Text> with a 5-digit code that expires in 5 minutes. Please enter this code below.
            </Text>

            <TextInput
              mode="outlined"
              keyboardType="numeric"
              maxLength={5}
              value={code}
              style={{ ...styles.codeInput, marginBottom: 24 }}
              onChangeText={text => setCode(text)}
              placeholder="00000"
            />

            <Button
              mode="contained"
              onPress={validateCode}
              disabled={code.length !== 5}
            >
              Validate code
            </Button>

            <Button
              icon="arrow-left"
              mode="text"
              onPress={() => setShowCodeInput(false)}
            >
              Go back
            </Button>
          </View>
        }
    </Wrapper>
  );
}
/*
Removi propriedades do TextInput que não são necessárias ou que não são reconhecidas pelo componente
TextInput do react-native-paper.
Para o efeito de sublinhado e a cor da linha de base no TextInput, o react-native-paper utiliza propriedades 
diferentes das do TextInput padrão do React Native. Se você precisar desses efeitos, pode ajustar as 
propriedades adequadas fornecidas pelo react-native-paper.
Como eu não tenho o código original dos estilos, fiz suposições sobre as propriedades do estilo, como styles.
input, styles.text e styles.codeInput. Você pode precisar ajustá-los conforme necessário.
Em relação ao problema com o envio de e-mails: a lógica de envio de e-mails parece estar correta aqui. 
Se os e-mails não estão sendo recebidos, recomendo verificar as configurações do seu servidor de e-mail, 
a implementação da rota de back-end /account-recovery/{email}/send-email, ou verificar se há algum problema
 com o serviço de e-mail que você está usando.
*/