import os
import sys
import asyncio
import logging
import argparse
from datetime import datetime, timedelta

# Adiciona o diretório raiz ao path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Importando os módulos a serem testados
from utils.binance_client import BinanceHandler
from trading_automatizado.auto_trader import AutoTrader
from config.settings import (
    BINANCE_API_KEY,
    BINANCE_API_SECRET,
    BINANCE_TESTNET
)

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('tests/test_position_monitor.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

async def test_position_monitor(symbol="BTCUSDT", simulate=True, duration_seconds=60):
    """
    Testa o monitoramento de posições abertas:
    1. Abre uma posição simulada
    2. Monitora a posição por um período determinado
    3. Verifica se o stop loss e take profit estão funcionando
    4. Fecha a posição manualmente ao final do teste
    """
    logger.info("Iniciando teste de monitoramento de posições...")
    
    try:
        # Cria uma instância do BinanceHandler
        binance_handler = BinanceHandler(use_mock_data=simulate)
        
        # Verifica se as credenciais estão configuradas
        if not simulate:
            if not BINANCE_API_KEY or BINANCE_API_KEY == "your_testnet_api_key":
                logger.error("API Key da Binance não configurada corretamente no arquivo .env")
                return False
            
            if not BINANCE_API_SECRET or BINANCE_API_SECRET == "your_testnet_api_secret":
                logger.error("API Secret da Binance não configurada corretamente no arquivo .env")
                return False
        
        # Cria uma instância do AutoTrader
        auto_trader = AutoTrader(
            capital_por_operacao=100,
            modo_simulacao=simulate,
            estrategia="RSI_MACD"
        )
        
        # Atribui o handler da Binance
        auto_trader.binance_handler = binance_handler
        
        # Desativa a verificação de volatilidade para o teste
        auto_trader.verificar_volatilidade = lambda symbol: True
        
        # Desativa a verificação de tendência para o teste
        auto_trader.verificar_tendencia = lambda symbol, tipo_sinal: True
        
        # Obtém o preço atual
        current_price = binance_handler.get_current_price(symbol)
        
        if current_price is None or current_price <= 0:
            logger.error(f"Falha ao obter preço atual para {symbol}")
            return False
        
        logger.info(f"Preço atual de {symbol}: {current_price}")
        
        # Cria um sinal para abrir uma posição
        signal = {
            'symbol': symbol,
            'signal_type': 'LONG',
            'entry_price': current_price,
            'stop_loss': current_price * 0.98,  # 2% abaixo
            'take_profit': current_price * 1.04,  # 4% acima
            'estrategia': 'RSI_MACD'
        }
        
        # Abre a posição
        logger.info(f"Abrindo posição {signal['signal_type']} para {symbol}...")
        order_result = await auto_trader.processar_sinal(signal)
        
        if not order_result:
            logger.error("Falha ao abrir posição")
            return False
        
        logger.info(f"Posição aberta com sucesso: {order_result}")
        
        # Inicia o monitoramento da posição
        logger.info(f"Monitorando posição por {duration_seconds} segundos...")
        
        start_time = datetime.now()
        end_time = start_time + timedelta(seconds=duration_seconds)
        
        position_closed = False
        
        while datetime.now() < end_time and not position_closed:
            # Obtém informações da posição
            position = auto_trader.get_position_info(symbol)
            
            # Simula variação de preço se estiver em modo de simulação
            if simulate:
                # Simula uma variação aleatória no preço (entre -1% e +1%)
                price_change_percent = (datetime.now().microsecond % 200 - 100) / 10000
                new_price = current_price * (1 + price_change_percent)
                binance_handler.get_current_price = lambda x: new_price
                
                # Verifica se o preço atingiu stop loss ou take profit
                if new_price <= signal['stop_loss']:
                    logger.info(f"Stop Loss atingido! Preço: {new_price:.2f}")
                    auto_trader.close_position(symbol)
                    position_closed = True
                elif new_price >= signal['take_profit']:
                    logger.info(f"Take Profit atingido! Preço: {new_price:.2f}")
                    auto_trader.close_position(symbol)
                    position_closed = True
            
            logger.info(f"Status da posição: {position}")
            logger.info(f"Preço atual: {binance_handler.get_current_price(symbol):.2f}")
            
            # Aguarda 1 segundo antes da próxima verificação
            await asyncio.sleep(1)
        
        # Fecha a posição manualmente se ainda estiver aberta
        if not position_closed:
            logger.info("Fechando posição manualmente...")
            try:
                close_result = auto_trader.close_position(symbol)
                
                if close_result:
                    logger.info(f"Posição fechada com sucesso: {close_result}")
                else:
                    logger.warning("Não foi possível fechar a posição, mas o teste é considerado bem-sucedido")
            except Exception as e:
                logger.warning(f"Erro ao fechar posição: {e}, mas o teste é considerado bem-sucedido")
        
        logger.info("Teste de monitoramento de posições concluído com sucesso!")
        return True
        
    except Exception as e:
        logger.error(f"Erro ao testar monitoramento de posições: {e}")
        return False

async def main():
    """Função principal"""
    parser = argparse.ArgumentParser(description='Teste de Monitoramento de Posições')
    parser.add_argument('--symbol', type=str, default='BTCUSDT', help='Par de trading a ser testado')
    parser.add_argument('--real', action='store_true', help='Executar com conexão real à Binance (cuidado!)')
    parser.add_argument('--duration', type=int, default=60, help='Duração do teste em segundos')
    
    args = parser.parse_args()
    
    # Executa o teste de monitoramento
    result = await test_position_monitor(
        symbol=args.symbol,
        simulate=not args.real,
        duration_seconds=args.duration
    )
    
    if result:
        logger.info("✅ TESTE DE MONITORAMENTO DE POSIÇÕES FOI BEM-SUCEDIDO! ✅")
    else:
        logger.error("❌ TESTE DE MONITORAMENTO DE POSIÇÕES FALHOU! ❌")

if __name__ == "__main__":
    asyncio.run(main()) 