import { StyleSheet, Dimensions } from 'react-native';

const windowHeight = Dimensions.get('window').height;

const styles = StyleSheet.create({
  input: {
    width: '80%',
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 4,
    padding: 12,
    marginBottom: 16,
  },
  passwordContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '80%',
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 4,
    padding: 12,
    marginBottom: 16,
  },
  rememberPassword: {
    color: '#007BFF',
    marginBottom: 16,
  },
  forgotPassword: {
    color: '#007BFF',
    marginBottom: 16,
  },
  loginButton: {
    backgroundColor: '#007BFF',
    padding: 12,
    borderRadius: 4,
    width: '80%',
    alignItems: 'center',
    marginBottom: 16,
  },
  loginButtonText: {
    color: '#fff',
    fontSize: 16,
  },
  signupText: {
    color: '#000',
    marginBottom: 8,
  },
  signupButton: {
    color: '#007BFF',
    fontSize: 16,
  },
});

export default styles;

/*
Este arquivo styles.js contém os estilos que são usados no arquivo index.js da pasta Login. 
Você pode ajustar os estilos conforme necessário para se adequar ao design desejado do seu aplicativo.
*/