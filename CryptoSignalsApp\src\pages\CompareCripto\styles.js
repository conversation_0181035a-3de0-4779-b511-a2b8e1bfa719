import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Button } from 'react-native';
import DropDownPicker from 'react-native-dropdown-picker';
import { AntDesign } from '@expo/vector-icons';
import styles from './CryptoComparisonStyles';

const CryptoComparison = () => {
  const [crypto1, setCrypto1] = useState(null);
  const [crypto2, setCrypto2] = useState(null);
  const [comparisonData, setComparisonData] = useState({});

  const toggleCrypto1 = (value) => {
    setCrypto1(value);
  };

  const toggleCrypto2 = (value) => {
    setCrypto2(value);
  };

  const fetchComparisonData = () => {
    // Simule a busca de dados aqui
    const data = {
      crypto1: {
        name: 'Bitcoin',
        price: '$42,000',
        marketCap: '$800 billion',
        // Adicione mais informações relevantes aqui
      },
      crypto2: {
        name: 'Ethereum',
        price: '$3,000',
        marketCap: '$350 billion',
        // Adicione mais informações relevantes aqui
      },
    };
    setComparisonData(data);
  };

  useEffect(() => {
    if (crypto1 && crypto2) {
      fetchComparisonData();
    }
  }, [crypto1, crypto2]);

  return (
    <ScrollView style={styles.container}>
      <View style={styles.selectCrypto}>
        <Text style={styles.title}>Selecione duas criptomoedas para comparar:</Text>
        <DropDownPicker
          items={[
            { label: 'Bitcoin', value: 'btc' },
            { label: 'Ethereum', value: 'eth' },
            // Adicione mais criptomoedas aqui
          ]}
          placeholder="Selecione a primeira criptomoeda"
          onChangeItem={(item) => toggleCrypto1(item.value)}
          containerStyle={styles.dropDownContainer}
          style={styles.dropDown}
        />
        <DropDownPicker
          items={[
            { label: 'Bitcoin', value: 'btc' },
            { label: 'Ethereum', value: 'eth' },
            // Adicione mais criptomoedas aqui
          ]}
          placeholder="Selecione a segunda criptomoeda"
          onChangeItem={(item) => toggleCrypto2(item.value)}
          containerStyle={styles.dropDownContainer}
          style={styles.dropDown}
        />
        <TouchableOpacity onPress={fetchComparisonData} style={styles.compareButton}>
          <Text style={styles.compareButtonText}>Comparar</Text>
        </TouchableOpacity>
      </View>
      <View style={styles.comparisonData}>
        {comparisonData.crypto1 && comparisonData.crypto2 && (
          <>
            <View style={styles.cryptoRow}>
              <Text style={styles.cryptoName}>{comparisonData.crypto1.name}</Text>
              <Text style={styles.cryptoPrice}>{comparisonData.crypto1.price}</Text>
              <Text style={styles.cryptoMarketCap}>{comparisonData.crypto1.marketCap}</Text>
              {/* Adicione mais informações relevantes aqui */}
            </View>
            <View style={styles.cryptoRow}>
              <Text style={styles.cryptoName}>{comparisonData.crypto2.name}</Text>
              <Text style={styles.cryptoPrice}>{comparisonData.crypto2.price}</Text>
              <Text style={styles.cryptoMarketCap}>{comparisonData.crypto2.marketCap}</Text>
              {/* Adicione mais informações relevantes aqui */}
            </View>
          </>
        )}
      </View>
    </ScrollView>
  );
};

export default CryptoComparison;
