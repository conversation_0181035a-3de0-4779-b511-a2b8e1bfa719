import logging
import pandas as pd
import numpy as np
from config.settings import LEVERAGE

logger = logging.getLogger(__name__)

class MFIStrategy:
    def __init__(self, binance_handler):
        self.binance = binance_handler
        self.leverage = LEVERAGE
        self.risk_percent = 0.01  # 1% de risco para stop loss

    def analyze_symbol(self, symbol, interval="15m", period=3, threshold=99.0):
        """
        Analisa um símbolo para verificar se há sinal baseado no MFI

        Returns:
            tuple: (signal_type, entry_price, stop_loss, take_profit) ou (None, None, None, None) se não houver sinal
        """
        try:
            # Verificar se houve 3 toques no MFI
            entry_price = self._monitor_mfi_touches(symbol, interval, period, threshold)

            if entry_price is None:
                return None, None, None, None

            # Validar se o preço de entrada é realista
            if not self._validate_price_range(symbol, entry_price):
                logger.error(f"Preço de entrada irrealista para {symbol}: {entry_price}")
                return None, None, None, None

            # Calcular stop loss e take profit (mais conservador)
            stop_loss = round(entry_price * (1 - self.risk_percent), 6)
            take_profit = round(entry_price * (1 + self.risk_percent * 2), 6)  # Relação risco:recompensa de 1:2 (mais conservador)

            # Sempre LONG para sinais MFI
            signal_type = "LONG"

            # Validar se os cálculos fazem sentido
            if not self._validate_signal_values(symbol, signal_type, entry_price, stop_loss, take_profit):
                return None, None, None, None

            logger.info(f"{symbol} - Sinal MFI gerado. Entrada: {entry_price}, SL: {stop_loss}, TP: {take_profit}")

            # Garantir que todos os valores são float e válidos
            try:
                return signal_type, float(entry_price), float(stop_loss), float(take_profit)
            except (ValueError, TypeError) as e:
                logger.error(f"Erro ao converter valores para float em {symbol}: {e}")
                return None, None, None, None

        except Exception as e:
            logger.error(f"Erro ao analisar {symbol} para estratégia MFI: {e}")
            return None, None, None, None

    def _compute_mfi(self, df, period=3):
        """
        Calcula o Money Flow Index (MFI) para o ativo usando candles fechados.
        """
        try:
            if len(df) < period + 1:
                return None

            # Criar uma cópia explícita do DataFrame para evitar SettingWithCopyWarning
            df = df.copy()

            # Calcular Typical Price e Raw Money Flow
            df.loc[:, 'typical_price'] = (df['high'] + df['low'] + df['close']) / 3
            df.loc[:, 'money_flow'] = df['typical_price'] * df['volume']

            # Inicializar listas para fluxos positivos e negativos
            pos_flow = 0.0
            neg_flow = 0.0

            # Calcular fluxos positivos e negativos
            for i in range(1, period + 1):
                if df['typical_price'].iloc[-i] > df['typical_price'].iloc[-(i+1)]:
                    pos_flow += df['money_flow'].iloc[-i]
                elif df['typical_price'].iloc[-i] < df['typical_price'].iloc[-(i+1)]:
                    neg_flow += df['money_flow'].iloc[-i]

            # Calcular MFI
            if neg_flow == 0:
                mfi = 100
            else:
                money_flow_ratio = pos_flow / neg_flow
                mfi = 100 - (100 / (1 + money_flow_ratio))

            return mfi

        except Exception as e:
            logger.error(f"Erro ao calcular MFI: {e}")
            return None

    def _monitor_mfi_touches(self, symbol, interval="15m", period=3, threshold=99.0):
        """
        Monitora os candles fechados para identificar toques no limiar do MFI.
        Retorna o preço de entrada se detectar 3 toques consecutivos no MFI acima do threshold.
        """
        try:
            # Obter dados históricos
            df = self.binance.get_historical_klines(
                symbol=symbol,
                interval=interval,
                lookback_days=1
            )

            if df.empty or len(df) < period + 1:
                return None

            # Calcular MFI atual
            mfi = self._compute_mfi(df, period)

            if mfi is None:
                return None

            # Verificar se o MFI está acima do threshold
            if mfi >= threshold:
                # Verificar se os últimos 3 candles também tiveram MFI alto
                touch_count = 1

                # Verificar os candles anteriores
                for i in range(1, 3):
                    # Criar um DataFrame com os dados até o candle i atrás
                    prev_df = df.iloc[:-i]

                    if len(prev_df) < period + 1:
                        break

                    prev_mfi = self._compute_mfi(prev_df, period)

                    if prev_mfi is None or prev_mfi < threshold:
                        break

                    touch_count += 1

                # Se tivermos 3 toques consecutivos, gerar sinal
                if touch_count >= 3:
                    current_price = self.binance.get_current_price(symbol)
                    logger.info(f"{symbol} - 3 toques consecutivos no MFI detectados! MFI atual: {mfi:.2f}")
                    return current_price

            return None

        except Exception as e:
            logger.error(f"Erro ao monitorar toques no MFI para {symbol}: {e}")
            return None

    def _validate_price_range(self, symbol, price):
        """Valida se um preço está dentro de uma faixa realista para o símbolo"""
        if price <= 0:
            return False

        # Faixas de preços realistas (com margem de segurança)
        price_ranges = {
            'BTC': (10000, 150000),
            'ETH': (500, 10000),
            'BNB': (50, 1000),
            'ADA': (0.1, 5),
            'SOL': (10, 1000),
            'DOT': (1, 50),
            'LINK': (1, 100),
            'MATIC': (0.1, 10),
            'AVAX': (5, 200),
            'ATOM': (1, 100)
        }

        for coin, (min_price, max_price) in price_ranges.items():
            if coin in symbol:
                return min_price <= price <= max_price

        # Para moedas não mapeadas, aceitar uma faixa ampla
        return 0.001 <= price <= 100000

    def _validate_signal_values(self, symbol, signal_type, entry_price, stop_loss, take_profit):
        """Valida se os valores do sinal fazem sentido"""
        try:
            # Validar se todos os valores são positivos
            if entry_price <= 0 or stop_loss <= 0 or take_profit <= 0:
                logger.error(f"Valores inválidos para {symbol}: entry={entry_price}, sl={stop_loss}, tp={take_profit}")
                return False

            # Validar lógica do stop loss (sempre LONG para MFI)
            if stop_loss >= entry_price:
                logger.error(f"Stop loss inválido para {symbol}: {stop_loss} >= {entry_price}")
                return False

            # Validar lógica do take profit (sempre LONG para MFI)
            if take_profit <= entry_price:
                logger.error(f"Take profit inválido para {symbol}: {take_profit} <= {entry_price}")
                return False

            # Validar se o take profit não é muito agressivo (máximo 5% para MFI)
            max_tp = entry_price * 1.05
            if take_profit > max_tp:
                logger.warning(f"Take profit muito agressivo para {symbol}, ajustando de {take_profit} para {max_tp}")
                return False

            return True

        except Exception as e:
            logger.error(f"Erro ao validar valores do sinal para {symbol}: {e}")
            return False