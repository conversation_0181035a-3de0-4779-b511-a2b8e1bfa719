{"ast": null, "code": "'use client';\n\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nvar clipboardAvailable;\nvar Clipboard = function () {\n  function Clipboard() {\n    _classCallCheck(this, Clipboard);\n  }\n  return _createClass(Clipboard, null, [{\n    key: \"isAvailable\",\n    value: function isAvailable() {\n      if (clipboardAvailable === undefined) {\n        clipboardAvailable = typeof document.queryCommandSupported === 'function' && document.queryCommandSupported('copy');\n      }\n      return clipboardAvailable;\n    }\n  }, {\n    key: \"getString\",\n    value: function getString() {\n      return Promise.resolve('');\n    }\n  }, {\n    key: \"setString\",\n    value: function setString(text) {\n      var success = false;\n      var body = document.body;\n      if (body) {\n        var node = document.createElement('span');\n        node.textContent = text;\n        node.style.opacity = '0';\n        node.style.position = 'absolute';\n        node.style.whiteSpace = 'pre-wrap';\n        node.style.userSelect = 'auto';\n        body.appendChild(node);\n        var selection = window.getSelection();\n        selection.removeAllRanges();\n        var range = document.createRange();\n        range.selectNodeContents(node);\n        selection.addRange(range);\n        try {\n          document.execCommand('copy');\n          success = true;\n        } catch (e) {}\n        selection.removeAllRanges();\n        body.removeChild(node);\n      }\n      return success;\n    }\n  }]);\n}();\nexport { Clipboard as default };", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "clipboardAvailable", "Clipboard", "key", "value", "isAvailable", "undefined", "document", "queryCommandSupported", "getString", "Promise", "resolve", "setString", "text", "success", "body", "node", "createElement", "textContent", "style", "opacity", "position", "whiteSpace", "userSelect", "append<PERSON><PERSON><PERSON>", "selection", "window", "getSelection", "removeAllRanges", "range", "createRange", "selectNodeContents", "addRange", "execCommand", "e", "<PERSON><PERSON><PERSON><PERSON>", "default"], "sources": ["C:/Users/<USER>/Desktop/Signals/CryptoSignalsApp/node_modules/react-native-web/dist/exports/Clipboard/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n'use client';\n\nvar clipboardAvailable;\nexport default class Clipboard {\n  static isAvailable() {\n    if (clipboardAvailable === undefined) {\n      clipboardAvailable = typeof document.queryCommandSupported === 'function' && document.queryCommandSupported('copy');\n    }\n    return clipboardAvailable;\n  }\n  static getString() {\n    return Promise.resolve('');\n  }\n  static setString(text) {\n    var success = false;\n    var body = document.body;\n    if (body) {\n      // add the text to a hidden node\n      var node = document.createElement('span');\n      node.textContent = text;\n      node.style.opacity = '0';\n      node.style.position = 'absolute';\n      node.style.whiteSpace = 'pre-wrap';\n      node.style.userSelect = 'auto';\n      body.appendChild(node);\n\n      // select the text\n      var selection = window.getSelection();\n      selection.removeAllRanges();\n      var range = document.createRange();\n      range.selectNodeContents(node);\n      selection.addRange(range);\n\n      // attempt to copy\n      try {\n        document.execCommand('copy');\n        success = true;\n      } catch (e) {}\n\n      // remove selection and node\n      selection.removeAllRanges();\n      body.removeChild(node);\n    }\n    return success;\n  }\n}"], "mappings": "AAUA,YAAY;;AAAC,OAAAA,eAAA;AAAA,OAAAC,YAAA;AAEb,IAAIC,kBAAkB;AAAC,IACFC,SAAS;EAAA,SAAAA,UAAA;IAAAH,eAAA,OAAAG,SAAA;EAAA;EAAA,OAAAF,YAAA,CAAAE,SAAA;IAAAC,GAAA;IAAAC,KAAA,EAC5B,SAAOC,WAAWA,CAAA,EAAG;MACnB,IAAIJ,kBAAkB,KAAKK,SAAS,EAAE;QACpCL,kBAAkB,GAAG,OAAOM,QAAQ,CAACC,qBAAqB,KAAK,UAAU,IAAID,QAAQ,CAACC,qBAAqB,CAAC,MAAM,CAAC;MACrH;MACA,OAAOP,kBAAkB;IAC3B;EAAC;IAAAE,GAAA;IAAAC,KAAA,EACD,SAAOK,SAASA,CAAA,EAAG;MACjB,OAAOC,OAAO,CAACC,OAAO,CAAC,EAAE,CAAC;IAC5B;EAAC;IAAAR,GAAA;IAAAC,KAAA,EACD,SAAOQ,SAASA,CAACC,IAAI,EAAE;MACrB,IAAIC,OAAO,GAAG,KAAK;MACnB,IAAIC,IAAI,GAAGR,QAAQ,CAACQ,IAAI;MACxB,IAAIA,IAAI,EAAE;QAER,IAAIC,IAAI,GAAGT,QAAQ,CAACU,aAAa,CAAC,MAAM,CAAC;QACzCD,IAAI,CAACE,WAAW,GAAGL,IAAI;QACvBG,IAAI,CAACG,KAAK,CAACC,OAAO,GAAG,GAAG;QACxBJ,IAAI,CAACG,KAAK,CAACE,QAAQ,GAAG,UAAU;QAChCL,IAAI,CAACG,KAAK,CAACG,UAAU,GAAG,UAAU;QAClCN,IAAI,CAACG,KAAK,CAACI,UAAU,GAAG,MAAM;QAC9BR,IAAI,CAACS,WAAW,CAACR,IAAI,CAAC;QAGtB,IAAIS,SAAS,GAAGC,MAAM,CAACC,YAAY,CAAC,CAAC;QACrCF,SAAS,CAACG,eAAe,CAAC,CAAC;QAC3B,IAAIC,KAAK,GAAGtB,QAAQ,CAACuB,WAAW,CAAC,CAAC;QAClCD,KAAK,CAACE,kBAAkB,CAACf,IAAI,CAAC;QAC9BS,SAAS,CAACO,QAAQ,CAACH,KAAK,CAAC;QAGzB,IAAI;UACFtB,QAAQ,CAAC0B,WAAW,CAAC,MAAM,CAAC;UAC5BnB,OAAO,GAAG,IAAI;QAChB,CAAC,CAAC,OAAOoB,CAAC,EAAE,CAAC;QAGbT,SAAS,CAACG,eAAe,CAAC,CAAC;QAC3Bb,IAAI,CAACoB,WAAW,CAACnB,IAAI,CAAC;MACxB;MACA,OAAOF,OAAO;IAChB;EAAC;AAAA;AAAA,SAzCkBZ,SAAS,IAAAkC,OAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}