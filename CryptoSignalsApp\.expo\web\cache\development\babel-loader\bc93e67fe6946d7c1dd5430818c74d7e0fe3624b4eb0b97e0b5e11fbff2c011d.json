{"ast": null, "code": "import StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport Dimensions from \"react-native-web/dist/exports/Dimensions\";\nvar windowHeight = Dimensions.get('window').height;\nvar MARGIN_BOTTOM = 16;\nvar FONT_SIZE_TITLE = 18;\nvar FONT_SIZE_TYPE = 16;\nvar DOT_SIZE = 6;\nvar ICON_SIZE = 24;\nvar MARGIN_TOP_EMPTY_STATE = 18;\nvar MARGIN_TOP_ROW_SPACE_BETWEEN = 32;\nvar styles = StyleSheet.create({\n  scrollView: {\n    height: windowHeight - 280\n  },\n  emptyState: {\n    color: '#fff',\n    fontSize: FONT_SIZE_TITLE,\n    textAlign: 'center',\n    fontFamily: 'Poppins_400Regular',\n    marginTop: MARGIN_TOP_EMPTY_STATE\n  },\n  container: {\n    flex: 1,\n    backgroundColor: '#fff',\n    padding: 16\n  },\n  row: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    marginBottom: MARGIN_BOTTOM\n  },\n  title: {\n    fontSize: FONT_SIZE_TITLE,\n    fontWeight: 'bold'\n  },\n  type: {\n    fontSize: FONT_SIZE_TYPE,\n    marginLeft: 8\n  },\n  dot: {\n    width: DOT_SIZE,\n    height: DOT_SIZE,\n    borderRadius: DOT_SIZE / 2,\n    backgroundColor: 'black',\n    marginLeft: 8,\n    marginRight: 8\n  },\n  rowSpaceBetween: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    justifyContent: 'space-between',\n    marginTop: MARGIN_TOP_ROW_SPACE_BETWEEN\n  },\n  icon: {\n    fontSize: ICON_SIZE\n  }\n});\nexport default styles;", "map": {"version": 3, "names": ["windowHeight", "Dimensions", "get", "height", "MARGIN_BOTTOM", "FONT_SIZE_TITLE", "FONT_SIZE_TYPE", "DOT_SIZE", "ICON_SIZE", "MARGIN_TOP_EMPTY_STATE", "MARGIN_TOP_ROW_SPACE_BETWEEN", "styles", "StyleSheet", "create", "scrollView", "emptyState", "color", "fontSize", "textAlign", "fontFamily", "marginTop", "container", "flex", "backgroundColor", "padding", "row", "flexDirection", "alignItems", "marginBottom", "title", "fontWeight", "type", "marginLeft", "dot", "width", "borderRadius", "marginRight", "rowSpaceBetween", "justifyContent", "icon"], "sources": ["E:/CryptoSignalsApp/src/pages/Home/styles.js"], "sourcesContent": ["import { StyleSheet, Dimensions } from 'react-native';\r\n\r\nconst windowHeight = Dimensions.get('window').height;\r\nconst MARGIN_BOTTOM = 16;\r\nconst FONT_SIZE_TITLE = 18;\r\nconst FONT_SIZE_TYPE = 16;\r\nconst DOT_SIZE = 6;\r\nconst ICON_SIZE = 24;\r\nconst MARGIN_TOP_EMPTY_STATE = 18;\r\nconst MARGIN_TOP_ROW_SPACE_BETWEEN = 32;\r\n\r\nconst styles = StyleSheet.create({\r\n    // Estilos Gerais\r\n    scrollView: {\r\n        height: windowHeight - 280, // Considere criar uma constante para '280' se tiver um significado específico\r\n    },\r\n    emptyState: {\r\n        color: '#fff',\r\n        fontSize: FONT_SIZE_TITLE,\r\n        textAlign: 'center',\r\n        fontFamily: 'Poppins_400Regular',\r\n        marginTop: MARGIN_TOP_EMPTY_STATE,\r\n    },\r\n\r\n    // Estilos de Home\r\n    container: {\r\n        flex: 1,\r\n        backgroundColor: '#fff',\r\n        padding: 16,\r\n    },\r\n    row: {\r\n        flexDirection: 'row',\r\n        alignItems: 'center',\r\n        marginBottom: MARGIN_BOTTOM,\r\n    },\r\n    title: {\r\n        fontSize: FONT_SIZE_TITLE,\r\n        fontWeight: 'bold',\r\n    },\r\n    type: {\r\n        fontSize: FONT_SIZE_TYPE,\r\n        marginLeft: 8,\r\n    },\r\n    dot: {\r\n        width: DOT_SIZE,\r\n        height: DOT_SIZE,\r\n        borderRadius: DOT_SIZE / 2,\r\n        backgroundColor: 'black',\r\n        marginLeft: 8,\r\n        marginRight: 8,\r\n    },\r\n    rowSpaceBetween: {\r\n        flexDirection: 'row',\r\n        alignItems: 'center',\r\n        justifyContent: 'space-between',\r\n        marginTop: MARGIN_TOP_ROW_SPACE_BETWEEN,\r\n    },\r\n    icon: {\r\n        fontSize: ICON_SIZE,\r\n        // TODO: Adicione outras propriedades se necessário, como color, etc.\r\n    },\r\n});\r\n\r\nexport default styles;"], "mappings": ";;AAEA,IAAMA,YAAY,GAAGC,UAAU,CAACC,GAAG,CAAC,QAAQ,CAAC,CAACC,MAAM;AACpD,IAAMC,aAAa,GAAG,EAAE;AACxB,IAAMC,eAAe,GAAG,EAAE;AAC1B,IAAMC,cAAc,GAAG,EAAE;AACzB,IAAMC,QAAQ,GAAG,CAAC;AAClB,IAAMC,SAAS,GAAG,EAAE;AACpB,IAAMC,sBAAsB,GAAG,EAAE;AACjC,IAAMC,4BAA4B,GAAG,EAAE;AAEvC,IAAMC,MAAM,GAAGC,UAAU,CAACC,MAAM,CAAC;EAE7BC,UAAU,EAAE;IACRX,MAAM,EAAEH,YAAY,GAAG;EAC3B,CAAC;EACDe,UAAU,EAAE;IACRC,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAEZ,eAAe;IACzBa,SAAS,EAAE,QAAQ;IACnBC,UAAU,EAAE,oBAAoB;IAChCC,SAAS,EAAEX;EACf,CAAC;EAGDY,SAAS,EAAE;IACPC,IAAI,EAAE,CAAC;IACPC,eAAe,EAAE,MAAM;IACvBC,OAAO,EAAE;EACb,CAAC;EACDC,GAAG,EAAE;IACDC,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,QAAQ;IACpBC,YAAY,EAAExB;EAClB,CAAC;EACDyB,KAAK,EAAE;IACHZ,QAAQ,EAAEZ,eAAe;IACzByB,UAAU,EAAE;EAChB,CAAC;EACDC,IAAI,EAAE;IACFd,QAAQ,EAAEX,cAAc;IACxB0B,UAAU,EAAE;EAChB,CAAC;EACDC,GAAG,EAAE;IACDC,KAAK,EAAE3B,QAAQ;IACfJ,MAAM,EAAEI,QAAQ;IAChB4B,YAAY,EAAE5B,QAAQ,GAAG,CAAC;IAC1BgB,eAAe,EAAE,OAAO;IACxBS,UAAU,EAAE,CAAC;IACbI,WAAW,EAAE;EACjB,CAAC;EACDC,eAAe,EAAE;IACbX,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,QAAQ;IACpBW,cAAc,EAAE,eAAe;IAC/BlB,SAAS,EAAEV;EACf,CAAC;EACD6B,IAAI,EAAE;IACFtB,QAAQ,EAAET;EAEd;AACJ,CAAC,CAAC;AAEF,eAAeG,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}