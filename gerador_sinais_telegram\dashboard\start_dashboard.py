#!/usr/bin/env python3
"""
Script para inicializar o dashboard do CryptoSignals
"""

import subprocess
import sys
import os
import time
import threading
from pathlib import Path

def run_backend():
    """Executa o backend Flask"""
    print("🚀 Iniciando backend Flask...")
    backend_dir = Path(__file__).parent / "backend"
    os.chdir(backend_dir)
    
    # Instalar dependências se necessário
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                      check=True, capture_output=True)
        print("✅ Dependências do backend instaladas")
    except subprocess.CalledProcessError as e:
        print(f"❌ Erro ao instalar dependências: {e}")
        return
    
    # Executar Flask
    try:
        subprocess.run([sys.executable, "app.py"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Erro ao executar backend: {e}")
    except KeyboardInterrupt:
        print("\n🛑 Backend interrompido pelo usuário")

def run_frontend():
    """Executa o frontend React"""
    print("🚀 Iniciando frontend React...")
    frontend_dir = Path(__file__).parent / "frontend"
    
    if not frontend_dir.exists():
        print("❌ Diretório frontend não encontrado")
        return
    
    os.chdir(frontend_dir)
    
    # Verificar se node_modules existe
    if not (frontend_dir / "node_modules").exists():
        print("📦 Instalando dependências do frontend...")
        try:
            subprocess.run(["npm", "install"], check=True)
            print("✅ Dependências do frontend instaladas")
        except subprocess.CalledProcessError as e:
            print(f"❌ Erro ao instalar dependências: {e}")
            print("💡 Certifique-se de que o Node.js está instalado")
            return
        except FileNotFoundError:
            print("❌ npm não encontrado. Instale o Node.js primeiro")
            return
    
    # Executar React
    try:
        subprocess.run(["npm", "start"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Erro ao executar frontend: {e}")
    except KeyboardInterrupt:
        print("\n🛑 Frontend interrompido pelo usuário")
    except FileNotFoundError:
        print("❌ npm não encontrado. Instale o Node.js primeiro")

def main():
    """Função principal"""
    print("🎯 CryptoSignals Dashboard")
    print("=" * 50)
    
    # Verificar se estamos no diretório correto
    dashboard_dir = Path(__file__).parent
    if not (dashboard_dir / "backend" / "app.py").exists():
        print("❌ Estrutura do dashboard não encontrada")
        print("💡 Execute este script do diretório dashboard/")
        return
    
    print("📋 Opções disponíveis:")
    print("1. Executar apenas backend (Flask)")
    print("2. Executar apenas frontend (React)")
    print("3. Executar ambos (recomendado)")
    print("4. Sair")
    
    choice = input("\n🔢 Escolha uma opção (1-4): ").strip()
    
    if choice == "1":
        run_backend()
    elif choice == "2":
        run_frontend()
    elif choice == "3":
        print("\n🚀 Iniciando dashboard completo...")
        print("📝 Backend: http://localhost:5000")
        print("📝 Frontend: http://localhost:3000")
        print("📝 Pressione Ctrl+C para parar\n")
        
        # Executar backend em thread separada
        backend_thread = threading.Thread(target=run_backend, daemon=True)
        backend_thread.start()
        
        # Aguardar um pouco para o backend inicializar
        time.sleep(3)
        
        # Executar frontend (bloqueia até ser interrompido)
        try:
            run_frontend()
        except KeyboardInterrupt:
            print("\n🛑 Dashboard interrompido pelo usuário")
    elif choice == "4":
        print("👋 Saindo...")
    else:
        print("❌ Opção inválida")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 Programa interrompido pelo usuário")
    except Exception as e:
        print(f"❌ Erro inesperado: {e}")
        import traceback
        traceback.print_exc()
