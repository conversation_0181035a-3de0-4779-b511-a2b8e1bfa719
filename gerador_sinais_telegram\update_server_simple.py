#!/usr/bin/env python3
"""
Script SIMPLES de atualização do servidor CryptoSignals com autenticação
Funciona no Windows sem dependências externas
"""

import subprocess
import sys
import time
import os

# Configurações
SERVER_IP = "**************"
SERVER_USER = "root"
SERVER_PASSWORD = "h4*ls:FtJw0e"
PROJECT_PATH = "/opt/gerador_sinais_telegram"
PHONE_NUMBER = "+5521982301476"

def print_colored(text, color=""):
    """Imprime texto com cor (simplificado para Windows)"""
    print(f"{text}")

def run_ssh_command(command):
    """Executa comando SSH usando subprocess"""
    # Usar ssh direto (funciona se SSH client estiver instalado)
    full_command = f'ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null {SERVER_USER}@{SERVER_IP} "{command}"'

    print_colored(f"🔄 Executando: {command}")

    try:
        result = subprocess.run(full_command, shell=True, capture_output=True, text=True, timeout=30)

        if result.stdout:
            print(result.stdout)
        if result.stderr and result.returncode != 0:
            print(f"⚠️ Erro: {result.stderr}")

        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        print("⚠️ Comando expirou")
        return False, "", "Timeout"
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False, "", str(e)

def manual_ssh_instructions():
    """Mostra instruções para SSH manual"""
    print()
    print("=" * 60)
    print("📋 INSTRUÇÕES PARA ATUALIZAÇÃO MANUAL")
    print("=" * 60)
    print()
    print("1️⃣ CONECTAR AO SERVIDOR:")
    print(f"   ssh {SERVER_USER}@{SERVER_IP}")
    print(f"   Senha: {SERVER_PASSWORD}")
    print()
    print("2️⃣ EXECUTAR COMANDOS DE ATUALIZAÇÃO:")
    print("   systemctl stop gerador_sinais.service")
    print(f"   cd {PROJECT_PATH}")
    print("   git config pull.rebase false")
    print("   git stash")
    print("   git pull origin main")
    print("   # Se git pull falhar:")
    print("   git fetch origin main")
    print("   git reset --hard origin/main")
    print()
    print("3️⃣ ATUALIZAR DEPENDÊNCIAS:")
    print("   source venv/bin/activate")
    print("   pip install --upgrade pip")
    print("   pip install pandas-ta finta --upgrade")
    print()
    print("4️⃣ REINICIAR SERVIÇO:")
    print("   systemctl daemon-reload")
    print("   systemctl restart gerador_sinais.service")
    print()
    print("5️⃣ SE PEDIR AUTENTICAÇÃO DO TELEGRAM:")
    print("   systemctl stop gerador_sinais.service")
    print(f"   cd {PROJECT_PATH}")
    print("   source venv/bin/activate")
    print("   python main.py")
    print(f"   # Digite: {PHONE_NUMBER}")
    print("   # Digite o código do Telegram")
    print("   # Pressione Ctrl+C após autenticação")
    print("   systemctl restart gerador_sinais.service")
    print()
    print("6️⃣ VERIFICAR STATUS:")
    print("   systemctl status gerador_sinais.service")
    print("   journalctl -u gerador_sinais.service -n 20")
    print()
    print("=" * 60)

def try_automatic_update():
    """Tenta atualização automática"""
    print("🚀 TENTANDO ATUALIZAÇÃO AUTOMÁTICA")
    print("=" * 50)

    commands = [
        "systemctl stop gerador_sinais.service",
        f"cd {PROJECT_PATH}",
        f"cd {PROJECT_PATH} && git config pull.rebase false",
        f"cd {PROJECT_PATH} && git stash",
        f"cd {PROJECT_PATH} && git pull origin main",
        f"cd {PROJECT_PATH} && source venv/bin/activate && pip install --upgrade pip",
        f"cd {PROJECT_PATH} && source venv/bin/activate && pip install pandas-ta finta --upgrade",
        "systemctl daemon-reload",
        "systemctl restart gerador_sinais.service",
        "systemctl status gerador_sinais.service --no-pager -l"
    ]

    success_count = 0

    for i, cmd in enumerate(commands, 1):
        print(f"\n[{i}/{len(commands)}] Executando comando...")
        success, output, error = run_ssh_command(cmd)

        if success:
            success_count += 1
            print("✅ Sucesso")
        else:
            print("⚠️ Falhou, mas continuando...")

            # Se git pull falhar, tentar reset
            if "git pull" in cmd:
                print("🔄 Tentando git reset...")
                reset_cmd = f"cd {PROJECT_PATH} && git fetch origin main && git reset --hard origin/main"
                success2, _, _ = run_ssh_command(reset_cmd)
                if success2:
                    print("✅ Git reset bem-sucedido")
                    success_count += 1

    print(f"\n📊 Resultado: {success_count}/{len(commands)} comandos executados com sucesso")

    if success_count >= len(commands) * 0.7:  # 70% de sucesso
        print("✅ Atualização provavelmente bem-sucedida!")

        # Verificar se precisa de autenticação
        print("\n🔍 Verificando se precisa de autenticação...")
        success, output, error = run_ssh_command("journalctl -u gerador_sinais.service --no-pager -l -n 10")

        if success and output:
            if any(keyword in output.lower() for keyword in ["phone number", "código", "authentication", "login"]):
                print("📱 AUTENTICAÇÃO DO TELEGRAM NECESSÁRIA!")
                print(f"📱 Seu número: {PHONE_NUMBER}")
                print()
                print("🔧 INSTRUÇÕES PARA AUTENTICAÇÃO:")
                print(f"1. ssh {SERVER_USER}@{SERVER_IP}")
                print("2. systemctl stop gerador_sinais.service")
                print(f"3. cd {PROJECT_PATH}")
                print("4. source venv/bin/activate")
                print("5. python main.py")
                print(f"6. Digite: {PHONE_NUMBER}")
                print("7. Digite o código do Telegram")
                print("8. Pressione Ctrl+C após autenticação")
                print("9. systemctl restart gerador_sinais.service")
                return True
            else:
                print("✅ Nenhuma autenticação necessária")
                return True
    else:
        print("⚠️ Muitos comandos falharam. Recomendo atualização manual.")
        return False

def main():
    """Função principal"""
    print("🚀 ATUALIZAÇÃO DO CRYPTOSIGNALS")
    print("=" * 50)
    print()
    print("📋 Este script vai tentar atualizar o servidor automaticamente.")
    print("Se falhar, mostrará instruções para atualização manual.")
    print()
    print(f"📱 Seu número do Telegram: {PHONE_NUMBER}")
    print()

    try:
        # Tentar atualização automática
        success = try_automatic_update()

        if success:
            print()
            print("✅ ATUALIZAÇÃO CONCLUÍDA!")
            print()
            print("📋 COMANDOS ÚTEIS:")
            print(f"ssh {SERVER_USER}@{SERVER_IP}")
            print("journalctl -u gerador_sinais.service -f  # Ver logs")
            print("systemctl status gerador_sinais.service  # Ver status")
        else:
            print()
            print("⚠️ ATUALIZAÇÃO AUTOMÁTICA FALHOU")
            manual_ssh_instructions()

    except KeyboardInterrupt:
        print("\n⚠️ Interrompido pelo usuário")
    except Exception as e:
        print(f"\n❌ Erro inesperado: {e}")
        manual_ssh_instructions()

if __name__ == "__main__":
    main()
