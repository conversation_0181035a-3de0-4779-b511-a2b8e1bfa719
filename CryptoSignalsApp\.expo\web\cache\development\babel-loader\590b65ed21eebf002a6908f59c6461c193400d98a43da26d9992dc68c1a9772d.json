{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport React, { useState, useEffect } from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport ScrollView from \"react-native-web/dist/exports/ScrollView\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport Alert from \"react-native-web/dist/exports/Alert\";\nimport Wrapper from \"../../components/Wrapper\";\nimport Card from \"../../components/Card\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport default function Channels(_ref) {\n  var _ref$route = _ref.route,\n    route = _ref$route === void 0 ? {} : _ref$route;\n  var _useState = useState(true),\n    _useState2 = _slicedToArray(_useState, 2),\n    isLoading = _useState2[0],\n    setIsLoading = _useState2[1];\n  var _useState3 = useState([]),\n    _useState4 = _slicedToArray(_useState3, 2),\n    channels = _useState4[0],\n    setChannels = _useState4[1];\n  var _ref2 = route.params || {\n      accountHasBeenRecovered: false\n    },\n    accountHasBeenRecovered = _ref2.accountHasBeenRecovered;\n  useEffect(function () {\n    var mockChannels = [{\n      id: 1,\n      name: 'Bitcoin Pro Signals',\n      description: 'Premium Bitcoin trading signals',\n      isPremium: true,\n      subscribers: 12500,\n      avatar: '₿'\n    }, {\n      id: 2,\n      name: 'Ethereum Futures',\n      description: 'Ethereum trading strategies',\n      isPremium: true,\n      subscribers: 8900,\n      avatar: '⚡'\n    }, {\n      id: 3,\n      name: 'Altcoin Gems',\n      description: 'Hidden altcoin opportunities',\n      isPremium: false,\n      subscribers: 15600,\n      avatar: '💎'\n    }];\n    setTimeout(function () {\n      setChannels(mockChannels);\n      setIsLoading(false);\n    }, 500);\n  }, []);\n  var handleChannelPress = function handleChannelPress(channel) {\n    Alert.alert(channel.name, `View signals for ${channel.name}. Feature coming soon!`);\n  };\n  useEffect(function () {\n    if (accountHasBeenRecovered) {\n      Alert.alert(\"Success\", \"Account recovered successfully\");\n    }\n  }, [accountHasBeenRecovered]);\n  if (isLoading) {\n    return _jsx(Wrapper, {\n      children: _jsxs(View, {\n        style: {\n          padding: 16\n        },\n        children: [_jsx(Text, {\n          style: {\n            color: '#fff',\n            fontSize: 24,\n            fontWeight: 'bold',\n            marginBottom: 20\n          },\n          children: \"Signal Channels \\uD83D\\uDCFA\"\n        }), _jsx(Text, {\n          style: {\n            color: '#8a8a8a',\n            fontSize: 16,\n            textAlign: 'center',\n            marginTop: 50\n          },\n          children: \"Loading channels...\"\n        })]\n      })\n    });\n  }\n  return _jsx(Wrapper, {\n    children: _jsxs(ScrollView, {\n      style: {\n        flex: 1,\n        padding: 16\n      },\n      children: [_jsx(Text, {\n        style: {\n          color: '#fff',\n          fontSize: 28,\n          fontWeight: 'bold',\n          marginBottom: 4\n        },\n        children: \"Signal Channels \\uD83D\\uDCFA\"\n      }), _jsx(Text, {\n        style: {\n          color: '#8a8a8a',\n          fontSize: 14,\n          marginBottom: 20\n        },\n        children: \"Follow the best crypto trading signal providers\"\n      }), channels.map(function (channel) {\n        return _jsx(Card, {\n          style: {\n            marginBottom: 12\n          },\n          onPress: function onPress() {\n            return handleChannelPress(channel);\n          },\n          children: _jsxs(View, {\n            style: {\n              flexDirection: 'row',\n              alignItems: 'center'\n            },\n            children: [_jsx(View, {\n              style: {\n                backgroundColor: '#333',\n                borderRadius: 12,\n                padding: 12,\n                marginRight: 12,\n                alignItems: 'center',\n                justifyContent: 'center'\n              },\n              children: _jsx(Text, {\n                style: {\n                  fontSize: 24\n                },\n                children: channel.avatar\n              })\n            }), _jsxs(View, {\n              style: {\n                flex: 1\n              },\n              children: [_jsx(Text, {\n                style: {\n                  color: '#fff',\n                  fontSize: 16,\n                  fontWeight: '600',\n                  marginBottom: 4\n                },\n                children: channel.name\n              }), _jsx(Text, {\n                style: {\n                  color: '#ccc',\n                  fontSize: 13,\n                  marginBottom: 8\n                },\n                children: channel.description\n              }), _jsxs(View, {\n                style: {\n                  flexDirection: 'row',\n                  alignItems: 'center'\n                },\n                children: [_jsx(View, {\n                  style: {\n                    backgroundColor: channel.isPremium ? '#FECB37' : '#4CAF50',\n                    paddingHorizontal: 6,\n                    paddingVertical: 2,\n                    borderRadius: 4,\n                    marginRight: 8\n                  },\n                  children: _jsx(Text, {\n                    style: {\n                      color: channel.isPremium ? '#000' : '#fff',\n                      fontSize: 10,\n                      fontWeight: '600'\n                    },\n                    children: channel.isPremium ? 'PREMIUM' : 'FREE'\n                  })\n                }), _jsxs(Text, {\n                  style: {\n                    color: '#8a8a8a',\n                    fontSize: 12\n                  },\n                  children: [channel.subscribers.toLocaleString(), \" subscribers\"]\n                })]\n              })]\n            })]\n          })\n        }, channel.id);\n      })]\n    })\n  });\n}", "map": {"version": 3, "names": ["React", "useState", "useEffect", "View", "ScrollView", "Text", "<PERSON><PERSON>", "Wrapper", "Card", "jsx", "_jsx", "jsxs", "_jsxs", "Channels", "_ref", "_ref$route", "route", "_useState", "_useState2", "_slicedToArray", "isLoading", "setIsLoading", "_useState3", "_useState4", "channels", "setChannels", "_ref2", "params", "accountHasBeenRecovered", "mockChannels", "id", "name", "description", "isPremium", "subscribers", "avatar", "setTimeout", "handleChannelPress", "channel", "alert", "children", "style", "padding", "color", "fontSize", "fontWeight", "marginBottom", "textAlign", "marginTop", "flex", "map", "onPress", "flexDirection", "alignItems", "backgroundColor", "borderRadius", "marginRight", "justifyContent", "paddingHorizontal", "paddingVertical", "toLocaleString"], "sources": ["E:/CryptoSignalsApp/src/pages/Channels/index.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { View, ScrollView, Text, Alert } from 'react-native';\r\nimport Wrapper from '../../components/Wrapper';\r\nimport Card from '../../components/Card';\r\n\r\nexport default function Channels({route = {}}) {\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [channels, setChannels] = useState([]);\r\n\r\n  const { accountHasBeenRecovered } = route.params || {accountHasBeenRecovered: false};\r\n\r\n  useEffect(() => {\r\n    const mockChannels = [\r\n      {\r\n        id: 1,\r\n        name: 'Bitcoin Pro Signals',\r\n        description: 'Premium Bitcoin trading signals',\r\n        isPremium: true,\r\n        subscribers: 12500,\r\n        avatar: '₿'\r\n      },\r\n      {\r\n        id: 2,\r\n        name: 'Ethereum Futures',\r\n        description: 'Ethereum trading strategies',\r\n        isPremium: true,\r\n        subscribers: 8900,\r\n        avatar: '⚡'\r\n      },\r\n      {\r\n        id: 3,\r\n        name: 'Altcoin Gems',\r\n        description: 'Hidden altcoin opportunities',\r\n        isPremium: false,\r\n        subscribers: 15600,\r\n        avatar: '💎'\r\n      }\r\n    ];\r\n\r\n    setTimeout(() => {\r\n      setChannels(mockChannels);\r\n      setIsLoading(false);\r\n    }, 500);\r\n  }, []);\r\n\r\n  const handleChannelPress = (channel) => {\r\n    Alert.alert(\r\n      channel.name,\r\n      `View signals for ${channel.name}. Feature coming soon!`\r\n    );\r\n  };\r\n\r\n  useEffect(() => {\r\n    if(accountHasBeenRecovered) {\r\n      Alert.alert(\"Success\", \"Account recovered successfully\");\r\n    }\r\n  }, [accountHasBeenRecovered]);\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <Wrapper>\r\n        <View style={{ padding: 16 }}>\r\n          <Text style={{ color: '#fff', fontSize: 24, fontWeight: 'bold', marginBottom: 20 }}>\r\n            Signal Channels 📺\r\n          </Text>\r\n          <Text style={{ color: '#8a8a8a', fontSize: 16, textAlign: 'center', marginTop: 50 }}>\r\n            Loading channels...\r\n          </Text>\r\n        </View>\r\n      </Wrapper>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Wrapper>\r\n      <ScrollView style={{ flex: 1, padding: 16 }}>\r\n        {/* Header */}\r\n        <Text style={{\r\n          color: '#fff',\r\n          fontSize: 28,\r\n          fontWeight: 'bold',\r\n          marginBottom: 4\r\n        }}>\r\n          Signal Channels 📺\r\n        </Text>\r\n        <Text style={{\r\n          color: '#8a8a8a',\r\n          fontSize: 14,\r\n          marginBottom: 20\r\n        }}>\r\n          Follow the best crypto trading signal providers\r\n        </Text>\r\n\r\n        {/* Channels List */}\r\n        {channels.map((channel) => (\r\n          <Card key={channel.id} style={{ marginBottom: 12 }} onPress={() => handleChannelPress(channel)}>\r\n            <View style={{ flexDirection: 'row', alignItems: 'center' }}>\r\n              <View style={{\r\n                backgroundColor: '#333',\r\n                borderRadius: 12,\r\n                padding: 12,\r\n                marginRight: 12,\r\n                alignItems: 'center',\r\n                justifyContent: 'center'\r\n              }}>\r\n                <Text style={{ fontSize: 24 }}>{channel.avatar}</Text>\r\n              </View>\r\n\r\n              <View style={{ flex: 1 }}>\r\n                <Text style={{\r\n                  color: '#fff',\r\n                  fontSize: 16,\r\n                  fontWeight: '600',\r\n                  marginBottom: 4\r\n                }}>\r\n                  {channel.name}\r\n                </Text>\r\n\r\n                <Text style={{\r\n                  color: '#ccc',\r\n                  fontSize: 13,\r\n                  marginBottom: 8\r\n                }}>\r\n                  {channel.description}\r\n                </Text>\r\n\r\n                <View style={{ flexDirection: 'row', alignItems: 'center' }}>\r\n                  <View style={{\r\n                    backgroundColor: channel.isPremium ? '#FECB37' : '#4CAF50',\r\n                    paddingHorizontal: 6,\r\n                    paddingVertical: 2,\r\n                    borderRadius: 4,\r\n                    marginRight: 8\r\n                  }}>\r\n                    <Text style={{\r\n                      color: channel.isPremium ? '#000' : '#fff',\r\n                      fontSize: 10,\r\n                      fontWeight: '600'\r\n                    }}>\r\n                      {channel.isPremium ? 'PREMIUM' : 'FREE'}\r\n                    </Text>\r\n                  </View>\r\n                  <Text style={{ color: '#8a8a8a', fontSize: 12 }}>\r\n                    {channel.subscribers.toLocaleString()} subscribers\r\n                  </Text>\r\n                </View>\r\n              </View>\r\n            </View>\r\n          </Card>\r\n        ))}\r\n      </ScrollView>\r\n    </Wrapper>\r\n  );\r\n}"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,KAAA;AAEnD,OAAOC,OAAO;AACd,OAAOC,IAAI;AAA8B,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAEzC,eAAe,SAASC,QAAQA,CAAAC,IAAA,EAAe;EAAA,IAAAC,UAAA,GAAAD,IAAA,CAAbE,KAAK;IAALA,KAAK,GAAAD,UAAA,cAAG,CAAC,CAAC,GAAAA,UAAA;EAC1C,IAAAE,SAAA,GAAkChB,QAAQ,CAAC,IAAI,CAAC;IAAAiB,UAAA,GAAAC,cAAA,CAAAF,SAAA;IAAzCG,SAAS,GAAAF,UAAA;IAAEG,YAAY,GAAAH,UAAA;EAC9B,IAAAI,UAAA,GAAgCrB,QAAQ,CAAC,EAAE,CAAC;IAAAsB,UAAA,GAAAJ,cAAA,CAAAG,UAAA;IAArCE,QAAQ,GAAAD,UAAA;IAAEE,WAAW,GAAAF,UAAA;EAE5B,IAAAG,KAAA,GAAoCV,KAAK,CAACW,MAAM,IAAI;MAACC,uBAAuB,EAAE;IAAK,CAAC;IAA5EA,uBAAuB,GAAAF,KAAA,CAAvBE,uBAAuB;EAE/B1B,SAAS,CAAC,YAAM;IACd,IAAM2B,YAAY,GAAG,CACnB;MACEC,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,qBAAqB;MAC3BC,WAAW,EAAE,iCAAiC;MAC9CC,SAAS,EAAE,IAAI;MACfC,WAAW,EAAE,KAAK;MAClBC,MAAM,EAAE;IACV,CAAC,EACD;MACEL,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,kBAAkB;MACxBC,WAAW,EAAE,6BAA6B;MAC1CC,SAAS,EAAE,IAAI;MACfC,WAAW,EAAE,IAAI;MACjBC,MAAM,EAAE;IACV,CAAC,EACD;MACEL,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,cAAc;MACpBC,WAAW,EAAE,8BAA8B;MAC3CC,SAAS,EAAE,KAAK;MAChBC,WAAW,EAAE,KAAK;MAClBC,MAAM,EAAE;IACV,CAAC,CACF;IAEDC,UAAU,CAAC,YAAM;MACfX,WAAW,CAACI,YAAY,CAAC;MACzBR,YAAY,CAAC,KAAK,CAAC;IACrB,CAAC,EAAE,GAAG,CAAC;EACT,CAAC,EAAE,EAAE,CAAC;EAEN,IAAMgB,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAIC,OAAO,EAAK;IACtChC,KAAK,CAACiC,KAAK,CACTD,OAAO,CAACP,IAAI,EACZ,oBAAoBO,OAAO,CAACP,IAAI,wBAClC,CAAC;EACH,CAAC;EAED7B,SAAS,CAAC,YAAM;IACd,IAAG0B,uBAAuB,EAAE;MAC1BtB,KAAK,CAACiC,KAAK,CAAC,SAAS,EAAE,gCAAgC,CAAC;IAC1D;EACF,CAAC,EAAE,CAACX,uBAAuB,CAAC,CAAC;EAE7B,IAAIR,SAAS,EAAE;IACb,OACEV,IAAA,CAACH,OAAO;MAAAiC,QAAA,EACN5B,KAAA,CAACT,IAAI;QAACsC,KAAK,EAAE;UAAEC,OAAO,EAAE;QAAG,CAAE;QAAAF,QAAA,GAC3B9B,IAAA,CAACL,IAAI;UAACoC,KAAK,EAAE;YAAEE,KAAK,EAAE,MAAM;YAAEC,QAAQ,EAAE,EAAE;YAAEC,UAAU,EAAE,MAAM;YAAEC,YAAY,EAAE;UAAG,CAAE;UAAAN,QAAA,EAAC;QAEpF,CAAM,CAAC,EACP9B,IAAA,CAACL,IAAI;UAACoC,KAAK,EAAE;YAAEE,KAAK,EAAE,SAAS;YAAEC,QAAQ,EAAE,EAAE;YAAEG,SAAS,EAAE,QAAQ;YAAEC,SAAS,EAAE;UAAG,CAAE;UAAAR,QAAA,EAAC;QAErF,CAAM,CAAC;MAAA,CACH;IAAC,CACA,CAAC;EAEd;EAEA,OACE9B,IAAA,CAACH,OAAO;IAAAiC,QAAA,EACN5B,KAAA,CAACR,UAAU;MAACqC,KAAK,EAAE;QAAEQ,IAAI,EAAE,CAAC;QAAEP,OAAO,EAAE;MAAG,CAAE;MAAAF,QAAA,GAE1C9B,IAAA,CAACL,IAAI;QAACoC,KAAK,EAAE;UACXE,KAAK,EAAE,MAAM;UACbC,QAAQ,EAAE,EAAE;UACZC,UAAU,EAAE,MAAM;UAClBC,YAAY,EAAE;QAChB,CAAE;QAAAN,QAAA,EAAC;MAEH,CAAM,CAAC,EACP9B,IAAA,CAACL,IAAI;QAACoC,KAAK,EAAE;UACXE,KAAK,EAAE,SAAS;UAChBC,QAAQ,EAAE,EAAE;UACZE,YAAY,EAAE;QAChB,CAAE;QAAAN,QAAA,EAAC;MAEH,CAAM,CAAC,EAGNhB,QAAQ,CAAC0B,GAAG,CAAC,UAACZ,OAAO;QAAA,OACpB5B,IAAA,CAACF,IAAI;UAAkBiC,KAAK,EAAE;YAAEK,YAAY,EAAE;UAAG,CAAE;UAACK,OAAO,EAAE,SAATA,OAAOA,CAAA;YAAA,OAAQd,kBAAkB,CAACC,OAAO,CAAC;UAAA,CAAC;UAAAE,QAAA,EAC7F5B,KAAA,CAACT,IAAI;YAACsC,KAAK,EAAE;cAAEW,aAAa,EAAE,KAAK;cAAEC,UAAU,EAAE;YAAS,CAAE;YAAAb,QAAA,GAC1D9B,IAAA,CAACP,IAAI;cAACsC,KAAK,EAAE;gBACXa,eAAe,EAAE,MAAM;gBACvBC,YAAY,EAAE,EAAE;gBAChBb,OAAO,EAAE,EAAE;gBACXc,WAAW,EAAE,EAAE;gBACfH,UAAU,EAAE,QAAQ;gBACpBI,cAAc,EAAE;cAClB,CAAE;cAAAjB,QAAA,EACA9B,IAAA,CAACL,IAAI;gBAACoC,KAAK,EAAE;kBAAEG,QAAQ,EAAE;gBAAG,CAAE;gBAAAJ,QAAA,EAAEF,OAAO,CAACH;cAAM,CAAO;YAAC,CAClD,CAAC,EAEPvB,KAAA,CAACT,IAAI;cAACsC,KAAK,EAAE;gBAAEQ,IAAI,EAAE;cAAE,CAAE;cAAAT,QAAA,GACvB9B,IAAA,CAACL,IAAI;gBAACoC,KAAK,EAAE;kBACXE,KAAK,EAAE,MAAM;kBACbC,QAAQ,EAAE,EAAE;kBACZC,UAAU,EAAE,KAAK;kBACjBC,YAAY,EAAE;gBAChB,CAAE;gBAAAN,QAAA,EACCF,OAAO,CAACP;cAAI,CACT,CAAC,EAEPrB,IAAA,CAACL,IAAI;gBAACoC,KAAK,EAAE;kBACXE,KAAK,EAAE,MAAM;kBACbC,QAAQ,EAAE,EAAE;kBACZE,YAAY,EAAE;gBAChB,CAAE;gBAAAN,QAAA,EACCF,OAAO,CAACN;cAAW,CAChB,CAAC,EAEPpB,KAAA,CAACT,IAAI;gBAACsC,KAAK,EAAE;kBAAEW,aAAa,EAAE,KAAK;kBAAEC,UAAU,EAAE;gBAAS,CAAE;gBAAAb,QAAA,GAC1D9B,IAAA,CAACP,IAAI;kBAACsC,KAAK,EAAE;oBACXa,eAAe,EAAEhB,OAAO,CAACL,SAAS,GAAG,SAAS,GAAG,SAAS;oBAC1DyB,iBAAiB,EAAE,CAAC;oBACpBC,eAAe,EAAE,CAAC;oBAClBJ,YAAY,EAAE,CAAC;oBACfC,WAAW,EAAE;kBACf,CAAE;kBAAAhB,QAAA,EACA9B,IAAA,CAACL,IAAI;oBAACoC,KAAK,EAAE;sBACXE,KAAK,EAAEL,OAAO,CAACL,SAAS,GAAG,MAAM,GAAG,MAAM;sBAC1CW,QAAQ,EAAE,EAAE;sBACZC,UAAU,EAAE;oBACd,CAAE;oBAAAL,QAAA,EACCF,OAAO,CAACL,SAAS,GAAG,SAAS,GAAG;kBAAM,CACnC;gBAAC,CACH,CAAC,EACPrB,KAAA,CAACP,IAAI;kBAACoC,KAAK,EAAE;oBAAEE,KAAK,EAAE,SAAS;oBAAEC,QAAQ,EAAE;kBAAG,CAAE;kBAAAJ,QAAA,GAC7CF,OAAO,CAACJ,WAAW,CAAC0B,cAAc,CAAC,CAAC,EAAC,cACxC;gBAAA,CAAM,CAAC;cAAA,CACH,CAAC;YAAA,CACH,CAAC;UAAA,CACH;QAAC,GApDEtB,OAAO,CAACR,EAqDb,CAAC;MAAA,CACR,CAAC;IAAA,CACQ;EAAC,CACN,CAAC;AAEd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}