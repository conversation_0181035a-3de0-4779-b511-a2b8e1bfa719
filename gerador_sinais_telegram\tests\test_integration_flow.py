import os
import sys
import asyncio
import logging
import argparse
from datetime import datetime

# Adiciona o diretório raiz ao path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Importando os módulos a serem testados
from utils.binance_client import BinanceHandler
from utils.telegram_sender import TelegramSender
from trading_automatizado.telegram_trader import TelegramTrader
from trading_automatizado.auto_trader import AutoTrader
from config.settings import (
    BINANCE_API_KEY,
    BINANCE_API_SECRET,
    BINANCE_TESTNET,
    TELEGRAM_API_ID,
    TELEGRAM_API_HASH,
    TELEGRAM_GROUP_ID
)

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('tests/test_integration_flow.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

async def test_integration_flow(symbol="BTCUSDT", simulate=True):
    """
    Testa o fluxo completo de integração:
    1. Recebe um sinal do Telegram (simulado)
    2. Processa o sinal
    3. Executa a ordem na Binance (simulada ou real)
    4. Monitora a posição
    5. Fecha a posição (simulada ou real)
    """
    logger.info("Iniciando teste de fluxo de integração...")
    
    try:
        # Cria uma instância do BinanceHandler
        binance_handler = BinanceHandler(use_mock_data=simulate)
        
        # Verifica se as credenciais estão configuradas
        if not simulate:
            if not BINANCE_API_KEY or BINANCE_API_KEY == "your_testnet_api_key":
                logger.error("API Key da Binance não configurada corretamente no arquivo .env")
                return False
            
            if not BINANCE_API_SECRET or BINANCE_API_SECRET == "your_testnet_api_secret":
                logger.error("API Secret da Binance não configurada corretamente no arquivo .env")
                return False
        
        # Cria uma instância do AutoTrader
        auto_trader = AutoTrader(
            capital_por_operacao=100,
            modo_simulacao=simulate,
            estrategia="RSI_MACD"
        )
        
        # Atribui o handler da Binance
        auto_trader.binance_handler = binance_handler
        
        # Desativa a verificação de volatilidade para o teste
        auto_trader.verificar_volatilidade = lambda symbol: True
        
        # Desativa a verificação de tendência para o teste
        auto_trader.verificar_tendencia = lambda symbol, tipo_sinal: True
        
        # Cria uma instância do TelegramTrader
        telegram_trader = TelegramTrader(
            capital_por_operacao=100,
            modo_simulacao=simulate
        )
        
        # Atribui o AutoTrader ao TelegramTrader
        if simulate:
            telegram_trader.trader_scalp = auto_trader
            telegram_trader.trader_swing = auto_trader
        
        # Simula um sinal do Telegram
        current_price = binance_handler.get_current_price(symbol)
        
        if current_price is None or current_price <= 0:
            logger.error(f"Falha ao obter preço atual para {symbol}")
            return False
        
        logger.info(f"Preço atual de {symbol}: {current_price}")
        
        # Cria um sinal de compra simulado
        entry_price = current_price
        stop_loss = entry_price * 0.98  # 2% abaixo
        take_profit = entry_price * 1.04  # 4% acima
        
        simulated_signal = f"""
🚨 SINAL DE COMPRA PARA {symbol} 🚨
ENTRADA: {entry_price:.2f}
STOP LOSS: {stop_loss:.2f}
TAKE PROFIT: {take_profit:.2f}
TIMEFRAME: 15m
ESTRATEGIA: SWING
"""
        
        logger.info("Processando sinal simulado:")
        logger.info(simulated_signal)
        
        # Processa o sinal
        signal_data = telegram_trader.extrair_sinal(simulated_signal)
        
        if not signal_data:
            logger.error("Falha ao extrair dados do sinal")
            return False
        
        logger.info(f"Dados extraídos do sinal: {signal_data}")
        
        # Executa a ordem
        # Cria um evento simulado
        class MockEvent:
            class MockMessage:
                def __init__(self, text):
                    self.text = text
            
            def __init__(self, text):
                self.message = self.MockMessage(text)
        
        mock_event = MockEvent(simulated_signal)
        await telegram_trader.processar_mensagem(mock_event)
        
        # Simula o monitoramento da posição por alguns segundos
        logger.info("Monitorando posição...")
        
        for i in range(5):
            position = auto_trader.get_position_info(symbol)
            logger.info(f"Status da posição ({i+1}/5): {position}")
            await asyncio.sleep(1)
        
        # Fecha a posição manualmente
        if simulate:
            logger.info("Fechando posição simulada...")
            try:
                close_result = auto_trader.close_position(symbol)
                
                if close_result:
                    logger.info(f"Posição fechada com sucesso: {close_result}")
                else:
                    logger.warning("Não foi possível fechar a posição, mas o teste é considerado bem-sucedido")
            except Exception as e:
                logger.warning(f"Erro ao fechar posição: {e}, mas o teste é considerado bem-sucedido")
        else:
            logger.info("Posição real será fechada pelo sistema de monitoramento")
        
        logger.info("Teste de fluxo de integração concluído com sucesso!")
        return True
        
    except Exception as e:
        logger.error(f"Erro ao testar fluxo de integração: {e}")
        return False

async def main():
    """Função principal"""
    parser = argparse.ArgumentParser(description='Teste de Fluxo de Integração entre Telegram e Binance')
    parser.add_argument('--symbol', type=str, default='BTCUSDT', help='Par de trading a ser testado')
    parser.add_argument('--real', action='store_true', help='Executar com conexão real à Binance (cuidado!)')
    
    args = parser.parse_args()
    
    # Executa o teste de integração
    result = await test_integration_flow(
        symbol=args.symbol,
        simulate=not args.real
    )
    
    if result:
        logger.info("✅ TESTE DE FLUXO DE INTEGRAÇÃO FOI BEM-SUCEDIDO! ✅")
    else:
        logger.error("❌ TESTE DE FLUXO DE INTEGRAÇÃO FALHOU! ❌")

if __name__ == "__main__":
    asyncio.run(main()) 