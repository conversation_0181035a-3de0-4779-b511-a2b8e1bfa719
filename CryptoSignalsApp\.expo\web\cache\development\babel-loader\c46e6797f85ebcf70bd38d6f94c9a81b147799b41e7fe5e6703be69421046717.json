{"ast": null, "code": "import Dimensions from \"react-native-web/dist/exports/Dimensions\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nvar overflowLeft = function overflowLeft(center) {\n  return center < 0;\n};\nvar overflowRight = function overflowRight(center, tooltipWidth) {\n  var _Dimensions$get = Dimensions.get('window'),\n    layoutWidth = _Dimensions$get.width;\n  return center + tooltipWidth > layoutWidth;\n};\nvar overflowBottom = function overflowBottom(childrenY, childrenHeight, tooltipHeight) {\n  var _Dimensions$get2 = Dimensions.get('window'),\n    layoutHeight = _Dimensions$get2.height;\n  return childrenY + childrenHeight + tooltipHeight > layoutHeight;\n};\nvar getTooltipXPosition = function getTooltipXPosition(_ref, _ref2) {\n  var childrenX = _ref.pageX,\n    childrenWidth = _ref.width;\n  var tooltipWidth = _ref2.width;\n  var center = childrenWidth > 0 ? childrenX + (childrenWidth - tooltipWidth) / 2 : childrenX;\n  if (overflowLeft(center)) return childrenX;\n  if (overflowRight(center, tooltipWidth)) return childrenX + childrenWidth - tooltipWidth;\n  return center;\n};\nvar getTooltipYPosition = function getTooltipYPosition(_ref3, _ref4) {\n  var childrenY = _ref3.pageY,\n    childrenHeight = _ref3.height;\n  var tooltipHeight = _ref4.height;\n  if (overflowBottom(childrenY, childrenHeight, tooltipHeight)) return childrenY - tooltipHeight;\n  return childrenY + childrenHeight;\n};\nvar getChildrenMeasures = function getChildrenMeasures(style, measures) {\n  var _StyleSheet$flatten = StyleSheet.flatten(style),\n    position = _StyleSheet$flatten.position,\n    top = _StyleSheet$flatten.top,\n    bottom = _StyleSheet$flatten.bottom,\n    left = _StyleSheet$flatten.left,\n    right = _StyleSheet$flatten.right;\n  if (position === 'absolute') {\n    var pageX = 0;\n    var pageY = measures.pageY;\n    var height = 0;\n    var width = 0;\n    if (typeof left === 'number') {\n      pageX = left;\n      width = 0;\n    }\n    if (typeof right === 'number') {\n      pageX = measures.width - right;\n      width = 0;\n    }\n    if (typeof top === 'number') {\n      pageY = pageY + top;\n    }\n    if (typeof bottom === 'number') {\n      pageY = pageY - bottom;\n    }\n    return {\n      pageX: pageX,\n      pageY: pageY,\n      width: width,\n      height: height\n    };\n  }\n  return measures;\n};\nexport var getTooltipPosition = function getTooltipPosition(_ref5, component) {\n  var children = _ref5.children,\n    tooltip = _ref5.tooltip,\n    measured = _ref5.measured;\n  if (!measured) return {};\n  var measures = children;\n  if (component.props.style) {\n    measures = getChildrenMeasures(component.props.style, children);\n  }\n  return {\n    left: getTooltipXPosition(measures, tooltip),\n    top: getTooltipYPosition(measures, tooltip)\n  };\n};", "map": {"version": 3, "names": ["overflowLeft", "center", "overflowRight", "tooltipWidth", "_Dimensions$get", "Dimensions", "get", "<PERSON><PERSON><PERSON><PERSON>", "width", "overflowBottom", "childrenY", "childrenHeight", "tooltipHeight", "_Dimensions$get2", "layoutHeight", "height", "getTooltipXPosition", "_ref", "_ref2", "childrenX", "pageX", "children<PERSON><PERSON>th", "getTooltipYPosition", "_ref3", "_ref4", "pageY", "getChildrenMeasures", "style", "measures", "_StyleSheet$flatten", "StyleSheet", "flatten", "position", "top", "bottom", "left", "right", "getTooltipPosition", "_ref5", "component", "children", "tooltip", "measured", "props"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\Tooltip\\utils.ts"], "sourcesContent": ["import {\n  Dimensions,\n  LayoutRectangle,\n  StyleProp,\n  ViewStyle,\n  StyleSheet,\n} from 'react-native';\n\ntype ChildrenMeasurement = {\n  width: number;\n  height: number;\n  pageX: number;\n  pageY: number;\n};\n\ntype TooltipLayout = LayoutRectangle;\n\nexport type Measurement = {\n  children: ChildrenMeasurement;\n  tooltip: TooltipLayout;\n  measured: boolean;\n};\n\nexport type TooltipChildProps = {\n  style: StyleProp<ViewStyle>;\n  disabled?: boolean;\n  onPress?: () => void;\n  onHoverIn?: () => void;\n  onHoverOut?: () => void;\n};\n\n/**\n * Return true when the tooltip center x-coordinate relative to the wrapped element is negative.\n * The tooltip will be placed at the starting x-coordinate from the wrapped element.\n */\nconst overflowLeft = (center: number): boolean => {\n  return center < 0;\n};\n\n/**\n * Return true when the tooltip center x-coordinate + tooltip width is greater than the layout width\n * The tooltip width will grow from right to left relative to the wrapped element.\n */\nconst overflowRight = (center: number, tooltipWidth: number): boolean => {\n  const { width: layoutWidth } = Dimensions.get('window');\n\n  return center + tooltipWidth > layoutWidth;\n};\n\n/**\n * Return true when the children y-coordinate + its height + tooltip height is greater than the layout height.\n * The tooltip will be placed at the top of the wrapped element.\n */\nconst overflowBottom = (\n  childrenY: number,\n  childrenHeight: number,\n  tooltipHeight: number\n): boolean => {\n  const { height: layoutHeight } = Dimensions.get('window');\n\n  return childrenY + childrenHeight + tooltipHeight > layoutHeight;\n};\n\nconst getTooltipXPosition = (\n  { pageX: childrenX, width: childrenWidth }: ChildrenMeasurement,\n  { width: tooltipWidth }: TooltipLayout\n): number => {\n  // when the children use position absolute the childrenWidth is measured as 0,\n  // so it's best to anchor the tooltip at the start of the children\n  const center =\n    childrenWidth > 0\n      ? childrenX + (childrenWidth - tooltipWidth) / 2\n      : childrenX;\n\n  if (overflowLeft(center)) return childrenX;\n\n  if (overflowRight(center, tooltipWidth))\n    return childrenX + childrenWidth - tooltipWidth;\n\n  return center;\n};\n\nconst getTooltipYPosition = (\n  { pageY: childrenY, height: childrenHeight }: ChildrenMeasurement,\n  { height: tooltipHeight }: TooltipLayout\n): number => {\n  if (overflowBottom(childrenY, childrenHeight, tooltipHeight))\n    return childrenY - tooltipHeight;\n\n  return childrenY + childrenHeight;\n};\n\nconst getChildrenMeasures = (\n  style: StyleProp<ViewStyle>,\n  measures: ChildrenMeasurement\n): ChildrenMeasurement => {\n  const { position, top, bottom, left, right } = StyleSheet.flatten(style);\n\n  if (position === 'absolute') {\n    let pageX = 0;\n    let pageY = measures.pageY;\n    let height = 0;\n    let width = 0;\n    if (typeof left === 'number') {\n      pageX = left;\n      width = 0;\n    }\n    if (typeof right === 'number') {\n      pageX = measures.width - right;\n      width = 0;\n    }\n    if (typeof top === 'number') {\n      pageY = pageY + top;\n    }\n    if (typeof bottom === 'number') {\n      pageY = pageY - bottom;\n    }\n\n    return { pageX, pageY, width, height };\n  }\n\n  return measures;\n};\n\nexport const getTooltipPosition = (\n  { children, tooltip, measured }: Measurement,\n  component: React.ReactElement<{\n    style: StyleProp<ViewStyle>;\n  }>\n): {} | { left: number; top: number } => {\n  if (!measured) return {};\n  let measures = children;\n  if (component.props.style) {\n    measures = getChildrenMeasures(component.props.style, children);\n  }\n\n  return {\n    left: getTooltipXPosition(measures, tooltip),\n    top: getTooltipYPosition(measures, tooltip),\n  };\n};\n"], "mappings": ";;AAmCA,IAAMA,YAAY,GAAI,SAAhBA,YAAYA,CAAIC,MAAc,EAAc;EAChD,OAAOA,MAAM,GAAG,CAAC;AACnB,CAAC;AAMD,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAID,MAAc,EAAEE,YAAoB,EAAc;EACvE,IAAAC,eAAA,GAA+BC,UAAU,CAACC,GAAG,CAAC,QAAQ,CAAC;IAAxCC,WAAA,GAAAH,eAAA,CAAPI,KAAK;EAEb,OAAOP,MAAM,GAAGE,YAAY,GAAGI,WAAW;AAC5C,CAAC;AAMD,IAAME,cAAc,GAAG,SAAjBA,cAAcA,CAClBC,SAAiB,EACjBC,cAAsB,EACtBC,aAAqB,EACT;EACZ,IAAAC,gBAAA,GAAiCR,UAAU,CAACC,GAAG,CAAC,QAAQ,CAAC;IAAzCQ,YAAA,GAAAD,gBAAA,CAARE,MAAM;EAEd,OAAOL,SAAS,GAAGC,cAAc,GAAGC,aAAa,GAAGE,YAAY;AAClE,CAAC;AAED,IAAME,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAAC,IAAA,EAAAC,KAAA,EAGZ;EAAA,IAFFC,SAAS,GAAAF,IAAA,CAAhBG,KAAK;IAAoBC,aAAA,GAAAJ,IAAA,CAAPT,KAAK;EAAA,IAChBL,YAAA,GAAAe,KAAA,CAAPV,KAAK;EAIP,IAAMP,MAAM,GACVoB,aAAa,GAAG,CAAC,GACbF,SAAS,GAAG,CAACE,aAAa,GAAGlB,YAAY,IAAI,CAAC,GAC9CgB,SAAS;EAEf,IAAInB,YAAY,CAACC,MAAM,CAAC,EAAE,OAAOkB,SAAS;EAE1C,IAAIjB,aAAa,CAACD,MAAM,EAAEE,YAAY,CAAC,EACrC,OAAOgB,SAAS,GAAGE,aAAa,GAAGlB,YAAY;EAEjD,OAAOF,MAAM;AACf,CAAC;AAED,IAAMqB,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAAC,KAAA,EAAAC,KAAA,EAGZ;EAAA,IAFFd,SAAS,GAAAa,KAAA,CAAhBE,KAAK;IAAqBd,cAAA,GAAAY,KAAA,CAARR,MAAM;EAAA,IAChBH,aAAA,GAAAY,KAAA,CAART,MAAM;EAER,IAAIN,cAAc,CAACC,SAAS,EAAEC,cAAc,EAAEC,aAAa,CAAC,EAC1D,OAAOF,SAAS,GAAGE,aAAa;EAElC,OAAOF,SAAS,GAAGC,cAAc;AACnC,CAAC;AAED,IAAMe,mBAAmB,GAAG,SAAtBA,mBAAmBA,CACvBC,KAA2B,EAC3BC,QAA6B,EACL;EACxB,IAAAC,mBAAA,GAA+CC,UAAU,CAACC,OAAO,CAACJ,KAAK,CAAC;IAAhEK,QAAQ,GAAAH,mBAAA,CAARG,QAAQ;IAAEC,GAAG,GAAAJ,mBAAA,CAAHI,GAAG;IAAEC,MAAM,GAAAL,mBAAA,CAANK,MAAM;IAAEC,IAAI,GAAAN,mBAAA,CAAJM,IAAI;IAAEC,KAAA,GAAAP,mBAAA,CAAAO,KAAA;EAErC,IAAIJ,QAAQ,KAAK,UAAU,EAAE;IAC3B,IAAIZ,KAAK,GAAG,CAAC;IACb,IAAIK,KAAK,GAAGG,QAAQ,CAACH,KAAK;IAC1B,IAAIV,MAAM,GAAG,CAAC;IACd,IAAIP,KAAK,GAAG,CAAC;IACb,IAAI,OAAO2B,IAAI,KAAK,QAAQ,EAAE;MAC5Bf,KAAK,GAAGe,IAAI;MACZ3B,KAAK,GAAG,CAAC;IACX;IACA,IAAI,OAAO4B,KAAK,KAAK,QAAQ,EAAE;MAC7BhB,KAAK,GAAGQ,QAAQ,CAACpB,KAAK,GAAG4B,KAAK;MAC9B5B,KAAK,GAAG,CAAC;IACX;IACA,IAAI,OAAOyB,GAAG,KAAK,QAAQ,EAAE;MAC3BR,KAAK,GAAGA,KAAK,GAAGQ,GAAG;IACrB;IACA,IAAI,OAAOC,MAAM,KAAK,QAAQ,EAAE;MAC9BT,KAAK,GAAGA,KAAK,GAAGS,MAAM;IACxB;IAEA,OAAO;MAAEd,KAAK,EAALA,KAAK;MAAEK,KAAK,EAALA,KAAK;MAAEjB,KAAK,EAALA,KAAK;MAAEO,MAAA,EAAAA;IAAO,CAAC;EACxC;EAEA,OAAOa,QAAQ;AACjB,CAAC;AAED,OAAO,IAAMS,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAAC,KAAA,EAE7BC,SAEE,EACqC;EAAA,IAJrCC,QAAQ,GAAAF,KAAA,CAARE,QAAQ;IAAEC,OAAO,GAAAH,KAAA,CAAPG,OAAO;IAAEC,QAAA,GAAAJ,KAAA,CAAAI,QAAA;EAKrB,IAAI,CAACA,QAAQ,EAAE,OAAO,CAAC,CAAC;EACxB,IAAId,QAAQ,GAAGY,QAAQ;EACvB,IAAID,SAAS,CAACI,KAAK,CAAChB,KAAK,EAAE;IACzBC,QAAQ,GAAGF,mBAAmB,CAACa,SAAS,CAACI,KAAK,CAAChB,KAAK,EAAEa,QAAQ,CAAC;EACjE;EAEA,OAAO;IACLL,IAAI,EAAEnB,mBAAmB,CAACY,QAAQ,EAAEa,OAAO,CAAC;IAC5CR,GAAG,EAAEX,mBAAmB,CAACM,QAAQ,EAAEa,OAAO;EAC5C,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}