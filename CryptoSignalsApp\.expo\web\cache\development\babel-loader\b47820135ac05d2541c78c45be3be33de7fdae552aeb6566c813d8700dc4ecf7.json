{"ast": null, "code": "export var MAXIMIZED_LABEL_FONT_SIZE = 16;\nexport var MINIMIZED_LABEL_FONT_SIZE = 12;\nexport var LABEL_WIGGLE_X_OFFSET = 4;\nexport var ADORNMENT_SIZE = 24;\nexport var MIN_WIDTH = 100;\nexport var MD2_AFFIX_OFFSET = 12;\nexport var MD3_AFFIX_OFFSET = 16;\nexport var ICON_SIZE = 24;\nexport var MD2_ICON_OFFSET = 12;\nexport var MD3_ICON_OFFSET = 16;\nexport var MD2_MIN_HEIGHT = 64;\nexport var MD3_MIN_HEIGHT = 56;\nexport var MD3_ADORNMENT_OFFSET = 16;\nexport var MD2_ADORNMENT_OFFSET = 12;\nexport var LABEL_PADDING_TOP_DENSE = 24;\nexport var LABEL_PADDING_TOP = 8;\nexport var MD2_LABEL_PADDING_TOP = 30;\nexport var MD3_LABEL_PADDING_TOP = 26;\nexport var MD2_LABEL_PADDING_HORIZONTAL = 12;\nexport var MD3_LABEL_PADDING_HORIZONTAL = 16;\nexport var MD2_FLAT_INPUT_OFFSET = 8;\nexport var MD3_FLAT_INPUT_OFFSET = 16;\nexport var MINIMIZED_LABEL_Y_OFFSET = -18;\nexport var MIN_DENSE_HEIGHT_WL = 52;\nexport var MIN_DENSE_HEIGHT = 40;\nexport var MD2_INPUT_PADDING_HORIZONTAL = 14;\nexport var MD3_INPUT_PADDING_HORIZONTAL = 16;\nexport var MD2_OUTLINED_INPUT_OFFSET = 8;\nexport var MD3_OUTLINED_INPUT_OFFSET = 16;\nexport var OUTLINE_MINIMIZED_LABEL_Y_OFFSET = -6;\nexport var MIN_DENSE_HEIGHT_OUTLINED = 48;", "map": {"version": 3, "names": ["MAXIMIZED_LABEL_FONT_SIZE", "MINIMIZED_LABEL_FONT_SIZE", "LABEL_WIGGLE_X_OFFSET", "ADORNMENT_SIZE", "MIN_WIDTH", "MD2_AFFIX_OFFSET", "MD3_AFFIX_OFFSET", "ICON_SIZE", "MD2_ICON_OFFSET", "MD3_ICON_OFFSET", "MD2_MIN_HEIGHT", "MD3_MIN_HEIGHT", "MD3_ADORNMENT_OFFSET", "MD2_ADORNMENT_OFFSET", "LABEL_PADDING_TOP_DENSE", "LABEL_PADDING_TOP", "MD2_LABEL_PADDING_TOP", "MD3_LABEL_PADDING_TOP", "MD2_LABEL_PADDING_HORIZONTAL", "MD3_LABEL_PADDING_HORIZONTAL", "MD2_FLAT_INPUT_OFFSET", "MD3_FLAT_INPUT_OFFSET", "MINIMIZED_LABEL_Y_OFFSET", "MIN_DENSE_HEIGHT_WL", "MIN_DENSE_HEIGHT", "MD2_INPUT_PADDING_HORIZONTAL", "MD3_INPUT_PADDING_HORIZONTAL", "MD2_OUTLINED_INPUT_OFFSET", "MD3_OUTLINED_INPUT_OFFSET", "OUTLINE_MINIMIZED_LABEL_Y_OFFSET", "MIN_DENSE_HEIGHT_OUTLINED"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\TextInput\\constants.tsx"], "sourcesContent": ["export const MAXIMIZED_LABEL_FONT_SIZE = 16;\nexport const MINIMIZED_LABEL_FONT_SIZE = 12;\nexport const LABEL_WIGGLE_X_OFFSET = 4;\n\nexport const ADORNMENT_SIZE = 24;\nexport const MIN_WIDTH = 100;\n\n//Text input affix offset\nexport const MD2_AFFIX_OFFSET = 12;\nexport const MD3_AFFIX_OFFSET = 16;\n\n// Text input icon\nexport const ICON_SIZE = 24;\nexport const MD2_ICON_OFFSET = 12;\nexport const MD3_ICON_OFFSET = 16;\n\n// Text input common\nexport const MD2_MIN_HEIGHT = 64;\nexport const MD3_MIN_HEIGHT = 56;\nexport const MD3_ADORNMENT_OFFSET = 16;\nexport const MD2_ADORNMENT_OFFSET = 12;\nexport const LABEL_PADDING_TOP_DENSE = 24;\nexport const LABEL_PADDING_TOP = 8;\n\n// Text input flat\nexport const MD2_LABEL_PADDING_TOP = 30;\nexport const MD3_LABEL_PADDING_TOP = 26;\n\nexport const MD2_LABEL_PADDING_HORIZONTAL = 12;\nexport const MD3_LABEL_PADDING_HORIZONTAL = 16;\n\nexport const MD2_FLAT_INPUT_OFFSET = 8;\nexport const MD3_FLAT_INPUT_OFFSET = 16;\n\nexport const MINIMIZED_LABEL_Y_OFFSET = -18;\nexport const MIN_DENSE_HEIGHT_WL = 52;\nexport const MIN_DENSE_HEIGHT = 40;\n\n// Text input outlined\nexport const MD2_INPUT_PADDING_HORIZONTAL = 14;\nexport const MD3_INPUT_PADDING_HORIZONTAL = 16;\n\n// extra space to avoid overlapping input's text and icon\nexport const MD2_OUTLINED_INPUT_OFFSET = 8;\nexport const MD3_OUTLINED_INPUT_OFFSET = 16;\n\nexport const OUTLINE_MINIMIZED_LABEL_Y_OFFSET = -6;\nexport const MIN_DENSE_HEIGHT_OUTLINED = 48;\n"], "mappings": "AAAA,OAAO,IAAMA,yBAAyB,GAAG,EAAE;AAC3C,OAAO,IAAMC,yBAAyB,GAAG,EAAE;AAC3C,OAAO,IAAMC,qBAAqB,GAAG,CAAC;AAEtC,OAAO,IAAMC,cAAc,GAAG,EAAE;AAChC,OAAO,IAAMC,SAAS,GAAG,GAAG;AAG5B,OAAO,IAAMC,gBAAgB,GAAG,EAAE;AAClC,OAAO,IAAMC,gBAAgB,GAAG,EAAE;AAGlC,OAAO,IAAMC,SAAS,GAAG,EAAE;AAC3B,OAAO,IAAMC,eAAe,GAAG,EAAE;AACjC,OAAO,IAAMC,eAAe,GAAG,EAAE;AAGjC,OAAO,IAAMC,cAAc,GAAG,EAAE;AAChC,OAAO,IAAMC,cAAc,GAAG,EAAE;AAChC,OAAO,IAAMC,oBAAoB,GAAG,EAAE;AACtC,OAAO,IAAMC,oBAAoB,GAAG,EAAE;AACtC,OAAO,IAAMC,uBAAuB,GAAG,EAAE;AACzC,OAAO,IAAMC,iBAAiB,GAAG,CAAC;AAGlC,OAAO,IAAMC,qBAAqB,GAAG,EAAE;AACvC,OAAO,IAAMC,qBAAqB,GAAG,EAAE;AAEvC,OAAO,IAAMC,4BAA4B,GAAG,EAAE;AAC9C,OAAO,IAAMC,4BAA4B,GAAG,EAAE;AAE9C,OAAO,IAAMC,qBAAqB,GAAG,CAAC;AACtC,OAAO,IAAMC,qBAAqB,GAAG,EAAE;AAEvC,OAAO,IAAMC,wBAAwB,GAAG,CAAC,EAAE;AAC3C,OAAO,IAAMC,mBAAmB,GAAG,EAAE;AACrC,OAAO,IAAMC,gBAAgB,GAAG,EAAE;AAGlC,OAAO,IAAMC,4BAA4B,GAAG,EAAE;AAC9C,OAAO,IAAMC,4BAA4B,GAAG,EAAE;AAG9C,OAAO,IAAMC,yBAAyB,GAAG,CAAC;AAC1C,OAAO,IAAMC,yBAAyB,GAAG,EAAE;AAE3C,OAAO,IAAMC,gCAAgC,GAAG,CAAC,CAAC;AAClD,OAAO,IAAMC,yBAAyB,GAAG,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}