// HomeNavigator.js
import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import Home from './path_to_Home';  // Certifique-se de que este é o caminho correto para o componente Home
import Signals from './path_to_Signals';  // Certifique-se de que este é o caminho correto para o componente Signals
// ... você pode adicionar outras subpáginas aqui

const HomeStack = createStackNavigator();

const HomeNavigator = () => {
  return (
    <HomeStack.Navigator initialRouteName="Home">
      <HomeStack.Screen name="Home" component={Home} />
      <HomeStack.Screen name="Signals" component={Signals} />
      {/* ... outras subpáginas de "Home" */}
    </HomeStack.Navigator>
  );
};

export default HomeNavigator;
