{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport React, { useState, useContext } from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport TouchableOpacity from \"react-native-web/dist/exports/TouchableOpacity\";\nimport FlatList from \"react-native-web/dist/exports/FlatList\";\nimport Alert from \"react-native-web/dist/exports/Alert\";\nimport { Button, useTheme } from 'react-native-paper';\nimport PageTitle from \"../../components/PageTitle\";\nimport Wrapper from \"../../components/Wrapper\";\nimport { StoreContext } from \"../../store\";\nimport styles from \"./styles\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar Inicio = function Inicio(_ref) {\n  var navigation = _ref.navigation,\n    _ref$route = _ref.route,\n    route = _ref$route === void 0 ? {} : _ref$route;\n  var _useTheme = useTheme(),\n    colors = _useTheme.colors;\n  var _useContext = useContext(StoreContext),\n    _useContext2 = _slicedToArray(_useContext, 2),\n    state = _useContext2[0],\n    _ = _useContext2[1];\n  var _useState = useState([{\n      id: '1',\n      text: 'Bitcoin Signal: BUY at $45,000',\n      likes: 12,\n      comments: 3\n    }, {\n      id: '2',\n      text: 'Ethereum Signal: SELL at $3,200',\n      likes: 8,\n      comments: 1\n    }, {\n      id: '3',\n      text: 'Altcoin Alert: DOGE pumping!',\n      likes: 25,\n      comments: 7\n    }]),\n    _useState2 = _slicedToArray(_useState, 2),\n    signals = _useState2[0],\n    setSignals = _useState2[1];\n  var handleLike = function handleLike(id) {\n    var updatedSignals = signals.map(function (signal) {\n      if (signal.id === id) {\n        return _objectSpread(_objectSpread({}, signal), {}, {\n          likes: signal.likes + 1\n        });\n      }\n      return signal;\n    });\n    setSignals(updatedSignals);\n  };\n  var handleComment = function handleComment(id) {\n    Alert.alert(\"Comments\", \"Comments feature coming soon!\");\n  };\n  return _jsxs(Wrapper, {\n    children: [_jsx(PageTitle, {\n      text: \"Crypto Signals Home\"\n    }, \"TitleHome\"), _jsxs(View, {\n      style: {\n        padding: 16\n      },\n      children: [_jsx(Text, {\n        style: {\n          color: '#fff',\n          fontSize: 18,\n          marginBottom: 16,\n          fontFamily: 'Poppins_500Medium'\n        },\n        children: \"Latest Signals\"\n      }), _jsx(FlatList, {\n        data: signals,\n        keyExtractor: function keyExtractor(item) {\n          return item.id;\n        },\n        renderItem: function renderItem(_ref2) {\n          var item = _ref2.item;\n          return _jsxs(View, {\n            style: {\n              backgroundColor: '#2a2a2a',\n              padding: 16,\n              marginBottom: 12,\n              borderRadius: 8,\n              borderLeftWidth: 4,\n              borderLeftColor: '#FECB37'\n            },\n            children: [_jsx(Text, {\n              style: {\n                color: '#fff',\n                fontSize: 16,\n                marginBottom: 8\n              },\n              children: item.text\n            }), _jsxs(View, {\n              style: {\n                flexDirection: 'row',\n                justifyContent: 'space-between'\n              },\n              children: [_jsxs(TouchableOpacity, {\n                onPress: function onPress() {\n                  return handleLike(item.id);\n                },\n                style: {\n                  flexDirection: 'row',\n                  alignItems: 'center'\n                },\n                children: [_jsx(Text, {\n                  style: {\n                    color: '#FECB37',\n                    marginRight: 4\n                  },\n                  children: \"\\uD83D\\uDC4D\"\n                }), _jsx(Text, {\n                  style: {\n                    color: '#8a8a8a'\n                  },\n                  children: item.likes\n                })]\n              }), _jsxs(TouchableOpacity, {\n                onPress: function onPress() {\n                  return handleComment(item.id);\n                },\n                style: {\n                  flexDirection: 'row',\n                  alignItems: 'center'\n                },\n                children: [_jsx(Text, {\n                  style: {\n                    color: '#FECB37',\n                    marginRight: 4\n                  },\n                  children: \"\\uD83D\\uDCAC\"\n                }), _jsx(Text, {\n                  style: {\n                    color: '#8a8a8a'\n                  },\n                  children: item.comments\n                })]\n              })]\n            })]\n          });\n        }\n      }), _jsxs(View, {\n        style: {\n          marginTop: 20\n        },\n        children: [_jsx(Button, {\n          mode: \"contained\",\n          onPress: function onPress() {\n            return navigation == null ? void 0 : navigation.navigate('Channels');\n          },\n          style: {\n            marginBottom: 10\n          },\n          children: \"View All Channels\"\n        }), _jsx(Button, {\n          mode: \"outlined\",\n          onPress: function onPress() {\n            return navigation == null ? void 0 : navigation.navigate('Premium');\n          },\n          children: \"Upgrade to Premium\"\n        })]\n      })]\n    })]\n  });\n};\nexport default Inicio;", "map": {"version": 3, "names": ["React", "useState", "useContext", "View", "Text", "TouchableOpacity", "FlatList", "<PERSON><PERSON>", "<PERSON><PERSON>", "useTheme", "Page<PERSON><PERSON>le", "Wrapper", "StoreContext", "styles", "jsx", "_jsx", "jsxs", "_jsxs", "<PERSON><PERSON>o", "_ref", "navigation", "_ref$route", "route", "_useTheme", "colors", "_useContext", "_useContext2", "_slicedToArray", "state", "_", "_useState", "id", "text", "likes", "comments", "_useState2", "signals", "setSignals", "handleLike", "updatedSignals", "map", "signal", "_objectSpread", "handleComment", "alert", "children", "style", "padding", "color", "fontSize", "marginBottom", "fontFamily", "data", "keyExtractor", "item", "renderItem", "_ref2", "backgroundColor", "borderRadius", "borderLeftWidth", "borderLeftColor", "flexDirection", "justifyContent", "onPress", "alignItems", "marginRight", "marginTop", "mode", "navigate"], "sources": ["E:/CryptoSignalsApp/src/pages/Home/index.js"], "sourcesContent": ["import React, { useState, useContext } from 'react';\r\nimport { View, Text, TouchableOpacity, FlatList, Alert } from 'react-native';\r\nimport { Button, useTheme } from 'react-native-paper';\r\nimport PageTitle from '../../components/PageTitle';\r\nimport Wrapper from '../../components/Wrapper';\r\nimport { StoreContext } from '../../store';\r\nimport styles from './styles';\r\n\r\nconst Inicio = ({ navigation, route = {} }) => {\r\n    const { colors } = useTheme();\r\n    const [state, _] = useContext(StoreContext);\r\n\r\n    // Estado para Sinais (posts, atualizações, etc.)\r\n    const [signals, setSignals] = useState([\r\n        { id: '1', text: 'Bitcoin Signal: BUY at $45,000', likes: 12, comments: 3 },\r\n        { id: '2', text: 'Ethereum Signal: SELL at $3,200', likes: 8, comments: 1 },\r\n        { id: '3', text: 'Altcoin Alert: DOGE pumping!', likes: 25, comments: 7 },\r\n    ]);\r\n\r\n    // Função para lidar com likes\r\n    const handleLike = (id) => {\r\n        const updatedSignals = signals.map((signal) => {\r\n            if (signal.id === id) {\r\n                return { ...signal, likes: signal.likes + 1 };\r\n            }\r\n            return signal;\r\n        });\r\n        setSignals(updatedSignals);\r\n    };\r\n\r\n    // Função para lidar com comentários\r\n    const handleComment = (id) => {\r\n        Alert.alert(\"Comments\", \"Comments feature coming soon!\");\r\n    };\r\n\r\n\r\n    return (\r\n        <Wrapper>\r\n            <PageTitle key=\"TitleHome\" text=\"Crypto Signals Home\" />\r\n\r\n            <View style={{ padding: 16 }}>\r\n                <Text style={{ color: '#fff', fontSize: 18, marginBottom: 16, fontFamily: 'Poppins_500Medium' }}>\r\n                    Latest Signals\r\n                </Text>\r\n\r\n                {/* Lista de Sinais */}\r\n                <FlatList\r\n                    data={signals}\r\n                    keyExtractor={(item) => item.id}\r\n                    renderItem={({ item }) => (\r\n                        <View style={{\r\n                            backgroundColor: '#2a2a2a',\r\n                            padding: 16,\r\n                            marginBottom: 12,\r\n                            borderRadius: 8,\r\n                            borderLeftWidth: 4,\r\n                            borderLeftColor: '#FECB37'\r\n                        }}>\r\n                            <Text style={{ color: '#fff', fontSize: 16, marginBottom: 8 }}>{item.text}</Text>\r\n                            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>\r\n                                <TouchableOpacity\r\n                                    onPress={() => handleLike(item.id)}\r\n                                    style={{ flexDirection: 'row', alignItems: 'center' }}\r\n                                >\r\n                                    <Text style={{ color: '#FECB37', marginRight: 4 }}>👍</Text>\r\n                                    <Text style={{ color: '#8a8a8a' }}>{item.likes}</Text>\r\n                                </TouchableOpacity>\r\n                                <TouchableOpacity\r\n                                    onPress={() => handleComment(item.id)}\r\n                                    style={{ flexDirection: 'row', alignItems: 'center' }}\r\n                                >\r\n                                    <Text style={{ color: '#FECB37', marginRight: 4 }}>💬</Text>\r\n                                    <Text style={{ color: '#8a8a8a' }}>{item.comments}</Text>\r\n                                </TouchableOpacity>\r\n                            </View>\r\n                        </View>\r\n                    )}\r\n                />\r\n\r\n                <View style={{ marginTop: 20 }}>\r\n                    <Button\r\n                        mode=\"contained\"\r\n                        onPress={() => navigation?.navigate('Channels')}\r\n                        style={{ marginBottom: 10 }}\r\n                    >\r\n                        View All Channels\r\n                    </Button>\r\n                    <Button\r\n                        mode=\"outlined\"\r\n                        onPress={() => navigation?.navigate('Premium')}\r\n                    >\r\n                        Upgrade to Premium\r\n                    </Button>\r\n                </View>\r\n            </View>\r\n        </Wrapper>\r\n    );\r\n};\r\n\r\nexport default Inicio;\r\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,UAAU,QAAQ,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,gBAAA;AAAA,OAAAC,QAAA;AAAA,OAAAC,KAAA;AAEpD,SAASC,MAAM,EAAEC,QAAQ,QAAQ,oBAAoB;AACrD,OAAOC,SAAS;AAChB,OAAOC,OAAO;AACd,SAASC,YAAY;AACrB,OAAOC,MAAM;AAAiB,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAE9B,IAAMC,MAAM,GAAG,SAATA,MAAMA,CAAAC,IAAA,EAAmC;EAAA,IAA7BC,UAAU,GAAAD,IAAA,CAAVC,UAAU;IAAAC,UAAA,GAAAF,IAAA,CAAEG,KAAK;IAALA,KAAK,GAAAD,UAAA,cAAG,CAAC,CAAC,GAAAA,UAAA;EACpC,IAAAE,SAAA,GAAmBd,QAAQ,CAAC,CAAC;IAArBe,MAAM,GAAAD,SAAA,CAANC,MAAM;EACd,IAAAC,WAAA,GAAmBvB,UAAU,CAACU,YAAY,CAAC;IAAAc,YAAA,GAAAC,cAAA,CAAAF,WAAA;IAApCG,KAAK,GAAAF,YAAA;IAAEG,CAAC,GAAAH,YAAA;EAGf,IAAAI,SAAA,GAA8B7B,QAAQ,CAAC,CACnC;MAAE8B,EAAE,EAAE,GAAG;MAAEC,IAAI,EAAE,gCAAgC;MAAEC,KAAK,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAE,CAAC,EAC3E;MAAEH,EAAE,EAAE,GAAG;MAAEC,IAAI,EAAE,iCAAiC;MAAEC,KAAK,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAE,CAAC,EAC3E;MAAEH,EAAE,EAAE,GAAG;MAAEC,IAAI,EAAE,8BAA8B;MAAEC,KAAK,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAE,CAAC,CAC5E,CAAC;IAAAC,UAAA,GAAAR,cAAA,CAAAG,SAAA;IAJKM,OAAO,GAAAD,UAAA;IAAEE,UAAU,GAAAF,UAAA;EAO1B,IAAMG,UAAU,GAAG,SAAbA,UAAUA,CAAIP,EAAE,EAAK;IACvB,IAAMQ,cAAc,GAAGH,OAAO,CAACI,GAAG,CAAC,UAACC,MAAM,EAAK;MAC3C,IAAIA,MAAM,CAACV,EAAE,KAAKA,EAAE,EAAE;QAClB,OAAAW,aAAA,CAAAA,aAAA,KAAYD,MAAM;UAAER,KAAK,EAAEQ,MAAM,CAACR,KAAK,GAAG;QAAC;MAC/C;MACA,OAAOQ,MAAM;IACjB,CAAC,CAAC;IACFJ,UAAU,CAACE,cAAc,CAAC;EAC9B,CAAC;EAGD,IAAMI,aAAa,GAAG,SAAhBA,aAAaA,CAAIZ,EAAE,EAAK;IAC1BxB,KAAK,CAACqC,KAAK,CAAC,UAAU,EAAE,+BAA+B,CAAC;EAC5D,CAAC;EAGD,OACI3B,KAAA,CAACN,OAAO;IAAAkC,QAAA,GACJ9B,IAAA,CAACL,SAAS;MAAiBsB,IAAI,EAAC;IAAqB,GAAtC,WAAwC,CAAC,EAExDf,KAAA,CAACd,IAAI;MAAC2C,KAAK,EAAE;QAAEC,OAAO,EAAE;MAAG,CAAE;MAAAF,QAAA,GACzB9B,IAAA,CAACX,IAAI;QAAC0C,KAAK,EAAE;UAAEE,KAAK,EAAE,MAAM;UAAEC,QAAQ,EAAE,EAAE;UAAEC,YAAY,EAAE,EAAE;UAAEC,UAAU,EAAE;QAAoB,CAAE;QAAAN,QAAA,EAAC;MAEjG,CAAM,CAAC,EAGP9B,IAAA,CAACT,QAAQ;QACL8C,IAAI,EAAEhB,OAAQ;QACdiB,YAAY,EAAE,SAAdA,YAAYA,CAAGC,IAAI;UAAA,OAAKA,IAAI,CAACvB,EAAE;QAAA,CAAC;QAChCwB,UAAU,EAAE,SAAZA,UAAUA,CAAAC,KAAA;UAAA,IAAKF,IAAI,GAAAE,KAAA,CAAJF,IAAI;UAAA,OACfrC,KAAA,CAACd,IAAI;YAAC2C,KAAK,EAAE;cACTW,eAAe,EAAE,SAAS;cAC1BV,OAAO,EAAE,EAAE;cACXG,YAAY,EAAE,EAAE;cAChBQ,YAAY,EAAE,CAAC;cACfC,eAAe,EAAE,CAAC;cAClBC,eAAe,EAAE;YACrB,CAAE;YAAAf,QAAA,GACE9B,IAAA,CAACX,IAAI;cAAC0C,KAAK,EAAE;gBAAEE,KAAK,EAAE,MAAM;gBAAEC,QAAQ,EAAE,EAAE;gBAAEC,YAAY,EAAE;cAAE,CAAE;cAAAL,QAAA,EAAES,IAAI,CAACtB;YAAI,CAAO,CAAC,EACjFf,KAAA,CAACd,IAAI;cAAC2C,KAAK,EAAE;gBAAEe,aAAa,EAAE,KAAK;gBAAEC,cAAc,EAAE;cAAgB,CAAE;cAAAjB,QAAA,GACnE5B,KAAA,CAACZ,gBAAgB;gBACb0D,OAAO,EAAE,SAATA,OAAOA,CAAA;kBAAA,OAAQzB,UAAU,CAACgB,IAAI,CAACvB,EAAE,CAAC;gBAAA,CAAC;gBACnCe,KAAK,EAAE;kBAAEe,aAAa,EAAE,KAAK;kBAAEG,UAAU,EAAE;gBAAS,CAAE;gBAAAnB,QAAA,GAEtD9B,IAAA,CAACX,IAAI;kBAAC0C,KAAK,EAAE;oBAAEE,KAAK,EAAE,SAAS;oBAAEiB,WAAW,EAAE;kBAAE,CAAE;kBAAApB,QAAA,EAAC;gBAAE,CAAM,CAAC,EAC5D9B,IAAA,CAACX,IAAI;kBAAC0C,KAAK,EAAE;oBAAEE,KAAK,EAAE;kBAAU,CAAE;kBAAAH,QAAA,EAAES,IAAI,CAACrB;gBAAK,CAAO,CAAC;cAAA,CACxC,CAAC,EACnBhB,KAAA,CAACZ,gBAAgB;gBACb0D,OAAO,EAAE,SAATA,OAAOA,CAAA;kBAAA,OAAQpB,aAAa,CAACW,IAAI,CAACvB,EAAE,CAAC;gBAAA,CAAC;gBACtCe,KAAK,EAAE;kBAAEe,aAAa,EAAE,KAAK;kBAAEG,UAAU,EAAE;gBAAS,CAAE;gBAAAnB,QAAA,GAEtD9B,IAAA,CAACX,IAAI;kBAAC0C,KAAK,EAAE;oBAAEE,KAAK,EAAE,SAAS;oBAAEiB,WAAW,EAAE;kBAAE,CAAE;kBAAApB,QAAA,EAAC;gBAAE,CAAM,CAAC,EAC5D9B,IAAA,CAACX,IAAI;kBAAC0C,KAAK,EAAE;oBAAEE,KAAK,EAAE;kBAAU,CAAE;kBAAAH,QAAA,EAAES,IAAI,CAACpB;gBAAQ,CAAO,CAAC;cAAA,CAC3C,CAAC;YAAA,CACjB,CAAC;UAAA,CACL,CAAC;QAAA;MACT,CACL,CAAC,EAEFjB,KAAA,CAACd,IAAI;QAAC2C,KAAK,EAAE;UAAEoB,SAAS,EAAE;QAAG,CAAE;QAAArB,QAAA,GAC3B9B,IAAA,CAACP,MAAM;UACH2D,IAAI,EAAC,WAAW;UAChBJ,OAAO,EAAE,SAATA,OAAOA,CAAA;YAAA,OAAQ3C,UAAU,oBAAVA,UAAU,CAAEgD,QAAQ,CAAC,UAAU,CAAC;UAAA,CAAC;UAChDtB,KAAK,EAAE;YAAEI,YAAY,EAAE;UAAG,CAAE;UAAAL,QAAA,EAC/B;QAED,CAAQ,CAAC,EACT9B,IAAA,CAACP,MAAM;UACH2D,IAAI,EAAC,UAAU;UACfJ,OAAO,EAAE,SAATA,OAAOA,CAAA;YAAA,OAAQ3C,UAAU,oBAAVA,UAAU,CAAEgD,QAAQ,CAAC,SAAS,CAAC;UAAA,CAAC;UAAAvB,QAAA,EAClD;QAED,CAAQ,CAAC;MAAA,CACP,CAAC;IAAA,CACL,CAAC;EAAA,CACF,CAAC;AAElB,CAAC;AAED,eAAe3B,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}