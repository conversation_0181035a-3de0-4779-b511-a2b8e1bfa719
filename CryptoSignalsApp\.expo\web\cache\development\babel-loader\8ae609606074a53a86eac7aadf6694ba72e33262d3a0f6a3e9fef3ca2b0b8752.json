{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport React, { useState } from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport TouchableOpacity from \"react-native-web/dist/exports/TouchableOpacity\";\nimport Switch from \"react-native-web/dist/exports/Switch\";\nimport ScrollView from \"react-native-web/dist/exports/ScrollView\";\nimport Linking from \"react-native-web/dist/exports/Linking\";\nimport Alert from \"react-native-web/dist/exports/Alert\";\nimport styles from \"./styles\";\nimport { Button } from 'react-native-paper';\nimport PageTitle from \"../../components/PageTitle\";\nimport Wrapper from \"../../components/Wrapper\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar Settings = function Settings() {\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    isDarkMode = _useState2[0],\n    setIsDarkMode = _useState2[1];\n  var _useState3 = useState(true),\n    _useState4 = _slicedToArray(_useState3, 2),\n    notificationsEnabled = _useState4[0],\n    setNotificationsEnabled = _useState4[1];\n  var _useState5 = useState(false),\n    _useState6 = _slicedToArray(_useState5, 2),\n    twoFactorAuth = _useState6[0],\n    setTwoFactorAuth = _useState6[1];\n  var _useState7 = useState(false),\n    _useState8 = _slicedToArray(_useState7, 2),\n    dataEncryption = _useState8[0],\n    setDataEncryption = _useState8[1];\n  var toggleTheme = function toggleTheme() {\n    return setIsDarkMode(function (prev) {\n      return !prev;\n    });\n  };\n  var toggleNotifications = function toggleNotifications() {\n    return setNotificationsEnabled(function (prev) {\n      return !prev;\n    });\n  };\n  var toggleTwoFactorAuth = function toggleTwoFactorAuth() {\n    return setTwoFactorAuth(function (prev) {\n      return !prev;\n    });\n  };\n  var toggleDataEncryption = function toggleDataEncryption() {\n    return setDataEncryption(function (prev) {\n      return !prev;\n    });\n  };\n  var handleCancelSubscription = function handleCancelSubscription() {\n    Alert.alert(\"Cancel Subscription\", \"This feature is not available in demo mode\");\n  };\n  return _jsxs(Wrapper, {\n    children: [_jsx(PageTitle, {\n      text: \"Settings\"\n    }), _jsxs(ScrollView, {\n      style: {\n        padding: 16\n      },\n      children: [_jsxs(View, {\n        style: {\n          flexDirection: 'row',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          paddingVertical: 16,\n          borderBottomWidth: 1,\n          borderBottomColor: '#333'\n        },\n        children: [_jsx(Text, {\n          style: {\n            color: '#fff',\n            fontSize: 16\n          },\n          children: \"Dark Mode\"\n        }), _jsx(Switch, {\n          value: isDarkMode,\n          onValueChange: toggleTheme,\n          thumbColor: isDarkMode ? '#FECB37' : '#ccc'\n        })]\n      }), _jsxs(View, {\n        style: {\n          flexDirection: 'row',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          paddingVertical: 16,\n          borderBottomWidth: 1,\n          borderBottomColor: '#333'\n        },\n        children: [_jsx(Text, {\n          style: {\n            color: '#fff',\n            fontSize: 16\n          },\n          children: \"Notifications\"\n        }), _jsx(Switch, {\n          value: notificationsEnabled,\n          onValueChange: toggleNotifications,\n          thumbColor: notificationsEnabled ? '#FECB37' : '#ccc'\n        })]\n      }), _jsx(Text, {\n        style: {\n          color: '#fff',\n          fontSize: 18,\n          marginTop: 20,\n          marginBottom: 16,\n          fontFamily: 'Poppins_500Medium'\n        },\n        children: \"Security & Privacy\"\n      }), _jsxs(View, {\n        style: {\n          flexDirection: 'row',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          paddingVertical: 16,\n          borderBottomWidth: 1,\n          borderBottomColor: '#333'\n        },\n        children: [_jsx(Text, {\n          style: {\n            color: '#fff',\n            fontSize: 16\n          },\n          children: \"Two-Factor Authentication\"\n        }), _jsx(Switch, {\n          value: twoFactorAuth,\n          onValueChange: toggleTwoFactorAuth,\n          thumbColor: twoFactorAuth ? '#FECB37' : '#ccc'\n        })]\n      }), _jsxs(View, {\n        style: {\n          flexDirection: 'row',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          paddingVertical: 16,\n          borderBottomWidth: 1,\n          borderBottomColor: '#333'\n        },\n        children: [_jsx(Text, {\n          style: {\n            color: '#fff',\n            fontSize: 16\n          },\n          children: \"Data Encryption\"\n        }), _jsx(Switch, {\n          value: dataEncryption,\n          onValueChange: toggleDataEncryption,\n          thumbColor: dataEncryption ? '#FECB37' : '#ccc'\n        })]\n      }), _jsx(TouchableOpacity, {\n        style: {\n          paddingVertical: 16,\n          borderBottomWidth: 1,\n          borderBottomColor: '#333'\n        },\n        onPress: function onPress() {\n          return Alert.alert(\"Privacy Policy\", \"Privacy policy coming soon\");\n        },\n        children: _jsx(Text, {\n          style: {\n            color: '#fff',\n            fontSize: 16\n          },\n          children: \"Privacy Policy\"\n        })\n      }), _jsx(TouchableOpacity, {\n        style: {\n          paddingVertical: 16,\n          borderBottomWidth: 1,\n          borderBottomColor: '#333'\n        },\n        onPress: function onPress() {\n          return Alert.alert(\"Terms of Service\", \"Terms of service coming soon\");\n        },\n        children: _jsx(Text, {\n          style: {\n            color: '#fff',\n            fontSize: 16\n          },\n          children: \"Terms of Service\"\n        })\n      }), _jsx(View, {\n        style: {\n          marginTop: 30\n        },\n        children: _jsx(Button, {\n          mode: \"outlined\",\n          onPress: handleCancelSubscription,\n          textColor: \"#ff4444\",\n          style: {\n            borderColor: '#ff4444'\n          },\n          children: \"Cancel Subscription\"\n        })\n      })]\n    })]\n  });\n};\nexport default Settings;", "map": {"version": 3, "names": ["React", "useState", "View", "Text", "TouchableOpacity", "Switch", "ScrollView", "Linking", "<PERSON><PERSON>", "styles", "<PERSON><PERSON>", "Page<PERSON><PERSON>le", "Wrapper", "jsx", "_jsx", "jsxs", "_jsxs", "Settings", "_useState", "_useState2", "_slicedToArray", "isDarkMode", "setIsDarkMode", "_useState3", "_useState4", "notificationsEnabled", "setNotificationsEnabled", "_useState5", "_useState6", "twoFactorAuth", "setTwoFactorAuth", "_useState7", "_useState8", "dataEncryption", "setDataEncryption", "toggleTheme", "prev", "toggleNotifications", "toggleTwoFactorAuth", "toggleDataEncryption", "handleCancelSubscription", "alert", "children", "text", "style", "padding", "flexDirection", "justifyContent", "alignItems", "paddingVertical", "borderBottomWidth", "borderBottomColor", "color", "fontSize", "value", "onValueChange", "thumbColor", "marginTop", "marginBottom", "fontFamily", "onPress", "mode", "textColor", "borderColor"], "sources": ["E:/CryptoSignalsApp/src/pages/Settings/index.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { View, Text, TouchableOpacity, Switch, ScrollView, Linking, Alert } from 'react-native';\r\nimport styles from './styles';\r\nimport { Button } from 'react-native-paper';\r\nimport PageTitle from '../../components/PageTitle';\r\nimport Wrapper from '../../components/Wrapper';\r\n\r\nconst Settings = () => {\r\n  const [isDarkMode, setIsDarkMode] = useState(false);\r\n  const [notificationsEnabled, setNotificationsEnabled] = useState(true);\r\n  const [twoFactorAuth, setTwoFactorAuth] = useState(false);\r\n  const [dataEncryption, setDataEncryption] = useState(false);\r\n\r\n  // Funções para alternar configurações\r\n  const toggleTheme = () => setIsDarkMode(prev => !prev);\r\n  const toggleNotifications = () => setNotificationsEnabled(prev => !prev);\r\n  const toggleTwoFactorAuth = () => setTwoFactorAuth(prev => !prev);\r\n  const toggleDataEncryption = () => setDataEncryption(prev => !prev);\r\n\r\n  // Função para cancelar a assinatura\r\n  const handleCancelSubscription = () => {\r\n    Alert.alert(\"Cancel Subscription\", \"This feature is not available in demo mode\");\r\n  };\r\n\r\n  return (\r\n    <Wrapper>\r\n      <PageTitle text=\"Settings\" />\r\n      <ScrollView style={{ padding: 16 }}>\r\n        {/* Configuração de Modo Escuro */}\r\n        <View style={{\r\n          flexDirection: 'row',\r\n          justifyContent: 'space-between',\r\n          alignItems: 'center',\r\n          paddingVertical: 16,\r\n          borderBottomWidth: 1,\r\n          borderBottomColor: '#333'\r\n        }}>\r\n          <Text style={{ color: '#fff', fontSize: 16 }}>Dark Mode</Text>\r\n          <Switch\r\n            value={isDarkMode}\r\n            onValueChange={toggleTheme}\r\n            thumbColor={isDarkMode ? '#FECB37' : '#ccc'}\r\n          />\r\n        </View>\r\n\r\n        {/* Notificações */}\r\n        <View style={{\r\n          flexDirection: 'row',\r\n          justifyContent: 'space-between',\r\n          alignItems: 'center',\r\n          paddingVertical: 16,\r\n          borderBottomWidth: 1,\r\n          borderBottomColor: '#333'\r\n        }}>\r\n          <Text style={{ color: '#fff', fontSize: 16 }}>Notifications</Text>\r\n          <Switch\r\n            value={notificationsEnabled}\r\n            onValueChange={toggleNotifications}\r\n            thumbColor={notificationsEnabled ? '#FECB37' : '#ccc'}\r\n          />\r\n        </View>\r\n\r\n        {/* Seção de Segurança e Privacidade */}\r\n        <Text style={{ color: '#fff', fontSize: 18, marginTop: 20, marginBottom: 16, fontFamily: 'Poppins_500Medium' }}>\r\n          Security & Privacy\r\n        </Text>\r\n\r\n        <View style={{\r\n          flexDirection: 'row',\r\n          justifyContent: 'space-between',\r\n          alignItems: 'center',\r\n          paddingVertical: 16,\r\n          borderBottomWidth: 1,\r\n          borderBottomColor: '#333'\r\n        }}>\r\n          <Text style={{ color: '#fff', fontSize: 16 }}>Two-Factor Authentication</Text>\r\n          <Switch\r\n            value={twoFactorAuth}\r\n            onValueChange={toggleTwoFactorAuth}\r\n            thumbColor={twoFactorAuth ? '#FECB37' : '#ccc'}\r\n          />\r\n        </View>\r\n\r\n        <View style={{\r\n          flexDirection: 'row',\r\n          justifyContent: 'space-between',\r\n          alignItems: 'center',\r\n          paddingVertical: 16,\r\n          borderBottomWidth: 1,\r\n          borderBottomColor: '#333'\r\n        }}>\r\n          <Text style={{ color: '#fff', fontSize: 16 }}>Data Encryption</Text>\r\n          <Switch\r\n            value={dataEncryption}\r\n            onValueChange={toggleDataEncryption}\r\n            thumbColor={dataEncryption ? '#FECB37' : '#ccc'}\r\n          />\r\n        </View>\r\n\r\n        <TouchableOpacity\r\n          style={{ paddingVertical: 16, borderBottomWidth: 1, borderBottomColor: '#333' }}\r\n          onPress={() => Alert.alert(\"Privacy Policy\", \"Privacy policy coming soon\")}\r\n        >\r\n          <Text style={{ color: '#fff', fontSize: 16 }}>Privacy Policy</Text>\r\n        </TouchableOpacity>\r\n\r\n        <TouchableOpacity\r\n          style={{ paddingVertical: 16, borderBottomWidth: 1, borderBottomColor: '#333' }}\r\n          onPress={() => Alert.alert(\"Terms of Service\", \"Terms of service coming soon\")}\r\n        >\r\n          <Text style={{ color: '#fff', fontSize: 16 }}>Terms of Service</Text>\r\n        </TouchableOpacity>\r\n\r\n        {/* Botão de cancelamento de assinatura */}\r\n        <View style={{ marginTop: 30 }}>\r\n          <Button\r\n            mode=\"outlined\"\r\n            onPress={handleCancelSubscription}\r\n            textColor=\"#ff4444\"\r\n            style={{ borderColor: '#ff4444' }}\r\n          >\r\n            Cancel Subscription\r\n          </Button>\r\n        </View>\r\n      </ScrollView>\r\n    </Wrapper>\r\n  );\r\n};\r\n\r\nexport default Settings;\r\n\r\n/*\r\nFoi adicionado um Switch para alternar entre os modos escuro e claro.\r\nInformações irrelevantes para a página de configurações foram removidas.\r\nA estrutura do código foi simplificada para torná-lo mais legível e conciso.\r\nCom essas alterações, a página Settings agora fornece ao usuário a capacidade de ajustar\r\n suas preferências, incluindo alternar entre modos de tema, definir preferências de privacidade e\r\n cancelar assinaturas.\r\n\r\n Neste código, adicionei a seção \"Segurança e Privacidade\", incluindo opções para Autenticação de\r\n dois fatores e Criptografia de dados (como switches) e links para a Política de privacidade e Termos\r\n de serviço.\r\n\r\nAs URLs 'https://www.seusite.com/politica-de-privacidade' e\r\n'https://www.seusite.com/termos-de-servico' são exemplos, e você deve substituí-las pelas\r\nURLs reais quando as páginas forem criadas.\r\n\r\n */"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,gBAAA;AAAA,OAAAC,MAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,OAAA;AAAA,OAAAC,KAAA;AAExC,OAAOC,MAAM;AACb,SAASC,MAAM,QAAQ,oBAAoB;AAC3C,OAAOC,SAAS;AAChB,OAAOC,OAAO;AAAiC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAE/C,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAA,EAAS;EACrB,IAAAC,SAAA,GAAoCjB,QAAQ,CAAC,KAAK,CAAC;IAAAkB,UAAA,GAAAC,cAAA,CAAAF,SAAA;IAA5CG,UAAU,GAAAF,UAAA;IAAEG,aAAa,GAAAH,UAAA;EAChC,IAAAI,UAAA,GAAwDtB,QAAQ,CAAC,IAAI,CAAC;IAAAuB,UAAA,GAAAJ,cAAA,CAAAG,UAAA;IAA/DE,oBAAoB,GAAAD,UAAA;IAAEE,uBAAuB,GAAAF,UAAA;EACpD,IAAAG,UAAA,GAA0C1B,QAAQ,CAAC,KAAK,CAAC;IAAA2B,UAAA,GAAAR,cAAA,CAAAO,UAAA;IAAlDE,aAAa,GAAAD,UAAA;IAAEE,gBAAgB,GAAAF,UAAA;EACtC,IAAAG,UAAA,GAA4C9B,QAAQ,CAAC,KAAK,CAAC;IAAA+B,UAAA,GAAAZ,cAAA,CAAAW,UAAA;IAApDE,cAAc,GAAAD,UAAA;IAAEE,iBAAiB,GAAAF,UAAA;EAGxC,IAAMG,WAAW,GAAG,SAAdA,WAAWA,CAAA;IAAA,OAASb,aAAa,CAAC,UAAAc,IAAI;MAAA,OAAI,CAACA,IAAI;IAAA,EAAC;EAAA;EACtD,IAAMC,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAA;IAAA,OAASX,uBAAuB,CAAC,UAAAU,IAAI;MAAA,OAAI,CAACA,IAAI;IAAA,EAAC;EAAA;EACxE,IAAME,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAA;IAAA,OAASR,gBAAgB,CAAC,UAAAM,IAAI;MAAA,OAAI,CAACA,IAAI;IAAA,EAAC;EAAA;EACjE,IAAMG,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAA;IAAA,OAASL,iBAAiB,CAAC,UAAAE,IAAI;MAAA,OAAI,CAACA,IAAI;IAAA,EAAC;EAAA;EAGnE,IAAMI,wBAAwB,GAAG,SAA3BA,wBAAwBA,CAAA,EAAS;IACrChC,KAAK,CAACiC,KAAK,CAAC,qBAAqB,EAAE,4CAA4C,CAAC;EAClF,CAAC;EAED,OACEzB,KAAA,CAACJ,OAAO;IAAA8B,QAAA,GACN5B,IAAA,CAACH,SAAS;MAACgC,IAAI,EAAC;IAAU,CAAE,CAAC,EAC7B3B,KAAA,CAACV,UAAU;MAACsC,KAAK,EAAE;QAAEC,OAAO,EAAE;MAAG,CAAE;MAAAH,QAAA,GAEjC1B,KAAA,CAACd,IAAI;QAAC0C,KAAK,EAAE;UACXE,aAAa,EAAE,KAAK;UACpBC,cAAc,EAAE,eAAe;UAC/BC,UAAU,EAAE,QAAQ;UACpBC,eAAe,EAAE,EAAE;UACnBC,iBAAiB,EAAE,CAAC;UACpBC,iBAAiB,EAAE;QACrB,CAAE;QAAAT,QAAA,GACA5B,IAAA,CAACX,IAAI;UAACyC,KAAK,EAAE;YAAEQ,KAAK,EAAE,MAAM;YAAEC,QAAQ,EAAE;UAAG,CAAE;UAAAX,QAAA,EAAC;QAAS,CAAM,CAAC,EAC9D5B,IAAA,CAACT,MAAM;UACLiD,KAAK,EAAEjC,UAAW;UAClBkC,aAAa,EAAEpB,WAAY;UAC3BqB,UAAU,EAAEnC,UAAU,GAAG,SAAS,GAAG;QAAO,CAC7C,CAAC;MAAA,CACE,CAAC,EAGPL,KAAA,CAACd,IAAI;QAAC0C,KAAK,EAAE;UACXE,aAAa,EAAE,KAAK;UACpBC,cAAc,EAAE,eAAe;UAC/BC,UAAU,EAAE,QAAQ;UACpBC,eAAe,EAAE,EAAE;UACnBC,iBAAiB,EAAE,CAAC;UACpBC,iBAAiB,EAAE;QACrB,CAAE;QAAAT,QAAA,GACA5B,IAAA,CAACX,IAAI;UAACyC,KAAK,EAAE;YAAEQ,KAAK,EAAE,MAAM;YAAEC,QAAQ,EAAE;UAAG,CAAE;UAAAX,QAAA,EAAC;QAAa,CAAM,CAAC,EAClE5B,IAAA,CAACT,MAAM;UACLiD,KAAK,EAAE7B,oBAAqB;UAC5B8B,aAAa,EAAElB,mBAAoB;UACnCmB,UAAU,EAAE/B,oBAAoB,GAAG,SAAS,GAAG;QAAO,CACvD,CAAC;MAAA,CACE,CAAC,EAGPX,IAAA,CAACX,IAAI;QAACyC,KAAK,EAAE;UAAEQ,KAAK,EAAE,MAAM;UAAEC,QAAQ,EAAE,EAAE;UAAEI,SAAS,EAAE,EAAE;UAAEC,YAAY,EAAE,EAAE;UAAEC,UAAU,EAAE;QAAoB,CAAE;QAAAjB,QAAA,EAAC;MAEhH,CAAM,CAAC,EAEP1B,KAAA,CAACd,IAAI;QAAC0C,KAAK,EAAE;UACXE,aAAa,EAAE,KAAK;UACpBC,cAAc,EAAE,eAAe;UAC/BC,UAAU,EAAE,QAAQ;UACpBC,eAAe,EAAE,EAAE;UACnBC,iBAAiB,EAAE,CAAC;UACpBC,iBAAiB,EAAE;QACrB,CAAE;QAAAT,QAAA,GACA5B,IAAA,CAACX,IAAI;UAACyC,KAAK,EAAE;YAAEQ,KAAK,EAAE,MAAM;YAAEC,QAAQ,EAAE;UAAG,CAAE;UAAAX,QAAA,EAAC;QAAyB,CAAM,CAAC,EAC9E5B,IAAA,CAACT,MAAM;UACLiD,KAAK,EAAEzB,aAAc;UACrB0B,aAAa,EAAEjB,mBAAoB;UACnCkB,UAAU,EAAE3B,aAAa,GAAG,SAAS,GAAG;QAAO,CAChD,CAAC;MAAA,CACE,CAAC,EAEPb,KAAA,CAACd,IAAI;QAAC0C,KAAK,EAAE;UACXE,aAAa,EAAE,KAAK;UACpBC,cAAc,EAAE,eAAe;UAC/BC,UAAU,EAAE,QAAQ;UACpBC,eAAe,EAAE,EAAE;UACnBC,iBAAiB,EAAE,CAAC;UACpBC,iBAAiB,EAAE;QACrB,CAAE;QAAAT,QAAA,GACA5B,IAAA,CAACX,IAAI;UAACyC,KAAK,EAAE;YAAEQ,KAAK,EAAE,MAAM;YAAEC,QAAQ,EAAE;UAAG,CAAE;UAAAX,QAAA,EAAC;QAAe,CAAM,CAAC,EACpE5B,IAAA,CAACT,MAAM;UACLiD,KAAK,EAAErB,cAAe;UACtBsB,aAAa,EAAEhB,oBAAqB;UACpCiB,UAAU,EAAEvB,cAAc,GAAG,SAAS,GAAG;QAAO,CACjD,CAAC;MAAA,CACE,CAAC,EAEPnB,IAAA,CAACV,gBAAgB;QACfwC,KAAK,EAAE;UAAEK,eAAe,EAAE,EAAE;UAAEC,iBAAiB,EAAE,CAAC;UAAEC,iBAAiB,EAAE;QAAO,CAAE;QAChFS,OAAO,EAAE,SAATA,OAAOA,CAAA;UAAA,OAAQpD,KAAK,CAACiC,KAAK,CAAC,gBAAgB,EAAE,4BAA4B,CAAC;QAAA,CAAC;QAAAC,QAAA,EAE3E5B,IAAA,CAACX,IAAI;UAACyC,KAAK,EAAE;YAAEQ,KAAK,EAAE,MAAM;YAAEC,QAAQ,EAAE;UAAG,CAAE;UAAAX,QAAA,EAAC;QAAc,CAAM;MAAC,CACnD,CAAC,EAEnB5B,IAAA,CAACV,gBAAgB;QACfwC,KAAK,EAAE;UAAEK,eAAe,EAAE,EAAE;UAAEC,iBAAiB,EAAE,CAAC;UAAEC,iBAAiB,EAAE;QAAO,CAAE;QAChFS,OAAO,EAAE,SAATA,OAAOA,CAAA;UAAA,OAAQpD,KAAK,CAACiC,KAAK,CAAC,kBAAkB,EAAE,8BAA8B,CAAC;QAAA,CAAC;QAAAC,QAAA,EAE/E5B,IAAA,CAACX,IAAI;UAACyC,KAAK,EAAE;YAAEQ,KAAK,EAAE,MAAM;YAAEC,QAAQ,EAAE;UAAG,CAAE;UAAAX,QAAA,EAAC;QAAgB,CAAM;MAAC,CACrD,CAAC,EAGnB5B,IAAA,CAACZ,IAAI;QAAC0C,KAAK,EAAE;UAAEa,SAAS,EAAE;QAAG,CAAE;QAAAf,QAAA,EAC7B5B,IAAA,CAACJ,MAAM;UACLmD,IAAI,EAAC,UAAU;UACfD,OAAO,EAAEpB,wBAAyB;UAClCsB,SAAS,EAAC,SAAS;UACnBlB,KAAK,EAAE;YAAEmB,WAAW,EAAE;UAAU,CAAE;UAAArB,QAAA,EACnC;QAED,CAAQ;MAAC,CACL,CAAC;IAAA,CACG,CAAC;EAAA,CACN,CAAC;AAEd,CAAC;AAED,eAAezB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}