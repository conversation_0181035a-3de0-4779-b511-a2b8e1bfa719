import logging
import pandas as pd
import numpy as np
from config.settings import LEVERAGE

logger = logging.getLogger(__name__)

class InsideBarStrategy:
    def __init__(self, binance_handler):
        self.binance = binance_handler
        self.leverage = LEVERAGE

    def analyze_symbol(self, symbol, interval="15m"):
        """
        Analisa um símbolo para verificar se há padrão Inside Bar + 1-3

        Returns:
            tuple: (signal_type, entry_price, stop_loss, take_profit) ou (None, None, None, None) se não houver sinal
        """
        try:
            # Verificar padrão Inside Bar
            pattern_detected, breakout_level, stop_loss, take_profit = self._detect_inside_bar_pattern(symbol, interval)

            if not pattern_detected:
                return None, None, None, None

            # Verificar se o preço atual está acima do nível de breakout
            current_price = self.binance.get_current_price(symbol)
            if current_price is None:
                logger.warning(f"Não foi possível obter preço atual para {symbol}")
                return None, None, None, None

            # Validar se o preço atual é realista
            if not self._validate_price_range(symbol, current_price):
                logger.error(f"Preço atual irrealista para {symbol}: {current_price}")
                return None, None, None, None

            # Verificar se realmente há breakout (preço deve estar pelo menos 0.1% acima do nível)
            breakout_threshold = breakout_level * 1.001  # 0.1% acima do breakout
            if current_price <= breakout_threshold:
                return None, None, None, None

            # Definir signal_type como LONG (padrão para Inside Bar)
            signal_type = "LONG"

            # Validar se os cálculos fazem sentido
            if not self._validate_signal_values(symbol, signal_type, current_price, stop_loss, take_profit):
                return None, None, None, None

            logger.info(f"{symbol} - Padrão Inside Bar detectado. Entrada: {current_price}, SL: {stop_loss}, TP: {take_profit}")

            # Garantir que todos os valores são float e válidos
            try:
                return signal_type, float(current_price), float(stop_loss), float(take_profit)
            except (ValueError, TypeError) as e:
                logger.error(f"Erro ao converter valores para float em {symbol}: {e}")
                return None, None, None, None

        except Exception as e:
            logger.error(f"Erro ao analisar {symbol} para padrão Inside Bar: {e}")
            return None, None, None, None

    def _detect_inside_bar_pattern(self, symbol, interval="15m"):
        """
        Verifica se ocorreu o padrão:
          - Candle A: grande candle vermelho (decisão)
          - Candle B: candle verde que forma uma "inside bar" (com máxima e mínima contidos em A)
          - Candle C: candle verde de breakout, que fecha acima da máxima de A
        """
        try:
            # Obter os últimos 3 candles
            df = self.binance.get_historical_klines(
                symbol=symbol,
                interval=interval,
                lookback_days=1
            )

            if df.empty or len(df) < 3:
                return False, None, None, None

            # Extrair os últimos 3 candles
            candles = df.iloc[-3:]

            # Candle A (primeiro candle - decisão)
            open_A = candles['open'].iloc[0]
            high_A = candles['high'].iloc[0]
            low_A = candles['low'].iloc[0]
            close_A = candles['close'].iloc[0]

            # Candle B (segundo candle - inside bar)
            open_B = candles['open'].iloc[1]
            high_B = candles['high'].iloc[1]
            low_B = candles['low'].iloc[1]
            close_B = candles['close'].iloc[1]

            # Candle C (terceiro candle - breakout)
            open_C = candles['open'].iloc[2]
            high_C = candles['high'].iloc[2]
            low_C = candles['low'].iloc[2]
            close_C = candles['close'].iloc[2]

            # Verificar condições do padrão
            cond_A = open_A > close_A  # Candle A é vermelho
            cond_B1 = open_B < close_B  # Candle B é verde
            cond_B2 = high_B < high_A and low_B > low_A  # B está contido em A
            cond_C = open_C < close_C and close_C > high_A  # C é verde e fecha acima da máxima de A

            pattern_detected = cond_A and cond_B1 and cond_B2 and cond_C

            if pattern_detected:
                # Nível de breakout é a máxima do candle A
                breakout_level = high_A

                # Stop loss abaixo da mínima do candle B
                stop_loss = low_B * 0.998  # Ligeiramente abaixo

                # Validar se o stop loss faz sentido
                if stop_loss >= breakout_level:
                    logger.error(f"Stop loss inválido para {symbol}: {stop_loss} >= {breakout_level}")
                    return False, None, None, None

                # Take profit com relação risco:recompensa de 1:2 (mais conservador)
                risk = breakout_level - stop_loss
                if risk <= 0:
                    logger.error(f"Risco inválido para {symbol}: {risk}")
                    return False, None, None, None

                take_profit = breakout_level + (2 * risk)  # Mudado de 3x para 2x

                # Validar se o take profit não é muito agressivo (máximo 15% acima do breakout)
                max_tp = breakout_level * 1.15
                if take_profit > max_tp:
                    take_profit = max_tp
                    logger.warning(f"Take profit ajustado para {symbol}: {take_profit} (máximo 15%)")

                logger.info(f"{symbol} - Padrão Inside Bar detectado. Breakout: {breakout_level}, SL: {stop_loss}, TP: {take_profit}")
                return True, breakout_level, stop_loss, take_profit

            return False, None, None, None

        except Exception as e:
            logger.error(f"Erro ao detectar padrão Inside Bar para {symbol}: {e}")
            return False, None, None, None

    def _validate_price_range(self, symbol, price):
        """Valida se um preço está dentro de uma faixa realista para o símbolo"""
        if price <= 0:
            return False

        # Faixas de preços realistas (com margem de segurança)
        price_ranges = {
            'BTC': (10000, 150000),
            'ETH': (500, 10000),
            'BNB': (50, 1000),
            'ADA': (0.1, 5),
            'SOL': (10, 1000),
            'DOT': (1, 50),
            'LINK': (1, 100),
            'MATIC': (0.1, 10),
            'AVAX': (5, 200),
            'ATOM': (1, 100)
        }

        for coin, (min_price, max_price) in price_ranges.items():
            if coin in symbol:
                return min_price <= price <= max_price

        # Para moedas não mapeadas, aceitar uma faixa ampla
        return 0.001 <= price <= 100000

    def _validate_signal_values(self, symbol, signal_type, entry_price, stop_loss, take_profit):
        """Valida se os valores do sinal fazem sentido"""
        try:
            # Validar se todos os valores são positivos
            if entry_price <= 0 or stop_loss <= 0 or take_profit <= 0:
                logger.error(f"Valores inválidos para {symbol}: entry={entry_price}, sl={stop_loss}, tp={take_profit}")
                return False

            # Validar lógica do stop loss (sempre LONG para Inside Bar)
            if stop_loss >= entry_price:
                logger.error(f"Stop loss inválido para {symbol}: {stop_loss} >= {entry_price}")
                return False

            # Validar lógica do take profit (sempre LONG para Inside Bar)
            if take_profit <= entry_price:
                logger.error(f"Take profit inválido para {symbol}: {take_profit} <= {entry_price}")
                return False

            # Validar se o take profit não é muito agressivo (máximo 15% para Inside Bar)
            max_tp = entry_price * 1.15
            if take_profit > max_tp:
                logger.warning(f"Take profit muito agressivo para {symbol}, ajustando de {take_profit} para {max_tp}")
                return False

            return True

        except Exception as e:
            logger.error(f"Erro ao validar valores do sinal para {symbol}: {e}")
            return False