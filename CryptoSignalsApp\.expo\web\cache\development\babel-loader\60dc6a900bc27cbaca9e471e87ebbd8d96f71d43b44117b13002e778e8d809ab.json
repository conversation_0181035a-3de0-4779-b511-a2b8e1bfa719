{"ast": null, "code": "var I18nManager = {\n  allowRTL: function allowRTL() {\n    return;\n  },\n  forceRTL: function forceRTL() {\n    return;\n  },\n  getConstants: function getConstants() {\n    return {\n      isRTL: false\n    };\n  }\n};\nexport default I18nManager;", "map": {"version": 3, "names": ["I18nManager", "allowRTL", "forceRTL", "getConstants", "isRTL"], "sources": ["C:/Users/<USER>/Desktop/Signals/CryptoSignalsApp/node_modules/react-native-web/dist/exports/I18nManager/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nvar I18nManager = {\n  allowRTL() {\n    return;\n  },\n  forceRTL() {\n    return;\n  },\n  getConstants() {\n    return {\n      isRTL: false\n    };\n  }\n};\nexport default I18nManager;"], "mappings": "AAUA,IAAIA,WAAW,GAAG;EAChBC,QAAQ,WAARA,QAAQA,CAAA,EAAG;IACT;EACF,CAAC;EACDC,QAAQ,WAARA,QAAQA,CAAA,EAAG;IACT;EACF,CAAC;EACDC,YAAY,WAAZA,YAAYA,CAAA,EAAG;IACb,OAAO;MACLC,KAAK,EAAE;IACT,CAAC;EACH;AACF,CAAC;AACD,eAAeJ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}