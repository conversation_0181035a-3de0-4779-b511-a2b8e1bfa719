{"ast": null, "code": "import color from 'color';\nimport { tokens } from \"../../styles/themes/v3/tokens\";\nexport var getToggleButtonColor = function getToggleButtonColor(_ref) {\n  var theme = _ref.theme,\n    checked = _ref.checked;\n  if (checked) {\n    if (theme.isV3) {\n      return color(theme.colors.onSecondaryContainer).alpha(tokens.md.ref.opacity.level2).rgb().string();\n    }\n    if (theme.dark) {\n      return 'rgba(255, 255, 255, .12)';\n    }\n    return 'rgba(0, 0, 0, .08)';\n  }\n  return 'transparent';\n};", "map": {"version": 3, "names": ["color", "tokens", "getToggleButtonColor", "_ref", "theme", "checked", "isV3", "colors", "onSecondaryContainer", "alpha", "md", "ref", "opacity", "level2", "rgb", "string", "dark"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\ToggleButton\\utils.ts"], "sourcesContent": ["import color from 'color';\n\nimport { tokens } from '../../styles/themes/v3/tokens';\nimport type { InternalTheme } from '../../types';\n\nexport const getToggleButtonColor = ({\n  theme,\n  checked,\n}: {\n  theme: InternalTheme;\n  checked: boolean | null;\n}) => {\n  if (checked) {\n    if (theme.isV3) {\n      return color(theme.colors.onSecondaryContainer)\n        .alpha(tokens.md.ref.opacity.level2)\n        .rgb()\n        .string();\n    }\n    if (theme.dark) {\n      return 'rgba(255, 255, 255, .12)';\n    }\n    return 'rgba(0, 0, 0, .08)';\n  }\n  return 'transparent';\n};\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,SAASC,MAAM;AAGf,OAAO,IAAMC,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAAC,IAAA,EAM3B;EAAA,IALJC,KAAK,GAAAD,IAAA,CAALC,KAAK;IACLC,OAAA,GAAAF,IAAA,CAAAE,OAAA;EAKA,IAAIA,OAAO,EAAE;IACX,IAAID,KAAK,CAACE,IAAI,EAAE;MACd,OAAON,KAAK,CAACI,KAAK,CAACG,MAAM,CAACC,oBAAoB,CAAC,CAC5CC,KAAK,CAACR,MAAM,CAACS,EAAE,CAACC,GAAG,CAACC,OAAO,CAACC,MAAM,CAAC,CACnCC,GAAG,CAAC,CAAC,CACLC,MAAM,CAAC,CAAC;IACb;IACA,IAAIX,KAAK,CAACY,IAAI,EAAE;MACd,OAAO,0BAA0B;IACnC;IACA,OAAO,oBAAoB;EAC7B;EACA,OAAO,aAAa;AACtB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}