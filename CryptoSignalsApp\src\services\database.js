// Banco de dados temporário para armazenar informações processadas
const temporaryDatabase = {};

// Funções para operações no banco de dados

/**
 * Salva dados no banco de dados temporário.
 * @param {string} key - A chave sob a qual os dados serão armazenados.
 * @param {any} value - O valor a ser armazenado.
 */
function saveToDatabase(key, value) {
  if (!key) {
    throw new Error('Key must be provided to save data.');
  }
  temporaryDatabase[key] = value;
}

/**
 * Lê dados do banco de dados temporário.
 * @param {string} key - A chave dos dados a serem lidos.
 * @returns {any|null} - Retorna os dados armazenados ou null se a chave não existir.
 */
function readFromDatabase(key) {
  return temporaryDatabase.hasOwnProperty(key) ? temporaryDatabase[key] : null;
}

/**
 * Deleta dados do banco de dados temporário.
 * @param {string} key - A chave dos dados a serem deletados.
 */
function deleteFromDatabase(key) {
  if (temporaryDatabase.hasOwnProperty(key)) {
    delete temporaryDatabase[key];
  }
}

/**
 * Verifica se a chave existe no banco de dados temporário.
 * @param {string} key - A chave a ser verificada.
 * @returns {boolean} - Retorna true se a chave existir, caso contrário false.
 */
function existsInDatabase(key) {
  return temporaryDatabase.hasOwnProperty(key);
}

module.exports = {
  saveToDatabase,
  readFromDatabase,
  deleteFromDatabase,
  existsInDatabase,
};

/*
Adicionadas docstrings para documentar as funções, o que é útil para outros desenvolvedores que possam usar ou modificar seu código no futuro.
Adicionada uma verificação em saveToDatabase para garantir que uma chave seja fornecida.
Modificado readFromDatabase para retornar null se a chave não existir, em vez de undefined.
Adicionada uma função deleteFromDatabase para deletar dados.
Adicionada uma função existsInDatabase para verificar se uma chave existe no banco de dados.
*/