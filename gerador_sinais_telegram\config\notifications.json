{"enabled": true, "email": {"enabled": false, "smtp_server": "smtp.gmail.com", "smtp_port": 587, "email": "", "password": "", "sender_name": "CryptoSignals Professional", "recipients": []}, "push": {"enabled": false, "vapid_private_key": "", "vapid_public_key": "", "vapid_claims": {"sub": "mailto:<EMAIL>"}}, "webhook": {"enabled": false, "webhooks": [{"name": "Discord Example", "type": "discord", "url": "https://discord.com/api/webhooks/YOUR_WEBHOOK_ID/YOUR_WEBHOOK_TOKEN", "enabled": false, "events": {"signals": true, "performance": true, "system": true}}, {"name": "Slack Example", "type": "slack", "url": "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK", "enabled": false, "events": {"signals": true, "performance": false, "system": true}}]}, "rate_limit": {"signals": 60, "performance": 3600, "system": 300}, "filters": {"min_confidence": 70, "strategies": [], "symbols": [], "signal_types": ["LONG", "SHORT"]}}