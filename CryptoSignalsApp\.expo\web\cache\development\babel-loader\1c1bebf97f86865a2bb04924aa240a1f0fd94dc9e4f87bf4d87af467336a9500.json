{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport React, { useState, useEffect, useContext } from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport ScrollView from \"react-native-web/dist/exports/ScrollView\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport Alert from \"react-native-web/dist/exports/Alert\";\nimport { Button, useTheme } from 'react-native-paper';\nimport PageTitle from \"../../components/PageTitle\";\nimport Wrapper from \"../../components/Wrapper\";\nimport CheckButtons from \"../../components/CheckButtons\";\nimport InputSearch from \"../../components/InputSearch\";\nimport Loading from \"../../components/Loading\";\nimport Card from \"./Card\";\nimport { StoreContext } from \"../../store\";\nimport styles from \"./styles\";\nimport { AxiosContext } from \"../../store/axios\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport default function Channels(_ref) {\n  var _ref$route = _ref.route,\n    route = _ref$route === void 0 ? {} : _ref$route;\n  var _useTheme = useTheme(),\n    colors = _useTheme.colors;\n  var _useContext = useContext(StoreContext),\n    _useContext2 = _slicedToArray(_useContext, 2),\n    state = _useContext2[0],\n    _ = _useContext2[1];\n  var _useContext3 = useContext(AxiosContext),\n    _useContext4 = _slicedToArray(_useContext3, 1),\n    api = _useContext4[0];\n  var _useState = useState(true),\n    _useState2 = _slicedToArray(_useState, 2),\n    isLoading = _useState2[0],\n    setIsLoading = _useState2[1];\n  var _useState3 = useState([]),\n    _useState4 = _slicedToArray(_useState3, 2),\n    channels = _useState4[0],\n    setChannels = _useState4[1];\n  var _useState5 = useState([]),\n    _useState6 = _slicedToArray(_useState5, 2),\n    filters = _useState6[0],\n    setFilters = _useState6[1];\n  var _useState7 = useState(null),\n    _useState8 = _slicedToArray(_useState7, 2),\n    inputSearchValue = _useState8[0],\n    setInputSearchValue = _useState8[1];\n  var _ref2 = state || {\n      subscription: {\n        subscriptionStatus: false\n      }\n    },\n    subscriptionStatus = _ref2.subscription.subscriptionStatus;\n  var channelOrderedByNewSignal = state == null ? void 0 : state.channelOrderedByNewSignal;\n  var _ref3 = route.params || {\n      accountHasBeenRecovered: false\n    },\n    accountHasBeenRecovered = _ref3.accountHasBeenRecovered;\n  var getChannelsFiltered = function getChannelsFiltered() {\n    if (!filters.length) {\n      return channels;\n    }\n    if (!!inputSearchValue && filters.includes('search')) {\n      return channels.filter(function (channel) {\n        return channel.description.toLowerCase().includes(inputSearchValue.toLowerCase());\n      });\n    }\n    return channels.filter(function (channel) {\n      if (channel.type === 'SPOT' && filters.includes('spot') || channel.type === 'FUTURES' && filters.includes('futures') || channel.isPremium && filters.includes('premium') || !channel.isPremium && filters.includes('free')) {\n        return channel;\n      }\n    });\n  };\n  var channelsFiltered = getChannelsFiltered();\n  var getChannels = function () {\n    var _ref4 = _asyncToGenerator(function* () {\n      try {\n        var response = yield api.get('/channels');\n        setChannels(response.data);\n        setIsLoading(false);\n      } catch (e) {\n        console.error(e);\n        setIsLoading(false);\n      }\n    });\n    return function getChannels() {\n      return _ref4.apply(this, arguments);\n    };\n  }();\n  var onCheckButtons = function onCheckButtons(filter) {\n    if (filter.all) {\n      setFilters([]);\n      return;\n    }\n    var filters = [];\n    channels.forEach(function (channel) {\n      if (channel.type === 'SPOT' && filter.spot) {\n        filters.push('spot');\n      }\n      if (channel.type === 'FUTURES' && filter.futures) {\n        filters.push('futures');\n      }\n      if (channel.isPremium && filter.premium) {\n        filters.push('premium');\n      }\n      if (!channel.isPremium && filter.free) {\n        filters.push('free');\n      }\n    });\n    setFilters(filters);\n  };\n  var returnNotificationPermission = function () {\n    var _ref5 = _asyncToGenerator(function* (_permission) {\n      Alert.alert(\"Notification\", _permission.isActive ? \"Notification enabled\" : \"Notification disabled\");\n    });\n    return function returnNotificationPermission(_x) {\n      return _ref5.apply(this, arguments);\n    };\n  }();\n  useEffect(function () {\n    if (!channels.length) {\n      getChannels();\n    }\n  }, []);\n  useEffect(function () {\n    if (!channelOrderedByNewSignal) return;\n    if (!channels.length) return;\n    channels = channels.map(function (c) {\n      if (c.externalId === channelOrderedByNewSignal.channelId) {\n        c.lastSignalAt = channelOrderedByNewSignal.createdAt;\n      }\n      return c;\n    });\n    channels.sort(function (a, b) {\n      return new Date(b.lastSignalAt) - new Date(a.lastSignalAt);\n    });\n    setChannels(channels);\n  }, [channelOrderedByNewSignal]);\n  useEffect(function () {\n    if (accountHasBeenRecovered) {\n      Alert.alert(\"Success\", \"Account recovered successfully\");\n    }\n  }, [accountHasBeenRecovered]);\n  if (isLoading) {\n    return _jsx(Loading, {});\n  }\n  var handleSearch = function handleSearch(value) {\n    setInputSearchValue(value);\n    setFilters(!value ? [] : ['search']);\n  };\n  return _jsxs(Wrapper, {\n    children: [_jsx(PageTitle, {\n      text: \"Channels\"\n    }, \"TitleChannels\"), _jsx(View, {\n      children: _jsx(InputSearch, {\n        placeholder: \"Search Channels\",\n        onChangeText: function onChangeText(text) {\n          return handleSearch(text);\n        },\n        value: inputSearchValue\n      }, \"InputSearchChannels\")\n    }), _jsx(CheckButtons, {\n      onFiltered: function onFiltered(filter) {\n        return onCheckButtons(filter);\n      }\n    }), _jsxs(ScrollView, {\n      style: styles.scrollView,\n      onScroll: null,\n      scrollEventThrottle: 400,\n      children: [inputSearchValue && channelsFiltered && !channelsFiltered.length && _jsx(Text, {\n        style: styles.emptyState,\n        children: \"No matching channels found.\"\n      }), channelsFiltered && (channelsFiltered == null ? void 0 : channelsFiltered.map(function (channel, index) {\n        return _jsx(Card, {\n          channel: channel,\n          paidPremium: subscriptionStatus,\n          isLoadedNotification: true,\n          notificationPermission: {\n            channelId: channel.externalId,\n            isActive: false\n          },\n          returnNotificationPermission: returnNotificationPermission\n        }, \"ChannelCard__\" + index);\n      })), !inputSearchValue && !channelsFiltered.length && _jsx(Button, {\n        mode: \"contained\",\n        textColor: colors.grey,\n        style: {\n          marginTop: 16\n        },\n        onPress: function onPress() {\n          setIsLoading(true);\n          getChannels();\n        },\n        children: \"Refresh channels\"\n      })]\n    })]\n  });\n}", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useContext", "View", "ScrollView", "Text", "<PERSON><PERSON>", "<PERSON><PERSON>", "useTheme", "Page<PERSON><PERSON>le", "Wrapper", "CheckButtons", "InputSearch", "Loading", "Card", "StoreContext", "styles", "AxiosContext", "jsx", "_jsx", "jsxs", "_jsxs", "Channels", "_ref", "_ref$route", "route", "_useTheme", "colors", "_useContext", "_useContext2", "_slicedToArray", "state", "_", "_useContext3", "_useContext4", "api", "_useState", "_useState2", "isLoading", "setIsLoading", "_useState3", "_useState4", "channels", "setChannels", "_useState5", "_useState6", "filters", "setFilters", "_useState7", "_useState8", "inputSearchValue", "setInputSearchValue", "_ref2", "subscription", "subscriptionStatus", "channelOrderedByNewSignal", "_ref3", "params", "accountHasBeenRecovered", "getChannelsFiltered", "length", "includes", "filter", "channel", "description", "toLowerCase", "type", "isPremium", "channelsFiltered", "getChannels", "_ref4", "_asyncToGenerator", "response", "get", "data", "e", "console", "error", "apply", "arguments", "onCheckButtons", "all", "for<PERSON>ach", "spot", "push", "futures", "premium", "free", "returnNotificationPermission", "_ref5", "_permission", "alert", "isActive", "_x", "map", "c", "externalId", "channelId", "lastSignalAt", "createdAt", "sort", "a", "b", "Date", "handleSearch", "value", "children", "text", "placeholder", "onChangeText", "onFiltered", "style", "scrollView", "onScroll", "scrollEventThrottle", "emptyState", "index", "paidPremium", "isLoadedNotification", "notificationPermission", "mode", "textColor", "grey", "marginTop", "onPress"], "sources": ["E:/CryptoSignalsApp/src/pages/Channels/index.js"], "sourcesContent": ["import React, { useState, useEffect, useContext } from 'react';\r\nimport { View, ScrollView, Text, Alert } from 'react-native';\r\nimport {\r\n  Button,\r\n  useTheme\r\n} from 'react-native-paper';\r\nimport PageTitle from '../../components/PageTitle';\r\nimport Wrapper from '../../components/Wrapper';\r\nimport CheckButtons from '../../components/CheckButtons';\r\nimport InputSearch from '../../components/InputSearch';\r\nimport Loading from '../../components/Loading';\r\nimport Card  from './Card';\r\nimport { StoreContext } from '../../store';\r\nimport styles from './styles';\r\nimport { AxiosContext } from '../../store/axios';\r\n\r\nexport default function Channels({route = {}}) {\r\n  const { colors } = useTheme();\r\n  const [state, _] = useContext(StoreContext);\r\n  const [api] = useContext(AxiosContext);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  let [channels, setChannels] = useState([]);\r\n  const [filters, setFilters] = useState([]);\r\n  const [inputSearchValue, setInputSearchValue] = useState(null);\r\n\r\n  const { subscription: { subscriptionStatus } } = state || { subscription: { subscriptionStatus: false } };\r\n  const channelOrderedByNewSignal = state?.channelOrderedByNewSignal;\r\n\r\n  const { accountHasBeenRecovered } = route.params || {accountHasBeenRecovered: false};\r\n\r\n  const getChannelsFiltered = () => {\r\n    if (!filters.length) {\r\n      return channels;\r\n    }\r\n\r\n    if (!!inputSearchValue && filters.includes('search')) {\r\n      return channels.filter((channel) => {\r\n        return channel.description.toLowerCase().includes(inputSearchValue.toLowerCase());\r\n      });\r\n    }\r\n\r\n    return channels.filter((channel) => {\r\n      if (channel.type === 'SPOT' && filters.includes('spot')\r\n        || channel.type === 'FUTURES' && filters.includes('futures')\r\n        || channel.isPremium && filters.includes('premium')\r\n        || (!channel.isPremium && filters.includes('free'))\r\n      ) {\r\n        return channel;\r\n      }\r\n    });\r\n  }\r\n\r\n  const channelsFiltered = getChannelsFiltered();\r\n\r\n  const getChannels = async () => {\r\n    try {\r\n      const response = await api.get('/channels');\r\n      setChannels(response.data);\r\n      setIsLoading(false);\r\n    } catch(e) {\r\n      console.error(e);\r\n      setIsLoading(false);\r\n    }\r\n  }\r\n\r\n\r\n\r\n  const onCheckButtons = (filter) => {\r\n    if (filter.all) {\r\n      setFilters([]);\r\n      return\r\n    }\r\n\r\n    let filters = [];\r\n\r\n    channels.forEach(channel => {\r\n      if (channel.type === 'SPOT' && filter.spot) {\r\n        filters.push('spot');\r\n      }\r\n\r\n      if (channel.type === 'FUTURES' && filter.futures) {\r\n        filters.push('futures');\r\n      }\r\n\r\n      if (channel.isPremium && filter.premium) {\r\n        filters.push('premium');\r\n      }\r\n\r\n      if (!channel.isPremium && filter.free) {\r\n        filters.push('free');\r\n      }\r\n    })\r\n\r\n    setFilters(filters);\r\n  }\r\n\r\n  const returnNotificationPermission = async (_permission) => {\r\n    // Mock notification toggle\r\n    Alert.alert(\r\n      \"Notification\",\r\n      _permission.isActive ? \"Notification enabled\" : \"Notification disabled\"\r\n    );\r\n  }\r\n\r\n  useEffect(() => {\r\n    if (!channels.length) {\r\n      getChannels();\r\n    }\r\n  }, []);\r\n\r\n\r\n\r\n  useEffect(() => {\r\n    if (!channelOrderedByNewSignal) return;\r\n    if (!channels.length) return;\r\n\r\n    channels = channels.map((c) => {\r\n      if (c.externalId === channelOrderedByNewSignal.channelId) {\r\n        c.lastSignalAt = channelOrderedByNewSignal.createdAt;\r\n      }\r\n\r\n      return c;\r\n    });\r\n\r\n    channels.sort((a,b) => new Date(b.lastSignalAt) - new Date(a.lastSignalAt));\r\n\r\n    setChannels(channels);\r\n\r\n  }, [channelOrderedByNewSignal]);\r\n\r\n  useEffect(() => {\r\n    if(accountHasBeenRecovered) {\r\n      Alert.alert(\"Success\", \"Account recovered successfully\");\r\n    }\r\n  }, [accountHasBeenRecovered]);\r\n\r\n  if (isLoading) {\r\n    return <Loading />\r\n  }\r\n\r\n  const handleSearch = (value) => {\r\n    setInputSearchValue(value);\r\n    setFilters(!value ? [] : ['search']);\r\n  }\r\n\r\n  return (\r\n    <Wrapper>\r\n      <PageTitle key=\"TitleChannels\" text=\"Channels\" />\r\n\r\n      <View>\r\n        <InputSearch\r\n          key=\"InputSearchChannels\"\r\n          placeholder=\"Search Channels\"\r\n          onChangeText={(text) => handleSearch(text)}\r\n          value={inputSearchValue}\r\n        />\r\n      </View>\r\n\r\n      <CheckButtons onFiltered={(filter) => onCheckButtons(filter)} />\r\n\r\n      <ScrollView\r\n        style={styles.scrollView}\r\n        onScroll={null}\r\n        scrollEventThrottle={400}\r\n      >\r\n        {inputSearchValue && channelsFiltered && !channelsFiltered.length && (\r\n          <Text style={styles.emptyState}>No matching channels found.</Text>\r\n        )}\r\n\r\n        {channelsFiltered &&\r\n          channelsFiltered?.map((channel, index) => (\r\n            <Card\r\n              channel={channel}\r\n              key={\"ChannelCard__\" + index}\r\n              paidPremium={subscriptionStatus}\r\n              isLoadedNotification={true}\r\n              notificationPermission={{channelId: channel.externalId, isActive: false}}\r\n              returnNotificationPermission={returnNotificationPermission}\r\n            />\r\n          ))}\r\n\r\n        {!inputSearchValue && !channelsFiltered.length && (\r\n          <Button\r\n            mode=\"contained\"\r\n            textColor={colors.grey}\r\n            style={{ marginTop: 16 }}\r\n            onPress={() => {\r\n              setIsLoading(true);\r\n              getChannels();\r\n            }}\r\n          >\r\n            Refresh channels\r\n          </Button>\r\n        )}\r\n      </ScrollView>\r\n    </Wrapper>\r\n  );\r\n}\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,QAAQ,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,KAAA;AAE/D,SACEC,MAAM,EACNC,QAAQ,QACH,oBAAoB;AAC3B,OAAOC,SAAS;AAChB,OAAOC,OAAO;AACd,OAAOC,YAAY;AACnB,OAAOC,WAAW;AAClB,OAAOC,OAAO;AACd,OAAOC,IAAI;AACX,SAASC,YAAY;AACrB,OAAOC,MAAM;AACb,SAASC,YAAY;AAA4B,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAEjD,eAAe,SAASC,QAAQA,CAAAC,IAAA,EAAe;EAAA,IAAAC,UAAA,GAAAD,IAAA,CAAbE,KAAK;IAALA,KAAK,GAAAD,UAAA,cAAG,CAAC,CAAC,GAAAA,UAAA;EAC1C,IAAAE,SAAA,GAAmBlB,QAAQ,CAAC,CAAC;IAArBmB,MAAM,GAAAD,SAAA,CAANC,MAAM;EACd,IAAAC,WAAA,GAAmB1B,UAAU,CAACa,YAAY,CAAC;IAAAc,YAAA,GAAAC,cAAA,CAAAF,WAAA;IAApCG,KAAK,GAAAF,YAAA;IAAEG,CAAC,GAAAH,YAAA;EACf,IAAAI,YAAA,GAAc/B,UAAU,CAACe,YAAY,CAAC;IAAAiB,YAAA,GAAAJ,cAAA,CAAAG,YAAA;IAA/BE,GAAG,GAAAD,YAAA;EACV,IAAAE,SAAA,GAAkCpC,QAAQ,CAAC,IAAI,CAAC;IAAAqC,UAAA,GAAAP,cAAA,CAAAM,SAAA;IAAzCE,SAAS,GAAAD,UAAA;IAAEE,YAAY,GAAAF,UAAA;EAC9B,IAAAG,UAAA,GAA8BxC,QAAQ,CAAC,EAAE,CAAC;IAAAyC,UAAA,GAAAX,cAAA,CAAAU,UAAA;IAArCE,QAAQ,GAAAD,UAAA;IAAEE,WAAW,GAAAF,UAAA;EAC1B,IAAAG,UAAA,GAA8B5C,QAAQ,CAAC,EAAE,CAAC;IAAA6C,UAAA,GAAAf,cAAA,CAAAc,UAAA;IAAnCE,OAAO,GAAAD,UAAA;IAAEE,UAAU,GAAAF,UAAA;EAC1B,IAAAG,UAAA,GAAgDhD,QAAQ,CAAC,IAAI,CAAC;IAAAiD,UAAA,GAAAnB,cAAA,CAAAkB,UAAA;IAAvDE,gBAAgB,GAAAD,UAAA;IAAEE,mBAAmB,GAAAF,UAAA;EAE5C,IAAAG,KAAA,GAAiDrB,KAAK,IAAI;MAAEsB,YAAY,EAAE;QAAEC,kBAAkB,EAAE;MAAM;IAAE,CAAC;IAAjFA,kBAAkB,GAAAF,KAAA,CAAlCC,YAAY,CAAIC,kBAAkB;EAC1C,IAAMC,yBAAyB,GAAGxB,KAAK,oBAALA,KAAK,CAAEwB,yBAAyB;EAElE,IAAAC,KAAA,GAAoC/B,KAAK,CAACgC,MAAM,IAAI;MAACC,uBAAuB,EAAE;IAAK,CAAC;IAA5EA,uBAAuB,GAAAF,KAAA,CAAvBE,uBAAuB;EAE/B,IAAMC,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAA,EAAS;IAChC,IAAI,CAACb,OAAO,CAACc,MAAM,EAAE;MACnB,OAAOlB,QAAQ;IACjB;IAEA,IAAI,CAAC,CAACQ,gBAAgB,IAAIJ,OAAO,CAACe,QAAQ,CAAC,QAAQ,CAAC,EAAE;MACpD,OAAOnB,QAAQ,CAACoB,MAAM,CAAC,UAACC,OAAO,EAAK;QAClC,OAAOA,OAAO,CAACC,WAAW,CAACC,WAAW,CAAC,CAAC,CAACJ,QAAQ,CAACX,gBAAgB,CAACe,WAAW,CAAC,CAAC,CAAC;MACnF,CAAC,CAAC;IACJ;IAEA,OAAOvB,QAAQ,CAACoB,MAAM,CAAC,UAACC,OAAO,EAAK;MAClC,IAAIA,OAAO,CAACG,IAAI,KAAK,MAAM,IAAIpB,OAAO,CAACe,QAAQ,CAAC,MAAM,CAAC,IAClDE,OAAO,CAACG,IAAI,KAAK,SAAS,IAAIpB,OAAO,CAACe,QAAQ,CAAC,SAAS,CAAC,IACzDE,OAAO,CAACI,SAAS,IAAIrB,OAAO,CAACe,QAAQ,CAAC,SAAS,CAAC,IAC/C,CAACE,OAAO,CAACI,SAAS,IAAIrB,OAAO,CAACe,QAAQ,CAAC,MAAM,CAAE,EACnD;QACA,OAAOE,OAAO;MAChB;IACF,CAAC,CAAC;EACJ,CAAC;EAED,IAAMK,gBAAgB,GAAGT,mBAAmB,CAAC,CAAC;EAE9C,IAAMU,WAAW;IAAA,IAAAC,KAAA,GAAAC,iBAAA,CAAG,aAAY;MAC9B,IAAI;QACF,IAAMC,QAAQ,SAASrC,GAAG,CAACsC,GAAG,CAAC,WAAW,CAAC;QAC3C9B,WAAW,CAAC6B,QAAQ,CAACE,IAAI,CAAC;QAC1BnC,YAAY,CAAC,KAAK,CAAC;MACrB,CAAC,CAAC,OAAMoC,CAAC,EAAE;QACTC,OAAO,CAACC,KAAK,CAACF,CAAC,CAAC;QAChBpC,YAAY,CAAC,KAAK,CAAC;MACrB;IACF,CAAC;IAAA,gBATK8B,WAAWA,CAAA;MAAA,OAAAC,KAAA,CAAAQ,KAAA,OAAAC,SAAA;IAAA;EAAA,GAShB;EAID,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAAIlB,MAAM,EAAK;IACjC,IAAIA,MAAM,CAACmB,GAAG,EAAE;MACdlC,UAAU,CAAC,EAAE,CAAC;MACd;IACF;IAEA,IAAID,OAAO,GAAG,EAAE;IAEhBJ,QAAQ,CAACwC,OAAO,CAAC,UAAAnB,OAAO,EAAI;MAC1B,IAAIA,OAAO,CAACG,IAAI,KAAK,MAAM,IAAIJ,MAAM,CAACqB,IAAI,EAAE;QAC1CrC,OAAO,CAACsC,IAAI,CAAC,MAAM,CAAC;MACtB;MAEA,IAAIrB,OAAO,CAACG,IAAI,KAAK,SAAS,IAAIJ,MAAM,CAACuB,OAAO,EAAE;QAChDvC,OAAO,CAACsC,IAAI,CAAC,SAAS,CAAC;MACzB;MAEA,IAAIrB,OAAO,CAACI,SAAS,IAAIL,MAAM,CAACwB,OAAO,EAAE;QACvCxC,OAAO,CAACsC,IAAI,CAAC,SAAS,CAAC;MACzB;MAEA,IAAI,CAACrB,OAAO,CAACI,SAAS,IAAIL,MAAM,CAACyB,IAAI,EAAE;QACrCzC,OAAO,CAACsC,IAAI,CAAC,MAAM,CAAC;MACtB;IACF,CAAC,CAAC;IAEFrC,UAAU,CAACD,OAAO,CAAC;EACrB,CAAC;EAED,IAAM0C,4BAA4B;IAAA,IAAAC,KAAA,GAAAlB,iBAAA,CAAG,WAAOmB,WAAW,EAAK;MAE1DpF,KAAK,CAACqF,KAAK,CACT,cAAc,EACdD,WAAW,CAACE,QAAQ,GAAG,sBAAsB,GAAG,uBAClD,CAAC;IACH,CAAC;IAAA,gBANKJ,4BAA4BA,CAAAK,EAAA;MAAA,OAAAJ,KAAA,CAAAX,KAAA,OAAAC,SAAA;IAAA;EAAA,GAMjC;EAED9E,SAAS,CAAC,YAAM;IACd,IAAI,CAACyC,QAAQ,CAACkB,MAAM,EAAE;MACpBS,WAAW,CAAC,CAAC;IACf;EACF,CAAC,EAAE,EAAE,CAAC;EAINpE,SAAS,CAAC,YAAM;IACd,IAAI,CAACsD,yBAAyB,EAAE;IAChC,IAAI,CAACb,QAAQ,CAACkB,MAAM,EAAE;IAEtBlB,QAAQ,GAAGA,QAAQ,CAACoD,GAAG,CAAC,UAACC,CAAC,EAAK;MAC7B,IAAIA,CAAC,CAACC,UAAU,KAAKzC,yBAAyB,CAAC0C,SAAS,EAAE;QACxDF,CAAC,CAACG,YAAY,GAAG3C,yBAAyB,CAAC4C,SAAS;MACtD;MAEA,OAAOJ,CAAC;IACV,CAAC,CAAC;IAEFrD,QAAQ,CAAC0D,IAAI,CAAC,UAACC,CAAC,EAACC,CAAC;MAAA,OAAK,IAAIC,IAAI,CAACD,CAAC,CAACJ,YAAY,CAAC,GAAG,IAAIK,IAAI,CAACF,CAAC,CAACH,YAAY,CAAC;IAAA,EAAC;IAE3EvD,WAAW,CAACD,QAAQ,CAAC;EAEvB,CAAC,EAAE,CAACa,yBAAyB,CAAC,CAAC;EAE/BtD,SAAS,CAAC,YAAM;IACd,IAAGyD,uBAAuB,EAAE;MAC1BpD,KAAK,CAACqF,KAAK,CAAC,SAAS,EAAE,gCAAgC,CAAC;IAC1D;EACF,CAAC,EAAE,CAACjC,uBAAuB,CAAC,CAAC;EAE7B,IAAIpB,SAAS,EAAE;IACb,OAAOnB,IAAA,CAACN,OAAO,IAAE,CAAC;EACpB;EAEA,IAAM2F,YAAY,GAAG,SAAfA,YAAYA,CAAIC,KAAK,EAAK;IAC9BtD,mBAAmB,CAACsD,KAAK,CAAC;IAC1B1D,UAAU,CAAC,CAAC0D,KAAK,GAAG,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAC;EACtC,CAAC;EAED,OACEpF,KAAA,CAACX,OAAO;IAAAgG,QAAA,GACNvF,IAAA,CAACV,SAAS;MAAqBkG,IAAI,EAAC;IAAU,GAA/B,eAAiC,CAAC,EAEjDxF,IAAA,CAAChB,IAAI;MAAAuG,QAAA,EACHvF,IAAA,CAACP,WAAW;QAEVgG,WAAW,EAAC,iBAAiB;QAC7BC,YAAY,EAAE,SAAdA,YAAYA,CAAGF,IAAI;UAAA,OAAKH,YAAY,CAACG,IAAI,CAAC;QAAA,CAAC;QAC3CF,KAAK,EAAEvD;MAAiB,GAHpB,qBAIL;IAAC,CACE,CAAC,EAEP/B,IAAA,CAACR,YAAY;MAACmG,UAAU,EAAE,SAAZA,UAAUA,CAAGhD,MAAM;QAAA,OAAKkB,cAAc,CAAClB,MAAM,CAAC;MAAA;IAAC,CAAE,CAAC,EAEhEzC,KAAA,CAACjB,UAAU;MACT2G,KAAK,EAAE/F,MAAM,CAACgG,UAAW;MACzBC,QAAQ,EAAE,IAAK;MACfC,mBAAmB,EAAE,GAAI;MAAAR,QAAA,GAExBxD,gBAAgB,IAAIkB,gBAAgB,IAAI,CAACA,gBAAgB,CAACR,MAAM,IAC/DzC,IAAA,CAACd,IAAI;QAAC0G,KAAK,EAAE/F,MAAM,CAACmG,UAAW;QAAAT,QAAA,EAAC;MAA2B,CAAM,CAClE,EAEAtC,gBAAgB,KACfA,gBAAgB,oBAAhBA,gBAAgB,CAAE0B,GAAG,CAAC,UAAC/B,OAAO,EAAEqD,KAAK;QAAA,OACnCjG,IAAA,CAACL,IAAI;UACHiD,OAAO,EAAEA,OAAQ;UAEjBsD,WAAW,EAAE/D,kBAAmB;UAChCgE,oBAAoB,EAAE,IAAK;UAC3BC,sBAAsB,EAAE;YAACtB,SAAS,EAAElC,OAAO,CAACiC,UAAU;YAAEJ,QAAQ,EAAE;UAAK,CAAE;UACzEJ,4BAA4B,EAAEA;QAA6B,GAJtD,eAAe,GAAG4B,KAKxB,CAAC;MAAA,CACH,CAAC,GAEH,CAAClE,gBAAgB,IAAI,CAACkB,gBAAgB,CAACR,MAAM,IAC5CzC,IAAA,CAACZ,MAAM;QACLiH,IAAI,EAAC,WAAW;QAChBC,SAAS,EAAE9F,MAAM,CAAC+F,IAAK;QACvBX,KAAK,EAAE;UAAEY,SAAS,EAAE;QAAG,CAAE;QACzBC,OAAO,EAAE,SAATA,OAAOA,CAAA,EAAQ;UACbrF,YAAY,CAAC,IAAI,CAAC;UAClB8B,WAAW,CAAC,CAAC;QACf,CAAE;QAAAqC,QAAA,EACH;MAED,CAAQ,CACT;IAAA,CACS,CAAC;EAAA,CACN,CAAC;AAEd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}