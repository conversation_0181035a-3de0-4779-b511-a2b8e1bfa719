{"ast": null, "code": "import View from \"react-native-web/dist/exports/View\";\nvar ScreenContentWrapper = View;\nexport default ScreenContentWrapper;", "map": {"version": 3, "names": ["ScreenContentWrapper", "View"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\react-native-screens\\src\\components\\ScreenContentWrapper.web.tsx"], "sourcesContent": ["import { View } from 'react-native';\n\nconst ScreenContentWrapper = View;\n\nexport default ScreenContentWrapper;\n"], "mappings": ";AAEA,IAAMA,oBAAoB,GAAGC,IAAI;AAEjC,eAAeD,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}