# Sistema de Backtesting para CryptoSignals

## Visão Geral
O sistema de backtesting foi desenvolvido para testar e validar estratégias de trading de criptomoedas usando dados históricos da Binance. O sistema permite simular operações de trading e avaliar o desempenho das estratégias antes de utilizá-las em ambiente real.

## Funcionalidades Principais

### 1. Obtenção de Dados
- Integração direta com a API da Binance
- Suporte a diferentes timeframes (1m, 5m, 15m, 1h, etc.)
- Dados OHLCV (Open, High, Low, Close, Volume) completos
- Dados adicionais como número de trades e volumes de compra/venda

### 2. Simulação de Trading
- Suporte a posições LONG e SHORT
- Gerenciamento de risco com Stop Loss e Take Profit
- Tamanho de posição configurável (padrão: 1% do capital)
- Simulação precisa de execução de ordens
- Rastreamento de posições abertas e fechadas

### 3. Métricas de Performance
O sistema calcula as seguintes métricas:

- **Total de Trades**: Número total de operações realizadas
- **Win Rate**: Percentual de trades lucrativos
- **Profit Factor**: Razão entre lucros brutos e perdas brutas
- **Drawdown Máximo**: Maior queda percentual do capital
- **Sharpe Ratio**: Relação risco/retorno dos trades
- **Retorno Total**: Percentual de lucro/prejuízo total
- **Curva de Equity**: Evolução do capital ao longo do tempo

## Estrutura do Sistema

### Arquivos Principais
- `utils/backtesting.py`: Implementação principal do backtester
- `tests/test_backtesting.py`: Testes unitários do sistema
- `scripts/run_backtesting.py`: Script para execução de backtests

### Classes e Métodos Principais

#### Classe Backtester
```python
class Backtester:
    def __init__(self, binance_client, strategy, initial_capital=1000.0)
    def get_historical_data(self, symbol, interval, start_date, end_date)
    def run_backtest(self, symbol, interval, start_date, end_date)
    def execute_trade(self, symbol, signal_type, entry_price, stop_loss, take_profit, timestamp)
    def calculate_performance_metrics()
```

## Como Usar

### 1. Configuração Inicial
```python
from utils.backtesting import Backtester
from binance.client import Client

# Inicializar cliente Binance
binance_client = Client(api_key, api_secret)

# Criar instância do backtester
backtester = Backtester(
    binance_client=binance_client,
    strategy=your_strategy,
    initial_capital=1000.0
)
```

### 2. Executar Backtest
```python
results = backtester.run_backtest(
    symbol='BTCUSDT',
    interval='1h',
    start_date=datetime(2024, 1, 1),
    end_date=datetime(2024, 2, 1)
)
```

### 3. Analisar Resultados
```python
print(f"Total de Trades: {results['total_trades']}")
print(f"Win Rate: {results['win_rate']*100:.2f}%")
print(f"Retorno Total: {results['total_return']:.2f}%")
print(f"Drawdown Máximo: {results['max_drawdown']:.2f}%")
```

## Implementando Estratégias

Para implementar uma nova estratégia, crie uma classe que implemente o método `analyze_symbol`:

```python
class MinhaEstrategia:
    def analyze_symbol(self, df: pd.DataFrame) -> Optional[Tuple[str, float, float, float]]:
        """
        Analisa os dados e retorna sinais de trading
        
        Returns:
            Tuple[signal_type, entry_price, stop_loss, take_profit] ou None
        """
        # Implementar lógica da estratégia aqui
        pass
```

## Limitações e Considerações

1. **Slippage**: O sistema assume execução perfeita nos preços alvo
2. **Liquidez**: Não considera impacto de mercado das ordens
3. **Custos**: Taxas de trading não são consideradas por padrão
4. **Dados**: Qualidade depende da disponibilidade de dados históricos

## Próximos Passos

1. **Otimização**:
   - Implementar otimização de parâmetros
   - Adicionar backtesting com walk-forward
   - Suporte a múltiplos ativos simultaneamente

2. **Melhorias**:
   - Adicionar simulação de slippage
   - Incluir custos de trading
   - Implementar mais indicadores técnicos
   - Adicionar visualizações gráficas dos resultados

3. **Análise**:
   - Expandir métricas de performance
   - Adicionar análise de risco mais detalhada
   - Implementar relatórios personalizados

## Contribuindo

Para contribuir com o desenvolvimento do sistema de backtesting:

1. Fork o repositório
2. Crie uma branch para sua feature
3. Implemente suas mudanças
4. Adicione/atualize testes
5. Envie um Pull Request

## Suporte

Para dúvidas ou sugestões, abra uma issue no repositório ou entre em contato com a equipe de desenvolvimento. 