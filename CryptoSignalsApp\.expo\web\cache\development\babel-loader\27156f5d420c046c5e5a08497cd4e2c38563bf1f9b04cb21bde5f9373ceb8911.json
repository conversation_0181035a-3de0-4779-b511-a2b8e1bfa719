{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"theme\"];\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nimport * as React from 'react';\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport RadioButtonAndroid from \"./RadioButtonAndroid\";\nimport RadioButtonIOS from \"./RadioButtonIOS\";\nimport { useInternalTheme } from \"../../core/theming\";\nvar RadioButton = function RadioButton(_ref) {\n  var themeOverrides = _ref.theme,\n    props = _objectWithoutProperties(_ref, _excluded);\n  var theme = useInternalTheme(themeOverrides);\n  var Button = Platform.select({\n    default: RadioButtonAndroid,\n    ios: RadioButtonIOS\n  });\n  return React.createElement(Button, _extends({}, props, {\n    theme: theme\n  }));\n};\nexport default RadioButton;", "map": {"version": 3, "names": ["React", "Platform", "RadioButtonAndroid", "RadioButtonIOS", "useInternalTheme", "RadioButton", "_ref", "themeOverrides", "theme", "props", "_objectWithoutProperties", "_excluded", "<PERSON><PERSON>", "select", "default", "ios", "createElement", "_extends"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\RadioButton\\RadioButton.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { GestureResponderEvent, Platform } from 'react-native';\n\nimport RadioButtonAndroid from './RadioButtonAndroid';\nimport RadioButtonIOS from './RadioButtonIOS';\nimport { useInternalTheme } from '../../core/theming';\nimport type { ThemeProp } from '../../types';\n\nexport type Props = {\n  /**\n   * Value of the radio button\n   */\n  value: string;\n  /**\n   * Status of radio button.\n   */\n  status?: 'checked' | 'unchecked';\n  /**\n   * Whether radio is disabled.\n   */\n  disabled?: boolean;\n  /**\n   * Function to execute on press.\n   */\n  onPress?: (e: GestureResponderEvent) => void;\n  /**\n   * Custom color for unchecked radio.\n   */\n  uncheckedColor?: string;\n  /**\n   * Custom color for radio.\n   */\n  color?: string;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n  /**\n   * testID to be used on tests.\n   */\n  testID?: string;\n};\n\n/**\n * Radio buttons allow the selection a single option from a set.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { View } from 'react-native';\n * import { RadioButton } from 'react-native-paper';\n *\n * const MyComponent = () => {\n *   const [checked, setChecked] = React.useState('first');\n *\n *   return (\n *     <View>\n *       <RadioButton\n *         value=\"first\"\n *         status={ checked === 'first' ? 'checked' : 'unchecked' }\n *         onPress={() => setChecked('first')}\n *       />\n *       <RadioButton\n *         value=\"second\"\n *         status={ checked === 'second' ? 'checked' : 'unchecked' }\n *         onPress={() => setChecked('second')}\n *       />\n *     </View>\n *   );\n * };\n *\n * export default MyComponent;\n * ```\n */\nconst RadioButton = ({ theme: themeOverrides, ...props }: Props) => {\n  const theme = useInternalTheme(themeOverrides);\n\n  const Button = Platform.select({\n    default: RadioButtonAndroid,\n    ios: RadioButtonIOS,\n  });\n\n  return <Button {...props} theme={theme} />;\n};\n\nexport default RadioButton;\n"], "mappings": ";;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAG9B,OAAOC,kBAAkB;AACzB,OAAOC,cAAc;AACrB,SAASC,gBAAgB;AAqEzB,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAAC,IAAA,EAAmD;EAAA,IAAtCC,cAAc,GAAAD,IAAA,CAArBE,KAAK;IAAqBC,KAAA,GAAAC,wBAAA,CAAAJ,IAAA,EAAAK,SAAA;EAC/C,IAAMH,KAAK,GAAGJ,gBAAgB,CAACG,cAAc,CAAC;EAE9C,IAAMK,MAAM,GAAGX,QAAQ,CAACY,MAAM,CAAC;IAC7BC,OAAO,EAAEZ,kBAAkB;IAC3Ba,GAAG,EAAEZ;EACP,CAAC,CAAC;EAEF,OAAOH,KAAA,CAAAgB,aAAA,CAACJ,MAAM,EAAAK,QAAA,KAAKR,KAAK;IAAED,KAAK,EAAEA;EAAM,EAAE,CAAC;AAC5C,CAAC;AAED,eAAeH,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}