const TelegramBot = require('node-telegram-bot-api');
const { processData } = require('./dataProcessing');
const { saveData } = require('./firebaseIntegration');

// Carregue o token do bot a partir de uma variável de ambiente ou configuração segura
const TELEGRAM_TOKEN = process.env.TELEGRAM_TOKEN || 'SEU_TOKEN_AQUI';

// Inicie o bot do Telegram
const bot = new TelegramBot(TELEGRAM_TOKEN, { polling: true });

/**
 * Evento para tratar mensagens com o comando /receber
 */
bot.onText(/\/receber/, async (msg) => {
  const { chat: { id: chatId }, text: messageText } = msg;

  try {
    // Processar a mensagem
    const processedData = processData(messageText);

    // Armazene os dados processados no Firebase
    await saveData(processedData);

    // Envie uma confirmação ao usuário
    bot.sendMessage(chatId, 'Mensagem recebida e processada com sucesso.');
  } catch (error) {
    console.error('Error processing the message:', error);
    bot.sendMessage(chatId, 'Ocorreu um erro ao processar sua mensagem. Por favor, tente novamente mais tarde.');
  }
});

// Adicione aqui outros eventos e funcionalidades relacionadas ao bot do Telegram
//Neste código, aprimorado o tratamento de erros, a leitura e a estrutura geral do bot.




