#!/usr/bin/env python3
"""
Script para testar a operação 24/7 do sistema CryptoSignals
"""

import asyncio
import logging
import sys
import os
from datetime import datetime, timedelta

# Adicionar o diretório raiz ao path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.settings import (
    MAX_SIGNALS_PER_DAY,
    SIGNAL_INTERVAL_SECONDS,
    TRADING_START_HOUR,
    TRADING_END_HOUR
)
from main import SignalGenerator
from utils.telegram_sender import TelegramSender

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_configuration():
    """Testa as configurações 24/7"""
    logger.info("=== TESTE DE CONFIGURAÇÕES 24/7 ===")
    
    logger.info(f"📊 Configurações atuais:")
    logger.info(f"  MAX_SIGNALS_PER_DAY: {MAX_SIGNALS_PER_DAY}")
    logger.info(f"  SIGNAL_INTERVAL_SECONDS: {SIGNAL_INTERVAL_SECONDS}")
    logger.info(f"  TRADING_START_HOUR: {TRADING_START_HOUR}")
    logger.info(f"  TRADING_END_HOUR: {TRADING_END_HOUR}")
    
    # Verificar se as configurações estão corretas para 24/7
    if TRADING_START_HOUR == 0 and TRADING_END_HOUR == 23:
        logger.info("✅ Configuração 24/7 ativada corretamente")
    else:
        logger.warning("⚠️ Configuração não está em modo 24/7")
    
    if MAX_SIGNALS_PER_DAY >= 50:
        logger.info("✅ Limite de sinais aumentado para operação contínua")
    else:
        logger.warning("⚠️ Limite de sinais pode ser baixo para 24/7")
    
    if SIGNAL_INTERVAL_SECONDS <= 180:
        logger.info("✅ Intervalo entre sinais otimizado")
    else:
        logger.warning("⚠️ Intervalo entre sinais pode ser muito alto")

def test_trading_hours():
    """Testa a função is_trading_hours em diferentes horários"""
    logger.info("\n=== TESTE DE HORÁRIOS DE TRADING ===")
    
    # Criar instância do gerador de sinais
    generator = SignalGenerator()
    telegram = TelegramSender()
    
    # Testar diferentes horários
    test_hours = [0, 3, 6, 9, 12, 15, 18, 21, 23]
    
    for hour in test_hours:
        # Simular horário
        test_time = datetime.now().replace(hour=hour, minute=0, second=0)
        
        # Testar função do gerador
        is_trading_generator = generator.is_trading_hours()
        is_trading_telegram = telegram.is_trading_hours()
        
        logger.info(f"  {hour:02d}:00 - Generator: {is_trading_generator}, Telegram: {is_trading_telegram}")
        
        if not (is_trading_generator and is_trading_telegram):
            logger.error(f"❌ Horário {hour:02d}:00 não está permitindo trading!")
            return False
    
    logger.info("✅ Todos os horários permitem trading (24/7 funcionando)")
    return True

def test_signal_frequency():
    """Testa a frequência de sinais"""
    logger.info("\n=== TESTE DE FREQUÊNCIA DE SINAIS ===")
    
    # Calcular quantos sinais por hora são possíveis
    signals_per_hour = 3600 / SIGNAL_INTERVAL_SECONDS
    signals_per_day_theoretical = signals_per_hour * 24
    
    logger.info(f"📊 Análise de frequência:")
    logger.info(f"  Intervalo entre sinais: {SIGNAL_INTERVAL_SECONDS}s ({SIGNAL_INTERVAL_SECONDS/60:.1f} min)")
    logger.info(f"  Sinais possíveis por hora: {signals_per_hour:.1f}")
    logger.info(f"  Sinais teóricos por dia: {signals_per_day_theoretical:.0f}")
    logger.info(f"  Limite configurado: {MAX_SIGNALS_PER_DAY}")
    
    if MAX_SIGNALS_PER_DAY < signals_per_day_theoretical:
        efficiency = (MAX_SIGNALS_PER_DAY / signals_per_day_theoretical) * 100
        logger.info(f"  Eficiência: {efficiency:.1f}% (limitado por MAX_SIGNALS_PER_DAY)")
    else:
        logger.info(f"  Eficiência: 100% (sem limitação)")
    
    # Verificar se a configuração é adequada para 24/7
    if signals_per_hour >= 10:  # Pelo menos 10 sinais por hora
        logger.info("✅ Frequência adequada para operação contínua")
        return True
    else:
        logger.warning("⚠️ Frequência pode ser baixa para 24/7")
        return False

async def test_signal_generation():
    """Testa a geração de sinais em modo simulação"""
    logger.info("\n=== TESTE DE GERAÇÃO DE SINAIS ===")
    
    try:
        # Criar gerador em modo teste
        generator = SignalGenerator()
        
        # Verificar se pode gerar sinais agora
        can_generate = generator.is_trading_hours()
        logger.info(f"Pode gerar sinais agora: {can_generate}")
        
        if not can_generate:
            logger.error("❌ Sistema não permite geração de sinais no horário atual")
            return False
        
        # Verificar limite diário
        generator.reset_daily_counter()
        logger.info(f"Sinais enviados hoje: {generator.signals_sent_today}/{MAX_SIGNALS_PER_DAY}")
        
        if generator.signals_sent_today >= MAX_SIGNALS_PER_DAY:
            logger.warning("⚠️ Limite diário já atingido")
        else:
            logger.info("✅ Ainda há capacidade para enviar sinais")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Erro ao testar geração de sinais: {e}")
        return False

def test_performance_impact():
    """Analisa o impacto da operação 24/7"""
    logger.info("\n=== ANÁLISE DE IMPACTO 24/7 ===")
    
    # Configurações antigas vs novas
    old_config = {
        'hours_per_day': 15,  # 6h às 21h = 15 horas
        'max_signals': 20,
        'interval': 300  # 5 minutos
    }
    
    new_config = {
        'hours_per_day': 24,  # 24 horas
        'max_signals': MAX_SIGNALS_PER_DAY,
        'interval': SIGNAL_INTERVAL_SECONDS
    }
    
    # Calcular capacidade
    old_capacity = (old_config['hours_per_day'] * 3600 / old_config['interval'])
    old_actual = min(old_capacity, old_config['max_signals'])
    
    new_capacity = (new_config['hours_per_day'] * 3600 / new_config['interval'])
    new_actual = min(new_capacity, new_config['max_signals'])
    
    improvement = (new_actual / old_actual - 1) * 100
    
    logger.info(f"📊 Comparação de capacidade:")
    logger.info(f"  Configuração antiga:")
    logger.info(f"    Horas de operação: {old_config['hours_per_day']}h")
    logger.info(f"    Sinais possíveis: {old_actual:.0f}/dia")
    logger.info(f"  Configuração nova:")
    logger.info(f"    Horas de operação: {new_config['hours_per_day']}h")
    logger.info(f"    Sinais possíveis: {new_actual:.0f}/dia")
    logger.info(f"  Melhoria: +{improvement:.0f}%")
    
    if improvement > 100:
        logger.info("✅ Melhoria significativa na capacidade de sinais")
    else:
        logger.info("✅ Melhoria moderada na capacidade de sinais")

async def main():
    """Função principal de teste"""
    logger.info("🚀 TESTE DE OPERAÇÃO 24/7 - CRYPTOSIGNALS")
    logger.info("=" * 60)
    
    try:
        # Executar todos os testes
        test_configuration()
        
        trading_hours_ok = test_trading_hours()
        frequency_ok = test_signal_frequency()
        generation_ok = await test_signal_generation()
        
        test_performance_impact()
        
        # Resumo final
        logger.info("\n" + "=" * 60)
        logger.info("📋 RESUMO DOS TESTES:")
        
        tests_passed = 0
        total_tests = 3
        
        if trading_hours_ok:
            logger.info("✅ Horários de trading: APROVADO")
            tests_passed += 1
        else:
            logger.info("❌ Horários de trading: FALHOU")
        
        if frequency_ok:
            logger.info("✅ Frequência de sinais: APROVADO")
            tests_passed += 1
        else:
            logger.info("❌ Frequência de sinais: FALHOU")
        
        if generation_ok:
            logger.info("✅ Geração de sinais: APROVADO")
            tests_passed += 1
        else:
            logger.info("❌ Geração de sinais: FALHOU")
        
        success_rate = (tests_passed / total_tests) * 100
        logger.info(f"\n🎯 Taxa de sucesso: {tests_passed}/{total_tests} ({success_rate:.0f}%)")
        
        if tests_passed == total_tests:
            logger.info("🎉 SISTEMA 24/7 CONFIGURADO COM SUCESSO!")
            logger.info("💡 O sistema agora pode operar continuamente")
            logger.info("📈 Capacidade de sinais significativamente aumentada")
        else:
            logger.warning("⚠️ Alguns testes falharam. Verifique as configurações.")
        
    except Exception as e:
        logger.error(f"❌ Erro durante os testes: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
