{"ast": null, "code": "'use client';\n\nimport Dimensions from \"../Dimensions\";\nimport { useEffect, useState } from 'react';\nexport default function useWindowDimensions() {\n  var _useState = useState(function () {\n      return Dimensions.get('window');\n    }),\n    dims = _useState[0],\n    setDims = _useState[1];\n  useEffect(function () {\n    function handleChange(_ref) {\n      var window = _ref.window;\n      if (window != null) {\n        setDims(window);\n      }\n    }\n    Dimensions.addEventListener('change', handleChange);\n    setDims(Dimensions.get('window'));\n    return function () {\n      Dimensions.removeEventListener('change', handleChange);\n    };\n  }, []);\n  return dims;\n}", "map": {"version": 3, "names": ["Dimensions", "useEffect", "useState", "useWindowDimensions", "_useState", "get", "dims", "setDims", "handleChange", "_ref", "window", "addEventListener", "removeEventListener"], "sources": ["E:/CryptoSignalsApp/node_modules/react-native-web/dist/exports/useWindowDimensions/index.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * \n */\n\n'use client';\n\nimport Dimensions from '../Dimensions';\nimport { useEffect, useState } from 'react';\nexport default function useWindowDimensions() {\n  var _useState = useState(() => Dimensions.get('window')),\n    dims = _useState[0],\n    setDims = _useState[1];\n  useEffect(() => {\n    function handleChange(_ref) {\n      var window = _ref.window;\n      if (window != null) {\n        setDims(window);\n      }\n    }\n    Dimensions.addEventListener('change', handleChange);\n    // We might have missed an update between calling `get` in render and\n    // `addEventListener` in this handler, so we set it here. If there was\n    // no change, <PERSON>act will filter out this update as a no-op.\n    setDims(Dimensions.get('window'));\n    return () => {\n      Dimensions.removeEventListener('change', handleChange);\n    };\n  }, []);\n  return dims;\n}"], "mappings": "AAUA,YAAY;;AAEZ,OAAOA,UAAU;AACjB,SAASC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,eAAe,SAASC,mBAAmBA,CAAA,EAAG;EAC5C,IAAIC,SAAS,GAAGF,QAAQ,CAAC;MAAA,OAAMF,UAAU,CAACK,GAAG,CAAC,QAAQ,CAAC;IAAA,EAAC;IACtDC,IAAI,GAAGF,SAAS,CAAC,CAAC,CAAC;IACnBG,OAAO,GAAGH,SAAS,CAAC,CAAC,CAAC;EACxBH,SAAS,CAAC,YAAM;IACd,SAASO,YAAYA,CAACC,IAAI,EAAE;MAC1B,IAAIC,MAAM,GAAGD,IAAI,CAACC,MAAM;MACxB,IAAIA,MAAM,IAAI,IAAI,EAAE;QAClBH,OAAO,CAACG,MAAM,CAAC;MACjB;IACF;IACAV,UAAU,CAACW,gBAAgB,CAAC,QAAQ,EAAEH,YAAY,CAAC;IAInDD,OAAO,CAACP,UAAU,CAACK,GAAG,CAAC,QAAQ,CAAC,CAAC;IACjC,OAAO,YAAM;MACXL,UAAU,CAACY,mBAAmB,CAAC,QAAQ,EAAEJ,YAAY,CAAC;IACxD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,OAAOF,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}