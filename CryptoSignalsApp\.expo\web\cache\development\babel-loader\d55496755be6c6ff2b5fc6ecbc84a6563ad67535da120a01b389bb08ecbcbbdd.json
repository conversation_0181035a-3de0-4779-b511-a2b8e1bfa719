{"ast": null, "code": "import StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    padding: 16,\n    backgroundColor: '#fff'\n  },\n  pageTitle: {\n    fontSize: 24,\n    fontWeight: 'bold',\n    marginBottom: 16\n  },\n  newsItem: {\n    backgroundColor: '#f0f0f0',\n    borderRadius: 8,\n    padding: 16,\n    marginBottom: 16\n  },\n  newsTitle: {\n    fontSize: 18,\n    fontWeight: 'bold',\n    marginBottom: 8\n  },\n  newsDate: {\n    fontSize: 14,\n    color: '#777',\n    marginBottom: 8\n  },\n  newsContent: {\n    fontSize: 16\n  },\n  loadingText: {\n    fontSize: 16,\n    textAlign: 'center',\n    marginTop: 16\n  }\n});\nexport default styles;", "map": {"version": 3, "names": ["styles", "StyleSheet", "create", "container", "flex", "padding", "backgroundColor", "pageTitle", "fontSize", "fontWeight", "marginBottom", "newsItem", "borderRadius", "newsTitle", "newsDate", "color", "newsContent", "loadingText", "textAlign", "marginTop"], "sources": ["E:/CryptoSignalsApp/src/pages/News/styles.js"], "sourcesContent": ["import { StyleSheet } from 'react-native';\r\n\r\nconst styles = StyleSheet.create({\r\n  container: {\r\n    flex: 1,\r\n    padding: 16,\r\n    backgroundColor: '#fff',\r\n  },\r\n  pageTitle: {\r\n    fontSize: 24,\r\n    fontWeight: 'bold',\r\n    marginBottom: 16,\r\n  },\r\n  newsItem: {\r\n    backgroundColor: '#f0f0f0',\r\n    borderRadius: 8,\r\n    padding: 16,\r\n    marginBottom: 16,\r\n  },\r\n  newsTitle: {\r\n    fontSize: 18,\r\n    fontWeight: 'bold',\r\n    marginBottom: 8,\r\n  },\r\n  newsDate: {\r\n    fontSize: 14,\r\n    color: '#777',\r\n    marginBottom: 8,\r\n  },\r\n  newsContent: {\r\n    fontSize: 16,\r\n  },\r\n  loadingText: {\r\n    fontSize: 16,\r\n    textAlign: 'center',\r\n    marginTop: 16,\r\n  },\r\n});\r\n\r\nexport default styles;\r\n"], "mappings": ";AAEA,IAAMA,MAAM,GAAGC,UAAU,CAACC,MAAM,CAAC;EAC/BC,SAAS,EAAE;IACTC,IAAI,EAAE,CAAC;IACPC,OAAO,EAAE,EAAE;IACXC,eAAe,EAAE;EACnB,CAAC;EACDC,SAAS,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBC,YAAY,EAAE;EAChB,CAAC;EACDC,QAAQ,EAAE;IACRL,eAAe,EAAE,SAAS;IAC1BM,YAAY,EAAE,CAAC;IACfP,OAAO,EAAE,EAAE;IACXK,YAAY,EAAE;EAChB,CAAC;EACDG,SAAS,EAAE;IACTL,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBC,YAAY,EAAE;EAChB,CAAC;EACDI,QAAQ,EAAE;IACRN,QAAQ,EAAE,EAAE;IACZO,KAAK,EAAE,MAAM;IACbL,YAAY,EAAE;EAChB,CAAC;EACDM,WAAW,EAAE;IACXR,QAAQ,EAAE;EACZ,CAAC;EACDS,WAAW,EAAE;IACXT,QAAQ,EAAE,EAAE;IACZU,SAAS,EAAE,QAAQ;IACnBC,SAAS,EAAE;EACb;AACF,CAAC,CAAC;AAEF,eAAenB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}