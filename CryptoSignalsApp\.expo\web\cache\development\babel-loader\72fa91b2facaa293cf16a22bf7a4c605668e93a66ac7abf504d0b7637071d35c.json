{"ast": null, "code": "'use strict';\n\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _get from \"@babel/runtime/helpers/get\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nfunction _superPropGet(t, o, e, r) { var p = _get(_getPrototypeOf(1 & r ? t.prototype : t), o, e); return 2 & r && \"function\" == typeof p ? function (t) { return p.apply(e, t); } : p; }\nimport AnimatedInterpolation from \"./AnimatedInterpolation\";\nimport AnimatedWithChildren from \"./AnimatedWithChildren\";\nvar AnimatedDiffClamp = function (_AnimatedWithChildren) {\n  function AnimatedDiffClamp(a, min, max) {\n    var _this;\n    _classCallCheck(this, AnimatedDiffClamp);\n    _this = _callSuper(this, AnimatedDiffClamp);\n    _this._a = a;\n    _this._min = min;\n    _this._max = max;\n    _this._value = _this._lastValue = _this._a.__getValue();\n    return _this;\n  }\n  _inherits(AnimatedDiffClamp, _AnimatedWithChildren);\n  return _createClass(AnimatedDiffClamp, [{\n    key: \"__makeNative\",\n    value: function __makeNative(platformConfig) {\n      this._a.__makeNative(platformConfig);\n      _superPropGet(AnimatedDiffClamp, \"__makeNative\", this, 3)([platformConfig]);\n    }\n  }, {\n    key: \"interpolate\",\n    value: function interpolate(config) {\n      return new AnimatedInterpolation(this, config);\n    }\n  }, {\n    key: \"__getValue\",\n    value: function __getValue() {\n      var value = this._a.__getValue();\n      var diff = value - this._lastValue;\n      this._lastValue = value;\n      this._value = Math.min(Math.max(this._value + diff, this._min), this._max);\n      return this._value;\n    }\n  }, {\n    key: \"__attach\",\n    value: function __attach() {\n      this._a.__addChild(this);\n    }\n  }, {\n    key: \"__detach\",\n    value: function __detach() {\n      this._a.__removeChild(this);\n      _superPropGet(AnimatedDiffClamp, \"__detach\", this, 3)([]);\n    }\n  }, {\n    key: \"__getNativeConfig\",\n    value: function __getNativeConfig() {\n      return {\n        type: 'diffclamp',\n        input: this._a.__getNativeTag(),\n        min: this._min,\n        max: this._max\n      };\n    }\n  }]);\n}(AnimatedWithChildren);\nexport default AnimatedDiffClamp;", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "_possibleConstructorReturn", "_getPrototypeOf", "_get", "_inherits", "_callSuper", "t", "o", "e", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_superPropGet", "r", "p", "AnimatedInterpolation", "AnimatedWithChildren", "AnimatedDiffClamp", "_Animated<PERSON>ith<PERSON><PERSON><PERSON><PERSON>", "a", "min", "max", "_this", "_a", "_min", "_max", "_value", "_lastValue", "__getValue", "key", "value", "__makeNative", "platformConfig", "interpolate", "config", "diff", "Math", "__attach", "__add<PERSON><PERSON>d", "__detach", "__remove<PERSON><PERSON>d", "__getNativeConfig", "type", "input", "__getNativeTag"], "sources": ["E:/CryptoSignalsApp/node_modules/react-native-web/dist/vendor/react-native/Animated/nodes/AnimatedDiffClamp.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use strict';\n\nimport AnimatedInterpolation from './AnimatedInterpolation';\nimport AnimatedWithChildren from './AnimatedWithChildren';\nclass AnimatedDiffClamp extends AnimatedWithChildren {\n  constructor(a, min, max) {\n    super();\n    this._a = a;\n    this._min = min;\n    this._max = max;\n    this._value = this._lastValue = this._a.__getValue();\n  }\n  __makeNative(platformConfig) {\n    this._a.__makeNative(platformConfig);\n    super.__makeNative(platformConfig);\n  }\n  interpolate(config) {\n    return new AnimatedInterpolation(this, config);\n  }\n  __getValue() {\n    var value = this._a.__getValue();\n    var diff = value - this._lastValue;\n    this._lastValue = value;\n    this._value = Math.min(Math.max(this._value + diff, this._min), this._max);\n    return this._value;\n  }\n  __attach() {\n    this._a.__addChild(this);\n  }\n  __detach() {\n    this._a.__removeChild(this);\n    super.__detach();\n  }\n  __getNativeConfig() {\n    return {\n      type: 'diffclamp',\n      input: this._a.__getNativeTag(),\n      min: this._min,\n      max: this._max\n    };\n  }\n}\nexport default AnimatedDiffClamp;"], "mappings": "AAUA,YAAY;;AAAC,OAAAA,eAAA;AAAA,OAAAC,YAAA;AAAA,OAAAC,0BAAA;AAAA,OAAAC,eAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,SAAA;AAAA,SAAAC,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,GAAAL,eAAA,CAAAK,CAAA,GAAAN,0BAAA,CAAAK,CAAA,EAAAG,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAJ,CAAA,EAAAC,CAAA,QAAAN,eAAA,CAAAI,CAAA,EAAAM,WAAA,IAAAL,CAAA,CAAAM,KAAA,CAAAP,CAAA,EAAAE,CAAA;AAAA,SAAAC,0BAAA,cAAAH,CAAA,IAAAQ,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAR,CAAA,aAAAG,yBAAA,YAAAA,0BAAA,aAAAH,CAAA;AAAA,SAAAY,cAAAZ,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAW,CAAA,QAAAC,CAAA,GAAAjB,IAAA,CAAAD,eAAA,KAAAiB,CAAA,GAAAb,CAAA,CAAAS,SAAA,GAAAT,CAAA,GAAAC,CAAA,EAAAC,CAAA,cAAAW,CAAA,yBAAAC,CAAA,aAAAd,CAAA,WAAAc,CAAA,CAAAP,KAAA,CAAAL,CAAA,EAAAF,CAAA,OAAAc,CAAA;AAEb,OAAOC,qBAAqB;AAC5B,OAAOC,oBAAoB;AAA+B,IACpDC,iBAAiB,aAAAC,qBAAA;EACrB,SAAAD,kBAAYE,CAAC,EAAEC,GAAG,EAAEC,GAAG,EAAE;IAAA,IAAAC,KAAA;IAAA7B,eAAA,OAAAwB,iBAAA;IACvBK,KAAA,GAAAvB,UAAA,OAAAkB,iBAAA;IACAK,KAAA,CAAKC,EAAE,GAAGJ,CAAC;IACXG,KAAA,CAAKE,IAAI,GAAGJ,GAAG;IACfE,KAAA,CAAKG,IAAI,GAAGJ,GAAG;IACfC,KAAA,CAAKI,MAAM,GAAGJ,KAAA,CAAKK,UAAU,GAAGL,KAAA,CAAKC,EAAE,CAACK,UAAU,CAAC,CAAC;IAAC,OAAAN,KAAA;EACvD;EAACxB,SAAA,CAAAmB,iBAAA,EAAAC,qBAAA;EAAA,OAAAxB,YAAA,CAAAuB,iBAAA;IAAAY,GAAA;IAAAC,KAAA,EACD,SAAAC,YAAYA,CAACC,cAAc,EAAE;MAC3B,IAAI,CAACT,EAAE,CAACQ,YAAY,CAACC,cAAc,CAAC;MACpCpB,aAAA,CAAAK,iBAAA,4BAAmBe,cAAc;IACnC;EAAC;IAAAH,GAAA;IAAAC,KAAA,EACD,SAAAG,WAAWA,CAACC,MAAM,EAAE;MAClB,OAAO,IAAInB,qBAAqB,CAAC,IAAI,EAAEmB,MAAM,CAAC;IAChD;EAAC;IAAAL,GAAA;IAAAC,KAAA,EACD,SAAAF,UAAUA,CAAA,EAAG;MACX,IAAIE,KAAK,GAAG,IAAI,CAACP,EAAE,CAACK,UAAU,CAAC,CAAC;MAChC,IAAIO,IAAI,GAAGL,KAAK,GAAG,IAAI,CAACH,UAAU;MAClC,IAAI,CAACA,UAAU,GAAGG,KAAK;MACvB,IAAI,CAACJ,MAAM,GAAGU,IAAI,CAAChB,GAAG,CAACgB,IAAI,CAACf,GAAG,CAAC,IAAI,CAACK,MAAM,GAAGS,IAAI,EAAE,IAAI,CAACX,IAAI,CAAC,EAAE,IAAI,CAACC,IAAI,CAAC;MAC1E,OAAO,IAAI,CAACC,MAAM;IACpB;EAAC;IAAAG,GAAA;IAAAC,KAAA,EACD,SAAAO,QAAQA,CAAA,EAAG;MACT,IAAI,CAACd,EAAE,CAACe,UAAU,CAAC,IAAI,CAAC;IAC1B;EAAC;IAAAT,GAAA;IAAAC,KAAA,EACD,SAAAS,QAAQA,CAAA,EAAG;MACT,IAAI,CAAChB,EAAE,CAACiB,aAAa,CAAC,IAAI,CAAC;MAC3B5B,aAAA,CAAAK,iBAAA;IACF;EAAC;IAAAY,GAAA;IAAAC,KAAA,EACD,SAAAW,iBAAiBA,CAAA,EAAG;MAClB,OAAO;QACLC,IAAI,EAAE,WAAW;QACjBC,KAAK,EAAE,IAAI,CAACpB,EAAE,CAACqB,cAAc,CAAC,CAAC;QAC/BxB,GAAG,EAAE,IAAI,CAACI,IAAI;QACdH,GAAG,EAAE,IAAI,CAACI;MACZ,CAAC;IACH;EAAC;AAAA,EApC6BT,oBAAoB;AAsCpD,eAAeC,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}