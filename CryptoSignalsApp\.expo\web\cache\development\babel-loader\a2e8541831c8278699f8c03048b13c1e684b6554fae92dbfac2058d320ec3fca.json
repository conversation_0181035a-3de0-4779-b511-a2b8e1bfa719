{"ast": null, "code": "import NativeModulesProxy from \"./NativeModulesProxy\";\nexport function requireNativeModule(moduleName) {\n  var _ref, _globalThis$expo$modu, _globalThis$expo, _globalThis$expo$modu2, _globalThis$ExpoModul;\n  var nativeModule = (_ref = (_globalThis$expo$modu = (_globalThis$expo = globalThis.expo) == null ? void 0 : (_globalThis$expo$modu2 = _globalThis$expo.modules) == null ? void 0 : _globalThis$expo$modu2[moduleName]) != null ? _globalThis$expo$modu : (_globalThis$ExpoModul = globalThis.ExpoModules) == null ? void 0 : _globalThis$ExpoModul[moduleName]) != null ? _ref : NativeModulesProxy[moduleName];\n  if (!nativeModule) {\n    throw new Error(`Cannot find native module '${moduleName}'`);\n  }\n  return nativeModule;\n}", "map": {"version": 3, "names": ["NativeModulesProxy", "requireNativeModule", "moduleName", "_ref", "_globalThis$expo$modu", "_globalThis$expo", "_globalThis$expo$modu2", "_globalThis$ExpoModul", "nativeModule", "globalThis", "expo", "modules", "ExpoModules", "Error"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\expo-modules-core\\src\\requireNativeModule.ts"], "sourcesContent": ["import NativeModulesProxy from './NativeModulesProxy';\n\ntype ExpoObject = {\n  modules:\n    | undefined\n    | {\n        [key: string]: any;\n      };\n};\n\ndeclare global {\n  // eslint-disable-next-line no-var\n  var expo: ExpoObject | undefined;\n\n  /**\n   * @deprecated `global.ExpoModules` is deprecated, use `global.expo.modules` instead.\n   */\n  // eslint-disable-next-line no-var\n  var ExpoModules:\n    | undefined\n    | {\n        [key: string]: any;\n      };\n}\n\n/**\n * Imports the native module registered with given name. In the first place it tries to load\n * the module installed through the JSI host object and then falls back to the bridge proxy module.\n * Notice that the modules loaded from the proxy may not support some features like synchronous functions.\n *\n * @param moduleName Name of the requested native module.\n * @returns Object representing the native module.\n * @throws Error when there is no native module with given name.\n */\nexport function requireNativeModule<ModuleType = any>(moduleName: string): ModuleType {\n  const nativeModule: ModuleType =\n    globalThis.expo?.modules?.[moduleName] ??\n    globalThis.ExpoModules?.[moduleName] ??\n    NativeModulesProxy[moduleName];\n\n  if (!nativeModule) {\n    throw new Error(`Cannot find native module '${moduleName}'`);\n  }\n  return nativeModule;\n}\n"], "mappings": "AAAA,OAAOA,kBAAkB;AAkCzB,OAAM,SAAUC,mBAAmBA,CAAmBC,UAAkB;EAAA,IAAAC,IAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,sBAAA,EAAAC,qBAAA;EACtE,IAAMC,YAAY,IAAAL,IAAA,IAAAC,qBAAA,IAAAC,gBAAA,GAChBI,UAAU,CAACC,IAAI,sBAAAJ,sBAAA,GAAfD,gBAAA,CAAiBM,OAAO,qBAAxBL,sBAAA,CAA2BJ,UAAU,CAAC,YAAAE,qBAAA,IAAAG,qBAAA,GACtCE,UAAU,CAACG,WAAW,qBAAtBL,qBAAA,CAAyBL,UAAU,CAAC,YAAAC,IAAA,GACpCH,kBAAkB,CAACE,UAAU,CAAC;EAEhC,IAAI,CAACM,YAAY,EAAE;IACjB,MAAM,IAAIK,KAAK,CAAC,8BAA8BX,UAAU,GAAG,CAAC;;EAE9D,OAAOM,YAAY;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}