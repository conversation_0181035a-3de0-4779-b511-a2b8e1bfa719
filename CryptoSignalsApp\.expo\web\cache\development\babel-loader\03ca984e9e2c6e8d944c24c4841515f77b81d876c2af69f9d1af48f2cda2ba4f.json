{"ast": null, "code": "import * as React from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nvar Dummy = function Dummy(_ref) {\n  var children = _ref.children;\n  return React.createElement(React.Fragment, null, children);\n};\nexport var PanGestureHandler = Dummy;\nexport var GestureHandlerRootView = View;\nexport var GestureState = {\n  UNDETERMINED: 0,\n  FAILED: 1,\n  BEGAN: 2,\n  CANCELLED: 3,\n  ACTIVE: 4,\n  END: 5\n};", "map": {"version": 3, "names": ["React", "View", "Dummy", "_ref", "children", "createElement", "Fragment", "PanGestureHandler", "GestureHandlerRootView", "GestureState", "UNDETERMINED", "FAILED", "BEGAN", "CANCELLED", "ACTIVE", "END"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\@react-navigation\\stack\\src\\views\\GestureHandler.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { View } from 'react-native';\nimport type { PanGestureHandlerProperties } from 'react-native-gesture-handler';\n\nconst Dummy: any = ({ children }: { children: React.ReactNode }) => (\n  <>{children}</>\n);\n\nexport const PanGestureHandler =\n  Dummy as React.ComponentType<PanGestureHandlerProperties>;\n\nexport const GestureHandlerRootView = View;\n\nexport const GestureState = {\n  UNDETERMINED: 0,\n  FAILED: 1,\n  BEGAN: 2,\n  CANCELLED: 3,\n  ACTIVE: 4,\n  END: 5,\n};\n\nexport type { PanGestureHandlerGestureEvent } from 'react-native-gesture-handler';\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,IAAA;AAI9B,IAAMC,KAAU,GAAG,SAAbA,KAAUA,CAAGC,IAAA;EAAA,IAAGC,QAAA,GAAyCD,IAAA,CAAzCC,QAAA;EAAyC,OAC7DJ,KAAA,CAAAK,aAAA,CAAAL,KAAA,CAAAM,QAAA,QAAGF,QAAQ,CAAI;AAAA,CAChB;AAED,OAAO,IAAMG,iBAAiB,GAC5BL,KAAyD;AAE3D,OAAO,IAAMM,sBAAsB,GAAGP,IAAI;AAE1C,OAAO,IAAMQ,YAAY,GAAG;EAC1BC,YAAY,EAAE,CAAC;EACfC,MAAM,EAAE,CAAC;EACTC,KAAK,EAAE,CAAC;EACRC,SAAS,EAAE,CAAC;EACZC,MAAM,EAAE,CAAC;EACTC,GAAG,EAAE;AACP,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}