import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  container: {
    backgroundColor: "#0D0D0D",
    borderRadius: 8,  // Aumentando o raio da borda para um visual mais suave
    paddingVertical: 14,
    paddingHorizontal: 18,
    marginTop: 16,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    elevation: 2,  // Sombra para Android
    shadowColor: '#000',  // Sombra para iOS
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.5,
  },
  photo: {
    backgroundColor: "#fff",
    borderRadius: 24,
    width: 48,
    height: 48,
    marginRight: 18,
  },
  title: {
    color: "#fff",
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 6,
    fontFamily: "Poppins_700Bold",
  },
  time: {
    color: "#ccc",
    fontSize: 11,
    position: 'absolute',
    bottom: 10,
    right: 10,
  },
  typeContainer: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
  },
  type: {
    color: "#fff",
    fontSize: 11,
    textTransform: "uppercase",
    lineHeight: 15,
    fontFamily: "Poppins_500Medium",
  },
  dot: {
    width: 5,
    height: 5,
    backgroundColor: "#fff",
    borderRadius: 2.5,
    marginHorizontal: 6,
  },
  icon: {
    color: "#fff",
    fontSize: 20,  // Ajustando o tamanho do ícone
  },
  dFlex: {
    display: "flex",
  },
  row: {
    flexDirection: "row",
  },
});

export default styles;
