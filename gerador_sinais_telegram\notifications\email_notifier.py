#!/usr/bin/env python3
"""
Sistema de Notificações por Email
Envia alertas profissionais por email para traders
"""

import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
import logging
from datetime import datetime
from typing import List, Dict, Optional
import os
from jinja2 import Template

logger = logging.getLogger(__name__)

class EmailNotifier:
    """Sistema de notificações por email"""
    
    def __init__(self, config: Dict):
        self.smtp_server = config.get('smtp_server', 'smtp.gmail.com')
        self.smtp_port = config.get('smtp_port', 587)
        self.email = config.get('email')
        self.password = config.get('password')
        self.sender_name = config.get('sender_name', 'CryptoSignals Professional')
        self.enabled = config.get('enabled', False)
        
        if not self.email or not self.password:
            logger.warning("Email credentials not configured")
            self.enabled = False
    
    def send_signal_alert(self, signal_data: Dict, recipients: List[str]) -> bool:
        """Envia alerta de novo sinal"""
        if not self.enabled or not recipients:
            return False
        
        try:
            subject = f"🎯 New {signal_data['signal_type']} Signal: {signal_data['symbol']}"
            
            # Template HTML profissional
            html_template = """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <style>
                    body { font-family: 'Segoe UI', Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
                    .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
                    .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; }
                    .content { padding: 30px; }
                    .signal-box { background: #f8f9fa; border-left: 4px solid #007bff; padding: 20px; margin: 20px 0; border-radius: 5px; }
                    .metric { display: inline-block; margin: 10px 15px; text-align: center; }
                    .metric-label { font-size: 12px; color: #666; text-transform: uppercase; }
                    .metric-value { font-size: 18px; font-weight: bold; color: #333; }
                    .long { color: #28a745; }
                    .short { color: #dc3545; }
                    .footer { background: #f8f9fa; padding: 20px; text-align: center; font-size: 12px; color: #666; }
                    .btn { display: inline-block; padding: 12px 24px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 10px; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>CryptoSignals Professional</h1>
                        <p>New Trading Signal Alert</p>
                    </div>
                    
                    <div class="content">
                        <div class="signal-box">
                            <h2 class="{{ 'long' if signal_data['signal_type'] == 'LONG' else 'short' }}">
                                {{ signal_data['signal_type'] }} {{ signal_data['symbol'] }}
                            </h2>
                            
                            <div style="margin: 20px 0;">
                                <div class="metric">
                                    <div class="metric-label">Strategy</div>
                                    <div class="metric-value">{{ signal_data['strategy'] }}</div>
                                </div>
                                <div class="metric">
                                    <div class="metric-label">Leverage</div>
                                    <div class="metric-value">{{ signal_data.get('leverage', 'N/A') }}x</div>
                                </div>
                                <div class="metric">
                                    <div class="metric-label">Timeframe</div>
                                    <div class="metric-value">{{ signal_data.get('timeframe', 'N/A') }}</div>
                                </div>
                            </div>
                            
                            <div style="margin: 20px 0;">
                                <div class="metric">
                                    <div class="metric-label">Entry Price</div>
                                    <div class="metric-value">${{ "%.6f"|format(signal_data['entry_price']) }}</div>
                                </div>
                                <div class="metric">
                                    <div class="metric-label">Stop Loss</div>
                                    <div class="metric-value">${{ "%.6f"|format(signal_data['stop_loss']) }}</div>
                                </div>
                                <div class="metric">
                                    <div class="metric-label">Take Profit</div>
                                    <div class="metric-value">${{ "%.6f"|format(signal_data['take_profit']) }}</div>
                                </div>
                            </div>
                            
                            {% if signal_data.get('confidence') %}
                            <div style="margin: 20px 0;">
                                <div class="metric">
                                    <div class="metric-label">Confidence</div>
                                    <div class="metric-value">{{ signal_data['confidence'] }}%</div>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                        
                        <div style="text-align: center; margin: 30px 0;">
                            <a href="#" class="btn">View in Dashboard</a>
                        </div>
                        
                        <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;">
                            <strong>⚠️ Risk Warning:</strong> Trading cryptocurrencies involves substantial risk. 
                            Never invest more than you can afford to lose. This is not financial advice.
                        </div>
                    </div>
                    
                    <div class="footer">
                        <p>CryptoSignals Professional v2.0.0</p>
                        <p>Generated at {{ datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC') }}</p>
                        <p>To unsubscribe from these alerts, please contact support.</p>
                    </div>
                </div>
            </body>
            </html>
            """
            
            template = Template(html_template)
            html_content = template.render(signal_data=signal_data, datetime=datetime)
            
            # Texto simples como fallback
            text_content = f"""
CryptoSignals Professional - New Signal Alert

{signal_data['signal_type']} {signal_data['symbol']}
Strategy: {signal_data['strategy']}
Entry: ${signal_data['entry_price']:.6f}
Stop Loss: ${signal_data['stop_loss']:.6f}
Take Profit: ${signal_data['take_profit']:.6f}

Generated at {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}

Risk Warning: Trading involves substantial risk. This is not financial advice.
            """
            
            return self._send_email(recipients, subject, text_content, html_content)
            
        except Exception as e:
            logger.error(f"Erro ao enviar alerta de sinal: {e}")
            return False
    
    def send_performance_report(self, report_data: Dict, recipients: List[str]) -> bool:
        """Envia relatório de performance"""
        if not self.enabled or not recipients:
            return False
        
        try:
            subject = f"📊 CryptoSignals Performance Report - {report_data.get('period', 'Daily')}"
            
            html_template = """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <style>
                    body { font-family: 'Segoe UI', Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
                    .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
                    .header { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 30px; text-align: center; }
                    .content { padding: 30px; }
                    .stats-grid { display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px; margin: 20px 0; }
                    .stat-card { background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; border-left: 4px solid #007bff; }
                    .stat-value { font-size: 24px; font-weight: bold; color: #333; }
                    .stat-label { font-size: 12px; color: #666; text-transform: uppercase; margin-top: 5px; }
                    .positive { color: #28a745; }
                    .negative { color: #dc3545; }
                    .footer { background: #f8f9fa; padding: 20px; text-align: center; font-size: 12px; color: #666; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>📊 Performance Report</h1>
                        <p>{{ report_data.get('period', 'Daily') }} Summary</p>
                    </div>
                    
                    <div class="content">
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-value">{{ report_data.get('total_signals', 0) }}</div>
                                <div class="stat-label">Total Signals</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value {{ 'positive' if report_data.get('win_rate', 0) >= 50 else 'negative' }}">
                                    {{ report_data.get('win_rate', 0) }}%
                                </div>
                                <div class="stat-label">Win Rate</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value {{ 'positive' if report_data.get('total_profit', 0) > 0 else 'negative' }}">
                                    {{ "%.2f"|format(report_data.get('total_profit', 0)) }}%
                                </div>
                                <div class="stat-label">Total Profit</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">{{ report_data.get('active_strategies', 0) }}</div>
                                <div class="stat-label">Active Strategies</div>
                            </div>
                        </div>
                        
                        {% if report_data.get('best_strategy') %}
                        <div style="background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 20px 0;">
                            <strong>🏆 Best Strategy:</strong> {{ report_data['best_strategy']['name'] }} 
                            ({{ report_data['best_strategy']['win_rate'] }}% win rate)
                        </div>
                        {% endif %}
                        
                        {% if report_data.get('top_signals') %}
                        <h3>🎯 Top Performing Signals</h3>
                        <ul>
                        {% for signal in report_data['top_signals'][:5] %}
                            <li>{{ signal['symbol'] }} - {{ signal['profit'] }}% profit</li>
                        {% endfor %}
                        </ul>
                        {% endif %}
                    </div>
                    
                    <div class="footer">
                        <p>CryptoSignals Professional v2.0.0</p>
                        <p>Report generated at {{ datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC') }}</p>
                    </div>
                </div>
            </body>
            </html>
            """
            
            template = Template(html_template)
            html_content = template.render(report_data=report_data, datetime=datetime)
            
            text_content = f"""
CryptoSignals Performance Report - {report_data.get('period', 'Daily')}

Total Signals: {report_data.get('total_signals', 0)}
Win Rate: {report_data.get('win_rate', 0)}%
Total Profit: {report_data.get('total_profit', 0):.2f}%
Active Strategies: {report_data.get('active_strategies', 0)}

Generated at {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}
            """
            
            return self._send_email(recipients, subject, text_content, html_content)
            
        except Exception as e:
            logger.error(f"Erro ao enviar relatório de performance: {e}")
            return False
    
    def _send_email(self, recipients: List[str], subject: str, text_content: str, html_content: str = None) -> bool:
        """Envia email usando SMTP"""
        try:
            # Criar mensagem
            message = MIMEMultipart("alternative")
            message["Subject"] = subject
            message["From"] = f"{self.sender_name} <{self.email}>"
            message["To"] = ", ".join(recipients)
            
            # Adicionar conteúdo texto
            text_part = MIMEText(text_content, "plain")
            message.attach(text_part)
            
            # Adicionar conteúdo HTML se fornecido
            if html_content:
                html_part = MIMEText(html_content, "html")
                message.attach(html_part)
            
            # Conectar e enviar
            context = ssl.create_default_context()
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls(context=context)
                server.login(self.email, self.password)
                server.sendmail(self.email, recipients, message.as_string())
            
            logger.info(f"Email enviado com sucesso para {len(recipients)} destinatários")
            return True
            
        except Exception as e:
            logger.error(f"Erro ao enviar email: {e}")
            return False
    
    def test_connection(self) -> bool:
        """Testa conexão SMTP"""
        if not self.enabled:
            return False
        
        try:
            context = ssl.create_default_context()
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls(context=context)
                server.login(self.email, self.password)
            
            logger.info("Conexão SMTP testada com sucesso")
            return True
            
        except Exception as e:
            logger.error(f"Erro na conexão SMTP: {e}")
            return False
