{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport React, { useState, useEffect } from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport ScrollView from \"react-native-web/dist/exports/ScrollView\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport Alert from \"react-native-web/dist/exports/Alert\";\nimport { Button } from 'react-native-paper';\nimport Wrapper from \"../../components/Wrapper\";\nimport Card from \"../../components/Card\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport default function Channels(_ref) {\n  var _ref$route = _ref.route,\n    route = _ref$route === void 0 ? {} : _ref$route;\n  var _useState = useState(true),\n    _useState2 = _slicedToArray(_useState, 2),\n    isLoading = _useState2[0],\n    setIsLoading = _useState2[1];\n  var _useState3 = useState([]),\n    _useState4 = _slicedToArray(_useState3, 2),\n    channels = _useState4[0],\n    setChannels = _useState4[1];\n  var _ref2 = route.params || {\n      accountHasBeenRecovered: false\n    },\n    accountHasBeenRecovered = _ref2.accountHasBeenRecovered;\n  useEffect(function () {\n    var mockChannels = [{\n      id: 1,\n      name: 'Bitcoin Pro Signals',\n      description: 'Premium Bitcoin trading signals',\n      isPremium: true,\n      subscribers: 12500,\n      avatar: '₿'\n    }, {\n      id: 2,\n      name: 'Ethereum Futures',\n      description: 'Ethereum trading strategies',\n      isPremium: true,\n      subscribers: 8900,\n      avatar: '⚡'\n    }, {\n      id: 3,\n      name: 'Altcoin Gems',\n      description: 'Hidden altcoin opportunities',\n      isPremium: false,\n      subscribers: 15600,\n      avatar: '💎'\n    }];\n    setTimeout(function () {\n      setChannels(mockChannels);\n      setIsLoading(false);\n    }, 500);\n  }, []);\n  var onRefresh = function onRefresh() {\n    setRefreshing(true);\n    setTimeout(function () {\n      setRefreshing(false);\n    }, 1000);\n  };\n  var filteredChannels = channels.filter(function (channel) {\n    var matchesSearch = channel.name.toLowerCase().includes(searchQuery.toLowerCase()) || channel.description.toLowerCase().includes(searchQuery.toLowerCase());\n    var matchesFilter = true;\n    if (selectedFilter !== 'All') {\n      switch (selectedFilter) {\n        case 'Free':\n          matchesFilter = !channel.isPremium;\n          break;\n        case 'Premium':\n          matchesFilter = channel.isPremium;\n          break;\n        case 'Spot':\n          matchesFilter = channel.type === 'SPOT';\n          break;\n        case 'Futures':\n          matchesFilter = channel.type === 'FUTURES';\n          break;\n        case 'Active':\n          matchesFilter = channel.activeSignals > 0;\n          break;\n      }\n    }\n    return matchesSearch && matchesFilter;\n  });\n  var getTimeAgo = function getTimeAgo(timestamp) {\n    var now = new Date();\n    var signalTime = new Date(timestamp);\n    var diffInMinutes = Math.floor((now - signalTime) / (1000 * 60));\n    if (diffInMinutes < 60) {\n      return `${diffInMinutes}m ago`;\n    } else if (diffInMinutes < 1440) {\n      return `${Math.floor(diffInMinutes / 60)}h ago`;\n    } else {\n      return `${Math.floor(diffInMinutes / 1440)}d ago`;\n    }\n  };\n  var handleChannelPress = function handleChannelPress(channel) {\n    Alert.alert(channel.name, `View detailed signals and analytics for ${channel.name}. This feature will be available soon!`);\n  };\n  var handleSubscribe = function handleSubscribe(channel) {\n    Alert.alert(\"Subscribe\", `Subscribe to ${channel.name} for real-time notifications. This feature will be available soon!`);\n  };\n  useEffect(function () {\n    if (accountHasBeenRecovered) {\n      Alert.alert(\"Success\", \"Account recovered successfully\");\n    }\n  }, [accountHasBeenRecovered]);\n  if (isLoading) {\n    return _jsxs(Wrapper, {\n      children: [_jsx(PageTitle, {\n        text: \"Signal Channels\"\n      }), _jsxs(ScrollView, {\n        style: {\n          padding: 16\n        },\n        children: [_jsx(SkeletonCard, {}), _jsx(SkeletonCard, {}), _jsx(SkeletonCard, {})]\n      })]\n    });\n  }\n  return _jsx(Wrapper, {\n    children: _jsxs(ScrollView, {\n      style: {\n        flex: 1\n      },\n      refreshControl: _jsx(RefreshControl, {\n        refreshing: refreshing,\n        onRefresh: onRefresh\n      }),\n      children: [_jsxs(View, {\n        style: {\n          padding: 16,\n          paddingBottom: 8\n        },\n        children: [_jsx(Text, {\n          style: {\n            color: '#fff',\n            fontSize: 28,\n            fontWeight: 'bold',\n            marginBottom: 4\n          },\n          children: \"Signal Channels \\uD83D\\uDCFA\"\n        }), _jsx(Text, {\n          style: {\n            color: '#8a8a8a',\n            fontSize: 14\n          },\n          children: \"Follow the best crypto trading signal providers\"\n        })]\n      }), _jsxs(View, {\n        style: {\n          paddingHorizontal: 8,\n          marginBottom: 16\n        },\n        children: [_jsx(Text, {\n          style: {\n            color: '#fff',\n            fontSize: 18,\n            fontWeight: '600',\n            marginBottom: 12,\n            paddingHorizontal: 8\n          },\n          children: \"Channel Overview\"\n        }), _jsxs(View, {\n          style: {\n            flexDirection: 'row',\n            flexWrap: 'wrap'\n          },\n          children: [_jsx(StatCard, {\n            title: \"Total Channels\",\n            value: channelStats.totalChannels.toString(),\n            subtitle: \"Available\",\n            icon: \"\\uD83D\\uDCFA\"\n          }), _jsx(StatCard, {\n            title: \"Active Signals\",\n            value: channelStats.activeSignals.toString(),\n            change: \"+12 today\",\n            changeType: \"positive\",\n            icon: \"\\uD83D\\uDCE1\"\n          }), _jsx(StatCard, {\n            title: \"Premium Channels\",\n            value: channelStats.premiumChannels.toString(),\n            subtitle: \"High accuracy\",\n            icon: \"\\uD83D\\uDC8E\"\n          }), _jsx(StatCard, {\n            title: \"Avg Success Rate\",\n            value: channelStats.avgSuccessRate,\n            change: \"+2.1%\",\n            changeType: \"positive\",\n            icon: \"\\uD83C\\uDFAF\"\n          })]\n        })]\n      }), _jsx(View, {\n        style: {\n          paddingHorizontal: 16,\n          marginBottom: 16\n        },\n        children: _jsx(Searchbar, {\n          placeholder: \"Search channels...\",\n          onChangeText: setSearchQuery,\n          value: searchQuery,\n          style: {\n            backgroundColor: '#2a2a2a',\n            elevation: 0\n          },\n          inputStyle: {\n            color: '#fff'\n          },\n          iconColor: \"#8a8a8a\",\n          placeholderTextColor: \"#8a8a8a\"\n        })\n      }), _jsx(View, {\n        style: {\n          paddingHorizontal: 16,\n          marginBottom: 16\n        },\n        children: _jsx(ScrollView, {\n          horizontal: true,\n          showsHorizontalScrollIndicator: false,\n          children: filters.map(function (filter) {\n            return _jsx(Chip, {\n              selected: selectedFilter === filter,\n              onPress: function onPress() {\n                return setSelectedFilter(filter);\n              },\n              style: {\n                marginRight: 8,\n                backgroundColor: selectedFilter === filter ? '#FECB37' : '#2a2a2a'\n              },\n              textStyle: {\n                color: selectedFilter === filter ? '#000' : '#fff',\n                fontWeight: '500'\n              },\n              children: filter\n            }, filter);\n          })\n        })\n      }), _jsxs(View, {\n        style: {\n          paddingHorizontal: 16,\n          marginBottom: 20\n        },\n        children: [_jsxs(Text, {\n          style: {\n            color: '#fff',\n            fontSize: 18,\n            fontWeight: '600',\n            marginBottom: 12\n          },\n          children: [\"Available Channels (\", filteredChannels.length, \")\"]\n        }), filteredChannels.length === 0 ? _jsx(Card, {\n          children: _jsx(Text, {\n            style: {\n              color: '#8a8a8a',\n              fontSize: 14,\n              textAlign: 'center',\n              padding: 20\n            },\n            children: \"No channels found matching your criteria.\"\n          })\n        }) : filteredChannels.map(function (channel) {\n          return _jsx(Card, {\n            style: {\n              marginBottom: 12\n            },\n            onPress: function onPress() {\n              return handleChannelPress(channel);\n            },\n            children: _jsxs(View, {\n              style: {\n                flexDirection: 'row',\n                alignItems: 'flex-start'\n              },\n              children: [_jsx(View, {\n                style: {\n                  backgroundColor: '#333',\n                  borderRadius: 12,\n                  padding: 12,\n                  marginRight: 12,\n                  alignItems: 'center',\n                  justifyContent: 'center'\n                },\n                children: _jsx(Text, {\n                  style: {\n                    fontSize: 24\n                  },\n                  children: channel.avatar\n                })\n              }), _jsxs(View, {\n                style: {\n                  flex: 1\n                },\n                children: [_jsxs(View, {\n                  style: {\n                    flexDirection: 'row',\n                    alignItems: 'center',\n                    marginBottom: 4\n                  },\n                  children: [_jsx(Text, {\n                    style: {\n                      color: '#fff',\n                      fontSize: 16,\n                      fontWeight: '600',\n                      flex: 1\n                    },\n                    children: channel.name\n                  }), channel.verified && _jsx(Text, {\n                    style: {\n                      fontSize: 12,\n                      marginLeft: 4\n                    },\n                    children: \"\\u2705\"\n                  })]\n                }), _jsxs(View, {\n                  style: {\n                    flexDirection: 'row',\n                    alignItems: 'center',\n                    marginBottom: 6\n                  },\n                  children: [_jsx(View, {\n                    style: {\n                      backgroundColor: channel.isPremium ? '#FECB37' : '#4CAF50',\n                      paddingHorizontal: 6,\n                      paddingVertical: 2,\n                      borderRadius: 4,\n                      marginRight: 8\n                    },\n                    children: _jsx(Text, {\n                      style: {\n                        color: channel.isPremium ? '#000' : '#fff',\n                        fontSize: 10,\n                        fontWeight: '600'\n                      },\n                      children: channel.isPremium ? 'PREMIUM' : 'FREE'\n                    })\n                  }), _jsx(View, {\n                    style: {\n                      backgroundColor: '#333',\n                      paddingHorizontal: 6,\n                      paddingVertical: 2,\n                      borderRadius: 4,\n                      marginRight: 8\n                    },\n                    children: _jsx(Text, {\n                      style: {\n                        color: '#fff',\n                        fontSize: 10,\n                        fontWeight: '500'\n                      },\n                      children: channel.type\n                    })\n                  }), channel.activeSignals > 0 && _jsx(View, {\n                    style: {\n                      backgroundColor: '#F44336',\n                      paddingHorizontal: 6,\n                      paddingVertical: 2,\n                      borderRadius: 10\n                    },\n                    children: _jsxs(Text, {\n                      style: {\n                        color: '#fff',\n                        fontSize: 8,\n                        fontWeight: '500'\n                      },\n                      children: [channel.activeSignals, \" ACTIVE\"]\n                    })\n                  })]\n                }), _jsx(Text, {\n                  style: {\n                    color: '#ccc',\n                    fontSize: 13,\n                    marginBottom: 8,\n                    lineHeight: 18\n                  },\n                  children: channel.description\n                }), _jsxs(View, {\n                  style: {\n                    flexDirection: 'row',\n                    justifyContent: 'space-between',\n                    marginBottom: 8\n                  },\n                  children: [_jsxs(View, {\n                    style: {\n                      flex: 1,\n                      marginRight: 8\n                    },\n                    children: [_jsx(Text, {\n                      style: {\n                        color: '#8a8a8a',\n                        fontSize: 10\n                      },\n                      children: \"Subscribers\"\n                    }), _jsx(Text, {\n                      style: {\n                        color: '#fff',\n                        fontSize: 12,\n                        fontWeight: '500'\n                      },\n                      children: channel.subscribers.toLocaleString()\n                    })]\n                  }), _jsxs(View, {\n                    style: {\n                      flex: 1,\n                      marginRight: 8\n                    },\n                    children: [_jsx(Text, {\n                      style: {\n                        color: '#8a8a8a',\n                        fontSize: 10\n                      },\n                      children: \"Success Rate\"\n                    }), _jsxs(Text, {\n                      style: {\n                        color: '#4CAF50',\n                        fontSize: 12,\n                        fontWeight: '500'\n                      },\n                      children: [channel.successRate, \"%\"]\n                    })]\n                  }), _jsxs(View, {\n                    style: {\n                      flex: 1,\n                      marginRight: 8\n                    },\n                    children: [_jsx(Text, {\n                      style: {\n                        color: '#8a8a8a',\n                        fontSize: 10\n                      },\n                      children: \"Performance\"\n                    }), _jsx(Text, {\n                      style: {\n                        color: '#FECB37',\n                        fontSize: 12,\n                        fontWeight: '500'\n                      },\n                      children: channel.performance\n                    })]\n                  }), _jsxs(View, {\n                    style: {\n                      flex: 1\n                    },\n                    children: [_jsx(Text, {\n                      style: {\n                        color: '#8a8a8a',\n                        fontSize: 10\n                      },\n                      children: \"Last Signal\"\n                    }), _jsx(Text, {\n                      style: {\n                        color: '#8a8a8a',\n                        fontSize: 12,\n                        fontWeight: '500'\n                      },\n                      children: getTimeAgo(channel.lastSignalAt)\n                    })]\n                  })]\n                }), _jsx(ProgressBar, {\n                  progress: channel.successRate / 100,\n                  color: \"#4CAF50\",\n                  style: {\n                    height: 4,\n                    borderRadius: 2,\n                    marginBottom: 12\n                  }\n                }), _jsxs(View, {\n                  style: {\n                    flexDirection: 'row',\n                    justifyContent: 'space-between'\n                  },\n                  children: [_jsx(Button, {\n                    mode: \"outlined\",\n                    onPress: function onPress() {\n                      return handleChannelPress(channel);\n                    },\n                    style: {\n                      flex: 1,\n                      marginRight: 8,\n                      borderColor: '#FECB37'\n                    },\n                    labelStyle: {\n                      color: '#FECB37',\n                      fontWeight: '500',\n                      fontSize: 12\n                    },\n                    children: \"View Signals\"\n                  }), _jsx(Button, {\n                    mode: \"contained\",\n                    onPress: function onPress() {\n                      return handleSubscribe(channel);\n                    },\n                    style: {\n                      flex: 1,\n                      marginLeft: 8,\n                      backgroundColor: '#FECB37'\n                    },\n                    labelStyle: {\n                      color: '#000',\n                      fontWeight: '500',\n                      fontSize: 12\n                    },\n                    children: \"Subscribe\"\n                  })]\n                })]\n              })]\n            })\n          }, channel.id);\n        })]\n      })]\n    })\n  });\n}", "map": {"version": 3, "names": ["React", "useState", "useEffect", "View", "ScrollView", "Text", "<PERSON><PERSON>", "<PERSON><PERSON>", "Wrapper", "Card", "jsx", "_jsx", "jsxs", "_jsxs", "Channels", "_ref", "_ref$route", "route", "_useState", "_useState2", "_slicedToArray", "isLoading", "setIsLoading", "_useState3", "_useState4", "channels", "setChannels", "_ref2", "params", "accountHasBeenRecovered", "mockChannels", "id", "name", "description", "isPremium", "subscribers", "avatar", "setTimeout", "onRefresh", "setRefreshing", "filteredChannels", "filter", "channel", "matchesSearch", "toLowerCase", "includes", "searchQuery", "matchesFilter", "<PERSON><PERSON><PERSON><PERSON>", "type", "activeSignals", "getTimeAgo", "timestamp", "now", "Date", "signalTime", "diffInMinutes", "Math", "floor", "handleChannelPress", "alert", "handleSubscribe", "children", "Page<PERSON><PERSON>le", "text", "style", "padding", "SkeletonCard", "flex", "refreshControl", "RefreshControl", "refreshing", "paddingBottom", "color", "fontSize", "fontWeight", "marginBottom", "paddingHorizontal", "flexDirection", "flexWrap", "StatCard", "title", "value", "channelStats", "totalChannels", "toString", "subtitle", "icon", "change", "changeType", "premiumChannels", "avgSuccessRate", "Searchbar", "placeholder", "onChangeText", "setSearch<PERSON>uery", "backgroundColor", "elevation", "inputStyle", "iconColor", "placeholderTextColor", "horizontal", "showsHorizontalScrollIndicator", "filters", "map", "Chip", "selected", "onPress", "setSelectedFilter", "marginRight", "textStyle", "length", "textAlign", "alignItems", "borderRadius", "justifyContent", "verified", "marginLeft", "paddingVertical", "lineHeight", "toLocaleString", "successRate", "performance", "lastSignalAt", "ProgressBar", "progress", "height", "mode", "borderColor", "labelStyle"], "sources": ["E:/CryptoSignalsApp/src/pages/Channels/index.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { View, ScrollView, Text, Alert } from 'react-native';\r\nimport { Button } from 'react-native-paper';\r\nimport Wrapper from '../../components/Wrapper';\r\nimport Card from '../../components/Card';\r\n\r\nexport default function Channels({route = {}}) {\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [channels, setChannels] = useState([]);\r\n\r\n  const { accountHasBeenRecovered } = route.params || {accountHasBeenRecovered: false};\r\n\r\n  useEffect(() => {\r\n    const mockChannels = [\r\n      {\r\n        id: 1,\r\n        name: 'Bitcoin Pro Signals',\r\n        description: 'Premium Bitcoin trading signals',\r\n        isPremium: true,\r\n        subscribers: 12500,\r\n        avatar: '₿'\r\n      },\r\n      {\r\n        id: 2,\r\n        name: 'Ethereum Futures',\r\n        description: 'Ethereum trading strategies',\r\n        isPremium: true,\r\n        subscribers: 8900,\r\n        avatar: '⚡'\r\n      },\r\n      {\r\n        id: 3,\r\n        name: 'Altcoin Gems',\r\n        description: 'Hidden altcoin opportunities',\r\n        isPremium: false,\r\n        subscribers: 15600,\r\n        avatar: '💎'\r\n      }\r\n    ];\r\n\r\n    setTimeout(() => {\r\n      setChannels(mockChannels);\r\n      setIsLoading(false);\r\n    }, 500);\r\n  }, []);\r\n\r\n  const onRefresh = () => {\r\n    setRefreshing(true);\r\n    setTimeout(() => {\r\n      setRefreshing(false);\r\n    }, 1000);\r\n  };\r\n\r\n  const filteredChannels = channels.filter(channel => {\r\n    const matchesSearch = channel.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\r\n                         channel.description.toLowerCase().includes(searchQuery.toLowerCase());\r\n\r\n    let matchesFilter = true;\r\n    if (selectedFilter !== 'All') {\r\n      switch (selectedFilter) {\r\n        case 'Free':\r\n          matchesFilter = !channel.isPremium;\r\n          break;\r\n        case 'Premium':\r\n          matchesFilter = channel.isPremium;\r\n          break;\r\n        case 'Spot':\r\n          matchesFilter = channel.type === 'SPOT';\r\n          break;\r\n        case 'Futures':\r\n          matchesFilter = channel.type === 'FUTURES';\r\n          break;\r\n        case 'Active':\r\n          matchesFilter = channel.activeSignals > 0;\r\n          break;\r\n      }\r\n    }\r\n\r\n    return matchesSearch && matchesFilter;\r\n  });\r\n\r\n\r\n  const getTimeAgo = (timestamp) => {\r\n    const now = new Date();\r\n    const signalTime = new Date(timestamp);\r\n    const diffInMinutes = Math.floor((now - signalTime) / (1000 * 60));\r\n\r\n    if (diffInMinutes < 60) {\r\n      return `${diffInMinutes}m ago`;\r\n    } else if (diffInMinutes < 1440) {\r\n      return `${Math.floor(diffInMinutes / 60)}h ago`;\r\n    } else {\r\n      return `${Math.floor(diffInMinutes / 1440)}d ago`;\r\n    }\r\n  };\r\n\r\n  const handleChannelPress = (channel) => {\r\n    Alert.alert(\r\n      channel.name,\r\n      `View detailed signals and analytics for ${channel.name}. This feature will be available soon!`\r\n    );\r\n  };\r\n\r\n  const handleSubscribe = (channel) => {\r\n    Alert.alert(\r\n      \"Subscribe\",\r\n      `Subscribe to ${channel.name} for real-time notifications. This feature will be available soon!`\r\n    );\r\n  };\r\n\r\n  useEffect(() => {\r\n    if(accountHasBeenRecovered) {\r\n      Alert.alert(\"Success\", \"Account recovered successfully\");\r\n    }\r\n  }, [accountHasBeenRecovered]);\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <Wrapper>\r\n        <PageTitle text=\"Signal Channels\" />\r\n        <ScrollView style={{ padding: 16 }}>\r\n          <SkeletonCard />\r\n          <SkeletonCard />\r\n          <SkeletonCard />\r\n        </ScrollView>\r\n      </Wrapper>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Wrapper>\r\n      <ScrollView\r\n        style={{ flex: 1 }}\r\n        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}\r\n      >\r\n        {/* Header */}\r\n        <View style={{ padding: 16, paddingBottom: 8 }}>\r\n          <Text style={{\r\n            color: '#fff',\r\n            fontSize: 28,\r\n            fontWeight: 'bold',\r\n            marginBottom: 4\r\n          }}>\r\n            Signal Channels 📺\r\n          </Text>\r\n          <Text style={{\r\n            color: '#8a8a8a',\r\n            fontSize: 14\r\n          }}>\r\n            Follow the best crypto trading signal providers\r\n          </Text>\r\n        </View>\r\n\r\n        {/* Channel Stats */}\r\n        <View style={{ paddingHorizontal: 8, marginBottom: 16 }}>\r\n          <Text style={{\r\n            color: '#fff',\r\n            fontSize: 18,\r\n            fontWeight: '600',\r\n            marginBottom: 12,\r\n            paddingHorizontal: 8\r\n          }}>\r\n            Channel Overview\r\n          </Text>\r\n          <View style={{ flexDirection: 'row', flexWrap: 'wrap' }}>\r\n            <StatCard\r\n              title=\"Total Channels\"\r\n              value={channelStats.totalChannels.toString()}\r\n              subtitle=\"Available\"\r\n              icon=\"📺\"\r\n            />\r\n            <StatCard\r\n              title=\"Active Signals\"\r\n              value={channelStats.activeSignals.toString()}\r\n              change=\"+12 today\"\r\n              changeType=\"positive\"\r\n              icon=\"📡\"\r\n            />\r\n            <StatCard\r\n              title=\"Premium Channels\"\r\n              value={channelStats.premiumChannels.toString()}\r\n              subtitle=\"High accuracy\"\r\n              icon=\"💎\"\r\n            />\r\n            <StatCard\r\n              title=\"Avg Success Rate\"\r\n              value={channelStats.avgSuccessRate}\r\n              change=\"+2.1%\"\r\n              changeType=\"positive\"\r\n              icon=\"🎯\"\r\n            />\r\n          </View>\r\n        </View>\r\n\r\n        {/* Search Bar */}\r\n        <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>\r\n          <Searchbar\r\n            placeholder=\"Search channels...\"\r\n            onChangeText={setSearchQuery}\r\n            value={searchQuery}\r\n            style={{ backgroundColor: '#2a2a2a', elevation: 0 }}\r\n            inputStyle={{ color: '#fff' }}\r\n            iconColor=\"#8a8a8a\"\r\n            placeholderTextColor=\"#8a8a8a\"\r\n          />\r\n        </View>\r\n\r\n        {/* Filters */}\r\n        <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>\r\n          <ScrollView horizontal showsHorizontalScrollIndicator={false}>\r\n            {filters.map((filter) => (\r\n              <Chip\r\n                key={filter}\r\n                selected={selectedFilter === filter}\r\n                onPress={() => setSelectedFilter(filter)}\r\n                style={{\r\n                  marginRight: 8,\r\n                  backgroundColor: selectedFilter === filter ? '#FECB37' : '#2a2a2a'\r\n                }}\r\n                textStyle={{\r\n                  color: selectedFilter === filter ? '#000' : '#fff',\r\n                  fontWeight: '500'\r\n                }}\r\n              >\r\n                {filter}\r\n              </Chip>\r\n            ))}\r\n          </ScrollView>\r\n        </View>\r\n\r\n        {/* Channels List */}\r\n        <View style={{ paddingHorizontal: 16, marginBottom: 20 }}>\r\n          <Text style={{\r\n            color: '#fff',\r\n            fontSize: 18,\r\n            fontWeight: '600',\r\n            marginBottom: 12\r\n          }}>\r\n            Available Channels ({filteredChannels.length})\r\n          </Text>\r\n\r\n          {filteredChannels.length === 0 ? (\r\n            <Card>\r\n              <Text style={{\r\n                color: '#8a8a8a',\r\n                fontSize: 14,\r\n                textAlign: 'center',\r\n                padding: 20\r\n              }}>\r\n                No channels found matching your criteria.\r\n              </Text>\r\n            </Card>\r\n          ) : (\r\n            filteredChannels.map((channel) => (\r\n              <Card key={channel.id} style={{ marginBottom: 12 }} onPress={() => handleChannelPress(channel)}>\r\n                <View style={{ flexDirection: 'row', alignItems: 'flex-start' }}>\r\n                  <View style={{\r\n                    backgroundColor: '#333',\r\n                    borderRadius: 12,\r\n                    padding: 12,\r\n                    marginRight: 12,\r\n                    alignItems: 'center',\r\n                    justifyContent: 'center'\r\n                  }}>\r\n                    <Text style={{ fontSize: 24 }}>{channel.avatar}</Text>\r\n                  </View>\r\n\r\n                  <View style={{ flex: 1 }}>\r\n                    <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 4 }}>\r\n                      <Text style={{\r\n                        color: '#fff',\r\n                        fontSize: 16,\r\n                        fontWeight: '600',\r\n                        flex: 1\r\n                      }}>\r\n                        {channel.name}\r\n                      </Text>\r\n                      {channel.verified && (\r\n                        <Text style={{ fontSize: 12, marginLeft: 4 }}>✅</Text>\r\n                      )}\r\n                    </View>\r\n\r\n                    <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 6 }}>\r\n                      <View style={{\r\n                        backgroundColor: channel.isPremium ? '#FECB37' : '#4CAF50',\r\n                        paddingHorizontal: 6,\r\n                        paddingVertical: 2,\r\n                        borderRadius: 4,\r\n                        marginRight: 8\r\n                      }}>\r\n                        <Text style={{\r\n                          color: channel.isPremium ? '#000' : '#fff',\r\n                          fontSize: 10,\r\n                          fontWeight: '600'\r\n                        }}>\r\n                          {channel.isPremium ? 'PREMIUM' : 'FREE'}\r\n                        </Text>\r\n                      </View>\r\n                      <View style={{\r\n                        backgroundColor: '#333',\r\n                        paddingHorizontal: 6,\r\n                        paddingVertical: 2,\r\n                        borderRadius: 4,\r\n                        marginRight: 8\r\n                      }}>\r\n                        <Text style={{\r\n                          color: '#fff',\r\n                          fontSize: 10,\r\n                          fontWeight: '500'\r\n                        }}>\r\n                          {channel.type}\r\n                        </Text>\r\n                      </View>\r\n                      {channel.activeSignals > 0 && (\r\n                        <View style={{\r\n                          backgroundColor: '#F44336',\r\n                          paddingHorizontal: 6,\r\n                          paddingVertical: 2,\r\n                          borderRadius: 10\r\n                        }}>\r\n                          <Text style={{\r\n                            color: '#fff',\r\n                            fontSize: 8,\r\n                            fontWeight: '500'\r\n                          }}>\r\n                            {channel.activeSignals} ACTIVE\r\n                          </Text>\r\n                        </View>\r\n                      )}\r\n                    </View>\r\n\r\n                    <Text style={{\r\n                      color: '#ccc',\r\n                      fontSize: 13,\r\n                      marginBottom: 8,\r\n                      lineHeight: 18\r\n                    }}>\r\n                      {channel.description}\r\n                    </Text>\r\n\r\n                    <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8 }}>\r\n                      <View style={{ flex: 1, marginRight: 8 }}>\r\n                        <Text style={{ color: '#8a8a8a', fontSize: 10 }}>Subscribers</Text>\r\n                        <Text style={{ color: '#fff', fontSize: 12, fontWeight: '500' }}>{channel.subscribers.toLocaleString()}</Text>\r\n                      </View>\r\n                      <View style={{ flex: 1, marginRight: 8 }}>\r\n                        <Text style={{ color: '#8a8a8a', fontSize: 10 }}>Success Rate</Text>\r\n                        <Text style={{ color: '#4CAF50', fontSize: 12, fontWeight: '500' }}>{channel.successRate}%</Text>\r\n                      </View>\r\n                      <View style={{ flex: 1, marginRight: 8 }}>\r\n                        <Text style={{ color: '#8a8a8a', fontSize: 10 }}>Performance</Text>\r\n                        <Text style={{ color: '#FECB37', fontSize: 12, fontWeight: '500' }}>{channel.performance}</Text>\r\n                      </View>\r\n                      <View style={{ flex: 1 }}>\r\n                        <Text style={{ color: '#8a8a8a', fontSize: 10 }}>Last Signal</Text>\r\n                        <Text style={{ color: '#8a8a8a', fontSize: 12, fontWeight: '500' }}>{getTimeAgo(channel.lastSignalAt)}</Text>\r\n                      </View>\r\n                    </View>\r\n\r\n                    <ProgressBar\r\n                      progress={channel.successRate / 100}\r\n                      color=\"#4CAF50\"\r\n                      style={{ height: 4, borderRadius: 2, marginBottom: 12 }}\r\n                    />\r\n\r\n                    <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>\r\n                      <Button\r\n                        mode=\"outlined\"\r\n                        onPress={() => handleChannelPress(channel)}\r\n                        style={{\r\n                          flex: 1,\r\n                          marginRight: 8,\r\n                          borderColor: '#FECB37'\r\n                        }}\r\n                        labelStyle={{\r\n                          color: '#FECB37',\r\n                          fontWeight: '500',\r\n                          fontSize: 12\r\n                        }}\r\n                      >\r\n                        View Signals\r\n                      </Button>\r\n                      <Button\r\n                        mode=\"contained\"\r\n                        onPress={() => handleSubscribe(channel)}\r\n                        style={{\r\n                          flex: 1,\r\n                          marginLeft: 8,\r\n                          backgroundColor: '#FECB37'\r\n                        }}\r\n                        labelStyle={{\r\n                          color: '#000',\r\n                          fontWeight: '500',\r\n                          fontSize: 12\r\n                        }}\r\n                      >\r\n                        Subscribe\r\n                      </Button>\r\n                    </View>\r\n                  </View>\r\n                </View>\r\n              </Card>\r\n            ))\r\n          )}\r\n        </View>\r\n      </ScrollView>\r\n    </Wrapper>\r\n  );\r\n}"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,KAAA;AAEnD,SAASC,MAAM,QAAQ,oBAAoB;AAC3C,OAAOC,OAAO;AACd,OAAOC,IAAI;AAA8B,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAEzC,eAAe,SAASC,QAAQA,CAAAC,IAAA,EAAe;EAAA,IAAAC,UAAA,GAAAD,IAAA,CAAbE,KAAK;IAALA,KAAK,GAAAD,UAAA,cAAG,CAAC,CAAC,GAAAA,UAAA;EAC1C,IAAAE,SAAA,GAAkCjB,QAAQ,CAAC,IAAI,CAAC;IAAAkB,UAAA,GAAAC,cAAA,CAAAF,SAAA;IAAzCG,SAAS,GAAAF,UAAA;IAAEG,YAAY,GAAAH,UAAA;EAC9B,IAAAI,UAAA,GAAgCtB,QAAQ,CAAC,EAAE,CAAC;IAAAuB,UAAA,GAAAJ,cAAA,CAAAG,UAAA;IAArCE,QAAQ,GAAAD,UAAA;IAAEE,WAAW,GAAAF,UAAA;EAE5B,IAAAG,KAAA,GAAoCV,KAAK,CAACW,MAAM,IAAI;MAACC,uBAAuB,EAAE;IAAK,CAAC;IAA5EA,uBAAuB,GAAAF,KAAA,CAAvBE,uBAAuB;EAE/B3B,SAAS,CAAC,YAAM;IACd,IAAM4B,YAAY,GAAG,CACnB;MACEC,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,qBAAqB;MAC3BC,WAAW,EAAE,iCAAiC;MAC9CC,SAAS,EAAE,IAAI;MACfC,WAAW,EAAE,KAAK;MAClBC,MAAM,EAAE;IACV,CAAC,EACD;MACEL,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,kBAAkB;MACxBC,WAAW,EAAE,6BAA6B;MAC1CC,SAAS,EAAE,IAAI;MACfC,WAAW,EAAE,IAAI;MACjBC,MAAM,EAAE;IACV,CAAC,EACD;MACEL,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,cAAc;MACpBC,WAAW,EAAE,8BAA8B;MAC3CC,SAAS,EAAE,KAAK;MAChBC,WAAW,EAAE,KAAK;MAClBC,MAAM,EAAE;IACV,CAAC,CACF;IAEDC,UAAU,CAAC,YAAM;MACfX,WAAW,CAACI,YAAY,CAAC;MACzBR,YAAY,CAAC,KAAK,CAAC;IACrB,CAAC,EAAE,GAAG,CAAC;EACT,CAAC,EAAE,EAAE,CAAC;EAEN,IAAMgB,SAAS,GAAG,SAAZA,SAASA,CAAA,EAAS;IACtBC,aAAa,CAAC,IAAI,CAAC;IACnBF,UAAU,CAAC,YAAM;MACfE,aAAa,CAAC,KAAK,CAAC;IACtB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,IAAMC,gBAAgB,GAAGf,QAAQ,CAACgB,MAAM,CAAC,UAAAC,OAAO,EAAI;IAClD,IAAMC,aAAa,GAAGD,OAAO,CAACV,IAAI,CAACY,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACC,WAAW,CAACF,WAAW,CAAC,CAAC,CAAC,IAC/DF,OAAO,CAACT,WAAW,CAACW,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACC,WAAW,CAACF,WAAW,CAAC,CAAC,CAAC;IAE1F,IAAIG,aAAa,GAAG,IAAI;IACxB,IAAIC,cAAc,KAAK,KAAK,EAAE;MAC5B,QAAQA,cAAc;QACpB,KAAK,MAAM;UACTD,aAAa,GAAG,CAACL,OAAO,CAACR,SAAS;UAClC;QACF,KAAK,SAAS;UACZa,aAAa,GAAGL,OAAO,CAACR,SAAS;UACjC;QACF,KAAK,MAAM;UACTa,aAAa,GAAGL,OAAO,CAACO,IAAI,KAAK,MAAM;UACvC;QACF,KAAK,SAAS;UACZF,aAAa,GAAGL,OAAO,CAACO,IAAI,KAAK,SAAS;UAC1C;QACF,KAAK,QAAQ;UACXF,aAAa,GAAGL,OAAO,CAACQ,aAAa,GAAG,CAAC;UACzC;MACJ;IACF;IAEA,OAAOP,aAAa,IAAII,aAAa;EACvC,CAAC,CAAC;EAGF,IAAMI,UAAU,GAAG,SAAbA,UAAUA,CAAIC,SAAS,EAAK;IAChC,IAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,IAAMC,UAAU,GAAG,IAAID,IAAI,CAACF,SAAS,CAAC;IACtC,IAAMI,aAAa,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACL,GAAG,GAAGE,UAAU,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;IAElE,IAAIC,aAAa,GAAG,EAAE,EAAE;MACtB,OAAO,GAAGA,aAAa,OAAO;IAChC,CAAC,MAAM,IAAIA,aAAa,GAAG,IAAI,EAAE;MAC/B,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,EAAE,CAAC,OAAO;IACjD,CAAC,MAAM;MACL,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,IAAI,CAAC,OAAO;IACnD;EACF,CAAC;EAED,IAAMG,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAIjB,OAAO,EAAK;IACtCpC,KAAK,CAACsD,KAAK,CACTlB,OAAO,CAACV,IAAI,EACZ,2CAA2CU,OAAO,CAACV,IAAI,wCACzD,CAAC;EACH,CAAC;EAED,IAAM6B,eAAe,GAAG,SAAlBA,eAAeA,CAAInB,OAAO,EAAK;IACnCpC,KAAK,CAACsD,KAAK,CACT,WAAW,EACX,gBAAgBlB,OAAO,CAACV,IAAI,oEAC9B,CAAC;EACH,CAAC;EAED9B,SAAS,CAAC,YAAM;IACd,IAAG2B,uBAAuB,EAAE;MAC1BvB,KAAK,CAACsD,KAAK,CAAC,SAAS,EAAE,gCAAgC,CAAC;IAC1D;EACF,CAAC,EAAE,CAAC/B,uBAAuB,CAAC,CAAC;EAE7B,IAAIR,SAAS,EAAE;IACb,OACER,KAAA,CAACL,OAAO;MAAAsD,QAAA,GACNnD,IAAA,CAACoD,SAAS;QAACC,IAAI,EAAC;MAAiB,CAAE,CAAC,EACpCnD,KAAA,CAACT,UAAU;QAAC6D,KAAK,EAAE;UAAEC,OAAO,EAAE;QAAG,CAAE;QAAAJ,QAAA,GACjCnD,IAAA,CAACwD,YAAY,IAAE,CAAC,EAChBxD,IAAA,CAACwD,YAAY,IAAE,CAAC,EAChBxD,IAAA,CAACwD,YAAY,IAAE,CAAC;MAAA,CACN,CAAC;IAAA,CACN,CAAC;EAEd;EAEA,OACExD,IAAA,CAACH,OAAO;IAAAsD,QAAA,EACNjD,KAAA,CAACT,UAAU;MACT6D,KAAK,EAAE;QAAEG,IAAI,EAAE;MAAE,CAAE;MACnBC,cAAc,EAAE1D,IAAA,CAAC2D,cAAc;QAACC,UAAU,EAAEA,UAAW;QAACjC,SAAS,EAAEA;MAAU,CAAE,CAAE;MAAAwB,QAAA,GAGjFjD,KAAA,CAACV,IAAI;QAAC8D,KAAK,EAAE;UAAEC,OAAO,EAAE,EAAE;UAAEM,aAAa,EAAE;QAAE,CAAE;QAAAV,QAAA,GAC7CnD,IAAA,CAACN,IAAI;UAAC4D,KAAK,EAAE;YACXQ,KAAK,EAAE,MAAM;YACbC,QAAQ,EAAE,EAAE;YACZC,UAAU,EAAE,MAAM;YAClBC,YAAY,EAAE;UAChB,CAAE;UAAAd,QAAA,EAAC;QAEH,CAAM,CAAC,EACPnD,IAAA,CAACN,IAAI;UAAC4D,KAAK,EAAE;YACXQ,KAAK,EAAE,SAAS;YAChBC,QAAQ,EAAE;UACZ,CAAE;UAAAZ,QAAA,EAAC;QAEH,CAAM,CAAC;MAAA,CACH,CAAC,EAGPjD,KAAA,CAACV,IAAI;QAAC8D,KAAK,EAAE;UAAEY,iBAAiB,EAAE,CAAC;UAAED,YAAY,EAAE;QAAG,CAAE;QAAAd,QAAA,GACtDnD,IAAA,CAACN,IAAI;UAAC4D,KAAK,EAAE;YACXQ,KAAK,EAAE,MAAM;YACbC,QAAQ,EAAE,EAAE;YACZC,UAAU,EAAE,KAAK;YACjBC,YAAY,EAAE,EAAE;YAChBC,iBAAiB,EAAE;UACrB,CAAE;UAAAf,QAAA,EAAC;QAEH,CAAM,CAAC,EACPjD,KAAA,CAACV,IAAI;UAAC8D,KAAK,EAAE;YAAEa,aAAa,EAAE,KAAK;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAAjB,QAAA,GACtDnD,IAAA,CAACqE,QAAQ;YACPC,KAAK,EAAC,gBAAgB;YACtBC,KAAK,EAAEC,YAAY,CAACC,aAAa,CAACC,QAAQ,CAAC,CAAE;YAC7CC,QAAQ,EAAC,WAAW;YACpBC,IAAI,EAAC;UAAI,CACV,CAAC,EACF5E,IAAA,CAACqE,QAAQ;YACPC,KAAK,EAAC,gBAAgB;YACtBC,KAAK,EAAEC,YAAY,CAACjC,aAAa,CAACmC,QAAQ,CAAC,CAAE;YAC7CG,MAAM,EAAC,WAAW;YAClBC,UAAU,EAAC,UAAU;YACrBF,IAAI,EAAC;UAAI,CACV,CAAC,EACF5E,IAAA,CAACqE,QAAQ;YACPC,KAAK,EAAC,kBAAkB;YACxBC,KAAK,EAAEC,YAAY,CAACO,eAAe,CAACL,QAAQ,CAAC,CAAE;YAC/CC,QAAQ,EAAC,eAAe;YACxBC,IAAI,EAAC;UAAI,CACV,CAAC,EACF5E,IAAA,CAACqE,QAAQ;YACPC,KAAK,EAAC,kBAAkB;YACxBC,KAAK,EAAEC,YAAY,CAACQ,cAAe;YACnCH,MAAM,EAAC,OAAO;YACdC,UAAU,EAAC,UAAU;YACrBF,IAAI,EAAC;UAAI,CACV,CAAC;QAAA,CACE,CAAC;MAAA,CACH,CAAC,EAGP5E,IAAA,CAACR,IAAI;QAAC8D,KAAK,EAAE;UAAEY,iBAAiB,EAAE,EAAE;UAAED,YAAY,EAAE;QAAG,CAAE;QAAAd,QAAA,EACvDnD,IAAA,CAACiF,SAAS;UACRC,WAAW,EAAC,oBAAoB;UAChCC,YAAY,EAAEC,cAAe;UAC7Bb,KAAK,EAAEpC,WAAY;UACnBmB,KAAK,EAAE;YAAE+B,eAAe,EAAE,SAAS;YAAEC,SAAS,EAAE;UAAE,CAAE;UACpDC,UAAU,EAAE;YAAEzB,KAAK,EAAE;UAAO,CAAE;UAC9B0B,SAAS,EAAC,SAAS;UACnBC,oBAAoB,EAAC;QAAS,CAC/B;MAAC,CACE,CAAC,EAGPzF,IAAA,CAACR,IAAI;QAAC8D,KAAK,EAAE;UAAEY,iBAAiB,EAAE,EAAE;UAAED,YAAY,EAAE;QAAG,CAAE;QAAAd,QAAA,EACvDnD,IAAA,CAACP,UAAU;UAACiG,UAAU;UAACC,8BAA8B,EAAE,KAAM;UAAAxC,QAAA,EAC1DyC,OAAO,CAACC,GAAG,CAAC,UAAC/D,MAAM;YAAA,OAClB9B,IAAA,CAAC8F,IAAI;cAEHC,QAAQ,EAAE1D,cAAc,KAAKP,MAAO;cACpCkE,OAAO,EAAE,SAATA,OAAOA,CAAA;gBAAA,OAAQC,iBAAiB,CAACnE,MAAM,CAAC;cAAA,CAAC;cACzCwB,KAAK,EAAE;gBACL4C,WAAW,EAAE,CAAC;gBACdb,eAAe,EAAEhD,cAAc,KAAKP,MAAM,GAAG,SAAS,GAAG;cAC3D,CAAE;cACFqE,SAAS,EAAE;gBACTrC,KAAK,EAAEzB,cAAc,KAAKP,MAAM,GAAG,MAAM,GAAG,MAAM;gBAClDkC,UAAU,EAAE;cACd,CAAE;cAAAb,QAAA,EAEDrB;YAAM,GAZFA,MAaD,CAAC;UAAA,CACR;QAAC,CACQ;MAAC,CACT,CAAC,EAGP5B,KAAA,CAACV,IAAI;QAAC8D,KAAK,EAAE;UAAEY,iBAAiB,EAAE,EAAE;UAAED,YAAY,EAAE;QAAG,CAAE;QAAAd,QAAA,GACvDjD,KAAA,CAACR,IAAI;UAAC4D,KAAK,EAAE;YACXQ,KAAK,EAAE,MAAM;YACbC,QAAQ,EAAE,EAAE;YACZC,UAAU,EAAE,KAAK;YACjBC,YAAY,EAAE;UAChB,CAAE;UAAAd,QAAA,GAAC,sBACmB,EAACtB,gBAAgB,CAACuE,MAAM,EAAC,GAC/C;QAAA,CAAM,CAAC,EAENvE,gBAAgB,CAACuE,MAAM,KAAK,CAAC,GAC5BpG,IAAA,CAACF,IAAI;UAAAqD,QAAA,EACHnD,IAAA,CAACN,IAAI;YAAC4D,KAAK,EAAE;cACXQ,KAAK,EAAE,SAAS;cAChBC,QAAQ,EAAE,EAAE;cACZsC,SAAS,EAAE,QAAQ;cACnB9C,OAAO,EAAE;YACX,CAAE;YAAAJ,QAAA,EAAC;UAEH,CAAM;QAAC,CACH,CAAC,GAEPtB,gBAAgB,CAACgE,GAAG,CAAC,UAAC9D,OAAO;UAAA,OAC3B/B,IAAA,CAACF,IAAI;YAAkBwD,KAAK,EAAE;cAAEW,YAAY,EAAE;YAAG,CAAE;YAAC+B,OAAO,EAAE,SAATA,OAAOA,CAAA;cAAA,OAAQhD,kBAAkB,CAACjB,OAAO,CAAC;YAAA,CAAC;YAAAoB,QAAA,EAC7FjD,KAAA,CAACV,IAAI;cAAC8D,KAAK,EAAE;gBAAEa,aAAa,EAAE,KAAK;gBAAEmC,UAAU,EAAE;cAAa,CAAE;cAAAnD,QAAA,GAC9DnD,IAAA,CAACR,IAAI;gBAAC8D,KAAK,EAAE;kBACX+B,eAAe,EAAE,MAAM;kBACvBkB,YAAY,EAAE,EAAE;kBAChBhD,OAAO,EAAE,EAAE;kBACX2C,WAAW,EAAE,EAAE;kBACfI,UAAU,EAAE,QAAQ;kBACpBE,cAAc,EAAE;gBAClB,CAAE;gBAAArD,QAAA,EACAnD,IAAA,CAACN,IAAI;kBAAC4D,KAAK,EAAE;oBAAES,QAAQ,EAAE;kBAAG,CAAE;kBAAAZ,QAAA,EAAEpB,OAAO,CAACN;gBAAM,CAAO;cAAC,CAClD,CAAC,EAEPvB,KAAA,CAACV,IAAI;gBAAC8D,KAAK,EAAE;kBAAEG,IAAI,EAAE;gBAAE,CAAE;gBAAAN,QAAA,GACvBjD,KAAA,CAACV,IAAI;kBAAC8D,KAAK,EAAE;oBAAEa,aAAa,EAAE,KAAK;oBAAEmC,UAAU,EAAE,QAAQ;oBAAErC,YAAY,EAAE;kBAAE,CAAE;kBAAAd,QAAA,GAC3EnD,IAAA,CAACN,IAAI;oBAAC4D,KAAK,EAAE;sBACXQ,KAAK,EAAE,MAAM;sBACbC,QAAQ,EAAE,EAAE;sBACZC,UAAU,EAAE,KAAK;sBACjBP,IAAI,EAAE;oBACR,CAAE;oBAAAN,QAAA,EACCpB,OAAO,CAACV;kBAAI,CACT,CAAC,EACNU,OAAO,CAAC0E,QAAQ,IACfzG,IAAA,CAACN,IAAI;oBAAC4D,KAAK,EAAE;sBAAES,QAAQ,EAAE,EAAE;sBAAE2C,UAAU,EAAE;oBAAE,CAAE;oBAAAvD,QAAA,EAAC;kBAAC,CAAM,CACtD;gBAAA,CACG,CAAC,EAEPjD,KAAA,CAACV,IAAI;kBAAC8D,KAAK,EAAE;oBAAEa,aAAa,EAAE,KAAK;oBAAEmC,UAAU,EAAE,QAAQ;oBAAErC,YAAY,EAAE;kBAAE,CAAE;kBAAAd,QAAA,GAC3EnD,IAAA,CAACR,IAAI;oBAAC8D,KAAK,EAAE;sBACX+B,eAAe,EAAEtD,OAAO,CAACR,SAAS,GAAG,SAAS,GAAG,SAAS;sBAC1D2C,iBAAiB,EAAE,CAAC;sBACpByC,eAAe,EAAE,CAAC;sBAClBJ,YAAY,EAAE,CAAC;sBACfL,WAAW,EAAE;oBACf,CAAE;oBAAA/C,QAAA,EACAnD,IAAA,CAACN,IAAI;sBAAC4D,KAAK,EAAE;wBACXQ,KAAK,EAAE/B,OAAO,CAACR,SAAS,GAAG,MAAM,GAAG,MAAM;wBAC1CwC,QAAQ,EAAE,EAAE;wBACZC,UAAU,EAAE;sBACd,CAAE;sBAAAb,QAAA,EACCpB,OAAO,CAACR,SAAS,GAAG,SAAS,GAAG;oBAAM,CACnC;kBAAC,CACH,CAAC,EACPvB,IAAA,CAACR,IAAI;oBAAC8D,KAAK,EAAE;sBACX+B,eAAe,EAAE,MAAM;sBACvBnB,iBAAiB,EAAE,CAAC;sBACpByC,eAAe,EAAE,CAAC;sBAClBJ,YAAY,EAAE,CAAC;sBACfL,WAAW,EAAE;oBACf,CAAE;oBAAA/C,QAAA,EACAnD,IAAA,CAACN,IAAI;sBAAC4D,KAAK,EAAE;wBACXQ,KAAK,EAAE,MAAM;wBACbC,QAAQ,EAAE,EAAE;wBACZC,UAAU,EAAE;sBACd,CAAE;sBAAAb,QAAA,EACCpB,OAAO,CAACO;oBAAI,CACT;kBAAC,CACH,CAAC,EACNP,OAAO,CAACQ,aAAa,GAAG,CAAC,IACxBvC,IAAA,CAACR,IAAI;oBAAC8D,KAAK,EAAE;sBACX+B,eAAe,EAAE,SAAS;sBAC1BnB,iBAAiB,EAAE,CAAC;sBACpByC,eAAe,EAAE,CAAC;sBAClBJ,YAAY,EAAE;oBAChB,CAAE;oBAAApD,QAAA,EACAjD,KAAA,CAACR,IAAI;sBAAC4D,KAAK,EAAE;wBACXQ,KAAK,EAAE,MAAM;wBACbC,QAAQ,EAAE,CAAC;wBACXC,UAAU,EAAE;sBACd,CAAE;sBAAAb,QAAA,GACCpB,OAAO,CAACQ,aAAa,EAAC,SACzB;oBAAA,CAAM;kBAAC,CACH,CACP;gBAAA,CACG,CAAC,EAEPvC,IAAA,CAACN,IAAI;kBAAC4D,KAAK,EAAE;oBACXQ,KAAK,EAAE,MAAM;oBACbC,QAAQ,EAAE,EAAE;oBACZE,YAAY,EAAE,CAAC;oBACf2C,UAAU,EAAE;kBACd,CAAE;kBAAAzD,QAAA,EACCpB,OAAO,CAACT;gBAAW,CAChB,CAAC,EAEPpB,KAAA,CAACV,IAAI;kBAAC8D,KAAK,EAAE;oBAAEa,aAAa,EAAE,KAAK;oBAAEqC,cAAc,EAAE,eAAe;oBAAEvC,YAAY,EAAE;kBAAE,CAAE;kBAAAd,QAAA,GACtFjD,KAAA,CAACV,IAAI;oBAAC8D,KAAK,EAAE;sBAAEG,IAAI,EAAE,CAAC;sBAAEyC,WAAW,EAAE;oBAAE,CAAE;oBAAA/C,QAAA,GACvCnD,IAAA,CAACN,IAAI;sBAAC4D,KAAK,EAAE;wBAAEQ,KAAK,EAAE,SAAS;wBAAEC,QAAQ,EAAE;sBAAG,CAAE;sBAAAZ,QAAA,EAAC;oBAAW,CAAM,CAAC,EACnEnD,IAAA,CAACN,IAAI;sBAAC4D,KAAK,EAAE;wBAAEQ,KAAK,EAAE,MAAM;wBAAEC,QAAQ,EAAE,EAAE;wBAAEC,UAAU,EAAE;sBAAM,CAAE;sBAAAb,QAAA,EAAEpB,OAAO,CAACP,WAAW,CAACqF,cAAc,CAAC;oBAAC,CAAO,CAAC;kBAAA,CAC1G,CAAC,EACP3G,KAAA,CAACV,IAAI;oBAAC8D,KAAK,EAAE;sBAAEG,IAAI,EAAE,CAAC;sBAAEyC,WAAW,EAAE;oBAAE,CAAE;oBAAA/C,QAAA,GACvCnD,IAAA,CAACN,IAAI;sBAAC4D,KAAK,EAAE;wBAAEQ,KAAK,EAAE,SAAS;wBAAEC,QAAQ,EAAE;sBAAG,CAAE;sBAAAZ,QAAA,EAAC;oBAAY,CAAM,CAAC,EACpEjD,KAAA,CAACR,IAAI;sBAAC4D,KAAK,EAAE;wBAAEQ,KAAK,EAAE,SAAS;wBAAEC,QAAQ,EAAE,EAAE;wBAAEC,UAAU,EAAE;sBAAM,CAAE;sBAAAb,QAAA,GAAEpB,OAAO,CAAC+E,WAAW,EAAC,GAAC;oBAAA,CAAM,CAAC;kBAAA,CAC7F,CAAC,EACP5G,KAAA,CAACV,IAAI;oBAAC8D,KAAK,EAAE;sBAAEG,IAAI,EAAE,CAAC;sBAAEyC,WAAW,EAAE;oBAAE,CAAE;oBAAA/C,QAAA,GACvCnD,IAAA,CAACN,IAAI;sBAAC4D,KAAK,EAAE;wBAAEQ,KAAK,EAAE,SAAS;wBAAEC,QAAQ,EAAE;sBAAG,CAAE;sBAAAZ,QAAA,EAAC;oBAAW,CAAM,CAAC,EACnEnD,IAAA,CAACN,IAAI;sBAAC4D,KAAK,EAAE;wBAAEQ,KAAK,EAAE,SAAS;wBAAEC,QAAQ,EAAE,EAAE;wBAAEC,UAAU,EAAE;sBAAM,CAAE;sBAAAb,QAAA,EAAEpB,OAAO,CAACgF;oBAAW,CAAO,CAAC;kBAAA,CAC5F,CAAC,EACP7G,KAAA,CAACV,IAAI;oBAAC8D,KAAK,EAAE;sBAAEG,IAAI,EAAE;oBAAE,CAAE;oBAAAN,QAAA,GACvBnD,IAAA,CAACN,IAAI;sBAAC4D,KAAK,EAAE;wBAAEQ,KAAK,EAAE,SAAS;wBAAEC,QAAQ,EAAE;sBAAG,CAAE;sBAAAZ,QAAA,EAAC;oBAAW,CAAM,CAAC,EACnEnD,IAAA,CAACN,IAAI;sBAAC4D,KAAK,EAAE;wBAAEQ,KAAK,EAAE,SAAS;wBAAEC,QAAQ,EAAE,EAAE;wBAAEC,UAAU,EAAE;sBAAM,CAAE;sBAAAb,QAAA,EAAEX,UAAU,CAACT,OAAO,CAACiF,YAAY;oBAAC,CAAO,CAAC;kBAAA,CACzG,CAAC;gBAAA,CACH,CAAC,EAEPhH,IAAA,CAACiH,WAAW;kBACVC,QAAQ,EAAEnF,OAAO,CAAC+E,WAAW,GAAG,GAAI;kBACpChD,KAAK,EAAC,SAAS;kBACfR,KAAK,EAAE;oBAAE6D,MAAM,EAAE,CAAC;oBAAEZ,YAAY,EAAE,CAAC;oBAAEtC,YAAY,EAAE;kBAAG;gBAAE,CACzD,CAAC,EAEF/D,KAAA,CAACV,IAAI;kBAAC8D,KAAK,EAAE;oBAAEa,aAAa,EAAE,KAAK;oBAAEqC,cAAc,EAAE;kBAAgB,CAAE;kBAAArD,QAAA,GACrEnD,IAAA,CAACJ,MAAM;oBACLwH,IAAI,EAAC,UAAU;oBACfpB,OAAO,EAAE,SAATA,OAAOA,CAAA;sBAAA,OAAQhD,kBAAkB,CAACjB,OAAO,CAAC;oBAAA,CAAC;oBAC3CuB,KAAK,EAAE;sBACLG,IAAI,EAAE,CAAC;sBACPyC,WAAW,EAAE,CAAC;sBACdmB,WAAW,EAAE;oBACf,CAAE;oBACFC,UAAU,EAAE;sBACVxD,KAAK,EAAE,SAAS;sBAChBE,UAAU,EAAE,KAAK;sBACjBD,QAAQ,EAAE;oBACZ,CAAE;oBAAAZ,QAAA,EACH;kBAED,CAAQ,CAAC,EACTnD,IAAA,CAACJ,MAAM;oBACLwH,IAAI,EAAC,WAAW;oBAChBpB,OAAO,EAAE,SAATA,OAAOA,CAAA;sBAAA,OAAQ9C,eAAe,CAACnB,OAAO,CAAC;oBAAA,CAAC;oBACxCuB,KAAK,EAAE;sBACLG,IAAI,EAAE,CAAC;sBACPiD,UAAU,EAAE,CAAC;sBACbrB,eAAe,EAAE;oBACnB,CAAE;oBACFiC,UAAU,EAAE;sBACVxD,KAAK,EAAE,MAAM;sBACbE,UAAU,EAAE,KAAK;sBACjBD,QAAQ,EAAE;oBACZ,CAAE;oBAAAZ,QAAA,EACH;kBAED,CAAQ,CAAC;gBAAA,CACL,CAAC;cAAA,CACH,CAAC;YAAA,CACH;UAAC,GAlJEpB,OAAO,CAACX,EAmJb,CAAC;QAAA,CACR,CACF;MAAA,CACG,CAAC;IAAA,CACG;EAAC,CACN,CAAC;AAEd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}