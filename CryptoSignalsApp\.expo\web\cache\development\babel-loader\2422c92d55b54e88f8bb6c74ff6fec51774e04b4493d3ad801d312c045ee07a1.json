{"ast": null, "code": "'use client';\n\nimport canUseDOM from \"../../modules/canUseDom\";\nfunction getQuery() {\n  return canUseDOM && window.matchMedia != null ? window.matchMedia('(prefers-color-scheme: dark)') : null;\n}\nvar query = getQuery();\nvar listenerMapping = new WeakMap();\nvar Appearance = {\n  getColorScheme: function getColorScheme() {\n    return query && query.matches ? 'dark' : 'light';\n  },\n  addChangeListener: function addChangeListener(listener) {\n    var mappedListener = listenerMapping.get(listener);\n    if (!mappedListener) {\n      mappedListener = function mappedListener(_ref) {\n        var matches = _ref.matches;\n        listener({\n          colorScheme: matches ? 'dark' : 'light'\n        });\n      };\n      listenerMapping.set(listener, mappedListener);\n    }\n    if (query) {\n      query.addListener(mappedListener);\n    }\n    function remove() {\n      var mappedListener = listenerMapping.get(listener);\n      if (query && mappedListener) {\n        query.removeListener(mappedListener);\n      }\n      listenerMapping.delete(listener);\n    }\n    return {\n      remove: remove\n    };\n  }\n};\nexport default Appearance;", "map": {"version": 3, "names": ["canUseDOM", "<PERSON><PERSON><PERSON><PERSON>", "window", "matchMedia", "query", "listenerMapping", "WeakMap", "Appearance", "getColorScheme", "matches", "addChangeListener", "listener", "mappedListener", "get", "_ref", "colorScheme", "set", "addListener", "remove", "removeListener", "delete"], "sources": ["C:/Users/<USER>/Desktop/Signals/CryptoSignalsApp/node_modules/react-native-web/dist/exports/Appearance/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n'use client';\n\nimport canUseDOM from '../../modules/canUseDom';\nfunction getQuery() {\n  return canUseDOM && window.matchMedia != null ? window.matchMedia('(prefers-color-scheme: dark)') : null;\n}\nvar query = getQuery();\nvar listenerMapping = new WeakMap();\nvar Appearance = {\n  getColorScheme() {\n    return query && query.matches ? 'dark' : 'light';\n  },\n  addChangeListener(listener) {\n    var mappedListener = listenerMapping.get(listener);\n    if (!mappedListener) {\n      mappedListener = _ref => {\n        var matches = _ref.matches;\n        listener({\n          colorScheme: matches ? 'dark' : 'light'\n        });\n      };\n      listenerMapping.set(listener, mappedListener);\n    }\n    if (query) {\n      query.addListener(mappedListener);\n    }\n    function remove() {\n      var mappedListener = listenerMapping.get(listener);\n      if (query && mappedListener) {\n        query.removeListener(mappedListener);\n      }\n      listenerMapping.delete(listener);\n    }\n    return {\n      remove\n    };\n  }\n};\nexport default Appearance;"], "mappings": "AAUA,YAAY;;AAEZ,OAAOA,SAAS;AAChB,SAASC,QAAQA,CAAA,EAAG;EAClB,OAAOD,SAAS,IAAIE,MAAM,CAACC,UAAU,IAAI,IAAI,GAAGD,MAAM,CAACC,UAAU,CAAC,8BAA8B,CAAC,GAAG,IAAI;AAC1G;AACA,IAAIC,KAAK,GAAGH,QAAQ,CAAC,CAAC;AACtB,IAAII,eAAe,GAAG,IAAIC,OAAO,CAAC,CAAC;AACnC,IAAIC,UAAU,GAAG;EACfC,cAAc,WAAdA,cAAcA,CAAA,EAAG;IACf,OAAOJ,KAAK,IAAIA,KAAK,CAACK,OAAO,GAAG,MAAM,GAAG,OAAO;EAClD,CAAC;EACDC,iBAAiB,WAAjBA,iBAAiBA,CAACC,QAAQ,EAAE;IAC1B,IAAIC,cAAc,GAAGP,eAAe,CAACQ,GAAG,CAACF,QAAQ,CAAC;IAClD,IAAI,CAACC,cAAc,EAAE;MACnBA,cAAc,GAAG,SAAjBA,cAAcA,CAAGE,IAAI,EAAI;QACvB,IAAIL,OAAO,GAAGK,IAAI,CAACL,OAAO;QAC1BE,QAAQ,CAAC;UACPI,WAAW,EAAEN,OAAO,GAAG,MAAM,GAAG;QAClC,CAAC,CAAC;MACJ,CAAC;MACDJ,eAAe,CAACW,GAAG,CAACL,QAAQ,EAAEC,cAAc,CAAC;IAC/C;IACA,IAAIR,KAAK,EAAE;MACTA,KAAK,CAACa,WAAW,CAACL,cAAc,CAAC;IACnC;IACA,SAASM,MAAMA,CAAA,EAAG;MAChB,IAAIN,cAAc,GAAGP,eAAe,CAACQ,GAAG,CAACF,QAAQ,CAAC;MAClD,IAAIP,KAAK,IAAIQ,cAAc,EAAE;QAC3BR,KAAK,CAACe,cAAc,CAACP,cAAc,CAAC;MACtC;MACAP,eAAe,CAACe,MAAM,CAACT,QAAQ,CAAC;IAClC;IACA,OAAO;MACLO,MAAM,EAANA;IACF,CAAC;EACH;AACF,CAAC;AACD,eAAeX,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}