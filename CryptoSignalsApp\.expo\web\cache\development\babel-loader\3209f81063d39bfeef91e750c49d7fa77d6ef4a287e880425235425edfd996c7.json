{"ast": null, "code": "import * as React from 'react';\nimport CardAnimationContext from \"./CardAnimationContext\";\nexport default function useCardAnimation() {\n  var animation = React.useContext(CardAnimationContext);\n  if (animation === undefined) {\n    throw new Error(\"Couldn't find values for card animation. Are you inside a screen in Stack?\");\n  }\n  return animation;\n}", "map": {"version": 3, "names": ["React", "CardAnimationContext", "useCardAnimation", "animation", "useContext", "undefined", "Error"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\@react-navigation\\stack\\src\\utils\\useCardAnimation.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport CardAnimationContext from './CardAnimationContext';\n\nexport default function useCardAnimation() {\n  const animation = React.useContext(CardAnimationContext);\n\n  if (animation === undefined) {\n    throw new Error(\n      \"Couldn't find values for card animation. Are you inside a screen in Stack?\"\n    );\n  }\n\n  return animation;\n}\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,OAAOC,oBAAoB;AAE3B,eAAe,SAASC,gBAAgBA,CAAA,EAAG;EACzC,IAAMC,SAAS,GAAGH,KAAK,CAACI,UAAU,CAACH,oBAAoB,CAAC;EAExD,IAAIE,SAAS,KAAKE,SAAS,EAAE;IAC3B,MAAM,IAAIC,KAAK,CACb,4EAA4E,CAC7E;EACH;EAEA,OAAOH,SAAS;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}