{"ast": null, "code": "import * as React from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport Text from \"react-native-web/dist/exports/Text\";\nexport default function MissingIcon(_ref) {\n  var color = _ref.color,\n    size = _ref.size,\n    style = _ref.style;\n  return React.createElement(Text, {\n    style: [styles.icon, {\n      color: color,\n      fontSize: size\n    }, style]\n  }, \"\\u23F7\");\n}\nvar styles = StyleSheet.create({\n  icon: {\n    backgroundColor: 'transparent'\n  }\n});", "map": {"version": 3, "names": ["React", "StyleSheet", "Text", "MissingIcon", "_ref", "color", "size", "style", "createElement", "styles", "icon", "fontSize", "create", "backgroundColor"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\@react-navigation\\elements\\src\\MissingIcon.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { StyleProp, StyleSheet, Text, TextStyle } from 'react-native';\n\ntype Props = {\n  color?: string;\n  size?: number;\n  style?: StyleProp<TextStyle>;\n};\n\nexport default function MissingIcon({ color, size, style }: Props) {\n  return <Text style={[styles.icon, { color, fontSize: size }, style]}>⏷</Text>;\n}\n\nconst styles = StyleSheet.create({\n  icon: {\n    backgroundColor: 'transparent',\n  },\n});\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAS9B,eAAe,SAASC,WAAWA,CAAAC,IAAA,EAAgC;EAAA,IAA7BC,KAAK,GAAsBD,IAAA,CAA3BC,KAAK;IAAEC,IAAI,GAAgBF,IAAA,CAApBE,IAAI;IAAEC,KAAA,GAAcH,IAAA,CAAdG,KAAA;EACjD,OAAOP,KAAA,CAAAQ,aAAA,CAACN,IAAI;IAACK,KAAK,EAAE,CAACE,MAAM,CAACC,IAAI,EAAE;MAAEL,KAAK,EAALA,KAAK;MAAEM,QAAQ,EAAEL;IAAK,CAAC,EAAEC,KAAK;EAAE,YAAS;AAC/E;AAEA,IAAME,MAAM,GAAGR,UAAU,CAACW,MAAM,CAAC;EAC/BF,IAAI,EAAE;IACJG,eAAe,EAAE;EACnB;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}