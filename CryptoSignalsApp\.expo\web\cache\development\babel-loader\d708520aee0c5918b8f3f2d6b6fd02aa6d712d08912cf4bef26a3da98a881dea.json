{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport * as SecureStore from 'expo-secure-store';\nfunction setSecureStoreItem(_x, _x2) {\n  return _setSecureStoreItem.apply(this, arguments);\n}\nfunction _setSecureStoreItem() {\n  _setSecureStoreItem = _asyncToGenerator(function* (key, value) {\n    try {\n      yield SecureStore.setItemAsync(key, JSON.stringify(value));\n    } catch (e) {\n      console.error('SET SECURE STORE ITEM', e);\n      throw e;\n    }\n  });\n  return _setSecureStoreItem.apply(this, arguments);\n}\nfunction getSecureStoreItem(_x3) {\n  return _getSecureStoreItem.apply(this, arguments);\n}\nfunction _getSecureStoreItem() {\n  _getSecureStoreItem = _asyncToGenerator(function* (key) {\n    try {\n      var item = yield SecureStore.getItemAsync(key);\n      return item ? JSON.parse(item) : null;\n    } catch (e) {\n      console.error('GET SECURE STORE ITEM', e);\n      throw e;\n    }\n  });\n  return _getSecureStoreItem.apply(this, arguments);\n}\nfunction removeSecureStoreItem(_x4) {\n  return _removeSecureStoreItem.apply(this, arguments);\n}\nfunction _removeSecureStoreItem() {\n  _removeSecureStoreItem = _asyncToGenerator(function* (key) {\n    try {\n      yield SecureStore.deleteItemAsync(key);\n    } catch (e) {\n      console.error('REMOVE SECURE STORE ITEM', e);\n      throw e;\n    }\n  });\n  return _removeSecureStoreItem.apply(this, arguments);\n}\nexport { setSecureStoreItem, getSecureStoreItem, removeSecureStoreItem };", "map": {"version": 3, "names": ["SecureStore", "setSecureStoreItem", "_x", "_x2", "_setSecureStoreItem", "apply", "arguments", "_asyncToGenerator", "key", "value", "setItemAsync", "JSON", "stringify", "e", "console", "error", "getSecureStoreItem", "_x3", "_getSecureStoreItem", "item", "getItemAsync", "parse", "removeSecureStoreItem", "_x4", "_removeSecureStoreItem", "deleteItemAsync"], "sources": ["E:/CryptoSignalsApp/src/services/secureStore.js"], "sourcesContent": ["import * as SecureStore from 'expo-secure-store';\r\n\r\n/**\r\n * Armazena um valor associado a uma chave no armazenamento seguro.\r\n * @param {string} key - A chave sob a qual o valor será armazenado.\r\n * @param {any} value - O valor a ser armazenado.\r\n * @returns {Promise<void>}\r\n */\r\nasync function setSecureStoreItem(key, value) {\r\n  try {\r\n    await SecureStore.setItemAsync(key, JSON.stringify(value));\r\n  } catch (e) {\r\n    console.error('SET SECURE STORE ITEM', e);\r\n    throw e;\r\n  }\r\n}\r\n\r\n/**\r\n * Recupera um valor associado a uma chave do armazenamento seguro.\r\n * @param {string} key - A chave do valor a ser recuperado.\r\n * @returns {Promise<any | null>} - O valor recuperado ou null se não existir.\r\n */\r\nasync function getSecureStoreItem(key) {\r\n  try {\r\n    const item = await SecureStore.getItemAsync(key);\r\n    return item ? JSON.parse(item) : null;\r\n  } catch (e) {\r\n    console.error('GET SECURE STORE ITEM', e);\r\n    throw e;\r\n  }\r\n}\r\n\r\n/**\r\n * Remove um valor associado a uma chave do armazenamento seguro.\r\n * @param {string} key - A chave do valor a ser removido.\r\n * @returns {Promise<void>}\r\n */\r\nasync function removeSecureStoreItem(key) {\r\n  try {\r\n    await SecureStore.deleteItemAsync(key);\r\n  } catch (e) {\r\n    console.error('REMOVE SECURE STORE ITEM', e);\r\n    throw e;\r\n  }\r\n}\r\n\r\nexport { setSecureStoreItem, getSecureStoreItem, removeSecureStoreItem };\r\n"], "mappings": ";AAAA,OAAO,KAAKA,WAAW,MAAM,mBAAmB;AAAC,SAQlCC,kBAAkBA,CAAAC,EAAA,EAAAC,GAAA;EAAA,OAAAC,mBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,oBAAA;EAAAA,mBAAA,GAAAG,iBAAA,CAAjC,WAAkCC,GAAG,EAAEC,KAAK,EAAE;IAC5C,IAAI;MACF,MAAMT,WAAW,CAACU,YAAY,CAACF,GAAG,EAAEG,IAAI,CAACC,SAAS,CAACH,KAAK,CAAC,CAAC;IAC5D,CAAC,CAAC,OAAOI,CAAC,EAAE;MACVC,OAAO,CAACC,KAAK,CAAC,uBAAuB,EAAEF,CAAC,CAAC;MACzC,MAAMA,CAAC;IACT;EACF,CAAC;EAAA,OAAAT,mBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAOcU,kBAAkBA,CAAAC,GAAA;EAAA,OAAAC,mBAAA,CAAAb,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAY,oBAAA;EAAAA,mBAAA,GAAAX,iBAAA,CAAjC,WAAkCC,GAAG,EAAE;IACrC,IAAI;MACF,IAAMW,IAAI,SAASnB,WAAW,CAACoB,YAAY,CAACZ,GAAG,CAAC;MAChD,OAAOW,IAAI,GAAGR,IAAI,CAACU,KAAK,CAACF,IAAI,CAAC,GAAG,IAAI;IACvC,CAAC,CAAC,OAAON,CAAC,EAAE;MACVC,OAAO,CAACC,KAAK,CAAC,uBAAuB,EAAEF,CAAC,CAAC;MACzC,MAAMA,CAAC;IACT;EACF,CAAC;EAAA,OAAAK,mBAAA,CAAAb,KAAA,OAAAC,SAAA;AAAA;AAAA,SAOcgB,qBAAqBA,CAAAC,GAAA;EAAA,OAAAC,sBAAA,CAAAnB,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAkB,uBAAA;EAAAA,sBAAA,GAAAjB,iBAAA,CAApC,WAAqCC,GAAG,EAAE;IACxC,IAAI;MACF,MAAMR,WAAW,CAACyB,eAAe,CAACjB,GAAG,CAAC;IACxC,CAAC,CAAC,OAAOK,CAAC,EAAE;MACVC,OAAO,CAACC,KAAK,CAAC,0BAA0B,EAAEF,CAAC,CAAC;MAC5C,MAAMA,CAAC;IACT;EACF,CAAC;EAAA,OAAAW,sBAAA,CAAAnB,KAAA,OAAAC,SAAA;AAAA;AAED,SAASL,kBAAkB,EAAEe,kBAAkB,EAAEM,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}