{"ast": null, "code": "import React from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport Svg, { Path } from \"react-native-svg\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default function IconArrowLeft() {\n  return _jsx(View, {\n    children: _jsx(Svg, {\n      width: 30,\n      height: 30,\n      viewBox: \"0 0 22 22\",\n      children: _jsx(Path, {\n        d: \"M13.6792 3.13242V1.06189C13.6792 0.882423 13.473 0.783316 13.3337 0.893137L1.2587 10.3244C1.15611 10.4042 1.07309 10.5063 1.01599 10.6231C0.958884 10.7398 0.929199 10.8681 0.929199 10.998C0.929199 11.128 0.958884 11.2563 1.01599 11.373C1.07309 11.4898 1.15611 11.5919 1.2587 11.6717L13.3337 21.103C13.4757 21.2128 13.6792 21.1137 13.6792 20.9342V18.8637C13.6792 18.7324 13.6176 18.6065 13.5158 18.5262L3.87299 10.9994L13.5158 3.46992C13.6176 3.38957 13.6792 3.26367 13.6792 3.13242Z\",\n        fill: \"#FECB37\"\n      })\n    })\n  });\n}", "map": {"version": 3, "names": ["React", "View", "Svg", "Path", "jsx", "_jsx", "IconArrowLeft", "children", "width", "height", "viewBox", "d", "fill"], "sources": ["C:/Users/<USER>/Desktop/Signals/CryptoSignalsApp/src/components/Icon/ArrowLeft/index.js"], "sourcesContent": ["import React from 'react';\r\nimport { View } from 'react-native';\r\nimport Svg, { Path } from \"react-native-svg\"\r\n\r\nexport default function IconArrowLeft() {\r\n  return (\r\n    <View>\r\n      <Svg\r\n        width={30}\r\n        height={30}\r\n        viewBox=\"0 0 22 22\"\r\n      >\r\n        <Path d=\"M13.6792 3.13242V1.06189C13.6792 0.882423 13.473 0.783316 13.3337 0.893137L1.2587 10.3244C1.15611 10.4042 1.07309 10.5063 1.01599 10.6231C0.958884 10.7398 0.929199 10.8681 0.929199 10.998C0.929199 11.128 0.958884 11.2563 1.01599 11.373C1.07309 11.4898 1.15611 11.5919 1.2587 11.6717L13.3337 21.103C13.4757 21.2128 13.6792 21.1137 13.6792 20.9342V18.8637C13.6792 18.7324 13.6176 18.6065 13.5158 18.5262L3.87299 10.9994L13.5158 3.46992C13.6176 3.38957 13.6792 3.26367 13.6792 3.13242Z\" fill=\"#FECB37\"/>\r\n      </Svg>\r\n    </View>\r\n  );\r\n}\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,OAAAC,IAAA;AAE1B,OAAOC,GAAG,IAAIC,IAAI,QAAQ,kBAAkB;AAAA,SAAAC,GAAA,IAAAC,IAAA;AAE5C,eAAe,SAASC,aAAaA,CAAA,EAAG;EACtC,OACED,IAAA,CAACJ,IAAI;IAAAM,QAAA,EACHF,IAAA,CAACH,GAAG;MACFM,KAAK,EAAE,EAAG;MACVC,MAAM,EAAE,EAAG;MACXC,OAAO,EAAC,WAAW;MAAAH,QAAA,EAEnBF,IAAA,CAACF,IAAI;QAACQ,CAAC,EAAC,oeAAoe;QAACC,IAAI,EAAC;MAAS,CAAC;IAAC,CAC1f;EAAC,CACF,CAAC;AAEX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}