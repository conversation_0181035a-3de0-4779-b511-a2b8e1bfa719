{"ast": null, "code": "import { nanoid } from 'nanoid/non-secure';\nexport default function createMemoryHistory() {\n  var index = 0;\n  var items = [];\n  var pending = [];\n  var interrupt = function interrupt() {\n    pending.forEach(function (it) {\n      var cb = it.cb;\n      it.cb = function () {\n        return cb(true);\n      };\n    });\n  };\n  var history = {\n    get index() {\n      var _window$history$state;\n      var id = (_window$history$state = window.history.state) === null || _window$history$state === void 0 ? void 0 : _window$history$state.id;\n      if (id) {\n        var _index = items.findIndex(function (item) {\n          return item.id === id;\n        });\n        return _index > -1 ? _index : 0;\n      }\n      return 0;\n    },\n    get: function get(index) {\n      return items[index];\n    },\n    backIndex: function backIndex(_ref) {\n      var path = _ref.path;\n      for (var i = index - 1; i >= 0; i--) {\n        var item = items[i];\n        if (item.path === path) {\n          return i;\n        }\n      }\n      return -1;\n    },\n    push: function push(_ref2) {\n      var path = _ref2.path,\n        state = _ref2.state;\n      interrupt();\n      var id = nanoid();\n      items = items.slice(0, index + 1);\n      items.push({\n        path: path,\n        state: state,\n        id: id\n      });\n      index = items.length - 1;\n      window.history.pushState({\n        id: id\n      }, '', path);\n    },\n    replace: function replace(_ref3) {\n      var _ref4;\n      var _window$history$state2;\n      var path = _ref3.path,\n        state = _ref3.state;\n      interrupt();\n      var id = (_ref4 = (_window$history$state2 = window.history.state) === null || _window$history$state2 === void 0 ? void 0 : _window$history$state2.id) != null ? _ref4 : nanoid();\n      var pathWithHash = path;\n      if (!items.length || items.findIndex(function (item) {\n        return item.id === id;\n      }) < 0) {\n        pathWithHash = pathWithHash + location.hash;\n        items = [{\n          path: pathWithHash,\n          state: state,\n          id: id\n        }];\n        index = 0;\n      } else {\n        if (items[index].path === path) {\n          pathWithHash = pathWithHash + location.hash;\n        }\n        items[index] = {\n          path: path,\n          state: state,\n          id: id\n        };\n      }\n      window.history.replaceState({\n        id: id\n      }, '', pathWithHash);\n    },\n    go: function go(n) {\n      interrupt();\n      var nextIndex = index + n;\n      var lastItemIndex = items.length - 1;\n      if (n < 0 && !items[nextIndex]) {\n        n = -index;\n        index = 0;\n      } else if (n > 0 && nextIndex > lastItemIndex) {\n        n = lastItemIndex - index;\n        index = lastItemIndex;\n      } else {\n        index = nextIndex;\n      }\n      if (n === 0) {\n        return;\n      }\n      return new Promise(function (resolve, reject) {\n        var done = function done(interrupted) {\n          clearTimeout(timer);\n          if (interrupted) {\n            reject(new Error('History was changed during navigation.'));\n            return;\n          }\n          var title = window.document.title;\n          window.document.title = '';\n          window.document.title = title;\n          resolve();\n        };\n        pending.push({\n          ref: done,\n          cb: done\n        });\n        var timer = setTimeout(function () {\n          var index = pending.findIndex(function (it) {\n            return it.ref === done;\n          });\n          if (index > -1) {\n            pending[index].cb();\n            pending.splice(index, 1);\n          }\n        }, 100);\n        var _onPopState = function onPopState() {\n          var _window$history$state3;\n          var id = (_window$history$state3 = window.history.state) === null || _window$history$state3 === void 0 ? void 0 : _window$history$state3.id;\n          var currentIndex = items.findIndex(function (item) {\n            return item.id === id;\n          });\n          index = Math.max(currentIndex, 0);\n          var last = pending.pop();\n          window.removeEventListener('popstate', _onPopState);\n          last === null || last === void 0 ? void 0 : last.cb();\n        };\n        window.addEventListener('popstate', _onPopState);\n        window.history.go(n);\n      });\n    },\n    listen: function listen(listener) {\n      var onPopState = function onPopState() {\n        if (pending.length) {\n          return;\n        }\n        listener();\n      };\n      window.addEventListener('popstate', onPopState);\n      return function () {\n        return window.removeEventListener('popstate', onPopState);\n      };\n    }\n  };\n  return history;\n}", "map": {"version": 3, "names": ["nanoid", "createMemoryHistory", "index", "items", "pending", "interrupt", "for<PERSON>ach", "it", "cb", "history", "_window$history$state", "id", "window", "state", "findIndex", "item", "get", "backIndex", "_ref", "path", "i", "push", "_ref2", "slice", "length", "pushState", "replace", "_ref3", "_ref4", "_window$history$state2", "pathWithHash", "location", "hash", "replaceState", "go", "n", "nextIndex", "lastItemIndex", "Promise", "resolve", "reject", "done", "interrupted", "clearTimeout", "timer", "Error", "title", "document", "ref", "setTimeout", "splice", "onPopState", "_window$history$state3", "currentIndex", "Math", "max", "last", "pop", "removeEventListener", "addEventListener", "listen", "listener"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\@react-navigation\\native\\src\\createMemoryHistory.tsx"], "sourcesContent": ["import type { NavigationState } from '@react-navigation/core';\nimport { nanoid } from 'nanoid/non-secure';\n\ntype HistoryRecord = {\n  // Unique identifier for this record to match it with window.history.state\n  id: string;\n  // Navigation state object for the history entry\n  state: NavigationState;\n  // Path of the history entry\n  path: string;\n};\n\nexport default function createMemoryHistory() {\n  let index = 0;\n  let items: HistoryRecord[] = [];\n\n  // Pending callbacks for `history.go(n)`\n  // We might modify the callback stored if it was interrupted, so we have a ref to identify it\n  const pending: { ref: unknown; cb: (interrupted?: boolean) => void }[] = [];\n\n  const interrupt = () => {\n    // If another history operation was performed we need to interrupt existing ones\n    // This makes sure that calls such as `history.replace` after `history.go` don't happen\n    // Since otherwise it won't be correct if something else has changed\n    pending.forEach((it) => {\n      const cb = it.cb;\n      it.cb = () => cb(true);\n    });\n  };\n\n  const history = {\n    get index(): number {\n      // We store an id in the state instead of an index\n      // Index could get out of sync with in-memory values if page reloads\n      const id = window.history.state?.id;\n\n      if (id) {\n        const index = items.findIndex((item) => item.id === id);\n\n        return index > -1 ? index : 0;\n      }\n\n      return 0;\n    },\n\n    get(index: number) {\n      return items[index];\n    },\n\n    backIndex({ path }: { path: string }) {\n      // We need to find the index from the element before current to get closest path to go back to\n      for (let i = index - 1; i >= 0; i--) {\n        const item = items[i];\n\n        if (item.path === path) {\n          return i;\n        }\n      }\n\n      return -1;\n    },\n\n    push({ path, state }: { path: string; state: NavigationState }) {\n      interrupt();\n\n      const id = nanoid();\n\n      // When a new entry is pushed, all the existing entries after index will be inaccessible\n      // So we remove any existing entries after the current index to clean them up\n      items = items.slice(0, index + 1);\n\n      items.push({ path, state, id });\n      index = items.length - 1;\n\n      // We pass empty string for title because it's ignored in all browsers except safari\n      // We don't store state object in history.state because:\n      // - browsers have limits on how big it can be, and we don't control the size\n      // - while not recommended, there could be non-serializable data in state\n      window.history.pushState({ id }, '', path);\n    },\n\n    replace({ path, state }: { path: string; state: NavigationState }) {\n      interrupt();\n\n      const id = window.history.state?.id ?? nanoid();\n\n      // Need to keep the hash part of the path if there was no previous history entry\n      // or the previous history entry had the same path\n      let pathWithHash = path;\n\n      if (!items.length || items.findIndex((item) => item.id === id) < 0) {\n        // There are two scenarios for creating an array with only one history record:\n        // - When loaded id not found in the items array, this function by default will replace\n        //   the first item. We need to keep only the new updated object, otherwise it will break\n        //   the page when navigating forward in history.\n        // - This is the first time any state modifications are done\n        //   So we need to push the entry as there's nothing to replace\n        pathWithHash = pathWithHash + location.hash;\n        items = [{ path: pathWithHash, state, id }];\n        index = 0;\n      } else {\n        if (items[index].path === path) {\n          pathWithHash = pathWithHash + location.hash;\n        }\n        items[index] = { path, state, id };\n      }\n\n      window.history.replaceState({ id }, '', pathWithHash);\n    },\n\n    // `history.go(n)` is asynchronous, there are couple of things to keep in mind:\n    // - it won't do anything if we can't go `n` steps, the `popstate` event won't fire.\n    // - each `history.go(n)` call will trigger a separate `popstate` event with correct location.\n    // - the `popstate` event fires before the next frame after calling `history.go(n)`.\n    // This method differs from `history.go(n)` in the sense that it'll go back as many steps it can.\n    go(n: number) {\n      interrupt();\n\n      // To guard against unexpected navigation out of the app we will assume that browser history is only as deep as the length of our memory\n      // history. If we don't have an item to navigate to then update our index and navigate as far as we can without taking the user out of the app.\n      const nextIndex = index + n;\n      const lastItemIndex = items.length - 1;\n      if (n < 0 && !items[nextIndex]) {\n        // Attempted to navigate beyond the first index. Negating the current index will align the browser history with the first item.\n        n = -index;\n        index = 0;\n      } else if (n > 0 && nextIndex > lastItemIndex) {\n        // Attempted to navigate past the last index. Calculate how many indices away from the last index and go there.\n        n = lastItemIndex - index;\n        index = lastItemIndex;\n      } else {\n        index = nextIndex;\n      }\n\n      if (n === 0) {\n        return;\n      }\n\n      // When we call `history.go`, `popstate` will fire when there's history to go back to\n      // So we need to somehow handle following cases:\n      // - There's history to go back, `history.go` is called, and `popstate` fires\n      // - `history.go` is called multiple times, we need to resolve on respective `popstate`\n      // - No history to go back, but `history.go` was called, browser has no API to detect it\n      return new Promise<void>((resolve, reject) => {\n        const done = (interrupted?: boolean) => {\n          clearTimeout(timer);\n\n          if (interrupted) {\n            reject(new Error('History was changed during navigation.'));\n            return;\n          }\n\n          // There seems to be a bug in Chrome regarding updating the title\n          // If we set a title just before calling `history.go`, the title gets lost\n          // However the value of `document.title` is still what we set it to\n          // It's just not displayed in the tab bar\n          // To update the tab bar, we need to reset the title to something else first (e.g. '')\n          // And set the title to what it was before so it gets applied\n          // It won't work without setting it to empty string coz otherwise title isn't changing\n          // Which means that the browser won't do anything after setting the title\n          const { title } = window.document;\n\n          window.document.title = '';\n          window.document.title = title;\n\n          resolve();\n        };\n\n        pending.push({ ref: done, cb: done });\n\n        // If navigation didn't happen within 100ms, assume that it won't happen\n        // This may not be accurate, but hopefully it won't take so much time\n        // In Chrome, navigation seems to happen instantly in next microtask\n        // But on Firefox, it seems to take much longer, around 50ms from our testing\n        // We're using a hacky timeout since there doesn't seem to be way to know for sure\n        const timer = setTimeout(() => {\n          const index = pending.findIndex((it) => it.ref === done);\n\n          if (index > -1) {\n            pending[index].cb();\n            pending.splice(index, 1);\n          }\n        }, 100);\n\n        const onPopState = () => {\n          const id = window.history.state?.id;\n          const currentIndex = items.findIndex((item) => item.id === id);\n\n          // Fix createMemoryHistory.index variable's value\n          // as it may go out of sync when navigating in the browser.\n          index = Math.max(currentIndex, 0);\n\n          const last = pending.pop();\n\n          window.removeEventListener('popstate', onPopState);\n          last?.cb();\n        };\n\n        window.addEventListener('popstate', onPopState);\n        window.history.go(n);\n      });\n    },\n\n    // The `popstate` event is triggered when history changes, except `pushState` and `replaceState`\n    // If we call `history.go(n)` ourselves, we don't want it to trigger the listener\n    // Here we normalize it so that only external changes (e.g. user pressing back/forward) trigger the listener\n    listen(listener: () => void) {\n      const onPopState = () => {\n        if (pending.length) {\n          // This was triggered by `history.go(n)`, we shouldn't call the listener\n          return;\n        }\n\n        listener();\n      };\n\n      window.addEventListener('popstate', onPopState);\n\n      return () => window.removeEventListener('popstate', onPopState);\n    },\n  };\n\n  return history;\n}\n"], "mappings": "AACA,SAASA,MAAM,QAAQ,mBAAmB;AAW1C,eAAe,SAASC,mBAAmBA,CAAA,EAAG;EAC5C,IAAIC,KAAK,GAAG,CAAC;EACb,IAAIC,KAAsB,GAAG,EAAE;EAI/B,IAAMC,OAAgE,GAAG,EAAE;EAE3E,IAAMC,SAAS,GAAG,SAAZA,SAASA,CAAA,EAAS;IAItBD,OAAO,CAACE,OAAO,CAAE,UAAAC,EAAE,EAAK;MACtB,IAAMC,EAAE,GAAGD,EAAE,CAACC,EAAE;MAChBD,EAAE,CAACC,EAAE,GAAG;QAAA,OAAMA,EAAE,CAAC,IAAI,CAAC;MAAA;IACxB,CAAC,CAAC;EACJ,CAAC;EAED,IAAMC,OAAO,GAAG;IACd,IAAIP,KAAKA,CAAA,EAAW;MAAA,IAAAQ,qBAAA;MAGlB,IAAMC,EAAE,IAAAD,qBAAA,GAAGE,MAAM,CAACH,OAAO,CAACI,KAAK,cAAAH,qBAAA,uBAApBA,qBAAA,CAAsBC,EAAE;MAEnC,IAAIA,EAAE,EAAE;QACN,IAAMT,MAAK,GAAGC,KAAK,CAACW,SAAS,CAAE,UAAAC,IAAI;UAAA,OAAKA,IAAI,CAACJ,EAAE,KAAKA,EAAE;QAAA,EAAC;QAEvD,OAAOT,MAAK,GAAG,CAAC,CAAC,GAAGA,MAAK,GAAG,CAAC;MAC/B;MAEA,OAAO,CAAC;IACV,CAAC;IAEDc,GAAG,WAAHA,GAAGA,CAACd,KAAa,EAAE;MACjB,OAAOC,KAAK,CAACD,KAAK,CAAC;IACrB,CAAC;IAEDe,SAAS,WAATA,SAASA,CAAAC,IAAA,EAA6B;MAAA,IAA1BC,IAAA,GAAwBD,IAAA,CAAxBC,IAAA;MAEV,KAAK,IAAIC,CAAC,GAAGlB,KAAK,GAAG,CAAC,EAAEkB,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QACnC,IAAML,IAAI,GAAGZ,KAAK,CAACiB,CAAC,CAAC;QAErB,IAAIL,IAAI,CAACI,IAAI,KAAKA,IAAI,EAAE;UACtB,OAAOC,CAAC;QACV;MACF;MAEA,OAAO,CAAC,CAAC;IACX,CAAC;IAEDC,IAAI,WAAJA,IAAIA,CAAAC,KAAA,EAA4D;MAAA,IAAzDH,IAAI,GAAmDG,KAAA,CAAvDH,IAAI;QAAEN,KAAA,GAAiDS,KAAA,CAAjDT,KAAA;MACXR,SAAS,EAAE;MAEX,IAAMM,EAAE,GAAGX,MAAM,EAAE;MAInBG,KAAK,GAAGA,KAAK,CAACoB,KAAK,CAAC,CAAC,EAAErB,KAAK,GAAG,CAAC,CAAC;MAEjCC,KAAK,CAACkB,IAAI,CAAC;QAAEF,IAAI,EAAJA,IAAI;QAAEN,KAAK,EAALA,KAAK;QAAEF,EAAA,EAAAA;MAAG,CAAC,CAAC;MAC/BT,KAAK,GAAGC,KAAK,CAACqB,MAAM,GAAG,CAAC;MAMxBZ,MAAM,CAACH,OAAO,CAACgB,SAAS,CAAC;QAAEd,EAAA,EAAAA;MAAG,CAAC,EAAE,EAAE,EAAEQ,IAAI,CAAC;IAC5C,CAAC;IAEDO,OAAO,WAAPA,OAAOA,CAAAC,KAAA,EAA4D;MAAA,IAAAC,KAAA;MAAA,IAAAC,sBAAA;MAAA,IAAzDV,IAAI,GAAmDQ,KAAA,CAAvDR,IAAI;QAAEN,KAAA,GAAiDc,KAAA,CAAjDd,KAAA;MACdR,SAAS,EAAE;MAEX,IAAMM,EAAE,IAAAiB,KAAA,GAAG,CAAAC,sBAAA,GAAAjB,MAAM,CAACH,OAAO,CAACI,KAAK,cAAAgB,sBAAA,uBAApBA,sBAAA,CAAsBlB,EAAE,YAAAiB,KAAA,GAAI5B,MAAM,EAAE;MAI/C,IAAI8B,YAAY,GAAGX,IAAI;MAEvB,IAAI,CAAChB,KAAK,CAACqB,MAAM,IAAIrB,KAAK,CAACW,SAAS,CAAE,UAAAC,IAAI;QAAA,OAAKA,IAAI,CAACJ,EAAE,KAAKA,EAAE;MAAA,EAAC,GAAG,CAAC,EAAE;QAOlEmB,YAAY,GAAGA,YAAY,GAAGC,QAAQ,CAACC,IAAI;QAC3C7B,KAAK,GAAG,CAAC;UAAEgB,IAAI,EAAEW,YAAY;UAAEjB,KAAK,EAALA,KAAK;UAAEF,EAAA,EAAAA;QAAG,CAAC,CAAC;QAC3CT,KAAK,GAAG,CAAC;MACX,CAAC,MAAM;QACL,IAAIC,KAAK,CAACD,KAAK,CAAC,CAACiB,IAAI,KAAKA,IAAI,EAAE;UAC9BW,YAAY,GAAGA,YAAY,GAAGC,QAAQ,CAACC,IAAI;QAC7C;QACA7B,KAAK,CAACD,KAAK,CAAC,GAAG;UAAEiB,IAAI,EAAJA,IAAI;UAAEN,KAAK,EAALA,KAAK;UAAEF,EAAA,EAAAA;QAAG,CAAC;MACpC;MAEAC,MAAM,CAACH,OAAO,CAACwB,YAAY,CAAC;QAAEtB,EAAA,EAAAA;MAAG,CAAC,EAAE,EAAE,EAAEmB,YAAY,CAAC;IACvD,CAAC;IAODI,EAAE,WAAFA,EAAEA,CAACC,CAAS,EAAE;MACZ9B,SAAS,EAAE;MAIX,IAAM+B,SAAS,GAAGlC,KAAK,GAAGiC,CAAC;MAC3B,IAAME,aAAa,GAAGlC,KAAK,CAACqB,MAAM,GAAG,CAAC;MACtC,IAAIW,CAAC,GAAG,CAAC,IAAI,CAAChC,KAAK,CAACiC,SAAS,CAAC,EAAE;QAE9BD,CAAC,GAAG,CAACjC,KAAK;QACVA,KAAK,GAAG,CAAC;MACX,CAAC,MAAM,IAAIiC,CAAC,GAAG,CAAC,IAAIC,SAAS,GAAGC,aAAa,EAAE;QAE7CF,CAAC,GAAGE,aAAa,GAAGnC,KAAK;QACzBA,KAAK,GAAGmC,aAAa;MACvB,CAAC,MAAM;QACLnC,KAAK,GAAGkC,SAAS;MACnB;MAEA,IAAID,CAAC,KAAK,CAAC,EAAE;QACX;MACF;MAOA,OAAO,IAAIG,OAAO,CAAO,UAACC,OAAO,EAAEC,MAAM,EAAK;QAC5C,IAAMC,IAAI,GAAI,SAARA,IAAIA,CAAIC,WAAqB,EAAK;UACtCC,YAAY,CAACC,KAAK,CAAC;UAEnB,IAAIF,WAAW,EAAE;YACfF,MAAM,CAAC,IAAIK,KAAK,CAAC,wCAAwC,CAAC,CAAC;YAC3D;UACF;UAUA,IAAQC,KAAA,GAAUlC,MAAM,CAACmC,QAAQ,CAAzBD,KAAA;UAERlC,MAAM,CAACmC,QAAQ,CAACD,KAAK,GAAG,EAAE;UAC1BlC,MAAM,CAACmC,QAAQ,CAACD,KAAK,GAAGA,KAAK;UAE7BP,OAAO,EAAE;QACX,CAAC;QAEDnC,OAAO,CAACiB,IAAI,CAAC;UAAE2B,GAAG,EAAEP,IAAI;UAAEjC,EAAE,EAAEiC;QAAK,CAAC,CAAC;QAOrC,IAAMG,KAAK,GAAGK,UAAU,CAAC,YAAM;UAC7B,IAAM/C,KAAK,GAAGE,OAAO,CAACU,SAAS,CAAE,UAAAP,EAAE;YAAA,OAAKA,EAAE,CAACyC,GAAG,KAAKP,IAAI;UAAA,EAAC;UAExD,IAAIvC,KAAK,GAAG,CAAC,CAAC,EAAE;YACdE,OAAO,CAACF,KAAK,CAAC,CAACM,EAAE,EAAE;YACnBJ,OAAO,CAAC8C,MAAM,CAAChD,KAAK,EAAE,CAAC,CAAC;UAC1B;QACF,CAAC,EAAE,GAAG,CAAC;QAEP,IAAMiD,WAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;UAAA,IAAAC,sBAAA;UACvB,IAAMzC,EAAE,IAAAyC,sBAAA,GAAGxC,MAAM,CAACH,OAAO,CAACI,KAAK,cAAAuC,sBAAA,uBAApBA,sBAAA,CAAsBzC,EAAE;UACnC,IAAM0C,YAAY,GAAGlD,KAAK,CAACW,SAAS,CAAE,UAAAC,IAAI;YAAA,OAAKA,IAAI,CAACJ,EAAE,KAAKA,EAAE;UAAA,EAAC;UAI9DT,KAAK,GAAGoD,IAAI,CAACC,GAAG,CAACF,YAAY,EAAE,CAAC,CAAC;UAEjC,IAAMG,IAAI,GAAGpD,OAAO,CAACqD,GAAG,EAAE;UAE1B7C,MAAM,CAAC8C,mBAAmB,CAAC,UAAU,EAAEP,WAAU,CAAC;UAClDK,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEhD,EAAE,EAAE;QACZ,CAAC;QAEDI,MAAM,CAAC+C,gBAAgB,CAAC,UAAU,EAAER,WAAU,CAAC;QAC/CvC,MAAM,CAACH,OAAO,CAACyB,EAAE,CAACC,CAAC,CAAC;MACtB,CAAC,CAAC;IACJ,CAAC;IAKDyB,MAAM,WAANA,MAAMA,CAACC,QAAoB,EAAE;MAC3B,IAAMV,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;QACvB,IAAI/C,OAAO,CAACoB,MAAM,EAAE;UAElB;QACF;QAEAqC,QAAQ,EAAE;MACZ,CAAC;MAEDjD,MAAM,CAAC+C,gBAAgB,CAAC,UAAU,EAAER,UAAU,CAAC;MAE/C,OAAO;QAAA,OAAMvC,MAAM,CAAC8C,mBAAmB,CAAC,UAAU,EAAEP,UAAU,CAAC;MAAA;IACjE;EACF,CAAC;EAED,OAAO1C,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}