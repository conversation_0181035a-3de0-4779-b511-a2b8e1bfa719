/**
 * Serviço de Pagamentos USDT
 * Gerencia pagamentos via BEP20 e ERC20
 */

import { PAYMENT_CONFIG } from '../config/api';

class PaymentService {
  constructor() {
    this.activePayments = new Map();
    this.paymentHistory = [];
  }

  /**
   * Cria uma nova sessão de pagamento
   */
  async createPaymentSession(planId, userId = null) {
    try {
      const plan = this.getPlanDetails(planId);
      if (!plan) {
        throw new Error('Plano não encontrado');
      }

      const paymentId = this.generatePaymentId();
      const session = {
        id: paymentId,
        planId,
        userId,
        amount: plan.price,
        currency: 'USDT',
        status: 'pending',
        walletAddress: PAYMENT_CONFIG.USDT_WALLET,
        networks: PAYMENT_CONFIG.SUPPORTED_NETWORKS,
        createdAt: new Date().toISOString(),
        expiresAt: new Date(Date.now() + PAYMENT_CONFIG.PAYMENT_TIMEOUT * 60 * 1000).toISOString(),
        confirmations: 0,
        requiredConfirmations: 3,
        transactionHash: null
      };

      this.activePayments.set(paymentId, session);
      
      console.log('Sessão de pagamento criada:', paymentId);
      return session;

    } catch (error) {
      console.error('Erro ao criar sessão de pagamento:', error);
      throw error;
    }
  }

  /**
   * Verifica o status de um pagamento
   */
  async checkPaymentStatus(paymentId) {
    try {
      const session = this.activePayments.get(paymentId);
      if (!session) {
        throw new Error('Sessão de pagamento não encontrada');
      }

      // Verificar se expirou
      if (new Date() > new Date(session.expiresAt)) {
        session.status = 'expired';
        this.activePayments.set(paymentId, session);
        return session;
      }

      // Simular verificação na blockchain
      // Em produção, aqui você faria chamadas para APIs da blockchain
      const mockTransaction = this.simulateBlockchainCheck(session);
      
      if (mockTransaction) {
        session.transactionHash = mockTransaction.hash;
        session.confirmations = mockTransaction.confirmations;
        session.network = mockTransaction.network;
        
        if (mockTransaction.confirmations >= session.requiredConfirmations) {
          session.status = 'confirmed';
          session.confirmedAt = new Date().toISOString();
          
          // Mover para histórico
          this.paymentHistory.push({ ...session });
          this.activePayments.delete(paymentId);
          
          console.log('Pagamento confirmado:', paymentId);
        } else {
          session.status = 'confirming';
        }
        
        this.activePayments.set(paymentId, session);
      }

      return session;

    } catch (error) {
      console.error('Erro ao verificar status do pagamento:', error);
      throw error;
    }
  }

  /**
   * Simula verificação na blockchain (para demonstração)
   * Em produção, substituir por chamadas reais para APIs da blockchain
   */
  simulateBlockchainCheck(session) {
    // Simular 30% de chance de encontrar transação
    if (Math.random() < 0.3) {
      return {
        hash: '0x' + Math.random().toString(16).substr(2, 64),
        confirmations: Math.floor(Math.random() * 6),
        network: Math.random() < 0.5 ? 'BSC' : 'ETH',
        amount: session.amount,
        from: '0x' + Math.random().toString(16).substr(2, 40),
        to: session.walletAddress
      };
    }
    return null;
  }

  /**
   * Obtém detalhes de um plano
   */
  getPlanDetails(planId) {
    const plans = {
      pro: {
        id: 'pro',
        name: 'Pro',
        price: PAYMENT_CONFIG.PLAN_PRICES.pro,
        features: ['15 Signal Channels', 'Priority Signals', 'Advanced Analytics']
      },
      elite: {
        id: 'elite',
        name: 'Elite',
        price: PAYMENT_CONFIG.PLAN_PRICES.elite,
        features: ['Unlimited Channels', 'Auto Trading', 'Premium Support']
      }
    };

    return plans[planId] || null;
  }

  /**
   * Gera ID único para pagamento
   */
  generatePaymentId() {
    return 'pay_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * Obtém histórico de pagamentos
   */
  getPaymentHistory(userId = null) {
    if (userId) {
      return this.paymentHistory.filter(payment => payment.userId === userId);
    }
    return this.paymentHistory;
  }

  /**
   * Cancela um pagamento pendente
   */
  cancelPayment(paymentId) {
    const session = this.activePayments.get(paymentId);
    if (session && session.status === 'pending') {
      session.status = 'cancelled';
      session.cancelledAt = new Date().toISOString();
      this.activePayments.set(paymentId, session);
      return true;
    }
    return false;
  }

  /**
   * Obtém estatísticas de pagamentos
   */
  getPaymentStats() {
    const total = this.paymentHistory.length;
    const confirmed = this.paymentHistory.filter(p => p.status === 'confirmed').length;
    const totalAmount = this.paymentHistory
      .filter(p => p.status === 'confirmed')
      .reduce((sum, p) => sum + p.amount, 0);

    return {
      totalPayments: total,
      confirmedPayments: confirmed,
      successRate: total > 0 ? (confirmed / total * 100).toFixed(2) : 0,
      totalRevenue: totalAmount,
      activePayments: this.activePayments.size
    };
  }

  /**
   * Gera QR Code para pagamento
   */
  generatePaymentQR(session, network = 'BSC') {
    const networkConfig = PAYMENT_CONFIG.SUPPORTED_NETWORKS[network];
    if (!networkConfig) {
      throw new Error('Rede não suportada');
    }

    // Formato para QR Code de pagamento crypto
    const paymentData = {
      address: session.walletAddress,
      amount: session.amount,
      token: 'USDT',
      network: network,
      memo: `CryptoSignals-${session.id}`
    };

    // Em produção, usar biblioteca de QR Code
    const qrData = `ethereum:${session.walletAddress}?value=${session.amount}&token=${networkConfig.usdtContract}`;
    
    return {
      qrData,
      paymentData,
      instructions: this.getPaymentInstructions(session, network)
    };
  }

  /**
   * Gera instruções de pagamento
   */
  getPaymentInstructions(session, network) {
    const networkConfig = PAYMENT_CONFIG.SUPPORTED_NETWORKS[network];
    
    return {
      title: `Pagamento via ${networkConfig.name}`,
      steps: [
        'Abra sua carteira crypto (MetaMask, Trust Wallet, etc.)',
        `Selecione a rede ${networkConfig.name}`,
        'Escolha o token USDT',
        `Envie exatamente ${session.amount} USDT para o endereço abaixo`,
        'Aguarde 3 confirmações na blockchain',
        'Seu plano será ativado automaticamente'
      ],
      warnings: [
        'Envie apenas USDT na rede correta',
        'Valores incorretos não serão processados',
        'Guarde o hash da transação para referência'
      ],
      support: 'Em caso de problemas, entre em contato com o suporte'
    };
  }
}

// Instância singleton
const paymentService = new PaymentService();

export default paymentService;
