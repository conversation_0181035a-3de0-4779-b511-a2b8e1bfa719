{"ast": null, "code": "import PressableNative from \"react-native-web/dist/exports/Pressable\";\nexport var Pressable = PressableNative;", "map": {"version": 3, "names": ["Pressable", "PressableNative"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\TouchableRipple\\Pressable.tsx"], "sourcesContent": ["import type * as React from 'react';\nimport type {\n  Animated,\n  PressableProps as PressableNativeProps,\n  StyleProp,\n  View,\n  ViewStyle,\n} from 'react-native';\nimport { Pressable as PressableNative } from 'react-native';\n\n// This component is added to support type-safe hover and focus states on web\n// https://necolas.github.io/react-native-web/docs/pressable/\n\nexport type PressableStateCallbackType = {\n  hovered: boolean;\n  pressed: boolean;\n  focused: boolean;\n};\n\nexport type PressableProps = Omit<\n  PressableNativeProps,\n  'children' | 'style'\n> & {\n  children:\n    | React.ReactNode\n    | ((state: PressableStateCallbackType) => React.ReactNode)\n    | undefined;\n  style?:\n    | StyleProp<ViewStyle>\n    | Animated.WithAnimatedValue<StyleProp<ViewStyle>>\n    | ((\n        state: PressableStateCallbackType\n      ) =>\n        | StyleProp<ViewStyle>\n        | Animated.WithAnimatedValue<StyleProp<ViewStyle>>)\n    | undefined;\n};\n\nexport const Pressable: React.ForwardRefExoticComponent<\n  PressableProps & React.RefAttributes<View>\n> = PressableNative as any;\n"], "mappings": ";AAsCA,OAAO,IAAMA,SAEZ,GAAGC,eAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}