#!/usr/bin/env python3
"""
Script para testar a integração entre o gerador de sinais e o CryptoSignalsApp
"""

import requests
import json
import time
import sys
import os

# Adicionar o diretório do gerador de sinais ao path
sys.path.append(os.path.join(os.path.dirname(__file__), 'gerador_sinais_telegram'))

def test_dashboard_api():
    """Testa se a API do dashboard está funcionando"""
    base_url = "http://localhost:5000/api"
    
    print("🔍 Testando API do Dashboard...")
    
    # Teste 1: Health check
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print("✅ Health check: OK")
        else:
            print(f"❌ Health check falhou: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Erro no health check: {e}")
        return False
    
    # Teste 2: Overview
    try:
        response = requests.get(f"{base_url}/overview", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Overview: {data.get('total_signals', 0)} sinais totais")
        else:
            print(f"❌ Overview falhou: {response.status_code}")
    except Exception as e:
        print(f"⚠️ Erro no overview: {e}")
    
    # Teste 3: Channels
    try:
        response = requests.get(f"{base_url}/channels", timeout=5)
        if response.status_code == 200:
            channels = response.json()
            print(f"✅ Channels: {len(channels)} canais disponíveis")
            for channel in channels[:3]:  # Mostrar apenas os primeiros 3
                print(f"   - {channel.get('name', 'N/A')}: {channel.get('totalSignals', 0)} sinais")
        else:
            print(f"❌ Channels falhou: {response.status_code}")
    except Exception as e:
        print(f"⚠️ Erro nos channels: {e}")
    
    # Teste 4: Signals
    try:
        response = requests.get(f"{base_url}/signals?limit=5", timeout=5)
        if response.status_code == 200:
            signals = response.json()
            print(f"✅ Signals: {len(signals)} sinais recentes")
            for signal in signals[:2]:  # Mostrar apenas os primeiros 2
                print(f"   - {signal.get('symbol', 'N/A')} {signal.get('signal_type', 'N/A')} ({signal.get('strategy', 'N/A')})")
        else:
            print(f"❌ Signals falhou: {response.status_code}")
    except Exception as e:
        print(f"⚠️ Erro nos signals: {e}")
    
    return True

def test_database_connection():
    """Testa se o banco de dados está acessível"""
    print("\n🗄️ Testando conexão com banco de dados...")
    
    try:
        from gerador_sinais_telegram.utils.database import DatabaseHandler
        
        db = DatabaseHandler("gerador_sinais_telegram/signals.db")
        
        # Testar consulta simples
        signals = db.get_open_signals()
        print(f"✅ Banco de dados: {len(signals)} sinais abertos")
        
        # Testar estatísticas
        stats = db.get_statistics()
        print(f"✅ Estatísticas: {len(stats)} registros")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Erro no banco de dados: {e}")
        return False

def test_signal_generator():
    """Testa se o gerador de sinais está rodando"""
    print("\n⚡ Testando gerador de sinais...")
    
    try:
        # Verificar se o arquivo de log existe
        log_file = "gerador_sinais_telegram/gerador_sinais.log"
        if os.path.exists(log_file):
            # Ler as últimas linhas do log
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                if lines:
                    last_line = lines[-1].strip()
                    print(f"✅ Log encontrado: {last_line[:100]}...")
                else:
                    print("⚠️ Arquivo de log vazio")
        else:
            print("⚠️ Arquivo de log não encontrado")
        
        # Verificar se o banco de sinais existe
        db_file = "gerador_sinais_telegram/signals.db"
        if os.path.exists(db_file):
            print("✅ Banco de sinais encontrado")
        else:
            print("⚠️ Banco de sinais não encontrado")
            
        return True
        
    except Exception as e:
        print(f"❌ Erro ao testar gerador: {e}")
        return False

def main():
    """Função principal"""
    print("🚀 TESTE DE INTEGRAÇÃO CRYPTOSIGNALS")
    print("=" * 50)
    
    # Teste 1: API do Dashboard
    api_ok = test_dashboard_api()
    
    # Teste 2: Banco de dados
    db_ok = test_database_connection()
    
    # Teste 3: Gerador de sinais
    generator_ok = test_signal_generator()
    
    print("\n" + "=" * 50)
    print("📊 RESUMO DOS TESTES:")
    print(f"API Dashboard: {'✅ OK' if api_ok else '❌ FALHOU'}")
    print(f"Banco de Dados: {'✅ OK' if db_ok else '❌ FALHOU'}")
    print(f"Gerador de Sinais: {'✅ OK' if generator_ok else '❌ FALHOU'}")
    
    if api_ok and db_ok:
        print("\n🎉 Integração funcionando! O CryptoSignalsApp pode se conectar aos sinais.")
        print("\n📱 Para testar o app:")
        print("1. Certifique-se de que o dashboard backend está rodando (python gerador_sinais_telegram/dashboard/backend/app.py)")
        print("2. Abra o CryptoSignalsApp")
        print("3. Navegue para 'Channels' para ver as estratégias")
        print("4. Toque em uma estratégia para ver os sinais")
    else:
        print("\n⚠️ Alguns componentes não estão funcionando corretamente.")
        print("Verifique os erros acima e tente novamente.")

if __name__ == "__main__":
    main()
