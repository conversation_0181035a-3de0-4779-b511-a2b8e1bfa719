# Guia de Atualização e Implantação do Gerador de Sinais Telegram

Este guia contém instruções detalhadas para configurar e iniciar o serviço no servidor.

## 1. Acessando o Servidor
root@ubuntu:/opt/gerador_sinais_telegram#

```bash
ssh root@**************
# Senha: h4*ls:FtJw0e
```

## 2. Preparando o Ambiente

Após fazer upload do arquivo zipado para o servidor e extraí-lo, siga estes passos:

```bash
# Navegar até o diretório do projeto
cd gerador_sinais_telegram
git pull

# Instalar pacotes necessários para o ambiente virtual
apt update
apt install python3-full build-essential

# Criar ambiente virtual
python3 -m venv venv

# Ativar o ambiente virtual
source venv/bin/activate
```

## 3. Instalando Dependências

Devido a problemas de compatibilidade com Python 3.12, precisamos modificar o arquivo requirements.txt:

```bash
# Editar o arquivo requirements.txt
nano requirements.txt
```

Substituir o conteúdo por:
```
python-binance==1.0.16
telethon==1.24.0
pandas>=2.0.0
numpy>=2.0.0
ta==0.10.2
nest-asyncio==1.5.6
python-dotenv==1.0.0
```

Em seguida, instalar as dependências:

```bash
# Atualizar pip
pip install --upgrade pip setuptools wheel

# Instalar as dependências
pip install -r requirements.txt
```

Se encontrar problemas com alguma dependência específica, tente instalá-las individualmente:

```bash
pip install python-binance==1.0.16
pip install telethon==1.24.0
pip install numpy  # Versão mais recente
pip install pandas  # Versão mais recente
pip install ta==0.10.2
pip install nest-asyncio==1.5.6
pip install python-dotenv==1.0.0
```


## 5. Configurando o Serviço Systemd

Edite o arquivo de serviço:

```bash
nano /etc/systemd/system/gerador_sinais.service
```

Conteúdo do arquivo:
```
[Unit]
Description=Gerador de Sinais Telegram
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=/root/gerador_sinais_telegram
Environment=PATH=/root/gerador_sinais_telegram/venv/bin:/usr/local/bin:/usr/bin:/bin
ExecStart=/root/gerador_sinais_telegram/venv/bin/python main.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

Recarregue o daemon e inicie o serviço:

```bash
# Recarregar o daemon do systemd
systemctl daemon-reload

# Reiniciar o serviço
systemctl restart gerador_sinais.service

# Verificar o status do serviço
systemctl status gerador_sinais.service

# Habilitar o serviço para iniciar na inicialização
systemctl enable gerador_sinais.service
```

## 6. Monitoramento e Logs

Para verificar os logs do serviço:

```bash
# Ver os logs do serviço systemd
journalctl -u gerador_sinais.service -f

# Ver os logs do aplicativo
tail -f gerador_sinais.log
```

## 7. Método Alternativo: Usando Screen

Se preferir usar screen em vez do serviço systemd:

```bash
# Instalar screen
apt install screen

# Criar uma nova sessão screen
screen -S gerador_sinais

# Dentro da sessão screen, ativar o ambiente virtual e iniciar o aplicativo
source venv/bin/activate
python main.py

# Para desanexar o screen (deixar rodando em background): pressione Ctrl+A seguido de D
```

Para reconectar à sessão posteriormente:
```bash
screen -r gerador_sinais
```
