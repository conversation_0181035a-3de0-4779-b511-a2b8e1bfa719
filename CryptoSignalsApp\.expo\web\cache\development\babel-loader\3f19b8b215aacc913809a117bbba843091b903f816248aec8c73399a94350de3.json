{"ast": null, "code": "import * as React from 'react';\nimport ScreenContentWrapper from \"./ScreenContentWrapper\";\nexport default function DebugContainer(props) {\n  return React.createElement(ScreenContentWrapper, props);\n}", "map": {"version": 3, "names": ["React", "ScreenContentWrapper", "DebugContainer", "props", "createElement"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\react-native-screens\\src\\components\\DebugContainer.web.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { type ViewProps } from 'react-native';\nimport ScreenContentWrapper from './ScreenContentWrapper';\n\nexport default function DebugContainer(props: ViewProps) {\n  return <ScreenContentWrapper {...props} />;\n}\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,OAAOC,oBAAoB;AAE3B,eAAe,SAASC,cAAcA,CAACC,KAAgB,EAAE;EACvD,OAAOH,KAAA,CAAAI,aAAA,CAACH,oBAAoB,EAAKE,KAAQ,CAAC;AAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}