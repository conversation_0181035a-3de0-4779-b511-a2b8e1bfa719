{"ast": null, "code": "import StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nvar styles = StyleSheet.create({\n  container: {\n    backgroundColor: \"#0D0D0D\",\n    borderRadius: 4,\n    paddingVertical: 12,\n    paddingHorizontal: 16,\n    marginTop: 12,\n    flexDirection: \"row\",\n    alignItems: \"center\",\n    justifyContent: \"space-between\"\n  },\n  photo: {\n    backgroundColor: \"#fff\",\n    borderRadius: 23,\n    width: 46,\n    height: 46,\n    marginRight: 16\n  },\n  title: {\n    color: \"#fff\",\n    fontSize: 15,\n    lineHeight: 22,\n    marginBottom: 4,\n    fontFamily: \"Poppins_700Bold\"\n  },\n  time: {\n    color: \"#ccc\",\n    fontSize: 10,\n    position: 'absolute',\n    bottom: 8,\n    right: 8\n  },\n  typeContainer: {\n    display: \"flex\",\n    flexDirection: \"row\",\n    alignItems: \"center\"\n  },\n  type: {\n    color: \"#fff\",\n    fontSize: 10,\n    textTransform: \"uppercase\",\n    lineHeight: 14,\n    fontFamily: \"Poppins_500Medium\"\n  },\n  dot: {\n    width: 4,\n    height: 4,\n    backgroundColor: \"#fff\",\n    borderRadius: 2,\n    marginHorizontal: 5\n  },\n  icon: {\n    color: \"#fff\"\n  },\n  dFlex: {\n    display: \"flex\"\n  },\n  row: {\n    flexDirection: \"row\"\n  }\n});\nexport default styles;", "map": {"version": 3, "names": ["styles", "StyleSheet", "create", "container", "backgroundColor", "borderRadius", "paddingVertical", "paddingHorizontal", "marginTop", "flexDirection", "alignItems", "justifyContent", "photo", "width", "height", "marginRight", "title", "color", "fontSize", "lineHeight", "marginBottom", "fontFamily", "time", "position", "bottom", "right", "typeContainer", "display", "type", "textTransform", "dot", "marginHorizontal", "icon", "dFlex", "row"], "sources": ["E:/CryptoSignalsApp/src/pages/Channels/Card/styles.js"], "sourcesContent": ["import { StyleSheet } from 'react-native';\r\n\r\nconst styles = StyleSheet.create({\r\n  container: {\r\n    backgroundColor: \"#0D0D0D\",\r\n    borderRadius: 4,\r\n    paddingVertical: 12,\r\n    paddingHorizontal: 16,\r\n    marginTop: 12,\r\n    flexDirection: \"row\",\r\n    alignItems: \"center\",\r\n    justifyContent: \"space-between\",\r\n  },\r\n  photo: {\r\n    backgroundColor: \"#fff\",\r\n    borderRadius: 23,\r\n    width: 46,\r\n    height: 46,\r\n    marginRight: 16,\r\n  },\r\n  title: {\r\n    color: \"#fff\",\r\n    fontSize: 15,\r\n    lineHeight: 22,\r\n    marginBottom: 4,\r\n    fontFamily: \"Poppins_700Bold\",\r\n  },\r\n  time: {\r\n    color: \"#ccc\",\r\n    fontSize: 10,\r\n    position: 'absolute',\r\n    bottom: 8,\r\n    right: 8,\r\n  },\r\n  typeContainer: {\r\n    display: \"flex\",\r\n    flexDirection: \"row\",\r\n    alignItems: \"center\",\r\n  },\r\n  type: {\r\n    color: \"#fff\",\r\n    fontSize: 10,\r\n    textTransform: \"uppercase\",\r\n    lineHeight: 14,\r\n    fontFamily: \"Poppins_500Medium\",\r\n  },\r\n  dot: {\r\n    width: 4,\r\n    height: 4,\r\n    backgroundColor: \"#fff\",\r\n    borderRadius: 2,\r\n    marginHorizontal: 5,\r\n  },\r\n  icon: {\r\n    color: \"#fff\",\r\n  },\r\n  dFlex: {\r\n    display: \"flex\",\r\n  },\r\n  row: {\r\n    flexDirection: \"row\",\r\n  },\r\n});\r\n\r\nexport default styles;"], "mappings": ";AAEA,IAAMA,MAAM,GAAGC,UAAU,CAACC,MAAM,CAAC;EAC/BC,SAAS,EAAE;IACTC,eAAe,EAAE,SAAS;IAC1BC,YAAY,EAAE,CAAC;IACfC,eAAe,EAAE,EAAE;IACnBC,iBAAiB,EAAE,EAAE;IACrBC,SAAS,EAAE,EAAE;IACbC,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE;EAClB,CAAC;EACDC,KAAK,EAAE;IACLR,eAAe,EAAE,MAAM;IACvBC,YAAY,EAAE,EAAE;IAChBQ,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,WAAW,EAAE;EACf,CAAC;EACDC,KAAK,EAAE;IACLC,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,EAAE;IACdC,YAAY,EAAE,CAAC;IACfC,UAAU,EAAE;EACd,CAAC;EACDC,IAAI,EAAE;IACJL,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,EAAE;IACZK,QAAQ,EAAE,UAAU;IACpBC,MAAM,EAAE,CAAC;IACTC,KAAK,EAAE;EACT,CAAC;EACDC,aAAa,EAAE;IACbC,OAAO,EAAE,MAAM;IACflB,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE;EACd,CAAC;EACDkB,IAAI,EAAE;IACJX,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,EAAE;IACZW,aAAa,EAAE,WAAW;IAC1BV,UAAU,EAAE,EAAE;IACdE,UAAU,EAAE;EACd,CAAC;EACDS,GAAG,EAAE;IACHjB,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTV,eAAe,EAAE,MAAM;IACvBC,YAAY,EAAE,CAAC;IACf0B,gBAAgB,EAAE;EACpB,CAAC;EACDC,IAAI,EAAE;IACJf,KAAK,EAAE;EACT,CAAC;EACDgB,KAAK,EAAE;IACLN,OAAO,EAAE;EACX,CAAC;EACDO,GAAG,EAAE;IACHzB,aAAa,EAAE;EACjB;AACF,CAAC,CAAC;AAEF,eAAeT,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}