{"ast": null, "code": "import React, { useEffect, useRef } from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport Animated from \"react-native-web/dist/exports/Animated\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar LoadingSkeleton = function LoadingSkeleton(_ref) {\n  var _ref$width = _ref.width,\n    width = _ref$width === void 0 ? '100%' : _ref$width,\n    _ref$height = _ref.height,\n    height = _ref$height === void 0 ? 20 : _ref$height,\n    _ref$borderRadius = _ref.borderRadius,\n    borderRadius = _ref$borderRadius === void 0 ? 4 : _ref$borderRadius,\n    style = _ref.style;\n  var animatedValue = useRef(new Animated.Value(0)).current;\n  useEffect(function () {\n    var animation = Animated.loop(Animated.sequence([Animated.timing(animatedValue, {\n      toValue: 1,\n      duration: 1000,\n      useNativeDriver: false\n    }), Animated.timing(animatedValue, {\n      toValue: 0,\n      duration: 1000,\n      useNativeDriver: false\n    })]));\n    animation.start();\n    return function () {\n      return animation.stop();\n    };\n  }, []);\n  var backgroundColor = animatedValue.interpolate({\n    inputRange: [0, 1],\n    outputRange: ['#2a2a2a', '#3a3a3a']\n  });\n  return _jsx(Animated.View, {\n    style: [{\n      width: width,\n      height: height,\n      borderRadius: borderRadius,\n      backgroundColor: backgroundColor\n    }, style]\n  });\n};\nvar SkeletonCard = function SkeletonCard() {\n  return _jsxs(View, {\n    style: {\n      backgroundColor: '#2a2a2a',\n      borderRadius: 12,\n      padding: 16,\n      margin: 8,\n      borderWidth: 1,\n      borderColor: '#333'\n    },\n    children: [_jsx(LoadingSkeleton, {\n      width: \"60%\",\n      height: 16,\n      style: {\n        marginBottom: 8\n      }\n    }), _jsx(LoadingSkeleton, {\n      width: \"100%\",\n      height: 12,\n      style: {\n        marginBottom: 4\n      }\n    }), _jsx(LoadingSkeleton, {\n      width: \"80%\",\n      height: 12,\n      style: {\n        marginBottom: 8\n      }\n    }), _jsx(LoadingSkeleton, {\n      width: \"40%\",\n      height: 10\n    })]\n  });\n};\nexport { LoadingSkeleton, SkeletonCard };", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "View", "Animated", "jsx", "_jsx", "jsxs", "_jsxs", "LoadingSkeleton", "_ref", "_ref$width", "width", "_ref$height", "height", "_ref$borderRadius", "borderRadius", "style", "animatedValue", "Value", "current", "animation", "loop", "sequence", "timing", "toValue", "duration", "useNativeDriver", "start", "stop", "backgroundColor", "interpolate", "inputRange", "outputRange", "SkeletonCard", "padding", "margin", "borderWidth", "borderColor", "children", "marginBottom"], "sources": ["E:/CryptoSignalsApp/src/components/LoadingSkeleton/index.js"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\nimport { View, Animated } from 'react-native';\n\nconst LoadingSkeleton = ({ \n  width = '100%', \n  height = 20, \n  borderRadius = 4,\n  style \n}) => {\n  const animatedValue = useRef(new Animated.Value(0)).current;\n\n  useEffect(() => {\n    const animation = Animated.loop(\n      Animated.sequence([\n        Animated.timing(animatedValue, {\n          toValue: 1,\n          duration: 1000,\n          useNativeDriver: false,\n        }),\n        Animated.timing(animatedValue, {\n          toValue: 0,\n          duration: 1000,\n          useNativeDriver: false,\n        }),\n      ])\n    );\n    animation.start();\n    return () => animation.stop();\n  }, []);\n\n  const backgroundColor = animatedValue.interpolate({\n    inputRange: [0, 1],\n    outputRange: ['#2a2a2a', '#3a3a3a'],\n  });\n\n  return (\n    <Animated.View\n      style={[\n        {\n          width,\n          height,\n          borderRadius,\n          backgroundColor,\n        },\n        style,\n      ]}\n    />\n  );\n};\n\nconst SkeletonCard = () => (\n  <View style={{ \n    backgroundColor: '#2a2a2a', \n    borderRadius: 12, \n    padding: 16, \n    margin: 8,\n    borderWidth: 1,\n    borderColor: '#333'\n  }}>\n    <LoadingSkeleton width=\"60%\" height={16} style={{ marginBottom: 8 }} />\n    <LoadingSkeleton width=\"100%\" height={12} style={{ marginBottom: 4 }} />\n    <LoadingSkeleton width=\"80%\" height={12} style={{ marginBottom: 8 }} />\n    <LoadingSkeleton width=\"40%\" height={10} />\n  </View>\n);\n\nexport { LoadingSkeleton, SkeletonCard };\n"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,QAAA;AAAA,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAGjD,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAAC,IAAA,EAKf;EAAA,IAAAC,UAAA,GAAAD,IAAA,CAJJE,KAAK;IAALA,KAAK,GAAAD,UAAA,cAAG,MAAM,GAAAA,UAAA;IAAAE,WAAA,GAAAH,IAAA,CACdI,MAAM;IAANA,MAAM,GAAAD,WAAA,cAAG,EAAE,GAAAA,WAAA;IAAAE,iBAAA,GAAAL,IAAA,CACXM,YAAY;IAAZA,YAAY,GAAAD,iBAAA,cAAG,CAAC,GAAAA,iBAAA;IAChBE,KAAK,GAAAP,IAAA,CAALO,KAAK;EAEL,IAAMC,aAAa,GAAGhB,MAAM,CAAC,IAAIE,QAAQ,CAACe,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,OAAO;EAE3DnB,SAAS,CAAC,YAAM;IACd,IAAMoB,SAAS,GAAGjB,QAAQ,CAACkB,IAAI,CAC7BlB,QAAQ,CAACmB,QAAQ,CAAC,CAChBnB,QAAQ,CAACoB,MAAM,CAACN,aAAa,EAAE;MAC7BO,OAAO,EAAE,CAAC;MACVC,QAAQ,EAAE,IAAI;MACdC,eAAe,EAAE;IACnB,CAAC,CAAC,EACFvB,QAAQ,CAACoB,MAAM,CAACN,aAAa,EAAE;MAC7BO,OAAO,EAAE,CAAC;MACVC,QAAQ,EAAE,IAAI;MACdC,eAAe,EAAE;IACnB,CAAC,CAAC,CACH,CACH,CAAC;IACDN,SAAS,CAACO,KAAK,CAAC,CAAC;IACjB,OAAO;MAAA,OAAMP,SAAS,CAACQ,IAAI,CAAC,CAAC;IAAA;EAC/B,CAAC,EAAE,EAAE,CAAC;EAEN,IAAMC,eAAe,GAAGZ,aAAa,CAACa,WAAW,CAAC;IAChDC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,SAAS,EAAE,SAAS;EACpC,CAAC,CAAC;EAEF,OACE3B,IAAA,CAACF,QAAQ,CAACD,IAAI;IACZc,KAAK,EAAE,CACL;MACEL,KAAK,EAALA,KAAK;MACLE,MAAM,EAANA,MAAM;MACNE,YAAY,EAAZA,YAAY;MACZc,eAAe,EAAfA;IACF,CAAC,EACDb,KAAK;EACL,CACH,CAAC;AAEN,CAAC;AAED,IAAMiB,YAAY,GAAG,SAAfA,YAAYA,CAAA;EAAA,OAChB1B,KAAA,CAACL,IAAI;IAACc,KAAK,EAAE;MACXa,eAAe,EAAE,SAAS;MAC1Bd,YAAY,EAAE,EAAE;MAChBmB,OAAO,EAAE,EAAE;MACXC,MAAM,EAAE,CAAC;MACTC,WAAW,EAAE,CAAC;MACdC,WAAW,EAAE;IACf,CAAE;IAAAC,QAAA,GACAjC,IAAA,CAACG,eAAe;MAACG,KAAK,EAAC,KAAK;MAACE,MAAM,EAAE,EAAG;MAACG,KAAK,EAAE;QAAEuB,YAAY,EAAE;MAAE;IAAE,CAAE,CAAC,EACvElC,IAAA,CAACG,eAAe;MAACG,KAAK,EAAC,MAAM;MAACE,MAAM,EAAE,EAAG;MAACG,KAAK,EAAE;QAAEuB,YAAY,EAAE;MAAE;IAAE,CAAE,CAAC,EACxElC,IAAA,CAACG,eAAe;MAACG,KAAK,EAAC,KAAK;MAACE,MAAM,EAAE,EAAG;MAACG,KAAK,EAAE;QAAEuB,YAAY,EAAE;MAAE;IAAE,CAAE,CAAC,EACvElC,IAAA,CAACG,eAAe;MAACG,KAAK,EAAC,KAAK;MAACE,MAAM,EAAE;IAAG,CAAE,CAAC;EAAA,CACvC,CAAC;AAAA,CACR;AAED,SAASL,eAAe,EAAEyB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}