{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport React, { useState } from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport TouchableOpacity from \"react-native-web/dist/exports/TouchableOpacity\";\nimport Switch from \"react-native-web/dist/exports/Switch\";\nimport ScrollView from \"react-native-web/dist/exports/ScrollView\";\nimport Alert from \"react-native-web/dist/exports/Alert\";\nimport { Button } from 'react-native-paper';\nimport Wrapper from \"../../components/Wrapper\";\nimport Card from \"../../components/Card\";\nimport StatCard from \"../../components/StatCard\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar Settings = function Settings() {\n  var _useState = useState(true),\n    _useState2 = _slicedToArray(_useState, 2),\n    isDarkMode = _useState2[0],\n    setIsDarkMode = _useState2[1];\n  var _useState3 = useState('English'),\n    _useState4 = _slicedToArray(_useState3, 2),\n    language = _useState4[0],\n    setLanguage = _useState4[1];\n  var _useState5 = useState('USD'),\n    _useState6 = _slicedToArray(_useState5, 2),\n    currency = _useState6[0],\n    setCurrency = _useState6[1];\n  var _useState7 = useState(true),\n    _useState8 = _slicedToArray(_useState7, 2),\n    pushNotifications = _useState8[0],\n    setPushNotifications = _useState8[1];\n  var _useState9 = useState(true),\n    _useState0 = _slicedToArray(_useState9, 2),\n    emailNotifications = _useState0[0],\n    setEmailNotifications = _useState0[1];\n  var _useState1 = useState(true),\n    _useState10 = _slicedToArray(_useState1, 2),\n    signalAlerts = _useState10[0],\n    setSignalAlerts = _useState10[1];\n  var _useState11 = useState(false),\n    _useState12 = _slicedToArray(_useState11, 2),\n    priceAlerts = _useState12[0],\n    setPriceAlerts = _useState12[1];\n  var _useState13 = useState(false),\n    _useState14 = _slicedToArray(_useState13, 2),\n    twoFactorAuth = _useState14[0],\n    setTwoFactorAuth = _useState14[1];\n  var _useState15 = useState(true),\n    _useState16 = _slicedToArray(_useState15, 2),\n    biometricAuth = _useState16[0],\n    setBiometricAuth = _useState16[1];\n  var _useState17 = useState(true),\n    _useState18 = _slicedToArray(_useState17, 2),\n    autoLock = _useState18[0],\n    setAutoLock = _useState18[1];\n  var _useState19 = useState('Medium'),\n    _useState20 = _slicedToArray(_useState19, 2),\n    riskLevel = _useState20[0],\n    setRiskLevel = _useState20[1];\n  var _useState21 = useState(false),\n    _useState22 = _slicedToArray(_useState21, 2),\n    autoTrade = _useState22[0],\n    setAutoTrade = _useState22[1];\n  var _useState23 = useState(true),\n    _useState24 = _slicedToArray(_useState23, 2),\n    paperTrading = _useState24[0],\n    setPaperTrading = _useState24[1];\n  var userProfile = {\n    name: 'John Trader',\n    email: '<EMAIL>',\n    memberSince: 'January 2024',\n    subscription: 'Premium',\n    avatar: '👤'\n  };\n  var appInfo = {\n    version: '2.1.0',\n    buildNumber: '2024.01.15',\n    lastUpdate: 'January 15, 2024'\n  };\n  var toggleTheme = function toggleTheme() {\n    return setIsDarkMode(function (prev) {\n      return !prev;\n    });\n  };\n  var togglePushNotifications = function togglePushNotifications() {\n    return setPushNotifications(function (prev) {\n      return !prev;\n    });\n  };\n  var toggleEmailNotifications = function toggleEmailNotifications() {\n    return setEmailNotifications(function (prev) {\n      return !prev;\n    });\n  };\n  var toggleSignalAlerts = function toggleSignalAlerts() {\n    return setSignalAlerts(function (prev) {\n      return !prev;\n    });\n  };\n  var togglePriceAlerts = function togglePriceAlerts() {\n    return setPriceAlerts(function (prev) {\n      return !prev;\n    });\n  };\n  var toggleTwoFactorAuth = function toggleTwoFactorAuth() {\n    return setTwoFactorAuth(function (prev) {\n      return !prev;\n    });\n  };\n  var toggleBiometricAuth = function toggleBiometricAuth() {\n    return setBiometricAuth(function (prev) {\n      return !prev;\n    });\n  };\n  var toggleAutoLock = function toggleAutoLock() {\n    return setAutoLock(function (prev) {\n      return !prev;\n    });\n  };\n  var toggleAutoTrade = function toggleAutoTrade() {\n    return setAutoTrade(function (prev) {\n      return !prev;\n    });\n  };\n  var togglePaperTrading = function togglePaperTrading() {\n    return setPaperTrading(function (prev) {\n      return !prev;\n    });\n  };\n  var handleLanguageChange = function handleLanguageChange() {\n    Alert.alert(\"Language\", \"Language selection coming soon!\");\n  };\n  var handleCurrencyChange = function handleCurrencyChange() {\n    Alert.alert(\"Currency\", \"Currency selection coming soon!\");\n  };\n  var handleRiskLevelChange = function handleRiskLevelChange() {\n    Alert.alert(\"Risk Level\", \"Risk level configuration coming soon!\");\n  };\n  var handleExportData = function handleExportData() {\n    Alert.alert(\"Export Data\", \"Data export feature coming soon!\");\n  };\n  var handleDeleteAccount = function handleDeleteAccount() {\n    Alert.alert(\"Delete Account\", \"Are you sure you want to delete your account? This action cannot be undone.\", [{\n      text: \"Cancel\",\n      style: \"cancel\"\n    }, {\n      text: \"Delete\",\n      style: \"destructive\",\n      onPress: function onPress() {\n        return Alert.alert(\"Account Deleted\", \"This is a demo - account not actually deleted.\");\n      }\n    }]);\n  };\n  var handleCancelSubscription = function handleCancelSubscription() {\n    Alert.alert(\"Cancel Subscription\", \"Subscription management coming soon!\");\n  };\n  var handleContactSupport = function handleContactSupport() {\n    Alert.alert(\"Support\", \"Contact support feature coming soon!\");\n  };\n  var handlePrivacyPolicy = function handlePrivacyPolicy() {\n    Alert.alert(\"Privacy Policy\", \"Privacy policy coming soon!\");\n  };\n  var handleTermsOfService = function handleTermsOfService() {\n    Alert.alert(\"Terms of Service\", \"Terms of service coming soon!\");\n  };\n  return _jsx(Wrapper, {\n    children: _jsxs(ScrollView, {\n      style: {\n        flex: 1\n      },\n      children: [_jsxs(View, {\n        style: {\n          padding: 16,\n          paddingBottom: 8\n        },\n        children: [_jsx(Text, {\n          style: {\n            color: '#fff',\n            fontSize: 28,\n            fontFamily: 'Poppins_700Bold',\n            marginBottom: 4\n          },\n          children: \"Settings \\u2699\\uFE0F\"\n        }), _jsx(Text, {\n          style: {\n            color: '#8a8a8a',\n            fontSize: 14,\n            fontFamily: 'Poppins_400Regular'\n          },\n          children: \"Customize your trading experience\"\n        })]\n      }), _jsx(View, {\n        style: {\n          paddingHorizontal: 16,\n          marginBottom: 16\n        },\n        children: _jsx(Card, {\n          children: _jsxs(View, {\n            style: {\n              flexDirection: 'row',\n              alignItems: 'center'\n            },\n            children: [_jsx(View, {\n              style: {\n                backgroundColor: '#FECB37',\n                borderRadius: 25,\n                width: 50,\n                height: 50,\n                alignItems: 'center',\n                justifyContent: 'center',\n                marginRight: 16\n              },\n              children: _jsx(Text, {\n                style: {\n                  fontSize: 24\n                },\n                children: userProfile.avatar\n              })\n            }), _jsxs(View, {\n              style: {\n                flex: 1\n              },\n              children: [_jsx(Text, {\n                style: {\n                  color: '#fff',\n                  fontSize: 18,\n                  fontFamily: 'Poppins_600SemiBold',\n                  marginBottom: 2\n                },\n                children: userProfile.name\n              }), _jsx(Text, {\n                style: {\n                  color: '#8a8a8a',\n                  fontSize: 14,\n                  fontFamily: 'Poppins_400Regular',\n                  marginBottom: 2\n                },\n                children: userProfile.email\n              }), _jsxs(View, {\n                style: {\n                  flexDirection: 'row',\n                  alignItems: 'center'\n                },\n                children: [_jsx(View, {\n                  style: {\n                    backgroundColor: '#FECB37',\n                    paddingHorizontal: 8,\n                    paddingVertical: 2,\n                    borderRadius: 4,\n                    marginRight: 8\n                  },\n                  children: _jsx(Text, {\n                    style: {\n                      color: '#000',\n                      fontSize: 10,\n                      fontFamily: 'Poppins_600SemiBold'\n                    },\n                    children: userProfile.subscription\n                  })\n                }), _jsxs(Text, {\n                  style: {\n                    color: '#8a8a8a',\n                    fontSize: 12,\n                    fontFamily: 'Poppins_400Regular'\n                  },\n                  children: [\"Member since \", userProfile.memberSince]\n                })]\n              })]\n            }), _jsx(TouchableOpacity, {\n              children: _jsx(Text, {\n                style: {\n                  color: '#FECB37',\n                  fontSize: 12,\n                  fontFamily: 'Poppins_500Medium'\n                },\n                children: \"Edit\"\n              })\n            })]\n          })\n        })\n      }), _jsxs(View, {\n        style: {\n          paddingHorizontal: 8,\n          marginBottom: 16\n        },\n        children: [_jsx(Text, {\n          style: {\n            color: '#fff',\n            fontSize: 18,\n            fontFamily: 'Poppins_600SemiBold',\n            marginBottom: 12,\n            paddingHorizontal: 8\n          },\n          children: \"Account Overview\"\n        }), _jsxs(View, {\n          style: {\n            flexDirection: 'row',\n            flexWrap: 'wrap'\n          },\n          children: [_jsx(StatCard, {\n            title: \"Active Subscriptions\",\n            value: \"3\",\n            subtitle: \"Channels\",\n            icon: \"\\uD83D\\uDCFA\"\n          }), _jsx(StatCard, {\n            title: \"Total Signals\",\n            value: \"1,247\",\n            subtitle: \"Received\",\n            icon: \"\\uD83D\\uDCE1\"\n          }), _jsx(StatCard, {\n            title: \"Success Rate\",\n            value: \"78.5%\",\n            change: \"+2.1%\",\n            changeType: \"positive\",\n            icon: \"\\uD83C\\uDFAF\"\n          }), _jsx(StatCard, {\n            title: \"Account Level\",\n            value: \"Premium\",\n            subtitle: \"Active\",\n            icon: \"\\uD83D\\uDC8E\"\n          })]\n        })]\n      }), _jsxs(View, {\n        style: {\n          paddingHorizontal: 16,\n          marginBottom: 16\n        },\n        children: [_jsx(Text, {\n          style: {\n            color: '#fff',\n            fontSize: 18,\n            fontFamily: 'Poppins_600SemiBold',\n            marginBottom: 12\n          },\n          children: \"Display & Preferences \\uD83C\\uDFA8\"\n        }), _jsxs(Card, {\n          style: {\n            marginBottom: 12\n          },\n          children: [_jsxs(View, {\n            style: {\n              flexDirection: 'row',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              marginBottom: 16\n            },\n            children: [_jsxs(View, {\n              children: [_jsx(Text, {\n                style: {\n                  color: '#fff',\n                  fontSize: 16,\n                  fontFamily: 'Poppins_500Medium'\n                },\n                children: \"Dark Mode\"\n              }), _jsx(Text, {\n                style: {\n                  color: '#8a8a8a',\n                  fontSize: 12,\n                  fontFamily: 'Poppins_400Regular'\n                },\n                children: \"Use dark theme\"\n              })]\n            }), _jsx(Switch, {\n              value: isDarkMode,\n              onValueChange: toggleTheme,\n              thumbColor: isDarkMode ? '#FECB37' : '#ccc',\n              trackColor: {\n                false: '#333',\n                true: '#FECB37'\n              }\n            })]\n          }), _jsxs(TouchableOpacity, {\n            style: {\n              flexDirection: 'row',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              marginBottom: 16\n            },\n            onPress: handleLanguageChange,\n            children: [_jsxs(View, {\n              children: [_jsx(Text, {\n                style: {\n                  color: '#fff',\n                  fontSize: 16,\n                  fontFamily: 'Poppins_500Medium'\n                },\n                children: \"Language\"\n              }), _jsx(Text, {\n                style: {\n                  color: '#8a8a8a',\n                  fontSize: 12,\n                  fontFamily: 'Poppins_400Regular'\n                },\n                children: \"App language\"\n              })]\n            }), _jsxs(View, {\n              style: {\n                flexDirection: 'row',\n                alignItems: 'center'\n              },\n              children: [_jsx(Text, {\n                style: {\n                  color: '#FECB37',\n                  fontSize: 14,\n                  fontFamily: 'Poppins_500Medium',\n                  marginRight: 8\n                },\n                children: language\n              }), _jsx(Text, {\n                style: {\n                  color: '#8a8a8a',\n                  fontSize: 16\n                },\n                children: \"\\u203A\"\n              })]\n            })]\n          }), _jsxs(TouchableOpacity, {\n            style: {\n              flexDirection: 'row',\n              justifyContent: 'space-between',\n              alignItems: 'center'\n            },\n            onPress: handleCurrencyChange,\n            children: [_jsxs(View, {\n              children: [_jsx(Text, {\n                style: {\n                  color: '#fff',\n                  fontSize: 16,\n                  fontFamily: 'Poppins_500Medium'\n                },\n                children: \"Currency\"\n              }), _jsx(Text, {\n                style: {\n                  color: '#8a8a8a',\n                  fontSize: 12,\n                  fontFamily: 'Poppins_400Regular'\n                },\n                children: \"Display currency\"\n              })]\n            }), _jsxs(View, {\n              style: {\n                flexDirection: 'row',\n                alignItems: 'center'\n              },\n              children: [_jsx(Text, {\n                style: {\n                  color: '#FECB37',\n                  fontSize: 14,\n                  fontFamily: 'Poppins_500Medium',\n                  marginRight: 8\n                },\n                children: currency\n              }), _jsx(Text, {\n                style: {\n                  color: '#8a8a8a',\n                  fontSize: 16\n                },\n                children: \"\\u203A\"\n              })]\n            })]\n          })]\n        })]\n      }), _jsxs(View, {\n        style: {\n          paddingHorizontal: 16,\n          marginBottom: 16\n        },\n        children: [_jsx(Text, {\n          style: {\n            color: '#fff',\n            fontSize: 18,\n            fontFamily: 'Poppins_600SemiBold',\n            marginBottom: 12\n          },\n          children: \"Notifications \\uD83D\\uDD14\"\n        }), _jsxs(Card, {\n          children: [_jsxs(View, {\n            style: {\n              flexDirection: 'row',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              marginBottom: 16\n            },\n            children: [_jsxs(View, {\n              children: [_jsx(Text, {\n                style: {\n                  color: '#fff',\n                  fontSize: 16,\n                  fontFamily: 'Poppins_500Medium'\n                },\n                children: \"Push Notifications\"\n              }), _jsx(Text, {\n                style: {\n                  color: '#8a8a8a',\n                  fontSize: 12,\n                  fontFamily: 'Poppins_400Regular'\n                },\n                children: \"Receive push notifications\"\n              })]\n            }), _jsx(Switch, {\n              value: pushNotifications,\n              onValueChange: togglePushNotifications,\n              thumbColor: pushNotifications ? '#FECB37' : '#ccc',\n              trackColor: {\n                false: '#333',\n                true: '#FECB37'\n              }\n            })]\n          }), _jsxs(View, {\n            style: {\n              flexDirection: 'row',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              marginBottom: 16\n            },\n            children: [_jsxs(View, {\n              children: [_jsx(Text, {\n                style: {\n                  color: '#fff',\n                  fontSize: 16,\n                  fontFamily: 'Poppins_500Medium'\n                },\n                children: \"Email Notifications\"\n              }), _jsx(Text, {\n                style: {\n                  color: '#8a8a8a',\n                  fontSize: 12,\n                  fontFamily: 'Poppins_400Regular'\n                },\n                children: \"Receive email updates\"\n              })]\n            }), _jsx(Switch, {\n              value: emailNotifications,\n              onValueChange: toggleEmailNotifications,\n              thumbColor: emailNotifications ? '#FECB37' : '#ccc',\n              trackColor: {\n                false: '#333',\n                true: '#FECB37'\n              }\n            })]\n          }), _jsxs(View, {\n            style: {\n              flexDirection: 'row',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              marginBottom: 16\n            },\n            children: [_jsxs(View, {\n              children: [_jsx(Text, {\n                style: {\n                  color: '#fff',\n                  fontSize: 16,\n                  fontFamily: 'Poppins_500Medium'\n                },\n                children: \"Signal Alerts\"\n              }), _jsx(Text, {\n                style: {\n                  color: '#8a8a8a',\n                  fontSize: 12,\n                  fontFamily: 'Poppins_400Regular'\n                },\n                children: \"New trading signals\"\n              })]\n            }), _jsx(Switch, {\n              value: signalAlerts,\n              onValueChange: toggleSignalAlerts,\n              thumbColor: signalAlerts ? '#FECB37' : '#ccc',\n              trackColor: {\n                false: '#333',\n                true: '#FECB37'\n              }\n            })]\n          }), _jsxs(View, {\n            style: {\n              flexDirection: 'row',\n              justifyContent: 'space-between',\n              alignItems: 'center'\n            },\n            children: [_jsxs(View, {\n              children: [_jsx(Text, {\n                style: {\n                  color: '#fff',\n                  fontSize: 16,\n                  fontFamily: 'Poppins_500Medium'\n                },\n                children: \"Price Alerts\"\n              }), _jsx(Text, {\n                style: {\n                  color: '#8a8a8a',\n                  fontSize: 12,\n                  fontFamily: 'Poppins_400Regular'\n                },\n                children: \"Price movement alerts\"\n              })]\n            }), _jsx(Switch, {\n              value: priceAlerts,\n              onValueChange: togglePriceAlerts,\n              thumbColor: priceAlerts ? '#FECB37' : '#ccc',\n              trackColor: {\n                false: '#333',\n                true: '#FECB37'\n              }\n            })]\n          })]\n        })]\n      }), _jsxs(View, {\n        style: {\n          paddingHorizontal: 16,\n          marginBottom: 16\n        },\n        children: [_jsx(Text, {\n          style: {\n            color: '#fff',\n            fontSize: 18,\n            fontFamily: 'Poppins_600SemiBold',\n            marginBottom: 12\n          },\n          children: \"Security & Privacy \\uD83D\\uDD12\"\n        }), _jsxs(Card, {\n          children: [_jsxs(View, {\n            style: {\n              flexDirection: 'row',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              marginBottom: 16\n            },\n            children: [_jsxs(View, {\n              children: [_jsx(Text, {\n                style: {\n                  color: '#fff',\n                  fontSize: 16,\n                  fontFamily: 'Poppins_500Medium'\n                },\n                children: \"Two-Factor Authentication\"\n              }), _jsx(Text, {\n                style: {\n                  color: '#8a8a8a',\n                  fontSize: 12,\n                  fontFamily: 'Poppins_400Regular'\n                },\n                children: \"Extra security layer\"\n              })]\n            }), _jsx(Switch, {\n              value: twoFactorAuth,\n              onValueChange: toggleTwoFactorAuth,\n              thumbColor: twoFactorAuth ? '#FECB37' : '#ccc',\n              trackColor: {\n                false: '#333',\n                true: '#FECB37'\n              }\n            })]\n          }), _jsxs(View, {\n            style: {\n              flexDirection: 'row',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              marginBottom: 16\n            },\n            children: [_jsxs(View, {\n              children: [_jsx(Text, {\n                style: {\n                  color: '#fff',\n                  fontSize: 16,\n                  fontFamily: 'Poppins_500Medium'\n                },\n                children: \"Biometric Authentication\"\n              }), _jsx(Text, {\n                style: {\n                  color: '#8a8a8a',\n                  fontSize: 12,\n                  fontFamily: 'Poppins_400Regular'\n                },\n                children: \"Fingerprint/Face ID\"\n              })]\n            }), _jsx(Switch, {\n              value: biometricAuth,\n              onValueChange: toggleBiometricAuth,\n              thumbColor: biometricAuth ? '#FECB37' : '#ccc',\n              trackColor: {\n                false: '#333',\n                true: '#FECB37'\n              }\n            })]\n          }), _jsxs(View, {\n            style: {\n              flexDirection: 'row',\n              justifyContent: 'space-between',\n              alignItems: 'center'\n            },\n            children: [_jsxs(View, {\n              children: [_jsx(Text, {\n                style: {\n                  color: '#fff',\n                  fontSize: 16,\n                  fontFamily: 'Poppins_500Medium'\n                },\n                children: \"Auto Lock\"\n              }), _jsx(Text, {\n                style: {\n                  color: '#8a8a8a',\n                  fontSize: 12,\n                  fontFamily: 'Poppins_400Regular'\n                },\n                children: \"Lock app when inactive\"\n              })]\n            }), _jsx(Switch, {\n              value: autoLock,\n              onValueChange: toggleAutoLock,\n              thumbColor: autoLock ? '#FECB37' : '#ccc',\n              trackColor: {\n                false: '#333',\n                true: '#FECB37'\n              }\n            })]\n          })]\n        })]\n      }), _jsxs(View, {\n        style: {\n          paddingHorizontal: 16,\n          marginBottom: 16\n        },\n        children: [_jsx(Text, {\n          style: {\n            color: '#fff',\n            fontSize: 18,\n            fontFamily: 'Poppins_600SemiBold',\n            marginBottom: 12\n          },\n          children: \"Trading Settings \\uD83D\\uDCC8\"\n        }), _jsxs(Card, {\n          children: [_jsxs(TouchableOpacity, {\n            style: {\n              flexDirection: 'row',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              marginBottom: 16\n            },\n            onPress: handleRiskLevelChange,\n            children: [_jsxs(View, {\n              children: [_jsx(Text, {\n                style: {\n                  color: '#fff',\n                  fontSize: 16,\n                  fontFamily: 'Poppins_500Medium'\n                },\n                children: \"Risk Level\"\n              }), _jsx(Text, {\n                style: {\n                  color: '#8a8a8a',\n                  fontSize: 12,\n                  fontFamily: 'Poppins_400Regular'\n                },\n                children: \"Trading risk tolerance\"\n              })]\n            }), _jsxs(View, {\n              style: {\n                flexDirection: 'row',\n                alignItems: 'center'\n              },\n              children: [_jsx(Text, {\n                style: {\n                  color: '#FECB37',\n                  fontSize: 14,\n                  fontFamily: 'Poppins_500Medium',\n                  marginRight: 8\n                },\n                children: riskLevel\n              }), _jsx(Text, {\n                style: {\n                  color: '#8a8a8a',\n                  fontSize: 16\n                },\n                children: \"\\u203A\"\n              })]\n            })]\n          }), _jsxs(View, {\n            style: {\n              flexDirection: 'row',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              marginBottom: 16\n            },\n            children: [_jsxs(View, {\n              children: [_jsx(Text, {\n                style: {\n                  color: '#fff',\n                  fontSize: 16,\n                  fontFamily: 'Poppins_500Medium'\n                },\n                children: \"Auto Trading\"\n              }), _jsx(Text, {\n                style: {\n                  color: '#8a8a8a',\n                  fontSize: 12,\n                  fontFamily: 'Poppins_400Regular'\n                },\n                children: \"Automatic signal execution\"\n              })]\n            }), _jsx(Switch, {\n              value: autoTrade,\n              onValueChange: toggleAutoTrade,\n              thumbColor: autoTrade ? '#FECB37' : '#ccc',\n              trackColor: {\n                false: '#333',\n                true: '#FECB37'\n              }\n            })]\n          }), _jsxs(View, {\n            style: {\n              flexDirection: 'row',\n              justifyContent: 'space-between',\n              alignItems: 'center'\n            },\n            children: [_jsxs(View, {\n              children: [_jsx(Text, {\n                style: {\n                  color: '#fff',\n                  fontSize: 16,\n                  fontFamily: 'Poppins_500Medium'\n                },\n                children: \"Paper Trading\"\n              }), _jsx(Text, {\n                style: {\n                  color: '#8a8a8a',\n                  fontSize: 12,\n                  fontFamily: 'Poppins_400Regular'\n                },\n                children: \"Practice mode\"\n              })]\n            }), _jsx(Switch, {\n              value: paperTrading,\n              onValueChange: togglePaperTrading,\n              thumbColor: paperTrading ? '#FECB37' : '#ccc',\n              trackColor: {\n                false: '#333',\n                true: '#FECB37'\n              }\n            })]\n          })]\n        })]\n      }), _jsxs(View, {\n        style: {\n          paddingHorizontal: 16,\n          marginBottom: 16\n        },\n        children: [_jsx(Text, {\n          style: {\n            color: '#fff',\n            fontSize: 18,\n            fontFamily: 'Poppins_600SemiBold',\n            marginBottom: 12\n          },\n          children: \"Support & Legal \\uD83D\\uDCCB\"\n        }), _jsxs(Card, {\n          children: [_jsxs(TouchableOpacity, {\n            style: {\n              flexDirection: 'row',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              marginBottom: 16\n            },\n            onPress: handleContactSupport,\n            children: [_jsxs(View, {\n              children: [_jsx(Text, {\n                style: {\n                  color: '#fff',\n                  fontSize: 16,\n                  fontFamily: 'Poppins_500Medium'\n                },\n                children: \"Contact Support\"\n              }), _jsx(Text, {\n                style: {\n                  color: '#8a8a8a',\n                  fontSize: 12,\n                  fontFamily: 'Poppins_400Regular'\n                },\n                children: \"Get help and support\"\n              })]\n            }), _jsx(Text, {\n              style: {\n                color: '#8a8a8a',\n                fontSize: 16\n              },\n              children: \"\\u203A\"\n            })]\n          }), _jsxs(TouchableOpacity, {\n            style: {\n              flexDirection: 'row',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              marginBottom: 16\n            },\n            onPress: handleExportData,\n            children: [_jsxs(View, {\n              children: [_jsx(Text, {\n                style: {\n                  color: '#fff',\n                  fontSize: 16,\n                  fontFamily: 'Poppins_500Medium'\n                },\n                children: \"Export Data\"\n              }), _jsx(Text, {\n                style: {\n                  color: '#8a8a8a',\n                  fontSize: 12,\n                  fontFamily: 'Poppins_400Regular'\n                },\n                children: \"Download your data\"\n              })]\n            }), _jsx(Text, {\n              style: {\n                color: '#8a8a8a',\n                fontSize: 16\n              },\n              children: \"\\u203A\"\n            })]\n          }), _jsxs(TouchableOpacity, {\n            style: {\n              flexDirection: 'row',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              marginBottom: 16\n            },\n            onPress: handlePrivacyPolicy,\n            children: [_jsxs(View, {\n              children: [_jsx(Text, {\n                style: {\n                  color: '#fff',\n                  fontSize: 16,\n                  fontFamily: 'Poppins_500Medium'\n                },\n                children: \"Privacy Policy\"\n              }), _jsx(Text, {\n                style: {\n                  color: '#8a8a8a',\n                  fontSize: 12,\n                  fontFamily: 'Poppins_400Regular'\n                },\n                children: \"How we protect your data\"\n              })]\n            }), _jsx(Text, {\n              style: {\n                color: '#8a8a8a',\n                fontSize: 16\n              },\n              children: \"\\u203A\"\n            })]\n          }), _jsxs(TouchableOpacity, {\n            style: {\n              flexDirection: 'row',\n              justifyContent: 'space-between',\n              alignItems: 'center'\n            },\n            onPress: handleTermsOfService,\n            children: [_jsxs(View, {\n              children: [_jsx(Text, {\n                style: {\n                  color: '#fff',\n                  fontSize: 16,\n                  fontFamily: 'Poppins_500Medium'\n                },\n                children: \"Terms of Service\"\n              }), _jsx(Text, {\n                style: {\n                  color: '#8a8a8a',\n                  fontSize: 12,\n                  fontFamily: 'Poppins_400Regular'\n                },\n                children: \"App usage terms\"\n              })]\n            }), _jsx(Text, {\n              style: {\n                color: '#8a8a8a',\n                fontSize: 16\n              },\n              children: \"\\u203A\"\n            })]\n          })]\n        })]\n      }), _jsxs(View, {\n        style: {\n          paddingHorizontal: 16,\n          marginBottom: 16\n        },\n        children: [_jsx(Text, {\n          style: {\n            color: '#fff',\n            fontSize: 18,\n            fontFamily: 'Poppins_600SemiBold',\n            marginBottom: 12\n          },\n          children: \"App Information \\u2139\\uFE0F\"\n        }), _jsxs(Card, {\n          children: [_jsxs(View, {\n            style: {\n              flexDirection: 'row',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              marginBottom: 12\n            },\n            children: [_jsx(Text, {\n              style: {\n                color: '#8a8a8a',\n                fontSize: 14,\n                fontFamily: 'Poppins_400Regular'\n              },\n              children: \"Version\"\n            }), _jsx(Text, {\n              style: {\n                color: '#fff',\n                fontSize: 14,\n                fontFamily: 'Poppins_500Medium'\n              },\n              children: appInfo.version\n            })]\n          }), _jsxs(View, {\n            style: {\n              flexDirection: 'row',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              marginBottom: 12\n            },\n            children: [_jsx(Text, {\n              style: {\n                color: '#8a8a8a',\n                fontSize: 14,\n                fontFamily: 'Poppins_400Regular'\n              },\n              children: \"Build\"\n            }), _jsx(Text, {\n              style: {\n                color: '#fff',\n                fontSize: 14,\n                fontFamily: 'Poppins_500Medium'\n              },\n              children: appInfo.buildNumber\n            })]\n          }), _jsxs(View, {\n            style: {\n              flexDirection: 'row',\n              justifyContent: 'space-between',\n              alignItems: 'center'\n            },\n            children: [_jsx(Text, {\n              style: {\n                color: '#8a8a8a',\n                fontSize: 14,\n                fontFamily: 'Poppins_400Regular'\n              },\n              children: \"Last Update\"\n            }), _jsx(Text, {\n              style: {\n                color: '#fff',\n                fontSize: 14,\n                fontFamily: 'Poppins_500Medium'\n              },\n              children: appInfo.lastUpdate\n            })]\n          })]\n        })]\n      }), _jsxs(View, {\n        style: {\n          paddingHorizontal: 16,\n          marginBottom: 20\n        },\n        children: [_jsx(Text, {\n          style: {\n            color: '#F44336',\n            fontSize: 18,\n            fontFamily: 'Poppins_600SemiBold',\n            marginBottom: 12\n          },\n          children: \"Danger Zone \\u26A0\\uFE0F\"\n        }), _jsxs(Card, {\n          style: {\n            borderColor: '#F44336',\n            borderWidth: 1\n          },\n          children: [_jsx(Button, {\n            mode: \"outlined\",\n            onPress: handleCancelSubscription,\n            style: {\n              borderColor: '#FF9800',\n              marginBottom: 12\n            },\n            labelStyle: {\n              color: '#FF9800',\n              fontFamily: 'Poppins_500Medium'\n            },\n            children: \"Cancel Subscription\"\n          }), _jsx(Button, {\n            mode: \"outlined\",\n            onPress: handleDeleteAccount,\n            style: {\n              borderColor: '#F44336'\n            },\n            labelStyle: {\n              color: '#F44336',\n              fontFamily: 'Poppins_500Medium'\n            },\n            children: \"Delete Account\"\n          })]\n        })]\n      })]\n    })\n  });\n};\nexport default Settings;", "map": {"version": 3, "names": ["React", "useState", "View", "Text", "TouchableOpacity", "Switch", "ScrollView", "<PERSON><PERSON>", "<PERSON><PERSON>", "Wrapper", "Card", "StatCard", "jsx", "_jsx", "jsxs", "_jsxs", "Settings", "_useState", "_useState2", "_slicedToArray", "isDarkMode", "setIsDarkMode", "_useState3", "_useState4", "language", "setLanguage", "_useState5", "_useState6", "currency", "setCurrency", "_useState7", "_useState8", "pushNotifications", "setPushNotifications", "_useState9", "_useState0", "emailNotifications", "setEmailNotifications", "_useState1", "_useState10", "signalAlerts", "set<PERSON>ign<PERSON><PERSON><PERSON><PERSON>", "_useState11", "_useState12", "priceAlerts", "set<PERSON><PERSON><PERSON><PERSON><PERSON>", "_useState13", "_useState14", "twoFactorAuth", "setTwoFactorAuth", "_useState15", "_useState16", "biometricAuth", "setBiometricAuth", "_useState17", "_useState18", "autoLock", "setAutoLock", "_useState19", "_useState20", "riskLevel", "setRiskLevel", "_useState21", "_useState22", "autoTrade", "setAutoTrade", "_useState23", "_useState24", "paperTrading", "setPaperTrading", "userProfile", "name", "email", "memberSince", "subscription", "avatar", "appInfo", "version", "buildNumber", "lastUpdate", "toggleTheme", "prev", "togglePushNotifications", "toggleEmailNotifications", "toggleSignal<PERSON><PERSON><PERSON>", "togglePriceAlerts", "toggleTwoFactorAuth", "toggleBiometricAuth", "toggleAutoLock", "toggleAutoTrade", "togglePaperTrading", "handleLanguageChange", "alert", "handleCurrencyChange", "handleRiskLevelChange", "handleExportData", "handleDeleteAccount", "text", "style", "onPress", "handleCancelSubscription", "handleContactSupport", "handlePrivacyPolicy", "handleTermsOfService", "children", "flex", "padding", "paddingBottom", "color", "fontSize", "fontFamily", "marginBottom", "paddingHorizontal", "flexDirection", "alignItems", "backgroundColor", "borderRadius", "width", "height", "justifyContent", "marginRight", "paddingVertical", "flexWrap", "title", "value", "subtitle", "icon", "change", "changeType", "onValueChange", "thumbColor", "trackColor", "false", "true", "borderColor", "borderWidth", "mode", "labelStyle"], "sources": ["E:/CryptoSignalsApp/src/pages/Settings/index.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { View, Text, TouchableOpacity, Switch, ScrollView, Alert } from 'react-native';\r\nimport { Button } from 'react-native-paper';\r\nimport Wrapper from '../../components/Wrapper';\r\nimport Card from '../../components/Card';\r\nimport StatCard from '../../components/StatCard';\r\n\r\nconst Settings = () => {\r\n  // Theme & Display\r\n  const [isDarkMode, setIsDarkMode] = useState(true);\r\n  const [language, setLanguage] = useState('English');\r\n  const [currency, setCurrency] = useState('USD');\r\n\r\n  // Notifications\r\n  const [pushNotifications, setPushNotifications] = useState(true);\r\n  const [emailNotifications, setEmailNotifications] = useState(true);\r\n  const [signalAlerts, setSignalAlerts] = useState(true);\r\n  const [priceAlerts, setPriceAlerts] = useState(false);\r\n\r\n  // Security\r\n  const [twoFactorAuth, setTwoFactorAuth] = useState(false);\r\n  const [biometricAuth, setBiometricAuth] = useState(true);\r\n  const [autoLock, setAutoLock] = useState(true);\r\n\r\n  // Trading\r\n  const [riskLevel, setRiskLevel] = useState('Medium');\r\n  const [autoTrade, setAutoTrade] = useState(false);\r\n  const [paperTrading, setPaperTrading] = useState(true);\r\n\r\n  // User profile data\r\n  const userProfile = {\r\n    name: 'John Trader',\r\n    email: '<EMAIL>',\r\n    memberSince: 'January 2024',\r\n    subscription: 'Premium',\r\n    avatar: '👤'\r\n  };\r\n\r\n  // App info\r\n  const appInfo = {\r\n    version: '2.1.0',\r\n    buildNumber: '2024.01.15',\r\n    lastUpdate: 'January 15, 2024'\r\n  };\r\n\r\n  // Toggle functions\r\n  const toggleTheme = () => setIsDarkMode(prev => !prev);\r\n  const togglePushNotifications = () => setPushNotifications(prev => !prev);\r\n  const toggleEmailNotifications = () => setEmailNotifications(prev => !prev);\r\n  const toggleSignalAlerts = () => setSignalAlerts(prev => !prev);\r\n  const togglePriceAlerts = () => setPriceAlerts(prev => !prev);\r\n  const toggleTwoFactorAuth = () => setTwoFactorAuth(prev => !prev);\r\n  const toggleBiometricAuth = () => setBiometricAuth(prev => !prev);\r\n  const toggleAutoLock = () => setAutoLock(prev => !prev);\r\n  const toggleAutoTrade = () => setAutoTrade(prev => !prev);\r\n  const togglePaperTrading = () => setPaperTrading(prev => !prev);\r\n\r\n  // Action handlers\r\n  const handleLanguageChange = () => {\r\n    Alert.alert(\"Language\", \"Language selection coming soon!\");\r\n  };\r\n\r\n  const handleCurrencyChange = () => {\r\n    Alert.alert(\"Currency\", \"Currency selection coming soon!\");\r\n  };\r\n\r\n  const handleRiskLevelChange = () => {\r\n    Alert.alert(\"Risk Level\", \"Risk level configuration coming soon!\");\r\n  };\r\n\r\n  const handleExportData = () => {\r\n    Alert.alert(\"Export Data\", \"Data export feature coming soon!\");\r\n  };\r\n\r\n  const handleDeleteAccount = () => {\r\n    Alert.alert(\r\n      \"Delete Account\",\r\n      \"Are you sure you want to delete your account? This action cannot be undone.\",\r\n      [\r\n        { text: \"Cancel\", style: \"cancel\" },\r\n        { text: \"Delete\", style: \"destructive\", onPress: () => Alert.alert(\"Account Deleted\", \"This is a demo - account not actually deleted.\") }\r\n      ]\r\n    );\r\n  };\r\n\r\n  const handleCancelSubscription = () => {\r\n    Alert.alert(\"Cancel Subscription\", \"Subscription management coming soon!\");\r\n  };\r\n\r\n  const handleContactSupport = () => {\r\n    Alert.alert(\"Support\", \"Contact support feature coming soon!\");\r\n  };\r\n\r\n  const handlePrivacyPolicy = () => {\r\n    Alert.alert(\"Privacy Policy\", \"Privacy policy coming soon!\");\r\n  };\r\n\r\n  const handleTermsOfService = () => {\r\n    Alert.alert(\"Terms of Service\", \"Terms of service coming soon!\");\r\n  };\r\n\r\n  return (\r\n    <Wrapper>\r\n      <ScrollView style={{ flex: 1 }}>\r\n        {/* Header */}\r\n        <View style={{ padding: 16, paddingBottom: 8 }}>\r\n          <Text style={{\r\n            color: '#fff',\r\n            fontSize: 28,\r\n            fontFamily: 'Poppins_700Bold',\r\n            marginBottom: 4\r\n          }}>\r\n            Settings ⚙️\r\n          </Text>\r\n          <Text style={{\r\n            color: '#8a8a8a',\r\n            fontSize: 14,\r\n            fontFamily: 'Poppins_400Regular'\r\n          }}>\r\n            Customize your trading experience\r\n          </Text>\r\n        </View>\r\n\r\n        {/* User Profile Section */}\r\n        <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>\r\n          <Card>\r\n            <View style={{ flexDirection: 'row', alignItems: 'center' }}>\r\n              <View style={{\r\n                backgroundColor: '#FECB37',\r\n                borderRadius: 25,\r\n                width: 50,\r\n                height: 50,\r\n                alignItems: 'center',\r\n                justifyContent: 'center',\r\n                marginRight: 16\r\n              }}>\r\n                <Text style={{ fontSize: 24 }}>{userProfile.avatar}</Text>\r\n              </View>\r\n              <View style={{ flex: 1 }}>\r\n                <Text style={{\r\n                  color: '#fff',\r\n                  fontSize: 18,\r\n                  fontFamily: 'Poppins_600SemiBold',\r\n                  marginBottom: 2\r\n                }}>\r\n                  {userProfile.name}\r\n                </Text>\r\n                <Text style={{\r\n                  color: '#8a8a8a',\r\n                  fontSize: 14,\r\n                  fontFamily: 'Poppins_400Regular',\r\n                  marginBottom: 2\r\n                }}>\r\n                  {userProfile.email}\r\n                </Text>\r\n                <View style={{ flexDirection: 'row', alignItems: 'center' }}>\r\n                  <View style={{\r\n                    backgroundColor: '#FECB37',\r\n                    paddingHorizontal: 8,\r\n                    paddingVertical: 2,\r\n                    borderRadius: 4,\r\n                    marginRight: 8\r\n                  }}>\r\n                    <Text style={{\r\n                      color: '#000',\r\n                      fontSize: 10,\r\n                      fontFamily: 'Poppins_600SemiBold'\r\n                    }}>\r\n                      {userProfile.subscription}\r\n                    </Text>\r\n                  </View>\r\n                  <Text style={{\r\n                    color: '#8a8a8a',\r\n                    fontSize: 12,\r\n                    fontFamily: 'Poppins_400Regular'\r\n                  }}>\r\n                    Member since {userProfile.memberSince}\r\n                  </Text>\r\n                </View>\r\n              </View>\r\n              <TouchableOpacity>\r\n                <Text style={{ color: '#FECB37', fontSize: 12, fontFamily: 'Poppins_500Medium' }}>\r\n                  Edit\r\n                </Text>\r\n              </TouchableOpacity>\r\n            </View>\r\n          </Card>\r\n        </View>\r\n\r\n        {/* Account Stats */}\r\n        <View style={{ paddingHorizontal: 8, marginBottom: 16 }}>\r\n          <Text style={{\r\n            color: '#fff',\r\n            fontSize: 18,\r\n            fontFamily: 'Poppins_600SemiBold',\r\n            marginBottom: 12,\r\n            paddingHorizontal: 8\r\n          }}>\r\n            Account Overview\r\n          </Text>\r\n          <View style={{ flexDirection: 'row', flexWrap: 'wrap' }}>\r\n            <StatCard\r\n              title=\"Active Subscriptions\"\r\n              value=\"3\"\r\n              subtitle=\"Channels\"\r\n              icon=\"📺\"\r\n            />\r\n            <StatCard\r\n              title=\"Total Signals\"\r\n              value=\"1,247\"\r\n              subtitle=\"Received\"\r\n              icon=\"📡\"\r\n            />\r\n            <StatCard\r\n              title=\"Success Rate\"\r\n              value=\"78.5%\"\r\n              change=\"+2.1%\"\r\n              changeType=\"positive\"\r\n              icon=\"🎯\"\r\n            />\r\n            <StatCard\r\n              title=\"Account Level\"\r\n              value=\"Premium\"\r\n              subtitle=\"Active\"\r\n              icon=\"💎\"\r\n            />\r\n          </View>\r\n        </View>\r\n\r\n        {/* Display & Preferences */}\r\n        <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>\r\n          <Text style={{\r\n            color: '#fff',\r\n            fontSize: 18,\r\n            fontFamily: 'Poppins_600SemiBold',\r\n            marginBottom: 12\r\n          }}>\r\n            Display & Preferences 🎨\r\n          </Text>\r\n\r\n          <Card style={{ marginBottom: 12 }}>\r\n            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>\r\n              <View>\r\n                <Text style={{ color: '#fff', fontSize: 16, fontFamily: 'Poppins_500Medium' }}>Dark Mode</Text>\r\n                <Text style={{ color: '#8a8a8a', fontSize: 12, fontFamily: 'Poppins_400Regular' }}>Use dark theme</Text>\r\n              </View>\r\n              <Switch\r\n                value={isDarkMode}\r\n                onValueChange={toggleTheme}\r\n                thumbColor={isDarkMode ? '#FECB37' : '#ccc'}\r\n                trackColor={{ false: '#333', true: '#FECB37' }}\r\n              />\r\n            </View>\r\n\r\n            <TouchableOpacity\r\n              style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}\r\n              onPress={handleLanguageChange}\r\n            >\r\n              <View>\r\n                <Text style={{ color: '#fff', fontSize: 16, fontFamily: 'Poppins_500Medium' }}>Language</Text>\r\n                <Text style={{ color: '#8a8a8a', fontSize: 12, fontFamily: 'Poppins_400Regular' }}>App language</Text>\r\n              </View>\r\n              <View style={{ flexDirection: 'row', alignItems: 'center' }}>\r\n                <Text style={{ color: '#FECB37', fontSize: 14, fontFamily: 'Poppins_500Medium', marginRight: 8 }}>\r\n                  {language}\r\n                </Text>\r\n                <Text style={{ color: '#8a8a8a', fontSize: 16 }}>›</Text>\r\n              </View>\r\n            </TouchableOpacity>\r\n\r\n            <TouchableOpacity\r\n              style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}\r\n              onPress={handleCurrencyChange}\r\n            >\r\n              <View>\r\n                <Text style={{ color: '#fff', fontSize: 16, fontFamily: 'Poppins_500Medium' }}>Currency</Text>\r\n                <Text style={{ color: '#8a8a8a', fontSize: 12, fontFamily: 'Poppins_400Regular' }}>Display currency</Text>\r\n              </View>\r\n              <View style={{ flexDirection: 'row', alignItems: 'center' }}>\r\n                <Text style={{ color: '#FECB37', fontSize: 14, fontFamily: 'Poppins_500Medium', marginRight: 8 }}>\r\n                  {currency}\r\n                </Text>\r\n                <Text style={{ color: '#8a8a8a', fontSize: 16 }}>›</Text>\r\n              </View>\r\n            </TouchableOpacity>\r\n          </Card>\r\n        </View>\r\n\r\n        {/* Notifications */}\r\n        <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>\r\n          <Text style={{\r\n            color: '#fff',\r\n            fontSize: 18,\r\n            fontFamily: 'Poppins_600SemiBold',\r\n            marginBottom: 12\r\n          }}>\r\n            Notifications 🔔\r\n          </Text>\r\n\r\n          <Card>\r\n            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>\r\n              <View>\r\n                <Text style={{ color: '#fff', fontSize: 16, fontFamily: 'Poppins_500Medium' }}>Push Notifications</Text>\r\n                <Text style={{ color: '#8a8a8a', fontSize: 12, fontFamily: 'Poppins_400Regular' }}>Receive push notifications</Text>\r\n              </View>\r\n              <Switch\r\n                value={pushNotifications}\r\n                onValueChange={togglePushNotifications}\r\n                thumbColor={pushNotifications ? '#FECB37' : '#ccc'}\r\n                trackColor={{ false: '#333', true: '#FECB37' }}\r\n              />\r\n            </View>\r\n\r\n            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>\r\n              <View>\r\n                <Text style={{ color: '#fff', fontSize: 16, fontFamily: 'Poppins_500Medium' }}>Email Notifications</Text>\r\n                <Text style={{ color: '#8a8a8a', fontSize: 12, fontFamily: 'Poppins_400Regular' }}>Receive email updates</Text>\r\n              </View>\r\n              <Switch\r\n                value={emailNotifications}\r\n                onValueChange={toggleEmailNotifications}\r\n                thumbColor={emailNotifications ? '#FECB37' : '#ccc'}\r\n                trackColor={{ false: '#333', true: '#FECB37' }}\r\n              />\r\n            </View>\r\n\r\n            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>\r\n              <View>\r\n                <Text style={{ color: '#fff', fontSize: 16, fontFamily: 'Poppins_500Medium' }}>Signal Alerts</Text>\r\n                <Text style={{ color: '#8a8a8a', fontSize: 12, fontFamily: 'Poppins_400Regular' }}>New trading signals</Text>\r\n              </View>\r\n              <Switch\r\n                value={signalAlerts}\r\n                onValueChange={toggleSignalAlerts}\r\n                thumbColor={signalAlerts ? '#FECB37' : '#ccc'}\r\n                trackColor={{ false: '#333', true: '#FECB37' }}\r\n              />\r\n            </View>\r\n\r\n            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>\r\n              <View>\r\n                <Text style={{ color: '#fff', fontSize: 16, fontFamily: 'Poppins_500Medium' }}>Price Alerts</Text>\r\n                <Text style={{ color: '#8a8a8a', fontSize: 12, fontFamily: 'Poppins_400Regular' }}>Price movement alerts</Text>\r\n              </View>\r\n              <Switch\r\n                value={priceAlerts}\r\n                onValueChange={togglePriceAlerts}\r\n                thumbColor={priceAlerts ? '#FECB37' : '#ccc'}\r\n                trackColor={{ false: '#333', true: '#FECB37' }}\r\n              />\r\n            </View>\r\n          </Card>\r\n        </View>\r\n\r\n        {/* Security */}\r\n        <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>\r\n          <Text style={{\r\n            color: '#fff',\r\n            fontSize: 18,\r\n            fontFamily: 'Poppins_600SemiBold',\r\n            marginBottom: 12\r\n          }}>\r\n            Security & Privacy 🔒\r\n          </Text>\r\n\r\n          <Card>\r\n            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>\r\n              <View>\r\n                <Text style={{ color: '#fff', fontSize: 16, fontFamily: 'Poppins_500Medium' }}>Two-Factor Authentication</Text>\r\n                <Text style={{ color: '#8a8a8a', fontSize: 12, fontFamily: 'Poppins_400Regular' }}>Extra security layer</Text>\r\n              </View>\r\n              <Switch\r\n                value={twoFactorAuth}\r\n                onValueChange={toggleTwoFactorAuth}\r\n                thumbColor={twoFactorAuth ? '#FECB37' : '#ccc'}\r\n                trackColor={{ false: '#333', true: '#FECB37' }}\r\n              />\r\n            </View>\r\n\r\n            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>\r\n              <View>\r\n                <Text style={{ color: '#fff', fontSize: 16, fontFamily: 'Poppins_500Medium' }}>Biometric Authentication</Text>\r\n                <Text style={{ color: '#8a8a8a', fontSize: 12, fontFamily: 'Poppins_400Regular' }}>Fingerprint/Face ID</Text>\r\n              </View>\r\n              <Switch\r\n                value={biometricAuth}\r\n                onValueChange={toggleBiometricAuth}\r\n                thumbColor={biometricAuth ? '#FECB37' : '#ccc'}\r\n                trackColor={{ false: '#333', true: '#FECB37' }}\r\n              />\r\n            </View>\r\n\r\n            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>\r\n              <View>\r\n                <Text style={{ color: '#fff', fontSize: 16, fontFamily: 'Poppins_500Medium' }}>Auto Lock</Text>\r\n                <Text style={{ color: '#8a8a8a', fontSize: 12, fontFamily: 'Poppins_400Regular' }}>Lock app when inactive</Text>\r\n              </View>\r\n              <Switch\r\n                value={autoLock}\r\n                onValueChange={toggleAutoLock}\r\n                thumbColor={autoLock ? '#FECB37' : '#ccc'}\r\n                trackColor={{ false: '#333', true: '#FECB37' }}\r\n              />\r\n            </View>\r\n          </Card>\r\n        </View>\r\n\r\n        {/* Trading Settings */}\r\n        <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>\r\n          <Text style={{\r\n            color: '#fff',\r\n            fontSize: 18,\r\n            fontFamily: 'Poppins_600SemiBold',\r\n            marginBottom: 12\r\n          }}>\r\n            Trading Settings 📈\r\n          </Text>\r\n\r\n          <Card>\r\n            <TouchableOpacity\r\n              style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}\r\n              onPress={handleRiskLevelChange}\r\n            >\r\n              <View>\r\n                <Text style={{ color: '#fff', fontSize: 16, fontFamily: 'Poppins_500Medium' }}>Risk Level</Text>\r\n                <Text style={{ color: '#8a8a8a', fontSize: 12, fontFamily: 'Poppins_400Regular' }}>Trading risk tolerance</Text>\r\n              </View>\r\n              <View style={{ flexDirection: 'row', alignItems: 'center' }}>\r\n                <Text style={{ color: '#FECB37', fontSize: 14, fontFamily: 'Poppins_500Medium', marginRight: 8 }}>\r\n                  {riskLevel}\r\n                </Text>\r\n                <Text style={{ color: '#8a8a8a', fontSize: 16 }}>›</Text>\r\n              </View>\r\n            </TouchableOpacity>\r\n\r\n            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>\r\n              <View>\r\n                <Text style={{ color: '#fff', fontSize: 16, fontFamily: 'Poppins_500Medium' }}>Auto Trading</Text>\r\n                <Text style={{ color: '#8a8a8a', fontSize: 12, fontFamily: 'Poppins_400Regular' }}>Automatic signal execution</Text>\r\n              </View>\r\n              <Switch\r\n                value={autoTrade}\r\n                onValueChange={toggleAutoTrade}\r\n                thumbColor={autoTrade ? '#FECB37' : '#ccc'}\r\n                trackColor={{ false: '#333', true: '#FECB37' }}\r\n              />\r\n            </View>\r\n\r\n            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>\r\n              <View>\r\n                <Text style={{ color: '#fff', fontSize: 16, fontFamily: 'Poppins_500Medium' }}>Paper Trading</Text>\r\n                <Text style={{ color: '#8a8a8a', fontSize: 12, fontFamily: 'Poppins_400Regular' }}>Practice mode</Text>\r\n              </View>\r\n              <Switch\r\n                value={paperTrading}\r\n                onValueChange={togglePaperTrading}\r\n                thumbColor={paperTrading ? '#FECB37' : '#ccc'}\r\n                trackColor={{ false: '#333', true: '#FECB37' }}\r\n              />\r\n            </View>\r\n          </Card>\r\n        </View>\r\n\r\n        {/* Support & Legal */}\r\n        <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>\r\n          <Text style={{\r\n            color: '#fff',\r\n            fontSize: 18,\r\n            fontFamily: 'Poppins_600SemiBold',\r\n            marginBottom: 12\r\n          }}>\r\n            Support & Legal 📋\r\n          </Text>\r\n\r\n          <Card>\r\n            <TouchableOpacity\r\n              style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}\r\n              onPress={handleContactSupport}\r\n            >\r\n              <View>\r\n                <Text style={{ color: '#fff', fontSize: 16, fontFamily: 'Poppins_500Medium' }}>Contact Support</Text>\r\n                <Text style={{ color: '#8a8a8a', fontSize: 12, fontFamily: 'Poppins_400Regular' }}>Get help and support</Text>\r\n              </View>\r\n              <Text style={{ color: '#8a8a8a', fontSize: 16 }}>›</Text>\r\n            </TouchableOpacity>\r\n\r\n            <TouchableOpacity\r\n              style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}\r\n              onPress={handleExportData}\r\n            >\r\n              <View>\r\n                <Text style={{ color: '#fff', fontSize: 16, fontFamily: 'Poppins_500Medium' }}>Export Data</Text>\r\n                <Text style={{ color: '#8a8a8a', fontSize: 12, fontFamily: 'Poppins_400Regular' }}>Download your data</Text>\r\n              </View>\r\n              <Text style={{ color: '#8a8a8a', fontSize: 16 }}>›</Text>\r\n            </TouchableOpacity>\r\n\r\n            <TouchableOpacity\r\n              style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}\r\n              onPress={handlePrivacyPolicy}\r\n            >\r\n              <View>\r\n                <Text style={{ color: '#fff', fontSize: 16, fontFamily: 'Poppins_500Medium' }}>Privacy Policy</Text>\r\n                <Text style={{ color: '#8a8a8a', fontSize: 12, fontFamily: 'Poppins_400Regular' }}>How we protect your data</Text>\r\n              </View>\r\n              <Text style={{ color: '#8a8a8a', fontSize: 16 }}>›</Text>\r\n            </TouchableOpacity>\r\n\r\n            <TouchableOpacity\r\n              style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}\r\n              onPress={handleTermsOfService}\r\n            >\r\n              <View>\r\n                <Text style={{ color: '#fff', fontSize: 16, fontFamily: 'Poppins_500Medium' }}>Terms of Service</Text>\r\n                <Text style={{ color: '#8a8a8a', fontSize: 12, fontFamily: 'Poppins_400Regular' }}>App usage terms</Text>\r\n              </View>\r\n              <Text style={{ color: '#8a8a8a', fontSize: 16 }}>›</Text>\r\n            </TouchableOpacity>\r\n          </Card>\r\n        </View>\r\n\r\n        {/* App Information */}\r\n        <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>\r\n          <Text style={{\r\n            color: '#fff',\r\n            fontSize: 18,\r\n            fontFamily: 'Poppins_600SemiBold',\r\n            marginBottom: 12\r\n          }}>\r\n            App Information ℹ️\r\n          </Text>\r\n\r\n          <Card>\r\n            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 12 }}>\r\n              <Text style={{ color: '#8a8a8a', fontSize: 14, fontFamily: 'Poppins_400Regular' }}>Version</Text>\r\n              <Text style={{ color: '#fff', fontSize: 14, fontFamily: 'Poppins_500Medium' }}>{appInfo.version}</Text>\r\n            </View>\r\n            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 12 }}>\r\n              <Text style={{ color: '#8a8a8a', fontSize: 14, fontFamily: 'Poppins_400Regular' }}>Build</Text>\r\n              <Text style={{ color: '#fff', fontSize: 14, fontFamily: 'Poppins_500Medium' }}>{appInfo.buildNumber}</Text>\r\n            </View>\r\n            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>\r\n              <Text style={{ color: '#8a8a8a', fontSize: 14, fontFamily: 'Poppins_400Regular' }}>Last Update</Text>\r\n              <Text style={{ color: '#fff', fontSize: 14, fontFamily: 'Poppins_500Medium' }}>{appInfo.lastUpdate}</Text>\r\n            </View>\r\n          </Card>\r\n        </View>\r\n\r\n        {/* Danger Zone */}\r\n        <View style={{ paddingHorizontal: 16, marginBottom: 20 }}>\r\n          <Text style={{\r\n            color: '#F44336',\r\n            fontSize: 18,\r\n            fontFamily: 'Poppins_600SemiBold',\r\n            marginBottom: 12\r\n          }}>\r\n            Danger Zone ⚠️\r\n          </Text>\r\n\r\n          <Card style={{ borderColor: '#F44336', borderWidth: 1 }}>\r\n            <Button\r\n              mode=\"outlined\"\r\n              onPress={handleCancelSubscription}\r\n              style={{\r\n                borderColor: '#FF9800',\r\n                marginBottom: 12\r\n              }}\r\n              labelStyle={{\r\n                color: '#FF9800',\r\n                fontFamily: 'Poppins_500Medium'\r\n              }}\r\n            >\r\n              Cancel Subscription\r\n            </Button>\r\n\r\n            <Button\r\n              mode=\"outlined\"\r\n              onPress={handleDeleteAccount}\r\n              style={{\r\n                borderColor: '#F44336'\r\n              }}\r\n              labelStyle={{\r\n                color: '#F44336',\r\n                fontFamily: 'Poppins_500Medium'\r\n              }}\r\n            >\r\n              Delete Account\r\n            </Button>\r\n          </Card>\r\n        </View>\r\n      </ScrollView>\r\n    </Wrapper>\r\n  );\r\n};\r\n\r\nexport default Settings;"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,gBAAA;AAAA,OAAAC,MAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,KAAA;AAExC,SAASC,MAAM,QAAQ,oBAAoB;AAC3C,OAAOC,OAAO;AACd,OAAOC,IAAI;AACX,OAAOC,QAAQ;AAAkC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAEjD,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAA,EAAS;EAErB,IAAAC,SAAA,GAAoChB,QAAQ,CAAC,IAAI,CAAC;IAAAiB,UAAA,GAAAC,cAAA,CAAAF,SAAA;IAA3CG,UAAU,GAAAF,UAAA;IAAEG,aAAa,GAAAH,UAAA;EAChC,IAAAI,UAAA,GAAgCrB,QAAQ,CAAC,SAAS,CAAC;IAAAsB,UAAA,GAAAJ,cAAA,CAAAG,UAAA;IAA5CE,QAAQ,GAAAD,UAAA;IAAEE,WAAW,GAAAF,UAAA;EAC5B,IAAAG,UAAA,GAAgCzB,QAAQ,CAAC,KAAK,CAAC;IAAA0B,UAAA,GAAAR,cAAA,CAAAO,UAAA;IAAxCE,QAAQ,GAAAD,UAAA;IAAEE,WAAW,GAAAF,UAAA;EAG5B,IAAAG,UAAA,GAAkD7B,QAAQ,CAAC,IAAI,CAAC;IAAA8B,UAAA,GAAAZ,cAAA,CAAAW,UAAA;IAAzDE,iBAAiB,GAAAD,UAAA;IAAEE,oBAAoB,GAAAF,UAAA;EAC9C,IAAAG,UAAA,GAAoDjC,QAAQ,CAAC,IAAI,CAAC;IAAAkC,UAAA,GAAAhB,cAAA,CAAAe,UAAA;IAA3DE,kBAAkB,GAAAD,UAAA;IAAEE,qBAAqB,GAAAF,UAAA;EAChD,IAAAG,UAAA,GAAwCrC,QAAQ,CAAC,IAAI,CAAC;IAAAsC,WAAA,GAAApB,cAAA,CAAAmB,UAAA;IAA/CE,YAAY,GAAAD,WAAA;IAAEE,eAAe,GAAAF,WAAA;EACpC,IAAAG,WAAA,GAAsCzC,QAAQ,CAAC,KAAK,CAAC;IAAA0C,WAAA,GAAAxB,cAAA,CAAAuB,WAAA;IAA9CE,WAAW,GAAAD,WAAA;IAAEE,cAAc,GAAAF,WAAA;EAGlC,IAAAG,WAAA,GAA0C7C,QAAQ,CAAC,KAAK,CAAC;IAAA8C,WAAA,GAAA5B,cAAA,CAAA2B,WAAA;IAAlDE,aAAa,GAAAD,WAAA;IAAEE,gBAAgB,GAAAF,WAAA;EACtC,IAAAG,WAAA,GAA0CjD,QAAQ,CAAC,IAAI,CAAC;IAAAkD,WAAA,GAAAhC,cAAA,CAAA+B,WAAA;IAAjDE,aAAa,GAAAD,WAAA;IAAEE,gBAAgB,GAAAF,WAAA;EACtC,IAAAG,WAAA,GAAgCrD,QAAQ,CAAC,IAAI,CAAC;IAAAsD,WAAA,GAAApC,cAAA,CAAAmC,WAAA;IAAvCE,QAAQ,GAAAD,WAAA;IAAEE,WAAW,GAAAF,WAAA;EAG5B,IAAAG,WAAA,GAAkCzD,QAAQ,CAAC,QAAQ,CAAC;IAAA0D,WAAA,GAAAxC,cAAA,CAAAuC,WAAA;IAA7CE,SAAS,GAAAD,WAAA;IAAEE,YAAY,GAAAF,WAAA;EAC9B,IAAAG,WAAA,GAAkC7D,QAAQ,CAAC,KAAK,CAAC;IAAA8D,WAAA,GAAA5C,cAAA,CAAA2C,WAAA;IAA1CE,SAAS,GAAAD,WAAA;IAAEE,YAAY,GAAAF,WAAA;EAC9B,IAAAG,WAAA,GAAwCjE,QAAQ,CAAC,IAAI,CAAC;IAAAkE,WAAA,GAAAhD,cAAA,CAAA+C,WAAA;IAA/CE,YAAY,GAAAD,WAAA;IAAEE,eAAe,GAAAF,WAAA;EAGpC,IAAMG,WAAW,GAAG;IAClBC,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE,yBAAyB;IAChCC,WAAW,EAAE,cAAc;IAC3BC,YAAY,EAAE,SAAS;IACvBC,MAAM,EAAE;EACV,CAAC;EAGD,IAAMC,OAAO,GAAG;IACdC,OAAO,EAAE,OAAO;IAChBC,WAAW,EAAE,YAAY;IACzBC,UAAU,EAAE;EACd,CAAC;EAGD,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAA;IAAA,OAAS3D,aAAa,CAAC,UAAA4D,IAAI;MAAA,OAAI,CAACA,IAAI;IAAA,EAAC;EAAA;EACtD,IAAMC,uBAAuB,GAAG,SAA1BA,uBAAuBA,CAAA;IAAA,OAASjD,oBAAoB,CAAC,UAAAgD,IAAI;MAAA,OAAI,CAACA,IAAI;IAAA,EAAC;EAAA;EACzE,IAAME,wBAAwB,GAAG,SAA3BA,wBAAwBA,CAAA;IAAA,OAAS9C,qBAAqB,CAAC,UAAA4C,IAAI;MAAA,OAAI,CAACA,IAAI;IAAA,EAAC;EAAA;EAC3E,IAAMG,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAA;IAAA,OAAS3C,eAAe,CAAC,UAAAwC,IAAI;MAAA,OAAI,CAACA,IAAI;IAAA,EAAC;EAAA;EAC/D,IAAMI,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAA;IAAA,OAASxC,cAAc,CAAC,UAAAoC,IAAI;MAAA,OAAI,CAACA,IAAI;IAAA,EAAC;EAAA;EAC7D,IAAMK,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAA;IAAA,OAASrC,gBAAgB,CAAC,UAAAgC,IAAI;MAAA,OAAI,CAACA,IAAI;IAAA,EAAC;EAAA;EACjE,IAAMM,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAA;IAAA,OAASlC,gBAAgB,CAAC,UAAA4B,IAAI;MAAA,OAAI,CAACA,IAAI;IAAA,EAAC;EAAA;EACjE,IAAMO,cAAc,GAAG,SAAjBA,cAAcA,CAAA;IAAA,OAAS/B,WAAW,CAAC,UAAAwB,IAAI;MAAA,OAAI,CAACA,IAAI;IAAA,EAAC;EAAA;EACvD,IAAMQ,eAAe,GAAG,SAAlBA,eAAeA,CAAA;IAAA,OAASxB,YAAY,CAAC,UAAAgB,IAAI;MAAA,OAAI,CAACA,IAAI;IAAA,EAAC;EAAA;EACzD,IAAMS,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAA;IAAA,OAASrB,eAAe,CAAC,UAAAY,IAAI;MAAA,OAAI,CAACA,IAAI;IAAA,EAAC;EAAA;EAG/D,IAAMU,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAA,EAAS;IACjCpF,KAAK,CAACqF,KAAK,CAAC,UAAU,EAAE,iCAAiC,CAAC;EAC5D,CAAC;EAED,IAAMC,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAA,EAAS;IACjCtF,KAAK,CAACqF,KAAK,CAAC,UAAU,EAAE,iCAAiC,CAAC;EAC5D,CAAC;EAED,IAAME,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAA,EAAS;IAClCvF,KAAK,CAACqF,KAAK,CAAC,YAAY,EAAE,uCAAuC,CAAC;EACpE,CAAC;EAED,IAAMG,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA,EAAS;IAC7BxF,KAAK,CAACqF,KAAK,CAAC,aAAa,EAAE,kCAAkC,CAAC;EAChE,CAAC;EAED,IAAMI,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAA,EAAS;IAChCzF,KAAK,CAACqF,KAAK,CACT,gBAAgB,EAChB,6EAA6E,EAC7E,CACE;MAAEK,IAAI,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAS,CAAC,EACnC;MAAED,IAAI,EAAE,QAAQ;MAAEC,KAAK,EAAE,aAAa;MAAEC,OAAO,EAAE,SAATA,OAAOA,CAAA;QAAA,OAAQ5F,KAAK,CAACqF,KAAK,CAAC,iBAAiB,EAAE,gDAAgD,CAAC;MAAA;IAAC,CAAC,CAE7I,CAAC;EACH,CAAC;EAED,IAAMQ,wBAAwB,GAAG,SAA3BA,wBAAwBA,CAAA,EAAS;IACrC7F,KAAK,CAACqF,KAAK,CAAC,qBAAqB,EAAE,sCAAsC,CAAC;EAC5E,CAAC;EAED,IAAMS,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAA,EAAS;IACjC9F,KAAK,CAACqF,KAAK,CAAC,SAAS,EAAE,sCAAsC,CAAC;EAChE,CAAC;EAED,IAAMU,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAA,EAAS;IAChC/F,KAAK,CAACqF,KAAK,CAAC,gBAAgB,EAAE,6BAA6B,CAAC;EAC9D,CAAC;EAED,IAAMW,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAA,EAAS;IACjChG,KAAK,CAACqF,KAAK,CAAC,kBAAkB,EAAE,+BAA+B,CAAC;EAClE,CAAC;EAED,OACE/E,IAAA,CAACJ,OAAO;IAAA+F,QAAA,EACNzF,KAAA,CAACT,UAAU;MAAC4F,KAAK,EAAE;QAAEO,IAAI,EAAE;MAAE,CAAE;MAAAD,QAAA,GAE7BzF,KAAA,CAACb,IAAI;QAACgG,KAAK,EAAE;UAAEQ,OAAO,EAAE,EAAE;UAAEC,aAAa,EAAE;QAAE,CAAE;QAAAH,QAAA,GAC7C3F,IAAA,CAACV,IAAI;UAAC+F,KAAK,EAAE;YACXU,KAAK,EAAE,MAAM;YACbC,QAAQ,EAAE,EAAE;YACZC,UAAU,EAAE,iBAAiB;YAC7BC,YAAY,EAAE;UAChB,CAAE;UAAAP,QAAA,EAAC;QAEH,CAAM,CAAC,EACP3F,IAAA,CAACV,IAAI;UAAC+F,KAAK,EAAE;YACXU,KAAK,EAAE,SAAS;YAChBC,QAAQ,EAAE,EAAE;YACZC,UAAU,EAAE;UACd,CAAE;UAAAN,QAAA,EAAC;QAEH,CAAM,CAAC;MAAA,CACH,CAAC,EAGP3F,IAAA,CAACX,IAAI;QAACgG,KAAK,EAAE;UAAEc,iBAAiB,EAAE,EAAE;UAAED,YAAY,EAAE;QAAG,CAAE;QAAAP,QAAA,EACvD3F,IAAA,CAACH,IAAI;UAAA8F,QAAA,EACHzF,KAAA,CAACb,IAAI;YAACgG,KAAK,EAAE;cAAEe,aAAa,EAAE,KAAK;cAAEC,UAAU,EAAE;YAAS,CAAE;YAAAV,QAAA,GAC1D3F,IAAA,CAACX,IAAI;cAACgG,KAAK,EAAE;gBACXiB,eAAe,EAAE,SAAS;gBAC1BC,YAAY,EAAE,EAAE;gBAChBC,KAAK,EAAE,EAAE;gBACTC,MAAM,EAAE,EAAE;gBACVJ,UAAU,EAAE,QAAQ;gBACpBK,cAAc,EAAE,QAAQ;gBACxBC,WAAW,EAAE;cACf,CAAE;cAAAhB,QAAA,EACA3F,IAAA,CAACV,IAAI;gBAAC+F,KAAK,EAAE;kBAAEW,QAAQ,EAAE;gBAAG,CAAE;gBAAAL,QAAA,EAAElC,WAAW,CAACK;cAAM,CAAO;YAAC,CACtD,CAAC,EACP5D,KAAA,CAACb,IAAI;cAACgG,KAAK,EAAE;gBAAEO,IAAI,EAAE;cAAE,CAAE;cAAAD,QAAA,GACvB3F,IAAA,CAACV,IAAI;gBAAC+F,KAAK,EAAE;kBACXU,KAAK,EAAE,MAAM;kBACbC,QAAQ,EAAE,EAAE;kBACZC,UAAU,EAAE,qBAAqB;kBACjCC,YAAY,EAAE;gBAChB,CAAE;gBAAAP,QAAA,EACClC,WAAW,CAACC;cAAI,CACb,CAAC,EACP1D,IAAA,CAACV,IAAI;gBAAC+F,KAAK,EAAE;kBACXU,KAAK,EAAE,SAAS;kBAChBC,QAAQ,EAAE,EAAE;kBACZC,UAAU,EAAE,oBAAoB;kBAChCC,YAAY,EAAE;gBAChB,CAAE;gBAAAP,QAAA,EACClC,WAAW,CAACE;cAAK,CACd,CAAC,EACPzD,KAAA,CAACb,IAAI;gBAACgG,KAAK,EAAE;kBAAEe,aAAa,EAAE,KAAK;kBAAEC,UAAU,EAAE;gBAAS,CAAE;gBAAAV,QAAA,GAC1D3F,IAAA,CAACX,IAAI;kBAACgG,KAAK,EAAE;oBACXiB,eAAe,EAAE,SAAS;oBAC1BH,iBAAiB,EAAE,CAAC;oBACpBS,eAAe,EAAE,CAAC;oBAClBL,YAAY,EAAE,CAAC;oBACfI,WAAW,EAAE;kBACf,CAAE;kBAAAhB,QAAA,EACA3F,IAAA,CAACV,IAAI;oBAAC+F,KAAK,EAAE;sBACXU,KAAK,EAAE,MAAM;sBACbC,QAAQ,EAAE,EAAE;sBACZC,UAAU,EAAE;oBACd,CAAE;oBAAAN,QAAA,EACClC,WAAW,CAACI;kBAAY,CACrB;gBAAC,CACH,CAAC,EACP3D,KAAA,CAACZ,IAAI;kBAAC+F,KAAK,EAAE;oBACXU,KAAK,EAAE,SAAS;oBAChBC,QAAQ,EAAE,EAAE;oBACZC,UAAU,EAAE;kBACd,CAAE;kBAAAN,QAAA,GAAC,eACY,EAAClC,WAAW,CAACG,WAAW;gBAAA,CACjC,CAAC;cAAA,CACH,CAAC;YAAA,CACH,CAAC,EACP5D,IAAA,CAACT,gBAAgB;cAAAoG,QAAA,EACf3F,IAAA,CAACV,IAAI;gBAAC+F,KAAK,EAAE;kBAAEU,KAAK,EAAE,SAAS;kBAAEC,QAAQ,EAAE,EAAE;kBAAEC,UAAU,EAAE;gBAAoB,CAAE;gBAAAN,QAAA,EAAC;cAElF,CAAM;YAAC,CACS,CAAC;UAAA,CACf;QAAC,CACH;MAAC,CACH,CAAC,EAGPzF,KAAA,CAACb,IAAI;QAACgG,KAAK,EAAE;UAAEc,iBAAiB,EAAE,CAAC;UAAED,YAAY,EAAE;QAAG,CAAE;QAAAP,QAAA,GACtD3F,IAAA,CAACV,IAAI;UAAC+F,KAAK,EAAE;YACXU,KAAK,EAAE,MAAM;YACbC,QAAQ,EAAE,EAAE;YACZC,UAAU,EAAE,qBAAqB;YACjCC,YAAY,EAAE,EAAE;YAChBC,iBAAiB,EAAE;UACrB,CAAE;UAAAR,QAAA,EAAC;QAEH,CAAM,CAAC,EACPzF,KAAA,CAACb,IAAI;UAACgG,KAAK,EAAE;YAAEe,aAAa,EAAE,KAAK;YAAES,QAAQ,EAAE;UAAO,CAAE;UAAAlB,QAAA,GACtD3F,IAAA,CAACF,QAAQ;YACPgH,KAAK,EAAC,sBAAsB;YAC5BC,KAAK,EAAC,GAAG;YACTC,QAAQ,EAAC,UAAU;YACnBC,IAAI,EAAC;UAAI,CACV,CAAC,EACFjH,IAAA,CAACF,QAAQ;YACPgH,KAAK,EAAC,eAAe;YACrBC,KAAK,EAAC,OAAO;YACbC,QAAQ,EAAC,UAAU;YACnBC,IAAI,EAAC;UAAI,CACV,CAAC,EACFjH,IAAA,CAACF,QAAQ;YACPgH,KAAK,EAAC,cAAc;YACpBC,KAAK,EAAC,OAAO;YACbG,MAAM,EAAC,OAAO;YACdC,UAAU,EAAC,UAAU;YACrBF,IAAI,EAAC;UAAI,CACV,CAAC,EACFjH,IAAA,CAACF,QAAQ;YACPgH,KAAK,EAAC,eAAe;YACrBC,KAAK,EAAC,SAAS;YACfC,QAAQ,EAAC,QAAQ;YACjBC,IAAI,EAAC;UAAI,CACV,CAAC;QAAA,CACE,CAAC;MAAA,CACH,CAAC,EAGP/G,KAAA,CAACb,IAAI;QAACgG,KAAK,EAAE;UAAEc,iBAAiB,EAAE,EAAE;UAAED,YAAY,EAAE;QAAG,CAAE;QAAAP,QAAA,GACvD3F,IAAA,CAACV,IAAI;UAAC+F,KAAK,EAAE;YACXU,KAAK,EAAE,MAAM;YACbC,QAAQ,EAAE,EAAE;YACZC,UAAU,EAAE,qBAAqB;YACjCC,YAAY,EAAE;UAChB,CAAE;UAAAP,QAAA,EAAC;QAEH,CAAM,CAAC,EAEPzF,KAAA,CAACL,IAAI;UAACwF,KAAK,EAAE;YAAEa,YAAY,EAAE;UAAG,CAAE;UAAAP,QAAA,GAChCzF,KAAA,CAACb,IAAI;YAACgG,KAAK,EAAE;cAAEe,aAAa,EAAE,KAAK;cAAEM,cAAc,EAAE,eAAe;cAAEL,UAAU,EAAE,QAAQ;cAAEH,YAAY,EAAE;YAAG,CAAE;YAAAP,QAAA,GAC7GzF,KAAA,CAACb,IAAI;cAAAsG,QAAA,GACH3F,IAAA,CAACV,IAAI;gBAAC+F,KAAK,EAAE;kBAAEU,KAAK,EAAE,MAAM;kBAAEC,QAAQ,EAAE,EAAE;kBAAEC,UAAU,EAAE;gBAAoB,CAAE;gBAAAN,QAAA,EAAC;cAAS,CAAM,CAAC,EAC/F3F,IAAA,CAACV,IAAI;gBAAC+F,KAAK,EAAE;kBAAEU,KAAK,EAAE,SAAS;kBAAEC,QAAQ,EAAE,EAAE;kBAAEC,UAAU,EAAE;gBAAqB,CAAE;gBAAAN,QAAA,EAAC;cAAc,CAAM,CAAC;YAAA,CACpG,CAAC,EACP3F,IAAA,CAACR,MAAM;cACLuH,KAAK,EAAExG,UAAW;cAClB6G,aAAa,EAAEjD,WAAY;cAC3BkD,UAAU,EAAE9G,UAAU,GAAG,SAAS,GAAG,MAAO;cAC5C+G,UAAU,EAAE;gBAAEC,KAAK,EAAE,MAAM;gBAAEC,IAAI,EAAE;cAAU;YAAE,CAChD,CAAC;UAAA,CACE,CAAC,EAEPtH,KAAA,CAACX,gBAAgB;YACf8F,KAAK,EAAE;cAAEe,aAAa,EAAE,KAAK;cAAEM,cAAc,EAAE,eAAe;cAAEL,UAAU,EAAE,QAAQ;cAAEH,YAAY,EAAE;YAAG,CAAE;YACzGZ,OAAO,EAAER,oBAAqB;YAAAa,QAAA,GAE9BzF,KAAA,CAACb,IAAI;cAAAsG,QAAA,GACH3F,IAAA,CAACV,IAAI;gBAAC+F,KAAK,EAAE;kBAAEU,KAAK,EAAE,MAAM;kBAAEC,QAAQ,EAAE,EAAE;kBAAEC,UAAU,EAAE;gBAAoB,CAAE;gBAAAN,QAAA,EAAC;cAAQ,CAAM,CAAC,EAC9F3F,IAAA,CAACV,IAAI;gBAAC+F,KAAK,EAAE;kBAAEU,KAAK,EAAE,SAAS;kBAAEC,QAAQ,EAAE,EAAE;kBAAEC,UAAU,EAAE;gBAAqB,CAAE;gBAAAN,QAAA,EAAC;cAAY,CAAM,CAAC;YAAA,CAClG,CAAC,EACPzF,KAAA,CAACb,IAAI;cAACgG,KAAK,EAAE;gBAAEe,aAAa,EAAE,KAAK;gBAAEC,UAAU,EAAE;cAAS,CAAE;cAAAV,QAAA,GAC1D3F,IAAA,CAACV,IAAI;gBAAC+F,KAAK,EAAE;kBAAEU,KAAK,EAAE,SAAS;kBAAEC,QAAQ,EAAE,EAAE;kBAAEC,UAAU,EAAE,mBAAmB;kBAAEU,WAAW,EAAE;gBAAE,CAAE;gBAAAhB,QAAA,EAC9FhF;cAAQ,CACL,CAAC,EACPX,IAAA,CAACV,IAAI;gBAAC+F,KAAK,EAAE;kBAAEU,KAAK,EAAE,SAAS;kBAAEC,QAAQ,EAAE;gBAAG,CAAE;gBAAAL,QAAA,EAAC;cAAC,CAAM,CAAC;YAAA,CACrD,CAAC;UAAA,CACS,CAAC,EAEnBzF,KAAA,CAACX,gBAAgB;YACf8F,KAAK,EAAE;cAAEe,aAAa,EAAE,KAAK;cAAEM,cAAc,EAAE,eAAe;cAAEL,UAAU,EAAE;YAAS,CAAE;YACvFf,OAAO,EAAEN,oBAAqB;YAAAW,QAAA,GAE9BzF,KAAA,CAACb,IAAI;cAAAsG,QAAA,GACH3F,IAAA,CAACV,IAAI;gBAAC+F,KAAK,EAAE;kBAAEU,KAAK,EAAE,MAAM;kBAAEC,QAAQ,EAAE,EAAE;kBAAEC,UAAU,EAAE;gBAAoB,CAAE;gBAAAN,QAAA,EAAC;cAAQ,CAAM,CAAC,EAC9F3F,IAAA,CAACV,IAAI;gBAAC+F,KAAK,EAAE;kBAAEU,KAAK,EAAE,SAAS;kBAAEC,QAAQ,EAAE,EAAE;kBAAEC,UAAU,EAAE;gBAAqB,CAAE;gBAAAN,QAAA,EAAC;cAAgB,CAAM,CAAC;YAAA,CACtG,CAAC,EACPzF,KAAA,CAACb,IAAI;cAACgG,KAAK,EAAE;gBAAEe,aAAa,EAAE,KAAK;gBAAEC,UAAU,EAAE;cAAS,CAAE;cAAAV,QAAA,GAC1D3F,IAAA,CAACV,IAAI;gBAAC+F,KAAK,EAAE;kBAAEU,KAAK,EAAE,SAAS;kBAAEC,QAAQ,EAAE,EAAE;kBAAEC,UAAU,EAAE,mBAAmB;kBAAEU,WAAW,EAAE;gBAAE,CAAE;gBAAAhB,QAAA,EAC9F5E;cAAQ,CACL,CAAC,EACPf,IAAA,CAACV,IAAI;gBAAC+F,KAAK,EAAE;kBAAEU,KAAK,EAAE,SAAS;kBAAEC,QAAQ,EAAE;gBAAG,CAAE;gBAAAL,QAAA,EAAC;cAAC,CAAM,CAAC;YAAA,CACrD,CAAC;UAAA,CACS,CAAC;QAAA,CACf,CAAC;MAAA,CACH,CAAC,EAGPzF,KAAA,CAACb,IAAI;QAACgG,KAAK,EAAE;UAAEc,iBAAiB,EAAE,EAAE;UAAED,YAAY,EAAE;QAAG,CAAE;QAAAP,QAAA,GACvD3F,IAAA,CAACV,IAAI;UAAC+F,KAAK,EAAE;YACXU,KAAK,EAAE,MAAM;YACbC,QAAQ,EAAE,EAAE;YACZC,UAAU,EAAE,qBAAqB;YACjCC,YAAY,EAAE;UAChB,CAAE;UAAAP,QAAA,EAAC;QAEH,CAAM,CAAC,EAEPzF,KAAA,CAACL,IAAI;UAAA8F,QAAA,GACHzF,KAAA,CAACb,IAAI;YAACgG,KAAK,EAAE;cAAEe,aAAa,EAAE,KAAK;cAAEM,cAAc,EAAE,eAAe;cAAEL,UAAU,EAAE,QAAQ;cAAEH,YAAY,EAAE;YAAG,CAAE;YAAAP,QAAA,GAC7GzF,KAAA,CAACb,IAAI;cAAAsG,QAAA,GACH3F,IAAA,CAACV,IAAI;gBAAC+F,KAAK,EAAE;kBAAEU,KAAK,EAAE,MAAM;kBAAEC,QAAQ,EAAE,EAAE;kBAAEC,UAAU,EAAE;gBAAoB,CAAE;gBAAAN,QAAA,EAAC;cAAkB,CAAM,CAAC,EACxG3F,IAAA,CAACV,IAAI;gBAAC+F,KAAK,EAAE;kBAAEU,KAAK,EAAE,SAAS;kBAAEC,QAAQ,EAAE,EAAE;kBAAEC,UAAU,EAAE;gBAAqB,CAAE;gBAAAN,QAAA,EAAC;cAA0B,CAAM,CAAC;YAAA,CAChH,CAAC,EACP3F,IAAA,CAACR,MAAM;cACLuH,KAAK,EAAE5F,iBAAkB;cACzBiG,aAAa,EAAE/C,uBAAwB;cACvCgD,UAAU,EAAElG,iBAAiB,GAAG,SAAS,GAAG,MAAO;cACnDmG,UAAU,EAAE;gBAAEC,KAAK,EAAE,MAAM;gBAAEC,IAAI,EAAE;cAAU;YAAE,CAChD,CAAC;UAAA,CACE,CAAC,EAEPtH,KAAA,CAACb,IAAI;YAACgG,KAAK,EAAE;cAAEe,aAAa,EAAE,KAAK;cAAEM,cAAc,EAAE,eAAe;cAAEL,UAAU,EAAE,QAAQ;cAAEH,YAAY,EAAE;YAAG,CAAE;YAAAP,QAAA,GAC7GzF,KAAA,CAACb,IAAI;cAAAsG,QAAA,GACH3F,IAAA,CAACV,IAAI;gBAAC+F,KAAK,EAAE;kBAAEU,KAAK,EAAE,MAAM;kBAAEC,QAAQ,EAAE,EAAE;kBAAEC,UAAU,EAAE;gBAAoB,CAAE;gBAAAN,QAAA,EAAC;cAAmB,CAAM,CAAC,EACzG3F,IAAA,CAACV,IAAI;gBAAC+F,KAAK,EAAE;kBAAEU,KAAK,EAAE,SAAS;kBAAEC,QAAQ,EAAE,EAAE;kBAAEC,UAAU,EAAE;gBAAqB,CAAE;gBAAAN,QAAA,EAAC;cAAqB,CAAM,CAAC;YAAA,CAC3G,CAAC,EACP3F,IAAA,CAACR,MAAM;cACLuH,KAAK,EAAExF,kBAAmB;cAC1B6F,aAAa,EAAE9C,wBAAyB;cACxC+C,UAAU,EAAE9F,kBAAkB,GAAG,SAAS,GAAG,MAAO;cACpD+F,UAAU,EAAE;gBAAEC,KAAK,EAAE,MAAM;gBAAEC,IAAI,EAAE;cAAU;YAAE,CAChD,CAAC;UAAA,CACE,CAAC,EAEPtH,KAAA,CAACb,IAAI;YAACgG,KAAK,EAAE;cAAEe,aAAa,EAAE,KAAK;cAAEM,cAAc,EAAE,eAAe;cAAEL,UAAU,EAAE,QAAQ;cAAEH,YAAY,EAAE;YAAG,CAAE;YAAAP,QAAA,GAC7GzF,KAAA,CAACb,IAAI;cAAAsG,QAAA,GACH3F,IAAA,CAACV,IAAI;gBAAC+F,KAAK,EAAE;kBAAEU,KAAK,EAAE,MAAM;kBAAEC,QAAQ,EAAE,EAAE;kBAAEC,UAAU,EAAE;gBAAoB,CAAE;gBAAAN,QAAA,EAAC;cAAa,CAAM,CAAC,EACnG3F,IAAA,CAACV,IAAI;gBAAC+F,KAAK,EAAE;kBAAEU,KAAK,EAAE,SAAS;kBAAEC,QAAQ,EAAE,EAAE;kBAAEC,UAAU,EAAE;gBAAqB,CAAE;gBAAAN,QAAA,EAAC;cAAmB,CAAM,CAAC;YAAA,CACzG,CAAC,EACP3F,IAAA,CAACR,MAAM;cACLuH,KAAK,EAAEpF,YAAa;cACpByF,aAAa,EAAE7C,kBAAmB;cAClC8C,UAAU,EAAE1F,YAAY,GAAG,SAAS,GAAG,MAAO;cAC9C2F,UAAU,EAAE;gBAAEC,KAAK,EAAE,MAAM;gBAAEC,IAAI,EAAE;cAAU;YAAE,CAChD,CAAC;UAAA,CACE,CAAC,EAEPtH,KAAA,CAACb,IAAI;YAACgG,KAAK,EAAE;cAAEe,aAAa,EAAE,KAAK;cAAEM,cAAc,EAAE,eAAe;cAAEL,UAAU,EAAE;YAAS,CAAE;YAAAV,QAAA,GAC3FzF,KAAA,CAACb,IAAI;cAAAsG,QAAA,GACH3F,IAAA,CAACV,IAAI;gBAAC+F,KAAK,EAAE;kBAAEU,KAAK,EAAE,MAAM;kBAAEC,QAAQ,EAAE,EAAE;kBAAEC,UAAU,EAAE;gBAAoB,CAAE;gBAAAN,QAAA,EAAC;cAAY,CAAM,CAAC,EAClG3F,IAAA,CAACV,IAAI;gBAAC+F,KAAK,EAAE;kBAAEU,KAAK,EAAE,SAAS;kBAAEC,QAAQ,EAAE,EAAE;kBAAEC,UAAU,EAAE;gBAAqB,CAAE;gBAAAN,QAAA,EAAC;cAAqB,CAAM,CAAC;YAAA,CAC3G,CAAC,EACP3F,IAAA,CAACR,MAAM;cACLuH,KAAK,EAAEhF,WAAY;cACnBqF,aAAa,EAAE5C,iBAAkB;cACjC6C,UAAU,EAAEtF,WAAW,GAAG,SAAS,GAAG,MAAO;cAC7CuF,UAAU,EAAE;gBAAEC,KAAK,EAAE,MAAM;gBAAEC,IAAI,EAAE;cAAU;YAAE,CAChD,CAAC;UAAA,CACE,CAAC;QAAA,CACH,CAAC;MAAA,CACH,CAAC,EAGPtH,KAAA,CAACb,IAAI;QAACgG,KAAK,EAAE;UAAEc,iBAAiB,EAAE,EAAE;UAAED,YAAY,EAAE;QAAG,CAAE;QAAAP,QAAA,GACvD3F,IAAA,CAACV,IAAI;UAAC+F,KAAK,EAAE;YACXU,KAAK,EAAE,MAAM;YACbC,QAAQ,EAAE,EAAE;YACZC,UAAU,EAAE,qBAAqB;YACjCC,YAAY,EAAE;UAChB,CAAE;UAAAP,QAAA,EAAC;QAEH,CAAM,CAAC,EAEPzF,KAAA,CAACL,IAAI;UAAA8F,QAAA,GACHzF,KAAA,CAACb,IAAI;YAACgG,KAAK,EAAE;cAAEe,aAAa,EAAE,KAAK;cAAEM,cAAc,EAAE,eAAe;cAAEL,UAAU,EAAE,QAAQ;cAAEH,YAAY,EAAE;YAAG,CAAE;YAAAP,QAAA,GAC7GzF,KAAA,CAACb,IAAI;cAAAsG,QAAA,GACH3F,IAAA,CAACV,IAAI;gBAAC+F,KAAK,EAAE;kBAAEU,KAAK,EAAE,MAAM;kBAAEC,QAAQ,EAAE,EAAE;kBAAEC,UAAU,EAAE;gBAAoB,CAAE;gBAAAN,QAAA,EAAC;cAAyB,CAAM,CAAC,EAC/G3F,IAAA,CAACV,IAAI;gBAAC+F,KAAK,EAAE;kBAAEU,KAAK,EAAE,SAAS;kBAAEC,QAAQ,EAAE,EAAE;kBAAEC,UAAU,EAAE;gBAAqB,CAAE;gBAAAN,QAAA,EAAC;cAAoB,CAAM,CAAC;YAAA,CAC1G,CAAC,EACP3F,IAAA,CAACR,MAAM;cACLuH,KAAK,EAAE5E,aAAc;cACrBiF,aAAa,EAAE3C,mBAAoB;cACnC4C,UAAU,EAAElF,aAAa,GAAG,SAAS,GAAG,MAAO;cAC/CmF,UAAU,EAAE;gBAAEC,KAAK,EAAE,MAAM;gBAAEC,IAAI,EAAE;cAAU;YAAE,CAChD,CAAC;UAAA,CACE,CAAC,EAEPtH,KAAA,CAACb,IAAI;YAACgG,KAAK,EAAE;cAAEe,aAAa,EAAE,KAAK;cAAEM,cAAc,EAAE,eAAe;cAAEL,UAAU,EAAE,QAAQ;cAAEH,YAAY,EAAE;YAAG,CAAE;YAAAP,QAAA,GAC7GzF,KAAA,CAACb,IAAI;cAAAsG,QAAA,GACH3F,IAAA,CAACV,IAAI;gBAAC+F,KAAK,EAAE;kBAAEU,KAAK,EAAE,MAAM;kBAAEC,QAAQ,EAAE,EAAE;kBAAEC,UAAU,EAAE;gBAAoB,CAAE;gBAAAN,QAAA,EAAC;cAAwB,CAAM,CAAC,EAC9G3F,IAAA,CAACV,IAAI;gBAAC+F,KAAK,EAAE;kBAAEU,KAAK,EAAE,SAAS;kBAAEC,QAAQ,EAAE,EAAE;kBAAEC,UAAU,EAAE;gBAAqB,CAAE;gBAAAN,QAAA,EAAC;cAAmB,CAAM,CAAC;YAAA,CACzG,CAAC,EACP3F,IAAA,CAACR,MAAM;cACLuH,KAAK,EAAExE,aAAc;cACrB6E,aAAa,EAAE1C,mBAAoB;cACnC2C,UAAU,EAAE9E,aAAa,GAAG,SAAS,GAAG,MAAO;cAC/C+E,UAAU,EAAE;gBAAEC,KAAK,EAAE,MAAM;gBAAEC,IAAI,EAAE;cAAU;YAAE,CAChD,CAAC;UAAA,CACE,CAAC,EAEPtH,KAAA,CAACb,IAAI;YAACgG,KAAK,EAAE;cAAEe,aAAa,EAAE,KAAK;cAAEM,cAAc,EAAE,eAAe;cAAEL,UAAU,EAAE;YAAS,CAAE;YAAAV,QAAA,GAC3FzF,KAAA,CAACb,IAAI;cAAAsG,QAAA,GACH3F,IAAA,CAACV,IAAI;gBAAC+F,KAAK,EAAE;kBAAEU,KAAK,EAAE,MAAM;kBAAEC,QAAQ,EAAE,EAAE;kBAAEC,UAAU,EAAE;gBAAoB,CAAE;gBAAAN,QAAA,EAAC;cAAS,CAAM,CAAC,EAC/F3F,IAAA,CAACV,IAAI;gBAAC+F,KAAK,EAAE;kBAAEU,KAAK,EAAE,SAAS;kBAAEC,QAAQ,EAAE,EAAE;kBAAEC,UAAU,EAAE;gBAAqB,CAAE;gBAAAN,QAAA,EAAC;cAAsB,CAAM,CAAC;YAAA,CAC5G,CAAC,EACP3F,IAAA,CAACR,MAAM;cACLuH,KAAK,EAAEpE,QAAS;cAChByE,aAAa,EAAEzC,cAAe;cAC9B0C,UAAU,EAAE1E,QAAQ,GAAG,SAAS,GAAG,MAAO;cAC1C2E,UAAU,EAAE;gBAAEC,KAAK,EAAE,MAAM;gBAAEC,IAAI,EAAE;cAAU;YAAE,CAChD,CAAC;UAAA,CACE,CAAC;QAAA,CACH,CAAC;MAAA,CACH,CAAC,EAGPtH,KAAA,CAACb,IAAI;QAACgG,KAAK,EAAE;UAAEc,iBAAiB,EAAE,EAAE;UAAED,YAAY,EAAE;QAAG,CAAE;QAAAP,QAAA,GACvD3F,IAAA,CAACV,IAAI;UAAC+F,KAAK,EAAE;YACXU,KAAK,EAAE,MAAM;YACbC,QAAQ,EAAE,EAAE;YACZC,UAAU,EAAE,qBAAqB;YACjCC,YAAY,EAAE;UAChB,CAAE;UAAAP,QAAA,EAAC;QAEH,CAAM,CAAC,EAEPzF,KAAA,CAACL,IAAI;UAAA8F,QAAA,GACHzF,KAAA,CAACX,gBAAgB;YACf8F,KAAK,EAAE;cAAEe,aAAa,EAAE,KAAK;cAAEM,cAAc,EAAE,eAAe;cAAEL,UAAU,EAAE,QAAQ;cAAEH,YAAY,EAAE;YAAG,CAAE;YACzGZ,OAAO,EAAEL,qBAAsB;YAAAU,QAAA,GAE/BzF,KAAA,CAACb,IAAI;cAAAsG,QAAA,GACH3F,IAAA,CAACV,IAAI;gBAAC+F,KAAK,EAAE;kBAAEU,KAAK,EAAE,MAAM;kBAAEC,QAAQ,EAAE,EAAE;kBAAEC,UAAU,EAAE;gBAAoB,CAAE;gBAAAN,QAAA,EAAC;cAAU,CAAM,CAAC,EAChG3F,IAAA,CAACV,IAAI;gBAAC+F,KAAK,EAAE;kBAAEU,KAAK,EAAE,SAAS;kBAAEC,QAAQ,EAAE,EAAE;kBAAEC,UAAU,EAAE;gBAAqB,CAAE;gBAAAN,QAAA,EAAC;cAAsB,CAAM,CAAC;YAAA,CAC5G,CAAC,EACPzF,KAAA,CAACb,IAAI;cAACgG,KAAK,EAAE;gBAAEe,aAAa,EAAE,KAAK;gBAAEC,UAAU,EAAE;cAAS,CAAE;cAAAV,QAAA,GAC1D3F,IAAA,CAACV,IAAI;gBAAC+F,KAAK,EAAE;kBAAEU,KAAK,EAAE,SAAS;kBAAEC,QAAQ,EAAE,EAAE;kBAAEC,UAAU,EAAE,mBAAmB;kBAAEU,WAAW,EAAE;gBAAE,CAAE;gBAAAhB,QAAA,EAC9F5C;cAAS,CACN,CAAC,EACP/C,IAAA,CAACV,IAAI;gBAAC+F,KAAK,EAAE;kBAAEU,KAAK,EAAE,SAAS;kBAAEC,QAAQ,EAAE;gBAAG,CAAE;gBAAAL,QAAA,EAAC;cAAC,CAAM,CAAC;YAAA,CACrD,CAAC;UAAA,CACS,CAAC,EAEnBzF,KAAA,CAACb,IAAI;YAACgG,KAAK,EAAE;cAAEe,aAAa,EAAE,KAAK;cAAEM,cAAc,EAAE,eAAe;cAAEL,UAAU,EAAE,QAAQ;cAAEH,YAAY,EAAE;YAAG,CAAE;YAAAP,QAAA,GAC7GzF,KAAA,CAACb,IAAI;cAAAsG,QAAA,GACH3F,IAAA,CAACV,IAAI;gBAAC+F,KAAK,EAAE;kBAAEU,KAAK,EAAE,MAAM;kBAAEC,QAAQ,EAAE,EAAE;kBAAEC,UAAU,EAAE;gBAAoB,CAAE;gBAAAN,QAAA,EAAC;cAAY,CAAM,CAAC,EAClG3F,IAAA,CAACV,IAAI;gBAAC+F,KAAK,EAAE;kBAAEU,KAAK,EAAE,SAAS;kBAAEC,QAAQ,EAAE,EAAE;kBAAEC,UAAU,EAAE;gBAAqB,CAAE;gBAAAN,QAAA,EAAC;cAA0B,CAAM,CAAC;YAAA,CAChH,CAAC,EACP3F,IAAA,CAACR,MAAM;cACLuH,KAAK,EAAE5D,SAAU;cACjBiE,aAAa,EAAExC,eAAgB;cAC/ByC,UAAU,EAAElE,SAAS,GAAG,SAAS,GAAG,MAAO;cAC3CmE,UAAU,EAAE;gBAAEC,KAAK,EAAE,MAAM;gBAAEC,IAAI,EAAE;cAAU;YAAE,CAChD,CAAC;UAAA,CACE,CAAC,EAEPtH,KAAA,CAACb,IAAI;YAACgG,KAAK,EAAE;cAAEe,aAAa,EAAE,KAAK;cAAEM,cAAc,EAAE,eAAe;cAAEL,UAAU,EAAE;YAAS,CAAE;YAAAV,QAAA,GAC3FzF,KAAA,CAACb,IAAI;cAAAsG,QAAA,GACH3F,IAAA,CAACV,IAAI;gBAAC+F,KAAK,EAAE;kBAAEU,KAAK,EAAE,MAAM;kBAAEC,QAAQ,EAAE,EAAE;kBAAEC,UAAU,EAAE;gBAAoB,CAAE;gBAAAN,QAAA,EAAC;cAAa,CAAM,CAAC,EACnG3F,IAAA,CAACV,IAAI;gBAAC+F,KAAK,EAAE;kBAAEU,KAAK,EAAE,SAAS;kBAAEC,QAAQ,EAAE,EAAE;kBAAEC,UAAU,EAAE;gBAAqB,CAAE;gBAAAN,QAAA,EAAC;cAAa,CAAM,CAAC;YAAA,CACnG,CAAC,EACP3F,IAAA,CAACR,MAAM;cACLuH,KAAK,EAAExD,YAAa;cACpB6D,aAAa,EAAEvC,kBAAmB;cAClCwC,UAAU,EAAE9D,YAAY,GAAG,SAAS,GAAG,MAAO;cAC9C+D,UAAU,EAAE;gBAAEC,KAAK,EAAE,MAAM;gBAAEC,IAAI,EAAE;cAAU;YAAE,CAChD,CAAC;UAAA,CACE,CAAC;QAAA,CACH,CAAC;MAAA,CACH,CAAC,EAGPtH,KAAA,CAACb,IAAI;QAACgG,KAAK,EAAE;UAAEc,iBAAiB,EAAE,EAAE;UAAED,YAAY,EAAE;QAAG,CAAE;QAAAP,QAAA,GACvD3F,IAAA,CAACV,IAAI;UAAC+F,KAAK,EAAE;YACXU,KAAK,EAAE,MAAM;YACbC,QAAQ,EAAE,EAAE;YACZC,UAAU,EAAE,qBAAqB;YACjCC,YAAY,EAAE;UAChB,CAAE;UAAAP,QAAA,EAAC;QAEH,CAAM,CAAC,EAEPzF,KAAA,CAACL,IAAI;UAAA8F,QAAA,GACHzF,KAAA,CAACX,gBAAgB;YACf8F,KAAK,EAAE;cAAEe,aAAa,EAAE,KAAK;cAAEM,cAAc,EAAE,eAAe;cAAEL,UAAU,EAAE,QAAQ;cAAEH,YAAY,EAAE;YAAG,CAAE;YACzGZ,OAAO,EAAEE,oBAAqB;YAAAG,QAAA,GAE9BzF,KAAA,CAACb,IAAI;cAAAsG,QAAA,GACH3F,IAAA,CAACV,IAAI;gBAAC+F,KAAK,EAAE;kBAAEU,KAAK,EAAE,MAAM;kBAAEC,QAAQ,EAAE,EAAE;kBAAEC,UAAU,EAAE;gBAAoB,CAAE;gBAAAN,QAAA,EAAC;cAAe,CAAM,CAAC,EACrG3F,IAAA,CAACV,IAAI;gBAAC+F,KAAK,EAAE;kBAAEU,KAAK,EAAE,SAAS;kBAAEC,QAAQ,EAAE,EAAE;kBAAEC,UAAU,EAAE;gBAAqB,CAAE;gBAAAN,QAAA,EAAC;cAAoB,CAAM,CAAC;YAAA,CAC1G,CAAC,EACP3F,IAAA,CAACV,IAAI;cAAC+F,KAAK,EAAE;gBAAEU,KAAK,EAAE,SAAS;gBAAEC,QAAQ,EAAE;cAAG,CAAE;cAAAL,QAAA,EAAC;YAAC,CAAM,CAAC;UAAA,CACzC,CAAC,EAEnBzF,KAAA,CAACX,gBAAgB;YACf8F,KAAK,EAAE;cAAEe,aAAa,EAAE,KAAK;cAAEM,cAAc,EAAE,eAAe;cAAEL,UAAU,EAAE,QAAQ;cAAEH,YAAY,EAAE;YAAG,CAAE;YACzGZ,OAAO,EAAEJ,gBAAiB;YAAAS,QAAA,GAE1BzF,KAAA,CAACb,IAAI;cAAAsG,QAAA,GACH3F,IAAA,CAACV,IAAI;gBAAC+F,KAAK,EAAE;kBAAEU,KAAK,EAAE,MAAM;kBAAEC,QAAQ,EAAE,EAAE;kBAAEC,UAAU,EAAE;gBAAoB,CAAE;gBAAAN,QAAA,EAAC;cAAW,CAAM,CAAC,EACjG3F,IAAA,CAACV,IAAI;gBAAC+F,KAAK,EAAE;kBAAEU,KAAK,EAAE,SAAS;kBAAEC,QAAQ,EAAE,EAAE;kBAAEC,UAAU,EAAE;gBAAqB,CAAE;gBAAAN,QAAA,EAAC;cAAkB,CAAM,CAAC;YAAA,CACxG,CAAC,EACP3F,IAAA,CAACV,IAAI;cAAC+F,KAAK,EAAE;gBAAEU,KAAK,EAAE,SAAS;gBAAEC,QAAQ,EAAE;cAAG,CAAE;cAAAL,QAAA,EAAC;YAAC,CAAM,CAAC;UAAA,CACzC,CAAC,EAEnBzF,KAAA,CAACX,gBAAgB;YACf8F,KAAK,EAAE;cAAEe,aAAa,EAAE,KAAK;cAAEM,cAAc,EAAE,eAAe;cAAEL,UAAU,EAAE,QAAQ;cAAEH,YAAY,EAAE;YAAG,CAAE;YACzGZ,OAAO,EAAEG,mBAAoB;YAAAE,QAAA,GAE7BzF,KAAA,CAACb,IAAI;cAAAsG,QAAA,GACH3F,IAAA,CAACV,IAAI;gBAAC+F,KAAK,EAAE;kBAAEU,KAAK,EAAE,MAAM;kBAAEC,QAAQ,EAAE,EAAE;kBAAEC,UAAU,EAAE;gBAAoB,CAAE;gBAAAN,QAAA,EAAC;cAAc,CAAM,CAAC,EACpG3F,IAAA,CAACV,IAAI;gBAAC+F,KAAK,EAAE;kBAAEU,KAAK,EAAE,SAAS;kBAAEC,QAAQ,EAAE,EAAE;kBAAEC,UAAU,EAAE;gBAAqB,CAAE;gBAAAN,QAAA,EAAC;cAAwB,CAAM,CAAC;YAAA,CAC9G,CAAC,EACP3F,IAAA,CAACV,IAAI;cAAC+F,KAAK,EAAE;gBAAEU,KAAK,EAAE,SAAS;gBAAEC,QAAQ,EAAE;cAAG,CAAE;cAAAL,QAAA,EAAC;YAAC,CAAM,CAAC;UAAA,CACzC,CAAC,EAEnBzF,KAAA,CAACX,gBAAgB;YACf8F,KAAK,EAAE;cAAEe,aAAa,EAAE,KAAK;cAAEM,cAAc,EAAE,eAAe;cAAEL,UAAU,EAAE;YAAS,CAAE;YACvFf,OAAO,EAAEI,oBAAqB;YAAAC,QAAA,GAE9BzF,KAAA,CAACb,IAAI;cAAAsG,QAAA,GACH3F,IAAA,CAACV,IAAI;gBAAC+F,KAAK,EAAE;kBAAEU,KAAK,EAAE,MAAM;kBAAEC,QAAQ,EAAE,EAAE;kBAAEC,UAAU,EAAE;gBAAoB,CAAE;gBAAAN,QAAA,EAAC;cAAgB,CAAM,CAAC,EACtG3F,IAAA,CAACV,IAAI;gBAAC+F,KAAK,EAAE;kBAAEU,KAAK,EAAE,SAAS;kBAAEC,QAAQ,EAAE,EAAE;kBAAEC,UAAU,EAAE;gBAAqB,CAAE;gBAAAN,QAAA,EAAC;cAAe,CAAM,CAAC;YAAA,CACrG,CAAC,EACP3F,IAAA,CAACV,IAAI;cAAC+F,KAAK,EAAE;gBAAEU,KAAK,EAAE,SAAS;gBAAEC,QAAQ,EAAE;cAAG,CAAE;cAAAL,QAAA,EAAC;YAAC,CAAM,CAAC;UAAA,CACzC,CAAC;QAAA,CACf,CAAC;MAAA,CACH,CAAC,EAGPzF,KAAA,CAACb,IAAI;QAACgG,KAAK,EAAE;UAAEc,iBAAiB,EAAE,EAAE;UAAED,YAAY,EAAE;QAAG,CAAE;QAAAP,QAAA,GACvD3F,IAAA,CAACV,IAAI;UAAC+F,KAAK,EAAE;YACXU,KAAK,EAAE,MAAM;YACbC,QAAQ,EAAE,EAAE;YACZC,UAAU,EAAE,qBAAqB;YACjCC,YAAY,EAAE;UAChB,CAAE;UAAAP,QAAA,EAAC;QAEH,CAAM,CAAC,EAEPzF,KAAA,CAACL,IAAI;UAAA8F,QAAA,GACHzF,KAAA,CAACb,IAAI;YAACgG,KAAK,EAAE;cAAEe,aAAa,EAAE,KAAK;cAAEM,cAAc,EAAE,eAAe;cAAEL,UAAU,EAAE,QAAQ;cAAEH,YAAY,EAAE;YAAG,CAAE;YAAAP,QAAA,GAC7G3F,IAAA,CAACV,IAAI;cAAC+F,KAAK,EAAE;gBAAEU,KAAK,EAAE,SAAS;gBAAEC,QAAQ,EAAE,EAAE;gBAAEC,UAAU,EAAE;cAAqB,CAAE;cAAAN,QAAA,EAAC;YAAO,CAAM,CAAC,EACjG3F,IAAA,CAACV,IAAI;cAAC+F,KAAK,EAAE;gBAAEU,KAAK,EAAE,MAAM;gBAAEC,QAAQ,EAAE,EAAE;gBAAEC,UAAU,EAAE;cAAoB,CAAE;cAAAN,QAAA,EAAE5B,OAAO,CAACC;YAAO,CAAO,CAAC;UAAA,CACnG,CAAC,EACP9D,KAAA,CAACb,IAAI;YAACgG,KAAK,EAAE;cAAEe,aAAa,EAAE,KAAK;cAAEM,cAAc,EAAE,eAAe;cAAEL,UAAU,EAAE,QAAQ;cAAEH,YAAY,EAAE;YAAG,CAAE;YAAAP,QAAA,GAC7G3F,IAAA,CAACV,IAAI;cAAC+F,KAAK,EAAE;gBAAEU,KAAK,EAAE,SAAS;gBAAEC,QAAQ,EAAE,EAAE;gBAAEC,UAAU,EAAE;cAAqB,CAAE;cAAAN,QAAA,EAAC;YAAK,CAAM,CAAC,EAC/F3F,IAAA,CAACV,IAAI;cAAC+F,KAAK,EAAE;gBAAEU,KAAK,EAAE,MAAM;gBAAEC,QAAQ,EAAE,EAAE;gBAAEC,UAAU,EAAE;cAAoB,CAAE;cAAAN,QAAA,EAAE5B,OAAO,CAACE;YAAW,CAAO,CAAC;UAAA,CACvG,CAAC,EACP/D,KAAA,CAACb,IAAI;YAACgG,KAAK,EAAE;cAAEe,aAAa,EAAE,KAAK;cAAEM,cAAc,EAAE,eAAe;cAAEL,UAAU,EAAE;YAAS,CAAE;YAAAV,QAAA,GAC3F3F,IAAA,CAACV,IAAI;cAAC+F,KAAK,EAAE;gBAAEU,KAAK,EAAE,SAAS;gBAAEC,QAAQ,EAAE,EAAE;gBAAEC,UAAU,EAAE;cAAqB,CAAE;cAAAN,QAAA,EAAC;YAAW,CAAM,CAAC,EACrG3F,IAAA,CAACV,IAAI;cAAC+F,KAAK,EAAE;gBAAEU,KAAK,EAAE,MAAM;gBAAEC,QAAQ,EAAE,EAAE;gBAAEC,UAAU,EAAE;cAAoB,CAAE;cAAAN,QAAA,EAAE5B,OAAO,CAACG;YAAU,CAAO,CAAC;UAAA,CACtG,CAAC;QAAA,CACH,CAAC;MAAA,CACH,CAAC,EAGPhE,KAAA,CAACb,IAAI;QAACgG,KAAK,EAAE;UAAEc,iBAAiB,EAAE,EAAE;UAAED,YAAY,EAAE;QAAG,CAAE;QAAAP,QAAA,GACvD3F,IAAA,CAACV,IAAI;UAAC+F,KAAK,EAAE;YACXU,KAAK,EAAE,SAAS;YAChBC,QAAQ,EAAE,EAAE;YACZC,UAAU,EAAE,qBAAqB;YACjCC,YAAY,EAAE;UAChB,CAAE;UAAAP,QAAA,EAAC;QAEH,CAAM,CAAC,EAEPzF,KAAA,CAACL,IAAI;UAACwF,KAAK,EAAE;YAAEoC,WAAW,EAAE,SAAS;YAAEC,WAAW,EAAE;UAAE,CAAE;UAAA/B,QAAA,GACtD3F,IAAA,CAACL,MAAM;YACLgI,IAAI,EAAC,UAAU;YACfrC,OAAO,EAAEC,wBAAyB;YAClCF,KAAK,EAAE;cACLoC,WAAW,EAAE,SAAS;cACtBvB,YAAY,EAAE;YAChB,CAAE;YACF0B,UAAU,EAAE;cACV7B,KAAK,EAAE,SAAS;cAChBE,UAAU,EAAE;YACd,CAAE;YAAAN,QAAA,EACH;UAED,CAAQ,CAAC,EAET3F,IAAA,CAACL,MAAM;YACLgI,IAAI,EAAC,UAAU;YACfrC,OAAO,EAAEH,mBAAoB;YAC7BE,KAAK,EAAE;cACLoC,WAAW,EAAE;YACf,CAAE;YACFG,UAAU,EAAE;cACV7B,KAAK,EAAE,SAAS;cAChBE,UAAU,EAAE;YACd,CAAE;YAAAN,QAAA,EACH;UAED,CAAQ,CAAC;QAAA,CACL,CAAC;MAAA,CACH,CAAC;IAAA,CACG;EAAC,CACN,CAAC;AAEd,CAAC;AAED,eAAexF,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}