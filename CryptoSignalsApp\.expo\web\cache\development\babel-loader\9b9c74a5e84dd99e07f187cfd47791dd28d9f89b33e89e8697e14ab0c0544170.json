{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"color\", \"indeterminate\", \"progress\", \"visible\", \"theme\", \"animatedValue\", \"style\", \"fillStyle\", \"testID\"];\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nimport * as React from 'react';\nimport Animated from \"react-native-web/dist/exports/Animated\";\nimport I18nManager from \"react-native-web/dist/exports/I18nManager\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport setColor from 'color';\nimport { useInternalTheme } from \"../core/theming\";\nvar INDETERMINATE_DURATION = 2000;\nvar INDETERMINATE_MAX_WIDTH = 0.6;\nvar isRTL = I18nManager.isRTL;\nvar ProgressBar = function ProgressBar(_ref) {\n  var color = _ref.color,\n    indeterminate = _ref.indeterminate,\n    _ref$progress = _ref.progress,\n    progress = _ref$progress === void 0 ? 0 : _ref$progress,\n    _ref$visible = _ref.visible,\n    visible = _ref$visible === void 0 ? true : _ref$visible,\n    themeOverrides = _ref.theme,\n    animatedValue = _ref.animatedValue,\n    style = _ref.style,\n    fillStyle = _ref.fillStyle,\n    _ref$testID = _ref.testID,\n    testID = _ref$testID === void 0 ? 'progress-bar' : _ref$testID,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var _theme$colors;\n  var isWeb = Platform.OS === 'web';\n  var theme = useInternalTheme(themeOverrides);\n  var _React$useRef = React.useRef(new Animated.Value(0)),\n    timer = _React$useRef.current;\n  var _React$useRef2 = React.useRef(new Animated.Value(0)),\n    fade = _React$useRef2.current;\n  var passedAnimatedValue = React.useRef(animatedValue);\n  var _React$useState = React.useState(0),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    width = _React$useState2[0],\n    setWidth = _React$useState2[1];\n  var _React$useState3 = React.useState(0),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    prevWidth = _React$useState4[0],\n    setPrevWidth = _React$useState4[1];\n  var indeterminateAnimation = React.useRef(null);\n  var scale = theme.animation.scale;\n  React.useEffect(function () {\n    passedAnimatedValue.current = animatedValue;\n  });\n  var startAnimation = React.useCallback(function () {\n    Animated.timing(fade, {\n      duration: 200 * scale,\n      toValue: 1,\n      useNativeDriver: true,\n      isInteraction: false\n    }).start();\n    var externalAnimation = typeof passedAnimatedValue.current !== 'undefined' && passedAnimatedValue.current >= 0;\n    if (externalAnimation) {\n      return;\n    }\n    if (indeterminate) {\n      if (!indeterminateAnimation.current) {\n        indeterminateAnimation.current = Animated.timing(timer, {\n          duration: INDETERMINATE_DURATION,\n          toValue: 1,\n          useNativeDriver: !isWeb,\n          isInteraction: false\n        });\n      }\n      timer.setValue(0);\n      Animated.loop(indeterminateAnimation.current).start();\n    } else {\n      Animated.timing(timer, {\n        duration: 200 * scale,\n        toValue: progress ? progress : 0,\n        useNativeDriver: true,\n        isInteraction: false\n      }).start();\n    }\n  }, [fade, scale, indeterminate, timer, progress, isWeb]);\n  var stopAnimation = React.useCallback(function () {\n    if (indeterminateAnimation.current) {\n      indeterminateAnimation.current.stop();\n    }\n    Animated.timing(fade, {\n      duration: 200 * scale,\n      toValue: 0,\n      useNativeDriver: true,\n      isInteraction: false\n    }).start();\n  }, [fade, scale]);\n  React.useEffect(function () {\n    if (visible) startAnimation();else stopAnimation();\n  }, [visible, startAnimation, stopAnimation]);\n  React.useEffect(function () {\n    if (animatedValue && animatedValue >= 0) {\n      timer.setValue(animatedValue);\n    }\n  }, [animatedValue, timer]);\n  React.useEffect(function () {\n    if (visible && prevWidth === 0) {\n      startAnimation();\n    }\n  }, [prevWidth, startAnimation, visible]);\n  var onLayout = function onLayout(event) {\n    setPrevWidth(width);\n    setWidth(event.nativeEvent.layout.width);\n  };\n  var tintColor = color || ((_theme$colors = theme.colors) === null || _theme$colors === void 0 ? void 0 : _theme$colors.primary);\n  var trackTintColor = theme.isV3 ? theme.colors.surfaceVariant : setColor(tintColor).alpha(0.38).rgb().string();\n  return React.createElement(View, _extends({\n    onLayout: onLayout\n  }, rest, {\n    accessible: true,\n    accessibilityRole: \"progressbar\",\n    accessibilityState: {\n      busy: visible\n    },\n    accessibilityValue: indeterminate ? {} : {\n      min: 0,\n      max: 100,\n      now: Math.round(progress * 100)\n    },\n    style: isWeb && styles.webContainer,\n    testID: testID\n  }), React.createElement(Animated.View, {\n    style: [styles.container, {\n      backgroundColor: trackTintColor,\n      opacity: fade\n    }, style]\n  }, width ? React.createElement(Animated.View, {\n    testID: `${testID}-fill`,\n    style: [styles.progressBar, {\n      width: width,\n      backgroundColor: tintColor,\n      transform: [{\n        translateX: timer.interpolate(indeterminate ? {\n          inputRange: [0, 0.5, 1],\n          outputRange: [(isRTL ? 1 : -1) * 0.5 * width, (isRTL ? 1 : -1) * 0.5 * INDETERMINATE_MAX_WIDTH * width, (isRTL ? -1 : 1) * 0.7 * width]\n        } : {\n          inputRange: [0, 1],\n          outputRange: [(isRTL ? 1 : -1) * 0.5 * width, 0]\n        })\n      }, {\n        scaleX: timer.interpolate(indeterminate ? {\n          inputRange: [0, 0.5, 1],\n          outputRange: [0.0001, INDETERMINATE_MAX_WIDTH, 0.0001]\n        } : {\n          inputRange: [0, 1],\n          outputRange: [0.0001, 1]\n        })\n      }]\n    }, fillStyle]\n  }) : null));\n};\nvar styles = StyleSheet.create({\n  container: {\n    height: 4,\n    overflow: 'hidden'\n  },\n  webContainer: {\n    width: '100%',\n    height: '100%'\n  },\n  progressBar: {\n    flex: 1\n  }\n});\nexport default ProgressBar;", "map": {"version": 3, "names": ["React", "Animated", "I18nManager", "Platform", "StyleSheet", "View", "setColor", "useInternalTheme", "INDETERMINATE_DURATION", "INDETERMINATE_MAX_WIDTH", "isRTL", "ProgressBar", "_ref", "color", "indeterminate", "_ref$progress", "progress", "_ref$visible", "visible", "themeOverrides", "theme", "animatedValue", "style", "fillStyle", "_ref$testID", "testID", "rest", "_objectWithoutProperties", "_excluded", "_theme$colors", "isWeb", "OS", "_React$useRef", "useRef", "Value", "timer", "current", "_React$useRef2", "fade", "passedAnimatedValue", "_React$useState", "useState", "_React$useState2", "_slicedToArray", "width", "<PERSON><PERSON><PERSON><PERSON>", "_React$useState3", "_React$useState4", "prevWidth", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indeterminateAnimation", "scale", "animation", "useEffect", "startAnimation", "useCallback", "timing", "duration", "toValue", "useNativeDriver", "isInteraction", "start", "externalAnimation", "setValue", "loop", "stopAnimation", "stop", "onLayout", "event", "nativeEvent", "layout", "tintColor", "colors", "primary", "trackTintColor", "isV3", "surfaceVariant", "alpha", "rgb", "string", "createElement", "_extends", "accessible", "accessibilityRole", "accessibilityState", "busy", "accessibilityValue", "min", "max", "now", "Math", "round", "styles", "webContainer", "container", "backgroundColor", "opacity", "progressBar", "transform", "translateX", "interpolate", "inputRange", "outputRange", "scaleX", "create", "height", "overflow", "flex"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\ProgressBar.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  Animated,\n  I18nManager,\n  LayoutChangeEvent,\n  Platform,\n  StyleProp,\n  StyleSheet,\n  View,\n  ViewStyle,\n} from 'react-native';\n\nimport setColor from 'color';\n\nimport { useInternalTheme } from '../core/theming';\nimport type { ThemeProp } from '../types';\n\nexport type Props = React.ComponentPropsWithRef<typeof View> & {\n  /**\n   * Animated value (between 0 and 1). This tells the progress bar to rely on this value to animate it.\n   * Note: It should not be used in parallel with the `progress` prop.\n   */\n  animatedValue?: number;\n  /**\n   * Progress value (between 0 and 1).\n   * Note: It should not be used in parallel with the `animatedValue` prop.\n   */\n  progress?: number;\n  /**\n   * Color of the progress bar. The background color will be calculated based on this but you can change it by passing `backgroundColor` to `style` prop.\n   */\n  color?: string;\n  /**\n   * If the progress bar will show indeterminate progress.\n   */\n  indeterminate?: boolean;\n  /**\n   * Whether to show the ProgressBar (true, the default) or hide it (false).\n   */\n  visible?: boolean;\n  /**\n   * Style of filled part of the ProgresBar.\n   */\n  fillStyle?: Animated.WithAnimatedValue<StyleProp<ViewStyle>>;\n  style?: StyleProp<ViewStyle>;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n  /**\n   * testID to be used on tests.\n   */\n  testID?: string;\n};\n\nconst INDETERMINATE_DURATION = 2000;\nconst INDETERMINATE_MAX_WIDTH = 0.6;\nconst { isRTL } = I18nManager;\n\n/**\n * Progress bar is an indicator used to present progress of some activity in the app.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { ProgressBar, MD3Colors } from 'react-native-paper';\n *\n * const MyComponent = () => (\n *   <ProgressBar progress={0.5} color={MD3Colors.error50} />\n * );\n *\n * export default MyComponent;\n * ```\n */\nconst ProgressBar = ({\n  color,\n  indeterminate,\n  progress = 0,\n  visible = true,\n  theme: themeOverrides,\n  animatedValue,\n  style,\n  fillStyle,\n  testID = 'progress-bar',\n  ...rest\n}: Props) => {\n  const isWeb = Platform.OS === 'web';\n  const theme = useInternalTheme(themeOverrides);\n  const { current: timer } = React.useRef<Animated.Value>(\n    new Animated.Value(0)\n  );\n  const { current: fade } = React.useRef<Animated.Value>(new Animated.Value(0));\n  const passedAnimatedValue =\n    React.useRef<Props['animatedValue']>(animatedValue);\n  const [width, setWidth] = React.useState<number>(0);\n  const [prevWidth, setPrevWidth] = React.useState<number>(0);\n\n  const indeterminateAnimation =\n    React.useRef<Animated.CompositeAnimation | null>(null);\n\n  const { scale } = theme.animation;\n\n  React.useEffect(() => {\n    passedAnimatedValue.current = animatedValue;\n  });\n\n  const startAnimation = React.useCallback(() => {\n    // Show progress bar\n    Animated.timing(fade, {\n      duration: 200 * scale,\n      toValue: 1,\n      useNativeDriver: true,\n      isInteraction: false,\n    }).start();\n\n    /**\n     * We shouldn't add @param animatedValue to the\n     * deps array, to avoid the unnecessary loop.\n     * We can only check if the prop is passed initially,\n     * and we do early return.\n     */\n    const externalAnimation =\n      typeof passedAnimatedValue.current !== 'undefined' &&\n      passedAnimatedValue.current >= 0;\n\n    if (externalAnimation) {\n      return;\n    }\n\n    // Animate progress bar\n    if (indeterminate) {\n      if (!indeterminateAnimation.current) {\n        indeterminateAnimation.current = Animated.timing(timer, {\n          duration: INDETERMINATE_DURATION,\n          toValue: 1,\n          // Animated.loop does not work if useNativeDriver is true on web\n          useNativeDriver: !isWeb,\n          isInteraction: false,\n        });\n      }\n\n      // Reset timer to the beginning\n      timer.setValue(0);\n\n      Animated.loop(indeterminateAnimation.current).start();\n    } else {\n      Animated.timing(timer, {\n        duration: 200 * scale,\n        toValue: progress ? progress : 0,\n        useNativeDriver: true,\n        isInteraction: false,\n      }).start();\n    }\n  }, [fade, scale, indeterminate, timer, progress, isWeb]);\n\n  const stopAnimation = React.useCallback(() => {\n    // Stop indeterminate animation\n    if (indeterminateAnimation.current) {\n      indeterminateAnimation.current.stop();\n    }\n\n    Animated.timing(fade, {\n      duration: 200 * scale,\n      toValue: 0,\n      useNativeDriver: true,\n      isInteraction: false,\n    }).start();\n  }, [fade, scale]);\n\n  React.useEffect(() => {\n    if (visible) startAnimation();\n    else stopAnimation();\n  }, [visible, startAnimation, stopAnimation]);\n\n  React.useEffect(() => {\n    if (animatedValue && animatedValue >= 0) {\n      timer.setValue(animatedValue);\n    }\n  }, [animatedValue, timer]);\n\n  React.useEffect(() => {\n    // Start animation the very first time when previously the width was unclear\n    if (visible && prevWidth === 0) {\n      startAnimation();\n    }\n  }, [prevWidth, startAnimation, visible]);\n\n  const onLayout = (event: LayoutChangeEvent) => {\n    setPrevWidth(width);\n    setWidth(event.nativeEvent.layout.width);\n  };\n\n  const tintColor = color || theme.colors?.primary;\n  const trackTintColor = theme.isV3\n    ? theme.colors.surfaceVariant\n    : setColor(tintColor).alpha(0.38).rgb().string();\n\n  return (\n    <View\n      onLayout={onLayout}\n      {...rest}\n      accessible\n      accessibilityRole=\"progressbar\"\n      accessibilityState={{ busy: visible }}\n      accessibilityValue={\n        indeterminate\n          ? {}\n          : { min: 0, max: 100, now: Math.round(progress * 100) }\n      }\n      style={isWeb && styles.webContainer}\n      testID={testID}\n    >\n      <Animated.View\n        style={[\n          styles.container,\n          { backgroundColor: trackTintColor, opacity: fade },\n          style,\n        ]}\n      >\n        {width ? (\n          <Animated.View\n            testID={`${testID}-fill`}\n            style={[\n              styles.progressBar,\n              {\n                width,\n                backgroundColor: tintColor,\n                transform: [\n                  {\n                    translateX: timer.interpolate(\n                      indeterminate\n                        ? {\n                            inputRange: [0, 0.5, 1],\n                            outputRange: [\n                              (isRTL ? 1 : -1) * 0.5 * width,\n                              (isRTL ? 1 : -1) *\n                                0.5 *\n                                INDETERMINATE_MAX_WIDTH *\n                                width,\n                              (isRTL ? -1 : 1) * 0.7 * width,\n                            ],\n                          }\n                        : {\n                            inputRange: [0, 1],\n                            outputRange: [(isRTL ? 1 : -1) * 0.5 * width, 0],\n                          }\n                    ),\n                  },\n                  {\n                    // Workaround for workaround for https://github.com/facebook/react-native/issues/6278\n                    scaleX: timer.interpolate(\n                      indeterminate\n                        ? {\n                            inputRange: [0, 0.5, 1],\n                            outputRange: [\n                              0.0001,\n                              INDETERMINATE_MAX_WIDTH,\n                              0.0001,\n                            ],\n                          }\n                        : {\n                            inputRange: [0, 1],\n                            outputRange: [0.0001, 1],\n                          }\n                    ),\n                  },\n                ],\n              },\n              fillStyle,\n            ]}\n          />\n        ) : null}\n      </Animated.View>\n    </View>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    height: 4,\n    overflow: 'hidden',\n  },\n  webContainer: {\n    width: '100%',\n    height: '100%',\n  },\n  progressBar: {\n    flex: 1,\n  },\n});\n\nexport default ProgressBar;\n"], "mappings": ";;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAAA,OAAAC,WAAA;AAAA,OAAAC,QAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAY9B,OAAOC,QAAQ,MAAM,OAAO;AAE5B,SAASC,gBAAgB;AAyCzB,IAAMC,sBAAsB,GAAG,IAAI;AACnC,IAAMC,uBAAuB,GAAG,GAAG;AACnC,IAAQC,KAAA,GAAUR,WAAW,CAArBQ,KAAA;AAiBR,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAAC,IAAA,EAWJ;EAAA,IAVXC,KAAK,GAAAD,IAAA,CAALC,KAAK;IACLC,aAAa,GAAAF,IAAA,CAAbE,aAAa;IAAAC,aAAA,GAAAH,IAAA,CACbI,QAAQ;IAARA,QAAQ,GAAAD,aAAA,cAAG,CAAC,GAAAA,aAAA;IAAAE,YAAA,GAAAL,IAAA,CACZM,OAAO;IAAPA,OAAO,GAAAD,YAAA,cAAG,IAAI,GAAAA,YAAA;IACPE,cAAc,GAAAP,IAAA,CAArBQ,KAAK;IACLC,aAAa,GAAAT,IAAA,CAAbS,aAAa;IACbC,KAAK,GAAAV,IAAA,CAALU,KAAK;IACLC,SAAS,GAAAX,IAAA,CAATW,SAAS;IAAAC,WAAA,GAAAZ,IAAA,CACTa,MAAM;IAANA,MAAM,GAAAD,WAAA,cAAG,cAAc,GAAAA,WAAA;IACpBE,IAAA,GAAAC,wBAAA,CAAAf,IAAA,EAAAgB,SAAA;EACQ,IAAAC,aAAA;EACX,IAAMC,KAAK,GAAG3B,QAAQ,CAAC4B,EAAE,KAAK,KAAK;EACnC,IAAMX,KAAK,GAAGb,gBAAgB,CAACY,cAAc,CAAC;EAC9C,IAAAa,aAAA,GAA2BhC,KAAK,CAACiC,MAAM,CACrC,IAAIhC,QAAQ,CAACiC,KAAK,CAAC,CAAC,CACtB,CAAC;IAFgBC,KAAA,GAAAH,aAAA,CAATI,OAAO;EAGf,IAAAC,cAAA,GAA0BrC,KAAK,CAACiC,MAAM,CAAiB,IAAIhC,QAAQ,CAACiC,KAAK,CAAC,CAAC,CAAC,CAAC;IAA5DI,IAAA,GAAAD,cAAA,CAATD,OAAO;EACf,IAAMG,mBAAmB,GACvBvC,KAAK,CAACiC,MAAM,CAAyBZ,aAAa,CAAC;EACrD,IAAAmB,eAAA,GAA0BxC,KAAK,CAACyC,QAAQ,CAAS,CAAC,CAAC;IAAAC,gBAAA,GAAAC,cAAA,CAAAH,eAAA;IAA5CI,KAAK,GAAAF,gBAAA;IAAEG,QAAQ,GAAAH,gBAAA;EACtB,IAAAI,gBAAA,GAAkC9C,KAAK,CAACyC,QAAQ,CAAS,CAAC,CAAC;IAAAM,gBAAA,GAAAJ,cAAA,CAAAG,gBAAA;IAApDE,SAAS,GAAAD,gBAAA;IAAEE,YAAY,GAAAF,gBAAA;EAE9B,IAAMG,sBAAsB,GAC1BlD,KAAK,CAACiC,MAAM,CAAqC,IAAI,CAAC;EAExD,IAAQkB,KAAA,GAAU/B,KAAK,CAACgC,SAAS,CAAzBD,KAAA;EAERnD,KAAK,CAACqD,SAAS,CAAC,YAAM;IACpBd,mBAAmB,CAACH,OAAO,GAAGf,aAAa;EAC7C,CAAC,CAAC;EAEF,IAAMiC,cAAc,GAAGtD,KAAK,CAACuD,WAAW,CAAC,YAAM;IAE7CtD,QAAQ,CAACuD,MAAM,CAAClB,IAAI,EAAE;MACpBmB,QAAQ,EAAE,GAAG,GAAGN,KAAK;MACrBO,OAAO,EAAE,CAAC;MACVC,eAAe,EAAE,IAAI;MACrBC,aAAa,EAAE;IACjB,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;IAQV,IAAMC,iBAAiB,GACrB,OAAOvB,mBAAmB,CAACH,OAAO,KAAK,WAAW,IAClDG,mBAAmB,CAACH,OAAO,IAAI,CAAC;IAElC,IAAI0B,iBAAiB,EAAE;MACrB;IACF;IAGA,IAAIhD,aAAa,EAAE;MACjB,IAAI,CAACoC,sBAAsB,CAACd,OAAO,EAAE;QACnCc,sBAAsB,CAACd,OAAO,GAAGnC,QAAQ,CAACuD,MAAM,CAACrB,KAAK,EAAE;UACtDsB,QAAQ,EAAEjD,sBAAsB;UAChCkD,OAAO,EAAE,CAAC;UAEVC,eAAe,EAAE,CAAC7B,KAAK;UACvB8B,aAAa,EAAE;QACjB,CAAC,CAAC;MACJ;MAGAzB,KAAK,CAAC4B,QAAQ,CAAC,CAAC,CAAC;MAEjB9D,QAAQ,CAAC+D,IAAI,CAACd,sBAAsB,CAACd,OAAO,CAAC,CAACyB,KAAK,CAAC,CAAC;IACvD,CAAC,MAAM;MACL5D,QAAQ,CAACuD,MAAM,CAACrB,KAAK,EAAE;QACrBsB,QAAQ,EAAE,GAAG,GAAGN,KAAK;QACrBO,OAAO,EAAE1C,QAAQ,GAAGA,QAAQ,GAAG,CAAC;QAChC2C,eAAe,EAAE,IAAI;QACrBC,aAAa,EAAE;MACjB,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;IACZ;EACF,CAAC,EAAE,CAACvB,IAAI,EAAEa,KAAK,EAAErC,aAAa,EAAEqB,KAAK,EAAEnB,QAAQ,EAAEc,KAAK,CAAC,CAAC;EAExD,IAAMmC,aAAa,GAAGjE,KAAK,CAACuD,WAAW,CAAC,YAAM;IAE5C,IAAIL,sBAAsB,CAACd,OAAO,EAAE;MAClCc,sBAAsB,CAACd,OAAO,CAAC8B,IAAI,CAAC,CAAC;IACvC;IAEAjE,QAAQ,CAACuD,MAAM,CAAClB,IAAI,EAAE;MACpBmB,QAAQ,EAAE,GAAG,GAAGN,KAAK;MACrBO,OAAO,EAAE,CAAC;MACVC,eAAe,EAAE,IAAI;MACrBC,aAAa,EAAE;IACjB,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;EACZ,CAAC,EAAE,CAACvB,IAAI,EAAEa,KAAK,CAAC,CAAC;EAEjBnD,KAAK,CAACqD,SAAS,CAAC,YAAM;IACpB,IAAInC,OAAO,EAAEoC,cAAc,CAAC,CAAC,CAAC,KACzBW,aAAa,CAAC,CAAC;EACtB,CAAC,EAAE,CAAC/C,OAAO,EAAEoC,cAAc,EAAEW,aAAa,CAAC,CAAC;EAE5CjE,KAAK,CAACqD,SAAS,CAAC,YAAM;IACpB,IAAIhC,aAAa,IAAIA,aAAa,IAAI,CAAC,EAAE;MACvCc,KAAK,CAAC4B,QAAQ,CAAC1C,aAAa,CAAC;IAC/B;EACF,CAAC,EAAE,CAACA,aAAa,EAAEc,KAAK,CAAC,CAAC;EAE1BnC,KAAK,CAACqD,SAAS,CAAC,YAAM;IAEpB,IAAInC,OAAO,IAAI8B,SAAS,KAAK,CAAC,EAAE;MAC9BM,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAACN,SAAS,EAAEM,cAAc,EAAEpC,OAAO,CAAC,CAAC;EAExC,IAAMiD,QAAQ,GAAI,SAAZA,QAAQA,CAAIC,KAAwB,EAAK;IAC7CnB,YAAY,CAACL,KAAK,CAAC;IACnBC,QAAQ,CAACuB,KAAK,CAACC,WAAW,CAACC,MAAM,CAAC1B,KAAK,CAAC;EAC1C,CAAC;EAED,IAAM2B,SAAS,GAAG1D,KAAK,MAAAgB,aAAA,GAAIT,KAAK,CAACoD,MAAM,cAAA3C,aAAA,uBAAZA,aAAA,CAAc4C,OAAO;EAChD,IAAMC,cAAc,GAAGtD,KAAK,CAACuD,IAAI,GAC7BvD,KAAK,CAACoD,MAAM,CAACI,cAAc,GAC3BtE,QAAQ,CAACiE,SAAS,CAAC,CAACM,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EAElD,OACE/E,KAAA,CAAAgF,aAAA,CAAC3E,IAAI,EAAA4E,QAAA;IACHd,QAAQ,EAAEA;EAAS,GACfzC,IAAI;IACRwD,UAAU;IACVC,iBAAiB,EAAC,aAAa;IAC/BC,kBAAkB,EAAE;MAAEC,IAAI,EAAEnE;IAAQ,CAAE;IACtCoE,kBAAkB,EAChBxE,aAAa,GACT,CAAC,CAAC,GACF;MAAEyE,GAAG,EAAE,CAAC;MAAEC,GAAG,EAAE,GAAG;MAAEC,GAAG,EAAEC,IAAI,CAACC,KAAK,CAAC3E,QAAQ,GAAG,GAAG;IAAE,CACzD;IACDM,KAAK,EAAEQ,KAAK,IAAI8D,MAAM,CAACC,YAAa;IACpCpE,MAAM,EAAEA;EAAO,IAEfzB,KAAA,CAAAgF,aAAA,CAAC/E,QAAQ,CAACI,IAAI;IACZiB,KAAK,EAAE,CACLsE,MAAM,CAACE,SAAS,EAChB;MAAEC,eAAe,EAAErB,cAAc;MAAEsB,OAAO,EAAE1D;IAAK,CAAC,EAClDhB,KAAK;EACL,GAEDsB,KAAK,GACJ5C,KAAA,CAAAgF,aAAA,CAAC/E,QAAQ,CAACI,IAAI;IACZoB,MAAM,EAAE,GAAGA,MAAM,OAAQ;IACzBH,KAAK,EAAE,CACLsE,MAAM,CAACK,WAAW,EAClB;MACErD,KAAK,EAALA,KAAK;MACLmD,eAAe,EAAExB,SAAS;MAC1B2B,SAAS,EAAE,CACT;QACEC,UAAU,EAAEhE,KAAK,CAACiE,WAAW,CAC3BtF,aAAa,GACT;UACEuF,UAAU,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UACvBC,WAAW,EAAE,CACX,CAAC5F,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,GAAGkC,KAAK,EAC9B,CAAClC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,IACb,GAAG,GACHD,uBAAuB,GACvBmC,KAAK,EACP,CAAClC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,GAAGkC,KAAK;QAElC,CAAC,GACD;UACEyD,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;UAClBC,WAAW,EAAE,CAAC,CAAC5F,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,GAAGkC,KAAK,EAAE,CAAC;QACjD,CACN;MACF,CAAC,EACD;QAEE2D,MAAM,EAAEpE,KAAK,CAACiE,WAAW,CACvBtF,aAAa,GACT;UACEuF,UAAU,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UACvBC,WAAW,EAAE,CACX,MAAM,EACN7F,uBAAuB,EACvB,MAAM;QAEV,CAAC,GACD;UACE4F,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;UAClBC,WAAW,EAAE,CAAC,MAAM,EAAE,CAAC;QACzB,CACN;MACF,CAAC;IAEL,CAAC,EACD/E,SAAS;EACT,CACH,CAAC,GACA,IACS,CACX,CAAC;AAEX,CAAC;AAED,IAAMqE,MAAM,GAAGxF,UAAU,CAACoG,MAAM,CAAC;EAC/BV,SAAS,EAAE;IACTW,MAAM,EAAE,CAAC;IACTC,QAAQ,EAAE;EACZ,CAAC;EACDb,YAAY,EAAE;IACZjD,KAAK,EAAE,MAAM;IACb6D,MAAM,EAAE;EACV,CAAC;EACDR,WAAW,EAAE;IACXU,IAAI,EAAE;EACR;AACF,CAAC,CAAC;AAEF,eAAehG,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}