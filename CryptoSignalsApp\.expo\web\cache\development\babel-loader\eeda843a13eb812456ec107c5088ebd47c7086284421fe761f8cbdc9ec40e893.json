{"ast": null, "code": "import * as React from 'react';\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport { useSafeAreaInsets } from 'react-native-safe-area-context';\nimport DialogActions from \"./DialogActions\";\nimport DialogContent from \"./DialogContent\";\nimport DialogIcon from \"./DialogIcon\";\nimport DialogScrollArea from \"./DialogScrollArea\";\nimport DialogTitle from \"./DialogTitle\";\nimport { useInternalTheme } from \"../../core/theming\";\nimport overlay from \"../../styles/overlay\";\nimport Modal from \"../Modal\";\nvar DIALOG_ELEVATION = 24;\nvar Dialog = function Dialog(_ref) {\n  var children = _ref.children,\n    _ref$dismissable = _ref.dismissable,\n    dismissable = _ref$dismissable === void 0 ? true : _ref$dismissable,\n    _ref$dismissableBackB = _ref.dismissableBackButton,\n    dismissableBackButton = _ref$dismissableBackB === void 0 ? dismissable : _ref$dismissableBackB,\n    onDismiss = _ref.onDismiss,\n    _ref$visible = _ref.visible,\n    visible = _ref$visible === void 0 ? false : _ref$visible,\n    style = _ref.style,\n    themeOverrides = _ref.theme,\n    testID = _ref.testID;\n  var _useSafeAreaInsets = useSafeAreaInsets(),\n    right = _useSafeAreaInsets.right,\n    left = _useSafeAreaInsets.left;\n  var theme = useInternalTheme(themeOverrides);\n  var isV3 = theme.isV3,\n    dark = theme.dark,\n    mode = theme.mode,\n    colors = theme.colors,\n    roundness = theme.roundness;\n  var borderRadius = (isV3 ? 7 : 1) * roundness;\n  var backgroundColorV2 = dark && mode === 'adaptive' ? overlay(DIALOG_ELEVATION, colors === null || colors === void 0 ? void 0 : colors.surface) : colors === null || colors === void 0 ? void 0 : colors.surface;\n  var backgroundColor = isV3 ? theme.colors.elevation.level3 : backgroundColorV2;\n  return React.createElement(Modal, {\n    dismissable: dismissable,\n    dismissableBackButton: dismissableBackButton,\n    onDismiss: onDismiss,\n    visible: visible,\n    contentContainerStyle: [{\n      borderRadius: borderRadius,\n      backgroundColor: backgroundColor,\n      marginHorizontal: Math.max(left, right, 26)\n    }, styles.container, style],\n    theme: theme,\n    testID: testID\n  }, React.Children.toArray(children).filter(function (child) {\n    return child != null && typeof child !== 'boolean';\n  }).map(function (child, i) {\n    if (isV3) {\n      if (i === 0 && React.isValidElement(child)) {\n        return React.cloneElement(child, {\n          style: [{\n            marginTop: 24\n          }, child.props.style]\n        });\n      }\n    }\n    if (i === 0 && React.isValidElement(child) && child.type === DialogContent) {\n      return React.cloneElement(child, {\n        style: [{\n          paddingTop: 24\n        }, child.props.style]\n      });\n    }\n    return child;\n  }));\n};\nDialog.Content = DialogContent;\nDialog.Actions = DialogActions;\nDialog.Title = DialogTitle;\nDialog.ScrollArea = DialogScrollArea;\nDialog.Icon = DialogIcon;\nvar styles = StyleSheet.create({\n  container: {\n    marginVertical: Platform.OS === 'android' ? 44 : 0,\n    elevation: DIALOG_ELEVATION,\n    justifyContent: 'flex-start'\n  }\n});\nexport default Dialog;", "map": {"version": 3, "names": ["React", "Platform", "StyleSheet", "useSafeAreaInsets", "DialogActions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogIcon", "DialogScrollArea", "DialogTitle", "useInternalTheme", "overlay", "Modal", "DIALOG_ELEVATION", "Dialog", "_ref", "children", "_ref$dismissable", "dismissable", "_ref$dismissableBackB", "dismissable<PERSON><PERSON><PERSON><PERSON><PERSON>", "on<PERSON><PERSON><PERSON>", "_ref$visible", "visible", "style", "themeOverrides", "theme", "testID", "_useSafeAreaInsets", "right", "left", "isV3", "dark", "mode", "colors", "roundness", "borderRadius", "backgroundColorV2", "surface", "backgroundColor", "elevation", "level3", "createElement", "contentContainerStyle", "marginHorizontal", "Math", "max", "styles", "container", "Children", "toArray", "filter", "child", "map", "i", "isValidElement", "cloneElement", "marginTop", "props", "type", "paddingTop", "Content", "Actions", "Title", "ScrollArea", "Icon", "create", "marginVertical", "OS", "justifyContent"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\Dialog\\Dialog.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  Animated,\n  Platform,\n  StyleProp,\n  StyleSheet,\n  ViewStyle,\n} from 'react-native';\n\nimport { useSafeAreaInsets } from 'react-native-safe-area-context';\n\nimport DialogActions from './DialogActions';\nimport DialogContent from './DialogContent';\nimport DialogIcon from './DialogIcon';\nimport DialogScrollArea from './DialogScrollArea';\nimport DialogTitle from './DialogTitle';\nimport { useInternalTheme } from '../../core/theming';\nimport overlay from '../../styles/overlay';\nimport type { ThemeProp } from '../../types';\nimport Modal from '../Modal';\nimport { DialogChildProps } from './utils';\n\nexport type Props = {\n  /**\n   * Determines whether clicking outside the dialog dismiss it.\n   */\n  dismissable?: boolean;\n  /**\n   * Determines whether clicking Android hardware back button dismiss dialog.\n   */\n  dismissableBackButton?: boolean;\n  /**\n   * Callback that is called when the user dismisses the dialog.\n   */\n  onDismiss?: () => void;\n  /**\n   * Determines Whether the dialog is visible.\n   */\n  visible: boolean;\n  /**\n   * Content of the `Dialog`.\n   */\n  children: React.ReactNode;\n  style?: Animated.WithAnimatedValue<StyleProp<ViewStyle>>;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n  /**\n   * testID to be used on tests.\n   */\n  testID?: string;\n};\n\nconst DIALOG_ELEVATION: number = 24;\n\n/**\n * Dialogs inform users about a specific task and may contain critical information, require decisions, or involve multiple tasks.\n * To render the `Dialog` above other components, you'll need to wrap it with the [`Portal`](../../Portal) component.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { View } from 'react-native';\n * import { Button, Dialog, Portal, PaperProvider, Text } from 'react-native-paper';\n *\n * const MyComponent = () => {\n *   const [visible, setVisible] = React.useState(false);\n *\n *   const showDialog = () => setVisible(true);\n *\n *   const hideDialog = () => setVisible(false);\n *\n *   return (\n *     <PaperProvider>\n *       <View>\n *         <Button onPress={showDialog}>Show Dialog</Button>\n *         <Portal>\n *           <Dialog visible={visible} onDismiss={hideDialog}>\n *             <Dialog.Title>Alert</Dialog.Title>\n *             <Dialog.Content>\n *               <Text variant=\"bodyMedium\">This is simple dialog</Text>\n *             </Dialog.Content>\n *             <Dialog.Actions>\n *               <Button onPress={hideDialog}>Done</Button>\n *             </Dialog.Actions>\n *           </Dialog>\n *         </Portal>\n *       </View>\n *     </PaperProvider>\n *   );\n * };\n *\n * export default MyComponent;\n * ```\n */\nconst Dialog = ({\n  children,\n  dismissable = true,\n  dismissableBackButton = dismissable,\n  onDismiss,\n  visible = false,\n  style,\n  theme: themeOverrides,\n  testID,\n}: Props) => {\n  const { right, left } = useSafeAreaInsets();\n  const theme = useInternalTheme(themeOverrides);\n  const { isV3, dark, mode, colors, roundness } = theme;\n  const borderRadius = (isV3 ? 7 : 1) * roundness;\n\n  const backgroundColorV2 =\n    dark && mode === 'adaptive'\n      ? overlay(DIALOG_ELEVATION, colors?.surface)\n      : colors?.surface;\n  const backgroundColor = isV3\n    ? theme.colors.elevation.level3\n    : backgroundColorV2;\n\n  return (\n    <Modal\n      dismissable={dismissable}\n      dismissableBackButton={dismissableBackButton}\n      onDismiss={onDismiss}\n      visible={visible}\n      contentContainerStyle={[\n        {\n          borderRadius,\n          backgroundColor,\n          marginHorizontal: Math.max(left, right, 26),\n        },\n        styles.container,\n        style,\n      ]}\n      theme={theme}\n      testID={testID}\n    >\n      {React.Children.toArray(children)\n        .filter((child) => child != null && typeof child !== 'boolean')\n        .map((child, i) => {\n          if (isV3) {\n            if (i === 0 && React.isValidElement<DialogChildProps>(child)) {\n              return React.cloneElement(child, {\n                style: [{ marginTop: 24 }, child.props.style],\n              });\n            }\n          }\n\n          if (\n            i === 0 &&\n            React.isValidElement<DialogChildProps>(child) &&\n            child.type === DialogContent\n          ) {\n            // Dialog content is the first item, so we add a top padding\n            return React.cloneElement(child, {\n              style: [{ paddingTop: 24 }, child.props.style],\n            });\n          }\n\n          return child;\n        })}\n    </Modal>\n  );\n};\n\n// @component ./DialogContent.tsx\nDialog.Content = DialogContent;\n// @component ./DialogActions.tsx\nDialog.Actions = DialogActions;\n// @component ./DialogTitle.tsx\nDialog.Title = DialogTitle;\n// @component ./DialogScrollArea.tsx\nDialog.ScrollArea = DialogScrollArea;\n// @component ./DialogIcon.tsx\nDialog.Icon = DialogIcon;\n\nconst styles = StyleSheet.create({\n  container: {\n    /**\n     * This prevents the shadow from being clipped on Android since Android\n     * doesn't support `overflow: visible`.\n     * One downside for this fix is that it will disable clicks on the area\n     * of the shadow around the dialog, consequently, if you click around the\n     * dialog (44 pixel from the top and bottom) it won't be dismissed.\n     */\n    marginVertical: Platform.OS === 'android' ? 44 : 0,\n    elevation: DIALOG_ELEVATION,\n    justifyContent: 'flex-start',\n  },\n});\n\nexport default Dialog;\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAAA,OAAAC,UAAA;AAS9B,SAASC,iBAAiB,QAAQ,gCAAgC;AAElE,OAAOC,aAAa;AACpB,OAAOC,aAAa;AACpB,OAAOC,UAAU;AACjB,OAAOC,gBAAgB;AACvB,OAAOC,WAAW;AAClB,SAASC,gBAAgB;AACzB,OAAOC,OAAO;AAEd,OAAOC,KAAK;AAmCZ,IAAMC,gBAAwB,GAAG,EAAE;AA0CnC,IAAMC,MAAM,GAAG,SAATA,MAAMA,CAAAC,IAAA,EASC;EAAA,IARXC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;IAAAC,gBAAA,GAAAF,IAAA,CACRG,WAAW;IAAXA,WAAW,GAAAD,gBAAA,cAAG,IAAI,GAAAA,gBAAA;IAAAE,qBAAA,GAAAJ,IAAA,CAClBK,qBAAqB;IAArBA,qBAAqB,GAAAD,qBAAA,cAAGD,WAAW,GAAAC,qBAAA;IACnCE,SAAS,GAAAN,IAAA,CAATM,SAAS;IAAAC,YAAA,GAAAP,IAAA,CACTQ,OAAO;IAAPA,OAAO,GAAAD,YAAA,cAAG,KAAK,GAAAA,YAAA;IACfE,KAAK,GAAAT,IAAA,CAALS,KAAK;IACEC,cAAc,GAAAV,IAAA,CAArBW,KAAK;IACLC,MAAA,GAAAZ,IAAA,CAAAY,MAAA;EAEA,IAAAC,kBAAA,GAAwBxB,iBAAiB,CAAC,CAAC;IAAnCyB,KAAK,GAAAD,kBAAA,CAALC,KAAK;IAAEC,IAAA,GAAAF,kBAAA,CAAAE,IAAA;EACf,IAAMJ,KAAK,GAAGhB,gBAAgB,CAACe,cAAc,CAAC;EAC9C,IAAQM,IAAI,GAAoCL,KAAK,CAA7CK,IAAI;IAAEC,IAAI,GAA8BN,KAAK,CAAvCM,IAAI;IAAEC,IAAI,GAAwBP,KAAK,CAAjCO,IAAI;IAAEC,MAAM,GAAgBR,KAAK,CAA3BQ,MAAM;IAAEC,SAAA,GAAcT,KAAK,CAAnBS,SAAA;EAClC,IAAMC,YAAY,GAAG,CAACL,IAAI,GAAG,CAAC,GAAG,CAAC,IAAII,SAAS;EAE/C,IAAME,iBAAiB,GACrBL,IAAI,IAAIC,IAAI,KAAK,UAAU,GACvBtB,OAAO,CAACE,gBAAgB,EAAEqB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEI,OAAO,CAAC,GAC1CJ,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEI,OAAO;EACrB,IAAMC,eAAe,GAAGR,IAAI,GACxBL,KAAK,CAACQ,MAAM,CAACM,SAAS,CAACC,MAAM,GAC7BJ,iBAAiB;EAErB,OACEpC,KAAA,CAAAyC,aAAA,CAAC9B,KAAK;IACJM,WAAW,EAAEA,WAAY;IACzBE,qBAAqB,EAAEA,qBAAsB;IAC7CC,SAAS,EAAEA,SAAU;IACrBE,OAAO,EAAEA,OAAQ;IACjBoB,qBAAqB,EAAE,CACrB;MACEP,YAAY,EAAZA,YAAY;MACZG,eAAe,EAAfA,eAAe;MACfK,gBAAgB,EAAEC,IAAI,CAACC,GAAG,CAAChB,IAAI,EAAED,KAAK,EAAE,EAAE;IAC5C,CAAC,EACDkB,MAAM,CAACC,SAAS,EAChBxB,KAAK,CACL;IACFE,KAAK,EAAEA,KAAM;IACbC,MAAM,EAAEA;EAAO,GAEd1B,KAAK,CAACgD,QAAQ,CAACC,OAAO,CAAClC,QAAQ,CAAC,CAC9BmC,MAAM,CAAE,UAAAC,KAAK;IAAA,OAAKA,KAAK,IAAI,IAAI,IAAI,OAAOA,KAAK,KAAK,SAAS;EAAA,EAAC,CAC9DC,GAAG,CAAC,UAACD,KAAK,EAAEE,CAAC,EAAK;IACjB,IAAIvB,IAAI,EAAE;MACR,IAAIuB,CAAC,KAAK,CAAC,IAAIrD,KAAK,CAACsD,cAAc,CAAmBH,KAAK,CAAC,EAAE;QAC5D,OAAOnD,KAAK,CAACuD,YAAY,CAACJ,KAAK,EAAE;UAC/B5B,KAAK,EAAE,CAAC;YAAEiC,SAAS,EAAE;UAAG,CAAC,EAAEL,KAAK,CAACM,KAAK,CAAClC,KAAK;QAC9C,CAAC,CAAC;MACJ;IACF;IAEA,IACE8B,CAAC,KAAK,CAAC,IACPrD,KAAK,CAACsD,cAAc,CAAmBH,KAAK,CAAC,IAC7CA,KAAK,CAACO,IAAI,KAAKrD,aAAa,EAC5B;MAEA,OAAOL,KAAK,CAACuD,YAAY,CAACJ,KAAK,EAAE;QAC/B5B,KAAK,EAAE,CAAC;UAAEoC,UAAU,EAAE;QAAG,CAAC,EAAER,KAAK,CAACM,KAAK,CAAClC,KAAK;MAC/C,CAAC,CAAC;IACJ;IAEA,OAAO4B,KAAK;EACd,CAAC,CACE,CAAC;AAEZ,CAAC;AAGDtC,MAAM,CAAC+C,OAAO,GAAGvD,aAAa;AAE9BQ,MAAM,CAACgD,OAAO,GAAGzD,aAAa;AAE9BS,MAAM,CAACiD,KAAK,GAAGtD,WAAW;AAE1BK,MAAM,CAACkD,UAAU,GAAGxD,gBAAgB;AAEpCM,MAAM,CAACmD,IAAI,GAAG1D,UAAU;AAExB,IAAMwC,MAAM,GAAG5C,UAAU,CAAC+D,MAAM,CAAC;EAC/BlB,SAAS,EAAE;IAQTmB,cAAc,EAAEjE,QAAQ,CAACkE,EAAE,KAAK,SAAS,GAAG,EAAE,GAAG,CAAC;IAClD5B,SAAS,EAAE3B,gBAAgB;IAC3BwD,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;AAEF,eAAevD,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}