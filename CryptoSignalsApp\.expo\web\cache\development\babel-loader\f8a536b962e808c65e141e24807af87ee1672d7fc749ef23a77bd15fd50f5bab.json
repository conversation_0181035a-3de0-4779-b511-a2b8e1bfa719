{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"disabled\", \"onPress\", \"theme\", \"status\", \"value\", \"testID\"];\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nimport * as React from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport { RadioButtonContext } from \"./RadioButtonGroup\";\nimport { handlePress, isChecked } from \"./utils\";\nimport { useInternalTheme } from \"../../core/theming\";\nimport { getSelectionControlIOSColor } from \"../Checkbox/utils\";\nimport MaterialCommunityIcon from \"../MaterialCommunityIcon\";\nimport TouchableRipple from \"../TouchableRipple/TouchableRipple\";\nvar RadioButtonIOS = function RadioButtonIOS(_ref) {\n  var disabled = _ref.disabled,\n    onPress = _ref.onPress,\n    themeOverrides = _ref.theme,\n    status = _ref.status,\n    value = _ref.value,\n    testID = _ref.testID,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var theme = useInternalTheme(themeOverrides);\n  return React.createElement(RadioButtonContext.Consumer, null, function (context) {\n    var checked = isChecked({\n      contextValue: context === null || context === void 0 ? void 0 : context.value,\n      status: status,\n      value: value\n    }) === 'checked';\n    var _getSelectionControlI = getSelectionControlIOSColor({\n        theme: theme,\n        disabled: disabled,\n        customColor: rest.color\n      }),\n      checkedColor = _getSelectionControlI.checkedColor,\n      rippleColor = _getSelectionControlI.rippleColor;\n    var opacity = checked ? 1 : 0;\n    return React.createElement(TouchableRipple, _extends({}, rest, {\n      borderless: true,\n      rippleColor: rippleColor,\n      onPress: disabled ? undefined : function (event) {\n        handlePress({\n          onPress: onPress,\n          value: value,\n          onValueChange: context === null || context === void 0 ? void 0 : context.onValueChange,\n          event: event\n        });\n      },\n      accessibilityRole: \"radio\",\n      accessibilityState: {\n        disabled: disabled,\n        checked: checked\n      },\n      accessibilityLiveRegion: \"polite\",\n      style: styles.container,\n      testID: testID,\n      theme: theme\n    }), React.createElement(View, {\n      style: {\n        opacity: opacity\n      }\n    }, React.createElement(MaterialCommunityIcon, {\n      allowFontScaling: false,\n      name: \"check\",\n      size: 24,\n      color: checkedColor,\n      direction: \"ltr\"\n    })));\n  });\n};\nRadioButtonIOS.displayName = 'RadioButton.IOS';\nvar styles = StyleSheet.create({\n  container: {\n    borderRadius: 18,\n    padding: 6\n  }\n});\nexport default RadioButtonIOS;\nexport { RadioButtonIOS };", "map": {"version": 3, "names": ["React", "StyleSheet", "View", "RadioButtonContext", "handlePress", "isChecked", "useInternalTheme", "getSelectionControlIOSColor", "MaterialCommunityIcon", "TouchableRipple", "RadioButtonIOS", "_ref", "disabled", "onPress", "themeOverrides", "theme", "status", "value", "testID", "rest", "_objectWithoutProperties", "_excluded", "createElement", "Consumer", "context", "checked", "contextValue", "_getSelectionControlI", "customColor", "color", "checkedColor", "rippleColor", "opacity", "_extends", "borderless", "undefined", "event", "onValueChange", "accessibilityRole", "accessibilityState", "accessibilityLiveRegion", "style", "styles", "container", "allowFontScaling", "name", "size", "direction", "displayName", "create", "borderRadius", "padding"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\RadioButton\\RadioButtonIOS.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { GestureResponderEvent, StyleSheet, View } from 'react-native';\n\nimport { RadioButtonContext, RadioButtonContextType } from './RadioButtonGroup';\nimport { handlePress, isChecked } from './utils';\nimport { useInternalTheme } from '../../core/theming';\nimport type { $RemoveChildren, ThemeProp } from '../../types';\nimport { getSelectionControlIOSColor } from '../Checkbox/utils';\nimport MaterialCommunityIcon from '../MaterialCommunityIcon';\nimport TouchableRipple from '../TouchableRipple/TouchableRipple';\n\nexport type Props = $RemoveChildren<typeof TouchableRipple> & {\n  /**\n   * Value of the radio button\n   */\n  value: string;\n  /**\n   * Status of radio button.\n   */\n  status?: 'checked' | 'unchecked';\n  /**\n   * Whether radio is disabled.\n   */\n  disabled?: boolean;\n  /**\n   * Function to execute on press.\n   */\n  onPress?: (e: GestureResponderEvent) => void;\n  /**\n   * Custom color for radio.\n   */\n  color?: string;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n  /**\n   * testID to be used on tests.\n   */\n  testID?: string;\n};\n\n/**\n * Radio buttons allow the selection a single option from a set.\n * This component follows platform guidelines for iOS, but can be used\n * on any platform.\n *\n * @extends TouchableRipple props https://callstack.github.io/react-native-paper/docs/components/TouchableRipple\n */\nconst RadioButtonIOS = ({\n  disabled,\n  onPress,\n  theme: themeOverrides,\n  status,\n  value,\n  testID,\n  ...rest\n}: Props) => {\n  const theme = useInternalTheme(themeOverrides);\n\n  return (\n    <RadioButtonContext.Consumer>\n      {(context?: RadioButtonContextType) => {\n        const checked =\n          isChecked({\n            contextValue: context?.value,\n            status,\n            value,\n          }) === 'checked';\n\n        const { checkedColor, rippleColor } = getSelectionControlIOSColor({\n          theme,\n          disabled,\n          customColor: rest.color,\n        });\n        const opacity = checked ? 1 : 0;\n\n        return (\n          <TouchableRipple\n            {...rest}\n            borderless\n            rippleColor={rippleColor}\n            onPress={\n              disabled\n                ? undefined\n                : (event) => {\n                    handlePress({\n                      onPress,\n                      value,\n                      onValueChange: context?.onValueChange,\n                      event,\n                    });\n                  }\n            }\n            accessibilityRole=\"radio\"\n            accessibilityState={{ disabled, checked }}\n            accessibilityLiveRegion=\"polite\"\n            style={styles.container}\n            testID={testID}\n            theme={theme}\n          >\n            <View style={{ opacity }}>\n              <MaterialCommunityIcon\n                allowFontScaling={false}\n                name=\"check\"\n                size={24}\n                color={checkedColor}\n                direction=\"ltr\"\n              />\n            </View>\n          </TouchableRipple>\n        );\n      }}\n    </RadioButtonContext.Consumer>\n  );\n};\n\nRadioButtonIOS.displayName = 'RadioButton.IOS';\n\nconst styles = StyleSheet.create({\n  container: {\n    borderRadius: 18,\n    padding: 6,\n  },\n});\n\nexport default RadioButtonIOS;\n\n// @component-docs ignore-next-line\nexport { RadioButtonIOS };\n"], "mappings": ";;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAG9B,SAASC,kBAAkB;AAC3B,SAASC,WAAW,EAAEC,SAAS;AAC/B,SAASC,gBAAgB;AAEzB,SAASC,2BAA2B;AACpC,OAAOC,qBAAqB;AAC5B,OAAOC,eAAe;AAwCtB,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAAAC,IAAA,EAQP;EAAA,IAPXC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;IACRC,OAAO,GAAAF,IAAA,CAAPE,OAAO;IACAC,cAAc,GAAAH,IAAA,CAArBI,KAAK;IACLC,MAAM,GAAAL,IAAA,CAANK,MAAM;IACNC,KAAK,GAAAN,IAAA,CAALM,KAAK;IACLC,MAAM,GAAAP,IAAA,CAANO,MAAM;IACHC,IAAA,GAAAC,wBAAA,CAAAT,IAAA,EAAAU,SAAA;EAEH,IAAMN,KAAK,GAAGT,gBAAgB,CAACQ,cAAc,CAAC;EAE9C,OACEd,KAAA,CAAAsB,aAAA,CAACnB,kBAAkB,CAACoB,QAAQ,QACxB,UAAAC,OAAgC,EAAK;IACrC,IAAMC,OAAO,GACXpB,SAAS,CAAC;MACRqB,YAAY,EAAEF,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEP,KAAK;MAC5BD,MAAM,EAANA,MAAM;MACNC,KAAA,EAAAA;IACF,CAAC,CAAC,KAAK,SAAS;IAElB,IAAAU,qBAAA,GAAsCpB,2BAA2B,CAAC;QAChEQ,KAAK,EAALA,KAAK;QACLH,QAAQ,EAARA,QAAQ;QACRgB,WAAW,EAAET,IAAI,CAACU;MACpB,CAAC,CAAC;MAJMC,YAAY,GAAAH,qBAAA,CAAZG,YAAY;MAAEC,WAAA,GAAAJ,qBAAA,CAAAI,WAAA;IAKtB,IAAMC,OAAO,GAAGP,OAAO,GAAG,CAAC,GAAG,CAAC;IAE/B,OACEzB,KAAA,CAAAsB,aAAA,CAACb,eAAe,EAAAwB,QAAA,KACVd,IAAI;MACRe,UAAU;MACVH,WAAW,EAAEA,WAAY;MACzBlB,OAAO,EACLD,QAAQ,GACJuB,SAAS,GACR,UAAAC,KAAK,EAAK;QACThC,WAAW,CAAC;UACVS,OAAO,EAAPA,OAAO;UACPI,KAAK,EAALA,KAAK;UACLoB,aAAa,EAAEb,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEa,aAAa;UACrCD,KAAA,EAAAA;QACF,CAAC,CAAC;MACJ,CACL;MACDE,iBAAiB,EAAC,OAAO;MACzBC,kBAAkB,EAAE;QAAE3B,QAAQ,EAARA,QAAQ;QAAEa,OAAA,EAAAA;MAAQ,CAAE;MAC1Ce,uBAAuB,EAAC,QAAQ;MAChCC,KAAK,EAAEC,MAAM,CAACC,SAAU;MACxBzB,MAAM,EAAEA,MAAO;MACfH,KAAK,EAAEA;IAAM,IAEbf,KAAA,CAAAsB,aAAA,CAACpB,IAAI;MAACuC,KAAK,EAAE;QAAET,OAAA,EAAAA;MAAQ;IAAE,GACvBhC,KAAA,CAAAsB,aAAA,CAACd,qBAAqB;MACpBoC,gBAAgB,EAAE,KAAM;MACxBC,IAAI,EAAC,OAAO;MACZC,IAAI,EAAE,EAAG;MACTjB,KAAK,EAAEC,YAAa;MACpBiB,SAAS,EAAC;IAAK,CAChB,CACG,CACS,CAAC;EAEtB,CAC2B,CAAC;AAElC,CAAC;AAEDrC,cAAc,CAACsC,WAAW,GAAG,iBAAiB;AAE9C,IAAMN,MAAM,GAAGzC,UAAU,CAACgD,MAAM,CAAC;EAC/BN,SAAS,EAAE;IACTO,YAAY,EAAE,EAAE;IAChBC,OAAO,EAAE;EACX;AACF,CAAC,CAAC;AAEF,eAAezC,cAAc;AAG7B,SAASA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}