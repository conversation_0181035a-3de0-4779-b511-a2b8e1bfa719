#!/usr/bin/env python3
"""
Wrapper para indicadores técnicos usando pandas-ta e finta
Substitui o TA-Lib que tem problemas de instalação no Windows
"""

import pandas as pd
import numpy as np
import pandas_ta as ta
from finta import TA
import logging

logger = logging.getLogger(__name__)

class TechnicalIndicators:
    """Classe wrapper para indicadores técnicos"""
    
    @staticmethod
    def RSI(close_prices, timeperiod=14):
        """Relative Strength Index"""
        try:
            if isinstance(close_prices, pd.Series):
                df = pd.DataFrame({'close': close_prices})
            else:
                df = pd.DataFrame({'close': close_prices})
            
            rsi = ta.rsi(df['close'], length=timeperiod)
            return rsi.values
        except Exception as e:
            logger.error(f"Erro ao calcular RSI: {e}")
            return np.array([50.0] * len(close_prices))  # Valor neutro
    
    @staticmethod
    def BBANDS(close_prices, timeperiod=20, nbdevup=2, nbdevdn=2):
        """Bollinger Bands"""
        try:
            if isinstance(close_prices, pd.Series):
                df = pd.DataFrame({'close': close_prices})
            else:
                df = pd.DataFrame({'close': close_prices})
            
            bb = ta.bbands(df['close'], length=timeperiod, std=nbdevup)
            
            upper = bb[f'BBU_{timeperiod}_{nbdevup}.0'].values
            middle = bb[f'BBM_{timeperiod}_{nbdevup}.0'].values
            lower = bb[f'BBL_{timeperiod}_{nbdevup}.0'].values
            
            return upper, middle, lower
        except Exception as e:
            logger.error(f"Erro ao calcular Bollinger Bands: {e}")
            # Retornar valores padrão
            close_array = np.array(close_prices)
            sma = np.mean(close_array[-timeperiod:])
            std = np.std(close_array[-timeperiod:])
            upper = np.array([sma + (nbdevup * std)] * len(close_prices))
            middle = np.array([sma] * len(close_prices))
            lower = np.array([sma - (nbdevdn * std)] * len(close_prices))
            return upper, middle, lower
    
    @staticmethod
    def MACD(close_prices, fastperiod=12, slowperiod=26, signalperiod=9):
        """MACD"""
        try:
            if isinstance(close_prices, pd.Series):
                df = pd.DataFrame({'close': close_prices})
            else:
                df = pd.DataFrame({'close': close_prices})
            
            macd_data = ta.macd(df['close'], fast=fastperiod, slow=slowperiod, signal=signalperiod)
            
            macd = macd_data[f'MACD_{fastperiod}_{slowperiod}_{signalperiod}'].values
            signal = macd_data[f'MACDs_{fastperiod}_{slowperiod}_{signalperiod}'].values
            hist = macd_data[f'MACDh_{fastperiod}_{slowperiod}_{signalperiod}'].values
            
            return macd, signal, hist
        except Exception as e:
            logger.error(f"Erro ao calcular MACD: {e}")
            # Retornar valores padrão
            zeros = np.zeros(len(close_prices))
            return zeros, zeros, zeros
    
    @staticmethod
    def SMA(values, timeperiod=20):
        """Simple Moving Average"""
        try:
            if isinstance(values, pd.Series):
                df = pd.DataFrame({'values': values})
            else:
                df = pd.DataFrame({'values': values})
            
            sma = ta.sma(df['values'], length=timeperiod)
            return sma.values
        except Exception as e:
            logger.error(f"Erro ao calcular SMA: {e}")
            # Calcular manualmente
            values_array = np.array(values)
            sma_values = []
            for i in range(len(values_array)):
                if i < timeperiod - 1:
                    sma_values.append(np.nan)
                else:
                    sma_values.append(np.mean(values_array[i-timeperiod+1:i+1]))
            return np.array(sma_values)
    
    @staticmethod
    def ATR(high_prices, low_prices, close_prices, timeperiod=14):
        """Average True Range"""
        try:
            df = pd.DataFrame({
                'high': high_prices,
                'low': low_prices,
                'close': close_prices
            })
            
            atr = ta.atr(df['high'], df['low'], df['close'], length=timeperiod)
            return atr.values
        except Exception as e:
            logger.error(f"Erro ao calcular ATR: {e}")
            # Calcular manualmente
            high_array = np.array(high_prices)
            low_array = np.array(low_prices)
            close_array = np.array(close_prices)
            
            tr_values = []
            for i in range(len(high_array)):
                if i == 0:
                    tr = high_array[i] - low_array[i]
                else:
                    tr = max(
                        high_array[i] - low_array[i],
                        abs(high_array[i] - close_array[i-1]),
                        abs(low_array[i] - close_array[i-1])
                    )
                tr_values.append(tr)
            
            # Calcular ATR como média móvel do TR
            atr_values = []
            for i in range(len(tr_values)):
                if i < timeperiod - 1:
                    atr_values.append(np.nan)
                else:
                    atr_values.append(np.mean(tr_values[i-timeperiod+1:i+1]))
            
            return np.array(atr_values)
    
    @staticmethod
    def STOCH(high_prices, low_prices, close_prices, fastk_period=14, slowk_period=3, slowd_period=3):
        """Stochastic Oscillator"""
        try:
            df = pd.DataFrame({
                'high': high_prices,
                'low': low_prices,
                'close': close_prices
            })
            
            stoch = ta.stoch(df['high'], df['low'], df['close'], 
                           k=fastk_period, d=slowd_period, smooth_k=slowk_period)
            
            slowk = stoch[f'STOCHk_{fastk_period}_{slowk_period}_{slowd_period}'].values
            slowd = stoch[f'STOCHd_{fastk_period}_{slowk_period}_{slowd_period}'].values
            
            return slowk, slowd
        except Exception as e:
            logger.error(f"Erro ao calcular Stochastic: {e}")
            # Retornar valores padrão
            default_values = np.array([50.0] * len(close_prices))
            return default_values, default_values
    
    @staticmethod
    def WILLR(high_prices, low_prices, close_prices, timeperiod=14):
        """Williams %R"""
        try:
            df = pd.DataFrame({
                'high': high_prices,
                'low': low_prices,
                'close': close_prices
            })
            
            willr = ta.willr(df['high'], df['low'], df['close'], length=timeperiod)
            return willr.values
        except Exception as e:
            logger.error(f"Erro ao calcular Williams %R: {e}")
            return np.array([-50.0] * len(close_prices))  # Valor neutro
    
    @staticmethod
    def CCI(high_prices, low_prices, close_prices, timeperiod=20):
        """Commodity Channel Index"""
        try:
            df = pd.DataFrame({
                'high': high_prices,
                'low': low_prices,
                'close': close_prices
            })
            
            cci = ta.cci(df['high'], df['low'], df['close'], length=timeperiod)
            return cci.values
        except Exception as e:
            logger.error(f"Erro ao calcular CCI: {e}")
            return np.array([0.0] * len(close_prices))  # Valor neutro
    
    @staticmethod
    def OBV(close_prices, volume):
        """On-Balance Volume"""
        try:
            df = pd.DataFrame({
                'close': close_prices,
                'volume': volume
            })
            
            obv = ta.obv(df['close'], df['volume'])
            return obv.values
        except Exception as e:
            logger.error(f"Erro ao calcular OBV: {e}")
            # Calcular manualmente
            close_array = np.array(close_prices)
            volume_array = np.array(volume)
            
            obv_values = [volume_array[0]]
            for i in range(1, len(close_array)):
                if close_array[i] > close_array[i-1]:
                    obv_values.append(obv_values[-1] + volume_array[i])
                elif close_array[i] < close_array[i-1]:
                    obv_values.append(obv_values[-1] - volume_array[i])
                else:
                    obv_values.append(obv_values[-1])
            
            return np.array(obv_values)
    
    @staticmethod
    def AD(high_prices, low_prices, close_prices, volume):
        """Accumulation/Distribution Line"""
        try:
            df = pd.DataFrame({
                'high': high_prices,
                'low': low_prices,
                'close': close_prices,
                'volume': volume
            })
            
            ad = ta.ad(df['high'], df['low'], df['close'], df['volume'])
            return ad.values
        except Exception as e:
            logger.error(f"Erro ao calcular A/D Line: {e}")
            # Calcular manualmente
            high_array = np.array(high_prices)
            low_array = np.array(low_prices)
            close_array = np.array(close_prices)
            volume_array = np.array(volume)
            
            ad_values = []
            ad_sum = 0
            
            for i in range(len(close_array)):
                if high_array[i] != low_array[i]:
                    clv = ((close_array[i] - low_array[i]) - (high_array[i] - close_array[i])) / (high_array[i] - low_array[i])
                else:
                    clv = 0
                
                ad_sum += clv * volume_array[i]
                ad_values.append(ad_sum)
            
            return np.array(ad_values)
    
    @staticmethod
    def MOM(close_prices, timeperiod=10):
        """Momentum"""
        try:
            if isinstance(close_prices, pd.Series):
                df = pd.DataFrame({'close': close_prices})
            else:
                df = pd.DataFrame({'close': close_prices})
            
            mom = ta.mom(df['close'], length=timeperiod)
            return mom.values
        except Exception as e:
            logger.error(f"Erro ao calcular Momentum: {e}")
            # Calcular manualmente
            close_array = np.array(close_prices)
            mom_values = []
            
            for i in range(len(close_array)):
                if i < timeperiod:
                    mom_values.append(0)
                else:
                    mom_values.append(close_array[i] - close_array[i - timeperiod])
            
            return np.array(mom_values)

# Criar alias para compatibilidade com talib
talib = TechnicalIndicators
