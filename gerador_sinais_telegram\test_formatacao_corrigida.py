#!/usr/bin/env python3
"""
Script para testar se a formatação profissional está funcionando corretamente
"""

import sys
import os

# Adicionar o diretório raiz ao path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.signal_formatter import SignalFormatter

def test_scalp_signal_corrected():
    """Testa se a formatação de sinal scalp está usando o formato profissional"""
    print("=" * 60)
    print("TESTE: VERIFICAÇÃO DA FORMATAÇÃO PROFISSIONAL")
    print("=" * 60)
    
    # Dados de exemplo
    symbol = "CAKEUSDT"
    entry_price = 2.35040
    take_profits = {
        40: 2.30339,
        60: 2.27989,
        80: 2.25638,
        100: 2.23288
    }
    signal_type = "SHORT"
    leverage = 20
    
    # Gerar sinal formatado
    message = SignalFormatter.format_scalp_signal(
        symbol, entry_price, take_profits, signal_type, leverage
    )
    
    print("SINAL GERADO:")
    print("-" * 40)
    print(message)
    print("-" * 40)
    
    # Verificar se está usando a formatação profissional
    if message and "CRYPTOSIGNALS PROFESSIONAL" in message:
        print("✅ SUCESSO: Formatação profissional está sendo usada!")
        print("✅ Sem emojis detectados")
        print("✅ Layout clean e técnico")
    elif message and "CryptoSignals App" in message:
        print("❌ ERRO: Ainda está usando formatação antiga com emojis!")
        print("❌ Detectado: 'CryptoSignals App'")
    else:
        print("❌ ERRO: Mensagem não foi gerada ou formato desconhecido")
    
    print("\n")

def test_profit_message():
    """Testa se a formatação de mensagem de profit está funcionando"""
    print("=" * 60)
    print("TESTE: FORMATAÇÃO DE MENSAGEM DE PROFIT")
    print("=" * 60)
    
    # Dados de exemplo
    symbol = "CAKEUSDT"
    signal_type = "SHORT"
    leverage = 20
    entry_price = 2.35040
    current_price = 2.30339
    profit_level = 40
    
    # Gerar mensagem de profit
    message = SignalFormatter.format_profit_message(
        symbol, signal_type, leverage, current_price, profit_level, entry_price
    )
    
    print("MENSAGEM DE PROFIT GERADA:")
    print("-" * 40)
    print(message)
    print("-" * 40)
    
    # Verificar se está usando a formatação profissional
    if message and "CRYPTOSIGNALS PROFESSIONAL" in message:
        print("✅ SUCESSO: Formatação profissional de profit está funcionando!")
        print("✅ Sem emojis detectados")
        print("✅ Layout clean e técnico")
    else:
        print("❌ ERRO: Formatação de profit não está funcionando corretamente")
    
    print("\n")

def test_breakout_signal():
    """Testa formatação de sinal de breakout"""
    print("=" * 60)
    print("TESTE: FORMATAÇÃO DE SINAL BREAKOUT")
    print("=" * 60)
    
    # Dados de exemplo
    symbol = "ETHUSDT"
    entry_price = 3000.0
    stop_loss = 2950.0
    take_profit = 3100.0
    leverage = 10
    signal_type = "LONG"
    
    # Gerar sinal formatado
    message = SignalFormatter.format_breakout_signal(
        symbol, entry_price, stop_loss, take_profit, leverage, signal_type
    )
    
    print("SINAL BREAKOUT GERADO:")
    print("-" * 40)
    print(message)
    print("-" * 40)
    
    # Verificar se está usando a formatação profissional
    if message and "CRYPTOSIGNALS PROFESSIONAL" in message:
        print("✅ SUCESSO: Formatação profissional de breakout está funcionando!")
    else:
        print("❌ ERRO: Formatação de breakout não está funcionando corretamente")
    
    print("\n")

def main():
    """Função principal de teste"""
    print("TESTE DE CORREÇÃO DA FORMATAÇÃO DE SINAIS")
    print("=" * 60)
    print("Verificando se a formatação profissional está sendo usada")
    print("ao invés da formatação antiga com emojis")
    print("=" * 60)
    print("\n")
    
    # Executar todos os testes
    test_scalp_signal_corrected()
    test_profit_message()
    test_breakout_signal()
    
    print("=" * 60)
    print("RESUMO DOS TESTES:")
    print("=" * 60)
    print("Se todos os testes mostraram ✅ SUCESSO, então:")
    print("- A formatação profissional está funcionando")
    print("- Os sinais não usarão mais emojis")
    print("- O layout será clean e técnico")
    print("- O problema foi corrigido!")
    print("\n")
    print("Se algum teste mostrou ❌ ERRO, então:")
    print("- Ainda há código usando formatação antiga")
    print("- Pode ser necessário verificar outros arquivos")
    print("- Cache do Python pode precisar ser limpo")

if __name__ == "__main__":
    main()
