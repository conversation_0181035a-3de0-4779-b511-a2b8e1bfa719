{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/toConsumableArray\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(<PERSON><PERSON><PERSON>, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nimport _createForOfIteratorHelperLoose from \"@babel/runtime/helpers/createForOfIteratorHelperLoose\";\nimport _extends from \"@babel/runtime/helpers/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/objectSpread2\";\nimport RefreshControl from \"../../../exports/RefreshControl\";\nimport ScrollView from \"../../../exports/ScrollView\";\nimport View from \"../../../exports/View\";\nimport StyleSheet from \"../../../exports/StyleSheet\";\nimport findNodeHandle from \"../../../exports/findNodeHandle\";\nimport Batchinator from \"../Batchinator\";\nimport clamp from \"../Utilities/clamp\";\nimport infoLog from \"../infoLog\";\nimport { CellRenderMask } from \"./CellRenderMask\";\nimport ChildListCollection from \"./ChildListCollection\";\nimport FillRateHelper from \"../FillRateHelper\";\nimport StateSafePureComponent from \"./StateSafePureComponent\";\nimport ViewabilityHelper from \"../ViewabilityHelper\";\nimport CellRenderer from \"./VirtualizedListCellRenderer\";\nimport { VirtualizedListCellContextProvider, VirtualizedListContext, VirtualizedListContextProvider } from \"./VirtualizedListContext.js\";\nimport { computeWindowedRenderLimits, keyExtractor as defaultKeyExtractor } from \"../VirtualizeUtils\";\nimport invariant from 'fbjs/lib/invariant';\nimport nullthrows from 'nullthrows';\nimport * as React from 'react';\nvar __DEV__ = process.env.NODE_ENV !== 'production';\nvar ON_EDGE_REACHED_EPSILON = 0.001;\nvar _usedIndexForKey = false;\nvar _keylessItemComponentName = '';\nfunction horizontalOrDefault(horizontal) {\n  return horizontal !== null && horizontal !== void 0 ? horizontal : false;\n}\nfunction initialNumToRenderOrDefault(initialNumToRender) {\n  return initialNumToRender !== null && initialNumToRender !== void 0 ? initialNumToRender : 10;\n}\nfunction maxToRenderPerBatchOrDefault(maxToRenderPerBatch) {\n  return maxToRenderPerBatch !== null && maxToRenderPerBatch !== void 0 ? maxToRenderPerBatch : 10;\n}\nfunction onStartReachedThresholdOrDefault(onStartReachedThreshold) {\n  return onStartReachedThreshold !== null && onStartReachedThreshold !== void 0 ? onStartReachedThreshold : 2;\n}\nfunction onEndReachedThresholdOrDefault(onEndReachedThreshold) {\n  return onEndReachedThreshold !== null && onEndReachedThreshold !== void 0 ? onEndReachedThreshold : 2;\n}\nfunction getScrollingThreshold(threshold, visibleLength) {\n  return threshold * visibleLength / 2;\n}\nfunction scrollEventThrottleOrDefault(scrollEventThrottle) {\n  return scrollEventThrottle !== null && scrollEventThrottle !== void 0 ? scrollEventThrottle : 50;\n}\nfunction windowSizeOrDefault(windowSize) {\n  return windowSize !== null && windowSize !== void 0 ? windowSize : 21;\n}\nfunction findLastWhere(arr, predicate) {\n  for (var i = arr.length - 1; i >= 0; i--) {\n    if (predicate(arr[i])) {\n      return arr[i];\n    }\n  }\n  return null;\n}\nvar VirtualizedList = function (_StateSafePureCompone) {\n  function VirtualizedList(_props) {\n    var _this2;\n    _classCallCheck(this, VirtualizedList);\n    var _this$props$updateCel;\n    _this2 = _callSuper(this, VirtualizedList, [_props]);\n    _this2._getScrollMetrics = function () {\n      return _this2._scrollMetrics;\n    };\n    _this2._getOutermostParentListRef = function () {\n      if (_this2._isNestedWithSameOrientation()) {\n        return _this2.context.getOutermostParentListRef();\n      } else {\n        return _this2;\n      }\n    };\n    _this2._registerAsNestedChild = function (childList) {\n      _this2._nestedChildLists.add(childList.ref, childList.cellKey);\n      if (_this2._hasInteracted) {\n        childList.ref.recordInteraction();\n      }\n    };\n    _this2._unregisterAsNestedChild = function (childList) {\n      _this2._nestedChildLists.remove(childList.ref);\n    };\n    _this2._onUpdateSeparators = function (keys, newProps) {\n      keys.forEach(function (key) {\n        var ref = key != null && _this2._cellRefs[key];\n        ref && ref.updateSeparatorProps(newProps);\n      });\n    };\n    _this2._getSpacerKey = function (isVertical) {\n      return isVertical ? 'height' : 'width';\n    };\n    _this2._averageCellLength = 0;\n    _this2._cellRefs = {};\n    _this2._frames = {};\n    _this2._footerLength = 0;\n    _this2._hasTriggeredInitialScrollToIndex = false;\n    _this2._hasInteracted = false;\n    _this2._hasMore = false;\n    _this2._hasWarned = {};\n    _this2._headerLength = 0;\n    _this2._hiPriInProgress = false;\n    _this2._highestMeasuredFrameIndex = 0;\n    _this2._indicesToKeys = new Map();\n    _this2._lastFocusedCellKey = null;\n    _this2._nestedChildLists = new ChildListCollection();\n    _this2._offsetFromParentVirtualizedList = 0;\n    _this2._prevParentOffset = 0;\n    _this2._scrollMetrics = {\n      contentLength: 0,\n      dOffset: 0,\n      dt: 10,\n      offset: 0,\n      timestamp: 0,\n      velocity: 0,\n      visibleLength: 0,\n      zoomScale: 1\n    };\n    _this2._scrollRef = null;\n    _this2._sentStartForContentLength = 0;\n    _this2._sentEndForContentLength = 0;\n    _this2._totalCellLength = 0;\n    _this2._totalCellsMeasured = 0;\n    _this2._viewabilityTuples = [];\n    _this2._captureScrollRef = function (ref) {\n      _this2._scrollRef = ref;\n    };\n    _this2._defaultRenderScrollComponent = function (props) {\n      var onRefresh = props.onRefresh;\n      if (_this2._isNestedWithSameOrientation()) {\n        return React.createElement(View, props);\n      } else if (onRefresh) {\n        var _props$refreshing;\n        invariant(typeof props.refreshing === 'boolean', '`refreshing` prop must be set as a boolean in order to use `onRefresh`, but got `' + JSON.stringify((_props$refreshing = props.refreshing) !== null && _props$refreshing !== void 0 ? _props$refreshing : 'undefined') + '`');\n        return (React.createElement(ScrollView, _extends({}, props, {\n            refreshControl: props.refreshControl == null ? React.createElement(RefreshControl, {\n              refreshing: props.refreshing,\n              onRefresh: onRefresh,\n              progressViewOffset: props.progressViewOffset\n            }) : props.refreshControl\n          }))\n        );\n      } else {\n        return React.createElement(ScrollView, props);\n      }\n    };\n    _this2._onCellLayout = function (e, cellKey, index) {\n      var layout = e.nativeEvent.layout;\n      var next = {\n        offset: _this2._selectOffset(layout),\n        length: _this2._selectLength(layout),\n        index: index,\n        inLayout: true\n      };\n      var curr = _this2._frames[cellKey];\n      if (!curr || next.offset !== curr.offset || next.length !== curr.length || index !== curr.index) {\n        _this2._totalCellLength += next.length - (curr ? curr.length : 0);\n        _this2._totalCellsMeasured += curr ? 0 : 1;\n        _this2._averageCellLength = _this2._totalCellLength / _this2._totalCellsMeasured;\n        _this2._frames[cellKey] = next;\n        _this2._highestMeasuredFrameIndex = Math.max(_this2._highestMeasuredFrameIndex, index);\n        _this2._scheduleCellsToRenderUpdate();\n      } else {\n        _this2._frames[cellKey].inLayout = true;\n      }\n      _this2._triggerRemeasureForChildListsInCell(cellKey);\n      _this2._computeBlankness();\n      _this2._updateViewableItems(_this2.props, _this2.state.cellsAroundViewport);\n    };\n    _this2._onCellUnmount = function (cellKey) {\n      delete _this2._cellRefs[cellKey];\n      var curr = _this2._frames[cellKey];\n      if (curr) {\n        _this2._frames[cellKey] = _objectSpread(_objectSpread({}, curr), {}, {\n          inLayout: false\n        });\n      }\n    };\n    _this2._onLayout = function (e) {\n      if (_this2._isNestedWithSameOrientation()) {\n        _this2.measureLayoutRelativeToContainingList();\n      } else {\n        _this2._scrollMetrics.visibleLength = _this2._selectLength(e.nativeEvent.layout);\n      }\n      _this2.props.onLayout && _this2.props.onLayout(e);\n      _this2._scheduleCellsToRenderUpdate();\n      _this2._maybeCallOnEdgeReached();\n    };\n    _this2._onLayoutEmpty = function (e) {\n      _this2.props.onLayout && _this2.props.onLayout(e);\n    };\n    _this2._onLayoutFooter = function (e) {\n      _this2._triggerRemeasureForChildListsInCell(_this2._getFooterCellKey());\n      _this2._footerLength = _this2._selectLength(e.nativeEvent.layout);\n    };\n    _this2._onLayoutHeader = function (e) {\n      _this2._headerLength = _this2._selectLength(e.nativeEvent.layout);\n    };\n    _this2._onContentSizeChange = function (width, height) {\n      if (width > 0 && height > 0 && _this2.props.initialScrollIndex != null && _this2.props.initialScrollIndex > 0 && !_this2._hasTriggeredInitialScrollToIndex) {\n        if (_this2.props.contentOffset == null) {\n          if (_this2.props.initialScrollIndex < _this2.props.getItemCount(_this2.props.data)) {\n            _this2.scrollToIndex({\n              animated: false,\n              index: nullthrows(_this2.props.initialScrollIndex)\n            });\n          } else {\n            _this2.scrollToEnd({\n              animated: false\n            });\n          }\n        }\n        _this2._hasTriggeredInitialScrollToIndex = true;\n      }\n      if (_this2.props.onContentSizeChange) {\n        _this2.props.onContentSizeChange(width, height);\n      }\n      _this2._scrollMetrics.contentLength = _this2._selectLength({\n        height: height,\n        width: width\n      });\n      _this2._scheduleCellsToRenderUpdate();\n      _this2._maybeCallOnEdgeReached();\n    };\n    _this2._convertParentScrollMetrics = function (metrics) {\n      var offset = metrics.offset - _this2._offsetFromParentVirtualizedList;\n      var visibleLength = metrics.visibleLength;\n      var dOffset = offset - _this2._scrollMetrics.offset;\n      var contentLength = _this2._scrollMetrics.contentLength;\n      return {\n        visibleLength: visibleLength,\n        contentLength: contentLength,\n        offset: offset,\n        dOffset: dOffset\n      };\n    };\n    _this2._onScroll = function (e) {\n      _this2._nestedChildLists.forEach(function (childList) {\n        childList._onScroll(e);\n      });\n      if (_this2.props.onScroll) {\n        _this2.props.onScroll(e);\n      }\n      var timestamp = e.timeStamp;\n      var visibleLength = _this2._selectLength(e.nativeEvent.layoutMeasurement);\n      var contentLength = _this2._selectLength(e.nativeEvent.contentSize);\n      var offset = _this2._selectOffset(e.nativeEvent.contentOffset);\n      var dOffset = offset - _this2._scrollMetrics.offset;\n      if (_this2._isNestedWithSameOrientation()) {\n        if (_this2._scrollMetrics.contentLength === 0) {\n          return;\n        }\n        var _this$_convertParentS = _this2._convertParentScrollMetrics({\n          visibleLength: visibleLength,\n          offset: offset\n        });\n        visibleLength = _this$_convertParentS.visibleLength;\n        contentLength = _this$_convertParentS.contentLength;\n        offset = _this$_convertParentS.offset;\n        dOffset = _this$_convertParentS.dOffset;\n      }\n      var dt = _this2._scrollMetrics.timestamp ? Math.max(1, timestamp - _this2._scrollMetrics.timestamp) : 1;\n      var velocity = dOffset / dt;\n      if (dt > 500 && _this2._scrollMetrics.dt > 500 && contentLength > 5 * visibleLength && !_this2._hasWarned.perf) {\n        infoLog('VirtualizedList: You have a large list that is slow to update - make sure your ' + 'renderItem function renders components that follow React performance best practices ' + 'like PureComponent, shouldComponentUpdate, etc.', {\n          dt: dt,\n          prevDt: _this2._scrollMetrics.dt,\n          contentLength: contentLength\n        });\n        _this2._hasWarned.perf = true;\n      }\n      var zoomScale = e.nativeEvent.zoomScale < 0 ? 1 : e.nativeEvent.zoomScale;\n      _this2._scrollMetrics = {\n        contentLength: contentLength,\n        dt: dt,\n        dOffset: dOffset,\n        offset: offset,\n        timestamp: timestamp,\n        velocity: velocity,\n        visibleLength: visibleLength,\n        zoomScale: zoomScale\n      };\n      _this2._updateViewableItems(_this2.props, _this2.state.cellsAroundViewport);\n      if (!_this2.props) {\n        return;\n      }\n      _this2._maybeCallOnEdgeReached();\n      if (velocity !== 0) {\n        _this2._fillRateHelper.activate();\n      }\n      _this2._computeBlankness();\n      _this2._scheduleCellsToRenderUpdate();\n    };\n    _this2._onScrollBeginDrag = function (e) {\n      _this2._nestedChildLists.forEach(function (childList) {\n        childList._onScrollBeginDrag(e);\n      });\n      _this2._viewabilityTuples.forEach(function (tuple) {\n        tuple.viewabilityHelper.recordInteraction();\n      });\n      _this2._hasInteracted = true;\n      _this2.props.onScrollBeginDrag && _this2.props.onScrollBeginDrag(e);\n    };\n    _this2._onScrollEndDrag = function (e) {\n      _this2._nestedChildLists.forEach(function (childList) {\n        childList._onScrollEndDrag(e);\n      });\n      var velocity = e.nativeEvent.velocity;\n      if (velocity) {\n        _this2._scrollMetrics.velocity = _this2._selectOffset(velocity);\n      }\n      _this2._computeBlankness();\n      _this2.props.onScrollEndDrag && _this2.props.onScrollEndDrag(e);\n    };\n    _this2._onMomentumScrollBegin = function (e) {\n      _this2._nestedChildLists.forEach(function (childList) {\n        childList._onMomentumScrollBegin(e);\n      });\n      _this2.props.onMomentumScrollBegin && _this2.props.onMomentumScrollBegin(e);\n    };\n    _this2._onMomentumScrollEnd = function (e) {\n      _this2._nestedChildLists.forEach(function (childList) {\n        childList._onMomentumScrollEnd(e);\n      });\n      _this2._scrollMetrics.velocity = 0;\n      _this2._computeBlankness();\n      _this2.props.onMomentumScrollEnd && _this2.props.onMomentumScrollEnd(e);\n    };\n    _this2._updateCellsToRender = function () {\n      _this2._updateViewableItems(_this2.props, _this2.state.cellsAroundViewport);\n      _this2.setState(function (state, props) {\n        var cellsAroundViewport = _this2._adjustCellsAroundViewport(props, state.cellsAroundViewport);\n        var renderMask = VirtualizedList._createRenderMask(props, cellsAroundViewport, _this2._getNonViewportRenderRegions(props));\n        if (cellsAroundViewport.first === state.cellsAroundViewport.first && cellsAroundViewport.last === state.cellsAroundViewport.last && renderMask.equals(state.renderMask)) {\n          return null;\n        }\n        return {\n          cellsAroundViewport: cellsAroundViewport,\n          renderMask: renderMask\n        };\n      });\n    };\n    _this2._createViewToken = function (index, isViewable, props) {\n      var data = props.data,\n        getItem = props.getItem;\n      var item = getItem(data, index);\n      return {\n        index: index,\n        item: item,\n        key: _this2._keyExtractor(item, index, props),\n        isViewable: isViewable\n      };\n    };\n    _this2._getOffsetApprox = function (index, props) {\n      if (Number.isInteger(index)) {\n        return _this2.__getFrameMetricsApprox(index, props).offset;\n      } else {\n        var frameMetrics = _this2.__getFrameMetricsApprox(Math.floor(index), props);\n        var remainder = index - Math.floor(index);\n        return frameMetrics.offset + remainder * frameMetrics.length;\n      }\n    };\n    _this2.__getFrameMetricsApprox = function (index, props) {\n      var frame = _this2._getFrameMetrics(index, props);\n      if (frame && frame.index === index) {\n        return frame;\n      } else {\n        var data = props.data,\n          getItemCount = props.getItemCount,\n          getItemLayout = props.getItemLayout;\n        invariant(index >= 0 && index < getItemCount(data), 'Tried to get frame for out of range index ' + index);\n        invariant(!getItemLayout, 'Should not have to estimate frames when a measurement metrics function is provided');\n        return {\n          length: _this2._averageCellLength,\n          offset: _this2._averageCellLength * index\n        };\n      }\n    };\n    _this2._getFrameMetrics = function (index, props) {\n      var data = props.data,\n        getItem = props.getItem,\n        getItemCount = props.getItemCount,\n        getItemLayout = props.getItemLayout;\n      invariant(index >= 0 && index < getItemCount(data), 'Tried to get frame for out of range index ' + index);\n      var item = getItem(data, index);\n      var frame = _this2._frames[_this2._keyExtractor(item, index, props)];\n      if (!frame || frame.index !== index) {\n        if (getItemLayout) {\n          return getItemLayout(data, index);\n        }\n      }\n      return frame;\n    };\n    _this2._getNonViewportRenderRegions = function (props) {\n      if (!(_this2._lastFocusedCellKey && _this2._cellRefs[_this2._lastFocusedCellKey])) {\n        return [];\n      }\n      var lastFocusedCellRenderer = _this2._cellRefs[_this2._lastFocusedCellKey];\n      var focusedCellIndex = lastFocusedCellRenderer.props.index;\n      var itemCount = props.getItemCount(props.data);\n      if (focusedCellIndex >= itemCount || _this2._keyExtractor(props.getItem(props.data, focusedCellIndex), focusedCellIndex, props) !== _this2._lastFocusedCellKey) {\n        return [];\n      }\n      var first = focusedCellIndex;\n      var heightOfCellsBeforeFocused = 0;\n      for (var i = first - 1; i >= 0 && heightOfCellsBeforeFocused < _this2._scrollMetrics.visibleLength; i--) {\n        first--;\n        heightOfCellsBeforeFocused += _this2.__getFrameMetricsApprox(i, props).length;\n      }\n      var last = focusedCellIndex;\n      var heightOfCellsAfterFocused = 0;\n      for (var _i = last + 1; _i < itemCount && heightOfCellsAfterFocused < _this2._scrollMetrics.visibleLength; _i++) {\n        last++;\n        heightOfCellsAfterFocused += _this2.__getFrameMetricsApprox(_i, props).length;\n      }\n      return [{\n        first: first,\n        last: last\n      }];\n    };\n    _this2._checkProps(_props);\n    _this2._fillRateHelper = new FillRateHelper(_this2._getFrameMetrics);\n    _this2._updateCellsToRenderBatcher = new Batchinator(_this2._updateCellsToRender, (_this$props$updateCel = _this2.props.updateCellsBatchingPeriod) !== null && _this$props$updateCel !== void 0 ? _this$props$updateCel : 50);\n    if (_this2.props.viewabilityConfigCallbackPairs) {\n      _this2._viewabilityTuples = _this2.props.viewabilityConfigCallbackPairs.map(function (pair) {\n        return {\n          viewabilityHelper: new ViewabilityHelper(pair.viewabilityConfig),\n          onViewableItemsChanged: pair.onViewableItemsChanged\n        };\n      });\n    } else {\n      var _this$props3 = _this2.props,\n        onViewableItemsChanged = _this$props3.onViewableItemsChanged,\n        viewabilityConfig = _this$props3.viewabilityConfig;\n      if (onViewableItemsChanged) {\n        _this2._viewabilityTuples.push({\n          viewabilityHelper: new ViewabilityHelper(viewabilityConfig),\n          onViewableItemsChanged: onViewableItemsChanged\n        });\n      }\n    }\n    var initialRenderRegion = VirtualizedList._initialRenderRegion(_props);\n    _this2.state = {\n      cellsAroundViewport: initialRenderRegion,\n      renderMask: VirtualizedList._createRenderMask(_props, initialRenderRegion)\n    };\n    _this2.invertedWheelEventHandler = function (ev) {\n      var scrollOffset = _this2.props.horizontal ? ev.target.scrollLeft : ev.target.scrollTop;\n      var scrollLength = _this2.props.horizontal ? ev.target.scrollWidth : ev.target.scrollHeight;\n      var clientLength = _this2.props.horizontal ? ev.target.clientWidth : ev.target.clientHeight;\n      var isEventTargetScrollable = scrollLength > clientLength;\n      var delta = _this2.props.horizontal ? ev.deltaX || ev.wheelDeltaX : ev.deltaY || ev.wheelDeltaY;\n      var leftoverDelta = delta;\n      if (isEventTargetScrollable) {\n        leftoverDelta = delta < 0 ? Math.min(delta + scrollOffset, 0) : Math.max(delta - (scrollLength - clientLength - scrollOffset), 0);\n      }\n      var targetDelta = delta - leftoverDelta;\n      if (_this2.props.inverted && _this2._scrollRef && _this2._scrollRef.getScrollableNode) {\n        var node = _this2._scrollRef.getScrollableNode();\n        if (_this2.props.horizontal) {\n          ev.target.scrollLeft += targetDelta;\n          var nextScrollLeft = node.scrollLeft - leftoverDelta;\n          node.scrollLeft = !_this2.props.getItemLayout ? Math.min(nextScrollLeft, _this2._totalCellLength) : nextScrollLeft;\n        } else {\n          ev.target.scrollTop += targetDelta;\n          var nextScrollTop = node.scrollTop - leftoverDelta;\n          node.scrollTop = !_this2.props.getItemLayout ? Math.min(nextScrollTop, _this2._totalCellLength) : nextScrollTop;\n        }\n        ev.preventDefault();\n      }\n    };\n    return _this2;\n  }\n  _inherits(VirtualizedList, _StateSafePureCompone);\n  return _createClass(VirtualizedList, [{\n    key: \"scrollToEnd\",\n    value: function scrollToEnd(params) {\n      var animated = params ? params.animated : true;\n      var veryLast = this.props.getItemCount(this.props.data) - 1;\n      if (veryLast < 0) {\n        return;\n      }\n      var frame = this.__getFrameMetricsApprox(veryLast, this.props);\n      var offset = Math.max(0, frame.offset + frame.length + this._footerLength - this._scrollMetrics.visibleLength);\n      if (this._scrollRef == null) {\n        return;\n      }\n      if (this._scrollRef.scrollTo == null) {\n        console.warn('No scrollTo method provided. This may be because you have two nested ' + 'VirtualizedLists with the same orientation, or because you are ' + 'using a custom component that does not implement scrollTo.');\n        return;\n      }\n      this._scrollRef.scrollTo(horizontalOrDefault(this.props.horizontal) ? {\n        x: offset,\n        animated: animated\n      } : {\n        y: offset,\n        animated: animated\n      });\n    }\n  }, {\n    key: \"scrollToIndex\",\n    value: function scrollToIndex(params) {\n      var _this$props = this.props,\n        data = _this$props.data,\n        horizontal = _this$props.horizontal,\n        getItemCount = _this$props.getItemCount,\n        getItemLayout = _this$props.getItemLayout,\n        onScrollToIndexFailed = _this$props.onScrollToIndexFailed;\n      var animated = params.animated,\n        index = params.index,\n        viewOffset = params.viewOffset,\n        viewPosition = params.viewPosition;\n      invariant(index >= 0, \"scrollToIndex out of range: requested index \" + index + \" but minimum is 0\");\n      invariant(getItemCount(data) >= 1, \"scrollToIndex out of range: item length \" + getItemCount(data) + \" but minimum is 1\");\n      invariant(index < getItemCount(data), \"scrollToIndex out of range: requested index \" + index + \" is out of 0 to \" + (getItemCount(data) - 1));\n      if (!getItemLayout && index > this._highestMeasuredFrameIndex) {\n        invariant(!!onScrollToIndexFailed, 'scrollToIndex should be used in conjunction with getItemLayout or onScrollToIndexFailed, ' + 'otherwise there is no way to know the location of offscreen indices or handle failures.');\n        onScrollToIndexFailed({\n          averageItemLength: this._averageCellLength,\n          highestMeasuredFrameIndex: this._highestMeasuredFrameIndex,\n          index: index\n        });\n        return;\n      }\n      var frame = this.__getFrameMetricsApprox(Math.floor(index), this.props);\n      var offset = Math.max(0, this._getOffsetApprox(index, this.props) - (viewPosition || 0) * (this._scrollMetrics.visibleLength - frame.length)) - (viewOffset || 0);\n      if (this._scrollRef == null) {\n        return;\n      }\n      if (this._scrollRef.scrollTo == null) {\n        console.warn('No scrollTo method provided. This may be because you have two nested ' + 'VirtualizedLists with the same orientation, or because you are ' + 'using a custom component that does not implement scrollTo.');\n        return;\n      }\n      this._scrollRef.scrollTo(horizontal ? {\n        x: offset,\n        animated: animated\n      } : {\n        y: offset,\n        animated: animated\n      });\n    }\n  }, {\n    key: \"scrollToItem\",\n    value: function scrollToItem(params) {\n      var item = params.item;\n      var _this$props2 = this.props,\n        data = _this$props2.data,\n        getItem = _this$props2.getItem,\n        getItemCount = _this$props2.getItemCount;\n      var itemCount = getItemCount(data);\n      for (var _index = 0; _index < itemCount; _index++) {\n        if (getItem(data, _index) === item) {\n          this.scrollToIndex(_objectSpread(_objectSpread({}, params), {}, {\n            index: _index\n          }));\n          break;\n        }\n      }\n    }\n  }, {\n    key: \"scrollToOffset\",\n    value: function scrollToOffset(params) {\n      var animated = params.animated,\n        offset = params.offset;\n      if (this._scrollRef == null) {\n        return;\n      }\n      if (this._scrollRef.scrollTo == null) {\n        console.warn('No scrollTo method provided. This may be because you have two nested ' + 'VirtualizedLists with the same orientation, or because you are ' + 'using a custom component that does not implement scrollTo.');\n        return;\n      }\n      this._scrollRef.scrollTo(horizontalOrDefault(this.props.horizontal) ? {\n        x: offset,\n        animated: animated\n      } : {\n        y: offset,\n        animated: animated\n      });\n    }\n  }, {\n    key: \"recordInteraction\",\n    value: function recordInteraction() {\n      this._nestedChildLists.forEach(function (childList) {\n        childList.recordInteraction();\n      });\n      this._viewabilityTuples.forEach(function (t) {\n        t.viewabilityHelper.recordInteraction();\n      });\n      this._updateViewableItems(this.props, this.state.cellsAroundViewport);\n    }\n  }, {\n    key: \"flashScrollIndicators\",\n    value: function flashScrollIndicators() {\n      if (this._scrollRef == null) {\n        return;\n      }\n      this._scrollRef.flashScrollIndicators();\n    }\n  }, {\n    key: \"getScrollResponder\",\n    value: function getScrollResponder() {\n      if (this._scrollRef && this._scrollRef.getScrollResponder) {\n        return this._scrollRef.getScrollResponder();\n      }\n    }\n  }, {\n    key: \"getScrollableNode\",\n    value: function getScrollableNode() {\n      if (this._scrollRef && this._scrollRef.getScrollableNode) {\n        return this._scrollRef.getScrollableNode();\n      } else {\n        return this._scrollRef;\n      }\n    }\n  }, {\n    key: \"getScrollRef\",\n    value: function getScrollRef() {\n      if (this._scrollRef && this._scrollRef.getScrollRef) {\n        return this._scrollRef.getScrollRef();\n      } else {\n        return this._scrollRef;\n      }\n    }\n  }, {\n    key: \"_getCellKey\",\n    value: function _getCellKey() {\n      var _this$context;\n      return ((_this$context = this.context) == null ? void 0 : _this$context.cellKey) || 'rootList';\n    }\n  }, {\n    key: \"hasMore\",\n    value: function hasMore() {\n      return this._hasMore;\n    }\n  }, {\n    key: \"_checkProps\",\n    value: function _checkProps(props) {\n      var onScroll = props.onScroll,\n        windowSize = props.windowSize,\n        getItemCount = props.getItemCount,\n        data = props.data,\n        initialScrollIndex = props.initialScrollIndex;\n      invariant(!onScroll || !onScroll.__isNative, 'Components based on VirtualizedList must be wrapped with Animated.createAnimatedComponent ' + 'to support native onScroll events with useNativeDriver');\n      invariant(windowSizeOrDefault(windowSize) > 0, 'VirtualizedList: The windowSize prop must be present and set to a value greater than 0.');\n      invariant(getItemCount, 'VirtualizedList: The \"getItemCount\" prop must be provided');\n      var itemCount = getItemCount(data);\n      if (initialScrollIndex != null && !this._hasTriggeredInitialScrollToIndex && (initialScrollIndex < 0 || itemCount > 0 && initialScrollIndex >= itemCount) && !this._hasWarned.initialScrollIndex) {\n        console.warn(\"initialScrollIndex \\\"\" + initialScrollIndex + \"\\\" is not valid (list has \" + itemCount + \" items)\");\n        this._hasWarned.initialScrollIndex = true;\n      }\n      if (__DEV__ && !this._hasWarned.flexWrap) {\n        var flatStyles = StyleSheet.flatten(this.props.contentContainerStyle);\n        if (flatStyles != null && flatStyles.flexWrap === 'wrap') {\n          console.warn('`flexWrap: `wrap`` is not supported with the `VirtualizedList` components.' + 'Consider using `numColumns` with `FlatList` instead.');\n          this._hasWarned.flexWrap = true;\n        }\n      }\n    }\n  }, {\n    key: \"_adjustCellsAroundViewport\",\n    value: function _adjustCellsAroundViewport(props, cellsAroundViewport) {\n      var data = props.data,\n        getItemCount = props.getItemCount;\n      var onEndReachedThreshold = onEndReachedThresholdOrDefault(props.onEndReachedThreshold);\n      var _this$_scrollMetrics = this._scrollMetrics,\n        contentLength = _this$_scrollMetrics.contentLength,\n        offset = _this$_scrollMetrics.offset,\n        visibleLength = _this$_scrollMetrics.visibleLength;\n      var distanceFromEnd = contentLength - visibleLength - offset;\n      if (visibleLength <= 0 || contentLength <= 0) {\n        return cellsAroundViewport.last >= getItemCount(data) ? VirtualizedList._constrainToItemCount(cellsAroundViewport, props) : cellsAroundViewport;\n      }\n      var newCellsAroundViewport;\n      if (props.disableVirtualization) {\n        var renderAhead = distanceFromEnd < onEndReachedThreshold * visibleLength ? maxToRenderPerBatchOrDefault(props.maxToRenderPerBatch) : 0;\n        newCellsAroundViewport = {\n          first: 0,\n          last: Math.min(cellsAroundViewport.last + renderAhead, getItemCount(data) - 1)\n        };\n      } else {\n        if (props.initialScrollIndex && !this._scrollMetrics.offset && Math.abs(distanceFromEnd) >= Number.EPSILON) {\n          return cellsAroundViewport.last >= getItemCount(data) ? VirtualizedList._constrainToItemCount(cellsAroundViewport, props) : cellsAroundViewport;\n        }\n        newCellsAroundViewport = computeWindowedRenderLimits(props, maxToRenderPerBatchOrDefault(props.maxToRenderPerBatch), windowSizeOrDefault(props.windowSize), cellsAroundViewport, this.__getFrameMetricsApprox, this._scrollMetrics);\n        invariant(newCellsAroundViewport.last < getItemCount(data), 'computeWindowedRenderLimits() should return range in-bounds');\n      }\n      if (this._nestedChildLists.size() > 0) {\n        var childIdx = this._findFirstChildWithMore(newCellsAroundViewport.first, newCellsAroundViewport.last);\n        newCellsAroundViewport.last = childIdx !== null && childIdx !== void 0 ? childIdx : newCellsAroundViewport.last;\n      }\n      return newCellsAroundViewport;\n    }\n  }, {\n    key: \"_findFirstChildWithMore\",\n    value: function _findFirstChildWithMore(first, last) {\n      for (var ii = first; ii <= last; ii++) {\n        var cellKeyForIndex = this._indicesToKeys.get(ii);\n        if (cellKeyForIndex != null && this._nestedChildLists.anyInCell(cellKeyForIndex, function (childList) {\n          return childList.hasMore();\n        })) {\n          return ii;\n        }\n      }\n      return null;\n    }\n  }, {\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      if (this._isNestedWithSameOrientation()) {\n        this.context.registerAsNestedChild({\n          ref: this,\n          cellKey: this.context.cellKey\n        });\n      }\n      this.setupWebWheelHandler();\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      if (this._isNestedWithSameOrientation()) {\n        this.context.unregisterAsNestedChild({\n          ref: this\n        });\n      }\n      this._updateCellsToRenderBatcher.dispose({\n        abort: true\n      });\n      this._viewabilityTuples.forEach(function (tuple) {\n        tuple.viewabilityHelper.dispose();\n      });\n      this._fillRateHelper.deactivateAndFlush();\n      this.teardownWebWheelHandler();\n    }\n  }, {\n    key: \"setupWebWheelHandler\",\n    value: function setupWebWheelHandler() {\n      var _this3 = this;\n      if (this._scrollRef && this._scrollRef.getScrollableNode) {\n        this._scrollRef.getScrollableNode().addEventListener('wheel', this.invertedWheelEventHandler);\n      } else {\n        setTimeout(function () {\n          return _this3.setupWebWheelHandler();\n        }, 50);\n        return;\n      }\n    }\n  }, {\n    key: \"teardownWebWheelHandler\",\n    value: function teardownWebWheelHandler() {\n      if (this._scrollRef && this._scrollRef.getScrollableNode) {\n        this._scrollRef.getScrollableNode().removeEventListener('wheel', this.invertedWheelEventHandler);\n      }\n    }\n  }, {\n    key: \"_pushCells\",\n    value: function _pushCells(cells, stickyHeaderIndices, stickyIndicesFromProps, first, last, inversionStyle) {\n      var _this = this;\n      var _this$props4 = this.props,\n        CellRendererComponent = _this$props4.CellRendererComponent,\n        ItemSeparatorComponent = _this$props4.ItemSeparatorComponent,\n        ListHeaderComponent = _this$props4.ListHeaderComponent,\n        ListItemComponent = _this$props4.ListItemComponent,\n        data = _this$props4.data,\n        debug = _this$props4.debug,\n        getItem = _this$props4.getItem,\n        getItemCount = _this$props4.getItemCount,\n        getItemLayout = _this$props4.getItemLayout,\n        horizontal = _this$props4.horizontal,\n        renderItem = _this$props4.renderItem;\n      var stickyOffset = ListHeaderComponent ? 1 : 0;\n      var end = getItemCount(data) - 1;\n      var prevCellKey;\n      last = Math.min(end, last);\n      var _loop = function _loop() {\n        var item = getItem(data, ii);\n        var key = _this._keyExtractor(item, ii, _this.props);\n        _this._indicesToKeys.set(ii, key);\n        if (stickyIndicesFromProps.has(ii + stickyOffset)) {\n          stickyHeaderIndices.push(cells.length);\n        }\n        var shouldListenForLayout = getItemLayout == null || debug || _this._fillRateHelper.enabled();\n        cells.push(React.createElement(CellRenderer, _extends({\n          CellRendererComponent: CellRendererComponent,\n          ItemSeparatorComponent: ii < end ? ItemSeparatorComponent : undefined,\n          ListItemComponent: ListItemComponent,\n          cellKey: key,\n          horizontal: horizontal,\n          index: ii,\n          inversionStyle: inversionStyle,\n          item: item,\n          key: key,\n          prevCellKey: prevCellKey,\n          onUpdateSeparators: _this._onUpdateSeparators,\n          onCellFocusCapture: function onCellFocusCapture(e) {\n            return _this._onCellFocusCapture(key);\n          },\n          onUnmount: _this._onCellUnmount,\n          ref: function ref(_ref) {\n            _this._cellRefs[key] = _ref;\n          },\n          renderItem: renderItem\n        }, shouldListenForLayout && {\n          onCellLayout: _this._onCellLayout\n        })));\n        prevCellKey = key;\n      };\n      for (var ii = first; ii <= last; ii++) {\n        _loop();\n      }\n    }\n  }, {\n    key: \"_isNestedWithSameOrientation\",\n    value: function _isNestedWithSameOrientation() {\n      var nestedContext = this.context;\n      return !!(nestedContext && !!nestedContext.horizontal === horizontalOrDefault(this.props.horizontal));\n    }\n  }, {\n    key: \"_keyExtractor\",\n    value: function _keyExtractor(item, index, props) {\n      if (props.keyExtractor != null) {\n        return props.keyExtractor(item, index);\n      }\n      var key = defaultKeyExtractor(item, index);\n      if (key === String(index)) {\n        _usedIndexForKey = true;\n        if (item.type && item.type.displayName) {\n          _keylessItemComponentName = item.type.displayName;\n        }\n      }\n      return key;\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this4 = this;\n      this._checkProps(this.props);\n      var _this$props5 = this.props,\n        ListEmptyComponent = _this$props5.ListEmptyComponent,\n        ListFooterComponent = _this$props5.ListFooterComponent,\n        ListHeaderComponent = _this$props5.ListHeaderComponent;\n      var _this$props6 = this.props,\n        data = _this$props6.data,\n        horizontal = _this$props6.horizontal;\n      var inversionStyle = this.props.inverted ? horizontalOrDefault(this.props.horizontal) ? styles.horizontallyInverted : styles.verticallyInverted : null;\n      var cells = [];\n      var stickyIndicesFromProps = new Set(this.props.stickyHeaderIndices);\n      var stickyHeaderIndices = [];\n      if (ListHeaderComponent) {\n        if (stickyIndicesFromProps.has(0)) {\n          stickyHeaderIndices.push(0);\n        }\n        var _element = React.isValidElement(ListHeaderComponent) ? ListHeaderComponent : React.createElement(ListHeaderComponent, null);\n        cells.push(React.createElement(VirtualizedListCellContextProvider, {\n          cellKey: this._getCellKey() + '-header',\n          key: \"$header\"\n        }, React.createElement(View, {\n          onLayout: this._onLayoutHeader,\n          style: [inversionStyle, this.props.ListHeaderComponentStyle]\n        }, _element)));\n      }\n      var itemCount = this.props.getItemCount(data);\n      if (itemCount === 0 && ListEmptyComponent) {\n        var _element2 = React.isValidElement(ListEmptyComponent) ? ListEmptyComponent : React.createElement(ListEmptyComponent, null);\n        cells.push(React.createElement(VirtualizedListCellContextProvider, {\n          cellKey: this._getCellKey() + '-empty',\n          key: \"$empty\"\n        }, React.cloneElement(_element2, {\n          onLayout: function onLayout(event) {\n            _this4._onLayoutEmpty(event);\n            if (_element2.props.onLayout) {\n              _element2.props.onLayout(event);\n            }\n          },\n          style: [inversionStyle, _element2.props.style]\n        })));\n      }\n      if (itemCount > 0) {\n        _usedIndexForKey = false;\n        _keylessItemComponentName = '';\n        var spacerKey = this._getSpacerKey(!horizontal);\n        var renderRegions = this.state.renderMask.enumerateRegions();\n        var lastSpacer = findLastWhere(renderRegions, function (r) {\n          return r.isSpacer;\n        });\n        for (var _iterator = _createForOfIteratorHelperLoose(renderRegions), _step; !(_step = _iterator()).done;) {\n          var section = _step.value;\n          if (section.isSpacer) {\n            if (this.props.disableVirtualization) {\n              continue;\n            }\n            var isLastSpacer = section === lastSpacer;\n            var constrainToMeasured = isLastSpacer && !this.props.getItemLayout;\n            var last = constrainToMeasured ? clamp(section.first - 1, section.last, this._highestMeasuredFrameIndex) : section.last;\n            var firstMetrics = this.__getFrameMetricsApprox(section.first, this.props);\n            var lastMetrics = this.__getFrameMetricsApprox(last, this.props);\n            var spacerSize = lastMetrics.offset + lastMetrics.length - firstMetrics.offset;\n            cells.push(React.createElement(View, {\n              key: \"$spacer-\" + section.first,\n              style: _defineProperty({}, spacerKey, spacerSize)\n            }));\n          } else {\n            this._pushCells(cells, stickyHeaderIndices, stickyIndicesFromProps, section.first, section.last, inversionStyle);\n          }\n        }\n        if (!this._hasWarned.keys && _usedIndexForKey) {\n          console.warn('VirtualizedList: missing keys for items, make sure to specify a key or id property on each ' + 'item or provide a custom keyExtractor.', _keylessItemComponentName);\n          this._hasWarned.keys = true;\n        }\n      }\n      if (ListFooterComponent) {\n        var _element3 = React.isValidElement(ListFooterComponent) ? ListFooterComponent : React.createElement(ListFooterComponent, null);\n        cells.push(React.createElement(VirtualizedListCellContextProvider, {\n          cellKey: this._getFooterCellKey(),\n          key: \"$footer\"\n        }, React.createElement(View, {\n          onLayout: this._onLayoutFooter,\n          style: [inversionStyle, this.props.ListFooterComponentStyle]\n        }, _element3)));\n      }\n      var scrollProps = _objectSpread(_objectSpread({}, this.props), {}, {\n        onContentSizeChange: this._onContentSizeChange,\n        onLayout: this._onLayout,\n        onScroll: this._onScroll,\n        onScrollBeginDrag: this._onScrollBeginDrag,\n        onScrollEndDrag: this._onScrollEndDrag,\n        onMomentumScrollBegin: this._onMomentumScrollBegin,\n        onMomentumScrollEnd: this._onMomentumScrollEnd,\n        scrollEventThrottle: scrollEventThrottleOrDefault(this.props.scrollEventThrottle),\n        invertStickyHeaders: this.props.invertStickyHeaders !== undefined ? this.props.invertStickyHeaders : this.props.inverted,\n        stickyHeaderIndices: stickyHeaderIndices,\n        style: inversionStyle ? [inversionStyle, this.props.style] : this.props.style\n      });\n      this._hasMore = this.state.cellsAroundViewport.last < itemCount - 1;\n      var innerRet = React.createElement(VirtualizedListContextProvider, {\n        value: {\n          cellKey: null,\n          getScrollMetrics: this._getScrollMetrics,\n          horizontal: horizontalOrDefault(this.props.horizontal),\n          getOutermostParentListRef: this._getOutermostParentListRef,\n          registerAsNestedChild: this._registerAsNestedChild,\n          unregisterAsNestedChild: this._unregisterAsNestedChild\n        }\n      }, React.cloneElement((this.props.renderScrollComponent || this._defaultRenderScrollComponent)(scrollProps), {\n        ref: this._captureScrollRef\n      }, cells));\n      var ret = innerRet;\n      if (this.props.debug) {\n        return React.createElement(View, {\n          style: styles.debug\n        }, ret, this._renderDebugOverlay());\n      } else {\n        return ret;\n      }\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps) {\n      var _this$props7 = this.props,\n        data = _this$props7.data,\n        extraData = _this$props7.extraData;\n      if (data !== prevProps.data || extraData !== prevProps.extraData) {\n        this._viewabilityTuples.forEach(function (tuple) {\n          tuple.viewabilityHelper.resetViewableIndices();\n        });\n      }\n      var hiPriInProgress = this._hiPriInProgress;\n      this._scheduleCellsToRenderUpdate();\n      if (hiPriInProgress) {\n        this._hiPriInProgress = false;\n      }\n    }\n  }, {\n    key: \"_computeBlankness\",\n    value: function _computeBlankness() {\n      this._fillRateHelper.computeBlankness(this.props, this.state.cellsAroundViewport, this._scrollMetrics);\n    }\n  }, {\n    key: \"_onCellFocusCapture\",\n    value: function _onCellFocusCapture(cellKey) {\n      this._lastFocusedCellKey = cellKey;\n      this._updateCellsToRender();\n    }\n  }, {\n    key: \"_triggerRemeasureForChildListsInCell\",\n    value: function _triggerRemeasureForChildListsInCell(cellKey) {\n      this._nestedChildLists.forEachInCell(cellKey, function (childList) {\n        childList.measureLayoutRelativeToContainingList();\n      });\n    }\n  }, {\n    key: \"measureLayoutRelativeToContainingList\",\n    value: function measureLayoutRelativeToContainingList() {\n      var _this5 = this;\n      try {\n        if (!this._scrollRef) {\n          return;\n        }\n        this._scrollRef.measureLayout(this.context.getOutermostParentListRef().getScrollRef(), function (x, y, width, height) {\n          _this5._offsetFromParentVirtualizedList = _this5._selectOffset({\n            x: x,\n            y: y\n          });\n          _this5._scrollMetrics.contentLength = _this5._selectLength({\n            width: width,\n            height: height\n          });\n          var scrollMetrics = _this5._convertParentScrollMetrics(_this5.context.getScrollMetrics());\n          var metricsChanged = _this5._scrollMetrics.visibleLength !== scrollMetrics.visibleLength || _this5._scrollMetrics.offset !== scrollMetrics.offset;\n          if (metricsChanged) {\n            _this5._scrollMetrics.visibleLength = scrollMetrics.visibleLength;\n            _this5._scrollMetrics.offset = scrollMetrics.offset;\n            _this5._nestedChildLists.forEach(function (childList) {\n              childList.measureLayoutRelativeToContainingList();\n            });\n          }\n        }, function (error) {\n          console.warn(\"VirtualizedList: Encountered an error while measuring a list's\" + ' offset from its containing VirtualizedList.');\n        });\n      } catch (error) {\n        console.warn('measureLayoutRelativeToContainingList threw an error', error.stack);\n      }\n    }\n  }, {\n    key: \"_getFooterCellKey\",\n    value: function _getFooterCellKey() {\n      return this._getCellKey() + '-footer';\n    }\n  }, {\n    key: \"_renderDebugOverlay\",\n    value: function _renderDebugOverlay() {\n      var normalize = this._scrollMetrics.visibleLength / (this._scrollMetrics.contentLength || 1);\n      var framesInLayout = [];\n      var itemCount = this.props.getItemCount(this.props.data);\n      for (var ii = 0; ii < itemCount; ii++) {\n        var frame = this.__getFrameMetricsApprox(ii, this.props);\n        if (frame.inLayout) {\n          framesInLayout.push(frame);\n        }\n      }\n      var windowTop = this.__getFrameMetricsApprox(this.state.cellsAroundViewport.first, this.props).offset;\n      var frameLast = this.__getFrameMetricsApprox(this.state.cellsAroundViewport.last, this.props);\n      var windowLen = frameLast.offset + frameLast.length - windowTop;\n      var visTop = this._scrollMetrics.offset;\n      var visLen = this._scrollMetrics.visibleLength;\n      return React.createElement(View, {\n        style: [styles.debugOverlayBase, styles.debugOverlay]\n      }, framesInLayout.map(function (f, ii) {\n        return React.createElement(View, {\n          key: 'f' + ii,\n          style: [styles.debugOverlayBase, styles.debugOverlayFrame, {\n            top: f.offset * normalize,\n            height: f.length * normalize\n          }]\n        });\n      }), React.createElement(View, {\n        style: [styles.debugOverlayBase, styles.debugOverlayFrameLast, {\n          top: windowTop * normalize,\n          height: windowLen * normalize\n        }]\n      }), React.createElement(View, {\n        style: [styles.debugOverlayBase, styles.debugOverlayFrameVis, {\n          top: visTop * normalize,\n          height: visLen * normalize\n        }]\n      }));\n    }\n  }, {\n    key: \"_selectLength\",\n    value: function _selectLength(metrics) {\n      return !horizontalOrDefault(this.props.horizontal) ? metrics.height : metrics.width;\n    }\n  }, {\n    key: \"_selectOffset\",\n    value: function _selectOffset(metrics) {\n      return !horizontalOrDefault(this.props.horizontal) ? metrics.y : metrics.x;\n    }\n  }, {\n    key: \"_maybeCallOnEdgeReached\",\n    value: function _maybeCallOnEdgeReached() {\n      var _this$props8 = this.props,\n        data = _this$props8.data,\n        getItemCount = _this$props8.getItemCount,\n        onStartReached = _this$props8.onStartReached,\n        onStartReachedThreshold = _this$props8.onStartReachedThreshold,\n        onEndReached = _this$props8.onEndReached,\n        onEndReachedThreshold = _this$props8.onEndReachedThreshold,\n        initialScrollIndex = _this$props8.initialScrollIndex;\n      var _this$_scrollMetrics2 = this._scrollMetrics,\n        contentLength = _this$_scrollMetrics2.contentLength,\n        visibleLength = _this$_scrollMetrics2.visibleLength,\n        offset = _this$_scrollMetrics2.offset;\n      var distanceFromStart = offset;\n      var distanceFromEnd = contentLength - visibleLength - offset;\n      if (distanceFromStart < ON_EDGE_REACHED_EPSILON) {\n        distanceFromStart = 0;\n      }\n      if (distanceFromEnd < ON_EDGE_REACHED_EPSILON) {\n        distanceFromEnd = 0;\n      }\n      var DEFAULT_THRESHOLD_PX = 2;\n      var startThreshold = onStartReachedThreshold != null ? onStartReachedThreshold * visibleLength : DEFAULT_THRESHOLD_PX;\n      var endThreshold = onEndReachedThreshold != null ? onEndReachedThreshold * visibleLength : DEFAULT_THRESHOLD_PX;\n      var isWithinStartThreshold = distanceFromStart <= startThreshold;\n      var isWithinEndThreshold = distanceFromEnd <= endThreshold;\n      if (onEndReached && this.state.cellsAroundViewport.last === getItemCount(data) - 1 && isWithinEndThreshold && this._scrollMetrics.contentLength !== this._sentEndForContentLength) {\n        this._sentEndForContentLength = this._scrollMetrics.contentLength;\n        onEndReached({\n          distanceFromEnd: distanceFromEnd\n        });\n      } else if (onStartReached != null && this.state.cellsAroundViewport.first === 0 && isWithinStartThreshold && this._scrollMetrics.contentLength !== this._sentStartForContentLength) {\n        if (!initialScrollIndex || this._scrollMetrics.timestamp !== 0) {\n          this._sentStartForContentLength = this._scrollMetrics.contentLength;\n          onStartReached({\n            distanceFromStart: distanceFromStart\n          });\n        }\n      } else {\n        this._sentStartForContentLength = isWithinStartThreshold ? this._sentStartForContentLength : 0;\n        this._sentEndForContentLength = isWithinEndThreshold ? this._sentEndForContentLength : 0;\n      }\n    }\n  }, {\n    key: \"_scheduleCellsToRenderUpdate\",\n    value: function _scheduleCellsToRenderUpdate() {\n      var _this$state$cellsArou = this.state.cellsAroundViewport,\n        first = _this$state$cellsArou.first,\n        last = _this$state$cellsArou.last;\n      var _this$_scrollMetrics3 = this._scrollMetrics,\n        offset = _this$_scrollMetrics3.offset,\n        visibleLength = _this$_scrollMetrics3.visibleLength,\n        velocity = _this$_scrollMetrics3.velocity;\n      var itemCount = this.props.getItemCount(this.props.data);\n      var hiPri = false;\n      var onStartReachedThreshold = onStartReachedThresholdOrDefault(this.props.onStartReachedThreshold);\n      var onEndReachedThreshold = onEndReachedThresholdOrDefault(this.props.onEndReachedThreshold);\n      if (first > 0) {\n        var distTop = offset - this.__getFrameMetricsApprox(first, this.props).offset;\n        hiPri = distTop < 0 || velocity < -2 && distTop < getScrollingThreshold(onStartReachedThreshold, visibleLength);\n      }\n      if (!hiPri && last >= 0 && last < itemCount - 1) {\n        var distBottom = this.__getFrameMetricsApprox(last, this.props).offset - (offset + visibleLength);\n        hiPri = distBottom < 0 || velocity > 2 && distBottom < getScrollingThreshold(onEndReachedThreshold, visibleLength);\n      }\n      if (hiPri && (this._averageCellLength || this.props.getItemLayout) && !this._hiPriInProgress) {\n        this._hiPriInProgress = true;\n        this._updateCellsToRenderBatcher.dispose({\n          abort: true\n        });\n        this._updateCellsToRender();\n        return;\n      } else {\n        this._updateCellsToRenderBatcher.schedule();\n      }\n    }\n  }, {\n    key: \"_updateViewableItems\",\n    value: function _updateViewableItems(props, cellsAroundViewport) {\n      var _this6 = this;\n      this._viewabilityTuples.forEach(function (tuple) {\n        tuple.viewabilityHelper.onUpdate(props, _this6._scrollMetrics.offset, _this6._scrollMetrics.visibleLength, _this6._getFrameMetrics, _this6._createViewToken, tuple.onViewableItemsChanged, cellsAroundViewport);\n      });\n    }\n  }], [{\n    key: \"_createRenderMask\",\n    value: function _createRenderMask(props, cellsAroundViewport, additionalRegions) {\n      var itemCount = props.getItemCount(props.data);\n      invariant(cellsAroundViewport.first >= 0 && cellsAroundViewport.last >= cellsAroundViewport.first - 1 && cellsAroundViewport.last < itemCount, \"Invalid cells around viewport \\\"[\" + cellsAroundViewport.first + \", \" + cellsAroundViewport.last + \"]\\\" was passed to VirtualizedList._createRenderMask\");\n      var renderMask = new CellRenderMask(itemCount);\n      if (itemCount > 0) {\n        var allRegions = [cellsAroundViewport].concat(_toConsumableArray(additionalRegions !== null && additionalRegions !== void 0 ? additionalRegions : []));\n        for (var _i2 = 0, _allRegions = allRegions; _i2 < _allRegions.length; _i2++) {\n          var region = _allRegions[_i2];\n          renderMask.addCells(region);\n        }\n        if (props.initialScrollIndex == null || props.initialScrollIndex <= 0) {\n          var initialRegion = VirtualizedList._initialRenderRegion(props);\n          renderMask.addCells(initialRegion);\n        }\n        var stickyIndicesSet = new Set(props.stickyHeaderIndices);\n        VirtualizedList._ensureClosestStickyHeader(props, stickyIndicesSet, renderMask, cellsAroundViewport.first);\n      }\n      return renderMask;\n    }\n  }, {\n    key: \"_initialRenderRegion\",\n    value: function _initialRenderRegion(props) {\n      var _props$initialScrollI;\n      var itemCount = props.getItemCount(props.data);\n      var firstCellIndex = Math.max(0, Math.min(itemCount - 1, Math.floor((_props$initialScrollI = props.initialScrollIndex) !== null && _props$initialScrollI !== void 0 ? _props$initialScrollI : 0)));\n      var lastCellIndex = Math.min(itemCount, firstCellIndex + initialNumToRenderOrDefault(props.initialNumToRender)) - 1;\n      return {\n        first: firstCellIndex,\n        last: lastCellIndex\n      };\n    }\n  }, {\n    key: \"_ensureClosestStickyHeader\",\n    value: function _ensureClosestStickyHeader(props, stickyIndicesSet, renderMask, cellIdx) {\n      var stickyOffset = props.ListHeaderComponent ? 1 : 0;\n      for (var itemIdx = cellIdx - 1; itemIdx >= 0; itemIdx--) {\n        if (stickyIndicesSet.has(itemIdx + stickyOffset)) {\n          renderMask.addCells({\n            first: itemIdx,\n            last: itemIdx\n          });\n          break;\n        }\n      }\n    }\n  }, {\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(newProps, prevState) {\n      var itemCount = newProps.getItemCount(newProps.data);\n      if (itemCount === prevState.renderMask.numCells()) {\n        return prevState;\n      }\n      var constrainedCells = VirtualizedList._constrainToItemCount(prevState.cellsAroundViewport, newProps);\n      return {\n        cellsAroundViewport: constrainedCells,\n        renderMask: VirtualizedList._createRenderMask(newProps, constrainedCells)\n      };\n    }\n  }, {\n    key: \"_constrainToItemCount\",\n    value: function _constrainToItemCount(cells, props) {\n      var itemCount = props.getItemCount(props.data);\n      var last = Math.min(itemCount - 1, cells.last);\n      var maxToRenderPerBatch = maxToRenderPerBatchOrDefault(props.maxToRenderPerBatch);\n      return {\n        first: clamp(0, itemCount - 1 - maxToRenderPerBatch, cells.first),\n        last: last\n      };\n    }\n  }]);\n}(StateSafePureComponent);\nVirtualizedList.contextType = VirtualizedListContext;\nvar styles = StyleSheet.create({\n  verticallyInverted: {\n    transform: 'scaleY(-1)'\n  },\n  horizontallyInverted: {\n    transform: 'scaleX(-1)'\n  },\n  debug: {\n    flex: 1\n  },\n  debugOverlayBase: {\n    position: 'absolute',\n    top: 0,\n    right: 0\n  },\n  debugOverlay: {\n    bottom: 0,\n    width: 20,\n    borderColor: 'blue',\n    borderWidth: 1\n  },\n  debugOverlayFrame: {\n    left: 0,\n    backgroundColor: 'orange'\n  },\n  debugOverlayFrameLast: {\n    left: 0,\n    borderColor: 'green',\n    borderWidth: 2\n  },\n  debugOverlayFrameVis: {\n    left: 0,\n    borderColor: 'red',\n    borderWidth: 2\n  }\n});\nexport default VirtualizedList;", "map": {"version": 3, "names": ["_createForOfIteratorHelperLoose", "_extends", "_objectSpread", "RefreshControl", "ScrollView", "View", "StyleSheet", "findNodeHandle", "<PERSON><PERSON><PERSON>", "clamp", "infoLog", "CellRenderMask", "ChildListCollection", "FillRateHelper", "StateSafePureComponent", "ViewabilityHelper", "<PERSON><PERSON><PERSON><PERSON>", "VirtualizedListCellContextProvider", "VirtualizedListContext", "VirtualizedListContextProvider", "computeWindowedRenderLimits", "keyExtractor", "defaultKeyExtractor", "invariant", "nullthrows", "React", "__DEV__", "process", "env", "NODE_ENV", "ON_EDGE_REACHED_EPSILON", "_usedIndexForKey", "_keylessItemComponentName", "horizontalOrDefault", "horizontal", "initialNumToRenderOrDefault", "initialNumToRender", "maxToRenderPerBatchOrDefault", "maxToRenderPerBatch", "onStartReachedThresholdOrDefault", "onStartReachedThreshold", "onEndReachedThresholdOrDefault", "onEndReachedThreshold", "getScrollingThreshold", "threshold", "<PERSON><PERSON><PERSON><PERSON>", "scrollEventThrottleOrDefault", "scrollEventThrottle", "windowSizeOrDefault", "windowSize", "findLastWhere", "arr", "predicate", "i", "length", "VirtualizedList", "_StateSafePureCompone", "_props", "_this2", "_classCallCheck", "_this$props$updateCel", "_callSuper", "_getScrollMetrics", "_scrollMetrics", "_getOutermostParentListRef", "_isNestedWithSameOrientation", "context", "getOutermostParentListRef", "_registerAsNestedChild", "childList", "_nestedChildLists", "add", "ref", "cellKey", "_hasInteracted", "recordInteraction", "_unregisterAsNestedChild", "remove", "_onUpdateSeparators", "keys", "newProps", "for<PERSON>ach", "key", "_cellRefs", "updateSeparatorProps", "_getSpace<PERSON><PERSON><PERSON>", "isVertical", "_averageCellLength", "_frames", "_footer<PERSON><PERSON>th", "_hasTriggeredInitialScrollToIndex", "_hasMore", "_hasWarned", "_headerLength", "_hiPriInProgress", "_highestMeasuredFrameIndex", "_indicesToKeys", "Map", "_lastFocused<PERSON>ell<PERSON>ey", "_offsetFromParentVirtualizedList", "_prevParentOffset", "contentLength", "dOffset", "dt", "offset", "timestamp", "velocity", "zoomScale", "_scrollRef", "_sentStartForContentLength", "_sentEndForContentLength", "_totalCellLength", "_totalCellsMeasured", "_viewabilityTuples", "_captureScrollRef", "_defaultRenderScrollComponent", "props", "onRefresh", "createElement", "_props$refreshing", "refreshing", "JSON", "stringify", "refreshControl", "progressViewOffset", "_onCellLayout", "e", "index", "layout", "nativeEvent", "next", "_selectOffset", "_selectLength", "inLayout", "curr", "Math", "max", "_scheduleCellsToRenderUpdate", "_triggerRemeasureForChildListsInCell", "_computeBlankness", "_updateViewableItems", "state", "cellsAroundViewport", "_onCellUnmount", "_onLayout", "measureLayoutRelativeToContainingList", "onLayout", "_maybe<PERSON>all<PERSON>n<PERSON>dgeReached", "_onLayoutEmpty", "_onLayoutFooter", "_getFooter<PERSON><PERSON><PERSON><PERSON>", "_onLayoutHeader", "_onContentSizeChange", "width", "height", "initialScrollIndex", "contentOffset", "getItemCount", "data", "scrollToIndex", "animated", "scrollToEnd", "onContentSizeChange", "_convertParentScrollMetrics", "metrics", "_onScroll", "onScroll", "timeStamp", "layoutMeasurement", "contentSize", "_this$_convertParentS", "perf", "prevDt", "_fillRate<PERSON><PERSON><PERSON>", "activate", "_onScrollBeginDrag", "tuple", "viewabilityHelper", "onScrollBeginDrag", "_onScrollEndDrag", "onScrollEndDrag", "_onMomentumScrollBegin", "onMomentumScrollBegin", "_onMomentumScrollEnd", "onMomentumScrollEnd", "_updateCellsToRender", "setState", "_adjustCellsAroundViewport", "renderMask", "_createRenderMask", "_getNonViewportRenderRegions", "first", "last", "equals", "_createViewToken", "isViewable", "getItem", "item", "_keyExtractor", "_getOffsetApprox", "Number", "isInteger", "__getFrameMetricsApprox", "frameMetrics", "floor", "remainder", "frame", "_getFrameMetrics", "getItemLayout", "lastFoc<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "focusedCellIndex", "itemCount", "heightOfCellsBeforeFocused", "heightOfCellsAfterFocused", "_i", "_checkProps", "_updateCellsToRenderBatcher", "updateCellsBatchingPeriod", "viewabilityConfigCallbackPairs", "map", "pair", "viewabilityConfig", "onViewableItemsChanged", "_this$props3", "push", "initialRenderRegion", "_initialRenderRegion", "invertedWheelEventHandler", "ev", "scrollOffset", "target", "scrollLeft", "scrollTop", "<PERSON><PERSON><PERSON><PERSON>", "scrollWidth", "scrollHeight", "clientLength", "clientWidth", "clientHeight", "isEventTargetScrollable", "delta", "deltaX", "wheelDeltaX", "deltaY", "wheelDeltaY", "leftoverDel<PERSON>", "min", "targetDel<PERSON>", "inverted", "getScrollableNode", "node", "nextScrollLeft", "nextScrollTop", "preventDefault", "_inherits", "_createClass", "value", "params", "veryLast", "scrollTo", "console", "warn", "x", "y", "_this$props", "onScrollToIndexFailed", "viewOffset", "viewPosition", "averageItemLength", "highestMeasuredFrameIndex", "scrollToItem", "_this$props2", "_index", "scrollToOffset", "t", "flashScrollIndicators", "getScrollResponder", "getScrollRef", "_get<PERSON><PERSON><PERSON><PERSON>", "_this$context", "hasMore", "__isNative", "flexWrap", "flatStyles", "flatten", "contentContainerStyle", "_this$_scrollMetrics", "distanceFromEnd", "_constrainToItemCount", "newCellsAroundViewport", "disableVirtualization", "renderAhead", "abs", "EPSILON", "size", "childIdx", "_findFirstChildWithMore", "ii", "cellKeyForIndex", "get", "anyInCell", "componentDidMount", "registerAsNestedChild", "setupWebWheelHandler", "componentWillUnmount", "unregisterAsNestedChild", "dispose", "abort", "deactivateAndFlush", "teardownWebWheelHandler", "_this3", "addEventListener", "setTimeout", "removeEventListener", "_pushCells", "cells", "stickyHeaderIndices", "stickyIndicesFromProps", "inversionStyle", "_this", "_this$props4", "CellRendererComponent", "ItemSeparatorComponent", "ListHeaderComponent", "ListItemComponent", "debug", "renderItem", "stickyOffset", "end", "prevCell<PERSON>ey", "_loop", "set", "has", "shouldListenForLayout", "enabled", "undefined", "onUpdateSeparators", "onCellFocusCapture", "_onCellFocusCapture", "onUnmount", "_ref", "onCellLayout", "nested<PERSON><PERSON><PERSON><PERSON>", "String", "type", "displayName", "render", "_this4", "_this$props5", "ListEmptyComponent", "ListFooterComponent", "_this$props6", "styles", "horizontallyInverted", "verticallyInverted", "Set", "_element", "isValidElement", "style", "ListHeaderComponentStyle", "_element2", "cloneElement", "event", "<PERSON><PERSON><PERSON><PERSON>", "renderRegions", "enumerateRegions", "lastSpacer", "r", "isSpacer", "_iterator", "_step", "done", "section", "isLastSpacer", "constrainToMeasured", "firstMetrics", "lastMetrics", "spacerSize", "_defineProperty", "_element3", "ListFooterComponentStyle", "scrollProps", "invertStickyHeaders", "innerRet", "getScrollMetrics", "renderScrollComponent", "ret", "_renderDebugOverlay", "componentDidUpdate", "prevProps", "_this$props7", "extraData", "resetViewableIndices", "hiPriInProgress", "computeBlankness", "forEachInCell", "_this5", "measureLayout", "scrollMetrics", "metricsChanged", "error", "stack", "normalize", "framesInLayout", "windowTop", "frameLast", "windowLen", "visTop", "visLen", "debugOverlayBase", "debugOverlay", "f", "debugOverlayFrame", "top", "debugOverlayFrameLast", "debugOverlayFrameVis", "_this$props8", "onStartReached", "onEndReached", "_this$_scrollMetrics2", "distanceFromStart", "DEFAULT_THRESHOLD_PX", "startThreshold", "endThreshold", "isWithinStartThreshold", "isWithinEndThreshold", "_this$state$cellsArou", "_this$_scrollMetrics3", "<PERSON><PERSON><PERSON>", "distTop", "distBottom", "schedule", "_this6", "onUpdate", "additionalRegions", "allRegions", "concat", "_toConsumableArray", "_i2", "_allRegions", "region", "add<PERSON>ells", "initialRegion", "stickyIndicesSet", "_ensureClosestStickyHeader", "_props$initialScrollI", "firstCellIndex", "lastCellIndex", "cellIdx", "itemIdx", "getDerivedStateFromProps", "prevState", "num<PERSON>ells", "constrained<PERSON><PERSON><PERSON>", "contextType", "create", "transform", "flex", "position", "right", "bottom", "borderColor", "borderWidth", "left", "backgroundColor"], "sources": ["E:/CryptoSignalsApp/node_modules/react-native-web/dist/vendor/react-native/VirtualizedList/index.js"], "sourcesContent": ["import _createForOfIteratorHelperLoose from \"@babel/runtime/helpers/createForOfIteratorHelperLoose\";\nimport _extends from \"@babel/runtime/helpers/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/objectSpread2\";\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\nimport RefreshControl from '../../../exports/RefreshControl';\nimport ScrollView from '../../../exports/ScrollView';\nimport View from '../../../exports/View';\nimport StyleSheet from '../../../exports/StyleSheet';\nimport findNodeHandle from '../../../exports/findNodeHandle';\nimport Batchinator from '../Batchinator';\nimport clamp from '../Utilities/clamp';\nimport infoLog from '../infoLog';\nimport { CellRenderMask } from './CellRenderMask';\nimport ChildListCollection from './ChildListCollection';\nimport FillRateHelper from '../FillRateHelper';\nimport StateSafePureComponent from './StateSafePureComponent';\nimport ViewabilityHelper from '../ViewabilityHelper';\nimport CellRenderer from './VirtualizedListCellRenderer';\nimport { VirtualizedListCellContextProvider, VirtualizedListContext, VirtualizedListContextProvider } from './VirtualizedListContext.js';\nimport { computeWindowedRenderLimits, keyExtractor as defaultKeyExtractor } from '../VirtualizeUtils';\nimport invariant from 'fbjs/lib/invariant';\nimport nullthrows from 'nullthrows';\nimport * as React from 'react';\nvar __DEV__ = process.env.NODE_ENV !== 'production';\nvar ON_EDGE_REACHED_EPSILON = 0.001;\nvar _usedIndexForKey = false;\nvar _keylessItemComponentName = '';\n/**\n * Default Props Helper Functions\n * Use the following helper functions for default values\n */\n\n// horizontalOrDefault(this.props.horizontal)\nfunction horizontalOrDefault(horizontal) {\n  return horizontal !== null && horizontal !== void 0 ? horizontal : false;\n}\n\n// initialNumToRenderOrDefault(this.props.initialNumToRender)\nfunction initialNumToRenderOrDefault(initialNumToRender) {\n  return initialNumToRender !== null && initialNumToRender !== void 0 ? initialNumToRender : 10;\n}\n\n// maxToRenderPerBatchOrDefault(this.props.maxToRenderPerBatch)\nfunction maxToRenderPerBatchOrDefault(maxToRenderPerBatch) {\n  return maxToRenderPerBatch !== null && maxToRenderPerBatch !== void 0 ? maxToRenderPerBatch : 10;\n}\n\n// onStartReachedThresholdOrDefault(this.props.onStartReachedThreshold)\nfunction onStartReachedThresholdOrDefault(onStartReachedThreshold) {\n  return onStartReachedThreshold !== null && onStartReachedThreshold !== void 0 ? onStartReachedThreshold : 2;\n}\n\n// onEndReachedThresholdOrDefault(this.props.onEndReachedThreshold)\nfunction onEndReachedThresholdOrDefault(onEndReachedThreshold) {\n  return onEndReachedThreshold !== null && onEndReachedThreshold !== void 0 ? onEndReachedThreshold : 2;\n}\n\n// getScrollingThreshold(visibleLength, onEndReachedThreshold)\nfunction getScrollingThreshold(threshold, visibleLength) {\n  return threshold * visibleLength / 2;\n}\n\n// scrollEventThrottleOrDefault(this.props.scrollEventThrottle)\nfunction scrollEventThrottleOrDefault(scrollEventThrottle) {\n  return scrollEventThrottle !== null && scrollEventThrottle !== void 0 ? scrollEventThrottle : 50;\n}\n\n// windowSizeOrDefault(this.props.windowSize)\nfunction windowSizeOrDefault(windowSize) {\n  return windowSize !== null && windowSize !== void 0 ? windowSize : 21;\n}\nfunction findLastWhere(arr, predicate) {\n  for (var i = arr.length - 1; i >= 0; i--) {\n    if (predicate(arr[i])) {\n      return arr[i];\n    }\n  }\n  return null;\n}\n\n/**\n * Base implementation for the more convenient [`<FlatList>`](https://reactnative.dev/docs/flatlist)\n * and [`<SectionList>`](https://reactnative.dev/docs/sectionlist) components, which are also better\n * documented. In general, this should only really be used if you need more flexibility than\n * `FlatList` provides, e.g. for use with immutable data instead of plain arrays.\n *\n * Virtualization massively improves memory consumption and performance of large lists by\n * maintaining a finite render window of active items and replacing all items outside of the render\n * window with appropriately sized blank space. The window adapts to scrolling behavior, and items\n * are rendered incrementally with low-pri (after any running interactions) if they are far from the\n * visible area, or with hi-pri otherwise to minimize the potential of seeing blank space.\n *\n * Some caveats:\n *\n * - Internal state is not preserved when content scrolls out of the render window. Make sure all\n *   your data is captured in the item data or external stores like Flux, Redux, or Relay.\n * - This is a `PureComponent` which means that it will not re-render if `props` remain shallow-\n *   equal. Make sure that everything your `renderItem` function depends on is passed as a prop\n *   (e.g. `extraData`) that is not `===` after updates, otherwise your UI may not update on\n *   changes. This includes the `data` prop and parent component state.\n * - In order to constrain memory and enable smooth scrolling, content is rendered asynchronously\n *   offscreen. This means it's possible to scroll faster than the fill rate ands momentarily see\n *   blank content. This is a tradeoff that can be adjusted to suit the needs of each application,\n *   and we are working on improving it behind the scenes.\n * - By default, the list looks for a `key` or `id` prop on each item and uses that for the React key.\n *   Alternatively, you can provide a custom `keyExtractor` prop.\n * - As an effort to remove defaultProps, use helper functions when referencing certain props\n *\n */\nclass VirtualizedList extends StateSafePureComponent {\n  // scrollToEnd may be janky without getItemLayout prop\n  scrollToEnd(params) {\n    var animated = params ? params.animated : true;\n    var veryLast = this.props.getItemCount(this.props.data) - 1;\n    if (veryLast < 0) {\n      return;\n    }\n    var frame = this.__getFrameMetricsApprox(veryLast, this.props);\n    var offset = Math.max(0, frame.offset + frame.length + this._footerLength - this._scrollMetrics.visibleLength);\n    if (this._scrollRef == null) {\n      return;\n    }\n    if (this._scrollRef.scrollTo == null) {\n      console.warn('No scrollTo method provided. This may be because you have two nested ' + 'VirtualizedLists with the same orientation, or because you are ' + 'using a custom component that does not implement scrollTo.');\n      return;\n    }\n    this._scrollRef.scrollTo(horizontalOrDefault(this.props.horizontal) ? {\n      x: offset,\n      animated\n    } : {\n      y: offset,\n      animated\n    });\n  }\n\n  // scrollToIndex may be janky without getItemLayout prop\n  scrollToIndex(params) {\n    var _this$props = this.props,\n      data = _this$props.data,\n      horizontal = _this$props.horizontal,\n      getItemCount = _this$props.getItemCount,\n      getItemLayout = _this$props.getItemLayout,\n      onScrollToIndexFailed = _this$props.onScrollToIndexFailed;\n    var animated = params.animated,\n      index = params.index,\n      viewOffset = params.viewOffset,\n      viewPosition = params.viewPosition;\n    invariant(index >= 0, \"scrollToIndex out of range: requested index \" + index + \" but minimum is 0\");\n    invariant(getItemCount(data) >= 1, \"scrollToIndex out of range: item length \" + getItemCount(data) + \" but minimum is 1\");\n    invariant(index < getItemCount(data), \"scrollToIndex out of range: requested index \" + index + \" is out of 0 to \" + (getItemCount(data) - 1));\n    if (!getItemLayout && index > this._highestMeasuredFrameIndex) {\n      invariant(!!onScrollToIndexFailed, 'scrollToIndex should be used in conjunction with getItemLayout or onScrollToIndexFailed, ' + 'otherwise there is no way to know the location of offscreen indices or handle failures.');\n      onScrollToIndexFailed({\n        averageItemLength: this._averageCellLength,\n        highestMeasuredFrameIndex: this._highestMeasuredFrameIndex,\n        index\n      });\n      return;\n    }\n    var frame = this.__getFrameMetricsApprox(Math.floor(index), this.props);\n    var offset = Math.max(0, this._getOffsetApprox(index, this.props) - (viewPosition || 0) * (this._scrollMetrics.visibleLength - frame.length)) - (viewOffset || 0);\n    if (this._scrollRef == null) {\n      return;\n    }\n    if (this._scrollRef.scrollTo == null) {\n      console.warn('No scrollTo method provided. This may be because you have two nested ' + 'VirtualizedLists with the same orientation, or because you are ' + 'using a custom component that does not implement scrollTo.');\n      return;\n    }\n    this._scrollRef.scrollTo(horizontal ? {\n      x: offset,\n      animated\n    } : {\n      y: offset,\n      animated\n    });\n  }\n\n  // scrollToItem may be janky without getItemLayout prop. Required linear scan through items -\n  // use scrollToIndex instead if possible.\n  scrollToItem(params) {\n    var item = params.item;\n    var _this$props2 = this.props,\n      data = _this$props2.data,\n      getItem = _this$props2.getItem,\n      getItemCount = _this$props2.getItemCount;\n    var itemCount = getItemCount(data);\n    for (var _index = 0; _index < itemCount; _index++) {\n      if (getItem(data, _index) === item) {\n        this.scrollToIndex(_objectSpread(_objectSpread({}, params), {}, {\n          index: _index\n        }));\n        break;\n      }\n    }\n  }\n\n  /**\n   * Scroll to a specific content pixel offset in the list.\n   *\n   * Param `offset` expects the offset to scroll to.\n   * In case of `horizontal` is true, the offset is the x-value,\n   * in any other case the offset is the y-value.\n   *\n   * Param `animated` (`true` by default) defines whether the list\n   * should do an animation while scrolling.\n   */\n  scrollToOffset(params) {\n    var animated = params.animated,\n      offset = params.offset;\n    if (this._scrollRef == null) {\n      return;\n    }\n    if (this._scrollRef.scrollTo == null) {\n      console.warn('No scrollTo method provided. This may be because you have two nested ' + 'VirtualizedLists with the same orientation, or because you are ' + 'using a custom component that does not implement scrollTo.');\n      return;\n    }\n    this._scrollRef.scrollTo(horizontalOrDefault(this.props.horizontal) ? {\n      x: offset,\n      animated\n    } : {\n      y: offset,\n      animated\n    });\n  }\n  recordInteraction() {\n    this._nestedChildLists.forEach(childList => {\n      childList.recordInteraction();\n    });\n    this._viewabilityTuples.forEach(t => {\n      t.viewabilityHelper.recordInteraction();\n    });\n    this._updateViewableItems(this.props, this.state.cellsAroundViewport);\n  }\n  flashScrollIndicators() {\n    if (this._scrollRef == null) {\n      return;\n    }\n    this._scrollRef.flashScrollIndicators();\n  }\n\n  /**\n   * Provides a handle to the underlying scroll responder.\n   * Note that `this._scrollRef` might not be a `ScrollView`, so we\n   * need to check that it responds to `getScrollResponder` before calling it.\n   */\n  getScrollResponder() {\n    if (this._scrollRef && this._scrollRef.getScrollResponder) {\n      return this._scrollRef.getScrollResponder();\n    }\n  }\n  getScrollableNode() {\n    if (this._scrollRef && this._scrollRef.getScrollableNode) {\n      return this._scrollRef.getScrollableNode();\n    } else {\n      return this._scrollRef;\n    }\n  }\n  getScrollRef() {\n    if (this._scrollRef && this._scrollRef.getScrollRef) {\n      return this._scrollRef.getScrollRef();\n    } else {\n      return this._scrollRef;\n    }\n  }\n  _getCellKey() {\n    var _this$context;\n    return ((_this$context = this.context) == null ? void 0 : _this$context.cellKey) || 'rootList';\n  }\n\n  // $FlowFixMe[missing-local-annot]\n\n  hasMore() {\n    return this._hasMore;\n  }\n\n  // $FlowFixMe[missing-local-annot]\n\n  // REACT-NATIVE-WEB patch to preserve during future RN merges: Support inverted wheel scroller.\n\n  constructor(_props) {\n    var _this$props$updateCel;\n    super(_props);\n    this._getScrollMetrics = () => {\n      return this._scrollMetrics;\n    };\n    this._getOutermostParentListRef = () => {\n      if (this._isNestedWithSameOrientation()) {\n        return this.context.getOutermostParentListRef();\n      } else {\n        return this;\n      }\n    };\n    this._registerAsNestedChild = childList => {\n      this._nestedChildLists.add(childList.ref, childList.cellKey);\n      if (this._hasInteracted) {\n        childList.ref.recordInteraction();\n      }\n    };\n    this._unregisterAsNestedChild = childList => {\n      this._nestedChildLists.remove(childList.ref);\n    };\n    this._onUpdateSeparators = (keys, newProps) => {\n      keys.forEach(key => {\n        var ref = key != null && this._cellRefs[key];\n        ref && ref.updateSeparatorProps(newProps);\n      });\n    };\n    this._getSpacerKey = isVertical => isVertical ? 'height' : 'width';\n    this._averageCellLength = 0;\n    this._cellRefs = {};\n    this._frames = {};\n    this._footerLength = 0;\n    this._hasTriggeredInitialScrollToIndex = false;\n    this._hasInteracted = false;\n    this._hasMore = false;\n    this._hasWarned = {};\n    this._headerLength = 0;\n    this._hiPriInProgress = false;\n    this._highestMeasuredFrameIndex = 0;\n    this._indicesToKeys = new Map();\n    this._lastFocusedCellKey = null;\n    this._nestedChildLists = new ChildListCollection();\n    this._offsetFromParentVirtualizedList = 0;\n    this._prevParentOffset = 0;\n    this._scrollMetrics = {\n      contentLength: 0,\n      dOffset: 0,\n      dt: 10,\n      offset: 0,\n      timestamp: 0,\n      velocity: 0,\n      visibleLength: 0,\n      zoomScale: 1\n    };\n    this._scrollRef = null;\n    this._sentStartForContentLength = 0;\n    this._sentEndForContentLength = 0;\n    this._totalCellLength = 0;\n    this._totalCellsMeasured = 0;\n    this._viewabilityTuples = [];\n    this._captureScrollRef = ref => {\n      this._scrollRef = ref;\n    };\n    this._defaultRenderScrollComponent = props => {\n      var onRefresh = props.onRefresh;\n      if (this._isNestedWithSameOrientation()) {\n        // $FlowFixMe[prop-missing] - Typing ReactNativeComponent revealed errors\n        return /*#__PURE__*/React.createElement(View, props);\n      } else if (onRefresh) {\n        var _props$refreshing;\n        invariant(typeof props.refreshing === 'boolean', '`refreshing` prop must be set as a boolean in order to use `onRefresh`, but got `' + JSON.stringify((_props$refreshing = props.refreshing) !== null && _props$refreshing !== void 0 ? _props$refreshing : 'undefined') + '`');\n        return (\n          /*#__PURE__*/\n          // $FlowFixMe[prop-missing] Invalid prop usage\n          // $FlowFixMe[incompatible-use]\n          React.createElement(ScrollView, _extends({}, props, {\n            refreshControl: props.refreshControl == null ? /*#__PURE__*/React.createElement(RefreshControl\n            // $FlowFixMe[incompatible-type]\n            , {\n              refreshing: props.refreshing,\n              onRefresh: onRefresh,\n              progressViewOffset: props.progressViewOffset\n            }) : props.refreshControl\n          }))\n        );\n      } else {\n        // $FlowFixMe[prop-missing] Invalid prop usage\n        // $FlowFixMe[incompatible-use]\n        return /*#__PURE__*/React.createElement(ScrollView, props);\n      }\n    };\n    this._onCellLayout = (e, cellKey, index) => {\n      var layout = e.nativeEvent.layout;\n      var next = {\n        offset: this._selectOffset(layout),\n        length: this._selectLength(layout),\n        index,\n        inLayout: true\n      };\n      var curr = this._frames[cellKey];\n      if (!curr || next.offset !== curr.offset || next.length !== curr.length || index !== curr.index) {\n        this._totalCellLength += next.length - (curr ? curr.length : 0);\n        this._totalCellsMeasured += curr ? 0 : 1;\n        this._averageCellLength = this._totalCellLength / this._totalCellsMeasured;\n        this._frames[cellKey] = next;\n        this._highestMeasuredFrameIndex = Math.max(this._highestMeasuredFrameIndex, index);\n        this._scheduleCellsToRenderUpdate();\n      } else {\n        this._frames[cellKey].inLayout = true;\n      }\n      this._triggerRemeasureForChildListsInCell(cellKey);\n      this._computeBlankness();\n      this._updateViewableItems(this.props, this.state.cellsAroundViewport);\n    };\n    this._onCellUnmount = cellKey => {\n      delete this._cellRefs[cellKey];\n      var curr = this._frames[cellKey];\n      if (curr) {\n        this._frames[cellKey] = _objectSpread(_objectSpread({}, curr), {}, {\n          inLayout: false\n        });\n      }\n    };\n    this._onLayout = e => {\n      if (this._isNestedWithSameOrientation()) {\n        // Need to adjust our scroll metrics to be relative to our containing\n        // VirtualizedList before we can make claims about list item viewability\n        this.measureLayoutRelativeToContainingList();\n      } else {\n        this._scrollMetrics.visibleLength = this._selectLength(e.nativeEvent.layout);\n      }\n      this.props.onLayout && this.props.onLayout(e);\n      this._scheduleCellsToRenderUpdate();\n      this._maybeCallOnEdgeReached();\n    };\n    this._onLayoutEmpty = e => {\n      this.props.onLayout && this.props.onLayout(e);\n    };\n    this._onLayoutFooter = e => {\n      this._triggerRemeasureForChildListsInCell(this._getFooterCellKey());\n      this._footerLength = this._selectLength(e.nativeEvent.layout);\n    };\n    this._onLayoutHeader = e => {\n      this._headerLength = this._selectLength(e.nativeEvent.layout);\n    };\n    this._onContentSizeChange = (width, height) => {\n      if (width > 0 && height > 0 && this.props.initialScrollIndex != null && this.props.initialScrollIndex > 0 && !this._hasTriggeredInitialScrollToIndex) {\n        if (this.props.contentOffset == null) {\n          if (this.props.initialScrollIndex < this.props.getItemCount(this.props.data)) {\n            this.scrollToIndex({\n              animated: false,\n              index: nullthrows(this.props.initialScrollIndex)\n            });\n          } else {\n            this.scrollToEnd({\n              animated: false\n            });\n          }\n        }\n        this._hasTriggeredInitialScrollToIndex = true;\n      }\n      if (this.props.onContentSizeChange) {\n        this.props.onContentSizeChange(width, height);\n      }\n      this._scrollMetrics.contentLength = this._selectLength({\n        height,\n        width\n      });\n      this._scheduleCellsToRenderUpdate();\n      this._maybeCallOnEdgeReached();\n    };\n    this._convertParentScrollMetrics = metrics => {\n      // Offset of the top of the nested list relative to the top of its parent's viewport\n      var offset = metrics.offset - this._offsetFromParentVirtualizedList;\n      // Child's visible length is the same as its parent's\n      var visibleLength = metrics.visibleLength;\n      var dOffset = offset - this._scrollMetrics.offset;\n      var contentLength = this._scrollMetrics.contentLength;\n      return {\n        visibleLength,\n        contentLength,\n        offset,\n        dOffset\n      };\n    };\n    this._onScroll = e => {\n      this._nestedChildLists.forEach(childList => {\n        childList._onScroll(e);\n      });\n      if (this.props.onScroll) {\n        this.props.onScroll(e);\n      }\n      var timestamp = e.timeStamp;\n      var visibleLength = this._selectLength(e.nativeEvent.layoutMeasurement);\n      var contentLength = this._selectLength(e.nativeEvent.contentSize);\n      var offset = this._selectOffset(e.nativeEvent.contentOffset);\n      var dOffset = offset - this._scrollMetrics.offset;\n      if (this._isNestedWithSameOrientation()) {\n        if (this._scrollMetrics.contentLength === 0) {\n          // Ignore scroll events until onLayout has been called and we\n          // know our offset from our offset from our parent\n          return;\n        }\n        var _this$_convertParentS = this._convertParentScrollMetrics({\n          visibleLength,\n          offset\n        });\n        visibleLength = _this$_convertParentS.visibleLength;\n        contentLength = _this$_convertParentS.contentLength;\n        offset = _this$_convertParentS.offset;\n        dOffset = _this$_convertParentS.dOffset;\n      }\n      var dt = this._scrollMetrics.timestamp ? Math.max(1, timestamp - this._scrollMetrics.timestamp) : 1;\n      var velocity = dOffset / dt;\n      if (dt > 500 && this._scrollMetrics.dt > 500 && contentLength > 5 * visibleLength && !this._hasWarned.perf) {\n        infoLog('VirtualizedList: You have a large list that is slow to update - make sure your ' + 'renderItem function renders components that follow React performance best practices ' + 'like PureComponent, shouldComponentUpdate, etc.', {\n          dt,\n          prevDt: this._scrollMetrics.dt,\n          contentLength\n        });\n        this._hasWarned.perf = true;\n      }\n\n      // For invalid negative values (w/ RTL), set this to 1.\n      var zoomScale = e.nativeEvent.zoomScale < 0 ? 1 : e.nativeEvent.zoomScale;\n      this._scrollMetrics = {\n        contentLength,\n        dt,\n        dOffset,\n        offset,\n        timestamp,\n        velocity,\n        visibleLength,\n        zoomScale\n      };\n      this._updateViewableItems(this.props, this.state.cellsAroundViewport);\n      if (!this.props) {\n        return;\n      }\n      this._maybeCallOnEdgeReached();\n      if (velocity !== 0) {\n        this._fillRateHelper.activate();\n      }\n      this._computeBlankness();\n      this._scheduleCellsToRenderUpdate();\n    };\n    this._onScrollBeginDrag = e => {\n      this._nestedChildLists.forEach(childList => {\n        childList._onScrollBeginDrag(e);\n      });\n      this._viewabilityTuples.forEach(tuple => {\n        tuple.viewabilityHelper.recordInteraction();\n      });\n      this._hasInteracted = true;\n      this.props.onScrollBeginDrag && this.props.onScrollBeginDrag(e);\n    };\n    this._onScrollEndDrag = e => {\n      this._nestedChildLists.forEach(childList => {\n        childList._onScrollEndDrag(e);\n      });\n      var velocity = e.nativeEvent.velocity;\n      if (velocity) {\n        this._scrollMetrics.velocity = this._selectOffset(velocity);\n      }\n      this._computeBlankness();\n      this.props.onScrollEndDrag && this.props.onScrollEndDrag(e);\n    };\n    this._onMomentumScrollBegin = e => {\n      this._nestedChildLists.forEach(childList => {\n        childList._onMomentumScrollBegin(e);\n      });\n      this.props.onMomentumScrollBegin && this.props.onMomentumScrollBegin(e);\n    };\n    this._onMomentumScrollEnd = e => {\n      this._nestedChildLists.forEach(childList => {\n        childList._onMomentumScrollEnd(e);\n      });\n      this._scrollMetrics.velocity = 0;\n      this._computeBlankness();\n      this.props.onMomentumScrollEnd && this.props.onMomentumScrollEnd(e);\n    };\n    this._updateCellsToRender = () => {\n      this._updateViewableItems(this.props, this.state.cellsAroundViewport);\n      this.setState((state, props) => {\n        var cellsAroundViewport = this._adjustCellsAroundViewport(props, state.cellsAroundViewport);\n        var renderMask = VirtualizedList._createRenderMask(props, cellsAroundViewport, this._getNonViewportRenderRegions(props));\n        if (cellsAroundViewport.first === state.cellsAroundViewport.first && cellsAroundViewport.last === state.cellsAroundViewport.last && renderMask.equals(state.renderMask)) {\n          return null;\n        }\n        return {\n          cellsAroundViewport,\n          renderMask\n        };\n      });\n    };\n    this._createViewToken = (index, isViewable, props\n    // $FlowFixMe[missing-local-annot]\n    ) => {\n      var data = props.data,\n        getItem = props.getItem;\n      var item = getItem(data, index);\n      return {\n        index,\n        item,\n        key: this._keyExtractor(item, index, props),\n        isViewable\n      };\n    };\n    this._getOffsetApprox = (index, props) => {\n      if (Number.isInteger(index)) {\n        return this.__getFrameMetricsApprox(index, props).offset;\n      } else {\n        var frameMetrics = this.__getFrameMetricsApprox(Math.floor(index), props);\n        var remainder = index - Math.floor(index);\n        return frameMetrics.offset + remainder * frameMetrics.length;\n      }\n    };\n    this.__getFrameMetricsApprox = (index, props) => {\n      var frame = this._getFrameMetrics(index, props);\n      if (frame && frame.index === index) {\n        // check for invalid frames due to row re-ordering\n        return frame;\n      } else {\n        var data = props.data,\n          getItemCount = props.getItemCount,\n          getItemLayout = props.getItemLayout;\n        invariant(index >= 0 && index < getItemCount(data), 'Tried to get frame for out of range index ' + index);\n        invariant(!getItemLayout, 'Should not have to estimate frames when a measurement metrics function is provided');\n        return {\n          length: this._averageCellLength,\n          offset: this._averageCellLength * index\n        };\n      }\n    };\n    this._getFrameMetrics = (index, props) => {\n      var data = props.data,\n        getItem = props.getItem,\n        getItemCount = props.getItemCount,\n        getItemLayout = props.getItemLayout;\n      invariant(index >= 0 && index < getItemCount(data), 'Tried to get frame for out of range index ' + index);\n      var item = getItem(data, index);\n      var frame = this._frames[this._keyExtractor(item, index, props)];\n      if (!frame || frame.index !== index) {\n        if (getItemLayout) {\n          /* $FlowFixMe[prop-missing] (>=0.63.0 site=react_native_fb) This comment\n           * suppresses an error found when Flow v0.63 was deployed. To see the error\n           * delete this comment and run Flow. */\n          return getItemLayout(data, index);\n        }\n      }\n      return frame;\n    };\n    this._getNonViewportRenderRegions = props => {\n      // Keep a viewport's worth of content around the last focused cell to allow\n      // random navigation around it without any blanking. E.g. tabbing from one\n      // focused item out of viewport to another.\n      if (!(this._lastFocusedCellKey && this._cellRefs[this._lastFocusedCellKey])) {\n        return [];\n      }\n      var lastFocusedCellRenderer = this._cellRefs[this._lastFocusedCellKey];\n      var focusedCellIndex = lastFocusedCellRenderer.props.index;\n      var itemCount = props.getItemCount(props.data);\n\n      // The last cell we rendered may be at a new index. Bail if we don't know\n      // where it is.\n      if (focusedCellIndex >= itemCount || this._keyExtractor(props.getItem(props.data, focusedCellIndex), focusedCellIndex, props) !== this._lastFocusedCellKey) {\n        return [];\n      }\n      var first = focusedCellIndex;\n      var heightOfCellsBeforeFocused = 0;\n      for (var i = first - 1; i >= 0 && heightOfCellsBeforeFocused < this._scrollMetrics.visibleLength; i--) {\n        first--;\n        heightOfCellsBeforeFocused += this.__getFrameMetricsApprox(i, props).length;\n      }\n      var last = focusedCellIndex;\n      var heightOfCellsAfterFocused = 0;\n      for (var _i = last + 1; _i < itemCount && heightOfCellsAfterFocused < this._scrollMetrics.visibleLength; _i++) {\n        last++;\n        heightOfCellsAfterFocused += this.__getFrameMetricsApprox(_i, props).length;\n      }\n      return [{\n        first,\n        last\n      }];\n    };\n    this._checkProps(_props);\n    this._fillRateHelper = new FillRateHelper(this._getFrameMetrics);\n    this._updateCellsToRenderBatcher = new Batchinator(this._updateCellsToRender, (_this$props$updateCel = this.props.updateCellsBatchingPeriod) !== null && _this$props$updateCel !== void 0 ? _this$props$updateCel : 50);\n    if (this.props.viewabilityConfigCallbackPairs) {\n      this._viewabilityTuples = this.props.viewabilityConfigCallbackPairs.map(pair => ({\n        viewabilityHelper: new ViewabilityHelper(pair.viewabilityConfig),\n        onViewableItemsChanged: pair.onViewableItemsChanged\n      }));\n    } else {\n      var _this$props3 = this.props,\n        onViewableItemsChanged = _this$props3.onViewableItemsChanged,\n        viewabilityConfig = _this$props3.viewabilityConfig;\n      if (onViewableItemsChanged) {\n        this._viewabilityTuples.push({\n          viewabilityHelper: new ViewabilityHelper(viewabilityConfig),\n          onViewableItemsChanged: onViewableItemsChanged\n        });\n      }\n    }\n    var initialRenderRegion = VirtualizedList._initialRenderRegion(_props);\n    this.state = {\n      cellsAroundViewport: initialRenderRegion,\n      renderMask: VirtualizedList._createRenderMask(_props, initialRenderRegion)\n    };\n\n    // REACT-NATIVE-WEB patch to preserve during future RN merges: Support inverted wheel scroller.\n    // For issue https://github.com/necolas/react-native-web/issues/995\n    this.invertedWheelEventHandler = ev => {\n      var scrollOffset = this.props.horizontal ? ev.target.scrollLeft : ev.target.scrollTop;\n      var scrollLength = this.props.horizontal ? ev.target.scrollWidth : ev.target.scrollHeight;\n      var clientLength = this.props.horizontal ? ev.target.clientWidth : ev.target.clientHeight;\n      var isEventTargetScrollable = scrollLength > clientLength;\n      var delta = this.props.horizontal ? ev.deltaX || ev.wheelDeltaX : ev.deltaY || ev.wheelDeltaY;\n      var leftoverDelta = delta;\n      if (isEventTargetScrollable) {\n        leftoverDelta = delta < 0 ? Math.min(delta + scrollOffset, 0) : Math.max(delta - (scrollLength - clientLength - scrollOffset), 0);\n      }\n      var targetDelta = delta - leftoverDelta;\n      if (this.props.inverted && this._scrollRef && this._scrollRef.getScrollableNode) {\n        var node = this._scrollRef.getScrollableNode();\n        if (this.props.horizontal) {\n          ev.target.scrollLeft += targetDelta;\n          var nextScrollLeft = node.scrollLeft - leftoverDelta;\n          node.scrollLeft = !this.props.getItemLayout ? Math.min(nextScrollLeft, this._totalCellLength) : nextScrollLeft;\n        } else {\n          ev.target.scrollTop += targetDelta;\n          var nextScrollTop = node.scrollTop - leftoverDelta;\n          node.scrollTop = !this.props.getItemLayout ? Math.min(nextScrollTop, this._totalCellLength) : nextScrollTop;\n        }\n        ev.preventDefault();\n      }\n    };\n  }\n  _checkProps(props) {\n    var onScroll = props.onScroll,\n      windowSize = props.windowSize,\n      getItemCount = props.getItemCount,\n      data = props.data,\n      initialScrollIndex = props.initialScrollIndex;\n    invariant(\n    // $FlowFixMe[prop-missing]\n    !onScroll || !onScroll.__isNative, 'Components based on VirtualizedList must be wrapped with Animated.createAnimatedComponent ' + 'to support native onScroll events with useNativeDriver');\n    invariant(windowSizeOrDefault(windowSize) > 0, 'VirtualizedList: The windowSize prop must be present and set to a value greater than 0.');\n    invariant(getItemCount, 'VirtualizedList: The \"getItemCount\" prop must be provided');\n    var itemCount = getItemCount(data);\n    if (initialScrollIndex != null && !this._hasTriggeredInitialScrollToIndex && (initialScrollIndex < 0 || itemCount > 0 && initialScrollIndex >= itemCount) && !this._hasWarned.initialScrollIndex) {\n      console.warn(\"initialScrollIndex \\\"\" + initialScrollIndex + \"\\\" is not valid (list has \" + itemCount + \" items)\");\n      this._hasWarned.initialScrollIndex = true;\n    }\n    if (__DEV__ && !this._hasWarned.flexWrap) {\n      // $FlowFixMe[underconstrained-implicit-instantiation]\n      var flatStyles = StyleSheet.flatten(this.props.contentContainerStyle);\n      if (flatStyles != null && flatStyles.flexWrap === 'wrap') {\n        console.warn('`flexWrap: `wrap`` is not supported with the `VirtualizedList` components.' + 'Consider using `numColumns` with `FlatList` instead.');\n        this._hasWarned.flexWrap = true;\n      }\n    }\n  }\n  static _createRenderMask(props, cellsAroundViewport, additionalRegions) {\n    var itemCount = props.getItemCount(props.data);\n    invariant(cellsAroundViewport.first >= 0 && cellsAroundViewport.last >= cellsAroundViewport.first - 1 && cellsAroundViewport.last < itemCount, \"Invalid cells around viewport \\\"[\" + cellsAroundViewport.first + \", \" + cellsAroundViewport.last + \"]\\\" was passed to VirtualizedList._createRenderMask\");\n    var renderMask = new CellRenderMask(itemCount);\n    if (itemCount > 0) {\n      var allRegions = [cellsAroundViewport, ...(additionalRegions !== null && additionalRegions !== void 0 ? additionalRegions : [])];\n      for (var _i2 = 0, _allRegions = allRegions; _i2 < _allRegions.length; _i2++) {\n        var region = _allRegions[_i2];\n        renderMask.addCells(region);\n      }\n\n      // The initially rendered cells are retained as part of the\n      // \"scroll-to-top\" optimization\n      if (props.initialScrollIndex == null || props.initialScrollIndex <= 0) {\n        var initialRegion = VirtualizedList._initialRenderRegion(props);\n        renderMask.addCells(initialRegion);\n      }\n\n      // The layout coordinates of sticker headers may be off-screen while the\n      // actual header is on-screen. Keep the most recent before the viewport\n      // rendered, even if its layout coordinates are not in viewport.\n      var stickyIndicesSet = new Set(props.stickyHeaderIndices);\n      VirtualizedList._ensureClosestStickyHeader(props, stickyIndicesSet, renderMask, cellsAroundViewport.first);\n    }\n    return renderMask;\n  }\n  static _initialRenderRegion(props) {\n    var _props$initialScrollI;\n    var itemCount = props.getItemCount(props.data);\n    var firstCellIndex = Math.max(0, Math.min(itemCount - 1, Math.floor((_props$initialScrollI = props.initialScrollIndex) !== null && _props$initialScrollI !== void 0 ? _props$initialScrollI : 0)));\n    var lastCellIndex = Math.min(itemCount, firstCellIndex + initialNumToRenderOrDefault(props.initialNumToRender)) - 1;\n    return {\n      first: firstCellIndex,\n      last: lastCellIndex\n    };\n  }\n  static _ensureClosestStickyHeader(props, stickyIndicesSet, renderMask, cellIdx) {\n    var stickyOffset = props.ListHeaderComponent ? 1 : 0;\n    for (var itemIdx = cellIdx - 1; itemIdx >= 0; itemIdx--) {\n      if (stickyIndicesSet.has(itemIdx + stickyOffset)) {\n        renderMask.addCells({\n          first: itemIdx,\n          last: itemIdx\n        });\n        break;\n      }\n    }\n  }\n  _adjustCellsAroundViewport(props, cellsAroundViewport) {\n    var data = props.data,\n      getItemCount = props.getItemCount;\n    var onEndReachedThreshold = onEndReachedThresholdOrDefault(props.onEndReachedThreshold);\n    var _this$_scrollMetrics = this._scrollMetrics,\n      contentLength = _this$_scrollMetrics.contentLength,\n      offset = _this$_scrollMetrics.offset,\n      visibleLength = _this$_scrollMetrics.visibleLength;\n    var distanceFromEnd = contentLength - visibleLength - offset;\n\n    // Wait until the scroll view metrics have been set up. And until then,\n    // we will trust the initialNumToRender suggestion\n    if (visibleLength <= 0 || contentLength <= 0) {\n      return cellsAroundViewport.last >= getItemCount(data) ? VirtualizedList._constrainToItemCount(cellsAroundViewport, props) : cellsAroundViewport;\n    }\n    var newCellsAroundViewport;\n    if (props.disableVirtualization) {\n      var renderAhead = distanceFromEnd < onEndReachedThreshold * visibleLength ? maxToRenderPerBatchOrDefault(props.maxToRenderPerBatch) : 0;\n      newCellsAroundViewport = {\n        first: 0,\n        last: Math.min(cellsAroundViewport.last + renderAhead, getItemCount(data) - 1)\n      };\n    } else {\n      // If we have a non-zero initialScrollIndex and run this before we've scrolled,\n      // we'll wipe out the initialNumToRender rendered elements starting at initialScrollIndex.\n      // So let's wait until we've scrolled the view to the right place. And until then,\n      // we will trust the initialScrollIndex suggestion.\n\n      // Thus, we want to recalculate the windowed render limits if any of the following hold:\n      // - initialScrollIndex is undefined or is 0\n      // - initialScrollIndex > 0 AND scrolling is complete\n      // - initialScrollIndex > 0 AND the end of the list is visible (this handles the case\n      //   where the list is shorter than the visible area)\n      if (props.initialScrollIndex && !this._scrollMetrics.offset && Math.abs(distanceFromEnd) >= Number.EPSILON) {\n        return cellsAroundViewport.last >= getItemCount(data) ? VirtualizedList._constrainToItemCount(cellsAroundViewport, props) : cellsAroundViewport;\n      }\n      newCellsAroundViewport = computeWindowedRenderLimits(props, maxToRenderPerBatchOrDefault(props.maxToRenderPerBatch), windowSizeOrDefault(props.windowSize), cellsAroundViewport, this.__getFrameMetricsApprox, this._scrollMetrics);\n      invariant(newCellsAroundViewport.last < getItemCount(data), 'computeWindowedRenderLimits() should return range in-bounds');\n    }\n    if (this._nestedChildLists.size() > 0) {\n      // If some cell in the new state has a child list in it, we should only render\n      // up through that item, so that we give that list a chance to render.\n      // Otherwise there's churn from multiple child lists mounting and un-mounting\n      // their items.\n\n      // Will this prevent rendering if the nested list doesn't realize the end?\n      var childIdx = this._findFirstChildWithMore(newCellsAroundViewport.first, newCellsAroundViewport.last);\n      newCellsAroundViewport.last = childIdx !== null && childIdx !== void 0 ? childIdx : newCellsAroundViewport.last;\n    }\n    return newCellsAroundViewport;\n  }\n  _findFirstChildWithMore(first, last) {\n    for (var ii = first; ii <= last; ii++) {\n      var cellKeyForIndex = this._indicesToKeys.get(ii);\n      if (cellKeyForIndex != null && this._nestedChildLists.anyInCell(cellKeyForIndex, childList => childList.hasMore())) {\n        return ii;\n      }\n    }\n    return null;\n  }\n  componentDidMount() {\n    if (this._isNestedWithSameOrientation()) {\n      this.context.registerAsNestedChild({\n        ref: this,\n        cellKey: this.context.cellKey\n      });\n    }\n\n    // REACT-NATIVE-WEB patch to preserve during future RN merges: Support inverted wheel scroller.\n    this.setupWebWheelHandler();\n  }\n  componentWillUnmount() {\n    if (this._isNestedWithSameOrientation()) {\n      this.context.unregisterAsNestedChild({\n        ref: this\n      });\n    }\n    this._updateCellsToRenderBatcher.dispose({\n      abort: true\n    });\n    this._viewabilityTuples.forEach(tuple => {\n      tuple.viewabilityHelper.dispose();\n    });\n    this._fillRateHelper.deactivateAndFlush();\n\n    // REACT-NATIVE-WEB patch to preserve during future RN merges: Support inverted wheel scroller.\n    this.teardownWebWheelHandler();\n  }\n\n  // REACT-NATIVE-WEB patch to preserve during future RN merges: Support inverted wheel scroller.\n  setupWebWheelHandler() {\n    if (this._scrollRef && this._scrollRef.getScrollableNode) {\n      this._scrollRef.getScrollableNode().addEventListener('wheel', this.invertedWheelEventHandler);\n    } else {\n      setTimeout(() => this.setupWebWheelHandler(), 50);\n      return;\n    }\n  }\n\n  // REACT-NATIVE-WEB patch to preserve during future RN merges: Support inverted wheel scroller.\n  teardownWebWheelHandler() {\n    if (this._scrollRef && this._scrollRef.getScrollableNode) {\n      this._scrollRef.getScrollableNode().removeEventListener('wheel', this.invertedWheelEventHandler);\n    }\n  }\n  static getDerivedStateFromProps(newProps, prevState) {\n    // first and last could be stale (e.g. if a new, shorter items props is passed in), so we make\n    // sure we're rendering a reasonable range here.\n    var itemCount = newProps.getItemCount(newProps.data);\n    if (itemCount === prevState.renderMask.numCells()) {\n      return prevState;\n    }\n    var constrainedCells = VirtualizedList._constrainToItemCount(prevState.cellsAroundViewport, newProps);\n    return {\n      cellsAroundViewport: constrainedCells,\n      renderMask: VirtualizedList._createRenderMask(newProps, constrainedCells)\n    };\n  }\n  _pushCells(cells, stickyHeaderIndices, stickyIndicesFromProps, first, last, inversionStyle) {\n    var _this = this;\n    var _this$props4 = this.props,\n      CellRendererComponent = _this$props4.CellRendererComponent,\n      ItemSeparatorComponent = _this$props4.ItemSeparatorComponent,\n      ListHeaderComponent = _this$props4.ListHeaderComponent,\n      ListItemComponent = _this$props4.ListItemComponent,\n      data = _this$props4.data,\n      debug = _this$props4.debug,\n      getItem = _this$props4.getItem,\n      getItemCount = _this$props4.getItemCount,\n      getItemLayout = _this$props4.getItemLayout,\n      horizontal = _this$props4.horizontal,\n      renderItem = _this$props4.renderItem;\n    var stickyOffset = ListHeaderComponent ? 1 : 0;\n    var end = getItemCount(data) - 1;\n    var prevCellKey;\n    last = Math.min(end, last);\n    var _loop = function _loop() {\n      var item = getItem(data, ii);\n      var key = _this._keyExtractor(item, ii, _this.props);\n      _this._indicesToKeys.set(ii, key);\n      if (stickyIndicesFromProps.has(ii + stickyOffset)) {\n        stickyHeaderIndices.push(cells.length);\n      }\n      var shouldListenForLayout = getItemLayout == null || debug || _this._fillRateHelper.enabled();\n      cells.push(/*#__PURE__*/React.createElement(CellRenderer, _extends({\n        CellRendererComponent: CellRendererComponent,\n        ItemSeparatorComponent: ii < end ? ItemSeparatorComponent : undefined,\n        ListItemComponent: ListItemComponent,\n        cellKey: key,\n        horizontal: horizontal,\n        index: ii,\n        inversionStyle: inversionStyle,\n        item: item,\n        key: key,\n        prevCellKey: prevCellKey,\n        onUpdateSeparators: _this._onUpdateSeparators,\n        onCellFocusCapture: e => _this._onCellFocusCapture(key),\n        onUnmount: _this._onCellUnmount,\n        ref: _ref => {\n          _this._cellRefs[key] = _ref;\n        },\n        renderItem: renderItem\n      }, shouldListenForLayout && {\n        onCellLayout: _this._onCellLayout\n      })));\n      prevCellKey = key;\n    };\n    for (var ii = first; ii <= last; ii++) {\n      _loop();\n    }\n  }\n  static _constrainToItemCount(cells, props) {\n    var itemCount = props.getItemCount(props.data);\n    var last = Math.min(itemCount - 1, cells.last);\n    var maxToRenderPerBatch = maxToRenderPerBatchOrDefault(props.maxToRenderPerBatch);\n    return {\n      first: clamp(0, itemCount - 1 - maxToRenderPerBatch, cells.first),\n      last\n    };\n  }\n  _isNestedWithSameOrientation() {\n    var nestedContext = this.context;\n    return !!(nestedContext && !!nestedContext.horizontal === horizontalOrDefault(this.props.horizontal));\n  }\n  _keyExtractor(item, index, props\n  // $FlowFixMe[missing-local-annot]\n  ) {\n    if (props.keyExtractor != null) {\n      return props.keyExtractor(item, index);\n    }\n    var key = defaultKeyExtractor(item, index);\n    if (key === String(index)) {\n      _usedIndexForKey = true;\n      if (item.type && item.type.displayName) {\n        _keylessItemComponentName = item.type.displayName;\n      }\n    }\n    return key;\n  }\n  render() {\n    this._checkProps(this.props);\n    var _this$props5 = this.props,\n      ListEmptyComponent = _this$props5.ListEmptyComponent,\n      ListFooterComponent = _this$props5.ListFooterComponent,\n      ListHeaderComponent = _this$props5.ListHeaderComponent;\n    var _this$props6 = this.props,\n      data = _this$props6.data,\n      horizontal = _this$props6.horizontal;\n    var inversionStyle = this.props.inverted ? horizontalOrDefault(this.props.horizontal) ? styles.horizontallyInverted : styles.verticallyInverted : null;\n    var cells = [];\n    var stickyIndicesFromProps = new Set(this.props.stickyHeaderIndices);\n    var stickyHeaderIndices = [];\n\n    // 1. Add cell for ListHeaderComponent\n    if (ListHeaderComponent) {\n      if (stickyIndicesFromProps.has(0)) {\n        stickyHeaderIndices.push(0);\n      }\n      var _element = /*#__PURE__*/React.isValidElement(ListHeaderComponent) ? ListHeaderComponent :\n      /*#__PURE__*/\n      // $FlowFixMe[not-a-component]\n      // $FlowFixMe[incompatible-type-arg]\n      React.createElement(ListHeaderComponent, null);\n      cells.push(/*#__PURE__*/React.createElement(VirtualizedListCellContextProvider, {\n        cellKey: this._getCellKey() + '-header',\n        key: \"$header\"\n      }, /*#__PURE__*/React.createElement(View, {\n        onLayout: this._onLayoutHeader,\n        style: [inversionStyle, this.props.ListHeaderComponentStyle]\n      },\n      // $FlowFixMe[incompatible-type] - Typing ReactNativeComponent revealed errors\n      _element)));\n    }\n\n    // 2a. Add a cell for ListEmptyComponent if applicable\n    var itemCount = this.props.getItemCount(data);\n    if (itemCount === 0 && ListEmptyComponent) {\n      var _element2 = /*#__PURE__*/React.isValidElement(ListEmptyComponent) ? ListEmptyComponent :\n      /*#__PURE__*/\n      // $FlowFixMe[not-a-component]\n      // $FlowFixMe[incompatible-type-arg]\n      React.createElement(ListEmptyComponent, null);\n      cells.push(/*#__PURE__*/React.createElement(VirtualizedListCellContextProvider, {\n        cellKey: this._getCellKey() + '-empty',\n        key: \"$empty\"\n      }, /*#__PURE__*/React.cloneElement(_element2, {\n        onLayout: event => {\n          this._onLayoutEmpty(event);\n          if (_element2.props.onLayout) {\n            _element2.props.onLayout(event);\n          }\n        },\n        style: [inversionStyle, _element2.props.style]\n      })));\n    }\n\n    // 2b. Add cells and spacers for each item\n    if (itemCount > 0) {\n      _usedIndexForKey = false;\n      _keylessItemComponentName = '';\n      var spacerKey = this._getSpacerKey(!horizontal);\n      var renderRegions = this.state.renderMask.enumerateRegions();\n      var lastSpacer = findLastWhere(renderRegions, r => r.isSpacer);\n      for (var _iterator = _createForOfIteratorHelperLoose(renderRegions), _step; !(_step = _iterator()).done;) {\n        var section = _step.value;\n        if (section.isSpacer) {\n          // Legacy behavior is to avoid spacers when virtualization is\n          // disabled (including head spacers on initial render).\n          if (this.props.disableVirtualization) {\n            continue;\n          }\n\n          // Without getItemLayout, we limit our tail spacer to the _highestMeasuredFrameIndex to\n          // prevent the user for hyperscrolling into un-measured area because otherwise content will\n          // likely jump around as it renders in above the viewport.\n          var isLastSpacer = section === lastSpacer;\n          var constrainToMeasured = isLastSpacer && !this.props.getItemLayout;\n          var last = constrainToMeasured ? clamp(section.first - 1, section.last, this._highestMeasuredFrameIndex) : section.last;\n          var firstMetrics = this.__getFrameMetricsApprox(section.first, this.props);\n          var lastMetrics = this.__getFrameMetricsApprox(last, this.props);\n          var spacerSize = lastMetrics.offset + lastMetrics.length - firstMetrics.offset;\n          cells.push(/*#__PURE__*/React.createElement(View, {\n            key: \"$spacer-\" + section.first,\n            style: {\n              [spacerKey]: spacerSize\n            }\n          }));\n        } else {\n          this._pushCells(cells, stickyHeaderIndices, stickyIndicesFromProps, section.first, section.last, inversionStyle);\n        }\n      }\n      if (!this._hasWarned.keys && _usedIndexForKey) {\n        console.warn('VirtualizedList: missing keys for items, make sure to specify a key or id property on each ' + 'item or provide a custom keyExtractor.', _keylessItemComponentName);\n        this._hasWarned.keys = true;\n      }\n    }\n\n    // 3. Add cell for ListFooterComponent\n    if (ListFooterComponent) {\n      var _element3 = /*#__PURE__*/React.isValidElement(ListFooterComponent) ? ListFooterComponent :\n      /*#__PURE__*/\n      // $FlowFixMe[not-a-component]\n      // $FlowFixMe[incompatible-type-arg]\n      React.createElement(ListFooterComponent, null);\n      cells.push(/*#__PURE__*/React.createElement(VirtualizedListCellContextProvider, {\n        cellKey: this._getFooterCellKey(),\n        key: \"$footer\"\n      }, /*#__PURE__*/React.createElement(View, {\n        onLayout: this._onLayoutFooter,\n        style: [inversionStyle, this.props.ListFooterComponentStyle]\n      },\n      // $FlowFixMe[incompatible-type] - Typing ReactNativeComponent revealed errors\n      _element3)));\n    }\n\n    // 4. Render the ScrollView\n    var scrollProps = _objectSpread(_objectSpread({}, this.props), {}, {\n      onContentSizeChange: this._onContentSizeChange,\n      onLayout: this._onLayout,\n      onScroll: this._onScroll,\n      onScrollBeginDrag: this._onScrollBeginDrag,\n      onScrollEndDrag: this._onScrollEndDrag,\n      onMomentumScrollBegin: this._onMomentumScrollBegin,\n      onMomentumScrollEnd: this._onMomentumScrollEnd,\n      scrollEventThrottle: scrollEventThrottleOrDefault(this.props.scrollEventThrottle),\n      // TODO: Android support\n      invertStickyHeaders: this.props.invertStickyHeaders !== undefined ? this.props.invertStickyHeaders : this.props.inverted,\n      stickyHeaderIndices,\n      style: inversionStyle ? [inversionStyle, this.props.style] : this.props.style\n    });\n    this._hasMore = this.state.cellsAroundViewport.last < itemCount - 1;\n    var innerRet = /*#__PURE__*/React.createElement(VirtualizedListContextProvider, {\n      value: {\n        cellKey: null,\n        getScrollMetrics: this._getScrollMetrics,\n        horizontal: horizontalOrDefault(this.props.horizontal),\n        getOutermostParentListRef: this._getOutermostParentListRef,\n        registerAsNestedChild: this._registerAsNestedChild,\n        unregisterAsNestedChild: this._unregisterAsNestedChild\n      }\n    }, /*#__PURE__*/React.cloneElement((this.props.renderScrollComponent || this._defaultRenderScrollComponent)(scrollProps), {\n      ref: this._captureScrollRef\n    }, cells));\n    var ret = innerRet;\n    /* https://github.com/necolas/react-native-web/issues/2239: Re-enable when ScrollView.Context.Consumer is available.\n    if (__DEV__) {\n      ret = (\n        <ScrollView.Context.Consumer>\n          {scrollContext => {\n            if (\n              scrollContext != null &&\n              !scrollContext.horizontal ===\n                !horizontalOrDefault(this.props.horizontal) &&\n              !this._hasWarned.nesting &&\n              this.context == null &&\n              this.props.scrollEnabled !== false\n            ) {\n              // TODO (*********): use React.warn once 16.9 is sync'd: https://github.com/facebook/react/pull/15170\n              console.error(\n                'VirtualizedLists should never be nested inside plain ScrollViews with the same ' +\n                  'orientation because it can break windowing and other functionality - use another ' +\n                  'VirtualizedList-backed container instead.',\n              );\n              this._hasWarned.nesting = true;\n            }\n            return innerRet;\n          }}\n        </ScrollView.Context.Consumer>\n      );\n    }*/\n    if (this.props.debug) {\n      return /*#__PURE__*/React.createElement(View, {\n        style: styles.debug\n      }, ret, this._renderDebugOverlay());\n    } else {\n      return ret;\n    }\n  }\n  componentDidUpdate(prevProps) {\n    var _this$props7 = this.props,\n      data = _this$props7.data,\n      extraData = _this$props7.extraData;\n    if (data !== prevProps.data || extraData !== prevProps.extraData) {\n      // clear the viewableIndices cache to also trigger\n      // the onViewableItemsChanged callback with the new data\n      this._viewabilityTuples.forEach(tuple => {\n        tuple.viewabilityHelper.resetViewableIndices();\n      });\n    }\n    // The `this._hiPriInProgress` is guaranteeing a hiPri cell update will only happen\n    // once per fiber update. The `_scheduleCellsToRenderUpdate` will set it to true\n    // if a hiPri update needs to perform. If `componentDidUpdate` is triggered with\n    // `this._hiPriInProgress=true`, means it's triggered by the hiPri update. The\n    // `_scheduleCellsToRenderUpdate` will check this condition and not perform\n    // another hiPri update.\n    var hiPriInProgress = this._hiPriInProgress;\n    this._scheduleCellsToRenderUpdate();\n    // Make sure setting `this._hiPriInProgress` back to false after `componentDidUpdate`\n    // is triggered with `this._hiPriInProgress = true`\n    if (hiPriInProgress) {\n      this._hiPriInProgress = false;\n    }\n  }\n\n  // Used for preventing scrollToIndex from being called multiple times for initialScrollIndex\n\n  // flag to prevent infinite hiPri cell limit update\n\n  // $FlowFixMe[missing-local-annot]\n\n  /* $FlowFixMe[missing-local-annot] The type annotation(s) required by Flow's\n   * LTI update could not be added via codemod */\n\n  _computeBlankness() {\n    this._fillRateHelper.computeBlankness(this.props, this.state.cellsAroundViewport, this._scrollMetrics);\n  }\n\n  /* $FlowFixMe[missing-local-annot] The type annotation(s) required by Flow's\n   * LTI update could not be added via codemod */\n\n  _onCellFocusCapture(cellKey) {\n    this._lastFocusedCellKey = cellKey;\n    this._updateCellsToRender();\n  }\n  _triggerRemeasureForChildListsInCell(cellKey) {\n    this._nestedChildLists.forEachInCell(cellKey, childList => {\n      childList.measureLayoutRelativeToContainingList();\n    });\n  }\n  measureLayoutRelativeToContainingList() {\n    // TODO (*********): findNodeHandle sometimes crashes with \"Unable to find\n    // node on an unmounted component\" during scrolling\n    try {\n      if (!this._scrollRef) {\n        return;\n      }\n      // We are assuming that getOutermostParentListRef().getScrollRef()\n      // is a non-null reference to a ScrollView\n      this._scrollRef.measureLayout(this.context.getOutermostParentListRef().getScrollRef(), (x, y, width, height) => {\n        this._offsetFromParentVirtualizedList = this._selectOffset({\n          x,\n          y\n        });\n        this._scrollMetrics.contentLength = this._selectLength({\n          width,\n          height\n        });\n        var scrollMetrics = this._convertParentScrollMetrics(this.context.getScrollMetrics());\n        var metricsChanged = this._scrollMetrics.visibleLength !== scrollMetrics.visibleLength || this._scrollMetrics.offset !== scrollMetrics.offset;\n        if (metricsChanged) {\n          this._scrollMetrics.visibleLength = scrollMetrics.visibleLength;\n          this._scrollMetrics.offset = scrollMetrics.offset;\n\n          // If metrics of the scrollView changed, then we triggered remeasure for child list\n          // to ensure VirtualizedList has the right information.\n          this._nestedChildLists.forEach(childList => {\n            childList.measureLayoutRelativeToContainingList();\n          });\n        }\n      }, error => {\n        console.warn(\"VirtualizedList: Encountered an error while measuring a list's\" + ' offset from its containing VirtualizedList.');\n      });\n    } catch (error) {\n      console.warn('measureLayoutRelativeToContainingList threw an error', error.stack);\n    }\n  }\n  _getFooterCellKey() {\n    return this._getCellKey() + '-footer';\n  }\n  // $FlowFixMe[missing-local-annot]\n  _renderDebugOverlay() {\n    var normalize = this._scrollMetrics.visibleLength / (this._scrollMetrics.contentLength || 1);\n    var framesInLayout = [];\n    var itemCount = this.props.getItemCount(this.props.data);\n    for (var ii = 0; ii < itemCount; ii++) {\n      var frame = this.__getFrameMetricsApprox(ii, this.props);\n      /* $FlowFixMe[prop-missing] (>=0.68.0 site=react_native_fb) This comment\n       * suppresses an error found when Flow v0.68 was deployed. To see the\n       * error delete this comment and run Flow. */\n      if (frame.inLayout) {\n        framesInLayout.push(frame);\n      }\n    }\n    var windowTop = this.__getFrameMetricsApprox(this.state.cellsAroundViewport.first, this.props).offset;\n    var frameLast = this.__getFrameMetricsApprox(this.state.cellsAroundViewport.last, this.props);\n    var windowLen = frameLast.offset + frameLast.length - windowTop;\n    var visTop = this._scrollMetrics.offset;\n    var visLen = this._scrollMetrics.visibleLength;\n    return /*#__PURE__*/React.createElement(View, {\n      style: [styles.debugOverlayBase, styles.debugOverlay]\n    }, framesInLayout.map((f, ii) => /*#__PURE__*/React.createElement(View, {\n      key: 'f' + ii,\n      style: [styles.debugOverlayBase, styles.debugOverlayFrame, {\n        top: f.offset * normalize,\n        height: f.length * normalize\n      }]\n    })), /*#__PURE__*/React.createElement(View, {\n      style: [styles.debugOverlayBase, styles.debugOverlayFrameLast, {\n        top: windowTop * normalize,\n        height: windowLen * normalize\n      }]\n    }), /*#__PURE__*/React.createElement(View, {\n      style: [styles.debugOverlayBase, styles.debugOverlayFrameVis, {\n        top: visTop * normalize,\n        height: visLen * normalize\n      }]\n    }));\n  }\n  _selectLength(metrics) {\n    return !horizontalOrDefault(this.props.horizontal) ? metrics.height : metrics.width;\n  }\n  _selectOffset(metrics) {\n    return !horizontalOrDefault(this.props.horizontal) ? metrics.y : metrics.x;\n  }\n  _maybeCallOnEdgeReached() {\n    var _this$props8 = this.props,\n      data = _this$props8.data,\n      getItemCount = _this$props8.getItemCount,\n      onStartReached = _this$props8.onStartReached,\n      onStartReachedThreshold = _this$props8.onStartReachedThreshold,\n      onEndReached = _this$props8.onEndReached,\n      onEndReachedThreshold = _this$props8.onEndReachedThreshold,\n      initialScrollIndex = _this$props8.initialScrollIndex;\n    var _this$_scrollMetrics2 = this._scrollMetrics,\n      contentLength = _this$_scrollMetrics2.contentLength,\n      visibleLength = _this$_scrollMetrics2.visibleLength,\n      offset = _this$_scrollMetrics2.offset;\n    var distanceFromStart = offset;\n    var distanceFromEnd = contentLength - visibleLength - offset;\n\n    // Especially when oERT is zero it's necessary to 'floor' very small distance values to be 0\n    // since debouncing causes us to not fire this event for every single \"pixel\" we scroll and can thus\n    // be at the edge of the list with a distance approximating 0 but not quite there.\n    if (distanceFromStart < ON_EDGE_REACHED_EPSILON) {\n      distanceFromStart = 0;\n    }\n    if (distanceFromEnd < ON_EDGE_REACHED_EPSILON) {\n      distanceFromEnd = 0;\n    }\n\n    // TODO: T121172172 Look into why we're \"defaulting\" to a threshold of 2px\n    // when oERT is not present (different from 2 viewports used elsewhere)\n    var DEFAULT_THRESHOLD_PX = 2;\n    var startThreshold = onStartReachedThreshold != null ? onStartReachedThreshold * visibleLength : DEFAULT_THRESHOLD_PX;\n    var endThreshold = onEndReachedThreshold != null ? onEndReachedThreshold * visibleLength : DEFAULT_THRESHOLD_PX;\n    var isWithinStartThreshold = distanceFromStart <= startThreshold;\n    var isWithinEndThreshold = distanceFromEnd <= endThreshold;\n\n    // First check if the user just scrolled within the end threshold\n    // and call onEndReached only once for a given content length,\n    // and only if onStartReached is not being executed\n    if (onEndReached && this.state.cellsAroundViewport.last === getItemCount(data) - 1 && isWithinEndThreshold && this._scrollMetrics.contentLength !== this._sentEndForContentLength) {\n      this._sentEndForContentLength = this._scrollMetrics.contentLength;\n      onEndReached({\n        distanceFromEnd\n      });\n    }\n\n    // Next check if the user just scrolled within the start threshold\n    // and call onStartReached only once for a given content length,\n    // and only if onEndReached is not being executed\n    else if (onStartReached != null && this.state.cellsAroundViewport.first === 0 && isWithinStartThreshold && this._scrollMetrics.contentLength !== this._sentStartForContentLength) {\n      // On initial mount when using initialScrollIndex the offset will be 0 initially\n      // and will trigger an unexpected onStartReached. To avoid this we can use\n      // timestamp to differentiate between the initial scroll metrics and when we actually\n      // received the first scroll event.\n      if (!initialScrollIndex || this._scrollMetrics.timestamp !== 0) {\n        this._sentStartForContentLength = this._scrollMetrics.contentLength;\n        onStartReached({\n          distanceFromStart\n        });\n      }\n    }\n\n    // If the user scrolls away from the start or end and back again,\n    // cause onStartReached or onEndReached to be triggered again\n    else {\n      this._sentStartForContentLength = isWithinStartThreshold ? this._sentStartForContentLength : 0;\n      this._sentEndForContentLength = isWithinEndThreshold ? this._sentEndForContentLength : 0;\n    }\n  }\n\n  /* Translates metrics from a scroll event in a parent VirtualizedList into\n   * coordinates relative to the child list.\n   */\n\n  _scheduleCellsToRenderUpdate() {\n    var _this$state$cellsArou = this.state.cellsAroundViewport,\n      first = _this$state$cellsArou.first,\n      last = _this$state$cellsArou.last;\n    var _this$_scrollMetrics3 = this._scrollMetrics,\n      offset = _this$_scrollMetrics3.offset,\n      visibleLength = _this$_scrollMetrics3.visibleLength,\n      velocity = _this$_scrollMetrics3.velocity;\n    var itemCount = this.props.getItemCount(this.props.data);\n    var hiPri = false;\n    var onStartReachedThreshold = onStartReachedThresholdOrDefault(this.props.onStartReachedThreshold);\n    var onEndReachedThreshold = onEndReachedThresholdOrDefault(this.props.onEndReachedThreshold);\n    // Mark as high priority if we're close to the start of the first item\n    // But only if there are items before the first rendered item\n    if (first > 0) {\n      var distTop = offset - this.__getFrameMetricsApprox(first, this.props).offset;\n      hiPri = distTop < 0 || velocity < -2 && distTop < getScrollingThreshold(onStartReachedThreshold, visibleLength);\n    }\n    // Mark as high priority if we're close to the end of the last item\n    // But only if there are items after the last rendered item\n    if (!hiPri && last >= 0 && last < itemCount - 1) {\n      var distBottom = this.__getFrameMetricsApprox(last, this.props).offset - (offset + visibleLength);\n      hiPri = distBottom < 0 || velocity > 2 && distBottom < getScrollingThreshold(onEndReachedThreshold, visibleLength);\n    }\n    // Only trigger high-priority updates if we've actually rendered cells,\n    // and with that size estimate, accurately compute how many cells we should render.\n    // Otherwise, it would just render as many cells as it can (of zero dimension),\n    // each time through attempting to render more (limited by maxToRenderPerBatch),\n    // starving the renderer from actually laying out the objects and computing _averageCellLength.\n    // If this is triggered in an `componentDidUpdate` followed by a hiPri cellToRenderUpdate\n    // We shouldn't do another hipri cellToRenderUpdate\n    if (hiPri && (this._averageCellLength || this.props.getItemLayout) && !this._hiPriInProgress) {\n      this._hiPriInProgress = true;\n      // Don't worry about interactions when scrolling quickly; focus on filling content as fast\n      // as possible.\n      this._updateCellsToRenderBatcher.dispose({\n        abort: true\n      });\n      this._updateCellsToRender();\n      return;\n    } else {\n      this._updateCellsToRenderBatcher.schedule();\n    }\n  }\n\n  /**\n   * Gets an approximate offset to an item at a given index. Supports\n   * fractional indices.\n   */\n\n  _updateViewableItems(props, cellsAroundViewport) {\n    this._viewabilityTuples.forEach(tuple => {\n      tuple.viewabilityHelper.onUpdate(props, this._scrollMetrics.offset, this._scrollMetrics.visibleLength, this._getFrameMetrics, this._createViewToken, tuple.onViewableItemsChanged, cellsAroundViewport);\n    });\n  }\n}\nVirtualizedList.contextType = VirtualizedListContext;\nvar styles = StyleSheet.create({\n  verticallyInverted: {\n    transform: 'scaleY(-1)'\n  },\n  horizontallyInverted: {\n    transform: 'scaleX(-1)'\n  },\n  debug: {\n    flex: 1\n  },\n  debugOverlayBase: {\n    position: 'absolute',\n    top: 0,\n    right: 0\n  },\n  debugOverlay: {\n    bottom: 0,\n    width: 20,\n    borderColor: 'blue',\n    borderWidth: 1\n  },\n  debugOverlayFrame: {\n    left: 0,\n    backgroundColor: 'orange'\n  },\n  debugOverlayFrameLast: {\n    left: 0,\n    borderColor: 'green',\n    borderWidth: 2\n  },\n  debugOverlayFrameVis: {\n    left: 0,\n    borderColor: 'red',\n    borderWidth: 2\n  }\n});\nexport default VirtualizedList;"], "mappings": ";;;;;;;;;AAAA,OAAOA,+BAA+B,MAAM,uDAAuD;AACnG,OAAOC,QAAQ,MAAM,gCAAgC;AACrD,OAAOC,aAAa,MAAM,sCAAsC;AAWhE,OAAOC,cAAc;AACrB,OAAOC,UAAU;AACjB,OAAOC,IAAI;AACX,OAAOC,UAAU;AACjB,OAAOC,cAAc;AACrB,OAAOC,WAAW;AAClB,OAAOC,KAAK;AACZ,OAAOC,OAAO;AACd,SAASC,cAAc;AACvB,OAAOC,mBAAmB;AAC1B,OAAOC,cAAc;AACrB,OAAOC,sBAAsB;AAC7B,OAAOC,iBAAiB;AACxB,OAAOC,YAAY;AACnB,SAASC,kCAAkC,EAAEC,sBAAsB,EAAEC,8BAA8B;AACnG,SAASC,2BAA2B,EAAEC,YAAY,IAAIC,mBAAmB;AACzE,OAAOC,SAAS,MAAM,oBAAoB;AAC1C,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,IAAIC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY;AACnD,IAAIC,uBAAuB,GAAG,KAAK;AACnC,IAAIC,gBAAgB,GAAG,KAAK;AAC5B,IAAIC,yBAAyB,GAAG,EAAE;AAOlC,SAASC,mBAAmBA,CAACC,UAAU,EAAE;EACvC,OAAOA,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAGA,UAAU,GAAG,KAAK;AAC1E;AAGA,SAASC,2BAA2BA,CAACC,kBAAkB,EAAE;EACvD,OAAOA,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,KAAK,KAAK,CAAC,GAAGA,kBAAkB,GAAG,EAAE;AAC/F;AAGA,SAASC,4BAA4BA,CAACC,mBAAmB,EAAE;EACzD,OAAOA,mBAAmB,KAAK,IAAI,IAAIA,mBAAmB,KAAK,KAAK,CAAC,GAAGA,mBAAmB,GAAG,EAAE;AAClG;AAGA,SAASC,gCAAgCA,CAACC,uBAAuB,EAAE;EACjE,OAAOA,uBAAuB,KAAK,IAAI,IAAIA,uBAAuB,KAAK,KAAK,CAAC,GAAGA,uBAAuB,GAAG,CAAC;AAC7G;AAGA,SAASC,8BAA8BA,CAACC,qBAAqB,EAAE;EAC7D,OAAOA,qBAAqB,KAAK,IAAI,IAAIA,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAG,CAAC;AACvG;AAGA,SAASC,qBAAqBA,CAACC,SAAS,EAAEC,aAAa,EAAE;EACvD,OAAOD,SAAS,GAAGC,aAAa,GAAG,CAAC;AACtC;AAGA,SAASC,4BAA4BA,CAACC,mBAAmB,EAAE;EACzD,OAAOA,mBAAmB,KAAK,IAAI,IAAIA,mBAAmB,KAAK,KAAK,CAAC,GAAGA,mBAAmB,GAAG,EAAE;AAClG;AAGA,SAASC,mBAAmBA,CAACC,UAAU,EAAE;EACvC,OAAOA,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAGA,UAAU,GAAG,EAAE;AACvE;AACA,SAASC,aAAaA,CAACC,GAAG,EAAEC,SAAS,EAAE;EACrC,KAAK,IAAIC,CAAC,GAAGF,GAAG,CAACG,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IACxC,IAAID,SAAS,CAACD,GAAG,CAACE,CAAC,CAAC,CAAC,EAAE;MACrB,OAAOF,GAAG,CAACE,CAAC,CAAC;IACf;EACF;EACA,OAAO,IAAI;AACb;AAAC,IA+BKE,eAAe,aAAAC,qBAAA;EA0KnB,SAAAD,gBAAYE,MAAM,EAAE;IAAA,IAAAC,MAAA;IAAAC,eAAA,OAAAJ,eAAA;IAClB,IAAIK,qBAAqB;IACzBF,MAAA,GAAAG,UAAA,OAAAN,eAAA,GAAME,MAAM;IACZC,MAAA,CAAKI,iBAAiB,GAAG,YAAM;MAC7B,OAAOJ,MAAA,CAAKK,cAAc;IAC5B,CAAC;IACDL,MAAA,CAAKM,0BAA0B,GAAG,YAAM;MACtC,IAAIN,MAAA,CAAKO,4BAA4B,CAAC,CAAC,EAAE;QACvC,OAAOP,MAAA,CAAKQ,OAAO,CAACC,yBAAyB,CAAC,CAAC;MACjD,CAAC,MAAM;QACL,OAAAT,MAAA;MACF;IACF,CAAC;IACDA,MAAA,CAAKU,sBAAsB,GAAG,UAAAC,SAAS,EAAI;MACzCX,MAAA,CAAKY,iBAAiB,CAACC,GAAG,CAACF,SAAS,CAACG,GAAG,EAAEH,SAAS,CAACI,OAAO,CAAC;MAC5D,IAAIf,MAAA,CAAKgB,cAAc,EAAE;QACvBL,SAAS,CAACG,GAAG,CAACG,iBAAiB,CAAC,CAAC;MACnC;IACF,CAAC;IACDjB,MAAA,CAAKkB,wBAAwB,GAAG,UAAAP,SAAS,EAAI;MAC3CX,MAAA,CAAKY,iBAAiB,CAACO,MAAM,CAACR,SAAS,CAACG,GAAG,CAAC;IAC9C,CAAC;IACDd,MAAA,CAAKoB,mBAAmB,GAAG,UAACC,IAAI,EAAEC,QAAQ,EAAK;MAC7CD,IAAI,CAACE,OAAO,CAAC,UAAAC,GAAG,EAAI;QAClB,IAAIV,GAAG,GAAGU,GAAG,IAAI,IAAI,IAAIxB,MAAA,CAAKyB,SAAS,CAACD,GAAG,CAAC;QAC5CV,GAAG,IAAIA,GAAG,CAACY,oBAAoB,CAACJ,QAAQ,CAAC;MAC3C,CAAC,CAAC;IACJ,CAAC;IACDtB,MAAA,CAAK2B,aAAa,GAAG,UAAAC,UAAU;MAAA,OAAIA,UAAU,GAAG,QAAQ,GAAG,OAAO;IAAA;IAClE5B,MAAA,CAAK6B,kBAAkB,GAAG,CAAC;IAC3B7B,MAAA,CAAKyB,SAAS,GAAG,CAAC,CAAC;IACnBzB,MAAA,CAAK8B,OAAO,GAAG,CAAC,CAAC;IACjB9B,MAAA,CAAK+B,aAAa,GAAG,CAAC;IACtB/B,MAAA,CAAKgC,iCAAiC,GAAG,KAAK;IAC9ChC,MAAA,CAAKgB,cAAc,GAAG,KAAK;IAC3BhB,MAAA,CAAKiC,QAAQ,GAAG,KAAK;IACrBjC,MAAA,CAAKkC,UAAU,GAAG,CAAC,CAAC;IACpBlC,MAAA,CAAKmC,aAAa,GAAG,CAAC;IACtBnC,MAAA,CAAKoC,gBAAgB,GAAG,KAAK;IAC7BpC,MAAA,CAAKqC,0BAA0B,GAAG,CAAC;IACnCrC,MAAA,CAAKsC,cAAc,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC/BvC,MAAA,CAAKwC,mBAAmB,GAAG,IAAI;IAC/BxC,MAAA,CAAKY,iBAAiB,GAAG,IAAI1D,mBAAmB,CAAC,CAAC;IAClD8C,MAAA,CAAKyC,gCAAgC,GAAG,CAAC;IACzCzC,MAAA,CAAK0C,iBAAiB,GAAG,CAAC;IAC1B1C,MAAA,CAAKK,cAAc,GAAG;MACpBsC,aAAa,EAAE,CAAC;MAChBC,OAAO,EAAE,CAAC;MACVC,EAAE,EAAE,EAAE;MACNC,MAAM,EAAE,CAAC;MACTC,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE,CAAC;MACX7D,aAAa,EAAE,CAAC;MAChB8D,SAAS,EAAE;IACb,CAAC;IACDjD,MAAA,CAAKkD,UAAU,GAAG,IAAI;IACtBlD,MAAA,CAAKmD,0BAA0B,GAAG,CAAC;IACnCnD,MAAA,CAAKoD,wBAAwB,GAAG,CAAC;IACjCpD,MAAA,CAAKqD,gBAAgB,GAAG,CAAC;IACzBrD,MAAA,CAAKsD,mBAAmB,GAAG,CAAC;IAC5BtD,MAAA,CAAKuD,kBAAkB,GAAG,EAAE;IAC5BvD,MAAA,CAAKwD,iBAAiB,GAAG,UAAA1C,GAAG,EAAI;MAC9Bd,MAAA,CAAKkD,UAAU,GAAGpC,GAAG;IACvB,CAAC;IACDd,MAAA,CAAKyD,6BAA6B,GAAG,UAAAC,KAAK,EAAI;MAC5C,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;MAC/B,IAAI3D,MAAA,CAAKO,4BAA4B,CAAC,CAAC,EAAE;QAEvC,OAAoBxC,KAAK,CAAC6F,aAAa,CAACjH,IAAI,EAAE+G,KAAK,CAAC;MACtD,CAAC,MAAM,IAAIC,SAAS,EAAE;QACpB,IAAIE,iBAAiB;QACrBhG,SAAS,CAAC,OAAO6F,KAAK,CAACI,UAAU,KAAK,SAAS,EAAE,mFAAmF,GAAGC,IAAI,CAACC,SAAS,CAAC,CAACH,iBAAiB,GAAGH,KAAK,CAACI,UAAU,MAAM,IAAI,IAAID,iBAAiB,KAAK,KAAK,CAAC,GAAGA,iBAAiB,GAAG,WAAW,CAAC,GAAG,GAAG,CAAC;QAC/Q,QAIE9F,KAAK,CAAC6F,aAAa,CAAClH,UAAU,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEmH,KAAK,EAAE;YAClDO,cAAc,EAAEP,KAAK,CAACO,cAAc,IAAI,IAAI,GAAgBlG,KAAK,CAAC6F,aAAa,CAACnH,cAAc,EAE5F;cACAqH,UAAU,EAAEJ,KAAK,CAACI,UAAU;cAC5BH,SAAS,EAAEA,SAAS;cACpBO,kBAAkB,EAAER,KAAK,CAACQ;YAC5B,CAAC,CAAC,GAAGR,KAAK,CAACO;UACb,CAAC,CAAC;QAAC;MAEP,CAAC,MAAM;QAGL,OAAoBlG,KAAK,CAAC6F,aAAa,CAAClH,UAAU,EAAEgH,KAAK,CAAC;MAC5D;IACF,CAAC;IACD1D,MAAA,CAAKmE,aAAa,GAAG,UAACC,CAAC,EAAErD,OAAO,EAAEsD,KAAK,EAAK;MAC1C,IAAIC,MAAM,GAAGF,CAAC,CAACG,WAAW,CAACD,MAAM;MACjC,IAAIE,IAAI,GAAG;QACT1B,MAAM,EAAE9C,MAAA,CAAKyE,aAAa,CAACH,MAAM,CAAC;QAClC1E,MAAM,EAAEI,MAAA,CAAK0E,aAAa,CAACJ,MAAM,CAAC;QAClCD,KAAK,EAALA,KAAK;QACLM,QAAQ,EAAE;MACZ,CAAC;MACD,IAAIC,IAAI,GAAG5E,MAAA,CAAK8B,OAAO,CAACf,OAAO,CAAC;MAChC,IAAI,CAAC6D,IAAI,IAAIJ,IAAI,CAAC1B,MAAM,KAAK8B,IAAI,CAAC9B,MAAM,IAAI0B,IAAI,CAAC5E,MAAM,KAAKgF,IAAI,CAAChF,MAAM,IAAIyE,KAAK,KAAKO,IAAI,CAACP,KAAK,EAAE;QAC/FrE,MAAA,CAAKqD,gBAAgB,IAAImB,IAAI,CAAC5E,MAAM,IAAIgF,IAAI,GAAGA,IAAI,CAAChF,MAAM,GAAG,CAAC,CAAC;QAC/DI,MAAA,CAAKsD,mBAAmB,IAAIsB,IAAI,GAAG,CAAC,GAAG,CAAC;QACxC5E,MAAA,CAAK6B,kBAAkB,GAAG7B,MAAA,CAAKqD,gBAAgB,GAAGrD,MAAA,CAAKsD,mBAAmB;QAC1EtD,MAAA,CAAK8B,OAAO,CAACf,OAAO,CAAC,GAAGyD,IAAI;QAC5BxE,MAAA,CAAKqC,0BAA0B,GAAGwC,IAAI,CAACC,GAAG,CAAC9E,MAAA,CAAKqC,0BAA0B,EAAEgC,KAAK,CAAC;QAClFrE,MAAA,CAAK+E,4BAA4B,CAAC,CAAC;MACrC,CAAC,MAAM;QACL/E,MAAA,CAAK8B,OAAO,CAACf,OAAO,CAAC,CAAC4D,QAAQ,GAAG,IAAI;MACvC;MACA3E,MAAA,CAAKgF,oCAAoC,CAACjE,OAAO,CAAC;MAClDf,MAAA,CAAKiF,iBAAiB,CAAC,CAAC;MACxBjF,MAAA,CAAKkF,oBAAoB,CAAClF,MAAA,CAAK0D,KAAK,EAAE1D,MAAA,CAAKmF,KAAK,CAACC,mBAAmB,CAAC;IACvE,CAAC;IACDpF,MAAA,CAAKqF,cAAc,GAAG,UAAAtE,OAAO,EAAI;MAC/B,OAAOf,MAAA,CAAKyB,SAAS,CAACV,OAAO,CAAC;MAC9B,IAAI6D,IAAI,GAAG5E,MAAA,CAAK8B,OAAO,CAACf,OAAO,CAAC;MAChC,IAAI6D,IAAI,EAAE;QACR5E,MAAA,CAAK8B,OAAO,CAACf,OAAO,CAAC,GAAGvE,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoI,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;UACjED,QAAQ,EAAE;QACZ,CAAC,CAAC;MACJ;IACF,CAAC;IACD3E,MAAA,CAAKsF,SAAS,GAAG,UAAAlB,CAAC,EAAI;MACpB,IAAIpE,MAAA,CAAKO,4BAA4B,CAAC,CAAC,EAAE;QAGvCP,MAAA,CAAKuF,qCAAqC,CAAC,CAAC;MAC9C,CAAC,MAAM;QACLvF,MAAA,CAAKK,cAAc,CAAClB,aAAa,GAAGa,MAAA,CAAK0E,aAAa,CAACN,CAAC,CAACG,WAAW,CAACD,MAAM,CAAC;MAC9E;MACAtE,MAAA,CAAK0D,KAAK,CAAC8B,QAAQ,IAAIxF,MAAA,CAAK0D,KAAK,CAAC8B,QAAQ,CAACpB,CAAC,CAAC;MAC7CpE,MAAA,CAAK+E,4BAA4B,CAAC,CAAC;MACnC/E,MAAA,CAAKyF,uBAAuB,CAAC,CAAC;IAChC,CAAC;IACDzF,MAAA,CAAK0F,cAAc,GAAG,UAAAtB,CAAC,EAAI;MACzBpE,MAAA,CAAK0D,KAAK,CAAC8B,QAAQ,IAAIxF,MAAA,CAAK0D,KAAK,CAAC8B,QAAQ,CAACpB,CAAC,CAAC;IAC/C,CAAC;IACDpE,MAAA,CAAK2F,eAAe,GAAG,UAAAvB,CAAC,EAAI;MAC1BpE,MAAA,CAAKgF,oCAAoC,CAAChF,MAAA,CAAK4F,iBAAiB,CAAC,CAAC,CAAC;MACnE5F,MAAA,CAAK+B,aAAa,GAAG/B,MAAA,CAAK0E,aAAa,CAACN,CAAC,CAACG,WAAW,CAACD,MAAM,CAAC;IAC/D,CAAC;IACDtE,MAAA,CAAK6F,eAAe,GAAG,UAAAzB,CAAC,EAAI;MAC1BpE,MAAA,CAAKmC,aAAa,GAAGnC,MAAA,CAAK0E,aAAa,CAACN,CAAC,CAACG,WAAW,CAACD,MAAM,CAAC;IAC/D,CAAC;IACDtE,MAAA,CAAK8F,oBAAoB,GAAG,UAACC,KAAK,EAAEC,MAAM,EAAK;MAC7C,IAAID,KAAK,GAAG,CAAC,IAAIC,MAAM,GAAG,CAAC,IAAIhG,MAAA,CAAK0D,KAAK,CAACuC,kBAAkB,IAAI,IAAI,IAAIjG,MAAA,CAAK0D,KAAK,CAACuC,kBAAkB,GAAG,CAAC,IAAI,CAACjG,MAAA,CAAKgC,iCAAiC,EAAE;QACpJ,IAAIhC,MAAA,CAAK0D,KAAK,CAACwC,aAAa,IAAI,IAAI,EAAE;UACpC,IAAIlG,MAAA,CAAK0D,KAAK,CAACuC,kBAAkB,GAAGjG,MAAA,CAAK0D,KAAK,CAACyC,YAAY,CAACnG,MAAA,CAAK0D,KAAK,CAAC0C,IAAI,CAAC,EAAE;YAC5EpG,MAAA,CAAKqG,aAAa,CAAC;cACjBC,QAAQ,EAAE,KAAK;cACfjC,KAAK,EAAEvG,UAAU,CAACkC,MAAA,CAAK0D,KAAK,CAACuC,kBAAkB;YACjD,CAAC,CAAC;UACJ,CAAC,MAAM;YACLjG,MAAA,CAAKuG,WAAW,CAAC;cACfD,QAAQ,EAAE;YACZ,CAAC,CAAC;UACJ;QACF;QACAtG,MAAA,CAAKgC,iCAAiC,GAAG,IAAI;MAC/C;MACA,IAAIhC,MAAA,CAAK0D,KAAK,CAAC8C,mBAAmB,EAAE;QAClCxG,MAAA,CAAK0D,KAAK,CAAC8C,mBAAmB,CAACT,KAAK,EAAEC,MAAM,CAAC;MAC/C;MACAhG,MAAA,CAAKK,cAAc,CAACsC,aAAa,GAAG3C,MAAA,CAAK0E,aAAa,CAAC;QACrDsB,MAAM,EAANA,MAAM;QACND,KAAK,EAALA;MACF,CAAC,CAAC;MACF/F,MAAA,CAAK+E,4BAA4B,CAAC,CAAC;MACnC/E,MAAA,CAAKyF,uBAAuB,CAAC,CAAC;IAChC,CAAC;IACDzF,MAAA,CAAKyG,2BAA2B,GAAG,UAAAC,OAAO,EAAI;MAE5C,IAAI5D,MAAM,GAAG4D,OAAO,CAAC5D,MAAM,GAAG9C,MAAA,CAAKyC,gCAAgC;MAEnE,IAAItD,aAAa,GAAGuH,OAAO,CAACvH,aAAa;MACzC,IAAIyD,OAAO,GAAGE,MAAM,GAAG9C,MAAA,CAAKK,cAAc,CAACyC,MAAM;MACjD,IAAIH,aAAa,GAAG3C,MAAA,CAAKK,cAAc,CAACsC,aAAa;MACrD,OAAO;QACLxD,aAAa,EAAbA,aAAa;QACbwD,aAAa,EAAbA,aAAa;QACbG,MAAM,EAANA,MAAM;QACNF,OAAO,EAAPA;MACF,CAAC;IACH,CAAC;IACD5C,MAAA,CAAK2G,SAAS,GAAG,UAAAvC,CAAC,EAAI;MACpBpE,MAAA,CAAKY,iBAAiB,CAACW,OAAO,CAAC,UAAAZ,SAAS,EAAI;QAC1CA,SAAS,CAACgG,SAAS,CAACvC,CAAC,CAAC;MACxB,CAAC,CAAC;MACF,IAAIpE,MAAA,CAAK0D,KAAK,CAACkD,QAAQ,EAAE;QACvB5G,MAAA,CAAK0D,KAAK,CAACkD,QAAQ,CAACxC,CAAC,CAAC;MACxB;MACA,IAAIrB,SAAS,GAAGqB,CAAC,CAACyC,SAAS;MAC3B,IAAI1H,aAAa,GAAGa,MAAA,CAAK0E,aAAa,CAACN,CAAC,CAACG,WAAW,CAACuC,iBAAiB,CAAC;MACvE,IAAInE,aAAa,GAAG3C,MAAA,CAAK0E,aAAa,CAACN,CAAC,CAACG,WAAW,CAACwC,WAAW,CAAC;MACjE,IAAIjE,MAAM,GAAG9C,MAAA,CAAKyE,aAAa,CAACL,CAAC,CAACG,WAAW,CAAC2B,aAAa,CAAC;MAC5D,IAAItD,OAAO,GAAGE,MAAM,GAAG9C,MAAA,CAAKK,cAAc,CAACyC,MAAM;MACjD,IAAI9C,MAAA,CAAKO,4BAA4B,CAAC,CAAC,EAAE;QACvC,IAAIP,MAAA,CAAKK,cAAc,CAACsC,aAAa,KAAK,CAAC,EAAE;UAG3C;QACF;QACA,IAAIqE,qBAAqB,GAAGhH,MAAA,CAAKyG,2BAA2B,CAAC;UAC3DtH,aAAa,EAAbA,aAAa;UACb2D,MAAM,EAANA;QACF,CAAC,CAAC;QACF3D,aAAa,GAAG6H,qBAAqB,CAAC7H,aAAa;QACnDwD,aAAa,GAAGqE,qBAAqB,CAACrE,aAAa;QACnDG,MAAM,GAAGkE,qBAAqB,CAAClE,MAAM;QACrCF,OAAO,GAAGoE,qBAAqB,CAACpE,OAAO;MACzC;MACA,IAAIC,EAAE,GAAG7C,MAAA,CAAKK,cAAc,CAAC0C,SAAS,GAAG8B,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE/B,SAAS,GAAG/C,MAAA,CAAKK,cAAc,CAAC0C,SAAS,CAAC,GAAG,CAAC;MACnG,IAAIC,QAAQ,GAAGJ,OAAO,GAAGC,EAAE;MAC3B,IAAIA,EAAE,GAAG,GAAG,IAAI7C,MAAA,CAAKK,cAAc,CAACwC,EAAE,GAAG,GAAG,IAAIF,aAAa,GAAG,CAAC,GAAGxD,aAAa,IAAI,CAACa,MAAA,CAAKkC,UAAU,CAAC+E,IAAI,EAAE;QAC1GjK,OAAO,CAAC,iFAAiF,GAAG,sFAAsF,GAAG,iDAAiD,EAAE;UACtO6F,EAAE,EAAFA,EAAE;UACFqE,MAAM,EAAElH,MAAA,CAAKK,cAAc,CAACwC,EAAE;UAC9BF,aAAa,EAAbA;QACF,CAAC,CAAC;QACF3C,MAAA,CAAKkC,UAAU,CAAC+E,IAAI,GAAG,IAAI;MAC7B;MAGA,IAAIhE,SAAS,GAAGmB,CAAC,CAACG,WAAW,CAACtB,SAAS,GAAG,CAAC,GAAG,CAAC,GAAGmB,CAAC,CAACG,WAAW,CAACtB,SAAS;MACzEjD,MAAA,CAAKK,cAAc,GAAG;QACpBsC,aAAa,EAAbA,aAAa;QACbE,EAAE,EAAFA,EAAE;QACFD,OAAO,EAAPA,OAAO;QACPE,MAAM,EAANA,MAAM;QACNC,SAAS,EAATA,SAAS;QACTC,QAAQ,EAARA,QAAQ;QACR7D,aAAa,EAAbA,aAAa;QACb8D,SAAS,EAATA;MACF,CAAC;MACDjD,MAAA,CAAKkF,oBAAoB,CAAClF,MAAA,CAAK0D,KAAK,EAAE1D,MAAA,CAAKmF,KAAK,CAACC,mBAAmB,CAAC;MACrE,IAAI,CAACpF,MAAA,CAAK0D,KAAK,EAAE;QACf;MACF;MACA1D,MAAA,CAAKyF,uBAAuB,CAAC,CAAC;MAC9B,IAAIzC,QAAQ,KAAK,CAAC,EAAE;QAClBhD,MAAA,CAAKmH,eAAe,CAACC,QAAQ,CAAC,CAAC;MACjC;MACApH,MAAA,CAAKiF,iBAAiB,CAAC,CAAC;MACxBjF,MAAA,CAAK+E,4BAA4B,CAAC,CAAC;IACrC,CAAC;IACD/E,MAAA,CAAKqH,kBAAkB,GAAG,UAAAjD,CAAC,EAAI;MAC7BpE,MAAA,CAAKY,iBAAiB,CAACW,OAAO,CAAC,UAAAZ,SAAS,EAAI;QAC1CA,SAAS,CAAC0G,kBAAkB,CAACjD,CAAC,CAAC;MACjC,CAAC,CAAC;MACFpE,MAAA,CAAKuD,kBAAkB,CAAChC,OAAO,CAAC,UAAA+F,KAAK,EAAI;QACvCA,KAAK,CAACC,iBAAiB,CAACtG,iBAAiB,CAAC,CAAC;MAC7C,CAAC,CAAC;MACFjB,MAAA,CAAKgB,cAAc,GAAG,IAAI;MAC1BhB,MAAA,CAAK0D,KAAK,CAAC8D,iBAAiB,IAAIxH,MAAA,CAAK0D,KAAK,CAAC8D,iBAAiB,CAACpD,CAAC,CAAC;IACjE,CAAC;IACDpE,MAAA,CAAKyH,gBAAgB,GAAG,UAAArD,CAAC,EAAI;MAC3BpE,MAAA,CAAKY,iBAAiB,CAACW,OAAO,CAAC,UAAAZ,SAAS,EAAI;QAC1CA,SAAS,CAAC8G,gBAAgB,CAACrD,CAAC,CAAC;MAC/B,CAAC,CAAC;MACF,IAAIpB,QAAQ,GAAGoB,CAAC,CAACG,WAAW,CAACvB,QAAQ;MACrC,IAAIA,QAAQ,EAAE;QACZhD,MAAA,CAAKK,cAAc,CAAC2C,QAAQ,GAAGhD,MAAA,CAAKyE,aAAa,CAACzB,QAAQ,CAAC;MAC7D;MACAhD,MAAA,CAAKiF,iBAAiB,CAAC,CAAC;MACxBjF,MAAA,CAAK0D,KAAK,CAACgE,eAAe,IAAI1H,MAAA,CAAK0D,KAAK,CAACgE,eAAe,CAACtD,CAAC,CAAC;IAC7D,CAAC;IACDpE,MAAA,CAAK2H,sBAAsB,GAAG,UAAAvD,CAAC,EAAI;MACjCpE,MAAA,CAAKY,iBAAiB,CAACW,OAAO,CAAC,UAAAZ,SAAS,EAAI;QAC1CA,SAAS,CAACgH,sBAAsB,CAACvD,CAAC,CAAC;MACrC,CAAC,CAAC;MACFpE,MAAA,CAAK0D,KAAK,CAACkE,qBAAqB,IAAI5H,MAAA,CAAK0D,KAAK,CAACkE,qBAAqB,CAACxD,CAAC,CAAC;IACzE,CAAC;IACDpE,MAAA,CAAK6H,oBAAoB,GAAG,UAAAzD,CAAC,EAAI;MAC/BpE,MAAA,CAAKY,iBAAiB,CAACW,OAAO,CAAC,UAAAZ,SAAS,EAAI;QAC1CA,SAAS,CAACkH,oBAAoB,CAACzD,CAAC,CAAC;MACnC,CAAC,CAAC;MACFpE,MAAA,CAAKK,cAAc,CAAC2C,QAAQ,GAAG,CAAC;MAChChD,MAAA,CAAKiF,iBAAiB,CAAC,CAAC;MACxBjF,MAAA,CAAK0D,KAAK,CAACoE,mBAAmB,IAAI9H,MAAA,CAAK0D,KAAK,CAACoE,mBAAmB,CAAC1D,CAAC,CAAC;IACrE,CAAC;IACDpE,MAAA,CAAK+H,oBAAoB,GAAG,YAAM;MAChC/H,MAAA,CAAKkF,oBAAoB,CAAClF,MAAA,CAAK0D,KAAK,EAAE1D,MAAA,CAAKmF,KAAK,CAACC,mBAAmB,CAAC;MACrEpF,MAAA,CAAKgI,QAAQ,CAAC,UAAC7C,KAAK,EAAEzB,KAAK,EAAK;QAC9B,IAAI0B,mBAAmB,GAAGpF,MAAA,CAAKiI,0BAA0B,CAACvE,KAAK,EAAEyB,KAAK,CAACC,mBAAmB,CAAC;QAC3F,IAAI8C,UAAU,GAAGrI,eAAe,CAACsI,iBAAiB,CAACzE,KAAK,EAAE0B,mBAAmB,EAAEpF,MAAA,CAAKoI,4BAA4B,CAAC1E,KAAK,CAAC,CAAC;QACxH,IAAI0B,mBAAmB,CAACiD,KAAK,KAAKlD,KAAK,CAACC,mBAAmB,CAACiD,KAAK,IAAIjD,mBAAmB,CAACkD,IAAI,KAAKnD,KAAK,CAACC,mBAAmB,CAACkD,IAAI,IAAIJ,UAAU,CAACK,MAAM,CAACpD,KAAK,CAAC+C,UAAU,CAAC,EAAE;UACvK,OAAO,IAAI;QACb;QACA,OAAO;UACL9C,mBAAmB,EAAnBA,mBAAmB;UACnB8C,UAAU,EAAVA;QACF,CAAC;MACH,CAAC,CAAC;IACJ,CAAC;IACDlI,MAAA,CAAKwI,gBAAgB,GAAG,UAACnE,KAAK,EAAEoE,UAAU,EAAE/E,KAAK,EAE5C;MACH,IAAI0C,IAAI,GAAG1C,KAAK,CAAC0C,IAAI;QACnBsC,OAAO,GAAGhF,KAAK,CAACgF,OAAO;MACzB,IAAIC,IAAI,GAAGD,OAAO,CAACtC,IAAI,EAAE/B,KAAK,CAAC;MAC/B,OAAO;QACLA,KAAK,EAALA,KAAK;QACLsE,IAAI,EAAJA,IAAI;QACJnH,GAAG,EAAExB,MAAA,CAAK4I,aAAa,CAACD,IAAI,EAAEtE,KAAK,EAAEX,KAAK,CAAC;QAC3C+E,UAAU,EAAVA;MACF,CAAC;IACH,CAAC;IACDzI,MAAA,CAAK6I,gBAAgB,GAAG,UAACxE,KAAK,EAAEX,KAAK,EAAK;MACxC,IAAIoF,MAAM,CAACC,SAAS,CAAC1E,KAAK,CAAC,EAAE;QAC3B,OAAOrE,MAAA,CAAKgJ,uBAAuB,CAAC3E,KAAK,EAAEX,KAAK,CAAC,CAACZ,MAAM;MAC1D,CAAC,MAAM;QACL,IAAImG,YAAY,GAAGjJ,MAAA,CAAKgJ,uBAAuB,CAACnE,IAAI,CAACqE,KAAK,CAAC7E,KAAK,CAAC,EAAEX,KAAK,CAAC;QACzE,IAAIyF,SAAS,GAAG9E,KAAK,GAAGQ,IAAI,CAACqE,KAAK,CAAC7E,KAAK,CAAC;QACzC,OAAO4E,YAAY,CAACnG,MAAM,GAAGqG,SAAS,GAAGF,YAAY,CAACrJ,MAAM;MAC9D;IACF,CAAC;IACDI,MAAA,CAAKgJ,uBAAuB,GAAG,UAAC3E,KAAK,EAAEX,KAAK,EAAK;MAC/C,IAAI0F,KAAK,GAAGpJ,MAAA,CAAKqJ,gBAAgB,CAAChF,KAAK,EAAEX,KAAK,CAAC;MAC/C,IAAI0F,KAAK,IAAIA,KAAK,CAAC/E,KAAK,KAAKA,KAAK,EAAE;QAElC,OAAO+E,KAAK;MACd,CAAC,MAAM;QACL,IAAIhD,IAAI,GAAG1C,KAAK,CAAC0C,IAAI;UACnBD,YAAY,GAAGzC,KAAK,CAACyC,YAAY;UACjCmD,aAAa,GAAG5F,KAAK,CAAC4F,aAAa;QACrCzL,SAAS,CAACwG,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG8B,YAAY,CAACC,IAAI,CAAC,EAAE,4CAA4C,GAAG/B,KAAK,CAAC;QACzGxG,SAAS,CAAC,CAACyL,aAAa,EAAE,oFAAoF,CAAC;QAC/G,OAAO;UACL1J,MAAM,EAAEI,MAAA,CAAK6B,kBAAkB;UAC/BiB,MAAM,EAAE9C,MAAA,CAAK6B,kBAAkB,GAAGwC;QACpC,CAAC;MACH;IACF,CAAC;IACDrE,MAAA,CAAKqJ,gBAAgB,GAAG,UAAChF,KAAK,EAAEX,KAAK,EAAK;MACxC,IAAI0C,IAAI,GAAG1C,KAAK,CAAC0C,IAAI;QACnBsC,OAAO,GAAGhF,KAAK,CAACgF,OAAO;QACvBvC,YAAY,GAAGzC,KAAK,CAACyC,YAAY;QACjCmD,aAAa,GAAG5F,KAAK,CAAC4F,aAAa;MACrCzL,SAAS,CAACwG,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG8B,YAAY,CAACC,IAAI,CAAC,EAAE,4CAA4C,GAAG/B,KAAK,CAAC;MACzG,IAAIsE,IAAI,GAAGD,OAAO,CAACtC,IAAI,EAAE/B,KAAK,CAAC;MAC/B,IAAI+E,KAAK,GAAGpJ,MAAA,CAAK8B,OAAO,CAAC9B,MAAA,CAAK4I,aAAa,CAACD,IAAI,EAAEtE,KAAK,EAAEX,KAAK,CAAC,CAAC;MAChE,IAAI,CAAC0F,KAAK,IAAIA,KAAK,CAAC/E,KAAK,KAAKA,KAAK,EAAE;QACnC,IAAIiF,aAAa,EAAE;UAIjB,OAAOA,aAAa,CAAClD,IAAI,EAAE/B,KAAK,CAAC;QACnC;MACF;MACA,OAAO+E,KAAK;IACd,CAAC;IACDpJ,MAAA,CAAKoI,4BAA4B,GAAG,UAAA1E,KAAK,EAAI;MAI3C,IAAI,EAAE1D,MAAA,CAAKwC,mBAAmB,IAAIxC,MAAA,CAAKyB,SAAS,CAACzB,MAAA,CAAKwC,mBAAmB,CAAC,CAAC,EAAE;QAC3E,OAAO,EAAE;MACX;MACA,IAAI+G,uBAAuB,GAAGvJ,MAAA,CAAKyB,SAAS,CAACzB,MAAA,CAAKwC,mBAAmB,CAAC;MACtE,IAAIgH,gBAAgB,GAAGD,uBAAuB,CAAC7F,KAAK,CAACW,KAAK;MAC1D,IAAIoF,SAAS,GAAG/F,KAAK,CAACyC,YAAY,CAACzC,KAAK,CAAC0C,IAAI,CAAC;MAI9C,IAAIoD,gBAAgB,IAAIC,SAAS,IAAIzJ,MAAA,CAAK4I,aAAa,CAAClF,KAAK,CAACgF,OAAO,CAAChF,KAAK,CAAC0C,IAAI,EAAEoD,gBAAgB,CAAC,EAAEA,gBAAgB,EAAE9F,KAAK,CAAC,KAAK1D,MAAA,CAAKwC,mBAAmB,EAAE;QAC1J,OAAO,EAAE;MACX;MACA,IAAI6F,KAAK,GAAGmB,gBAAgB;MAC5B,IAAIE,0BAA0B,GAAG,CAAC;MAClC,KAAK,IAAI/J,CAAC,GAAG0I,KAAK,GAAG,CAAC,EAAE1I,CAAC,IAAI,CAAC,IAAI+J,0BAA0B,GAAG1J,MAAA,CAAKK,cAAc,CAAClB,aAAa,EAAEQ,CAAC,EAAE,EAAE;QACrG0I,KAAK,EAAE;QACPqB,0BAA0B,IAAI1J,MAAA,CAAKgJ,uBAAuB,CAACrJ,CAAC,EAAE+D,KAAK,CAAC,CAAC9D,MAAM;MAC7E;MACA,IAAI0I,IAAI,GAAGkB,gBAAgB;MAC3B,IAAIG,yBAAyB,GAAG,CAAC;MACjC,KAAK,IAAIC,EAAE,GAAGtB,IAAI,GAAG,CAAC,EAAEsB,EAAE,GAAGH,SAAS,IAAIE,yBAAyB,GAAG3J,MAAA,CAAKK,cAAc,CAAClB,aAAa,EAAEyK,EAAE,EAAE,EAAE;QAC7GtB,IAAI,EAAE;QACNqB,yBAAyB,IAAI3J,MAAA,CAAKgJ,uBAAuB,CAACY,EAAE,EAAElG,KAAK,CAAC,CAAC9D,MAAM;MAC7E;MACA,OAAO,CAAC;QACNyI,KAAK,EAALA,KAAK;QACLC,IAAI,EAAJA;MACF,CAAC,CAAC;IACJ,CAAC;IACDtI,MAAA,CAAK6J,WAAW,CAAC9J,MAAM,CAAC;IACxBC,MAAA,CAAKmH,eAAe,GAAG,IAAIhK,cAAc,CAAC6C,MAAA,CAAKqJ,gBAAgB,CAAC;IAChErJ,MAAA,CAAK8J,2BAA2B,GAAG,IAAIhN,WAAW,CAACkD,MAAA,CAAK+H,oBAAoB,EAAE,CAAC7H,qBAAqB,GAAGF,MAAA,CAAK0D,KAAK,CAACqG,yBAAyB,MAAM,IAAI,IAAI7J,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAG,EAAE,CAAC;IACvN,IAAIF,MAAA,CAAK0D,KAAK,CAACsG,8BAA8B,EAAE;MAC7ChK,MAAA,CAAKuD,kBAAkB,GAAGvD,MAAA,CAAK0D,KAAK,CAACsG,8BAA8B,CAACC,GAAG,CAAC,UAAAC,IAAI;QAAA,OAAK;UAC/E3C,iBAAiB,EAAE,IAAIlK,iBAAiB,CAAC6M,IAAI,CAACC,iBAAiB,CAAC;UAChEC,sBAAsB,EAAEF,IAAI,CAACE;QAC/B,CAAC;MAAA,CAAC,CAAC;IACL,CAAC,MAAM;MACL,IAAIC,YAAY,GAAGrK,MAAA,CAAK0D,KAAK;QAC3B0G,sBAAsB,GAAGC,YAAY,CAACD,sBAAsB;QAC5DD,iBAAiB,GAAGE,YAAY,CAACF,iBAAiB;MACpD,IAAIC,sBAAsB,EAAE;QAC1BpK,MAAA,CAAKuD,kBAAkB,CAAC+G,IAAI,CAAC;UAC3B/C,iBAAiB,EAAE,IAAIlK,iBAAiB,CAAC8M,iBAAiB,CAAC;UAC3DC,sBAAsB,EAAEA;QAC1B,CAAC,CAAC;MACJ;IACF;IACA,IAAIG,mBAAmB,GAAG1K,eAAe,CAAC2K,oBAAoB,CAACzK,MAAM,CAAC;IACtEC,MAAA,CAAKmF,KAAK,GAAG;MACXC,mBAAmB,EAAEmF,mBAAmB;MACxCrC,UAAU,EAAErI,eAAe,CAACsI,iBAAiB,CAACpI,MAAM,EAAEwK,mBAAmB;IAC3E,CAAC;IAIDvK,MAAA,CAAKyK,yBAAyB,GAAG,UAAAC,EAAE,EAAI;MACrC,IAAIC,YAAY,GAAG3K,MAAA,CAAK0D,KAAK,CAAClF,UAAU,GAAGkM,EAAE,CAACE,MAAM,CAACC,UAAU,GAAGH,EAAE,CAACE,MAAM,CAACE,SAAS;MACrF,IAAIC,YAAY,GAAG/K,MAAA,CAAK0D,KAAK,CAAClF,UAAU,GAAGkM,EAAE,CAACE,MAAM,CAACI,WAAW,GAAGN,EAAE,CAACE,MAAM,CAACK,YAAY;MACzF,IAAIC,YAAY,GAAGlL,MAAA,CAAK0D,KAAK,CAAClF,UAAU,GAAGkM,EAAE,CAACE,MAAM,CAACO,WAAW,GAAGT,EAAE,CAACE,MAAM,CAACQ,YAAY;MACzF,IAAIC,uBAAuB,GAAGN,YAAY,GAAGG,YAAY;MACzD,IAAII,KAAK,GAAGtL,MAAA,CAAK0D,KAAK,CAAClF,UAAU,GAAGkM,EAAE,CAACa,MAAM,IAAIb,EAAE,CAACc,WAAW,GAAGd,EAAE,CAACe,MAAM,IAAIf,EAAE,CAACgB,WAAW;MAC7F,IAAIC,aAAa,GAAGL,KAAK;MACzB,IAAID,uBAAuB,EAAE;QAC3BM,aAAa,GAAGL,KAAK,GAAG,CAAC,GAAGzG,IAAI,CAAC+G,GAAG,CAACN,KAAK,GAAGX,YAAY,EAAE,CAAC,CAAC,GAAG9F,IAAI,CAACC,GAAG,CAACwG,KAAK,IAAIP,YAAY,GAAGG,YAAY,GAAGP,YAAY,CAAC,EAAE,CAAC,CAAC;MACnI;MACA,IAAIkB,WAAW,GAAGP,KAAK,GAAGK,aAAa;MACvC,IAAI3L,MAAA,CAAK0D,KAAK,CAACoI,QAAQ,IAAI9L,MAAA,CAAKkD,UAAU,IAAIlD,MAAA,CAAKkD,UAAU,CAAC6I,iBAAiB,EAAE;QAC/E,IAAIC,IAAI,GAAGhM,MAAA,CAAKkD,UAAU,CAAC6I,iBAAiB,CAAC,CAAC;QAC9C,IAAI/L,MAAA,CAAK0D,KAAK,CAAClF,UAAU,EAAE;UACzBkM,EAAE,CAACE,MAAM,CAACC,UAAU,IAAIgB,WAAW;UACnC,IAAII,cAAc,GAAGD,IAAI,CAACnB,UAAU,GAAGc,aAAa;UACpDK,IAAI,CAACnB,UAAU,GAAG,CAAC7K,MAAA,CAAK0D,KAAK,CAAC4F,aAAa,GAAGzE,IAAI,CAAC+G,GAAG,CAACK,cAAc,EAAEjM,MAAA,CAAKqD,gBAAgB,CAAC,GAAG4I,cAAc;QAChH,CAAC,MAAM;UACLvB,EAAE,CAACE,MAAM,CAACE,SAAS,IAAIe,WAAW;UAClC,IAAIK,aAAa,GAAGF,IAAI,CAAClB,SAAS,GAAGa,aAAa;UAClDK,IAAI,CAAClB,SAAS,GAAG,CAAC9K,MAAA,CAAK0D,KAAK,CAAC4F,aAAa,GAAGzE,IAAI,CAAC+G,GAAG,CAACM,aAAa,EAAElM,MAAA,CAAKqD,gBAAgB,CAAC,GAAG6I,aAAa;QAC7G;QACAxB,EAAE,CAACyB,cAAc,CAAC,CAAC;MACrB;IACF,CAAC;IAAC,OAAAnM,MAAA;EACJ;EAACoM,SAAA,CAAAvM,eAAA,EAAAC,qBAAA;EAAA,OAAAuM,YAAA,CAAAxM,eAAA;IAAA2B,GAAA;IAAA8K,KAAA,EA9lBD,SAAA/F,WAAWA,CAACgG,MAAM,EAAE;MAClB,IAAIjG,QAAQ,GAAGiG,MAAM,GAAGA,MAAM,CAACjG,QAAQ,GAAG,IAAI;MAC9C,IAAIkG,QAAQ,GAAG,IAAI,CAAC9I,KAAK,CAACyC,YAAY,CAAC,IAAI,CAACzC,KAAK,CAAC0C,IAAI,CAAC,GAAG,CAAC;MAC3D,IAAIoG,QAAQ,GAAG,CAAC,EAAE;QAChB;MACF;MACA,IAAIpD,KAAK,GAAG,IAAI,CAACJ,uBAAuB,CAACwD,QAAQ,EAAE,IAAI,CAAC9I,KAAK,CAAC;MAC9D,IAAIZ,MAAM,GAAG+B,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEsE,KAAK,CAACtG,MAAM,GAAGsG,KAAK,CAACxJ,MAAM,GAAG,IAAI,CAACmC,aAAa,GAAG,IAAI,CAAC1B,cAAc,CAAClB,aAAa,CAAC;MAC9G,IAAI,IAAI,CAAC+D,UAAU,IAAI,IAAI,EAAE;QAC3B;MACF;MACA,IAAI,IAAI,CAACA,UAAU,CAACuJ,QAAQ,IAAI,IAAI,EAAE;QACpCC,OAAO,CAACC,IAAI,CAAC,uEAAuE,GAAG,iEAAiE,GAAG,4DAA4D,CAAC;QACxN;MACF;MACA,IAAI,CAACzJ,UAAU,CAACuJ,QAAQ,CAAClO,mBAAmB,CAAC,IAAI,CAACmF,KAAK,CAAClF,UAAU,CAAC,GAAG;QACpEoO,CAAC,EAAE9J,MAAM;QACTwD,QAAQ,EAARA;MACF,CAAC,GAAG;QACFuG,CAAC,EAAE/J,MAAM;QACTwD,QAAQ,EAARA;MACF,CAAC,CAAC;IACJ;EAAC;IAAA9E,GAAA;IAAA8K,KAAA,EAGD,SAAAjG,aAAaA,CAACkG,MAAM,EAAE;MACpB,IAAIO,WAAW,GAAG,IAAI,CAACpJ,KAAK;QAC1B0C,IAAI,GAAG0G,WAAW,CAAC1G,IAAI;QACvB5H,UAAU,GAAGsO,WAAW,CAACtO,UAAU;QACnC2H,YAAY,GAAG2G,WAAW,CAAC3G,YAAY;QACvCmD,aAAa,GAAGwD,WAAW,CAACxD,aAAa;QACzCyD,qBAAqB,GAAGD,WAAW,CAACC,qBAAqB;MAC3D,IAAIzG,QAAQ,GAAGiG,MAAM,CAACjG,QAAQ;QAC5BjC,KAAK,GAAGkI,MAAM,CAAClI,KAAK;QACpB2I,UAAU,GAAGT,MAAM,CAACS,UAAU;QAC9BC,YAAY,GAAGV,MAAM,CAACU,YAAY;MACpCpP,SAAS,CAACwG,KAAK,IAAI,CAAC,EAAE,8CAA8C,GAAGA,KAAK,GAAG,mBAAmB,CAAC;MACnGxG,SAAS,CAACsI,YAAY,CAACC,IAAI,CAAC,IAAI,CAAC,EAAE,0CAA0C,GAAGD,YAAY,CAACC,IAAI,CAAC,GAAG,mBAAmB,CAAC;MACzHvI,SAAS,CAACwG,KAAK,GAAG8B,YAAY,CAACC,IAAI,CAAC,EAAE,8CAA8C,GAAG/B,KAAK,GAAG,kBAAkB,IAAI8B,YAAY,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;MAC7I,IAAI,CAACkD,aAAa,IAAIjF,KAAK,GAAG,IAAI,CAAChC,0BAA0B,EAAE;QAC7DxE,SAAS,CAAC,CAAC,CAACkP,qBAAqB,EAAE,2FAA2F,GAAG,yFAAyF,CAAC;QAC3NA,qBAAqB,CAAC;UACpBG,iBAAiB,EAAE,IAAI,CAACrL,kBAAkB;UAC1CsL,yBAAyB,EAAE,IAAI,CAAC9K,0BAA0B;UAC1DgC,KAAK,EAALA;QACF,CAAC,CAAC;QACF;MACF;MACA,IAAI+E,KAAK,GAAG,IAAI,CAACJ,uBAAuB,CAACnE,IAAI,CAACqE,KAAK,CAAC7E,KAAK,CAAC,EAAE,IAAI,CAACX,KAAK,CAAC;MACvE,IAAIZ,MAAM,GAAG+B,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC+D,gBAAgB,CAACxE,KAAK,EAAE,IAAI,CAACX,KAAK,CAAC,GAAG,CAACuJ,YAAY,IAAI,CAAC,KAAK,IAAI,CAAC5M,cAAc,CAAClB,aAAa,GAAGiK,KAAK,CAACxJ,MAAM,CAAC,CAAC,IAAIoN,UAAU,IAAI,CAAC,CAAC;MACjK,IAAI,IAAI,CAAC9J,UAAU,IAAI,IAAI,EAAE;QAC3B;MACF;MACA,IAAI,IAAI,CAACA,UAAU,CAACuJ,QAAQ,IAAI,IAAI,EAAE;QACpCC,OAAO,CAACC,IAAI,CAAC,uEAAuE,GAAG,iEAAiE,GAAG,4DAA4D,CAAC;QACxN;MACF;MACA,IAAI,CAACzJ,UAAU,CAACuJ,QAAQ,CAACjO,UAAU,GAAG;QACpCoO,CAAC,EAAE9J,MAAM;QACTwD,QAAQ,EAARA;MACF,CAAC,GAAG;QACFuG,CAAC,EAAE/J,MAAM;QACTwD,QAAQ,EAARA;MACF,CAAC,CAAC;IACJ;EAAC;IAAA9E,GAAA;IAAA8K,KAAA,EAID,SAAAc,YAAYA,CAACb,MAAM,EAAE;MACnB,IAAI5D,IAAI,GAAG4D,MAAM,CAAC5D,IAAI;MACtB,IAAI0E,YAAY,GAAG,IAAI,CAAC3J,KAAK;QAC3B0C,IAAI,GAAGiH,YAAY,CAACjH,IAAI;QACxBsC,OAAO,GAAG2E,YAAY,CAAC3E,OAAO;QAC9BvC,YAAY,GAAGkH,YAAY,CAAClH,YAAY;MAC1C,IAAIsD,SAAS,GAAGtD,YAAY,CAACC,IAAI,CAAC;MAClC,KAAK,IAAIkH,MAAM,GAAG,CAAC,EAAEA,MAAM,GAAG7D,SAAS,EAAE6D,MAAM,EAAE,EAAE;QACjD,IAAI5E,OAAO,CAACtC,IAAI,EAAEkH,MAAM,CAAC,KAAK3E,IAAI,EAAE;UAClC,IAAI,CAACtC,aAAa,CAAC7J,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE+P,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;YAC9DlI,KAAK,EAAEiJ;UACT,CAAC,CAAC,CAAC;UACH;QACF;MACF;IACF;EAAC;IAAA9L,GAAA;IAAA8K,KAAA,EAYD,SAAAiB,cAAcA,CAAChB,MAAM,EAAE;MACrB,IAAIjG,QAAQ,GAAGiG,MAAM,CAACjG,QAAQ;QAC5BxD,MAAM,GAAGyJ,MAAM,CAACzJ,MAAM;MACxB,IAAI,IAAI,CAACI,UAAU,IAAI,IAAI,EAAE;QAC3B;MACF;MACA,IAAI,IAAI,CAACA,UAAU,CAACuJ,QAAQ,IAAI,IAAI,EAAE;QACpCC,OAAO,CAACC,IAAI,CAAC,uEAAuE,GAAG,iEAAiE,GAAG,4DAA4D,CAAC;QACxN;MACF;MACA,IAAI,CAACzJ,UAAU,CAACuJ,QAAQ,CAAClO,mBAAmB,CAAC,IAAI,CAACmF,KAAK,CAAClF,UAAU,CAAC,GAAG;QACpEoO,CAAC,EAAE9J,MAAM;QACTwD,QAAQ,EAARA;MACF,CAAC,GAAG;QACFuG,CAAC,EAAE/J,MAAM;QACTwD,QAAQ,EAARA;MACF,CAAC,CAAC;IACJ;EAAC;IAAA9E,GAAA;IAAA8K,KAAA,EACD,SAAArL,iBAAiBA,CAAA,EAAG;MAClB,IAAI,CAACL,iBAAiB,CAACW,OAAO,CAAC,UAAAZ,SAAS,EAAI;QAC1CA,SAAS,CAACM,iBAAiB,CAAC,CAAC;MAC/B,CAAC,CAAC;MACF,IAAI,CAACsC,kBAAkB,CAAChC,OAAO,CAAC,UAAAiM,CAAC,EAAI;QACnCA,CAAC,CAACjG,iBAAiB,CAACtG,iBAAiB,CAAC,CAAC;MACzC,CAAC,CAAC;MACF,IAAI,CAACiE,oBAAoB,CAAC,IAAI,CAACxB,KAAK,EAAE,IAAI,CAACyB,KAAK,CAACC,mBAAmB,CAAC;IACvE;EAAC;IAAA5D,GAAA;IAAA8K,KAAA,EACD,SAAAmB,qBAAqBA,CAAA,EAAG;MACtB,IAAI,IAAI,CAACvK,UAAU,IAAI,IAAI,EAAE;QAC3B;MACF;MACA,IAAI,CAACA,UAAU,CAACuK,qBAAqB,CAAC,CAAC;IACzC;EAAC;IAAAjM,GAAA;IAAA8K,KAAA,EAOD,SAAAoB,kBAAkBA,CAAA,EAAG;MACnB,IAAI,IAAI,CAACxK,UAAU,IAAI,IAAI,CAACA,UAAU,CAACwK,kBAAkB,EAAE;QACzD,OAAO,IAAI,CAACxK,UAAU,CAACwK,kBAAkB,CAAC,CAAC;MAC7C;IACF;EAAC;IAAAlM,GAAA;IAAA8K,KAAA,EACD,SAAAP,iBAAiBA,CAAA,EAAG;MAClB,IAAI,IAAI,CAAC7I,UAAU,IAAI,IAAI,CAACA,UAAU,CAAC6I,iBAAiB,EAAE;QACxD,OAAO,IAAI,CAAC7I,UAAU,CAAC6I,iBAAiB,CAAC,CAAC;MAC5C,CAAC,MAAM;QACL,OAAO,IAAI,CAAC7I,UAAU;MACxB;IACF;EAAC;IAAA1B,GAAA;IAAA8K,KAAA,EACD,SAAAqB,YAAYA,CAAA,EAAG;MACb,IAAI,IAAI,CAACzK,UAAU,IAAI,IAAI,CAACA,UAAU,CAACyK,YAAY,EAAE;QACnD,OAAO,IAAI,CAACzK,UAAU,CAACyK,YAAY,CAAC,CAAC;MACvC,CAAC,MAAM;QACL,OAAO,IAAI,CAACzK,UAAU;MACxB;IACF;EAAC;IAAA1B,GAAA;IAAA8K,KAAA,EACD,SAAAsB,WAAWA,CAAA,EAAG;MACZ,IAAIC,aAAa;MACjB,OAAO,CAAC,CAACA,aAAa,GAAG,IAAI,CAACrN,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGqN,aAAa,CAAC9M,OAAO,KAAK,UAAU;IAChG;EAAC;IAAAS,GAAA;IAAA8K,KAAA,EAID,SAAAwB,OAAOA,CAAA,EAAG;MACR,OAAO,IAAI,CAAC7L,QAAQ;IACtB;EAAC;IAAAT,GAAA;IAAA8K,KAAA,EA6bD,SAAAzC,WAAWA,CAACnG,KAAK,EAAE;MACjB,IAAIkD,QAAQ,GAAGlD,KAAK,CAACkD,QAAQ;QAC3BrH,UAAU,GAAGmE,KAAK,CAACnE,UAAU;QAC7B4G,YAAY,GAAGzC,KAAK,CAACyC,YAAY;QACjCC,IAAI,GAAG1C,KAAK,CAAC0C,IAAI;QACjBH,kBAAkB,GAAGvC,KAAK,CAACuC,kBAAkB;MAC/CpI,SAAS,CAET,CAAC+I,QAAQ,IAAI,CAACA,QAAQ,CAACmH,UAAU,EAAE,4FAA4F,GAAG,wDAAwD,CAAC;MAC3LlQ,SAAS,CAACyB,mBAAmB,CAACC,UAAU,CAAC,GAAG,CAAC,EAAE,yFAAyF,CAAC;MACzI1B,SAAS,CAACsI,YAAY,EAAE,2DAA2D,CAAC;MACpF,IAAIsD,SAAS,GAAGtD,YAAY,CAACC,IAAI,CAAC;MAClC,IAAIH,kBAAkB,IAAI,IAAI,IAAI,CAAC,IAAI,CAACjE,iCAAiC,KAAKiE,kBAAkB,GAAG,CAAC,IAAIwD,SAAS,GAAG,CAAC,IAAIxD,kBAAkB,IAAIwD,SAAS,CAAC,IAAI,CAAC,IAAI,CAACvH,UAAU,CAAC+D,kBAAkB,EAAE;QAChMyG,OAAO,CAACC,IAAI,CAAC,uBAAuB,GAAG1G,kBAAkB,GAAG,4BAA4B,GAAGwD,SAAS,GAAG,SAAS,CAAC;QACjH,IAAI,CAACvH,UAAU,CAAC+D,kBAAkB,GAAG,IAAI;MAC3C;MACA,IAAIjI,OAAO,IAAI,CAAC,IAAI,CAACkE,UAAU,CAAC8L,QAAQ,EAAE;QAExC,IAAIC,UAAU,GAAGrR,UAAU,CAACsR,OAAO,CAAC,IAAI,CAACxK,KAAK,CAACyK,qBAAqB,CAAC;QACrE,IAAIF,UAAU,IAAI,IAAI,IAAIA,UAAU,CAACD,QAAQ,KAAK,MAAM,EAAE;UACxDtB,OAAO,CAACC,IAAI,CAAC,4EAA4E,GAAG,sDAAsD,CAAC;UACnJ,IAAI,CAACzK,UAAU,CAAC8L,QAAQ,GAAG,IAAI;QACjC;MACF;IACF;EAAC;IAAAxM,GAAA;IAAA8K,KAAA,EAiDD,SAAArE,0BAA0BA,CAACvE,KAAK,EAAE0B,mBAAmB,EAAE;MACrD,IAAIgB,IAAI,GAAG1C,KAAK,CAAC0C,IAAI;QACnBD,YAAY,GAAGzC,KAAK,CAACyC,YAAY;MACnC,IAAInH,qBAAqB,GAAGD,8BAA8B,CAAC2E,KAAK,CAAC1E,qBAAqB,CAAC;MACvF,IAAIoP,oBAAoB,GAAG,IAAI,CAAC/N,cAAc;QAC5CsC,aAAa,GAAGyL,oBAAoB,CAACzL,aAAa;QAClDG,MAAM,GAAGsL,oBAAoB,CAACtL,MAAM;QACpC3D,aAAa,GAAGiP,oBAAoB,CAACjP,aAAa;MACpD,IAAIkP,eAAe,GAAG1L,aAAa,GAAGxD,aAAa,GAAG2D,MAAM;MAI5D,IAAI3D,aAAa,IAAI,CAAC,IAAIwD,aAAa,IAAI,CAAC,EAAE;QAC5C,OAAOyC,mBAAmB,CAACkD,IAAI,IAAInC,YAAY,CAACC,IAAI,CAAC,GAAGvG,eAAe,CAACyO,qBAAqB,CAAClJ,mBAAmB,EAAE1B,KAAK,CAAC,GAAG0B,mBAAmB;MACjJ;MACA,IAAImJ,sBAAsB;MAC1B,IAAI7K,KAAK,CAAC8K,qBAAqB,EAAE;QAC/B,IAAIC,WAAW,GAAGJ,eAAe,GAAGrP,qBAAqB,GAAGG,aAAa,GAAGR,4BAA4B,CAAC+E,KAAK,CAAC9E,mBAAmB,CAAC,GAAG,CAAC;QACvI2P,sBAAsB,GAAG;UACvBlG,KAAK,EAAE,CAAC;UACRC,IAAI,EAAEzD,IAAI,CAAC+G,GAAG,CAACxG,mBAAmB,CAACkD,IAAI,GAAGmG,WAAW,EAAEtI,YAAY,CAACC,IAAI,CAAC,GAAG,CAAC;QAC/E,CAAC;MACH,CAAC,MAAM;QAWL,IAAI1C,KAAK,CAACuC,kBAAkB,IAAI,CAAC,IAAI,CAAC5F,cAAc,CAACyC,MAAM,IAAI+B,IAAI,CAAC6J,GAAG,CAACL,eAAe,CAAC,IAAIvF,MAAM,CAAC6F,OAAO,EAAE;UAC1G,OAAOvJ,mBAAmB,CAACkD,IAAI,IAAInC,YAAY,CAACC,IAAI,CAAC,GAAGvG,eAAe,CAACyO,qBAAqB,CAAClJ,mBAAmB,EAAE1B,KAAK,CAAC,GAAG0B,mBAAmB;QACjJ;QACAmJ,sBAAsB,GAAG7Q,2BAA2B,CAACgG,KAAK,EAAE/E,4BAA4B,CAAC+E,KAAK,CAAC9E,mBAAmB,CAAC,EAAEU,mBAAmB,CAACoE,KAAK,CAACnE,UAAU,CAAC,EAAE6F,mBAAmB,EAAE,IAAI,CAAC4D,uBAAuB,EAAE,IAAI,CAAC3I,cAAc,CAAC;QACnOxC,SAAS,CAAC0Q,sBAAsB,CAACjG,IAAI,GAAGnC,YAAY,CAACC,IAAI,CAAC,EAAE,6DAA6D,CAAC;MAC5H;MACA,IAAI,IAAI,CAACxF,iBAAiB,CAACgO,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE;QAOrC,IAAIC,QAAQ,GAAG,IAAI,CAACC,uBAAuB,CAACP,sBAAsB,CAAClG,KAAK,EAAEkG,sBAAsB,CAACjG,IAAI,CAAC;QACtGiG,sBAAsB,CAACjG,IAAI,GAAGuG,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAGA,QAAQ,GAAGN,sBAAsB,CAACjG,IAAI;MACjH;MACA,OAAOiG,sBAAsB;IAC/B;EAAC;IAAA/M,GAAA;IAAA8K,KAAA,EACD,SAAAwC,uBAAuBA,CAACzG,KAAK,EAAEC,IAAI,EAAE;MACnC,KAAK,IAAIyG,EAAE,GAAG1G,KAAK,EAAE0G,EAAE,IAAIzG,IAAI,EAAEyG,EAAE,EAAE,EAAE;QACrC,IAAIC,eAAe,GAAG,IAAI,CAAC1M,cAAc,CAAC2M,GAAG,CAACF,EAAE,CAAC;QACjD,IAAIC,eAAe,IAAI,IAAI,IAAI,IAAI,CAACpO,iBAAiB,CAACsO,SAAS,CAACF,eAAe,EAAE,UAAArO,SAAS;UAAA,OAAIA,SAAS,CAACmN,OAAO,CAAC,CAAC;QAAA,EAAC,EAAE;UAClH,OAAOiB,EAAE;QACX;MACF;MACA,OAAO,IAAI;IACb;EAAC;IAAAvN,GAAA;IAAA8K,KAAA,EACD,SAAA6C,iBAAiBA,CAAA,EAAG;MAClB,IAAI,IAAI,CAAC5O,4BAA4B,CAAC,CAAC,EAAE;QACvC,IAAI,CAACC,OAAO,CAAC4O,qBAAqB,CAAC;UACjCtO,GAAG,EAAE,IAAI;UACTC,OAAO,EAAE,IAAI,CAACP,OAAO,CAACO;QACxB,CAAC,CAAC;MACJ;MAGA,IAAI,CAACsO,oBAAoB,CAAC,CAAC;IAC7B;EAAC;IAAA7N,GAAA;IAAA8K,KAAA,EACD,SAAAgD,oBAAoBA,CAAA,EAAG;MACrB,IAAI,IAAI,CAAC/O,4BAA4B,CAAC,CAAC,EAAE;QACvC,IAAI,CAACC,OAAO,CAAC+O,uBAAuB,CAAC;UACnCzO,GAAG,EAAE;QACP,CAAC,CAAC;MACJ;MACA,IAAI,CAACgJ,2BAA2B,CAAC0F,OAAO,CAAC;QACvCC,KAAK,EAAE;MACT,CAAC,CAAC;MACF,IAAI,CAAClM,kBAAkB,CAAChC,OAAO,CAAC,UAAA+F,KAAK,EAAI;QACvCA,KAAK,CAACC,iBAAiB,CAACiI,OAAO,CAAC,CAAC;MACnC,CAAC,CAAC;MACF,IAAI,CAACrI,eAAe,CAACuI,kBAAkB,CAAC,CAAC;MAGzC,IAAI,CAACC,uBAAuB,CAAC,CAAC;IAChC;EAAC;IAAAnO,GAAA;IAAA8K,KAAA,EAGD,SAAA+C,oBAAoBA,CAAA,EAAG;MAAA,IAAAO,MAAA;MACrB,IAAI,IAAI,CAAC1M,UAAU,IAAI,IAAI,CAACA,UAAU,CAAC6I,iBAAiB,EAAE;QACxD,IAAI,CAAC7I,UAAU,CAAC6I,iBAAiB,CAAC,CAAC,CAAC8D,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACpF,yBAAyB,CAAC;MAC/F,CAAC,MAAM;QACLqF,UAAU,CAAC;UAAA,OAAMF,MAAI,CAACP,oBAAoB,CAAC,CAAC;QAAA,GAAE,EAAE,CAAC;QACjD;MACF;IACF;EAAC;IAAA7N,GAAA;IAAA8K,KAAA,EAGD,SAAAqD,uBAAuBA,CAAA,EAAG;MACxB,IAAI,IAAI,CAACzM,UAAU,IAAI,IAAI,CAACA,UAAU,CAAC6I,iBAAiB,EAAE;QACxD,IAAI,CAAC7I,UAAU,CAAC6I,iBAAiB,CAAC,CAAC,CAACgE,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACtF,yBAAyB,CAAC;MAClG;IACF;EAAC;IAAAjJ,GAAA;IAAA8K,KAAA,EAcD,SAAA0D,UAAUA,CAACC,KAAK,EAAEC,mBAAmB,EAAEC,sBAAsB,EAAE9H,KAAK,EAAEC,IAAI,EAAE8H,cAAc,EAAE;MAC1F,IAAIC,KAAK,GAAG,IAAI;MAChB,IAAIC,YAAY,GAAG,IAAI,CAAC5M,KAAK;QAC3B6M,qBAAqB,GAAGD,YAAY,CAACC,qBAAqB;QAC1DC,sBAAsB,GAAGF,YAAY,CAACE,sBAAsB;QAC5DC,mBAAmB,GAAGH,YAAY,CAACG,mBAAmB;QACtDC,iBAAiB,GAAGJ,YAAY,CAACI,iBAAiB;QAClDtK,IAAI,GAAGkK,YAAY,CAAClK,IAAI;QACxBuK,KAAK,GAAGL,YAAY,CAACK,KAAK;QAC1BjI,OAAO,GAAG4H,YAAY,CAAC5H,OAAO;QAC9BvC,YAAY,GAAGmK,YAAY,CAACnK,YAAY;QACxCmD,aAAa,GAAGgH,YAAY,CAAChH,aAAa;QAC1C9K,UAAU,GAAG8R,YAAY,CAAC9R,UAAU;QACpCoS,UAAU,GAAGN,YAAY,CAACM,UAAU;MACtC,IAAIC,YAAY,GAAGJ,mBAAmB,GAAG,CAAC,GAAG,CAAC;MAC9C,IAAIK,GAAG,GAAG3K,YAAY,CAACC,IAAI,CAAC,GAAG,CAAC;MAChC,IAAI2K,WAAW;MACfzI,IAAI,GAAGzD,IAAI,CAAC+G,GAAG,CAACkF,GAAG,EAAExI,IAAI,CAAC;MAC1B,IAAI0I,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAG;QAC3B,IAAIrI,IAAI,GAAGD,OAAO,CAACtC,IAAI,EAAE2I,EAAE,CAAC;QAC5B,IAAIvN,GAAG,GAAG6O,KAAK,CAACzH,aAAa,CAACD,IAAI,EAAEoG,EAAE,EAAEsB,KAAK,CAAC3M,KAAK,CAAC;QACpD2M,KAAK,CAAC/N,cAAc,CAAC2O,GAAG,CAAClC,EAAE,EAAEvN,GAAG,CAAC;QACjC,IAAI2O,sBAAsB,CAACe,GAAG,CAACnC,EAAE,GAAG8B,YAAY,CAAC,EAAE;UACjDX,mBAAmB,CAAC5F,IAAI,CAAC2F,KAAK,CAACrQ,MAAM,CAAC;QACxC;QACA,IAAIuR,qBAAqB,GAAG7H,aAAa,IAAI,IAAI,IAAIqH,KAAK,IAAIN,KAAK,CAAClJ,eAAe,CAACiK,OAAO,CAAC,CAAC;QAC7FnB,KAAK,CAAC3F,IAAI,CAAcvM,KAAK,CAAC6F,aAAa,CAACtG,YAAY,EAAEf,QAAQ,CAAC;UACjEgU,qBAAqB,EAAEA,qBAAqB;UAC5CC,sBAAsB,EAAEzB,EAAE,GAAG+B,GAAG,GAAGN,sBAAsB,GAAGa,SAAS;UACrEX,iBAAiB,EAAEA,iBAAiB;UACpC3P,OAAO,EAAES,GAAG;UACZhD,UAAU,EAAEA,UAAU;UACtB6F,KAAK,EAAE0K,EAAE;UACTqB,cAAc,EAAEA,cAAc;UAC9BzH,IAAI,EAAEA,IAAI;UACVnH,GAAG,EAAEA,GAAG;UACRuP,WAAW,EAAEA,WAAW;UACxBO,kBAAkB,EAAEjB,KAAK,CAACjP,mBAAmB;UAC7CmQ,kBAAkB,EAAE,SAApBA,kBAAkBA,CAAEnN,CAAC;YAAA,OAAIiM,KAAK,CAACmB,mBAAmB,CAAChQ,GAAG,CAAC;UAAA;UACvDiQ,SAAS,EAAEpB,KAAK,CAAChL,cAAc;UAC/BvE,GAAG,EAAE,SAALA,GAAGA,CAAE4Q,IAAI,EAAI;YACXrB,KAAK,CAAC5O,SAAS,CAACD,GAAG,CAAC,GAAGkQ,IAAI;UAC7B,CAAC;UACDd,UAAU,EAAEA;QACd,CAAC,EAAEO,qBAAqB,IAAI;UAC1BQ,YAAY,EAAEtB,KAAK,CAAClM;QACtB,CAAC,CAAC,CAAC,CAAC;QACJ4M,WAAW,GAAGvP,GAAG;MACnB,CAAC;MACD,KAAK,IAAIuN,EAAE,GAAG1G,KAAK,EAAE0G,EAAE,IAAIzG,IAAI,EAAEyG,EAAE,EAAE,EAAE;QACrCiC,KAAK,CAAC,CAAC;MACT;IACF;EAAC;IAAAxP,GAAA;IAAA8K,KAAA,EAUD,SAAA/L,4BAA4BA,CAAA,EAAG;MAC7B,IAAIqR,aAAa,GAAG,IAAI,CAACpR,OAAO;MAChC,OAAO,CAAC,EAAEoR,aAAa,IAAI,CAAC,CAACA,aAAa,CAACpT,UAAU,KAAKD,mBAAmB,CAAC,IAAI,CAACmF,KAAK,CAAClF,UAAU,CAAC,CAAC;IACvG;EAAC;IAAAgD,GAAA;IAAA8K,KAAA,EACD,SAAA1D,aAAaA,CAACD,IAAI,EAAEtE,KAAK,EAAEX,KAAK,EAE9B;MACA,IAAIA,KAAK,CAAC/F,YAAY,IAAI,IAAI,EAAE;QAC9B,OAAO+F,KAAK,CAAC/F,YAAY,CAACgL,IAAI,EAAEtE,KAAK,CAAC;MACxC;MACA,IAAI7C,GAAG,GAAG5D,mBAAmB,CAAC+K,IAAI,EAAEtE,KAAK,CAAC;MAC1C,IAAI7C,GAAG,KAAKqQ,MAAM,CAACxN,KAAK,CAAC,EAAE;QACzBhG,gBAAgB,GAAG,IAAI;QACvB,IAAIsK,IAAI,CAACmJ,IAAI,IAAInJ,IAAI,CAACmJ,IAAI,CAACC,WAAW,EAAE;UACtCzT,yBAAyB,GAAGqK,IAAI,CAACmJ,IAAI,CAACC,WAAW;QACnD;MACF;MACA,OAAOvQ,GAAG;IACZ;EAAC;IAAAA,GAAA;IAAA8K,KAAA,EACD,SAAA0F,MAAMA,CAAA,EAAG;MAAA,IAAAC,MAAA;MACP,IAAI,CAACpI,WAAW,CAAC,IAAI,CAACnG,KAAK,CAAC;MAC5B,IAAIwO,YAAY,GAAG,IAAI,CAACxO,KAAK;QAC3ByO,kBAAkB,GAAGD,YAAY,CAACC,kBAAkB;QACpDC,mBAAmB,GAAGF,YAAY,CAACE,mBAAmB;QACtD3B,mBAAmB,GAAGyB,YAAY,CAACzB,mBAAmB;MACxD,IAAI4B,YAAY,GAAG,IAAI,CAAC3O,KAAK;QAC3B0C,IAAI,GAAGiM,YAAY,CAACjM,IAAI;QACxB5H,UAAU,GAAG6T,YAAY,CAAC7T,UAAU;MACtC,IAAI4R,cAAc,GAAG,IAAI,CAAC1M,KAAK,CAACoI,QAAQ,GAAGvN,mBAAmB,CAAC,IAAI,CAACmF,KAAK,CAAClF,UAAU,CAAC,GAAG8T,MAAM,CAACC,oBAAoB,GAAGD,MAAM,CAACE,kBAAkB,GAAG,IAAI;MACtJ,IAAIvC,KAAK,GAAG,EAAE;MACd,IAAIE,sBAAsB,GAAG,IAAIsC,GAAG,CAAC,IAAI,CAAC/O,KAAK,CAACwM,mBAAmB,CAAC;MACpE,IAAIA,mBAAmB,GAAG,EAAE;MAG5B,IAAIO,mBAAmB,EAAE;QACvB,IAAIN,sBAAsB,CAACe,GAAG,CAAC,CAAC,CAAC,EAAE;UACjChB,mBAAmB,CAAC5F,IAAI,CAAC,CAAC,CAAC;QAC7B;QACA,IAAIoI,QAAQ,GAAgB3U,KAAK,CAAC4U,cAAc,CAAClC,mBAAmB,CAAC,GAAGA,mBAAmB,GAI3F1S,KAAK,CAAC6F,aAAa,CAAC6M,mBAAmB,EAAE,IAAI,CAAC;QAC9CR,KAAK,CAAC3F,IAAI,CAAcvM,KAAK,CAAC6F,aAAa,CAACrG,kCAAkC,EAAE;UAC9EwD,OAAO,EAAE,IAAI,CAAC6M,WAAW,CAAC,CAAC,GAAG,SAAS;UACvCpM,GAAG,EAAE;QACP,CAAC,EAAezD,KAAK,CAAC6F,aAAa,CAACjH,IAAI,EAAE;UACxC6I,QAAQ,EAAE,IAAI,CAACK,eAAe;UAC9B+M,KAAK,EAAE,CAACxC,cAAc,EAAE,IAAI,CAAC1M,KAAK,CAACmP,wBAAwB;QAC7D,CAAC,EAEDH,QAAQ,CAAC,CAAC,CAAC;MACb;MAGA,IAAIjJ,SAAS,GAAG,IAAI,CAAC/F,KAAK,CAACyC,YAAY,CAACC,IAAI,CAAC;MAC7C,IAAIqD,SAAS,KAAK,CAAC,IAAI0I,kBAAkB,EAAE;QACzC,IAAIW,SAAS,GAAgB/U,KAAK,CAAC4U,cAAc,CAACR,kBAAkB,CAAC,GAAGA,kBAAkB,GAI1FpU,KAAK,CAAC6F,aAAa,CAACuO,kBAAkB,EAAE,IAAI,CAAC;QAC7ClC,KAAK,CAAC3F,IAAI,CAAcvM,KAAK,CAAC6F,aAAa,CAACrG,kCAAkC,EAAE;UAC9EwD,OAAO,EAAE,IAAI,CAAC6M,WAAW,CAAC,CAAC,GAAG,QAAQ;UACtCpM,GAAG,EAAE;QACP,CAAC,EAAezD,KAAK,CAACgV,YAAY,CAACD,SAAS,EAAE;UAC5CtN,QAAQ,EAAE,SAAVA,QAAQA,CAAEwN,KAAK,EAAI;YACjBf,MAAI,CAACvM,cAAc,CAACsN,KAAK,CAAC;YAC1B,IAAIF,SAAS,CAACpP,KAAK,CAAC8B,QAAQ,EAAE;cAC5BsN,SAAS,CAACpP,KAAK,CAAC8B,QAAQ,CAACwN,KAAK,CAAC;YACjC;UACF,CAAC;UACDJ,KAAK,EAAE,CAACxC,cAAc,EAAE0C,SAAS,CAACpP,KAAK,CAACkP,KAAK;QAC/C,CAAC,CAAC,CAAC,CAAC;MACN;MAGA,IAAInJ,SAAS,GAAG,CAAC,EAAE;QACjBpL,gBAAgB,GAAG,KAAK;QACxBC,yBAAyB,GAAG,EAAE;QAC9B,IAAI2U,SAAS,GAAG,IAAI,CAACtR,aAAa,CAAC,CAACnD,UAAU,CAAC;QAC/C,IAAI0U,aAAa,GAAG,IAAI,CAAC/N,KAAK,CAAC+C,UAAU,CAACiL,gBAAgB,CAAC,CAAC;QAC5D,IAAIC,UAAU,GAAG5T,aAAa,CAAC0T,aAAa,EAAE,UAAAG,CAAC;UAAA,OAAIA,CAAC,CAACC,QAAQ;QAAA,EAAC;QAC9D,KAAK,IAAIC,SAAS,GAAGjX,+BAA+B,CAAC4W,aAAa,CAAC,EAAEM,KAAK,EAAE,CAAC,CAACA,KAAK,GAAGD,SAAS,CAAC,CAAC,EAAEE,IAAI,GAAG;UACxG,IAAIC,OAAO,GAAGF,KAAK,CAAClH,KAAK;UACzB,IAAIoH,OAAO,CAACJ,QAAQ,EAAE;YAGpB,IAAI,IAAI,CAAC5P,KAAK,CAAC8K,qBAAqB,EAAE;cACpC;YACF;YAKA,IAAImF,YAAY,GAAGD,OAAO,KAAKN,UAAU;YACzC,IAAIQ,mBAAmB,GAAGD,YAAY,IAAI,CAAC,IAAI,CAACjQ,KAAK,CAAC4F,aAAa;YACnE,IAAIhB,IAAI,GAAGsL,mBAAmB,GAAG7W,KAAK,CAAC2W,OAAO,CAACrL,KAAK,GAAG,CAAC,EAAEqL,OAAO,CAACpL,IAAI,EAAE,IAAI,CAACjG,0BAA0B,CAAC,GAAGqR,OAAO,CAACpL,IAAI;YACvH,IAAIuL,YAAY,GAAG,IAAI,CAAC7K,uBAAuB,CAAC0K,OAAO,CAACrL,KAAK,EAAE,IAAI,CAAC3E,KAAK,CAAC;YAC1E,IAAIoQ,WAAW,GAAG,IAAI,CAAC9K,uBAAuB,CAACV,IAAI,EAAE,IAAI,CAAC5E,KAAK,CAAC;YAChE,IAAIqQ,UAAU,GAAGD,WAAW,CAAChR,MAAM,GAAGgR,WAAW,CAAClU,MAAM,GAAGiU,YAAY,CAAC/Q,MAAM;YAC9EmN,KAAK,CAAC3F,IAAI,CAAcvM,KAAK,CAAC6F,aAAa,CAACjH,IAAI,EAAE;cAChD6E,GAAG,EAAE,UAAU,GAAGkS,OAAO,CAACrL,KAAK;cAC/BuK,KAAK,EAAAoB,eAAA,KACFf,SAAS,EAAGc,UAAU;YAE3B,CAAC,CAAC,CAAC;UACL,CAAC,MAAM;YACL,IAAI,CAAC/D,UAAU,CAACC,KAAK,EAAEC,mBAAmB,EAAEC,sBAAsB,EAAEuD,OAAO,CAACrL,KAAK,EAAEqL,OAAO,CAACpL,IAAI,EAAE8H,cAAc,CAAC;UAClH;QACF;QACA,IAAI,CAAC,IAAI,CAAClO,UAAU,CAACb,IAAI,IAAIhD,gBAAgB,EAAE;UAC7CqO,OAAO,CAACC,IAAI,CAAC,6FAA6F,GAAG,wCAAwC,EAAErO,yBAAyB,CAAC;UACjL,IAAI,CAAC4D,UAAU,CAACb,IAAI,GAAG,IAAI;QAC7B;MACF;MAGA,IAAI+Q,mBAAmB,EAAE;QACvB,IAAI6B,SAAS,GAAgBlW,KAAK,CAAC4U,cAAc,CAACP,mBAAmB,CAAC,GAAGA,mBAAmB,GAI5FrU,KAAK,CAAC6F,aAAa,CAACwO,mBAAmB,EAAE,IAAI,CAAC;QAC9CnC,KAAK,CAAC3F,IAAI,CAAcvM,KAAK,CAAC6F,aAAa,CAACrG,kCAAkC,EAAE;UAC9EwD,OAAO,EAAE,IAAI,CAAC6E,iBAAiB,CAAC,CAAC;UACjCpE,GAAG,EAAE;QACP,CAAC,EAAezD,KAAK,CAAC6F,aAAa,CAACjH,IAAI,EAAE;UACxC6I,QAAQ,EAAE,IAAI,CAACG,eAAe;UAC9BiN,KAAK,EAAE,CAACxC,cAAc,EAAE,IAAI,CAAC1M,KAAK,CAACwQ,wBAAwB;QAC7D,CAAC,EAEDD,SAAS,CAAC,CAAC,CAAC;MACd;MAGA,IAAIE,WAAW,GAAG3X,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,CAACkH,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QACjE8C,mBAAmB,EAAE,IAAI,CAACV,oBAAoB;QAC9CN,QAAQ,EAAE,IAAI,CAACF,SAAS;QACxBsB,QAAQ,EAAE,IAAI,CAACD,SAAS;QACxBa,iBAAiB,EAAE,IAAI,CAACH,kBAAkB;QAC1CK,eAAe,EAAE,IAAI,CAACD,gBAAgB;QACtCG,qBAAqB,EAAE,IAAI,CAACD,sBAAsB;QAClDG,mBAAmB,EAAE,IAAI,CAACD,oBAAoB;QAC9CxI,mBAAmB,EAAED,4BAA4B,CAAC,IAAI,CAACsE,KAAK,CAACrE,mBAAmB,CAAC;QAEjF+U,mBAAmB,EAAE,IAAI,CAAC1Q,KAAK,CAAC0Q,mBAAmB,KAAK/C,SAAS,GAAG,IAAI,CAAC3N,KAAK,CAAC0Q,mBAAmB,GAAG,IAAI,CAAC1Q,KAAK,CAACoI,QAAQ;QACxHoE,mBAAmB,EAAnBA,mBAAmB;QACnB0C,KAAK,EAAExC,cAAc,GAAG,CAACA,cAAc,EAAE,IAAI,CAAC1M,KAAK,CAACkP,KAAK,CAAC,GAAG,IAAI,CAAClP,KAAK,CAACkP;MAC1E,CAAC,CAAC;MACF,IAAI,CAAC3Q,QAAQ,GAAG,IAAI,CAACkD,KAAK,CAACC,mBAAmB,CAACkD,IAAI,GAAGmB,SAAS,GAAG,CAAC;MACnE,IAAI4K,QAAQ,GAAgBtW,KAAK,CAAC6F,aAAa,CAACnG,8BAA8B,EAAE;QAC9E6O,KAAK,EAAE;UACLvL,OAAO,EAAE,IAAI;UACbuT,gBAAgB,EAAE,IAAI,CAAClU,iBAAiB;UACxC5B,UAAU,EAAED,mBAAmB,CAAC,IAAI,CAACmF,KAAK,CAAClF,UAAU,CAAC;UACtDiC,yBAAyB,EAAE,IAAI,CAACH,0BAA0B;UAC1D8O,qBAAqB,EAAE,IAAI,CAAC1O,sBAAsB;UAClD6O,uBAAuB,EAAE,IAAI,CAACrO;QAChC;MACF,CAAC,EAAenD,KAAK,CAACgV,YAAY,CAAC,CAAC,IAAI,CAACrP,KAAK,CAAC6Q,qBAAqB,IAAI,IAAI,CAAC9Q,6BAA6B,EAAE0Q,WAAW,CAAC,EAAE;QACxHrT,GAAG,EAAE,IAAI,CAAC0C;MACZ,CAAC,EAAEyM,KAAK,CAAC,CAAC;MACV,IAAIuE,GAAG,GAAGH,QAAQ;MA2BlB,IAAI,IAAI,CAAC3Q,KAAK,CAACiN,KAAK,EAAE;QACpB,OAAoB5S,KAAK,CAAC6F,aAAa,CAACjH,IAAI,EAAE;UAC5CiW,KAAK,EAAEN,MAAM,CAAC3B;QAChB,CAAC,EAAE6D,GAAG,EAAE,IAAI,CAACC,mBAAmB,CAAC,CAAC,CAAC;MACrC,CAAC,MAAM;QACL,OAAOD,GAAG;MACZ;IACF;EAAC;IAAAhT,GAAA;IAAA8K,KAAA,EACD,SAAAoI,kBAAkBA,CAACC,SAAS,EAAE;MAC5B,IAAIC,YAAY,GAAG,IAAI,CAAClR,KAAK;QAC3B0C,IAAI,GAAGwO,YAAY,CAACxO,IAAI;QACxByO,SAAS,GAAGD,YAAY,CAACC,SAAS;MACpC,IAAIzO,IAAI,KAAKuO,SAAS,CAACvO,IAAI,IAAIyO,SAAS,KAAKF,SAAS,CAACE,SAAS,EAAE;QAGhE,IAAI,CAACtR,kBAAkB,CAAChC,OAAO,CAAC,UAAA+F,KAAK,EAAI;UACvCA,KAAK,CAACC,iBAAiB,CAACuN,oBAAoB,CAAC,CAAC;QAChD,CAAC,CAAC;MACJ;MAOA,IAAIC,eAAe,GAAG,IAAI,CAAC3S,gBAAgB;MAC3C,IAAI,CAAC2C,4BAA4B,CAAC,CAAC;MAGnC,IAAIgQ,eAAe,EAAE;QACnB,IAAI,CAAC3S,gBAAgB,GAAG,KAAK;MAC/B;IACF;EAAC;IAAAZ,GAAA;IAAA8K,KAAA,EAWD,SAAArH,iBAAiBA,CAAA,EAAG;MAClB,IAAI,CAACkC,eAAe,CAAC6N,gBAAgB,CAAC,IAAI,CAACtR,KAAK,EAAE,IAAI,CAACyB,KAAK,CAACC,mBAAmB,EAAE,IAAI,CAAC/E,cAAc,CAAC;IACxG;EAAC;IAAAmB,GAAA;IAAA8K,KAAA,EAKD,SAAAkF,mBAAmBA,CAACzQ,OAAO,EAAE;MAC3B,IAAI,CAACyB,mBAAmB,GAAGzB,OAAO;MAClC,IAAI,CAACgH,oBAAoB,CAAC,CAAC;IAC7B;EAAC;IAAAvG,GAAA;IAAA8K,KAAA,EACD,SAAAtH,oCAAoCA,CAACjE,OAAO,EAAE;MAC5C,IAAI,CAACH,iBAAiB,CAACqU,aAAa,CAAClU,OAAO,EAAE,UAAAJ,SAAS,EAAI;QACzDA,SAAS,CAAC4E,qCAAqC,CAAC,CAAC;MACnD,CAAC,CAAC;IACJ;EAAC;IAAA/D,GAAA;IAAA8K,KAAA,EACD,SAAA/G,qCAAqCA,CAAA,EAAG;MAAA,IAAA2P,MAAA;MAGtC,IAAI;QACF,IAAI,CAAC,IAAI,CAAChS,UAAU,EAAE;UACpB;QACF;QAGA,IAAI,CAACA,UAAU,CAACiS,aAAa,CAAC,IAAI,CAAC3U,OAAO,CAACC,yBAAyB,CAAC,CAAC,CAACkN,YAAY,CAAC,CAAC,EAAE,UAACf,CAAC,EAAEC,CAAC,EAAE9G,KAAK,EAAEC,MAAM,EAAK;UAC9GkP,MAAI,CAACzS,gCAAgC,GAAGyS,MAAI,CAACzQ,aAAa,CAAC;YACzDmI,CAAC,EAADA,CAAC;YACDC,CAAC,EAADA;UACF,CAAC,CAAC;UACFqI,MAAI,CAAC7U,cAAc,CAACsC,aAAa,GAAGuS,MAAI,CAACxQ,aAAa,CAAC;YACrDqB,KAAK,EAALA,KAAK;YACLC,MAAM,EAANA;UACF,CAAC,CAAC;UACF,IAAIoP,aAAa,GAAGF,MAAI,CAACzO,2BAA2B,CAACyO,MAAI,CAAC1U,OAAO,CAAC8T,gBAAgB,CAAC,CAAC,CAAC;UACrF,IAAIe,cAAc,GAAGH,MAAI,CAAC7U,cAAc,CAAClB,aAAa,KAAKiW,aAAa,CAACjW,aAAa,IAAI+V,MAAI,CAAC7U,cAAc,CAACyC,MAAM,KAAKsS,aAAa,CAACtS,MAAM;UAC7I,IAAIuS,cAAc,EAAE;YAClBH,MAAI,CAAC7U,cAAc,CAAClB,aAAa,GAAGiW,aAAa,CAACjW,aAAa;YAC/D+V,MAAI,CAAC7U,cAAc,CAACyC,MAAM,GAAGsS,aAAa,CAACtS,MAAM;YAIjDoS,MAAI,CAACtU,iBAAiB,CAACW,OAAO,CAAC,UAAAZ,SAAS,EAAI;cAC1CA,SAAS,CAAC4E,qCAAqC,CAAC,CAAC;YACnD,CAAC,CAAC;UACJ;QACF,CAAC,EAAE,UAAA+P,KAAK,EAAI;UACV5I,OAAO,CAACC,IAAI,CAAC,gEAAgE,GAAG,8CAA8C,CAAC;QACjI,CAAC,CAAC;MACJ,CAAC,CAAC,OAAO2I,KAAK,EAAE;QACd5I,OAAO,CAACC,IAAI,CAAC,sDAAsD,EAAE2I,KAAK,CAACC,KAAK,CAAC;MACnF;IACF;EAAC;IAAA/T,GAAA;IAAA8K,KAAA,EACD,SAAA1G,iBAAiBA,CAAA,EAAG;MAClB,OAAO,IAAI,CAACgI,WAAW,CAAC,CAAC,GAAG,SAAS;IACvC;EAAC;IAAApM,GAAA;IAAA8K,KAAA,EAED,SAAAmI,mBAAmBA,CAAA,EAAG;MACpB,IAAIe,SAAS,GAAG,IAAI,CAACnV,cAAc,CAAClB,aAAa,IAAI,IAAI,CAACkB,cAAc,CAACsC,aAAa,IAAI,CAAC,CAAC;MAC5F,IAAI8S,cAAc,GAAG,EAAE;MACvB,IAAIhM,SAAS,GAAG,IAAI,CAAC/F,KAAK,CAACyC,YAAY,CAAC,IAAI,CAACzC,KAAK,CAAC0C,IAAI,CAAC;MACxD,KAAK,IAAI2I,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGtF,SAAS,EAAEsF,EAAE,EAAE,EAAE;QACrC,IAAI3F,KAAK,GAAG,IAAI,CAACJ,uBAAuB,CAAC+F,EAAE,EAAE,IAAI,CAACrL,KAAK,CAAC;QAIxD,IAAI0F,KAAK,CAACzE,QAAQ,EAAE;UAClB8Q,cAAc,CAACnL,IAAI,CAAClB,KAAK,CAAC;QAC5B;MACF;MACA,IAAIsM,SAAS,GAAG,IAAI,CAAC1M,uBAAuB,CAAC,IAAI,CAAC7D,KAAK,CAACC,mBAAmB,CAACiD,KAAK,EAAE,IAAI,CAAC3E,KAAK,CAAC,CAACZ,MAAM;MACrG,IAAI6S,SAAS,GAAG,IAAI,CAAC3M,uBAAuB,CAAC,IAAI,CAAC7D,KAAK,CAACC,mBAAmB,CAACkD,IAAI,EAAE,IAAI,CAAC5E,KAAK,CAAC;MAC7F,IAAIkS,SAAS,GAAGD,SAAS,CAAC7S,MAAM,GAAG6S,SAAS,CAAC/V,MAAM,GAAG8V,SAAS;MAC/D,IAAIG,MAAM,GAAG,IAAI,CAACxV,cAAc,CAACyC,MAAM;MACvC,IAAIgT,MAAM,GAAG,IAAI,CAACzV,cAAc,CAAClB,aAAa;MAC9C,OAAoBpB,KAAK,CAAC6F,aAAa,CAACjH,IAAI,EAAE;QAC5CiW,KAAK,EAAE,CAACN,MAAM,CAACyD,gBAAgB,EAAEzD,MAAM,CAAC0D,YAAY;MACtD,CAAC,EAAEP,cAAc,CAACxL,GAAG,CAAC,UAACgM,CAAC,EAAElH,EAAE;QAAA,OAAkBhR,KAAK,CAAC6F,aAAa,CAACjH,IAAI,EAAE;UACtE6E,GAAG,EAAE,GAAG,GAAGuN,EAAE;UACb6D,KAAK,EAAE,CAACN,MAAM,CAACyD,gBAAgB,EAAEzD,MAAM,CAAC4D,iBAAiB,EAAE;YACzDC,GAAG,EAAEF,CAAC,CAACnT,MAAM,GAAG0S,SAAS;YACzBxP,MAAM,EAAEiQ,CAAC,CAACrW,MAAM,GAAG4V;UACrB,CAAC;QACH,CAAC,CAAC;MAAA,EAAC,EAAezX,KAAK,CAAC6F,aAAa,CAACjH,IAAI,EAAE;QAC1CiW,KAAK,EAAE,CAACN,MAAM,CAACyD,gBAAgB,EAAEzD,MAAM,CAAC8D,qBAAqB,EAAE;UAC7DD,GAAG,EAAET,SAAS,GAAGF,SAAS;UAC1BxP,MAAM,EAAE4P,SAAS,GAAGJ;QACtB,CAAC;MACH,CAAC,CAAC,EAAezX,KAAK,CAAC6F,aAAa,CAACjH,IAAI,EAAE;QACzCiW,KAAK,EAAE,CAACN,MAAM,CAACyD,gBAAgB,EAAEzD,MAAM,CAAC+D,oBAAoB,EAAE;UAC5DF,GAAG,EAAEN,MAAM,GAAGL,SAAS;UACvBxP,MAAM,EAAE8P,MAAM,GAAGN;QACnB,CAAC;MACH,CAAC,CAAC,CAAC;IACL;EAAC;IAAAhU,GAAA;IAAA8K,KAAA,EACD,SAAA5H,aAAaA,CAACgC,OAAO,EAAE;MACrB,OAAO,CAACnI,mBAAmB,CAAC,IAAI,CAACmF,KAAK,CAAClF,UAAU,CAAC,GAAGkI,OAAO,CAACV,MAAM,GAAGU,OAAO,CAACX,KAAK;IACrF;EAAC;IAAAvE,GAAA;IAAA8K,KAAA,EACD,SAAA7H,aAAaA,CAACiC,OAAO,EAAE;MACrB,OAAO,CAACnI,mBAAmB,CAAC,IAAI,CAACmF,KAAK,CAAClF,UAAU,CAAC,GAAGkI,OAAO,CAACmG,CAAC,GAAGnG,OAAO,CAACkG,CAAC;IAC5E;EAAC;IAAApL,GAAA;IAAA8K,KAAA,EACD,SAAA7G,uBAAuBA,CAAA,EAAG;MACxB,IAAI6Q,YAAY,GAAG,IAAI,CAAC5S,KAAK;QAC3B0C,IAAI,GAAGkQ,YAAY,CAAClQ,IAAI;QACxBD,YAAY,GAAGmQ,YAAY,CAACnQ,YAAY;QACxCoQ,cAAc,GAAGD,YAAY,CAACC,cAAc;QAC5CzX,uBAAuB,GAAGwX,YAAY,CAACxX,uBAAuB;QAC9D0X,YAAY,GAAGF,YAAY,CAACE,YAAY;QACxCxX,qBAAqB,GAAGsX,YAAY,CAACtX,qBAAqB;QAC1DiH,kBAAkB,GAAGqQ,YAAY,CAACrQ,kBAAkB;MACtD,IAAIwQ,qBAAqB,GAAG,IAAI,CAACpW,cAAc;QAC7CsC,aAAa,GAAG8T,qBAAqB,CAAC9T,aAAa;QACnDxD,aAAa,GAAGsX,qBAAqB,CAACtX,aAAa;QACnD2D,MAAM,GAAG2T,qBAAqB,CAAC3T,MAAM;MACvC,IAAI4T,iBAAiB,GAAG5T,MAAM;MAC9B,IAAIuL,eAAe,GAAG1L,aAAa,GAAGxD,aAAa,GAAG2D,MAAM;MAK5D,IAAI4T,iBAAiB,GAAGtY,uBAAuB,EAAE;QAC/CsY,iBAAiB,GAAG,CAAC;MACvB;MACA,IAAIrI,eAAe,GAAGjQ,uBAAuB,EAAE;QAC7CiQ,eAAe,GAAG,CAAC;MACrB;MAIA,IAAIsI,oBAAoB,GAAG,CAAC;MAC5B,IAAIC,cAAc,GAAG9X,uBAAuB,IAAI,IAAI,GAAGA,uBAAuB,GAAGK,aAAa,GAAGwX,oBAAoB;MACrH,IAAIE,YAAY,GAAG7X,qBAAqB,IAAI,IAAI,GAAGA,qBAAqB,GAAGG,aAAa,GAAGwX,oBAAoB;MAC/G,IAAIG,sBAAsB,GAAGJ,iBAAiB,IAAIE,cAAc;MAChE,IAAIG,oBAAoB,GAAG1I,eAAe,IAAIwI,YAAY;MAK1D,IAAIL,YAAY,IAAI,IAAI,CAACrR,KAAK,CAACC,mBAAmB,CAACkD,IAAI,KAAKnC,YAAY,CAACC,IAAI,CAAC,GAAG,CAAC,IAAI2Q,oBAAoB,IAAI,IAAI,CAAC1W,cAAc,CAACsC,aAAa,KAAK,IAAI,CAACS,wBAAwB,EAAE;QACjL,IAAI,CAACA,wBAAwB,GAAG,IAAI,CAAC/C,cAAc,CAACsC,aAAa;QACjE6T,YAAY,CAAC;UACXnI,eAAe,EAAfA;QACF,CAAC,CAAC;MACJ,CAAC,MAKI,IAAIkI,cAAc,IAAI,IAAI,IAAI,IAAI,CAACpR,KAAK,CAACC,mBAAmB,CAACiD,KAAK,KAAK,CAAC,IAAIyO,sBAAsB,IAAI,IAAI,CAACzW,cAAc,CAACsC,aAAa,KAAK,IAAI,CAACQ,0BAA0B,EAAE;QAKhL,IAAI,CAAC8C,kBAAkB,IAAI,IAAI,CAAC5F,cAAc,CAAC0C,SAAS,KAAK,CAAC,EAAE;UAC9D,IAAI,CAACI,0BAA0B,GAAG,IAAI,CAAC9C,cAAc,CAACsC,aAAa;UACnE4T,cAAc,CAAC;YACbG,iBAAiB,EAAjBA;UACF,CAAC,CAAC;QACJ;MACF,CAAC,MAII;QACH,IAAI,CAACvT,0BAA0B,GAAG2T,sBAAsB,GAAG,IAAI,CAAC3T,0BAA0B,GAAG,CAAC;QAC9F,IAAI,CAACC,wBAAwB,GAAG2T,oBAAoB,GAAG,IAAI,CAAC3T,wBAAwB,GAAG,CAAC;MAC1F;IACF;EAAC;IAAA5B,GAAA;IAAA8K,KAAA,EAMD,SAAAvH,4BAA4BA,CAAA,EAAG;MAC7B,IAAIiS,qBAAqB,GAAG,IAAI,CAAC7R,KAAK,CAACC,mBAAmB;QACxDiD,KAAK,GAAG2O,qBAAqB,CAAC3O,KAAK;QACnCC,IAAI,GAAG0O,qBAAqB,CAAC1O,IAAI;MACnC,IAAI2O,qBAAqB,GAAG,IAAI,CAAC5W,cAAc;QAC7CyC,MAAM,GAAGmU,qBAAqB,CAACnU,MAAM;QACrC3D,aAAa,GAAG8X,qBAAqB,CAAC9X,aAAa;QACnD6D,QAAQ,GAAGiU,qBAAqB,CAACjU,QAAQ;MAC3C,IAAIyG,SAAS,GAAG,IAAI,CAAC/F,KAAK,CAACyC,YAAY,CAAC,IAAI,CAACzC,KAAK,CAAC0C,IAAI,CAAC;MACxD,IAAI8Q,KAAK,GAAG,KAAK;MACjB,IAAIpY,uBAAuB,GAAGD,gCAAgC,CAAC,IAAI,CAAC6E,KAAK,CAAC5E,uBAAuB,CAAC;MAClG,IAAIE,qBAAqB,GAAGD,8BAA8B,CAAC,IAAI,CAAC2E,KAAK,CAAC1E,qBAAqB,CAAC;MAG5F,IAAIqJ,KAAK,GAAG,CAAC,EAAE;QACb,IAAI8O,OAAO,GAAGrU,MAAM,GAAG,IAAI,CAACkG,uBAAuB,CAACX,KAAK,EAAE,IAAI,CAAC3E,KAAK,CAAC,CAACZ,MAAM;QAC7EoU,KAAK,GAAGC,OAAO,GAAG,CAAC,IAAInU,QAAQ,GAAG,CAAC,CAAC,IAAImU,OAAO,GAAGlY,qBAAqB,CAACH,uBAAuB,EAAEK,aAAa,CAAC;MACjH;MAGA,IAAI,CAAC+X,KAAK,IAAI5O,IAAI,IAAI,CAAC,IAAIA,IAAI,GAAGmB,SAAS,GAAG,CAAC,EAAE;QAC/C,IAAI2N,UAAU,GAAG,IAAI,CAACpO,uBAAuB,CAACV,IAAI,EAAE,IAAI,CAAC5E,KAAK,CAAC,CAACZ,MAAM,IAAIA,MAAM,GAAG3D,aAAa,CAAC;QACjG+X,KAAK,GAAGE,UAAU,GAAG,CAAC,IAAIpU,QAAQ,GAAG,CAAC,IAAIoU,UAAU,GAAGnY,qBAAqB,CAACD,qBAAqB,EAAEG,aAAa,CAAC;MACpH;MAQA,IAAI+X,KAAK,KAAK,IAAI,CAACrV,kBAAkB,IAAI,IAAI,CAAC6B,KAAK,CAAC4F,aAAa,CAAC,IAAI,CAAC,IAAI,CAAClH,gBAAgB,EAAE;QAC5F,IAAI,CAACA,gBAAgB,GAAG,IAAI;QAG5B,IAAI,CAAC0H,2BAA2B,CAAC0F,OAAO,CAAC;UACvCC,KAAK,EAAE;QACT,CAAC,CAAC;QACF,IAAI,CAAC1H,oBAAoB,CAAC,CAAC;QAC3B;MACF,CAAC,MAAM;QACL,IAAI,CAAC+B,2BAA2B,CAACuN,QAAQ,CAAC,CAAC;MAC7C;IACF;EAAC;IAAA7V,GAAA;IAAA8K,KAAA,EAOD,SAAApH,oBAAoBA,CAACxB,KAAK,EAAE0B,mBAAmB,EAAE;MAAA,IAAAkS,MAAA;MAC/C,IAAI,CAAC/T,kBAAkB,CAAChC,OAAO,CAAC,UAAA+F,KAAK,EAAI;QACvCA,KAAK,CAACC,iBAAiB,CAACgQ,QAAQ,CAAC7T,KAAK,EAAE4T,MAAI,CAACjX,cAAc,CAACyC,MAAM,EAAEwU,MAAI,CAACjX,cAAc,CAAClB,aAAa,EAAEmY,MAAI,CAACjO,gBAAgB,EAAEiO,MAAI,CAAC9O,gBAAgB,EAAElB,KAAK,CAAC8C,sBAAsB,EAAEhF,mBAAmB,CAAC;MACzM,CAAC,CAAC;IACJ;EAAC;IAAA5D,GAAA;IAAA8K,KAAA,EAhrBD,SAAOnE,iBAAiBA,CAACzE,KAAK,EAAE0B,mBAAmB,EAAEoS,iBAAiB,EAAE;MACtE,IAAI/N,SAAS,GAAG/F,KAAK,CAACyC,YAAY,CAACzC,KAAK,CAAC0C,IAAI,CAAC;MAC9CvI,SAAS,CAACuH,mBAAmB,CAACiD,KAAK,IAAI,CAAC,IAAIjD,mBAAmB,CAACkD,IAAI,IAAIlD,mBAAmB,CAACiD,KAAK,GAAG,CAAC,IAAIjD,mBAAmB,CAACkD,IAAI,GAAGmB,SAAS,EAAE,mCAAmC,GAAGrE,mBAAmB,CAACiD,KAAK,GAAG,IAAI,GAAGjD,mBAAmB,CAACkD,IAAI,GAAG,qDAAqD,CAAC;MACzS,IAAIJ,UAAU,GAAG,IAAIjL,cAAc,CAACwM,SAAS,CAAC;MAC9C,IAAIA,SAAS,GAAG,CAAC,EAAE;QACjB,IAAIgO,UAAU,IAAIrS,mBAAmB,EAAAsS,MAAA,CAAAC,kBAAA,CAAMH,iBAAiB,KAAK,IAAI,IAAIA,iBAAiB,KAAK,KAAK,CAAC,GAAGA,iBAAiB,GAAG,EAAE,EAAE;QAChI,KAAK,IAAII,GAAG,GAAG,CAAC,EAAEC,WAAW,GAAGJ,UAAU,EAAEG,GAAG,GAAGC,WAAW,CAACjY,MAAM,EAAEgY,GAAG,EAAE,EAAE;UAC3E,IAAIE,MAAM,GAAGD,WAAW,CAACD,GAAG,CAAC;UAC7B1P,UAAU,CAAC6P,QAAQ,CAACD,MAAM,CAAC;QAC7B;QAIA,IAAIpU,KAAK,CAACuC,kBAAkB,IAAI,IAAI,IAAIvC,KAAK,CAACuC,kBAAkB,IAAI,CAAC,EAAE;UACrE,IAAI+R,aAAa,GAAGnY,eAAe,CAAC2K,oBAAoB,CAAC9G,KAAK,CAAC;UAC/DwE,UAAU,CAAC6P,QAAQ,CAACC,aAAa,CAAC;QACpC;QAKA,IAAIC,gBAAgB,GAAG,IAAIxF,GAAG,CAAC/O,KAAK,CAACwM,mBAAmB,CAAC;QACzDrQ,eAAe,CAACqY,0BAA0B,CAACxU,KAAK,EAAEuU,gBAAgB,EAAE/P,UAAU,EAAE9C,mBAAmB,CAACiD,KAAK,CAAC;MAC5G;MACA,OAAOH,UAAU;IACnB;EAAC;IAAA1G,GAAA;IAAA8K,KAAA,EACD,SAAO9B,oBAAoBA,CAAC9G,KAAK,EAAE;MACjC,IAAIyU,qBAAqB;MACzB,IAAI1O,SAAS,GAAG/F,KAAK,CAACyC,YAAY,CAACzC,KAAK,CAAC0C,IAAI,CAAC;MAC9C,IAAIgS,cAAc,GAAGvT,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAAC+G,GAAG,CAACnC,SAAS,GAAG,CAAC,EAAE5E,IAAI,CAACqE,KAAK,CAAC,CAACiP,qBAAqB,GAAGzU,KAAK,CAACuC,kBAAkB,MAAM,IAAI,IAAIkS,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAG,CAAC,CAAC,CAAC,CAAC;MAClM,IAAIE,aAAa,GAAGxT,IAAI,CAAC+G,GAAG,CAACnC,SAAS,EAAE2O,cAAc,GAAG3Z,2BAA2B,CAACiF,KAAK,CAAChF,kBAAkB,CAAC,CAAC,GAAG,CAAC;MACnH,OAAO;QACL2J,KAAK,EAAE+P,cAAc;QACrB9P,IAAI,EAAE+P;MACR,CAAC;IACH;EAAC;IAAA7W,GAAA;IAAA8K,KAAA,EACD,SAAO4L,0BAA0BA,CAACxU,KAAK,EAAEuU,gBAAgB,EAAE/P,UAAU,EAAEoQ,OAAO,EAAE;MAC9E,IAAIzH,YAAY,GAAGnN,KAAK,CAAC+M,mBAAmB,GAAG,CAAC,GAAG,CAAC;MACpD,KAAK,IAAI8H,OAAO,GAAGD,OAAO,GAAG,CAAC,EAAEC,OAAO,IAAI,CAAC,EAAEA,OAAO,EAAE,EAAE;QACvD,IAAIN,gBAAgB,CAAC/G,GAAG,CAACqH,OAAO,GAAG1H,YAAY,CAAC,EAAE;UAChD3I,UAAU,CAAC6P,QAAQ,CAAC;YAClB1P,KAAK,EAAEkQ,OAAO;YACdjQ,IAAI,EAAEiQ;UACR,CAAC,CAAC;UACF;QACF;MACF;IACF;EAAC;IAAA/W,GAAA;IAAA8K,KAAA,EA0GD,SAAOkM,wBAAwBA,CAAClX,QAAQ,EAAEmX,SAAS,EAAE;MAGnD,IAAIhP,SAAS,GAAGnI,QAAQ,CAAC6E,YAAY,CAAC7E,QAAQ,CAAC8E,IAAI,CAAC;MACpD,IAAIqD,SAAS,KAAKgP,SAAS,CAACvQ,UAAU,CAACwQ,QAAQ,CAAC,CAAC,EAAE;QACjD,OAAOD,SAAS;MAClB;MACA,IAAIE,gBAAgB,GAAG9Y,eAAe,CAACyO,qBAAqB,CAACmK,SAAS,CAACrT,mBAAmB,EAAE9D,QAAQ,CAAC;MACrG,OAAO;QACL8D,mBAAmB,EAAEuT,gBAAgB;QACrCzQ,UAAU,EAAErI,eAAe,CAACsI,iBAAiB,CAAC7G,QAAQ,EAAEqX,gBAAgB;MAC1E,CAAC;IACH;EAAC;IAAAnX,GAAA;IAAA8K,KAAA,EAsDD,SAAOgC,qBAAqBA,CAAC2B,KAAK,EAAEvM,KAAK,EAAE;MACzC,IAAI+F,SAAS,GAAG/F,KAAK,CAACyC,YAAY,CAACzC,KAAK,CAAC0C,IAAI,CAAC;MAC9C,IAAIkC,IAAI,GAAGzD,IAAI,CAAC+G,GAAG,CAACnC,SAAS,GAAG,CAAC,EAAEwG,KAAK,CAAC3H,IAAI,CAAC;MAC9C,IAAI1J,mBAAmB,GAAGD,4BAA4B,CAAC+E,KAAK,CAAC9E,mBAAmB,CAAC;MACjF,OAAO;QACLyJ,KAAK,EAAEtL,KAAK,CAAC,CAAC,EAAE0M,SAAS,GAAG,CAAC,GAAG7K,mBAAmB,EAAEqR,KAAK,CAAC5H,KAAK,CAAC;QACjEC,IAAI,EAAJA;MACF,CAAC;IACH;EAAC;AAAA,EA71B2BlL,sBAAsB;AA4yCpDyC,eAAe,CAAC+Y,WAAW,GAAGpb,sBAAsB;AACpD,IAAI8U,MAAM,GAAG1V,UAAU,CAACic,MAAM,CAAC;EAC7BrG,kBAAkB,EAAE;IAClBsG,SAAS,EAAE;EACb,CAAC;EACDvG,oBAAoB,EAAE;IACpBuG,SAAS,EAAE;EACb,CAAC;EACDnI,KAAK,EAAE;IACLoI,IAAI,EAAE;EACR,CAAC;EACDhD,gBAAgB,EAAE;IAChBiD,QAAQ,EAAE,UAAU;IACpB7C,GAAG,EAAE,CAAC;IACN8C,KAAK,EAAE;EACT,CAAC;EACDjD,YAAY,EAAE;IACZkD,MAAM,EAAE,CAAC;IACTnT,KAAK,EAAE,EAAE;IACToT,WAAW,EAAE,MAAM;IACnBC,WAAW,EAAE;EACf,CAAC;EACDlD,iBAAiB,EAAE;IACjBmD,IAAI,EAAE,CAAC;IACPC,eAAe,EAAE;EACnB,CAAC;EACDlD,qBAAqB,EAAE;IACrBiD,IAAI,EAAE,CAAC;IACPF,WAAW,EAAE,OAAO;IACpBC,WAAW,EAAE;EACf,CAAC;EACD/C,oBAAoB,EAAE;IACpBgD,IAAI,EAAE,CAAC;IACPF,WAAW,EAAE,KAAK;IAClBC,WAAW,EAAE;EACf;AACF,CAAC,CAAC;AACF,eAAevZ,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}