{"ast": null, "code": "import createIconSet from \"./createIconSet\";\nimport font from \"./vendor/react-native-vector-icons/Fonts/Foundation.ttf\";\nimport glyphMap from \"./vendor/react-native-vector-icons/glyphmaps/Foundation.json\";\nexport default createIconSet(glyphMap, 'foundation', font);", "map": {"version": 3, "names": ["createIconSet", "font", "glyphMap"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\@expo\\vector-icons\\src\\Foundation.ts"], "sourcesContent": ["import createIconSet from './createIconSet';\nimport font from './vendor/react-native-vector-icons/Fonts/Foundation.ttf';\nimport glyphMap from './vendor/react-native-vector-icons/glyphmaps/Foundation.json';\n\nexport default createIconSet(glyphMap, 'foundation', font);\n"], "mappings": "AAAA,OAAOA,aAAa;AACpB,OAAOC,IAAI;AACX,OAAOC,QAAQ;AAEf,eAAeF,aAAa,CAACE,QAAQ,EAAE,YAAY,EAAED,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}