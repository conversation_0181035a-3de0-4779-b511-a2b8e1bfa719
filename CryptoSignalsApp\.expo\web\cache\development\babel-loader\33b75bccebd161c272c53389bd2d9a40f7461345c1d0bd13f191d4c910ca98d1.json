{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport color from 'color';\nimport { MD3LightTheme } from \"./LightTheme\";\nimport { MD3Colors, tokens } from \"./tokens\";\nvar _tokens$md$ref = tokens.md.ref,\n  palette = _tokens$md$ref.palette,\n  opacity = _tokens$md$ref.opacity;\nexport var MD3DarkTheme = _objectSpread(_objectSpread({}, MD3LightTheme), {}, {\n  dark: true,\n  mode: 'adaptive',\n  version: 3,\n  isV3: true,\n  colors: {\n    primary: palette.primary80,\n    primaryContainer: palette.primary30,\n    secondary: palette.secondary80,\n    secondaryContainer: palette.secondary30,\n    tertiary: palette.tertiary80,\n    tertiaryContainer: palette.tertiary30,\n    surface: palette.neutral10,\n    surfaceVariant: palette.neutralVariant30,\n    surfaceDisabled: color(palette.neutral90).alpha(opacity.level2).rgb().string(),\n    background: palette.neutral10,\n    error: palette.error80,\n    errorContainer: palette.error30,\n    onPrimary: palette.primary20,\n    onPrimaryContainer: palette.primary90,\n    onSecondary: palette.secondary20,\n    onSecondaryContainer: palette.secondary90,\n    onTertiary: palette.tertiary20,\n    onTertiaryContainer: palette.tertiary90,\n    onSurface: palette.neutral90,\n    onSurfaceVariant: palette.neutralVariant80,\n    onSurfaceDisabled: color(palette.neutral90).alpha(opacity.level4).rgb().string(),\n    onError: palette.error20,\n    onErrorContainer: palette.error80,\n    onBackground: palette.neutral90,\n    outline: palette.neutralVariant60,\n    outlineVariant: palette.neutralVariant30,\n    inverseSurface: palette.neutral90,\n    inverseOnSurface: palette.neutral20,\n    inversePrimary: palette.primary40,\n    shadow: palette.neutral0,\n    scrim: palette.neutral0,\n    backdrop: color(MD3Colors.neutralVariant20).alpha(0.4).rgb().string(),\n    elevation: {\n      level0: 'transparent',\n      level1: 'rgb(37, 35, 42)',\n      level2: 'rgb(44, 40, 49)',\n      level3: 'rgb(49, 44, 56)',\n      level4: 'rgb(51, 46, 58)',\n      level5: 'rgb(52, 49, 63)'\n    }\n  }\n});", "map": {"version": 3, "names": ["color", "MD3LightTheme", "MD3Colors", "tokens", "_tokens$md$ref", "md", "ref", "palette", "opacity", "MD3DarkTheme", "_objectSpread", "dark", "mode", "version", "isV3", "colors", "primary", "primary80", "primaryContainer", "primary30", "secondary", "secondary80", "secondaryContainer", "secondary30", "tertiary", "tertiary80", "tertiaryContainer", "tertiary30", "surface", "neutral10", "surfaceVariant", "neutralVariant30", "surfaceDisabled", "neutral90", "alpha", "level2", "rgb", "string", "background", "error", "error80", "<PERSON><PERSON><PERSON><PERSON>", "error30", "onPrimary", "primary20", "onPrimaryContainer", "primary90", "onSecondary", "secondary20", "onSecondaryContainer", "secondary90", "onTertiary", "tertiary20", "onTertiaryContainer", "tertiary90", "onSurface", "onSurfaceVariant", "neutralVariant80", "onSurfaceDisabled", "level4", "onError", "error20", "onError<PERSON><PERSON>r", "onBackground", "outline", "neutralVariant60", "outlineVariant", "inverseSurface", "inverseOnSurface", "neutral20", "inversePrimary", "primary40", "shadow", "neutral0", "scrim", "backdrop", "neutralVariant20", "elevation", "level0", "level1", "level3", "level5"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\styles\\themes\\v3\\DarkTheme.tsx"], "sourcesContent": ["import color from 'color';\n\nimport { MD3LightTheme } from './LightTheme';\nimport { MD3Colors, tokens } from './tokens';\nimport type { MD3Theme } from '../../../types';\n\nconst { palette, opacity } = tokens.md.ref;\n\nexport const MD3DarkTheme: MD3Theme = {\n  ...MD3LightTheme,\n  dark: true,\n  mode: 'adaptive',\n  version: 3,\n  isV3: true,\n  colors: {\n    primary: palette.primary80,\n    primaryContainer: palette.primary30,\n    secondary: palette.secondary80,\n    secondaryContainer: palette.secondary30,\n    tertiary: palette.tertiary80,\n    tertiaryContainer: palette.tertiary30,\n    surface: palette.neutral10,\n    surfaceVariant: palette.neutralVariant30,\n    surfaceDisabled: color(palette.neutral90)\n      .alpha(opacity.level2)\n      .rgb()\n      .string(),\n    background: palette.neutral10,\n    error: palette.error80,\n    errorContainer: palette.error30,\n    onPrimary: palette.primary20,\n    onPrimaryContainer: palette.primary90,\n    onSecondary: palette.secondary20,\n    onSecondaryContainer: palette.secondary90,\n    onTertiary: palette.tertiary20,\n    onTertiaryContainer: palette.tertiary90,\n    onSurface: palette.neutral90,\n    onSurfaceVariant: palette.neutralVariant80,\n    onSurfaceDisabled: color(palette.neutral90)\n      .alpha(opacity.level4)\n      .rgb()\n      .string(),\n    onError: palette.error20,\n    onErrorContainer: palette.error80,\n    onBackground: palette.neutral90,\n    outline: palette.neutralVariant60,\n    outlineVariant: palette.neutralVariant30,\n    inverseSurface: palette.neutral90,\n    inverseOnSurface: palette.neutral20,\n    inversePrimary: palette.primary40,\n    shadow: palette.neutral0,\n    scrim: palette.neutral0,\n    backdrop: color(MD3Colors.neutralVariant20).alpha(0.4).rgb().string(),\n    elevation: {\n      level0: 'transparent',\n      // Note: Color values with transparency cause RN to transfer shadows to children nodes\n      // instead of View component in Surface. Providing solid background fixes the issue.\n      // Opaque color values generated with `palette.primary80` used as background\n      level1: 'rgb(37, 35, 42)', // palette.primary80, alpha 0.05\n      level2: 'rgb(44, 40, 49)', // palette.primary80, alpha 0.08\n      level3: 'rgb(49, 44, 56)', // palette.primary80, alpha 0.11\n      level4: 'rgb(51, 46, 58)', // palette.primary80, alpha 0.12\n      level5: 'rgb(52, 49, 63)', // palette.primary80, alpha 0.14\n    },\n  },\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,SAASC,aAAa;AACtB,SAASC,SAAS,EAAEC,MAAM;AAG1B,IAAAC,cAAA,GAA6BD,MAAM,CAACE,EAAE,CAACC,GAAG;EAAlCC,OAAO,GAAAH,cAAA,CAAPG,OAAO;EAAEC,OAAA,GAAAJ,cAAA,CAAAI,OAAA;AAEjB,OAAO,IAAMC,YAAsB,GAAAC,aAAA,CAAAA,aAAA,KAC9BT,aAAa;EAChBU,IAAI,EAAE,IAAI;EACVC,IAAI,EAAE,UAAU;EAChBC,OAAO,EAAE,CAAC;EACVC,IAAI,EAAE,IAAI;EACVC,MAAM,EAAE;IACNC,OAAO,EAAET,OAAO,CAACU,SAAS;IAC1BC,gBAAgB,EAAEX,OAAO,CAACY,SAAS;IACnCC,SAAS,EAAEb,OAAO,CAACc,WAAW;IAC9BC,kBAAkB,EAAEf,OAAO,CAACgB,WAAW;IACvCC,QAAQ,EAAEjB,OAAO,CAACkB,UAAU;IAC5BC,iBAAiB,EAAEnB,OAAO,CAACoB,UAAU;IACrCC,OAAO,EAAErB,OAAO,CAACsB,SAAS;IAC1BC,cAAc,EAAEvB,OAAO,CAACwB,gBAAgB;IACxCC,eAAe,EAAEhC,KAAK,CAACO,OAAO,CAAC0B,SAAS,CAAC,CACtCC,KAAK,CAAC1B,OAAO,CAAC2B,MAAM,CAAC,CACrBC,GAAG,CAAC,CAAC,CACLC,MAAM,CAAC,CAAC;IACXC,UAAU,EAAE/B,OAAO,CAACsB,SAAS;IAC7BU,KAAK,EAAEhC,OAAO,CAACiC,OAAO;IACtBC,cAAc,EAAElC,OAAO,CAACmC,OAAO;IAC/BC,SAAS,EAAEpC,OAAO,CAACqC,SAAS;IAC5BC,kBAAkB,EAAEtC,OAAO,CAACuC,SAAS;IACrCC,WAAW,EAAExC,OAAO,CAACyC,WAAW;IAChCC,oBAAoB,EAAE1C,OAAO,CAAC2C,WAAW;IACzCC,UAAU,EAAE5C,OAAO,CAAC6C,UAAU;IAC9BC,mBAAmB,EAAE9C,OAAO,CAAC+C,UAAU;IACvCC,SAAS,EAAEhD,OAAO,CAAC0B,SAAS;IAC5BuB,gBAAgB,EAAEjD,OAAO,CAACkD,gBAAgB;IAC1CC,iBAAiB,EAAE1D,KAAK,CAACO,OAAO,CAAC0B,SAAS,CAAC,CACxCC,KAAK,CAAC1B,OAAO,CAACmD,MAAM,CAAC,CACrBvB,GAAG,CAAC,CAAC,CACLC,MAAM,CAAC,CAAC;IACXuB,OAAO,EAAErD,OAAO,CAACsD,OAAO;IACxBC,gBAAgB,EAAEvD,OAAO,CAACiC,OAAO;IACjCuB,YAAY,EAAExD,OAAO,CAAC0B,SAAS;IAC/B+B,OAAO,EAAEzD,OAAO,CAAC0D,gBAAgB;IACjCC,cAAc,EAAE3D,OAAO,CAACwB,gBAAgB;IACxCoC,cAAc,EAAE5D,OAAO,CAAC0B,SAAS;IACjCmC,gBAAgB,EAAE7D,OAAO,CAAC8D,SAAS;IACnCC,cAAc,EAAE/D,OAAO,CAACgE,SAAS;IACjCC,MAAM,EAAEjE,OAAO,CAACkE,QAAQ;IACxBC,KAAK,EAAEnE,OAAO,CAACkE,QAAQ;IACvBE,QAAQ,EAAE3E,KAAK,CAACE,SAAS,CAAC0E,gBAAgB,CAAC,CAAC1C,KAAK,CAAC,GAAG,CAAC,CAACE,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;IACrEwC,SAAS,EAAE;MACTC,MAAM,EAAE,aAAa;MAIrBC,MAAM,EAAE,iBAAiB;MACzB5C,MAAM,EAAE,iBAAiB;MACzB6C,MAAM,EAAE,iBAAiB;MACzBrB,MAAM,EAAE,iBAAiB;MACzBsB,MAAM,EAAE;IACV;EACF;AAAA,EACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}