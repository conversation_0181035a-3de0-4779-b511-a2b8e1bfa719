{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport Dimensions from \"../Dimensions\";\nvar PixelRatio = function () {\n  function PixelRatio() {\n    _classCallCheck(this, PixelRatio);\n  }\n  return _createClass(PixelRatio, null, [{\n    key: \"get\",\n    value: function get() {\n      return Dimensions.get('window').scale;\n    }\n  }, {\n    key: \"getFontScale\",\n    value: function getFontScale() {\n      return Dimensions.get('window').fontScale || PixelRatio.get();\n    }\n  }, {\n    key: \"getPixelSizeForLayoutSize\",\n    value: function getPixelSizeForLayoutSize(layoutSize) {\n      return Math.round(layoutSize * PixelRatio.get());\n    }\n  }, {\n    key: \"roundToNearestPixel\",\n    value: function roundToNearestPixel(layoutSize) {\n      var ratio = PixelRatio.get();\n      return Math.round(layoutSize * ratio) / ratio;\n    }\n  }]);\n}();\nexport { PixelRatio as default };", "map": {"version": 3, "names": ["Dimensions", "PixelRatio", "_classCallCheck", "_createClass", "key", "value", "get", "scale", "getFontScale", "fontScale", "getPixelSizeForLayoutSize", "layoutSize", "Math", "round", "roundToNearestPixel", "ratio", "default"], "sources": ["C:/Users/<USER>/Desktop/Signals/CryptoSignalsApp/node_modules/react-native-web/dist/exports/PixelRatio/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport Dimensions from '../Dimensions';\n\n/**\n * PixelRatio gives access to the device pixel density.\n */\nexport default class PixelRatio {\n  /**\n   * Returns the device pixel density.\n   */\n  static get() {\n    return Dimensions.get('window').scale;\n  }\n\n  /**\n   * No equivalent for Web\n   */\n  static getFontScale() {\n    return Dimensions.get('window').fontScale || PixelRatio.get();\n  }\n\n  /**\n   * Converts a layout size (dp) to pixel size (px).\n   * Guaranteed to return an integer number.\n   */\n  static getPixelSizeForLayoutSize(layoutSize) {\n    return Math.round(layoutSize * PixelRatio.get());\n  }\n\n  /**\n   * Rounds a layout size (dp) to the nearest layout size that corresponds to\n   * an integer number of pixels. For example, on a device with a PixelRatio\n   * of 3, `PixelRatio.roundToNearestPixel(8.4) = 8.33`, which corresponds to\n   * exactly (8.33 * 3) = 25 pixels.\n   */\n  static roundToNearestPixel(layoutSize) {\n    var ratio = PixelRatio.get();\n    return Math.round(layoutSize * ratio) / ratio;\n  }\n}"], "mappings": ";;AAUA,OAAOA,UAAU;AAAsB,IAKlBC,UAAU;EAAA,SAAAA,WAAA;IAAAC,eAAA,OAAAD,UAAA;EAAA;EAAA,OAAAE,YAAA,CAAAF,UAAA;IAAAG,GAAA;IAAAC,KAAA,EAI7B,SAAOC,GAAGA,CAAA,EAAG;MACX,OAAON,UAAU,CAACM,GAAG,CAAC,QAAQ,CAAC,CAACC,KAAK;IACvC;EAAC;IAAAH,GAAA;IAAAC,KAAA,EAKD,SAAOG,YAAYA,CAAA,EAAG;MACpB,OAAOR,UAAU,CAACM,GAAG,CAAC,QAAQ,CAAC,CAACG,SAAS,IAAIR,UAAU,CAACK,GAAG,CAAC,CAAC;IAC/D;EAAC;IAAAF,GAAA;IAAAC,KAAA,EAMD,SAAOK,yBAAyBA,CAACC,UAAU,EAAE;MAC3C,OAAOC,IAAI,CAACC,KAAK,CAACF,UAAU,GAAGV,UAAU,CAACK,GAAG,CAAC,CAAC,CAAC;IAClD;EAAC;IAAAF,GAAA;IAAAC,KAAA,EAQD,SAAOS,mBAAmBA,CAACH,UAAU,EAAE;MACrC,IAAII,KAAK,GAAGd,UAAU,CAACK,GAAG,CAAC,CAAC;MAC5B,OAAOM,IAAI,CAACC,KAAK,CAACF,UAAU,GAAGI,KAAK,CAAC,GAAGA,KAAK;IAC/C;EAAC;AAAA;AAAA,SAhCkBd,UAAU,IAAAe,OAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}