{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"status\", \"disabled\", \"onPress\", \"theme\", \"testID\"];\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nimport * as React from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport { getSelectionControlIOSColor } from \"./utils\";\nimport { useInternalTheme } from \"../../core/theming\";\nimport MaterialCommunityIcon from \"../MaterialCommunityIcon\";\nimport TouchableRipple from \"../TouchableRipple/TouchableRipple\";\nvar CheckboxIOS = function CheckboxIOS(_ref) {\n  var status = _ref.status,\n    disabled = _ref.disabled,\n    onPress = _ref.onPress,\n    themeOverrides = _ref.theme,\n    testID = _ref.testID,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var theme = useInternalTheme(themeOverrides);\n  var checked = status === 'checked';\n  var indeterminate = status === 'indeterminate';\n  var _getSelectionControlI = getSelectionControlIOSColor({\n      theme: theme,\n      disabled: disabled,\n      customColor: rest.color\n    }),\n    checkedColor = _getSelectionControlI.checkedColor,\n    rippleColor = _getSelectionControlI.rippleColor;\n  var icon = indeterminate ? 'minus' : 'check';\n  var opacity = indeterminate || checked ? 1 : 0;\n  return React.createElement(TouchableRipple, _extends({}, rest, {\n    borderless: true,\n    rippleColor: rippleColor,\n    onPress: onPress,\n    disabled: disabled,\n    accessibilityRole: \"checkbox\",\n    accessibilityState: {\n      disabled: disabled,\n      checked: checked\n    },\n    accessibilityLiveRegion: \"polite\",\n    style: styles.container,\n    testID: testID,\n    theme: theme\n  }), React.createElement(View, {\n    style: {\n      opacity: opacity\n    }\n  }, React.createElement(MaterialCommunityIcon, {\n    allowFontScaling: false,\n    name: icon,\n    size: 24,\n    color: checkedColor,\n    direction: \"ltr\"\n  })));\n};\nCheckboxIOS.displayName = 'Checkbox.IOS';\nvar styles = StyleSheet.create({\n  container: {\n    borderRadius: 18,\n    padding: 6\n  }\n});\nexport default CheckboxIOS;\nexport { CheckboxIOS };", "map": {"version": 3, "names": ["React", "StyleSheet", "View", "getSelectionControlIOSColor", "useInternalTheme", "MaterialCommunityIcon", "TouchableRipple", "CheckboxIOS", "_ref", "status", "disabled", "onPress", "themeOverrides", "theme", "testID", "rest", "_objectWithoutProperties", "_excluded", "checked", "indeterminate", "_getSelectionControlI", "customColor", "color", "checkedColor", "rippleColor", "icon", "opacity", "createElement", "_extends", "borderless", "accessibilityRole", "accessibilityState", "accessibilityLiveRegion", "style", "styles", "container", "allowFontScaling", "name", "size", "direction", "displayName", "create", "borderRadius", "padding"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\Checkbox\\CheckboxIOS.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { GestureResponderEvent, StyleSheet, View } from 'react-native';\n\nimport { getSelectionControlIOSColor } from './utils';\nimport { useInternalTheme } from '../../core/theming';\nimport type { $RemoveChildren, ThemeProp } from '../../types';\nimport MaterialCommunityIcon from '../MaterialCommunityIcon';\nimport TouchableRipple from '../TouchableRipple/TouchableRipple';\n\nexport type Props = $RemoveChildren<typeof TouchableRipple> & {\n  /**\n   * Status of checkbox.\n   */\n  status: 'checked' | 'unchecked' | 'indeterminate';\n  /**\n   * Whether checkbox is disabled.\n   */\n  disabled?: boolean;\n  /**\n   * Function to execute on press.\n   */\n  onPress?: (e: GestureResponderEvent) => void;\n  /**\n   * Custom color for checkbox.\n   */\n  color?: string;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n  /**\n   * testID to be used on tests.\n   */\n  testID?: string;\n};\n\n/**\n * Checkboxes allow the selection of multiple options from a set.\n * This component follows platform guidelines for iOS, but can be used\n * on any platform.\n *\n * @extends TouchableRipple props https://callstack.github.io/react-native-paper/docs/components/TouchableRipple\n */\nconst CheckboxIOS = ({\n  status,\n  disabled,\n  onPress,\n  theme: themeOverrides,\n  testID,\n  ...rest\n}: Props) => {\n  const theme = useInternalTheme(themeOverrides);\n  const checked = status === 'checked';\n  const indeterminate = status === 'indeterminate';\n\n  const { checkedColor, rippleColor } = getSelectionControlIOSColor({\n    theme,\n    disabled,\n    customColor: rest.color,\n  });\n\n  const icon = indeterminate ? 'minus' : 'check';\n  const opacity = indeterminate || checked ? 1 : 0;\n\n  return (\n    <TouchableRipple\n      {...rest}\n      borderless\n      rippleColor={rippleColor}\n      onPress={onPress}\n      disabled={disabled}\n      accessibilityRole=\"checkbox\"\n      accessibilityState={{ disabled, checked }}\n      accessibilityLiveRegion=\"polite\"\n      style={styles.container}\n      testID={testID}\n      theme={theme}\n    >\n      <View style={{ opacity }}>\n        <MaterialCommunityIcon\n          allowFontScaling={false}\n          name={icon}\n          size={24}\n          color={checkedColor}\n          direction=\"ltr\"\n        />\n      </View>\n    </TouchableRipple>\n  );\n};\n\nCheckboxIOS.displayName = 'Checkbox.IOS';\n\nconst styles = StyleSheet.create({\n  container: {\n    borderRadius: 18,\n    padding: 6,\n  },\n});\n\nexport default CheckboxIOS;\n\n// @component-docs ignore-next-line\nexport { CheckboxIOS };\n"], "mappings": ";;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAG9B,SAASC,2BAA2B;AACpC,SAASC,gBAAgB;AAEzB,OAAOC,qBAAqB;AAC5B,OAAOC,eAAe;AAoCtB,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAAC,IAAA,EAOJ;EAAA,IANXC,MAAM,GAAAD,IAAA,CAANC,MAAM;IACNC,QAAQ,GAAAF,IAAA,CAARE,QAAQ;IACRC,OAAO,GAAAH,IAAA,CAAPG,OAAO;IACAC,cAAc,GAAAJ,IAAA,CAArBK,KAAK;IACLC,MAAM,GAAAN,IAAA,CAANM,MAAM;IACHC,IAAA,GAAAC,wBAAA,CAAAR,IAAA,EAAAS,SAAA;EAEH,IAAMJ,KAAK,GAAGT,gBAAgB,CAACQ,cAAc,CAAC;EAC9C,IAAMM,OAAO,GAAGT,MAAM,KAAK,SAAS;EACpC,IAAMU,aAAa,GAAGV,MAAM,KAAK,eAAe;EAEhD,IAAAW,qBAAA,GAAsCjB,2BAA2B,CAAC;MAChEU,KAAK,EAALA,KAAK;MACLH,QAAQ,EAARA,QAAQ;MACRW,WAAW,EAAEN,IAAI,CAACO;IACpB,CAAC,CAAC;IAJMC,YAAY,GAAAH,qBAAA,CAAZG,YAAY;IAAEC,WAAA,GAAAJ,qBAAA,CAAAI,WAAA;EAMtB,IAAMC,IAAI,GAAGN,aAAa,GAAG,OAAO,GAAG,OAAO;EAC9C,IAAMO,OAAO,GAAGP,aAAa,IAAID,OAAO,GAAG,CAAC,GAAG,CAAC;EAEhD,OACElB,KAAA,CAAA2B,aAAA,CAACrB,eAAe,EAAAsB,QAAA,KACVb,IAAI;IACRc,UAAU;IACVL,WAAW,EAAEA,WAAY;IACzBb,OAAO,EAAEA,OAAQ;IACjBD,QAAQ,EAAEA,QAAS;IACnBoB,iBAAiB,EAAC,UAAU;IAC5BC,kBAAkB,EAAE;MAAErB,QAAQ,EAARA,QAAQ;MAAEQ,OAAA,EAAAA;IAAQ,CAAE;IAC1Cc,uBAAuB,EAAC,QAAQ;IAChCC,KAAK,EAAEC,MAAM,CAACC,SAAU;IACxBrB,MAAM,EAAEA,MAAO;IACfD,KAAK,EAAEA;EAAM,IAEbb,KAAA,CAAA2B,aAAA,CAACzB,IAAI;IAAC+B,KAAK,EAAE;MAAEP,OAAA,EAAAA;IAAQ;EAAE,GACvB1B,KAAA,CAAA2B,aAAA,CAACtB,qBAAqB;IACpB+B,gBAAgB,EAAE,KAAM;IACxBC,IAAI,EAAEZ,IAAK;IACXa,IAAI,EAAE,EAAG;IACThB,KAAK,EAAEC,YAAa;IACpBgB,SAAS,EAAC;EAAK,CAChB,CACG,CACS,CAAC;AAEtB,CAAC;AAEDhC,WAAW,CAACiC,WAAW,GAAG,cAAc;AAExC,IAAMN,MAAM,GAAGjC,UAAU,CAACwC,MAAM,CAAC;EAC/BN,SAAS,EAAE;IACTO,YAAY,EAAE,EAAE;IAChBC,OAAO,EAAE;EACX;AACF,CAAC,CAAC;AAEF,eAAepC,WAAW;AAG1B,SAASA,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}