{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport React, { useState, useEffect, useContext } from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport ScrollView from \"react-native-web/dist/exports/ScrollView\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport Alert from \"react-native-web/dist/exports/Alert\";\nimport { useNavigation } from '@react-navigation/native';\nimport Wrapper from \"../../components/Wrapper\";\nimport Card from \"../../components/Card\";\nimport { AxiosContext } from \"../../store/axios\";\nimport { StoreContext } from \"../../store\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport default function Channels(_ref) {\n  var _ref$route = _ref.route,\n    route = _ref$route === void 0 ? {} : _ref$route;\n  var _useState = useState(true),\n    _useState2 = _slicedToArray(_useState, 2),\n    isLoading = _useState2[0],\n    setIsLoading = _useState2[1];\n  var _useState3 = useState([]),\n    _useState4 = _slicedToArray(_useState3, 2),\n    channels = _useState4[0],\n    setChannels = _useState4[1];\n  var _useState5 = useState(null),\n    _useState6 = _slicedToArray(_useState5, 2),\n    error = _useState6[0],\n    setError = _useState6[1];\n  var _useContext = useContext(AxiosContext),\n    _useContext2 = _slicedToArray(_useContext, 1),\n    api = _useContext2[0];\n  var _useContext3 = useContext(StoreContext),\n    _useContext4 = _slicedToArray(_useContext3, 1),\n    state = _useContext4[0];\n  var navigation = useNavigation();\n  var _ref2 = route.params || {\n      accountHasBeenRecovered: false\n    },\n    accountHasBeenRecovered = _ref2.accountHasBeenRecovered;\n  var _ref3 = state || {\n      subscription: {\n        subscriptionStatus: false\n      }\n    },\n    subscriptionStatus = _ref3.subscription.subscriptionStatus;\n  useEffect(function () {\n    loadChannels();\n  }, []);\n  var loadChannels = function () {\n    var _ref4 = _asyncToGenerator(function* () {\n      try {\n        setIsLoading(true);\n        setError(null);\n        console.log('Carregando canais da API...');\n        var response = yield api.get('/channels');\n        if (response.data && Array.isArray(response.data)) {\n          console.log('Canais carregados:', response.data.length);\n          setChannels(response.data);\n        } else {\n          console.warn('Resposta da API não contém array de canais:', response.data);\n          setChannels([]);\n        }\n      } catch (err) {\n        console.error('Erro ao carregar canais:', err);\n        setError('Erro ao carregar canais. Usando dados offline.');\n        setChannels([{\n          id: 1,\n          externalId: 'strategy-scalp',\n          name: 'CryptoSignals Scalp',\n          description: 'Sinais de scalping de alta frequência',\n          type: 'FUTURES',\n          isPremium: false,\n          totalSignals: 0,\n          recentSignals: 0,\n          lastSignalAt: new Date().toISOString(),\n          photo: 'https://via.placeholder.com/50x50/4CAF50/FFFFFF?text=S'\n        }, {\n          id: 2,\n          externalId: 'strategy-swing',\n          name: 'CryptoSignals Swing',\n          description: 'Sinais de swing trading',\n          type: 'SPOT',\n          isPremium: false,\n          totalSignals: 0,\n          recentSignals: 0,\n          lastSignalAt: new Date().toISOString(),\n          photo: 'https://via.placeholder.com/50x50/2196F3/FFFFFF?text=S'\n        }]);\n      } finally {\n        setIsLoading(false);\n      }\n    });\n    return function loadChannels() {\n      return _ref4.apply(this, arguments);\n    };\n  }();\n  var handleChannelPress = function handleChannelPress(channel) {\n    console.log('Canal selecionado:', channel);\n    if (!subscriptionStatus) {\n      Alert.alert(\"Acesso Premium Necessário 💎\", \"Para acessar os sinais de trading, você precisa de uma assinatura premium.\", [{\n        text: \"Cancelar\",\n        style: \"cancel\"\n      }, {\n        text: \"Ver Planos\",\n        onPress: function onPress() {\n          return navigation.navigate('Premium');\n        }\n      }]);\n      return;\n    }\n    navigation.navigate('Signals', {\n      channelId: channel.externalId || channel.id,\n      channelName: channel.name\n    });\n  };\n  useEffect(function () {\n    if (accountHasBeenRecovered) {\n      Alert.alert(\"Success\", \"Account recovered successfully\");\n    }\n  }, [accountHasBeenRecovered]);\n  var formatLastSignalTime = function formatLastSignalTime(lastSignalAt) {\n    if (!lastSignalAt) return 'Nunca';\n    try {\n      var date = new Date(lastSignalAt);\n      var now = new Date();\n      var diffMs = now - date;\n      var diffHours = Math.floor(diffMs / (1000 * 60 * 60));\n      var diffDays = Math.floor(diffHours / 24);\n      if (diffHours < 1) return 'Agora mesmo';\n      if (diffHours < 24) return `${diffHours}h atrás`;\n      if (diffDays < 7) return `${diffDays}d atrás`;\n      return date.toLocaleDateString();\n    } catch (_unused) {\n      return 'Recente';\n    }\n  };\n  var getChannelAvatar = function getChannelAvatar(channel) {\n    var strategy = channel.name.toLowerCase();\n    if (strategy.includes('scalp')) return '⚡';\n    if (strategy.includes('swing')) return '📈';\n    if (strategy.includes('breakout')) return '🚀';\n    if (strategy.includes('mfi')) return '💰';\n    if (strategy.includes('volume')) return '📊';\n    if (strategy.includes('momentum')) return '🎯';\n    return '📡';\n  };\n  if (isLoading) {\n    return _jsx(Wrapper, {\n      children: _jsxs(View, {\n        style: {\n          padding: 16\n        },\n        children: [_jsx(Text, {\n          style: {\n            color: '#fff',\n            fontSize: 24,\n            fontWeight: 'bold',\n            marginBottom: 20\n          },\n          children: \"Signal Channels \\uD83D\\uDCFA\"\n        }), _jsx(Text, {\n          style: {\n            color: '#8a8a8a',\n            fontSize: 16,\n            textAlign: 'center',\n            marginTop: 50\n          },\n          children: \"Carregando canais...\"\n        })]\n      })\n    });\n  }\n  return _jsx(Wrapper, {\n    children: _jsxs(ScrollView, {\n      style: {\n        flex: 1,\n        padding: 16\n      },\n      children: [_jsxs(View, {\n        style: {\n          flexDirection: 'row',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          marginBottom: 8\n        },\n        children: [_jsxs(View, {\n          style: {\n            flex: 1\n          },\n          children: [_jsx(Text, {\n            style: {\n              color: '#fff',\n              fontSize: 28,\n              fontWeight: 'bold',\n              marginBottom: 4\n            },\n            children: \"Signal Channels \\uD83D\\uDCFA\"\n          }), _jsx(Text, {\n            style: {\n              color: '#8a8a8a',\n              fontSize: 14\n            },\n            children: \"Estrat\\xE9gias de trading automatizadas\"\n          })]\n        }), _jsx(View, {\n          style: {\n            backgroundColor: subscriptionStatus ? '#4CAF50' : '#FF6B35',\n            paddingHorizontal: 12,\n            paddingVertical: 6,\n            borderRadius: 16,\n            alignItems: 'center'\n          },\n          children: _jsx(Text, {\n            style: {\n              color: '#fff',\n              fontSize: 10,\n              fontWeight: 'bold'\n            },\n            children: subscriptionStatus ? '💎 PREMIUM' : '🔒 FREE'\n          })\n        })]\n      }), error && _jsx(View, {\n        style: {\n          backgroundColor: '#FF6B35',\n          padding: 12,\n          borderRadius: 8,\n          marginBottom: 16\n        },\n        children: _jsx(Text, {\n          style: {\n            color: '#fff',\n            fontSize: 12,\n            textAlign: 'center'\n          },\n          children: error\n        })\n      }), !subscriptionStatus && _jsx(Card, {\n        style: {\n          marginBottom: 16,\n          backgroundColor: '#FF6B3520',\n          borderColor: '#FF6B35',\n          borderWidth: 1\n        },\n        children: _jsxs(View, {\n          style: {\n            padding: 16,\n            alignItems: 'center'\n          },\n          children: [_jsx(Text, {\n            style: {\n              color: '#FF6B35',\n              fontSize: 16,\n              fontWeight: 'bold',\n              marginBottom: 8\n            },\n            children: \"\\uD83D\\uDD12 Acesso Premium Necess\\xE1rio\"\n          }), _jsx(Text, {\n            style: {\n              color: '#FFB74D',\n              fontSize: 14,\n              textAlign: 'center',\n              marginBottom: 12\n            },\n            children: \"Para acessar os sinais de trading profissionais, voc\\xEA precisa de uma assinatura premium.\"\n          }), _jsx(Card, {\n            style: {\n              backgroundColor: '#FF6B35'\n            },\n            onPress: function onPress() {\n              return navigation.navigate('Premium');\n            },\n            children: _jsx(View, {\n              style: {\n                paddingHorizontal: 20,\n                paddingVertical: 8\n              },\n              children: _jsx(Text, {\n                style: {\n                  color: '#fff',\n                  fontSize: 14,\n                  fontWeight: 'bold'\n                },\n                children: \"Ver Planos Premium\"\n              })\n            })\n          })]\n        })\n      }), _jsx(Card, {\n        style: {\n          marginBottom: 16\n        },\n        onPress: loadChannels,\n        children: _jsx(View, {\n          style: {\n            alignItems: 'center',\n            padding: 8\n          },\n          children: _jsx(Text, {\n            style: {\n              color: '#4CAF50',\n              fontSize: 14,\n              fontWeight: '600'\n            },\n            children: \"\\uD83D\\uDD04 Atualizar Canais\"\n          })\n        })\n      }), channels.length === 0 ? _jsxs(View, {\n        style: {\n          alignItems: 'center',\n          marginTop: 50\n        },\n        children: [_jsx(Text, {\n          style: {\n            color: '#8a8a8a',\n            fontSize: 16,\n            textAlign: 'center'\n          },\n          children: \"Nenhum canal dispon\\xEDvel\"\n        }), _jsx(Text, {\n          style: {\n            color: '#666',\n            fontSize: 12,\n            textAlign: 'center',\n            marginTop: 8\n          },\n          children: \"Verifique se o gerador de sinais est\\xE1 rodando\"\n        })]\n      }) : channels.map(function (channel) {\n        return _jsx(Card, {\n          style: {\n            marginBottom: 12,\n            opacity: !subscriptionStatus ? 0.6 : 1,\n            borderColor: !subscriptionStatus ? '#FF6B35' : 'transparent',\n            borderWidth: !subscriptionStatus ? 1 : 0\n          },\n          onPress: function onPress() {\n            return handleChannelPress(channel);\n          },\n          children: _jsxs(View, {\n            style: {\n              flexDirection: 'row',\n              alignItems: 'center'\n            },\n            children: [_jsx(View, {\n              style: {\n                backgroundColor: '#333',\n                borderRadius: 12,\n                padding: 12,\n                marginRight: 12,\n                alignItems: 'center',\n                justifyContent: 'center'\n              },\n              children: _jsx(Text, {\n                style: {\n                  fontSize: 24\n                },\n                children: getChannelAvatar(channel)\n              })\n            }), _jsxs(View, {\n              style: {\n                flex: 1\n              },\n              children: [_jsxs(View, {\n                style: {\n                  flexDirection: 'row',\n                  alignItems: 'center',\n                  marginBottom: 4\n                },\n                children: [_jsx(Text, {\n                  style: {\n                    color: '#fff',\n                    fontSize: 16,\n                    fontWeight: '600',\n                    flex: 1\n                  },\n                  children: channel.name\n                }), !subscriptionStatus && _jsx(View, {\n                  style: {\n                    backgroundColor: '#FF6B35',\n                    paddingHorizontal: 6,\n                    paddingVertical: 2,\n                    borderRadius: 4,\n                    marginLeft: 8\n                  },\n                  children: _jsx(Text, {\n                    style: {\n                      color: '#fff',\n                      fontSize: 8,\n                      fontWeight: 'bold'\n                    },\n                    children: \"PREMIUM\"\n                  })\n                })]\n              }), _jsx(Text, {\n                style: {\n                  color: '#ccc',\n                  fontSize: 13,\n                  marginBottom: 8\n                },\n                children: channel.description\n              }), _jsxs(View, {\n                style: {\n                  flexDirection: 'row',\n                  alignItems: 'center',\n                  flexWrap: 'wrap'\n                },\n                children: [_jsx(View, {\n                  style: {\n                    backgroundColor: channel.isPremium ? '#FECB37' : '#4CAF50',\n                    paddingHorizontal: 6,\n                    paddingVertical: 2,\n                    borderRadius: 4,\n                    marginRight: 8,\n                    marginBottom: 4\n                  },\n                  children: _jsx(Text, {\n                    style: {\n                      color: channel.isPremium ? '#000' : '#fff',\n                      fontSize: 10,\n                      fontWeight: '600'\n                    },\n                    children: channel.type || 'SPOT'\n                  })\n                }), _jsx(View, {\n                  style: {\n                    backgroundColor: '#2196F3',\n                    paddingHorizontal: 6,\n                    paddingVertical: 2,\n                    borderRadius: 4,\n                    marginRight: 8,\n                    marginBottom: 4\n                  },\n                  children: _jsxs(Text, {\n                    style: {\n                      color: '#fff',\n                      fontSize: 10,\n                      fontWeight: '600'\n                    },\n                    children: [channel.totalSignals || 0, \" sinais\"]\n                  })\n                }), _jsx(Text, {\n                  style: {\n                    color: '#8a8a8a',\n                    fontSize: 12\n                  },\n                  children: formatLastSignalTime(channel.lastSignalAt)\n                })]\n              })]\n            })]\n          })\n        }, channel.id);\n      })]\n    })\n  });\n}", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useContext", "View", "ScrollView", "Text", "<PERSON><PERSON>", "useNavigation", "Wrapper", "Card", "AxiosContext", "StoreContext", "jsx", "_jsx", "jsxs", "_jsxs", "Channels", "_ref", "_ref$route", "route", "_useState", "_useState2", "_slicedToArray", "isLoading", "setIsLoading", "_useState3", "_useState4", "channels", "setChannels", "_useState5", "_useState6", "error", "setError", "_useContext", "_useContext2", "api", "_useContext3", "_useContext4", "state", "navigation", "_ref2", "params", "accountHasBeenRecovered", "_ref3", "subscription", "subscriptionStatus", "loadChannels", "_ref4", "_asyncToGenerator", "console", "log", "response", "get", "data", "Array", "isArray", "length", "warn", "err", "id", "externalId", "name", "description", "type", "isPremium", "totalSignals", "recentSignals", "lastSignalAt", "Date", "toISOString", "photo", "apply", "arguments", "handleChannelPress", "channel", "alert", "text", "style", "onPress", "navigate", "channelId", "channelName", "formatLastSignalTime", "date", "now", "diffMs", "diffHours", "Math", "floor", "diffDays", "toLocaleDateString", "_unused", "getChannelAvatar", "strategy", "toLowerCase", "includes", "children", "padding", "color", "fontSize", "fontWeight", "marginBottom", "textAlign", "marginTop", "flex", "flexDirection", "justifyContent", "alignItems", "backgroundColor", "paddingHorizontal", "paddingVertical", "borderRadius", "borderColor", "borderWidth", "map", "opacity", "marginRight", "marginLeft", "flexWrap"], "sources": ["C:/Users/<USER>/Desktop/Signals/CryptoSignalsApp/src/pages/Channels/index.js"], "sourcesContent": ["import React, { useState, useEffect, useContext } from 'react';\r\nimport { View, ScrollView, Text, Alert } from 'react-native';\r\nimport { useNavigation } from '@react-navigation/native';\r\nimport Wrapper from '../../components/Wrapper';\r\nimport Card from '../../components/Card';\r\nimport { AxiosContext } from '../../store/axios';\r\nimport { StoreContext } from '../../store';\r\n\r\nexport default function Channels({route = {}}) {\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [channels, setChannels] = useState([]);\r\n  const [error, setError] = useState(null);\r\n  const [api] = useContext(AxiosContext);\r\n  const [state] = useContext(StoreContext);\r\n  const navigation = useNavigation();\r\n\r\n  const { accountHasBeenRecovered } = route.params || {accountHasBeenRecovered: false};\r\n  const { subscription: { subscriptionStatus } } = state || { subscription: { subscriptionStatus: false } };\r\n\r\n  useEffect(() => {\r\n    loadChannels();\r\n  }, []);\r\n\r\n  const loadChannels = async () => {\r\n    try {\r\n      setIsLoading(true);\r\n      setError(null);\r\n\r\n      console.log('Carregando canais da API...');\r\n      const response = await api.get('/channels');\r\n\r\n      if (response.data && Array.isArray(response.data)) {\r\n        console.log('Canais carregados:', response.data.length);\r\n        setChannels(response.data);\r\n      } else {\r\n        console.warn('Resposta da API não contém array de canais:', response.data);\r\n        setChannels([]);\r\n      }\r\n    } catch (err) {\r\n      console.error('Erro ao carregar canais:', err);\r\n      setError('Erro ao carregar canais. Usando dados offline.');\r\n\r\n      // Fallback para dados mock em caso de erro\r\n      setChannels([\r\n        {\r\n          id: 1,\r\n          externalId: 'strategy-scalp',\r\n          name: 'CryptoSignals Scalp',\r\n          description: 'Sinais de scalping de alta frequência',\r\n          type: 'FUTURES',\r\n          isPremium: false,\r\n          totalSignals: 0,\r\n          recentSignals: 0,\r\n          lastSignalAt: new Date().toISOString(),\r\n          photo: 'https://via.placeholder.com/50x50/4CAF50/FFFFFF?text=S'\r\n        },\r\n        {\r\n          id: 2,\r\n          externalId: 'strategy-swing',\r\n          name: 'CryptoSignals Swing',\r\n          description: 'Sinais de swing trading',\r\n          type: 'SPOT',\r\n          isPremium: false,\r\n          totalSignals: 0,\r\n          recentSignals: 0,\r\n          lastSignalAt: new Date().toISOString(),\r\n          photo: 'https://via.placeholder.com/50x50/2196F3/FFFFFF?text=S'\r\n        }\r\n      ]);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleChannelPress = (channel) => {\r\n    console.log('Canal selecionado:', channel);\r\n\r\n    // Verificar se o usuário tem assinatura premium\r\n    if (!subscriptionStatus) {\r\n      Alert.alert(\r\n        \"Acesso Premium Necessário 💎\",\r\n        \"Para acessar os sinais de trading, você precisa de uma assinatura premium.\",\r\n        [\r\n          {\r\n            text: \"Cancelar\",\r\n            style: \"cancel\"\r\n          },\r\n          {\r\n            text: \"Ver Planos\",\r\n            onPress: () => navigation.navigate('Premium')\r\n          }\r\n        ]\r\n      );\r\n      return;\r\n    }\r\n\r\n    // Navegar para a página de sinais do canal\r\n    navigation.navigate('Signals', {\r\n      channelId: channel.externalId || channel.id,\r\n      channelName: channel.name\r\n    });\r\n  };\r\n\r\n  useEffect(() => {\r\n    if(accountHasBeenRecovered) {\r\n      Alert.alert(\"Success\", \"Account recovered successfully\");\r\n    }\r\n  }, [accountHasBeenRecovered]);\r\n\r\n  const formatLastSignalTime = (lastSignalAt) => {\r\n    if (!lastSignalAt) return 'Nunca';\r\n\r\n    try {\r\n      const date = new Date(lastSignalAt);\r\n      const now = new Date();\r\n      const diffMs = now - date;\r\n      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));\r\n      const diffDays = Math.floor(diffHours / 24);\r\n\r\n      if (diffHours < 1) return 'Agora mesmo';\r\n      if (diffHours < 24) return `${diffHours}h atrás`;\r\n      if (diffDays < 7) return `${diffDays}d atrás`;\r\n      return date.toLocaleDateString();\r\n    } catch {\r\n      return 'Recente';\r\n    }\r\n  };\r\n\r\n  const getChannelAvatar = (channel) => {\r\n    const strategy = channel.name.toLowerCase();\r\n    if (strategy.includes('scalp')) return '⚡';\r\n    if (strategy.includes('swing')) return '📈';\r\n    if (strategy.includes('breakout')) return '🚀';\r\n    if (strategy.includes('mfi')) return '💰';\r\n    if (strategy.includes('volume')) return '📊';\r\n    if (strategy.includes('momentum')) return '🎯';\r\n    return '📡';\r\n  };\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <Wrapper>\r\n        <View style={{ padding: 16 }}>\r\n          <Text style={{ color: '#fff', fontSize: 24, fontWeight: 'bold', marginBottom: 20 }}>\r\n            Signal Channels 📺\r\n          </Text>\r\n          <Text style={{ color: '#8a8a8a', fontSize: 16, textAlign: 'center', marginTop: 50 }}>\r\n            Carregando canais...\r\n          </Text>\r\n        </View>\r\n      </Wrapper>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Wrapper>\r\n      <ScrollView style={{ flex: 1, padding: 16 }}>\r\n        {/* Header */}\r\n        <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>\r\n          <View style={{ flex: 1 }}>\r\n            <Text style={{\r\n              color: '#fff',\r\n              fontSize: 28,\r\n              fontWeight: 'bold',\r\n              marginBottom: 4\r\n            }}>\r\n              Signal Channels 📺\r\n            </Text>\r\n            <Text style={{\r\n              color: '#8a8a8a',\r\n              fontSize: 14\r\n            }}>\r\n              Estratégias de trading automatizadas\r\n            </Text>\r\n          </View>\r\n\r\n          {/* Status Badge */}\r\n          <View style={{\r\n            backgroundColor: subscriptionStatus ? '#4CAF50' : '#FF6B35',\r\n            paddingHorizontal: 12,\r\n            paddingVertical: 6,\r\n            borderRadius: 16,\r\n            alignItems: 'center'\r\n          }}>\r\n            <Text style={{\r\n              color: '#fff',\r\n              fontSize: 10,\r\n              fontWeight: 'bold'\r\n            }}>\r\n              {subscriptionStatus ? '💎 PREMIUM' : '🔒 FREE'}\r\n            </Text>\r\n          </View>\r\n        </View>\r\n\r\n        {/* Error Message */}\r\n        {error && (\r\n          <View style={{\r\n            backgroundColor: '#FF6B35',\r\n            padding: 12,\r\n            borderRadius: 8,\r\n            marginBottom: 16\r\n          }}>\r\n            <Text style={{ color: '#fff', fontSize: 12, textAlign: 'center' }}>\r\n              {error}\r\n            </Text>\r\n          </View>\r\n        )}\r\n\r\n        {/* Premium Notice for Free Users */}\r\n        {!subscriptionStatus && (\r\n          <Card style={{ marginBottom: 16, backgroundColor: '#FF6B3520', borderColor: '#FF6B35', borderWidth: 1 }}>\r\n            <View style={{ padding: 16, alignItems: 'center' }}>\r\n              <Text style={{ color: '#FF6B35', fontSize: 16, fontWeight: 'bold', marginBottom: 8 }}>\r\n                🔒 Acesso Premium Necessário\r\n              </Text>\r\n              <Text style={{ color: '#FFB74D', fontSize: 14, textAlign: 'center', marginBottom: 12 }}>\r\n                Para acessar os sinais de trading profissionais, você precisa de uma assinatura premium.\r\n              </Text>\r\n              <Card style={{ backgroundColor: '#FF6B35' }} onPress={() => navigation.navigate('Premium')}>\r\n                <View style={{ paddingHorizontal: 20, paddingVertical: 8 }}>\r\n                  <Text style={{ color: '#fff', fontSize: 14, fontWeight: 'bold' }}>\r\n                    Ver Planos Premium\r\n                  </Text>\r\n                </View>\r\n              </Card>\r\n            </View>\r\n          </Card>\r\n        )}\r\n\r\n        {/* Refresh Button */}\r\n        <Card style={{ marginBottom: 16 }} onPress={loadChannels}>\r\n          <View style={{ alignItems: 'center', padding: 8 }}>\r\n            <Text style={{ color: '#4CAF50', fontSize: 14, fontWeight: '600' }}>\r\n              🔄 Atualizar Canais\r\n            </Text>\r\n          </View>\r\n        </Card>\r\n\r\n        {/* Channels List */}\r\n        {channels.length === 0 ? (\r\n          <View style={{ alignItems: 'center', marginTop: 50 }}>\r\n            <Text style={{ color: '#8a8a8a', fontSize: 16, textAlign: 'center' }}>\r\n              Nenhum canal disponível\r\n            </Text>\r\n            <Text style={{ color: '#666', fontSize: 12, textAlign: 'center', marginTop: 8 }}>\r\n              Verifique se o gerador de sinais está rodando\r\n            </Text>\r\n          </View>\r\n        ) : (\r\n          channels.map((channel) => (\r\n            <Card key={channel.id} style={{\r\n              marginBottom: 12,\r\n              opacity: !subscriptionStatus ? 0.6 : 1,\r\n              borderColor: !subscriptionStatus ? '#FF6B35' : 'transparent',\r\n              borderWidth: !subscriptionStatus ? 1 : 0\r\n            }} onPress={() => handleChannelPress(channel)}>\r\n              <View style={{ flexDirection: 'row', alignItems: 'center' }}>\r\n                <View style={{\r\n                  backgroundColor: '#333',\r\n                  borderRadius: 12,\r\n                  padding: 12,\r\n                  marginRight: 12,\r\n                  alignItems: 'center',\r\n                  justifyContent: 'center'\r\n                }}>\r\n                  <Text style={{ fontSize: 24 }}>{getChannelAvatar(channel)}</Text>\r\n                </View>\r\n\r\n                <View style={{ flex: 1 }}>\r\n                  <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 4 }}>\r\n                    <Text style={{\r\n                      color: '#fff',\r\n                      fontSize: 16,\r\n                      fontWeight: '600',\r\n                      flex: 1\r\n                    }}>\r\n                      {channel.name}\r\n                    </Text>\r\n\r\n                    {!subscriptionStatus && (\r\n                      <View style={{\r\n                        backgroundColor: '#FF6B35',\r\n                        paddingHorizontal: 6,\r\n                        paddingVertical: 2,\r\n                        borderRadius: 4,\r\n                        marginLeft: 8\r\n                      }}>\r\n                        <Text style={{\r\n                          color: '#fff',\r\n                          fontSize: 8,\r\n                          fontWeight: 'bold'\r\n                        }}>\r\n                          PREMIUM\r\n                        </Text>\r\n                      </View>\r\n                    )}\r\n                  </View>\r\n\r\n                  <Text style={{\r\n                    color: '#ccc',\r\n                    fontSize: 13,\r\n                    marginBottom: 8\r\n                  }}>\r\n                    {channel.description}\r\n                  </Text>\r\n\r\n                  <View style={{ flexDirection: 'row', alignItems: 'center', flexWrap: 'wrap' }}>\r\n                    <View style={{\r\n                      backgroundColor: channel.isPremium ? '#FECB37' : '#4CAF50',\r\n                      paddingHorizontal: 6,\r\n                      paddingVertical: 2,\r\n                      borderRadius: 4,\r\n                      marginRight: 8,\r\n                      marginBottom: 4\r\n                    }}>\r\n                      <Text style={{\r\n                        color: channel.isPremium ? '#000' : '#fff',\r\n                        fontSize: 10,\r\n                        fontWeight: '600'\r\n                      }}>\r\n                        {channel.type || 'SPOT'}\r\n                      </Text>\r\n                    </View>\r\n\r\n                    <View style={{\r\n                      backgroundColor: '#2196F3',\r\n                      paddingHorizontal: 6,\r\n                      paddingVertical: 2,\r\n                      borderRadius: 4,\r\n                      marginRight: 8,\r\n                      marginBottom: 4\r\n                    }}>\r\n                      <Text style={{\r\n                        color: '#fff',\r\n                        fontSize: 10,\r\n                        fontWeight: '600'\r\n                      }}>\r\n                        {channel.totalSignals || 0} sinais\r\n                      </Text>\r\n                    </View>\r\n\r\n                    <Text style={{ color: '#8a8a8a', fontSize: 12 }}>\r\n                      {formatLastSignalTime(channel.lastSignalAt)}\r\n                    </Text>\r\n                  </View>\r\n                </View>\r\n              </View>\r\n            </Card>\r\n          ))\r\n        )}\r\n      </ScrollView>\r\n    </Wrapper>\r\n  );\r\n}"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,QAAQ,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,KAAA;AAE/D,SAASC,aAAa,QAAQ,0BAA0B;AACxD,OAAOC,OAAO;AACd,OAAOC,IAAI;AACX,SAASC,YAAY;AACrB,SAASC,YAAY;AAAsB,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAE3C,eAAe,SAASC,QAAQA,CAAAC,IAAA,EAAe;EAAA,IAAAC,UAAA,GAAAD,IAAA,CAAbE,KAAK;IAALA,KAAK,GAAAD,UAAA,cAAG,CAAC,CAAC,GAAAA,UAAA;EAC1C,IAAAE,SAAA,GAAkCpB,QAAQ,CAAC,IAAI,CAAC;IAAAqB,UAAA,GAAAC,cAAA,CAAAF,SAAA;IAAzCG,SAAS,GAAAF,UAAA;IAAEG,YAAY,GAAAH,UAAA;EAC9B,IAAAI,UAAA,GAAgCzB,QAAQ,CAAC,EAAE,CAAC;IAAA0B,UAAA,GAAAJ,cAAA,CAAAG,UAAA;IAArCE,QAAQ,GAAAD,UAAA;IAAEE,WAAW,GAAAF,UAAA;EAC5B,IAAAG,UAAA,GAA0B7B,QAAQ,CAAC,IAAI,CAAC;IAAA8B,UAAA,GAAAR,cAAA,CAAAO,UAAA;IAAjCE,KAAK,GAAAD,UAAA;IAAEE,QAAQ,GAAAF,UAAA;EACtB,IAAAG,WAAA,GAAc/B,UAAU,CAACQ,YAAY,CAAC;IAAAwB,YAAA,GAAAZ,cAAA,CAAAW,WAAA;IAA/BE,GAAG,GAAAD,YAAA;EACV,IAAAE,YAAA,GAAgBlC,UAAU,CAACS,YAAY,CAAC;IAAA0B,YAAA,GAAAf,cAAA,CAAAc,YAAA;IAAjCE,KAAK,GAAAD,YAAA;EACZ,IAAME,UAAU,GAAGhC,aAAa,CAAC,CAAC;EAElC,IAAAiC,KAAA,GAAoCrB,KAAK,CAACsB,MAAM,IAAI;MAACC,uBAAuB,EAAE;IAAK,CAAC;IAA5EA,uBAAuB,GAAAF,KAAA,CAAvBE,uBAAuB;EAC/B,IAAAC,KAAA,GAAiDL,KAAK,IAAI;MAAEM,YAAY,EAAE;QAAEC,kBAAkB,EAAE;MAAM;IAAE,CAAC;IAAjFA,kBAAkB,GAAAF,KAAA,CAAlCC,YAAY,CAAIC,kBAAkB;EAE1C5C,SAAS,CAAC,YAAM;IACd6C,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,IAAMA,YAAY;IAAA,IAAAC,KAAA,GAAAC,iBAAA,CAAG,aAAY;MAC/B,IAAI;QACFxB,YAAY,CAAC,IAAI,CAAC;QAClBQ,QAAQ,CAAC,IAAI,CAAC;QAEdiB,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;QAC1C,IAAMC,QAAQ,SAAShB,GAAG,CAACiB,GAAG,CAAC,WAAW,CAAC;QAE3C,IAAID,QAAQ,CAACE,IAAI,IAAIC,KAAK,CAACC,OAAO,CAACJ,QAAQ,CAACE,IAAI,CAAC,EAAE;UACjDJ,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEC,QAAQ,CAACE,IAAI,CAACG,MAAM,CAAC;UACvD5B,WAAW,CAACuB,QAAQ,CAACE,IAAI,CAAC;QAC5B,CAAC,MAAM;UACLJ,OAAO,CAACQ,IAAI,CAAC,6CAA6C,EAAEN,QAAQ,CAACE,IAAI,CAAC;UAC1EzB,WAAW,CAAC,EAAE,CAAC;QACjB;MACF,CAAC,CAAC,OAAO8B,GAAG,EAAE;QACZT,OAAO,CAAClB,KAAK,CAAC,0BAA0B,EAAE2B,GAAG,CAAC;QAC9C1B,QAAQ,CAAC,gDAAgD,CAAC;QAG1DJ,WAAW,CAAC,CACV;UACE+B,EAAE,EAAE,CAAC;UACLC,UAAU,EAAE,gBAAgB;UAC5BC,IAAI,EAAE,qBAAqB;UAC3BC,WAAW,EAAE,uCAAuC;UACpDC,IAAI,EAAE,SAAS;UACfC,SAAS,EAAE,KAAK;UAChBC,YAAY,EAAE,CAAC;UACfC,aAAa,EAAE,CAAC;UAChBC,YAAY,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UACtCC,KAAK,EAAE;QACT,CAAC,EACD;UACEX,EAAE,EAAE,CAAC;UACLC,UAAU,EAAE,gBAAgB;UAC5BC,IAAI,EAAE,qBAAqB;UAC3BC,WAAW,EAAE,yBAAyB;UACtCC,IAAI,EAAE,MAAM;UACZC,SAAS,EAAE,KAAK;UAChBC,YAAY,EAAE,CAAC;UACfC,aAAa,EAAE,CAAC;UAChBC,YAAY,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UACtCC,KAAK,EAAE;QACT,CAAC,CACF,CAAC;MACJ,CAAC,SAAS;QACR9C,YAAY,CAAC,KAAK,CAAC;MACrB;IACF,CAAC;IAAA,gBAjDKsB,YAAYA,CAAA;MAAA,OAAAC,KAAA,CAAAwB,KAAA,OAAAC,SAAA;IAAA;EAAA,GAiDjB;EAED,IAAMC,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAIC,OAAO,EAAK;IACtCzB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEwB,OAAO,CAAC;IAG1C,IAAI,CAAC7B,kBAAkB,EAAE;MACvBvC,KAAK,CAACqE,KAAK,CACT,8BAA8B,EAC9B,4EAA4E,EAC5E,CACE;QACEC,IAAI,EAAE,UAAU;QAChBC,KAAK,EAAE;MACT,CAAC,EACD;QACED,IAAI,EAAE,YAAY;QAClBE,OAAO,EAAE,SAATA,OAAOA,CAAA;UAAA,OAAQvC,UAAU,CAACwC,QAAQ,CAAC,SAAS,CAAC;QAAA;MAC/C,CAAC,CAEL,CAAC;MACD;IACF;IAGAxC,UAAU,CAACwC,QAAQ,CAAC,SAAS,EAAE;MAC7BC,SAAS,EAAEN,OAAO,CAACd,UAAU,IAAIc,OAAO,CAACf,EAAE;MAC3CsB,WAAW,EAAEP,OAAO,CAACb;IACvB,CAAC,CAAC;EACJ,CAAC;EAED5D,SAAS,CAAC,YAAM;IACd,IAAGyC,uBAAuB,EAAE;MAC1BpC,KAAK,CAACqE,KAAK,CAAC,SAAS,EAAE,gCAAgC,CAAC;IAC1D;EACF,CAAC,EAAE,CAACjC,uBAAuB,CAAC,CAAC;EAE7B,IAAMwC,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAIf,YAAY,EAAK;IAC7C,IAAI,CAACA,YAAY,EAAE,OAAO,OAAO;IAEjC,IAAI;MACF,IAAMgB,IAAI,GAAG,IAAIf,IAAI,CAACD,YAAY,CAAC;MACnC,IAAMiB,GAAG,GAAG,IAAIhB,IAAI,CAAC,CAAC;MACtB,IAAMiB,MAAM,GAAGD,GAAG,GAAGD,IAAI;MACzB,IAAMG,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACH,MAAM,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;MACvD,IAAMI,QAAQ,GAAGF,IAAI,CAACC,KAAK,CAACF,SAAS,GAAG,EAAE,CAAC;MAE3C,IAAIA,SAAS,GAAG,CAAC,EAAE,OAAO,aAAa;MACvC,IAAIA,SAAS,GAAG,EAAE,EAAE,OAAO,GAAGA,SAAS,SAAS;MAChD,IAAIG,QAAQ,GAAG,CAAC,EAAE,OAAO,GAAGA,QAAQ,SAAS;MAC7C,OAAON,IAAI,CAACO,kBAAkB,CAAC,CAAC;IAClC,CAAC,CAAC,OAAAC,OAAA,EAAM;MACN,OAAO,SAAS;IAClB;EACF,CAAC;EAED,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIlB,OAAO,EAAK;IACpC,IAAMmB,QAAQ,GAAGnB,OAAO,CAACb,IAAI,CAACiC,WAAW,CAAC,CAAC;IAC3C,IAAID,QAAQ,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,GAAG;IAC1C,IAAIF,QAAQ,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,IAAI;IAC3C,IAAIF,QAAQ,CAACE,QAAQ,CAAC,UAAU,CAAC,EAAE,OAAO,IAAI;IAC9C,IAAIF,QAAQ,CAACE,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,IAAI;IACzC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,QAAQ,CAAC,EAAE,OAAO,IAAI;IAC5C,IAAIF,QAAQ,CAACE,QAAQ,CAAC,UAAU,CAAC,EAAE,OAAO,IAAI;IAC9C,OAAO,IAAI;EACb,CAAC;EAED,IAAIxE,SAAS,EAAE;IACb,OACEV,IAAA,CAACL,OAAO;MAAAwF,QAAA,EACNjF,KAAA,CAACZ,IAAI;QAAC0E,KAAK,EAAE;UAAEoB,OAAO,EAAE;QAAG,CAAE;QAAAD,QAAA,GAC3BnF,IAAA,CAACR,IAAI;UAACwE,KAAK,EAAE;YAAEqB,KAAK,EAAE,MAAM;YAAEC,QAAQ,EAAE,EAAE;YAAEC,UAAU,EAAE,MAAM;YAAEC,YAAY,EAAE;UAAG,CAAE;UAAAL,QAAA,EAAC;QAEpF,CAAM,CAAC,EACPnF,IAAA,CAACR,IAAI;UAACwE,KAAK,EAAE;YAAEqB,KAAK,EAAE,SAAS;YAAEC,QAAQ,EAAE,EAAE;YAAEG,SAAS,EAAE,QAAQ;YAAEC,SAAS,EAAE;UAAG,CAAE;UAAAP,QAAA,EAAC;QAErF,CAAM,CAAC;MAAA,CACH;IAAC,CACA,CAAC;EAEd;EAEA,OACEnF,IAAA,CAACL,OAAO;IAAAwF,QAAA,EACNjF,KAAA,CAACX,UAAU;MAACyE,KAAK,EAAE;QAAE2B,IAAI,EAAE,CAAC;QAAEP,OAAO,EAAE;MAAG,CAAE;MAAAD,QAAA,GAE1CjF,KAAA,CAACZ,IAAI;QAAC0E,KAAK,EAAE;UAAE4B,aAAa,EAAE,KAAK;UAAEC,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE,QAAQ;UAAEN,YAAY,EAAE;QAAE,CAAE;QAAAL,QAAA,GAC5GjF,KAAA,CAACZ,IAAI;UAAC0E,KAAK,EAAE;YAAE2B,IAAI,EAAE;UAAE,CAAE;UAAAR,QAAA,GACvBnF,IAAA,CAACR,IAAI;YAACwE,KAAK,EAAE;cACXqB,KAAK,EAAE,MAAM;cACbC,QAAQ,EAAE,EAAE;cACZC,UAAU,EAAE,MAAM;cAClBC,YAAY,EAAE;YAChB,CAAE;YAAAL,QAAA,EAAC;UAEH,CAAM,CAAC,EACPnF,IAAA,CAACR,IAAI;YAACwE,KAAK,EAAE;cACXqB,KAAK,EAAE,SAAS;cAChBC,QAAQ,EAAE;YACZ,CAAE;YAAAH,QAAA,EAAC;UAEH,CAAM,CAAC;QAAA,CACH,CAAC,EAGPnF,IAAA,CAACV,IAAI;UAAC0E,KAAK,EAAE;YACX+B,eAAe,EAAE/D,kBAAkB,GAAG,SAAS,GAAG,SAAS;YAC3DgE,iBAAiB,EAAE,EAAE;YACrBC,eAAe,EAAE,CAAC;YAClBC,YAAY,EAAE,EAAE;YAChBJ,UAAU,EAAE;UACd,CAAE;UAAAX,QAAA,EACAnF,IAAA,CAACR,IAAI;YAACwE,KAAK,EAAE;cACXqB,KAAK,EAAE,MAAM;cACbC,QAAQ,EAAE,EAAE;cACZC,UAAU,EAAE;YACd,CAAE;YAAAJ,QAAA,EACCnD,kBAAkB,GAAG,YAAY,GAAG;UAAS,CAC1C;QAAC,CACH,CAAC;MAAA,CACH,CAAC,EAGNd,KAAK,IACJlB,IAAA,CAACV,IAAI;QAAC0E,KAAK,EAAE;UACX+B,eAAe,EAAE,SAAS;UAC1BX,OAAO,EAAE,EAAE;UACXc,YAAY,EAAE,CAAC;UACfV,YAAY,EAAE;QAChB,CAAE;QAAAL,QAAA,EACAnF,IAAA,CAACR,IAAI;UAACwE,KAAK,EAAE;YAAEqB,KAAK,EAAE,MAAM;YAAEC,QAAQ,EAAE,EAAE;YAAEG,SAAS,EAAE;UAAS,CAAE;UAAAN,QAAA,EAC/DjE;QAAK,CACF;MAAC,CACH,CACP,EAGA,CAACc,kBAAkB,IAClBhC,IAAA,CAACJ,IAAI;QAACoE,KAAK,EAAE;UAAEwB,YAAY,EAAE,EAAE;UAAEO,eAAe,EAAE,WAAW;UAAEI,WAAW,EAAE,SAAS;UAAEC,WAAW,EAAE;QAAE,CAAE;QAAAjB,QAAA,EACtGjF,KAAA,CAACZ,IAAI;UAAC0E,KAAK,EAAE;YAAEoB,OAAO,EAAE,EAAE;YAAEU,UAAU,EAAE;UAAS,CAAE;UAAAX,QAAA,GACjDnF,IAAA,CAACR,IAAI;YAACwE,KAAK,EAAE;cAAEqB,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE,EAAE;cAAEC,UAAU,EAAE,MAAM;cAAEC,YAAY,EAAE;YAAE,CAAE;YAAAL,QAAA,EAAC;UAEtF,CAAM,CAAC,EACPnF,IAAA,CAACR,IAAI;YAACwE,KAAK,EAAE;cAAEqB,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE,EAAE;cAAEG,SAAS,EAAE,QAAQ;cAAED,YAAY,EAAE;YAAG,CAAE;YAAAL,QAAA,EAAC;UAExF,CAAM,CAAC,EACPnF,IAAA,CAACJ,IAAI;YAACoE,KAAK,EAAE;cAAE+B,eAAe,EAAE;YAAU,CAAE;YAAC9B,OAAO,EAAE,SAATA,OAAOA,CAAA;cAAA,OAAQvC,UAAU,CAACwC,QAAQ,CAAC,SAAS,CAAC;YAAA,CAAC;YAAAiB,QAAA,EACzFnF,IAAA,CAACV,IAAI;cAAC0E,KAAK,EAAE;gBAAEgC,iBAAiB,EAAE,EAAE;gBAAEC,eAAe,EAAE;cAAE,CAAE;cAAAd,QAAA,EACzDnF,IAAA,CAACR,IAAI;gBAACwE,KAAK,EAAE;kBAAEqB,KAAK,EAAE,MAAM;kBAAEC,QAAQ,EAAE,EAAE;kBAAEC,UAAU,EAAE;gBAAO,CAAE;gBAAAJ,QAAA,EAAC;cAElE,CAAM;YAAC,CACH;UAAC,CACH,CAAC;QAAA,CACH;MAAC,CACH,CACP,EAGDnF,IAAA,CAACJ,IAAI;QAACoE,KAAK,EAAE;UAAEwB,YAAY,EAAE;QAAG,CAAE;QAACvB,OAAO,EAAEhC,YAAa;QAAAkD,QAAA,EACvDnF,IAAA,CAACV,IAAI;UAAC0E,KAAK,EAAE;YAAE8B,UAAU,EAAE,QAAQ;YAAEV,OAAO,EAAE;UAAE,CAAE;UAAAD,QAAA,EAChDnF,IAAA,CAACR,IAAI;YAACwE,KAAK,EAAE;cAAEqB,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE,EAAE;cAAEC,UAAU,EAAE;YAAM,CAAE;YAAAJ,QAAA,EAAC;UAEpE,CAAM;QAAC,CACH;MAAC,CACH,CAAC,EAGNrE,QAAQ,CAAC6B,MAAM,KAAK,CAAC,GACpBzC,KAAA,CAACZ,IAAI;QAAC0E,KAAK,EAAE;UAAE8B,UAAU,EAAE,QAAQ;UAAEJ,SAAS,EAAE;QAAG,CAAE;QAAAP,QAAA,GACnDnF,IAAA,CAACR,IAAI;UAACwE,KAAK,EAAE;YAAEqB,KAAK,EAAE,SAAS;YAAEC,QAAQ,EAAE,EAAE;YAAEG,SAAS,EAAE;UAAS,CAAE;UAAAN,QAAA,EAAC;QAEtE,CAAM,CAAC,EACPnF,IAAA,CAACR,IAAI;UAACwE,KAAK,EAAE;YAAEqB,KAAK,EAAE,MAAM;YAAEC,QAAQ,EAAE,EAAE;YAAEG,SAAS,EAAE,QAAQ;YAAEC,SAAS,EAAE;UAAE,CAAE;UAAAP,QAAA,EAAC;QAEjF,CAAM,CAAC;MAAA,CACH,CAAC,GAEPrE,QAAQ,CAACuF,GAAG,CAAC,UAACxC,OAAO;QAAA,OACnB7D,IAAA,CAACJ,IAAI;UAAkBoE,KAAK,EAAE;YAC5BwB,YAAY,EAAE,EAAE;YAChBc,OAAO,EAAE,CAACtE,kBAAkB,GAAG,GAAG,GAAG,CAAC;YACtCmE,WAAW,EAAE,CAACnE,kBAAkB,GAAG,SAAS,GAAG,aAAa;YAC5DoE,WAAW,EAAE,CAACpE,kBAAkB,GAAG,CAAC,GAAG;UACzC,CAAE;UAACiC,OAAO,EAAE,SAATA,OAAOA,CAAA;YAAA,OAAQL,kBAAkB,CAACC,OAAO,CAAC;UAAA,CAAC;UAAAsB,QAAA,EAC5CjF,KAAA,CAACZ,IAAI;YAAC0E,KAAK,EAAE;cAAE4B,aAAa,EAAE,KAAK;cAAEE,UAAU,EAAE;YAAS,CAAE;YAAAX,QAAA,GAC1DnF,IAAA,CAACV,IAAI;cAAC0E,KAAK,EAAE;gBACX+B,eAAe,EAAE,MAAM;gBACvBG,YAAY,EAAE,EAAE;gBAChBd,OAAO,EAAE,EAAE;gBACXmB,WAAW,EAAE,EAAE;gBACfT,UAAU,EAAE,QAAQ;gBACpBD,cAAc,EAAE;cAClB,CAAE;cAAAV,QAAA,EACAnF,IAAA,CAACR,IAAI;gBAACwE,KAAK,EAAE;kBAAEsB,QAAQ,EAAE;gBAAG,CAAE;gBAAAH,QAAA,EAAEJ,gBAAgB,CAAClB,OAAO;cAAC,CAAO;YAAC,CAC7D,CAAC,EAEP3D,KAAA,CAACZ,IAAI;cAAC0E,KAAK,EAAE;gBAAE2B,IAAI,EAAE;cAAE,CAAE;cAAAR,QAAA,GACvBjF,KAAA,CAACZ,IAAI;gBAAC0E,KAAK,EAAE;kBAAE4B,aAAa,EAAE,KAAK;kBAAEE,UAAU,EAAE,QAAQ;kBAAEN,YAAY,EAAE;gBAAE,CAAE;gBAAAL,QAAA,GAC3EnF,IAAA,CAACR,IAAI;kBAACwE,KAAK,EAAE;oBACXqB,KAAK,EAAE,MAAM;oBACbC,QAAQ,EAAE,EAAE;oBACZC,UAAU,EAAE,KAAK;oBACjBI,IAAI,EAAE;kBACR,CAAE;kBAAAR,QAAA,EACCtB,OAAO,CAACb;gBAAI,CACT,CAAC,EAEN,CAAChB,kBAAkB,IAClBhC,IAAA,CAACV,IAAI;kBAAC0E,KAAK,EAAE;oBACX+B,eAAe,EAAE,SAAS;oBAC1BC,iBAAiB,EAAE,CAAC;oBACpBC,eAAe,EAAE,CAAC;oBAClBC,YAAY,EAAE,CAAC;oBACfM,UAAU,EAAE;kBACd,CAAE;kBAAArB,QAAA,EACAnF,IAAA,CAACR,IAAI;oBAACwE,KAAK,EAAE;sBACXqB,KAAK,EAAE,MAAM;sBACbC,QAAQ,EAAE,CAAC;sBACXC,UAAU,EAAE;oBACd,CAAE;oBAAAJ,QAAA,EAAC;kBAEH,CAAM;gBAAC,CACH,CACP;cAAA,CACG,CAAC,EAEPnF,IAAA,CAACR,IAAI;gBAACwE,KAAK,EAAE;kBACXqB,KAAK,EAAE,MAAM;kBACbC,QAAQ,EAAE,EAAE;kBACZE,YAAY,EAAE;gBAChB,CAAE;gBAAAL,QAAA,EACCtB,OAAO,CAACZ;cAAW,CAChB,CAAC,EAEP/C,KAAA,CAACZ,IAAI;gBAAC0E,KAAK,EAAE;kBAAE4B,aAAa,EAAE,KAAK;kBAAEE,UAAU,EAAE,QAAQ;kBAAEW,QAAQ,EAAE;gBAAO,CAAE;gBAAAtB,QAAA,GAC5EnF,IAAA,CAACV,IAAI;kBAAC0E,KAAK,EAAE;oBACX+B,eAAe,EAAElC,OAAO,CAACV,SAAS,GAAG,SAAS,GAAG,SAAS;oBAC1D6C,iBAAiB,EAAE,CAAC;oBACpBC,eAAe,EAAE,CAAC;oBAClBC,YAAY,EAAE,CAAC;oBACfK,WAAW,EAAE,CAAC;oBACdf,YAAY,EAAE;kBAChB,CAAE;kBAAAL,QAAA,EACAnF,IAAA,CAACR,IAAI;oBAACwE,KAAK,EAAE;sBACXqB,KAAK,EAAExB,OAAO,CAACV,SAAS,GAAG,MAAM,GAAG,MAAM;sBAC1CmC,QAAQ,EAAE,EAAE;sBACZC,UAAU,EAAE;oBACd,CAAE;oBAAAJ,QAAA,EACCtB,OAAO,CAACX,IAAI,IAAI;kBAAM,CACnB;gBAAC,CACH,CAAC,EAEPlD,IAAA,CAACV,IAAI;kBAAC0E,KAAK,EAAE;oBACX+B,eAAe,EAAE,SAAS;oBAC1BC,iBAAiB,EAAE,CAAC;oBACpBC,eAAe,EAAE,CAAC;oBAClBC,YAAY,EAAE,CAAC;oBACfK,WAAW,EAAE,CAAC;oBACdf,YAAY,EAAE;kBAChB,CAAE;kBAAAL,QAAA,EACAjF,KAAA,CAACV,IAAI;oBAACwE,KAAK,EAAE;sBACXqB,KAAK,EAAE,MAAM;sBACbC,QAAQ,EAAE,EAAE;sBACZC,UAAU,EAAE;oBACd,CAAE;oBAAAJ,QAAA,GACCtB,OAAO,CAACT,YAAY,IAAI,CAAC,EAAC,SAC7B;kBAAA,CAAM;gBAAC,CACH,CAAC,EAEPpD,IAAA,CAACR,IAAI;kBAACwE,KAAK,EAAE;oBAAEqB,KAAK,EAAE,SAAS;oBAAEC,QAAQ,EAAE;kBAAG,CAAE;kBAAAH,QAAA,EAC7Cd,oBAAoB,CAACR,OAAO,CAACP,YAAY;gBAAC,CACvC,CAAC;cAAA,CACH,CAAC;YAAA,CACH,CAAC;UAAA,CACH;QAAC,GAhGEO,OAAO,CAACf,EAiGb,CAAC;MAAA,CACR,CACF;IAAA,CACS;EAAC,CACN,CAAC;AAEd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}