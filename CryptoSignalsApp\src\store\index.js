import React, { useReducer } from 'react';
import globalReducer from './reducers';

const initialArg = {
  subscription: {
    subscriptionStatus: false,
    lastSubscriptionStatusDate: null,
    customerId: null,
    subscriptionPeriodEnd: null,
    accessToken: null
  },
  signalFromWebsocket: {},
  channelOrderedByNewSignal: null,
  pushNotification: {
    token: null,
    lastLoadTokenDate: null
  }
}

export const StoreContext = React.createContext(initialArg);

export default function StoreProvider(props) {
  const [state, dispatch] = useReducer(globalReducer, initialArg);

  return (
    <StoreContext.Provider value={[ state, dispatch ]}>
      {props.children}
    </StoreContext.Provider>
  );
}
