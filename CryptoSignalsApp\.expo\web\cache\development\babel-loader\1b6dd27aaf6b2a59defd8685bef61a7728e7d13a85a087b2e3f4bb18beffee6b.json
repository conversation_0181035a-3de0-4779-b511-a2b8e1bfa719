{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport * as React from 'react';\nexport default function useThenable(create) {\n  var _React$useState = React.useState(create),\n    _React$useState2 = _slicedToArray(_React$useState, 1),\n    promise = _React$useState2[0];\n  var initialState = [false, undefined];\n  promise.then(function (result) {\n    initialState = [true, result];\n  });\n  var _React$useState3 = React.useState(initialState),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    state = _React$useState4[0],\n    setState = _React$useState4[1];\n  var _state = _slicedToArray(state, 1),\n    resolved = _state[0];\n  React.useEffect(function () {\n    var cancelled = false;\n    var resolve = function () {\n      var _ref = _asyncToGenerator(function* () {\n        var result;\n        try {\n          result = yield promise;\n        } finally {\n          if (!cancelled) {\n            setState([true, result]);\n          }\n        }\n      });\n      return function resolve() {\n        return _ref.apply(this, arguments);\n      };\n    }();\n    if (!resolved) {\n      resolve();\n    }\n    return function () {\n      cancelled = true;\n    };\n  }, [promise, resolved]);\n  return state;\n}", "map": {"version": 3, "names": ["React", "useThenable", "create", "_React$useState", "useState", "_React$useState2", "_slicedToArray", "promise", "initialState", "undefined", "then", "result", "_React$useState3", "_React$useState4", "state", "setState", "_state", "resolved", "useEffect", "cancelled", "resolve", "_ref", "_asyncToGenerator", "apply", "arguments"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\@react-navigation\\native\\src\\useThenable.tsx"], "sourcesContent": ["import * as React from 'react';\n\nexport default function useThenable<T>(create: () => PromiseLike<T>) {\n  const [promise] = React.useState(create);\n\n  let initialState: [boolean, T | undefined] = [false, undefined];\n\n  // Check if our thenable is synchronous\n  promise.then((result) => {\n    initialState = [true, result];\n  });\n\n  const [state, setState] = React.useState(initialState);\n  const [resolved] = state;\n\n  React.useEffect(() => {\n    let cancelled = false;\n\n    const resolve = async () => {\n      let result;\n\n      try {\n        result = await promise;\n      } finally {\n        if (!cancelled) {\n          setState([true, result]);\n        }\n      }\n    };\n\n    if (!resolved) {\n      resolve();\n    }\n\n    return () => {\n      cancelled = true;\n    };\n  }, [promise, resolved]);\n\n  return state;\n}\n"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,eAAe,SAASC,WAAWA,CAAIC,MAA4B,EAAE;EACnE,IAAAC,eAAA,GAAkBH,KAAK,CAACI,QAAQ,CAACF,MAAM,CAAC;IAAAG,gBAAA,GAAAC,cAAA,CAAAH,eAAA;IAAjCI,OAAO,GAAAF,gBAAA;EAEd,IAAIG,YAAsC,GAAG,CAAC,KAAK,EAAEC,SAAS,CAAC;EAG/DF,OAAO,CAACG,IAAI,CAAE,UAAAC,MAAM,EAAK;IACvBH,YAAY,GAAG,CAAC,IAAI,EAAEG,MAAM,CAAC;EAC/B,CAAC,CAAC;EAEF,IAAAC,gBAAA,GAA0BZ,KAAK,CAACI,QAAQ,CAACI,YAAY,CAAC;IAAAK,gBAAA,GAAAP,cAAA,CAAAM,gBAAA;IAA/CE,KAAK,GAAAD,gBAAA;IAAEE,QAAQ,GAAAF,gBAAA;EACtB,IAAAG,MAAA,GAAAV,cAAA,CAAmBQ,KAAK;IAAjBG,QAAQ,GAAAD,MAAA;EAEfhB,KAAK,CAACkB,SAAS,CAAC,YAAM;IACpB,IAAIC,SAAS,GAAG,KAAK;IAErB,IAAMC,OAAO;MAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,aAAY;QAC1B,IAAIX,MAAM;QAEV,IAAI;UACFA,MAAM,SAASJ,OAAO;QACxB,CAAC,SAAS;UACR,IAAI,CAACY,SAAS,EAAE;YACdJ,QAAQ,CAAC,CAAC,IAAI,EAAEJ,MAAM,CAAC,CAAC;UAC1B;QACF;MACF,CAAC;MAAA,gBAVKS,OAAOA,CAAA;QAAA,OAAAC,IAAA,CAAAE,KAAA,OAAAC,SAAA;MAAA;IAAA,GAUZ;IAED,IAAI,CAACP,QAAQ,EAAE;MACbG,OAAO,EAAE;IACX;IAEA,OAAO,YAAM;MACXD,SAAS,GAAG,IAAI;IAClB,CAAC;EACH,CAAC,EAAE,CAACZ,OAAO,EAAEU,QAAQ,CAAC,CAAC;EAEvB,OAAOH,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}