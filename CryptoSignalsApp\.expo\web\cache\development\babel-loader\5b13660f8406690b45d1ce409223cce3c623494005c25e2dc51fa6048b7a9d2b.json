{"ast": null, "code": "import \"./fabric/NativeScreensModule\";\nexport * from \"./types\";\nexport { enableScreens, enableFreeze, screensEnabled, freezeEnabled } from \"./core\";\nexport { default as Screen, InnerScreen, ScreenContext } from \"./components/Screen\";\nexport { ScreenStackHeaderConfig, ScreenStackHeaderSubview, ScreenStackHeaderLeftView, ScreenStackHeaderCenterView, ScreenStackHeaderRightView, ScreenStackHeaderBackButtonImage, ScreenStackHeaderSearchBarView } from \"./components/ScreenStackHeaderConfig\";\nexport { default as SearchBar } from \"./components/SearchBar\";\nexport { default as ScreenContainer } from \"./components/ScreenContainer\";\nexport { default as ScreenStack } from \"./components/ScreenStack\";\nexport { default as ScreenStackItem } from \"./components/ScreenStackItem\";\nexport { default as FullWindowOverlay } from \"./components/FullWindowOverlay\";\nexport { default as ScreenFooter } from \"./components/ScreenFooter\";\nexport { default as ScreenContentWrapper } from \"./components/ScreenContentWrapper\";\nexport { isSearchBarAvailableForCurrentPlatform, compatibilityFlags, executeNativeBackPress } from \"./utils\";\nexport { default as useTransitionProgress } from \"./useTransitionProgress\";", "map": {"version": 3, "names": ["enableScreens", "enableFreeze", "screensEnabled", "freezeEnabled", "default", "Screen", "InnerScreen", "ScreenContext", "ScreenStackHeaderConfig", "ScreenStackHeaderSubview", "ScreenStackHeaderLeftView", "ScreenStackHeaderCenterView", "ScreenStackHeaderRightView", "ScreenStackHeaderBackButtonImage", "ScreenStackHeaderSearchBarView", "SearchBar", "ScreenContainer", "ScreenStack", "ScreenStackItem", "FullWindowOverlay", "ScreenFooter", "ScreenContentWrapper", "isSearchBarAvailableForCurrentPlatform", "compatibilityFlags", "executeNativeBackPress", "useTransitionProgress"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\react-native-screens\\src\\index.tsx"], "sourcesContent": ["// Side effects import declaration to ensure our TurboModule\n// is loaded.\nimport './fabric/NativeScreensModule';\n\nexport * from './types';\n\n/**\n * Core\n */\nexport {\n  enableScreens,\n  enableFreeze,\n  screensEnabled,\n  freezeEnabled,\n} from './core';\n\n/**\n * RNS Components\n */\nexport {\n  default as Screen,\n  InnerScreen,\n  ScreenContext,\n} from './components/Screen';\n\nexport {\n  ScreenStackHeaderConfig,\n  ScreenStackHeaderSubview,\n  ScreenStackHeaderLeftView,\n  ScreenStackHeaderCenterView,\n  ScreenStackHeaderRightView,\n  ScreenStackHeaderBackButtonImage,\n  ScreenStackHeaderSearchBarView,\n} from './components/ScreenStackHeaderConfig';\n\nexport { default as SearchBar } from './components/SearchBar';\nexport { default as ScreenContainer } from './components/ScreenContainer';\nexport { default as ScreenStack } from './components/ScreenStack';\nexport { default as ScreenStackItem } from './components/ScreenStackItem';\nexport { default as FullWindowOverlay } from './components/FullWindowOverlay';\nexport { default as ScreenFooter } from './components/ScreenFooter';\nexport { default as ScreenContentWrapper } from './components/ScreenContentWrapper';\n\n/**\n * Utils\n */\nexport {\n  isSearchBarAvailableForCurrentPlatform,\n  compatibilityFlags,\n  executeNativeBackPress,\n} from './utils';\n\n/**\n * Hooks\n */\nexport { default as useTransitionProgress } from './useTransitionProgress';\n"], "mappings": "AAEA;AAEA;AAKA,SACEA,aAAa,EACbC,YAAY,EACZC,cAAc,EACdC,aAAa;AAMf,SACEC,OAAO,IAAIC,MAAM,EACjBC,WAAW,EACXC,aAAa;AAGf,SACEC,uBAAuB,EACvBC,wBAAwB,EACxBC,yBAAyB,EACzBC,2BAA2B,EAC3BC,0BAA0B,EAC1BC,gCAAgC,EAChCC,8BAA8B;AAGhC,SAASV,OAAO,IAAIW,SAAS;AAC7B,SAASX,OAAO,IAAIY,eAAe;AACnC,SAASZ,OAAO,IAAIa,WAAW;AAC/B,SAASb,OAAO,IAAIc,eAAe;AACnC,SAASd,OAAO,IAAIe,iBAAiB;AACrC,SAASf,OAAO,IAAIgB,YAAY;AAChC,SAAShB,OAAO,IAAIiB,oBAAoB;AAKxC,SACEC,sCAAsC,EACtCC,kBAAkB,EAClBC,sBAAsB;AAMxB,SAASpB,OAAO,IAAIqB,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}