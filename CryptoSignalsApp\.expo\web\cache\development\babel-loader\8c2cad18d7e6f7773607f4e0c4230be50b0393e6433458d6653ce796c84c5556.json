{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport { setSecureStoreItem, removeSecureStoreItem } from \"../services/secureStore\";\nvar globalReducer = function globalReducer(state, action) {\n  if (action.type === 'SET_SUBSCRIPTION') {\n    state.subscription = action.payload;\n    setSecureStoreItem('subscription', action.payload);\n    return _objectSpread({}, state);\n  }\n  if (action.type === 'REMOVE_SUBSCRIPTION') {\n    var _payload = {\n      subscriptionStatus: false,\n      lastSubscriptionStatusDate: null,\n      customerId: null,\n      subscriptionPeriodEnd: null,\n      accessToken: null\n    };\n    state.subscription = _payload;\n    removeSecureStoreItem('subscription');\n    return _objectSpread({}, state);\n  }\n  if (action.type === 'SET_SIGNAL_FROM_WEBSOCKET') {\n    state.signalFromWebsocket[action.payload.channel.externalId] = action.payload;\n    state.channelOrderedByNewSignal = action.payload;\n    return _objectSpread({}, state);\n  }\n  return state;\n};\nexport default globalReducer;", "map": {"version": 3, "names": ["setSecureStoreItem", "removeSecureStoreItem", "globalReducer", "state", "action", "type", "subscription", "payload", "_objectSpread", "_payload", "subscriptionStatus", "lastSubscriptionStatusDate", "customerId", "subscriptionPeriodEnd", "accessToken", "signalFromWebsocket", "channel", "externalId", "channelOrderedByNewSignal"], "sources": ["C:/Users/<USER>/Desktop/Signals/CryptoSignalsApp/src/store/reducers.js"], "sourcesContent": ["import { setSecureStoreItem, removeSecureStoreItem } from '../services/secureStore';\r\n\r\nconst globalReducer = (state, action) => {\r\n  if (action.type === 'SET_SUBSCRIPTION') {\r\n    state.subscription = action.payload;\r\n    setSecureStoreItem('subscription', action.payload);\r\n\r\n    return { ...state };\r\n  }\r\n\r\n  if (action.type === 'REMOVE_SUBSCRIPTION') {\r\n    const _payload = {\r\n      subscriptionStatus: false,\r\n      lastSubscriptionStatusDate: null,\r\n      customerId: null,\r\n      subscriptionPeriodEnd: null,\r\n      accessToken: null\r\n    }\r\n    state.subscription = _payload\r\n    removeSecureStoreItem('subscription');\r\n\r\n    return { ...state };\r\n  }\r\n\r\n  if (action.type === 'SET_SIGNAL_FROM_WEBSOCKET') {\r\n    state.signalFromWebsocket[action.payload.channel.externalId] = action.payload;\r\n    state.channelOrderedByNewSignal = action.payload;\r\n    return { ...state };\r\n  }\r\n\r\n  return state;\r\n}\r\n\r\nexport default globalReducer;\r\n"], "mappings": ";;;AAAA,SAASA,kBAAkB,EAAEC,qBAAqB;AAElD,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,KAAK,EAAEC,MAAM,EAAK;EACvC,IAAIA,MAAM,CAACC,IAAI,KAAK,kBAAkB,EAAE;IACtCF,KAAK,CAACG,YAAY,GAAGF,MAAM,CAACG,OAAO;IACnCP,kBAAkB,CAAC,cAAc,EAAEI,MAAM,CAACG,OAAO,CAAC;IAElD,OAAAC,aAAA,KAAYL,KAAK;EACnB;EAEA,IAAIC,MAAM,CAACC,IAAI,KAAK,qBAAqB,EAAE;IACzC,IAAMI,QAAQ,GAAG;MACfC,kBAAkB,EAAE,KAAK;MACzBC,0BAA0B,EAAE,IAAI;MAChCC,UAAU,EAAE,IAAI;MAChBC,qBAAqB,EAAE,IAAI;MAC3BC,WAAW,EAAE;IACf,CAAC;IACDX,KAAK,CAACG,YAAY,GAAGG,QAAQ;IAC7BR,qBAAqB,CAAC,cAAc,CAAC;IAErC,OAAAO,aAAA,KAAYL,KAAK;EACnB;EAEA,IAAIC,MAAM,CAACC,IAAI,KAAK,2BAA2B,EAAE;IAC/CF,KAAK,CAACY,mBAAmB,CAACX,MAAM,CAACG,OAAO,CAACS,OAAO,CAACC,UAAU,CAAC,GAAGb,MAAM,CAACG,OAAO;IAC7EJ,KAAK,CAACe,yBAAyB,GAAGd,MAAM,CAACG,OAAO;IAChD,OAAAC,aAAA,KAAYL,KAAK;EACnB;EAEA,OAAOA,KAAK;AACd,CAAC;AAED,eAAeD,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}