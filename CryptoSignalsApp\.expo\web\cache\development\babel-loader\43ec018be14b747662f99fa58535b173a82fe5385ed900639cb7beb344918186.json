{"ast": null, "code": "import * as React from 'react';\nimport MaterialCommunityIcon from \"../components/MaterialCommunityIcon\";\nexport var SettingsContext = React.createContext({\n  icon: MaterialCommunityIcon,\n  rippleEffectEnabled: true\n});\nvar Provider = SettingsContext.Provider,\n  Consumer = SettingsContext.Consumer;\nexport { Provider, Consumer };", "map": {"version": 3, "names": ["React", "MaterialCommunityIcon", "SettingsContext", "createContext", "icon", "rippleEffectEnabled", "Provider", "Consumer"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\core\\settings.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport MaterialCommunityIcon, {\n  IconProps,\n} from '../components/MaterialCommunityIcon';\n\nexport type Settings = {\n  icon?: ({\n    name,\n    color,\n    size,\n    direction,\n    testID,\n  }: IconProps) => React.ReactNode;\n  rippleEffectEnabled?: boolean;\n};\n\nexport const SettingsContext = React.createContext<Settings>({\n  icon: MaterialCommunityIcon,\n  rippleEffectEnabled: true,\n});\n\nexport const { Provider, Consumer } = SettingsContext;\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,OAAOC,qBAAqB;AAe5B,OAAO,IAAMC,eAAe,GAAGF,KAAK,CAACG,aAAa,CAAW;EAC3DC,IAAI,EAAEH,qBAAqB;EAC3BI,mBAAmB,EAAE;AACvB,CAAC,CAAC;AAEK,IAAQC,QAAQ,GAAeJ,eAAe,CAAtCI,QAAQ;EAAEC,QAAA,GAAaL,eAAe,CAA5BK,QAAA;AAA4B,SAAAD,QAAA,EAAAC,QAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}