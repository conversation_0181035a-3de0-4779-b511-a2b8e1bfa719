import React, { useState, useEffect } from 'react';
import { View, Text, FlatList, TouchableOpacity, TextInput } from 'react-native';

// Importe seus estilos aqui

const ForumPage = () => {
  const [posts, setPosts] = useState([]);
  const [newPostText, setNewPostText] = useState('');

  useEffect(() => {
    // Carregue os posts do fórum da sua fonte de dados (API, banco de dados, etc.)
    // Atualize o estado "posts" com os dados carregados.
  }, []);

  const handleAddPost = () => {
    // Envie o novo post para o servidor (ou sua fonte de dados) e atualize a lista de posts.
    // Limpe o campo de entrada de texto.
  };

  return (
    <View style={styles.container}>
      <Text style={styles.pageTitle}>Fórum</Text>

      <TextInput
        style={styles.input}
        placeholder="Digite sua postagem..."
        value={newPostText}
        onChangeText={(text) => setNewPostText(text)}
      />

      <TouchableOpacity style={styles.addButton} onPress={handleAddPost}>
        <Text style={styles.addButtonText}>Adicionar Postagem</Text>
      </TouchableOpacity>

      <FlatList
        data={posts}
        keyExtractor={(item) => item.id.toString()}
        renderItem={({ item }) => (
          <View style={styles.postContainer}>
            <Text style={styles.postText}>{item.text}</Text>
            <Text style={styles.postAuthor}>Autor: {item.author}</Text>
          </View>
        )}
      />
    </View>
  );
};

export default ForumPage;

// Estilos (use seus próprios estilos aqui)
const styles = {
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#FFFFFF',
  },
  pageTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  input: {
    borderWidth: 1,
    borderColor: '#CCCCCC',
    borderRadius: 4,
    padding: 8,
    marginBottom: 16,
  },
  addButton: {
    backgroundColor: '#3176c4',
    borderRadius: 4,
    padding: 12,
    alignItems: 'center',
  },
  addButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  postContainer: {
    borderWidth: 1,
    borderColor: '#CCCCCC',
    borderRadius: 4,
    padding: 16,
    marginBottom: 16,
  },
  postText: {
    fontSize: 16,
    marginBottom: 8,
  },
  postAuthor: {
    fontSize: 12,
    color: '#888888',
  },
};
