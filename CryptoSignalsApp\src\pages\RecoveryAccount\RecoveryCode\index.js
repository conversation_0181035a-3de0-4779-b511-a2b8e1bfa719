import React, { useState, useRef, useEffect } from 'react';
import { View, StyleSheet, TextInput } from 'react-native';
import { useTheme } from 'react-native-paper';

const RecoveryCodeInput = ({ value, onChangeText, onKeyPress, refInner, autoFocus, onSubmitEditing }) => {
  const theme = useTheme();

  return (
    <View style={styles.inputContainer}>
      <TextInput
        ref={refInner}
        autoFocus={autoFocus ?? false}
        style={[styles.input, { color: '#FFF' }]}
        placeholderTextColor={theme.colors.placeholder}
        value={value}
        maxLength={1}
        keyboardType="number-pad"
        onChangeText={onChangeText}
        onKeyPress={onKeyPress}
      />
    </View>
  );
};

export const RecoveryCode = ({ onCompleted }) => {
  const [inputState, setInputState] = useState({
    input1: {
      previousValue: null,
      currentValue: null
    },
    input2: {
      previousValue: null,
      currentValue: null
    },
    input3: {
      previousValue: null,
      currentValue: null
    },
    input4: {
      previousValue: null,
      currentValue: null
    },
    input5: {
      previousValue: null,
      currentValue: null
    }
  });

  const ref_input1 = useRef();
  const ref_input2 = useRef();
  const ref_input3 = useRef();
  const ref_input4 = useRef();
  const ref_input5 = useRef();

  const handleInputText = (ref, key, newValue) => {
    handleInputState(key, inputState[key].currentValue, newValue);

    if (newValue) {
      ref.current.focus();
    }
  }

  const handleInputState = (key, previousValue, currentValue) => {
    setInputState({...inputState, ...{ [key]: { previousValue, currentValue }}});
  }

  useEffect(() => {
    const code = `${inputState.input1.currentValue}${inputState.input2.currentValue}${inputState.input3.currentValue}${inputState.input4.currentValue}${inputState.input5.currentValue}`

    if (code.length === 5) {
      onCompleted(code)
    }
  }, [inputState])

  return (
    <View style={styles.container}>
      <RecoveryCodeInput
        refInner={ref_input1}
        value={inputState.input1.currentValue}
        autoFocus={true}
        onChangeText={(value) => {
          handleInputText(ref_input2, 'input1', value);

          if (value) {
            ref_input2.current.focus();
          }
        }}
      />

      <RecoveryCodeInput
        refInner={ref_input2}
        value={inputState.input2.currentValue}
        onChangeText={(value) => {
          handleInputText(ref_input3, 'input2', value);

          if (!value) {
            ref_input1.current.focus();
          }
        }}
      />

      <RecoveryCodeInput
        refInner={ref_input3}
        value={inputState.input3.currentValue}
        onChangeText={(value) => {
          handleInputText(ref_input4, 'input3', value)

          if (!value) {
            ref_input2.current.focus();
          }
        }}
      />

      <RecoveryCodeInput
        refInner={ref_input4}
        value={inputState.input4.currentValue}
        onChangeText={(value) => {
          handleInputText(ref_input5, 'input4', value);

          if (!value) {
            ref_input3.current.focus();
          }
        }}
      />

      <RecoveryCodeInput
        refInner={ref_input5}
        value={inputState.input5.currentValue}
        onChangeText={(value) => {
          handleInputText(ref_input5, 'input5', value);

          if (!value) {
            ref_input4.current.focus();
          }
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    margin: 16,
  },
  inputContainer: {
    width: 32,
    height: 48,
    marginHorizontal: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'gray',
  },
  input: {
    height: 48,
    fontSize: 24,
    textAlign: 'center',
    color: '#fff'
  },
});
