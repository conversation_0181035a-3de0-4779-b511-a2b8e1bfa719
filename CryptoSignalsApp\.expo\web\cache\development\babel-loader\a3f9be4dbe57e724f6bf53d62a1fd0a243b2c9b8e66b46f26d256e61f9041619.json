{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport * as React from 'react';\nvar UNINTIALIZED_STATE = {};\nexport default function useSyncState(initialState) {\n  var stateRef = React.useRef(UNINTIALIZED_STATE);\n  var isSchedulingRef = React.useRef(false);\n  var isMountedRef = React.useRef(true);\n  React.useEffect(function () {\n    isMountedRef.current = true;\n    return function () {\n      isMountedRef.current = false;\n    };\n  }, []);\n  if (stateRef.current === UNINTIALIZED_STATE) {\n    stateRef.current = typeof initialState === 'function' ? initialState() : initialState;\n  }\n  var _React$useState = React.useState(stateRef.current),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    trackingState = _React$useState2[0],\n    setTrackingState = _React$useState2[1];\n  var getState = React.useCallback(function () {\n    return stateRef.current;\n  }, []);\n  var setState = React.useCallback(function (state) {\n    if (state === stateRef.current || !isMountedRef.current) {\n      return;\n    }\n    stateRef.current = state;\n    if (!isSchedulingRef.current) {\n      setTrackingState(state);\n    }\n  }, []);\n  var scheduleUpdate = React.useCallback(function (callback) {\n    isSchedulingRef.current = true;\n    try {\n      callback();\n    } finally {\n      isSchedulingRef.current = false;\n    }\n  }, []);\n  var flushUpdates = React.useCallback(function () {\n    if (!isMountedRef.current) {\n      return;\n    }\n    setTrackingState(stateRef.current);\n  }, []);\n  if (trackingState !== stateRef.current) {\n    setTrackingState(stateRef.current);\n  }\n  var state = stateRef.current;\n  React.useDebugValue(state);\n  return [state, getState, setState, scheduleUpdate, flushUpdates];\n}", "map": {"version": 3, "names": ["React", "UNINTIALIZED_STATE", "useSyncState", "initialState", "stateRef", "useRef", "isSchedulingRef", "isMountedRef", "useEffect", "current", "_React$useState", "useState", "_React$useState2", "_slicedToArray", "trackingState", "setTrackingState", "getState", "useCallback", "setState", "state", "scheduleUpdate", "callback", "flushUpdates", "useDebugValue"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\@react-navigation\\core\\src\\useSyncState.tsx"], "sourcesContent": ["import * as React from 'react';\n\nconst UNINTIALIZED_STATE = {};\n\n/**\n * This is definitely not compatible with concurrent mode, but we don't have a solution for sync state yet.\n */\nexport default function useSyncState<T>(initialState?: (() => T) | T) {\n  const stateRef = React.useRef<T>(UNINTIALIZED_STATE as any);\n  const isSchedulingRef = React.useRef(false);\n  const isMountedRef = React.useRef(true);\n\n  React.useEffect(() => {\n    isMountedRef.current = true;\n\n    return () => {\n      isMountedRef.current = false;\n    };\n  }, []);\n\n  if (stateRef.current === UNINTIALIZED_STATE) {\n    stateRef.current =\n      // @ts-expect-error: initialState is a function, but TypeScript doesn't think so\n      typeof initialState === 'function' ? initialState() : initialState;\n  }\n\n  const [trackingState, setTrackingState] = React.useState(stateRef.current);\n\n  const getState = React.useCallback(() => stateRef.current, []);\n\n  const setState = React.useCallback((state: T) => {\n    if (state === stateRef.current || !isMountedRef.current) {\n      return;\n    }\n\n    stateRef.current = state;\n\n    if (!isSchedulingRef.current) {\n      setTrackingState(state);\n    }\n  }, []);\n\n  const scheduleUpdate = React.useCallback((callback: () => void) => {\n    isSchedulingRef.current = true;\n\n    try {\n      callback();\n    } finally {\n      isSchedulingRef.current = false;\n    }\n  }, []);\n\n  const flushUpdates = React.useCallback(() => {\n    if (!isMountedRef.current) {\n      return;\n    }\n\n    // Make sure that the tracking state is up-to-date.\n    // We call it unconditionally, but React should skip the update if state is unchanged.\n    setTrackingState(stateRef.current);\n  }, []);\n\n  // If we're rendering and the tracking state is out of date, update it immediately\n  // This will make sure that our updates are applied as early as possible.\n  if (trackingState !== stateRef.current) {\n    setTrackingState(stateRef.current);\n  }\n\n  const state = stateRef.current;\n\n  React.useDebugValue(state);\n\n  return [state, getState, setState, scheduleUpdate, flushUpdates] as const;\n}\n"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,IAAMC,kBAAkB,GAAG,CAAC,CAAC;AAK7B,eAAe,SAASC,YAAYA,CAAIC,YAA4B,EAAE;EACpE,IAAMC,QAAQ,GAAGJ,KAAK,CAACK,MAAM,CAAIJ,kBAAkB,CAAQ;EAC3D,IAAMK,eAAe,GAAGN,KAAK,CAACK,MAAM,CAAC,KAAK,CAAC;EAC3C,IAAME,YAAY,GAAGP,KAAK,CAACK,MAAM,CAAC,IAAI,CAAC;EAEvCL,KAAK,CAACQ,SAAS,CAAC,YAAM;IACpBD,YAAY,CAACE,OAAO,GAAG,IAAI;IAE3B,OAAO,YAAM;MACXF,YAAY,CAACE,OAAO,GAAG,KAAK;IAC9B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,IAAIL,QAAQ,CAACK,OAAO,KAAKR,kBAAkB,EAAE;IAC3CG,QAAQ,CAACK,OAAO,GAEd,OAAON,YAAY,KAAK,UAAU,GAAGA,YAAY,EAAE,GAAGA,YAAY;EACtE;EAEA,IAAAO,eAAA,GAA0CV,KAAK,CAACW,QAAQ,CAACP,QAAQ,CAACK,OAAO,CAAC;IAAAG,gBAAA,GAAAC,cAAA,CAAAH,eAAA;IAAnEI,aAAa,GAAAF,gBAAA;IAAEG,gBAAgB,GAAAH,gBAAA;EAEtC,IAAMI,QAAQ,GAAGhB,KAAK,CAACiB,WAAW,CAAC;IAAA,OAAMb,QAAQ,CAACK,OAAO;EAAA,GAAE,EAAE,CAAC;EAE9D,IAAMS,QAAQ,GAAGlB,KAAK,CAACiB,WAAW,CAAE,UAAAE,KAAQ,EAAK;IAC/C,IAAIA,KAAK,KAAKf,QAAQ,CAACK,OAAO,IAAI,CAACF,YAAY,CAACE,OAAO,EAAE;MACvD;IACF;IAEAL,QAAQ,CAACK,OAAO,GAAGU,KAAK;IAExB,IAAI,CAACb,eAAe,CAACG,OAAO,EAAE;MAC5BM,gBAAgB,CAACI,KAAK,CAAC;IACzB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,IAAMC,cAAc,GAAGpB,KAAK,CAACiB,WAAW,CAAE,UAAAI,QAAoB,EAAK;IACjEf,eAAe,CAACG,OAAO,GAAG,IAAI;IAE9B,IAAI;MACFY,QAAQ,EAAE;IACZ,CAAC,SAAS;MACRf,eAAe,CAACG,OAAO,GAAG,KAAK;IACjC;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,IAAMa,YAAY,GAAGtB,KAAK,CAACiB,WAAW,CAAC,YAAM;IAC3C,IAAI,CAACV,YAAY,CAACE,OAAO,EAAE;MACzB;IACF;IAIAM,gBAAgB,CAACX,QAAQ,CAACK,OAAO,CAAC;EACpC,CAAC,EAAE,EAAE,CAAC;EAIN,IAAIK,aAAa,KAAKV,QAAQ,CAACK,OAAO,EAAE;IACtCM,gBAAgB,CAACX,QAAQ,CAACK,OAAO,CAAC;EACpC;EAEA,IAAMU,KAAK,GAAGf,QAAQ,CAACK,OAAO;EAE9BT,KAAK,CAACuB,aAAa,CAACJ,KAAK,CAAC;EAE1B,OAAO,CAACA,KAAK,EAAEH,QAAQ,EAAEE,QAAQ,EAAEE,cAAc,EAAEE,YAAY,CAAC;AAClE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}