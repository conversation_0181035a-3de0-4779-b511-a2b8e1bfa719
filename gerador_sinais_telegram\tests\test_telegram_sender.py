import asyncio
import logging
import pytest
import pytest_asyncio
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime, timedelta
from telethon.errors import FloodWaitError
from utils.telegram_sender import TelegramSender

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_telegram_sender.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

@pytest_asyncio.fixture
async def sender():
    """Fixture para criar uma instância do TelegramSender"""
    _sender = TelegramSender()
    yield _sender
    if hasattr(_sender, 'client') and _sender.client:
        await _sender.disconnect()

@pytest.fixture
def test_message():
    """Fixture para mensagem de teste"""
    return "Teste de mensagem"

@pytest.fixture
def test_photo_path():
    """Fixture para caminho da foto de teste"""
    return "test_photo.jpg"

@pytest.mark.asyncio
async def test_connect(sender):
    """Testa a conexão com o Telegram"""
    with patch('utils.telegram_sender.TelegramClient') as mock_client:
        # Configura o mock
        mock_client_instance = AsyncMock()
        mock_client.return_value = mock_client_instance
        mock_client_instance.start = AsyncMock(return_value=True)
        mock_client_instance.get_entity = AsyncMock()
        
        # Executa o teste
        result = await sender.connect()
        
        # Verifica se a conexão foi bem sucedida
        assert result is True
        mock_client_instance.start.assert_called_once()
        mock_client_instance.get_entity.assert_called_once()

@pytest.mark.asyncio
async def test_disconnect(sender):
    """Testa a desconexão do Telegram"""
    with patch('utils.telegram_sender.TelegramClient') as mock_client:
        # Configura o mock
        mock_client_instance = AsyncMock()
        mock_client.return_value = mock_client_instance
        mock_client_instance.disconnect = AsyncMock()
        
        # Configura o cliente no sender
        sender.client = mock_client_instance
        
        # Executa o teste
        await sender.disconnect()
        
        # Verifica se a desconexão foi chamada
        mock_client_instance.disconnect.assert_called_once()

@pytest.mark.asyncio
async def test_is_trading_hours(sender):
    """Testa a verificação de horário de trading"""
    # Testa dentro do horário
    with patch('datetime.datetime') as mock_datetime:
        mock_datetime.now.return_value = datetime(2024, 1, 1, 14, 0)  # 14:00
        assert sender.is_trading_hours() is True
        
    # Testa fora do horário
    with patch('datetime.datetime') as mock_datetime:
        mock_datetime.now.return_value = datetime(2024, 1, 1, 23, 0)  # 23:00
        assert sender.is_trading_hours() is False

@pytest.mark.asyncio
async def test_reset_daily_counter(sender):
    """Testa o reset do contador diário"""
    # Configura data inicial
    sender.current_day = datetime.now().date()
    sender.signals_sent_today = 5
    
    # Simula mudança de dia
    with patch('datetime.datetime') as mock_datetime:
        mock_datetime.now.return_value = datetime.now() + timedelta(days=1)
        sender.reset_daily_counter()
        
    # Verifica se o contador foi resetado
    assert sender.signals_sent_today == 0

@pytest.mark.asyncio
async def test_send_message(sender, test_message):
    """Testa o envio de mensagem"""
    with patch('utils.telegram_sender.TelegramClient') as mock_client:
        # Configura o mock
        mock_client_instance = AsyncMock()
        mock_client.return_value = mock_client_instance
        mock_client_instance.send_message = AsyncMock(return_value=MagicMock(id=123))
        
        # Configura o cliente no sender
        sender.client = mock_client_instance
        sender.group_entity = MagicMock()
        
        # Executa o teste
        result = await sender.send_message(test_message)
        
        # Verifica se a mensagem foi enviada
        assert result == 123
        mock_client_instance.send_message.assert_called_once()

@pytest.mark.asyncio
async def test_send_message_flood_wait(sender, test_message):
    """Testa o tratamento de erro FloodWaitError"""
    with patch('utils.telegram_sender.TelegramClient') as mock_client:
        # Configura o mock para lançar FloodWaitError
        mock_client_instance = AsyncMock()
        mock_client.return_value = mock_client_instance
        mock_client_instance.send_message = AsyncMock(side_effect=FloodWaitError(5))
        
        # Configura o cliente no sender
        sender.client = mock_client_instance
        sender.group_entity = MagicMock()
        
        # Executa o teste
        result = await sender.send_message(test_message)
        
        # Verifica se o erro foi tratado
        assert result is False

@pytest.mark.asyncio
async def test_send_photo(sender, test_photo_path):
    """Testa o envio de foto"""
    with patch('utils.telegram_sender.TelegramClient') as mock_client:
        # Configura o mock
        mock_client_instance = AsyncMock()
        mock_client.return_value = mock_client_instance
        mock_client_instance.send_file = AsyncMock(return_value=True)
        
        # Configura o cliente no sender
        sender.client = mock_client_instance
        sender.group_entity = MagicMock()
        
        # Executa o teste
        result = await sender.send_photo(test_photo_path)
        
        # Verifica se a foto foi enviada
        assert result is True
        mock_client_instance.send_file.assert_called_once()

@pytest.mark.asyncio
async def test_send_reply_message(sender, test_message):
    """Testa o envio de mensagem como resposta"""
    with patch('utils.telegram_sender.TelegramClient') as mock_client:
        # Configura o mock
        mock_client_instance = AsyncMock()
        mock_client.return_value = mock_client_instance
        mock_client_instance.send_message = AsyncMock(return_value=True)
        
        # Configura o cliente no sender
        sender.client = mock_client_instance
        sender.group_entity = MagicMock()
        
        # Executa o teste
        result = await sender.send_reply_message(test_message, reply_to_msg_id=123)
        
        # Verifica se a mensagem foi enviada como resposta
        assert result is True
        mock_client_instance.send_message.assert_called_once()

@pytest.mark.asyncio
async def test_clean_old_message_ids(sender):
    """Testa a limpeza de IDs de mensagens antigas"""
    # Adiciona algumas mensagens antigas
    current_time = datetime.now().timestamp()
    old_time = current_time - 86401  # Mais de 24 horas
    sender.message_ids = {
        f"msg_{old_time}": 1,
        f"msg_{old_time + 1}": 2,
        f"msg_{current_time}": 3  # Mensagem recente
    }
    
    # Executa a limpeza
    sender._clean_old_message_ids()
    
    # Verifica se apenas a mensagem recente permanece
    assert len(sender.message_ids) == 1
    assert f"msg_{current_time}" in sender.message_ids 