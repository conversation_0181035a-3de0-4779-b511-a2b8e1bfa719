2025-04-13 11:09:37 - ERROR - Serviço gerador_sinais não está ativo! Status: activating
2025-04-13 11:09:47 - INFO - Serviço sistema-clientes está rodando normalmente
2025-04-13 11:09:47 - INFO - Uso de recursos de sistema-clientes: %CPU %MEM
 0.0  3.9
2025-04-13 11:13:30 - ERROR - Serviço gerador_sinais não está ativo! Status: activating
Detalhes do serviço:
● gerador_sinais.service - Gerador de Sinais Telegram
     Loaded: loaded (/etc/systemd/system/gerador_sinais.service; enabled; preset: enabled)
     Active: activating (auto-restart) (Result: exit-code) since Sun 2025-04-13 11:13:14 -03; 9s ago
    Process: 1734750 ExecStart=/root/gerador_sinais_telegram/venv/bin/python main.py (code=exited, status=203/EXEC)
   Main PID: 1734750 (code=exited, status=203/EXEC)
        CPU: 2ms

Apr 13 11:13:24 ubuntu systemd[1]: gerador_sinais.service: Scheduled restart job, restart counter is at 101424.
Apr 13 11:13:24 ubuntu systemd[1]: Started gerador_sinais.service - Gerador de Sinais Telegram.
Apr 13 11:13:24 ubuntu systemd[1]: gerador_sinais.service: Main process exited, code=exited, status=203/EXEC
Apr 13 11:13:24 ubuntu systemd[1]: gerador_sinais.service: Failed with result 'exit-code'.
2025-04-13 11:13:39 - INFO - Serviço sistema-clientes está rodando normalmente
2025-04-13 11:13:39 - INFO - Uso de recursos de sistema-clientes: %CPU %MEM
 0.0  3.9
2025-04-13 11:16:41 - ERROR - Serviço gerador_sinais não está ativo! Status: activating
Detalhes do serviço:
● gerador_sinais.service - Gerador de Sinais Telegram
     Loaded: loaded (/etc/systemd/system/gerador_sinais.service; enabled; preset: enabled)
     Active: active (running) since Sun 2025-04-13 11:16:34 -03; 1s ago
   Main PID: 1735274 (python)
      Tasks: 3 (limit: 4581)
     Memory: 121.9M (peak: 122.0M)
        CPU: 1.095s
     CGroup: /system.slice/gerador_sinais.service
             └─1735274 /opt/gerador_sinais_telegram/venv/bin/python main.py

Apr 13 11:16:24 ubuntu systemd[1]: gerador_sinais.service: Consumed 2.187s CPU time.
Apr 13 11:16:34 ubuntu systemd[1]: gerador_sinais.service: Scheduled restart job, restart counter is at 101440.
Apr 13 11:16:34 ubuntu systemd[1]: Started gerador_sinais.service - Gerador de Sinais Telegram.
Apr 13 11:16:38 ubuntu python[1735274]: 2025-04-13 11:16:38,508 - __main__ - INFO - Iniciando gerador de sinais...
Apr 13 11:16:38 ubuntu python[1735274]: 2025-04-13 11:16:38,527 - telethon.network.mtprotosender - INFO - Connecting to **************:443/TcpFull...
Apr 13 11:16:38 ubuntu python[1735274]: 2025-04-13 11:16:38,573 - telethon.network.mtprotosender - INFO - Connection to **************:443/TcpFull complete!
Apr 13 11:16:38 ubuntu python[1735274]: 2025-04-13 11:16:38,862 - utils.telegram_sender - INFO - Cliente Telegram conectado com sucesso
Apr 13 11:16:38 ubuntu python[1735274]: 2025-04-13 11:16:38,927 - utils.telegram_sender - INFO - Entidade do grupo obtida: Crypto Signals VIPs 💸
Apr 13 11:16:39 ubuntu python[1735274]: 2025-04-13 11:16:39,370 - estrategias.breakout_volume - INFO - ADAUSDT - Anomalia de volume detectada! Volume recente: 103816.41680403444, Máximo diário: 36167.14436647269
Apr 13 11:16:39 ubuntu python[1735274]: 2025-04-13 11:16:39,380 - estrategias.breakout_volume - INFO - ADAUSDT - Consolidação detectada: Suporte = 197.77620050520554, Resistência = 199.30462278816256
2025-04-13 11:16:50 - INFO - Serviço sistema-clientes está rodando normalmente
2025-04-13 11:16:50 - INFO - Uso de recursos de sistema-clientes: %CPU %MEM
 0.0  3.9
2025-04-13 11:28:51 - ERROR - Serviço gerador_sinais não está ativo! Status: activating
Detalhes do serviço:
● gerador_sinais.service - Gerador de Sinais Telegram
     Loaded: loaded (/etc/systemd/system/gerador_sinais.service; enabled; preset: enabled)
     Active: active (running) since Sun 2025-04-13 11:28:41 -03; 3s ago
   Main PID: 1736276 (python)
      Tasks: 3 (limit: 4581)
     Memory: 190.2M (peak: 190.2M)
        CPU: 2.500s
     CGroup: /system.slice/gerador_sinais.service
             └─1736276 /opt/gerador_sinais_telegram/venv/bin/python main.py

Apr 13 11:28:31 ubuntu systemd[1]: gerador_sinais.service: Consumed 2.085s CPU time.
Apr 13 11:28:41 ubuntu systemd[1]: gerador_sinais.service: Scheduled restart job, restart counter is at 101496.
Apr 13 11:28:41 ubuntu systemd[1]: Started gerador_sinais.service - Gerador de Sinais Telegram.
Apr 13 11:28:45 ubuntu python[1736276]: 2025-04-13 11:28:45,656 - __main__ - INFO - Iniciando gerador de sinais...
Apr 13 11:28:45 ubuntu python[1736276]: 2025-04-13 11:28:45,659 - telethon.network.mtprotosender - INFO - Connecting to **************:443/TcpFull...
Apr 13 11:28:45 ubuntu python[1736276]: 2025-04-13 11:28:45,703 - telethon.network.mtprotosender - INFO - Connection to **************:443/TcpFull complete!
Apr 13 11:28:45 ubuntu python[1736276]: 2025-04-13 11:28:45,995 - utils.telegram_sender - INFO - Cliente Telegram conectado com sucesso
Apr 13 11:28:46 ubuntu python[1736276]: 2025-04-13 11:28:46,047 - utils.telegram_sender - INFO - Entidade do grupo obtida: Crypto Signals VIPs 💸
Apr 13 11:28:46 ubuntu python[1736276]: 2025-04-13 11:28:46,456 - estrategias.breakout_volume - INFO - BNBUSDT - Anomalia de volume detectada! Volume recente: 131511.21697201452, Máximo diário: 26960.285982654055
Apr 13 11:28:46 ubuntu python[1736276]: 2025-04-13 11:28:46,462 - estrategias.breakout_volume - INFO - BNBUSDT - Consolidação detectada: Suporte = 193.68901310922132, Resistência = 194.37596924377144
2025-04-13 11:29:02 - INFO - Serviço sistema-clientes está rodando normalmente
2025-04-13 11:29:02 - INFO - Uso de recursos de sistema-clientes: %CPU %MEM
 0.0  3.9
2025-04-13 11:30:32 - ERROR - Serviço gerador_sinais não está ativo! Status: activating
Detalhes do serviço:
● gerador_sinais.service - Gerador de Sinais Telegram
     Loaded: loaded (/etc/systemd/system/gerador_sinais.service; enabled; preset: enabled)
     Active: activating (auto-restart) since Sun 2025-04-13 11:30:22 -03; 4s ago
    Process: 1736474 ExecStart=/opt/gerador_sinais_telegram/venv/bin/python main.py (code=exited, status=0/SUCCESS)
   Main PID: 1736474 (code=exited, status=0/SUCCESS)
        CPU: 2.652s

Apr 13 11:30:22 ubuntu systemd[1]: gerador_sinais.service: Consumed 2.652s CPU time, 30.2M memory peak, 0B memory swap peak.
2025-04-13 11:30:43 - INFO - Serviço sistema-clientes está rodando normalmente
2025-04-13 11:30:43 - INFO - Uso de recursos de sistema-clientes: %CPU %MEM
 0.0  3.9
2025-04-13 11:34:41 - WARNING - Mudança de status em gerador_sinais: activating -> active
2025-04-13 11:34:41 - INFO - Serviço gerador_sinais está rodando normalmente
2025-04-13 11:34:41 - INFO - Uso de recursos de gerador_sinais: 
2025-04-13 11:34:50 - INFO - Serviço sistema-clientes está rodando normalmente
2025-04-13 11:34:50 - INFO - Uso de recursos de sistema-clientes: %CPU %MEM
 0.0  3.9
