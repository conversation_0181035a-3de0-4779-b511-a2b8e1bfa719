{"ast": null, "code": "export default function debounce(func, duration) {\n  var timeout;\n  return function () {\n    if (!timeout) {\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      func.apply(this, args);\n      timeout = setTimeout(function () {\n        timeout = undefined;\n      }, duration);\n    }\n  };\n}", "map": {"version": 3, "names": ["debounce", "func", "duration", "timeout", "_len", "arguments", "length", "args", "Array", "_key", "apply", "setTimeout", "undefined"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\@react-navigation\\stack\\src\\utils\\debounce.tsx"], "sourcesContent": ["export default function debounce<T extends (...args: any[]) => void>(\n  func: T,\n  duration: number\n): T {\n  let timeout: NodeJS.Timeout | number | undefined;\n\n  return function (this: any, ...args) {\n    if (!timeout) {\n      // eslint-disable-next-line babel/no-invalid-this\n      func.apply(this, args);\n\n      timeout = setTimeout(() => {\n        timeout = undefined;\n      }, duration);\n    }\n  } as T;\n}\n"], "mappings": "AAAA,eAAe,SAASA,QAAQA,CAC9BC,IAAO,EACPC,QAAgB,EACb;EACH,IAAIC,OAA4C;EAEhD,OAAO,YAA8B;IACnC,IAAI,CAACA,OAAO,EAAE;MAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EADeC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;QAAJF,IAAI,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;MAAA;MAG/BR,IAAI,CAACS,KAAK,CAAC,IAAI,EAAEH,IAAI,CAAC;MAEtBJ,OAAO,GAAGQ,UAAU,CAAC,YAAM;QACzBR,OAAO,GAAGS,SAAS;MACrB,CAAC,EAAEV,QAAQ,CAAC;IACd;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}