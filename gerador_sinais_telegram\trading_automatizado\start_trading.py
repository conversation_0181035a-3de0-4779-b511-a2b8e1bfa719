import os
import sys
import asyncio
import logging
import argparse

# Importando configurações e utilitários do projeto principal
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from trading_automatizado.telegram_trader import TelegramTrader
from trading_automatizado.manual_trader import ManualTrader

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("trading_automatizado/start_trading.log"),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

async def iniciar_telegram_trader(capital_por_operacao, modo_simulacao, capital_total=None):
    """Inicia o trader baseado em sinais do Telegram"""
    if capital_total:
        logger.info(f"Iniciando TelegramTrader com capital total de ${capital_total}")
        logger.info(f"Capital por operação: ${capital_total * 0.1} (10% do capital total)")
    else:
        logger.info(f"Iniciando TelegramTrader com capital de ${capital_por_operacao} por operação")
    
    logger.info(f"Modo de simulação: {'ATIVADO' if modo_simulacao else 'DESATIVADO'}")
    
    trader = TelegramTrader(
        capital_por_operacao=capital_por_operacao, 
        modo_simulacao=modo_simulacao,
        capital_total=capital_total
    )
    await trader.iniciar()

async def iniciar_manual_trader(args):
    """Inicia o trader manual com os argumentos fornecidos"""
    if args.capital_total:
        logger.info(f"Iniciando ManualTrader com capital total de ${args.capital_total}")
        logger.info(f"Capital por operação: ${args.capital_total * 0.1} (10% do capital total)")
    else:
        logger.info(f"Iniciando ManualTrader com capital de ${args.capital} por operação")
    
    logger.info(f"Modo de simulação: {'DESATIVADO' if args.real else 'ATIVADO'}")
    logger.info(f"Estratégia: {args.estrategia}")
    
    trader = ManualTrader(
        capital_por_operacao=args.capital, 
        modo_simulacao=not args.real,
        estrategia=args.estrategia,
        capital_total=args.capital_total
    )
    
    if args.symbol and args.tipo and args.preco:
        logger.info(f"Executando operação manual para {args.symbol}: {args.tipo} em {args.preco}")
        
        resultado = await trader.executar_operacao(
            symbol=args.symbol,
            tipo_sinal=args.tipo,
            preco_entrada=args.preco,
            stop_loss=args.sl,
            take_profit=args.tp
        )
        
        if resultado:
            logger.info(f"Operação executada com sucesso")
            await trader.iniciar_monitoramento()
        else:
            logger.error("Falha ao executar operação")
    else:
        logger.error("Parâmetros insuficientes para operação manual")

async def main():
    """Função principal"""
    parser = argparse.ArgumentParser(description='Sistema de Trading Automatizado')
    
    # Argumentos gerais
    parser.add_argument('--modo', type=str, choices=['telegram', 'manual'], default='telegram',
                        help='Modo de operação: telegram ou manual (padrão: telegram)')
    parser.add_argument('--capital', type=float, default=20,
                        help='Capital por operação em USD (padrão: 20)')
    parser.add_argument('--capital_total', type=float, 
                        help='Capital total disponível para trading (se fornecido, usa 10% por operação)')
    parser.add_argument('--real', action='store_true',
                        help='Modo real (sem simulação)')
    parser.add_argument('--estrategia', type=str, choices=['scalp', 'swing'], default='swing',
                        help='Estratégia de trading (padrão: swing)')
    
    # Argumentos para o modo manual
    parser.add_argument('--symbol', type=str, help='Par de trading (ex: BTCUSDT)')
    parser.add_argument('--tipo', type=str, choices=['LONG', 'SHORT'], help='Tipo de operação')
    parser.add_argument('--preco', type=float, help='Preço de entrada')
    parser.add_argument('--sl', type=float, help='Stop Loss (opcional)')
    parser.add_argument('--tp', type=float, help='Take Profit (opcional)')
    
    args = parser.parse_args()
    
    # Verifica o modo de operação
    if args.modo == 'telegram':
        await iniciar_telegram_trader(args.capital, not args.real, args.capital_total)
    else:  # manual
        await iniciar_manual_trader(args)

if __name__ == "__main__":
    asyncio.run(main()) 