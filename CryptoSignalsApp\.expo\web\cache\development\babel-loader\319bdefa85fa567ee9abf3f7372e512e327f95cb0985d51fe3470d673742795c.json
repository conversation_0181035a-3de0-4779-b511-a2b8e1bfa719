{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport { typescale } from \"./themes/v3/tokens\";\nexport var fontConfig = {\n  web: {\n    regular: {\n      fontFamily: 'Roboto, \"Helvetica Neue\", Helvetica, Arial, sans-serif',\n      fontWeight: '400'\n    },\n    medium: {\n      fontFamily: 'Roboto, \"Helvetica Neue\", Helvetica, Arial, sans-serif',\n      fontWeight: '500'\n    },\n    light: {\n      fontFamily: 'Roboto, \"Helvetica Neue\", Helvetica, Arial, sans-serif',\n      fontWeight: '300'\n    },\n    thin: {\n      fontFamily: 'Roboto, \"Helvetica Neue\", Helvetica, Arial, sans-serif',\n      fontWeight: '100'\n    }\n  },\n  ios: {\n    regular: {\n      fontFamily: 'System',\n      fontWeight: '400'\n    },\n    medium: {\n      fontFamily: 'System',\n      fontWeight: '500'\n    },\n    light: {\n      fontFamily: 'System',\n      fontWeight: '300'\n    },\n    thin: {\n      fontFamily: 'System',\n      fontWeight: '100'\n    }\n  },\n  default: {\n    regular: {\n      fontFamily: 'sans-serif',\n      fontWeight: 'normal'\n    },\n    medium: {\n      fontFamily: 'sans-serif-medium',\n      fontWeight: 'normal'\n    },\n    light: {\n      fontFamily: 'sans-serif-light',\n      fontWeight: 'normal'\n    },\n    thin: {\n      fontFamily: 'sans-serif-thin',\n      fontWeight: 'normal'\n    }\n  }\n};\nfunction configureV2Fonts(config) {\n  var fonts = Platform.select(_objectSpread(_objectSpread({}, fontConfig), config));\n  return fonts;\n}\nfunction configureV3Fonts(config) {\n  if (!config) {\n    return typescale;\n  }\n  var isFlatConfig = Object.keys(config).every(function (key) {\n    return typeof config[key] !== 'object';\n  });\n  if (isFlatConfig) {\n    return Object.fromEntries(Object.entries(typescale).map(function (_ref) {\n      var _ref2 = _slicedToArray(_ref, 2),\n        variantName = _ref2[0],\n        variantProperties = _ref2[1];\n      return [variantName, _objectSpread(_objectSpread({}, variantProperties), config)];\n    }));\n  }\n  return Object.assign.apply(Object, [{}, typescale].concat(_toConsumableArray(Object.entries(config).map(function (_ref3) {\n    var _ref4 = _slicedToArray(_ref3, 2),\n      variantName = _ref4[0],\n      variantProperties = _ref4[1];\n    return _defineProperty({}, variantName, _objectSpread(_objectSpread({}, typescale[variantName]), variantProperties));\n  }))));\n}\nexport default function configureFonts(params) {\n  var _ref6 = params || {},\n    _ref6$isV = _ref6.isV3,\n    isV3 = _ref6$isV === void 0 ? true : _ref6$isV,\n    config = _ref6.config;\n  if (isV3) {\n    return configureV3Fonts(config);\n  }\n  return configureV2Fonts(config);\n}", "map": {"version": 3, "names": ["typescale", "fontConfig", "web", "regular", "fontFamily", "fontWeight", "medium", "light", "thin", "ios", "default", "configureV2Fonts", "config", "fonts", "Platform", "select", "_objectSpread", "configureV3Fonts", "isFlatConfig", "Object", "keys", "every", "key", "fromEntries", "entries", "map", "_ref", "_ref2", "_slicedToArray", "variantName", "variantProperties", "assign", "apply", "concat", "_toConsumableArray", "_ref3", "_ref4", "_defineProperty", "configure<PERSON>onts", "params", "_ref6", "_ref6$isV", "isV3"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\styles\\fonts.tsx"], "sourcesContent": ["import { Platform, PlatformOSType } from 'react-native';\n\nimport type { Fonts, MD3Type, MD3Typescale, MD3TypescaleKey } from '../types';\nimport { typescale } from './themes/v3/tokens';\n\nexport const fontConfig = {\n  web: {\n    regular: {\n      fontFamily: 'Roboto, \"Helvetica Neue\", Helvetica, Arial, sans-serif',\n      fontWeight: '400' as '400',\n    },\n    medium: {\n      fontFamily: 'Roboto, \"Helvetica Neue\", Helvetica, Arial, sans-serif',\n      fontWeight: '500' as '500',\n    },\n    light: {\n      fontFamily: 'Roboto, \"Helvetica Neue\", Helvetica, Arial, sans-serif',\n      fontWeight: '300' as '300',\n    },\n    thin: {\n      fontFamily: 'Roboto, \"Helvetica Neue\", Helvetica, Arial, sans-serif',\n      fontWeight: '100' as '100',\n    },\n  },\n  ios: {\n    regular: {\n      fontFamily: 'System',\n      fontWeight: '400' as '400',\n    },\n    medium: {\n      fontFamily: 'System',\n      fontWeight: '500' as '500',\n    },\n    light: {\n      fontFamily: 'System',\n      fontWeight: '300' as '300',\n    },\n    thin: {\n      fontFamily: 'System',\n      fontWeight: '100' as '100',\n    },\n  },\n  default: {\n    regular: {\n      fontFamily: 'sans-serif',\n      fontWeight: 'normal' as 'normal',\n    },\n    medium: {\n      fontFamily: 'sans-serif-medium',\n      fontWeight: 'normal' as 'normal',\n    },\n    light: {\n      fontFamily: 'sans-serif-light',\n      fontWeight: 'normal' as 'normal',\n    },\n    thin: {\n      fontFamily: 'sans-serif-thin',\n      fontWeight: 'normal' as 'normal',\n    },\n  },\n};\n\ntype MD2FontsConfig = {\n  [platform in PlatformOSType | 'default']?: Fonts;\n};\n\ntype MD3FontsConfig =\n  | {\n      [key in MD3TypescaleKey]: Partial<MD3Type>;\n    }\n  | {\n      [key: string]: MD3Type;\n    }\n  | Partial<MD3Type>;\n\nfunction configureV2Fonts(config: MD2FontsConfig): Fonts {\n  const fonts = Platform.select({ ...fontConfig, ...config }) as Fonts;\n  return fonts;\n}\n\nfunction configureV3Fonts(\n  config: MD3FontsConfig\n): MD3Typescale | (MD3Typescale & { [key: string]: MD3Type }) {\n  if (!config) {\n    return typescale;\n  }\n\n  const isFlatConfig = Object.keys(config).every(\n    (key) => typeof config[key as keyof typeof config] !== 'object'\n  );\n\n  if (isFlatConfig) {\n    return Object.fromEntries(\n      Object.entries(typescale).map(([variantName, variantProperties]) => [\n        variantName,\n        { ...variantProperties, ...config },\n      ])\n    ) as MD3Typescale;\n  }\n\n  return Object.assign(\n    {},\n    typescale,\n    ...Object.entries(config).map(([variantName, variantProperties]) => ({\n      [variantName]: {\n        ...typescale[variantName as MD3TypescaleKey],\n        ...variantProperties,\n      },\n    }))\n  );\n}\n\n// eslint-disable-next-line no-redeclare\nexport default function configureFonts(params: { isV3: false }): Fonts;\n// eslint-disable-next-line no-redeclare\nexport default function configureFonts(params: {\n  config?: MD2FontsConfig;\n  isV3: false;\n}): Fonts;\n// eslint-disable-next-line no-redeclare\nexport default function configureFonts(params?: {\n  config?: Partial<MD3Type>;\n  isV3?: true;\n}): MD3Typescale;\n// eslint-disable-next-line no-redeclare\nexport default function configureFonts(params?: {\n  config?: Partial<Record<MD3TypescaleKey, Partial<MD3Type>>>;\n  isV3?: true;\n}): MD3Typescale;\n// eslint-disable-next-line no-redeclare\nexport default function configureFonts(params: {\n  config: Record<string, MD3Type>;\n  isV3?: true;\n}): MD3Typescale & { [key: string]: MD3Type };\n// eslint-disable-next-line no-redeclare\nexport default function configureFonts(params?: any) {\n  const { isV3 = true, config } = params || {};\n\n  if (isV3) {\n    return configureV3Fonts(config);\n  }\n  return configureV2Fonts(config);\n}\n"], "mappings": ";;;;;;AAGA,SAASA,SAAS;AAElB,OAAO,IAAMC,UAAU,GAAG;EACxBC,GAAG,EAAE;IACHC,OAAO,EAAE;MACPC,UAAU,EAAE,wDAAwD;MACpEC,UAAU,EAAE;IACd,CAAC;IACDC,MAAM,EAAE;MACNF,UAAU,EAAE,wDAAwD;MACpEC,UAAU,EAAE;IACd,CAAC;IACDE,KAAK,EAAE;MACLH,UAAU,EAAE,wDAAwD;MACpEC,UAAU,EAAE;IACd,CAAC;IACDG,IAAI,EAAE;MACJJ,UAAU,EAAE,wDAAwD;MACpEC,UAAU,EAAE;IACd;EACF,CAAC;EACDI,GAAG,EAAE;IACHN,OAAO,EAAE;MACPC,UAAU,EAAE,QAAQ;MACpBC,UAAU,EAAE;IACd,CAAC;IACDC,MAAM,EAAE;MACNF,UAAU,EAAE,QAAQ;MACpBC,UAAU,EAAE;IACd,CAAC;IACDE,KAAK,EAAE;MACLH,UAAU,EAAE,QAAQ;MACpBC,UAAU,EAAE;IACd,CAAC;IACDG,IAAI,EAAE;MACJJ,UAAU,EAAE,QAAQ;MACpBC,UAAU,EAAE;IACd;EACF,CAAC;EACDK,OAAO,EAAE;IACPP,OAAO,EAAE;MACPC,UAAU,EAAE,YAAY;MACxBC,UAAU,EAAE;IACd,CAAC;IACDC,MAAM,EAAE;MACNF,UAAU,EAAE,mBAAmB;MAC/BC,UAAU,EAAE;IACd,CAAC;IACDE,KAAK,EAAE;MACLH,UAAU,EAAE,kBAAkB;MAC9BC,UAAU,EAAE;IACd,CAAC;IACDG,IAAI,EAAE;MACJJ,UAAU,EAAE,iBAAiB;MAC7BC,UAAU,EAAE;IACd;EACF;AACF,CAAC;AAeD,SAASM,gBAAgBA,CAACC,MAAsB,EAAS;EACvD,IAAMC,KAAK,GAAGC,QAAQ,CAACC,MAAM,CAAAC,aAAA,CAAAA,aAAA,KAAMf,UAAU,GAAKW,MAAA,CAAQ,CAAU;EACpE,OAAOC,KAAK;AACd;AAEA,SAASI,gBAAgBA,CACvBL,MAAsB,EACsC;EAC5D,IAAI,CAACA,MAAM,EAAE;IACX,OAAOZ,SAAS;EAClB;EAEA,IAAMkB,YAAY,GAAGC,MAAM,CAACC,IAAI,CAACR,MAAM,CAAC,CAACS,KAAK,CAC3C,UAAAC,GAAG;IAAA,OAAK,OAAOV,MAAM,CAACU,GAAG,CAAwB,KAAK,QACzD;EAAA,EAAC;EAED,IAAIJ,YAAY,EAAE;IAChB,OAAOC,MAAM,CAACI,WAAW,CACvBJ,MAAM,CAACK,OAAO,CAACxB,SAAS,CAAC,CAACyB,GAAG,CAAC,UAAAC,IAAA;MAAA,IAAAC,KAAA,GAAAC,cAAA,CAAAF,IAAA;QAAEG,WAAW,GAAAF,KAAA;QAAEG,iBAAiB,GAAAH,KAAA;MAAA,OAAM,CAClEE,WAAW,EAAAb,aAAA,CAAAA,aAAA,KACNc,iBAAiB,GAAKlB,MAAA,EAC5B;IAAA,EACH,CAAC;EACH;EAEA,OAAOO,MAAM,CAACY,MAAM,CAAAC,KAAA,CAAbb,MAAM,GACX,CAAC,CAAC,EACFnB,SAAS,EAAAiC,MAAA,CAAAC,kBAAA,CACNf,MAAM,CAACK,OAAO,CAACZ,MAAM,CAAC,CAACa,GAAG,CAAC,UAAAU,KAAA;IAAA,IAAAC,KAAA,GAAAR,cAAA,CAAAO,KAAA;MAAEN,WAAW,GAAAO,KAAA;MAAEN,iBAAiB,GAAAM,KAAA;IAAA,OAAAC,eAAA,KAC3DR,WAAW,EAAAb,aAAA,CAAAA,aAAA,KACPhB,SAAS,CAAC6B,WAAW,CAAoB,GACzCC,iBAAA;EAAA,CAEL,CACJ,GAAC;AACH;AAyBA,eAAe,SAASQ,cAAcA,CAACC,MAAY,EAAE;EACnD,IAAAC,KAAA,GAAgCD,MAAM,IAAI,CAAC,CAAC;IAAAE,SAAA,GAAAD,KAAA,CAApCE,IAAI;IAAJA,IAAI,GAAAD,SAAA,cAAG,IAAI,GAAAA,SAAA;IAAE7B,MAAA,GAAA4B,KAAA,CAAA5B,MAAA;EAErB,IAAI8B,IAAI,EAAE;IACR,OAAOzB,gBAAgB,CAACL,MAAM,CAAC;EACjC;EACA,OAAOD,gBAAgB,CAACC,MAAM,CAAC;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}