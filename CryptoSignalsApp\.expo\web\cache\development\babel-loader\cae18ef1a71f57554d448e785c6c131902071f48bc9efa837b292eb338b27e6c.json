{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"leftInset\", \"horizontalInset\", \"style\", \"theme\", \"bold\"];\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nimport * as React from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport color from 'color';\nimport { useInternalTheme } from \"../core/theming\";\nimport { black, white } from \"../styles/themes/v2/colors\";\nvar Divider = function Divider(_ref) {\n  var leftInset = _ref.leftInset,\n    _ref$horizontalInset = _ref.horizontalInset,\n    horizontalInset = _ref$horizontalInset === void 0 ? false : _ref$horizontalInset,\n    style = _ref.style,\n    themeOverrides = _ref.theme,\n    _ref$bold = _ref.bold,\n    bold = _ref$bold === void 0 ? false : _ref$bold,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var theme = useInternalTheme(themeOverrides);\n  var isDarkTheme = theme.dark,\n    isV3 = theme.isV3;\n  var dividerColor = isV3 ? theme.colors.outlineVariant : color(isDarkTheme ? white : black).alpha(0.12).rgb().string();\n  return React.createElement(View, _extends({}, rest, {\n    style: [{\n      height: StyleSheet.hairlineWidth,\n      backgroundColor: dividerColor\n    }, leftInset && (isV3 ? styles.v3LeftInset : styles.leftInset), isV3 && horizontalInset && styles.horizontalInset, isV3 && bold && styles.bold, style]\n  }));\n};\nvar styles = StyleSheet.create({\n  leftInset: {\n    marginLeft: 72\n  },\n  v3LeftInset: {\n    marginLeft: 16\n  },\n  horizontalInset: {\n    marginLeft: 16,\n    marginRight: 16\n  },\n  bold: {\n    height: 1\n  }\n});\nexport default Divider;", "map": {"version": 3, "names": ["React", "StyleSheet", "View", "color", "useInternalTheme", "black", "white", "Divider", "_ref", "leftInset", "_ref$horizontalInset", "horizontalInset", "style", "themeOverrides", "theme", "_ref$bold", "bold", "rest", "_objectWithoutProperties", "_excluded", "isDarkTheme", "dark", "isV3", "dividerColor", "colors", "outlineVariant", "alpha", "rgb", "string", "createElement", "_extends", "height", "hairlineWidth", "backgroundColor", "styles", "v3LeftInset", "create", "marginLeft", "marginRight"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\Divider.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { StyleProp, StyleSheet, View, ViewStyle } from 'react-native';\n\nimport color from 'color';\n\nimport { useInternalTheme } from '../core/theming';\nimport { black, white } from '../styles/themes/v2/colors';\nimport type { $RemoveChildren, ThemeProp } from '../types';\n\nexport type Props = $RemoveChildren<typeof View> & {\n  /**\n   * @renamed Renamed from 'inset' to 'leftInset` in v5.x\n   * Whether divider has a left inset.\n   */\n  leftInset?: boolean;\n  /**\n   * @supported Available in v5.x with theme version 3\n   *  Whether divider has a horizontal inset on both sides.\n   */\n  horizontalInset?: boolean;\n  /**\n   * @supported Available in v5.x with theme version 3\n   *  Whether divider should be bolded.\n   */\n  bold?: boolean;\n  style?: StyleProp<ViewStyle>;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n};\n\n/**\n * A divider is a thin, lightweight separator that groups content in lists and page layouts.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { View } from 'react-native';\n * import { Divider, Text } from 'react-native-paper';\n *\n * const MyComponent = () => (\n *   <View>\n *     <Text>Lemon</Text>\n *     <Divider />\n *     <Text>Mango</Text>\n *     <Divider />\n *   </View>\n * );\n *\n * export default MyComponent;\n * ```\n */\nconst Divider = ({\n  leftInset,\n  horizontalInset = false,\n  style,\n  theme: themeOverrides,\n  bold = false,\n  ...rest\n}: Props) => {\n  const theme = useInternalTheme(themeOverrides);\n  const { dark: isDarkTheme, isV3 } = theme;\n\n  const dividerColor = isV3\n    ? theme.colors.outlineVariant\n    : color(isDarkTheme ? white : black)\n        .alpha(0.12)\n        .rgb()\n        .string();\n\n  return (\n    <View\n      {...rest}\n      style={[\n        { height: StyleSheet.hairlineWidth, backgroundColor: dividerColor },\n        leftInset && (isV3 ? styles.v3LeftInset : styles.leftInset),\n        isV3 && horizontalInset && styles.horizontalInset,\n        isV3 && bold && styles.bold,\n        style,\n      ]}\n    />\n  );\n};\n\nconst styles = StyleSheet.create({\n  leftInset: {\n    marginLeft: 72,\n  },\n  v3LeftInset: {\n    marginLeft: 16,\n  },\n  horizontalInset: {\n    marginLeft: 16,\n    marginRight: 16,\n  },\n  bold: {\n    height: 1,\n  },\n});\n\nexport default Divider;\n"], "mappings": ";;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAG9B,OAAOC,KAAK,MAAM,OAAO;AAEzB,SAASC,gBAAgB;AACzB,SAASC,KAAK,EAAEC,KAAK;AA+CrB,IAAMC,OAAO,GAAG,SAAVA,OAAOA,CAAAC,IAAA,EAOA;EAAA,IANXC,SAAS,GAAAD,IAAA,CAATC,SAAS;IAAAC,oBAAA,GAAAF,IAAA,CACTG,eAAe;IAAfA,eAAe,GAAAD,oBAAA,cAAG,KAAK,GAAAA,oBAAA;IACvBE,KAAK,GAAAJ,IAAA,CAALI,KAAK;IACEC,cAAc,GAAAL,IAAA,CAArBM,KAAK;IAAAC,SAAA,GAAAP,IAAA,CACLQ,IAAI;IAAJA,IAAI,GAAAD,SAAA,cAAG,KAAK,GAAAA,SAAA;IACTE,IAAA,GAAAC,wBAAA,CAAAV,IAAA,EAAAW,SAAA;EAEH,IAAML,KAAK,GAAGV,gBAAgB,CAACS,cAAc,CAAC;EAC9C,IAAcO,WAAW,GAAWN,KAAK,CAAjCO,IAAI;IAAeC,IAAA,GAASR,KAAK,CAAdQ,IAAA;EAE3B,IAAMC,YAAY,GAAGD,IAAI,GACrBR,KAAK,CAACU,MAAM,CAACC,cAAc,GAC3BtB,KAAK,CAACiB,WAAW,GAAGd,KAAK,GAAGD,KAAK,CAAC,CAC/BqB,KAAK,CAAC,IAAI,CAAC,CACXC,GAAG,CAAC,CAAC,CACLC,MAAM,CAAC,CAAC;EAEf,OACE5B,KAAA,CAAA6B,aAAA,CAAC3B,IAAI,EAAA4B,QAAA,KACCb,IAAI;IACRL,KAAK,EAAE,CACL;MAAEmB,MAAM,EAAE9B,UAAU,CAAC+B,aAAa;MAAEC,eAAe,EAAEV;IAAa,CAAC,EACnEd,SAAS,KAAKa,IAAI,GAAGY,MAAM,CAACC,WAAW,GAAGD,MAAM,CAACzB,SAAS,CAAC,EAC3Da,IAAI,IAAIX,eAAe,IAAIuB,MAAM,CAACvB,eAAe,EACjDW,IAAI,IAAIN,IAAI,IAAIkB,MAAM,CAAClB,IAAI,EAC3BJ,KAAK;EACL,EACH,CAAC;AAEN,CAAC;AAED,IAAMsB,MAAM,GAAGjC,UAAU,CAACmC,MAAM,CAAC;EAC/B3B,SAAS,EAAE;IACT4B,UAAU,EAAE;EACd,CAAC;EACDF,WAAW,EAAE;IACXE,UAAU,EAAE;EACd,CAAC;EACD1B,eAAe,EAAE;IACf0B,UAAU,EAAE,EAAE;IACdC,WAAW,EAAE;EACf,CAAC;EACDtB,IAAI,EAAE;IACJe,MAAM,EAAE;EACV;AACF,CAAC,CAAC;AAEF,eAAexB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}