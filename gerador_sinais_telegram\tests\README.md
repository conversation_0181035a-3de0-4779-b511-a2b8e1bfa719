# Testes do Sistema de Trading Automatizado

Este diretório contém scripts de teste para verificar o funcionamento do sistema de trading automatizado.

## Configuração

Antes de executar os testes, certifique-se de que:

1. O arquivo `.env` está configurado corretamente com suas credenciais da Binance e do Telegram
2. As dependências do projeto estão instaladas

## Testes Disponíveis

### 1. Teste de Conexão Real

Verifica se a conexão com a Binance e o Telegram está funcionando corretamente.

```bash
# Testar ambas as conexões
python tests/test_connection_real.py

# Testar apenas a conexão com a Binance
python tests/test_connection_real.py --binance

# Testar apenas a conexão com o Telegram
python tests/test_connection_real.py --telegram
```

### 2. Teste de Fluxo de Integração

Simula o fluxo completo de receber um sinal, processá-lo e executar uma ordem.

```bash
# Executar com simulação (sem ordens reais)
python tests/test_integration_flow.py

# Executar com um par específico
python tests/test_integration_flow.py --symbol ETHUSDT

# Executar com conexão real (CUIDADO: irá executar ordens reais!)
python tests/test_integration_flow.py --real
```

### 3. Teste de Monitoramento de Posições

Testa o monitoramento de posições abertas, verificando se o stop loss e take profit estão funcionando.

```bash
# Executar com simulação (sem ordens reais)
python tests/test_position_monitor.py

# Executar com um par específico e duração personalizada
python tests/test_position_monitor.py --symbol ETHUSDT --duration 120

# Executar com conexão real (CUIDADO: irá executar ordens reais!)
python tests/test_position_monitor.py --real
```

### 4. Teste de Integração com Mocks

Testes unitários e de integração usando mocks para simular as dependências.

```bash
# Executar todos os testes
pytest tests/test_trading_integration.py

# Executar um teste específico
pytest tests/test_trading_integration.py::test_binance_get_historical_data
```

## Solução de Problemas

### Erro de Conexão com a Binance

Se você encontrar erros ao conectar-se à Binance, verifique:

1. Se as credenciais da API estão corretas no arquivo `.env`
2. Se a API tem permissões para futures (se estiver testando com ordens reais)
3. Se o modo testnet está configurado corretamente (`BINANCE_TESTNET=True` para testes)

### Erro de Conexão com o Telegram

Se você encontrar erros ao conectar-se ao Telegram, verifique:

1. Se as credenciais do Telegram estão corretas no arquivo `.env`
2. Se você já autenticou a sessão anteriormente (pode ser necessário autenticar manualmente na primeira execução)
3. Se o ID do grupo está correto

## Notas Importantes

- **NUNCA** execute testes com a flag `--real` em ambiente de produção sem entender completamente o que o teste fará
- Sempre comece com testes em modo de simulação antes de testar com ordens reais
- Os testes podem criar arquivos de log em `tests/` para ajudar na depuração 