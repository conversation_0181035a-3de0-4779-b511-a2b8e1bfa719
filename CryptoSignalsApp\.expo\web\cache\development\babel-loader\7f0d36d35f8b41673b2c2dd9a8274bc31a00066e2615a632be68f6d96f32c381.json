{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"children\", \"style\", \"theme\"];\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nimport * as React from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport color from 'color';\nimport { useInternalTheme } from \"../../core/theming\";\nimport { black, white } from \"../../styles/themes/v2/colors\";\nvar DataTableHeader = function DataTableHeader(_ref) {\n  var children = _ref.children,\n    style = _ref.style,\n    themeOverrides = _ref.theme,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var theme = useInternalTheme(themeOverrides);\n  var borderBottomColor = theme.isV3 ? theme.colors.surfaceVariant : color(theme.dark ? white : black).alpha(0.12).rgb().string();\n  return React.createElement(View, _extends({}, rest, {\n    style: [styles.header, {\n      borderBottomColor: borderBottomColor\n    }, style]\n  }), children);\n};\nDataTableHeader.displayName = 'DataTable.Header';\nvar styles = StyleSheet.create({\n  header: {\n    flexDirection: 'row',\n    paddingHorizontal: 16,\n    borderBottomWidth: StyleSheet.hairlineWidth * 2\n  }\n});\nexport default DataTableHeader;\nexport { DataTableHeader };", "map": {"version": 3, "names": ["React", "StyleSheet", "View", "color", "useInternalTheme", "black", "white", "DataTableHeader", "_ref", "children", "style", "themeOverrides", "theme", "rest", "_objectWithoutProperties", "_excluded", "borderBottomColor", "isV3", "colors", "surfaceVariant", "dark", "alpha", "rgb", "string", "createElement", "_extends", "styles", "header", "displayName", "create", "flexDirection", "paddingHorizontal", "borderBottomWidth", "hairlineWidth"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\DataTable\\DataTableHeader.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { StyleProp, StyleSheet, View, ViewStyle } from 'react-native';\n\nimport color from 'color';\n\nimport { useInternalTheme } from '../../core/theming';\nimport { black, white } from '../../styles/themes/v2/colors';\nimport type { ThemeProp } from '../../types';\n\nexport type Props = React.ComponentPropsWithRef<typeof View> & {\n  /**\n   * Content of the `DataTableHeader`.\n   */\n  children: React.ReactNode;\n  style?: StyleProp<ViewStyle>;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n};\n\n/**\n * A component to display title in table header.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { DataTable } from 'react-native-paper';\n *\n * const MyComponent = () => (\n *       <DataTable>\n *         <DataTable.Header>\n *           <DataTable.Title\n *             sortDirection='descending'\n *           >\n *             Dessert\n *           </DataTable.Title>\n *           <DataTable.Title numeric>Calories</DataTable.Title>\n *           <DataTable.Title numeric>Fat (g)</DataTable.Title>\n *         </DataTable.Header>\n *       </DataTable>\n * );\n *\n * export default MyComponent;\n * ```\n */\n\nconst DataTableHeader = ({\n  children,\n  style,\n  theme: themeOverrides,\n  ...rest\n}: Props) => {\n  const theme = useInternalTheme(themeOverrides);\n  const borderBottomColor = theme.isV3\n    ? theme.colors.surfaceVariant\n    : color(theme.dark ? white : black)\n        .alpha(0.12)\n        .rgb()\n        .string();\n\n  return (\n    <View {...rest} style={[styles.header, { borderBottomColor }, style]}>\n      {children}\n    </View>\n  );\n};\n\nDataTableHeader.displayName = 'DataTable.Header';\n\nconst styles = StyleSheet.create({\n  header: {\n    flexDirection: 'row',\n    paddingHorizontal: 16,\n    borderBottomWidth: StyleSheet.hairlineWidth * 2,\n  },\n});\n\nexport default DataTableHeader;\n\n// @component-docs ignore-next-line\nexport { DataTableHeader };\n"], "mappings": ";;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAG9B,OAAOC,KAAK,MAAM,OAAO;AAEzB,SAASC,gBAAgB;AACzB,SAASC,KAAK,EAAEC,KAAK;AAyCrB,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAAC,IAAA,EAKR;EAAA,IAJXC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;IACRC,KAAK,GAAAF,IAAA,CAALE,KAAK;IACEC,cAAc,GAAAH,IAAA,CAArBI,KAAK;IACFC,IAAA,GAAAC,wBAAA,CAAAN,IAAA,EAAAO,SAAA;EAEH,IAAMH,KAAK,GAAGR,gBAAgB,CAACO,cAAc,CAAC;EAC9C,IAAMK,iBAAiB,GAAGJ,KAAK,CAACK,IAAI,GAChCL,KAAK,CAACM,MAAM,CAACC,cAAc,GAC3BhB,KAAK,CAACS,KAAK,CAACQ,IAAI,GAAGd,KAAK,GAAGD,KAAK,CAAC,CAC9BgB,KAAK,CAAC,IAAI,CAAC,CACXC,GAAG,CAAC,CAAC,CACLC,MAAM,CAAC,CAAC;EAEf,OACEvB,KAAA,CAAAwB,aAAA,CAACtB,IAAI,EAAAuB,QAAA,KAAKZ,IAAI;IAAEH,KAAK,EAAE,CAACgB,MAAM,CAACC,MAAM,EAAE;MAAEX,iBAAA,EAAAA;IAAkB,CAAC,EAAEN,KAAK;EAAE,IAClED,QACG,CAAC;AAEX,CAAC;AAEDF,eAAe,CAACqB,WAAW,GAAG,kBAAkB;AAEhD,IAAMF,MAAM,GAAGzB,UAAU,CAAC4B,MAAM,CAAC;EAC/BF,MAAM,EAAE;IACNG,aAAa,EAAE,KAAK;IACpBC,iBAAiB,EAAE,EAAE;IACrBC,iBAAiB,EAAE/B,UAAU,CAACgC,aAAa,GAAG;EAChD;AACF,CAAC,CAAC;AAEF,eAAe1B,eAAe;AAG9B,SAASA,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}