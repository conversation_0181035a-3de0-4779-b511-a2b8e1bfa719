{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport invariant from 'fbjs/lib/invariant';\nimport canUseDOM from \"../../modules/canUseDom\";\nvar initialURL = canUseDOM ? window.location.href : '';\nvar Linking = function () {\n  function Linking() {\n    _classCallCheck(this, Linking);\n    this._eventCallbacks = {};\n  }\n  return _createClass(Linking, [{\n    key: \"_dispatchEvent\",\n    value: function _dispatchEvent(event) {\n      for (var _len = arguments.length, data = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        data[_key - 1] = arguments[_key];\n      }\n      var listeners = this._eventCallbacks[event];\n      if (listeners != null && Array.isArray(listeners)) {\n        listeners.map(function (listener) {\n          listener.apply(void 0, data);\n        });\n      }\n    }\n  }, {\n    key: \"addEventListener\",\n    value: function addEventListener(eventType, callback) {\n      var _this = this;\n      if (!_this._eventCallbacks[eventType]) {\n        _this._eventCallbacks[eventType] = [callback];\n      }\n      _this._eventCallbacks[eventType].push(callback);\n      return {\n        remove: function remove() {\n          var callbacks = _this._eventCallbacks[eventType];\n          var filteredCallbacks = callbacks.filter(function (c) {\n            return c.toString() !== callback.toString();\n          });\n          _this._eventCallbacks[eventType] = filteredCallbacks;\n        }\n      };\n    }\n  }, {\n    key: \"removeEventListener\",\n    value: function removeEventListener(eventType, callback) {\n      console.error(\"Linking.removeEventListener('\" + eventType + \"', ...): Method has been \" + 'deprecated. Please instead use `remove()` on the subscription ' + 'returned by `Linking.addEventListener`.');\n      var callbacks = this._eventCallbacks[eventType];\n      var filteredCallbacks = callbacks.filter(function (c) {\n        return c.toString() !== callback.toString();\n      });\n      this._eventCallbacks[eventType] = filteredCallbacks;\n    }\n  }, {\n    key: \"canOpenURL\",\n    value: function canOpenURL() {\n      return Promise.resolve(true);\n    }\n  }, {\n    key: \"getInitialURL\",\n    value: function getInitialURL() {\n      return Promise.resolve(initialURL);\n    }\n  }, {\n    key: \"openURL\",\n    value: function openURL(url, target) {\n      if (arguments.length === 1) {\n        target = '_blank';\n      }\n      try {\n        open(url, target);\n        this._dispatchEvent('onOpen', url);\n        return Promise.resolve();\n      } catch (e) {\n        return Promise.reject(e);\n      }\n    }\n  }, {\n    key: \"_validateURL\",\n    value: function _validateURL(url) {\n      invariant(typeof url === 'string', 'Invalid URL: should be a string. Was: ' + url);\n      invariant(url, 'Invalid URL: cannot be empty');\n    }\n  }]);\n}();\nvar open = function open(url, target) {\n  if (canUseDOM) {\n    var urlToOpen = new URL(url, window.location).toString();\n    if (urlToOpen.indexOf('tel:') === 0) {\n      window.location = urlToOpen;\n    } else {\n      window.open(urlToOpen, target, 'noopener');\n    }\n  }\n};\nexport default new Linking();", "map": {"version": 3, "names": ["invariant", "canUseDOM", "initialURL", "window", "location", "href", "Linking", "_classCallCheck", "_eventCallbacks", "_createClass", "key", "value", "_dispatchEvent", "event", "_len", "arguments", "length", "data", "Array", "_key", "listeners", "isArray", "map", "listener", "apply", "addEventListener", "eventType", "callback", "_this", "push", "remove", "callbacks", "filteredCallbacks", "filter", "c", "toString", "removeEventListener", "console", "error", "canOpenURL", "Promise", "resolve", "getInitialURL", "openURL", "url", "target", "open", "e", "reject", "_validateURL", "urlToOpen", "URL", "indexOf"], "sources": ["C:/Users/<USER>/Desktop/Signals/CryptoSignalsApp/node_modules/react-native-web/dist/exports/Linking/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport invariant from 'fbjs/lib/invariant';\nimport canUseDOM from '../../modules/canUseDom';\nvar initialURL = canUseDOM ? window.location.href : '';\nclass Linking {\n  constructor() {\n    this._eventCallbacks = {};\n  }\n  /**\n   * An object mapping of event name\n   * and all the callbacks subscribing to it\n   */\n  _dispatchEvent(event) {\n    for (var _len = arguments.length, data = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      data[_key - 1] = arguments[_key];\n    }\n    var listeners = this._eventCallbacks[event];\n    if (listeners != null && Array.isArray(listeners)) {\n      listeners.map(listener => {\n        listener(...data);\n      });\n    }\n  }\n\n  /**\n   * Adds a event listener for the specified event. The callback will be called when the\n   * said event is dispatched.\n   */\n  addEventListener(eventType, callback) {\n    var _this = this;\n    if (!_this._eventCallbacks[eventType]) {\n      _this._eventCallbacks[eventType] = [callback];\n    }\n    _this._eventCallbacks[eventType].push(callback);\n    return {\n      remove() {\n        var callbacks = _this._eventCallbacks[eventType];\n        var filteredCallbacks = callbacks.filter(c => c.toString() !== callback.toString());\n        _this._eventCallbacks[eventType] = filteredCallbacks;\n      }\n    };\n  }\n\n  /**\n   * Removes a previously added event listener for the specified event. The callback must\n   * be the same object as the one passed to `addEventListener`.\n   */\n  removeEventListener(eventType, callback) {\n    console.error(\"Linking.removeEventListener('\" + eventType + \"', ...): Method has been \" + 'deprecated. Please instead use `remove()` on the subscription ' + 'returned by `Linking.addEventListener`.');\n    var callbacks = this._eventCallbacks[eventType];\n    var filteredCallbacks = callbacks.filter(c => c.toString() !== callback.toString());\n    this._eventCallbacks[eventType] = filteredCallbacks;\n  }\n  canOpenURL() {\n    return Promise.resolve(true);\n  }\n  getInitialURL() {\n    return Promise.resolve(initialURL);\n  }\n\n  /**\n   * Try to open the given url in a secure fashion. The method returns a Promise object.\n   * If a target is passed (including undefined) that target will be used, otherwise '_blank'.\n   * If the url opens, the promise is resolved. If not, the promise is rejected.\n   * Dispatches the `onOpen` event if `url` is opened successfully.\n   */\n  openURL(url, target) {\n    if (arguments.length === 1) {\n      target = '_blank';\n    }\n    try {\n      open(url, target);\n      this._dispatchEvent('onOpen', url);\n      return Promise.resolve();\n    } catch (e) {\n      return Promise.reject(e);\n    }\n  }\n  _validateURL(url) {\n    invariant(typeof url === 'string', 'Invalid URL: should be a string. Was: ' + url);\n    invariant(url, 'Invalid URL: cannot be empty');\n  }\n}\nvar open = (url, target) => {\n  if (canUseDOM) {\n    var urlToOpen = new URL(url, window.location).toString();\n    if (urlToOpen.indexOf('tel:') === 0) {\n      window.location = urlToOpen;\n    } else {\n      window.open(urlToOpen, target, 'noopener');\n    }\n  }\n};\nexport default new Linking();"], "mappings": ";;AAUA,OAAOA,SAAS,MAAM,oBAAoB;AAC1C,OAAOC,SAAS;AAChB,IAAIC,UAAU,GAAGD,SAAS,GAAGE,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,EAAE;AAAC,IACjDC,OAAO;EACX,SAAAA,QAAA,EAAc;IAAAC,eAAA,OAAAD,OAAA;IACZ,IAAI,CAACE,eAAe,GAAG,CAAC,CAAC;EAC3B;EAAC,OAAAC,YAAA,CAAAH,OAAA;IAAAI,GAAA;IAAAC,KAAA,EAKD,SAAAC,cAAcA,CAACC,KAAK,EAAE;MACpB,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;QAC1GF,IAAI,CAACE,IAAI,GAAG,CAAC,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;MAClC;MACA,IAAIC,SAAS,GAAG,IAAI,CAACZ,eAAe,CAACK,KAAK,CAAC;MAC3C,IAAIO,SAAS,IAAI,IAAI,IAAIF,KAAK,CAACG,OAAO,CAACD,SAAS,CAAC,EAAE;QACjDA,SAAS,CAACE,GAAG,CAAC,UAAAC,QAAQ,EAAI;UACxBA,QAAQ,CAAAC,KAAA,SAAIP,IAAI,CAAC;QACnB,CAAC,CAAC;MACJ;IACF;EAAC;IAAAP,GAAA;IAAAC,KAAA,EAMD,SAAAc,gBAAgBA,CAACC,SAAS,EAAEC,QAAQ,EAAE;MACpC,IAAIC,KAAK,GAAG,IAAI;MAChB,IAAI,CAACA,KAAK,CAACpB,eAAe,CAACkB,SAAS,CAAC,EAAE;QACrCE,KAAK,CAACpB,eAAe,CAACkB,SAAS,CAAC,GAAG,CAACC,QAAQ,CAAC;MAC/C;MACAC,KAAK,CAACpB,eAAe,CAACkB,SAAS,CAAC,CAACG,IAAI,CAACF,QAAQ,CAAC;MAC/C,OAAO;QACLG,MAAM,WAANA,MAAMA,CAAA,EAAG;UACP,IAAIC,SAAS,GAAGH,KAAK,CAACpB,eAAe,CAACkB,SAAS,CAAC;UAChD,IAAIM,iBAAiB,GAAGD,SAAS,CAACE,MAAM,CAAC,UAAAC,CAAC;YAAA,OAAIA,CAAC,CAACC,QAAQ,CAAC,CAAC,KAAKR,QAAQ,CAACQ,QAAQ,CAAC,CAAC;UAAA,EAAC;UACnFP,KAAK,CAACpB,eAAe,CAACkB,SAAS,CAAC,GAAGM,iBAAiB;QACtD;MACF,CAAC;IACH;EAAC;IAAAtB,GAAA;IAAAC,KAAA,EAMD,SAAAyB,mBAAmBA,CAACV,SAAS,EAAEC,QAAQ,EAAE;MACvCU,OAAO,CAACC,KAAK,CAAC,+BAA+B,GAAGZ,SAAS,GAAG,2BAA2B,GAAG,gEAAgE,GAAG,yCAAyC,CAAC;MACvM,IAAIK,SAAS,GAAG,IAAI,CAACvB,eAAe,CAACkB,SAAS,CAAC;MAC/C,IAAIM,iBAAiB,GAAGD,SAAS,CAACE,MAAM,CAAC,UAAAC,CAAC;QAAA,OAAIA,CAAC,CAACC,QAAQ,CAAC,CAAC,KAAKR,QAAQ,CAACQ,QAAQ,CAAC,CAAC;MAAA,EAAC;MACnF,IAAI,CAAC3B,eAAe,CAACkB,SAAS,CAAC,GAAGM,iBAAiB;IACrD;EAAC;IAAAtB,GAAA;IAAAC,KAAA,EACD,SAAA4B,UAAUA,CAAA,EAAG;MACX,OAAOC,OAAO,CAACC,OAAO,CAAC,IAAI,CAAC;IAC9B;EAAC;IAAA/B,GAAA;IAAAC,KAAA,EACD,SAAA+B,aAAaA,CAAA,EAAG;MACd,OAAOF,OAAO,CAACC,OAAO,CAACvC,UAAU,CAAC;IACpC;EAAC;IAAAQ,GAAA;IAAAC,KAAA,EAQD,SAAAgC,OAAOA,CAACC,GAAG,EAAEC,MAAM,EAAE;MACnB,IAAI9B,SAAS,CAACC,MAAM,KAAK,CAAC,EAAE;QAC1B6B,MAAM,GAAG,QAAQ;MACnB;MACA,IAAI;QACFC,IAAI,CAACF,GAAG,EAAEC,MAAM,CAAC;QACjB,IAAI,CAACjC,cAAc,CAAC,QAAQ,EAAEgC,GAAG,CAAC;QAClC,OAAOJ,OAAO,CAACC,OAAO,CAAC,CAAC;MAC1B,CAAC,CAAC,OAAOM,CAAC,EAAE;QACV,OAAOP,OAAO,CAACQ,MAAM,CAACD,CAAC,CAAC;MAC1B;IACF;EAAC;IAAArC,GAAA;IAAAC,KAAA,EACD,SAAAsC,YAAYA,CAACL,GAAG,EAAE;MAChB5C,SAAS,CAAC,OAAO4C,GAAG,KAAK,QAAQ,EAAE,wCAAwC,GAAGA,GAAG,CAAC;MAClF5C,SAAS,CAAC4C,GAAG,EAAE,8BAA8B,CAAC;IAChD;EAAC;AAAA;AAEH,IAAIE,IAAI,GAAG,SAAPA,IAAIA,CAAIF,GAAG,EAAEC,MAAM,EAAK;EAC1B,IAAI5C,SAAS,EAAE;IACb,IAAIiD,SAAS,GAAG,IAAIC,GAAG,CAACP,GAAG,EAAEzC,MAAM,CAACC,QAAQ,CAAC,CAAC+B,QAAQ,CAAC,CAAC;IACxD,IAAIe,SAAS,CAACE,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;MACnCjD,MAAM,CAACC,QAAQ,GAAG8C,SAAS;IAC7B,CAAC,MAAM;MACL/C,MAAM,CAAC2C,IAAI,CAACI,SAAS,EAAEL,MAAM,EAAE,UAAU,CAAC;IAC5C;EACF;AACF,CAAC;AACD,eAAe,IAAIvC,OAAO,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}