import os
import sys
import asyncio
import logging
import random
import time
import datetime
import pandas as pd
import nest_asyncio
from config.settings import (
    MAX_SIGNALS_PER_DAY,
    SIGNAL_INTERVAL_SECONDS,
    LOG_LEVEL,
    LOG_FILE,
    SIGNA<PERSON>_VALIDITY_MINUTES,
    SCALP_PROFIT_LEVELS
)
from config.symbols import (
    SCALP_SYMBOLS,
    BREAKOUT_SYMBOLS,
    INSIDE_BAR_SYMBOLS,
    MFI_SYMBOLS,
    SWING_SYMBOLS
)
from utils.binance_client import BinanceHandler
from utils.telegram_sender import TelegramSender
from utils.signal_formatter import SignalFormatter
from utils.database import DatabaseHandler
from estrategias.scalp_strategy import ScalpStrategy
from estrategias.breakout_volume import BreakoutVolumeStrategy
from estrategias.inside_bar import InsideBarStrategy
from estrategias.mfi_strategy import MFIStrategy
from estrategias.swing_strategy import SwingStrategy
from estrategias.multi_source_strategy import MultiSourceStrategy
from estrategias.volume_analysis_strategy import VolumeAnalysisStrategy
from estrategias.momentum_strategy import MomentumStrategy
from notifications.notification_manager import NotificationManager

# Aplicar nest_asyncio para permitir loops aninhados (útil para debugging)
nest_asyncio.apply()

# Configuração de logging
import io

# Criar um handler de arquivo
file_handler = logging.FileHandler(LOG_FILE, encoding='utf-8')

# Criar um handler de console que lida com Unicode
class UnicodeStreamHandler(logging.StreamHandler):
    def __init__(self):
        logging.StreamHandler.__init__(self, io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8'))

    def emit(self, record):
        try:
            msg = self.format(record)
            stream = self.stream
            stream.write(msg + self.terminator)
            self.flush()
        except Exception:
            self.handleError(record)

# Configurar logging
logging.basicConfig(
    level=getattr(logging, LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        UnicodeStreamHandler(),
        file_handler
    ]
)

logger = logging.getLogger(__name__)

class SignalGenerator:
    def __init__(self, db_path="database.sqlite"):
        self.binance = BinanceHandler()
        self.telegram = TelegramSender()
        self.formatter = SignalFormatter()
        self.db = DatabaseHandler(db_path)

        # Inicializar estratégias - EXPANDIDO COM MÚLTIPLAS FONTES
        self.scalp_strategy = ScalpStrategy(self.binance)
        self.breakout_strategy = BreakoutVolumeStrategy(self.binance)
        self.inside_bar_strategy = InsideBarStrategy(self.binance)
        self.mfi_strategy = MFIStrategy(self.binance)
        self.swing_strategy = SwingStrategy(self.binance)
        self.multi_source_strategy = MultiSourceStrategy(self.binance)
        self.volume_analysis_strategy = VolumeAnalysisStrategy(self.binance)
        self.momentum_strategy = MomentumStrategy(self.binance)

        # Sistema de notificações
        self.notification_manager = NotificationManager()

        # Controle de sinais
        self.signals_sent_today = 0
        self.current_day = datetime.datetime.now().date()
        self.open_signals = []  # Lista para monitorar sinais abertos
        self.recent_signals = {}  # Dicionário para controlar sinais recentes e evitar duplicatas
        self.expired_signals = set()  # Conjunto para rastrear sinais que já expiraram

        # Carregar sinais abertos do banco de dados
        self._load_open_signals_from_db()

    def _load_open_signals_from_db(self):
        """Carrega sinais abertos do banco de dados"""
        try:
            db_signals = self.db.get_open_signals()

            for signal_data in db_signals:
                # Converter dados do banco para o formato usado pelo monitor
                signal = {
                    'db_id': signal_data['id'],
                    'symbol': signal_data['symbol'],
                    'signal_type': signal_data['signal_type'],
                    'strategy': signal_data['strategy'],
                    'entry_price': signal_data['entry_price'],
                    'stop_loss': signal_data['stop_loss'],
                    'take_profit': signal_data['take_profit_1'],  # Usar o primeiro TP como principal
                    'leverage': signal_data['leverage'],
                    'message_id': signal_data['message_id'],
                    'start_time': datetime.datetime.fromisoformat(signal_data['created_at']),
                    'is_closed': False,
                    'notified_levels': set()  # Iniciar com conjunto vazio
                }

                # Adicionar take profits como dicionário
                signal['take_profits'] = {}
                if signal_data['take_profit_1']:
                    signal['take_profits'][25] = signal_data['take_profit_1']
                if signal_data['take_profit_2']:
                    signal['take_profits'][50] = signal_data['take_profit_2']
                if signal_data['take_profit_3']:
                    signal['take_profits'][75] = signal_data['take_profit_3']
                if signal_data['take_profit_4']:
                    signal['take_profits'][100] = signal_data['take_profit_4']

                self.open_signals.append(signal)

            logger.info(f"Carregados {len(self.open_signals)} sinais abertos do banco de dados")

        except Exception as e:
            logger.error(f"Erro ao carregar sinais do banco de dados: {e}")

    def reset_daily_counter(self):
        """Reseta o contador diário se necessário"""
        today = datetime.datetime.now().date()
        if today != self.current_day:
            self.signals_sent_today = 0
            self.current_day = today
            logger.info("Contador de sinais diários resetado")

    def is_trading_hours(self):
        """Verifica se está dentro do horário de trading - OPERAÇÃO 24/7"""
        # Criptomoedas operam 24h, sempre retorna True
        # Mantemos a função para compatibilidade, mas sempre permite trading
        return True

    def is_signal_duplicate(self, symbol, signal_type, entry_price):
        """Verifica se um sinal é duplicado baseado no símbolo, tipo e preço"""
        key = f"{symbol}_{signal_type}"
        current_time = datetime.datetime.now()

        # Limpar sinais antigos (mais de 1 hora)
        self._cleanup_old_signals(current_time)

        if key in self.recent_signals:
            last_signal = self.recent_signals[key]
            time_diff = current_time - last_signal['timestamp']

            # Validar se o preço de entrada é válido
            if entry_price <= 0:
                logger.error(f"Preço de entrada inválido para {symbol}: {entry_price}")
                return True  # Considerar como duplicado para evitar envio

            # Calcular diferença percentual de preço
            try:
                price_diff = abs(entry_price - last_signal['entry_price']) / last_signal['entry_price']
            except (ZeroDivisionError, TypeError):
                logger.error(f"Erro ao calcular diferença de preço para {symbol}")
                return True  # Considerar como duplicado em caso de erro

            # Considera duplicado se dentro de 10 minutos e variação de preço menor que 1%
            time_threshold = 600  # 10 minutos em segundos
            price_threshold = 0.01  # 1%

            if time_diff.total_seconds() < time_threshold and price_diff < price_threshold:
                logger.info(f"Sinal duplicado detectado para {symbol}: tempo={time_diff.total_seconds():.0f}s, preço_diff={price_diff:.3f}")
                return True
        return False

    def register_signal(self, symbol, signal_type, entry_price):
        """Registra um sinal para controle de duplicatas"""
        key = f"{symbol}_{signal_type}"
        self.recent_signals[key] = {
            'timestamp': datetime.datetime.now(),
            'entry_price': entry_price
        }
        logger.debug(f"Sinal registrado para controle de duplicação: {key} - {entry_price}")

    def _cleanup_old_signals(self, current_time):
        """Remove sinais antigos do controle de duplicação"""
        keys_to_remove = []
        for key, signal_data in self.recent_signals.items():
            time_diff = current_time - signal_data['timestamp']
            if time_diff.total_seconds() > 3600:  # 1 hora
                keys_to_remove.append(key)

        for key in keys_to_remove:
            del self.recent_signals[key]
            logger.debug(f"Sinal antigo removido do controle: {key}")

    async def check_scalp_signals(self):
        """Verifica sinais de scalping"""
        if self.signals_sent_today >= MAX_SIGNALS_PER_DAY:
            return

        # Escolhe aleatoriamente alguns símbolos para analisar
        symbols_to_check = random.sample(SCALP_SYMBOLS, min(5, len(SCALP_SYMBOLS)))

        for symbol in symbols_to_check:
            result = self.scalp_strategy.analyze_symbol(symbol)

            # Se não há sinal, continua para o próximo símbolo
            if result is None or result[0] is None or result[1] is None:
                continue

            signal_type, entry_price, stop_loss, take_profit = result

            # Garantir que entry_price não é None antes de usar
            if entry_price is None:
                continue

            # Calcular níveis de take profit baseados no take_profit retornado
            # Para scalping, geralmente usamos múltiplos níveis
            profit_percentage = self.scalp_strategy.profit_percentage * 100  # Convertendo para porcentagem
            take_profits = {}

            # Define os níveis de take profit como percentuais do profit alvo
            for level in SCALP_PROFIT_LEVELS:
                # Calcular o percentual real (level% do profit_percentage)
                actual_percentage = (level / 100) * self.scalp_strategy.profit_percentage

                if signal_type == "LONG":
                    take_profits[level] = entry_price * (1 + actual_percentage)
                else:  # SHORT
                    take_profits[level] = entry_price * (1 - actual_percentage)

                # Verificar se o take profit é válido (não pode ser zero ou negativo)
                if take_profits[level] <= 0:
                    # Usar um valor mínimo razoável (1% do preço de entrada)
                    take_profits[level] = entry_price * 0.01 if signal_type == "SHORT" else entry_price * 1.01

            if signal_type is not None and not self.is_signal_duplicate(symbol, signal_type, entry_price):
                # Registra o sinal para evitar duplicatas
                self.register_signal(symbol, signal_type, entry_price)

                # Formatar e enviar sinal
                message = self.formatter.format_scalp_signal(
                    symbol, entry_price, take_profits, signal_type, self.scalp_strategy.leverage
                )

                message_id = await self.telegram.send_message(message)

                if message_id:
                    self.signals_sent_today += 1
                    logger.info(f"Sinal de scalp enviado para {symbol}. Total hoje: {self.signals_sent_today}")

                    # Criar objeto de sinal
                    signal_data = {
                        'symbol': symbol,
                        'signal_type': signal_type,
                        'strategy': 'Scalp',
                        'entry_price': entry_price,
                        'take_profits': take_profits,
                        'stop_loss': stop_loss,
                        'leverage': self.scalp_strategy.leverage,
                        'start_time': datetime.datetime.now(),
                        'notified_levels': set(),
                        'is_closed': False,
                        'message_id': message_id,  # Armazenar o ID da mensagem original
                        'take_profit': list(take_profits.values())[0] if take_profits else None,  # Para notificações
                        'confidence': 85  # Confidence padrão para scalp
                    }

                    # Enviar notificações
                    try:
                        notification_result = self.notification_manager.notify_new_signal(signal_data)
                        logger.info(f"Notificações enviadas para {symbol}: {notification_result}")
                    except Exception as e:
                        logger.error(f"Erro ao enviar notificações para {symbol}: {e}")

                    # Salvar no banco de dados
                    db_id = self.db.save_signal({
                        'symbol': symbol,
                        'signal_type': signal_type,
                        'strategy': 'Scalp',
                        'entry_price': entry_price,
                        'stop_loss': stop_loss,
                        'take_profits': take_profits,
                        'leverage': self.scalp_strategy.leverage,
                        'message_id': message_id
                    })

                    if db_id > 0:
                        signal_data['db_id'] = db_id
                        logger.info(f"Sinal de scalp salvo no banco de dados com ID {db_id}")

                    # Adicionar à lista de sinais abertos para monitoramento
                    self.open_signals.append(signal_data)

                # Se atingiu o limite diário, interrompe
                if self.signals_sent_today >= MAX_SIGNALS_PER_DAY:
                    return

    async def check_breakout_signals(self):
        """Verifica sinais de breakout com volume"""
        if self.signals_sent_today >= MAX_SIGNALS_PER_DAY:
            return

        # Escolhe aleatoriamente alguns símbolos para analisar
        symbols_to_check = random.sample(BREAKOUT_SYMBOLS, min(3, len(BREAKOUT_SYMBOLS)))

        for symbol in symbols_to_check:
            result = self.breakout_strategy.analyze_symbol(symbol)

            # Verificar se o resultado é válido
            if result is None or result[0] is None or result[1] is None:
                continue

            signal_type, entry_price, stop_loss, take_profit = result

            # Garantir que os valores não são None
            if entry_price is None or stop_loss is None or take_profit is None:
                continue

            if not self.is_signal_duplicate(symbol, signal_type, entry_price):
                # Registra o sinal para evitar duplicatas
                self.register_signal(symbol, signal_type, entry_price)

                # Formatar e enviar sinal
                message = self.formatter.format_breakout_signal(
                    symbol, entry_price, stop_loss, take_profit, self.breakout_strategy.leverage, signal_type
                )

                message_id = await self.telegram.send_message(message)

                if message_id:
                    self.signals_sent_today += 1
                    logger.info(f"Sinal de breakout enviado para {symbol}. Total hoje: {self.signals_sent_today}")

                    # Criar objeto de sinal
                    signal_data = {
                        'symbol': symbol,
                        'signal_type': signal_type,
                        'strategy': 'Breakout',
                        'entry_price': entry_price,
                        'take_profit': take_profit,
                        'stop_loss': stop_loss,
                        'leverage': self.breakout_strategy.leverage,
                        'start_time': datetime.datetime.now(),
                        'is_closed': False,
                        'message_id': message_id  # Armazenar o ID da mensagem original
                    }

                    # Salvar no banco de dados
                    db_id = self.db.save_signal({
                        'symbol': symbol,
                        'signal_type': signal_type,
                        'strategy': 'Breakout',
                        'entry_price': entry_price,
                        'stop_loss': stop_loss,
                        'take_profit': take_profit,
                        'leverage': self.breakout_strategy.leverage,
                        'message_id': message_id
                    })

                    if db_id > 0:
                        signal_data['db_id'] = db_id
                        logger.info(f"Sinal de breakout salvo no banco de dados com ID {db_id}")

                    # Adicionar à lista de sinais abertos para monitoramento
                    self.open_signals.append(signal_data)

                # Se atingiu o limite diário, interrompe
                if self.signals_sent_today >= MAX_SIGNALS_PER_DAY:
                    return

    async def check_inside_bar_signals(self):
        """Verifica sinais de inside bar"""
        if self.signals_sent_today >= MAX_SIGNALS_PER_DAY:
            return

        # Escolhe aleatoriamente alguns símbolos para analisar
        symbols_to_check = random.sample(INSIDE_BAR_SYMBOLS, min(3, len(INSIDE_BAR_SYMBOLS)))

        for symbol in symbols_to_check:
            result = self.inside_bar_strategy.analyze_symbol(symbol)

            # Verificar se o resultado é válido
            if result is None or result[0] is None or result[1] is None:
                continue

            signal_type, entry_price, stop_loss, take_profit = result

            # Garantir que os valores não são None
            if entry_price is None or stop_loss is None or take_profit is None:
                continue

            if not self.is_signal_duplicate(symbol, signal_type, entry_price):
                # Registra o sinal para evitar duplicatas
                self.register_signal(symbol, signal_type, entry_price)

                # Formatar e enviar sinal
                message = self.formatter.format_inside_bar_signal(
                    symbol, entry_price, stop_loss, take_profit, self.inside_bar_strategy.leverage, signal_type
                )

                message_id = await self.telegram.send_message(message)

                if message_id:
                    self.signals_sent_today += 1
                    logger.info(f"Sinal de inside bar enviado para {symbol}. Total hoje: {self.signals_sent_today}")

                    # Criar objeto de sinal
                    signal_data = {
                        'symbol': symbol,
                        'signal_type': signal_type,
                        'strategy': 'Inside Bar',
                        'entry_price': entry_price,
                        'take_profit': take_profit,
                        'stop_loss': stop_loss,
                        'leverage': self.inside_bar_strategy.leverage,
                        'start_time': datetime.datetime.now(),
                        'is_closed': False,
                        'message_id': message_id  # Armazenar o ID da mensagem original
                    }

                    # Salvar no banco de dados
                    db_id = self.db.save_signal({
                        'symbol': symbol,
                        'signal_type': signal_type,
                        'strategy': 'Inside Bar',
                        'entry_price': entry_price,
                        'stop_loss': stop_loss,
                        'take_profit': take_profit,
                        'leverage': self.inside_bar_strategy.leverage,
                        'message_id': message_id
                    })

                    if db_id > 0:
                        signal_data['db_id'] = db_id
                        logger.info(f"Sinal de inside bar salvo no banco de dados com ID {db_id}")

                    # Adicionar à lista de sinais abertos para monitoramento
                    self.open_signals.append(signal_data)

                # Se atingiu o limite diário, interrompe
                if self.signals_sent_today >= MAX_SIGNALS_PER_DAY:
                    return

    async def check_mfi_signals(self):
        """Verifica sinais de MFI (Money Flow Index)"""
        if self.signals_sent_today >= MAX_SIGNALS_PER_DAY:
            return

        # Escolhe aleatoriamente alguns símbolos para analisar
        symbols_to_check = random.sample(MFI_SYMBOLS, min(3, len(MFI_SYMBOLS)))

        for symbol in symbols_to_check:
            result = self.mfi_strategy.analyze_symbol(symbol)

            # Verificar se o resultado é válido
            if result is None or result[0] is None or result[1] is None:
                continue

            signal_type, entry_price, stop_loss, take_profit = result

            # Garantir que os valores não são None
            if entry_price is None or stop_loss is None or take_profit is None:
                continue

            if not self.is_signal_duplicate(symbol, signal_type, entry_price):
                # Registra o sinal para evitar duplicatas
                self.register_signal(symbol, signal_type, entry_price)

                # Formatar e enviar sinal
                message = self.formatter.format_mfi_signal(
                    symbol, entry_price, stop_loss, take_profit, self.mfi_strategy.leverage
                )

                message_id = await self.telegram.send_message(message)

                if message_id:
                    self.signals_sent_today += 1
                    logger.info(f"Sinal de MFI enviado para {symbol}. Total hoje: {self.signals_sent_today}")

                    # Criar objeto de sinal
                    signal_data = {
                        'symbol': symbol,
                        'signal_type': signal_type,
                        'strategy': 'MFI',
                        'entry_price': entry_price,
                        'take_profit': take_profit,
                        'stop_loss': stop_loss,
                        'leverage': self.mfi_strategy.leverage,
                        'start_time': datetime.datetime.now(),
                        'is_closed': False,
                        'message_id': message_id  # Armazenar o ID da mensagem original
                    }

                    # Salvar no banco de dados
                    db_id = self.db.save_signal({
                        'symbol': symbol,
                        'signal_type': signal_type,
                        'strategy': 'MFI',
                        'entry_price': entry_price,
                        'stop_loss': stop_loss,
                        'take_profit': take_profit,
                        'leverage': self.mfi_strategy.leverage,
                        'message_id': message_id
                    })

                    if db_id > 0:
                        signal_data['db_id'] = db_id
                        logger.info(f"Sinal de MFI salvo no banco de dados com ID {db_id}")

                    # Adicionar à lista de sinais abertos para monitoramento
                    self.open_signals.append(signal_data)

                # Se atingiu o limite diário, interrompe
                if self.signals_sent_today >= MAX_SIGNALS_PER_DAY:
                    return

    async def check_swing_signals(self):
        """Verifica sinais de swing trading"""
        if self.signals_sent_today >= MAX_SIGNALS_PER_DAY:
            return

        # Escolhe aleatoriamente alguns símbolos para analisar
        symbols_to_check = random.sample(SWING_SYMBOLS, min(2, len(SWING_SYMBOLS)))

        for symbol in symbols_to_check:
            result = self.swing_strategy.analyze_symbol(symbol)

            # Verificar se o resultado é válido
            if result is None or result[0] is None or result[1] is None:
                continue

            signal_type, entry_price, stop_loss, take_profit = result

            # Garantir que os valores não são None
            if entry_price is None or stop_loss is None or take_profit is None:
                continue

            if not self.is_signal_duplicate(symbol, signal_type, entry_price):
                # Registra o sinal para evitar duplicatas
                self.register_signal(symbol, signal_type, entry_price)

                # Formatar e enviar sinal
                message = self.formatter.format_swing_signal(
                    symbol, entry_price, stop_loss, take_profit, self.swing_strategy.leverage
                )

                message_id = await self.telegram.send_message(message)

                if message_id:
                    self.signals_sent_today += 1
                    logger.info(f"Sinal de swing enviado para {symbol}. Total hoje: {self.signals_sent_today}")

                    # Criar objeto de sinal
                    signal_data = {
                        'symbol': symbol,
                        'signal_type': signal_type,
                        'strategy': 'Swing',
                        'entry_price': entry_price,
                        'take_profit': take_profit,
                        'stop_loss': stop_loss,
                        'leverage': self.swing_strategy.leverage,
                        'start_time': datetime.datetime.now(),
                        'is_closed': False,
                        'message_id': message_id  # Armazenar o ID da mensagem original
                    }

                    # Salvar no banco de dados
                    db_id = self.db.save_signal({
                        'symbol': symbol,
                        'signal_type': signal_type,
                        'strategy': 'Swing',
                        'entry_price': entry_price,
                        'stop_loss': stop_loss,
                        'take_profit': take_profit,
                        'leverage': self.swing_strategy.leverage,
                        'message_id': message_id
                    })

                    if db_id > 0:
                        signal_data['db_id'] = db_id
                        logger.info(f"Sinal de swing salvo no banco de dados com ID {db_id}")

                    # Adicionar à lista de sinais abertos para monitoramento
                    self.open_signals.append(signal_data)

                # Se atingiu o limite diário, interrompe
                if self.signals_sent_today >= MAX_SIGNALS_PER_DAY:
                    return

    async def monitor_open_signals(self):
        """Monitora sinais abertos para notificar take profits atingidos"""
        if not self.open_signals:
            return

        current_time = datetime.datetime.now()
        signals_to_remove = []

        for signal in self.open_signals:
            if signal['is_closed']:
                signals_to_remove.append(signal)
                continue

            # Verificar se o sinal expirou
            time_elapsed = (current_time - signal['start_time']).total_seconds() / 60

            # Criar um identificador único para o sinal
            signal_id = f"{signal['symbol']}_{signal['signal_type']}_{signal['start_time'].strftime('%Y%m%d%H%M%S')}"

            if time_elapsed > SIGNAL_VALIDITY_MINUTES and signal_id not in self.expired_signals:
                # Adicionar ao conjunto de sinais expirados para evitar duplicatas
                self.expired_signals.add(signal_id)

                # Marcar como fechado e adicionar à lista de remoção
                signal['is_closed'] = True
                signals_to_remove.append(signal)

                # Registrar a expiração no banco de dados
                if 'db_id' in signal:
                    # Atualizar o status do sinal
                    self.db.update_signal_status(
                        signal_id=signal['db_id'],
                        status='EXPIRED',
                        result='BREAKEVEN'
                    )

                    # Registrar a atualização
                    update_id = self.db.save_signal_update(
                        signal_id=signal['db_id'],
                        update_type='EXPIRED'
                    )

                    if update_id > 0:
                        logger.info(f"Expiração do sinal registrada no banco de dados com ID {update_id}")

                continue

            # Obter preço atual
            current_price = self.binance.get_current_price(signal['symbol'])
            if current_price is None:
                continue

            # Calcular profit atual
            price_diff = ((current_price - signal['entry_price']) / signal['entry_price']) * 100
            if signal['signal_type'] == "SHORT":
                price_diff = -price_diff

            # Para sinais de scalp com múltiplos níveis de profit
            if 'notified_levels' in signal:
                # Verificar cada nível de profit não notificado
                for level in SCALP_PROFIT_LEVELS:
                    if level not in signal['notified_levels'] and price_diff >= level:
                        # Formatar mensagem de profit usando o formatter
                        message = self.formatter.format_profit_message(
                            symbol=signal['symbol'],
                            signal_type=signal['signal_type'],
                            leverage=signal['leverage'],
                            current_price=current_price,
                            profit_level=level,
                            entry_price=signal['entry_price']
                        )

                        # Responder à mensagem original do sinal
                        reply_message_id = None
                        if 'message_id' in signal and signal['message_id']:
                            reply_message_id = await self.telegram.send_reply_message(message, signal['message_id'])
                        else:
                            reply_message_id = await self.telegram.send_message(message)

                        signal['notified_levels'].add(level)

                        # Registrar a atualização no banco de dados
                        if 'db_id' in signal:
                            update_id = self.db.save_signal_update(
                                signal_id=signal['db_id'],
                                update_type='TP_HIT',
                                price=current_price,
                                profit_level=level,
                                message_id=reply_message_id
                            )

                            if update_id > 0:
                                logger.info(f"Atualização de profit {level}% registrada no banco de dados com ID {update_id}")

                            # Se atingiu o nível máximo, atualizar o status do sinal
                            if level == max(SCALP_PROFIT_LEVELS):
                                self.db.update_signal_status(
                                    signal_id=signal['db_id'],
                                    status='TP_HIT',
                                    result='WIN',
                                    profit_percentage=level,
                                    hit_level=level
                                )

                        # Se atingiu todos os níveis, marca como fechado
                        if len(signal['notified_levels']) == len(SCALP_PROFIT_LEVELS):
                            signal['is_closed'] = True
                            signals_to_remove.append(signal)
            # Para outros tipos de sinais com um único nível de take profit
            elif 'take_profit' in signal and not signal.get('profit_notified', False):
                # Verificar se atingiu o take profit
                if price_diff >= ((signal['take_profit'] - signal['entry_price']) / signal['entry_price']) * 100:
                    # Formatar mensagem de profit usando o formatter
                    message = self.formatter.format_profit_message(
                        symbol=signal['symbol'],
                        signal_type=signal['signal_type'],
                        leverage=signal['leverage'],
                        current_price=current_price,
                        profit_level=int(price_diff),  # Arredondar para o inteiro mais próximo
                        entry_price=signal['entry_price']
                    )

                    # Responder à mensagem original do sinal
                    reply_message_id = None
                    if 'message_id' in signal and signal['message_id']:
                        reply_message_id = await self.telegram.send_reply_message(message, signal['message_id'])
                    else:
                        reply_message_id = await self.telegram.send_message(message)

                    signal['profit_notified'] = True
                    signal['is_closed'] = True
                    signals_to_remove.append(signal)

                    # Registrar a atualização no banco de dados
                    if 'db_id' in signal:
                        # Salvar a atualização
                        update_id = self.db.save_signal_update(
                            signal_id=signal['db_id'],
                            update_type='TP_HIT',
                            price=current_price,
                            profit_level=int(price_diff),
                            message_id=reply_message_id
                        )

                        if update_id > 0:
                            logger.info(f"Atualização de take profit registrada no banco de dados com ID {update_id}")

                        # Atualizar o status do sinal
                        self.db.update_signal_status(
                            signal_id=signal['db_id'],
                            status='TP_HIT',
                            result='WIN',
                            profit_percentage=price_diff,
                            hit_level=100  # Consideramos como 100% do alvo
                        )
                # Verificar se atingiu o stop loss
                elif price_diff <= ((signal['stop_loss'] - signal['entry_price']) / signal['entry_price']) * 100:
                    message = self.formatter.format_stop_loss_message(
                        symbol=signal['symbol'],
                        signal_type=signal['signal_type'],
                        leverage=signal['leverage'],
                        entry_price=signal['entry_price'],
                        current_price=current_price,
                        loss_percentage=abs(price_diff)
                    )

                    # Responder à mensagem original do sinal
                    reply_message_id = None
                    if 'message_id' in signal and signal['message_id']:
                        reply_message_id = await self.telegram.send_reply_message(message, signal['message_id'])
                    else:
                        reply_message_id = await self.telegram.send_message(message)

                    signal['is_closed'] = True
                    signals_to_remove.append(signal)

                    # Registrar a atualização no banco de dados
                    if 'db_id' in signal:
                        # Salvar a atualização
                        update_id = self.db.save_signal_update(
                            signal_id=signal['db_id'],
                            update_type='SL_HIT',
                            price=current_price,
                            profit_level=int(price_diff),
                            message_id=reply_message_id
                        )

                        if update_id > 0:
                            logger.info(f"Atualização de stop loss registrada no banco de dados com ID {update_id}")

                        # Atualizar o status do sinal
                        self.db.update_signal_status(
                            signal_id=signal['db_id'],
                            status='SL_HIT',
                            result='LOSS',
                            profit_percentage=price_diff
                        )

        # Remove sinais fechados
        for signal in signals_to_remove:
            if signal in self.open_signals:
                self.open_signals.remove(signal)

    async def update_results_task(self):
        """Tarefa para atualizar resultados de sinais periodicamente"""
        try:
            logger.info("Iniciando tarefa de atualização de resultados")

            while True:
                try:
                    # Verificar sinais abertos no banco de dados
                    open_signals = self.db.get_open_signals()

                    if open_signals:
                        logger.info(f"Verificando resultados para {len(open_signals)} sinais abertos")

                        for signal in open_signals:
                            try:
                                # Obter preço atual e máximas/mínimas desde a última verificação
                                symbol = signal['symbol']
                                signal_type = signal['signal_type']
                                entry_price = signal['entry_price']

                                # Armazenar o timestamp da última verificação
                                last_check_time = signal.get('last_check_time')
                                current_time = datetime.datetime.now()

                                if last_check_time is None:
                                    # Primeira verificação, usar o timestamp de criação
                                    last_check_time = datetime.datetime.fromisoformat(signal['created_at'])
                                    signal['last_check_time'] = last_check_time

                                # Obter candles desde a última verificação
                                try:
                                    # Converter para timestamp em milissegundos
                                    start_ts = int(last_check_time.timestamp() * 1000)
                                    end_ts = int(current_time.timestamp() * 1000)

                                    # Obter candles de 1 minuto
                                    candles = self.binance.get_historical_klines(
                                        symbol=symbol,
                                        interval='1m',
                                        start_ts=start_ts,
                                        end_ts=end_ts,
                                        lookback_days=None
                                    )

                                    # Atualizar o timestamp da última verificação
                                    signal['last_check_time'] = current_time

                                    # Determinar preço atual e extremos (máxima/mínima)
                                    current_price = self.binance.get_current_price(symbol)

                                    if current_price is None:
                                        logger.warning(f"Não foi possível obter o preço atual para {symbol}")
                                        continue

                                    # Se temos candles, verificar máximas/mínimas
                                    if isinstance(candles, pd.DataFrame) and not candles.empty:
                                        if signal_type == "LONG":
                                            # Para LONG, nos interessa a máxima atingida
                                            highest_price = candles['high'].max()
                                            # Usar o maior entre o preço atual e a máxima para verificação
                                            check_price = max(current_price, highest_price)
                                        else:  # SHORT
                                            # Para SHORT, nos interessa a mínima atingida
                                            lowest_price = candles['low'].min()
                                            # Usar o menor entre o preço atual e a mínima para verificação
                                            check_price = min(current_price, lowest_price)
                                    else:
                                        # Se não temos candles, usar apenas o preço atual
                                        check_price = current_price

                                    # Calcular lucro/prejuízo com base no preço de verificação
                                    price_diff = ((check_price - entry_price) / entry_price) * 100
                                    if signal_type == "SHORT":
                                        price_diff = -price_diff

                                except Exception as e:
                                    logger.error(f"Erro ao obter candles para {symbol}: {e}")
                                    # Fallback para o método tradicional
                                    current_price = self.binance.get_current_price(symbol)

                                    if current_price is None:
                                        logger.warning(f"Não foi possível obter o preço atual para {symbol}")
                                        continue

                                    # Calcular lucro/prejuízo
                                    price_diff = ((current_price - entry_price) / entry_price) * 100
                                    if signal_type == "SHORT":
                                        price_diff = -price_diff

                                # Verificar se é um sinal de scalp (com múltiplos níveis de take profit)
                                is_scalp = signal['strategy'] == 'Scalp'

                                # Para sinais de scalp, verificar níveis de take profit
                                if is_scalp:
                                    # Verificar se atingiu algum nível de take profit
                                    hit_levels = []

                                    # Reconstruir take_profits do banco de dados
                                    take_profits = {}
                                    if signal['take_profit_1']:
                                        take_profits[25] = signal['take_profit_1']
                                    if signal['take_profit_2']:
                                        take_profits[50] = signal['take_profit_2']
                                    if signal['take_profit_3']:
                                        take_profits[75] = signal['take_profit_3']
                                    if signal['take_profit_4']:
                                        take_profits[100] = signal['take_profit_4']

                                    # Verificar níveis atingidos
                                    for level in SCALP_PROFIT_LEVELS:
                                        # Verificar se o preço atual atingiu o nível de take profit
                                        if signal_type == "LONG" and current_price >= take_profits.get(level, 0):
                                            hit_levels.append(level)
                                        elif signal_type == "SHORT" and current_price <= take_profits.get(level, float('inf')):
                                            hit_levels.append(level)

                                    if hit_levels:
                                        highest_level = max(hit_levels)
                                        # Verificar se este nível já foi notificado
                                        updates = self.db.get_signal_updates(signal['id'], 'TP_HIT')
                                        notified_levels = set()

                                        for update in updates:
                                            if update['profit_level'] is not None:
                                                notified_levels.add(update['profit_level'])

                                        if highest_level not in notified_levels:
                                            # Formatar mensagem
                                            message = self.formatter.format_profit_message(
                                                symbol=symbol,
                                                signal_type=signal_type,
                                                leverage=signal['leverage'],
                                                current_price=current_price,
                                                profit_level=highest_level,
                                                entry_price=entry_price
                                            )

                                            # Enviar mensagem
                                            reply_message_id = None
                                            if signal['message_id']:
                                                reply_message_id = await self.telegram.send_reply_message(message, signal['message_id'])
                                            else:
                                                reply_message_id = await self.telegram.send_message(message)

                                            # Registrar atualização no banco de dados
                                            update_id = self.db.save_signal_update(
                                                signal_id=signal['id'],
                                                update_type='TP_HIT',
                                                price=current_price,
                                                profit_level=highest_level,
                                                message_id=reply_message_id
                                            )

                                            logger.info(f"Notificação de profit {highest_level}% enviada para {symbol} {signal_type}")

                                            # Se for o nível máximo, atualizar o status do sinal
                                            if highest_level == max(SCALP_PROFIT_LEVELS):
                                                self.db.update_signal_status(
                                                    signal_id=signal['id'],
                                                    status='TP_HIT',
                                                    result='WIN',
                                                    profit_percentage=highest_level,
                                                    hit_level=highest_level
                                                )

                                    # Verificar stop loss para scalp
                                    if price_diff <= -3.0:  # Assumindo stop loss em -3%
                                        # Verificar se já foi notificado
                                        updates = self.db.get_signal_updates(signal['id'], 'SL_HIT')
                                        if not updates:
                                            # Formatar mensagem
                                            message = self.formatter.format_stop_loss_message(
                                                symbol=symbol,
                                                signal_type=signal_type,
                                                leverage=signal['leverage'],
                                                entry_price=entry_price,
                                                current_price=current_price,
                                                loss_percentage=abs(price_diff)
                                            )

                                            # Enviar mensagem
                                            reply_message_id = None
                                            if signal['message_id']:
                                                reply_message_id = await self.telegram.send_reply_message(message, signal['message_id'])
                                            else:
                                                reply_message_id = await self.telegram.send_message(message)

                                            # Registrar atualização no banco de dados
                                            update_id = self.db.save_signal_update(
                                                signal_id=signal['id'],
                                                update_type='SL_HIT',
                                                price=current_price,
                                                profit_level=int(price_diff),
                                                message_id=reply_message_id
                                            )

                                            # Atualizar o status do sinal
                                            self.db.update_signal_status(
                                                signal_id=signal['id'],
                                                status='SL_HIT',
                                                result='LOSS',
                                                profit_percentage=price_diff
                                            )

                                            logger.info(f"Notificação de stop loss enviada para {symbol} {signal_type}")
                                else:
                                    # Para outros tipos de sinais, verificar take profit e stop loss
                                    take_profit = signal['take_profit_1']
                                    stop_loss = signal['stop_loss']

                                    # Verificar se atingiu take profit
                                    tp_reached = (signal_type == "LONG" and current_price >= take_profit) or \
                                                (signal_type == "SHORT" and current_price <= take_profit)

                                    if tp_reached:
                                        # Verificar se já foi notificado
                                        updates = self.db.get_signal_updates(signal['id'], 'TP_HIT')
                                        if not updates:
                                            # Formatar mensagem
                                            message = self.formatter.format_profit_message(
                                                symbol=symbol,
                                                signal_type=signal_type,
                                                leverage=signal['leverage'],
                                                current_price=current_price,
                                                profit_level=int(price_diff),
                                                entry_price=entry_price
                                            )

                                            # Enviar mensagem
                                            reply_message_id = None
                                            if signal['message_id']:
                                                reply_message_id = await self.telegram.send_reply_message(message, signal['message_id'])
                                            else:
                                                reply_message_id = await self.telegram.send_message(message)

                                            # Registrar atualização no banco de dados
                                            update_id = self.db.save_signal_update(
                                                signal_id=signal['id'],
                                                update_type='TP_HIT',
                                                price=current_price,
                                                profit_level=int(price_diff),
                                                message_id=reply_message_id
                                            )

                                            # Atualizar o status do sinal
                                            self.db.update_signal_status(
                                                signal_id=signal['id'],
                                                status='TP_HIT',
                                                result='WIN',
                                                profit_percentage=price_diff,
                                                hit_level=100  # Consideramos como 100% do alvo
                                            )

                                            logger.info(f"Notificação de take profit enviada para {symbol} {signal_type}")

                                    # Verificar se atingiu stop loss
                                    sl_reached = (signal_type == "LONG" and current_price <= stop_loss) or \
                                                (signal_type == "SHORT" and current_price >= stop_loss)

                                    if sl_reached:
                                        # Verificar se já foi notificado
                                        updates = self.db.get_signal_updates(signal['id'], 'SL_HIT')
                                        if not updates:
                                            # Formatar mensagem
                                            message = self.formatter.format_stop_loss_message(
                                                symbol=symbol,
                                                signal_type=signal_type,
                                                leverage=signal['leverage'],
                                                entry_price=entry_price,
                                                current_price=current_price,
                                                loss_percentage=abs(price_diff)
                                            )

                                            # Enviar mensagem
                                            reply_message_id = None
                                            if signal['message_id']:
                                                reply_message_id = await self.telegram.send_reply_message(message, signal['message_id'])
                                            else:
                                                reply_message_id = await self.telegram.send_message(message)

                                            # Registrar atualização no banco de dados
                                            update_id = self.db.save_signal_update(
                                                signal_id=signal['id'],
                                                update_type='SL_HIT',
                                                price=current_price,
                                                profit_level=int(price_diff),
                                                message_id=reply_message_id
                                            )

                                            # Atualizar o status do sinal
                                            self.db.update_signal_status(
                                                signal_id=signal['id'],
                                                status='SL_HIT',
                                                result='LOSS',
                                                profit_percentage=price_diff
                                            )

                                            logger.info(f"Notificação de stop loss enviada para {symbol} {signal_type}")
                            except Exception as e:
                                logger.error(f"Erro ao processar sinal {signal['id']} ({signal['symbol']}): {e}")

                    # Verificar sinais expirados
                    await self.check_expired_signals()

                    # Determinar o intervalo de verificação com base nos tipos de sinais abertos
                    has_scalp_signals = any(signal['strategy'] == 'Scalp' for signal in open_signals)

                    if has_scalp_signals:
                        # Se temos sinais de scalp, verificar a cada 1 minuto
                        logger.debug("Sinais de scalp ativos: verificando a cada 1 minuto")
                        await asyncio.sleep(60)
                    else:
                        # Para outros tipos de sinais, verificar a cada 5 minutos
                        logger.debug("Sem sinais de scalp ativos: verificando a cada 5 minutos")
                        await asyncio.sleep(300)

                except Exception as e:
                    logger.error(f"Erro na tarefa de atualização de resultados: {e}")
                    await asyncio.sleep(60)  # Aguardar 1 minuto antes de tentar novamente
        except Exception as e:
            logger.error(f"Erro fatal na tarefa de atualização de resultados: {e}")

    async def check_expired_signals(self):
        """Verifica e atualiza sinais expirados"""
        try:
            # Obter sinais abertos
            open_signals = self.db.get_open_signals()

            current_time = datetime.datetime.now()
            for signal in open_signals:
                # Converter string para datetime
                created_at = datetime.datetime.fromisoformat(signal['created_at'])

                # Determinar tempo de validade com base na estratégia
                validity_hours = 4  # Padrão
                if signal['strategy'] == 'Scalp':
                    validity_hours = 4
                elif signal['strategy'] == 'Breakout':
                    validity_hours = 6
                elif signal['strategy'] == 'Inside Bar':
                    validity_hours = 8
                elif signal['strategy'] == 'MFI':
                    validity_hours = 12
                elif signal['strategy'] == 'Swing':
                    validity_hours = 48

                # Verificar se o sinal expirou
                time_elapsed = (current_time - created_at).total_seconds() / 3600  # Em horas
                if time_elapsed > validity_hours:
                    # Verificar se já foi notificado como expirado
                    updates = self.db.get_signal_updates(signal['id'], 'EXPIRED')
                    if not updates:
                        # Atualizar o status do sinal
                        self.db.update_signal_status(
                            signal_id=signal['id'],
                            status='EXPIRED',
                            result='BREAKEVEN'
                        )

                        # Registrar a atualização
                        update_id = self.db.save_signal_update(
                            signal_id=signal['id'],
                            update_type='EXPIRED'
                        )

                        logger.info(f"Sinal {signal['id']} ({signal['symbol']} {signal['signal_type']}) marcado como expirado")
        except Exception as e:
            logger.error(f"Erro ao verificar sinais expirados: {e}")

    async def check_multi_source_signals(self):
        """Verifica sinais da estratégia Multi-Source"""
        try:
            from config.symbols import ALL_SYMBOLS

            # Usar uma seleção dos melhores símbolos para Multi-Source
            symbols_to_check = ALL_SYMBOLS[:30]  # Top 30 símbolos

            for symbol in symbols_to_check:
                try:
                    signal_type, entry_price, stop_loss, take_profit = self.multi_source_strategy.analyze_symbol(symbol)

                    if signal_type and not self.is_signal_duplicate(symbol, signal_type, entry_price):
                        # Registrar sinal para controle de duplicatas
                        self.register_signal(symbol, signal_type, entry_price)

                        # Criar take profits para Multi-Source
                        take_profits = {
                            40: round(entry_price * (1.02 if signal_type == 'LONG' else 0.98), 6),
                            60: round(entry_price * (1.03 if signal_type == 'LONG' else 0.97), 6),
                            80: round(entry_price * (1.04 if signal_type == 'LONG' else 0.96), 6),
                            100: take_profit
                        }

                        # Formatar e enviar sinal
                        message = self.formatter.format_scalp_signal(  # Usar formato scalp por enquanto
                            symbol, entry_price, take_profits, signal_type
                        )

                        if message:
                            success = await self.telegram.send_message(message)
                            if success:
                                # Salvar no banco de dados
                                signal_id = self.db.save_signal(
                                    symbol, signal_type, "multi_source", entry_price,
                                    stop_loss, take_profit, SIGNAL_VALIDITY_MINUTES
                                )

                                self.signals_sent_today += 1
                                logger.info(f"Sinal Multi-Source enviado: {symbol} {signal_type}")

                                # Aguardar um pouco para evitar spam
                                await asyncio.sleep(2)

                        # Verificar limite diário
                        if self.signals_sent_today >= MAX_SIGNALS_PER_DAY:
                            break

                except Exception as e:
                    logger.error(f"Erro ao verificar {symbol} com Multi-Source: {e}")
                    continue

        except Exception as e:
            logger.error(f"Erro ao verificar sinais multi-source: {e}")

    async def check_volume_analysis_signals(self):
        """Verifica sinais da estratégia Volume Analysis"""
        try:
            from config.symbols import MAJOR_TOKENS, DEFI_TOKENS, GAMING_TOKENS

            # Usar tokens com boa liquidez para análise de volume
            symbols_to_check = MAJOR_TOKENS + DEFI_TOKENS[:10] + GAMING_TOKENS[:10]

            for symbol in symbols_to_check:
                try:
                    signal_type, entry_price, stop_loss, take_profit = self.volume_analysis_strategy.analyze_symbol(symbol)

                    if signal_type and not self.is_signal_duplicate(symbol, signal_type, entry_price):
                        # Registrar sinal para controle de duplicatas
                        self.register_signal(symbol, signal_type, entry_price)

                        # Criar take profits para Volume Analysis
                        take_profits = {
                            40: round(entry_price * (1.015 if signal_type == 'LONG' else 0.985), 6),
                            60: round(entry_price * (1.025 if signal_type == 'LONG' else 0.975), 6),
                            80: round(entry_price * (1.035 if signal_type == 'LONG' else 0.965), 6),
                            100: take_profit
                        }

                        # Formatar e enviar sinal
                        message = self.formatter.format_scalp_signal(  # Usar formato scalp por enquanto
                            symbol, entry_price, take_profits, signal_type
                        )

                        if message:
                            success = await self.telegram.send_message(message)
                            if success:
                                # Salvar no banco de dados
                                signal_id = self.db.save_signal(
                                    symbol, signal_type, "volume_analysis", entry_price,
                                    stop_loss, take_profit, SIGNAL_VALIDITY_MINUTES
                                )

                                self.signals_sent_today += 1
                                logger.info(f"Sinal Volume Analysis enviado: {symbol} {signal_type}")

                                # Aguardar um pouco para evitar spam
                                await asyncio.sleep(2)

                        # Verificar limite diário
                        if self.signals_sent_today >= MAX_SIGNALS_PER_DAY:
                            break

                except Exception as e:
                    logger.error(f"Erro ao verificar {symbol} com Volume Analysis: {e}")
                    continue

        except Exception as e:
            logger.error(f"Erro ao verificar sinais volume analysis: {e}")

    async def check_momentum_signals(self):
        """Verifica sinais da estratégia Momentum"""
        try:
            from config.symbols import MAJOR_TOKENS, MEME_TOKENS, NEW_TRENDING_TOKENS

            # Usar tokens com boa volatilidade para análise de momentum
            symbols_to_check = MAJOR_TOKENS + MEME_TOKENS[:8] + NEW_TRENDING_TOKENS[:12]

            for symbol in symbols_to_check:
                try:
                    signal_type, entry_price, stop_loss, take_profit = self.momentum_strategy.analyze_symbol(symbol)

                    if signal_type and not self.is_signal_duplicate(symbol, signal_type, entry_price):
                        # Registrar sinal para controle de duplicatas
                        self.register_signal(symbol, signal_type, entry_price)

                        # Criar take profits para Momentum
                        take_profits = {
                            40: round(entry_price * (1.02 if signal_type == 'LONG' else 0.98), 6),
                            60: round(entry_price * (1.035 if signal_type == 'LONG' else 0.965), 6),
                            80: round(entry_price * (1.05 if signal_type == 'LONG' else 0.95), 6),
                            100: take_profit
                        }

                        # Formatar e enviar sinal
                        message = self.formatter.format_scalp_signal(  # Usar formato scalp por enquanto
                            symbol, entry_price, take_profits, signal_type
                        )

                        if message:
                            success = await self.telegram.send_message(message)
                            if success:
                                # Salvar no banco de dados
                                signal_id = self.db.save_signal(
                                    symbol, signal_type, "momentum", entry_price,
                                    stop_loss, take_profit, SIGNAL_VALIDITY_MINUTES
                                )

                                self.signals_sent_today += 1
                                logger.info(f"Sinal Momentum enviado: {symbol} {signal_type}")

                                # Aguardar um pouco para evitar spam
                                await asyncio.sleep(2)

                        # Verificar limite diário
                        if self.signals_sent_today >= MAX_SIGNALS_PER_DAY:
                            break

                except Exception as e:
                    logger.error(f"Erro ao verificar {symbol} com Momentum: {e}")
                    continue

        except Exception as e:
            logger.error(f"Erro ao verificar sinais momentum: {e}")

    async def generate_daily_report(self):
        """Gera e envia um relatório diário de desempenho profissional"""
        try:
            # Obter estatísticas do dia
            today = datetime.date.today()
            stats = self.db.get_statistics(today, today)

            if not stats:
                logger.info("Sem estatísticas para hoje")
                return

            stat = stats[0]

            # Calcular métricas
            total_signals = stat['signals_sent']
            profitable_signals = stat['successful_signals']
            win_rate = (profitable_signals / max(1, profitable_signals + stat['failed_signals'])) * 100
            avg_profit = stat['average_profit']

            # Usar formatação profissional
            message = self.formatter.format_daily_report(
                total_signals, profitable_signals, win_rate, avg_profit
            )

            # Enviar mensagem
            await self.telegram.send_message(message)
            logger.info("Relatório diário profissional enviado")
        except Exception as e:
            logger.error(f"Erro ao gerar relatório diário: {e}")

    async def _schedule_report(self, seconds_until_report):
        """Agenda o relatório diário"""
        try:
            # Aguardar até o horário programado
            await asyncio.sleep(seconds_until_report)

            # Gerar relatório
            await self.generate_daily_report()

            # Agendar próximo relatório (24 horas depois)
            asyncio.create_task(self._schedule_report(86400))  # 24 horas em segundos
        except Exception as e:
            logger.error(f"Erro ao agendar relatório: {e}")
            # Tentar novamente em 1 hora
            asyncio.create_task(self._schedule_report(3600))

    async def run(self):
        """Executa o gerador de sinais"""
        logger.info("Iniciando gerador de sinais...")

        # Conectar ao Telegram
        connected = await self.telegram.connect()
        if not connected:
            logger.error("Falha ao conectar ao Telegram. Encerrando.")
            return

        try:
            # Iniciar tarefa de atualização de resultados em paralelo
            results_task = asyncio.create_task(self.update_results_task())

            # Agendar relatório diário para 23:30
            now = datetime.datetime.now()
            report_time = datetime.datetime(now.year, now.month, now.day, 23, 30)

            # Se já passou do horário hoje, agendar para amanhã
            if now > report_time:
                report_time = report_time + datetime.timedelta(days=1)

            # Calcular segundos até o relatório
            seconds_until_report = (report_time - now).total_seconds()

            # Agendar tarefa de relatório
            asyncio.create_task(self._schedule_report(seconds_until_report))

            while True:
                # Resetar contador diário se necessário
                self.reset_daily_counter()

                # OPERAÇÃO 24/7 ATIVADA - Removida verificação de horário
                # Criptomoedas operam 24h, sistema agora funciona continuamente
                logger.debug("Sistema operando 24/7 - verificando oportunidades de trading")

                # Verificar se atingiu o limite diário
                if self.signals_sent_today >= MAX_SIGNALS_PER_DAY:
                    logger.info(f"Limite diário de {MAX_SIGNALS_PER_DAY} sinais atingido. Aguardando próximo dia.")
                    await asyncio.sleep(3600)  # Aguarda 1 hora antes de verificar novamente
                    continue

                # Verificar sinais de cada estratégia - EXPANDIDO COM MÚLTIPLAS FONTES
                await self.check_scalp_signals()
                await self.check_breakout_signals()
                await self.check_inside_bar_signals()
                await self.check_mfi_signals()
                await self.check_swing_signals()
                await self.check_multi_source_signals()
                await self.check_volume_analysis_signals()
                await self.check_momentum_signals()

                # Monitorar sinais abertos
                await self.monitor_open_signals()

                # Aguardar intervalo antes da próxima verificação
                await asyncio.sleep(SIGNAL_INTERVAL_SECONDS)

        except KeyboardInterrupt:
            logger.info("Interrupção do usuário. Encerrando...")
        except Exception as e:
            logger.error(f"Erro no loop principal: {e}", exc_info=True)
        finally:
            # Desconectar do Telegram
            await self.telegram.disconnect()

            # Fechar conexão com o banco de dados
            self.db.close()

            logger.info("Gerador de sinais encerrado.")

async def main():
    generator = SignalGenerator()
    await generator.run()

if __name__ == "__main__":
    asyncio.run(main())