{"ast": null, "code": "import * as React from 'react';\nimport TransitionProgressContext from \"./TransitionProgressContext\";\nexport default function useTransitionProgress() {\n  var progress = React.useContext(TransitionProgressContext);\n  if (progress === undefined) {\n    throw new Error(\"Couldn't find values for transition progress. Are you inside a screen in Native Stack?\");\n  }\n  return progress;\n}", "map": {"version": 3, "names": ["React", "TransitionProgressContext", "useTransitionProgress", "progress", "useContext", "undefined", "Error"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\react-native-screens\\src\\useTransitionProgress.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport TransitionProgressContext from './TransitionProgressContext';\n\nexport default function useTransitionProgress() {\n  const progress = React.useContext(TransitionProgressContext);\n\n  if (progress === undefined) {\n    throw new Error(\n      \"Couldn't find values for transition progress. Are you inside a screen in Native Stack?\",\n    );\n  }\n\n  return progress;\n}\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,OAAOC,yBAAyB;AAEhC,eAAe,SAASC,qBAAqBA,CAAA,EAAG;EAC9C,IAAMC,QAAQ,GAAGH,KAAK,CAACI,UAAU,CAACH,yBAAyB,CAAC;EAE5D,IAAIE,QAAQ,KAAKE,SAAS,EAAE;IAC1B,MAAM,IAAIC,KAAK,CACb,wFACF,CAAC;EACH;EAEA,OAAOH,QAAQ;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}