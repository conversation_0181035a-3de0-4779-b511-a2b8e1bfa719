{"ast": null, "code": "export { default as Icon } from \"./AvatarIcon\";\nexport { default as Image } from \"./AvatarImage\";\nexport { default as Text } from \"./AvatarText\";", "map": {"version": 3, "names": ["default", "Icon", "Image", "Text"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\Avatar\\Avatar.tsx"], "sourcesContent": ["// @component ./AvatarIcon.tsx\nexport { default as Icon } from './AvatarIcon';\n\n// @component ./AvatarImage.tsx\nexport { default as Image } from './AvatarImage';\n\n// @component ./AvatarText.tsx\nexport { default as Text } from './AvatarText';\n"], "mappings": "AACA,SAASA,OAAO,IAAIC,IAAI;AAGxB,SAASD,OAAO,IAAIE,KAAK;AAGzB,SAASF,OAAO,IAAIG,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}