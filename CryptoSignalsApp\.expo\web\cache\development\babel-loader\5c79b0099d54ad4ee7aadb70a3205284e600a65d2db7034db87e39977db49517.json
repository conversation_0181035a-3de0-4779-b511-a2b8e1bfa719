{"ast": null, "code": "import React from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport styles from \"./styles\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nvar Wrapper = function Wrapper(_ref) {\n  var children = _ref.children;\n  return _jsx(View, {\n    style: styles.container,\n    children: children\n  });\n};\nexport default Wrapper;", "map": {"version": 3, "names": ["React", "View", "styles", "jsx", "_jsx", "Wrapper", "_ref", "children", "style", "container"], "sources": ["E:/CryptoSignalsApp/src/components/Wrapper/index.js"], "sourcesContent": ["import React from 'react';\r\nimport { View } from 'react-native';\r\nimport styles from './styles';\r\n\r\nconst Wrapper = ({ children }) => {\r\n  return (\r\n    <View style={styles.container}>\r\n      {children}\r\n    </View>\r\n  )\r\n}\r\n\r\nexport default Wrapper\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,OAAAC,IAAA;AAE1B,OAAOC,MAAM;AAAiB,SAAAC,GAAA,IAAAC,IAAA;AAE9B,IAAMC,OAAO,GAAG,SAAVA,OAAOA,CAAAC,IAAA,EAAqB;EAAA,IAAfC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;EACzB,OACEH,IAAA,CAACH,IAAI;IAACO,KAAK,EAAEN,MAAM,CAACO,SAAU;IAAAF,QAAA,EAC3BA;EAAQ,CACL,CAAC;AAEX,CAAC;AAED,eAAeF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}