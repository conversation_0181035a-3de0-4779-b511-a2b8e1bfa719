{"ast": null, "code": "import URL from 'url-parse';\nexport function getFilename(url) {\n  var _URL = new URL(url, {}),\n    pathname = _URL.pathname;\n  return pathname.substring(pathname.lastIndexOf('/') + 1);\n}\nexport function getFileExtension(url) {\n  var filename = getFilename(url);\n  var dotIndex = filename.lastIndexOf('.');\n  return dotIndex > 0 ? filename.substring(dotIndex) : '';\n}\nexport function getManifestBaseUrl(manifestUrl) {\n  var urlObject = new URL(manifestUrl, {});\n  if (urlObject.protocol === 'exp:') {\n    urlObject.set('protocol', 'http:');\n  } else if (urlObject.protocol === 'exps:') {\n    urlObject.set('protocol', 'https:');\n  }\n  var directory = urlObject.pathname.substring(0, urlObject.pathname.lastIndexOf('/') + 1);\n  urlObject.set('pathname', directory);\n  urlObject.set('query', '');\n  urlObject.set('hash', '');\n  return urlObject.href;\n}", "map": {"version": 3, "names": ["URL", "getFilename", "url", "_URL", "pathname", "substring", "lastIndexOf", "getFileExtension", "filename", "dotIndex", "getManifestBaseUrl", "manifestUrl", "urlObject", "protocol", "set", "directory", "href"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\expo-asset\\src\\AssetUris.ts"], "sourcesContent": ["import URL from 'url-parse';\n\nexport function getFilename(url: string): string {\n  const { pathname } = new URL(url, {});\n  return pathname.substring(pathname.lastIndexOf('/') + 1);\n}\n\nexport function getFileExtension(url: string): string {\n  const filename = getFilename(url);\n  const dotIndex = filename.lastIndexOf('.');\n  // Ignore leading dots for hidden files\n  return dotIndex > 0 ? filename.substring(dotIndex) : '';\n}\n\n/**\n * Returns the base URL from a manifest's URL. For example, given a manifest hosted at\n * https://example.com/app/manifest.json, the base URL would be https://example.com/app/. Query\n * parameters and fragments also are removed.\n *\n * For an Expo-hosted project with a manifest hosted at https://exp.host/@user/project/index.exp, the\n * base URL would be https://exp.host/@user/project.\n *\n * We also normalize the \"exp\" protocol to \"http\" to handle internal URLs with the Expo schemes used\n * to tell the OS to open the URLs in the the Expo client.\n */\nexport function getManifestBaseUrl(manifestUrl: string): string {\n  const urlObject = new URL(manifestUrl, {});\n\n  // Change the scheme to http(s) if it is exp(s)\n  if (urlObject.protocol === 'exp:') {\n    urlObject.set('protocol', 'http:');\n  } else if (urlObject.protocol === 'exps:') {\n    urlObject.set('protocol', 'https:');\n  }\n\n  // Trim filename, query parameters, and fragment, if any\n  const directory = urlObject.pathname.substring(0, urlObject.pathname.lastIndexOf('/') + 1);\n  urlObject.set('pathname', directory);\n  urlObject.set('query', '');\n  urlObject.set('hash', '');\n\n  return urlObject.href;\n}\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,WAAW;AAE3B,OAAM,SAAUC,WAAWA,CAACC,GAAW;EACrC,IAAAC,IAAA,GAAqB,IAAIH,GAAG,CAACE,GAAG,EAAE,EAAE,CAAC;IAA7BE,QAAQ,GAAAD,IAAA,CAARC,QAAQ;EAChB,OAAOA,QAAQ,CAACC,SAAS,CAACD,QAAQ,CAACE,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC1D;AAEA,OAAM,SAAUC,gBAAgBA,CAACL,GAAW;EAC1C,IAAMM,QAAQ,GAAGP,WAAW,CAACC,GAAG,CAAC;EACjC,IAAMO,QAAQ,GAAGD,QAAQ,CAACF,WAAW,CAAC,GAAG,CAAC;EAE1C,OAAOG,QAAQ,GAAG,CAAC,GAAGD,QAAQ,CAACH,SAAS,CAACI,QAAQ,CAAC,GAAG,EAAE;AACzD;AAaA,OAAM,SAAUC,kBAAkBA,CAACC,WAAmB;EACpD,IAAMC,SAAS,GAAG,IAAIZ,GAAG,CAACW,WAAW,EAAE,EAAE,CAAC;EAG1C,IAAIC,SAAS,CAACC,QAAQ,KAAK,MAAM,EAAE;IACjCD,SAAS,CAACE,GAAG,CAAC,UAAU,EAAE,OAAO,CAAC;GACnC,MAAM,IAAIF,SAAS,CAACC,QAAQ,KAAK,OAAO,EAAE;IACzCD,SAAS,CAACE,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC;;EAIrC,IAAMC,SAAS,GAAGH,SAAS,CAACR,QAAQ,CAACC,SAAS,CAAC,CAAC,EAAEO,SAAS,CAACR,QAAQ,CAACE,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EAC1FM,SAAS,CAACE,GAAG,CAAC,UAAU,EAAEC,SAAS,CAAC;EACpCH,SAAS,CAACE,GAAG,CAAC,OAAO,EAAE,EAAE,CAAC;EAC1BF,SAAS,CAACE,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC;EAEzB,OAAOF,SAAS,CAACI,IAAI;AACvB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}