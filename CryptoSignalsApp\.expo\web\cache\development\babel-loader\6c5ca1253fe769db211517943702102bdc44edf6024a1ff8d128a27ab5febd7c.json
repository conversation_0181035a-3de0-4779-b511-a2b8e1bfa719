{"ast": null, "code": "import * as TurboModuleRegistry from \"../TurboModule/TurboModuleRegistry\";\nexport default TurboModuleRegistry.get('NativeAnimatedModule');", "map": {"version": 3, "names": ["TurboModuleRegistry", "get"], "sources": ["C:/Users/<USER>/Desktop/Signals/CryptoSignalsApp/node_modules/react-native-web/dist/vendor/react-native/Animated/NativeAnimatedModule.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\nimport * as TurboModuleRegistry from '../TurboModule/TurboModuleRegistry';\n\n// The config has different keys depending on the type of the Node\n// TODO(*********): Make these types strict\n\nexport default TurboModuleRegistry.get('NativeAnimatedModule');"], "mappings": "AAUA,OAAO,KAAKA,mBAAmB;AAK/B,eAAeA,mBAAmB,CAACC,GAAG,CAAC,sBAAsB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}