#!/usr/bin/env python3
"""
Dashboard Web para Monitoramento do CryptoSignals
Uso: python web_dashboard.py
"""

from flask import Flask, render_template, jsonify
from flask_socketio import SocketIO, emit
import datetime
import threading
import time
from pathlib import Path

# Tentar importar o monitor avançado
try:
    from core.monitor import AdvancedServerMonitor
    print("✅ Monitor avançado importado com sucesso")
except ImportError as e:
    # Fallback se não conseguir importar
    print(f"⚠️ Aviso: Não foi possível importar AdvancedServerMonitor: {e}")
    print("🔄 Usando modo simulado para demonstração")
    AdvancedServerMonitor = None

app = Flask(__name__)
app.config['SECRET_KEY'] = 'cryptosignals_dashboard_2024'
socketio = SocketIO(app, cors_allowed_origins="*")

# Monitor global
monitor = None
monitoring_thread = None
monitoring_active = False

class WebDashboardMonitor:
    """Monitor adaptado para o dashboard web"""

    def __init__(self):
        if AdvancedServerMonitor:
            self.monitor = AdvancedServerMonitor()
        else:
            self.monitor = None

        self.data_dir = Path("dashboard_data")
        self.data_dir.mkdir(exist_ok=True)

        # Cache de dados
        self.latest_data = {
            'services': {},
            'server_metrics': {},
            'alerts': [],
            'logs': [],
            'timestamp': datetime.datetime.now().isoformat()
        }

    def get_service_status(self):
        """Obtém status dos serviços"""
        if not self.monitor:
            # Dados simulados para desenvolvimento
            return {
                'gerador_sinais': {
                    'name': 'Gerador de Sinais Telegram',
                    'status': 'active',
                    'uptime': '2d 14h 32m',
                    'cpu_usage': 15.2,
                    'memory_usage': 45.8,
                    'restart_count': 0,
                    'last_restart': None,
                    'error_count': 0
                },
                'sistema-clientes': {
                    'name': 'Sistema de Clientes Streamlit',
                    'status': 'inactive',
                    'uptime': '0m',
                    'cpu_usage': 0.0,
                    'memory_usage': 0.0,
                    'restart_count': 3,
                    'last_restart': '2024-01-15 10:30:00',
                    'error_count': 1
                }
            }

        # Código real com monitor
        ssh = self.monitor.get_ssh_connection()
        if not ssh:
            return {}

        services_status = {}
        for service_id, service_info in self.monitor.services.items():
            status = self.monitor.check_service_status(ssh, service_id)
            services_status[service_id] = {
                'name': service_info['name'],
                'status': status.get('status', 'unknown'),
                'uptime': self.get_service_uptime(ssh, service_id),
                'cpu_usage': self.parse_cpu_usage(status.get('resource_usage', '')),
                'memory_usage': self.parse_memory_usage(status.get('resource_usage', '')),
                'restart_count': self.get_restart_count(ssh, service_id),
                'last_restart': self.get_last_restart(ssh, service_id),
                'error_count': self.count_recent_errors(ssh, service_id)
            }

        return services_status

    def get_server_metrics(self):
        """Obtém métricas do servidor"""
        if not self.monitor:
            # Dados simulados
            return {
                'cpu_percent': 23.5,
                'memory_percent': 67.2,
                'disk_percent': 45.8,
                'network_io': {'bytes_sent': 1024000, 'bytes_recv': 2048000},
                'load_average': [0.5, 0.7, 0.9],
                'uptime': '15d 8h 42m',
                'processes': 156,
                'connections': 23
            }

        # Código real com monitor
        ssh = self.monitor.get_ssh_connection()
        if not ssh:
            return {}

        # Obter métricas reais
        memory_info = self.monitor.check_memory_usage(ssh)
        disk_info = self.monitor.check_disk_usage(ssh)

        return {
            'cpu_percent': self.parse_cpu_percent(ssh),
            'memory_percent': self.parse_memory_percent(memory_info),
            'disk_percent': self.parse_disk_percent(disk_info),
            'network_io': self.get_network_io(ssh),
            'load_average': self.get_load_average(ssh),
            'uptime': self.get_server_uptime(ssh),
            'processes': self.get_process_count(ssh),
            'connections': self.get_connection_count(ssh)
        }

    def get_recent_logs(self, lines=50):
        """Obtém logs recentes"""
        if not self.monitor:
            # Logs simulados
            return [
                {'timestamp': '2024-01-15 14:30:15', 'level': 'INFO', 'message': 'Sistema iniciado com sucesso'},
                {'timestamp': '2024-01-15 14:30:20', 'level': 'INFO', 'message': 'Conectado ao Telegram'},
                {'timestamp': '2024-01-15 14:30:25', 'level': 'INFO', 'message': 'Monitoramento de mercado ativo'},
                {'timestamp': '2024-01-15 14:31:00', 'level': 'WARNING', 'message': 'Alta volatilidade detectada em BTCUSDT'},
                {'timestamp': '2024-01-15 14:31:30', 'level': 'INFO', 'message': 'Sinal enviado: ETHUSDT LONG'},
            ]

        # Código real
        logs = self.monitor.view_gerador_sinais_logs(lines)
        return self.parse_logs(logs)

    def get_alerts(self):
        """Obtém alertas ativos"""
        if not self.monitor:
            # Alertas simulados
            return [
                {
                    'level': 'WARNING',
                    'service': 'sistema-clientes',
                    'message': 'Serviço inativo há mais de 1 hora',
                    'timestamp': '2024-01-15 13:30:00',
                    'resolved': False
                }
            ]

        # Código real
        return [
            {
                'level': alert.level,
                'service': alert.service,
                'message': alert.message,
                'timestamp': alert.timestamp,
                'resolved': alert.resolved
            }
            for alert in self.monitor.alerts
            if not alert.resolved
        ]

    def update_data(self):
        """Atualiza todos os dados"""
        try:
            self.latest_data = {
                'services': self.get_service_status(),
                'server_metrics': self.get_server_metrics(),
                'alerts': self.get_alerts(),
                'logs': self.get_recent_logs(),
                'timestamp': datetime.datetime.now().isoformat()
            }
            return True
        except Exception as e:
            print(f"Erro ao atualizar dados: {e}")
            return False

    # Métodos auxiliares para parsing
    def parse_cpu_usage(self, resource_usage):
        """Extrai uso de CPU do resource_usage"""
        try:
            if '%CPU' in resource_usage:
                lines = resource_usage.split('\n')
                for line in lines:
                    if '%CPU' not in line and line.strip():
                        parts = line.split()
                        if parts:
                            return float(parts[0])
        except:
            pass
        return 0.0

    def parse_memory_usage(self, resource_usage):
        """Extrai uso de memória do resource_usage"""
        try:
            if '%MEM' in resource_usage:
                lines = resource_usage.split('\n')
                for line in lines:
                    if '%MEM' not in line and line.strip():
                        parts = line.split()
                        if len(parts) > 1:
                            return float(parts[1])
        except:
            pass
        return 0.0

    def parse_logs(self, logs_text):
        """Converte logs de texto para lista estruturada"""
        if not logs_text:
            return []

        log_entries = []
        for line in logs_text.split('\n')[-20:]:  # Últimas 20 linhas
            if line.strip():
                # Tentar extrair timestamp, level e message
                parts = line.split(' - ')
                if len(parts) >= 3:
                    timestamp = parts[0]
                    level = parts[1] if parts[1] in ['INFO', 'WARNING', 'ERROR', 'DEBUG'] else 'INFO'
                    message = ' - '.join(parts[2:])
                else:
                    timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    level = 'INFO'
                    message = line.strip()

                log_entries.append({
                    'timestamp': timestamp,
                    'level': level,
                    'message': message
                })

        return log_entries

    def get_service_uptime(self, ssh, service):
        """Obtém uptime do serviço"""
        try:
            _, stdout, _ = ssh.exec_command(f"systemctl show -p ActiveEnterTimestamp {service}")
            output = stdout.read().decode().strip()
            # Processar timestamp e calcular uptime
            return "N/A"  # Implementar cálculo real
        except:
            return "N/A"

    def get_restart_count(self, ssh, service):
        """Obtém número de restarts do serviço"""
        try:
            _, stdout, _ = ssh.exec_command(f"systemctl show -p NRestarts {service}")
            output = stdout.read().decode().strip()
            if 'NRestarts=' in output:
                return int(output.split('=')[1])
        except:
            pass
        return 0

    def get_last_restart(self, ssh, service):
        """Obtém timestamp do último restart"""
        # Implementar lógica real
        return None

    def count_recent_errors(self, ssh, service):
        """Conta erros recentes nos logs"""
        try:
            _, stdout, _ = ssh.exec_command(f"journalctl -u {service} --since '1 hour ago' | grep -c ERROR")
            output = stdout.read().decode().strip()
            return int(output) if output.isdigit() else 0
        except:
            return 0

    def parse_cpu_percent(self, ssh):
        """Obtém percentual de CPU do servidor"""
        try:
            _, stdout, _ = ssh.exec_command("top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | awk -F'%' '{print $1}'")
            output = stdout.read().decode().strip()
            return float(output) if output else 0.0
        except:
            return 0.0

    def parse_memory_percent(self, memory_info):
        """Extrai percentual de memória do output do free"""
        try:
            lines = memory_info.split('\n')
            for line in lines:
                if 'Mem:' in line:
                    parts = line.split()
                    if len(parts) >= 3:
                        total = float(parts[1])
                        used = float(parts[2])
                        return (used / total) * 100 if total > 0 else 0.0
        except:
            pass
        return 0.0

    def parse_disk_percent(self, disk_info):
        """Extrai percentual de disco do output do df"""
        try:
            lines = disk_info.split('\n')
            for line in lines:
                if '/' in line and '%' in line:
                    parts = line.split()
                    for part in parts:
                        if '%' in part:
                            return float(part.replace('%', ''))
        except:
            pass
        return 0.0

    def get_network_io(self, ssh):
        """Obtém informações de I/O de rede"""
        try:
            _, stdout, _ = ssh.exec_command("cat /proc/net/dev | grep eth0")
            output = stdout.read().decode().strip()
            if output:
                parts = output.split()
                if len(parts) >= 10:
                    return {
                        'bytes_recv': int(parts[1]),
                        'bytes_sent': int(parts[9])
                    }
        except:
            pass
        return {'bytes_recv': 0, 'bytes_sent': 0}

    def get_load_average(self, ssh):
        """Obtém load average do servidor"""
        try:
            _, stdout, _ = ssh.exec_command("uptime | awk -F'load average:' '{print $2}'")
            output = stdout.read().decode().strip()
            if output:
                loads = [float(x.strip()) for x in output.split(',')[:3]]
                return loads
        except:
            pass
        return [0.0, 0.0, 0.0]

    def get_server_uptime(self, ssh):
        """Obtém uptime do servidor"""
        try:
            _, stdout, _ = ssh.exec_command("uptime -p")
            output = stdout.read().decode().strip()
            return output.replace('up ', '') if output else 'N/A'
        except:
            return 'N/A'

    def get_process_count(self, ssh):
        """Obtém número de processos"""
        try:
            _, stdout, _ = ssh.exec_command("ps aux | wc -l")
            output = stdout.read().decode().strip()
            return int(output) - 1 if output.isdigit() else 0  # -1 para remover header
        except:
            return 0

    def get_connection_count(self, ssh):
        """Obtém número de conexões ativas"""
        try:
            _, stdout, _ = ssh.exec_command("netstat -an | grep ESTABLISHED | wc -l")
            output = stdout.read().decode().strip()
            return int(output) if output.isdigit() else 0
        except:
            return 0

def background_monitoring():
    """Thread de monitoramento em background"""
    global monitoring_active

    while monitoring_active:
        try:
            if monitor.update_data():
                # Emitir dados atualizados via WebSocket
                socketio.emit('data_update', monitor.latest_data)

            time.sleep(10)  # Atualizar a cada 10 segundos

        except Exception as e:
            print(f"Erro no monitoramento: {e}")
            time.sleep(30)  # Aguardar mais tempo em caso de erro

@app.route('/')
def dashboard():
    """Página principal do dashboard"""
    return render_template('dashboard.html')

@app.route('/api/data')
def get_data():
    """API para obter dados atuais"""
    if monitor:
        monitor.update_data()
        return jsonify(monitor.latest_data)
    return jsonify({'error': 'Monitor não disponível'})

@app.route('/api/logs/<int:lines>')
def get_logs(lines):
    """API para obter logs com número específico de linhas"""
    if monitor:
        logs = monitor.get_recent_logs(lines)
        return jsonify({'logs': logs})
    return jsonify({'error': 'Monitor não disponível'})

@socketio.on('connect')
def handle_connect():
    """Cliente conectado via WebSocket"""
    print('Cliente conectado ao dashboard')
    if monitor:
        monitor.update_data()
        emit('data_update', monitor.latest_data)

@socketio.on('disconnect')
def handle_disconnect():
    """Cliente desconectado"""
    print('Cliente desconectado do dashboard')

@socketio.on('request_update')
def handle_request_update():
    """Cliente solicitou atualização manual"""
    if monitor:
        if monitor.update_data():
            socketio.emit('data_update', monitor.latest_data)

def main():
    """Função principal"""
    global monitor, monitoring_thread, monitoring_active

    print("🚀 Iniciando Dashboard Web do CryptoSignals")
    print("=" * 50)

    # Inicializar monitor
    monitor = WebDashboardMonitor()

    # Iniciar thread de monitoramento
    monitoring_active = True
    monitoring_thread = threading.Thread(target=background_monitoring, daemon=True)
    monitoring_thread.start()

    print("✅ Monitor inicializado")
    print("✅ Thread de monitoramento iniciada")
    print("\n📊 Dashboard disponível em:")
    print("   http://localhost:5000")
    print("   http://127.0.0.1:5000")
    print("\n🔄 Atualizações em tempo real ativas")
    print("⏹️  Pressione Ctrl+C para parar")
    print("=" * 50)

    try:
        # Iniciar servidor Flask com SocketIO
        socketio.run(app, host='0.0.0.0', port=5000, debug=False)
    except KeyboardInterrupt:
        print("\n🛑 Parando dashboard...")
        monitoring_active = False
        if monitoring_thread:
            monitoring_thread.join(timeout=5)
        print("✅ Dashboard parado")

if __name__ == '__main__':
    main()
