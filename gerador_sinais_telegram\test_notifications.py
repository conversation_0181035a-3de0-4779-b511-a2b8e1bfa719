#!/usr/bin/env python3
"""
Script de teste para o sistema de notificações
"""

import sys
import os
import logging
from datetime import datetime

# Adicionar o diretório raiz ao path
sys.path.append(os.path.dirname(__file__))

from notifications.notification_manager import NotificationManager

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_notification_system():
    """Testa o sistema de notificações"""
    print("🔔 TESTE DO SISTEMA DE NOTIFICAÇÕES")
    print("=" * 50)
    
    try:
        # Inicializar o gerenciador de notificações
        print("📋 Inicializando NotificationManager...")
        notification_manager = NotificationManager()
        
        # Verificar status
        print("\n📊 Status do sistema:")
        status = notification_manager.get_status()
        print(f"  - Sistema habilitado: {status['enabled']}")
        print(f"  - Email habilitado: {status['email']['enabled']}")
        print(f"  - Push habilitado: {status['push']['enabled']}")
        print(f"  - Webhook habilitado: {status['webhook']['enabled']}")
        print(f"  - Destinatários de email: {status['email']['recipients_count']}")
        print(f"  - Assinaturas push: {status['push']['subscriptions_count']}")
        print(f"  - Webhooks configurados: {status['webhook']['webhooks_count']}")
        
        # Dados de teste para sinal
        test_signal_data = {
            'symbol': 'BTCUSDT',
            'signal_type': 'LONG',
            'strategy': 'Test Strategy',
            'entry_price': 43250.50,
            'stop_loss': 42169.24,
            'take_profit': 44989.50,
            'leverage': 20,
            'confidence': 85,
            'timeframe': '1H'
        }
        
        print(f"\n🎯 Testando notificação de sinal:")
        print(f"  - Symbol: {test_signal_data['symbol']}")
        print(f"  - Type: {test_signal_data['signal_type']}")
        print(f"  - Strategy: {test_signal_data['strategy']}")
        
        # Testar notificação de sinal
        result = notification_manager.notify_new_signal(test_signal_data)
        print(f"  - Resultado: {result}")
        
        # Dados de teste para performance
        test_performance_data = {
            'period': 'Daily',
            'total_signals': 25,
            'win_rate': 72.5,
            'total_profit': 8.3,
            'active_strategies': 8,
            'best_strategy': {
                'name': 'Multi-Source',
                'win_rate': 78.2
            },
            'top_signals': [
                {'symbol': 'ETHUSDT', 'profit': 12.5},
                {'symbol': 'BNBUSDT', 'profit': 9.8},
                {'symbol': 'ADAUSDT', 'profit': 7.2}
            ]
        }
        
        print(f"\n📊 Testando relatório de performance:")
        print(f"  - Período: {test_performance_data['period']}")
        print(f"  - Win Rate: {test_performance_data['win_rate']}%")
        print(f"  - Total Profit: {test_performance_data['total_profit']}%")
        
        # Testar relatório de performance
        result = notification_manager.notify_performance_report(test_performance_data)
        print(f"  - Resultado: {result}")
        
        # Testar notificação do sistema
        print(f"\n🔔 Testando notificação do sistema:")
        result = notification_manager.notify_system_event(
            "Sistema de notificações testado com sucesso!", 
            "success"
        )
        print(f"  - Resultado: {result}")
        
        # Testar todas as notificações
        print(f"\n🧪 Testando conectividade:")
        test_results = notification_manager.test_all_notifications()
        print(f"  - Resultados dos testes: {test_results}")
        
        print(f"\n✅ TESTE CONCLUÍDO COM SUCESSO!")
        print("=" * 50)
        
        return True
        
    except Exception as e:
        print(f"\n❌ ERRO NO TESTE: {e}")
        logger.error(f"Erro no teste de notificações: {e}")
        return False

def test_individual_components():
    """Testa componentes individuais"""
    print("\n🔧 TESTE DE COMPONENTES INDIVIDUAIS")
    print("=" * 50)
    
    try:
        # Testar EmailNotifier
        print("📧 Testando EmailNotifier...")
        from notifications.email_notifier import EmailNotifier
        
        email_config = {
            'enabled': False,  # Desabilitado para teste
            'smtp_server': 'smtp.gmail.com',
            'smtp_port': 587,
            'email': '<EMAIL>',
            'password': 'test_password'
        }
        
        email_notifier = EmailNotifier(email_config)
        print(f"  - EmailNotifier inicializado: {email_notifier.enabled}")
        
        # Testar PushNotifier
        print("📱 Testando PushNotifier...")
        from notifications.push_notifier import PushNotifier
        
        push_config = {
            'enabled': False,  # Desabilitado para teste
            'vapid_private_key': 'test_key',
            'vapid_public_key': 'test_key'
        }
        
        push_notifier = PushNotifier(push_config)
        print(f"  - PushNotifier inicializado: {push_notifier.enabled}")
        print(f"  - Assinaturas: {push_notifier.get_subscription_count()}")
        
        # Testar WebhookNotifier
        print("🔗 Testando WebhookNotifier...")
        from notifications.webhook_notifier import WebhookNotifier
        
        webhook_config = {
            'enabled': False,  # Desabilitado para teste
            'webhooks': []
        }
        
        webhook_notifier = WebhookNotifier(webhook_config)
        print(f"  - WebhookNotifier inicializado: {webhook_notifier.enabled}")
        print(f"  - Webhooks: {webhook_notifier.get_webhook_count()}")
        
        print("✅ Todos os componentes testados com sucesso!")
        return True
        
    except Exception as e:
        print(f"❌ Erro no teste de componentes: {e}")
        logger.error(f"Erro no teste de componentes: {e}")
        return False

def show_configuration_example():
    """Mostra exemplo de configuração"""
    print("\n⚙️ EXEMPLO DE CONFIGURAÇÃO")
    print("=" * 50)
    
    print("""
Para configurar o sistema de notificações, edite o arquivo:
config/notifications.json

Exemplo de configuração completa:

{
  "enabled": true,
  "email": {
    "enabled": true,
    "smtp_server": "smtp.gmail.com",
    "smtp_port": 587,
    "email": "<EMAIL>",
    "password": "sua_senha_de_app",
    "recipients": ["<EMAIL>", "<EMAIL>"]
  },
  "webhook": {
    "enabled": true,
    "webhooks": [
      {
        "name": "Discord Trading",
        "type": "discord",
        "url": "https://discord.com/api/webhooks/SEU_WEBHOOK_ID/SEU_TOKEN",
        "enabled": true,
        "events": {
          "signals": true,
          "performance": true,
          "system": true
        }
      }
    ]
  },
  "filters": {
    "min_confidence": 75,
    "strategies": ["Scalp", "Breakout"],
    "symbols": ["BTCUSDT", "ETHUSDT"],
    "signal_types": ["LONG", "SHORT"]
  }
}

📧 Para email Gmail:
1. Ative a verificação em 2 etapas
2. Gere uma senha de app específica
3. Use a senha de app no campo "password"

🔗 Para Discord:
1. Vá nas configurações do canal
2. Integrações > Webhooks > Novo Webhook
3. Copie a URL do webhook

📱 Para notificações push:
1. Configure as chaves VAPID
2. Implemente service worker no frontend
3. Registre assinaturas via API
    """)

if __name__ == "__main__":
    print("🚀 INICIANDO TESTES DO SISTEMA DE NOTIFICAÇÕES")
    print("=" * 60)
    
    # Teste principal
    success1 = test_notification_system()
    
    # Teste de componentes
    success2 = test_individual_components()
    
    # Mostrar exemplo de configuração
    show_configuration_example()
    
    # Resultado final
    if success1 and success2:
        print("\n🎉 TODOS OS TESTES PASSARAM!")
        print("O sistema de notificações está funcionando corretamente.")
    else:
        print("\n⚠️ ALGUNS TESTES FALHARAM!")
        print("Verifique os logs para mais detalhes.")
    
    print("\n📋 PRÓXIMOS PASSOS:")
    print("1. Configure suas credenciais em config/notifications.json")
    print("2. Teste com dados reais")
    print("3. Configure webhooks do Discord/Slack")
    print("4. Adicione destinatários de email")
    print("5. Configure filtros personalizados")
    
    print("\n🔗 APIs disponíveis:")
    print("- GET  /api/notifications/status")
    print("- POST /api/notifications/test")
    print("- POST /api/notifications/email/recipients")
    print("- POST /api/notifications/push/subscribe")
    print("- POST /api/notifications/webhook")
    print("- POST /api/notifications/filters")
