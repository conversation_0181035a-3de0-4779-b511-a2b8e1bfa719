import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
  },
  welcomeText: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  infoText: {
    fontSize: 16,
    marginBottom: 32,
    textAlign: 'center',
  },
  input: {
    width: '100%',
    padding: 12,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 4,
    marginBottom: 16,
  },
  createAccountButton: {
    width: '100%',
    padding: 12,
    backgroundColor: '#007BFF',
    borderRadius: 4,
    alignItems: 'center',
    marginBottom: 16,
  },
  createAccountButtonText: {
    color: '#fff',
    fontSize: 16,
  },
  goBackButton: {
    width: '100%',
    padding: 12,
    backgroundColor: '#ccc',
    borderRadius: 4,
    alignItems: 'center',
  },
  goBackButtonText: {
    color: '#333',
    fontSize: 16,
  },
});

export default styles;
