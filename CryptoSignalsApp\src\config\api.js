/**
 * Configurações da API
 */

// URL base da API do dashboard backend
export const API_CONFIG = {
  // Para desenvolvimento local
  BASE_URL: 'http://localhost:5000/api',

  // Para produção (substitua pelo IP/domínio do seu servidor)
  // BASE_URL: 'http://SEU_IP:5000/api',

  // Timeout para requisições (em ms)
  TIMEOUT: 10000,

  // Intervalo para retry em caso de erro (em ms)
  RETRY_DELAY: 2000,

  // Número máximo de tentativas
  MAX_RETRIES: 3
};

// Configurações específicas por ambiente
export const getApiConfig = () => {
  // Detectar se está em desenvolvimento ou produção
  const isDevelopment = __DEV__;

  if (isDevelopment) {
    return {
      ...API_CONFIG,
      BASE_URL: 'http://localhost:5000/api'
    };
  }

  // Em produção, você pode configurar diferentes URLs
  return {
    ...API_CONFIG,
    BASE_URL: 'http://localhost:5000/api' // Altere para seu servidor de produção
  };
};

// Endpoints disponíveis
export const ENDPOINTS = {
  CHANNELS: '/channels',
  SIGNALS: '/signals',
  CHANNEL_SIGNALS: (channelId) => `/channels/${channelId}/signals`,
  OVERVIEW: '/overview',
  HEALTH: '/health',
  PAYMENTS: '/payments',
  PAYMENT_STATUS: (paymentId) => `/payments/${paymentId}/status`,
  SUBSCRIPTION: '/subscription'
};

// Configurações de pagamento
export const PAYMENT_CONFIG = {
  // Wallet para receber pagamentos USDT
  USDT_WALLET: '******************************************',

  // Redes suportadas
  SUPPORTED_NETWORKS: {
    BSC: {
      name: 'Binance Smart Chain (BEP20)',
      chainId: 56,
      rpcUrl: 'https://bsc-dataseed.binance.org/',
      explorerUrl: 'https://bscscan.com',
      usdtContract: '******************************************'
    },
    ETH: {
      name: 'Ethereum (ERC20)',
      chainId: 1,
      rpcUrl: 'https://mainnet.infura.io/v3/YOUR_INFURA_KEY',
      explorerUrl: 'https://etherscan.io',
      usdtContract: '******************************************'
    }
  },

  // Preços dos planos em USDT
  PLAN_PRICES: {
    pro: 29,
    elite: 79
  },

  // Timeout para confirmação de pagamento (em minutos)
  PAYMENT_TIMEOUT: 30
};
