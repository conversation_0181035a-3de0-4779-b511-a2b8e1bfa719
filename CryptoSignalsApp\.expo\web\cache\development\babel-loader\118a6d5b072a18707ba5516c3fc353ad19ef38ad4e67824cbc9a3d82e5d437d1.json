{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport Touchable from \"react-native-web/dist/exports/Touchable\";\nvar PRESS_RETENTION_OFFSET = {\n  top: 20,\n  left: 20,\n  right: 20,\n  bottom: 30\n};\nvar Mixin = Touchable.Mixin;\nvar _touchableHandleStartShouldSetResponder = Mixin.touchableHandleStartShouldSetResponder,\n  _touchableHandleResponderTerminationRequest = Mixin.touchableHandleResponderTerminationRequest,\n  _touchableHandleResponderGrant = Mixin.touchableHandleResponderGrant,\n  _touchableHandleResponderMove = Mixin.touchableHandleResponderMove,\n  _touchableHandleResponderRelease = Mixin.touchableHandleResponderRelease,\n  _touchableHandleResponderTerminate = Mixin.touchableHandleResponderTerminate,\n  touchableGetInitialState = Mixin.touchableGetInitialState;\nvar SvgTouchableMixin = _objectSpread(_objectSpread({}, Mixin), {}, {\n  touchableHandleStartShouldSetResponder: function touchableHandleStartShouldSetResponder(e) {\n    var onStartShouldSetResponder = this.props.onStartShouldSetResponder;\n    if (onStartShouldSetResponder) {\n      return onStartShouldSetResponder(e);\n    } else {\n      return _touchableHandleStartShouldSetResponder.call(this, e);\n    }\n  },\n  touchableHandleResponderTerminationRequest: function touchableHandleResponderTerminationRequest(e) {\n    var onResponderTerminationRequest = this.props.onResponderTerminationRequest;\n    if (onResponderTerminationRequest) {\n      return onResponderTerminationRequest(e);\n    } else {\n      return _touchableHandleResponderTerminationRequest.call(this, e);\n    }\n  },\n  touchableHandleResponderGrant: function touchableHandleResponderGrant(e) {\n    var onResponderGrant = this.props.onResponderGrant;\n    if (onResponderGrant) {\n      return onResponderGrant(e);\n    } else {\n      return _touchableHandleResponderGrant.call(this, e);\n    }\n  },\n  touchableHandleResponderMove: function touchableHandleResponderMove(e) {\n    var onResponderMove = this.props.onResponderMove;\n    if (onResponderMove) {\n      return onResponderMove(e);\n    } else {\n      return _touchableHandleResponderMove.call(this, e);\n    }\n  },\n  touchableHandleResponderRelease: function touchableHandleResponderRelease(e) {\n    var onResponderRelease = this.props.onResponderRelease;\n    if (onResponderRelease) {\n      return onResponderRelease(e);\n    } else {\n      return _touchableHandleResponderRelease.call(this, e);\n    }\n  },\n  touchableHandleResponderTerminate: function touchableHandleResponderTerminate(e) {\n    var onResponderTerminate = this.props.onResponderTerminate;\n    if (onResponderTerminate) {\n      return onResponderTerminate(e);\n    } else {\n      return _touchableHandleResponderTerminate.call(this, e);\n    }\n  },\n  touchableHandlePress: function touchableHandlePress(e) {\n    var onPress = this.props.onPress;\n    onPress && onPress(e);\n  },\n  touchableHandleActivePressIn: function touchableHandleActivePressIn(e) {\n    var onPressIn = this.props.onPressIn;\n    onPressIn && onPressIn(e);\n  },\n  touchableHandleActivePressOut: function touchableHandleActivePressOut(e) {\n    var onPressOut = this.props.onPressOut;\n    onPressOut && onPressOut(e);\n  },\n  touchableHandleLongPress: function touchableHandleLongPress(e) {\n    var onLongPress = this.props.onLongPress;\n    onLongPress && onLongPress(e);\n  },\n  touchableGetPressRectOffset: function touchableGetPressRectOffset() {\n    var pressRetentionOffset = this.props.pressRetentionOffset;\n    return pressRetentionOffset || PRESS_RETENTION_OFFSET;\n  },\n  touchableGetHitSlop: function touchableGetHitSlop() {\n    var hitSlop = this.props.hitSlop;\n    return hitSlop;\n  },\n  touchableGetHighlightDelayMS: function touchableGetHighlightDelayMS() {\n    var delayPressIn = this.props.delayPressIn;\n    return delayPressIn || 0;\n  },\n  touchableGetLongPressDelayMS: function touchableGetLongPressDelayMS() {\n    var delayLongPress = this.props.delayLongPress;\n    return delayLongPress === 0 ? 0 : delayLongPress || 500;\n  },\n  touchableGetPressOutDelayMS: function touchableGetPressOutDelayMS() {\n    var delayPressOut = this.props.delayPressOut;\n    return delayPressOut || 0;\n  }\n});\nvar touchKeys = Object.keys(SvgTouchableMixin);\nvar touchVals = touchKeys.map(function (key) {\n  return SvgTouchableMixin[key];\n});\nvar numTouchKeys = touchKeys.length;\nexport default (function (target) {\n  for (var i = 0; i < numTouchKeys; i++) {\n    var key = touchKeys[i];\n    var val = touchVals[i];\n    if (typeof val === 'function') {\n      target[key] = val.bind(target);\n    } else {\n      target[key] = val;\n    }\n  }\n  target.state = touchableGetInitialState();\n});", "map": {"version": 3, "names": ["PRESS_RETENTION_OFFSET", "top", "left", "right", "bottom", "Mixin", "Touchable", "touchableHandleStartShouldSetResponder", "touchableHandleResponderTerminationRequest", "touchableHandleResponderGrant", "touchableHandleResponderMove", "touchableHandleResponderRelease", "touchableHandleResponderTerminate", "touchableGetInitialState", "SvgTouchableMixin", "_objectSpread", "e", "onStartShouldSetResponder", "props", "call", "onResponderTerminationRequest", "onResponderGrant", "onResponderMove", "onResponderRelease", "onResponderTerminate", "touchableHandlePress", "onPress", "touchableHandleActivePressIn", "onPressIn", "touchableHandleActivePressOut", "onPressOut", "touchableHandleLongPress", "onLongPress", "touchableGetPressRectOffset", "pressRetentionOffset", "touchableGetHitSlop", "hitSlop", "touchableGetHighlightDelayMS", "delayPressIn", "touchableGetLongPressDelayMS", "delayLongPress", "touchableGetPressOutDelayMS", "delayPressOut", "touchKeys", "Object", "keys", "touchVals", "map", "key", "numTouchKeys", "length", "target", "i", "val", "bind", "state"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\react-native-svg\\src\\lib\\SvgTouchableMixin.ts"], "sourcesContent": ["// @ts-ignore\nimport type { GestureResponderEvent } from 'react-native';\nimport { Touchable } from 'react-native';\nconst PRESS_RETENTION_OFFSET = { top: 20, left: 20, right: 20, bottom: 30 };\n// @ts-ignore\nconst { Mixin } = Touchable;\nconst {\n  touchableHandleStartShouldSetResponder,\n  touchableHandleResponderTerminationRequest,\n  touchableHandleResponderGrant,\n  touchableHandleResponderMove,\n  touchableHandleResponderRelease,\n  touchableHandleResponderTerminate,\n  touchableGetInitialState,\n} = Mixin;\n\nconst SvgTouchableMixin = {\n  ...Mixin,\n\n  touchableHandleStartShouldSetResponder(e: GestureResponderEvent) {\n    const { onStartShouldSetResponder } = this.props;\n    if (onStartShouldSetResponder) {\n      return onStartShouldSetResponder(e);\n    } else {\n      return touchableHandleStartShouldSetResponder.call(this, e);\n    }\n  },\n\n  touchableHandleResponderTerminationRequest(e: GestureResponderEvent) {\n    const { onResponderTerminationRequest } = this.props;\n    if (onResponderTerminationRequest) {\n      return onResponderTerminationRequest(e);\n    } else {\n      return touchableHandleResponderTerminationRequest.call(this, e);\n    }\n  },\n\n  touchableHandleResponderGrant(e: GestureResponderEvent) {\n    const { onResponderGrant } = this.props;\n    if (onResponderGrant) {\n      return onResponderGrant(e);\n    } else {\n      return touchableHandleResponderGrant.call(this, e);\n    }\n  },\n\n  touchableHandleResponderMove(e: GestureResponderEvent) {\n    const { onResponderMove } = this.props;\n    if (onResponderMove) {\n      return onResponderMove(e);\n    } else {\n      return touchableHandleResponderMove.call(this, e);\n    }\n  },\n\n  touchableHandleResponderRelease(e: GestureResponderEvent) {\n    const { onResponderRelease } = this.props;\n    if (onResponderRelease) {\n      return onResponderRelease(e);\n    } else {\n      return touchableHandleResponderRelease.call(this, e);\n    }\n  },\n\n  touchableHandleResponderTerminate(e: GestureResponderEvent) {\n    const { onResponderTerminate } = this.props;\n    if (onResponderTerminate) {\n      return onResponderTerminate(e);\n    } else {\n      return touchableHandleResponderTerminate.call(this, e);\n    }\n  },\n\n  touchableHandlePress(e: GestureResponderEvent) {\n    const { onPress } = this.props;\n    onPress && onPress(e);\n  },\n\n  touchableHandleActivePressIn(e: GestureResponderEvent) {\n    const { onPressIn } = this.props;\n    onPressIn && onPressIn(e);\n  },\n\n  touchableHandleActivePressOut(e: GestureResponderEvent) {\n    const { onPressOut } = this.props;\n    onPressOut && onPressOut(e);\n  },\n\n  touchableHandleLongPress(e: GestureResponderEvent) {\n    const { onLongPress } = this.props;\n    onLongPress && onLongPress(e);\n  },\n\n  touchableGetPressRectOffset() {\n    const { pressRetentionOffset } = this.props;\n    return pressRetentionOffset || PRESS_RETENTION_OFFSET;\n  },\n\n  touchableGetHitSlop() {\n    const { hitSlop } = this.props;\n    return hitSlop;\n  },\n\n  touchableGetHighlightDelayMS() {\n    const { delayPressIn } = this.props;\n    return delayPressIn || 0;\n  },\n\n  touchableGetLongPressDelayMS() {\n    const { delayLongPress } = this.props;\n    return delayLongPress === 0 ? 0 : delayLongPress || 500;\n  },\n\n  touchableGetPressOutDelayMS() {\n    const { delayPressOut } = this.props;\n    return delayPressOut || 0;\n  },\n};\n\nconst touchKeys = Object.keys(SvgTouchableMixin);\nconst touchVals = touchKeys.map((key) => SvgTouchableMixin[key]);\nconst numTouchKeys = touchKeys.length;\n\nexport default (target: { [x: string]: unknown; state: unknown }) => {\n  for (let i = 0; i < numTouchKeys; i++) {\n    const key = touchKeys[i];\n    const val = touchVals[i];\n    if (typeof val === 'function') {\n      target[key] = val.bind(target);\n    } else {\n      target[key] = val;\n    }\n  }\n  target.state = touchableGetInitialState();\n};\n"], "mappings": ";;;;AAGA,IAAMA,sBAAsB,GAAG;EAAEC,GAAG,EAAE,EAAE;EAAEC,IAAI,EAAE,EAAE;EAAEC,KAAK,EAAE,EAAE;EAAEC,MAAM,EAAE;AAAG,CAAC;AAE3E,IAAQC,KAAA,GAAUC,SAAS,CAAnBD,KAAA;AACR,IACEE,uCAAsC,GAOpCF,KAAK,CAPPE,sCAAsC;EACtCC,2CAA0C,GAMxCH,KAAK,CANPG,0CAA0C;EAC1CC,8BAA6B,GAK3BJ,KAAK,CALPI,6BAA6B;EAC7BC,6BAA4B,GAI1BL,KAAK,CAJPK,4BAA4B;EAC5BC,gCAA+B,GAG7BN,KAAK,CAHPM,+BAA+B;EAC/BC,kCAAiC,GAE/BP,KAAK,CAFPO,iCAAiC;EACjCC,wBAAA,GACER,KAAK,CADPQ,wBAAA;AAGF,IAAMC,iBAAiB,GAAAC,aAAA,CAAAA,aAAA,KAClBV,KAAK;EAERE,sCAAsC,WAAtCA,sCAAsCA,CAACS,CAAwB,EAAE;IAC/D,IAAQC,yBAAA,GAA8B,IAAI,CAACC,KAAK,CAAxCD,yBAAA;IACR,IAAIA,yBAAyB,EAAE;MAC7B,OAAOA,yBAAyB,CAACD,CAAC,CAAC;IACrC,CAAC,MAAM;MACL,OAAOT,uCAAsC,CAACY,IAAI,CAAC,IAAI,EAAEH,CAAC,CAAC;IAC7D;EACF,CAAC;EAEDR,0CAA0C,WAA1CA,0CAA0CA,CAACQ,CAAwB,EAAE;IACnE,IAAQI,6BAAA,GAAkC,IAAI,CAACF,KAAK,CAA5CE,6BAAA;IACR,IAAIA,6BAA6B,EAAE;MACjC,OAAOA,6BAA6B,CAACJ,CAAC,CAAC;IACzC,CAAC,MAAM;MACL,OAAOR,2CAA0C,CAACW,IAAI,CAAC,IAAI,EAAEH,CAAC,CAAC;IACjE;EACF,CAAC;EAEDP,6BAA6B,WAA7BA,6BAA6BA,CAACO,CAAwB,EAAE;IACtD,IAAQK,gBAAA,GAAqB,IAAI,CAACH,KAAK,CAA/BG,gBAAA;IACR,IAAIA,gBAAgB,EAAE;MACpB,OAAOA,gBAAgB,CAACL,CAAC,CAAC;IAC5B,CAAC,MAAM;MACL,OAAOP,8BAA6B,CAACU,IAAI,CAAC,IAAI,EAAEH,CAAC,CAAC;IACpD;EACF,CAAC;EAEDN,4BAA4B,WAA5BA,4BAA4BA,CAACM,CAAwB,EAAE;IACrD,IAAQM,eAAA,GAAoB,IAAI,CAACJ,KAAK,CAA9BI,eAAA;IACR,IAAIA,eAAe,EAAE;MACnB,OAAOA,eAAe,CAACN,CAAC,CAAC;IAC3B,CAAC,MAAM;MACL,OAAON,6BAA4B,CAACS,IAAI,CAAC,IAAI,EAAEH,CAAC,CAAC;IACnD;EACF,CAAC;EAEDL,+BAA+B,WAA/BA,+BAA+BA,CAACK,CAAwB,EAAE;IACxD,IAAQO,kBAAA,GAAuB,IAAI,CAACL,KAAK,CAAjCK,kBAAA;IACR,IAAIA,kBAAkB,EAAE;MACtB,OAAOA,kBAAkB,CAACP,CAAC,CAAC;IAC9B,CAAC,MAAM;MACL,OAAOL,gCAA+B,CAACQ,IAAI,CAAC,IAAI,EAAEH,CAAC,CAAC;IACtD;EACF,CAAC;EAEDJ,iCAAiC,WAAjCA,iCAAiCA,CAACI,CAAwB,EAAE;IAC1D,IAAQQ,oBAAA,GAAyB,IAAI,CAACN,KAAK,CAAnCM,oBAAA;IACR,IAAIA,oBAAoB,EAAE;MACxB,OAAOA,oBAAoB,CAACR,CAAC,CAAC;IAChC,CAAC,MAAM;MACL,OAAOJ,kCAAiC,CAACO,IAAI,CAAC,IAAI,EAAEH,CAAC,CAAC;IACxD;EACF,CAAC;EAEDS,oBAAoB,WAApBA,oBAAoBA,CAACT,CAAwB,EAAE;IAC7C,IAAQU,OAAA,GAAY,IAAI,CAACR,KAAK,CAAtBQ,OAAA;IACRA,OAAO,IAAIA,OAAO,CAACV,CAAC,CAAC;EACvB,CAAC;EAEDW,4BAA4B,WAA5BA,4BAA4BA,CAACX,CAAwB,EAAE;IACrD,IAAQY,SAAA,GAAc,IAAI,CAACV,KAAK,CAAxBU,SAAA;IACRA,SAAS,IAAIA,SAAS,CAACZ,CAAC,CAAC;EAC3B,CAAC;EAEDa,6BAA6B,WAA7BA,6BAA6BA,CAACb,CAAwB,EAAE;IACtD,IAAQc,UAAA,GAAe,IAAI,CAACZ,KAAK,CAAzBY,UAAA;IACRA,UAAU,IAAIA,UAAU,CAACd,CAAC,CAAC;EAC7B,CAAC;EAEDe,wBAAwB,WAAxBA,wBAAwBA,CAACf,CAAwB,EAAE;IACjD,IAAQgB,WAAA,GAAgB,IAAI,CAACd,KAAK,CAA1Bc,WAAA;IACRA,WAAW,IAAIA,WAAW,CAAChB,CAAC,CAAC;EAC/B,CAAC;EAEDiB,2BAA2B,WAA3BA,2BAA2BA,CAAA,EAAG;IAC5B,IAAQC,oBAAA,GAAyB,IAAI,CAAChB,KAAK,CAAnCgB,oBAAA;IACR,OAAOA,oBAAoB,IAAIlC,sBAAsB;EACvD,CAAC;EAEDmC,mBAAmB,WAAnBA,mBAAmBA,CAAA,EAAG;IACpB,IAAQC,OAAA,GAAY,IAAI,CAAClB,KAAK,CAAtBkB,OAAA;IACR,OAAOA,OAAO;EAChB,CAAC;EAEDC,4BAA4B,WAA5BA,4BAA4BA,CAAA,EAAG;IAC7B,IAAQC,YAAA,GAAiB,IAAI,CAACpB,KAAK,CAA3BoB,YAAA;IACR,OAAOA,YAAY,IAAI,CAAC;EAC1B,CAAC;EAEDC,4BAA4B,WAA5BA,4BAA4BA,CAAA,EAAG;IAC7B,IAAQC,cAAA,GAAmB,IAAI,CAACtB,KAAK,CAA7BsB,cAAA;IACR,OAAOA,cAAc,KAAK,CAAC,GAAG,CAAC,GAAGA,cAAc,IAAI,GAAG;EACzD,CAAC;EAEDC,2BAA2B,WAA3BA,2BAA2BA,CAAA,EAAG;IAC5B,IAAQC,aAAA,GAAkB,IAAI,CAACxB,KAAK,CAA5BwB,aAAA;IACR,OAAOA,aAAa,IAAI,CAAC;EAC3B;AAAA,EACD;AAED,IAAMC,SAAS,GAAGC,MAAM,CAACC,IAAI,CAAC/B,iBAAiB,CAAC;AAChD,IAAMgC,SAAS,GAAGH,SAAS,CAACI,GAAG,CAAE,UAAAC,GAAG;EAAA,OAAKlC,iBAAiB,CAACkC,GAAG,CAAC;AAAA,EAAC;AAChE,IAAMC,YAAY,GAAGN,SAAS,CAACO,MAAM;AAErC,gBAAgB,UAAAC,MAAgD,EAAK;EACnE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,YAAY,EAAEG,CAAC,EAAE,EAAE;IACrC,IAAMJ,GAAG,GAAGL,SAAS,CAACS,CAAC,CAAC;IACxB,IAAMC,GAAG,GAAGP,SAAS,CAACM,CAAC,CAAC;IACxB,IAAI,OAAOC,GAAG,KAAK,UAAU,EAAE;MAC7BF,MAAM,CAACH,GAAG,CAAC,GAAGK,GAAG,CAACC,IAAI,CAACH,MAAM,CAAC;IAChC,CAAC,MAAM;MACLA,MAAM,CAACH,GAAG,CAAC,GAAGK,GAAG;IACnB;EACF;EACAF,MAAM,CAACI,KAAK,GAAG1C,wBAAwB,EAAE;AAC3C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}