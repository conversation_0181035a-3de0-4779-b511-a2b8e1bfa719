# ========================================
# SCRIPT DE ATUALIZAÇÃO AUTOMÁTICA - WINDOWS
# CryptoSignals - Servidor de Produção
# ========================================

# Configurações do servidor
$SERVER_IP = "**************"
$SERVER_USER = "root"
$SERVER_PASSWORD = "h4*ls:FtJw0e"
$PROJECT_PATH = "/opt/gerador_sinais_telegram"

Write-Host "========================================" -ForegroundColor Blue
Write-Host "  CRYPTOSIGNALS - ATUALIZAÇÃO SERVIDOR" -ForegroundColor Blue
Write-Host "========================================" -ForegroundColor Blue
Write-Host ""

# Verificar se o Plink está disponível (PuTTY)
function Test-Plink {
    try {
        $null = Get-Command plink -ErrorAction Stop
        return $true
    }
    catch {
        Write-Host "❌ Plink (PuTTY) não encontrado!" -ForegroundColor Red
        Write-Host "Baixe e instale PuTTY de: https://www.putty.org/" -ForegroundColor Yellow
        Write-Host "Ou use o script manual update_server_manual.bat" -ForegroundColor Yellow
        return $false
    }
}

# Função para executar comandos no servidor
function Invoke-RemoteCommand {
    param([string]$Command)
    
    Write-Host "Executando: $Command" -ForegroundColor Yellow
    
    # Usar plink para executar comando
    $result = & plink -ssh -batch -pw $SERVER_PASSWORD "$SERVER_USER@$SERVER_IP" $Command
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "⚠️ Comando retornou código de erro: $LASTEXITCODE" -ForegroundColor Yellow
    }
    
    return $result
}

# Verificar dependências
Write-Host "🔍 Verificando dependências..." -ForegroundColor Blue
if (-not (Test-Plink)) {
    exit 1
}

# Testar conexão
Write-Host "🔗 Testando conexão com servidor..." -ForegroundColor Blue
try {
    $testResult = & plink -ssh -batch -pw $SERVER_PASSWORD "$SERVER_USER@$SERVER_IP" "echo 'Conexão OK'"
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Conexão estabelecida com sucesso!" -ForegroundColor Green
    } else {
        throw "Falha na conexão"
    }
}
catch {
    Write-Host "❌ Falha na conexão com o servidor!" -ForegroundColor Red
    Write-Host "Verifique:" -ForegroundColor Yellow
    Write-Host "  - Conexão com internet"
    Write-Host "  - IP do servidor: $SERVER_IP"
    Write-Host "  - Credenciais de acesso"
    exit 1
}

Write-Host ""

# Parar o serviço
Write-Host "🛑 Parando serviço CryptoSignals..." -ForegroundColor Blue
Invoke-RemoteCommand "systemctl stop gerador_sinais.service"

# Fazer backup das configurações
Write-Host "💾 Fazendo backup das configurações..." -ForegroundColor Blue
$backupDate = Get-Date -Format "yyyyMMdd_HHmmss"
Invoke-RemoteCommand "cd $PROJECT_PATH && cp .env .env.backup.$backupDate 2>/dev/null || echo 'Arquivo .env não encontrado'"

# Atualizar código do repositório
Write-Host "📥 Atualizando código do repositório..." -ForegroundColor Blue
Invoke-RemoteCommand "cd $PROJECT_PATH && git stash && git pull origin main"

# Atualizar dependências se necessário
Write-Host "📦 Verificando e atualizando dependências..." -ForegroundColor Blue
Invoke-RemoteCommand "cd $PROJECT_PATH && source venv/bin/activate && pip install --upgrade pip"
Invoke-RemoteCommand "cd $PROJECT_PATH && source venv/bin/activate && pip install pandas-ta finta --upgrade"

# Verificar se o arquivo .env existe
Write-Host "⚙️ Verificando configurações..." -ForegroundColor Blue
Invoke-RemoteCommand "cd $PROJECT_PATH && ls -la .env || echo 'ATENÇÃO: Arquivo .env não encontrado!'"

# Reiniciar o serviço
Write-Host "🚀 Reiniciando serviço CryptoSignals..." -ForegroundColor Blue
Invoke-RemoteCommand "systemctl daemon-reload"
Invoke-RemoteCommand "systemctl restart gerador_sinais.service"

# Aguardar um momento para o serviço inicializar
Write-Host "⏳ Aguardando inicialização do serviço..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

# Verificar status do serviço
Write-Host "📊 Verificando status do serviço..." -ForegroundColor Blue
Invoke-RemoteCommand "systemctl status gerador_sinais.service --no-pager -l"

# Verificar logs recentes
Write-Host "📋 Verificando logs recentes..." -ForegroundColor Blue
Invoke-RemoteCommand "journalctl -u gerador_sinais.service --no-pager -l -n 20"

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "  ✅ ATUALIZAÇÃO CONCLUÍDA!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Comandos úteis para monitoramento:" -ForegroundColor Yellow
Write-Host ""
Write-Host "Ver logs em tempo real:" -ForegroundColor Blue
Write-Host "  plink -ssh -pw $SERVER_PASSWORD $SERVER_USER@$SERVER_IP"
Write-Host "  journalctl -u gerador_sinais.service -f"
Write-Host ""
Write-Host "Ver status do serviço:" -ForegroundColor Blue
Write-Host "  plink -ssh -pw $SERVER_PASSWORD $SERVER_USER@$SERVER_IP"
Write-Host "  systemctl status gerador_sinais.service"
Write-Host ""
Write-Host "Reiniciar serviço manualmente:" -ForegroundColor Blue
Write-Host "  plink -ssh -pw $SERVER_PASSWORD $SERVER_USER@$SERVER_IP"
Write-Host "  systemctl restart gerador_sinais.service"
Write-Host ""

# Perguntar se quer ver logs em tempo real
$response = Read-Host "Deseja ver os logs em tempo real? (y/n)"
if ($response -eq "y" -or $response -eq "Y") {
    Write-Host "📋 Mostrando logs em tempo real (Ctrl+C para sair)..." -ForegroundColor Blue
    & plink -ssh -pw $SERVER_PASSWORD "$SERVER_USER@$SERVER_IP" "journalctl -u gerador_sinais.service -f"
}

Write-Host "🎉 Script de atualização finalizado!" -ForegroundColor Green
