# 📊 CryptoSignals Dashboard

Dashboard web para monitoramento e controle do sistema CryptoSignals.

## 🚀 **Funcionalidades**

### ✅ **Implementadas**
- 📊 **Overview** - Métricas gerais do sistema
- 📈 **Monitoramento em tempo real** - Status e performance
- 🎯 **Interface responsiva** - Funciona em desktop e mobile
- 🔄 **Atualização automática** - Dados atualizados a cada 30s
- 📱 **Design moderno** - Interface limpa e profissional

### 🔄 **Em Desenvolvimento**
- 📋 **Lista de sinais** - Histórico e sinais ativos
- 📊 **Analytics avançados** - Gráficos e relatórios
- ⚙️ **Configurações** - Controle de parâmetros
- 🔔 **Notificações** - Alertas em tempo real

## 🏗️ **Arquitetura**

```
dashboard/
├── backend/                 # API Flask
│   ├── app.py              # Aplicação principal
│   └── requirements.txt    # Dependências Python
├── frontend/               # React App
│   ├── src/
│   │   ├── components/     # Componentes React
│   │   ├── pages/         # Páginas
│   │   └── App.js         # App principal
│   └── package.json       # Dependências Node.js
├── start_dashboard.py     # Script de inicialização
└── README.md             # Este arquivo
```

## 🚀 **Como Executar**

### **Método 1: Script Automático (Recomendado)**

```bash
# No diretório dashboard/
python start_dashboard.py
```

Escolha a opção 3 para executar backend + frontend.

### **Método 2: Manual**

#### **Backend (Terminal 1):**
```bash
cd dashboard/backend
pip install -r requirements.txt
python app.py
```

#### **Frontend (Terminal 2):**
```bash
cd dashboard/frontend
npm install
npm start
```

## 🌐 **Acesso**

- **Frontend:** http://localhost:3000
- **Backend API:** http://localhost:5000
- **API Docs:** http://localhost:5000/api/

## 📊 **Endpoints da API**

### **Métricas Gerais**
```
GET /api/overview
```
Retorna métricas principais do sistema.

### **Lista de Sinais**
```
GET /api/signals?limit=20
```
Retorna lista de sinais recentes.

### **Performance por Estratégia**
```
GET /api/performance/strategies?days=30
```
Retorna performance de cada estratégia.

### **Performance Diária**
```
GET /api/performance/daily?days=30
```
Retorna dados de performance por dia.

### **Status do Sistema**
```
GET /api/system/status
```
Retorna status geral do sistema.

## 🎨 **Interface**

### **📊 Overview**
- Métricas principais (sinais ativos, win rate, profit)
- Status do sistema em tempo real
- Ações rápidas para navegação

### **📈 Sinais (Em desenvolvimento)**
- Lista de sinais ativos e histórico
- Filtros por estratégia, status, data
- Detalhes de cada sinal

### **📊 Analytics (Em desenvolvimento)**
- Gráficos de performance
- Comparação de estratégias
- Heatmap de horários

### **⚙️ Configurações (Em desenvolvimento)**
- Controles do sistema
- Parâmetros das estratégias
- Logs e diagnósticos

## 🔧 **Tecnologias**

### **Backend**
- **Flask** - Framework web Python
- **SQLite** - Banco de dados (integração com sistema existente)
- **Flask-CORS** - Suporte a CORS para React

### **Frontend**
- **React** - Framework JavaScript
- **Tailwind CSS** - Framework CSS
- **Lucide React** - Ícones
- **Axios** - Cliente HTTP
- **Chart.js** - Gráficos (futuro)

## 📱 **Responsividade**

O dashboard é totalmente responsivo e funciona em:
- 💻 **Desktop** - Experiência completa
- 📱 **Mobile** - Interface adaptada
- 📟 **Tablet** - Layout otimizado

## 🔄 **Atualizações Automáticas**

- Métricas atualizadas a cada **30 segundos**
- Status do sistema em **tempo real**
- Indicadores visuais de conectividade

## 🚨 **Tratamento de Erros**

- Fallback para dados indisponíveis
- Mensagens de erro amigáveis
- Botão de retry para reconexão
- Indicadores de status de conexão

## 🔮 **Próximas Funcionalidades**

### **Curto Prazo (1-2 semanas)**
- ✅ Página de sinais completa
- ✅ Filtros e busca
- ✅ Gráficos básicos

### **Médio Prazo (3-4 semanas)**
- 📊 Analytics avançados
- 🔔 Sistema de notificações
- ⚙️ Configurações do sistema

### **Longo Prazo (1-2 meses)**
- 🤖 Controle de trading automatizado
- 📱 App mobile nativo
- 🔌 Integração com outras plataformas

## 🐛 **Troubleshooting**

### **Backend não inicia**
```bash
# Verificar dependências
pip install -r requirements.txt

# Verificar banco de dados
ls -la ../../signals.db
```

### **Frontend não carrega**
```bash
# Limpar cache
npm cache clean --force

# Reinstalar dependências
rm -rf node_modules package-lock.json
npm install
```

### **API não responde**
- Verificar se backend está rodando na porta 5000
- Verificar logs do Flask para erros
- Testar endpoints diretamente: `curl http://localhost:5000/api/health`

## 📞 **Suporte**

Para problemas ou sugestões:
1. Verificar logs do sistema
2. Testar endpoints da API
3. Verificar conectividade com banco de dados
4. Consultar documentação do sistema principal

---

**Status:** 🚧 **Em Desenvolvimento Ativo**
**Versão:** 1.0.0
**Última Atualização:** 2025-01-13
