# 🎨 Dashboard CryptoSignals - Paleta Dourada/Preta

Dashboard web profissional com a identidade visual do CryptoSignals aplicada.

## ✨ Características Visuais

### 🎨 Paleta de Cores
- **<PERSON><PERSON> Principal:** `#0D1117` (GitHub Dark)
- **Fundo Secundário:** `#161B22` (Cinza Escuro)
- **Cards:** `#21262D` (Cinza Médio)
- **<PERSON><PERSON><PERSON>:** `#30363D` (Cinza Claro)
- **<PERSON><PERSON><PERSON> Principal:** `#F7B500` (CryptoSignals Gold)
- **Laranja Accent:** `#FF8C00` (Secundária)
- **Texto Principal:** `#F0F6FC` (Branco Suave)
- **Texto Secundário:** `#8B949E` (Cinza Claro)

### 🌟 Elementos Visuais
- ✅ Tema escuro profissional
- ✅ Acentos dourados em botões e métricas
- ✅ Efeitos hover sutis
- ✅ Gradientes dourado/laranja
- ✅ Indicadores de status coloridos
- ✅ Gráficos com cores da paleta

## 🚀 Instalação Rápida

### Método 1: Script Automático
```bash
python install_dashboard.py
```

### Método 2: Manual
```bash
# Instalar dependências
pip install -r requirements_dashboard.txt

# Iniciar dashboard
python minimal_dashboard.py
```

## 📊 Versões Disponíveis

### 1. Dashboard Completo (`web_dashboard.py`)
- Socket.IO para tempo real
- Monitoramento SSH do servidor
- Funcionalidades avançadas

### 2. Dashboard Simples (`simple_dashboard.py`)
- HTTP polling
- Dados simulados
- Mais estável

### 3. Dashboard Mínimo (`minimal_dashboard.py`)
- Ultra-simples
- Garantido para funcionar
- Ideal para demonstração

## 🔧 Uso

### Iniciar Dashboard
```bash
# Opção 1: Script de inicialização
python start_dashboard.py

# Opção 2: Dashboard específico
python minimal_dashboard.py

# Opção 3: Dashboard completo
python web_dashboard.py
```

### Acessar Interface
- **URL:** http://localhost:5000
- **Atualização:** Automática a cada 10 segundos
- **Responsivo:** Funciona em mobile e desktop

## 📈 Funcionalidades

### Métricas do Servidor
- 🖥️ **CPU:** Uso em tempo real com barra de progresso dourada
- 💾 **Memória:** Percentual com barra laranja
- 💿 **Disco:** Utilização com barra verde
- ⏱️ **Uptime:** Tempo de atividade do servidor

### Status dos Serviços
- 🟢 **Ativo:** Serviços funcionando
- 🟡 **Inativo:** Serviços parados
- 🔴 **Erro:** Problemas detectados
- 📊 **Métricas:** CPU, RAM, restarts por serviço

### Alertas e Logs
- ⚠️ **Alertas:** Notificações em tempo real
- 📝 **Logs:** Histórico de eventos
- 🎯 **Filtros:** Por nível (INFO, WARNING, ERROR)

### Gráficos
- 📈 **Performance:** Histórico de CPU, Memória, Disco
- 🎨 **Cores:** Dourado, laranja e verde
- 📱 **Responsivo:** Adapta ao tamanho da tela

## 🛠️ Dependências

### Essenciais
```
Flask>=2.3.0
Flask-SocketIO>=5.3.0
eventlet>=0.33.0
paramiko>=3.0.0
psutil>=5.9.0
requests>=2.28.0
python-dotenv>=1.0.0
colorama>=0.4.6
cryptography>=41.0.0
```

### Opcionais
```
rich>=13.0.0
python-dateutil>=2.8.0
simplejson>=3.19.0
```

## 🔍 Troubleshooting

### Problema: Socket.IO Error
**Solução:** Use o dashboard mínimo
```bash
python minimal_dashboard.py
```

### Problema: Porta 5000 ocupada
**Solução:** O script tenta automaticamente a porta 5001

### Problema: Dependências
**Solução:** Execute o instalador
```bash
python install_dashboard.py
```

## 🎯 Estrutura de Arquivos

```
├── templates/
│   └── dashboard.html          # Template com paleta CryptoSignals
├── web_dashboard.py           # Dashboard completo
├── simple_dashboard.py        # Dashboard simples
├── minimal_dashboard.py       # Dashboard mínimo
├── requirements_dashboard.txt # Dependências específicas
├── install_dashboard.py       # Script de instalação
└── start_dashboard.py         # Script de inicialização
```

## 🌐 Acesso Remoto

Para acessar de outros dispositivos na rede:
```python
# Alterar em qualquer dashboard:
app.run(host='0.0.0.0', port=5000)
```

## 📱 Responsividade

O dashboard é totalmente responsivo:
- 💻 **Desktop:** Layout completo
- 📱 **Mobile:** Cards empilhados
- 📊 **Gráficos:** Adaptam automaticamente

---

**🎨 Design inspirado no CryptoSignals**  
**⚡ Desenvolvido para performance e estabilidade**
