{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport React from \"react\";\nimport { createStackNavigator } from \"@react-navigation/stack\";\nimport { createBottomTabNavigator } from \"@react-navigation/bottom-tabs\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport Home from \"./pages/Home\";\nimport Channels from \"./pages/Channels\";\nimport Signals from \"./pages/Signals\";\nimport Premium from \"./pages/Premium\";\nimport Academy from \"./pages/Academy\";\nimport News from \"./pages/News\";\nimport Settings from \"./pages/Settings\";\nimport User from \"./pages/User\";\nimport Forum from \"./pages/Forum\";\nimport CompareCripto from \"./pages/CompareCripto\";\nimport Rank from \"./pages/Rank\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar Tab = createBottomTabNavigator();\nvar Stack = createStackNavigator();\nfunction ExtendedTabNavigator() {\n  var labelStyle = {\n    fontFamily: 'Poppins_500Medium',\n    fontSize: 10\n  };\n  return _jsxs(Tab.Navigator, {\n    screenOptions: function screenOptions(_ref) {\n      var route = _ref.route;\n      return {\n        headerShown: false,\n        tabBarIcon: function tabBarIcon() {\n          return null;\n        },\n        tabBarLabelPosition: 'beside-icon',\n        tabBarStyle: [{\n          backgroundColor: \"#202020\",\n          height: 70,\n          borderTopColor: \"#5d5d5d\",\n          paddingBottom: 5,\n          paddingTop: 5\n        }],\n        tabBarLabel: function tabBarLabel(_ref2) {\n          var focused = _ref2.focused;\n          return focused ? _jsx(Text, {\n            style: _objectSpread(_objectSpread({}, labelStyle), {}, {\n              color: \"#FECB37\"\n            }),\n            children: route.name\n          }) : _jsx(Text, {\n            style: _objectSpread(_objectSpread({}, labelStyle), {}, {\n              color: \"#8a8a8a\"\n            }),\n            children: route.name\n          });\n        }\n      };\n    },\n    children: [_jsx(Tab.Screen, {\n      name: \"Home\",\n      component: Home\n    }), _jsx(Tab.Screen, {\n      name: \"Channels\",\n      component: Channels\n    }), _jsx(Tab.Screen, {\n      name: \"News\",\n      component: News\n    }), _jsx(Tab.Screen, {\n      name: \"Academy\",\n      component: Academy\n    }), _jsx(Tab.Screen, {\n      name: \"Settings\",\n      component: Settings\n    }), _jsx(Tab.Screen, {\n      name: \"Premium\",\n      component: Premium\n    })]\n  });\n}\nfunction AppNavigator() {\n  return _jsxs(Stack.Navigator, {\n    screenOptions: {\n      headerShown: false\n    },\n    children: [_jsx(Stack.Screen, {\n      name: \"Main\",\n      component: ExtendedTabNavigator\n    }), _jsx(Stack.Screen, {\n      name: \"Signals\",\n      component: Signals\n    }), _jsx(Stack.Screen, {\n      name: \"User\",\n      component: User\n    }), _jsx(Stack.Screen, {\n      name: \"Forum\",\n      component: Forum\n    }), _jsx(Stack.Screen, {\n      name: \"CompareCripto\",\n      component: CompareCripto\n    }), _jsx(Stack.Screen, {\n      name: \"Rank\",\n      component: Rank\n    })]\n  });\n}\nexport default function Routes() {\n  return _jsx(AppNavigator, {});\n}", "map": {"version": 3, "names": ["React", "createStackNavigator", "createBottomTabNavigator", "Text", "Home", "Channels", "Signals", "Premium", "Academy", "News", "Settings", "User", "Forum", "CompareCripto", "Rank", "jsx", "_jsx", "jsxs", "_jsxs", "Tab", "<PERSON><PERSON>", "ExtendedTabNavigator", "labelStyle", "fontFamily", "fontSize", "Navigator", "screenOptions", "_ref", "route", "headerShown", "tabBarIcon", "tabBarLabelPosition", "tabBarStyle", "backgroundColor", "height", "borderTopColor", "paddingBottom", "paddingTop", "tabBarLabel", "_ref2", "focused", "style", "_objectSpread", "color", "children", "name", "Screen", "component", "AppNavigator", "Routes"], "sources": ["E:/CryptoSignalsApp/src/routes.js"], "sourcesContent": ["import React from \"react\";\r\nimport { createStackNavigator } from \"@react-navigation/stack\";\r\nimport { createBottomTabNavigator } from \"@react-navigation/bottom-tabs\";\r\nimport { Text } from \"react-native\";\r\n\r\n// Import pages\r\nimport Home from \"./pages/Home\";\r\nimport Channels from \"./pages/Channels\";\r\nimport Signals from \"./pages/Signals\";\r\nimport Premium from \"./pages/Premium\";\r\nimport Academy from \"./pages/Academy\";\r\nimport News from \"./pages/News\";\r\nimport Settings from \"./pages/Settings\";\r\nimport User from \"./pages/User\";\r\nimport Forum from \"./pages/Forum\";\r\nimport CompareCripto from \"./pages/CompareCripto\";\r\nimport Rank from \"./pages/Rank\";\r\n\r\nconst Tab = createBottomTabNavigator();\r\nconst Stack = createStackNavigator();\r\n\r\n// Main Tab Navigator with all features\r\nfunction ExtendedTabNavigator() {\r\n  const labelStyle = {\r\n    fontFamily: 'Poppins_500Medium',\r\n    fontSize: 10,\r\n  }\r\n  return (\r\n    <Tab.Navigator\r\n      screenOptions={({ route }) => ({\r\n        headerShown: false,\r\n        tabBarIcon: () => null,\r\n        tabBarLabelPosition: 'beside-icon',\r\n        tabBarStyle: [\r\n          {\r\n            backgroundColor: \"#202020\",\r\n            height: 70,\r\n            borderTopColor: \"#5d5d5d\",\r\n            paddingBottom: 5,\r\n            paddingTop: 5,\r\n          },\r\n        ],\r\n        tabBarLabel: ({ focused }) => {\r\n          return focused ? (\r\n            <Text style={{ ...labelStyle, color: \"#FECB37\" }}>\r\n              {route.name}\r\n            </Text>\r\n          ) : (\r\n            <Text\r\n              style={{ ...labelStyle, color: \"#8a8a8a\" }}\r\n            >\r\n              {route.name}\r\n            </Text>\r\n          );\r\n        },\r\n      })}\r\n    >\r\n      <Tab.Screen name=\"Home\" component={Home} />\r\n      <Tab.Screen name=\"Channels\" component={Channels} />\r\n      <Tab.Screen name=\"News\" component={News} />\r\n      <Tab.Screen name=\"Academy\" component={Academy} />\r\n      <Tab.Screen name=\"Settings\" component={Settings} />\r\n      <Tab.Screen name=\"Premium\" component={Premium} />\r\n    </Tab.Navigator>\r\n  );\r\n}\r\n\r\n// Stack Navigator for modal screens and navigation\r\nfunction AppNavigator() {\r\n  return (\r\n    <Stack.Navigator screenOptions={{ headerShown: false }}>\r\n      <Stack.Screen name=\"Main\" component={ExtendedTabNavigator} />\r\n      <Stack.Screen name=\"Signals\" component={Signals} />\r\n      <Stack.Screen name=\"User\" component={User} />\r\n      <Stack.Screen name=\"Forum\" component={Forum} />\r\n      <Stack.Screen name=\"CompareCripto\" component={CompareCripto} />\r\n      <Stack.Screen name=\"Rank\" component={Rank} />\r\n    </Stack.Navigator>\r\n  );\r\n}\r\n\r\nexport default function Routes() {\r\n  return (\r\n    <AppNavigator />\r\n  );\r\n}\r\n\r\n/*\r\nAqui estão as mudanças que fiz:\r\n\r\nIntegrei o Drawer.Navigator diretamente no Routes para evitar redundância.\r\nRemovi a função AppNavigator que não estava sendo utilizada.\r\nA estrutura agora é que temos um Stack.Navigator principal com uma combinação de Drawer.Navigator e\r\n Tab.Navigator para uma melhor organização e navegação.\r\n*/"], "mappings": ";;;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,oBAAoB,QAAQ,yBAAyB;AAC9D,SAASC,wBAAwB,QAAQ,+BAA+B;AAAC,OAAAC,IAAA;AAIzE,OAAOC,IAAI;AACX,OAAOC,QAAQ;AACf,OAAOC,OAAO;AACd,OAAOC,OAAO;AACd,OAAOC,OAAO;AACd,OAAOC,IAAI;AACX,OAAOC,QAAQ;AACf,OAAOC,IAAI;AACX,OAAOC,KAAK;AACZ,OAAOC,aAAa;AACpB,OAAOC,IAAI;AAAqB,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAEhC,IAAMC,GAAG,GAAGjB,wBAAwB,CAAC,CAAC;AACtC,IAAMkB,KAAK,GAAGnB,oBAAoB,CAAC,CAAC;AAGpC,SAASoB,oBAAoBA,CAAA,EAAG;EAC9B,IAAMC,UAAU,GAAG;IACjBC,UAAU,EAAE,mBAAmB;IAC/BC,QAAQ,EAAE;EACZ,CAAC;EACD,OACEN,KAAA,CAACC,GAAG,CAACM,SAAS;IACZC,aAAa,EAAE,SAAfA,aAAaA,CAAAC,IAAA;MAAA,IAAKC,KAAK,GAAAD,IAAA,CAALC,KAAK;MAAA,OAAQ;QAC7BC,WAAW,EAAE,KAAK;QAClBC,UAAU,EAAE,SAAZA,UAAUA,CAAA;UAAA,OAAQ,IAAI;QAAA;QACtBC,mBAAmB,EAAE,aAAa;QAClCC,WAAW,EAAE,CACX;UACEC,eAAe,EAAE,SAAS;UAC1BC,MAAM,EAAE,EAAE;UACVC,cAAc,EAAE,SAAS;UACzBC,aAAa,EAAE,CAAC;UAChBC,UAAU,EAAE;QACd,CAAC,CACF;QACDC,WAAW,EAAE,SAAbA,WAAWA,CAAAC,KAAA,EAAmB;UAAA,IAAdC,OAAO,GAAAD,KAAA,CAAPC,OAAO;UACrB,OAAOA,OAAO,GACZxB,IAAA,CAACb,IAAI;YAACsC,KAAK,EAAAC,aAAA,CAAAA,aAAA,KAAOpB,UAAU;cAAEqB,KAAK,EAAE;YAAS,EAAG;YAAAC,QAAA,EAC9ChB,KAAK,CAACiB;UAAI,CACP,CAAC,GAEP7B,IAAA,CAACb,IAAI;YACHsC,KAAK,EAAAC,aAAA,CAAAA,aAAA,KAAOpB,UAAU;cAAEqB,KAAK,EAAE;YAAS,EAAG;YAAAC,QAAA,EAE1ChB,KAAK,CAACiB;UAAI,CACP,CACP;QACH;MACF,CAAC;IAAA,CAAE;IAAAD,QAAA,GAEH5B,IAAA,CAACG,GAAG,CAAC2B,MAAM;MAACD,IAAI,EAAC,MAAM;MAACE,SAAS,EAAE3C;IAAK,CAAE,CAAC,EAC3CY,IAAA,CAACG,GAAG,CAAC2B,MAAM;MAACD,IAAI,EAAC,UAAU;MAACE,SAAS,EAAE1C;IAAS,CAAE,CAAC,EACnDW,IAAA,CAACG,GAAG,CAAC2B,MAAM;MAACD,IAAI,EAAC,MAAM;MAACE,SAAS,EAAEtC;IAAK,CAAE,CAAC,EAC3CO,IAAA,CAACG,GAAG,CAAC2B,MAAM;MAACD,IAAI,EAAC,SAAS;MAACE,SAAS,EAAEvC;IAAQ,CAAE,CAAC,EACjDQ,IAAA,CAACG,GAAG,CAAC2B,MAAM;MAACD,IAAI,EAAC,UAAU;MAACE,SAAS,EAAErC;IAAS,CAAE,CAAC,EACnDM,IAAA,CAACG,GAAG,CAAC2B,MAAM;MAACD,IAAI,EAAC,SAAS;MAACE,SAAS,EAAExC;IAAQ,CAAE,CAAC;EAAA,CACpC,CAAC;AAEpB;AAGA,SAASyC,YAAYA,CAAA,EAAG;EACtB,OACE9B,KAAA,CAACE,KAAK,CAACK,SAAS;IAACC,aAAa,EAAE;MAAEG,WAAW,EAAE;IAAM,CAAE;IAAAe,QAAA,GACrD5B,IAAA,CAACI,KAAK,CAAC0B,MAAM;MAACD,IAAI,EAAC,MAAM;MAACE,SAAS,EAAE1B;IAAqB,CAAE,CAAC,EAC7DL,IAAA,CAACI,KAAK,CAAC0B,MAAM;MAACD,IAAI,EAAC,SAAS;MAACE,SAAS,EAAEzC;IAAQ,CAAE,CAAC,EACnDU,IAAA,CAACI,KAAK,CAAC0B,MAAM;MAACD,IAAI,EAAC,MAAM;MAACE,SAAS,EAAEpC;IAAK,CAAE,CAAC,EAC7CK,IAAA,CAACI,KAAK,CAAC0B,MAAM;MAACD,IAAI,EAAC,OAAO;MAACE,SAAS,EAAEnC;IAAM,CAAE,CAAC,EAC/CI,IAAA,CAACI,KAAK,CAAC0B,MAAM;MAACD,IAAI,EAAC,eAAe;MAACE,SAAS,EAAElC;IAAc,CAAE,CAAC,EAC/DG,IAAA,CAACI,KAAK,CAAC0B,MAAM;MAACD,IAAI,EAAC,MAAM;MAACE,SAAS,EAAEjC;IAAK,CAAE,CAAC;EAAA,CAC9B,CAAC;AAEtB;AAEA,eAAe,SAASmC,MAAMA,CAAA,EAAG;EAC/B,OACEjC,IAAA,CAACgC,YAAY,IAAE,CAAC;AAEpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}