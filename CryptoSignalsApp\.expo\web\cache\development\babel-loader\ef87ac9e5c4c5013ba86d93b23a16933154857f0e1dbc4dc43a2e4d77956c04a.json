{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"style\", \"status\", \"label\", \"onPress\", \"onLongPress\", \"labelStyle\", \"theme\", \"testID\", \"mode\", \"position\", \"accessibilityLabel\", \"disabled\", \"labelVariant\", \"labelMaxFontSizeMultiplier\", \"rippleColor\", \"background\", \"hitSlop\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport * as React from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport Checkbox from \"./Checkbox\";\nimport CheckboxAndroid from \"./CheckboxAndroid\";\nimport CheckboxIOS from \"./CheckboxIOS\";\nimport { useInternalTheme } from \"../../core/theming\";\nimport TouchableRipple from \"../TouchableRipple/TouchableRipple\";\nimport Text from \"../Typography/Text\";\nvar CheckboxItem = function CheckboxItem(_ref) {\n  var style = _ref.style,\n    status = _ref.status,\n    label = _ref.label,\n    onPress = _ref.onPress,\n    onLongPress = _ref.onLongPress,\n    labelStyle = _ref.labelStyle,\n    themeOverrides = _ref.theme,\n    testID = _ref.testID,\n    mode = _ref.mode,\n    _ref$position = _ref.position,\n    position = _ref$position === void 0 ? 'trailing' : _ref$position,\n    _ref$accessibilityLab = _ref.accessibilityLabel,\n    accessibilityLabel = _ref$accessibilityLab === void 0 ? label : _ref$accessibilityLab,\n    disabled = _ref.disabled,\n    _ref$labelVariant = _ref.labelVariant,\n    labelVariant = _ref$labelVariant === void 0 ? 'bodyLarge' : _ref$labelVariant,\n    _ref$labelMaxFontSize = _ref.labelMaxFontSizeMultiplier,\n    labelMaxFontSizeMultiplier = _ref$labelMaxFontSize === void 0 ? 1.5 : _ref$labelMaxFontSize,\n    rippleColor = _ref.rippleColor,\n    background = _ref.background,\n    hitSlop = _ref.hitSlop,\n    props = _objectWithoutProperties(_ref, _excluded);\n  var theme = useInternalTheme(themeOverrides);\n  var checkboxProps = _objectSpread(_objectSpread({}, props), {}, {\n    status: status,\n    theme: theme,\n    disabled: disabled\n  });\n  var isLeading = position === 'leading';\n  var checkbox;\n  if (mode === 'android') {\n    checkbox = React.createElement(CheckboxAndroid, checkboxProps);\n  } else if (mode === 'ios') {\n    checkbox = React.createElement(CheckboxIOS, checkboxProps);\n  } else {\n    checkbox = React.createElement(Checkbox, checkboxProps);\n  }\n  var textColor = theme.isV3 ? theme.colors.onSurface : theme.colors.text;\n  var disabledTextColor = theme.isV3 ? theme.colors.onSurfaceDisabled : theme.colors.disabled;\n  var textAlign = isLeading ? 'right' : 'left';\n  var computedStyle = {\n    color: disabled ? disabledTextColor : textColor,\n    textAlign: textAlign\n  };\n  return React.createElement(TouchableRipple, {\n    accessibilityLabel: accessibilityLabel,\n    accessibilityRole: \"checkbox\",\n    accessibilityState: {\n      checked: status === 'checked',\n      disabled: disabled\n    },\n    onPress: onPress,\n    onLongPress: onLongPress,\n    testID: testID,\n    disabled: disabled,\n    rippleColor: rippleColor,\n    theme: theme,\n    background: background,\n    hitSlop: hitSlop\n  }, React.createElement(View, {\n    style: [styles.container, style],\n    pointerEvents: \"none\",\n    importantForAccessibility: \"no-hide-descendants\"\n  }, isLeading && checkbox, React.createElement(Text, {\n    variant: labelVariant,\n    testID: `${testID}-text`,\n    maxFontSizeMultiplier: labelMaxFontSizeMultiplier,\n    style: [styles.label, !theme.isV3 && styles.font, computedStyle, labelStyle]\n  }, label), !isLeading && checkbox));\n};\nCheckboxItem.displayName = 'Checkbox.Item';\nexport default CheckboxItem;\nexport { CheckboxItem };\nvar styles = StyleSheet.create({\n  container: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    justifyContent: 'space-between',\n    paddingVertical: 8,\n    paddingHorizontal: 16\n  },\n  label: {\n    flexShrink: 1,\n    flexGrow: 1\n  },\n  font: {\n    fontSize: 16\n  }\n});", "map": {"version": 3, "names": ["React", "StyleSheet", "View", "Checkbox", "CheckboxAndroid", "CheckboxIOS", "useInternalTheme", "TouchableRipple", "Text", "CheckboxItem", "_ref", "style", "status", "label", "onPress", "onLongPress", "labelStyle", "themeOverrides", "theme", "testID", "mode", "_ref$position", "position", "_ref$accessibilityLab", "accessibilityLabel", "disabled", "_ref$labelVariant", "labelVariant", "_ref$labelMaxFontSize", "labelMaxFontSizeMultiplier", "rippleColor", "background", "hitSlop", "props", "_objectWithoutProperties", "_excluded", "checkboxProps", "_objectSpread", "isLeading", "checkbox", "createElement", "textColor", "isV3", "colors", "onSurface", "text", "disabledTextColor", "onSurfaceDisabled", "textAlign", "computedStyle", "color", "accessibilityRole", "accessibilityState", "checked", "styles", "container", "pointerEvents", "importantForAccessibility", "variant", "maxFontSizeMultiplier", "font", "displayName", "create", "flexDirection", "alignItems", "justifyContent", "paddingVertical", "paddingHorizontal", "flexShrink", "flexGrow", "fontSize"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\Checkbox\\CheckboxItem.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  ColorValue,\n  GestureResponderEvent,\n  PressableAndroidRippleConfig,\n  StyleProp,\n  StyleSheet,\n  TextStyle,\n  View,\n  ViewStyle,\n} from 'react-native';\n\nimport Checkbox from './Checkbox';\nimport CheckboxAndroid from './CheckboxAndroid';\nimport CheckboxIOS from './CheckboxIOS';\nimport { useInternalTheme } from '../../core/theming';\nimport type { ThemeProp, MD3TypescaleKey } from '../../types';\nimport TouchableRipple, {\n  Props as TouchableRippleProps,\n} from '../TouchableRipple/TouchableRipple';\nimport Text from '../Typography/Text';\n\nexport type Props = {\n  /**\n   * Status of checkbox.\n   */\n  status: 'checked' | 'unchecked' | 'indeterminate';\n  /**\n   * Whether checkbox is disabled.\n   */\n  disabled?: boolean;\n  /**\n   * Label to be displayed on the item.\n   */\n  label: string;\n  /**\n   * Function to execute on press.\n   */\n  onPress?: (e: GestureResponderEvent) => void;\n  /**\n   * Function to execute on long press.\n   */\n  onLongPress?: (e: GestureResponderEvent) => void;\n  /**\n   * Type of background drawabale to display the feedback (Android).\n   * https://reactnative.dev/docs/pressable#rippleconfig\n   */\n  background?: PressableAndroidRippleConfig;\n  /**\n   * Accessibility label for the touchable. This is read by the screen reader when the user taps the touchable.\n   */\n  accessibilityLabel?: string;\n  /**\n   * Custom color for unchecked checkbox.\n   */\n  uncheckedColor?: string;\n  /**\n   * Custom color for checkbox.\n   */\n  color?: string;\n  /**\n   * Color of the ripple effect.\n   */\n  rippleColor?: ColorValue;\n  /**\n   * Additional styles for container View.\n   */\n  style?: StyleProp<ViewStyle>;\n  /**\n   * Specifies the largest possible scale a label font can reach.\n   */\n  labelMaxFontSizeMultiplier?: number;\n  /**\n   * Style that is passed to Label element.\n   */\n  labelStyle?: StyleProp<TextStyle>;\n  /**\n   * @supported Available in v5.x with theme version 3\n   *\n   * Label text variant defines appropriate text styles for type role and its size.\n   * Available variants:\n   *\n   *  Display: `displayLarge`, `displayMedium`, `displaySmall`\n   *\n   *  Headline: `headlineLarge`, `headlineMedium`, `headlineSmall`\n   *\n   *  Title: `titleLarge`, `titleMedium`, `titleSmall`\n   *\n   *  Label:  `labelLarge`, `labelMedium`, `labelSmall`\n   *\n   *  Body: `bodyLarge`, `bodyMedium`, `bodySmall`\n   */\n  labelVariant?: keyof typeof MD3TypescaleKey;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n  /**\n   * testID to be used on tests.\n   */\n  testID?: string;\n  /**\n   * Checkbox control position.\n   */\n  position?: 'leading' | 'trailing';\n  /**\n   * Whether `<Checkbox.Android />` or `<Checkbox.IOS />` should be used.\n   * Left undefined `<Checkbox />` will be used.\n   */\n  mode?: 'android' | 'ios';\n  /**\n   * Sets additional distance outside of element in which a press can be detected.\n   */\n  hitSlop?: TouchableRippleProps['hitSlop'];\n};\n\n/**\n * Checkbox.Item allows you to press the whole row (item) instead of only the Checkbox.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { View } from 'react-native';\n * import { Checkbox } from 'react-native-paper';\n *\n * const MyComponent = () => (\n *   <View>\n *     <Checkbox.Item label=\"Item\" status=\"checked\" />\n *   </View>\n * );\n *\n * export default MyComponent;\n *```\n */\n\nconst CheckboxItem = ({\n  style,\n  status,\n  label,\n  onPress,\n  onLongPress,\n  labelStyle,\n  theme: themeOverrides,\n  testID,\n  mode,\n  position = 'trailing',\n  accessibilityLabel = label,\n  disabled,\n  labelVariant = 'bodyLarge',\n  labelMaxFontSizeMultiplier = 1.5,\n  rippleColor,\n  background,\n  hitSlop,\n  ...props\n}: Props) => {\n  const theme = useInternalTheme(themeOverrides);\n  const checkboxProps = { ...props, status, theme, disabled };\n  const isLeading = position === 'leading';\n  let checkbox;\n\n  if (mode === 'android') {\n    checkbox = <CheckboxAndroid {...checkboxProps} />;\n  } else if (mode === 'ios') {\n    checkbox = <CheckboxIOS {...checkboxProps} />;\n  } else {\n    checkbox = <Checkbox {...checkboxProps} />;\n  }\n\n  const textColor = theme.isV3 ? theme.colors.onSurface : theme.colors.text;\n  const disabledTextColor = theme.isV3\n    ? theme.colors.onSurfaceDisabled\n    : theme.colors.disabled;\n  const textAlign = isLeading ? 'right' : 'left';\n\n  const computedStyle = {\n    color: disabled ? disabledTextColor : textColor,\n    textAlign,\n  } as TextStyle;\n\n  return (\n    <TouchableRipple\n      accessibilityLabel={accessibilityLabel}\n      accessibilityRole=\"checkbox\"\n      accessibilityState={{\n        checked: status === 'checked',\n        disabled,\n      }}\n      onPress={onPress}\n      onLongPress={onLongPress}\n      testID={testID}\n      disabled={disabled}\n      rippleColor={rippleColor}\n      theme={theme}\n      background={background}\n      hitSlop={hitSlop}\n    >\n      <View\n        style={[styles.container, style]}\n        pointerEvents=\"none\"\n        importantForAccessibility=\"no-hide-descendants\"\n      >\n        {isLeading && checkbox}\n        <Text\n          variant={labelVariant}\n          testID={`${testID}-text`}\n          maxFontSizeMultiplier={labelMaxFontSizeMultiplier}\n          style={[\n            styles.label,\n            !theme.isV3 && styles.font,\n            computedStyle,\n            labelStyle,\n          ]}\n        >\n          {label}\n        </Text>\n        {!isLeading && checkbox}\n      </View>\n    </TouchableRipple>\n  );\n};\n\nCheckboxItem.displayName = 'Checkbox.Item';\n\nexport default CheckboxItem;\n\n// @component-docs ignore-next-line\nexport { CheckboxItem };\n\nconst styles = StyleSheet.create({\n  container: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    justifyContent: 'space-between',\n    paddingVertical: 8,\n    paddingHorizontal: 16,\n  },\n  label: {\n    flexShrink: 1,\n    flexGrow: 1,\n  },\n  font: {\n    fontSize: 16,\n  },\n});\n"], "mappings": ";;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAY9B,OAAOC,QAAQ;AACf,OAAOC,eAAe;AACtB,OAAOC,WAAW;AAClB,SAASC,gBAAgB;AAEzB,OAAOC,eAAe;AAGtB,OAAOC,IAAI;AAmHX,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAAC,IAAA,EAmBL;EAAA,IAlBXC,KAAK,GAAAD,IAAA,CAALC,KAAK;IACLC,MAAM,GAAAF,IAAA,CAANE,MAAM;IACNC,KAAK,GAAAH,IAAA,CAALG,KAAK;IACLC,OAAO,GAAAJ,IAAA,CAAPI,OAAO;IACPC,WAAW,GAAAL,IAAA,CAAXK,WAAW;IACXC,UAAU,GAAAN,IAAA,CAAVM,UAAU;IACHC,cAAc,GAAAP,IAAA,CAArBQ,KAAK;IACLC,MAAM,GAAAT,IAAA,CAANS,MAAM;IACNC,IAAI,GAAAV,IAAA,CAAJU,IAAI;IAAAC,aAAA,GAAAX,IAAA,CACJY,QAAQ;IAARA,QAAQ,GAAAD,aAAA,cAAG,UAAU,GAAAA,aAAA;IAAAE,qBAAA,GAAAb,IAAA,CACrBc,kBAAkB;IAAlBA,kBAAkB,GAAAD,qBAAA,cAAGV,KAAK,GAAAU,qBAAA;IAC1BE,QAAQ,GAAAf,IAAA,CAARe,QAAQ;IAAAC,iBAAA,GAAAhB,IAAA,CACRiB,YAAY;IAAZA,YAAY,GAAAD,iBAAA,cAAG,WAAW,GAAAA,iBAAA;IAAAE,qBAAA,GAAAlB,IAAA,CAC1BmB,0BAA0B;IAA1BA,0BAA0B,GAAAD,qBAAA,cAAG,GAAG,GAAAA,qBAAA;IAChCE,WAAW,GAAApB,IAAA,CAAXoB,WAAW;IACXC,UAAU,GAAArB,IAAA,CAAVqB,UAAU;IACVC,OAAO,GAAAtB,IAAA,CAAPsB,OAAO;IACJC,KAAA,GAAAC,wBAAA,CAAAxB,IAAA,EAAAyB,SAAA;EAEH,IAAMjB,KAAK,GAAGZ,gBAAgB,CAACW,cAAc,CAAC;EAC9C,IAAMmB,aAAa,GAAAC,aAAA,CAAAA,aAAA,KAAQJ,KAAK;IAAErB,MAAM,EAANA,MAAM;IAAEM,KAAK,EAALA,KAAK;IAAEO,QAAA,EAAAA;EAAA,EAAU;EAC3D,IAAMa,SAAS,GAAGhB,QAAQ,KAAK,SAAS;EACxC,IAAIiB,QAAQ;EAEZ,IAAInB,IAAI,KAAK,SAAS,EAAE;IACtBmB,QAAQ,GAAGvC,KAAA,CAAAwC,aAAA,CAACpC,eAAe,EAAKgC,aAAgB,CAAC;EACnD,CAAC,MAAM,IAAIhB,IAAI,KAAK,KAAK,EAAE;IACzBmB,QAAQ,GAAGvC,KAAA,CAAAwC,aAAA,CAACnC,WAAW,EAAK+B,aAAgB,CAAC;EAC/C,CAAC,MAAM;IACLG,QAAQ,GAAGvC,KAAA,CAAAwC,aAAA,CAACrC,QAAQ,EAAKiC,aAAgB,CAAC;EAC5C;EAEA,IAAMK,SAAS,GAAGvB,KAAK,CAACwB,IAAI,GAAGxB,KAAK,CAACyB,MAAM,CAACC,SAAS,GAAG1B,KAAK,CAACyB,MAAM,CAACE,IAAI;EACzE,IAAMC,iBAAiB,GAAG5B,KAAK,CAACwB,IAAI,GAChCxB,KAAK,CAACyB,MAAM,CAACI,iBAAiB,GAC9B7B,KAAK,CAACyB,MAAM,CAAClB,QAAQ;EACzB,IAAMuB,SAAS,GAAGV,SAAS,GAAG,OAAO,GAAG,MAAM;EAE9C,IAAMW,aAAa,GAAG;IACpBC,KAAK,EAAEzB,QAAQ,GAAGqB,iBAAiB,GAAGL,SAAS;IAC/CO,SAAA,EAAAA;EACF,CAAc;EAEd,OACEhD,KAAA,CAAAwC,aAAA,CAACjC,eAAe;IACdiB,kBAAkB,EAAEA,kBAAmB;IACvC2B,iBAAiB,EAAC,UAAU;IAC5BC,kBAAkB,EAAE;MAClBC,OAAO,EAAEzC,MAAM,KAAK,SAAS;MAC7Ba,QAAA,EAAAA;IACF,CAAE;IACFX,OAAO,EAAEA,OAAQ;IACjBC,WAAW,EAAEA,WAAY;IACzBI,MAAM,EAAEA,MAAO;IACfM,QAAQ,EAAEA,QAAS;IACnBK,WAAW,EAAEA,WAAY;IACzBZ,KAAK,EAAEA,KAAM;IACba,UAAU,EAAEA,UAAW;IACvBC,OAAO,EAAEA;EAAQ,GAEjBhC,KAAA,CAAAwC,aAAA,CAACtC,IAAI;IACHS,KAAK,EAAE,CAAC2C,MAAM,CAACC,SAAS,EAAE5C,KAAK,CAAE;IACjC6C,aAAa,EAAC,MAAM;IACpBC,yBAAyB,EAAC;EAAqB,GAE9CnB,SAAS,IAAIC,QAAQ,EACtBvC,KAAA,CAAAwC,aAAA,CAAChC,IAAI;IACHkD,OAAO,EAAE/B,YAAa;IACtBR,MAAM,EAAE,GAAGA,MAAM,OAAQ;IACzBwC,qBAAqB,EAAE9B,0BAA2B;IAClDlB,KAAK,EAAE,CACL2C,MAAM,CAACzC,KAAK,EACZ,CAACK,KAAK,CAACwB,IAAI,IAAIY,MAAM,CAACM,IAAI,EAC1BX,aAAa,EACbjC,UAAU;EACV,GAEDH,KACG,CAAC,EACN,CAACyB,SAAS,IAAIC,QACX,CACS,CAAC;AAEtB,CAAC;AAED9B,YAAY,CAACoD,WAAW,GAAG,eAAe;AAE1C,eAAepD,YAAY;AAG3B,SAASA,YAAY;AAErB,IAAM6C,MAAM,GAAGrD,UAAU,CAAC6D,MAAM,CAAC;EAC/BP,SAAS,EAAE;IACTQ,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,eAAe;IAC/BC,eAAe,EAAE,CAAC;IAClBC,iBAAiB,EAAE;EACrB,CAAC;EACDtD,KAAK,EAAE;IACLuD,UAAU,EAAE,CAAC;IACbC,QAAQ,EAAE;EACZ,CAAC;EACDT,IAAI,EAAE;IACJU,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}