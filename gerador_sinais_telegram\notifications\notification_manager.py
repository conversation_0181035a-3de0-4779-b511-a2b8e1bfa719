#!/usr/bin/env python3
"""
Gerenciador Principal de Notificações
Coordena todos os tipos de notificações (Email, Push, Webhook)
"""

import logging
import asyncio
from typing import Dict, List, Optional
from datetime import datetime, timedelta
import json
import os
from .email_notifier import EmailNotifier
from .push_notifier import PushNotifier
from .webhook_notifier import WebhookNotifier

logger = logging.getLogger(__name__)

class NotificationManager:
    """Gerenciador central de notificações"""
    
    def __init__(self, config_file: str = 'config/notifications.json'):
        self.config_file = config_file
        self.config = self._load_config()
        
        # Inicializar notificadores
        self.email_notifier = EmailNotifier(self.config.get('email', {}))
        self.push_notifier = PushNotifier(self.config.get('push', {}))
        self.webhook_notifier = WebhookNotifier(self.config.get('webhook', {}))
        
        # Configurações gerais
        self.enabled = self.config.get('enabled', True)
        self.rate_limit = self.config.get('rate_limit', {
            'signals': 60,  # segundos entre notificações de sinais
            'performance': 3600,  # segundos entre relatórios de performance
            'system': 300  # segundos entre notificações do sistema
        })
        
        # Controle de rate limiting
        self.last_notifications = {
            'signals': {},
            'performance': None,
            'system': {}
        }
        
        # Filtros de notificação
        self.filters = self.config.get('filters', {
            'min_confidence': 70,
            'strategies': [],  # vazio = todas
            'symbols': [],    # vazio = todos
            'signal_types': ['LONG', 'SHORT']
        })
        
        logger.info("NotificationManager inicializado")
    
    def _load_config(self) -> Dict:
        """Carrega configuração do arquivo"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    return json.load(f)
            else:
                logger.warning(f"Arquivo de configuração não encontrado: {self.config_file}")
                return self._get_default_config()
        except Exception as e:
            logger.error(f"Erro ao carregar configuração: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict:
        """Retorna configuração padrão"""
        return {
            "enabled": True,
            "email": {
                "enabled": False,
                "smtp_server": "smtp.gmail.com",
                "smtp_port": 587,
                "email": "",
                "password": "",
                "recipients": []
            },
            "push": {
                "enabled": False,
                "vapid_private_key": "",
                "vapid_public_key": ""
            },
            "webhook": {
                "enabled": False,
                "webhooks": []
            },
            "rate_limit": {
                "signals": 60,
                "performance": 3600,
                "system": 300
            },
            "filters": {
                "min_confidence": 70,
                "strategies": [],
                "symbols": [],
                "signal_types": ["LONG", "SHORT"]
            }
        }
    
    def save_config(self) -> bool:
        """Salva configuração no arquivo"""
        try:
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=2)
            logger.info("Configuração salva com sucesso")
            return True
        except Exception as e:
            logger.error(f"Erro ao salvar configuração: {e}")
            return False
    
    def notify_new_signal(self, signal_data: Dict) -> Dict:
        """Envia notificação de novo sinal"""
        if not self.enabled:
            return {'status': 'disabled'}
        
        # Aplicar filtros
        if not self._should_notify_signal(signal_data):
            return {'status': 'filtered'}
        
        # Verificar rate limiting
        symbol = signal_data.get('symbol', '')
        now = datetime.now()
        
        if symbol in self.last_notifications['signals']:
            last_time = self.last_notifications['signals'][symbol]
            if (now - last_time).total_seconds() < self.rate_limit['signals']:
                return {'status': 'rate_limited'}
        
        # Atualizar timestamp
        self.last_notifications['signals'][symbol] = now
        
        # Enviar notificações
        results = {}
        
        try:
            # Email
            if self.email_notifier.enabled:
                recipients = self.config.get('email', {}).get('recipients', [])
                if recipients:
                    email_result = self.email_notifier.send_signal_alert(signal_data, recipients)
                    results['email'] = {'sent': email_result}
            
            # Push
            if self.push_notifier.enabled:
                push_result = self.push_notifier.send_signal_notification(signal_data)
                results['push'] = push_result
            
            # Webhook
            if self.webhook_notifier.enabled:
                webhook_result = self.webhook_notifier.send_signal_webhook(signal_data)
                results['webhook'] = webhook_result
            
            logger.info(f"Notificação de sinal enviada para {signal_data.get('symbol')}: {results}")
            return {'status': 'sent', 'results': results}
            
        except Exception as e:
            logger.error(f"Erro ao enviar notificação de sinal: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def notify_performance_report(self, performance_data: Dict) -> Dict:
        """Envia relatório de performance"""
        if not self.enabled:
            return {'status': 'disabled'}
        
        # Verificar rate limiting
        now = datetime.now()
        if self.last_notifications['performance']:
            last_time = self.last_notifications['performance']
            if (now - last_time).total_seconds() < self.rate_limit['performance']:
                return {'status': 'rate_limited'}
        
        # Atualizar timestamp
        self.last_notifications['performance'] = now
        
        # Enviar notificações
        results = {}
        
        try:
            # Email
            if self.email_notifier.enabled:
                recipients = self.config.get('email', {}).get('recipients', [])
                if recipients:
                    email_result = self.email_notifier.send_performance_report(performance_data, recipients)
                    results['email'] = {'sent': email_result}
            
            # Push
            if self.push_notifier.enabled:
                push_result = self.push_notifier.send_performance_notification(performance_data)
                results['push'] = push_result
            
            # Webhook
            if self.webhook_notifier.enabled:
                webhook_result = self.webhook_notifier.send_performance_webhook(performance_data)
                results['webhook'] = webhook_result
            
            logger.info(f"Relatório de performance enviado: {results}")
            return {'status': 'sent', 'results': results}
            
        except Exception as e:
            logger.error(f"Erro ao enviar relatório de performance: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def notify_system_event(self, message: str, level: str = 'info') -> Dict:
        """Envia notificação do sistema"""
        if not self.enabled:
            return {'status': 'disabled'}
        
        # Verificar rate limiting
        now = datetime.now()
        key = f"{level}_{hash(message)}"
        
        if key in self.last_notifications['system']:
            last_time = self.last_notifications['system'][key]
            if (now - last_time).total_seconds() < self.rate_limit['system']:
                return {'status': 'rate_limited'}
        
        # Atualizar timestamp
        self.last_notifications['system'][key] = now
        
        # Enviar notificações
        results = {}
        
        try:
            # Push
            if self.push_notifier.enabled:
                push_result = self.push_notifier.send_system_notification(message, level)
                results['push'] = push_result
            
            # Webhook
            if self.webhook_notifier.enabled:
                webhook_result = self.webhook_notifier.send_system_webhook(message, level)
                results['webhook'] = webhook_result
            
            logger.info(f"Notificação do sistema enviada ({level}): {message}")
            return {'status': 'sent', 'results': results}
            
        except Exception as e:
            logger.error(f"Erro ao enviar notificação do sistema: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def _should_notify_signal(self, signal_data: Dict) -> bool:
        """Verifica se deve notificar baseado nos filtros"""
        try:
            # Filtro de confiança
            confidence = signal_data.get('confidence', 100)
            if confidence < self.filters['min_confidence']:
                return False
            
            # Filtro de estratégias
            if self.filters['strategies']:
                strategy = signal_data.get('strategy', '')
                if strategy not in self.filters['strategies']:
                    return False
            
            # Filtro de símbolos
            if self.filters['symbols']:
                symbol = signal_data.get('symbol', '')
                if symbol not in self.filters['symbols']:
                    return False
            
            # Filtro de tipos de sinal
            signal_type = signal_data.get('signal_type', '')
            if signal_type not in self.filters['signal_types']:
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Erro ao aplicar filtros: {e}")
            return True  # Em caso de erro, permitir notificação
    
    def update_filters(self, new_filters: Dict) -> bool:
        """Atualiza filtros de notificação"""
        try:
            self.filters.update(new_filters)
            self.config['filters'] = self.filters
            return self.save_config()
        except Exception as e:
            logger.error(f"Erro ao atualizar filtros: {e}")
            return False
    
    def add_email_recipient(self, email: str) -> bool:
        """Adiciona destinatário de email"""
        try:
            recipients = self.config.get('email', {}).get('recipients', [])
            if email not in recipients:
                recipients.append(email)
                self.config['email']['recipients'] = recipients
                return self.save_config()
            return True
        except Exception as e:
            logger.error(f"Erro ao adicionar destinatário: {e}")
            return False
    
    def remove_email_recipient(self, email: str) -> bool:
        """Remove destinatário de email"""
        try:
            recipients = self.config.get('email', {}).get('recipients', [])
            if email in recipients:
                recipients.remove(email)
                self.config['email']['recipients'] = recipients
                return self.save_config()
            return True
        except Exception as e:
            logger.error(f"Erro ao remover destinatário: {e}")
            return False
    
    def add_push_subscription(self, subscription_data: Dict) -> bool:
        """Adiciona assinatura push"""
        return self.push_notifier.add_subscription(subscription_data)
    
    def add_webhook(self, webhook_config: Dict) -> bool:
        """Adiciona webhook"""
        success = self.webhook_notifier.add_webhook(webhook_config)
        if success:
            self.config['webhook']['webhooks'] = self.webhook_notifier.webhooks
            self.save_config()
        return success
    
    def get_status(self) -> Dict:
        """Retorna status de todos os notificadores"""
        return {
            'enabled': self.enabled,
            'email': {
                'enabled': self.email_notifier.enabled,
                'recipients_count': len(self.config.get('email', {}).get('recipients', []))
            },
            'push': {
                'enabled': self.push_notifier.enabled,
                'subscriptions_count': self.push_notifier.get_subscription_count()
            },
            'webhook': {
                'enabled': self.webhook_notifier.enabled,
                'webhooks_count': self.webhook_notifier.get_webhook_count(),
                'webhooks': self.webhook_notifier.get_webhooks_status()
            },
            'filters': self.filters,
            'rate_limit': self.rate_limit
        }
    
    def test_all_notifications(self) -> Dict:
        """Testa todas as notificações"""
        results = {}
        
        # Teste de email
        if self.email_notifier.enabled:
            results['email'] = self.email_notifier.test_connection()
        
        # Teste de push
        if self.push_notifier.enabled:
            results['push'] = self.push_notifier.test_notification()
        
        # Teste de webhooks
        if self.webhook_notifier.enabled:
            webhook_results = {}
            for webhook in self.webhook_notifier.webhooks:
                webhook_results[webhook['name']] = self.webhook_notifier.test_webhook(webhook['name'])
            results['webhooks'] = webhook_results
        
        return results
