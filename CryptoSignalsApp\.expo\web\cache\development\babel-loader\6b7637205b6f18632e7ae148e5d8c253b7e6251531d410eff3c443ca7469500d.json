{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport React from \"react\";\nimport { createStackNavigator } from \"@react-navigation/stack\";\nimport { createBottomTabNavigator } from \"@react-navigation/bottom-tabs\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport Channels from \"./pages/Channels\";\nimport Signals from \"./pages/Signals\";\nimport Premium from \"./pages/Premium\";\nimport Profile from \"./pages/Profile\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar Tab = createBottomTabNavigator();\nvar Stack = createStackNavigator();\nfunction MainTabNavigator() {\n  var labelStyle = {\n    fontFamily: 'Poppins_500Medium',\n    fontSize: 10\n  };\n  var getTabBarIcon = function getTabBarIcon(routeName, focused) {\n    var iconName;\n    switch (routeName) {\n      case 'Channels':\n        iconName = focused ? '📺' : '📺';\n        break;\n      case 'Premium':\n        iconName = focused ? '💎' : '💎';\n        break;\n      case 'Profile':\n        iconName = focused ? '👤' : '👤';\n        break;\n      default:\n        iconName = '📱';\n    }\n    return iconName;\n  };\n  return _jsxs(Tab.Navigator, {\n    screenOptions: function screenOptions(_ref) {\n      var route = _ref.route;\n      return {\n        headerShown: false,\n        tabBarIcon: function tabBarIcon(_ref2) {\n          var focused = _ref2.focused;\n          return _jsx(Text, {\n            style: {\n              fontSize: focused ? 18 : 16,\n              opacity: focused ? 1 : 0.7,\n              marginBottom: 2\n            },\n            children: getTabBarIcon(route.name, focused)\n          });\n        },\n        tabBarLabelPosition: 'below-icon',\n        tabBarStyle: [{\n          backgroundColor: \"#202020\",\n          height: 70,\n          borderTopColor: \"#5d5d5d\",\n          paddingBottom: 8,\n          paddingTop: 8\n        }],\n        tabBarLabel: function tabBarLabel(_ref3) {\n          var focused = _ref3.focused;\n          return _jsx(Text, {\n            style: _objectSpread(_objectSpread({}, labelStyle), {}, {\n              color: focused ? \"#FECB37\" : \"#8a8a8a\",\n              marginTop: 2\n            }),\n            children: route.name\n          });\n        }\n      };\n    },\n    children: [_jsx(Tab.Screen, {\n      name: \"Channels\",\n      component: Channels,\n      options: {\n        tabBarLabel: 'Signals'\n      }\n    }), _jsx(Tab.Screen, {\n      name: \"Premium\",\n      component: Premium,\n      options: {\n        tabBarLabel: 'Premium'\n      }\n    }), _jsx(Tab.Screen, {\n      name: \"Profile\",\n      component: Profile,\n      options: {\n        tabBarLabel: 'Profile'\n      }\n    })]\n  });\n}\nfunction AppNavigator() {\n  return _jsxs(Stack.Navigator, {\n    screenOptions: {\n      headerShown: false\n    },\n    children: [_jsx(Stack.Screen, {\n      name: \"Main\",\n      component: MainTabNavigator\n    }), _jsx(Stack.Screen, {\n      name: \"Signals\",\n      component: Signals\n    })]\n  });\n}\nexport default function Routes() {\n  return _jsx(AppNavigator, {});\n}", "map": {"version": 3, "names": ["React", "createStackNavigator", "createBottomTabNavigator", "Text", "Channels", "Signals", "Premium", "Profile", "jsx", "_jsx", "jsxs", "_jsxs", "Tab", "<PERSON><PERSON>", "MainTabNavigator", "labelStyle", "fontFamily", "fontSize", "getTabBarIcon", "routeName", "focused", "iconName", "Navigator", "screenOptions", "_ref", "route", "headerShown", "tabBarIcon", "_ref2", "style", "opacity", "marginBottom", "children", "name", "tabBarLabelPosition", "tabBarStyle", "backgroundColor", "height", "borderTopColor", "paddingBottom", "paddingTop", "tabBarLabel", "_ref3", "_objectSpread", "color", "marginTop", "Screen", "component", "options", "AppNavigator", "Routes"], "sources": ["C:/Users/<USER>/Desktop/Signals/CryptoSignalsApp/src/routes.js"], "sourcesContent": ["import React from \"react\";\r\nimport { createStackNavigator } from \"@react-navigation/stack\";\r\nimport { createBottomTabNavigator } from \"@react-navigation/bottom-tabs\";\r\nimport { Text } from \"react-native\";\r\n\r\n// Import essential pages only\r\nimport Channels from \"./pages/Channels\";\r\nimport Signals from \"./pages/Signals\";\r\nimport Premium from \"./pages/Premium\";\r\nimport Profile from \"./pages/Profile\";\r\n\r\nconst Tab = createBottomTabNavigator();\r\nconst Stack = createStackNavigator();\r\n\r\n// Simplified Tab Navigator with essential features only\r\nfunction MainTabNavigator() {\r\n  const labelStyle = {\r\n    fontFamily: 'Poppins_500Medium',\r\n    fontSize: 10,\r\n  }\r\n\r\n  const getTabBarIcon = (routeName, focused) => {\r\n    let iconName;\r\n    switch (routeName) {\r\n      case 'Channels':\r\n        iconName = focused ? '📺' : '📺';\r\n        break;\r\n      case 'Premium':\r\n        iconName = focused ? '💎' : '💎';\r\n        break;\r\n      case 'Profile':\r\n        iconName = focused ? '👤' : '👤';\r\n        break;\r\n      default:\r\n        iconName = '📱';\r\n    }\r\n    return iconName;\r\n  };\r\n\r\n  return (\r\n    <Tab.Navigator\r\n      screenOptions={({ route }) => ({\r\n        headerShown: false,\r\n        tabBarIcon: ({ focused }) => (\r\n          <Text style={{\r\n            fontSize: focused ? 18 : 16,\r\n            opacity: focused ? 1 : 0.7,\r\n            marginBottom: 2\r\n          }}>\r\n            {getTabBarIcon(route.name, focused)}\r\n          </Text>\r\n        ),\r\n        tabBarLabelPosition: 'below-icon',\r\n        tabBarStyle: [\r\n          {\r\n            backgroundColor: \"#202020\",\r\n            height: 70,\r\n            borderTopColor: \"#5d5d5d\",\r\n            paddingBottom: 8,\r\n            paddingTop: 8,\r\n          },\r\n        ],\r\n        tabBarLabel: ({ focused }) => {\r\n          return (\r\n            <Text style={{\r\n              ...labelStyle,\r\n              color: focused ? \"#FECB37\" : \"#8a8a8a\",\r\n              marginTop: 2\r\n            }}>\r\n              {route.name}\r\n            </Text>\r\n          );\r\n        },\r\n      })}\r\n    >\r\n      <Tab.Screen\r\n        name=\"Channels\"\r\n        component={Channels}\r\n        options={{\r\n          tabBarLabel: 'Signals'\r\n        }}\r\n      />\r\n      <Tab.Screen\r\n        name=\"Premium\"\r\n        component={Premium}\r\n        options={{\r\n          tabBarLabel: 'Premium'\r\n        }}\r\n      />\r\n      <Tab.Screen\r\n        name=\"Profile\"\r\n        component={Profile}\r\n        options={{\r\n          tabBarLabel: 'Profile'\r\n        }}\r\n      />\r\n    </Tab.Navigator>\r\n  );\r\n}\r\n\r\n// Stack Navigator for modal screens and navigation\r\nfunction AppNavigator() {\r\n  return (\r\n    <Stack.Navigator screenOptions={{ headerShown: false }}>\r\n      <Stack.Screen name=\"Main\" component={MainTabNavigator} />\r\n      <Stack.Screen name=\"Signals\" component={Signals} />\r\n    </Stack.Navigator>\r\n  );\r\n}\r\n\r\nexport default function Routes() {\r\n  return (\r\n    <AppNavigator />\r\n  );\r\n}\r\n\r\n/*\r\nAqui estão as mudanças que fiz:\r\n\r\nIntegrei o Drawer.Navigator diretamente no Routes para evitar redundância.\r\nRemovi a função AppNavigator que não estava sendo utilizada.\r\nA estrutura agora é que temos um Stack.Navigator principal com uma combinação de Drawer.Navigator e\r\n Tab.Navigator para uma melhor organização e navegação.\r\n*/"], "mappings": ";;;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,oBAAoB,QAAQ,yBAAyB;AAC9D,SAASC,wBAAwB,QAAQ,+BAA+B;AAAC,OAAAC,IAAA;AAIzE,OAAOC,QAAQ;AACf,OAAOC,OAAO;AACd,OAAOC,OAAO;AACd,OAAOC,OAAO;AAAwB,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAEtC,IAAMC,GAAG,GAAGV,wBAAwB,CAAC,CAAC;AACtC,IAAMW,KAAK,GAAGZ,oBAAoB,CAAC,CAAC;AAGpC,SAASa,gBAAgBA,CAAA,EAAG;EAC1B,IAAMC,UAAU,GAAG;IACjBC,UAAU,EAAE,mBAAmB;IAC/BC,QAAQ,EAAE;EACZ,CAAC;EAED,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,SAAS,EAAEC,OAAO,EAAK;IAC5C,IAAIC,QAAQ;IACZ,QAAQF,SAAS;MACf,KAAK,UAAU;QACbE,QAAQ,GAAGD,OAAO,GAAG,IAAI,GAAG,IAAI;QAChC;MACF,KAAK,SAAS;QACZC,QAAQ,GAAGD,OAAO,GAAG,IAAI,GAAG,IAAI;QAChC;MACF,KAAK,SAAS;QACZC,QAAQ,GAAGD,OAAO,GAAG,IAAI,GAAG,IAAI;QAChC;MACF;QACEC,QAAQ,GAAG,IAAI;IACnB;IACA,OAAOA,QAAQ;EACjB,CAAC;EAED,OACEV,KAAA,CAACC,GAAG,CAACU,SAAS;IACZC,aAAa,EAAE,SAAfA,aAAaA,CAAAC,IAAA;MAAA,IAAKC,KAAK,GAAAD,IAAA,CAALC,KAAK;MAAA,OAAQ;QAC7BC,WAAW,EAAE,KAAK;QAClBC,UAAU,EAAE,SAAZA,UAAUA,CAAAC,KAAA;UAAA,IAAKR,OAAO,GAAAQ,KAAA,CAAPR,OAAO;UAAA,OACpBX,IAAA,CAACN,IAAI;YAAC0B,KAAK,EAAE;cACXZ,QAAQ,EAAEG,OAAO,GAAG,EAAE,GAAG,EAAE;cAC3BU,OAAO,EAAEV,OAAO,GAAG,CAAC,GAAG,GAAG;cAC1BW,YAAY,EAAE;YAChB,CAAE;YAAAC,QAAA,EACCd,aAAa,CAACO,KAAK,CAACQ,IAAI,EAAEb,OAAO;UAAC,CAC/B,CAAC;QAAA,CACR;QACDc,mBAAmB,EAAE,YAAY;QACjCC,WAAW,EAAE,CACX;UACEC,eAAe,EAAE,SAAS;UAC1BC,MAAM,EAAE,EAAE;UACVC,cAAc,EAAE,SAAS;UACzBC,aAAa,EAAE,CAAC;UAChBC,UAAU,EAAE;QACd,CAAC,CACF;QACDC,WAAW,EAAE,SAAbA,WAAWA,CAAAC,KAAA,EAAmB;UAAA,IAAdtB,OAAO,GAAAsB,KAAA,CAAPtB,OAAO;UACrB,OACEX,IAAA,CAACN,IAAI;YAAC0B,KAAK,EAAAc,aAAA,CAAAA,aAAA,KACN5B,UAAU;cACb6B,KAAK,EAAExB,OAAO,GAAG,SAAS,GAAG,SAAS;cACtCyB,SAAS,EAAE;YAAC,EACZ;YAAAb,QAAA,EACCP,KAAK,CAACQ;UAAI,CACP,CAAC;QAEX;MACF,CAAC;IAAA,CAAE;IAAAD,QAAA,GAEHvB,IAAA,CAACG,GAAG,CAACkC,MAAM;MACTb,IAAI,EAAC,UAAU;MACfc,SAAS,EAAE3C,QAAS;MACpB4C,OAAO,EAAE;QACPP,WAAW,EAAE;MACf;IAAE,CACH,CAAC,EACFhC,IAAA,CAACG,GAAG,CAACkC,MAAM;MACTb,IAAI,EAAC,SAAS;MACdc,SAAS,EAAEzC,OAAQ;MACnB0C,OAAO,EAAE;QACPP,WAAW,EAAE;MACf;IAAE,CACH,CAAC,EACFhC,IAAA,CAACG,GAAG,CAACkC,MAAM;MACTb,IAAI,EAAC,SAAS;MACdc,SAAS,EAAExC,OAAQ;MACnByC,OAAO,EAAE;QACPP,WAAW,EAAE;MACf;IAAE,CACH,CAAC;EAAA,CACW,CAAC;AAEpB;AAGA,SAASQ,YAAYA,CAAA,EAAG;EACtB,OACEtC,KAAA,CAACE,KAAK,CAACS,SAAS;IAACC,aAAa,EAAE;MAAEG,WAAW,EAAE;IAAM,CAAE;IAAAM,QAAA,GACrDvB,IAAA,CAACI,KAAK,CAACiC,MAAM;MAACb,IAAI,EAAC,MAAM;MAACc,SAAS,EAAEjC;IAAiB,CAAE,CAAC,EACzDL,IAAA,CAACI,KAAK,CAACiC,MAAM;MAACb,IAAI,EAAC,SAAS;MAACc,SAAS,EAAE1C;IAAQ,CAAE,CAAC;EAAA,CACpC,CAAC;AAEtB;AAEA,eAAe,SAAS6C,MAAMA,CAAA,EAAG;EAC/B,OACEzC,IAAA,CAACwC,YAAY,IAAE,CAAC;AAEpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}