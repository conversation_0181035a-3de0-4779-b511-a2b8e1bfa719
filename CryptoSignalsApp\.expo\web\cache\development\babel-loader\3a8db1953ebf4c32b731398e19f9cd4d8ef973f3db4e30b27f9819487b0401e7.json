{"ast": null, "code": "import View from \"react-native-web/dist/exports/View\";\nvar ScreenFooter = View;\nvar FooterComponent = View;\nexport default ScreenFooter;\nexport { FooterComponent };", "map": {"version": 3, "names": ["ScreenFooter", "View", "FooterComponent"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\react-native-screens\\src\\components\\ScreenFooter.web.tsx"], "sourcesContent": ["import { View } from 'react-native';\n\nconst ScreenFooter = View;\nconst FooterComponent = View;\n\nexport default ScreenFooter;\nexport { FooterComponent };\n"], "mappings": ";AAEA,IAAMA,YAAY,GAAGC,IAAI;AACzB,IAAMC,eAAe,GAAGD,IAAI;AAE5B,eAAeD,YAAY;AAC3B,SAASE,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}