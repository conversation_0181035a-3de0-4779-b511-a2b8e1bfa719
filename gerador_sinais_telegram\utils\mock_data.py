import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON><PERSON>

def generate_mock_data(symbol, interval, start_date, end_date):
    """
    Gera dados simulados para backtesting
    
    Args:
        symbol: Par de trading
        interval: Timeframe
        start_date: Data inicial
        end_date: Data final
        
    Returns:
        DataFrame com dados simulados
    """
    # Definir número de candles baseado no intervalo
    interval_minutes = {
        '1m': 1,
        '5m': 5,
        '15m': 15,
        '1h': 60,
        '4h': 240,
        '1d': 1440
    }
    
    minutes = interval_minutes.get(interval, 1)
    total_minutes = int((end_date - start_date).total_seconds() / 60)
    num_candles = total_minutes // minutes
    
    # Gerar timestamps
    timestamps = [start_date + timedelta(minutes=i*minutes) for i in range(num_candles)]
    
    # Gerar preços simulados
    base_price = 50000 if 'BTC' in symbol else (2000 if 'ETH' in symbol else 200)
    volatility = 0.002  # 0.2% de volatilidade por candle
    
    prices = [base_price]
    for _ in range(num_candles-1):
        change = np.random.normal(0, volatility)
        new_price = prices[-1] * (1 + change)
        prices.append(new_price)
    
    # Criar DataFrame
    df = pd.DataFrame({
        'timestamp': timestamps,
        'open': prices,
        'high': [p * (1 + abs(np.random.normal(0, volatility/2))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, volatility/2))) for p in prices],
        'close': [p * (1 + np.random.normal(0, volatility/2)) for p in prices],
        'volume': [abs(np.random.normal(100, 30)) * base_price for _ in range(num_candles)],
        'close_time': [t + timedelta(minutes=minutes-1) for t in timestamps],
        'quote_volume': [abs(np.random.normal(100, 30)) for _ in range(num_candles)],
        'trades': [int(abs(np.random.normal(1000, 300))) for _ in range(num_candles)],
        'taker_buy_base': [abs(np.random.normal(50, 15)) for _ in range(num_candles)],
        'taker_buy_quote': [abs(np.random.normal(50, 15)) * base_price for _ in range(num_candles)],
        'ignore': [0 for _ in range(num_candles)]
    })
    
    return df 