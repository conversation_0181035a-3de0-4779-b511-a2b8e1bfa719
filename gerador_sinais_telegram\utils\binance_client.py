import time
import logging
import pandas as pd
from binance.client import Client
from binance.exceptions import BinanceAPIException
from config.settings import BINANCE_API_KEY, BINANCE_API_SECRET, BINANCE_TESTNET
from utils.mock_data import generate_mock_data
import numpy as np

logger = logging.getLogger(__name__)

class BinanceHandler:
    def __init__(self, use_mock_data=True):
        self.client = Client(BINANCE_API_KEY, BINANCE_API_SECRET)
        if BINANCE_TESTNET:
            self.client.API_URL = 'https://testnet.binancefuture.com/fapi'
            self.client.FUTURES_API_URL = 'https://testnet.binancefuture.com/fapi'
        self.last_request_time = 0
        self.request_interval = 0.5  # Intervalo mínimo entre requisições (500ms)
        self.use_mock_data = use_mock_data
        self.sync_time()

    def sync_time(self):
        """Sincroniza o relógio local com o da Binance"""
        try:
            if not self.use_mock_data:
                server_time = self.client.get_server_time()["serverTime"]
                local_time = int(time.time() * 1000)
                offset = server_time - local_time
                logger.info(f"Sincronização de tempo ajustada. Offset: {offset} ms")
                self.client.timestamp_offset = offset
                return offset
        except Exception as e:
            logger.error(f"Erro ao sincronizar tempo com a Binance: {e}")
            return 0

    def _rate_limit(self):
        """Implementa limitação de taxa para evitar erros de API"""
        if not self.use_mock_data:
            current_time = time.time()
            elapsed = current_time - self.last_request_time
            if elapsed < self.request_interval:
                time.sleep(self.request_interval - elapsed)
            self.last_request_time = time.time()

    def get_historical_klines(self, symbol, interval, start_ts=None, end_ts=None, lookback_days=None):
        """
        Obtém dados históricos com limitação de taxa

        Args:
            symbol: Par de trading (ex: 'BTCUSDT')
            interval: Timeframe (ex: '1h', '4h', '1d')
            start_ts: Timestamp inicial em milissegundos
            end_ts: Timestamp final em milissegundos
            lookback_days: Número de dias para olhar para trás (alternativa a start_ts/end_ts)
        """
        try:
            if self.use_mock_data:
                if lookback_days:
                    start_date = pd.Timestamp.now() - pd.Timedelta(days=lookback_days)
                    end_date = pd.Timestamp.now()
                else:
                    start_date = pd.Timestamp(start_ts, unit='ms')
                    end_date = pd.Timestamp(end_ts, unit='ms')

                return generate_mock_data(symbol, interval, start_date, end_date)

            self._rate_limit()

            if lookback_days:
                klines = self.client.futures_historical_klines(
                    symbol=symbol,
                    interval=interval,
                    start_str=f"{lookback_days} days ago UTC",
                    limit=1000
                )
            else:
                klines = self.client.futures_historical_klines(
                    symbol=symbol,
                    interval=interval,
                    start_str=str(start_ts),
                    end_str=str(end_ts),
                    limit=1000
                )

            if not klines:
                logger.warning(f"Nenhum dado encontrado para {symbol}")
                return pd.DataFrame()

            # Verificar se todos os klines têm o mesmo número de elementos
            expected_length = 12  # Número padrão de campos nos klines da Binance
            valid_klines = []

            for kline in klines:
                if len(kline) == expected_length:
                    valid_klines.append(kline)
                else:
                    logger.warning(f"Kline inválido para {symbol}: {len(kline)} campos (esperado: {expected_length})")

            if not valid_klines:
                logger.error(f"Nenhum kline válido encontrado para {symbol}")
                return pd.DataFrame()

            df = pd.DataFrame(valid_klines, columns=[
                'OpenTime', 'open', 'high', 'low', 'close', 'volume',
                'CloseTime', 'QuoteAssetVolume', 'NumberOfTrades',
                'TakerBuyBaseAssetVolume', 'TakerBuyQuoteAssetVolume', 'Ignore'
            ])

            # Converter timestamps
            df['OpenTime'] = pd.to_datetime(df['OpenTime'], unit='ms')
            df['CloseTime'] = pd.to_datetime(df['CloseTime'], unit='ms')

            # Converter colunas numéricas com validação
            numeric_cols = ['open', 'high', 'low', 'close', 'volume']
            for col in numeric_cols:
                df[col] = pd.to_numeric(df[col], errors='coerce')

                # Verificar se há valores inválidos
                invalid_count = df[col].isna().sum()
                if invalid_count > 0:
                    logger.warning(f"{invalid_count} valores inválidos em {col} para {symbol}")

            # Remover linhas com dados inválidos
            initial_count = len(df)
            df = df.dropna()
            final_count = len(df)

            if initial_count != final_count:
                logger.warning(f"Removidas {initial_count - final_count} linhas inválidas para {symbol}")

            # Verificar se temos dados suficientes
            if len(df) < 10:
                logger.warning(f"Dados insuficientes para {symbol}: apenas {len(df)} registros")
                return pd.DataFrame()

            # Validar se os preços fazem sentido (não podem ser zero ou negativos)
            if (df[['open', 'high', 'low', 'close']] <= 0).any().any():
                logger.error(f"Preços inválidos (zero ou negativos) encontrados para {symbol}")
                return pd.DataFrame()

            # Validar se high >= low e close está entre high e low
            invalid_ohlc = (df['high'] < df['low']) | (df['close'] > df['high']) | (df['close'] < df['low'])
            if invalid_ohlc.any():
                logger.error(f"Dados OHLC inconsistentes encontrados para {symbol}")
                df = df[~invalid_ohlc]

                if len(df) < 10:
                    logger.error(f"Muitos dados inconsistentes para {symbol}, retornando DataFrame vazio")
                    return pd.DataFrame()

            df.set_index('OpenTime', inplace=True)
            df.sort_index(inplace=True)

            logger.debug(f"Obtidos {len(df)} registros válidos para {symbol}")
            return df
        except BinanceAPIException as e:
            logger.error(f"Erro na API da Binance ao obter klines para {symbol}: {e}")
            return pd.DataFrame()
        except Exception as e:
            logger.error(f"Erro ao obter klines para {symbol}: {e}")
            return pd.DataFrame()

    def get_current_price(self, symbol):
        """Obtém o preço atual de um símbolo com validação"""
        # Verificar se existe um cache de preços simulados
        try:
            cache_file = 'price_cache.txt'
            with open(cache_file, 'r') as f:
                for line in f:
                    if ':' in line:
                        sym, price = line.strip().split(':', 1)
                        if sym == symbol:
                            price_value = float(price)
                            if self._validate_price(symbol, price_value):
                                return price_value
                            else:
                                logger.warning(f"Preço inválido no cache para {symbol}: {price_value}")
        except (FileNotFoundError, ValueError):
            pass

        # Se não encontrou no cache ou houve erro, usar o método padrão
        if self.use_mock_data:
            base_price = self._get_realistic_base_price(symbol)
            return base_price * (1 + np.random.normal(0, 0.001))

        self._rate_limit()
        try:
            # Tente primeiro com futures_ticker_price
            try:
                ticker = self.client.futures_ticker_price(symbol=symbol)
                if isinstance(ticker, list):
                    ticker = ticker[0]
                price = float(ticker['price'])
            except AttributeError:
                # Se não existir, use ticker_price (para spot)
                ticker = self.client.get_symbol_ticker(symbol=symbol)
                price = float(ticker['price'])

            # Validar se o preço é realista
            if self._validate_price(symbol, price):
                return price
            else:
                logger.error(f"Preço irrealista obtido da API para {symbol}: {price}")
                return None

        except Exception as e:
            logger.error(f"Erro ao obter preço de {symbol}: {e}")
            return None

    def _get_realistic_base_price(self, symbol):
        """Retorna preços base realistas para diferentes símbolos"""
        # Preços aproximados realistas (atualizados para 2025)
        price_ranges = {
            'BTC': (40000, 70000),
            'ETH': (1500, 4000),
            'BNB': (200, 600),
            'ADA': (0.3, 1.5),
            'SOL': (80, 300),
            'DOT': (4, 15),
            'LINK': (10, 30),
            'MATIC': (0.5, 2.5),
            'AVAX': (15, 60),
            'ATOM': (5, 25)
        }

        for coin, (min_price, max_price) in price_ranges.items():
            if coin in symbol:
                return np.random.uniform(min_price, max_price)

        # Preço padrão para moedas não mapeadas
        return np.random.uniform(0.1, 10)

    def _validate_price(self, symbol, price):
        """Valida se um preço é realista para um determinado símbolo"""
        if price <= 0:
            return False

        # Faixas de preços realistas (com margem de segurança)
        price_ranges = {
            'BTC': (10000, 150000),
            'ETH': (500, 10000),
            'BNB': (50, 1000),
            'ADA': (0.1, 5),
            'SOL': (10, 1000),
            'DOT': (1, 50),
            'LINK': (1, 100),
            'MATIC': (0.1, 10),
            'AVAX': (5, 200),
            'ATOM': (1, 100)
        }

        for coin, (min_price, max_price) in price_ranges.items():
            if coin in symbol:
                return min_price <= price <= max_price

        # Para moedas não mapeadas, aceitar uma faixa ampla
        return 0.001 <= price <= 100000