{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"children\", \"style\", \"onPress\", \"to\", \"accessibilityRole\"];\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport { Link, useTheme } from '@react-navigation/native';\nimport Color from 'color';\nimport React from 'react';\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport Pressable from \"react-native-web/dist/exports/Pressable\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport TabBarIcon from \"./TabBarIcon\";\nexport default function BottomTabBarItem(_ref) {\n  var focused = _ref.focused,\n    route = _ref.route,\n    descriptor = _ref.descriptor,\n    label = _ref.label,\n    icon = _ref.icon,\n    badge = _ref.badge,\n    badgeStyle = _ref.badgeStyle,\n    to = _ref.to,\n    _ref$button = _ref.button,\n    button = _ref$button === void 0 ? function (_ref2) {\n      var children = _ref2.children,\n        style = _ref2.style,\n        _onPress = _ref2.onPress,\n        to = _ref2.to,\n        accessibilityRole = _ref2.accessibilityRole,\n        rest = _objectWithoutProperties(_ref2, _excluded);\n      if (Platform.OS === 'web' && to) {\n        return React.createElement(Link, _extends({}, rest, {\n          to: to,\n          style: [styles.button, style],\n          onPress: function onPress(e) {\n            if (!(e.metaKey || e.altKey || e.ctrlKey || e.shiftKey) && (e.button == null || e.button === 0)) {\n              e.preventDefault();\n              _onPress === null || _onPress === void 0 ? void 0 : _onPress(e);\n            }\n          }\n        }), children);\n      } else {\n        return React.createElement(Pressable, _extends({}, rest, {\n          accessibilityRole: accessibilityRole,\n          onPress: _onPress,\n          style: style\n        }), children);\n      }\n    } : _ref$button,\n    accessibilityLabel = _ref.accessibilityLabel,\n    testID = _ref.testID,\n    onPress = _ref.onPress,\n    onLongPress = _ref.onLongPress,\n    horizontal = _ref.horizontal,\n    customActiveTintColor = _ref.activeTintColor,\n    customInactiveTintColor = _ref.inactiveTintColor,\n    _ref$activeBackground = _ref.activeBackgroundColor,\n    activeBackgroundColor = _ref$activeBackground === void 0 ? 'transparent' : _ref$activeBackground,\n    _ref$inactiveBackgrou = _ref.inactiveBackgroundColor,\n    inactiveBackgroundColor = _ref$inactiveBackgrou === void 0 ? 'transparent' : _ref$inactiveBackgrou,\n    _ref$showLabel = _ref.showLabel,\n    showLabel = _ref$showLabel === void 0 ? true : _ref$showLabel,\n    allowFontScaling = _ref.allowFontScaling,\n    labelStyle = _ref.labelStyle,\n    iconStyle = _ref.iconStyle,\n    style = _ref.style;\n  var _useTheme = useTheme(),\n    colors = _useTheme.colors;\n  var activeTintColor = customActiveTintColor === undefined ? colors.primary : customActiveTintColor;\n  var inactiveTintColor = customInactiveTintColor === undefined ? Color(colors.text).mix(Color(colors.card), 0.5).hex() : customInactiveTintColor;\n  var renderLabel = function renderLabel(_ref3) {\n    var focused = _ref3.focused;\n    if (showLabel === false) {\n      return null;\n    }\n    var color = focused ? activeTintColor : inactiveTintColor;\n    if (typeof label === 'string') {\n      return React.createElement(Text, {\n        numberOfLines: 1,\n        style: [styles.label, {\n          color: color\n        }, horizontal ? styles.labelBeside : styles.labelBeneath, labelStyle],\n        allowFontScaling: allowFontScaling\n      }, label);\n    }\n    var options = descriptor.options;\n    var children = typeof options.tabBarLabel === 'string' ? options.tabBarLabel : options.title !== undefined ? options.title : route.name;\n    return label({\n      focused: focused,\n      color: color,\n      position: horizontal ? 'beside-icon' : 'below-icon',\n      children: children\n    });\n  };\n  var renderIcon = function renderIcon(_ref4) {\n    var focused = _ref4.focused;\n    if (icon === undefined) {\n      return null;\n    }\n    var activeOpacity = focused ? 1 : 0;\n    var inactiveOpacity = focused ? 0 : 1;\n    return React.createElement(TabBarIcon, {\n      route: route,\n      horizontal: horizontal,\n      badge: badge,\n      badgeStyle: badgeStyle,\n      activeOpacity: activeOpacity,\n      inactiveOpacity: inactiveOpacity,\n      activeTintColor: activeTintColor,\n      inactiveTintColor: inactiveTintColor,\n      renderIcon: icon,\n      style: iconStyle\n    });\n  };\n  var scene = {\n    route: route,\n    focused: focused\n  };\n  var backgroundColor = focused ? activeBackgroundColor : inactiveBackgroundColor;\n  return button({\n    to: to,\n    onPress: onPress,\n    onLongPress: onLongPress,\n    testID: testID,\n    accessibilityLabel: accessibilityLabel,\n    accessibilityRole: Platform.select({\n      ios: 'button',\n      default: 'tab'\n    }),\n    accessibilityState: {\n      selected: focused\n    },\n    accessibilityStates: focused ? ['selected'] : [],\n    style: [styles.tab, {\n      backgroundColor: backgroundColor\n    }, horizontal ? styles.tabLandscape : styles.tabPortrait, style],\n    children: React.createElement(React.Fragment, null, renderIcon(scene), renderLabel(scene))\n  });\n}\nvar styles = StyleSheet.create({\n  tab: {\n    flex: 1,\n    alignItems: 'center'\n  },\n  tabPortrait: {\n    justifyContent: 'flex-end',\n    flexDirection: 'column'\n  },\n  tabLandscape: {\n    justifyContent: 'center',\n    flexDirection: 'row'\n  },\n  label: {\n    textAlign: 'center',\n    backgroundColor: 'transparent'\n  },\n  labelBeneath: {\n    fontSize: 10\n  },\n  labelBeside: {\n    fontSize: 13,\n    marginLeft: 20,\n    marginTop: 3\n  },\n  button: {\n    display: 'flex'\n  }\n});", "map": {"version": 3, "names": ["Link", "useTheme", "Color", "React", "Platform", "Pressable", "StyleSheet", "Text", "TabBarIcon", "BottomTabBarItem", "_ref", "focused", "route", "descriptor", "label", "icon", "badge", "badgeStyle", "to", "_ref$button", "button", "_ref2", "children", "style", "onPress", "accessibilityRole", "rest", "_objectWithoutProperties", "_excluded", "OS", "createElement", "_extends", "styles", "e", "metaKey", "altKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "preventDefault", "accessibilityLabel", "testID", "onLongPress", "horizontal", "customActiveTintColor", "activeTintColor", "customInactiveTintColor", "inactiveTintColor", "_ref$activeBackground", "activeBackgroundColor", "_ref$inactiveBackgrou", "inactiveBackgroundColor", "_ref$showLabel", "showLabel", "allowFontScaling", "labelStyle", "iconStyle", "_useTheme", "colors", "undefined", "primary", "text", "mix", "card", "hex", "renderLabel", "_ref3", "color", "numberOfLines", "labelBeside", "labelBeneath", "options", "tabBarLabel", "title", "name", "position", "renderIcon", "_ref4", "activeOpacity", "inactiveOpacity", "scene", "backgroundColor", "select", "ios", "default", "accessibilityState", "selected", "accessibilityStates", "tab", "tabLandscape", "tabPortrait", "Fragment", "create", "flex", "alignItems", "justifyContent", "flexDirection", "textAlign", "fontSize", "marginLeft", "marginTop", "display"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\@react-navigation\\bottom-tabs\\src\\views\\BottomTabItem.tsx"], "sourcesContent": ["import { Link, Route, useTheme } from '@react-navigation/native';\nimport Color from 'color';\nimport React from 'react';\nimport {\n  GestureResponderEvent,\n  Platform,\n  Pressable,\n  StyleProp,\n  StyleSheet,\n  Text,\n  TextStyle,\n  ViewStyle,\n} from 'react-native';\n\nimport type {\n  BottomTabBarButtonProps,\n  BottomTabDescriptor,\n  LabelPosition,\n} from '../types';\nimport TabBarIcon from './TabBarIcon';\n\ntype Props = {\n  /**\n   * Whether the tab is focused.\n   */\n  focused: boolean;\n  /**\n   * The route object which should be specified by the tab.\n   */\n  route: Route<string>;\n  /**\n   * The descriptor object for the route.\n   */\n  descriptor: BottomTabDescriptor;\n  /**\n   * The label text of the tab.\n   */\n  label:\n    | string\n    | ((props: {\n        focused: boolean;\n        color: string;\n        position: LabelPosition;\n        children: string;\n      }) => React.ReactNode);\n  /**\n   * Icon to display for the tab.\n   */\n  icon: (props: {\n    focused: boolean;\n    size: number;\n    color: string;\n  }) => React.ReactNode;\n  /**\n   * Text to show in a badge on the tab icon.\n   */\n  badge?: number | string;\n  /**\n   * Custom style for the badge.\n   */\n  badgeStyle?: StyleProp<TextStyle>;\n  /**\n   * URL to use for the link to the tab.\n   */\n  to?: string;\n  /**\n   * The button for the tab. Uses a `TouchableWithoutFeedback` by default.\n   */\n  button?: (props: BottomTabBarButtonProps) => React.ReactNode;\n  /**\n   * The accessibility label for the tab.\n   */\n  accessibilityLabel?: string;\n  /**\n   * An unique ID for testing for the tab.\n   */\n  testID?: string;\n  /**\n   * Function to execute on press in React Native.\n   * On the web, this will use onClick.\n   */\n  onPress: (\n    e: React.MouseEvent<HTMLElement, MouseEvent> | GestureResponderEvent\n  ) => void;\n  /**\n   * Function to execute on long press.\n   */\n  onLongPress: (e: GestureResponderEvent) => void;\n  /**\n   * Whether the label should be aligned with the icon horizontally.\n   */\n  horizontal: boolean;\n  /**\n   * Color for the icon and label when the item is active.\n   */\n  activeTintColor?: string;\n  /**\n   * Color for the icon and label when the item is inactive.\n   */\n  inactiveTintColor?: string;\n  /**\n   * Background color for item when its active.\n   */\n  activeBackgroundColor?: string;\n  /**\n   * Background color for item when its inactive.\n   */\n  inactiveBackgroundColor?: string;\n  /**\n   * Whether to show the label text for the tab.\n   */\n  showLabel?: boolean;\n  /**\n   * Whether to allow scaling the font for the label for accessibility purposes.\n   */\n  allowFontScaling?: boolean;\n  /**\n   * Style object for the label element.\n   */\n  labelStyle?: StyleProp<TextStyle>;\n  /**\n   * Style object for the icon element.\n   */\n  iconStyle?: StyleProp<ViewStyle>;\n  /**\n   * Style object for the wrapper element.\n   */\n  style?: StyleProp<ViewStyle>;\n};\n\nexport default function BottomTabBarItem({\n  focused,\n  route,\n  descriptor,\n  label,\n  icon,\n  badge,\n  badgeStyle,\n  to,\n  button = ({\n    children,\n    style,\n    onPress,\n    to,\n    accessibilityRole,\n    ...rest\n  }: BottomTabBarButtonProps) => {\n    if (Platform.OS === 'web' && to) {\n      // React Native Web doesn't forward `onClick` if we use `TouchableWithoutFeedback`.\n      // We need to use `onClick` to be able to prevent default browser handling of links.\n      return (\n        <Link\n          {...rest}\n          to={to}\n          style={[styles.button, style]}\n          onPress={(e: any) => {\n            if (\n              !(e.metaKey || e.altKey || e.ctrlKey || e.shiftKey) && // ignore clicks with modifier keys\n              (e.button == null || e.button === 0) // ignore everything but left clicks\n            ) {\n              e.preventDefault();\n              onPress?.(e);\n            }\n          }}\n        >\n          {children}\n        </Link>\n      );\n    } else {\n      return (\n        <Pressable\n          {...rest}\n          accessibilityRole={accessibilityRole}\n          onPress={onPress}\n          style={style}\n        >\n          {children}\n        </Pressable>\n      );\n    }\n  },\n  accessibilityLabel,\n  testID,\n  onPress,\n  onLongPress,\n  horizontal,\n  activeTintColor: customActiveTintColor,\n  inactiveTintColor: customInactiveTintColor,\n  activeBackgroundColor = 'transparent',\n  inactiveBackgroundColor = 'transparent',\n  showLabel = true,\n  allowFontScaling,\n  labelStyle,\n  iconStyle,\n  style,\n}: Props) {\n  const { colors } = useTheme();\n\n  const activeTintColor =\n    customActiveTintColor === undefined\n      ? colors.primary\n      : customActiveTintColor;\n\n  const inactiveTintColor =\n    customInactiveTintColor === undefined\n      ? Color(colors.text).mix(Color(colors.card), 0.5).hex()\n      : customInactiveTintColor;\n\n  const renderLabel = ({ focused }: { focused: boolean }) => {\n    if (showLabel === false) {\n      return null;\n    }\n\n    const color = focused ? activeTintColor : inactiveTintColor;\n\n    if (typeof label === 'string') {\n      return (\n        <Text\n          numberOfLines={1}\n          style={[\n            styles.label,\n            { color },\n            horizontal ? styles.labelBeside : styles.labelBeneath,\n            labelStyle,\n          ]}\n          allowFontScaling={allowFontScaling}\n        >\n          {label}\n        </Text>\n      );\n    }\n\n    const { options } = descriptor;\n    const children =\n      typeof options.tabBarLabel === 'string'\n        ? options.tabBarLabel\n        : options.title !== undefined\n        ? options.title\n        : route.name;\n\n    return label({\n      focused,\n      color,\n      position: horizontal ? 'beside-icon' : 'below-icon',\n      children,\n    });\n  };\n\n  const renderIcon = ({ focused }: { focused: boolean }) => {\n    if (icon === undefined) {\n      return null;\n    }\n\n    const activeOpacity = focused ? 1 : 0;\n    const inactiveOpacity = focused ? 0 : 1;\n\n    return (\n      <TabBarIcon\n        route={route}\n        horizontal={horizontal}\n        badge={badge}\n        badgeStyle={badgeStyle}\n        activeOpacity={activeOpacity}\n        inactiveOpacity={inactiveOpacity}\n        activeTintColor={activeTintColor}\n        inactiveTintColor={inactiveTintColor}\n        renderIcon={icon}\n        style={iconStyle}\n      />\n    );\n  };\n\n  const scene = { route, focused };\n\n  const backgroundColor = focused\n    ? activeBackgroundColor\n    : inactiveBackgroundColor;\n\n  return button({\n    to,\n    onPress,\n    onLongPress,\n    testID,\n    accessibilityLabel,\n    // FIXME: accessibilityRole: 'tab' doesn't seem to work as expected on iOS\n    accessibilityRole: Platform.select({ ios: 'button', default: 'tab' }),\n    accessibilityState: { selected: focused },\n    // @ts-expect-error: keep for compatibility with older React Native versions\n    accessibilityStates: focused ? ['selected'] : [],\n    style: [\n      styles.tab,\n      { backgroundColor },\n      horizontal ? styles.tabLandscape : styles.tabPortrait,\n      style,\n    ],\n    children: (\n      <React.Fragment>\n        {renderIcon(scene)}\n        {renderLabel(scene)}\n      </React.Fragment>\n    ),\n  }) as React.ReactElement;\n}\n\nconst styles = StyleSheet.create({\n  tab: {\n    flex: 1,\n    alignItems: 'center',\n  },\n  tabPortrait: {\n    justifyContent: 'flex-end',\n    flexDirection: 'column',\n  },\n  tabLandscape: {\n    justifyContent: 'center',\n    flexDirection: 'row',\n  },\n  label: {\n    textAlign: 'center',\n    backgroundColor: 'transparent',\n  },\n  labelBeneath: {\n    fontSize: 10,\n  },\n  labelBeside: {\n    fontSize: 13,\n    marginLeft: 20,\n    marginTop: 3,\n  },\n  button: {\n    display: 'flex',\n  },\n});\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA,SAASA,IAAI,EAASC,QAAQ,QAAQ,0BAA0B;AAChE,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAAA,OAAAC,SAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAiBzB,OAAOC,UAAU;AA+GjB,eAAe,SAASC,gBAAgBA,CAAAC,IAAA,EAiE9B;EAAA,IAhERC,OAAO,GAgEDD,IAAA,CAhENC,OAAO;IACPC,KAAK,GA+DCF,IAAA,CA/DNE,KAAK;IACLC,UAAU,GA8DJH,IAAA,CA9DNG,UAAU;IACVC,KAAK,GA6DCJ,IAAA,CA7DNI,KAAK;IACLC,IAAI,GA4DEL,IAAA,CA5DNK,IAAI;IACJC,KAAK,GA2DCN,IAAA,CA3DNM,KAAK;IACLC,UAAU,GA0DJP,IAAA,CA1DNO,UAAU;IACVC,EAAE,GAyDIR,IAAA,CAzDNQ,EAAE;IAAAC,WAAA,GAyDIT,IAAA,CAxDNU,MAAM;IAANA,MAAM,GAAAD,WAAA,cAAG,UAAAE,KAAA,EAOsB;MAAA,IAN7BC,QAAQ,GAMgBD,KAAA,CANxBC,QAAQ;QACRC,KAAK,GAKmBF,KAAA,CALxBE,KAAK;QACLC,QAAO,GAIiBH,KAAA,CAJxBG,OAAO;QACPN,EAAE,GAGsBG,KAAA,CAHxBH,EAAE;QACFO,iBAAiB,GAEOJ,KAAA,CAFxBI,iBAAiB;QACdC,IAAA,GAAAC,wBAAA,CACqBN,KAAA,EAAAO,SAAA;MACxB,IAAIxB,QAAQ,CAACyB,EAAE,KAAK,KAAK,IAAIX,EAAE,EAAE;QAG/B,OACEf,KAAA,CAAA2B,aAAA,CAAC9B,IAAI,EAAA+B,QAAA,KACCL,IAAI;UACRR,EAAE,EAAEA,EAAG;UACPK,KAAK,EAAE,CAACS,MAAM,CAACZ,MAAM,EAAEG,KAAK,CAAE;UAC9BC,OAAO,EAAG,SAAVA,OAAOA,CAAGS,CAAM,EAAK;YACnB,IACE,EAAEA,CAAC,CAACC,OAAO,IAAID,CAAC,CAACE,MAAM,IAAIF,CAAC,CAACG,OAAO,IAAIH,CAAC,CAACI,QAAQ,CAAC,KAClDJ,CAAC,CAACb,MAAM,IAAI,IAAI,IAAIa,CAAC,CAACb,MAAM,KAAK,CAAC,CAAC,EACpC;cACAa,CAAC,CAACK,cAAc,EAAE;cAClBd,QAAO,aAAPA,QAAO,uBAAPA,QAAO,CAAGS,CAAC,CAAC;YACd;UACF;QAAE,IAEDX,QAAQ,CACJ;MAEX,CAAC,MAAM;QACL,OACEnB,KAAA,CAAA2B,aAAA,CAACzB,SAAS,EAAA0B,QAAA,KACJL,IAAI;UACRD,iBAAiB,EAAEA,iBAAkB;UACrCD,OAAO,EAAEA,QAAQ;UACjBD,KAAK,EAAEA;QAAM,IAEZD,QAAQ,CACC;MAEhB;IACF,CAAC,GAAAH,WAAA;IACDoB,kBAAkB,GAcZ7B,IAAA,CAdN6B,kBAAkB;IAClBC,MAAM,GAaA9B,IAAA,CAbN8B,MAAM;IACNhB,OAAO,GAYDd,IAAA,CAZNc,OAAO;IACPiB,WAAW,GAWL/B,IAAA,CAXN+B,WAAW;IACXC,UAAU,GAUJhC,IAAA,CAVNgC,UAAU;IACOC,qBAAqB,GAShCjC,IAAA,CATNkC,eAAe;IACIC,uBAAuB,GAQpCnC,IAAA,CARNoC,iBAAiB;IAAAC,qBAAA,GAQXrC,IAAA,CAPNsC,qBAAqB;IAArBA,qBAAqB,GAAAD,qBAAA,cAAG,aAAa,GAAAA,qBAAA;IAAAE,qBAAA,GAO/BvC,IAAA,CANNwC,uBAAuB;IAAvBA,uBAAuB,GAAAD,qBAAA,cAAG,aAAa,GAAAA,qBAAA;IAAAE,cAAA,GAMjCzC,IAAA,CALN0C,SAAS;IAATA,SAAS,GAAAD,cAAA,cAAG,IAAI,GAAAA,cAAA;IAChBE,gBAAgB,GAIV3C,IAAA,CAJN2C,gBAAgB;IAChBC,UAAU,GAGJ5C,IAAA,CAHN4C,UAAU;IACVC,SAAS,GAEH7C,IAAA,CAFN6C,SAAS;IACThC,KAAA,GACMb,IAAA,CADNa,KAAA;EAEA,IAAAiC,SAAA,GAAmBvD,QAAQ,EAAE;IAArBwD,MAAA,GAAAD,SAAA,CAAAC,MAAA;EAER,IAAMb,eAAe,GACnBD,qBAAqB,KAAKe,SAAS,GAC/BD,MAAM,CAACE,OAAO,GACdhB,qBAAqB;EAE3B,IAAMG,iBAAiB,GACrBD,uBAAuB,KAAKa,SAAS,GACjCxD,KAAK,CAACuD,MAAM,CAACG,IAAI,CAAC,CAACC,GAAG,CAAC3D,KAAK,CAACuD,MAAM,CAACK,IAAI,CAAC,EAAE,GAAG,CAAC,CAACC,GAAG,EAAE,GACrDlB,uBAAuB;EAE7B,IAAMmB,WAAW,GAAG,SAAdA,WAAWA,CAAGC,KAAA,EAAuC;IAAA,IAApCtD,OAAA,GAA+BsD,KAAA,CAA/BtD,OAAA;IACrB,IAAIyC,SAAS,KAAK,KAAK,EAAE;MACvB,OAAO,IAAI;IACb;IAEA,IAAMc,KAAK,GAAGvD,OAAO,GAAGiC,eAAe,GAAGE,iBAAiB;IAE3D,IAAI,OAAOhC,KAAK,KAAK,QAAQ,EAAE;MAC7B,OACEX,KAAA,CAAA2B,aAAA,CAACvB,IAAI;QACH4D,aAAa,EAAE,CAAE;QACjB5C,KAAK,EAAE,CACLS,MAAM,CAAClB,KAAK,EACZ;UAAEoD,KAAA,EAAAA;QAAM,CAAC,EACTxB,UAAU,GAAGV,MAAM,CAACoC,WAAW,GAAGpC,MAAM,CAACqC,YAAY,EACrDf,UAAU,CACV;QACFD,gBAAgB,EAAEA;MAAiB,GAElCvC,KAAK,CACD;IAEX;IAEA,IAAQwD,OAAA,GAAYzD,UAAU,CAAtByD,OAAA;IACR,IAAMhD,QAAQ,GACZ,OAAOgD,OAAO,CAACC,WAAW,KAAK,QAAQ,GACnCD,OAAO,CAACC,WAAW,GACnBD,OAAO,CAACE,KAAK,KAAKd,SAAS,GAC3BY,OAAO,CAACE,KAAK,GACb5D,KAAK,CAAC6D,IAAI;IAEhB,OAAO3D,KAAK,CAAC;MACXH,OAAO,EAAPA,OAAO;MACPuD,KAAK,EAALA,KAAK;MACLQ,QAAQ,EAAEhC,UAAU,GAAG,aAAa,GAAG,YAAY;MACnDpB,QAAA,EAAAA;IACF,CAAC,CAAC;EACJ,CAAC;EAED,IAAMqD,UAAU,GAAG,SAAbA,UAAUA,CAAGC,KAAA,EAAuC;IAAA,IAApCjE,OAAA,GAA+BiE,KAAA,CAA/BjE,OAAA;IACpB,IAAII,IAAI,KAAK2C,SAAS,EAAE;MACtB,OAAO,IAAI;IACb;IAEA,IAAMmB,aAAa,GAAGlE,OAAO,GAAG,CAAC,GAAG,CAAC;IACrC,IAAMmE,eAAe,GAAGnE,OAAO,GAAG,CAAC,GAAG,CAAC;IAEvC,OACER,KAAA,CAAA2B,aAAA,CAACtB,UAAU;MACTI,KAAK,EAAEA,KAAM;MACb8B,UAAU,EAAEA,UAAW;MACvB1B,KAAK,EAAEA,KAAM;MACbC,UAAU,EAAEA,UAAW;MACvB4D,aAAa,EAAEA,aAAc;MAC7BC,eAAe,EAAEA,eAAgB;MACjClC,eAAe,EAAEA,eAAgB;MACjCE,iBAAiB,EAAEA,iBAAkB;MACrC6B,UAAU,EAAE5D,IAAK;MACjBQ,KAAK,EAAEgC;IAAU,EACjB;EAEN,CAAC;EAED,IAAMwB,KAAK,GAAG;IAAEnE,KAAK,EAALA,KAAK;IAAED,OAAA,EAAAA;EAAQ,CAAC;EAEhC,IAAMqE,eAAe,GAAGrE,OAAO,GAC3BqC,qBAAqB,GACrBE,uBAAuB;EAE3B,OAAO9B,MAAM,CAAC;IACZF,EAAE,EAAFA,EAAE;IACFM,OAAO,EAAPA,OAAO;IACPiB,WAAW,EAAXA,WAAW;IACXD,MAAM,EAANA,MAAM;IACND,kBAAkB,EAAlBA,kBAAkB;IAElBd,iBAAiB,EAAErB,QAAQ,CAAC6E,MAAM,CAAC;MAAEC,GAAG,EAAE,QAAQ;MAAEC,OAAO,EAAE;IAAM,CAAC,CAAC;IACrEC,kBAAkB,EAAE;MAAEC,QAAQ,EAAE1E;IAAQ,CAAC;IAEzC2E,mBAAmB,EAAE3E,OAAO,GAAG,CAAC,UAAU,CAAC,GAAG,EAAE;IAChDY,KAAK,EAAE,CACLS,MAAM,CAACuD,GAAG,EACV;MAAEP,eAAA,EAAAA;IAAgB,CAAC,EACnBtC,UAAU,GAAGV,MAAM,CAACwD,YAAY,GAAGxD,MAAM,CAACyD,WAAW,EACrDlE,KAAK,CACN;IACDD,QAAQ,EACNnB,KAAA,CAAA2B,aAAA,CAAC3B,KAAK,CAACuF,QAAQ,QACZf,UAAU,CAACI,KAAK,CAAC,EACjBf,WAAW,CAACe,KAAK,CAAC;EAGzB,CAAC,CAAC;AACJ;AAEA,IAAM/C,MAAM,GAAG1B,UAAU,CAACqF,MAAM,CAAC;EAC/BJ,GAAG,EAAE;IACHK,IAAI,EAAE,CAAC;IACPC,UAAU,EAAE;EACd,CAAC;EACDJ,WAAW,EAAE;IACXK,cAAc,EAAE,UAAU;IAC1BC,aAAa,EAAE;EACjB,CAAC;EACDP,YAAY,EAAE;IACZM,cAAc,EAAE,QAAQ;IACxBC,aAAa,EAAE;EACjB,CAAC;EACDjF,KAAK,EAAE;IACLkF,SAAS,EAAE,QAAQ;IACnBhB,eAAe,EAAE;EACnB,CAAC;EACDX,YAAY,EAAE;IACZ4B,QAAQ,EAAE;EACZ,CAAC;EACD7B,WAAW,EAAE;IACX6B,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,EAAE;IACdC,SAAS,EAAE;EACb,CAAC;EACD/E,MAAM,EAAE;IACNgF,OAAO,EAAE;EACX;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}