import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, Image, Switch, ScrollView, Alert } from 'react-native';
import styles from './styles';

const Profile = () => {
  const [isPublic, setIsPublic] = useState(false);
  const [userData, setUserData] = useState({
    username: '<PERSON><PERSON><PERSON>',
    fullName: '<PERSON>',
    bio: 'Crypto enthusiast',
    profilePicture: 'https://example.com/profile.jpg',
  });

  useEffect(() => {
    // Buscar os dados do usuário do seu banco de dados ou API
    // e atualizar o estado userData com as informações do perfil.
  }, []);

  const togglePrivacy = () => {
    setIsPublic((prevState) => !prevState);
  };

  const handleCancelSubscription = () => {
    // Implementação de cancelamento de assinatura
  };

  const handleEditProfile = () => {
    // Exemplo simplificado usando um alerta
    Alert.alert('Edição de Perfil', 'Aqui você implementaria a funcionalidade de edição do perfil.');
  };

  const handleChangeProfilePicture = () => {
    // Exemplo simplificado usando um alerta
    Alert.alert('Alterar Foto do Perfil', 'Aqui você implementaria a funcionalidade para trocar a foto do perfil.');
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.profileHeader}>
        <TouchableOpacity onPress={handleChangeProfilePicture}>
          <Image source={{ uri: userData.profilePicture }} style={styles.profilePicture} />
        </TouchableOpacity>
        <Text style={styles.username}>{userData.username}</Text>
        <Text style={styles.fullName}>{userData.fullName}</Text>
        <Text style={styles.bio}>{userData.bio}</Text>
        <TouchableOpacity onPress={handleEditProfile}>
          <Text>Editar perfil</Text>
        </TouchableOpacity>
        <View style={styles.privacySwitch}>
          <Text style={styles.privacyLabel}>Perfil {isPublic ? 'Público' : 'Privado'}</Text>
          <Switch
            value={isPublic}
            onValueChange={togglePrivacy}
            thumbColor={isPublic ? '#28a745' : '#dc3545'}
          />
        </View>
      </View>

      <View style={styles.metricsSection}>
        <Text style={styles.sectionTitle}>Métricas de Desempenho</Text>
        {/* Exibir métricas de desempenho aqui */}
      </View>

      <View style={styles.badgesSection}>
        <Text style={styles.sectionTitle}>Distintivos</Text>
        {/* Exibir distintivos aqui */}
      </View>

      <TouchableOpacity
        style={styles.cancelSubscriptionButton}
        onPress={handleCancelSubscription}
      >
        <Text style={styles.cancelSubscriptionText}>Cancelar Assinatura</Text>
      </TouchableOpacity>
    </ScrollView>
  );
};

export default Profile;
