{"ast": null, "code": "var isErrorHandlingEnabled = true;\nvar developmentBuildMessage = `If you're trying to use a module that is not supported in Expo Go, you need to create a development build of your app. See https://docs.expo.dev/development/introduction/ for more info.`;\nfunction customizeUnavailableMessage(error) {\n  error.message += '\\n\\n' + developmentBuildMessage;\n}\nfunction customizeModuleIsMissingMessage(error) {\n  error.message = `Your JavaScript code tried to access a native module that doesn't exist. \n\n${developmentBuildMessage}`;\n}\nfunction customizeError(error) {\n  if ('code' in error && error.code === 'ERR_UNAVAILABLE') {\n    customizeUnavailableMessage(error);\n  } else if (error.message.includes('Native module cannot be null') || error.message.includes('`new NativeEventEmitter()` requires a non-null argument.')) {\n    customizeModuleIsMissingMessage(error);\n  }\n}\nfunction errorHandler(originalHandler, error, isFatal) {\n  if (error instanceof Error) {\n    customizeError(error);\n  }\n  originalHand<PERSON>(error, isFatal);\n}\nexport function createError<PERSON><PERSON>ler(originalHandler) {\n  return function (error, isFatal) {\n    if (isErrorHandlingEnabled) {\n      errorHandler(originalHandler, error, isFatal);\n      return;\n    }\n    originalHandler(error, isFatal);\n  };\n}\nexport function disableErrorHandling() {\n  isErrorHandlingEnabled = false;\n}", "map": {"version": 3, "names": ["isErrorHandlingEnabled", "developmentBuildMessage", "customizeUnavailableMessage", "error", "message", "customizeModuleIsMissingMessage", "customizeError", "code", "includes", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "isFatal", "Error", "createErrorHandler", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\expo\\src\\errors\\ExpoErrorManager.ts"], "sourcesContent": ["// Similar interface to the one used in expo modules.\ntype CodedError = Error & { code?: string };\n\nlet isErrorHandlingEnabled = true;\n\nconst developmentBuildMessage = `If you're trying to use a module that is not supported in Expo Go, you need to create a development build of your app. See https://docs.expo.dev/development/introduction/ for more info.`;\n\nfunction customizeUnavailableMessage(error: CodedError) {\n  error.message += '\\n\\n' + developmentBuildMessage;\n}\n\nfunction customizeModuleIsMissingMessage(error: Error) {\n  error.message = `Your JavaScript code tried to access a native module that doesn't exist. \n\n${developmentBuildMessage}`;\n}\n\nfunction customizeError(error: Error | CodedError) {\n  if ('code' in error && error.code === 'ERR_UNAVAILABLE') {\n    customizeUnavailableMessage(error);\n  } else if (\n    error.message.includes('Native module cannot be null') || // RN 0.64 and below message\n    error.message.includes('`new NativeEventEmitter()` requires a non-null argument.') // RN 0.65+ message\n  ) {\n    customizeModuleIsMissingMessage(error);\n  }\n}\n\nfunction errorHandler(originalHandler, error, isFatal) {\n  if (error instanceof Error) {\n    customizeError(error);\n  }\n  originalHandler(error, isFatal);\n}\n\nexport function createErrorHandler(originalHandler) {\n  return (error, isFatal) => {\n    if (isErrorHandlingEnabled) {\n      errorHandler(originalHandler, error, isFatal);\n      return;\n    }\n\n    originalHandler(error, isFatal);\n  };\n}\n\nexport function disableErrorHandling() {\n  isErrorHandlingEnabled = false;\n}\n"], "mappings": "AAGA,IAAIA,sBAAsB,GAAG,IAAI;AAEjC,IAAMC,uBAAuB,GAAG,2LAA2L;AAE3N,SAASC,2BAA2BA,CAACC,KAAiB;EACpDA,KAAK,CAACC,OAAO,IAAI,MAAM,GAAGH,uBAAuB;AACnD;AAEA,SAASI,+BAA+BA,CAACF,KAAY;EACnDA,KAAK,CAACC,OAAO,GAAG;;EAEhBH,uBAAuB,EAAE;AAC3B;AAEA,SAASK,cAAcA,CAACH,KAAyB;EAC/C,IAAI,MAAM,IAAIA,KAAK,IAAIA,KAAK,CAACI,IAAI,KAAK,iBAAiB,EAAE;IACvDL,2BAA2B,CAACC,KAAK,CAAC;GACnC,MAAM,IACLA,KAAK,CAACC,OAAO,CAACI,QAAQ,CAAC,8BAA8B,CAAC,IACtDL,KAAK,CAACC,OAAO,CAACI,QAAQ,CAAC,0DAA0D,CAAC,EAClF;IACAH,+BAA+B,CAACF,KAAK,CAAC;;AAE1C;AAEA,SAASM,YAAYA,CAACC,eAAe,EAAEP,KAAK,EAAEQ,OAAO;EACnD,IAAIR,KAAK,YAAYS,KAAK,EAAE;IAC1BN,cAAc,CAACH,KAAK,CAAC;;EAEvBO,eAAe,CAACP,KAAK,EAAEQ,OAAO,CAAC;AACjC;AAEA,OAAM,SAAUE,kBAAkBA,CAACH,eAAe;EAChD,OAAO,UAACP,KAAK,EAAEQ,OAAO,EAAI;IACxB,IAAIX,sBAAsB,EAAE;MAC1BS,YAAY,CAACC,eAAe,EAAEP,KAAK,EAAEQ,OAAO,CAAC;MAC7C;;IAGFD,eAAe,CAACP,KAAK,EAAEQ,OAAO,CAAC;EACjC,CAAC;AACH;AAEA,OAAM,SAAUG,oBAAoBA,CAAA;EAClCd,sBAAsB,GAAG,KAAK;AAChC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}