{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"name\", \"color\", \"size\"];\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nimport * as React from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport { black } from \"../styles/themes/v2/colors\";\nexport var accessibilityProps = Platform.OS === 'web' ? {\n  role: 'img',\n  focusable: false\n} : {\n  accessibilityElementsHidden: true,\n  importantForAccessibility: 'no-hide-descendants'\n};\nvar loadIconModule = function loadIconModule() {\n  try {\n    return require('@react-native-vector-icons/material-design-icons').default;\n  } catch (e) {\n    try {\n      return require('@expo/vector-icons/MaterialCommunityIcons').default;\n    } catch (e) {\n      try {\n        return require(\"@expo/vector-icons/MaterialCommunityIcons\").default;\n      } catch (e) {\n        return null;\n      }\n    }\n  }\n};\nvar IconModule = loadIconModule();\nvar FallbackIcon = function FallbackIcon(_ref) {\n  var name = _ref.name,\n    color = _ref.color,\n    size = _ref.size,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  console.warn(`Tried to use the icon '${name}' in a component from 'react-native-paper', but none of the required icon libraries are installed.`, `To fix this, please install one of the following:\\n` + `- @expo/vector-icons\\n` + `- @react-native-vector-icons/material-design-icons\\n` + `- react-native-vector-icons\\n\\n` + `You can also use another method to specify icon: https://callstack.github.io/react-native-paper/docs/guides/icons`);\n  return React.createElement(Text, _extends({}, rest, {\n    style: [styles.icon, {\n      color: color,\n      fontSize: size\n    }],\n    selectable: false\n  }), \"\\u25A1\");\n};\nvar MaterialCommunityIcons = IconModule || FallbackIcon;\nvar DefaultIcon = function DefaultIcon(_ref2) {\n  var name = _ref2.name,\n    _ref2$color = _ref2.color,\n    color = _ref2$color === void 0 ? black : _ref2$color,\n    size = _ref2.size,\n    direction = _ref2.direction,\n    allowFontScaling = _ref2.allowFontScaling,\n    testID = _ref2.testID;\n  return React.createElement(MaterialCommunityIcons, _extends({\n    allowFontScaling: allowFontScaling,\n    name: name,\n    color: color,\n    size: size,\n    style: [{\n      transform: [{\n        scaleX: direction === 'rtl' ? -1 : 1\n      }],\n      lineHeight: size\n    }, styles.icon],\n    pointerEvents: \"none\",\n    selectable: false,\n    testID: testID\n  }, accessibilityProps));\n};\nvar styles = StyleSheet.create({\n  icon: {\n    backgroundColor: 'transparent'\n  }\n});\nexport default DefaultIcon;", "map": {"version": 3, "names": ["React", "StyleSheet", "Text", "Platform", "black", "accessibilityProps", "OS", "role", "focusable", "accessibilityElementsHidden", "importantForAccessibility", "loadIconModule", "require", "default", "e", "IconModule", "FallbackIcon", "_ref", "name", "color", "size", "rest", "_objectWithoutProperties", "_excluded", "console", "warn", "createElement", "_extends", "style", "styles", "icon", "fontSize", "selectable", "MaterialCommunityIcons", "DefaultIcon", "_ref2", "_ref2$color", "direction", "allowFontScaling", "testID", "transform", "scaleX", "lineHeight", "pointerEvents", "create", "backgroundColor"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\MaterialCommunityIcon.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { ComponentProps } from 'react';\nimport { StyleSheet, Text, Platform, Role, ViewProps } from 'react-native';\n\nimport { black } from '../styles/themes/v2/colors';\n\nexport type IconProps = {\n  name: ComponentProps<typeof MaterialCommunityIcons>['name'];\n  color?: string;\n  size: number;\n  direction: 'rtl' | 'ltr';\n  allowFontScaling?: boolean;\n  testID?: string;\n};\n\ntype AccessibilityProps =\n  | {\n      role?: Role;\n      focusable?: boolean;\n    }\n  | {\n      accessibilityElementsHidden?: boolean;\n      importantForAccessibility?: 'auto' | 'yes' | 'no' | 'no-hide-descendants';\n    };\n\nexport const accessibilityProps: AccessibilityProps =\n  Platform.OS === 'web'\n    ? {\n        role: 'img',\n        focusable: false,\n      }\n    : {\n        accessibilityElementsHidden: true,\n        importantForAccessibility: 'no-hide-descendants',\n      };\n\n/**\n * Loads the appropriate icon module based on available dependencies\n */\nconst loadIconModule = () => {\n  try {\n    return require('@react-native-vector-icons/material-design-icons').default;\n  } catch (e) {\n    try {\n      return require('@expo/vector-icons/MaterialCommunityIcons').default;\n    } catch (e) {\n      try {\n        return require('react-native-vector-icons/MaterialCommunityIcons')\n          .default;\n      } catch (e) {\n        return null;\n      }\n    }\n  }\n};\n\ntype IconModuleType = React.ComponentType<\n  React.ComponentProps<\n    | typeof import('@react-native-vector-icons/material-design-icons').default\n    | typeof import('react-native-vector-icons/MaterialCommunityIcons').default\n  > & {\n    color: string;\n    pointerEvents?: ViewProps['pointerEvents'];\n  }\n>;\n\nconst IconModule = loadIconModule();\n\n/**\n * Fallback component displayed when no icon library is available\n */\nconst FallbackIcon = ({ name, color, size, ...rest }: IconProps) => {\n  console.warn(\n    `Tried to use the icon '${name}' in a component from 'react-native-paper', but none of the required icon libraries are installed.`,\n    `To fix this, please install one of the following:\\n` +\n      `- @expo/vector-icons\\n` +\n      `- @react-native-vector-icons/material-design-icons\\n` +\n      `- react-native-vector-icons\\n\\n` +\n      `You can also use another method to specify icon: https://callstack.github.io/react-native-paper/docs/guides/icons`\n  );\n\n  return (\n    <Text\n      {...rest}\n      style={[styles.icon, { color, fontSize: size }]}\n      selectable={false}\n    >\n      □\n    </Text>\n  );\n};\n\nconst MaterialCommunityIcons: IconModuleType = IconModule || FallbackIcon;\n\n/**\n * Default icon component that handles icon rendering with proper styling and accessibility\n */\nconst DefaultIcon = ({\n  name,\n  color = black,\n  size,\n  direction,\n  allowFontScaling,\n  testID,\n}: IconProps) => {\n  return (\n    <MaterialCommunityIcons\n      allowFontScaling={allowFontScaling}\n      name={name}\n      color={color}\n      size={size}\n      style={[\n        {\n          transform: [{ scaleX: direction === 'rtl' ? -1 : 1 }],\n          lineHeight: size,\n        },\n        styles.icon,\n      ]}\n      pointerEvents=\"none\"\n      selectable={false}\n      testID={testID}\n      {...accessibilityProps}\n    />\n  );\n};\n\nconst styles = StyleSheet.create({\n  // eslint-disable-next-line react-native/no-color-literals\n  icon: {\n    backgroundColor: 'transparent',\n  },\n});\n\nexport default DefaultIcon;\n"], "mappings": ";;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,QAAA;AAI9B,SAASC,KAAK;AAqBd,OAAO,IAAMC,kBAAsC,GACjDF,QAAQ,CAACG,EAAE,KAAK,KAAK,GACjB;EACEC,IAAI,EAAE,KAAK;EACXC,SAAS,EAAE;AACb,CAAC,GACD;EACEC,2BAA2B,EAAE,IAAI;EACjCC,yBAAyB,EAAE;AAC7B,CAAC;AAKP,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;EAC3B,IAAI;IACF,OAAOC,OAAO,CAAC,kDAAkD,CAAC,CAACC,OAAO;EAC5E,CAAC,CAAC,OAAOC,CAAC,EAAE;IACV,IAAI;MACF,OAAOF,OAAO,CAAC,2CAA2C,CAAC,CAACC,OAAO;IACrE,CAAC,CAAC,OAAOC,CAAC,EAAE;MACV,IAAI;QACF,OAAOF,OAAO,4CAAmD,CAAC,CAC/DC,OAAO;MACZ,CAAC,CAAC,OAAOC,CAAC,EAAE;QACV,OAAO,IAAI;MACb;IACF;EACF;AACF,CAAC;AAYD,IAAMC,UAAU,GAAGJ,cAAc,CAAC,CAAC;AAKnC,IAAMK,YAAY,GAAG,SAAfA,YAAYA,CAAAC,IAAA,EAAkD;EAAA,IAA5CC,IAAI,GAAAD,IAAA,CAAJC,IAAI;IAAEC,KAAK,GAAAF,IAAA,CAALE,KAAK;IAAEC,IAAI,GAAAH,IAAA,CAAJG,IAAI;IAAKC,IAAA,GAAAC,wBAAA,CAAAL,IAAA,EAAAM,SAAA;EAC5CC,OAAO,CAACC,IAAI,CACV,0BAA0BP,IAAI,oGAAoG,EAClI,qDAAqD,GACnD,wBAAwB,GACxB,sDAAsD,GACtD,iCAAiC,GACjC,mHACJ,CAAC;EAED,OACElB,KAAA,CAAA0B,aAAA,CAACxB,IAAI,EAAAyB,QAAA,KACCN,IAAI;IACRO,KAAK,EAAE,CAACC,MAAM,CAACC,IAAI,EAAE;MAAEX,KAAK,EAALA,KAAK;MAAEY,QAAQ,EAAEX;IAAK,CAAC,CAAE;IAChDY,UAAU,EAAE;EAAM,IACnB,QAEK,CAAC;AAEX,CAAC;AAED,IAAMC,sBAAsC,GAAGlB,UAAU,IAAIC,YAAY;AAKzE,IAAMkB,WAAW,GAAG,SAAdA,WAAWA,CAAAC,KAAA,EAOA;EAAA,IANfjB,IAAI,GAAAiB,KAAA,CAAJjB,IAAI;IAAAkB,WAAA,GAAAD,KAAA,CACJhB,KAAK;IAALA,KAAK,GAAAiB,WAAA,cAAGhC,KAAK,GAAAgC,WAAA;IACbhB,IAAI,GAAAe,KAAA,CAAJf,IAAI;IACJiB,SAAS,GAAAF,KAAA,CAATE,SAAS;IACTC,gBAAgB,GAAAH,KAAA,CAAhBG,gBAAgB;IAChBC,MAAA,GAAAJ,KAAA,CAAAI,MAAA;EAEA,OACEvC,KAAA,CAAA0B,aAAA,CAACO,sBAAsB,EAAAN,QAAA;IACrBW,gBAAgB,EAAEA,gBAAiB;IACnCpB,IAAI,EAAEA,IAAK;IACXC,KAAK,EAAEA,KAAM;IACbC,IAAI,EAAEA,IAAK;IACXQ,KAAK,EAAE,CACL;MACEY,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAEJ,SAAS,KAAK,KAAK,GAAG,CAAC,CAAC,GAAG;MAAE,CAAC,CAAC;MACrDK,UAAU,EAAEtB;IACd,CAAC,EACDS,MAAM,CAACC,IAAI,CACX;IACFa,aAAa,EAAC,MAAM;IACpBX,UAAU,EAAE,KAAM;IAClBO,MAAM,EAAEA;EAAO,GACXlC,kBAAkB,CACvB,CAAC;AAEN,CAAC;AAED,IAAMwB,MAAM,GAAG5B,UAAU,CAAC2C,MAAM,CAAC;EAE/Bd,IAAI,EAAE;IACJe,eAAe,EAAE;EACnB;AACF,CAAC,CAAC;AAEF,eAAeX,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}