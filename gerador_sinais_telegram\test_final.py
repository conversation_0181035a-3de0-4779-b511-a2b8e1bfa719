from utils.signal_formatter import SignalFormatter

print("TESTE FINAL - VER<PERSON><PERSON><PERSON>AO DE FORMATACAO PROFISSIONAL")
print("=" * 60)

# Teste 1: Sinal Scalp
print("\n1. TESTE SINAL SCALP:")
print("-" * 30)
scalp_message = SignalFormatter.format_scalp_signal(
    'CAKEUSDT', 2.35040, {40: 2.30339, 60: 2.27989}, 'SHORT', 20
)
print(scalp_message[:200] + "...")
if 'CRYPTOSIGNALS PROFESSIONAL' in scalp_message:
    print("✅ SCALP: Formatacao profissional OK")
else:
    print("❌ SCALP: Formatacao antiga detectada")

# Teste 2: Mensagem de Profit
print("\n2. TESTE MENSAGEM DE PROFIT:")
print("-" * 30)
profit_message = SignalFormatter.format_profit_message(
    'CAKEUSDT', 'SHORT', 20, 2.30339, 40, 2.35040
)
print(profit_message[:200] + "...")
if 'CRYPTOSIGNALS PROFESSIONAL' in profit_message:
    print("✅ PROFIT: Formatacao profissional OK")
else:
    print("❌ PROFIT: Formatacao antiga detectada")

# Teste 3: Mensagem de Stop Loss
print("\n3. TESTE MENSAGEM DE STOP LOSS:")
print("-" * 30)
sl_message = SignalFormatter.format_stop_loss_message(
    'CAKEUSDT', 'SHORT', 20, 2.35040, 2.42000, 3.0
)
print(sl_message[:200] + "...")
if 'CRYPTOSIGNALS PROFESSIONAL' in sl_message:
    print("✅ STOP LOSS: Formatacao profissional OK")
else:
    print("❌ STOP LOSS: Formatacao antiga detectada")

# Teste 4: Sinal Breakout
print("\n4. TESTE SINAL BREAKOUT:")
print("-" * 30)
breakout_message = SignalFormatter.format_breakout_signal(
    'ETHUSDT', 3000.0, 2950.0, 3100.0, 10, 'LONG'
)
print(breakout_message[:200] + "...")
if 'CRYPTOSIGNALS PROFESSIONAL' in breakout_message:
    print("✅ BREAKOUT: Formatacao profissional OK")
else:
    print("❌ BREAKOUT: Formatacao antiga detectada")

print("\n" + "=" * 60)
print("RESUMO:")
print("Se todos os testes mostraram ✅, entao:")
print("- A formatacao profissional esta funcionando")
print("- Nao ha mais emojis nos sinais")
print("- O layout e clean e tecnico")
print("- O problema foi CORRIGIDO!")
print("=" * 60)
