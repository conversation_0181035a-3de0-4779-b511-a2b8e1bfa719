{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport React, { useContext, useEffect, useState } from \"react\";\nimport View from \"react-native-web/dist/exports/View\";\nimport Alert from \"react-native-web/dist/exports/Alert\";\nimport { Button, Text } from 'react-native-paper';\nimport Wrapper from \"../../components/Wrapper\";\nimport PageTitle from \"../../components/PageTitle\";\nimport styles from \"./styles\";\nimport { StoreContext } from \"../../store\";\nimport { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from \"react/jsx-runtime\";\nexport default function Premium(_ref) {\n  var _ref$route = _ref.route,\n    route = _ref$route === void 0 ? {} : _ref$route;\n  var _useContext = useContext(StoreContext),\n    _useContext2 = _slicedToArray(_useContext, 2),\n    state = _useContext2[0],\n    dispatch = _useContext2[1];\n  var _ref2 = state || {\n      subscription: {\n        subscriptionStatus: false\n      }\n    },\n    subscriptionStatus = _ref2.subscription.subscriptionStatus;\n  var _ref3 = route.params || {\n      accountNotRecovered: false\n    },\n    accountNotRecovered = _ref3.accountNotRecovered;\n  useEffect(function () {\n    if (accountNotRecovered) {\n      Alert.alert(\"Info\", \"No active subscription for this account.\");\n    }\n  }, [accountNotRecovered]);\n  return _jsxs(Wrapper, {\n    children: [_jsx(View, {\n      style: styles.pageTitle,\n      children: _jsx(PageTitle, {\n        text: \"Premium\"\n      }, \"TitlePremium\")\n    }), subscriptionStatus ? _jsxs(View, {\n      style: _objectSpread(_objectSpread({}, styles.row), {}, {\n        marginTop: 30\n      }),\n      children: [_jsx(View, {\n        style: {\n          alignItems: \"center\",\n          marginBottom: 15\n        },\n        children: _jsx(Text, {\n          style: {\n            fontSize: 100\n          },\n          children: \"\\uD83D\\uDC8E\"\n        })\n      }), _jsx(Text, {\n        style: _objectSpread(_objectSpread({}, styles.h1), {}, {\n          marginBottom: 24\n        }),\n        children: \"Your premium subscription is active\"\n      }), _jsx(Text, {\n        style: styles.premiumDates,\n        children: \"Premium features unlocked!\"\n      })]\n    }) : _jsxs(_Fragment, {\n      children: [_jsx(View, {\n        style: styles.row,\n        children: _jsx(Text, {\n          style: styles.h1,\n          children: \"More Signals, More Profits!\"\n        })\n      }), _jsx(View, {\n        style: styles.row,\n        children: _jsx(Text, {\n          style: styles.paragraph,\n          children: \"Unlock all premium signals!\"\n        })\n      }), _jsx(View, {\n        style: styles.row,\n        children: _jsx(Text, {\n          style: styles.paragraph,\n          children: \"Premium features coming soon...\"\n        })\n      }), _jsx(View, {\n        style: styles.row,\n        children: _jsx(Button, {\n          mode: \"contained\",\n          onPress: function onPress() {\n            return Alert.alert(\"Info\", \"Premium subscription not available in demo mode\");\n          },\n          children: \"Subscribe to Premium\"\n        })\n      })]\n    })]\n  });\n}", "map": {"version": 3, "names": ["React", "useContext", "useEffect", "useState", "View", "<PERSON><PERSON>", "<PERSON><PERSON>", "Text", "Wrapper", "Page<PERSON><PERSON>le", "styles", "StoreContext", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "Premium", "_ref", "_ref$route", "route", "_useContext", "_useContext2", "_slicedToArray", "state", "dispatch", "_ref2", "subscription", "subscriptionStatus", "_ref3", "params", "accountNotRecovered", "alert", "children", "style", "pageTitle", "text", "_objectSpread", "row", "marginTop", "alignItems", "marginBottom", "fontSize", "h1", "premiumDates", "paragraph", "mode", "onPress"], "sources": ["E:/CryptoSignalsApp/src/pages/Premium/index.js"], "sourcesContent": ["import React, { useContext, useEffect, useState } from \"react\";\r\nimport { View, Alert } from \"react-native\";\r\nimport { Button, Text } from 'react-native-paper';\r\nimport Wrapper from \"../../components/Wrapper\";\r\nimport PageTitle from \"../../components/PageTitle\";\r\nimport styles from \"./styles\";\r\nimport { StoreContext } from '../../store';\r\n\r\nexport default function Premium({ route = {} }) {\r\n  const [state, dispatch] = useContext(StoreContext);\r\n\r\n  const { subscription: { subscriptionStatus } } = state || { subscription: { subscriptionStatus: false } };\r\n  let { accountNotRecovered } = route.params || { accountNotRecovered: false };\r\n\r\n  useEffect(() => {\r\n    if (accountNotRecovered) {\r\n      Alert.alert(\"Info\", \"No active subscription for this account.\");\r\n    }\r\n  }, [accountNotRecovered]);\r\n\r\n  return (\r\n    <Wrapper>\r\n      <View style={styles.pageTitle}>\r\n        <PageTitle key=\"TitlePremium\" text={\"Premium\"} />\r\n      </View>\r\n      {subscriptionStatus ? (\r\n        <View style={{ ...styles.row, marginTop: 30 }}>\r\n          <View style={{ alignItems: \"center\", marginBottom: 15 }}>\r\n            <Text style={{ fontSize: 100 }}>💎</Text>\r\n          </View>\r\n\r\n          <Text style={{ ...styles.h1, marginBottom: 24 }}>\r\n            Your premium subscription is active\r\n          </Text>\r\n\r\n          <Text style={styles.premiumDates}>\r\n            Premium features unlocked!\r\n          </Text>\r\n        </View>\r\n      ) : (\r\n        <>\r\n          <View style={styles.row}>\r\n            <Text style={styles.h1}>More Signals, More Profits!</Text>\r\n          </View>\r\n          <View style={styles.row}>\r\n            <Text style={styles.paragraph}>Unlock all premium signals!</Text>\r\n          </View>\r\n          <View style={styles.row}>\r\n            <Text style={styles.paragraph}>Premium features coming soon...</Text>\r\n          </View>\r\n          <View style={styles.row}>\r\n            <Button\r\n              mode=\"contained\"\r\n              onPress={() => Alert.alert(\"Info\", \"Premium subscription not available in demo mode\")}\r\n            >\r\n              Subscribe to Premium\r\n            </Button>\r\n          </View>\r\n        </>\r\n      )}\r\n    </Wrapper>\r\n  );\r\n}\r\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,KAAA;AAE/D,SAASC,MAAM,EAAEC,IAAI,QAAQ,oBAAoB;AACjD,OAAOC,OAAO;AACd,OAAOC,SAAS;AAChB,OAAOC,MAAM;AACb,SAASC,YAAY;AAAsB,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA,EAAAC,QAAA,IAAAC,SAAA;AAE3C,eAAe,SAASC,OAAOA,CAAAC,IAAA,EAAiB;EAAA,IAAAC,UAAA,GAAAD,IAAA,CAAdE,KAAK;IAALA,KAAK,GAAAD,UAAA,cAAG,CAAC,CAAC,GAAAA,UAAA;EAC1C,IAAAE,WAAA,GAA0BrB,UAAU,CAACU,YAAY,CAAC;IAAAY,YAAA,GAAAC,cAAA,CAAAF,WAAA;IAA3CG,KAAK,GAAAF,YAAA;IAAEG,QAAQ,GAAAH,YAAA;EAEtB,IAAAI,KAAA,GAAiDF,KAAK,IAAI;MAAEG,YAAY,EAAE;QAAEC,kBAAkB,EAAE;MAAM;IAAE,CAAC;IAAjFA,kBAAkB,GAAAF,KAAA,CAAlCC,YAAY,CAAIC,kBAAkB;EAC1C,IAAAC,KAAA,GAA8BT,KAAK,CAACU,MAAM,IAAI;MAAEC,mBAAmB,EAAE;IAAM,CAAC;IAAtEA,mBAAmB,GAAAF,KAAA,CAAnBE,mBAAmB;EAEzB9B,SAAS,CAAC,YAAM;IACd,IAAI8B,mBAAmB,EAAE;MACvB3B,KAAK,CAAC4B,KAAK,CAAC,MAAM,EAAE,0CAA0C,CAAC;IACjE;EACF,CAAC,EAAE,CAACD,mBAAmB,CAAC,CAAC;EAEzB,OACEjB,KAAA,CAACP,OAAO;IAAA0B,QAAA,GACNrB,IAAA,CAACT,IAAI;MAAC+B,KAAK,EAAEzB,MAAM,CAAC0B,SAAU;MAAAF,QAAA,EAC5BrB,IAAA,CAACJ,SAAS;QAAoB4B,IAAI,EAAE;MAAU,GAA/B,cAAiC;IAAC,CAC7C,CAAC,EACNR,kBAAkB,GACjBd,KAAA,CAACX,IAAI;MAAC+B,KAAK,EAAAG,aAAA,CAAAA,aAAA,KAAO5B,MAAM,CAAC6B,GAAG;QAAEC,SAAS,EAAE;MAAE,EAAG;MAAAN,QAAA,GAC5CrB,IAAA,CAACT,IAAI;QAAC+B,KAAK,EAAE;UAAEM,UAAU,EAAE,QAAQ;UAAEC,YAAY,EAAE;QAAG,CAAE;QAAAR,QAAA,EACtDrB,IAAA,CAACN,IAAI;UAAC4B,KAAK,EAAE;YAAEQ,QAAQ,EAAE;UAAI,CAAE;UAAAT,QAAA,EAAC;QAAE,CAAM;MAAC,CACrC,CAAC,EAEPrB,IAAA,CAACN,IAAI;QAAC4B,KAAK,EAAAG,aAAA,CAAAA,aAAA,KAAO5B,MAAM,CAACkC,EAAE;UAAEF,YAAY,EAAE;QAAE,EAAG;QAAAR,QAAA,EAAC;MAEjD,CAAM,CAAC,EAEPrB,IAAA,CAACN,IAAI;QAAC4B,KAAK,EAAEzB,MAAM,CAACmC,YAAa;QAAAX,QAAA,EAAC;MAElC,CAAM,CAAC;IAAA,CACH,CAAC,GAEPnB,KAAA,CAAAE,SAAA;MAAAiB,QAAA,GACErB,IAAA,CAACT,IAAI;QAAC+B,KAAK,EAAEzB,MAAM,CAAC6B,GAAI;QAAAL,QAAA,EACtBrB,IAAA,CAACN,IAAI;UAAC4B,KAAK,EAAEzB,MAAM,CAACkC,EAAG;UAAAV,QAAA,EAAC;QAA2B,CAAM;MAAC,CACtD,CAAC,EACPrB,IAAA,CAACT,IAAI;QAAC+B,KAAK,EAAEzB,MAAM,CAAC6B,GAAI;QAAAL,QAAA,EACtBrB,IAAA,CAACN,IAAI;UAAC4B,KAAK,EAAEzB,MAAM,CAACoC,SAAU;UAAAZ,QAAA,EAAC;QAA2B,CAAM;MAAC,CAC7D,CAAC,EACPrB,IAAA,CAACT,IAAI;QAAC+B,KAAK,EAAEzB,MAAM,CAAC6B,GAAI;QAAAL,QAAA,EACtBrB,IAAA,CAACN,IAAI;UAAC4B,KAAK,EAAEzB,MAAM,CAACoC,SAAU;UAAAZ,QAAA,EAAC;QAA+B,CAAM;MAAC,CACjE,CAAC,EACPrB,IAAA,CAACT,IAAI;QAAC+B,KAAK,EAAEzB,MAAM,CAAC6B,GAAI;QAAAL,QAAA,EACtBrB,IAAA,CAACP,MAAM;UACLyC,IAAI,EAAC,WAAW;UAChBC,OAAO,EAAE,SAATA,OAAOA,CAAA;YAAA,OAAQ3C,KAAK,CAAC4B,KAAK,CAAC,MAAM,EAAE,iDAAiD,CAAC;UAAA,CAAC;UAAAC,QAAA,EACvF;QAED,CAAQ;MAAC,CACL,CAAC;IAAA,CACP,CACH;EAAA,CACM,CAAC;AAEd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}