{"ast": null, "code": "import i18n from 'i18next';\nimport { initReactI18next } from 'react-i18next';\nimport enTranslation from \"./idiomas/en.json\";\nimport esTranslation from \"./idiomas/es.json\";\nimport frTranslation from \"./idiomas/fr.json\";\nimport deTranslation from \"./idiomas/de.json\";\nimport itTranslation from \"./idiomas/it.json\";\nimport koTranslation from \"./idiomas/ko.json\";\nimport ptBRTranslation from \"./idiomas/pt-BR.json\";\nimport zhTranslation from \"./idiomas/zh.json\";\nimport ruTranslation from \"./idiomas/ru.json\";\nvar translations = {\n  en: enTranslation,\n  es: esTranslation,\n  fr: frTranslation,\n  de: deTranslation,\n  it: itTranslation,\n  ko: koTranslation,\n  ptBR: ptBRTranslation,\n  zh: zhTranslation,\n  ru: ruTranslation\n};\ni18n.use(initReactI18next).init({\n  resources: Object.keys(translations).reduce(function (acc, key) {\n    acc[key] = {\n      translation: translations[key]\n    };\n    return acc;\n  }, {}),\n  lng: 'en',\n  fallbackLng: 'en',\n  interpolation: {\n    escapeValue: false\n  }\n});\nexport default i18n;", "map": {"version": 3, "names": ["i18n", "initReactI18next", "enTranslation", "esTranslation", "frTranslation", "deTranslation", "itTranslation", "koTranslation", "ptBRTranslation", "zhTranslation", "ruTranslation", "translations", "en", "es", "fr", "de", "it", "ko", "ptBR", "zh", "ru", "use", "init", "resources", "Object", "keys", "reduce", "acc", "key", "translation", "lng", "fallbackLng", "interpolation", "escapeValue"], "sources": ["C:/Users/<USER>/Desktop/Signals/CryptoSignalsApp/i18n.js"], "sourcesContent": ["import i18n from 'i18next';\r\nimport { initReactI18next } from 'react-i18next';\r\n\r\n// Arquivos de tradução\r\nimport enTranslation from './idiomas/en.json';\r\nimport esTranslation from './idiomas/es.json';\r\nimport frTranslation from './idiomas/fr.json';\r\nimport deTranslation from './idiomas/de.json';\r\nimport itTranslation from './idiomas/it.json';\r\nimport koTranslation from './idiomas/ko.json';\r\nimport ptBRTranslation from './idiomas/pt-BR.json';\r\nimport zhTranslation from './idiomas/zh.json';\r\nimport ruTranslation from './idiomas/ru.json';\r\n\r\n// Mapeamento de traduções\r\nconst translations = {\r\n  en: enTranslation,\r\n  es: esTranslation,\r\n  fr: frTranslation,\r\n  de: deTranslation,\r\n  it: itTranslation,\r\n  ko: koTranslation,\r\n  ptBR: ptBRTranslation,\r\n  zh: zhTranslation,\r\n  ru: ruTranslation,\r\n};\r\n\r\n// Configuração do i18next\r\ni18n\r\n  .use(initReactI18next)\r\n  .init({\r\n    resources: Object.keys(translations).reduce((acc, key) => {\r\n      acc[key] = { translation: translations[key] };\r\n      return acc;\r\n    }, {}),\r\n    lng: 'en', // Idioma padrão\r\n    fallbackLng: 'en', // Idioma de fallback se a tradução não estiver disponível\r\n    interpolation: {\r\n      escapeValue: false, // Não escapar HTML ou caracteres especiais nas traduções\r\n    },\r\n  });\r\n\r\nexport default i18n;\r\n\r\n/*\r\nCorrigi os caminhos dos arquivos de tradução removendo o ponto extra (de .src para ./src).\r\nCriei um objeto translations para mapear as traduções por idioma e usei um loop .reduce para construir\r\ndinamicamente os recursos.\r\n*/"], "mappings": "AAAA,OAAOA,IAAI,MAAM,SAAS;AAC1B,SAASC,gBAAgB,QAAQ,eAAe;AAGhD,OAAOC,aAAa;AACpB,OAAOC,aAAa;AACpB,OAAOC,aAAa;AACpB,OAAOC,aAAa;AACpB,OAAOC,aAAa;AACpB,OAAOC,aAAa;AACpB,OAAOC,eAAe;AACtB,OAAOC,aAAa;AACpB,OAAOC,aAAa;AAGpB,IAAMC,YAAY,GAAG;EACnBC,EAAE,EAAEV,aAAa;EACjBW,EAAE,EAAEV,aAAa;EACjBW,EAAE,EAAEV,aAAa;EACjBW,EAAE,EAAEV,aAAa;EACjBW,EAAE,EAAEV,aAAa;EACjBW,EAAE,EAAEV,aAAa;EACjBW,IAAI,EAAEV,eAAe;EACrBW,EAAE,EAAEV,aAAa;EACjBW,EAAE,EAAEV;AACN,CAAC;AAGDV,IAAI,CACDqB,GAAG,CAACpB,gBAAgB,CAAC,CACrBqB,IAAI,CAAC;EACJC,SAAS,EAAEC,MAAM,CAACC,IAAI,CAACd,YAAY,CAAC,CAACe,MAAM,CAAC,UAACC,GAAG,EAAEC,GAAG,EAAK;IACxDD,GAAG,CAACC,GAAG,CAAC,GAAG;MAAEC,WAAW,EAAElB,YAAY,CAACiB,GAAG;IAAE,CAAC;IAC7C,OAAOD,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;EACNG,GAAG,EAAE,IAAI;EACTC,WAAW,EAAE,IAAI;EACjBC,aAAa,EAAE;IACbC,WAAW,EAAE;EACf;AACF,CAAC,CAAC;AAEJ,eAAejC,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}