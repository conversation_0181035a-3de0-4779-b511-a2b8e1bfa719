{"ast": null, "code": "import * as React from 'react';\nimport BottomTabBarHeightContext from \"./BottomTabBarHeightContext\";\nexport default function useBottomTabBarHeight() {\n  var height = React.useContext(BottomTabBarHeightContext);\n  if (height === undefined) {\n    throw new Error(\"Couldn't find the bottom tab bar height. Are you inside a screen in Bottom Tab Navigator?\");\n  }\n  return height;\n}", "map": {"version": 3, "names": ["React", "BottomTabBarHeightContext", "useBottomTabBarHeight", "height", "useContext", "undefined", "Error"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\@react-navigation\\bottom-tabs\\src\\utils\\useBottomTabBarHeight.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport BottomTabBarHeightContext from './BottomTabBarHeightContext';\n\nexport default function useBottomTabBarHeight() {\n  const height = React.useContext(BottomTabBarHeightContext);\n\n  if (height === undefined) {\n    throw new Error(\n      \"Couldn't find the bottom tab bar height. Are you inside a screen in Bottom Tab Navigator?\"\n    );\n  }\n\n  return height;\n}\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,OAAOC,yBAAyB;AAEhC,eAAe,SAASC,qBAAqBA,CAAA,EAAG;EAC9C,IAAMC,MAAM,GAAGH,KAAK,CAACI,UAAU,CAACH,yBAAyB,CAAC;EAE1D,IAAIE,MAAM,KAAKE,SAAS,EAAE;IACxB,MAAM,IAAIC,KAAK,CACb,2FAA2F,CAC5F;EACH;EAEA,OAAOH,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}