{"ast": null, "code": "import getInvertedMultiplier from \"./getInvertedMultiplier\";\nexport default function getDistanceForDirection(layout, gestureDirection) {\n  var multiplier = getInvertedMultiplier(gestureDirection);\n  switch (gestureDirection) {\n    case 'vertical':\n    case 'vertical-inverted':\n      return layout.height * multiplier;\n    case 'horizontal':\n    case 'horizontal-inverted':\n      return layout.width * multiplier;\n  }\n}", "map": {"version": 3, "names": ["getInvertedMultiplier", "getDistanceForDirection", "layout", "gestureDirection", "multiplier", "height", "width"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\@react-navigation\\stack\\src\\utils\\getDistanceForDirection.tsx"], "sourcesContent": ["import type { GestureDirection, Layout } from '../types';\nimport getInvertedMultiplier from './getInvertedMultiplier';\n\nexport default function getDistanceForDirection(\n  layout: Layout,\n  gestureDirection: GestureDirection\n): number {\n  const multiplier = getInvertedMultiplier(gestureDirection);\n\n  switch (gestureDirection) {\n    case 'vertical':\n    case 'vertical-inverted':\n      return layout.height * multiplier;\n    case 'horizontal':\n    case 'horizontal-inverted':\n      return layout.width * multiplier;\n  }\n}\n"], "mappings": "AACA,OAAOA,qBAAqB;AAE5B,eAAe,SAASC,uBAAuBA,CAC7CC,MAAc,EACdC,gBAAkC,EAC1B;EACR,IAAMC,UAAU,GAAGJ,qBAAqB,CAACG,gBAAgB,CAAC;EAE1D,QAAQA,gBAAgB;IACtB,KAAK,UAAU;IACf,KAAK,mBAAmB;MACtB,OAAOD,MAAM,CAACG,MAAM,GAAGD,UAAU;IACnC,KAAK,YAAY;IACjB,KAAK,qBAAqB;MACxB,OAAOF,MAAM,CAACI,KAAK,GAAGF,UAAU;EAAC;AAEvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}