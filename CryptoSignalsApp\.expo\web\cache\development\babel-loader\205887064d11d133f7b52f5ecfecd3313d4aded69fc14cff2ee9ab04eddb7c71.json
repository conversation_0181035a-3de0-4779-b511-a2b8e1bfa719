{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"theme\", \"linking\", \"fallback\", \"documentTitle\", \"onReady\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport { BaseNavigationContainer, getActionFromState, getPathFromState, getStateFromPath, validatePathConfig } from '@react-navigation/core';\nimport * as React from 'react';\nimport LinkingContext from \"./LinkingContext\";\nimport DefaultTheme from \"./theming/DefaultTheme\";\nimport ThemeProvider from \"./theming/ThemeProvider\";\nimport useBackButton from \"./useBackButton\";\nimport useDocumentTitle from \"./useDocumentTitle\";\nimport useLinking from \"./useLinking\";\nimport useThenable from \"./useThenable\";\nglobal.REACT_NAVIGATION_DEVTOOLS = new WeakMap();\nfunction NavigationContainerInner(_ref, ref) {\n  var _ref$theme = _ref.theme,\n    theme = _ref$theme === void 0 ? DefaultTheme : _ref$theme,\n    linking = _ref.linking,\n    _ref$fallback = _ref.fallback,\n    fallback = _ref$fallback === void 0 ? null : _ref$fallback,\n    documentTitle = _ref.documentTitle,\n    onReady = _ref.onReady,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var isLinkingEnabled = linking ? linking.enabled !== false : false;\n  if (linking !== null && linking !== void 0 && linking.config) {\n    validatePathConfig(linking.config);\n  }\n  var refContainer = React.useRef(null);\n  useBackButton(refContainer);\n  useDocumentTitle(refContainer, documentTitle);\n  var _useLinking = useLinking(refContainer, _objectSpread({\n      independent: rest.independent,\n      enabled: isLinkingEnabled,\n      prefixes: []\n    }, linking)),\n    getInitialState = _useLinking.getInitialState;\n  React.useEffect(function () {\n    if (refContainer.current) {\n      REACT_NAVIGATION_DEVTOOLS.set(refContainer.current, {\n        get linking() {\n          var _ref2, _ref3, _ref4, _ref5;\n          return _objectSpread(_objectSpread({}, linking), {}, {\n            enabled: isLinkingEnabled,\n            prefixes: (_ref2 = linking === null || linking === void 0 ? void 0 : linking.prefixes) != null ? _ref2 : [],\n            getStateFromPath: (_ref3 = linking === null || linking === void 0 ? void 0 : linking.getStateFromPath) != null ? _ref3 : getStateFromPath,\n            getPathFromState: (_ref4 = linking === null || linking === void 0 ? void 0 : linking.getPathFromState) != null ? _ref4 : getPathFromState,\n            getActionFromState: (_ref5 = linking === null || linking === void 0 ? void 0 : linking.getActionFromState) != null ? _ref5 : getActionFromState\n          });\n        }\n      });\n    }\n  });\n  var _useThenable = useThenable(getInitialState),\n    _useThenable2 = _slicedToArray(_useThenable, 2),\n    isResolved = _useThenable2[0],\n    initialState = _useThenable2[1];\n  React.useImperativeHandle(ref, function () {\n    return refContainer.current;\n  });\n  var linkingContext = React.useMemo(function () {\n    return {\n      options: linking\n    };\n  }, [linking]);\n  var isReady = rest.initialState != null || !isLinkingEnabled || isResolved;\n  var onReadyRef = React.useRef(onReady);\n  React.useEffect(function () {\n    onReadyRef.current = onReady;\n  });\n  React.useEffect(function () {\n    if (isReady) {\n      var _onReadyRef$current;\n      (_onReadyRef$current = onReadyRef.current) === null || _onReadyRef$current === void 0 ? void 0 : _onReadyRef$current.call(onReadyRef);\n    }\n  }, [isReady]);\n  if (!isReady) {\n    return fallback;\n  }\n  return React.createElement(LinkingContext.Provider, {\n    value: linkingContext\n  }, React.createElement(ThemeProvider, {\n    value: theme\n  }, React.createElement(BaseNavigationContainer, _extends({}, rest, {\n    initialState: rest.initialState == null ? initialState : rest.initialState,\n    ref: refContainer\n  }))));\n}\nvar NavigationContainer = React.forwardRef(NavigationContainerInner);\nexport default NavigationContainer;", "map": {"version": 3, "names": ["BaseNavigationContainer", "getActionFromState", "getPathFromState", "getStateFromPath", "validatePathConfig", "React", "LinkingContext", "DefaultTheme", "ThemeProvider", "useBackButton", "useDocumentTitle", "useLinking", "useThenable", "global", "REACT_NAVIGATION_DEVTOOLS", "WeakMap", "NavigationContainerInner", "_ref", "ref", "_ref$theme", "theme", "linking", "_ref$fallback", "fallback", "documentTitle", "onReady", "rest", "_objectWithoutProperties", "_excluded", "isLinkingEnabled", "enabled", "config", "ref<PERSON><PERSON><PERSON>", "useRef", "_useLinking", "_objectSpread", "independent", "prefixes", "getInitialState", "useEffect", "current", "set", "_ref2", "_ref3", "_ref4", "_ref5", "_useThenable", "_useThenable2", "_slicedToArray", "isResolved", "initialState", "useImperativeHandle", "linkingContext", "useMemo", "options", "isReady", "onReadyRef", "_onReadyRef$current", "call", "createElement", "Provider", "value", "_extends", "NavigationContainer", "forwardRef"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\@react-navigation\\native\\src\\NavigationContainer.tsx"], "sourcesContent": ["import {\n  BaseNavigationContainer,\n  getActionFromState,\n  getPathFromState,\n  getStateFromPath,\n  NavigationContainerProps,\n  NavigationContainerRef,\n  ParamListBase,\n  validatePathConfig,\n} from '@react-navigation/core';\nimport * as React from 'react';\n\nimport LinkingContext from './LinkingContext';\nimport DefaultTheme from './theming/DefaultTheme';\nimport ThemeProvider from './theming/ThemeProvider';\nimport type { DocumentTitleOptions, LinkingOptions, Theme } from './types';\nimport useBackButton from './useBackButton';\nimport useDocumentTitle from './useDocumentTitle';\nimport useLinking from './useLinking';\nimport useThenable from './useThenable';\n\ndeclare global {\n  var REACT_NAVIGATION_DEVTOOLS: WeakMap<\n    NavigationContainerRef<any>,\n    { readonly linking: LinkingOptions<any> }\n  >;\n}\n\nglobal.REACT_NAVIGATION_DEVTOOLS = new WeakMap();\n\ntype Props<ParamList extends {}> = NavigationContainerProps & {\n  theme?: Theme;\n  linking?: LinkingOptions<ParamList>;\n  fallback?: React.ReactNode;\n  documentTitle?: DocumentTitleOptions;\n  onReady?: () => void;\n};\n\n/**\n * Container component which holds the navigation state designed for React Native apps.\n * This should be rendered at the root wrapping the whole app.\n *\n * @param props.initialState Initial state object for the navigation tree. When deep link handling is enabled, this will override deep links when specified. Make sure that you don't specify an `initialState` when there's a deep link (`Linking.getInitialURL()`).\n * @param props.onReady Callback which is called after the navigation tree mounts.\n * @param props.onStateChange Callback which is called with the latest navigation state when it changes.\n * @param props.theme Theme object for the navigators.\n * @param props.linking Options for deep linking. Deep link handling is enabled when this prop is provided, unless `linking.enabled` is `false`.\n * @param props.fallback Fallback component to render until we have finished getting initial state when linking is enabled. Defaults to `null`.\n * @param props.documentTitle Options to configure the document title on Web. Updating document title is handled by default unless `documentTitle.enabled` is `false`.\n * @param props.children Child elements to render the content.\n * @param props.ref Ref object which refers to the navigation object containing helper methods.\n */\nfunction NavigationContainerInner(\n  {\n    theme = DefaultTheme,\n    linking,\n    fallback = null,\n    documentTitle,\n    onReady,\n    ...rest\n  }: Props<ParamListBase>,\n  ref?: React.Ref<NavigationContainerRef<ParamListBase> | null>\n) {\n  const isLinkingEnabled = linking ? linking.enabled !== false : false;\n\n  if (linking?.config) {\n    validatePathConfig(linking.config);\n  }\n\n  const refContainer =\n    React.useRef<NavigationContainerRef<ParamListBase>>(null);\n\n  useBackButton(refContainer);\n  useDocumentTitle(refContainer, documentTitle);\n\n  const { getInitialState } = useLinking(refContainer, {\n    independent: rest.independent,\n    enabled: isLinkingEnabled,\n    prefixes: [],\n    ...linking,\n  });\n\n  // Add additional linking related info to the ref\n  // This will be used by the devtools\n  React.useEffect(() => {\n    if (refContainer.current) {\n      REACT_NAVIGATION_DEVTOOLS.set(refContainer.current, {\n        get linking() {\n          return {\n            ...linking,\n            enabled: isLinkingEnabled,\n            prefixes: linking?.prefixes ?? [],\n            getStateFromPath: linking?.getStateFromPath ?? getStateFromPath,\n            getPathFromState: linking?.getPathFromState ?? getPathFromState,\n            getActionFromState:\n              linking?.getActionFromState ?? getActionFromState,\n          };\n        },\n      });\n    }\n  });\n\n  const [isResolved, initialState] = useThenable(getInitialState);\n\n  React.useImperativeHandle(ref, () => refContainer.current);\n\n  const linkingContext = React.useMemo(() => ({ options: linking }), [linking]);\n\n  const isReady = rest.initialState != null || !isLinkingEnabled || isResolved;\n\n  const onReadyRef = React.useRef(onReady);\n\n  React.useEffect(() => {\n    onReadyRef.current = onReady;\n  });\n\n  React.useEffect(() => {\n    if (isReady) {\n      onReadyRef.current?.();\n    }\n  }, [isReady]);\n\n  if (!isReady) {\n    // This is temporary until we have Suspense for data-fetching\n    // Then the fallback will be handled by a parent `Suspense` component\n    return fallback as React.ReactElement;\n  }\n\n  return (\n    <LinkingContext.Provider value={linkingContext}>\n      <ThemeProvider value={theme}>\n        <BaseNavigationContainer\n          {...rest}\n          initialState={\n            rest.initialState == null ? initialState : rest.initialState\n          }\n          ref={refContainer}\n        />\n      </ThemeProvider>\n    </LinkingContext.Provider>\n  );\n}\n\nconst NavigationContainer = React.forwardRef(NavigationContainerInner) as <\n  RootParamList extends {} = ReactNavigation.RootParamList\n>(\n  props: Props<RootParamList> & {\n    ref?: React.Ref<NavigationContainerRef<RootParamList>>;\n  }\n) => React.ReactElement;\n\nexport default NavigationContainer;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA,SACEA,uBAAuB,EACvBC,kBAAkB,EAClBC,gBAAgB,EAChBC,gBAAgB,EAIhBC,kBAAkB,QACb,wBAAwB;AAC/B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAE9B,OAAOC,cAAc;AACrB,OAAOC,YAAY;AACnB,OAAOC,aAAa;AAEpB,OAAOC,aAAa;AACpB,OAAOC,gBAAgB;AACvB,OAAOC,UAAU;AACjB,OAAOC,WAAW;AASlBC,MAAM,CAACC,yBAAyB,GAAG,IAAIC,OAAO,EAAE;AAwBhD,SAASC,wBAAwBA,CAAAC,IAAA,EAS/BC,GAA6D,EAC7D;EAAA,IAAAC,UAAA,GAFuBF,IAAA,CANrBG,KAAK;IAALA,KAAK,GAAAD,UAAA,cAAGZ,YAAY,GAAAY,UAAA;IACpBE,OAAO,GAKcJ,IAAA,CALrBI,OAAO;IAAAC,aAAA,GAKcL,IAAA,CAJrBM,QAAQ;IAARA,QAAQ,GAAAD,aAAA,cAAG,IAAI,GAAAA,aAAA;IACfE,aAAa,GAGQP,IAAA,CAHrBO,aAAa;IACbC,OAAO,GAEcR,IAAA,CAFrBQ,OAAO;IACJC,IAAA,GAAAC,wBAAA,CACkBV,IAAA,EAAAW,SAAA;EAGvB,IAAMC,gBAAgB,GAAGR,OAAO,GAAGA,OAAO,CAACS,OAAO,KAAK,KAAK,GAAG,KAAK;EAEpE,IAAIT,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEU,MAAM,EAAE;IACnB3B,kBAAkB,CAACiB,OAAO,CAACU,MAAM,CAAC;EACpC;EAEA,IAAMC,YAAY,GAChB3B,KAAK,CAAC4B,MAAM,CAAwC,IAAI,CAAC;EAE3DxB,aAAa,CAACuB,YAAY,CAAC;EAC3BtB,gBAAgB,CAACsB,YAAY,EAAER,aAAa,CAAC;EAE7C,IAAAU,WAAA,GAA4BvB,UAAU,CAACqB,YAAY,EAAAG,aAAA;MACjDC,WAAW,EAAEV,IAAI,CAACU,WAAW;MAC7BN,OAAO,EAAED,gBAAgB;MACzBQ,QAAQ,EAAE;IAAE,GACThB,OAAA,CACJ,CAAC;IALMiB,eAAA,GAAAJ,WAAA,CAAAI,eAAA;EASRjC,KAAK,CAACkC,SAAS,CAAC,YAAM;IACpB,IAAIP,YAAY,CAACQ,OAAO,EAAE;MACxB1B,yBAAyB,CAAC2B,GAAG,CAACT,YAAY,CAACQ,OAAO,EAAE;QAClD,IAAInB,OAAOA,CAAA,EAAG;UAAA,IAAAqB,KAAA,EAAAC,KAAA,EAAAC,KAAA,EAAAC,KAAA;UACZ,OAAAV,aAAA,CAAAA,aAAA,KACKd,OAAO;YACVS,OAAO,EAAED,gBAAgB;YACzBQ,QAAQ,GAAAK,KAAA,GAAErB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEgB,QAAQ,YAAAK,KAAA,GAAI,EAAE;YACjCvC,gBAAgB,GAAAwC,KAAA,GAAEtB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAElB,gBAAgB,YAAAwC,KAAA,GAAIxC,gBAAgB;YAC/DD,gBAAgB,GAAA0C,KAAA,GAAEvB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEnB,gBAAgB,YAAA0C,KAAA,GAAI1C,gBAAgB;YAC/DD,kBAAkB,GAAA4C,KAAA,GAChBxB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEpB,kBAAkB,YAAA4C,KAAA,GAAI5C;UAAA;QAErC;MACF,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EAEF,IAAA6C,YAAA,GAAmClC,WAAW,CAAC0B,eAAe,CAAC;IAAAS,aAAA,GAAAC,cAAA,CAAAF,YAAA;IAAxDG,UAAU,GAAAF,aAAA;IAAEG,YAAY,GAAAH,aAAA;EAE/B1C,KAAK,CAAC8C,mBAAmB,CAACjC,GAAG,EAAE;IAAA,OAAMc,YAAY,CAACQ,OAAO;EAAA,EAAC;EAE1D,IAAMY,cAAc,GAAG/C,KAAK,CAACgD,OAAO,CAAC;IAAA,OAAO;MAAEC,OAAO,EAAEjC;IAAQ,CAAC;EAAA,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;EAE7E,IAAMkC,OAAO,GAAG7B,IAAI,CAACwB,YAAY,IAAI,IAAI,IAAI,CAACrB,gBAAgB,IAAIoB,UAAU;EAE5E,IAAMO,UAAU,GAAGnD,KAAK,CAAC4B,MAAM,CAACR,OAAO,CAAC;EAExCpB,KAAK,CAACkC,SAAS,CAAC,YAAM;IACpBiB,UAAU,CAAChB,OAAO,GAAGf,OAAO;EAC9B,CAAC,CAAC;EAEFpB,KAAK,CAACkC,SAAS,CAAC,YAAM;IACpB,IAAIgB,OAAO,EAAE;MAAA,IAAAE,mBAAA;MACX,CAAAA,mBAAA,GAAAD,UAAU,CAAChB,OAAO,cAAAiB,mBAAA,uBAAlBA,mBAAA,CAAAC,IAAA,CAAAF,UAAU,CAAY;IACxB;EACF,CAAC,EAAE,CAACD,OAAO,CAAC,CAAC;EAEb,IAAI,CAACA,OAAO,EAAE;IAGZ,OAAOhC,QAAQ;EACjB;EAEA,OACElB,KAAA,CAAAsD,aAAA,CAACrD,cAAc,CAACsD,QAAQ;IAACC,KAAK,EAAET;EAAe,GAC7C/C,KAAA,CAAAsD,aAAA,CAACnD,aAAa;IAACqD,KAAK,EAAEzC;EAAM,GAC1Bf,KAAA,CAAAsD,aAAA,CAAC3D,uBAAuB,EAAA8D,QAAA,KAClBpC,IAAI;IACRwB,YAAY,EACVxB,IAAI,CAACwB,YAAY,IAAI,IAAI,GAAGA,YAAY,GAAGxB,IAAI,CAACwB,YACjD;IACDhC,GAAG,EAAEc;EAAa,GAClB,CACY,CACQ;AAE9B;AAEA,IAAM+B,mBAAmB,GAAG1D,KAAK,CAAC2D,UAAU,CAAChD,wBAAwB,CAM9C;AAEvB,eAAe+C,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}