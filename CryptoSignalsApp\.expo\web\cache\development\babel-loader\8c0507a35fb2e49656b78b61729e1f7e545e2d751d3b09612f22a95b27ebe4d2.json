{"ast": null, "code": "import * as React from 'react';\nimport NavigationBuilderContext from \"./NavigationBuilderContext\";\nimport useOnPreventRemove, { shouldPreventRemove } from \"./useOnPreventRemove\";\nexport default function useOnAction(_ref) {\n  var router = _ref.router,\n    getState = _ref.getState,\n    setState = _ref.setState,\n    key = _ref.key,\n    actionListeners = _ref.actionListeners,\n    beforeRemoveListeners = _ref.beforeRemoveListeners,\n    routerConfigOptions = _ref.routerConfigOptions,\n    emitter = _ref.emitter;\n  var _React$useContext = React.useContext(NavigationBuilderContext),\n    onActionParent = _React$useContext.onAction,\n    onRouteFocusParent = _React$useContext.onRouteFocus,\n    addListenerParent = _React$useContext.addListener,\n    onDispatchAction = _React$useContext.onDispatchAction;\n  var routerConfigOptionsRef = React.useRef(routerConfigOptions);\n  React.useEffect(function () {\n    routerConfigOptionsRef.current = routerConfigOptions;\n  });\n  var onAction = React.useCallback(function (action) {\n    var visitedNavigators = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : new Set();\n    var state = getState();\n    if (visitedNavigators.has(state.key)) {\n      return false;\n    }\n    visitedNavigators.add(state.key);\n    if (typeof action.target !== 'string' || action.target === state.key) {\n      var result = router.getStateForAction(state, action, routerConfigOptionsRef.current);\n      result = result === null && action.target === state.key ? state : result;\n      if (result !== null) {\n        onDispatchAction(action, state === result);\n        if (state !== result) {\n          var isPrevented = shouldPreventRemove(emitter, beforeRemoveListeners, state.routes, result.routes, action);\n          if (isPrevented) {\n            return true;\n          }\n          setState(result);\n        }\n        if (onRouteFocusParent !== undefined) {\n          var shouldFocus = router.shouldActionChangeFocus(action);\n          if (shouldFocus && key !== undefined) {\n            onRouteFocusParent(key);\n          }\n        }\n        return true;\n      }\n    }\n    if (onActionParent !== undefined) {\n      if (onActionParent(action, visitedNavigators)) {\n        return true;\n      }\n    }\n    for (var i = actionListeners.length - 1; i >= 0; i--) {\n      var listener = actionListeners[i];\n      if (listener(action, visitedNavigators)) {\n        return true;\n      }\n    }\n    return false;\n  }, [actionListeners, beforeRemoveListeners, emitter, getState, key, onActionParent, onDispatchAction, onRouteFocusParent, router, setState]);\n  useOnPreventRemove({\n    getState: getState,\n    emitter: emitter,\n    beforeRemoveListeners: beforeRemoveListeners\n  });\n  React.useEffect(function () {\n    return addListenerParent === null || addListenerParent === void 0 ? void 0 : addListenerParent('action', onAction);\n  }, [addListenerParent, onAction]);\n  return onAction;\n}", "map": {"version": 3, "names": ["React", "NavigationBuilderContext", "useOnPreventRemove", "shouldPreventRemove", "useOnAction", "_ref", "router", "getState", "setState", "key", "actionListeners", "beforeRemoveListeners", "routerConfigOptions", "emitter", "_React$useContext", "useContext", "onActionParent", "onAction", "onRouteFocusParent", "onRouteFocus", "addListenerParent", "addListener", "onDispatchAction", "routerConfigOptionsRef", "useRef", "useEffect", "current", "useCallback", "action", "visitedNavigators", "arguments", "length", "undefined", "Set", "state", "has", "add", "target", "result", "getStateForAction", "isPrevented", "routes", "shouldFocus", "shouldActionChangeFocus", "i", "listener"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\@react-navigation\\core\\src\\useOnAction.tsx"], "sourcesContent": ["import type {\n  NavigationAction,\n  NavigationState,\n  PartialState,\n  Router,\n  RouterConfigOptions,\n} from '@react-navigation/routers';\nimport * as React from 'react';\n\nimport NavigationBuilderContext, {\n  ChildActionListener,\n  ChildBeforeRemoveListener,\n} from './NavigationBuilderContext';\nimport type { EventMapCore } from './types';\nimport type { NavigationEventEmitter } from './useEventEmitter';\nimport useOnPreventRemove, { shouldPreventRemove } from './useOnPreventRemove';\n\ntype Options = {\n  router: Router<NavigationState, NavigationAction>;\n  key?: string;\n  getState: () => NavigationState;\n  setState: (state: NavigationState | PartialState<NavigationState>) => void;\n  actionListeners: ChildActionListener[];\n  beforeRemoveListeners: Record<string, ChildBeforeRemoveListener | undefined>;\n  routerConfigOptions: RouterConfigOptions;\n  emitter: NavigationEventEmitter<EventMapCore<any>>;\n};\n\n/**\n * Hook to handle actions for a navigator, including state updates and bubbling.\n *\n * Bubbling an action is achieved in 2 ways:\n * 1. To bubble action to parent, we expose the action handler in context and then access the parent context\n * 2. To bubble action to child, child adds event listeners subscribing to actions from parent\n *\n * When the action handler handles as action, it returns `true`, otherwise `false`.\n */\nexport default function useOnAction({\n  router,\n  getState,\n  setState,\n  key,\n  actionListeners,\n  beforeRemoveListeners,\n  routerConfigOptions,\n  emitter,\n}: Options) {\n  const {\n    onAction: onActionParent,\n    onRouteFocus: onRouteFocusParent,\n    addListener: addListenerParent,\n    onDispatchAction,\n  } = React.useContext(NavigationBuilderContext);\n\n  const routerConfigOptionsRef =\n    React.useRef<RouterConfigOptions>(routerConfigOptions);\n\n  React.useEffect(() => {\n    routerConfigOptionsRef.current = routerConfigOptions;\n  });\n\n  const onAction = React.useCallback(\n    (\n      action: NavigationAction,\n      visitedNavigators: Set<string> = new Set<string>()\n    ) => {\n      const state = getState();\n\n      // Since actions can bubble both up and down, they could come to the same navigator again\n      // We keep track of navigators which have already tried to handle the action and return if it's already visited\n      if (visitedNavigators.has(state.key)) {\n        return false;\n      }\n\n      visitedNavigators.add(state.key);\n\n      if (typeof action.target !== 'string' || action.target === state.key) {\n        let result = router.getStateForAction(\n          state,\n          action,\n          routerConfigOptionsRef.current\n        );\n\n        // If a target is specified and set to current navigator, the action shouldn't bubble\n        // So instead of `null`, we use the state object for such cases to signal that action was handled\n        result =\n          result === null && action.target === state.key ? state : result;\n\n        if (result !== null) {\n          onDispatchAction(action, state === result);\n\n          if (state !== result) {\n            const isPrevented = shouldPreventRemove(\n              emitter,\n              beforeRemoveListeners,\n              state.routes,\n              result.routes,\n              action\n            );\n\n            if (isPrevented) {\n              return true;\n            }\n\n            setState(result);\n          }\n\n          if (onRouteFocusParent !== undefined) {\n            // Some actions such as `NAVIGATE` also want to bring the navigated route to focus in the whole tree\n            // This means we need to focus all of the parent navigators of this navigator as well\n            const shouldFocus = router.shouldActionChangeFocus(action);\n\n            if (shouldFocus && key !== undefined) {\n              onRouteFocusParent(key);\n            }\n          }\n\n          return true;\n        }\n      }\n\n      if (onActionParent !== undefined) {\n        // Bubble action to the parent if the current navigator didn't handle it\n        if (onActionParent(action, visitedNavigators)) {\n          return true;\n        }\n      }\n\n      // If the action wasn't handled by current navigator or a parent navigator, let children handle it\n      for (let i = actionListeners.length - 1; i >= 0; i--) {\n        const listener = actionListeners[i];\n\n        if (listener(action, visitedNavigators)) {\n          return true;\n        }\n      }\n\n      return false;\n    },\n    [\n      actionListeners,\n      beforeRemoveListeners,\n      emitter,\n      getState,\n      key,\n      onActionParent,\n      onDispatchAction,\n      onRouteFocusParent,\n      router,\n      setState,\n    ]\n  );\n\n  useOnPreventRemove({\n    getState,\n    emitter,\n    beforeRemoveListeners,\n  });\n\n  React.useEffect(\n    () => addListenerParent?.('action', onAction),\n    [addListenerParent, onAction]\n  );\n\n  return onAction;\n}\n"], "mappings": "AAOA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,OAAOC,wBAAwB;AAM/B,OAAOC,kBAAkB,IAAIC,mBAAmB;AAsBhD,eAAe,SAASC,WAAWA,CAAAC,IAAA,EASvB;EAAA,IARVC,MAAM,GAQED,IAAA,CARRC,MAAM;IACNC,QAAQ,GAOAF,IAAA,CAPRE,QAAQ;IACRC,QAAQ,GAMAH,IAAA,CANRG,QAAQ;IACRC,GAAG,GAKKJ,IAAA,CALRI,GAAG;IACHC,eAAe,GAIPL,IAAA,CAJRK,eAAe;IACfC,qBAAqB,GAGbN,IAAA,CAHRM,qBAAqB;IACrBC,mBAAmB,GAEXP,IAAA,CAFRO,mBAAmB;IACnBC,OAAA,GACQR,IAAA,CADRQ,OAAA;EAEA,IAAAC,iBAAA,GAKId,KAAK,CAACe,UAAU,CAACd,wBAAwB,CAAC;IAJlCe,cAAc,GAAAF,iBAAA,CAAxBG,QAAQ;IACMC,kBAAkB,GAAAJ,iBAAA,CAAhCK,YAAY;IACCC,iBAAiB,GAAAN,iBAAA,CAA9BO,WAAW;IACXC,gBAAA,GAAAR,iBAAA,CAAAQ,gBAAA;EAGF,IAAMC,sBAAsB,GAC1BvB,KAAK,CAACwB,MAAM,CAAsBZ,mBAAmB,CAAC;EAExDZ,KAAK,CAACyB,SAAS,CAAC,YAAM;IACpBF,sBAAsB,CAACG,OAAO,GAAGd,mBAAmB;EACtD,CAAC,CAAC;EAEF,IAAMK,QAAQ,GAAGjB,KAAK,CAAC2B,WAAW,CAChC,UACEC,MAAwB,EAErB;IAAA,IADHC,iBAA8B,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAIG,GAAG,EAAU;IAElD,IAAMC,KAAK,GAAG3B,QAAQ,EAAE;IAIxB,IAAIsB,iBAAiB,CAACM,GAAG,CAACD,KAAK,CAACzB,GAAG,CAAC,EAAE;MACpC,OAAO,KAAK;IACd;IAEAoB,iBAAiB,CAACO,GAAG,CAACF,KAAK,CAACzB,GAAG,CAAC;IAEhC,IAAI,OAAOmB,MAAM,CAACS,MAAM,KAAK,QAAQ,IAAIT,MAAM,CAACS,MAAM,KAAKH,KAAK,CAACzB,GAAG,EAAE;MACpE,IAAI6B,MAAM,GAAGhC,MAAM,CAACiC,iBAAiB,CACnCL,KAAK,EACLN,MAAM,EACNL,sBAAsB,CAACG,OAAO,CAC/B;MAIDY,MAAM,GACJA,MAAM,KAAK,IAAI,IAAIV,MAAM,CAACS,MAAM,KAAKH,KAAK,CAACzB,GAAG,GAAGyB,KAAK,GAAGI,MAAM;MAEjE,IAAIA,MAAM,KAAK,IAAI,EAAE;QACnBhB,gBAAgB,CAACM,MAAM,EAAEM,KAAK,KAAKI,MAAM,CAAC;QAE1C,IAAIJ,KAAK,KAAKI,MAAM,EAAE;UACpB,IAAME,WAAW,GAAGrC,mBAAmB,CACrCU,OAAO,EACPF,qBAAqB,EACrBuB,KAAK,CAACO,MAAM,EACZH,MAAM,CAACG,MAAM,EACbb,MAAM,CACP;UAED,IAAIY,WAAW,EAAE;YACf,OAAO,IAAI;UACb;UAEAhC,QAAQ,CAAC8B,MAAM,CAAC;QAClB;QAEA,IAAIpB,kBAAkB,KAAKc,SAAS,EAAE;UAGpC,IAAMU,WAAW,GAAGpC,MAAM,CAACqC,uBAAuB,CAACf,MAAM,CAAC;UAE1D,IAAIc,WAAW,IAAIjC,GAAG,KAAKuB,SAAS,EAAE;YACpCd,kBAAkB,CAACT,GAAG,CAAC;UACzB;QACF;QAEA,OAAO,IAAI;MACb;IACF;IAEA,IAAIO,cAAc,KAAKgB,SAAS,EAAE;MAEhC,IAAIhB,cAAc,CAACY,MAAM,EAAEC,iBAAiB,CAAC,EAAE;QAC7C,OAAO,IAAI;MACb;IACF;IAGA,KAAK,IAAIe,CAAC,GAAGlC,eAAe,CAACqB,MAAM,GAAG,CAAC,EAAEa,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACpD,IAAMC,QAAQ,GAAGnC,eAAe,CAACkC,CAAC,CAAC;MAEnC,IAAIC,QAAQ,CAACjB,MAAM,EAAEC,iBAAiB,CAAC,EAAE;QACvC,OAAO,IAAI;MACb;IACF;IAEA,OAAO,KAAK;EACd,CAAC,EACD,CACEnB,eAAe,EACfC,qBAAqB,EACrBE,OAAO,EACPN,QAAQ,EACRE,GAAG,EACHO,cAAc,EACdM,gBAAgB,EAChBJ,kBAAkB,EAClBZ,MAAM,EACNE,QAAQ,CACT,CACF;EAEDN,kBAAkB,CAAC;IACjBK,QAAQ,EAARA,QAAQ;IACRM,OAAO,EAAPA,OAAO;IACPF,qBAAA,EAAAA;EACF,CAAC,CAAC;EAEFX,KAAK,CAACyB,SAAS,CACb;IAAA,OAAML,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAG,QAAQ,EAAEH,QAAQ,CAAC;EAAA,GAC7C,CAACG,iBAAiB,EAAEH,QAAQ,CAAC,CAC9B;EAED,OAAOA,QAAQ;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}