{"ast": null, "code": "import StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nvar styles = StyleSheet.create({\n  container: {\n    backgroundColor: '#0D0D0D',\n    color: '#FFF',\n    borderRadius: 4,\n    fontSize: 16,\n    padding: 16,\n    marginVertical: 5\n  },\n  header: {\n    display: 'flex',\n    justifyContent: 'flex-end',\n    alignItems: 'center',\n    color: '#fff',\n    flexDirection: 'row',\n    marginBottom: 16\n  },\n  textDate: {\n    fontSize: 12,\n    color: '#fff',\n    fontFamily: 'Poppins_400Regular'\n  },\n  textMessage: {\n    fontSize: 14,\n    color: '#fff',\n    fontFamily: 'Poppins_400Regular'\n  }\n});\nexport default styles;", "map": {"version": 3, "names": ["styles", "StyleSheet", "create", "container", "backgroundColor", "color", "borderRadius", "fontSize", "padding", "marginVertical", "header", "display", "justifyContent", "alignItems", "flexDirection", "marginBottom", "textDate", "fontFamily", "textMessage"], "sources": ["C:/Users/<USER>/Desktop/Signals/CryptoSignalsApp/src/pages/Signals/Card/styles.js"], "sourcesContent": ["import { StyleSheet } from 'react-native';\r\n\r\nconst styles = StyleSheet.create({\r\n  container: {\r\n    backgroundColor: '#0D0D0D',\r\n    color: '#FFF',\r\n    borderRadius: 4,\r\n    fontSize: 16,\r\n    padding: 16,\r\n    marginVertical: 5,\r\n  },\r\n  header: {\r\n    display: 'flex',\r\n    justifyContent: 'flex-end',\r\n    alignItems: 'center',\r\n    color: '#fff',\r\n    flexDirection: 'row',\r\n    marginBottom: 16,\r\n  },\r\n  textDate: {\r\n    fontSize: 12,\r\n    color: '#fff',\r\n    fontFamily: 'Poppins_400Regular'\r\n  },\r\n  textMessage: {\r\n    fontSize: 14,\r\n    color: '#fff',\r\n    fontFamily: 'Poppins_400Regular'\r\n  }\r\n});\r\n\r\nexport default styles\r\n"], "mappings": ";AAEA,IAAMA,MAAM,GAAGC,UAAU,CAACC,MAAM,CAAC;EAC/BC,SAAS,EAAE;IACTC,eAAe,EAAE,SAAS;IAC1BC,KAAK,EAAE,MAAM;IACbC,YAAY,EAAE,CAAC;IACfC,QAAQ,EAAE,EAAE;IACZC,OAAO,EAAE,EAAE;IACXC,cAAc,EAAE;EAClB,CAAC;EACDC,MAAM,EAAE;IACNC,OAAO,EAAE,MAAM;IACfC,cAAc,EAAE,UAAU;IAC1BC,UAAU,EAAE,QAAQ;IACpBR,KAAK,EAAE,MAAM;IACbS,aAAa,EAAE,KAAK;IACpBC,YAAY,EAAE;EAChB,CAAC;EACDC,QAAQ,EAAE;IACRT,QAAQ,EAAE,EAAE;IACZF,KAAK,EAAE,MAAM;IACbY,UAAU,EAAE;EACd,CAAC;EACDC,WAAW,EAAE;IACXX,QAAQ,EAAE,EAAE;IACZF,KAAK,EAAE,MAAM;IACbY,UAAU,EAAE;EACd;AACF,CAAC,CAAC;AAEF,eAAejB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}