import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, Alert, Clipboard, Linking } from 'react-native';
import { Button, Card, Chip, ProgressBar } from 'react-native-paper';
import QRCode from 'react-native-qrcode-svg';
import paymentService from '../../services/PaymentService';
import { PAYMENT_CONFIG } from '../../config/api';
import styles from './styles';

const USDTPayment = ({ 
  planId, 
  onPaymentSuccess, 
  onPaymentCancel, 
  visible = true 
}) => {
  const [paymentSession, setPaymentSession] = useState(null);
  const [selectedNetwork, setSelectedNetwork] = useState('BSC');
  const [isLoading, setIsLoading] = useState(false);
  const [timeRemaining, setTimeRemaining] = useState(0);
  const [paymentStatus, setPaymentStatus] = useState('pending');

  useEffect(() => {
    if (visible && planId) {
      initializePayment();
    }
  }, [visible, planId]);

  useEffect(() => {
    let interval;
    if (paymentSession && paymentStatus === 'pending') {
      // Verificar status a cada 10 segundos
      interval = setInterval(checkPaymentStatus, 10000);
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [paymentSession, paymentStatus]);

  useEffect(() => {
    let timer;
    if (paymentSession && timeRemaining > 0) {
      timer = setTimeout(() => {
        setTimeRemaining(prev => prev - 1);
      }, 1000);
    } else if (timeRemaining === 0 && paymentSession) {
      setPaymentStatus('expired');
    }
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [timeRemaining, paymentSession]);

  const initializePayment = async () => {
    try {
      setIsLoading(true);
      const session = await paymentService.createPaymentSession(planId);
      setPaymentSession(session);
      
      // Calcular tempo restante
      const expiresAt = new Date(session.expiresAt);
      const now = new Date();
      const remaining = Math.max(0, Math.floor((expiresAt - now) / 1000));
      setTimeRemaining(remaining);
      
    } catch (error) {
      console.error('Erro ao inicializar pagamento:', error);
      Alert.alert('Erro', 'Não foi possível inicializar o pagamento. Tente novamente.');
    } finally {
      setIsLoading(false);
    }
  };

  const checkPaymentStatus = async () => {
    if (!paymentSession) return;

    try {
      const updatedSession = await paymentService.checkPaymentStatus(paymentSession.id);
      setPaymentSession(updatedSession);
      setPaymentStatus(updatedSession.status);

      if (updatedSession.status === 'confirmed') {
        Alert.alert(
          'Pagamento Confirmado! 🎉',
          'Seu plano foi ativado com sucesso!',
          [{ text: 'OK', onPress: () => onPaymentSuccess(updatedSession) }]
        );
      } else if (updatedSession.status === 'expired') {
        Alert.alert(
          'Pagamento Expirado',
          'O tempo para pagamento expirou. Inicie um novo pagamento.',
          [{ text: 'OK', onPress: onPaymentCancel }]
        );
      }
    } catch (error) {
      console.error('Erro ao verificar status:', error);
    }
  };

  const copyToClipboard = (text, label) => {
    Clipboard.setString(text);
    Alert.alert('Copiado!', `${label} copiado para a área de transferência.`);
  };

  const openExplorer = (network, address) => {
    const networkConfig = PAYMENT_CONFIG.SUPPORTED_NETWORKS[network];
    const url = `${networkConfig.explorerUrl}/address/${address}`;
    Linking.openURL(url);
  };

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getStatusColor = () => {
    switch (paymentStatus) {
      case 'pending': return '#FF9800';
      case 'confirming': return '#2196F3';
      case 'confirmed': return '#4CAF50';
      case 'expired': return '#F44336';
      default: return '#9E9E9E';
    }
  };

  const getStatusText = () => {
    switch (paymentStatus) {
      case 'pending': return 'Aguardando Pagamento';
      case 'confirming': return `Confirmando (${paymentSession?.confirmations || 0}/3)`;
      case 'confirmed': return 'Pagamento Confirmado';
      case 'expired': return 'Pagamento Expirado';
      default: return 'Processando...';
    }
  };

  if (!visible || !paymentSession) {
    return null;
  }

  const plan = paymentService.getPlanDetails(planId);
  const qrInfo = paymentService.generatePaymentQR(paymentSession, selectedNetwork);
  const networkConfig = PAYMENT_CONFIG.SUPPORTED_NETWORKS[selectedNetwork];

  return (
    <View style={styles.container}>
      {/* Header */}
      <Card style={styles.headerCard}>
        <View style={styles.header}>
          <Text style={styles.title}>Pagamento USDT</Text>
          <Text style={styles.planName}>Plano {plan.name} - ${plan.price} USDT</Text>
          
          <View style={styles.statusContainer}>
            <Chip 
              mode="outlined" 
              style={[styles.statusChip, { borderColor: getStatusColor() }]}
              textStyle={{ color: getStatusColor() }}
            >
              {getStatusText()}
            </Chip>
            
            {timeRemaining > 0 && paymentStatus === 'pending' && (
              <Text style={styles.timer}>
                ⏰ {formatTime(timeRemaining)}
              </Text>
            )}
          </View>
        </View>
      </Card>

      {/* Network Selection */}
      <Card style={styles.networkCard}>
        <Text style={styles.sectionTitle}>Selecione a Rede</Text>
        <View style={styles.networkButtons}>
          {Object.entries(PAYMENT_CONFIG.SUPPORTED_NETWORKS).map(([key, network]) => (
            <TouchableOpacity
              key={key}
              style={[
                styles.networkButton,
                selectedNetwork === key && styles.networkButtonSelected
              ]}
              onPress={() => setSelectedNetwork(key)}
            >
              <Text style={[
                styles.networkButtonText,
                selectedNetwork === key && styles.networkButtonTextSelected
              ]}>
                {network.name}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </Card>

      {/* QR Code */}
      <Card style={styles.qrCard}>
        <Text style={styles.sectionTitle}>Escaneie o QR Code</Text>
        <View style={styles.qrContainer}>
          <QRCode
            value={qrInfo.qrData}
            size={200}
            backgroundColor="white"
            color="black"
          />
        </View>
        <Text style={styles.qrInstructions}>
          Escaneie com sua carteira crypto ou copie o endereço abaixo
        </Text>
      </Card>

      {/* Payment Details */}
      <Card style={styles.detailsCard}>
        <Text style={styles.sectionTitle}>Detalhes do Pagamento</Text>
        
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Endereço:</Text>
          <TouchableOpacity 
            style={styles.copyButton}
            onPress={() => copyToClipboard(paymentSession.walletAddress, 'Endereço')}
          >
            <Text style={styles.addressText} numberOfLines={1}>
              {paymentSession.walletAddress}
            </Text>
            <Text style={styles.copyIcon}>📋</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Valor:</Text>
          <Text style={styles.detailValue}>{paymentSession.amount} USDT</Text>
        </View>

        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Rede:</Text>
          <Text style={styles.detailValue}>{networkConfig.name}</Text>
        </View>

        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>ID do Pagamento:</Text>
          <TouchableOpacity 
            onPress={() => copyToClipboard(paymentSession.id, 'ID do Pagamento')}
          >
            <Text style={styles.paymentId}>{paymentSession.id}</Text>
          </TouchableOpacity>
        </View>
      </Card>

      {/* Progress Bar for Confirmations */}
      {paymentStatus === 'confirming' && (
        <Card style={styles.progressCard}>
          <Text style={styles.sectionTitle}>Confirmações da Blockchain</Text>
          <ProgressBar 
            progress={(paymentSession.confirmations || 0) / 3} 
            color="#4CAF50"
            style={styles.progressBar}
          />
          <Text style={styles.progressText}>
            {paymentSession.confirmations || 0} de 3 confirmações
          </Text>
        </Card>
      )}

      {/* Action Buttons */}
      <View style={styles.actionButtons}>
        <Button
          mode="outlined"
          onPress={onPaymentCancel}
          style={styles.cancelButton}
          labelStyle={styles.cancelButtonText}
        >
          Cancelar
        </Button>
        
        <Button
          mode="contained"
          onPress={() => openExplorer(selectedNetwork, paymentSession.walletAddress)}
          style={styles.explorerButton}
          labelStyle={styles.explorerButtonText}
        >
          Ver no Explorer
        </Button>
      </View>

      {/* Instructions */}
      <Card style={styles.instructionsCard}>
        <Text style={styles.sectionTitle}>Instruções</Text>
        {qrInfo.instructions.steps.map((step, index) => (
          <Text key={index} style={styles.instructionStep}>
            {index + 1}. {step}
          </Text>
        ))}
        
        <View style={styles.warningsContainer}>
          <Text style={styles.warningsTitle}>⚠️ Importante:</Text>
          {qrInfo.instructions.warnings.map((warning, index) => (
            <Text key={index} style={styles.warningText}>
              • {warning}
            </Text>
          ))}
        </View>
      </Card>
    </View>
  );
};

export default USDTPayment;
