#!/usr/bin/env python3
"""
Dashboard Backend - API Flask para o sistema de sinais
"""

from flask import Flask, jsonify, request
from flask_cors import CORS
import sqlite3
import logging
import os
import sys
from datetime import datetime, timedelta
import json

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Adicionar o diretório raiz ao path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

try:
    from notifications.notification_manager import NotificationManager
except ImportError:
    NotificationManager = None
    logger.warning("Sistema de notificações não disponível")

# Definir caminho do banco de dados
DATABASE_PATH = os.path.join(os.path.dirname(__file__), '..', '..', 'signals.db')

app = Flask(__name__)
CORS(app)  # Permitir requisições do frontend React

class DashboardAPI:
    def __init__(self):
        self.db_path = DATABASE_PATH
        # Inicializar sistema de notificações se disponível
        self.notification_manager = NotificationManager() if NotificationManager else None

    def get_db_connection(self):
        """Conecta ao banco de dados SQLite"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row  # Permite acesso por nome da coluna
            return conn
        except Exception as e:
            logger.error(f"Erro ao conectar ao banco: {e}")
            return None

    def get_overview_metrics(self):
        """Obtém métricas gerais do sistema"""
        try:
            conn = self.get_db_connection()
            if not conn:
                # Retornar dados mock se não conseguir conectar
                return {
                    'active_signals': 0,
                    'total_signals': 0,
                    'win_rate': 0,
                    'avg_profit': 0,
                    'last_updated': datetime.now().isoformat(),
                    'status': 'database_error'
                }

            cursor = conn.cursor()

            # Verificar se a tabela signals existe
            cursor.execute("""
                SELECT name FROM sqlite_master
                WHERE type='table' AND name='signals'
            """)
            table_exists = cursor.fetchone()

            if not table_exists:
                # Tabela não existe, retornar dados padrão
                conn.close()
                return {
                    'active_signals': 0,
                    'total_signals': 0,
                    'win_rate': 0,
                    'avg_profit': 0,
                    'last_updated': datetime.now().isoformat(),
                    'status': 'no_data'
                }

            # Sinais ativos (últimas 24h)
            cursor.execute("""
                SELECT COUNT(*) as active_signals
                FROM signals
                WHERE created_at > datetime('now', '-24 hours')
                AND status != 'expired'
            """)
            result = cursor.fetchone()
            active_signals = result['active_signals'] if result else 0

            # Total de sinais
            cursor.execute("SELECT COUNT(*) as total_signals FROM signals")
            result = cursor.fetchone()
            total_signals = result['total_signals'] if result else 0

            # Win rate (últimos 30 dias)
            cursor.execute("""
                SELECT
                    COUNT(*) as total,
                    SUM(CASE WHEN profit_percentage > 0 THEN 1 ELSE 0 END) as wins
                FROM signals
                WHERE created_at > datetime('now', '-30 days')
                AND status = 'completed'
            """)
            result = cursor.fetchone()
            if result and result['total'] and result['total'] > 0:
                win_rate = (result['wins'] / result['total'] * 100)
            else:
                win_rate = 0

            # Profit total (últimos 30 dias)
            cursor.execute("""
                SELECT AVG(profit_percentage) as avg_profit
                FROM signals
                WHERE created_at > datetime('now', '-30 days')
                AND status = 'completed'
                AND profit_percentage IS NOT NULL
            """)
            result = cursor.fetchone()
            avg_profit = result['avg_profit'] if result and result['avg_profit'] else 0

            conn.close()

            return {
                'active_signals': active_signals,
                'total_signals': total_signals,
                'win_rate': round(win_rate, 2),
                'avg_profit': round(avg_profit, 2),
                'last_updated': datetime.now().isoformat(),
                'status': 'ok'
            }

        except Exception as e:
            logger.error(f"Erro ao obter métricas: {e}")
            # Retornar dados mock em caso de erro
            return {
                'active_signals': 0,
                'total_signals': 0,
                'win_rate': 0,
                'avg_profit': 0,
                'last_updated': datetime.now().isoformat(),
                'status': 'error',
                'error_message': str(e)
            }

    def get_recent_signals(self, limit=20):
        """Obtém sinais recentes"""
        try:
            conn = self.get_db_connection()
            if not conn:
                return []

            cursor = conn.cursor()
            cursor.execute("""
                SELECT
                    id, symbol, signal_type, strategy, entry_price,
                    stop_loss, take_profit_1, take_profit_2, take_profit_3, take_profit_4,
                    status, profit_percentage, leverage, created_at, closed_at
                FROM signals
                ORDER BY created_at DESC
                LIMIT ?
            """, (limit,))

            signals = []
            for row in cursor.fetchall():
                signals.append({
                    'id': row['id'],
                    'symbol': row['symbol'],
                    'signal_type': row['signal_type'],
                    'strategy': row['strategy'],
                    'entry_price': row['entry_price'],
                    'stop_loss': row['stop_loss'],
                    'take_profit_1': row['take_profit_1'],
                    'take_profit_2': row['take_profit_2'],
                    'take_profit_3': row['take_profit_3'],
                    'take_profit_4': row['take_profit_4'],
                    'status': row['status'],
                    'profit_percentage': row['profit_percentage'],
                    'leverage': row['leverage'],
                    'created_at': row['created_at'],
                    'closed_at': row['closed_at']
                })

            conn.close()
            return signals

        except Exception as e:
            logger.error(f"Erro ao obter sinais recentes: {e}")
            return []

    def get_signals_filtered(self, limit=20, skip=0, search='', status='', strategy=''):
        """Obtém sinais com filtros"""
        try:
            conn = self.get_db_connection()
            if not conn:
                return []

            cursor = conn.cursor()

            # Construir query com filtros
            query = """
                SELECT
                    id, symbol, signal_type, strategy, entry_price,
                    stop_loss, take_profit_1, take_profit_2, take_profit_3, take_profit_4,
                    status, profit_percentage, leverage, created_at, closed_at
                FROM signals
                WHERE 1=1
            """
            params = []

            if search:
                query += " AND symbol LIKE ?"
                params.append(f"%{search}%")

            if status:
                query += " AND status = ?"
                params.append(status)

            if strategy:
                query += " AND strategy = ?"
                params.append(strategy)

            query += " ORDER BY created_at DESC LIMIT ? OFFSET ?"
            params.extend([limit, skip])

            cursor.execute(query, params)

            signals = []
            for row in cursor.fetchall():
                # Formatar mensagem original para compatibilidade
                message_original = self._format_signal_message(row)

                signals.append({
                    'id': row['id'],
                    'symbol': row['symbol'],
                    'signal_type': row['signal_type'],
                    'strategy': row['strategy'],
                    'entry_price': row['entry_price'],
                    'stop_loss': row['stop_loss'],
                    'take_profit_1': row['take_profit_1'],
                    'take_profit_2': row['take_profit_2'],
                    'take_profit_3': row['take_profit_3'],
                    'take_profit_4': row['take_profit_4'],
                    'status': row['status'],
                    'profit_percentage': row['profit_percentage'],
                    'leverage': row['leverage'],
                    'createdAt': row['created_at'],
                    'closedAt': row['closed_at'],
                    'messageOriginal': message_original
                })

            conn.close()
            return signals

        except Exception as e:
            logger.error(f"Erro ao obter sinais filtrados: {e}")
            return []

    def get_channels(self):
        """Obtém lista de canais/estratégias disponíveis"""
        try:
            conn = self.get_db_connection()
            if not conn:
                # Retornar canais padrão se não conseguir conectar
                return self._get_default_channels()

            cursor = conn.cursor()

            # Obter estratégias únicas com estatísticas
            cursor.execute("""
                SELECT
                    strategy,
                    COUNT(*) as total_signals,
                    COUNT(CASE WHEN created_at > datetime('now', '-24 hours') THEN 1 END) as recent_signals,
                    MAX(created_at) as last_signal_at
                FROM signals
                GROUP BY strategy
                ORDER BY last_signal_at DESC
            """)

            channels = []
            for i, row in enumerate(cursor.fetchall()):
                channels.append({
                    'id': i + 1,
                    'externalId': f"strategy-{row['strategy'].lower()}",
                    'name': f"CryptoSignals {row['strategy']}",
                    'description': f"Sinais da estratégia {row['strategy']}",
                    'type': 'FUTURES' if row['strategy'] in ['Scalp', 'Breakout'] else 'SPOT',
                    'isPremium': False,
                    'lastSignalAt': row['last_signal_at'],
                    'totalSignals': row['total_signals'],
                    'recentSignals': row['recent_signals'],
                    'photo': f"https://via.placeholder.com/50x50/4CAF50/FFFFFF?text={row['strategy'][0]}"
                })

            conn.close()

            if not channels:
                return self._get_default_channels()

            return channels

        except Exception as e:
            logger.error(f"Erro ao obter canais: {e}")
            return self._get_default_channels()

    def get_channel_signals(self, channel_id, limit=20, skip=0, search=''):
        """Obtém sinais de um canal específico"""
        try:
            # Extrair estratégia do channel_id
            if channel_id.startswith('strategy-'):
                strategy = channel_id.replace('strategy-', '').title()
            else:
                # Mapear IDs numéricos para estratégias
                strategy_map = {
                    '1': 'Scalp',
                    '2': 'Breakout',
                    '3': 'Inside_Bar',
                    '4': 'MFI',
                    '5': 'Swing',
                    '6': 'Multi_Source',
                    '7': 'Volume_Analysis',
                    '8': 'Momentum'
                }
                strategy = strategy_map.get(str(channel_id), 'Scalp')

            return self.get_signals_filtered(limit, skip, search, '', strategy)

        except Exception as e:
            logger.error(f"Erro ao obter sinais do canal {channel_id}: {e}")
            return []

    def _get_default_channels(self):
        """Retorna canais padrão quando não há dados no banco"""
        strategies = ['Scalp', 'Breakout', 'Inside_Bar', 'MFI', 'Swing', 'Multi_Source', 'Volume_Analysis', 'Momentum']
        channels = []

        for i, strategy in enumerate(strategies):
            channels.append({
                'id': i + 1,
                'externalId': f"strategy-{strategy.lower()}",
                'name': f"CryptoSignals {strategy}",
                'description': f"Sinais da estratégia {strategy}",
                'type': 'FUTURES' if strategy in ['Scalp', 'Breakout'] else 'SPOT',
                'isPremium': False,
                'lastSignalAt': datetime.now().isoformat(),
                'totalSignals': 0,
                'recentSignals': 0,
                'photo': f"https://via.placeholder.com/50x50/4CAF50/FFFFFF?text={strategy[0]}"
            })

        return channels

    def _format_signal_message(self, signal_row):
        """Formata um sinal como mensagem original para compatibilidade"""
        try:
            direction = signal_row['signal_type']
            symbol = signal_row['symbol']
            strategy = signal_row['strategy']
            entry_price = signal_row['entry_price']
            stop_loss = signal_row['stop_loss']
            leverage = signal_row['leverage'] or 1

            # Determinar casas decimais baseado no preço
            decimal_places = 2 if entry_price >= 1 else 6

            message = f"""CRYPTOSIGNALS PROFESSIONAL
{'=' * 35}

ASSET: {symbol}
STRATEGY: {strategy.upper()}
DIRECTION: {direction}
LEVERAGE: {leverage}x
TIMEFRAME: 1-4H

ENTRY ZONE: {entry_price:.{decimal_places}f}
STOP LOSS: {stop_loss:.{decimal_places}f}

TAKE PROFIT LEVELS:"""

            # Adicionar take profits se existirem
            if signal_row['take_profit_1']:
                message += f"\nTP1: {signal_row['take_profit_1']:.{decimal_places}f}"
            if signal_row['take_profit_2']:
                message += f"\nTP2: {signal_row['take_profit_2']:.{decimal_places}f}"
            if signal_row['take_profit_3']:
                message += f"\nTP3: {signal_row['take_profit_3']:.{decimal_places}f}"
            if signal_row['take_profit_4']:
                message += f"\nTP4: {signal_row['take_profit_4']:.{decimal_places}f}"

            message += f"""

RISK MANAGEMENT:
- Position size: 1-2% of portfolio
- Risk/Reward: 1:2.5 minimum
- Strict stop loss adherence

{'=' * 35}
CRYPTOSIGNALS PROFESSIONAL"""

            return message

        except Exception as e:
            logger.error(f"Erro ao formatar mensagem do sinal: {e}")
            return f"Sinal {signal_row.get('symbol', 'N/A')} - {signal_row.get('signal_type', 'N/A')}"

    def get_performance_by_strategy(self, days=30):
        """Obtém performance por estratégia"""
        try:
            conn = self.get_db_connection()
            if not conn:
                return []

            cursor = conn.cursor()
            cursor.execute("""
                SELECT
                    strategy,
                    COUNT(*) as total_signals,
                    SUM(CASE WHEN profit_percentage > 0 THEN 1 ELSE 0 END) as wins,
                    AVG(profit_percentage) as avg_profit,
                    MAX(profit_percentage) as max_profit,
                    MIN(profit_percentage) as min_profit
                FROM signals
                WHERE created_at > datetime('now', '-{} days')
                AND status = 'completed'
                AND profit_percentage IS NOT NULL
                GROUP BY strategy
                ORDER BY avg_profit DESC
            """.format(days))

            strategies = []
            for row in cursor.fetchall():
                win_rate = (row['wins'] / row['total_signals'] * 100) if row['total_signals'] > 0 else 0
                strategies.append({
                    'strategy': row['strategy'],
                    'total_signals': row['total_signals'],
                    'win_rate': round(win_rate, 2),
                    'avg_profit': round(row['avg_profit'], 2),
                    'max_profit': round(row['max_profit'], 2),
                    'min_profit': round(row['min_profit'], 2)
                })

            conn.close()
            return strategies

        except Exception as e:
            logger.error(f"Erro ao obter performance por estratégia: {e}")
            return []

    def get_daily_performance(self, days=30):
        """Obtém performance diária"""
        try:
            conn = self.get_db_connection()
            if not conn:
                return []

            cursor = conn.cursor()
            cursor.execute("""
                SELECT
                    DATE(created_at) as date,
                    COUNT(*) as signals_count,
                    AVG(profit_percentage) as avg_profit,
                    SUM(CASE WHEN profit_percentage > 0 THEN 1 ELSE 0 END) as wins
                FROM signals
                WHERE created_at > datetime('now', '-{} days')
                AND status = 'completed'
                AND profit_percentage IS NOT NULL
                GROUP BY DATE(created_at)
                ORDER BY date DESC
            """.format(days))

            daily_data = []
            for row in cursor.fetchall():
                win_rate = (row['wins'] / row['signals_count'] * 100) if row['signals_count'] > 0 else 0
                daily_data.append({
                    'date': row['date'],
                    'signals_count': row['signals_count'],
                    'avg_profit': round(row['avg_profit'], 2),
                    'win_rate': round(win_rate, 2)
                })

            conn.close()
            return daily_data

        except Exception as e:
            logger.error(f"Erro ao obter performance diária: {e}")
            return []

# Instanciar a API
dashboard_api = DashboardAPI()

# Rotas da API
@app.route('/api/overview', methods=['GET'])
def get_overview():
    """Endpoint para métricas gerais"""
    try:
        metrics = dashboard_api.get_overview_metrics()
        if metrics:
            return jsonify(metrics)
        else:
            return jsonify({'error': 'Erro ao obter métricas'}), 500
    except Exception as e:
        logger.error(f"Erro no endpoint overview: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/signals', methods=['GET'])
def get_signals():
    """Endpoint para lista de sinais"""
    try:
        limit = request.args.get('limit', 20, type=int)
        skip = request.args.get('skip', 0, type=int)
        search = request.args.get('search', '', type=str)
        status = request.args.get('status', '', type=str)
        strategy = request.args.get('strategy', '', type=str)

        signals = dashboard_api.get_signals_filtered(limit, skip, search, status, strategy)
        return jsonify(signals)
    except Exception as e:
        logger.error(f"Erro no endpoint signals: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/channels', methods=['GET'])
def get_channels():
    """Endpoint para lista de canais/estratégias"""
    try:
        channels = dashboard_api.get_channels()
        return jsonify(channels)
    except Exception as e:
        logger.error(f"Erro no endpoint channels: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/channels/<channel_id>/signals', methods=['GET'])
def get_channel_signals(channel_id):
    """Endpoint para sinais de um canal específico"""
    try:
        limit = request.args.get('limit', 20, type=int)
        skip = request.args.get('skip', 0, type=int)
        search = request.args.get('search', '', type=str)

        signals = dashboard_api.get_channel_signals(channel_id, limit, skip, search)
        return jsonify(signals)
    except Exception as e:
        logger.error(f"Erro no endpoint channel signals: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/performance/strategies', methods=['GET'])
def get_strategy_performance():
    """Endpoint para performance por estratégia"""
    try:
        days = request.args.get('days', 30, type=int)
        performance = dashboard_api.get_performance_by_strategy(days)
        return jsonify(performance)
    except Exception as e:
        logger.error(f"Erro no endpoint strategy performance: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/performance/daily', methods=['GET'])
def get_daily_performance():
    """Endpoint para performance diária"""
    try:
        days = request.args.get('days', 30, type=int)
        performance = dashboard_api.get_daily_performance(days)
        return jsonify(performance)
    except Exception as e:
        logger.error(f"Erro no endpoint daily performance: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/system/status', methods=['GET'])
def get_system_status():
    """Endpoint para status do sistema"""
    try:
        # Verificar se o banco está acessível
        conn = dashboard_api.get_db_connection()
        db_status = "online" if conn else "offline"
        if conn:
            conn.close()

        return jsonify({
            'status': 'online',
            'database': db_status,
            'timestamp': datetime.now().isoformat(),
            'version': '1.1.0'
        })
    except Exception as e:
        logger.error(f"Erro no endpoint system status: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({'status': 'healthy', 'timestamp': datetime.now().isoformat()})

# Endpoints de Notificações
@app.route('/api/notifications/status', methods=['GET'])
def get_notifications_status():
    """Status do sistema de notificações"""
    try:
        if not dashboard_api.notification_manager:
            return jsonify({'error': 'Sistema de notificações não disponível'}), 503

        status = dashboard_api.notification_manager.get_status()
        return jsonify(status)
    except Exception as e:
        logger.error(f"Erro no endpoint notifications status: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/notifications/test', methods=['POST'])
def test_notifications():
    """Testa todas as notificações"""
    try:
        if not dashboard_api.notification_manager:
            return jsonify({'error': 'Sistema de notificações não disponível'}), 503

        results = dashboard_api.notification_manager.test_all_notifications()
        return jsonify(results)
    except Exception as e:
        logger.error(f"Erro no endpoint test notifications: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/notifications/email/recipients', methods=['GET', 'POST', 'DELETE'])
def manage_email_recipients():
    """Gerencia destinatários de email"""
    try:
        if not dashboard_api.notification_manager:
            return jsonify({'error': 'Sistema de notificações não disponível'}), 503

        if request.method == 'GET':
            status = dashboard_api.notification_manager.get_status()
            recipients = dashboard_api.notification_manager.config.get('email', {}).get('recipients', [])
            return jsonify({'recipients': recipients})

        elif request.method == 'POST':
            data = request.get_json()
            email = data.get('email')
            if not email:
                return jsonify({'error': 'Email é obrigatório'}), 400

            success = dashboard_api.notification_manager.add_email_recipient(email)
            if success:
                return jsonify({'message': 'Email adicionado com sucesso'})
            else:
                return jsonify({'error': 'Erro ao adicionar email'}), 500

        elif request.method == 'DELETE':
            data = request.get_json()
            email = data.get('email')
            if not email:
                return jsonify({'error': 'Email é obrigatório'}), 400

            success = dashboard_api.notification_manager.remove_email_recipient(email)
            if success:
                return jsonify({'message': 'Email removido com sucesso'})
            else:
                return jsonify({'error': 'Erro ao remover email'}), 500

    except Exception as e:
        logger.error(f"Erro no endpoint email recipients: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/notifications/push/subscribe', methods=['POST'])
def subscribe_push():
    """Adiciona assinatura push"""
    try:
        if not dashboard_api.notification_manager:
            return jsonify({'error': 'Sistema de notificações não disponível'}), 503

        data = request.get_json()
        if not data:
            return jsonify({'error': 'Dados de assinatura são obrigatórios'}), 400

        success = dashboard_api.notification_manager.add_push_subscription(data)
        if success:
            return jsonify({'message': 'Assinatura push adicionada com sucesso'})
        else:
            return jsonify({'error': 'Erro ao adicionar assinatura push'}), 500

    except Exception as e:
        logger.error(f"Erro no endpoint push subscribe: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/notifications/webhook', methods=['GET', 'POST', 'DELETE'])
def manage_webhooks():
    """Gerencia webhooks"""
    try:
        if not dashboard_api.notification_manager:
            return jsonify({'error': 'Sistema de notificações não disponível'}), 503

        if request.method == 'GET':
            status = dashboard_api.notification_manager.get_status()
            return jsonify(status.get('webhook', {}))

        elif request.method == 'POST':
            data = request.get_json()
            if not data:
                return jsonify({'error': 'Dados do webhook são obrigatórios'}), 400

            success = dashboard_api.notification_manager.add_webhook(data)
            if success:
                return jsonify({'message': 'Webhook adicionado com sucesso'})
            else:
                return jsonify({'error': 'Erro ao adicionar webhook'}), 500

        elif request.method == 'DELETE':
            data = request.get_json()
            webhook_name = data.get('name')
            if not webhook_name:
                return jsonify({'error': 'Nome do webhook é obrigatório'}), 400

            success = dashboard_api.notification_manager.webhook_notifier.remove_webhook(webhook_name)
            if success:
                return jsonify({'message': 'Webhook removido com sucesso'})
            else:
                return jsonify({'error': 'Erro ao remover webhook'}), 500

    except Exception as e:
        logger.error(f"Erro no endpoint webhooks: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/notifications/filters', methods=['GET', 'POST'])
def manage_notification_filters():
    """Gerencia filtros de notificação"""
    try:
        if not dashboard_api.notification_manager:
            return jsonify({'error': 'Sistema de notificações não disponível'}), 503

        if request.method == 'GET':
            status = dashboard_api.notification_manager.get_status()
            return jsonify(status.get('filters', {}))

        elif request.method == 'POST':
            data = request.get_json()
            if not data:
                return jsonify({'error': 'Dados dos filtros são obrigatórios'}), 400

            success = dashboard_api.notification_manager.update_filters(data)
            if success:
                return jsonify({'message': 'Filtros atualizados com sucesso'})
            else:
                return jsonify({'error': 'Erro ao atualizar filtros'}), 500

    except Exception as e:
        logger.error(f"Erro no endpoint notification filters: {e}")
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    logger.info("Iniciando Dashboard API...")
    app.run(debug=True, host='0.0.0.0', port=5000)
