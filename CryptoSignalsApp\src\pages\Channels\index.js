import React, { useState, useEffect } from 'react';
import { View, ScrollView, Text, Alert } from 'react-native';
import Wrapper from '../../components/Wrapper';
import Card from '../../components/Card';

export default function Channels({route = {}}) {
  const [isLoading, setIsLoading] = useState(true);
  const [channels, setChannels] = useState([]);

  const { accountHasBeenRecovered } = route.params || {accountHasBeenRecovered: false};

  useEffect(() => {
    const mockChannels = [
      {
        id: 1,
        name: 'Bitcoin Pro Signals',
        description: 'Premium Bitcoin trading signals',
        isPremium: true,
        subscribers: 12500,
        avatar: '₿'
      },
      {
        id: 2,
        name: 'Ethereum Futures',
        description: 'Ethereum trading strategies',
        isPremium: true,
        subscribers: 8900,
        avatar: '⚡'
      },
      {
        id: 3,
        name: 'Altcoin Gems',
        description: 'Hidden altcoin opportunities',
        isPremium: false,
        subscribers: 15600,
        avatar: '💎'
      }
    ];

    setTimeout(() => {
      setChannels(mockChannels);
      setIsLoading(false);
    }, 500);
  }, []);

  const handleChannelPress = (channel) => {
    Alert.alert(
      channel.name,
      `View signals for ${channel.name}. Feature coming soon!`
    );
  };

  useEffect(() => {
    if(accountHasBeenRecovered) {
      Alert.alert("Success", "Account recovered successfully");
    }
  }, [accountHasBeenRecovered]);

  if (isLoading) {
    return (
      <Wrapper>
        <View style={{ padding: 16 }}>
          <Text style={{ color: '#fff', fontSize: 24, fontWeight: 'bold', marginBottom: 20 }}>
            Signal Channels 📺
          </Text>
          <Text style={{ color: '#8a8a8a', fontSize: 16, textAlign: 'center', marginTop: 50 }}>
            Loading channels...
          </Text>
        </View>
      </Wrapper>
    );
  }

  return (
    <Wrapper>
      <ScrollView style={{ flex: 1, padding: 16 }}>
        {/* Header */}
        <Text style={{
          color: '#fff',
          fontSize: 28,
          fontWeight: 'bold',
          marginBottom: 4
        }}>
          Signal Channels 📺
        </Text>
        <Text style={{
          color: '#8a8a8a',
          fontSize: 14,
          marginBottom: 20
        }}>
          Follow the best crypto trading signal providers
        </Text>

        {/* Channels List */}
        {channels.map((channel) => (
          <Card key={channel.id} style={{ marginBottom: 12 }} onPress={() => handleChannelPress(channel)}>
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <View style={{
                backgroundColor: '#333',
                borderRadius: 12,
                padding: 12,
                marginRight: 12,
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <Text style={{ fontSize: 24 }}>{channel.avatar}</Text>
              </View>

              <View style={{ flex: 1 }}>
                <Text style={{
                  color: '#fff',
                  fontSize: 16,
                  fontWeight: '600',
                  marginBottom: 4
                }}>
                  {channel.name}
                </Text>

                <Text style={{
                  color: '#ccc',
                  fontSize: 13,
                  marginBottom: 8
                }}>
                  {channel.description}
                </Text>

                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                  <View style={{
                    backgroundColor: channel.isPremium ? '#FECB37' : '#4CAF50',
                    paddingHorizontal: 6,
                    paddingVertical: 2,
                    borderRadius: 4,
                    marginRight: 8
                  }}>
                    <Text style={{
                      color: channel.isPremium ? '#000' : '#fff',
                      fontSize: 10,
                      fontWeight: '600'
                    }}>
                      {channel.isPremium ? 'PREMIUM' : 'FREE'}
                    </Text>
                  </View>
                  <Text style={{ color: '#8a8a8a', fontSize: 12 }}>
                    {channel.subscribers.toLocaleString()} subscribers
                  </Text>
                </View>
              </View>
            </View>
          </Card>
        ))}
      </ScrollView>
    </Wrapper>
  );
}