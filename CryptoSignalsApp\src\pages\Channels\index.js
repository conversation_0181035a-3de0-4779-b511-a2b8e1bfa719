import React, { useState, useEffect, useContext } from 'react';
import { View, ScrollView, Text, Alert } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Wrapper from '../../components/Wrapper';
import Card from '../../components/Card';
import { AxiosContext } from '../../store/axios';

export default function Channels({route = {}}) {
  const [isLoading, setIsLoading] = useState(true);
  const [channels, setChannels] = useState([]);
  const [error, setError] = useState(null);
  const [api] = useContext(AxiosContext);
  const navigation = useNavigation();

  const { accountHasBeenRecovered } = route.params || {accountHasBeenRecovered: false};

  useEffect(() => {
    loadChannels();
  }, []);

  const loadChannels = async () => {
    try {
      setIsLoading(true);
      setError(null);

      console.log('Carregando canais da API...');
      const response = await api.get('/channels');

      if (response.data && Array.isArray(response.data)) {
        console.log('Canais carregados:', response.data.length);
        setChannels(response.data);
      } else {
        console.warn('Resposta da API não contém array de canais:', response.data);
        setChannels([]);
      }
    } catch (err) {
      console.error('Erro ao carregar canais:', err);
      setError('Erro ao carregar canais. Usando dados offline.');

      // Fallback para dados mock em caso de erro
      setChannels([
        {
          id: 1,
          externalId: 'strategy-scalp',
          name: 'CryptoSignals Scalp',
          description: 'Sinais de scalping de alta frequência',
          type: 'FUTURES',
          isPremium: false,
          totalSignals: 0,
          recentSignals: 0,
          lastSignalAt: new Date().toISOString(),
          photo: 'https://via.placeholder.com/50x50/4CAF50/FFFFFF?text=S'
        },
        {
          id: 2,
          externalId: 'strategy-swing',
          name: 'CryptoSignals Swing',
          description: 'Sinais de swing trading',
          type: 'SPOT',
          isPremium: false,
          totalSignals: 0,
          recentSignals: 0,
          lastSignalAt: new Date().toISOString(),
          photo: 'https://via.placeholder.com/50x50/2196F3/FFFFFF?text=S'
        }
      ]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleChannelPress = (channel) => {
    console.log('Canal selecionado:', channel);

    // Navegar para a página de sinais do canal
    navigation.navigate('Signals', {
      channelId: channel.externalId || channel.id,
      channelName: channel.name
    });
  };

  useEffect(() => {
    if(accountHasBeenRecovered) {
      Alert.alert("Success", "Account recovered successfully");
    }
  }, [accountHasBeenRecovered]);

  const formatLastSignalTime = (lastSignalAt) => {
    if (!lastSignalAt) return 'Nunca';

    try {
      const date = new Date(lastSignalAt);
      const now = new Date();
      const diffMs = now - date;
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
      const diffDays = Math.floor(diffHours / 24);

      if (diffHours < 1) return 'Agora mesmo';
      if (diffHours < 24) return `${diffHours}h atrás`;
      if (diffDays < 7) return `${diffDays}d atrás`;
      return date.toLocaleDateString();
    } catch {
      return 'Recente';
    }
  };

  const getChannelAvatar = (channel) => {
    const strategy = channel.name.toLowerCase();
    if (strategy.includes('scalp')) return '⚡';
    if (strategy.includes('swing')) return '📈';
    if (strategy.includes('breakout')) return '🚀';
    if (strategy.includes('mfi')) return '💰';
    if (strategy.includes('volume')) return '📊';
    if (strategy.includes('momentum')) return '🎯';
    return '📡';
  };

  if (isLoading) {
    return (
      <Wrapper>
        <View style={{ padding: 16 }}>
          <Text style={{ color: '#fff', fontSize: 24, fontWeight: 'bold', marginBottom: 20 }}>
            Signal Channels 📺
          </Text>
          <Text style={{ color: '#8a8a8a', fontSize: 16, textAlign: 'center', marginTop: 50 }}>
            Carregando canais...
          </Text>
        </View>
      </Wrapper>
    );
  }

  return (
    <Wrapper>
      <ScrollView style={{ flex: 1, padding: 16 }}>
        {/* Header */}
        <Text style={{
          color: '#fff',
          fontSize: 28,
          fontWeight: 'bold',
          marginBottom: 4
        }}>
          Signal Channels 📺
        </Text>
        <Text style={{
          color: '#8a8a8a',
          fontSize: 14,
          marginBottom: 8
        }}>
          Estratégias de trading automatizadas
        </Text>

        {/* Error Message */}
        {error && (
          <View style={{
            backgroundColor: '#FF6B35',
            padding: 12,
            borderRadius: 8,
            marginBottom: 16
          }}>
            <Text style={{ color: '#fff', fontSize: 12, textAlign: 'center' }}>
              {error}
            </Text>
          </View>
        )}

        {/* Refresh Button */}
        <Card style={{ marginBottom: 16 }} onPress={loadChannels}>
          <View style={{ alignItems: 'center', padding: 8 }}>
            <Text style={{ color: '#4CAF50', fontSize: 14, fontWeight: '600' }}>
              🔄 Atualizar Canais
            </Text>
          </View>
        </Card>

        {/* Channels List */}
        {channels.length === 0 ? (
          <View style={{ alignItems: 'center', marginTop: 50 }}>
            <Text style={{ color: '#8a8a8a', fontSize: 16, textAlign: 'center' }}>
              Nenhum canal disponível
            </Text>
            <Text style={{ color: '#666', fontSize: 12, textAlign: 'center', marginTop: 8 }}>
              Verifique se o gerador de sinais está rodando
            </Text>
          </View>
        ) : (
          channels.map((channel) => (
            <Card key={channel.id} style={{ marginBottom: 12 }} onPress={() => handleChannelPress(channel)}>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <View style={{
                  backgroundColor: '#333',
                  borderRadius: 12,
                  padding: 12,
                  marginRight: 12,
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <Text style={{ fontSize: 24 }}>{getChannelAvatar(channel)}</Text>
                </View>

                <View style={{ flex: 1 }}>
                  <Text style={{
                    color: '#fff',
                    fontSize: 16,
                    fontWeight: '600',
                    marginBottom: 4
                  }}>
                    {channel.name}
                  </Text>

                  <Text style={{
                    color: '#ccc',
                    fontSize: 13,
                    marginBottom: 8
                  }}>
                    {channel.description}
                  </Text>

                  <View style={{ flexDirection: 'row', alignItems: 'center', flexWrap: 'wrap' }}>
                    <View style={{
                      backgroundColor: channel.isPremium ? '#FECB37' : '#4CAF50',
                      paddingHorizontal: 6,
                      paddingVertical: 2,
                      borderRadius: 4,
                      marginRight: 8,
                      marginBottom: 4
                    }}>
                      <Text style={{
                        color: channel.isPremium ? '#000' : '#fff',
                        fontSize: 10,
                        fontWeight: '600'
                      }}>
                        {channel.type || 'SPOT'}
                      </Text>
                    </View>

                    <View style={{
                      backgroundColor: '#2196F3',
                      paddingHorizontal: 6,
                      paddingVertical: 2,
                      borderRadius: 4,
                      marginRight: 8,
                      marginBottom: 4
                    }}>
                      <Text style={{
                        color: '#fff',
                        fontSize: 10,
                        fontWeight: '600'
                      }}>
                        {channel.totalSignals || 0} sinais
                      </Text>
                    </View>

                    <Text style={{ color: '#8a8a8a', fontSize: 12 }}>
                      {formatLastSignalTime(channel.lastSignalAt)}
                    </Text>
                  </View>
                </View>
              </View>
            </Card>
          ))
        )}
      </ScrollView>
    </Wrapper>
  );
}