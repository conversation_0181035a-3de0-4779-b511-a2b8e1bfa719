{"ast": null, "code": "import { registerRootComponent } from 'expo';\nimport App from \"./App\";\nregisterRootComponent(App);", "map": {"version": 3, "names": ["registerRootComponent", "App"], "sources": ["C:/Users/<USER>/Desktop/Signals/CryptoSignalsApp/index.js"], "sourcesContent": ["import { registerRootComponent } from 'expo';\r\n\r\nimport App from './App';\r\n\r\n// registerRootComponent calls AppRegistry.registerComponent('main', () => App);\r\n// It also ensures that whether you load the app in Expo Go or in a native build,\r\n// the environment is set up appropriately\r\nregisterRootComponent(App);\r\n"], "mappings": "AAAA,SAASA,qBAAqB,QAAQ,MAAM;AAE5C,OAAOC,GAAG;AAKVD,qBAAqB,CAACC,GAAG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}