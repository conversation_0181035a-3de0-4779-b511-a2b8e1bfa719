{"ast": null, "code": "import { addEventListener } from \"../addEventListener\";\nimport canUseDOM from \"../canUseDom\";\nvar supportsPointerEvent = function supportsPointerEvent() {\n  return !!(typeof window !== 'undefined' && window.PointerEvent != null);\n};\nvar activeModality = 'keyboard';\nvar modality = 'keyboard';\nvar previousModality;\nvar previousActiveModality;\nvar isEmulatingMouseEvents = false;\nvar listeners = new Set();\nvar KEYBOARD = 'keyboard';\nvar MOUSE = 'mouse';\nvar TOUCH = 'touch';\nvar BLUR = 'blur';\nvar CONTEXTMENU = 'contextmenu';\nvar FOCUS = 'focus';\nvar KEYDOWN = 'keydown';\nvar MOUSEDOWN = 'mousedown';\nvar MOUSEMOVE = 'mousemove';\nvar MOUSEUP = 'mouseup';\nvar POINTERDOWN = 'pointerdown';\nvar POINTERMOVE = 'pointermove';\nvar SCROLL = 'scroll';\nvar SELECTIONCHANGE = 'selectionchange';\nvar TOUCHCANCEL = 'touchcancel';\nvar TOUCHMOVE = 'touchmove';\nvar TOUCHSTART = 'touchstart';\nvar VISIBILITYCHANGE = 'visibilitychange';\nvar bubbleOptions = {\n  passive: true\n};\nvar captureOptions = {\n  capture: true,\n  passive: true\n};\nfunction restoreModality() {\n  if (previousModality != null || previousActiveModality != null) {\n    if (previousModality != null) {\n      modality = previousModality;\n      previousModality = null;\n    }\n    if (previousActiveModality != null) {\n      activeModality = previousActiveModality;\n      previousActiveModality = null;\n    }\n    callListeners();\n  }\n}\nfunction onBlurWindow() {\n  previousModality = modality;\n  previousActiveModality = activeModality;\n  activeModality = KEYBOARD;\n  modality = KEYBOARD;\n  callListeners();\n  isEmulatingMouseEvents = false;\n}\nfunction onFocusWindow() {\n  restoreModality();\n}\nfunction onKeyDown(event) {\n  if (event.metaKey || event.altKey || event.ctrlKey) {\n    return;\n  }\n  if (modality !== KEYBOARD) {\n    modality = KEYBOARD;\n    activeModality = KEYBOARD;\n    callListeners();\n  }\n}\nfunction onVisibilityChange() {\n  if (document.visibilityState !== 'hidden') {\n    restoreModality();\n  }\n}\nfunction onPointerish(event) {\n  var eventType = event.type;\n  if (supportsPointerEvent()) {\n    if (eventType === POINTERDOWN) {\n      if (activeModality !== event.pointerType) {\n        modality = event.pointerType;\n        activeModality = event.pointerType;\n        callListeners();\n      }\n      return;\n    }\n    if (eventType === POINTERMOVE) {\n      if (modality !== event.pointerType) {\n        modality = event.pointerType;\n        callListeners();\n      }\n      return;\n    }\n  } else {\n    if (!isEmulatingMouseEvents) {\n      if (eventType === MOUSEDOWN) {\n        if (activeModality !== MOUSE) {\n          modality = MOUSE;\n          activeModality = MOUSE;\n          callListeners();\n        }\n      }\n      if (eventType === MOUSEMOVE) {\n        if (modality !== MOUSE) {\n          modality = MOUSE;\n          callListeners();\n        }\n      }\n    }\n    if (eventType === TOUCHSTART) {\n      isEmulatingMouseEvents = true;\n      if (event.touches && event.touches.length > 1) {\n        isEmulatingMouseEvents = false;\n      }\n      if (activeModality !== TOUCH) {\n        modality = TOUCH;\n        activeModality = TOUCH;\n        callListeners();\n      }\n      return;\n    }\n    if (eventType === CONTEXTMENU || eventType === MOUSEUP || eventType === SELECTIONCHANGE || eventType === SCROLL || eventType === TOUCHCANCEL || eventType === TOUCHMOVE) {\n      isEmulatingMouseEvents = false;\n    }\n  }\n}\nif (canUseDOM) {\n  addEventListener(window, BLUR, onBlurWindow, bubbleOptions);\n  addEventListener(window, FOCUS, onFocusWindow, bubbleOptions);\n  addEventListener(document, KEYDOWN, onKeyDown, captureOptions);\n  addEventListener(document, VISIBILITYCHANGE, onVisibilityChange, captureOptions);\n  addEventListener(document, POINTERDOWN, onPointerish, captureOptions);\n  addEventListener(document, POINTERMOVE, onPointerish, captureOptions);\n  addEventListener(document, CONTEXTMENU, onPointerish, captureOptions);\n  addEventListener(document, MOUSEDOWN, onPointerish, captureOptions);\n  addEventListener(document, MOUSEMOVE, onPointerish, captureOptions);\n  addEventListener(document, MOUSEUP, onPointerish, captureOptions);\n  addEventListener(document, TOUCHCANCEL, onPointerish, captureOptions);\n  addEventListener(document, TOUCHMOVE, onPointerish, captureOptions);\n  addEventListener(document, TOUCHSTART, onPointerish, captureOptions);\n  addEventListener(document, SELECTIONCHANGE, onPointerish, captureOptions);\n  addEventListener(document, SCROLL, onPointerish, captureOptions);\n}\nfunction callListeners() {\n  var value = {\n    activeModality: activeModality,\n    modality: modality\n  };\n  listeners.forEach(function (listener) {\n    listener(value);\n  });\n}\nexport function getActiveModality() {\n  return activeModality;\n}\nexport function getModality() {\n  return modality;\n}\nexport function addModalityListener(listener) {\n  listeners.add(listener);\n  return function () {\n    listeners.delete(listener);\n  };\n}\nexport function testOnly_resetActiveModality() {\n  isEmulatingMouseEvents = false;\n  activeModality = KEYBOARD;\n  modality = KEYBOARD;\n}", "map": {"version": 3, "names": ["addEventListener", "canUseDOM", "supportsPointerEvent", "window", "PointerEvent", "activeModality", "modality", "previousModality", "previousActiveModality", "isEmulatingMouseEvents", "listeners", "Set", "KEYBOARD", "MOUSE", "TOUCH", "BLUR", "CONTEXTMENU", "FOCUS", "KEYDOWN", "MOUSEDOWN", "MOUSEMOVE", "MOUSEUP", "POINTERDOWN", "POINTERMOVE", "SCROLL", "SELECTIONCHANGE", "TOUCHCANCEL", "TOUCHMOVE", "TOUCHSTART", "VISIBILITYCHANGE", "bubbleOptions", "passive", "captureOptions", "capture", "restoreModality", "callListeners", "onBlurWindow", "onFocusWindow", "onKeyDown", "event", "metaKey", "altKey", "ctrl<PERSON>ey", "onVisibilityChange", "document", "visibilityState", "onPointerish", "eventType", "type", "pointerType", "touches", "length", "value", "for<PERSON>ach", "listener", "getActiveModality", "getModality", "addModalityListener", "add", "delete", "testOnly_resetActiveModality"], "sources": ["C:/Users/<USER>/Desktop/Signals/CryptoSignalsApp/node_modules/react-native-web/dist/modules/modality/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport { addEventListener } from '../addEventListener';\nimport canUseDOM from '../canUseDom';\nvar supportsPointerEvent = () => !!(typeof window !== 'undefined' && window.PointerEvent != null);\nvar activeModality = 'keyboard';\nvar modality = 'keyboard';\nvar previousModality;\nvar previousActiveModality;\nvar isEmulatingMouseEvents = false;\nvar listeners = new Set();\nvar KEYBOARD = 'keyboard';\nvar MOUSE = 'mouse';\nvar TOUCH = 'touch';\nvar BLUR = 'blur';\nvar CONTEXTMENU = 'contextmenu';\nvar FOCUS = 'focus';\nvar KEYDOWN = 'keydown';\nvar MOUSEDOWN = 'mousedown';\nvar MOUSEMOVE = 'mousemove';\nvar MOUSEUP = 'mouseup';\nvar POINTERDOWN = 'pointerdown';\nvar POINTERMOVE = 'pointermove';\nvar SCROLL = 'scroll';\nvar SELECTIONCHANGE = 'selectionchange';\nvar TOUCHCANCEL = 'touchcancel';\nvar TOUCHMOVE = 'touchmove';\nvar TOUCHSTART = 'touchstart';\nvar VISIBILITYCHANGE = 'visibilitychange';\nvar bubbleOptions = {\n  passive: true\n};\nvar captureOptions = {\n  capture: true,\n  passive: true\n};\nfunction restoreModality() {\n  if (previousModality != null || previousActiveModality != null) {\n    if (previousModality != null) {\n      modality = previousModality;\n      previousModality = null;\n    }\n    if (previousActiveModality != null) {\n      activeModality = previousActiveModality;\n      previousActiveModality = null;\n    }\n    callListeners();\n  }\n}\nfunction onBlurWindow() {\n  previousModality = modality;\n  previousActiveModality = activeModality;\n  activeModality = KEYBOARD;\n  modality = KEYBOARD;\n  callListeners();\n  // for fallback events\n  isEmulatingMouseEvents = false;\n}\nfunction onFocusWindow() {\n  restoreModality();\n}\nfunction onKeyDown(event) {\n  if (event.metaKey || event.altKey || event.ctrlKey) {\n    return;\n  }\n  if (modality !== KEYBOARD) {\n    modality = KEYBOARD;\n    activeModality = KEYBOARD;\n    callListeners();\n  }\n}\nfunction onVisibilityChange() {\n  if (document.visibilityState !== 'hidden') {\n    restoreModality();\n  }\n}\nfunction onPointerish(event) {\n  var eventType = event.type;\n  if (supportsPointerEvent()) {\n    if (eventType === POINTERDOWN) {\n      if (activeModality !== event.pointerType) {\n        modality = event.pointerType;\n        activeModality = event.pointerType;\n        callListeners();\n      }\n      return;\n    }\n    if (eventType === POINTERMOVE) {\n      if (modality !== event.pointerType) {\n        modality = event.pointerType;\n        callListeners();\n      }\n      return;\n    }\n  }\n  // Fallback for non-PointerEvent environment\n  else {\n    if (!isEmulatingMouseEvents) {\n      if (eventType === MOUSEDOWN) {\n        if (activeModality !== MOUSE) {\n          modality = MOUSE;\n          activeModality = MOUSE;\n          callListeners();\n        }\n      }\n      if (eventType === MOUSEMOVE) {\n        if (modality !== MOUSE) {\n          modality = MOUSE;\n          callListeners();\n        }\n      }\n    }\n\n    // Flag when browser may produce emulated events\n    if (eventType === TOUCHSTART) {\n      isEmulatingMouseEvents = true;\n      if (event.touches && event.touches.length > 1) {\n        isEmulatingMouseEvents = false;\n      }\n      if (activeModality !== TOUCH) {\n        modality = TOUCH;\n        activeModality = TOUCH;\n        callListeners();\n      }\n      return;\n    }\n\n    // Remove flag after emulated events are finished or cancelled, and if an\n    // event occurs that cuts short a touch event sequence.\n    if (eventType === CONTEXTMENU || eventType === MOUSEUP || eventType === SELECTIONCHANGE || eventType === SCROLL || eventType === TOUCHCANCEL || eventType === TOUCHMOVE) {\n      isEmulatingMouseEvents = false;\n    }\n  }\n}\nif (canUseDOM) {\n  // Window events\n  addEventListener(window, BLUR, onBlurWindow, bubbleOptions);\n  addEventListener(window, FOCUS, onFocusWindow, bubbleOptions);\n  // Must be capture phase because 'stopPropagation' might prevent these\n  // events bubbling to the document.\n  addEventListener(document, KEYDOWN, onKeyDown, captureOptions);\n  addEventListener(document, VISIBILITYCHANGE, onVisibilityChange, captureOptions);\n  addEventListener(document, POINTERDOWN, onPointerish, captureOptions);\n  addEventListener(document, POINTERMOVE, onPointerish, captureOptions);\n  // Fallback events\n  addEventListener(document, CONTEXTMENU, onPointerish, captureOptions);\n  addEventListener(document, MOUSEDOWN, onPointerish, captureOptions);\n  addEventListener(document, MOUSEMOVE, onPointerish, captureOptions);\n  addEventListener(document, MOUSEUP, onPointerish, captureOptions);\n  addEventListener(document, TOUCHCANCEL, onPointerish, captureOptions);\n  addEventListener(document, TOUCHMOVE, onPointerish, captureOptions);\n  addEventListener(document, TOUCHSTART, onPointerish, captureOptions);\n  addEventListener(document, SELECTIONCHANGE, onPointerish, captureOptions);\n  addEventListener(document, SCROLL, onPointerish, captureOptions);\n}\nfunction callListeners() {\n  var value = {\n    activeModality,\n    modality\n  };\n  listeners.forEach(listener => {\n    listener(value);\n  });\n}\nexport function getActiveModality() {\n  return activeModality;\n}\nexport function getModality() {\n  return modality;\n}\nexport function addModalityListener(listener) {\n  listeners.add(listener);\n  return () => {\n    listeners.delete(listener);\n  };\n}\nexport function testOnly_resetActiveModality() {\n  isEmulatingMouseEvents = false;\n  activeModality = KEYBOARD;\n  modality = KEYBOARD;\n}"], "mappings": "AASA,SAASA,gBAAgB;AACzB,OAAOC,SAAS;AAChB,IAAIC,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAA;EAAA,OAAS,CAAC,EAAE,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,YAAY,IAAI,IAAI,CAAC;AAAA;AACjG,IAAIC,cAAc,GAAG,UAAU;AAC/B,IAAIC,QAAQ,GAAG,UAAU;AACzB,IAAIC,gBAAgB;AACpB,IAAIC,sBAAsB;AAC1B,IAAIC,sBAAsB,GAAG,KAAK;AAClC,IAAIC,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;AACzB,IAAIC,QAAQ,GAAG,UAAU;AACzB,IAAIC,KAAK,GAAG,OAAO;AACnB,IAAIC,KAAK,GAAG,OAAO;AACnB,IAAIC,IAAI,GAAG,MAAM;AACjB,IAAIC,WAAW,GAAG,aAAa;AAC/B,IAAIC,KAAK,GAAG,OAAO;AACnB,IAAIC,OAAO,GAAG,SAAS;AACvB,IAAIC,SAAS,GAAG,WAAW;AAC3B,IAAIC,SAAS,GAAG,WAAW;AAC3B,IAAIC,OAAO,GAAG,SAAS;AACvB,IAAIC,WAAW,GAAG,aAAa;AAC/B,IAAIC,WAAW,GAAG,aAAa;AAC/B,IAAIC,MAAM,GAAG,QAAQ;AACrB,IAAIC,eAAe,GAAG,iBAAiB;AACvC,IAAIC,WAAW,GAAG,aAAa;AAC/B,IAAIC,SAAS,GAAG,WAAW;AAC3B,IAAIC,UAAU,GAAG,YAAY;AAC7B,IAAIC,gBAAgB,GAAG,kBAAkB;AACzC,IAAIC,aAAa,GAAG;EAClBC,OAAO,EAAE;AACX,CAAC;AACD,IAAIC,cAAc,GAAG;EACnBC,OAAO,EAAE,IAAI;EACbF,OAAO,EAAE;AACX,CAAC;AACD,SAASG,eAAeA,CAAA,EAAG;EACzB,IAAI3B,gBAAgB,IAAI,IAAI,IAAIC,sBAAsB,IAAI,IAAI,EAAE;IAC9D,IAAID,gBAAgB,IAAI,IAAI,EAAE;MAC5BD,QAAQ,GAAGC,gBAAgB;MAC3BA,gBAAgB,GAAG,IAAI;IACzB;IACA,IAAIC,sBAAsB,IAAI,IAAI,EAAE;MAClCH,cAAc,GAAGG,sBAAsB;MACvCA,sBAAsB,GAAG,IAAI;IAC/B;IACA2B,aAAa,CAAC,CAAC;EACjB;AACF;AACA,SAASC,YAAYA,CAAA,EAAG;EACtB7B,gBAAgB,GAAGD,QAAQ;EAC3BE,sBAAsB,GAAGH,cAAc;EACvCA,cAAc,GAAGO,QAAQ;EACzBN,QAAQ,GAAGM,QAAQ;EACnBuB,aAAa,CAAC,CAAC;EAEf1B,sBAAsB,GAAG,KAAK;AAChC;AACA,SAAS4B,aAAaA,CAAA,EAAG;EACvBH,eAAe,CAAC,CAAC;AACnB;AACA,SAASI,SAASA,CAACC,KAAK,EAAE;EACxB,IAAIA,KAAK,CAACC,OAAO,IAAID,KAAK,CAACE,MAAM,IAAIF,KAAK,CAACG,OAAO,EAAE;IAClD;EACF;EACA,IAAIpC,QAAQ,KAAKM,QAAQ,EAAE;IACzBN,QAAQ,GAAGM,QAAQ;IACnBP,cAAc,GAAGO,QAAQ;IACzBuB,aAAa,CAAC,CAAC;EACjB;AACF;AACA,SAASQ,kBAAkBA,CAAA,EAAG;EAC5B,IAAIC,QAAQ,CAACC,eAAe,KAAK,QAAQ,EAAE;IACzCX,eAAe,CAAC,CAAC;EACnB;AACF;AACA,SAASY,YAAYA,CAACP,KAAK,EAAE;EAC3B,IAAIQ,SAAS,GAAGR,KAAK,CAACS,IAAI;EAC1B,IAAI9C,oBAAoB,CAAC,CAAC,EAAE;IAC1B,IAAI6C,SAAS,KAAKzB,WAAW,EAAE;MAC7B,IAAIjB,cAAc,KAAKkC,KAAK,CAACU,WAAW,EAAE;QACxC3C,QAAQ,GAAGiC,KAAK,CAACU,WAAW;QAC5B5C,cAAc,GAAGkC,KAAK,CAACU,WAAW;QAClCd,aAAa,CAAC,CAAC;MACjB;MACA;IACF;IACA,IAAIY,SAAS,KAAKxB,WAAW,EAAE;MAC7B,IAAIjB,QAAQ,KAAKiC,KAAK,CAACU,WAAW,EAAE;QAClC3C,QAAQ,GAAGiC,KAAK,CAACU,WAAW;QAC5Bd,aAAa,CAAC,CAAC;MACjB;MACA;IACF;EACF,CAAC,MAEI;IACH,IAAI,CAAC1B,sBAAsB,EAAE;MAC3B,IAAIsC,SAAS,KAAK5B,SAAS,EAAE;QAC3B,IAAId,cAAc,KAAKQ,KAAK,EAAE;UAC5BP,QAAQ,GAAGO,KAAK;UAChBR,cAAc,GAAGQ,KAAK;UACtBsB,aAAa,CAAC,CAAC;QACjB;MACF;MACA,IAAIY,SAAS,KAAK3B,SAAS,EAAE;QAC3B,IAAId,QAAQ,KAAKO,KAAK,EAAE;UACtBP,QAAQ,GAAGO,KAAK;UAChBsB,aAAa,CAAC,CAAC;QACjB;MACF;IACF;IAGA,IAAIY,SAAS,KAAKnB,UAAU,EAAE;MAC5BnB,sBAAsB,GAAG,IAAI;MAC7B,IAAI8B,KAAK,CAACW,OAAO,IAAIX,KAAK,CAACW,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;QAC7C1C,sBAAsB,GAAG,KAAK;MAChC;MACA,IAAIJ,cAAc,KAAKS,KAAK,EAAE;QAC5BR,QAAQ,GAAGQ,KAAK;QAChBT,cAAc,GAAGS,KAAK;QACtBqB,aAAa,CAAC,CAAC;MACjB;MACA;IACF;IAIA,IAAIY,SAAS,KAAK/B,WAAW,IAAI+B,SAAS,KAAK1B,OAAO,IAAI0B,SAAS,KAAKtB,eAAe,IAAIsB,SAAS,KAAKvB,MAAM,IAAIuB,SAAS,KAAKrB,WAAW,IAAIqB,SAAS,KAAKpB,SAAS,EAAE;MACvKlB,sBAAsB,GAAG,KAAK;IAChC;EACF;AACF;AACA,IAAIR,SAAS,EAAE;EAEbD,gBAAgB,CAACG,MAAM,EAAEY,IAAI,EAAEqB,YAAY,EAAEN,aAAa,CAAC;EAC3D9B,gBAAgB,CAACG,MAAM,EAAEc,KAAK,EAAEoB,aAAa,EAAEP,aAAa,CAAC;EAG7D9B,gBAAgB,CAAC4C,QAAQ,EAAE1B,OAAO,EAAEoB,SAAS,EAAEN,cAAc,CAAC;EAC9DhC,gBAAgB,CAAC4C,QAAQ,EAAEf,gBAAgB,EAAEc,kBAAkB,EAAEX,cAAc,CAAC;EAChFhC,gBAAgB,CAAC4C,QAAQ,EAAEtB,WAAW,EAAEwB,YAAY,EAAEd,cAAc,CAAC;EACrEhC,gBAAgB,CAAC4C,QAAQ,EAAErB,WAAW,EAAEuB,YAAY,EAAEd,cAAc,CAAC;EAErEhC,gBAAgB,CAAC4C,QAAQ,EAAE5B,WAAW,EAAE8B,YAAY,EAAEd,cAAc,CAAC;EACrEhC,gBAAgB,CAAC4C,QAAQ,EAAEzB,SAAS,EAAE2B,YAAY,EAAEd,cAAc,CAAC;EACnEhC,gBAAgB,CAAC4C,QAAQ,EAAExB,SAAS,EAAE0B,YAAY,EAAEd,cAAc,CAAC;EACnEhC,gBAAgB,CAAC4C,QAAQ,EAAEvB,OAAO,EAAEyB,YAAY,EAAEd,cAAc,CAAC;EACjEhC,gBAAgB,CAAC4C,QAAQ,EAAElB,WAAW,EAAEoB,YAAY,EAAEd,cAAc,CAAC;EACrEhC,gBAAgB,CAAC4C,QAAQ,EAAEjB,SAAS,EAAEmB,YAAY,EAAEd,cAAc,CAAC;EACnEhC,gBAAgB,CAAC4C,QAAQ,EAAEhB,UAAU,EAAEkB,YAAY,EAAEd,cAAc,CAAC;EACpEhC,gBAAgB,CAAC4C,QAAQ,EAAEnB,eAAe,EAAEqB,YAAY,EAAEd,cAAc,CAAC;EACzEhC,gBAAgB,CAAC4C,QAAQ,EAAEpB,MAAM,EAAEsB,YAAY,EAAEd,cAAc,CAAC;AAClE;AACA,SAASG,aAAaA,CAAA,EAAG;EACvB,IAAIiB,KAAK,GAAG;IACV/C,cAAc,EAAdA,cAAc;IACdC,QAAQ,EAARA;EACF,CAAC;EACDI,SAAS,CAAC2C,OAAO,CAAC,UAAAC,QAAQ,EAAI;IAC5BA,QAAQ,CAACF,KAAK,CAAC;EACjB,CAAC,CAAC;AACJ;AACA,OAAO,SAASG,iBAAiBA,CAAA,EAAG;EAClC,OAAOlD,cAAc;AACvB;AACA,OAAO,SAASmD,WAAWA,CAAA,EAAG;EAC5B,OAAOlD,QAAQ;AACjB;AACA,OAAO,SAASmD,mBAAmBA,CAACH,QAAQ,EAAE;EAC5C5C,SAAS,CAACgD,GAAG,CAACJ,QAAQ,CAAC;EACvB,OAAO,YAAM;IACX5C,SAAS,CAACiD,MAAM,CAACL,QAAQ,CAAC;EAC5B,CAAC;AACH;AACA,OAAO,SAASM,4BAA4BA,CAAA,EAAG;EAC7CnD,sBAAsB,GAAG,KAAK;EAC9BJ,cAAc,GAAGO,QAAQ;EACzBN,QAAQ,GAAGM,QAAQ;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}