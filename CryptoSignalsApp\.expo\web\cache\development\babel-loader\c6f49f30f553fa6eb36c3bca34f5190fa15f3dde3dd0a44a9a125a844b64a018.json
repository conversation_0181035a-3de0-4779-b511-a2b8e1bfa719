{"ast": null, "code": "import React from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport Svg, { Path } from \"react-native-svg\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default function IconLocked() {\n  return _jsx(View, {\n    children: _jsx(Svg, {\n      width: 22,\n      height: 22,\n      viewBox: \"0 0 22 22\",\n      children: _jsx(Path, {\n        d: \"M18.5715 9.71751H16.7501V3.71751C16.7501 1.82376 15.2153 0.28894 13.3215 0.28894H6.67868C4.78493 0.28894 3.2501 1.82376 3.2501 3.71751V9.71751H1.42868C0.954569 9.71751 0.571533 10.1005 0.571533 10.5747V20.8604C0.571533 21.3345 0.954569 21.7175 1.42868 21.7175H18.5715C19.0456 21.7175 19.4287 21.3345 19.4287 20.8604V10.5747C19.4287 10.1005 19.0456 9.71751 18.5715 9.71751ZM5.17868 3.71751C5.17868 2.88983 5.851 2.21751 6.67868 2.21751H13.3215C14.1492 2.21751 14.8215 2.88983 14.8215 3.71751V9.71751H5.17868V3.71751ZM17.5001 19.7889H2.5001V11.6461H17.5001V19.7889ZM9.2501 16.0657V17.4854C9.2501 17.6032 9.34653 17.6997 9.46439 17.6997H10.5358C10.6537 17.6997 10.7501 17.6032 10.7501 17.4854V16.0657C10.9712 15.907 11.1362 15.6822 11.2215 15.4237C11.3067 15.1651 11.3076 14.8863 11.2243 14.6272C11.1409 14.3681 10.9774 14.1421 10.7575 13.9818C10.5375 13.8215 10.2723 13.7351 10.0001 13.7351C9.72791 13.7351 9.46274 13.8215 9.24276 13.9818C9.02277 14.1421 8.85933 14.3681 8.77595 14.6272C8.69256 14.8863 8.69355 15.1651 8.77876 15.4237C8.86396 15.6822 9.029 15.907 9.2501 16.0657Z\",\n        fill: \"#FECB37\"\n      })\n    })\n  });\n}", "map": {"version": 3, "names": ["React", "View", "Svg", "Path", "jsx", "_jsx", "IconLocked", "children", "width", "height", "viewBox", "d", "fill"], "sources": ["E:/CryptoSignalsApp/src/components/Icon/Locked/index.js"], "sourcesContent": ["import React from 'react';\r\nimport { View } from 'react-native';\r\nimport Svg, { Path } from \"react-native-svg\"\r\n\r\nexport default function IconLocked() {\r\n  return (\r\n    <View>\r\n      <Svg\r\n        width={22}\r\n        height={22}\r\n        viewBox=\"0 0 22 22\"\r\n      >\r\n        <Path d=\"M18.5715 9.71751H16.7501V3.71751C16.7501 1.82376 15.2153 0.28894 13.3215 0.28894H6.67868C4.78493 0.28894 3.2501 1.82376 3.2501 3.71751V9.71751H1.42868C0.954569 9.71751 0.571533 10.1005 0.571533 10.5747V20.8604C0.571533 21.3345 0.954569 21.7175 1.42868 21.7175H18.5715C19.0456 21.7175 19.4287 21.3345 19.4287 20.8604V10.5747C19.4287 10.1005 19.0456 9.71751 18.5715 9.71751ZM5.17868 3.71751C5.17868 2.88983 5.851 2.21751 6.67868 2.21751H13.3215C14.1492 2.21751 14.8215 2.88983 14.8215 3.71751V9.71751H5.17868V3.71751ZM17.5001 19.7889H2.5001V11.6461H17.5001V19.7889ZM9.2501 16.0657V17.4854C9.2501 17.6032 9.34653 17.6997 9.46439 17.6997H10.5358C10.6537 17.6997 10.7501 17.6032 10.7501 17.4854V16.0657C10.9712 15.907 11.1362 15.6822 11.2215 15.4237C11.3067 15.1651 11.3076 14.8863 11.2243 14.6272C11.1409 14.3681 10.9774 14.1421 10.7575 13.9818C10.5375 13.8215 10.2723 13.7351 10.0001 13.7351C9.72791 13.7351 9.46274 13.8215 9.24276 13.9818C9.02277 14.1421 8.85933 14.3681 8.77595 14.6272C8.69256 14.8863 8.69355 15.1651 8.77876 15.4237C8.86396 15.6822 9.029 15.907 9.2501 16.0657Z\" fill=\"#FECB37\" />\r\n      </Svg>\r\n    </View>\r\n  );\r\n}\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,OAAAC,IAAA;AAE1B,OAAOC,GAAG,IAAIC,IAAI,QAAQ,kBAAkB;AAAA,SAAAC,GAAA,IAAAC,IAAA;AAE5C,eAAe,SAASC,UAAUA,CAAA,EAAG;EACnC,OACED,IAAA,CAACJ,IAAI;IAAAM,QAAA,EACHF,IAAA,CAACH,GAAG;MACFM,KAAK,EAAE,EAAG;MACVC,MAAM,EAAE,EAAG;MACXC,OAAO,EAAC,WAAW;MAAAH,QAAA,EAEnBF,IAAA,CAACF,IAAI;QAACQ,CAAC,EAAC,ujCAAujC;QAACC,IAAI,EAAC;MAAS,CAAE;IAAC,CAC9kC;EAAC,CACF,CAAC;AAEX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}