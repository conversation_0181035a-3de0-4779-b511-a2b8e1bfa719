#!/usr/bin/env python3
"""
Script de Instalação do Dashboard CryptoSignals
Instala todas as dependências necessárias para o dashboard web
"""

import subprocess
import sys
import os

def run_command(command, description):
    """Executa um comando e mostra o resultado"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} - Concluído")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} - Erro: {e}")
        print(f"   Saída: {e.stdout}")
        print(f"   Erro: {e.stderr}")
        return False

def check_python_version():
    """Verifica se a versão do Python é compatível"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8+ é necessário")
        print(f"   Versão atual: {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} - Compatível")
    return True

def install_dashboard_dependencies():
    """Instala as dependências do dashboard"""
    print("🚀 Instalando Dashboard CryptoSignals")
    print("=" * 50)
    
    # Verificar versão do Python
    if not check_python_version():
        return False
    
    # Atualizar pip
    if not run_command(f"{sys.executable} -m pip install --upgrade pip", "Atualizando pip"):
        print("⚠️ Aviso: Falha ao atualizar pip, continuando...")
    
    # Instalar dependências essenciais do dashboard
    essential_packages = [
        "Flask>=2.3.0",
        "Flask-SocketIO>=5.3.0", 
        "eventlet>=0.33.0",
        "paramiko>=3.0.0",
        "psutil>=5.9.0",
        "requests>=2.28.0",
        "python-dotenv>=1.0.0",
        "colorama>=0.4.6",
        "cryptography>=41.0.0"
    ]
    
    print("\n📦 Instalando dependências essenciais...")
    for package in essential_packages:
        if not run_command(f"{sys.executable} -m pip install '{package}'", f"Instalando {package.split('>=')[0]}"):
            print(f"❌ Falha ao instalar {package}")
            return False
    
    # Instalar dependências opcionais
    optional_packages = [
        "rich>=13.0.0",
        "python-dateutil>=2.8.0"
    ]
    
    print("\n📦 Instalando dependências opcionais...")
    for package in optional_packages:
        run_command(f"{sys.executable} -m pip install '{package}'", f"Instalando {package.split('>=')[0]} (opcional)")
    
    return True

def create_startup_script():
    """Cria script de inicialização do dashboard"""
    startup_content = '''#!/usr/bin/env python3
"""
Script de Inicialização do Dashboard CryptoSignals
"""

import sys
import os

# Adicionar diretório atual ao path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    print("🚀 Iniciando Dashboard CryptoSignals...")
    
    # Tentar importar e executar o dashboard
    try:
        # Primeiro tentar o dashboard completo
        import web_dashboard
        web_dashboard.main()
    except ImportError as e:
        print(f"⚠️ Dashboard completo não disponível: {e}")
        try:
            # Fallback para dashboard simples
            import simple_dashboard
            simple_dashboard.main()
        except ImportError:
            try:
                # Fallback para dashboard mínimo
                import minimal_dashboard
                minimal_dashboard.app.run(host='127.0.0.1', port=5000, debug=False)
            except ImportError:
                print("❌ Nenhum dashboard disponível")
                print("   Execute: python web_dashboard.py")
                return False
    
    return True

if __name__ == '__main__':
    main()
'''
    
    try:
        with open('start_dashboard.py', 'w', encoding='utf-8') as f:
            f.write(startup_content)
        print("✅ Script de inicialização criado: start_dashboard.py")
        return True
    except Exception as e:
        print(f"❌ Erro ao criar script de inicialização: {e}")
        return False

def main():
    """Função principal"""
    print("🎨 Dashboard CryptoSignals - Instalador")
    print("   Paleta de cores dourada/preta aplicada")
    print("=" * 50)
    
    # Instalar dependências
    if not install_dashboard_dependencies():
        print("\n❌ Falha na instalação das dependências")
        return False
    
    # Criar script de inicialização
    create_startup_script()
    
    print("\n" + "=" * 50)
    print("✅ Instalação concluída com sucesso!")
    print("\n📊 Para iniciar o dashboard:")
    print("   python start_dashboard.py")
    print("   ou")
    print("   python minimal_dashboard.py")
    print("\n🌐 Acesse: http://localhost:5000")
    print("=" * 50)
    
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
