2025-05-19 19:56:26,603 - __main__ - INFO - Iniciando agendador de atualizações de resultados
2025-05-19 19:56:26,604 - __main__ - INFO - Executando: C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe update_results.py
2025-05-19 19:56:31,413 - __main__ - INFO - Script executado com sucesso
2025-05-19 19:56:31,413 - __main__ - INFO - Saída: 2025-05-19 19:56:29,250 - utils.database - INFO - Banco de dados inicializado com sucesso
2025-05-19 19:56:29,595 - utils.telegram_sender - INFO - Usando nÃºmero de telefone: +5521982301476
2025-05-19 19:56:29,595 - telethon.network.mtprotosender - INFO - Connecting to **************:443/TcpFull...
2025-05-19 19:56:29,712 - telethon.network.mtprotosender - INFO - Connection to **************:443/TcpFull complete!
2025-05-19 19:56:30,717 - utils.telegram_sender - INFO - Cliente Telegram conectado com sucesso
2025-05-19 19:56:30,827 - utils.telegram_sender - INFO - Entidade do grupo obtida com sucesso
2025-05-19 19:56:30,827 - __main__ - INFO - Verificando 0 sinais abertos
2025-05-19 19:56:30,827 - telethon.network.mtprotosender - INFO - Disconnecting from **************:443/TcpFull...
2025-05-19 19:56:30,827 - telethon.network.mtprotosender - INFO - Disconnection from **************:443/TcpFull complete!
2025-05-19 19:56:31,131 - utils.telegram_sender - INFO - Cliente Telegram desconectado
2025-05-19 19:56:31,131 - utils.database - INFO - ConexÃ£o com o banco de dados fechada
