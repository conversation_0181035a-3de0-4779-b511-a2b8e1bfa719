{"ast": null, "code": "import * as React from 'react';\nimport NavigationBuilderContext from \"./NavigationBuilderContext\";\nexport default function useFocusedListenersChildrenAdapter(_ref) {\n  var navigation = _ref.navigation,\n    focusedListeners = _ref.focusedListeners;\n  var _React$useContext = React.useContext(NavigationBuilderContext),\n    addListener = _React$useContext.addListener;\n  var listener = React.useCallback(function (callback) {\n    if (navigation.isFocused()) {\n      for (var _listener of focusedListeners) {\n        var _listener2 = _listener(callback),\n          handled = _listener2.handled,\n          result = _listener2.result;\n        if (handled) {\n          return {\n            handled: handled,\n            result: result\n          };\n        }\n      }\n      return {\n        handled: true,\n        result: callback(navigation)\n      };\n    } else {\n      return {\n        handled: false,\n        result: null\n      };\n    }\n  }, [focusedListeners, navigation]);\n  React.useEffect(function () {\n    return addListener === null || addListener === void 0 ? void 0 : addListener('focus', listener);\n  }, [addListener, listener]);\n}", "map": {"version": 3, "names": ["React", "NavigationBuilderContext", "useFocusedListenersChildrenAdapter", "_ref", "navigation", "focusedListeners", "_React$useContext", "useContext", "addListener", "listener", "useCallback", "callback", "isFocused", "_listener2", "handled", "result", "useEffect"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\@react-navigation\\core\\src\\useFocusedListenersChildrenAdapter.tsx"], "sourcesContent": ["import type { ParamListBase } from '@react-navigation/routers';\nimport * as React from 'react';\n\nimport NavigationBuilderContext, {\n  FocusedNavigationCallback,\n  FocusedNavigationListener,\n} from './NavigationBuilderContext';\nimport type { NavigationHelpers } from './types';\n\ntype Options = {\n  navigation: NavigationHelpers<ParamListBase>;\n  focusedListeners: FocusedNavigationListener[];\n};\n\n/**\n * Hook for passing focus callback to children\n */\nexport default function useFocusedListenersChildrenAdapter({\n  navigation,\n  focusedListeners,\n}: Options) {\n  const { addListener } = React.useContext(NavigationBuilderContext);\n\n  const listener = React.useCallback(\n    (callback: FocusedNavigationCallback<any>) => {\n      if (navigation.isFocused()) {\n        for (const listener of focusedListeners) {\n          const { handled, result } = listener(callback);\n\n          if (handled) {\n            return { handled, result };\n          }\n        }\n\n        return { handled: true, result: callback(navigation) };\n      } else {\n        return { handled: false, result: null };\n      }\n    },\n    [focusedListeners, navigation]\n  );\n\n  React.useEffect(\n    () => addListener?.('focus', listener),\n    [addListener, listener]\n  );\n}\n"], "mappings": "AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,OAAOC,wBAAwB;AAc/B,eAAe,SAASC,kCAAkCA,CAAAC,IAAA,EAG9C;EAAA,IAFVC,UAAU,GAEFD,IAAA,CAFRC,UAAU;IACVC,gBAAA,GACQF,IAAA,CADRE,gBAAA;EAEA,IAAAC,iBAAA,GAAwBN,KAAK,CAACO,UAAU,CAACN,wBAAwB,CAAC;IAA1DO,WAAA,GAAAF,iBAAA,CAAAE,WAAA;EAER,IAAMC,QAAQ,GAAGT,KAAK,CAACU,WAAW,CAC/B,UAAAC,QAAwC,EAAK;IAC5C,IAAIP,UAAU,CAACQ,SAAS,EAAE,EAAE;MAC1B,KAAK,IAAMH,SAAQ,IAAIJ,gBAAgB,EAAE;QACvC,IAAAQ,UAAA,GAA4BJ,SAAQ,CAACE,QAAQ,CAAC;UAAtCG,OAAO,GAAAD,UAAA,CAAPC,OAAO;UAAEC,MAAA,GAAAF,UAAA,CAAAE,MAAA;QAEjB,IAAID,OAAO,EAAE;UACX,OAAO;YAAEA,OAAO,EAAPA,OAAO;YAAEC,MAAA,EAAAA;UAAO,CAAC;QAC5B;MACF;MAEA,OAAO;QAAED,OAAO,EAAE,IAAI;QAAEC,MAAM,EAAEJ,QAAQ,CAACP,UAAU;MAAE,CAAC;IACxD,CAAC,MAAM;MACL,OAAO;QAAEU,OAAO,EAAE,KAAK;QAAEC,MAAM,EAAE;MAAK,CAAC;IACzC;EACF,CAAC,EACD,CAACV,gBAAgB,EAAED,UAAU,CAAC,CAC/B;EAEDJ,KAAK,CAACgB,SAAS,CACb;IAAA,OAAMR,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAG,OAAO,EAAEC,QAAQ,CAAC;EAAA,GACtC,CAACD,WAAW,EAAEC,QAAQ,CAAC,CACxB;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}