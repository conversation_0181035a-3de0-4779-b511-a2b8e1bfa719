{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nfunction setSecureStoreItem(_x, _x2) {\n  return _setSecureStoreItem.apply(this, arguments);\n}\nfunction _setSecureStoreItem() {\n  _setSecureStoreItem = _asyncToGenerator(function* (key, value) {\n    try {\n      localStorage.setItem(key, JSON.stringify(value));\n    } catch (e) {\n      console.error('SET SECURE STORE ITEM', e);\n      throw e;\n    }\n  });\n  return _setSecureStoreItem.apply(this, arguments);\n}\nfunction getSecureStoreItem(_x3) {\n  return _getSecureStoreItem.apply(this, arguments);\n}\nfunction _getSecureStoreItem() {\n  _getSecureStoreItem = _asyncToGenerator(function* (key) {\n    try {\n      var item = localStorage.getItem(key);\n      return item ? JSON.parse(item) : null;\n    } catch (e) {\n      console.error('GET SECURE STORE ITEM', e);\n      throw e;\n    }\n  });\n  return _getSecureStoreItem.apply(this, arguments);\n}\nfunction removeSecureStoreItem(_x4) {\n  return _removeSecureStoreItem.apply(this, arguments);\n}\nfunction _removeSecureStoreItem() {\n  _removeSecureStoreItem = _asyncToGenerator(function* (key) {\n    try {\n      localStorage.removeItem(key);\n    } catch (e) {\n      console.error('REMOVE SECURE STORE ITEM', e);\n      throw e;\n    }\n  });\n  return _removeSecureStoreItem.apply(this, arguments);\n}\nexport { setSecureStoreItem, getSecureStoreItem, removeSecureStoreItem };", "map": {"version": 3, "names": ["setSecureStoreItem", "_x", "_x2", "_setSecureStoreItem", "apply", "arguments", "_asyncToGenerator", "key", "value", "localStorage", "setItem", "JSON", "stringify", "e", "console", "error", "getSecureStoreItem", "_x3", "_getSecureStoreItem", "item", "getItem", "parse", "removeSecureStoreItem", "_x4", "_removeSecureStoreItem", "removeItem"], "sources": ["E:/CryptoSignalsApp/src/services/secureStore.js"], "sourcesContent": ["// Mock SecureStore for web compatibility\r\n\r\n/**\r\n * Armazena um valor associado a uma chave no armazenamento seguro.\r\n * @param {string} key - A chave sob a qual o valor será armazenado.\r\n * @param {any} value - O valor a ser armazenado.\r\n * @returns {Promise<void>}\r\n */\r\nasync function setSecureStoreItem(key, value) {\r\n  try {\r\n    localStorage.setItem(key, JSON.stringify(value));\r\n  } catch (e) {\r\n    console.error('SET SECURE STORE ITEM', e);\r\n    throw e;\r\n  }\r\n}\r\n\r\n/**\r\n * Recupera um valor associado a uma chave do armazenamento seguro.\r\n * @param {string} key - A chave do valor a ser recuperado.\r\n * @returns {Promise<any | null>} - O valor recuperado ou null se não existir.\r\n */\r\nasync function getSecureStoreItem(key) {\r\n  try {\r\n    const item = localStorage.getItem(key);\r\n    return item ? JSON.parse(item) : null;\r\n  } catch (e) {\r\n    console.error('GET SECURE STORE ITEM', e);\r\n    throw e;\r\n  }\r\n}\r\n\r\n/**\r\n * Remove um valor associado a uma chave do armazenamento seguro.\r\n * @param {string} key - A chave do valor a ser removido.\r\n * @returns {Promise<void>}\r\n */\r\nasync function removeSecureStoreItem(key) {\r\n  try {\r\n    localStorage.removeItem(key);\r\n  } catch (e) {\r\n    console.error('REMOVE SECURE STORE ITEM', e);\r\n    throw e;\r\n  }\r\n}\r\n\r\nexport { setSecureStoreItem, getSecureStoreItem, removeSecureStoreItem };\r\n"], "mappings": ";SAQeA,kBAAkBA,CAAAC,EAAA,EAAAC,GAAA;EAAA,OAAAC,mBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,oBAAA;EAAAA,mBAAA,GAAAG,iBAAA,CAAjC,WAAkCC,GAAG,EAAEC,KAAK,EAAE;IAC5C,IAAI;MACFC,YAAY,CAACC,OAAO,CAACH,GAAG,EAAEI,IAAI,CAACC,SAAS,CAACJ,KAAK,CAAC,CAAC;IAClD,CAAC,CAAC,OAAOK,CAAC,EAAE;MACVC,OAAO,CAACC,KAAK,CAAC,uBAAuB,EAAEF,CAAC,CAAC;MACzC,MAAMA,CAAC;IACT;EACF,CAAC;EAAA,OAAAV,mBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAOcW,kBAAkBA,CAAAC,GAAA;EAAA,OAAAC,mBAAA,CAAAd,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAa,oBAAA;EAAAA,mBAAA,GAAAZ,iBAAA,CAAjC,WAAkCC,GAAG,EAAE;IACrC,IAAI;MACF,IAAMY,IAAI,GAAGV,YAAY,CAACW,OAAO,CAACb,GAAG,CAAC;MACtC,OAAOY,IAAI,GAAGR,IAAI,CAACU,KAAK,CAACF,IAAI,CAAC,GAAG,IAAI;IACvC,CAAC,CAAC,OAAON,CAAC,EAAE;MACVC,OAAO,CAACC,KAAK,CAAC,uBAAuB,EAAEF,CAAC,CAAC;MACzC,MAAMA,CAAC;IACT;EACF,CAAC;EAAA,OAAAK,mBAAA,CAAAd,KAAA,OAAAC,SAAA;AAAA;AAAA,SAOciB,qBAAqBA,CAAAC,GAAA;EAAA,OAAAC,sBAAA,CAAApB,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAmB,uBAAA;EAAAA,sBAAA,GAAAlB,iBAAA,CAApC,WAAqCC,GAAG,EAAE;IACxC,IAAI;MACFE,YAAY,CAACgB,UAAU,CAAClB,GAAG,CAAC;IAC9B,CAAC,CAAC,OAAOM,CAAC,EAAE;MACVC,OAAO,CAACC,KAAK,CAAC,0BAA0B,EAAEF,CAAC,CAAC;MAC5C,MAAMA,CAAC;IACT;EACF,CAAC;EAAA,OAAAW,sBAAA,CAAApB,KAAA,OAAAC,SAAA;AAAA;AAED,SAASL,kBAAkB,EAAEgB,kBAAkB,EAAEM,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}