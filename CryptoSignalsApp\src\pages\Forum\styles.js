import React from 'react';
import { View, Text, TouchableOpacity, TextInput, ScrollView, ToastAndroid } from 'react-native';
import { AntDesign } from '@expo/vector-icons';
import styles from './styles';

const ForumPage = () => {
  const handleAddPost = () => {
    // Lógica para adicionar um novo post no fórum
    // Você pode implementar esta funcionalidade conforme suas necessidades
    ToastAndroid.showWithGravity("Post adicionado com sucesso!", ToastAndroid.SHORT, ToastAndroid.BOTTOM);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.pageTitle}>Fórum</Text>

      <TextInput
        style={styles.input}
        placeholder="Digite sua postagem..."
        multiline={true}
      />

      <TouchableOpacity style={styles.addButton} onPress={handleAddPost}>
        <Text style={styles.addButtonText}>Adicionar Postagem</Text>
      </TouchableOpacity>

      <ScrollView style={styles.postsContainer}>
        {/* Exemplo de post */}
        <View style={styles.postContainer}>
          <Text style={styles.postText}>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nulla vehicula felis vel
            nunc bibendum, eu bibendum risus rhoncus. Integer eget lectus et augue ultrices
            tincidunt.
          </Text>
          <Text style={styles.postAuthor}>Autor: John Doe</Text>
        </View>

        {/* Outros posts podem ser renderizados aqui */}
      </ScrollView>

      {/* Botão de Voltar */}
      <TouchableOpacity style={styles.backButton} onPress={() => {}}>
        <AntDesign name="arrowleft" size={24} style={styles.backIcon} />
        <Text style={styles.backText}>Voltar</Text>
      </TouchableOpacity>
    </View>
  );
};

export default ForumPage;
