{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"alpha\", \"family\", \"style\", \"theme\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nimport * as React from 'react';\nimport I18nManager from \"react-native-web/dist/exports/I18nManager\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport color from 'color';\nimport Text from \"./Text\";\nimport { useInternalTheme } from \"../../../core/theming\";\nvar StyledText = function StyledText(_ref) {\n  var _ref$alpha = _ref.alpha,\n    alpha = _ref$alpha === void 0 ? 1 : _ref$alpha,\n    family = _ref.family,\n    style = _ref.style,\n    themeOverrides = _ref.theme,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var _theme$colors, _theme$fonts;\n  var theme = useInternalTheme(themeOverrides);\n  var textColor = color(theme.isV3 ? theme.colors.onSurface : (_theme$colors = theme.colors) === null || _theme$colors === void 0 ? void 0 : _theme$colors.text).alpha(alpha).rgb().string();\n  var writingDirection = I18nManager.getConstants().isRTL ? 'rtl' : 'ltr';\n  return React.createElement(Text, _extends({}, rest, {\n    style: [styles.text, _objectSpread(_objectSpread({\n      color: textColor\n    }, !theme.isV3 && ((_theme$fonts = theme.fonts) === null || _theme$fonts === void 0 ? void 0 : _theme$fonts[family])), {}, {\n      writingDirection: writingDirection\n    }), style]\n  }));\n};\nvar styles = StyleSheet.create({\n  text: {\n    textAlign: 'left'\n  }\n});\nexport default StyledText;", "map": {"version": 3, "names": ["React", "I18nManager", "StyleSheet", "color", "Text", "useInternalTheme", "StyledText", "_ref", "_ref$alpha", "alpha", "family", "style", "themeOverrides", "theme", "rest", "_objectWithoutProperties", "_excluded", "_theme$colors", "_theme$fonts", "textColor", "isV3", "colors", "onSurface", "text", "rgb", "string", "writingDirection", "getConstants", "isRTL", "createElement", "_extends", "styles", "_objectSpread", "fonts", "create", "textAlign"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\Typography\\v2\\StyledText.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { I18nManager, StyleProp, StyleSheet, TextStyle } from 'react-native';\n\nimport color from 'color';\nimport type { ThemeProp } from 'src/types';\n\nimport Text from './Text';\nimport { useInternalTheme } from '../../../core/theming';\n\ntype Props = React.ComponentProps<typeof Text> & {\n  alpha?: number;\n  family: 'regular' | 'medium' | 'light' | 'thin';\n  style?: StyleProp<TextStyle>;\n  theme?: ThemeProp;\n};\n\nconst StyledText = ({\n  alpha = 1,\n  family,\n  style,\n  theme: themeOverrides,\n  ...rest\n}: Props) => {\n  const theme = useInternalTheme(themeOverrides);\n\n  const textColor = color(\n    theme.isV3 ? theme.colors.onSurface : theme.colors?.text\n  )\n    .alpha(alpha)\n    .rgb()\n    .string();\n  const writingDirection = I18nManager.getConstants().isRTL ? 'rtl' : 'ltr';\n\n  return (\n    <Text\n      {...rest}\n      style={[\n        styles.text,\n        {\n          color: textColor,\n          ...(!theme.isV3 && theme.fonts?.[family]),\n          writingDirection,\n        },\n        style,\n      ]}\n    />\n  );\n};\n\nconst styles = StyleSheet.create({\n  text: {\n    textAlign: 'left',\n  },\n});\n\nexport default StyledText;\n"], "mappings": ";;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,WAAA;AAAA,OAAAC,UAAA;AAG9B,OAAOC,KAAK,MAAM,OAAO;AAGzB,OAAOC,IAAI;AACX,SAASC,gBAAgB;AASzB,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAAC,IAAA,EAMH;EAAA,IAAAC,UAAA,GAAAD,IAAA,CALXE,KAAK;IAALA,KAAK,GAAAD,UAAA,cAAG,CAAC,GAAAA,UAAA;IACTE,MAAM,GAAAH,IAAA,CAANG,MAAM;IACNC,KAAK,GAAAJ,IAAA,CAALI,KAAK;IACEC,cAAc,GAAAL,IAAA,CAArBM,KAAK;IACFC,IAAA,GAAAC,wBAAA,CAAAR,IAAA,EAAAS,SAAA;EACQ,IAAAC,aAAA,EAAAC,YAAA;EACX,IAAML,KAAK,GAAGR,gBAAgB,CAACO,cAAc,CAAC;EAE9C,IAAMO,SAAS,GAAGhB,KAAK,CACrBU,KAAK,CAACO,IAAI,GAAGP,KAAK,CAACQ,MAAM,CAACC,SAAS,IAAAL,aAAA,GAAGJ,KAAK,CAACQ,MAAM,cAAAJ,aAAA,uBAAZA,aAAA,CAAcM,IACtD,CAAC,CACEd,KAAK,CAACA,KAAK,CAAC,CACZe,GAAG,CAAC,CAAC,CACLC,MAAM,CAAC,CAAC;EACX,IAAMC,gBAAgB,GAAGzB,WAAW,CAAC0B,YAAY,CAAC,CAAC,CAACC,KAAK,GAAG,KAAK,GAAG,KAAK;EAEzE,OACE5B,KAAA,CAAA6B,aAAA,CAACzB,IAAI,EAAA0B,QAAA,KACChB,IAAI;IACRH,KAAK,EAAE,CACLoB,MAAM,CAACR,IAAI,EAAAS,aAAA,CAAAA,aAAA;MAET7B,KAAK,EAAEgB;IAAS,GACZ,CAACN,KAAK,CAACO,IAAI,MAAAF,YAAA,GAAIL,KAAK,CAACoB,KAAK,cAAAf,YAAA,uBAAXA,YAAA,CAAcR,MAAM,CAAC;MACxCgB,gBAAA,EAAAA;IAAA,IAEFf,KAAK;EACL,EACH,CAAC;AAEN,CAAC;AAED,IAAMoB,MAAM,GAAG7B,UAAU,CAACgC,MAAM,CAAC;EAC/BX,IAAI,EAAE;IACJY,SAAS,EAAE;EACb;AACF,CAAC,CAAC;AAEF,eAAe7B,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}