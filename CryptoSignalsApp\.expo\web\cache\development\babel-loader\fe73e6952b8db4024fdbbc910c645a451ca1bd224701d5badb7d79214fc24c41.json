{"ast": null, "code": "import React from 'react';\nexport var GHContext = React.createContext(function (props) {\n  return React.createElement(React.Fragment, null, props.children);\n});\nexport var RNSScreensRefContext = React.createContext(null);", "map": {"version": 3, "names": ["React", "GHContext", "createContext", "props", "createElement", "Fragment", "children", "RNSScreensRefContext"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\react-native-screens\\src\\contexts.tsx"], "sourcesContent": ["import React, { PropsWithChildren } from 'react';\nimport { GestureProviderProps, ScreensRefsHolder } from './types';\n\nexport const GHContext = React.createContext(\n  (props: PropsWithChildren<GestureProviderProps>) => <>{props.children}</>,\n);\n\nexport const RNSScreensRefContext =\n  React.createContext<React.MutableRefObject<ScreensRefsHolder> | null>(null);\n"], "mappings": "AAAA,OAAOA,KAAK,MAA6B,OAAO;AAGhD,OAAO,IAAMC,SAAS,GAAGD,KAAK,CAACE,aAAa,CACzC,UAAAC,KAA8C;EAAA,OAAKH,KAAA,CAAAI,aAAA,CAAAJ,KAAA,CAAAK,QAAA,QAAGF,KAAK,CAACG,QAAW,CAC1E;AAAA,EAAC;AAED,OAAO,IAAMC,oBAAoB,GAC/BP,KAAK,CAACE,aAAa,CAAmD,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}