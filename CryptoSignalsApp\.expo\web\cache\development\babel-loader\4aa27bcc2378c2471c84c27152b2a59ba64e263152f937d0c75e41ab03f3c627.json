{"ast": null, "code": "import StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport Dimensions from \"react-native-web/dist/exports/Dimensions\";\nvar windowHeight = Dimensions.get('window').height;\nvar styles = StyleSheet.create({\n  scrollViewSignals: {\n    height: windowHeight - 136\n  },\n  searchContainer: {\n    marginHorizontal: 10,\n    marginBottom: 15\n  },\n  containerLoading: {\n    backgroundColor: 'rgba(0,0,0,.5)',\n    position: 'absolute',\n    left: 0,\n    right: 0,\n    top: 0,\n    bottom: 0,\n    alignItems: 'center',\n    justifyContent: 'center'\n  },\n  emptyState: {\n    color: '#fff',\n    fontSize: 16,\n    textAlign: 'center',\n    fontFamily: 'Poppins_400Regular',\n    marginTop: 18\n  }\n});\nexport default styles;", "map": {"version": 3, "names": ["windowHeight", "Dimensions", "get", "height", "styles", "StyleSheet", "create", "scrollViewSignals", "searchContainer", "marginHorizontal", "marginBottom", "containerLoading", "backgroundColor", "position", "left", "right", "top", "bottom", "alignItems", "justifyContent", "emptyState", "color", "fontSize", "textAlign", "fontFamily", "marginTop"], "sources": ["E:/CryptoSignalsApp/src/pages/Signals/styles.js"], "sourcesContent": ["import { StyleSheet, Dimensions } from 'react-native';\r\nconst windowHeight = Dimensions.get('window').height;\r\n\r\nconst styles = StyleSheet.create({\r\n  scrollViewSignals: {\r\n    height: windowHeight - 136\r\n  },\r\n  searchContainer: {\r\n    marginHorizontal: 10,\r\n    marginBottom: 15,\r\n  },\r\n  containerLoading: {\r\n    backgroundColor: 'rgba(0,0,0,.5)',\r\n    position: 'absolute',\r\n    left: 0,\r\n    right: 0,\r\n    top: 0,\r\n    bottom: 0,\r\n    alignItems: 'center',\r\n    justifyContent: 'center'\r\n  },\r\n  emptyState: {\r\n    color: '#fff',\r\n    fontSize: 16,\r\n    textAlign: 'center',\r\n    fontFamily: 'Poppins_400Regular',\r\n    marginTop: 18\r\n  }\r\n});\r\n\r\nexport default styles;\r\n"], "mappings": ";;AACA,IAAMA,YAAY,GAAGC,UAAU,CAACC,GAAG,CAAC,QAAQ,CAAC,CAACC,MAAM;AAEpD,IAAMC,MAAM,GAAGC,UAAU,CAACC,MAAM,CAAC;EAC/BC,iBAAiB,EAAE;IACjBJ,MAAM,EAAEH,YAAY,GAAG;EACzB,CAAC;EACDQ,eAAe,EAAE;IACfC,gBAAgB,EAAE,EAAE;IACpBC,YAAY,EAAE;EAChB,CAAC;EACDC,gBAAgB,EAAE;IAChBC,eAAe,EAAE,gBAAgB;IACjCC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,GAAG,EAAE,CAAC;IACNC,MAAM,EAAE,CAAC;IACTC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE;EAClB,CAAC;EACDC,UAAU,EAAE;IACVC,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,QAAQ;IACnBC,UAAU,EAAE,oBAAoB;IAChCC,SAAS,EAAE;EACb;AACF,CAAC,CAAC;AAEF,eAAerB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}