{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"style\", \"background\", \"borderless\", \"disabled\", \"rippleColor\", \"underlayColor\", \"children\", \"theme\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nimport * as React from 'react';\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport color from 'color';\nimport { Pressable } from \"./Pressable\";\nimport { getTouchableRippleColors } from \"./utils\";\nimport { SettingsContext } from \"../../core/settings\";\nimport { useInternalTheme } from \"../../core/theming\";\nimport { forwardRef } from \"../../utils/forwardRef\";\nimport hasTouchHandler from \"../../utils/hasTouchHandler\";\nvar TouchableRipple = function TouchableRipple(_ref, ref) {\n  var _style2 = _ref.style,\n    _background = _ref.background,\n    _ref$borderless = _ref.borderless,\n    borderless = _ref$borderless === void 0 ? false : _ref$borderless,\n    disabledProp = _ref.disabled,\n    rippleColor = _ref.rippleColor,\n    _underlayColor = _ref.underlayColor,\n    children = _ref.children,\n    themeOverrides = _ref.theme,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var theme = useInternalTheme(themeOverrides);\n  var _getTouchableRippleCo = getTouchableRippleColors({\n      theme: theme,\n      rippleColor: rippleColor\n    }),\n    calculatedRippleColor = _getTouchableRippleCo.calculatedRippleColor;\n  var hoverColor = color(calculatedRippleColor).fade(0.5).rgb().string();\n  var _React$useContext = React.useContext(SettingsContext),\n    rippleEffectEnabled = _React$useContext.rippleEffectEnabled;\n  var onPress = rest.onPress,\n    onLongPress = rest.onLongPress,\n    onPressIn = rest.onPressIn,\n    onPressOut = rest.onPressOut;\n  var handlePressIn = React.useCallback(function (e) {\n    onPressIn === null || onPressIn === void 0 || onPressIn(e);\n    if (rippleEffectEnabled) {\n      var _ref2;\n      var centered = rest.centered;\n      var button = e.currentTarget;\n      var _style = window.getComputedStyle(button);\n      var dimensions = button.getBoundingClientRect();\n      var touchX;\n      var touchY;\n      var _e$nativeEvent = e.nativeEvent,\n        changedTouches = _e$nativeEvent.changedTouches,\n        touches = _e$nativeEvent.touches;\n      var touch = (_ref2 = touches === null || touches === void 0 ? void 0 : touches[0]) != null ? _ref2 : changedTouches === null || changedTouches === void 0 ? void 0 : changedTouches[0];\n      if (centered || !touch) {\n        touchX = dimensions.width / 2;\n        touchY = dimensions.height / 2;\n      } else {\n        var _touch$locationX, _touch$locationY;\n        touchX = (_touch$locationX = touch.locationX) != null ? _touch$locationX : e.pageX;\n        touchY = (_touch$locationY = touch.locationY) != null ? _touch$locationY : e.pageY;\n      }\n      var size = centered ? Math.min(dimensions.width, dimensions.height) * 1.5 : Math.max(dimensions.width, dimensions.height) * 2;\n      var container = document.createElement('span');\n      container.setAttribute('data-paper-ripple', '');\n      Object.assign(container.style, {\n        position: 'absolute',\n        pointerEvents: 'none',\n        top: '0',\n        left: '0',\n        right: '0',\n        bottom: '0',\n        borderTopLeftRadius: _style.borderTopLeftRadius,\n        borderTopRightRadius: _style.borderTopRightRadius,\n        borderBottomRightRadius: _style.borderBottomRightRadius,\n        borderBottomLeftRadius: _style.borderBottomLeftRadius,\n        overflow: centered ? 'visible' : 'hidden'\n      });\n      var ripple = document.createElement('span');\n      Object.assign(ripple.style, {\n        position: 'absolute',\n        pointerEvents: 'none',\n        backgroundColor: calculatedRippleColor,\n        borderRadius: '50%',\n        transitionProperty: 'transform opacity',\n        transitionDuration: `${Math.min(size * 1.5, 350)}ms`,\n        transitionTimingFunction: 'linear',\n        transformOrigin: 'center',\n        transform: 'translate3d(-50%, -50%, 0) scale3d(0.1, 0.1, 0.1)',\n        opacity: '0.5',\n        left: `${touchX}px`,\n        top: `${touchY}px`,\n        width: `${size}px`,\n        height: `${size}px`\n      });\n      container.appendChild(ripple);\n      button.appendChild(container);\n      requestAnimationFrame(function () {\n        requestAnimationFrame(function () {\n          Object.assign(ripple.style, {\n            transform: 'translate3d(-50%, -50%, 0) scale3d(1, 1, 1)',\n            opacity: '1'\n          });\n        });\n      });\n    }\n  }, [onPressIn, rest, rippleEffectEnabled, calculatedRippleColor]);\n  var handlePressOut = React.useCallback(function (e) {\n    onPressOut === null || onPressOut === void 0 || onPressOut(e);\n    if (rippleEffectEnabled) {\n      var containers = e.currentTarget.querySelectorAll('[data-paper-ripple]');\n      requestAnimationFrame(function () {\n        requestAnimationFrame(function () {\n          containers.forEach(function (container) {\n            var ripple = container.firstChild;\n            Object.assign(ripple.style, {\n              transitionDuration: '250ms',\n              opacity: 0\n            });\n            setTimeout(function () {\n              var parentNode = container.parentNode;\n              if (parentNode) {\n                parentNode.removeChild(container);\n              }\n            }, 500);\n          });\n        });\n      });\n    }\n  }, [onPressOut, rippleEffectEnabled]);\n  var hasPassedTouchHandler = hasTouchHandler({\n    onPress: onPress,\n    onLongPress: onLongPress,\n    onPressIn: onPressIn,\n    onPressOut: onPressOut\n  });\n  var disabled = disabledProp || !hasPassedTouchHandler;\n  return React.createElement(Pressable, _extends({}, rest, {\n    ref: ref,\n    onPressIn: handlePressIn,\n    onPressOut: handlePressOut,\n    disabled: disabled,\n    style: function style(state) {\n      return [styles.touchable, borderless && styles.borderless, state.hovered && {\n        backgroundColor: hoverColor\n      }, disabled && styles.disabled, typeof _style2 === 'function' ? _style2(state) : _style2];\n    }\n  }), function (state) {\n    return React.Children.only(typeof children === 'function' ? children(state) : children);\n  });\n};\nTouchableRipple.supported = true;\nvar styles = StyleSheet.create({\n  touchable: _objectSpread({\n    position: 'relative'\n  }, Platform.OS === 'web' && {\n    cursor: 'pointer',\n    transition: '150ms background-color'\n  }),\n  disabled: _objectSpread({}, Platform.OS === 'web' && {\n    cursor: 'auto'\n  }),\n  borderless: {\n    overflow: 'hidden'\n  }\n});\nvar Component = forwardRef(TouchableRipple);\nexport default Component;", "map": {"version": 3, "names": ["React", "Platform", "StyleSheet", "color", "Pressable", "getTouchableRippleColors", "SettingsContext", "useInternalTheme", "forwardRef", "has<PERSON>ou<PERSON><PERSON><PERSON><PERSON>", "TouchableRipple", "_ref", "ref", "style", "_background", "background", "_ref$borderless", "borderless", "disabledProp", "disabled", "rippleColor", "_underlayColor", "underlayColor", "children", "themeOverrides", "theme", "rest", "_objectWithoutProperties", "_excluded", "_getTouchableRippleCo", "calculatedRippleColor", "hoverColor", "fade", "rgb", "string", "_React$useContext", "useContext", "rippleEffectEnabled", "onPress", "onLongPress", "onPressIn", "onPressOut", "handlePressIn", "useCallback", "e", "_ref2", "centered", "button", "currentTarget", "window", "getComputedStyle", "dimensions", "getBoundingClientRect", "touchX", "touchY", "_e$nativeEvent", "nativeEvent", "changedTouches", "touches", "touch", "width", "height", "_touch$locationX", "_touch$locationY", "locationX", "pageX", "locationY", "pageY", "size", "Math", "min", "max", "container", "document", "createElement", "setAttribute", "Object", "assign", "position", "pointerEvents", "top", "left", "right", "bottom", "borderTopLeftRadius", "borderTopRightRadius", "borderBottomRightRadius", "borderBottomLeftRadius", "overflow", "ripple", "backgroundColor", "borderRadius", "transitionProperty", "transitionDuration", "transitionTimingFunction", "transform<PERSON><PERSON>in", "transform", "opacity", "append<PERSON><PERSON><PERSON>", "requestAnimationFrame", "handlePressOut", "containers", "querySelectorAll", "for<PERSON>ach", "<PERSON><PERSON><PERSON><PERSON>", "setTimeout", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "has<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_extends", "state", "styles", "touchable", "hovered", "Children", "only", "supported", "create", "_objectSpread", "OS", "cursor", "transition", "Component"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\TouchableRipple\\TouchableRipple.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  ColorValue,\n  GestureResponderEvent,\n  Platform,\n  StyleProp,\n  StyleSheet,\n  View,\n  ViewStyle,\n} from 'react-native';\n\nimport color from 'color';\n\nimport type { PressableProps, PressableStateCallbackType } from './Pressable';\nimport { Pressable } from './Pressable';\nimport { getTouchableRippleColors } from './utils';\nimport { Settings, SettingsContext } from '../../core/settings';\nimport { useInternalTheme } from '../../core/theming';\nimport type { ThemeProp } from '../../types';\nimport { forwardRef } from '../../utils/forwardRef';\nimport hasTouchHandler from '../../utils/hasTouchHandler';\n\nexport type Props = PressableProps & {\n  /**\n   * Whether to render the ripple outside the view bounds.\n   */\n  borderless?: boolean;\n  /**\n   * Type of background drawabale to display the feedback (Android).\n   * https://reactnative.dev/docs/pressable#rippleconfig\n   */\n  background?: Object;\n  /**\n   * Whether to start the ripple at the center (Web).\n   */\n  centered?: boolean;\n  /**\n   * Whether to prevent interaction with the touchable.\n   */\n  disabled?: boolean;\n  /**\n   * Function to execute on press. If not set, will cause the touchable to be disabled.\n   */\n  onPress?: (e: GestureResponderEvent) => void;\n  /**\n   * Function to execute on long press.\n   */\n  onLongPress?: (e: GestureResponderEvent) => void;\n  /**\n   * Function to execute immediately when a touch is engaged, before `onPressOut` and `onPress`.\n   */\n  onPressIn?: (e: GestureResponderEvent) => void;\n  /**\n   * Function to execute when a touch is released.\n   */\n  onPressOut?: (e: GestureResponderEvent) => void;\n  /**\n   * Color of the ripple effect (Android >= 5.0 and Web).\n   */\n  rippleColor?: ColorValue;\n  /**\n   * Color of the underlay for the highlight effect (Android < 5.0 and iOS).\n   */\n  underlayColor?: string;\n  /**\n   * Content of the `TouchableRipple`.\n   */\n  children:\n    | ((state: PressableStateCallbackType) => React.ReactNode)\n    | React.ReactNode;\n  style?:\n    | StyleProp<ViewStyle>\n    | ((state: PressableStateCallbackType) => StyleProp<ViewStyle>)\n    | undefined;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n};\n\n/**\n * A wrapper for views that should respond to touches.\n * Provides a material \"ink ripple\" interaction effect for supported platforms (>= Android Lollipop).\n * On unsupported platforms, it falls back to a highlight effect.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { View } from 'react-native';\n * import { Text, TouchableRipple } from 'react-native-paper';\n *\n * const MyComponent = () => (\n *   <TouchableRipple\n *     onPress={() => console.log('Pressed')}\n *     rippleColor=\"rgba(0, 0, 0, .32)\"\n *   >\n *     <Text>Press anywhere</Text>\n *   </TouchableRipple>\n * );\n *\n * export default MyComponent;\n * ```\n *\n * @extends Pressable props https://reactnative.dev/docs/Pressable#props\n */\nconst TouchableRipple = (\n  {\n    style,\n    background: _background,\n    borderless = false,\n    disabled: disabledProp,\n    rippleColor,\n    underlayColor: _underlayColor,\n    children,\n    theme: themeOverrides,\n    ...rest\n  }: Props,\n  ref: React.ForwardedRef<View>\n) => {\n  const theme = useInternalTheme(themeOverrides);\n  const { calculatedRippleColor } = getTouchableRippleColors({\n    theme,\n    rippleColor,\n  });\n  const hoverColor = color(calculatedRippleColor).fade(0.5).rgb().string();\n  const { rippleEffectEnabled } = React.useContext<Settings>(SettingsContext);\n\n  const { onPress, onLongPress, onPressIn, onPressOut } = rest;\n\n  const handlePressIn = React.useCallback(\n    (e: any) => {\n      onPressIn?.(e);\n\n      if (rippleEffectEnabled) {\n        const { centered } = rest;\n\n        const button = e.currentTarget;\n        const style = window.getComputedStyle(button);\n        const dimensions = button.getBoundingClientRect();\n\n        let touchX;\n        let touchY;\n\n        const { changedTouches, touches } = e.nativeEvent;\n        const touch = touches?.[0] ?? changedTouches?.[0];\n\n        // If centered or it was pressed using keyboard - enter or space\n        if (centered || !touch) {\n          touchX = dimensions.width / 2;\n          touchY = dimensions.height / 2;\n        } else {\n          touchX = touch.locationX ?? e.pageX;\n          touchY = touch.locationY ?? e.pageY;\n        }\n\n        // Get the size of the button to determine how big the ripple should be\n        const size = centered\n          ? // If ripple is always centered, we don't need to make it too big\n            Math.min(dimensions.width, dimensions.height) * 1.5\n          : // Otherwise make it twice as big so clicking on one end spreads ripple to other\n            Math.max(dimensions.width, dimensions.height) * 2;\n\n        // Create a container for our ripple effect so we don't need to change the parent's style\n        const container = document.createElement('span');\n\n        container.setAttribute('data-paper-ripple', '');\n\n        Object.assign(container.style, {\n          position: 'absolute',\n          pointerEvents: 'none',\n          top: '0',\n          left: '0',\n          right: '0',\n          bottom: '0',\n          borderTopLeftRadius: style.borderTopLeftRadius,\n          borderTopRightRadius: style.borderTopRightRadius,\n          borderBottomRightRadius: style.borderBottomRightRadius,\n          borderBottomLeftRadius: style.borderBottomLeftRadius,\n          overflow: centered ? 'visible' : 'hidden',\n        });\n\n        // Create span to show the ripple effect\n        const ripple = document.createElement('span');\n\n        Object.assign(ripple.style, {\n          position: 'absolute',\n          pointerEvents: 'none',\n          backgroundColor: calculatedRippleColor,\n          borderRadius: '50%',\n\n          /* Transition configuration */\n          transitionProperty: 'transform opacity',\n          transitionDuration: `${Math.min(size * 1.5, 350)}ms`,\n          transitionTimingFunction: 'linear',\n          transformOrigin: 'center',\n\n          /* We'll animate these properties */\n          transform: 'translate3d(-50%, -50%, 0) scale3d(0.1, 0.1, 0.1)',\n          opacity: '0.5',\n\n          // Position the ripple where cursor was\n          left: `${touchX}px`,\n          top: `${touchY}px`,\n          width: `${size}px`,\n          height: `${size}px`,\n        });\n\n        // Finally, append it to DOM\n        container.appendChild(ripple);\n        button.appendChild(container);\n\n        // rAF runs in the same frame as the event handler\n        // Use double rAF to ensure the transition class is added in next frame\n        // This will make sure that the transition animation is triggered\n        requestAnimationFrame(() => {\n          requestAnimationFrame(() => {\n            Object.assign(ripple.style, {\n              transform: 'translate3d(-50%, -50%, 0) scale3d(1, 1, 1)',\n              opacity: '1',\n            });\n          });\n        });\n      }\n    },\n    [onPressIn, rest, rippleEffectEnabled, calculatedRippleColor]\n  );\n\n  const handlePressOut = React.useCallback(\n    (e: any) => {\n      onPressOut?.(e);\n\n      if (rippleEffectEnabled) {\n        const containers = e.currentTarget.querySelectorAll(\n          '[data-paper-ripple]'\n        ) as HTMLElement[];\n\n        requestAnimationFrame(() => {\n          requestAnimationFrame(() => {\n            containers.forEach((container) => {\n              const ripple = container.firstChild as HTMLSpanElement;\n\n              Object.assign(ripple.style, {\n                transitionDuration: '250ms',\n                opacity: 0,\n              });\n\n              // Finally remove the span after the transition\n              setTimeout(() => {\n                const { parentNode } = container;\n\n                if (parentNode) {\n                  parentNode.removeChild(container);\n                }\n              }, 500);\n            });\n          });\n        });\n      }\n    },\n    [onPressOut, rippleEffectEnabled]\n  );\n\n  const hasPassedTouchHandler = hasTouchHandler({\n    onPress,\n    onLongPress,\n    onPressIn,\n    onPressOut,\n  });\n\n  const disabled = disabledProp || !hasPassedTouchHandler;\n\n  return (\n    <Pressable\n      {...rest}\n      ref={ref}\n      onPressIn={handlePressIn}\n      onPressOut={handlePressOut}\n      disabled={disabled}\n      style={(state) => [\n        styles.touchable,\n        borderless && styles.borderless,\n        // focused state is not ready yet: https://github.com/necolas/react-native-web/issues/1849\n        // state.focused && { backgroundColor: ___ },\n        state.hovered && { backgroundColor: hoverColor },\n        disabled && styles.disabled,\n        typeof style === 'function' ? style(state) : style,\n      ]}\n    >\n      {(state) =>\n        React.Children.only(\n          typeof children === 'function' ? children(state) : children\n        )\n      }\n    </Pressable>\n  );\n};\n\n/**\n * Whether ripple effect is supported.\n */\nTouchableRipple.supported = true;\n\nconst styles = StyleSheet.create({\n  touchable: {\n    position: 'relative',\n    ...(Platform.OS === 'web' && {\n      cursor: 'pointer',\n      transition: '150ms background-color',\n    }),\n  },\n  disabled: {\n    ...(Platform.OS === 'web' && {\n      cursor: 'auto',\n    }),\n  },\n  borderless: {\n    overflow: 'hidden',\n  },\n});\n\nconst Component = forwardRef(TouchableRipple);\n\nexport default Component as typeof Component & { supported: boolean };\n"], "mappings": ";;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAAA,OAAAC,UAAA;AAW9B,OAAOC,KAAK,MAAM,OAAO;AAGzB,SAASC,SAAS;AAClB,SAASC,wBAAwB;AACjC,SAAmBC,eAAe;AAClC,SAASC,gBAAgB;AAEzB,SAASC,UAAU;AACnB,OAAOC,eAAe;AAqFtB,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAAC,IAAA,EAYnBC,GAA6B,EAC1B;EAAA,IAXDC,OAAK,GAAAF,IAAA,CAALE,KAAK;IACOC,WAAW,GAAAH,IAAA,CAAvBI,UAAU;IAAAC,eAAA,GAAAL,IAAA,CACVM,UAAU;IAAVA,UAAU,GAAAD,eAAA,cAAG,KAAK,GAAAA,eAAA;IACRE,YAAY,GAAAP,IAAA,CAAtBQ,QAAQ;IACRC,WAAW,GAAAT,IAAA,CAAXS,WAAW;IACIC,cAAc,GAAAV,IAAA,CAA7BW,aAAa;IACbC,QAAQ,GAAAZ,IAAA,CAARY,QAAQ;IACDC,cAAc,GAAAb,IAAA,CAArBc,KAAK;IACFC,IAAA,GAAAC,wBAAA,CAAAhB,IAAA,EAAAiB,SAAA;EAIL,IAAMH,KAAK,GAAGlB,gBAAgB,CAACiB,cAAc,CAAC;EAC9C,IAAAK,qBAAA,GAAkCxB,wBAAwB,CAAC;MACzDoB,KAAK,EAALA,KAAK;MACLL,WAAA,EAAAA;IACF,CAAC,CAAC;IAHMU,qBAAA,GAAAD,qBAAA,CAAAC,qBAAA;EAIR,IAAMC,UAAU,GAAG5B,KAAK,CAAC2B,qBAAqB,CAAC,CAACE,IAAI,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EACxE,IAAAC,iBAAA,GAAgCnC,KAAK,CAACoC,UAAU,CAAW9B,eAAe,CAAC;IAAnE+B,mBAAA,GAAAF,iBAAA,CAAAE,mBAAA;EAER,IAAQC,OAAO,GAAyCZ,IAAI,CAApDY,OAAO;IAAEC,WAAW,GAA4Bb,IAAI,CAA3Ca,WAAW;IAAEC,SAAS,GAAiBd,IAAI,CAA9Bc,SAAS;IAAEC,UAAA,GAAef,IAAI,CAAnBe,UAAA;EAEzC,IAAMC,aAAa,GAAG1C,KAAK,CAAC2C,WAAW,CACpC,UAAAC,CAAM,EAAK;IACVJ,SAAS,aAATA,SAAS,eAATA,SAAS,CAAGI,CAAC,CAAC;IAEd,IAAIP,mBAAmB,EAAE;MAAA,IAAAQ,KAAA;MACvB,IAAQC,QAAA,GAAapB,IAAI,CAAjBoB,QAAA;MAER,IAAMC,MAAM,GAAGH,CAAC,CAACI,aAAa;MAC9B,IAAMnC,MAAK,GAAGoC,MAAM,CAACC,gBAAgB,CAACH,MAAM,CAAC;MAC7C,IAAMI,UAAU,GAAGJ,MAAM,CAACK,qBAAqB,CAAC,CAAC;MAEjD,IAAIC,MAAM;MACV,IAAIC,MAAM;MAEV,IAAAC,cAAA,GAAoCX,CAAC,CAACY,WAAW;QAAzCC,cAAc,GAAAF,cAAA,CAAdE,cAAc;QAAEC,OAAA,GAAAH,cAAA,CAAAG,OAAA;MACxB,IAAMC,KAAK,IAAAd,KAAA,GAAGa,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAG,CAAC,CAAC,YAAAb,KAAA,GAAIY,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAG,CAAC,CAAC;MAGjD,IAAIX,QAAQ,IAAI,CAACa,KAAK,EAAE;QACtBN,MAAM,GAAGF,UAAU,CAACS,KAAK,GAAG,CAAC;QAC7BN,MAAM,GAAGH,UAAU,CAACU,MAAM,GAAG,CAAC;MAChC,CAAC,MAAM;QAAA,IAAAC,gBAAA,EAAAC,gBAAA;QACLV,MAAM,IAAAS,gBAAA,GAAGH,KAAK,CAACK,SAAS,YAAAF,gBAAA,GAAIlB,CAAC,CAACqB,KAAK;QACnCX,MAAM,IAAAS,gBAAA,GAAGJ,KAAK,CAACO,SAAS,YAAAH,gBAAA,GAAInB,CAAC,CAACuB,KAAK;MACrC;MAGA,IAAMC,IAAI,GAAGtB,QAAQ,GAEjBuB,IAAI,CAACC,GAAG,CAACnB,UAAU,CAACS,KAAK,EAAET,UAAU,CAACU,MAAM,CAAC,GAAG,GAAG,GAEnDQ,IAAI,CAACE,GAAG,CAACpB,UAAU,CAACS,KAAK,EAAET,UAAU,CAACU,MAAM,CAAC,GAAG,CAAC;MAGrD,IAAMW,SAAS,GAAGC,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;MAEhDF,SAAS,CAACG,YAAY,CAAC,mBAAmB,EAAE,EAAE,CAAC;MAE/CC,MAAM,CAACC,MAAM,CAACL,SAAS,CAAC3D,KAAK,EAAE;QAC7BiE,QAAQ,EAAE,UAAU;QACpBC,aAAa,EAAE,MAAM;QACrBC,GAAG,EAAE,GAAG;QACRC,IAAI,EAAE,GAAG;QACTC,KAAK,EAAE,GAAG;QACVC,MAAM,EAAE,GAAG;QACXC,mBAAmB,EAAEvE,MAAK,CAACuE,mBAAmB;QAC9CC,oBAAoB,EAAExE,MAAK,CAACwE,oBAAoB;QAChDC,uBAAuB,EAAEzE,MAAK,CAACyE,uBAAuB;QACtDC,sBAAsB,EAAE1E,MAAK,CAAC0E,sBAAsB;QACpDC,QAAQ,EAAE1C,QAAQ,GAAG,SAAS,GAAG;MACnC,CAAC,CAAC;MAGF,IAAM2C,MAAM,GAAGhB,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;MAE7CE,MAAM,CAACC,MAAM,CAACY,MAAM,CAAC5E,KAAK,EAAE;QAC1BiE,QAAQ,EAAE,UAAU;QACpBC,aAAa,EAAE,MAAM;QACrBW,eAAe,EAAE5D,qBAAqB;QACtC6D,YAAY,EAAE,KAAK;QAGnBC,kBAAkB,EAAE,mBAAmB;QACvCC,kBAAkB,EAAE,GAAGxB,IAAI,CAACC,GAAG,CAACF,IAAI,GAAG,GAAG,EAAE,GAAG,CAAC,IAAI;QACpD0B,wBAAwB,EAAE,QAAQ;QAClCC,eAAe,EAAE,QAAQ;QAGzBC,SAAS,EAAE,mDAAmD;QAC9DC,OAAO,EAAE,KAAK;QAGdhB,IAAI,EAAE,GAAG5B,MAAM,IAAI;QACnB2B,GAAG,EAAE,GAAG1B,MAAM,IAAI;QAClBM,KAAK,EAAE,GAAGQ,IAAI,IAAI;QAClBP,MAAM,EAAE,GAAGO,IAAI;MACjB,CAAC,CAAC;MAGFI,SAAS,CAAC0B,WAAW,CAACT,MAAM,CAAC;MAC7B1C,MAAM,CAACmD,WAAW,CAAC1B,SAAS,CAAC;MAK7B2B,qBAAqB,CAAC,YAAM;QAC1BA,qBAAqB,CAAC,YAAM;UAC1BvB,MAAM,CAACC,MAAM,CAACY,MAAM,CAAC5E,KAAK,EAAE;YAC1BmF,SAAS,EAAE,6CAA6C;YACxDC,OAAO,EAAE;UACX,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF,CAAC,EACD,CAACzD,SAAS,EAAEd,IAAI,EAAEW,mBAAmB,EAAEP,qBAAqB,CAC9D,CAAC;EAED,IAAMsE,cAAc,GAAGpG,KAAK,CAAC2C,WAAW,CACrC,UAAAC,CAAM,EAAK;IACVH,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAGG,CAAC,CAAC;IAEf,IAAIP,mBAAmB,EAAE;MACvB,IAAMgE,UAAU,GAAGzD,CAAC,CAACI,aAAa,CAACsD,gBAAgB,CACjD,qBACF,CAAkB;MAElBH,qBAAqB,CAAC,YAAM;QAC1BA,qBAAqB,CAAC,YAAM;UAC1BE,UAAU,CAACE,OAAO,CAAE,UAAA/B,SAAS,EAAK;YAChC,IAAMiB,MAAM,GAAGjB,SAAS,CAACgC,UAA6B;YAEtD5B,MAAM,CAACC,MAAM,CAACY,MAAM,CAAC5E,KAAK,EAAE;cAC1BgF,kBAAkB,EAAE,OAAO;cAC3BI,OAAO,EAAE;YACX,CAAC,CAAC;YAGFQ,UAAU,CAAC,YAAM;cACf,IAAQC,UAAA,GAAelC,SAAS,CAAxBkC,UAAA;cAER,IAAIA,UAAU,EAAE;gBACdA,UAAU,CAACC,WAAW,CAACnC,SAAS,CAAC;cACnC;YACF,CAAC,EAAE,GAAG,CAAC;UACT,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF,CAAC,EACD,CAAC/B,UAAU,EAAEJ,mBAAmB,CAClC,CAAC;EAED,IAAMuE,qBAAqB,GAAGnG,eAAe,CAAC;IAC5C6B,OAAO,EAAPA,OAAO;IACPC,WAAW,EAAXA,WAAW;IACXC,SAAS,EAATA,SAAS;IACTC,UAAA,EAAAA;EACF,CAAC,CAAC;EAEF,IAAMtB,QAAQ,GAAGD,YAAY,IAAI,CAAC0F,qBAAqB;EAEvD,OACE5G,KAAA,CAAA0E,aAAA,CAACtE,SAAS,EAAAyG,QAAA,KACJnF,IAAI;IACRd,GAAG,EAAEA,GAAI;IACT4B,SAAS,EAAEE,aAAc;IACzBD,UAAU,EAAE2D,cAAe;IAC3BjF,QAAQ,EAAEA,QAAS;IACnBN,KAAK,EAAG,SAARA,KAAKA,CAAGiG,KAAK;MAAA,OAAK,CAChBC,MAAM,CAACC,SAAS,EAChB/F,UAAU,IAAI8F,MAAM,CAAC9F,UAAU,EAG/B6F,KAAK,CAACG,OAAO,IAAI;QAAEvB,eAAe,EAAE3D;MAAW,CAAC,EAChDZ,QAAQ,IAAI4F,MAAM,CAAC5F,QAAQ,EAC3B,OAAON,OAAK,KAAK,UAAU,GAAGA,OAAK,CAACiG,KAAK,CAAC,GAAGjG,OAAK;IAAA;EAClD,IAEA,UAAAiG,KAAK;IAAA,OACL9G,KAAK,CAACkH,QAAQ,CAACC,IAAI,CACjB,OAAO5F,QAAQ,KAAK,UAAU,GAAGA,QAAQ,CAACuF,KAAK,CAAC,GAAGvF,QACrD,CAEO;EAAA,EAAC;AAEhB,CAAC;AAKDb,eAAe,CAAC0G,SAAS,GAAG,IAAI;AAEhC,IAAML,MAAM,GAAG7G,UAAU,CAACmH,MAAM,CAAC;EAC/BL,SAAS,EAAAM,aAAA;IACPxC,QAAQ,EAAE;EAAU,GAChB7E,QAAQ,CAACsH,EAAE,KAAK,KAAK,IAAI;IAC3BC,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE;EACd,CAAC,CACF;EACDtG,QAAQ,EAAAmG,aAAA,KACFrH,QAAQ,CAACsH,EAAE,KAAK,KAAK,IAAI;IAC3BC,MAAM,EAAE;EACV,CAAC,CACF;EACDvG,UAAU,EAAE;IACVuE,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC;AAEF,IAAMkC,SAAS,GAAGlH,UAAU,CAACE,eAAe,CAAC;AAE7C,eAAegH,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}