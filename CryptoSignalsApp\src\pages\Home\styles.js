import { StyleSheet, Dimensions } from 'react-native';

const windowHeight = Dimensions.get('window').height;
const MARGIN_BOTTOM = 16;
const FONT_SIZE_TITLE = 18;
const FONT_SIZE_TYPE = 16;
const DOT_SIZE = 6;
const ICON_SIZE = 24;
const MARGIN_TOP_EMPTY_STATE = 18;
const MARGIN_TOP_ROW_SPACE_BETWEEN = 32;

const styles = StyleSheet.create({
    // Estilos Gerais
    scrollView: {
        height: windowHeight - 280, // Considere criar uma constante para '280' se tiver um significado específico
    },
    emptyState: {
        color: '#fff',
        fontSize: FONT_SIZE_TITLE,
        textAlign: 'center',
        fontFamily: 'Poppins_400Regular',
        marginTop: MARGIN_TOP_EMPTY_STATE,
    },

    // Estilos de Home
    container: {
        flex: 1,
        backgroundColor: '#fff',
        padding: 16,
    },
    row: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: MARGIN_BOTTOM,
    },
    title: {
        fontSize: FONT_SIZE_TITLE,
        fontWeight: 'bold',
    },
    type: {
        fontSize: FONT_SIZE_TYPE,
        marginLeft: 8,
    },
    dot: {
        width: DOT_SIZE,
        height: DOT_SIZE,
        borderRadius: DOT_SIZE / 2,
        backgroundColor: 'black',
        marginLeft: 8,
        marginRight: 8,
    },
    rowSpaceBetween: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginTop: MARGIN_TOP_ROW_SPACE_BETWEEN,
    },
    icon: {
        fontSize: ICON_SIZE,
        // TODO: Adicione outras propriedades se necessário, como color, etc.
    },
});

export default styles;