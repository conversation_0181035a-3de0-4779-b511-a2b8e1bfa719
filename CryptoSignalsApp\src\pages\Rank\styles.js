import React, { useState, useEffect } from 'react';
import { View, Text, Image, Switch, ScrollView } from 'react-native';
import { AntDesign } from '@expo/vector-icons';
import styles from './styles';

const Profile = () => {
  const [isPublic, setIsPublic] = useState(false);
  const [userData, setUserData] = useState({
    username: '<PERSON><PERSON><PERSON>',
    fullName: '<PERSON>',
    bio: 'Crypto enthusiast',
    profilePicture: 'https://example.com/profile.jpg',
  });

  const togglePrivacy = () => {
    setIsPublic((prevState) => !prevState);
  };

  useEffect(() => {
    // Simulando a obtenção de dados do usuário
    setTimeout(() => {
      setUserData({
        username: '<PERSON><PERSON><PERSON>',
        fullName: '<PERSON>',
        bio: 'Crypto enthusiast',
        profilePicture: 'https://example.com/profile.jpg',
      });
    }, 2000);
  }, []);

  return (
    <ScrollView style={styles.container}>
      <View style={styles.profileHeader}>
        <Image source={{ uri: userData.profilePicture }} style={styles.profilePicture} />
        <Text style={styles.username}>{userData.username}</Text>
        <Text style={styles.fullName}>{userData.fullName}</Text>
        <Text style={styles.bio}>{userData.bio}</Text>
        <View style={styles.privacySwitch}>
          <Text style={styles.privacyLabel}>Perfil {isPublic ? 'Público' : 'Privado'}</Text>
          <Switch
            value={isPublic}
            onValueChange={togglePrivacy}
            thumbColor={isPublic ? '#28a745' : '#dc3545'}
          />
        </View>
      </View>

      <View style={styles.metricsSection}>
        <Text style={styles.sectionTitle}>Métricas de Desempenho</Text>
        {/* Exiba as métricas de desempenho aqui */}
      </View>

      <View style={styles.badgesSection}>
        <Text style={styles.sectionTitle}>Distintivos</Text>
        {/* Exiba os distintivos aqui */}
      </View>
    </ScrollView>
  );
};

export default Profile;
