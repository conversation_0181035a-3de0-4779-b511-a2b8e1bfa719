{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport * as React from 'react';\nimport isArrayEqual from \"./isArrayEqual\";\nimport NavigationBuilderContext from \"./NavigationBuilderContext\";\nimport NavigationRouteContext from \"./NavigationRouteContext\";\nexport default function useOnGetState(_ref) {\n  var getState = _ref.getState,\n    getStateListeners = _ref.getStateListeners;\n  var _React$useContext = React.useContext(NavigationBuilderContext),\n    addKeyedListener = _React$useContext.addKeyedListener;\n  var route = React.useContext(NavigationRouteContext);\n  var key = route ? route.key : 'root';\n  var getRehydratedState = React.useCallback(function () {\n    var state = getState();\n    var routes = state.routes.map(function (route) {\n      var _getStateListeners$ro;\n      var childState = (_getStateListeners$ro = getStateListeners[route.key]) === null || _getStateListeners$ro === void 0 ? void 0 : _getStateListeners$ro.call(getStateListeners);\n      if (route.state === childState) {\n        return route;\n      }\n      return _objectSpread(_objectSpread({}, route), {}, {\n        state: childState\n      });\n    });\n    if (isArrayEqual(state.routes, routes)) {\n      return state;\n    }\n    return _objectSpread(_objectSpread({}, state), {}, {\n      routes: routes\n    });\n  }, [getState, getStateListeners]);\n  React.useEffect(function () {\n    return addKeyedListener === null || addKeyedListener === void 0 ? void 0 : addKeyedListener('getState', key, getRehydratedState);\n  }, [addKeyedListener, getRehydratedState, key]);\n}", "map": {"version": 3, "names": ["React", "isArrayEqual", "NavigationBuilderContext", "NavigationRouteContext", "useOnGetState", "_ref", "getState", "getStateListeners", "_React$useContext", "useContext", "addKeyedListener", "route", "key", "getRehydratedState", "useCallback", "state", "routes", "map", "_getStateListeners$ro", "childState", "call", "_objectSpread", "useEffect"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\@react-navigation\\core\\src\\useOnGetState.tsx"], "sourcesContent": ["import type { NavigationState } from '@react-navigation/routers';\nimport * as React from 'react';\n\nimport isArrayEqual from './isArrayEqual';\nimport NavigationBuilderContext, {\n  GetStateListener,\n} from './NavigationBuilderContext';\nimport NavigationRouteContext from './NavigationRouteContext';\n\ntype Options = {\n  getState: () => NavigationState;\n  getStateListeners: Record<string, GetStateListener | undefined>;\n};\n\nexport default function useOnGetState({\n  getState,\n  getStateListeners,\n}: Options) {\n  const { addKeyedListener } = React.useContext(NavigationBuilderContext);\n  const route = React.useContext(NavigationRouteContext);\n  const key = route ? route.key : 'root';\n\n  const getRehydratedState = React.useCallback(() => {\n    const state = getState();\n\n    // Avoid returning new route objects if we don't need to\n    const routes = state.routes.map((route) => {\n      const childState = getStateListeners[route.key]?.();\n\n      if (route.state === childState) {\n        return route;\n      }\n\n      return { ...route, state: childState };\n    });\n\n    if (isArrayEqual(state.routes, routes)) {\n      return state;\n    }\n\n    return { ...state, routes };\n  }, [getState, getStateListeners]);\n\n  React.useEffect(() => {\n    return addKeyedListener?.('getState', key, getRehydratedState);\n  }, [addKeyedListener, getRehydratedState, key]);\n}\n"], "mappings": ";;;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,OAAOC,YAAY;AACnB,OAAOC,wBAAwB;AAG/B,OAAOC,sBAAsB;AAO7B,eAAe,SAASC,aAAaA,CAAAC,IAAA,EAGzB;EAAA,IAFVC,QAAQ,GAEAD,IAAA,CAFRC,QAAQ;IACRC,iBAAA,GACQF,IAAA,CADRE,iBAAA;EAEA,IAAAC,iBAAA,GAA6BR,KAAK,CAACS,UAAU,CAACP,wBAAwB,CAAC;IAA/DQ,gBAAA,GAAAF,iBAAA,CAAAE,gBAAA;EACR,IAAMC,KAAK,GAAGX,KAAK,CAACS,UAAU,CAACN,sBAAsB,CAAC;EACtD,IAAMS,GAAG,GAAGD,KAAK,GAAGA,KAAK,CAACC,GAAG,GAAG,MAAM;EAEtC,IAAMC,kBAAkB,GAAGb,KAAK,CAACc,WAAW,CAAC,YAAM;IACjD,IAAMC,KAAK,GAAGT,QAAQ,EAAE;IAGxB,IAAMU,MAAM,GAAGD,KAAK,CAACC,MAAM,CAACC,GAAG,CAAE,UAAAN,KAAK,EAAK;MAAA,IAAAO,qBAAA;MACzC,IAAMC,UAAU,IAAAD,qBAAA,GAAGX,iBAAiB,CAACI,KAAK,CAACC,GAAG,CAAC,cAAAM,qBAAA,uBAA5BA,qBAAA,CAAAE,IAAA,CAAAb,iBAAiB,CAAe;MAEnD,IAAII,KAAK,CAACI,KAAK,KAAKI,UAAU,EAAE;QAC9B,OAAOR,KAAK;MACd;MAEA,OAAAU,aAAA,CAAAA,aAAA,KAAYV,KAAK;QAAEI,KAAK,EAAEI;MAAA;IAC5B,CAAC,CAAC;IAEF,IAAIlB,YAAY,CAACc,KAAK,CAACC,MAAM,EAAEA,MAAM,CAAC,EAAE;MACtC,OAAOD,KAAK;IACd;IAEA,OAAAM,aAAA,CAAAA,aAAA,KAAYN,KAAK;MAAEC,MAAA,EAAAA;IAAA;EACrB,CAAC,EAAE,CAACV,QAAQ,EAAEC,iBAAiB,CAAC,CAAC;EAEjCP,KAAK,CAACsB,SAAS,CAAC,YAAM;IACpB,OAAOZ,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAG,UAAU,EAAEE,GAAG,EAAEC,kBAAkB,CAAC;EAChE,CAAC,EAAE,CAACH,gBAAgB,EAAEG,kBAAkB,EAAED,GAAG,CAAC,CAAC;AACjD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}