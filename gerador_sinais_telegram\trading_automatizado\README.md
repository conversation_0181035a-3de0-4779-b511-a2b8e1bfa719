# Sistema de Trading Automatizado

Este módulo permite a execução automatizada de operações na Binance Futures com base em sinais recebidos via Telegram ou inseridos manualmente.

## Características

- Execução automática de sinais recebidos via Telegram
- Execução manual de operações via linha de comando
- Suporte para operações LONG e SHORT
- Configuração de Stop Loss e Take Profit automáticos
- Modo de simulação para testes sem risco
- Monitoramento contínuo de operações abertas
- Alavancagem configurável
- Limite de 4 posições abertas simultaneamente
- Alocação de 10% do capital total por operação
- Suporte para múltiplas estratégias (Scalping e Swing Trading)
- Otimizações baseadas em resultados de backtesting
- Filtros de volatilidade e tendência
- Take Profits em múltiplos níveis

## Estratégias Implementadas

### Scalping

Otimizada para operações de curto prazo em timeframes menores (1m, 5m, 15m).

- **Retorno no Backtesting**: ****%
- **Win Rate**: 56.3%
- **Melhor Timeframe**: 1M
- **Stop Loss**: 0.5% (ajustado para reduzir perdas)
- **Take Profits**: Múltiplos níveis (0.4%, 0.6%, 0.8%, 1.0%)
- **Distribuição de Posição**: 25% para cada nível de TP
- **Filtros**: Volatilidade moderada (0.5% a 3.0%), verificação de tendência

### Swing Trading

Otimizada para operações de médio prazo em timeframes maiores (4h, 1d).

- **Retorno no Backtesting**: +12.5%
- **Win Rate**: 62.2%
- **Melhor Timeframe**: 4H
- **Stop Loss**: 1.0% (maior tolerância para swing)
- **Take Profits**: Múltiplos níveis (1.0%, 1.5%, 2.0%, 2.5%)
- **Distribuição de Posição**: 20%, 30%, 30%, 20%
- **Filtros**: Volatilidade mais ampla (0.8% a 5.0%), verificação de tendência

## Requisitos

- Python 3.7+
- Conta na Binance Futures (real ou testnet)
- Chaves de API da Binance configuradas no arquivo `.env`
- Conta no Telegram e configurações de API no arquivo `.env`

## Configuração

1. Certifique-se de que o arquivo `.env` na raiz do projeto contém as seguintes configurações:

```
# Configurações da Binance
BINANCE_API_KEY=sua_api_key_aqui
BINANCE_API_SECRET=seu_api_secret_aqui
BINANCE_TESTNET=True  # Mude para False para usar a rede real

# Configurações do Telegram
TELEGRAM_API_ID=seu_api_id_aqui
TELEGRAM_API_HASH=seu_api_hash_aqui
TELEGRAM_GROUP_ID=id_do_grupo_aqui
TELEGRAM_SESSION_FILE=session.session

# Configurações de trading
LEVERAGE=20  # Alavancagem padrão
TRADING_START_HOUR=6  # Hora de início das operações (6:00 AM)
TRADING_END_HOUR=21   # Hora de término das operações (9:00 PM)
```

2. Instale as dependências necessárias:

```bash
pip install -r requirements.txt
```

## Uso

### Iniciar o Trader Automático (Telegram)

```bash
python -m trading_automatizado.start_trading --modo telegram --capital 20 --real
```

### Iniciar o Trader Automático com Capital Total

```bash
python -m trading_automatizado.start_trading --modo telegram --capital_total 1000 --real
```

Isso alocará 10% do capital total ($100) para cada operação e limitará a 4 posições abertas simultaneamente.

### Executar uma Operação Manual

```bash
python -m trading_automatizado.start_trading --modo manual --capital 20 --symbol BTCUSDT --tipo LONG --preco 50000 --sl 49500 --tp 51000
```

### Executar uma Operação Manual com Capital Total

```bash
python -m trading_automatizado.start_trading --modo manual --capital_total 1000 --symbol BTCUSDT --tipo LONG --preco 50000 --sl 49500 --tp 51000
```

## Parâmetros

- `--modo`: Modo de operação (`telegram` ou `manual`)
- `--capital`: Capital por operação em USD (padrão: 20)
- `--capital_total`: Capital total disponível para trading (se fornecido, usa 10% por operação)
- `--real`: Ativa o modo real (sem simulação)
- `--estrategia`: Estratégia de trading (`scalp` ou `swing`, padrão: `swing`)
- `--symbol`: Par de trading (ex: BTCUSDT) - apenas para modo manual
- `--tipo`: Tipo de operação (`LONG` ou `SHORT`) - apenas para modo manual
- `--preco`: Preço de entrada - apenas para modo manual
- `--sl`: Stop Loss (opcional) - apenas para modo manual
- `--tp`: Take Profit (opcional) - apenas para modo manual

### Trading Automatizado via Telegram

O módulo `telegram_trader.py` monitora mensagens em um grupo do Telegram e executa operações automaticamente quando detecta sinais no formato adequado.

Para iniciar o monitoramento:

```bash
python -m trading_automatizado.telegram_trader
```

O sistema reconhece sinais no seguinte formato:

```
SINAL DE COMPRA PARA BTCUSDT
ENTRADA: 65000
STOP LOSS: 64500
TAKE PROFIT: 66000
ESTRATEGIA: SWING
```

ou

```
SINAL DE VENDA PARA ETHUSDT
ENTRADA EM 3500
SL: 3550
TP: 3400
TIMEFRAME: 1M
```

O sistema detecta automaticamente a estratégia mais adequada com base nas informações do sinal:
- Se "ESTRATEGIA" ou "TIMEFRAME" estiverem presentes, serão usados para determinar a estratégia
- Se não estiverem presentes, o sistema usará Swing Trading por padrão (melhor performance no backtesting)

### Trading Manual

O módulo `manual_trader.py` permite executar operações manualmente via linha de comando:

```bash
python -m trading_automatizado.manual_trader --symbol BTCUSDT --tipo LONG --preco 65000 --sl 64500 --tp 66000 --estrategia swing
```

Parâmetros disponíveis:

- `--symbol`: Par de trading (ex: BTCUSDT)
- `--tipo`: Tipo de operação (LONG ou SHORT)
- `--preco`: Preço de entrada
- `--sl`: Stop Loss (opcional, calculado automaticamente se não fornecido)
- `--tp`: Take Profit (opcional, calculado automaticamente se não fornecido)
- `--capital`: Capital por operação em USD (padrão: 20)
- `--estrategia`: Estratégia de trading (`scalp` ou `swing`, padrão: `swing`)
- `--real`: Modo real (sem simulação)

### Modo de Simulação

Por padrão, o sistema opera em modo de simulação, o que significa que as ordens não são realmente enviadas para a Binance. Para desativar o modo de simulação:

- No script unificado, use o parâmetro `--real`
- No `telegram_trader.py`, modifique o parâmetro `modo_simulacao` para `False` na inicialização do `TelegramTrader`
- No `manual_trader.py`, use o parâmetro `--real` na linha de comando

## Monitoramento

O sistema mantém logs detalhados das operações em:

- `trading_automatizado/auto_trader.log`: Log do trader automático
- `trading_automatizado/telegram_trader.log`: Log do trader baseado em Telegram
- `trading_automatizado/manual_trader.log`: Log do trader manual
- `trading_automatizado/start_trading.log`: Log do script de inicialização

## Melhorias Baseadas no Backtesting

Com base nos resultados do backtesting, foram implementadas as seguintes melhorias:

1. **Preferência por Swing Trading**: O sistema prioriza a estratégia de Swing Trading por padrão, que apresentou melhor performance no backtesting (retorno de 12.5% vs 8.2% do Scalping).

2. **Otimização de Take Profits**: Implementação de múltiplos níveis de take profit com distribuição otimizada da posição.

3. **Ajuste de Stop Loss**: Valores de stop loss ajustados para cada estratégia (0.5% para Scalping e 1.0% para Swing).

4. **Filtros de Volatilidade**: Verificação da volatilidade atual para garantir que seja adequada para a estratégia selecionada.

5. **Filtros de Tendência**: Verificação da tendência atual para garantir que seja favorável para o tipo de sinal.

6. **Timeframes Otimizados**: Uso dos timeframes que apresentaram melhor performance no backtesting (1M para Scalping e 4H para Swing).

## Limitações

- O sistema não suporta múltiplas posições simultâneas para o mesmo par de trading
- O capital por operação é fixo (configurável, mas não dinâmico)
- A detecção de sinais do Telegram é baseada em padrões de texto e pode não reconhecer todos os formatos

## Aviso de Risco

O trading de criptomoedas com alavancagem envolve riscos significativos. Este sistema é fornecido apenas para fins educacionais e de demonstração. Use por sua conta e risco.

## Exemplos de Uso

### Exemplo 1: Monitorar sinais do Telegram em modo de simulação

```python
from trading_automatizado.telegram_trader import TelegramTrader
import asyncio

async def main():
    trader = TelegramTrader(capital_por_operacao=20, modo_simulacao=True)
    await trader.iniciar()

if __name__ == "__main__":
    asyncio.run(main())
```

### Exemplo 2: Executar uma operação manual com estratégia de swing em modo real

```python
from trading_automatizado.manual_trader import ManualTrader
import asyncio

async def main():
    trader = ManualTrader(capital_por_operacao=20, modo_simulacao=False, estrategia="swing")
    await trader.executar_operacao(
        symbol="BTCUSDT",
        tipo_sinal="LONG",
        preco_entrada=65000,
        stop_loss=64500,
        take_profit=66000
    )
    await trader.iniciar_monitoramento()

if __name__ == "__main__":
    asyncio.run(main())
```

### Exemplo 3: Executar uma operação manual com estratégia de scalping

```python
from trading_automatizado.manual_trader import ManualTrader
import asyncio

async def main():
    trader = ManualTrader(capital_por_operacao=20, modo_simulacao=True, estrategia="scalp")
    await trader.executar_operacao(
        symbol="ETHUSDT",
        tipo_sinal="SHORT",
        preco_entrada=3500
        # Stop loss e take profit serão calculados automaticamente
    )
    await trader.iniciar_monitoramento()

if __name__ == "__main__":
    asyncio.run(main())
``` 