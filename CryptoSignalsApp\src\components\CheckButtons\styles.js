import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    flexDirection: 'row',
    position: 'relative',
    marginBottom: 4,
  },
  btn: {
    color: '#fff',
    fontSize: 13,
    borderStyle: 'solid',
    borderColor: '#0D0D0D',
    borderWidth: 1,
    borderRadius: 4,
    paddingVertical: 6,
    paddingHorizontal: 8,
    marginHorizontal: 4,
    flexGrow: 1,
    textAlign: 'center',
    fontFamily: 'Poppins_500Medium'
  },
  btnChecked: {
    backgroundColor: '#0D0D0D',
    color: '#FECB37',
    fontWeight: '500'
  },
  firstBtn: {
    marginLeft: 0
  },
  lastBtn: {
    marginRight:0
  }
});

export default styles
