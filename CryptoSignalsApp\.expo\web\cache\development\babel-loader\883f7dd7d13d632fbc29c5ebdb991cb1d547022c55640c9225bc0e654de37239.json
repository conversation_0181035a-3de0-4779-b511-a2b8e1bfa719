{"ast": null, "code": "import * as React from 'react';\nexport default function useDocumentTitle(ref) {\n  var _ref = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},\n    _ref$enabled = _ref.enabled,\n    enabled = _ref$enabled === void 0 ? true : _ref$enabled,\n    _ref$formatter = _ref.formatter,\n    formatter = _ref$formatter === void 0 ? function (options, route) {\n      var _ref2;\n      return (_ref2 = options === null || options === void 0 ? void 0 : options.title) != null ? _ref2 : route === null || route === void 0 ? void 0 : route.name;\n    } : _ref$formatter;\n  React.useEffect(function () {\n    if (!enabled) {\n      return;\n    }\n    var navigation = ref.current;\n    if (navigation) {\n      var title = formatter(navigation.getCurrentOptions(), navigation.getCurrentRoute());\n      document.title = title;\n    }\n    return navigation === null || navigation === void 0 ? void 0 : navigation.addListener('options', function (e) {\n      var title = formatter(e.data.options, navigation === null || navigation === void 0 ? void 0 : navigation.getCurrentRoute());\n      document.title = title;\n    });\n  });\n}", "map": {"version": 3, "names": ["React", "useDocumentTitle", "ref", "_ref", "arguments", "length", "undefined", "_ref$enabled", "enabled", "_ref$formatter", "formatter", "options", "route", "_ref2", "title", "name", "useEffect", "navigation", "current", "getCurrentOptions", "getCurrentRoute", "document", "addListener", "e", "data"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\@react-navigation\\native\\src\\useDocumentTitle.tsx"], "sourcesContent": ["import type {\n  NavigationContainerRef,\n  ParamListBase,\n} from '@react-navigation/core';\nimport * as React from 'react';\n\nimport type { DocumentTitleOptions } from './types';\n\n/**\n * Set the document title for the active screen\n */\nexport default function useDocumentTitle(\n  ref: React.RefObject<NavigationContainerRef<ParamListBase>>,\n  {\n    enabled = true,\n    formatter = (options, route) => options?.title ?? route?.name,\n  }: DocumentTitleOptions = {}\n) {\n  React.useEffect(() => {\n    if (!enabled) {\n      return;\n    }\n\n    const navigation = ref.current;\n\n    if (navigation) {\n      const title = formatter(\n        navigation.getCurrentOptions(),\n        navigation.getCurrentRoute()\n      );\n\n      document.title = title;\n    }\n\n    return navigation?.addListener('options', (e) => {\n      const title = formatter(e.data.options, navigation?.getCurrentRoute());\n\n      document.title = title;\n    });\n  });\n}\n"], "mappings": "AAIA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAO9B,eAAe,SAASC,gBAAgBA,CACtCC,GAA2D,EAK3D;EAAA,IAAAC,IAAA,GADuBC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAAAG,YAAA,GAAAJ,IAAA,CAF1BK,OAAO;IAAPA,OAAO,GAAAD,YAAA,cAAG,IAAI,GAAAA,YAAA;IAAAE,cAAA,GAAAN,IAAA,CACdO,SAAS;IAATA,SAAS,GAAAD,cAAA,cAAG,UAACE,OAAO,EAAEC,KAAK;MAAA,IAAAC,KAAA;MAAA,QAAAA,KAAA,GAAKF,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEG,KAAK,YAAAD,KAAA,GAAID,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEG,IAAI;IAAA,IAAAN,cAAA;EAG/DT,KAAK,CAACgB,SAAS,CAAC,YAAM;IACpB,IAAI,CAACR,OAAO,EAAE;MACZ;IACF;IAEA,IAAMS,UAAU,GAAGf,GAAG,CAACgB,OAAO;IAE9B,IAAID,UAAU,EAAE;MACd,IAAMH,KAAK,GAAGJ,SAAS,CACrBO,UAAU,CAACE,iBAAiB,EAAE,EAC9BF,UAAU,CAACG,eAAe,EAAE,CAC7B;MAEDC,QAAQ,CAACP,KAAK,GAAGA,KAAK;IACxB;IAEA,OAAOG,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEK,WAAW,CAAC,SAAS,EAAG,UAAAC,CAAC,EAAK;MAC/C,IAAMT,KAAK,GAAGJ,SAAS,CAACa,CAAC,CAACC,IAAI,CAACb,OAAO,EAAEM,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEG,eAAe,EAAE,CAAC;MAEtEC,QAAQ,CAACP,KAAK,GAAGA,KAAK;IACxB,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}