{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"style\", \"theme\", \"variant\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nimport * as React from 'react';\nimport Animated from \"react-native-web/dist/exports/Animated\";\nimport I18nManager from \"react-native-web/dist/exports/I18nManager\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport { useInternalTheme } from \"../../core/theming\";\nimport { forwardRef } from \"../../utils/forwardRef\";\nvar AnimatedText = forwardRef(function AnimatedText(_ref, ref) {\n  var style = _ref.style,\n    themeOverrides = _ref.theme,\n    variant = _ref.variant,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var theme = useInternalTheme(themeOverrides);\n  var writingDirection = I18nManager.getConstants().isRTL ? 'rtl' : 'ltr';\n  if (theme.isV3 && variant) {\n    var font = theme.fonts[variant];\n    if (typeof font !== 'object') {\n      throw new Error(`Variant ${variant} was not provided properly. Valid variants are ${Object.keys(theme.fonts).join(', ')}.`);\n    }\n    return React.createElement(Animated.Text, _extends({\n      ref: ref\n    }, rest, {\n      style: [font, styles.text, {\n        writingDirection: writingDirection,\n        color: theme.colors.onSurface\n      }, style]\n    }));\n  } else {\n    var _font = !theme.isV3 ? theme.fonts.regular : theme.fonts.bodyMedium;\n    var textStyle = _objectSpread(_objectSpread({}, _font), {}, {\n      color: theme.isV3 ? theme.colors.onSurface : theme.colors.text\n    });\n    return React.createElement(Animated.Text, _extends({\n      ref: ref\n    }, rest, {\n      style: [styles.text, textStyle, {\n        writingDirection: writingDirection\n      }, style]\n    }));\n  }\n});\nvar styles = StyleSheet.create({\n  text: {\n    textAlign: 'left'\n  }\n});\nexport var customAnimatedText = function customAnimatedText() {\n  return AnimatedText;\n};\nexport default AnimatedText;", "map": {"version": 3, "names": ["React", "Animated", "I18nManager", "StyleSheet", "useInternalTheme", "forwardRef", "AnimatedText", "_ref", "ref", "style", "themeOverrides", "theme", "variant", "rest", "_objectWithoutProperties", "_excluded", "writingDirection", "getConstants", "isRTL", "isV3", "font", "fonts", "Error", "Object", "keys", "join", "createElement", "Text", "_extends", "styles", "text", "color", "colors", "onSurface", "regular", "bodyMedium", "textStyle", "_objectSpread", "create", "textAlign", "customAnimatedText"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\Typography\\AnimatedText.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  Animated,\n  I18nManager,\n  StyleProp,\n  StyleSheet,\n  TextStyle,\n  Text,\n} from 'react-native';\n\nimport type { VariantProp } from './types';\nimport { useInternalTheme } from '../../core/theming';\nimport type { ThemeProp } from '../../types';\nimport { forwardRef } from '../../utils/forwardRef';\n\ntype Props<T> = React.ComponentPropsWithRef<typeof Animated.Text> & {\n  /**\n   * Variant defines appropriate text styles for type role and its size.\n   * Available variants:\n   *\n   *  Display: `displayLarge`, `displayMedium`, `displaySmall`\n   *\n   *  Headline: `headlineLarge`, `headlineMedium`, `headlineSmall`\n   *\n   *  Title: `titleLarge`, `titleMedium`, `titleSmall`\n   *\n   *  Label:  `labelLarge`, `labelMedium`, `labelSmall`\n   *\n   *  Body: `bodyLarge`, `bodyMedium`, `bodySmall`\n   */\n  variant?: VariantProp<T>;\n  style?: StyleProp<TextStyle>;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n};\n\n/**\n * Animated text component which follows styles from the theme.\n *\n * @extends Text props https://reactnative.dev/docs/text#props\n */\nconst AnimatedText = forwardRef<Text & HTMLElement, Props<never>>(\n  function AnimatedText(\n    { style, theme: themeOverrides, variant, ...rest },\n    ref\n  ) {\n    const theme = useInternalTheme(themeOverrides);\n    const writingDirection = I18nManager.getConstants().isRTL ? 'rtl' : 'ltr';\n\n    if (theme.isV3 && variant) {\n      const font = theme.fonts[variant];\n      if (typeof font !== 'object') {\n        throw new Error(\n          `Variant ${variant} was not provided properly. Valid variants are ${Object.keys(\n            theme.fonts\n          ).join(', ')}.`\n        );\n      }\n\n      return (\n        <Animated.Text\n          ref={ref}\n          {...rest}\n          style={[\n            font,\n            styles.text,\n            { writingDirection, color: theme.colors.onSurface },\n            style,\n          ]}\n        />\n      );\n    } else {\n      const font = !theme.isV3 ? theme.fonts.regular : theme.fonts.bodyMedium;\n      const textStyle = {\n        ...font,\n        color: theme.isV3 ? theme.colors.onSurface : theme.colors.text,\n      };\n      return (\n        <Animated.Text\n          ref={ref}\n          {...rest}\n          style={[\n            styles.text,\n            textStyle,\n            {\n              writingDirection,\n            },\n            style,\n          ]}\n        />\n      );\n    }\n  }\n);\n\nconst styles = StyleSheet.create({\n  text: {\n    textAlign: 'left',\n  },\n});\n\nexport const customAnimatedText = <T,>() =>\n  AnimatedText as (props: Props<T>) => JSX.Element;\n\nexport default AnimatedText;\n"], "mappings": ";;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAAA,OAAAC,WAAA;AAAA,OAAAC,UAAA;AAW9B,SAASC,gBAAgB;AAEzB,SAASC,UAAU;AA8BnB,IAAMC,YAAY,GAAGD,UAAU,CAC7B,SAASC,YAAYA,CAAAC,IAAA,EAEnBC,GAAG,EACH;EAAA,IAFEC,KAAK,GAAAF,IAAA,CAALE,KAAK;IAASC,cAAc,GAAAH,IAAA,CAArBI,KAAK;IAAkBC,OAAO,GAAAL,IAAA,CAAPK,OAAO;IAAKC,IAAA,GAAAC,wBAAA,CAAAP,IAAA,EAAAQ,SAAA;EAG5C,IAAMJ,KAAK,GAAGP,gBAAgB,CAACM,cAAc,CAAC;EAC9C,IAAMM,gBAAgB,GAAGd,WAAW,CAACe,YAAY,CAAC,CAAC,CAACC,KAAK,GAAG,KAAK,GAAG,KAAK;EAEzE,IAAIP,KAAK,CAACQ,IAAI,IAAIP,OAAO,EAAE;IACzB,IAAMQ,IAAI,GAAGT,KAAK,CAACU,KAAK,CAACT,OAAO,CAAC;IACjC,IAAI,OAAOQ,IAAI,KAAK,QAAQ,EAAE;MAC5B,MAAM,IAAIE,KAAK,CACb,WAAWV,OAAO,kDAAkDW,MAAM,CAACC,IAAI,CAC7Eb,KAAK,CAACU,KACR,CAAC,CAACI,IAAI,CAAC,IAAI,CAAC,GACd,CAAC;IACH;IAEA,OACEzB,KAAA,CAAA0B,aAAA,CAACzB,QAAQ,CAAC0B,IAAI,EAAAC,QAAA;MACZpB,GAAG,EAAEA;IAAI,GACLK,IAAI;MACRJ,KAAK,EAAE,CACLW,IAAI,EACJS,MAAM,CAACC,IAAI,EACX;QAAEd,gBAAgB,EAAhBA,gBAAgB;QAAEe,KAAK,EAAEpB,KAAK,CAACqB,MAAM,CAACC;MAAU,CAAC,EACnDxB,KAAK;IACL,EACH,CAAC;EAEN,CAAC,MAAM;IACL,IAAMW,KAAI,GAAG,CAACT,KAAK,CAACQ,IAAI,GAAGR,KAAK,CAACU,KAAK,CAACa,OAAO,GAAGvB,KAAK,CAACU,KAAK,CAACc,UAAU;IACvE,IAAMC,SAAS,GAAAC,aAAA,CAAAA,aAAA,KACVjB,KAAI;MACPW,KAAK,EAAEpB,KAAK,CAACQ,IAAI,GAAGR,KAAK,CAACqB,MAAM,CAACC,SAAS,GAAGtB,KAAK,CAACqB,MAAM,CAACF;IAAA,EAC3D;IACD,OACE9B,KAAA,CAAA0B,aAAA,CAACzB,QAAQ,CAAC0B,IAAI,EAAAC,QAAA;MACZpB,GAAG,EAAEA;IAAI,GACLK,IAAI;MACRJ,KAAK,EAAE,CACLoB,MAAM,CAACC,IAAI,EACXM,SAAS,EACT;QACEpB,gBAAA,EAAAA;MACF,CAAC,EACDP,KAAK;IACL,EACH,CAAC;EAEN;AACF,CACF,CAAC;AAED,IAAMoB,MAAM,GAAG1B,UAAU,CAACmC,MAAM,CAAC;EAC/BR,IAAI,EAAE;IACJS,SAAS,EAAE;EACb;AACF,CAAC,CAAC;AAEF,OAAO,IAAMC,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAA;EAAA,OAC7BlC,YAAgD;AAAA;AAElD,eAAeA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}