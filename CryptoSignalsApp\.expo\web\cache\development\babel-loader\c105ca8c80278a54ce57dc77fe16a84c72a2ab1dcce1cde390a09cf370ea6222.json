{"ast": null, "code": "import React from \"react\";\nimport ActivityIndicator from \"react-native-web/dist/exports/ActivityIndicator\";\nimport View from \"react-native-web/dist/exports/View\";\nimport styles from \"./styles\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nvar Loading = function Loading(_ref) {\n  var color = _ref.color;\n  return _jsx(View, {\n    style: styles.container,\n    children: _jsx(ActivityIndicator, {\n      size: 60,\n      color: color != null ? color : '#FECB37'\n    })\n  });\n};\nexport default Loading;", "map": {"version": 3, "names": ["React", "ActivityIndicator", "View", "styles", "jsx", "_jsx", "Loading", "_ref", "color", "style", "container", "children", "size"], "sources": ["C:/Users/<USER>/Desktop/Signals/CryptoSignalsApp/src/components/Loading/index.js"], "sourcesContent": ["import React from \"react\";\r\nimport { ActivityIndicator, View } from \"react-native\";\r\nimport styles from './styles'\r\n\r\nconst Loading = ({ color }) => {\r\n  return (\r\n    <View style={styles.container}>\r\n      <ActivityIndicator size={60} color={color ?? '#FECB37'} />\r\n    </View>\r\n  )\r\n}\r\n\r\nexport default Loading\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,OAAAC,iBAAA;AAAA,OAAAC,IAAA;AAE1B,OAAOC,MAAM;AAAgB,SAAAC,GAAA,IAAAC,IAAA;AAE7B,IAAMC,OAAO,GAAG,SAAVA,OAAOA,CAAAC,IAAA,EAAkB;EAAA,IAAZC,KAAK,GAAAD,IAAA,CAALC,KAAK;EACtB,OACEH,IAAA,CAACH,IAAI;IAACO,KAAK,EAAEN,MAAM,CAACO,SAAU;IAAAC,QAAA,EAC5BN,IAAA,CAACJ,iBAAiB;MAACW,IAAI,EAAE,EAAG;MAACJ,KAAK,EAAEA,KAAK,WAALA,KAAK,GAAI;IAAU,CAAE;EAAC,CACtD,CAAC;AAEX,CAAC;AAED,eAAeF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}