{"ast": null, "code": "import * as React from 'react';\nimport I18nManager from \"react-native-web/dist/exports/I18nManager\";\nimport Image from \"react-native-web/dist/exports/Image\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport MaterialCommunityIcon from \"../MaterialCommunityIcon\";\nvar AppbarBackIcon = function AppbarBackIcon(_ref) {\n  var size = _ref.size,\n    color = _ref.color;\n  var iosIconSize = size - 3;\n  return Platform.OS === 'ios' ? React.createElement(View, {\n    style: [styles.wrapper, {\n      width: size,\n      height: size,\n      transform: [{\n        scaleX: I18nManager.getConstants().isRTL ? -1 : 1\n      }]\n    }]\n  }, React.createElement(Image, {\n    source: require(\"../../assets/back-chevron.png\"),\n    style: [styles.icon, {\n      tintColor: color,\n      width: iosIconSize,\n      height: iosIconSize\n    }],\n    accessibilityIgnoresInvertColors: true\n  })) : React.createElement(MaterialCommunityIcon, {\n    name: \"arrow-left\",\n    color: color,\n    size: size,\n    direction: I18nManager.getConstants().isRTL ? 'rtl' : 'ltr'\n  });\n};\nvar styles = StyleSheet.create({\n  wrapper: {\n    alignItems: 'center',\n    justifyContent: 'center'\n  },\n  icon: {\n    resizeMode: 'contain'\n  }\n});\nexport default AppbarBackIcon;\nexport { AppbarBackIcon };", "map": {"version": 3, "names": ["React", "I18nManager", "Image", "Platform", "StyleSheet", "View", "MaterialCommunityIcon", "AppbarBackIcon", "_ref", "size", "color", "iosIconSize", "OS", "createElement", "style", "styles", "wrapper", "width", "height", "transform", "scaleX", "getConstants", "isRTL", "source", "require", "icon", "tintColor", "accessibilityIgnoresInvertColors", "name", "direction", "create", "alignItems", "justifyContent", "resizeMode"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\Appbar\\AppbarBackIcon.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { I18nManager, Image, Platform, StyleSheet, View } from 'react-native';\n\nimport MaterialCommunityIcon from '../MaterialCommunityIcon';\n\nconst AppbarBackIcon = ({ size, color }: { size: number; color: string }) => {\n  const iosIconSize = size - 3;\n\n  return Platform.OS === 'ios' ? (\n    <View\n      style={[\n        styles.wrapper,\n        {\n          width: size,\n          height: size,\n          transform: [{ scaleX: I18nManager.getConstants().isRTL ? -1 : 1 }],\n        },\n      ]}\n    >\n      <Image\n        source={require('../../assets/back-chevron.png')}\n        style={[\n          styles.icon,\n          { tintColor: color, width: iosIconSize, height: iosIconSize },\n        ]}\n        accessibilityIgnoresInvertColors\n      />\n    </View>\n  ) : (\n    <MaterialCommunityIcon\n      name=\"arrow-left\"\n      color={color}\n      size={size}\n      direction={I18nManager.getConstants().isRTL ? 'rtl' : 'ltr'}\n    />\n  );\n};\n\nconst styles = StyleSheet.create({\n  wrapper: {\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n  icon: {\n    resizeMode: 'contain',\n  },\n});\n\nexport default AppbarBackIcon;\n\n// @component-docs ignore-next-line\nexport { AppbarBackIcon };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,WAAA;AAAA,OAAAC,KAAA;AAAA,OAAAC,QAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAG9B,OAAOC,qBAAqB;AAE5B,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAAAC,IAAA,EAAyD;EAAA,IAAnDC,IAAI,GAAAD,IAAA,CAAJC,IAAI;IAAEC,KAAA,GAAAF,IAAA,CAAAE,KAAA;EAC9B,IAAMC,WAAW,GAAGF,IAAI,GAAG,CAAC;EAE5B,OAAON,QAAQ,CAACS,EAAE,KAAK,KAAK,GAC1BZ,KAAA,CAAAa,aAAA,CAACR,IAAI;IACHS,KAAK,EAAE,CACLC,MAAM,CAACC,OAAO,EACd;MACEC,KAAK,EAAER,IAAI;MACXS,MAAM,EAAET,IAAI;MACZU,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAEnB,WAAW,CAACoB,YAAY,CAAC,CAAC,CAACC,KAAK,GAAG,CAAC,CAAC,GAAG;MAAE,CAAC;IACnE,CAAC;EACD,GAEFtB,KAAA,CAAAa,aAAA,CAACX,KAAK;IACJqB,MAAM,EAAEC,OAAO,gCAAgC,CAAE;IACjDV,KAAK,EAAE,CACLC,MAAM,CAACU,IAAI,EACX;MAAEC,SAAS,EAAEhB,KAAK;MAAEO,KAAK,EAAEN,WAAW;MAAEO,MAAM,EAAEP;IAAY,CAAC,CAC7D;IACFgB,gCAAgC;EAAA,CACjC,CACG,CAAC,GAEP3B,KAAA,CAAAa,aAAA,CAACP,qBAAqB;IACpBsB,IAAI,EAAC,YAAY;IACjBlB,KAAK,EAAEA,KAAM;IACbD,IAAI,EAAEA,IAAK;IACXoB,SAAS,EAAE5B,WAAW,CAACoB,YAAY,CAAC,CAAC,CAACC,KAAK,GAAG,KAAK,GAAG;EAAM,CAC7D,CACF;AACH,CAAC;AAED,IAAMP,MAAM,GAAGX,UAAU,CAAC0B,MAAM,CAAC;EAC/Bd,OAAO,EAAE;IACPe,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE;EAClB,CAAC;EACDP,IAAI,EAAE;IACJQ,UAAU,EAAE;EACd;AACF,CAAC,CAAC;AAEF,eAAe1B,cAAc;AAG7B,SAASA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}