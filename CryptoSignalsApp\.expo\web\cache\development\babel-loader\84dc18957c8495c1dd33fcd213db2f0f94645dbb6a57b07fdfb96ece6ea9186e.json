{"ast": null, "code": "import invariant from 'fbjs/lib/invariant';\nvar twoArgumentPooler = function twoArgumentPooler(a1, a2) {\n  var Klass = this;\n  if (Klass.instancePool.length) {\n    var instance = Klass.instancePool.pop();\n    Klass.call(instance, a1, a2);\n    return instance;\n  } else {\n    return new Klass(a1, a2);\n  }\n};\nvar standardReleaser = function standardReleaser(instance) {\n  var Klass = this;\n  instance.destructor();\n  if (Klass.instancePool.length < Klass.poolSize) {\n    Klass.instancePool.push(instance);\n  }\n};\nvar DEFAULT_POOL_SIZE = 10;\nvar DEFAULT_POOLER = twoArgumentPooler;\nvar addPoolingTo = function addPoolingTo(CopyConstructor, pooler) {\n  var NewKlass = CopyConstructor;\n  NewKlass.instancePool = [];\n  NewKlass.getPooled = pooler || DEFAULT_POOLER;\n  if (!NewKlass.poolSize) {\n    NewKlass.poolSize = DEFAULT_POOL_SIZE;\n  }\n  NewKlass.release = standardReleaser;\n  return NewKlass;\n};\nvar PooledClass = {\n  addPoolingTo: addPoolingTo,\n  twoArgumentPooler: twoArgumentPooler\n};\nexport default PooledClass;", "map": {"version": 3, "names": ["invariant", "twoArgumentPooler", "a1", "a2", "<PERSON><PERSON>", "instancePool", "length", "instance", "pop", "call", "standardReleaser", "destructor", "poolSize", "push", "DEFAULT_POOL_SIZE", "DEFAULT_POOLER", "addPoolingTo", "CopyConstructor", "pooler", "NewKlass", "getPooled", "release", "PooledClass"], "sources": ["C:/Users/<USER>/Desktop/Signals/CryptoSignalsApp/node_modules/react-native-web/dist/vendor/react-native/PooledClass/index.js"], "sourcesContent": ["/* eslint-disable */\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * From React 16.0.0\n */\n\nimport invariant from 'fbjs/lib/invariant';\nvar twoArgumentPooler = function twoArgumentPooler(a1, a2) {\n  var Klass = this;\n  if (Klass.instancePool.length) {\n    var instance = Klass.instancePool.pop();\n    Klass.call(instance, a1, a2);\n    return instance;\n  } else {\n    return new Klass(a1, a2);\n  }\n};\nvar standardReleaser = function standardReleaser(instance) {\n  var Klass = this;\n  instance.destructor();\n  if (Klass.instancePool.length < Klass.poolSize) {\n    Klass.instancePool.push(instance);\n  }\n};\nvar DEFAULT_POOL_SIZE = 10;\nvar DEFAULT_POOLER = twoArgumentPooler;\n\n/**\n * Augments `CopyConstructor` to be a poolable class, augmenting only the class\n * itself (statically) not adding any prototypical fields. Any CopyConstructor\n * you give this may have a `poolSize` property, and will look for a\n * prototypical `destructor` on instances.\n *\n * @param {Function} CopyConstructor Constructor that can be used to reset.\n * @param {Function} pooler Customizable pooler.\n */\nvar addPoolingTo = function addPoolingTo(CopyConstructor, pooler) {\n  // Casting as any so that flow ignores the actual implementation and trusts\n  // it to match the type we declared\n  var NewKlass = CopyConstructor;\n  NewKlass.instancePool = [];\n  NewKlass.getPooled = pooler || DEFAULT_POOLER;\n  if (!NewKlass.poolSize) {\n    NewKlass.poolSize = DEFAULT_POOL_SIZE;\n  }\n  NewKlass.release = standardReleaser;\n  return NewKlass;\n};\nvar PooledClass = {\n  addPoolingTo: addPoolingTo,\n  twoArgumentPooler: twoArgumentPooler\n};\nexport default PooledClass;"], "mappings": "AAWA,OAAOA,SAAS,MAAM,oBAAoB;AAC1C,IAAIC,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,EAAE,EAAEC,EAAE,EAAE;EACzD,IAAIC,KAAK,GAAG,IAAI;EAChB,IAAIA,KAAK,CAACC,YAAY,CAACC,MAAM,EAAE;IAC7B,IAAIC,QAAQ,GAAGH,KAAK,CAACC,YAAY,CAACG,GAAG,CAAC,CAAC;IACvCJ,KAAK,CAACK,IAAI,CAACF,QAAQ,EAAEL,EAAE,EAAEC,EAAE,CAAC;IAC5B,OAAOI,QAAQ;EACjB,CAAC,MAAM;IACL,OAAO,IAAIH,KAAK,CAACF,EAAE,EAAEC,EAAE,CAAC;EAC1B;AACF,CAAC;AACD,IAAIO,gBAAgB,GAAG,SAASA,gBAAgBA,CAACH,QAAQ,EAAE;EACzD,IAAIH,KAAK,GAAG,IAAI;EAChBG,QAAQ,CAACI,UAAU,CAAC,CAAC;EACrB,IAAIP,KAAK,CAACC,YAAY,CAACC,MAAM,GAAGF,KAAK,CAACQ,QAAQ,EAAE;IAC9CR,KAAK,CAACC,YAAY,CAACQ,IAAI,CAACN,QAAQ,CAAC;EACnC;AACF,CAAC;AACD,IAAIO,iBAAiB,GAAG,EAAE;AAC1B,IAAIC,cAAc,GAAGd,iBAAiB;AAWtC,IAAIe,YAAY,GAAG,SAASA,YAAYA,CAACC,eAAe,EAAEC,MAAM,EAAE;EAGhE,IAAIC,QAAQ,GAAGF,eAAe;EAC9BE,QAAQ,CAACd,YAAY,GAAG,EAAE;EAC1Bc,QAAQ,CAACC,SAAS,GAAGF,MAAM,IAAIH,cAAc;EAC7C,IAAI,CAACI,QAAQ,CAACP,QAAQ,EAAE;IACtBO,QAAQ,CAACP,QAAQ,GAAGE,iBAAiB;EACvC;EACAK,QAAQ,CAACE,OAAO,GAAGX,gBAAgB;EACnC,OAAOS,QAAQ;AACjB,CAAC;AACD,IAAIG,WAAW,GAAG;EAChBN,YAAY,EAAEA,YAAY;EAC1Bf,iBAAiB,EAAEA;AACrB,CAAC;AACD,eAAeqB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}