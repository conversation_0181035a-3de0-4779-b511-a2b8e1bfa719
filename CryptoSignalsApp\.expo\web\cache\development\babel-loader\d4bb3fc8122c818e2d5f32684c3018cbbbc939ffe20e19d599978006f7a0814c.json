{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport React, { useState, useContext, useEffect } from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport TouchableOpacity from \"react-native-web/dist/exports/TouchableOpacity\";\nimport FlatList from \"react-native-web/dist/exports/FlatList\";\nimport Alert from \"react-native-web/dist/exports/Alert\";\nimport ScrollView from \"react-native-web/dist/exports/ScrollView\";\nimport RefreshControl from \"react-native-web/dist/exports/RefreshControl\";\nimport { Button, useTheme, Searchbar } from 'react-native-paper';\nimport PageTitle from \"../../components/PageTitle\";\nimport Wrapper from \"../../components/Wrapper\";\nimport Card from \"../../components/Card\";\nimport StatCard from \"../../components/StatCard\";\nimport { SkeletonCard } from \"../../components/LoadingSkeleton\";\nimport { StoreContext } from \"../../store\";\nimport styles from \"./styles\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar Inicio = function Inicio(_ref) {\n  var navigation = _ref.navigation,\n    _ref$route = _ref.route,\n    route = _ref$route === void 0 ? {} : _ref$route;\n  var _useTheme = useTheme(),\n    colors = _useTheme.colors;\n  var _useContext = useContext(StoreContext),\n    _useContext2 = _slicedToArray(_useContext, 2),\n    state = _useContext2[0],\n    _ = _useContext2[1];\n  var _useState = useState(true),\n    _useState2 = _slicedToArray(_useState, 2),\n    isLoading = _useState2[0],\n    setIsLoading = _useState2[1];\n  var _useState3 = useState(false),\n    _useState4 = _slicedToArray(_useState3, 2),\n    refreshing = _useState4[0],\n    setRefreshing = _useState4[1];\n  var _useState5 = useState(''),\n    _useState6 = _slicedToArray(_useState5, 2),\n    searchQuery = _useState6[0],\n    setSearchQuery = _useState6[1];\n  var _useState7 = useState({\n      totalMarketCap: '$2.1T',\n      totalVolume: '$89.2B',\n      btcDominance: '52.3%',\n      activeSignals: '47'\n    }),\n    _useState8 = _slicedToArray(_useState7, 2),\n    marketStats = _useState8[0],\n    setMarketStats = _useState8[1];\n  var _useState9 = useState([{\n      id: '1',\n      type: 'BUY',\n      pair: 'BTC/USDT',\n      price: '$45,230',\n      target: '$47,500',\n      stopLoss: '$43,800',\n      confidence: 85,\n      timeframe: '4H',\n      likes: 124,\n      comments: 23,\n      timestamp: '2 hours ago',\n      channel: 'Bitcoin Pro Signals',\n      status: 'active'\n    }, {\n      id: '2',\n      type: 'SELL',\n      pair: 'ETH/USDT',\n      price: '$3,185',\n      target: '$3,050',\n      stopLoss: '$3,280',\n      confidence: 78,\n      timeframe: '1H',\n      likes: 89,\n      comments: 15,\n      timestamp: '45 minutes ago',\n      channel: 'Ethereum Signals',\n      status: 'active'\n    }, {\n      id: '3',\n      type: 'BUY',\n      pair: 'SOL/USDT',\n      price: '$95.40',\n      target: '$102.00',\n      stopLoss: '$91.20',\n      confidence: 92,\n      timeframe: '2H',\n      likes: 156,\n      comments: 31,\n      timestamp: '1 hour ago',\n      channel: 'Altcoin Masters',\n      status: 'profit'\n    }]),\n    _useState0 = _slicedToArray(_useState9, 2),\n    signals = _useState0[0],\n    setSignals = _useState0[1];\n  var _useState1 = useState([{\n      symbol: 'BTC',\n      price: '$45,230',\n      change: '+2.34%',\n      changeType: 'positive'\n    }, {\n      symbol: 'ETH',\n      price: '$3,185',\n      change: '+1.87%',\n      changeType: 'positive'\n    }, {\n      symbol: 'SOL',\n      price: '$95.40',\n      change: '+5.23%',\n      changeType: 'positive'\n    }, {\n      symbol: 'ADA',\n      price: '$0.485',\n      change: '-0.92%',\n      changeType: 'negative'\n    }]),\n    _useState10 = _slicedToArray(_useState1, 2),\n    trendingPairs = _useState10[0],\n    setTrendingPairs = _useState10[1];\n  useEffect(function () {\n    setTimeout(function () {\n      setIsLoading(false);\n    }, 1500);\n  }, []);\n  var onRefresh = function onRefresh() {\n    setRefreshing(true);\n    setTimeout(function () {\n      setRefreshing(false);\n    }, 1000);\n  };\n  var handleLike = function handleLike(id) {\n    var updatedSignals = signals.map(function (signal) {\n      if (signal.id === id) {\n        return _objectSpread(_objectSpread({}, signal), {}, {\n          likes: signal.likes + 1\n        });\n      }\n      return signal;\n    });\n    setSignals(updatedSignals);\n  };\n  var handleComment = function handleComment(id) {\n    Alert.alert(\"Comments\", \"Comments feature coming soon!\");\n  };\n  if (isLoading) {\n    return _jsxs(Wrapper, {\n      children: [_jsx(PageTitle, {\n        text: \"Dashboard\"\n      }), _jsxs(ScrollView, {\n        style: {\n          padding: 16\n        },\n        children: [_jsx(SkeletonCard, {}), _jsx(SkeletonCard, {}), _jsx(SkeletonCard, {})]\n      })]\n    });\n  }\n  return _jsx(Wrapper, {\n    children: _jsxs(ScrollView, {\n      style: {\n        flex: 1\n      },\n      refreshControl: _jsx(RefreshControl, {\n        refreshing: refreshing,\n        onRefresh: onRefresh\n      }),\n      children: [_jsxs(View, {\n        style: {\n          padding: 16,\n          paddingBottom: 8\n        },\n        children: [_jsx(Text, {\n          style: {\n            color: '#fff',\n            fontSize: 28,\n            fontFamily: 'Poppins_700Bold',\n            marginBottom: 4\n          },\n          children: \"Dashboard\"\n        }), _jsx(Text, {\n          style: {\n            color: '#8a8a8a',\n            fontSize: 14,\n            fontFamily: 'Poppins_400Regular'\n          },\n          children: \"Welcome back! Here's your trading overview\"\n        })]\n      }), _jsx(View, {\n        style: {\n          paddingHorizontal: 16,\n          marginBottom: 16\n        },\n        children: _jsx(Searchbar, {\n          placeholder: \"Search signals, pairs, channels...\",\n          onChangeText: setSearchQuery,\n          value: searchQuery,\n          style: {\n            backgroundColor: '#2a2a2a',\n            elevation: 0\n          },\n          inputStyle: {\n            color: '#fff'\n          },\n          iconColor: \"#8a8a8a\",\n          placeholderTextColor: \"#8a8a8a\"\n        })\n      }), _jsxs(View, {\n        style: {\n          paddingHorizontal: 8,\n          marginBottom: 16\n        },\n        children: [_jsx(Text, {\n          style: {\n            color: '#fff',\n            fontSize: 18,\n            fontFamily: 'Poppins_600SemiBold',\n            marginBottom: 12,\n            paddingHorizontal: 8\n          },\n          children: \"Market Overview\"\n        }), _jsxs(View, {\n          style: {\n            flexDirection: 'row',\n            flexWrap: 'wrap'\n          },\n          children: [_jsx(StatCard, {\n            title: \"Market Cap\",\n            value: marketStats.totalMarketCap,\n            change: \"+2.4%\",\n            changeType: \"positive\",\n            icon: \"\\uD83D\\uDCCA\"\n          }), _jsx(StatCard, {\n            title: \"24h Volume\",\n            value: marketStats.totalVolume,\n            change: \"+8.1%\",\n            changeType: \"positive\",\n            icon: \"\\uD83D\\uDCB0\"\n          }), _jsx(StatCard, {\n            title: \"BTC Dominance\",\n            value: marketStats.btcDominance,\n            change: \"-0.3%\",\n            changeType: \"negative\",\n            icon: \"\\u20BF\"\n          }), _jsx(StatCard, {\n            title: \"Active Signals\",\n            value: marketStats.activeSignals,\n            subtitle: \"Last 24h\",\n            icon: \"\\uD83D\\uDCE1\"\n          })]\n        })]\n      }), _jsxs(View, {\n        style: {\n          paddingHorizontal: 16,\n          marginBottom: 16\n        },\n        children: [_jsx(Text, {\n          style: {\n            color: '#fff',\n            fontSize: 18,\n            fontFamily: 'Poppins_600SemiBold',\n            marginBottom: 12\n          },\n          children: \"Trending Now \\uD83D\\uDD25\"\n        }), _jsx(ScrollView, {\n          horizontal: true,\n          showsHorizontalScrollIndicator: false,\n          children: trendingPairs.map(function (pair, index) {\n            return _jsxs(Card, {\n              style: {\n                marginRight: 12,\n                minWidth: 120\n              },\n              children: [_jsx(Text, {\n                style: {\n                  color: '#FECB37',\n                  fontSize: 16,\n                  fontFamily: 'Poppins_600SemiBold',\n                  marginBottom: 4\n                },\n                children: pair.symbol\n              }), _jsx(Text, {\n                style: {\n                  color: '#fff',\n                  fontSize: 14,\n                  fontFamily: 'Poppins_500Medium',\n                  marginBottom: 2\n                },\n                children: pair.price\n              }), _jsx(Text, {\n                style: {\n                  color: pair.changeType === 'positive' ? '#4CAF50' : '#F44336',\n                  fontSize: 12,\n                  fontFamily: 'Poppins_500Medium'\n                },\n                children: pair.change\n              })]\n            }, index);\n          })\n        })]\n      }), _jsxs(View, {\n        style: {\n          paddingHorizontal: 16,\n          marginBottom: 16\n        },\n        children: [_jsxs(View, {\n          style: {\n            flexDirection: 'row',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            marginBottom: 12\n          },\n          children: [_jsx(Text, {\n            style: {\n              color: '#fff',\n              fontSize: 18,\n              fontFamily: 'Poppins_600SemiBold'\n            },\n            children: \"Latest Signals \\u26A1\"\n          }), _jsx(TouchableOpacity, {\n            onPress: function onPress() {\n              return navigation == null ? void 0 : navigation.navigate('Channels');\n            },\n            children: _jsx(Text, {\n              style: {\n                color: '#FECB37',\n                fontSize: 14,\n                fontFamily: 'Poppins_500Medium'\n              },\n              children: \"View All\"\n            })\n          })]\n        }), signals.map(function (signal) {\n          return _jsxs(Card, {\n            style: {\n              marginBottom: 12\n            },\n            children: [_jsxs(View, {\n              style: {\n                flexDirection: 'row',\n                justifyContent: 'space-between',\n                alignItems: 'flex-start',\n                marginBottom: 8\n              },\n              children: [_jsxs(View, {\n                style: {\n                  flex: 1\n                },\n                children: [_jsxs(View, {\n                  style: {\n                    flexDirection: 'row',\n                    alignItems: 'center',\n                    marginBottom: 4\n                  },\n                  children: [_jsx(View, {\n                    style: {\n                      backgroundColor: signal.type === 'BUY' ? '#4CAF50' : '#F44336',\n                      paddingHorizontal: 8,\n                      paddingVertical: 2,\n                      borderRadius: 4,\n                      marginRight: 8\n                    },\n                    children: _jsx(Text, {\n                      style: {\n                        color: '#fff',\n                        fontSize: 10,\n                        fontFamily: 'Poppins_600SemiBold'\n                      },\n                      children: signal.type\n                    })\n                  }), _jsx(Text, {\n                    style: {\n                      color: '#FECB37',\n                      fontSize: 16,\n                      fontFamily: 'Poppins_600SemiBold'\n                    },\n                    children: signal.pair\n                  }), _jsx(View, {\n                    style: {\n                      backgroundColor: signal.status === 'profit' ? '#4CAF50' : '#FECB37',\n                      paddingHorizontal: 6,\n                      paddingVertical: 2,\n                      borderRadius: 10,\n                      marginLeft: 8\n                    },\n                    children: _jsx(Text, {\n                      style: {\n                        color: signal.status === 'profit' ? '#fff' : '#000',\n                        fontSize: 8,\n                        fontFamily: 'Poppins_500Medium'\n                      },\n                      children: signal.status.toUpperCase()\n                    })\n                  })]\n                }), _jsxs(Text, {\n                  style: {\n                    color: '#8a8a8a',\n                    fontSize: 12,\n                    fontFamily: 'Poppins_400Regular',\n                    marginBottom: 8\n                  },\n                  children: [signal.channel, \" \\u2022 \", signal.timestamp]\n                })]\n              }), _jsxs(View, {\n                style: {\n                  alignItems: 'flex-end'\n                },\n                children: [_jsx(Text, {\n                  style: {\n                    color: '#fff',\n                    fontSize: 14,\n                    fontFamily: 'Poppins_600SemiBold'\n                  },\n                  children: signal.price\n                }), _jsx(Text, {\n                  style: {\n                    color: '#8a8a8a',\n                    fontSize: 10,\n                    fontFamily: 'Poppins_400Regular'\n                  },\n                  children: signal.timeframe\n                })]\n              })]\n            }), _jsxs(View, {\n              style: {\n                flexDirection: 'row',\n                justifyContent: 'space-between',\n                marginBottom: 12\n              },\n              children: [_jsxs(View, {\n                style: {\n                  flex: 1,\n                  marginRight: 8\n                },\n                children: [_jsx(Text, {\n                  style: {\n                    color: '#8a8a8a',\n                    fontSize: 10,\n                    fontFamily: 'Poppins_400Regular'\n                  },\n                  children: \"Target\"\n                }), _jsx(Text, {\n                  style: {\n                    color: '#4CAF50',\n                    fontSize: 12,\n                    fontFamily: 'Poppins_500Medium'\n                  },\n                  children: signal.target\n                })]\n              }), _jsxs(View, {\n                style: {\n                  flex: 1,\n                  marginRight: 8\n                },\n                children: [_jsx(Text, {\n                  style: {\n                    color: '#8a8a8a',\n                    fontSize: 10,\n                    fontFamily: 'Poppins_400Regular'\n                  },\n                  children: \"Stop Loss\"\n                }), _jsx(Text, {\n                  style: {\n                    color: '#F44336',\n                    fontSize: 12,\n                    fontFamily: 'Poppins_500Medium'\n                  },\n                  children: signal.stopLoss\n                })]\n              }), _jsxs(View, {\n                style: {\n                  flex: 1\n                },\n                children: [_jsx(Text, {\n                  style: {\n                    color: '#8a8a8a',\n                    fontSize: 10,\n                    fontFamily: 'Poppins_400Regular'\n                  },\n                  children: \"Confidence\"\n                }), _jsxs(Text, {\n                  style: {\n                    color: '#FECB37',\n                    fontSize: 12,\n                    fontFamily: 'Poppins_500Medium'\n                  },\n                  children: [signal.confidence, \"%\"]\n                })]\n              })]\n            }), _jsxs(View, {\n              style: {\n                flexDirection: 'row',\n                justifyContent: 'space-between',\n                alignItems: 'center'\n              },\n              children: [_jsxs(View, {\n                style: {\n                  flexDirection: 'row',\n                  alignItems: 'center'\n                },\n                children: [_jsxs(TouchableOpacity, {\n                  onPress: function onPress() {\n                    return handleLike(signal.id);\n                  },\n                  style: {\n                    flexDirection: 'row',\n                    alignItems: 'center',\n                    marginRight: 16\n                  },\n                  children: [_jsx(Text, {\n                    style: {\n                      color: '#FECB37',\n                      marginRight: 4,\n                      fontSize: 14\n                    },\n                    children: \"\\uD83D\\uDC4D\"\n                  }), _jsx(Text, {\n                    style: {\n                      color: '#8a8a8a',\n                      fontSize: 12,\n                      fontFamily: 'Poppins_400Regular'\n                    },\n                    children: signal.likes\n                  })]\n                }), _jsxs(TouchableOpacity, {\n                  onPress: function onPress() {\n                    return handleComment(signal.id);\n                  },\n                  style: {\n                    flexDirection: 'row',\n                    alignItems: 'center'\n                  },\n                  children: [_jsx(Text, {\n                    style: {\n                      color: '#FECB37',\n                      marginRight: 4,\n                      fontSize: 14\n                    },\n                    children: \"\\uD83D\\uDCAC\"\n                  }), _jsx(Text, {\n                    style: {\n                      color: '#8a8a8a',\n                      fontSize: 12,\n                      fontFamily: 'Poppins_400Regular'\n                    },\n                    children: signal.comments\n                  })]\n                })]\n              }), _jsx(TouchableOpacity, {\n                children: _jsx(Text, {\n                  style: {\n                    color: '#FECB37',\n                    fontSize: 12,\n                    fontFamily: 'Poppins_500Medium'\n                  },\n                  children: \"View Details\"\n                })\n              })]\n            })]\n          }, signal.id);\n        })]\n      }), _jsxs(View, {\n        style: {\n          paddingHorizontal: 16,\n          marginBottom: 20\n        },\n        children: [_jsx(Text, {\n          style: {\n            color: '#fff',\n            fontSize: 18,\n            fontFamily: 'Poppins_600SemiBold',\n            marginBottom: 12\n          },\n          children: \"Quick Actions\"\n        }), _jsxs(View, {\n          style: {\n            flexDirection: 'row',\n            justifyContent: 'space-between'\n          },\n          children: [_jsx(Button, {\n            mode: \"contained\",\n            onPress: function onPress() {\n              return navigation == null ? void 0 : navigation.navigate('Channels');\n            },\n            style: {\n              flex: 1,\n              marginRight: 8,\n              backgroundColor: '#FECB37'\n            },\n            labelStyle: {\n              color: '#000',\n              fontFamily: 'Poppins_500Medium'\n            },\n            children: \"View Channels\"\n          }), _jsx(Button, {\n            mode: \"outlined\",\n            onPress: function onPress() {\n              return navigation == null ? void 0 : navigation.navigate('Premium');\n            },\n            style: {\n              flex: 1,\n              marginLeft: 8,\n              borderColor: '#FECB37'\n            },\n            labelStyle: {\n              color: '#FECB37',\n              fontFamily: 'Poppins_500Medium'\n            },\n            children: \"Go Premium\"\n          })]\n        })]\n      })]\n    })\n  });\n};\nexport default Inicio;", "map": {"version": 3, "names": ["React", "useState", "useContext", "useEffect", "View", "Text", "TouchableOpacity", "FlatList", "<PERSON><PERSON>", "ScrollView", "RefreshControl", "<PERSON><PERSON>", "useTheme", "Searchbar", "Page<PERSON><PERSON>le", "Wrapper", "Card", "StatCard", "SkeletonCard", "StoreContext", "styles", "jsx", "_jsx", "jsxs", "_jsxs", "<PERSON><PERSON>o", "_ref", "navigation", "_ref$route", "route", "_useTheme", "colors", "_useContext", "_useContext2", "_slicedToArray", "state", "_", "_useState", "_useState2", "isLoading", "setIsLoading", "_useState3", "_useState4", "refreshing", "setRefreshing", "_useState5", "_useState6", "searchQuery", "setSearch<PERSON>uery", "_useState7", "totalMarketCap", "totalVolume", "btcDominance", "activeSignals", "_useState8", "marketStats", "setMarketStats", "_useState9", "id", "type", "pair", "price", "target", "stopLoss", "confidence", "timeframe", "likes", "comments", "timestamp", "channel", "status", "_useState0", "signals", "setSignals", "_useState1", "symbol", "change", "changeType", "_useState10", "trendingPairs", "setTrendingPairs", "setTimeout", "onRefresh", "handleLike", "updatedSignals", "map", "signal", "_objectSpread", "handleComment", "alert", "children", "text", "style", "padding", "flex", "refreshControl", "paddingBottom", "color", "fontSize", "fontFamily", "marginBottom", "paddingHorizontal", "placeholder", "onChangeText", "value", "backgroundColor", "elevation", "inputStyle", "iconColor", "placeholderTextColor", "flexDirection", "flexWrap", "title", "icon", "subtitle", "horizontal", "showsHorizontalScrollIndicator", "index", "marginRight", "min<PERSON><PERSON><PERSON>", "justifyContent", "alignItems", "onPress", "navigate", "paddingVertical", "borderRadius", "marginLeft", "toUpperCase", "mode", "labelStyle", "borderColor"], "sources": ["E:/CryptoSignalsApp/src/pages/Home/index.js"], "sourcesContent": ["import React, { useState, useContext, useEffect } from 'react';\r\nimport { View, Text, TouchableOpacity, FlatList, Alert, ScrollView, RefreshControl } from 'react-native';\r\nimport { Button, useTheme, Searchbar } from 'react-native-paper';\r\nimport PageTitle from '../../components/PageTitle';\r\nimport Wrapper from '../../components/Wrapper';\r\nimport Card from '../../components/Card';\r\nimport StatCard from '../../components/StatCard';\r\nimport { SkeletonCard } from '../../components/LoadingSkeleton';\r\nimport { StoreContext } from '../../store';\r\nimport styles from './styles';\r\n\r\nconst Inicio = ({ navigation, route = {} }) => {\r\n    const { colors } = useTheme();\r\n    const [state, _] = useContext(StoreContext);\r\n    const [isLoading, setIsLoading] = useState(true);\r\n    const [refreshing, setRefreshing] = useState(false);\r\n    const [searchQuery, setSearchQuery] = useState('');\r\n\r\n    // Market stats\r\n    const [marketStats, setMarketStats] = useState({\r\n        totalMarketCap: '$2.1T',\r\n        totalVolume: '$89.2B',\r\n        btcDominance: '52.3%',\r\n        activeSignals: '47'\r\n    });\r\n\r\n    // Enhanced signals data\r\n    const [signals, setSignals] = useState([\r\n        {\r\n            id: '1',\r\n            type: 'BUY',\r\n            pair: 'BTC/USDT',\r\n            price: '$45,230',\r\n            target: '$47,500',\r\n            stopLoss: '$43,800',\r\n            confidence: 85,\r\n            timeframe: '4H',\r\n            likes: 124,\r\n            comments: 23,\r\n            timestamp: '2 hours ago',\r\n            channel: 'Bitcoin Pro Signals',\r\n            status: 'active'\r\n        },\r\n        {\r\n            id: '2',\r\n            type: 'SELL',\r\n            pair: 'ETH/USDT',\r\n            price: '$3,185',\r\n            target: '$3,050',\r\n            stopLoss: '$3,280',\r\n            confidence: 78,\r\n            timeframe: '1H',\r\n            likes: 89,\r\n            comments: 15,\r\n            timestamp: '45 minutes ago',\r\n            channel: 'Ethereum Signals',\r\n            status: 'active'\r\n        },\r\n        {\r\n            id: '3',\r\n            type: 'BUY',\r\n            pair: 'SOL/USDT',\r\n            price: '$95.40',\r\n            target: '$102.00',\r\n            stopLoss: '$91.20',\r\n            confidence: 92,\r\n            timeframe: '2H',\r\n            likes: 156,\r\n            comments: 31,\r\n            timestamp: '1 hour ago',\r\n            channel: 'Altcoin Masters',\r\n            status: 'profit'\r\n        },\r\n    ]);\r\n\r\n    // Trending pairs\r\n    const [trendingPairs, setTrendingPairs] = useState([\r\n        { symbol: 'BTC', price: '$45,230', change: '+2.34%', changeType: 'positive' },\r\n        { symbol: 'ETH', price: '$3,185', change: '+1.87%', changeType: 'positive' },\r\n        { symbol: 'SOL', price: '$95.40', change: '+5.23%', changeType: 'positive' },\r\n        { symbol: 'ADA', price: '$0.485', change: '-0.92%', changeType: 'negative' },\r\n    ]);\r\n\r\n    useEffect(() => {\r\n        // Simulate loading\r\n        setTimeout(() => {\r\n            setIsLoading(false);\r\n        }, 1500);\r\n    }, []);\r\n\r\n    const onRefresh = () => {\r\n        setRefreshing(true);\r\n        // Simulate refresh\r\n        setTimeout(() => {\r\n            setRefreshing(false);\r\n        }, 1000);\r\n    };\r\n\r\n    const handleLike = (id) => {\r\n        const updatedSignals = signals.map((signal) => {\r\n            if (signal.id === id) {\r\n                return { ...signal, likes: signal.likes + 1 };\r\n            }\r\n            return signal;\r\n        });\r\n        setSignals(updatedSignals);\r\n    };\r\n\r\n    const handleComment = (id) => {\r\n        Alert.alert(\"Comments\", \"Comments feature coming soon!\");\r\n    };\r\n\r\n\r\n    if (isLoading) {\r\n        return (\r\n            <Wrapper>\r\n                <PageTitle text=\"Dashboard\" />\r\n                <ScrollView style={{ padding: 16 }}>\r\n                    <SkeletonCard />\r\n                    <SkeletonCard />\r\n                    <SkeletonCard />\r\n                </ScrollView>\r\n            </Wrapper>\r\n        );\r\n    }\r\n\r\n    return (\r\n        <Wrapper>\r\n            <ScrollView\r\n                style={{ flex: 1 }}\r\n                refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}\r\n            >\r\n                {/* Header */}\r\n                <View style={{ padding: 16, paddingBottom: 8 }}>\r\n                    <Text style={{\r\n                        color: '#fff',\r\n                        fontSize: 28,\r\n                        fontFamily: 'Poppins_700Bold',\r\n                        marginBottom: 4\r\n                    }}>\r\n                        Dashboard\r\n                    </Text>\r\n                    <Text style={{\r\n                        color: '#8a8a8a',\r\n                        fontSize: 14,\r\n                        fontFamily: 'Poppins_400Regular'\r\n                    }}>\r\n                        Welcome back! Here's your trading overview\r\n                    </Text>\r\n                </View>\r\n\r\n                {/* Search Bar */}\r\n                <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>\r\n                    <Searchbar\r\n                        placeholder=\"Search signals, pairs, channels...\"\r\n                        onChangeText={setSearchQuery}\r\n                        value={searchQuery}\r\n                        style={{ backgroundColor: '#2a2a2a', elevation: 0 }}\r\n                        inputStyle={{ color: '#fff' }}\r\n                        iconColor=\"#8a8a8a\"\r\n                        placeholderTextColor=\"#8a8a8a\"\r\n                    />\r\n                </View>\r\n\r\n                {/* Market Stats */}\r\n                <View style={{ paddingHorizontal: 8, marginBottom: 16 }}>\r\n                    <Text style={{\r\n                        color: '#fff',\r\n                        fontSize: 18,\r\n                        fontFamily: 'Poppins_600SemiBold',\r\n                        marginBottom: 12,\r\n                        paddingHorizontal: 8\r\n                    }}>\r\n                        Market Overview\r\n                    </Text>\r\n                    <View style={{ flexDirection: 'row', flexWrap: 'wrap' }}>\r\n                        <StatCard\r\n                            title=\"Market Cap\"\r\n                            value={marketStats.totalMarketCap}\r\n                            change=\"+2.4%\"\r\n                            changeType=\"positive\"\r\n                            icon=\"📊\"\r\n                        />\r\n                        <StatCard\r\n                            title=\"24h Volume\"\r\n                            value={marketStats.totalVolume}\r\n                            change=\"+8.1%\"\r\n                            changeType=\"positive\"\r\n                            icon=\"💰\"\r\n                        />\r\n                        <StatCard\r\n                            title=\"BTC Dominance\"\r\n                            value={marketStats.btcDominance}\r\n                            change=\"-0.3%\"\r\n                            changeType=\"negative\"\r\n                            icon=\"₿\"\r\n                        />\r\n                        <StatCard\r\n                            title=\"Active Signals\"\r\n                            value={marketStats.activeSignals}\r\n                            subtitle=\"Last 24h\"\r\n                            icon=\"📡\"\r\n                        />\r\n                    </View>\r\n                </View>\r\n\r\n                {/* Trending Pairs */}\r\n                <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>\r\n                    <Text style={{\r\n                        color: '#fff',\r\n                        fontSize: 18,\r\n                        fontFamily: 'Poppins_600SemiBold',\r\n                        marginBottom: 12\r\n                    }}>\r\n                        Trending Now 🔥\r\n                    </Text>\r\n                    <ScrollView horizontal showsHorizontalScrollIndicator={false}>\r\n                        {trendingPairs.map((pair, index) => (\r\n                            <Card key={index} style={{ marginRight: 12, minWidth: 120 }}>\r\n                                <Text style={{\r\n                                    color: '#FECB37',\r\n                                    fontSize: 16,\r\n                                    fontFamily: 'Poppins_600SemiBold',\r\n                                    marginBottom: 4\r\n                                }}>\r\n                                    {pair.symbol}\r\n                                </Text>\r\n                                <Text style={{\r\n                                    color: '#fff',\r\n                                    fontSize: 14,\r\n                                    fontFamily: 'Poppins_500Medium',\r\n                                    marginBottom: 2\r\n                                }}>\r\n                                    {pair.price}\r\n                                </Text>\r\n                                <Text style={{\r\n                                    color: pair.changeType === 'positive' ? '#4CAF50' : '#F44336',\r\n                                    fontSize: 12,\r\n                                    fontFamily: 'Poppins_500Medium'\r\n                                }}>\r\n                                    {pair.change}\r\n                                </Text>\r\n                            </Card>\r\n                        ))}\r\n                    </ScrollView>\r\n                </View>\r\n\r\n                {/* Latest Signals */}\r\n                <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>\r\n                    <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 12 }}>\r\n                        <Text style={{\r\n                            color: '#fff',\r\n                            fontSize: 18,\r\n                            fontFamily: 'Poppins_600SemiBold'\r\n                        }}>\r\n                            Latest Signals ⚡\r\n                        </Text>\r\n                        <TouchableOpacity onPress={() => navigation?.navigate('Channels')}>\r\n                            <Text style={{\r\n                                color: '#FECB37',\r\n                                fontSize: 14,\r\n                                fontFamily: 'Poppins_500Medium'\r\n                            }}>\r\n                                View All\r\n                            </Text>\r\n                        </TouchableOpacity>\r\n                    </View>\r\n\r\n                    {signals.map((signal) => (\r\n                        <Card key={signal.id} style={{ marginBottom: 12 }}>\r\n                            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: 8 }}>\r\n                                <View style={{ flex: 1 }}>\r\n                                    <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 4 }}>\r\n                                        <View style={{\r\n                                            backgroundColor: signal.type === 'BUY' ? '#4CAF50' : '#F44336',\r\n                                            paddingHorizontal: 8,\r\n                                            paddingVertical: 2,\r\n                                            borderRadius: 4,\r\n                                            marginRight: 8\r\n                                        }}>\r\n                                            <Text style={{\r\n                                                color: '#fff',\r\n                                                fontSize: 10,\r\n                                                fontFamily: 'Poppins_600SemiBold'\r\n                                            }}>\r\n                                                {signal.type}\r\n                                            </Text>\r\n                                        </View>\r\n                                        <Text style={{\r\n                                            color: '#FECB37',\r\n                                            fontSize: 16,\r\n                                            fontFamily: 'Poppins_600SemiBold'\r\n                                        }}>\r\n                                            {signal.pair}\r\n                                        </Text>\r\n                                        <View style={{\r\n                                            backgroundColor: signal.status === 'profit' ? '#4CAF50' : '#FECB37',\r\n                                            paddingHorizontal: 6,\r\n                                            paddingVertical: 2,\r\n                                            borderRadius: 10,\r\n                                            marginLeft: 8\r\n                                        }}>\r\n                                            <Text style={{\r\n                                                color: signal.status === 'profit' ? '#fff' : '#000',\r\n                                                fontSize: 8,\r\n                                                fontFamily: 'Poppins_500Medium'\r\n                                            }}>\r\n                                                {signal.status.toUpperCase()}\r\n                                            </Text>\r\n                                        </View>\r\n                                    </View>\r\n                                    <Text style={{\r\n                                        color: '#8a8a8a',\r\n                                        fontSize: 12,\r\n                                        fontFamily: 'Poppins_400Regular',\r\n                                        marginBottom: 8\r\n                                    }}>\r\n                                        {signal.channel} • {signal.timestamp}\r\n                                    </Text>\r\n                                </View>\r\n                                <View style={{ alignItems: 'flex-end' }}>\r\n                                    <Text style={{\r\n                                        color: '#fff',\r\n                                        fontSize: 14,\r\n                                        fontFamily: 'Poppins_600SemiBold'\r\n                                    }}>\r\n                                        {signal.price}\r\n                                    </Text>\r\n                                    <Text style={{\r\n                                        color: '#8a8a8a',\r\n                                        fontSize: 10,\r\n                                        fontFamily: 'Poppins_400Regular'\r\n                                    }}>\r\n                                        {signal.timeframe}\r\n                                    </Text>\r\n                                </View>\r\n                            </View>\r\n\r\n                            <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 12 }}>\r\n                                <View style={{ flex: 1, marginRight: 8 }}>\r\n                                    <Text style={{ color: '#8a8a8a', fontSize: 10, fontFamily: 'Poppins_400Regular' }}>Target</Text>\r\n                                    <Text style={{ color: '#4CAF50', fontSize: 12, fontFamily: 'Poppins_500Medium' }}>{signal.target}</Text>\r\n                                </View>\r\n                                <View style={{ flex: 1, marginRight: 8 }}>\r\n                                    <Text style={{ color: '#8a8a8a', fontSize: 10, fontFamily: 'Poppins_400Regular' }}>Stop Loss</Text>\r\n                                    <Text style={{ color: '#F44336', fontSize: 12, fontFamily: 'Poppins_500Medium' }}>{signal.stopLoss}</Text>\r\n                                </View>\r\n                                <View style={{ flex: 1 }}>\r\n                                    <Text style={{ color: '#8a8a8a', fontSize: 10, fontFamily: 'Poppins_400Regular' }}>Confidence</Text>\r\n                                    <Text style={{ color: '#FECB37', fontSize: 12, fontFamily: 'Poppins_500Medium' }}>{signal.confidence}%</Text>\r\n                                </View>\r\n                            </View>\r\n\r\n                            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>\r\n                                <View style={{ flexDirection: 'row', alignItems: 'center' }}>\r\n                                    <TouchableOpacity\r\n                                        onPress={() => handleLike(signal.id)}\r\n                                        style={{ flexDirection: 'row', alignItems: 'center', marginRight: 16 }}\r\n                                    >\r\n                                        <Text style={{ color: '#FECB37', marginRight: 4, fontSize: 14 }}>👍</Text>\r\n                                        <Text style={{ color: '#8a8a8a', fontSize: 12, fontFamily: 'Poppins_400Regular' }}>{signal.likes}</Text>\r\n                                    </TouchableOpacity>\r\n                                    <TouchableOpacity\r\n                                        onPress={() => handleComment(signal.id)}\r\n                                        style={{ flexDirection: 'row', alignItems: 'center' }}\r\n                                    >\r\n                                        <Text style={{ color: '#FECB37', marginRight: 4, fontSize: 14 }}>💬</Text>\r\n                                        <Text style={{ color: '#8a8a8a', fontSize: 12, fontFamily: 'Poppins_400Regular' }}>{signal.comments}</Text>\r\n                                    </TouchableOpacity>\r\n                                </View>\r\n                                <TouchableOpacity>\r\n                                    <Text style={{ color: '#FECB37', fontSize: 12, fontFamily: 'Poppins_500Medium' }}>View Details</Text>\r\n                                </TouchableOpacity>\r\n                            </View>\r\n                        </Card>\r\n                    ))}\r\n                </View>\r\n\r\n                {/* Quick Actions */}\r\n                <View style={{ paddingHorizontal: 16, marginBottom: 20 }}>\r\n                    <Text style={{\r\n                        color: '#fff',\r\n                        fontSize: 18,\r\n                        fontFamily: 'Poppins_600SemiBold',\r\n                        marginBottom: 12\r\n                    }}>\r\n                        Quick Actions\r\n                    </Text>\r\n                    <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>\r\n                        <Button\r\n                            mode=\"contained\"\r\n                            onPress={() => navigation?.navigate('Channels')}\r\n                            style={{ flex: 1, marginRight: 8, backgroundColor: '#FECB37' }}\r\n                            labelStyle={{ color: '#000', fontFamily: 'Poppins_500Medium' }}\r\n                        >\r\n                            View Channels\r\n                        </Button>\r\n                        <Button\r\n                            mode=\"outlined\"\r\n                            onPress={() => navigation?.navigate('Premium')}\r\n                            style={{ flex: 1, marginLeft: 8, borderColor: '#FECB37' }}\r\n                            labelStyle={{ color: '#FECB37', fontFamily: 'Poppins_500Medium' }}\r\n                        >\r\n                            Go Premium\r\n                        </Button>\r\n                    </View>\r\n                </View>\r\n            </ScrollView>\r\n        </Wrapper>\r\n    );\r\n};\r\n\r\nexport default Inicio;\r\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,gBAAA;AAAA,OAAAC,QAAA;AAAA,OAAAC,KAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,cAAA;AAE/D,SAASC,MAAM,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,oBAAoB;AAChE,OAAOC,SAAS;AAChB,OAAOC,OAAO;AACd,OAAOC,IAAI;AACX,OAAOC,QAAQ;AACf,SAASC,YAAY;AACrB,SAASC,YAAY;AACrB,OAAOC,MAAM;AAAiB,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAE9B,IAAMC,MAAM,GAAG,SAATA,MAAMA,CAAAC,IAAA,EAAmC;EAAA,IAA7BC,UAAU,GAAAD,IAAA,CAAVC,UAAU;IAAAC,UAAA,GAAAF,IAAA,CAAEG,KAAK;IAALA,KAAK,GAAAD,UAAA,cAAG,CAAC,CAAC,GAAAA,UAAA;EACpC,IAAAE,SAAA,GAAmBlB,QAAQ,CAAC,CAAC;IAArBmB,MAAM,GAAAD,SAAA,CAANC,MAAM;EACd,IAAAC,WAAA,GAAmB9B,UAAU,CAACiB,YAAY,CAAC;IAAAc,YAAA,GAAAC,cAAA,CAAAF,WAAA;IAApCG,KAAK,GAAAF,YAAA;IAAEG,CAAC,GAAAH,YAAA;EACf,IAAAI,SAAA,GAAkCpC,QAAQ,CAAC,IAAI,CAAC;IAAAqC,UAAA,GAAAJ,cAAA,CAAAG,SAAA;IAAzCE,SAAS,GAAAD,UAAA;IAAEE,YAAY,GAAAF,UAAA;EAC9B,IAAAG,UAAA,GAAoCxC,QAAQ,CAAC,KAAK,CAAC;IAAAyC,UAAA,GAAAR,cAAA,CAAAO,UAAA;IAA5CE,UAAU,GAAAD,UAAA;IAAEE,aAAa,GAAAF,UAAA;EAChC,IAAAG,UAAA,GAAsC5C,QAAQ,CAAC,EAAE,CAAC;IAAA6C,UAAA,GAAAZ,cAAA,CAAAW,UAAA;IAA3CE,WAAW,GAAAD,UAAA;IAAEE,cAAc,GAAAF,UAAA;EAGlC,IAAAG,UAAA,GAAsChD,QAAQ,CAAC;MAC3CiD,cAAc,EAAE,OAAO;MACvBC,WAAW,EAAE,QAAQ;MACrBC,YAAY,EAAE,OAAO;MACrBC,aAAa,EAAE;IACnB,CAAC,CAAC;IAAAC,UAAA,GAAApB,cAAA,CAAAe,UAAA;IALKM,WAAW,GAAAD,UAAA;IAAEE,cAAc,GAAAF,UAAA;EAQlC,IAAAG,UAAA,GAA8BxD,QAAQ,CAAC,CACnC;MACIyD,EAAE,EAAE,GAAG;MACPC,IAAI,EAAE,KAAK;MACXC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,SAAS;MAChBC,MAAM,EAAE,SAAS;MACjBC,QAAQ,EAAE,SAAS;MACnBC,UAAU,EAAE,EAAE;MACdC,SAAS,EAAE,IAAI;MACfC,KAAK,EAAE,GAAG;MACVC,QAAQ,EAAE,EAAE;MACZC,SAAS,EAAE,aAAa;MACxBC,OAAO,EAAE,qBAAqB;MAC9BC,MAAM,EAAE;IACZ,CAAC,EACD;MACIZ,EAAE,EAAE,GAAG;MACPC,IAAI,EAAE,MAAM;MACZC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,QAAQ;MACfC,MAAM,EAAE,QAAQ;MAChBC,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE,EAAE;MACdC,SAAS,EAAE,IAAI;MACfC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,EAAE;MACZC,SAAS,EAAE,gBAAgB;MAC3BC,OAAO,EAAE,kBAAkB;MAC3BC,MAAM,EAAE;IACZ,CAAC,EACD;MACIZ,EAAE,EAAE,GAAG;MACPC,IAAI,EAAE,KAAK;MACXC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,QAAQ;MACfC,MAAM,EAAE,SAAS;MACjBC,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE,EAAE;MACdC,SAAS,EAAE,IAAI;MACfC,KAAK,EAAE,GAAG;MACVC,QAAQ,EAAE,EAAE;MACZC,SAAS,EAAE,YAAY;MACvBC,OAAO,EAAE,iBAAiB;MAC1BC,MAAM,EAAE;IACZ,CAAC,CACJ,CAAC;IAAAC,UAAA,GAAArC,cAAA,CAAAuB,UAAA;IA9CKe,OAAO,GAAAD,UAAA;IAAEE,UAAU,GAAAF,UAAA;EAiD1B,IAAAG,UAAA,GAA0CzE,QAAQ,CAAC,CAC/C;MAAE0E,MAAM,EAAE,KAAK;MAAEd,KAAK,EAAE,SAAS;MAAEe,MAAM,EAAE,QAAQ;MAAEC,UAAU,EAAE;IAAW,CAAC,EAC7E;MAAEF,MAAM,EAAE,KAAK;MAAEd,KAAK,EAAE,QAAQ;MAAEe,MAAM,EAAE,QAAQ;MAAEC,UAAU,EAAE;IAAW,CAAC,EAC5E;MAAEF,MAAM,EAAE,KAAK;MAAEd,KAAK,EAAE,QAAQ;MAAEe,MAAM,EAAE,QAAQ;MAAEC,UAAU,EAAE;IAAW,CAAC,EAC5E;MAAEF,MAAM,EAAE,KAAK;MAAEd,KAAK,EAAE,QAAQ;MAAEe,MAAM,EAAE,QAAQ;MAAEC,UAAU,EAAE;IAAW,CAAC,CAC/E,CAAC;IAAAC,WAAA,GAAA5C,cAAA,CAAAwC,UAAA;IALKK,aAAa,GAAAD,WAAA;IAAEE,gBAAgB,GAAAF,WAAA;EAOtC3E,SAAS,CAAC,YAAM;IAEZ8E,UAAU,CAAC,YAAM;MACbzC,YAAY,CAAC,KAAK,CAAC;IACvB,CAAC,EAAE,IAAI,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;EAEN,IAAM0C,SAAS,GAAG,SAAZA,SAASA,CAAA,EAAS;IACpBtC,aAAa,CAAC,IAAI,CAAC;IAEnBqC,UAAU,CAAC,YAAM;MACbrC,aAAa,CAAC,KAAK,CAAC;IACxB,CAAC,EAAE,IAAI,CAAC;EACZ,CAAC;EAED,IAAMuC,UAAU,GAAG,SAAbA,UAAUA,CAAIzB,EAAE,EAAK;IACvB,IAAM0B,cAAc,GAAGZ,OAAO,CAACa,GAAG,CAAC,UAACC,MAAM,EAAK;MAC3C,IAAIA,MAAM,CAAC5B,EAAE,KAAKA,EAAE,EAAE;QAClB,OAAA6B,aAAA,CAAAA,aAAA,KAAYD,MAAM;UAAEpB,KAAK,EAAEoB,MAAM,CAACpB,KAAK,GAAG;QAAC;MAC/C;MACA,OAAOoB,MAAM;IACjB,CAAC,CAAC;IACFb,UAAU,CAACW,cAAc,CAAC;EAC9B,CAAC;EAED,IAAMI,aAAa,GAAG,SAAhBA,aAAaA,CAAI9B,EAAE,EAAK;IAC1BlD,KAAK,CAACiF,KAAK,CAAC,UAAU,EAAE,+BAA+B,CAAC;EAC5D,CAAC;EAGD,IAAIlD,SAAS,EAAE;IACX,OACIf,KAAA,CAACT,OAAO;MAAA2E,QAAA,GACJpE,IAAA,CAACR,SAAS;QAAC6E,IAAI,EAAC;MAAW,CAAE,CAAC,EAC9BnE,KAAA,CAACf,UAAU;QAACmF,KAAK,EAAE;UAAEC,OAAO,EAAE;QAAG,CAAE;QAAAH,QAAA,GAC/BpE,IAAA,CAACJ,YAAY,IAAE,CAAC,EAChBI,IAAA,CAACJ,YAAY,IAAE,CAAC,EAChBI,IAAA,CAACJ,YAAY,IAAE,CAAC;MAAA,CACR,CAAC;IAAA,CACR,CAAC;EAElB;EAEA,OACII,IAAA,CAACP,OAAO;IAAA2E,QAAA,EACJlE,KAAA,CAACf,UAAU;MACPmF,KAAK,EAAE;QAAEE,IAAI,EAAE;MAAE,CAAE;MACnBC,cAAc,EAAEzE,IAAA,CAACZ,cAAc;QAACiC,UAAU,EAAEA,UAAW;QAACuC,SAAS,EAAEA;MAAU,CAAE,CAAE;MAAAQ,QAAA,GAGjFlE,KAAA,CAACpB,IAAI;QAACwF,KAAK,EAAE;UAAEC,OAAO,EAAE,EAAE;UAAEG,aAAa,EAAE;QAAE,CAAE;QAAAN,QAAA,GAC3CpE,IAAA,CAACjB,IAAI;UAACuF,KAAK,EAAE;YACTK,KAAK,EAAE,MAAM;YACbC,QAAQ,EAAE,EAAE;YACZC,UAAU,EAAE,iBAAiB;YAC7BC,YAAY,EAAE;UAClB,CAAE;UAAAV,QAAA,EAAC;QAEH,CAAM,CAAC,EACPpE,IAAA,CAACjB,IAAI;UAACuF,KAAK,EAAE;YACTK,KAAK,EAAE,SAAS;YAChBC,QAAQ,EAAE,EAAE;YACZC,UAAU,EAAE;UAChB,CAAE;UAAAT,QAAA,EAAC;QAEH,CAAM,CAAC;MAAA,CACL,CAAC,EAGPpE,IAAA,CAAClB,IAAI;QAACwF,KAAK,EAAE;UAAES,iBAAiB,EAAE,EAAE;UAAED,YAAY,EAAE;QAAG,CAAE;QAAAV,QAAA,EACrDpE,IAAA,CAACT,SAAS;UACNyF,WAAW,EAAC,oCAAoC;UAChDC,YAAY,EAAEvD,cAAe;UAC7BwD,KAAK,EAAEzD,WAAY;UACnB6C,KAAK,EAAE;YAAEa,eAAe,EAAE,SAAS;YAAEC,SAAS,EAAE;UAAE,CAAE;UACpDC,UAAU,EAAE;YAAEV,KAAK,EAAE;UAAO,CAAE;UAC9BW,SAAS,EAAC,SAAS;UACnBC,oBAAoB,EAAC;QAAS,CACjC;MAAC,CACA,CAAC,EAGPrF,KAAA,CAACpB,IAAI;QAACwF,KAAK,EAAE;UAAES,iBAAiB,EAAE,CAAC;UAAED,YAAY,EAAE;QAAG,CAAE;QAAAV,QAAA,GACpDpE,IAAA,CAACjB,IAAI;UAACuF,KAAK,EAAE;YACTK,KAAK,EAAE,MAAM;YACbC,QAAQ,EAAE,EAAE;YACZC,UAAU,EAAE,qBAAqB;YACjCC,YAAY,EAAE,EAAE;YAChBC,iBAAiB,EAAE;UACvB,CAAE;UAAAX,QAAA,EAAC;QAEH,CAAM,CAAC,EACPlE,KAAA,CAACpB,IAAI;UAACwF,KAAK,EAAE;YAAEkB,aAAa,EAAE,KAAK;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAArB,QAAA,GACpDpE,IAAA,CAACL,QAAQ;YACL+F,KAAK,EAAC,YAAY;YAClBR,KAAK,EAAEjD,WAAW,CAACL,cAAe;YAClC0B,MAAM,EAAC,OAAO;YACdC,UAAU,EAAC,UAAU;YACrBoC,IAAI,EAAC;UAAI,CACZ,CAAC,EACF3F,IAAA,CAACL,QAAQ;YACL+F,KAAK,EAAC,YAAY;YAClBR,KAAK,EAAEjD,WAAW,CAACJ,WAAY;YAC/ByB,MAAM,EAAC,OAAO;YACdC,UAAU,EAAC,UAAU;YACrBoC,IAAI,EAAC;UAAI,CACZ,CAAC,EACF3F,IAAA,CAACL,QAAQ;YACL+F,KAAK,EAAC,eAAe;YACrBR,KAAK,EAAEjD,WAAW,CAACH,YAAa;YAChCwB,MAAM,EAAC,OAAO;YACdC,UAAU,EAAC,UAAU;YACrBoC,IAAI,EAAC;UAAG,CACX,CAAC,EACF3F,IAAA,CAACL,QAAQ;YACL+F,KAAK,EAAC,gBAAgB;YACtBR,KAAK,EAAEjD,WAAW,CAACF,aAAc;YACjC6D,QAAQ,EAAC,UAAU;YACnBD,IAAI,EAAC;UAAI,CACZ,CAAC;QAAA,CACA,CAAC;MAAA,CACL,CAAC,EAGPzF,KAAA,CAACpB,IAAI;QAACwF,KAAK,EAAE;UAAES,iBAAiB,EAAE,EAAE;UAAED,YAAY,EAAE;QAAG,CAAE;QAAAV,QAAA,GACrDpE,IAAA,CAACjB,IAAI;UAACuF,KAAK,EAAE;YACTK,KAAK,EAAE,MAAM;YACbC,QAAQ,EAAE,EAAE;YACZC,UAAU,EAAE,qBAAqB;YACjCC,YAAY,EAAE;UAClB,CAAE;UAAAV,QAAA,EAAC;QAEH,CAAM,CAAC,EACPpE,IAAA,CAACb,UAAU;UAAC0G,UAAU;UAACC,8BAA8B,EAAE,KAAM;UAAA1B,QAAA,EACxDX,aAAa,CAACM,GAAG,CAAC,UAACzB,IAAI,EAAEyD,KAAK;YAAA,OAC3B7F,KAAA,CAACR,IAAI;cAAa4E,KAAK,EAAE;gBAAE0B,WAAW,EAAE,EAAE;gBAAEC,QAAQ,EAAE;cAAI,CAAE;cAAA7B,QAAA,GACxDpE,IAAA,CAACjB,IAAI;gBAACuF,KAAK,EAAE;kBACTK,KAAK,EAAE,SAAS;kBAChBC,QAAQ,EAAE,EAAE;kBACZC,UAAU,EAAE,qBAAqB;kBACjCC,YAAY,EAAE;gBAClB,CAAE;gBAAAV,QAAA,EACG9B,IAAI,CAACe;cAAM,CACV,CAAC,EACPrD,IAAA,CAACjB,IAAI;gBAACuF,KAAK,EAAE;kBACTK,KAAK,EAAE,MAAM;kBACbC,QAAQ,EAAE,EAAE;kBACZC,UAAU,EAAE,mBAAmB;kBAC/BC,YAAY,EAAE;gBAClB,CAAE;gBAAAV,QAAA,EACG9B,IAAI,CAACC;cAAK,CACT,CAAC,EACPvC,IAAA,CAACjB,IAAI;gBAACuF,KAAK,EAAE;kBACTK,KAAK,EAAErC,IAAI,CAACiB,UAAU,KAAK,UAAU,GAAG,SAAS,GAAG,SAAS;kBAC7DqB,QAAQ,EAAE,EAAE;kBACZC,UAAU,EAAE;gBAChB,CAAE;gBAAAT,QAAA,EACG9B,IAAI,CAACgB;cAAM,CACV,CAAC;YAAA,GAvBAyC,KAwBL,CAAC;UAAA,CACV;QAAC,CACM,CAAC;MAAA,CACX,CAAC,EAGP7F,KAAA,CAACpB,IAAI;QAACwF,KAAK,EAAE;UAAES,iBAAiB,EAAE,EAAE;UAAED,YAAY,EAAE;QAAG,CAAE;QAAAV,QAAA,GACrDlE,KAAA,CAACpB,IAAI;UAACwF,KAAK,EAAE;YAAEkB,aAAa,EAAE,KAAK;YAAEU,cAAc,EAAE,eAAe;YAAEC,UAAU,EAAE,QAAQ;YAAErB,YAAY,EAAE;UAAG,CAAE;UAAAV,QAAA,GAC3GpE,IAAA,CAACjB,IAAI;YAACuF,KAAK,EAAE;cACTK,KAAK,EAAE,MAAM;cACbC,QAAQ,EAAE,EAAE;cACZC,UAAU,EAAE;YAChB,CAAE;YAAAT,QAAA,EAAC;UAEH,CAAM,CAAC,EACPpE,IAAA,CAAChB,gBAAgB;YAACoH,OAAO,EAAE,SAATA,OAAOA,CAAA;cAAA,OAAQ/F,UAAU,oBAAVA,UAAU,CAAEgG,QAAQ,CAAC,UAAU,CAAC;YAAA,CAAC;YAAAjC,QAAA,EAC9DpE,IAAA,CAACjB,IAAI;cAACuF,KAAK,EAAE;gBACTK,KAAK,EAAE,SAAS;gBAChBC,QAAQ,EAAE,EAAE;gBACZC,UAAU,EAAE;cAChB,CAAE;cAAAT,QAAA,EAAC;YAEH,CAAM;UAAC,CACO,CAAC;QAAA,CACjB,CAAC,EAENlB,OAAO,CAACa,GAAG,CAAC,UAACC,MAAM;UAAA,OAChB9D,KAAA,CAACR,IAAI;YAAiB4E,KAAK,EAAE;cAAEQ,YAAY,EAAE;YAAG,CAAE;YAAAV,QAAA,GAC9ClE,KAAA,CAACpB,IAAI;cAACwF,KAAK,EAAE;gBAAEkB,aAAa,EAAE,KAAK;gBAAEU,cAAc,EAAE,eAAe;gBAAEC,UAAU,EAAE,YAAY;gBAAErB,YAAY,EAAE;cAAE,CAAE;cAAAV,QAAA,GAC9GlE,KAAA,CAACpB,IAAI;gBAACwF,KAAK,EAAE;kBAAEE,IAAI,EAAE;gBAAE,CAAE;gBAAAJ,QAAA,GACrBlE,KAAA,CAACpB,IAAI;kBAACwF,KAAK,EAAE;oBAAEkB,aAAa,EAAE,KAAK;oBAAEW,UAAU,EAAE,QAAQ;oBAAErB,YAAY,EAAE;kBAAE,CAAE;kBAAAV,QAAA,GACzEpE,IAAA,CAAClB,IAAI;oBAACwF,KAAK,EAAE;sBACTa,eAAe,EAAEnB,MAAM,CAAC3B,IAAI,KAAK,KAAK,GAAG,SAAS,GAAG,SAAS;sBAC9D0C,iBAAiB,EAAE,CAAC;sBACpBuB,eAAe,EAAE,CAAC;sBAClBC,YAAY,EAAE,CAAC;sBACfP,WAAW,EAAE;oBACjB,CAAE;oBAAA5B,QAAA,EACEpE,IAAA,CAACjB,IAAI;sBAACuF,KAAK,EAAE;wBACTK,KAAK,EAAE,MAAM;wBACbC,QAAQ,EAAE,EAAE;wBACZC,UAAU,EAAE;sBAChB,CAAE;sBAAAT,QAAA,EACGJ,MAAM,CAAC3B;oBAAI,CACV;kBAAC,CACL,CAAC,EACPrC,IAAA,CAACjB,IAAI;oBAACuF,KAAK,EAAE;sBACTK,KAAK,EAAE,SAAS;sBAChBC,QAAQ,EAAE,EAAE;sBACZC,UAAU,EAAE;oBAChB,CAAE;oBAAAT,QAAA,EACGJ,MAAM,CAAC1B;kBAAI,CACV,CAAC,EACPtC,IAAA,CAAClB,IAAI;oBAACwF,KAAK,EAAE;sBACTa,eAAe,EAAEnB,MAAM,CAAChB,MAAM,KAAK,QAAQ,GAAG,SAAS,GAAG,SAAS;sBACnE+B,iBAAiB,EAAE,CAAC;sBACpBuB,eAAe,EAAE,CAAC;sBAClBC,YAAY,EAAE,EAAE;sBAChBC,UAAU,EAAE;oBAChB,CAAE;oBAAApC,QAAA,EACEpE,IAAA,CAACjB,IAAI;sBAACuF,KAAK,EAAE;wBACTK,KAAK,EAAEX,MAAM,CAAChB,MAAM,KAAK,QAAQ,GAAG,MAAM,GAAG,MAAM;wBACnD4B,QAAQ,EAAE,CAAC;wBACXC,UAAU,EAAE;sBAChB,CAAE;sBAAAT,QAAA,EACGJ,MAAM,CAAChB,MAAM,CAACyD,WAAW,CAAC;oBAAC,CAC1B;kBAAC,CACL,CAAC;gBAAA,CACL,CAAC,EACPvG,KAAA,CAACnB,IAAI;kBAACuF,KAAK,EAAE;oBACTK,KAAK,EAAE,SAAS;oBAChBC,QAAQ,EAAE,EAAE;oBACZC,UAAU,EAAE,oBAAoB;oBAChCC,YAAY,EAAE;kBAClB,CAAE;kBAAAV,QAAA,GACGJ,MAAM,CAACjB,OAAO,EAAC,UAAG,EAACiB,MAAM,CAAClB,SAAS;gBAAA,CAClC,CAAC;cAAA,CACL,CAAC,EACP5C,KAAA,CAACpB,IAAI;gBAACwF,KAAK,EAAE;kBAAE6B,UAAU,EAAE;gBAAW,CAAE;gBAAA/B,QAAA,GACpCpE,IAAA,CAACjB,IAAI;kBAACuF,KAAK,EAAE;oBACTK,KAAK,EAAE,MAAM;oBACbC,QAAQ,EAAE,EAAE;oBACZC,UAAU,EAAE;kBAChB,CAAE;kBAAAT,QAAA,EACGJ,MAAM,CAACzB;gBAAK,CACX,CAAC,EACPvC,IAAA,CAACjB,IAAI;kBAACuF,KAAK,EAAE;oBACTK,KAAK,EAAE,SAAS;oBAChBC,QAAQ,EAAE,EAAE;oBACZC,UAAU,EAAE;kBAChB,CAAE;kBAAAT,QAAA,EACGJ,MAAM,CAACrB;gBAAS,CACf,CAAC;cAAA,CACL,CAAC;YAAA,CACL,CAAC,EAEPzC,KAAA,CAACpB,IAAI;cAACwF,KAAK,EAAE;gBAAEkB,aAAa,EAAE,KAAK;gBAAEU,cAAc,EAAE,eAAe;gBAAEpB,YAAY,EAAE;cAAG,CAAE;cAAAV,QAAA,GACrFlE,KAAA,CAACpB,IAAI;gBAACwF,KAAK,EAAE;kBAAEE,IAAI,EAAE,CAAC;kBAAEwB,WAAW,EAAE;gBAAE,CAAE;gBAAA5B,QAAA,GACrCpE,IAAA,CAACjB,IAAI;kBAACuF,KAAK,EAAE;oBAAEK,KAAK,EAAE,SAAS;oBAAEC,QAAQ,EAAE,EAAE;oBAAEC,UAAU,EAAE;kBAAqB,CAAE;kBAAAT,QAAA,EAAC;gBAAM,CAAM,CAAC,EAChGpE,IAAA,CAACjB,IAAI;kBAACuF,KAAK,EAAE;oBAAEK,KAAK,EAAE,SAAS;oBAAEC,QAAQ,EAAE,EAAE;oBAAEC,UAAU,EAAE;kBAAoB,CAAE;kBAAAT,QAAA,EAAEJ,MAAM,CAACxB;gBAAM,CAAO,CAAC;cAAA,CACtG,CAAC,EACPtC,KAAA,CAACpB,IAAI;gBAACwF,KAAK,EAAE;kBAAEE,IAAI,EAAE,CAAC;kBAAEwB,WAAW,EAAE;gBAAE,CAAE;gBAAA5B,QAAA,GACrCpE,IAAA,CAACjB,IAAI;kBAACuF,KAAK,EAAE;oBAAEK,KAAK,EAAE,SAAS;oBAAEC,QAAQ,EAAE,EAAE;oBAAEC,UAAU,EAAE;kBAAqB,CAAE;kBAAAT,QAAA,EAAC;gBAAS,CAAM,CAAC,EACnGpE,IAAA,CAACjB,IAAI;kBAACuF,KAAK,EAAE;oBAAEK,KAAK,EAAE,SAAS;oBAAEC,QAAQ,EAAE,EAAE;oBAAEC,UAAU,EAAE;kBAAoB,CAAE;kBAAAT,QAAA,EAAEJ,MAAM,CAACvB;gBAAQ,CAAO,CAAC;cAAA,CACxG,CAAC,EACPvC,KAAA,CAACpB,IAAI;gBAACwF,KAAK,EAAE;kBAAEE,IAAI,EAAE;gBAAE,CAAE;gBAAAJ,QAAA,GACrBpE,IAAA,CAACjB,IAAI;kBAACuF,KAAK,EAAE;oBAAEK,KAAK,EAAE,SAAS;oBAAEC,QAAQ,EAAE,EAAE;oBAAEC,UAAU,EAAE;kBAAqB,CAAE;kBAAAT,QAAA,EAAC;gBAAU,CAAM,CAAC,EACpGlE,KAAA,CAACnB,IAAI;kBAACuF,KAAK,EAAE;oBAAEK,KAAK,EAAE,SAAS;oBAAEC,QAAQ,EAAE,EAAE;oBAAEC,UAAU,EAAE;kBAAoB,CAAE;kBAAAT,QAAA,GAAEJ,MAAM,CAACtB,UAAU,EAAC,GAAC;gBAAA,CAAM,CAAC;cAAA,CAC3G,CAAC;YAAA,CACL,CAAC,EAEPxC,KAAA,CAACpB,IAAI;cAACwF,KAAK,EAAE;gBAAEkB,aAAa,EAAE,KAAK;gBAAEU,cAAc,EAAE,eAAe;gBAAEC,UAAU,EAAE;cAAS,CAAE;cAAA/B,QAAA,GACzFlE,KAAA,CAACpB,IAAI;gBAACwF,KAAK,EAAE;kBAAEkB,aAAa,EAAE,KAAK;kBAAEW,UAAU,EAAE;gBAAS,CAAE;gBAAA/B,QAAA,GACxDlE,KAAA,CAAClB,gBAAgB;kBACboH,OAAO,EAAE,SAATA,OAAOA,CAAA;oBAAA,OAAQvC,UAAU,CAACG,MAAM,CAAC5B,EAAE,CAAC;kBAAA,CAAC;kBACrCkC,KAAK,EAAE;oBAAEkB,aAAa,EAAE,KAAK;oBAAEW,UAAU,EAAE,QAAQ;oBAAEH,WAAW,EAAE;kBAAG,CAAE;kBAAA5B,QAAA,GAEvEpE,IAAA,CAACjB,IAAI;oBAACuF,KAAK,EAAE;sBAAEK,KAAK,EAAE,SAAS;sBAAEqB,WAAW,EAAE,CAAC;sBAAEpB,QAAQ,EAAE;oBAAG,CAAE;oBAAAR,QAAA,EAAC;kBAAE,CAAM,CAAC,EAC1EpE,IAAA,CAACjB,IAAI;oBAACuF,KAAK,EAAE;sBAAEK,KAAK,EAAE,SAAS;sBAAEC,QAAQ,EAAE,EAAE;sBAAEC,UAAU,EAAE;oBAAqB,CAAE;oBAAAT,QAAA,EAAEJ,MAAM,CAACpB;kBAAK,CAAO,CAAC;gBAAA,CAC1F,CAAC,EACnB1C,KAAA,CAAClB,gBAAgB;kBACboH,OAAO,EAAE,SAATA,OAAOA,CAAA;oBAAA,OAAQlC,aAAa,CAACF,MAAM,CAAC5B,EAAE,CAAC;kBAAA,CAAC;kBACxCkC,KAAK,EAAE;oBAAEkB,aAAa,EAAE,KAAK;oBAAEW,UAAU,EAAE;kBAAS,CAAE;kBAAA/B,QAAA,GAEtDpE,IAAA,CAACjB,IAAI;oBAACuF,KAAK,EAAE;sBAAEK,KAAK,EAAE,SAAS;sBAAEqB,WAAW,EAAE,CAAC;sBAAEpB,QAAQ,EAAE;oBAAG,CAAE;oBAAAR,QAAA,EAAC;kBAAE,CAAM,CAAC,EAC1EpE,IAAA,CAACjB,IAAI;oBAACuF,KAAK,EAAE;sBAAEK,KAAK,EAAE,SAAS;sBAAEC,QAAQ,EAAE,EAAE;sBAAEC,UAAU,EAAE;oBAAqB,CAAE;oBAAAT,QAAA,EAAEJ,MAAM,CAACnB;kBAAQ,CAAO,CAAC;gBAAA,CAC7F,CAAC;cAAA,CACjB,CAAC,EACP7C,IAAA,CAAChB,gBAAgB;gBAAAoF,QAAA,EACbpE,IAAA,CAACjB,IAAI;kBAACuF,KAAK,EAAE;oBAAEK,KAAK,EAAE,SAAS;oBAAEC,QAAQ,EAAE,EAAE;oBAAEC,UAAU,EAAE;kBAAoB,CAAE;kBAAAT,QAAA,EAAC;gBAAY,CAAM;cAAC,CACvF,CAAC;YAAA,CACjB,CAAC;UAAA,GAxGAJ,MAAM,CAAC5B,EAyGZ,CAAC;QAAA,CACV,CAAC;MAAA,CACA,CAAC,EAGPlC,KAAA,CAACpB,IAAI;QAACwF,KAAK,EAAE;UAAES,iBAAiB,EAAE,EAAE;UAAED,YAAY,EAAE;QAAG,CAAE;QAAAV,QAAA,GACrDpE,IAAA,CAACjB,IAAI;UAACuF,KAAK,EAAE;YACTK,KAAK,EAAE,MAAM;YACbC,QAAQ,EAAE,EAAE;YACZC,UAAU,EAAE,qBAAqB;YACjCC,YAAY,EAAE;UAClB,CAAE;UAAAV,QAAA,EAAC;QAEH,CAAM,CAAC,EACPlE,KAAA,CAACpB,IAAI;UAACwF,KAAK,EAAE;YAAEkB,aAAa,EAAE,KAAK;YAAEU,cAAc,EAAE;UAAgB,CAAE;UAAA9B,QAAA,GACnEpE,IAAA,CAACX,MAAM;YACHqH,IAAI,EAAC,WAAW;YAChBN,OAAO,EAAE,SAATA,OAAOA,CAAA;cAAA,OAAQ/F,UAAU,oBAAVA,UAAU,CAAEgG,QAAQ,CAAC,UAAU,CAAC;YAAA,CAAC;YAChD/B,KAAK,EAAE;cAAEE,IAAI,EAAE,CAAC;cAAEwB,WAAW,EAAE,CAAC;cAAEb,eAAe,EAAE;YAAU,CAAE;YAC/DwB,UAAU,EAAE;cAAEhC,KAAK,EAAE,MAAM;cAAEE,UAAU,EAAE;YAAoB,CAAE;YAAAT,QAAA,EAClE;UAED,CAAQ,CAAC,EACTpE,IAAA,CAACX,MAAM;YACHqH,IAAI,EAAC,UAAU;YACfN,OAAO,EAAE,SAATA,OAAOA,CAAA;cAAA,OAAQ/F,UAAU,oBAAVA,UAAU,CAAEgG,QAAQ,CAAC,SAAS,CAAC;YAAA,CAAC;YAC/C/B,KAAK,EAAE;cAAEE,IAAI,EAAE,CAAC;cAAEgC,UAAU,EAAE,CAAC;cAAEI,WAAW,EAAE;YAAU,CAAE;YAC1DD,UAAU,EAAE;cAAEhC,KAAK,EAAE,SAAS;cAAEE,UAAU,EAAE;YAAoB,CAAE;YAAAT,QAAA,EACrE;UAED,CAAQ,CAAC;QAAA,CACP,CAAC;MAAA,CACL,CAAC;IAAA,CACC;EAAC,CACR,CAAC;AAElB,CAAC;AAED,eAAejE,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}