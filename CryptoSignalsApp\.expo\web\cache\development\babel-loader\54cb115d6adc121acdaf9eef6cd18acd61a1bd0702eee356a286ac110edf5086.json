{"ast": null, "code": "import React from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport TouchableOpacity from \"react-native-web/dist/exports/TouchableOpacity\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar Academy = function Academy(_ref) {\n  var navigation = _ref.navigation;\n  var navigateToTopic = function navigateToTopic(topic) {\n    navigation.navigate('TopicDetail', {\n      topic: topic\n    });\n  };\n  return _jsxs(View, {\n    style: styles.container,\n    children: [_jsx(Text, {\n      style: styles.title,\n      children: \"Academia de Aprendizado\"\n    }), _jsx(TouchableOpacity, {\n      onPress: function onPress() {\n        return navigateToTopic('TradingBasics');\n      },\n      style: styles.topicButton,\n      children: _jsx(Text, {\n        style: styles.topicText,\n        children: \"Trading B\\xE1sico\"\n      })\n    }), _jsx(TouchableOpacity, {\n      onPress: function onPress() {\n        return navigateToTopic('Cryptocurrencies');\n      },\n      style: styles.topicButton,\n      children: _jsx(Text, {\n        style: styles.topicText,\n        children: \"Criptomoedas\"\n      })\n    }), _jsx(TouchableOpacity, {\n      onPress: function onPress() {\n        return navigateToTopic('TechnicalAnalysis');\n      },\n      style: styles.topicButton,\n      children: _jsx(Text, {\n        style: styles.topicText,\n        children: \"An\\xE1lise T\\xE9cnica\"\n      })\n    })]\n  });\n};\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    justifyContent: 'center',\n    alignItems: 'center',\n    padding: 16\n  },\n  title: {\n    fontSize: 24,\n    fontWeight: 'bold',\n    marginBottom: 16\n  },\n  topicButton: {\n    backgroundColor: '#3176c4',\n    padding: 12,\n    borderRadius: 8,\n    marginBottom: 16\n  },\n  topicText: {\n    color: '#fff',\n    fontSize: 18,\n    fontWeight: 'bold'\n  }\n});\nexport default Academy;", "map": {"version": 3, "names": ["React", "View", "Text", "TouchableOpacity", "StyleSheet", "jsx", "_jsx", "jsxs", "_jsxs", "Academy", "_ref", "navigation", "navigateToTopic", "topic", "navigate", "style", "styles", "container", "children", "title", "onPress", "topicButton", "topicText", "create", "flex", "justifyContent", "alignItems", "padding", "fontSize", "fontWeight", "marginBottom", "backgroundColor", "borderRadius", "color"], "sources": ["E:/CryptoSignalsApp/src/pages/Academy/index.js"], "sourcesContent": ["import React from 'react';\r\nimport { View, Text, TouchableOpacity, StyleSheet } from 'react-native';\r\n\r\nconst Academy = ({ navigation }) => {\r\n  // Função para navegar para a página de um tópico de aprendizado\r\n  const navigateToTopic = (topic) => {\r\n    navigation.navigate('TopicDetail', { topic });\r\n  };\r\n\r\n  return (\r\n    <View style={styles.container}>\r\n      <Text style={styles.title}>Academia de Aprendizado</Text>\r\n\r\n      <TouchableOpacity onPress={() => navigateToTopic('TradingBasics')} style={styles.topicButton}>\r\n        <Text style={styles.topicText}>Trading Básico</Text>\r\n      </TouchableOpacity>\r\n\r\n      <TouchableOpacity onPress={() => navigateToTopic('Cryptocurrencies')} style={styles.topicButton}>\r\n        <Text style={styles.topicText}>Criptomoedas</Text>\r\n      </TouchableOpacity>\r\n\r\n      <TouchableOpacity onPress={() => navigateToTopic('TechnicalAnalysis')} style={styles.topicButton}>\r\n        <Text style={styles.topicText}>An<PERSON><PERSON>e Técnica</Text>\r\n      </TouchableOpacity>\r\n\r\n      {/* Adicione mais botões para outros tópicos de aprendizado aqui */}\r\n    </View>\r\n  );\r\n};\r\n\r\nconst styles = StyleSheet.create({\r\n  container: {\r\n    flex: 1,\r\n    justifyContent: 'center',\r\n    alignItems: 'center',\r\n    padding: 16,\r\n  },\r\n  title: {\r\n    fontSize: 24,\r\n    fontWeight: 'bold',\r\n    marginBottom: 16,\r\n  },\r\n  topicButton: {\r\n    backgroundColor: '#3176c4',\r\n    padding: 12,\r\n    borderRadius: 8,\r\n    marginBottom: 16,\r\n  },\r\n  topicText: {\r\n    color: '#fff',\r\n    fontSize: 18,\r\n    fontWeight: 'bold',\r\n  },\r\n});\r\n\r\nexport default Academy;\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,gBAAA;AAAA,OAAAC,UAAA;AAAA,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAG1B,IAAMC,OAAO,GAAG,SAAVA,OAAOA,CAAAC,IAAA,EAAuB;EAAA,IAAjBC,UAAU,GAAAD,IAAA,CAAVC,UAAU;EAE3B,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAIC,KAAK,EAAK;IACjCF,UAAU,CAACG,QAAQ,CAAC,aAAa,EAAE;MAAED,KAAK,EAALA;IAAM,CAAC,CAAC;EAC/C,CAAC;EAED,OACEL,KAAA,CAACP,IAAI;IAACc,KAAK,EAAEC,MAAM,CAACC,SAAU;IAAAC,QAAA,GAC5BZ,IAAA,CAACJ,IAAI;MAACa,KAAK,EAAEC,MAAM,CAACG,KAAM;MAAAD,QAAA,EAAC;IAAuB,CAAM,CAAC,EAEzDZ,IAAA,CAACH,gBAAgB;MAACiB,OAAO,EAAE,SAATA,OAAOA,CAAA;QAAA,OAAQR,eAAe,CAAC,eAAe,CAAC;MAAA,CAAC;MAACG,KAAK,EAAEC,MAAM,CAACK,WAAY;MAAAH,QAAA,EAC3FZ,IAAA,CAACJ,IAAI;QAACa,KAAK,EAAEC,MAAM,CAACM,SAAU;QAAAJ,QAAA,EAAC;MAAc,CAAM;IAAC,CACpC,CAAC,EAEnBZ,IAAA,CAACH,gBAAgB;MAACiB,OAAO,EAAE,SAATA,OAAOA,CAAA;QAAA,OAAQR,eAAe,CAAC,kBAAkB,CAAC;MAAA,CAAC;MAACG,KAAK,EAAEC,MAAM,CAACK,WAAY;MAAAH,QAAA,EAC9FZ,IAAA,CAACJ,IAAI;QAACa,KAAK,EAAEC,MAAM,CAACM,SAAU;QAAAJ,QAAA,EAAC;MAAY,CAAM;IAAC,CAClC,CAAC,EAEnBZ,IAAA,CAACH,gBAAgB;MAACiB,OAAO,EAAE,SAATA,OAAOA,CAAA;QAAA,OAAQR,eAAe,CAAC,mBAAmB,CAAC;MAAA,CAAC;MAACG,KAAK,EAAEC,MAAM,CAACK,WAAY;MAAAH,QAAA,EAC/FZ,IAAA,CAACJ,IAAI;QAACa,KAAK,EAAEC,MAAM,CAACM,SAAU;QAAAJ,QAAA,EAAC;MAAe,CAAM;IAAC,CACrC,CAAC;EAAA,CAGf,CAAC;AAEX,CAAC;AAED,IAAMF,MAAM,GAAGZ,UAAU,CAACmB,MAAM,CAAC;EAC/BN,SAAS,EAAE;IACTO,IAAI,EAAE,CAAC;IACPC,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE,QAAQ;IACpBC,OAAO,EAAE;EACX,CAAC;EACDR,KAAK,EAAE;IACLS,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBC,YAAY,EAAE;EAChB,CAAC;EACDT,WAAW,EAAE;IACXU,eAAe,EAAE,SAAS;IAC1BJ,OAAO,EAAE,EAAE;IACXK,YAAY,EAAE,CAAC;IACfF,YAAY,EAAE;EAChB,CAAC;EACDR,SAAS,EAAE;IACTW,KAAK,EAAE,MAAM;IACbL,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE;EACd;AACF,CAAC,CAAC;AAEF,eAAepB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}