{"ast": null, "code": "export function enableExpoCliLogging() {\n  console.warn('Expo.Logs.enableExpoCliLogging: is not supported on web');\n}\nexport function disableExpoCliLogging() {\n  console.warn('Expo.Logs.disableExpoCliLogging: is not supported on web');\n}", "map": {"version": 3, "names": ["enableExpoCliLogging", "console", "warn", "disableExpoCliLogging"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\expo\\src\\logs\\Logs.web.ts"], "sourcesContent": ["export function enableExpoCliLogging(): void {\n  console.warn('Expo.Logs.enableExpoCliLogging: is not supported on web');\n}\nexport function disableExpoCliLogging(): void {\n  console.warn('Expo.Logs.disableExpoCliLogging: is not supported on web');\n}\n"], "mappings": "AAAA,OAAM,SAAUA,oBAAoBA,CAAA;EAClCC,OAAO,CAACC,IAAI,CAAC,yDAAyD,CAAC;AACzE;AACA,OAAM,SAAUC,qBAAqBA,CAAA;EACnCF,OAAO,CAACC,IAAI,CAAC,0DAA0D,CAAC;AAC1E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}