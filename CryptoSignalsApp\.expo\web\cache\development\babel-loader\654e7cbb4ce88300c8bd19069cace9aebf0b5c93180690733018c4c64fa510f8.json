{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/toConsumableArray\";\nimport _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport React, { useEffect, useState, useRef, useContext } from \"react\";\nimport AppState from \"react-native-web/dist/exports/AppState\";\nimport ScrollView from \"react-native-web/dist/exports/ScrollView\";\nimport { Text } from 'react-native-paper';\nimport View from \"react-native-web/dist/exports/View\";\nimport Wrapper from \"../../components/Wrapper\";\nimport PageTitle from \"../../components/PageTitle\";\nimport InputSearch from \"../../components/InputSearch\";\nimport Loading from \"../../components/Loading\";\nimport { StoreContext } from \"../../store/index\";\nimport SignalsCard from \"../Signals/Card\";\nimport styles from \"./styles\";\nimport { AxiosContext } from \"../../store/axios\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar Signals = function Signals(_ref) {\n  var _state$signalFromWebs;\n  var route = _ref.route;\n  var _useContext = useContext(StoreContext),\n    _useContext2 = _slicedToArray(_useContext, 2),\n    state = _useContext2[0],\n    dispatch = _useContext2[1];\n  var _useContext3 = useContext(AxiosContext),\n    _useContext4 = _slicedToArray(_useContext3, 1),\n    api = _useContext4[0];\n  var _useState = useState(AppState.currentState),\n    _useState2 = _slicedToArray(_useState, 2),\n    _ = _useState2[0],\n    setAppState = _useState2[1];\n  var timeoutRef = useRef();\n  var _useState3 = useState(true),\n    _useState4 = _slicedToArray(_useState3, 2),\n    isLoading = _useState4[0],\n    setIsLoading = _useState4[1];\n  var _useState5 = useState([]),\n    _useState6 = _slicedToArray(_useState5, 2),\n    signals = _useState6[0],\n    setSignals = _useState6[1];\n  var _useState7 = useState([]),\n    _useState8 = _slicedToArray(_useState7, 2),\n    signalsImmutable = _useState8[0],\n    setSignalsImmutable = _useState8[1];\n  var _useState9 = useState(\"\"),\n    _useState0 = _slicedToArray(_useState9, 2),\n    inputSearchValue = _useState0[0],\n    setInputSearchValue = _useState0[1];\n  var _useState1 = useState(0),\n    _useState10 = _slicedToArray(_useState1, 2),\n    skip = _useState10[0],\n    setSkip = _useState10[1];\n  var _useState11 = useState(\"\"),\n    _useState12 = _slicedToArray(_useState11, 2),\n    effectiveSearchPairs = _useState12[0],\n    setEffectiveSearchPairs = _useState12[1];\n  var _useState13 = useState(true),\n    _useState14 = _slicedToArray(_useState13, 2),\n    canGetSignals = _useState14[0],\n    setCanGetSignals = _useState14[1];\n  var params = route.params;\n  var channelId = params.channelId,\n    channelName = params.channelName;\n  var signalFromWebsocket = (_state$signalFromWebs = state == null ? void 0 : state.signalFromWebsocket[channelId]) != null ? _state$signalFromWebs : null;\n  var getSignals = function () {\n    var _ref2 = _asyncToGenerator(function* (skipValue) {\n      try {\n        console.log(`Carregando sinais do canal ${channelId}, skip: ${skipValue}`);\n        var response = yield api.get(`/channels/${channelId}/signals?skip=${skipValue}&limit=20`);\n        if (response.data && Array.isArray(response.data)) {\n          console.log(`Sinais carregados: ${response.data.length}`);\n          if (response.data.length === 0) {\n            setCanGetSignals(false);\n          }\n          if (skipValue === 0) {\n            setSignals(response.data);\n            setSignalsImmutable(response.data);\n          } else {\n            setSignals(function (prev) {\n              return [].concat(_toConsumableArray(prev), _toConsumableArray(response.data));\n            });\n            setSignalsImmutable(function (prev) {\n              return [].concat(_toConsumableArray(prev), _toConsumableArray(response.data));\n            });\n          }\n        } else {\n          console.warn('Resposta da API não contém array de sinais:', response.data);\n          if (skipValue === 0) {\n            setSignals([]);\n            setSignalsImmutable([]);\n          }\n        }\n        setIsLoading(false);\n      } catch (e) {\n        console.error('Erro ao carregar sinais:', e);\n        setIsLoading(false);\n        if (skipValue === 0) {\n          setSignals([]);\n          setSignalsImmutable([]);\n        }\n      }\n    });\n    return function getSignals(_x) {\n      return _ref2.apply(this, arguments);\n    };\n  }();\n  var getSignalsSearch = function () {\n    var _ref3 = _asyncToGenerator(function* (search) {\n      try {\n        console.log(`Buscando sinais com termo: ${search}`);\n        var response = yield api.get(`/channels/${channelId}/signals?search=${search}&limit=50`);\n        if (response.data && Array.isArray(response.data)) {\n          console.log(`Sinais encontrados na busca: ${response.data.length}`);\n          setSignals(response.data);\n        } else {\n          console.warn('Resposta da busca não contém array de sinais:', response.data);\n          setSignals([]);\n        }\n        setIsLoading(false);\n      } catch (e) {\n        console.error('Erro ao buscar sinais:', e);\n        setSignals([]);\n        setIsLoading(false);\n      }\n    });\n    return function getSignalsSearch(_x2) {\n      return _ref3.apply(this, arguments);\n    };\n  }();\n  var handleScrollView = function handleScrollView(_ref4) {\n    var layoutMeasurement = _ref4.layoutMeasurement,\n      contentOffset = _ref4.contentOffset,\n      contentSize = _ref4.contentSize;\n    if (inputSearchValue) {\n      return;\n    }\n    var paddingToBottom = 20;\n    var isCloseToBottom = layoutMeasurement.height + contentOffset.y >= contentSize.height - paddingToBottom;\n    if (!isCloseToBottom) {\n      return;\n    }\n    if (!canGetSignals) {\n      return;\n    }\n    setSkip(skip + 1);\n  };\n  var handleSearchPairs = function handleSearchPairs(value) {\n    setInputSearchValue(value);\n    setEffectiveSearchPairs(value);\n  };\n  useEffect(function () {\n    var appStateListener = AppState.addEventListener(\"change\", function (nextAppState) {\n      setAppState(nextAppState);\n      if (nextAppState === \"active\") {\n        setIsLoading(true);\n        setSignals(signalsImmutable);\n      }\n    });\n    return function () {\n      appStateListener == null ? void 0 : appStateListener.remove();\n    };\n  }, []);\n  useEffect(function () {\n    if (channelId) {\n      console.log('Carregando sinais iniciais para canal:', channelId);\n      setIsLoading(true);\n      getSignals(0);\n      setSkip(0);\n    }\n  }, [channelId]);\n  useEffect(function () {\n    if (skip > 0) {\n      setIsLoading(true);\n      getSignals(skip);\n    }\n  }, [skip]);\n  useEffect(function () {\n    if (!effectiveSearchPairs) {\n      setIsLoading(false);\n      if (timeoutRef.current) {\n        clearTimeout(timeoutRef.current);\n      }\n      setSignals(signalsImmutable);\n      return;\n    }\n    timeoutRef.current = setTimeout(_asyncToGenerator(function* () {\n      setIsLoading(true);\n      yield getSignalsSearch(effectiveSearchPairs);\n    }), 700);\n    return function () {\n      clearTimeout(timeoutRef.current);\n    };\n  }, [effectiveSearchPairs]);\n  useEffect(function () {\n    if (!signals.length) return;\n    if (signalFromWebsocket) {\n      signals.unshift(signalFromWebsocket);\n      signalsImmutable.unshift(signalFromWebsocket);\n    }\n    ;\n  }, [signalFromWebsocket]);\n  return _jsxs(Wrapper, {\n    children: [_jsx(PageTitle, {\n      text: channelName != null ? channelName : \"Noname\",\n      goBack: \"Channels\"\n    }, \"TitleSignals\"), _jsxs(View, {\n      style: styles.searchContainer,\n      children: [\"  // Adicionado estilo espec\\xEDfico para alinhar o InputSearch\", _jsx(InputSearch, {\n        onChangeText: function onChangeText(text) {\n          return handleSearchPairs(text);\n        },\n        placeholder: \"Search Pairs\",\n        value: inputSearchValue\n      }, \"InputSearchSignals\")]\n    }), _jsxs(ScrollView, {\n      style: styles.scrollViewSignals,\n      scrollEventThrottle: 400,\n      onScroll: function onScroll(_ref6) {\n        var nativeEvent = _ref6.nativeEvent;\n        return handleScrollView(nativeEvent);\n      },\n      children: [inputSearchValue && !signals.length && !isLoading && _jsxs(Text, {\n        style: styles.emptyState,\n        children: [\"Nenhum sinal encontrado para \\\"\", inputSearchValue, \"\\\".\"]\n      }), !inputSearchValue && !signals.length && !isLoading && _jsxs(View, {\n        style: {\n          padding: 20,\n          alignItems: 'center'\n        },\n        children: [_jsx(Text, {\n          style: [styles.emptyState, {\n            marginBottom: 8\n          }],\n          children: \"Nenhum sinal dispon\\xEDvel\"\n        }), _jsx(Text, {\n          style: {\n            color: '#666',\n            fontSize: 12,\n            textAlign: 'center'\n          },\n          children: \"Este canal ainda n\\xE3o possui sinais ou o gerador n\\xE3o est\\xE1 ativo.\"\n        })]\n      }), signals.map(function (signal, index) {\n        return _jsx(SignalsCard, {\n          createdAt: signal.createdAt,\n          message: signal.messageOriginal,\n          signal: signal\n        }, `SignalsCard--${signal.id || index}`);\n      })]\n    }), isLoading && _jsx(View, {\n      style: styles.containerLoading,\n      children: _jsx(Loading, {})\n    })]\n  });\n};\nexport default Signals;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "useContext", "AppState", "ScrollView", "Text", "View", "Wrapper", "Page<PERSON><PERSON>le", "InputSearch", "Loading", "StoreContext", "SignalsCard", "styles", "AxiosContext", "jsx", "_jsx", "jsxs", "_jsxs", "Signals", "_ref", "_state$signalFromWebs", "route", "_useContext", "_useContext2", "_slicedToArray", "state", "dispatch", "_useContext3", "_useContext4", "api", "_useState", "currentState", "_useState2", "_", "setAppState", "timeoutRef", "_useState3", "_useState4", "isLoading", "setIsLoading", "_useState5", "_useState6", "signals", "setSignals", "_useState7", "_useState8", "signalsImmutable", "setSignalsImmutable", "_useState9", "_useState0", "inputSearchValue", "setInputSearchValue", "_useState1", "_useState10", "skip", "setSkip", "_useState11", "_useState12", "effectiveSearchPairs", "setEffectiveSearchPairs", "_useState13", "_useState14", "canGetSignals", "setCanGetSignals", "params", "channelId", "channelName", "signalFromWebsocket", "getSignals", "_ref2", "_asyncToGenerator", "skip<PERSON><PERSON><PERSON>", "console", "log", "response", "get", "data", "Array", "isArray", "length", "prev", "concat", "_toConsumableArray", "warn", "e", "error", "_x", "apply", "arguments", "getSignalsSearch", "_ref3", "search", "_x2", "handleScrollView", "_ref4", "layoutMeasurement", "contentOffset", "contentSize", "paddingToBottom", "isCloseToBottom", "height", "y", "handleSearchPairs", "value", "appStateListener", "addEventListener", "nextAppState", "remove", "current", "clearTimeout", "setTimeout", "unshift", "children", "text", "goBack", "style", "searchContainer", "onChangeText", "placeholder", "scrollViewSignals", "scrollEventThrottle", "onScroll", "_ref6", "nativeEvent", "emptyState", "padding", "alignItems", "marginBottom", "color", "fontSize", "textAlign", "map", "signal", "index", "createdAt", "message", "messageOriginal", "id", "containerLoading"], "sources": ["C:/Users/<USER>/Desktop/Signals/CryptoSignalsApp/src/pages/Signals/index.js"], "sourcesContent": ["import React, { useEffect, useState, useRef, useContext } from \"react\";\r\nimport { AppState, ScrollView } from \"react-native\";\r\nimport { Text } from 'react-native-paper';\r\nimport { View } from 'react-native';\r\nimport Wrapper from \"../../components/Wrapper\";\r\nimport PageTitle from \"../../components/PageTitle\";\r\nimport InputSearch from \"../../components/InputSearch\";\r\nimport Loading from \"../../components/Loading\";\r\nimport { StoreContext } from \"../../store/index\";\r\nimport SignalsCard from \"../Signals/Card\";\r\nimport styles from \"./styles\";\r\nimport { AxiosContext } from \"../../store/axios\";\r\n\r\nconst Signals = ({ route }) => {\r\n  const [state, dispatch] = useContext(StoreContext);\r\n  const [api] = useContext(AxiosContext);\r\n  const [_, setAppState] = useState(AppState.currentState);\r\n  const timeoutRef = useRef();\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [signals, setSignals] = useState([]);\r\n  const [signalsImmutable, setSignalsImmutable] = useState([]);\r\n  const [inputSearchValue, setInputSearchValue] = useState(\"\");\r\n  const [skip, setSkip] = useState(0);\r\n  const [effectiveSearchPairs, setEffectiveSearchPairs] = useState(\"\");\r\n  const [canGetSignals, setCanGetSignals] = useState(true);\r\n\r\n  const { params } = route;\r\n  const { channelId, channelName } = params;\r\n  const signalFromWebsocket = state?.signalFromWebsocket[channelId] ?? null;\r\n\r\n  const getSignals = async (skipValue) => {\r\n    try {\r\n      console.log(`Carregando sinais do canal ${channelId}, skip: ${skipValue}`);\r\n\r\n      const response = await api.get(\r\n        `/channels/${channelId}/signals?skip=${skipValue}&limit=20`\r\n      );\r\n\r\n      if (response.data && Array.isArray(response.data)) {\r\n        console.log(`Sinais carregados: ${response.data.length}`);\r\n\r\n        if (response.data.length === 0) {\r\n          setCanGetSignals(false);\r\n        }\r\n\r\n        if (skipValue === 0) {\r\n          // Primeira carga ou refresh\r\n          setSignals(response.data);\r\n          setSignalsImmutable(response.data);\r\n        } else {\r\n          // Carregamento adicional (paginação)\r\n          setSignals(prev => [...prev, ...response.data]);\r\n          setSignalsImmutable(prev => [...prev, ...response.data]);\r\n        }\r\n      } else {\r\n        console.warn('Resposta da API não contém array de sinais:', response.data);\r\n        if (skipValue === 0) {\r\n          setSignals([]);\r\n          setSignalsImmutable([]);\r\n        }\r\n      }\r\n\r\n      setIsLoading(false);\r\n    } catch (e) {\r\n      console.error('Erro ao carregar sinais:', e);\r\n      setIsLoading(false);\r\n\r\n      // Em caso de erro na primeira carga, definir arrays vazios\r\n      if (skipValue === 0) {\r\n        setSignals([]);\r\n        setSignalsImmutable([]);\r\n      }\r\n    }\r\n  };\r\n\r\n  const getSignalsSearch = async (search) => {\r\n    try {\r\n      console.log(`Buscando sinais com termo: ${search}`);\r\n\r\n      const response = await api.get(\r\n        `/channels/${channelId}/signals?search=${search}&limit=50`\r\n      );\r\n\r\n      if (response.data && Array.isArray(response.data)) {\r\n        console.log(`Sinais encontrados na busca: ${response.data.length}`);\r\n        setSignals(response.data);\r\n      } else {\r\n        console.warn('Resposta da busca não contém array de sinais:', response.data);\r\n        setSignals([]);\r\n      }\r\n\r\n      setIsLoading(false);\r\n    } catch (e) {\r\n      console.error('Erro ao buscar sinais:', e);\r\n      setSignals([]);\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleScrollView = ({\r\n    layoutMeasurement,\r\n    contentOffset,\r\n    contentSize,\r\n  }) => {\r\n    if (inputSearchValue) {\r\n      return;\r\n    }\r\n\r\n    const paddingToBottom = 20;\r\n    const isCloseToBottom =\r\n      layoutMeasurement.height + contentOffset.y >=\r\n      contentSize.height - paddingToBottom;\r\n\r\n    if (!isCloseToBottom) {\r\n      return;\r\n    }\r\n\r\n    if (!canGetSignals) {\r\n      return;\r\n    }\r\n\r\n    setSkip(skip + 1);\r\n  };\r\n\r\n  const handleSearchPairs = (value) => {\r\n    setInputSearchValue(value);\r\n    setEffectiveSearchPairs(value);\r\n  };\r\n\r\n  useEffect(() => {\r\n    const appStateListener = AppState.addEventListener(\r\n      \"change\",\r\n      (nextAppState) => {\r\n        setAppState(nextAppState);\r\n\r\n        if (nextAppState === \"active\") {\r\n          setIsLoading(true);\r\n          setSignals(signalsImmutable);\r\n        }\r\n      }\r\n    );\r\n\r\n    return () => {\r\n      appStateListener?.remove();\r\n    };\r\n  }, []);\r\n\r\n  // Carregar sinais inicialmente\r\n  useEffect(() => {\r\n    if (channelId) {\r\n      console.log('Carregando sinais iniciais para canal:', channelId);\r\n      setIsLoading(true);\r\n      getSignals(0);\r\n      setSkip(0);\r\n    }\r\n  }, [channelId]);\r\n\r\n  useEffect(() => {\r\n    if (skip > 0) {\r\n      setIsLoading(true);\r\n      getSignals(skip);\r\n    }\r\n  }, [skip]);\r\n\r\n  useEffect(() => {\r\n    if (!effectiveSearchPairs) {\r\n      setIsLoading(false);\r\n\r\n      if (timeoutRef.current) {\r\n        clearTimeout(timeoutRef.current);\r\n      }\r\n\r\n      setSignals(signalsImmutable);\r\n\r\n      return;\r\n    }\r\n\r\n    timeoutRef.current = setTimeout(async () => {\r\n      setIsLoading(true);\r\n      await getSignalsSearch(effectiveSearchPairs);\r\n    }, 700);\r\n\r\n    return () => {\r\n      clearTimeout(timeoutRef.current);\r\n    };\r\n  }, [effectiveSearchPairs]);\r\n\r\n  useEffect(() => {\r\n    if (!signals.length) return;\r\n    // Está funcionando sem a necessidade de usar o setSignals\r\n    if (signalFromWebsocket) {\r\n      signals.unshift(signalFromWebsocket);\r\n      signalsImmutable.unshift(signalFromWebsocket);\r\n    };\r\n  }, [signalFromWebsocket]);\r\n\r\n  return (\r\n    <Wrapper>\r\n      <PageTitle\r\n        key=\"TitleSignals\"\r\n        text={channelName ?? \"Noname\"}\r\n        goBack=\"Channels\"\r\n      />\r\n\r\n      <View style={styles.searchContainer}>  // Adicionado estilo específico para alinhar o InputSearch\r\n        <InputSearch\r\n          key=\"InputSearchSignals\"\r\n          onChangeText={(text) => handleSearchPairs(text)}\r\n          placeholder=\"Search Pairs\"\r\n          value={inputSearchValue}\r\n        />\r\n      </View>\r\n\r\n      <ScrollView\r\n        style={styles.scrollViewSignals}\r\n        scrollEventThrottle={400}\r\n        onScroll={({ nativeEvent }) => handleScrollView(nativeEvent)}\r\n      >\r\n        {inputSearchValue && !signals.length && !isLoading && (\r\n          <Text style={styles.emptyState}>Nenhum sinal encontrado para \"{inputSearchValue}\".</Text>\r\n        )}\r\n\r\n        {!inputSearchValue && !signals.length && !isLoading && (\r\n          <View style={{ padding: 20, alignItems: 'center' }}>\r\n            <Text style={[styles.emptyState, { marginBottom: 8 }]}>\r\n              Nenhum sinal disponível\r\n            </Text>\r\n            <Text style={{ color: '#666', fontSize: 12, textAlign: 'center' }}>\r\n              Este canal ainda não possui sinais ou o gerador não está ativo.\r\n            </Text>\r\n          </View>\r\n        )}\r\n\r\n        {signals.map((signal, index) => (\r\n          <SignalsCard\r\n            key={`SignalsCard--${signal.id || index}`}\r\n            createdAt={signal.createdAt}\r\n            message={signal.messageOriginal}\r\n            signal={signal}\r\n          />\r\n        ))}\r\n      </ScrollView>\r\n\r\n      {isLoading && (\r\n        <View style={styles.containerLoading}>\r\n          <Loading />\r\n        </View>\r\n      )}\r\n    </Wrapper>\r\n  );\r\n};\r\n\r\nexport default Signals;"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,UAAU,QAAQ,OAAO;AAAC,OAAAC,QAAA;AAAA,OAAAC,UAAA;AAEvE,SAASC,IAAI,QAAQ,oBAAoB;AAAC,OAAAC,IAAA;AAE1C,OAAOC,OAAO;AACd,OAAOC,SAAS;AAChB,OAAOC,WAAW;AAClB,OAAOC,OAAO;AACd,SAASC,YAAY;AACrB,OAAOC,WAAW;AAClB,OAAOC,MAAM;AACb,SAASC,YAAY;AAA4B,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAEjD,IAAMC,OAAO,GAAG,SAAVA,OAAOA,CAAAC,IAAA,EAAkB;EAAA,IAAAC,qBAAA;EAAA,IAAZC,KAAK,GAAAF,IAAA,CAALE,KAAK;EACtB,IAAAC,WAAA,GAA0BrB,UAAU,CAACS,YAAY,CAAC;IAAAa,YAAA,GAAAC,cAAA,CAAAF,WAAA;IAA3CG,KAAK,GAAAF,YAAA;IAAEG,QAAQ,GAAAH,YAAA;EACtB,IAAAI,YAAA,GAAc1B,UAAU,CAACY,YAAY,CAAC;IAAAe,YAAA,GAAAJ,cAAA,CAAAG,YAAA;IAA/BE,GAAG,GAAAD,YAAA;EACV,IAAAE,SAAA,GAAyB/B,QAAQ,CAACG,QAAQ,CAAC6B,YAAY,CAAC;IAAAC,UAAA,GAAAR,cAAA,CAAAM,SAAA;IAAjDG,CAAC,GAAAD,UAAA;IAAEE,WAAW,GAAAF,UAAA;EACrB,IAAMG,UAAU,GAAGnC,MAAM,CAAC,CAAC;EAC3B,IAAAoC,UAAA,GAAkCrC,QAAQ,CAAC,IAAI,CAAC;IAAAsC,UAAA,GAAAb,cAAA,CAAAY,UAAA;IAAzCE,SAAS,GAAAD,UAAA;IAAEE,YAAY,GAAAF,UAAA;EAC9B,IAAAG,UAAA,GAA8BzC,QAAQ,CAAC,EAAE,CAAC;IAAA0C,UAAA,GAAAjB,cAAA,CAAAgB,UAAA;IAAnCE,OAAO,GAAAD,UAAA;IAAEE,UAAU,GAAAF,UAAA;EAC1B,IAAAG,UAAA,GAAgD7C,QAAQ,CAAC,EAAE,CAAC;IAAA8C,UAAA,GAAArB,cAAA,CAAAoB,UAAA;IAArDE,gBAAgB,GAAAD,UAAA;IAAEE,mBAAmB,GAAAF,UAAA;EAC5C,IAAAG,UAAA,GAAgDjD,QAAQ,CAAC,EAAE,CAAC;IAAAkD,UAAA,GAAAzB,cAAA,CAAAwB,UAAA;IAArDE,gBAAgB,GAAAD,UAAA;IAAEE,mBAAmB,GAAAF,UAAA;EAC5C,IAAAG,UAAA,GAAwBrD,QAAQ,CAAC,CAAC,CAAC;IAAAsD,WAAA,GAAA7B,cAAA,CAAA4B,UAAA;IAA5BE,IAAI,GAAAD,WAAA;IAAEE,OAAO,GAAAF,WAAA;EACpB,IAAAG,WAAA,GAAwDzD,QAAQ,CAAC,EAAE,CAAC;IAAA0D,WAAA,GAAAjC,cAAA,CAAAgC,WAAA;IAA7DE,oBAAoB,GAAAD,WAAA;IAAEE,uBAAuB,GAAAF,WAAA;EACpD,IAAAG,WAAA,GAA0C7D,QAAQ,CAAC,IAAI,CAAC;IAAA8D,WAAA,GAAArC,cAAA,CAAAoC,WAAA;IAAjDE,aAAa,GAAAD,WAAA;IAAEE,gBAAgB,GAAAF,WAAA;EAEtC,IAAQG,MAAM,GAAK3C,KAAK,CAAhB2C,MAAM;EACd,IAAQC,SAAS,GAAkBD,MAAM,CAAjCC,SAAS;IAAEC,WAAW,GAAKF,MAAM,CAAtBE,WAAW;EAC9B,IAAMC,mBAAmB,IAAA/C,qBAAA,GAAGK,KAAK,oBAALA,KAAK,CAAE0C,mBAAmB,CAACF,SAAS,CAAC,YAAA7C,qBAAA,GAAI,IAAI;EAEzE,IAAMgD,UAAU;IAAA,IAAAC,KAAA,GAAAC,iBAAA,CAAG,WAAOC,SAAS,EAAK;MACtC,IAAI;QACFC,OAAO,CAACC,GAAG,CAAC,8BAA8BR,SAAS,WAAWM,SAAS,EAAE,CAAC;QAE1E,IAAMG,QAAQ,SAAS7C,GAAG,CAAC8C,GAAG,CAC5B,aAAaV,SAAS,iBAAiBM,SAAS,WAClD,CAAC;QAED,IAAIG,QAAQ,CAACE,IAAI,IAAIC,KAAK,CAACC,OAAO,CAACJ,QAAQ,CAACE,IAAI,CAAC,EAAE;UACjDJ,OAAO,CAACC,GAAG,CAAC,sBAAsBC,QAAQ,CAACE,IAAI,CAACG,MAAM,EAAE,CAAC;UAEzD,IAAIL,QAAQ,CAACE,IAAI,CAACG,MAAM,KAAK,CAAC,EAAE;YAC9BhB,gBAAgB,CAAC,KAAK,CAAC;UACzB;UAEA,IAAIQ,SAAS,KAAK,CAAC,EAAE;YAEnB5B,UAAU,CAAC+B,QAAQ,CAACE,IAAI,CAAC;YACzB7B,mBAAmB,CAAC2B,QAAQ,CAACE,IAAI,CAAC;UACpC,CAAC,MAAM;YAELjC,UAAU,CAAC,UAAAqC,IAAI;cAAA,UAAAC,MAAA,CAAAC,kBAAA,CAAQF,IAAI,GAAAE,kBAAA,CAAKR,QAAQ,CAACE,IAAI;YAAA,CAAC,CAAC;YAC/C7B,mBAAmB,CAAC,UAAAiC,IAAI;cAAA,UAAAC,MAAA,CAAAC,kBAAA,CAAQF,IAAI,GAAAE,kBAAA,CAAKR,QAAQ,CAACE,IAAI;YAAA,CAAC,CAAC;UAC1D;QACF,CAAC,MAAM;UACLJ,OAAO,CAACW,IAAI,CAAC,6CAA6C,EAAET,QAAQ,CAACE,IAAI,CAAC;UAC1E,IAAIL,SAAS,KAAK,CAAC,EAAE;YACnB5B,UAAU,CAAC,EAAE,CAAC;YACdI,mBAAmB,CAAC,EAAE,CAAC;UACzB;QACF;QAEAR,YAAY,CAAC,KAAK,CAAC;MACrB,CAAC,CAAC,OAAO6C,CAAC,EAAE;QACVZ,OAAO,CAACa,KAAK,CAAC,0BAA0B,EAAED,CAAC,CAAC;QAC5C7C,YAAY,CAAC,KAAK,CAAC;QAGnB,IAAIgC,SAAS,KAAK,CAAC,EAAE;UACnB5B,UAAU,CAAC,EAAE,CAAC;UACdI,mBAAmB,CAAC,EAAE,CAAC;QACzB;MACF;IACF,CAAC;IAAA,gBA3CKqB,UAAUA,CAAAkB,EAAA;MAAA,OAAAjB,KAAA,CAAAkB,KAAA,OAAAC,SAAA;IAAA;EAAA,GA2Cf;EAED,IAAMC,gBAAgB;IAAA,IAAAC,KAAA,GAAApB,iBAAA,CAAG,WAAOqB,MAAM,EAAK;MACzC,IAAI;QACFnB,OAAO,CAACC,GAAG,CAAC,8BAA8BkB,MAAM,EAAE,CAAC;QAEnD,IAAMjB,QAAQ,SAAS7C,GAAG,CAAC8C,GAAG,CAC5B,aAAaV,SAAS,mBAAmB0B,MAAM,WACjD,CAAC;QAED,IAAIjB,QAAQ,CAACE,IAAI,IAAIC,KAAK,CAACC,OAAO,CAACJ,QAAQ,CAACE,IAAI,CAAC,EAAE;UACjDJ,OAAO,CAACC,GAAG,CAAC,gCAAgCC,QAAQ,CAACE,IAAI,CAACG,MAAM,EAAE,CAAC;UACnEpC,UAAU,CAAC+B,QAAQ,CAACE,IAAI,CAAC;QAC3B,CAAC,MAAM;UACLJ,OAAO,CAACW,IAAI,CAAC,+CAA+C,EAAET,QAAQ,CAACE,IAAI,CAAC;UAC5EjC,UAAU,CAAC,EAAE,CAAC;QAChB;QAEAJ,YAAY,CAAC,KAAK,CAAC;MACrB,CAAC,CAAC,OAAO6C,CAAC,EAAE;QACVZ,OAAO,CAACa,KAAK,CAAC,wBAAwB,EAAED,CAAC,CAAC;QAC1CzC,UAAU,CAAC,EAAE,CAAC;QACdJ,YAAY,CAAC,KAAK,CAAC;MACrB;IACF,CAAC;IAAA,gBAtBKkD,gBAAgBA,CAAAG,GAAA;MAAA,OAAAF,KAAA,CAAAH,KAAA,OAAAC,SAAA;IAAA;EAAA,GAsBrB;EAED,IAAMK,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAAC,KAAA,EAIhB;IAAA,IAHJC,iBAAiB,GAAAD,KAAA,CAAjBC,iBAAiB;MACjBC,aAAa,GAAAF,KAAA,CAAbE,aAAa;MACbC,WAAW,GAAAH,KAAA,CAAXG,WAAW;IAEX,IAAI/C,gBAAgB,EAAE;MACpB;IACF;IAEA,IAAMgD,eAAe,GAAG,EAAE;IAC1B,IAAMC,eAAe,GACnBJ,iBAAiB,CAACK,MAAM,GAAGJ,aAAa,CAACK,CAAC,IAC1CJ,WAAW,CAACG,MAAM,GAAGF,eAAe;IAEtC,IAAI,CAACC,eAAe,EAAE;MACpB;IACF;IAEA,IAAI,CAACrC,aAAa,EAAE;MAClB;IACF;IAEAP,OAAO,CAACD,IAAI,GAAG,CAAC,CAAC;EACnB,CAAC;EAED,IAAMgD,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAIC,KAAK,EAAK;IACnCpD,mBAAmB,CAACoD,KAAK,CAAC;IAC1B5C,uBAAuB,CAAC4C,KAAK,CAAC;EAChC,CAAC;EAEDzG,SAAS,CAAC,YAAM;IACd,IAAM0G,gBAAgB,GAAGtG,QAAQ,CAACuG,gBAAgB,CAChD,QAAQ,EACR,UAACC,YAAY,EAAK;MAChBxE,WAAW,CAACwE,YAAY,CAAC;MAEzB,IAAIA,YAAY,KAAK,QAAQ,EAAE;QAC7BnE,YAAY,CAAC,IAAI,CAAC;QAClBI,UAAU,CAACG,gBAAgB,CAAC;MAC9B;IACF,CACF,CAAC;IAED,OAAO,YAAM;MACX0D,gBAAgB,oBAAhBA,gBAAgB,CAAEG,MAAM,CAAC,CAAC;IAC5B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAGN7G,SAAS,CAAC,YAAM;IACd,IAAImE,SAAS,EAAE;MACbO,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAER,SAAS,CAAC;MAChE1B,YAAY,CAAC,IAAI,CAAC;MAClB6B,UAAU,CAAC,CAAC,CAAC;MACbb,OAAO,CAAC,CAAC,CAAC;IACZ;EACF,CAAC,EAAE,CAACU,SAAS,CAAC,CAAC;EAEfnE,SAAS,CAAC,YAAM;IACd,IAAIwD,IAAI,GAAG,CAAC,EAAE;MACZf,YAAY,CAAC,IAAI,CAAC;MAClB6B,UAAU,CAACd,IAAI,CAAC;IAClB;EACF,CAAC,EAAE,CAACA,IAAI,CAAC,CAAC;EAEVxD,SAAS,CAAC,YAAM;IACd,IAAI,CAAC4D,oBAAoB,EAAE;MACzBnB,YAAY,CAAC,KAAK,CAAC;MAEnB,IAAIJ,UAAU,CAACyE,OAAO,EAAE;QACtBC,YAAY,CAAC1E,UAAU,CAACyE,OAAO,CAAC;MAClC;MAEAjE,UAAU,CAACG,gBAAgB,CAAC;MAE5B;IACF;IAEAX,UAAU,CAACyE,OAAO,GAAGE,UAAU,CAAAxC,iBAAA,CAAC,aAAY;MAC1C/B,YAAY,CAAC,IAAI,CAAC;MAClB,MAAMkD,gBAAgB,CAAC/B,oBAAoB,CAAC;IAC9C,CAAC,GAAE,GAAG,CAAC;IAEP,OAAO,YAAM;MACXmD,YAAY,CAAC1E,UAAU,CAACyE,OAAO,CAAC;IAClC,CAAC;EACH,CAAC,EAAE,CAAClD,oBAAoB,CAAC,CAAC;EAE1B5D,SAAS,CAAC,YAAM;IACd,IAAI,CAAC4C,OAAO,CAACqC,MAAM,EAAE;IAErB,IAAIZ,mBAAmB,EAAE;MACvBzB,OAAO,CAACqE,OAAO,CAAC5C,mBAAmB,CAAC;MACpCrB,gBAAgB,CAACiE,OAAO,CAAC5C,mBAAmB,CAAC;IAC/C;IAAC;EACH,CAAC,EAAE,CAACA,mBAAmB,CAAC,CAAC;EAEzB,OACElD,KAAA,CAACX,OAAO;IAAA0G,QAAA,GACNjG,IAAA,CAACR,SAAS;MAER0G,IAAI,EAAE/C,WAAW,WAAXA,WAAW,GAAI,QAAS;MAC9BgD,MAAM,EAAC;IAAU,GAFb,cAGL,CAAC,EAEFjG,KAAA,CAACZ,IAAI;MAAC8G,KAAK,EAAEvG,MAAM,CAACwG,eAAgB;MAAAJ,QAAA,GAAC,iEACnC,EAAAjG,IAAA,CAACP,WAAW;QAEV6G,YAAY,EAAE,SAAdA,YAAYA,CAAGJ,IAAI;UAAA,OAAKX,iBAAiB,CAACW,IAAI,CAAC;QAAA,CAAC;QAChDK,WAAW,EAAC,cAAc;QAC1Bf,KAAK,EAAErD;MAAiB,GAHpB,oBAIL,CAAC;IAAA,CACE,CAAC,EAEPjC,KAAA,CAACd,UAAU;MACTgH,KAAK,EAAEvG,MAAM,CAAC2G,iBAAkB;MAChCC,mBAAmB,EAAE,GAAI;MACzBC,QAAQ,EAAE,SAAVA,QAAQA,CAAAC,KAAA;QAAA,IAAKC,WAAW,GAAAD,KAAA,CAAXC,WAAW;QAAA,OAAO9B,gBAAgB,CAAC8B,WAAW,CAAC;MAAA,CAAC;MAAAX,QAAA,GAE5D9D,gBAAgB,IAAI,CAACR,OAAO,CAACqC,MAAM,IAAI,CAACzC,SAAS,IAChDrB,KAAA,CAACb,IAAI;QAAC+G,KAAK,EAAEvG,MAAM,CAACgH,UAAW;QAAAZ,QAAA,GAAC,iCAA8B,EAAC9D,gBAAgB,EAAC,KAAE;MAAA,CAAM,CACzF,EAEA,CAACA,gBAAgB,IAAI,CAACR,OAAO,CAACqC,MAAM,IAAI,CAACzC,SAAS,IACjDrB,KAAA,CAACZ,IAAI;QAAC8G,KAAK,EAAE;UAAEU,OAAO,EAAE,EAAE;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAd,QAAA,GACjDjG,IAAA,CAACX,IAAI;UAAC+G,KAAK,EAAE,CAACvG,MAAM,CAACgH,UAAU,EAAE;YAAEG,YAAY,EAAE;UAAE,CAAC,CAAE;UAAAf,QAAA,EAAC;QAEvD,CAAM,CAAC,EACPjG,IAAA,CAACX,IAAI;UAAC+G,KAAK,EAAE;YAAEa,KAAK,EAAE,MAAM;YAAEC,QAAQ,EAAE,EAAE;YAAEC,SAAS,EAAE;UAAS,CAAE;UAAAlB,QAAA,EAAC;QAEnE,CAAM,CAAC;MAAA,CACH,CACP,EAEAtE,OAAO,CAACyF,GAAG,CAAC,UAACC,MAAM,EAAEC,KAAK;QAAA,OACzBtH,IAAA,CAACJ,WAAW;UAEV2H,SAAS,EAAEF,MAAM,CAACE,SAAU;UAC5BC,OAAO,EAAEH,MAAM,CAACI,eAAgB;UAChCJ,MAAM,EAAEA;QAAO,GAHV,gBAAgBA,MAAM,CAACK,EAAE,IAAIJ,KAAK,EAIxC,CAAC;MAAA,CACH,CAAC;IAAA,CACQ,CAAC,EAEZ/F,SAAS,IACRvB,IAAA,CAACV,IAAI;MAAC8G,KAAK,EAAEvG,MAAM,CAAC8H,gBAAiB;MAAA1B,QAAA,EACnCjG,IAAA,CAACN,OAAO,IAAE;IAAC,CACP,CACP;EAAA,CACM,CAAC;AAEd,CAAC;AAED,eAAeS,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}