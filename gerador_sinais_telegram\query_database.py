import sqlite3
import sys
import datetime
from tabulate import tabulate

def connect_to_db(db_path="database.sqlite"):
    """Conecta ao banco de dados SQLite"""
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row  # Para acessar colunas pelo nome
        return conn
    except Exception as e:
        print(f"Erro ao conectar ao banco de dados: {e}")
        sys.exit(1)

def list_signals(conn, limit=10):
    """Lista os sinais mais recentes"""
    try:
        cursor = conn.cursor()
        cursor.execute('''
        SELECT id, symbol, signal_type, strategy, entry_price, stop_loss, 
               take_profit_1, leverage, created_at, status, result, profit_percentage
        FROM signals
        ORDER BY created_at DESC
        LIMIT ?
        ''', (limit,))
        
        rows = cursor.fetchall()
        
        if not rows:
            print("Nenhum sinal encontrado.")
            return
        
        # Converter para lista de dicionários
        signals = []
        for row in rows:
            signal = dict(row)
            # Formatar data
            if signal['created_at']:
                signal['created_at'] = signal['created_at'].split('.')[0]  # Remover milissegundos
            signals.append(signal)
        
        # Imprimir tabela
        headers = ["ID", "Symbol", "Type", "Strategy", "Entry", "Stop Loss", "TP1", "Leverage", "Created", "Status", "Result", "Profit %"]
        table_data = [[
            s['id'], 
            s['symbol'], 
            s['signal_type'], 
            s['strategy'], 
            f"{s['entry_price']:.5f}", 
            f"{s['stop_loss']:.5f}", 
            f"{s['take_profit_1']:.5f}", 
            s['leverage'], 
            s['created_at'], 
            s['status'], 
            s['result'] or '-', 
            f"{s['profit_percentage']:.2f}" if s['profit_percentage'] else '-'
        ] for s in signals]
        
        print("\n=== SINAIS MAIS RECENTES ===")
        print(tabulate(table_data, headers=headers, tablefmt="grid"))
        
    except Exception as e:
        print(f"Erro ao listar sinais: {e}")

def list_updates(conn, signal_id=None, limit=10):
    """Lista as atualizações de sinais"""
    try:
        cursor = conn.cursor()
        
        if signal_id:
            cursor.execute('''
            SELECT su.id, s.symbol, s.signal_type, su.update_type, su.update_time, su.price, su.profit_level
            FROM signal_updates su
            JOIN signals s ON su.signal_id = s.id
            WHERE su.signal_id = ?
            ORDER BY su.update_time DESC
            ''', (signal_id,))
        else:
            cursor.execute('''
            SELECT su.id, s.symbol, s.signal_type, su.update_type, su.update_time, su.price, su.profit_level
            FROM signal_updates su
            JOIN signals s ON su.signal_id = s.id
            ORDER BY su.update_time DESC
            LIMIT ?
            ''', (limit,))
        
        rows = cursor.fetchall()
        
        if not rows:
            print(f"Nenhuma atualização encontrada{' para o sinal ' + str(signal_id) if signal_id else ''}.")
            return
        
        # Converter para lista de dicionários
        updates = []
        for row in rows:
            update = dict(row)
            # Formatar data
            if update['update_time']:
                update['update_time'] = update['update_time'].split('.')[0]  # Remover milissegundos
            updates.append(update)
        
        # Imprimir tabela
        headers = ["ID", "Symbol", "Type", "Update Type", "Time", "Price", "Profit %"]
        table_data = [[
            u['id'], 
            u['symbol'], 
            u['signal_type'], 
            u['update_type'], 
            u['update_time'], 
            f"{u['price']:.5f}" if u['price'] else '-', 
            f"{u['profit_level']}%" if u['profit_level'] else '-'
        ] for u in updates]
        
        print(f"\n=== ATUALIZAÇÕES DE SINAIS{' (SINAL ' + str(signal_id) + ')' if signal_id else ''} ===")
        print(tabulate(table_data, headers=headers, tablefmt="grid"))
        
    except Exception as e:
        print(f"Erro ao listar atualizações: {e}")

def list_statistics(conn):
    """Lista estatísticas diárias"""
    try:
        cursor = conn.cursor()
        cursor.execute('''
        SELECT date, signals_sent, successful_signals, failed_signals, total_profit, average_profit
        FROM statistics
        ORDER BY date DESC
        LIMIT 30
        ''')
        
        rows = cursor.fetchall()
        
        if not rows:
            print("Nenhuma estatística encontrada.")
            return
        
        # Converter para lista de dicionários
        stats = [dict(row) for row in rows]
        
        # Imprimir tabela
        headers = ["Data", "Sinais", "Sucessos", "Falhas", "Lucro Total %", "Lucro Médio %"]
        table_data = [[
            s['date'], 
            s['signals_sent'], 
            s['successful_signals'], 
            s['failed_signals'], 
            f"{s['total_profit']:.2f}%" if s['total_profit'] else '0.00%', 
            f"{s['average_profit']:.2f}%" if s['average_profit'] else '0.00%'
        ] for s in stats]
        
        print("\n=== ESTATÍSTICAS DIÁRIAS ===")
        print(tabulate(table_data, headers=headers, tablefmt="grid"))
        
    except Exception as e:
        print(f"Erro ao listar estatísticas: {e}")

def show_signal_details(conn, signal_id):
    """Mostra detalhes de um sinal específico"""
    try:
        cursor = conn.cursor()
        cursor.execute('''
        SELECT * FROM signals WHERE id = ?
        ''', (signal_id,))
        
        row = cursor.fetchone()
        
        if not row:
            print(f"Sinal com ID {signal_id} não encontrado.")
            return
        
        # Converter para dicionário
        signal = dict(row)
        
        # Formatar datas
        if signal['created_at']:
            signal['created_at'] = signal['created_at'].split('.')[0]
        if signal['closed_at']:
            signal['closed_at'] = signal['closed_at'].split('.')[0]
        
        # Imprimir detalhes
        print(f"\n=== DETALHES DO SINAL {signal_id} ===")
        print(f"Symbol: {signal['symbol']}")
        print(f"Tipo: {signal['signal_type']}")
        print(f"Estratégia: {signal['strategy']}")
        print(f"Preço de entrada: {signal['entry_price']:.5f}")
        print(f"Stop Loss: {signal['stop_loss']:.5f}")
        print(f"Take Profit 1: {signal['take_profit_1']:.5f}")
        
        if signal['take_profit_2']:
            print(f"Take Profit 2: {signal['take_profit_2']:.5f}")
        if signal['take_profit_3']:
            print(f"Take Profit 3: {signal['take_profit_3']:.5f}")
        if signal['take_profit_4']:
            print(f"Take Profit 4: {signal['take_profit_4']:.5f}")
            
        print(f"Alavancagem: {signal['leverage']}x")
        print(f"Criado em: {signal['created_at']}")
        print(f"Status: {signal['status']}")
        
        if signal['result']:
            print(f"Resultado: {signal['result']}")
        if signal['profit_percentage']:
            print(f"Lucro/Perda: {signal['profit_percentage']:.2f}%")
        if signal['hit_level']:
            print(f"Nível atingido: {signal['hit_level']}%")
        if signal['closed_at']:
            print(f"Fechado em: {signal['closed_at']}")
        
        # Listar atualizações deste sinal
        list_updates(conn, signal_id)
        
    except Exception as e:
        print(f"Erro ao mostrar detalhes do sinal: {e}")

def main():
    """Função principal"""
    conn = connect_to_db()
    
    if len(sys.argv) < 2:
        print("Uso: python query_database.py [signals|updates|stats|signal <id>]")
        sys.exit(1)
    
    command = sys.argv[1].lower()
    
    if command == "signals":
        limit = int(sys.argv[2]) if len(sys.argv) > 2 else 10
        list_signals(conn, limit)
    elif command == "updates":
        if len(sys.argv) > 2 and sys.argv[2].isdigit():
            list_updates(conn, int(sys.argv[2]))
        else:
            limit = int(sys.argv[2]) if len(sys.argv) > 2 and sys.argv[2].isdigit() else 10
            list_updates(conn, limit=limit)
    elif command == "stats":
        list_statistics(conn)
    elif command == "signal" and len(sys.argv) > 2:
        show_signal_details(conn, int(sys.argv[2]))
    else:
        print("Comando inválido. Use: signals, updates, stats ou signal <id>")
    
    conn.close()

if __name__ == "__main__":
    main()
