{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport { PAYMENT_CONFIG } from \"../config/api\";\nvar PaymentService = function () {\n  function PaymentService() {\n    _classCallCheck(this, PaymentService);\n    this.activePayments = new Map();\n    this.paymentHistory = [];\n  }\n  return _createClass(PaymentService, [{\n    key: \"createPaymentSession\",\n    value: (function () {\n      var _createPaymentSession = _asyncToGenerator(function* (planId) {\n        var userId = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n        try {\n          var plan = this.getPlanDetails(planId);\n          if (!plan) {\n            throw new Error('Plano não encontrado');\n          }\n          var paymentId = this.generatePaymentId();\n          var session = {\n            id: paymentId,\n            planId: planId,\n            userId: userId,\n            amount: plan.price,\n            currency: 'USDT',\n            status: 'pending',\n            walletAddress: PAYMENT_CONFIG.USDT_WALLET,\n            networks: PAYMENT_CONFIG.SUPPORTED_NETWORKS,\n            createdAt: new Date().toISOString(),\n            expiresAt: new Date(Date.now() + PAYMENT_CONFIG.PAYMENT_TIMEOUT * 60 * 1000).toISOString(),\n            confirmations: 0,\n            requiredConfirmations: 3,\n            transactionHash: null\n          };\n          this.activePayments.set(paymentId, session);\n          console.log('Sessão de pagamento criada:', paymentId);\n          return session;\n        } catch (error) {\n          console.error('Erro ao criar sessão de pagamento:', error);\n          throw error;\n        }\n      });\n      function createPaymentSession(_x) {\n        return _createPaymentSession.apply(this, arguments);\n      }\n      return createPaymentSession;\n    }())\n  }, {\n    key: \"checkPaymentStatus\",\n    value: (function () {\n      var _checkPaymentStatus = _asyncToGenerator(function* (paymentId) {\n        try {\n          var session = this.activePayments.get(paymentId);\n          if (!session) {\n            throw new Error('Sessão de pagamento não encontrada');\n          }\n          if (new Date() > new Date(session.expiresAt)) {\n            session.status = 'expired';\n            this.activePayments.set(paymentId, session);\n            return session;\n          }\n          var mockTransaction = this.simulateBlockchainCheck(session);\n          if (mockTransaction) {\n            session.transactionHash = mockTransaction.hash;\n            session.confirmations = mockTransaction.confirmations;\n            session.network = mockTransaction.network;\n            if (mockTransaction.confirmations >= session.requiredConfirmations) {\n              session.status = 'confirmed';\n              session.confirmedAt = new Date().toISOString();\n              this.paymentHistory.push(_objectSpread({}, session));\n              this.activePayments.delete(paymentId);\n              console.log('Pagamento confirmado:', paymentId);\n            } else {\n              session.status = 'confirming';\n            }\n            this.activePayments.set(paymentId, session);\n          }\n          return session;\n        } catch (error) {\n          console.error('Erro ao verificar status do pagamento:', error);\n          throw error;\n        }\n      });\n      function checkPaymentStatus(_x2) {\n        return _checkPaymentStatus.apply(this, arguments);\n      }\n      return checkPaymentStatus;\n    }())\n  }, {\n    key: \"simulateBlockchainCheck\",\n    value: function simulateBlockchainCheck(session) {\n      if (Math.random() < 0.3) {\n        return {\n          hash: '0x' + Math.random().toString(16).substr(2, 64),\n          confirmations: Math.floor(Math.random() * 6),\n          network: Math.random() < 0.5 ? 'BSC' : 'ETH',\n          amount: session.amount,\n          from: '0x' + Math.random().toString(16).substr(2, 40),\n          to: session.walletAddress\n        };\n      }\n      return null;\n    }\n  }, {\n    key: \"getPlanDetails\",\n    value: function getPlanDetails(planId) {\n      var plans = {\n        pro: {\n          id: 'pro',\n          name: 'Pro',\n          price: PAYMENT_CONFIG.PLAN_PRICES.pro,\n          features: ['15 Signal Channels', 'Priority Signals', 'Advanced Analytics']\n        },\n        elite: {\n          id: 'elite',\n          name: 'Elite',\n          price: PAYMENT_CONFIG.PLAN_PRICES.elite,\n          features: ['Unlimited Channels', 'Auto Trading', 'Premium Support']\n        }\n      };\n      return plans[planId] || null;\n    }\n  }, {\n    key: \"generatePaymentId\",\n    value: function generatePaymentId() {\n      return 'pay_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);\n    }\n  }, {\n    key: \"getPaymentHistory\",\n    value: function getPaymentHistory() {\n      var userId = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;\n      if (userId) {\n        return this.paymentHistory.filter(function (payment) {\n          return payment.userId === userId;\n        });\n      }\n      return this.paymentHistory;\n    }\n  }, {\n    key: \"cancelPayment\",\n    value: function cancelPayment(paymentId) {\n      var session = this.activePayments.get(paymentId);\n      if (session && session.status === 'pending') {\n        session.status = 'cancelled';\n        session.cancelledAt = new Date().toISOString();\n        this.activePayments.set(paymentId, session);\n        return true;\n      }\n      return false;\n    }\n  }, {\n    key: \"getPaymentStats\",\n    value: function getPaymentStats() {\n      var total = this.paymentHistory.length;\n      var confirmed = this.paymentHistory.filter(function (p) {\n        return p.status === 'confirmed';\n      }).length;\n      var totalAmount = this.paymentHistory.filter(function (p) {\n        return p.status === 'confirmed';\n      }).reduce(function (sum, p) {\n        return sum + p.amount;\n      }, 0);\n      return {\n        totalPayments: total,\n        confirmedPayments: confirmed,\n        successRate: total > 0 ? (confirmed / total * 100).toFixed(2) : 0,\n        totalRevenue: totalAmount,\n        activePayments: this.activePayments.size\n      };\n    }\n  }, {\n    key: \"generatePaymentQR\",\n    value: function generatePaymentQR(session) {\n      var network = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'BSC';\n      var networkConfig = PAYMENT_CONFIG.SUPPORTED_NETWORKS[network];\n      if (!networkConfig) {\n        throw new Error('Rede não suportada');\n      }\n      var paymentData = {\n        address: session.walletAddress,\n        amount: session.amount,\n        token: 'USDT',\n        network: network,\n        memo: `CryptoSignals-${session.id}`\n      };\n      var qrData = `ethereum:${session.walletAddress}?value=${session.amount}&token=${networkConfig.usdtContract}`;\n      return {\n        qrData: qrData,\n        paymentData: paymentData,\n        instructions: this.getPaymentInstructions(session, network)\n      };\n    }\n  }, {\n    key: \"getPaymentInstructions\",\n    value: function getPaymentInstructions(session, network) {\n      var networkConfig = PAYMENT_CONFIG.SUPPORTED_NETWORKS[network];\n      return {\n        title: `Pagamento via ${networkConfig.name}`,\n        steps: ['Abra sua carteira crypto (MetaMask, Trust Wallet, etc.)', `Selecione a rede ${networkConfig.name}`, 'Escolha o token USDT', `Envie exatamente ${session.amount} USDT para o endereço abaixo`, 'Aguarde 3 confirmações na blockchain', 'Seu plano será ativado automaticamente'],\n        warnings: ['Envie apenas USDT na rede correta', 'Valores incorretos não serão processados', 'Guarde o hash da transação para referência'],\n        support: 'Em caso de problemas, entre em contato com o suporte'\n      };\n    }\n  }]);\n}();\nvar paymentService = new PaymentService();\nexport default paymentService;", "map": {"version": 3, "names": ["PAYMENT_CONFIG", "PaymentService", "_classCallCheck", "activePayments", "Map", "paymentHistory", "_createClass", "key", "value", "_createPaymentSession", "_asyncToGenerator", "planId", "userId", "arguments", "length", "undefined", "plan", "getPlanDetails", "Error", "paymentId", "generatePaymentId", "session", "id", "amount", "price", "currency", "status", "wallet<PERSON>ddress", "USDT_WALLET", "networks", "SUPPORTED_NETWORKS", "createdAt", "Date", "toISOString", "expiresAt", "now", "PAYMENT_TIMEOUT", "confirmations", "requiredConfirmations", "transactionHash", "set", "console", "log", "error", "createPaymentSession", "_x", "apply", "_checkPaymentStatus", "get", "mockTransaction", "simulateBlockchainCheck", "hash", "network", "confirmedAt", "push", "_objectSpread", "delete", "checkPaymentStatus", "_x2", "Math", "random", "toString", "substr", "floor", "from", "to", "plans", "pro", "name", "PLAN_PRICES", "features", "elite", "getPaymentHistory", "filter", "payment", "cancelPayment", "cancelledAt", "getPaymentStats", "total", "confirmed", "p", "totalAmount", "reduce", "sum", "totalPayments", "confirmedPayments", "successRate", "toFixed", "totalRevenue", "size", "generatePaymentQR", "networkConfig", "paymentData", "address", "token", "memo", "qrData", "usdtContract", "instructions", "getPaymentInstructions", "title", "steps", "warnings", "support", "paymentService"], "sources": ["C:/Users/<USER>/Desktop/Signals/CryptoSignalsApp/src/services/PaymentService.js"], "sourcesContent": ["/**\n * Serviço de Pagamentos USDT\n * Gerencia pagamentos via BEP20 e ERC20\n */\n\nimport { PAYMENT_CONFIG } from '../config/api';\n\nclass PaymentService {\n  constructor() {\n    this.activePayments = new Map();\n    this.paymentHistory = [];\n  }\n\n  /**\n   * Cria uma nova sessão de pagamento\n   */\n  async createPaymentSession(planId, userId = null) {\n    try {\n      const plan = this.getPlanDetails(planId);\n      if (!plan) {\n        throw new Error('Plano não encontrado');\n      }\n\n      const paymentId = this.generatePaymentId();\n      const session = {\n        id: paymentId,\n        planId,\n        userId,\n        amount: plan.price,\n        currency: 'USDT',\n        status: 'pending',\n        walletAddress: PAYMENT_CONFIG.USDT_WALLET,\n        networks: PAYMENT_CONFIG.SUPPORTED_NETWORKS,\n        createdAt: new Date().toISOString(),\n        expiresAt: new Date(Date.now() + PAYMENT_CONFIG.PAYMENT_TIMEOUT * 60 * 1000).toISOString(),\n        confirmations: 0,\n        requiredConfirmations: 3,\n        transactionHash: null\n      };\n\n      this.activePayments.set(paymentId, session);\n      \n      console.log('Sessão de pagamento criada:', paymentId);\n      return session;\n\n    } catch (error) {\n      console.error('Erro ao criar sessão de pagamento:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Verifica o status de um pagamento\n   */\n  async checkPaymentStatus(paymentId) {\n    try {\n      const session = this.activePayments.get(paymentId);\n      if (!session) {\n        throw new Error('Sessão de pagamento não encontrada');\n      }\n\n      // Verificar se expirou\n      if (new Date() > new Date(session.expiresAt)) {\n        session.status = 'expired';\n        this.activePayments.set(paymentId, session);\n        return session;\n      }\n\n      // Simular verificação na blockchain\n      // Em produção, aqui você faria chamadas para APIs da blockchain\n      const mockTransaction = this.simulateBlockchainCheck(session);\n      \n      if (mockTransaction) {\n        session.transactionHash = mockTransaction.hash;\n        session.confirmations = mockTransaction.confirmations;\n        session.network = mockTransaction.network;\n        \n        if (mockTransaction.confirmations >= session.requiredConfirmations) {\n          session.status = 'confirmed';\n          session.confirmedAt = new Date().toISOString();\n          \n          // Mover para histórico\n          this.paymentHistory.push({ ...session });\n          this.activePayments.delete(paymentId);\n          \n          console.log('Pagamento confirmado:', paymentId);\n        } else {\n          session.status = 'confirming';\n        }\n        \n        this.activePayments.set(paymentId, session);\n      }\n\n      return session;\n\n    } catch (error) {\n      console.error('Erro ao verificar status do pagamento:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Simula verificação na blockchain (para demonstração)\n   * Em produção, substituir por chamadas reais para APIs da blockchain\n   */\n  simulateBlockchainCheck(session) {\n    // Simular 30% de chance de encontrar transação\n    if (Math.random() < 0.3) {\n      return {\n        hash: '0x' + Math.random().toString(16).substr(2, 64),\n        confirmations: Math.floor(Math.random() * 6),\n        network: Math.random() < 0.5 ? 'BSC' : 'ETH',\n        amount: session.amount,\n        from: '0x' + Math.random().toString(16).substr(2, 40),\n        to: session.walletAddress\n      };\n    }\n    return null;\n  }\n\n  /**\n   * Obtém detalhes de um plano\n   */\n  getPlanDetails(planId) {\n    const plans = {\n      pro: {\n        id: 'pro',\n        name: 'Pro',\n        price: PAYMENT_CONFIG.PLAN_PRICES.pro,\n        features: ['15 Signal Channels', 'Priority Signals', 'Advanced Analytics']\n      },\n      elite: {\n        id: 'elite',\n        name: 'Elite',\n        price: PAYMENT_CONFIG.PLAN_PRICES.elite,\n        features: ['Unlimited Channels', 'Auto Trading', 'Premium Support']\n      }\n    };\n\n    return plans[planId] || null;\n  }\n\n  /**\n   * Gera ID único para pagamento\n   */\n  generatePaymentId() {\n    return 'pay_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);\n  }\n\n  /**\n   * Obtém histórico de pagamentos\n   */\n  getPaymentHistory(userId = null) {\n    if (userId) {\n      return this.paymentHistory.filter(payment => payment.userId === userId);\n    }\n    return this.paymentHistory;\n  }\n\n  /**\n   * Cancela um pagamento pendente\n   */\n  cancelPayment(paymentId) {\n    const session = this.activePayments.get(paymentId);\n    if (session && session.status === 'pending') {\n      session.status = 'cancelled';\n      session.cancelledAt = new Date().toISOString();\n      this.activePayments.set(paymentId, session);\n      return true;\n    }\n    return false;\n  }\n\n  /**\n   * Obtém estatísticas de pagamentos\n   */\n  getPaymentStats() {\n    const total = this.paymentHistory.length;\n    const confirmed = this.paymentHistory.filter(p => p.status === 'confirmed').length;\n    const totalAmount = this.paymentHistory\n      .filter(p => p.status === 'confirmed')\n      .reduce((sum, p) => sum + p.amount, 0);\n\n    return {\n      totalPayments: total,\n      confirmedPayments: confirmed,\n      successRate: total > 0 ? (confirmed / total * 100).toFixed(2) : 0,\n      totalRevenue: totalAmount,\n      activePayments: this.activePayments.size\n    };\n  }\n\n  /**\n   * Gera QR Code para pagamento\n   */\n  generatePaymentQR(session, network = 'BSC') {\n    const networkConfig = PAYMENT_CONFIG.SUPPORTED_NETWORKS[network];\n    if (!networkConfig) {\n      throw new Error('Rede não suportada');\n    }\n\n    // Formato para QR Code de pagamento crypto\n    const paymentData = {\n      address: session.walletAddress,\n      amount: session.amount,\n      token: 'USDT',\n      network: network,\n      memo: `CryptoSignals-${session.id}`\n    };\n\n    // Em produção, usar biblioteca de QR Code\n    const qrData = `ethereum:${session.walletAddress}?value=${session.amount}&token=${networkConfig.usdtContract}`;\n    \n    return {\n      qrData,\n      paymentData,\n      instructions: this.getPaymentInstructions(session, network)\n    };\n  }\n\n  /**\n   * Gera instruções de pagamento\n   */\n  getPaymentInstructions(session, network) {\n    const networkConfig = PAYMENT_CONFIG.SUPPORTED_NETWORKS[network];\n    \n    return {\n      title: `Pagamento via ${networkConfig.name}`,\n      steps: [\n        'Abra sua carteira crypto (MetaMask, Trust Wallet, etc.)',\n        `Selecione a rede ${networkConfig.name}`,\n        'Escolha o token USDT',\n        `Envie exatamente ${session.amount} USDT para o endereço abaixo`,\n        'Aguarde 3 confirmações na blockchain',\n        'Seu plano será ativado automaticamente'\n      ],\n      warnings: [\n        'Envie apenas USDT na rede correta',\n        'Valores incorretos não serão processados',\n        'Guarde o hash da transação para referência'\n      ],\n      support: 'Em caso de problemas, entre em contato com o suporte'\n    };\n  }\n}\n\n// Instância singleton\nconst paymentService = new PaymentService();\n\nexport default paymentService;\n"], "mappings": ";;;;;;AAKA,SAASA,cAAc;AAAwB,IAEzCC,cAAc;EAClB,SAAAA,eAAA,EAAc;IAAAC,eAAA,OAAAD,cAAA;IACZ,IAAI,CAACE,cAAc,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC/B,IAAI,CAACC,cAAc,GAAG,EAAE;EAC1B;EAAC,OAAAC,YAAA,CAAAL,cAAA;IAAAM,GAAA;IAAAC,KAAA;MAAA,IAAAC,qBAAA,GAAAC,iBAAA,CAKD,WAA2BC,MAAM,EAAiB;QAAA,IAAfC,MAAM,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;QAC9C,IAAI;UACF,IAAMG,IAAI,GAAG,IAAI,CAACC,cAAc,CAACN,MAAM,CAAC;UACxC,IAAI,CAACK,IAAI,EAAE;YACT,MAAM,IAAIE,KAAK,CAAC,sBAAsB,CAAC;UACzC;UAEA,IAAMC,SAAS,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC;UAC1C,IAAMC,OAAO,GAAG;YACdC,EAAE,EAAEH,SAAS;YACbR,MAAM,EAANA,MAAM;YACNC,MAAM,EAANA,MAAM;YACNW,MAAM,EAAEP,IAAI,CAACQ,KAAK;YAClBC,QAAQ,EAAE,MAAM;YAChBC,MAAM,EAAE,SAAS;YACjBC,aAAa,EAAE3B,cAAc,CAAC4B,WAAW;YACzCC,QAAQ,EAAE7B,cAAc,CAAC8B,kBAAkB;YAC3CC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;YACnCC,SAAS,EAAE,IAAIF,IAAI,CAACA,IAAI,CAACG,GAAG,CAAC,CAAC,GAAGnC,cAAc,CAACoC,eAAe,GAAG,EAAE,GAAG,IAAI,CAAC,CAACH,WAAW,CAAC,CAAC;YAC1FI,aAAa,EAAE,CAAC;YAChBC,qBAAqB,EAAE,CAAC;YACxBC,eAAe,EAAE;UACnB,CAAC;UAED,IAAI,CAACpC,cAAc,CAACqC,GAAG,CAACrB,SAAS,EAAEE,OAAO,CAAC;UAE3CoB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEvB,SAAS,CAAC;UACrD,OAAOE,OAAO;QAEhB,CAAC,CAAC,OAAOsB,KAAK,EAAE;UACdF,OAAO,CAACE,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;UAC1D,MAAMA,KAAK;QACb;MACF,CAAC;MAAA,SAjCKC,oBAAoBA,CAAAC,EAAA;QAAA,OAAApC,qBAAA,CAAAqC,KAAA,OAAAjC,SAAA;MAAA;MAAA,OAApB+B,oBAAoB;IAAA;EAAA;IAAArC,GAAA;IAAAC,KAAA;MAAA,IAAAuC,mBAAA,GAAArC,iBAAA,CAsC1B,WAAyBS,SAAS,EAAE;QAClC,IAAI;UACF,IAAME,OAAO,GAAG,IAAI,CAAClB,cAAc,CAAC6C,GAAG,CAAC7B,SAAS,CAAC;UAClD,IAAI,CAACE,OAAO,EAAE;YACZ,MAAM,IAAIH,KAAK,CAAC,oCAAoC,CAAC;UACvD;UAGA,IAAI,IAAIc,IAAI,CAAC,CAAC,GAAG,IAAIA,IAAI,CAACX,OAAO,CAACa,SAAS,CAAC,EAAE;YAC5Cb,OAAO,CAACK,MAAM,GAAG,SAAS;YAC1B,IAAI,CAACvB,cAAc,CAACqC,GAAG,CAACrB,SAAS,EAAEE,OAAO,CAAC;YAC3C,OAAOA,OAAO;UAChB;UAIA,IAAM4B,eAAe,GAAG,IAAI,CAACC,uBAAuB,CAAC7B,OAAO,CAAC;UAE7D,IAAI4B,eAAe,EAAE;YACnB5B,OAAO,CAACkB,eAAe,GAAGU,eAAe,CAACE,IAAI;YAC9C9B,OAAO,CAACgB,aAAa,GAAGY,eAAe,CAACZ,aAAa;YACrDhB,OAAO,CAAC+B,OAAO,GAAGH,eAAe,CAACG,OAAO;YAEzC,IAAIH,eAAe,CAACZ,aAAa,IAAIhB,OAAO,CAACiB,qBAAqB,EAAE;cAClEjB,OAAO,CAACK,MAAM,GAAG,WAAW;cAC5BL,OAAO,CAACgC,WAAW,GAAG,IAAIrB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;cAG9C,IAAI,CAAC5B,cAAc,CAACiD,IAAI,CAAAC,aAAA,KAAMlC,OAAO,CAAE,CAAC;cACxC,IAAI,CAAClB,cAAc,CAACqD,MAAM,CAACrC,SAAS,CAAC;cAErCsB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEvB,SAAS,CAAC;YACjD,CAAC,MAAM;cACLE,OAAO,CAACK,MAAM,GAAG,YAAY;YAC/B;YAEA,IAAI,CAACvB,cAAc,CAACqC,GAAG,CAACrB,SAAS,EAAEE,OAAO,CAAC;UAC7C;UAEA,OAAOA,OAAO;QAEhB,CAAC,CAAC,OAAOsB,KAAK,EAAE;UACdF,OAAO,CAACE,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;UAC9D,MAAMA,KAAK;QACb;MACF,CAAC;MAAA,SA7CKc,kBAAkBA,CAAAC,GAAA;QAAA,OAAAX,mBAAA,CAAAD,KAAA,OAAAjC,SAAA;MAAA;MAAA,OAAlB4C,kBAAkB;IAAA;EAAA;IAAAlD,GAAA;IAAAC,KAAA,EAmDxB,SAAA0C,uBAAuBA,CAAC7B,OAAO,EAAE;MAE/B,IAAIsC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,EAAE;QACvB,OAAO;UACLT,IAAI,EAAE,IAAI,GAAGQ,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC;UACrDzB,aAAa,EAAEsB,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;UAC5CR,OAAO,EAAEO,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,KAAK,GAAG,KAAK;UAC5CrC,MAAM,EAAEF,OAAO,CAACE,MAAM;UACtByC,IAAI,EAAE,IAAI,GAAGL,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC;UACrDG,EAAE,EAAE5C,OAAO,CAACM;QACd,CAAC;MACH;MACA,OAAO,IAAI;IACb;EAAC;IAAApB,GAAA;IAAAC,KAAA,EAKD,SAAAS,cAAcA,CAACN,MAAM,EAAE;MACrB,IAAMuD,KAAK,GAAG;QACZC,GAAG,EAAE;UACH7C,EAAE,EAAE,KAAK;UACT8C,IAAI,EAAE,KAAK;UACX5C,KAAK,EAAExB,cAAc,CAACqE,WAAW,CAACF,GAAG;UACrCG,QAAQ,EAAE,CAAC,oBAAoB,EAAE,kBAAkB,EAAE,oBAAoB;QAC3E,CAAC;QACDC,KAAK,EAAE;UACLjD,EAAE,EAAE,OAAO;UACX8C,IAAI,EAAE,OAAO;UACb5C,KAAK,EAAExB,cAAc,CAACqE,WAAW,CAACE,KAAK;UACvCD,QAAQ,EAAE,CAAC,oBAAoB,EAAE,cAAc,EAAE,iBAAiB;QACpE;MACF,CAAC;MAED,OAAOJ,KAAK,CAACvD,MAAM,CAAC,IAAI,IAAI;IAC9B;EAAC;IAAAJ,GAAA;IAAAC,KAAA,EAKD,SAAAY,iBAAiBA,CAAA,EAAG;MAClB,OAAO,MAAM,GAAGY,IAAI,CAACG,GAAG,CAAC,CAAC,GAAG,GAAG,GAAGwB,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5E;EAAC;IAAAvD,GAAA;IAAAC,KAAA,EAKD,SAAAgE,iBAAiBA,CAAA,EAAgB;MAAA,IAAf5D,MAAM,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;MAC7B,IAAID,MAAM,EAAE;QACV,OAAO,IAAI,CAACP,cAAc,CAACoE,MAAM,CAAC,UAAAC,OAAO;UAAA,OAAIA,OAAO,CAAC9D,MAAM,KAAKA,MAAM;QAAA,EAAC;MACzE;MACA,OAAO,IAAI,CAACP,cAAc;IAC5B;EAAC;IAAAE,GAAA;IAAAC,KAAA,EAKD,SAAAmE,aAAaA,CAACxD,SAAS,EAAE;MACvB,IAAME,OAAO,GAAG,IAAI,CAAClB,cAAc,CAAC6C,GAAG,CAAC7B,SAAS,CAAC;MAClD,IAAIE,OAAO,IAAIA,OAAO,CAACK,MAAM,KAAK,SAAS,EAAE;QAC3CL,OAAO,CAACK,MAAM,GAAG,WAAW;QAC5BL,OAAO,CAACuD,WAAW,GAAG,IAAI5C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QAC9C,IAAI,CAAC9B,cAAc,CAACqC,GAAG,CAACrB,SAAS,EAAEE,OAAO,CAAC;QAC3C,OAAO,IAAI;MACb;MACA,OAAO,KAAK;IACd;EAAC;IAAAd,GAAA;IAAAC,KAAA,EAKD,SAAAqE,eAAeA,CAAA,EAAG;MAChB,IAAMC,KAAK,GAAG,IAAI,CAACzE,cAAc,CAACS,MAAM;MACxC,IAAMiE,SAAS,GAAG,IAAI,CAAC1E,cAAc,CAACoE,MAAM,CAAC,UAAAO,CAAC;QAAA,OAAIA,CAAC,CAACtD,MAAM,KAAK,WAAW;MAAA,EAAC,CAACZ,MAAM;MAClF,IAAMmE,WAAW,GAAG,IAAI,CAAC5E,cAAc,CACpCoE,MAAM,CAAC,UAAAO,CAAC;QAAA,OAAIA,CAAC,CAACtD,MAAM,KAAK,WAAW;MAAA,EAAC,CACrCwD,MAAM,CAAC,UAACC,GAAG,EAAEH,CAAC;QAAA,OAAKG,GAAG,GAAGH,CAAC,CAACzD,MAAM;MAAA,GAAE,CAAC,CAAC;MAExC,OAAO;QACL6D,aAAa,EAAEN,KAAK;QACpBO,iBAAiB,EAAEN,SAAS;QAC5BO,WAAW,EAAER,KAAK,GAAG,CAAC,GAAG,CAACC,SAAS,GAAGD,KAAK,GAAG,GAAG,EAAES,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;QACjEC,YAAY,EAAEP,WAAW;QACzB9E,cAAc,EAAE,IAAI,CAACA,cAAc,CAACsF;MACtC,CAAC;IACH;EAAC;IAAAlF,GAAA;IAAAC,KAAA,EAKD,SAAAkF,iBAAiBA,CAACrE,OAAO,EAAmB;MAAA,IAAjB+B,OAAO,GAAAvC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;MACxC,IAAM8E,aAAa,GAAG3F,cAAc,CAAC8B,kBAAkB,CAACsB,OAAO,CAAC;MAChE,IAAI,CAACuC,aAAa,EAAE;QAClB,MAAM,IAAIzE,KAAK,CAAC,oBAAoB,CAAC;MACvC;MAGA,IAAM0E,WAAW,GAAG;QAClBC,OAAO,EAAExE,OAAO,CAACM,aAAa;QAC9BJ,MAAM,EAAEF,OAAO,CAACE,MAAM;QACtBuE,KAAK,EAAE,MAAM;QACb1C,OAAO,EAAEA,OAAO;QAChB2C,IAAI,EAAE,iBAAiB1E,OAAO,CAACC,EAAE;MACnC,CAAC;MAGD,IAAM0E,MAAM,GAAG,YAAY3E,OAAO,CAACM,aAAa,UAAUN,OAAO,CAACE,MAAM,UAAUoE,aAAa,CAACM,YAAY,EAAE;MAE9G,OAAO;QACLD,MAAM,EAANA,MAAM;QACNJ,WAAW,EAAXA,WAAW;QACXM,YAAY,EAAE,IAAI,CAACC,sBAAsB,CAAC9E,OAAO,EAAE+B,OAAO;MAC5D,CAAC;IACH;EAAC;IAAA7C,GAAA;IAAAC,KAAA,EAKD,SAAA2F,sBAAsBA,CAAC9E,OAAO,EAAE+B,OAAO,EAAE;MACvC,IAAMuC,aAAa,GAAG3F,cAAc,CAAC8B,kBAAkB,CAACsB,OAAO,CAAC;MAEhE,OAAO;QACLgD,KAAK,EAAE,iBAAiBT,aAAa,CAACvB,IAAI,EAAE;QAC5CiC,KAAK,EAAE,CACL,yDAAyD,EACzD,oBAAoBV,aAAa,CAACvB,IAAI,EAAE,EACxC,sBAAsB,EACtB,oBAAoB/C,OAAO,CAACE,MAAM,8BAA8B,EAChE,sCAAsC,EACtC,wCAAwC,CACzC;QACD+E,QAAQ,EAAE,CACR,mCAAmC,EACnC,0CAA0C,EAC1C,4CAA4C,CAC7C;QACDC,OAAO,EAAE;MACX,CAAC;IACH;EAAC;AAAA;AAIH,IAAMC,cAAc,GAAG,IAAIvG,cAAc,CAAC,CAAC;AAE3C,eAAeuG,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}