{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport { useEffect, useState } from 'react';\nimport { loadAsync } from \"./Font\";\nexport function useFonts(map) {\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    loaded = _useState2[0],\n    setLoaded = _useState2[1];\n  var _useState3 = useState(null),\n    _useState4 = _slicedToArray(_useState3, 2),\n    error = _useState4[0],\n    setError = _useState4[1];\n  useEffect(function () {\n    loadAsync(map).then(function () {\n      return setLoaded(true);\n    }).catch(setError);\n  }, []);\n  return [loaded, error];\n}", "map": {"version": 3, "names": ["useEffect", "useState", "loadAsync", "useFonts", "map", "_useState", "_useState2", "_slicedToArray", "loaded", "setLoaded", "_useState3", "_useState4", "error", "setError", "then", "catch"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\expo-font\\src\\FontHooks.ts"], "sourcesContent": ["import { useEffect, useState } from 'react';\n\nimport { loadAsync } from './Font';\nimport { FontSource } from './Font.types';\n\n// @needsAudit\n/**\n * ```ts\n * const [loaded, error] = useFonts({ ... });\n * ```\n * Load a map of fonts with [`loadAsync`](#loadasync). This returns a `boolean` if the fonts are\n * loaded and ready to use. It also returns an error if something went wrong, to use in development.\n *\n * > Note, the fonts are not \"reloaded\" when you dynamically change the font map.\n *\n * @param map A map of `fontFamily`s to [`FontSource`](#fontsource)s. After loading the font you can\n * use the key in the `fontFamily` style prop of a `Text` element.\n *\n * @return\n * - __loaded__ (`boolean`) - A boolean to detect if the font for `fontFamily` has finished\n * loading.\n * - __error__ (`Error | null`) - An error encountered when loading the fonts.\n */\nexport function useFonts(map: string | Record<string, FontSource>): [boolean, Error | null] {\n  const [loaded, setLoaded] = useState(false);\n  const [error, setError] = useState<Error | null>(null);\n\n  useEffect(() => {\n    loadAsync(map)\n      .then(() => setLoaded(true))\n      .catch(setError);\n  }, []);\n\n  return [loaded, error];\n}\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAE3C,SAASC,SAAS;AAqBlB,OAAM,SAAUC,QAAQA,CAACC,GAAwC;EAC/D,IAAAC,SAAA,GAA4BJ,QAAQ,CAAC,KAAK,CAAC;IAAAK,UAAA,GAAAC,cAAA,CAAAF,SAAA;IAApCG,MAAM,GAAAF,UAAA;IAAEG,SAAS,GAAAH,UAAA;EACxB,IAAAI,UAAA,GAA0BT,QAAQ,CAAe,IAAI,CAAC;IAAAU,UAAA,GAAAJ,cAAA,CAAAG,UAAA;IAA/CE,KAAK,GAAAD,UAAA;IAAEE,QAAQ,GAAAF,UAAA;EAEtBX,SAAS,CAAC,YAAK;IACbE,SAAS,CAACE,GAAG,CAAC,CACXU,IAAI,CAAC;MAAA,OAAML,SAAS,CAAC,IAAI,CAAC;IAAA,EAAC,CAC3BM,KAAK,CAACF,QAAQ,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAEN,OAAO,CAACL,MAAM,EAAEI,KAAK,CAAC;AACxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}