import React, { useEffect, useState, useRef, useContext } from "react";
import { AppState, ScrollView } from "react-native";
import { Text } from 'react-native-paper';
import { View } from 'react-native';
import Wrapper from "../../components/Wrapper";
import PageTitle from "../../components/PageTitle";
import InputSearch from "../../components/InputSearch";
import Loading from "../../components/Loading";
import { StoreContext } from "../../store/index";
import SignalsCard from "../Signals/Card";
import styles from "./styles";
import { AxiosContext } from "../../store/axios";

const Signals = ({ route }) => {
  const [state, dispatch] = useContext(StoreContext);
  const [api] = useContext(AxiosContext);
  const [_, setAppState] = useState(AppState.currentState);
  const timeoutRef = useRef();
  const [isLoading, setIsLoading] = useState(true);
  const [signals, setSignals] = useState([]);
  const [signalsImmutable, setSignalsImmutable] = useState([]);
  const [inputSearchValue, setInputSearchValue] = useState("");
  const [skip, setSkip] = useState(0);
  const [effectiveSearchPairs, setEffectiveSearchPairs] = useState("");
  const [canGetSignals, setCanGetSignals] = useState(true);

  const { params } = route;
  const { channelId, channelName } = params;
  const signalFromWebsocket = state?.signalFromWebsocket[channelId] ?? null;

  const getSignals = async (skipValue) => {
    try {
      console.log(`Carregando sinais do canal ${channelId}, skip: ${skipValue}`);

      const response = await api.get(
        `/channels/${channelId}/signals?skip=${skipValue}&limit=20`
      );

      if (response.data && Array.isArray(response.data)) {
        console.log(`Sinais carregados: ${response.data.length}`);

        if (response.data.length === 0) {
          setCanGetSignals(false);
        }

        if (skipValue === 0) {
          // Primeira carga ou refresh
          setSignals(response.data);
          setSignalsImmutable(response.data);
        } else {
          // Carregamento adicional (paginação)
          setSignals(prev => [...prev, ...response.data]);
          setSignalsImmutable(prev => [...prev, ...response.data]);
        }
      } else {
        console.warn('Resposta da API não contém array de sinais:', response.data);
        if (skipValue === 0) {
          setSignals([]);
          setSignalsImmutable([]);
        }
      }

      setIsLoading(false);
    } catch (e) {
      console.error('Erro ao carregar sinais:', e);
      setIsLoading(false);

      // Em caso de erro na primeira carga, definir arrays vazios
      if (skipValue === 0) {
        setSignals([]);
        setSignalsImmutable([]);
      }
    }
  };

  const getSignalsSearch = async (search) => {
    try {
      console.log(`Buscando sinais com termo: ${search}`);

      const response = await api.get(
        `/channels/${channelId}/signals?search=${search}&limit=50`
      );

      if (response.data && Array.isArray(response.data)) {
        console.log(`Sinais encontrados na busca: ${response.data.length}`);
        setSignals(response.data);
      } else {
        console.warn('Resposta da busca não contém array de sinais:', response.data);
        setSignals([]);
      }

      setIsLoading(false);
    } catch (e) {
      console.error('Erro ao buscar sinais:', e);
      setSignals([]);
      setIsLoading(false);
    }
  };

  const handleScrollView = ({
    layoutMeasurement,
    contentOffset,
    contentSize,
  }) => {
    if (inputSearchValue) {
      return;
    }

    const paddingToBottom = 20;
    const isCloseToBottom =
      layoutMeasurement.height + contentOffset.y >=
      contentSize.height - paddingToBottom;

    if (!isCloseToBottom) {
      return;
    }

    if (!canGetSignals) {
      return;
    }

    setSkip(skip + 1);
  };

  const handleSearchPairs = (value) => {
    setInputSearchValue(value);
    setEffectiveSearchPairs(value);
  };

  useEffect(() => {
    const appStateListener = AppState.addEventListener(
      "change",
      (nextAppState) => {
        setAppState(nextAppState);

        if (nextAppState === "active") {
          setIsLoading(true);
          setSignals(signalsImmutable);
        }
      }
    );

    return () => {
      appStateListener?.remove();
    };
  }, []);

  // Carregar sinais inicialmente
  useEffect(() => {
    if (channelId) {
      console.log('Carregando sinais iniciais para canal:', channelId);
      setIsLoading(true);
      getSignals(0);
      setSkip(0);
    }
  }, [channelId]);

  useEffect(() => {
    if (skip > 0) {
      setIsLoading(true);
      getSignals(skip);
    }
  }, [skip]);

  useEffect(() => {
    if (!effectiveSearchPairs) {
      setIsLoading(false);

      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      setSignals(signalsImmutable);

      return;
    }

    timeoutRef.current = setTimeout(async () => {
      setIsLoading(true);
      await getSignalsSearch(effectiveSearchPairs);
    }, 700);

    return () => {
      clearTimeout(timeoutRef.current);
    };
  }, [effectiveSearchPairs]);

  useEffect(() => {
    if (!signals.length) return;
    // Está funcionando sem a necessidade de usar o setSignals
    if (signalFromWebsocket) {
      signals.unshift(signalFromWebsocket);
      signalsImmutable.unshift(signalFromWebsocket);
    };
  }, [signalFromWebsocket]);

  return (
    <Wrapper>
      <PageTitle
        key="TitleSignals"
        text={channelName ?? "Noname"}
        goBack="Channels"
      />

      <View style={styles.searchContainer}>  // Adicionado estilo específico para alinhar o InputSearch
        <InputSearch
          key="InputSearchSignals"
          onChangeText={(text) => handleSearchPairs(text)}
          placeholder="Search Pairs"
          value={inputSearchValue}
        />
      </View>

      <ScrollView
        style={styles.scrollViewSignals}
        scrollEventThrottle={400}
        onScroll={({ nativeEvent }) => handleScrollView(nativeEvent)}
      >
        {inputSearchValue && !signals.length && !isLoading && (
          <Text style={styles.emptyState}>Nenhum sinal encontrado para "{inputSearchValue}".</Text>
        )}

        {!inputSearchValue && !signals.length && !isLoading && (
          <View style={{ padding: 20, alignItems: 'center' }}>
            <Text style={[styles.emptyState, { marginBottom: 8 }]}>
              Nenhum sinal disponível
            </Text>
            <Text style={{ color: '#666', fontSize: 12, textAlign: 'center' }}>
              Este canal ainda não possui sinais ou o gerador não está ativo.
            </Text>
          </View>
        )}

        {signals.map((signal, index) => (
          <SignalsCard
            key={`SignalsCard--${signal.id || index}`}
            createdAt={signal.createdAt}
            message={signal.messageOriginal}
            signal={signal}
          />
        ))}
      </ScrollView>

      {isLoading && (
        <View style={styles.containerLoading}>
          <Loading />
        </View>
      )}
    </Wrapper>
  );
};

export default Signals;