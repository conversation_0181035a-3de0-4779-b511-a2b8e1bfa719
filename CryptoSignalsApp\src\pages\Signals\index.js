import React, { useEffect, useState, useRef, useContext } from "react";
import { AppState, ScrollView } from "react-native";
import { Text } from 'react-native-paper';
import { View } from 'react-native';
import Wrapper from "../../components/Wrapper";
import PageTitle from "../../components/PageTitle";
import InputSearch from "../../components/InputSearch";
import Loading from "../../components/Loading";
import { StoreContext } from "../../store/index";
import SignalsCard from "../Signals/Card";
import styles from "./styles";
import { AxiosContext } from "../../store/axios";

const Signals = ({ route }) => {
  const [state, dispatch] = useContext(StoreContext);
  const [api] = useContext(AxiosContext);
  const [_, setAppState] = useState(AppState.currentState);
  const timeoutRef = useRef();
  const [isLoading, setIsLoading] = useState(true);
  const [signals, setSignals] = useState([]);
  const [signalsImmutable, setSignalsImmutable] = useState([]);
  const [inputSearchValue, setInputSearchValue] = useState("");
  const [skip, setSkip] = useState(0);
  const [effectiveSearchPairs, setEffectiveSearchPairs] = useState("");
  const [canGetSignals, setCanGetSignals] = useState(true);

  const { params } = route;
  const { channelId, channelName } = params;
  const signalFromWebsocket = state?.signalFromWebsocket[channelId] ?? null;

  const getSignals = async (skip) => {
    try {
      const response = await api.get(
        `/channels/${channelId}/signals?skip=${skip}`
      );

      if (response.data.length === 0) {
        setCanGetSignals(false);
      }

      setSignals([...signals, ...response.data]);
      setSignalsImmutable([...signals, ...response.data]);
      setIsLoading(false);
    } catch (e) {
      console.error(e);
      setIsLoading(false);
    }
  };

  const getSignalsSearch = async (search) => {
    try {
      const response = await api.get(
        `/channels/${channelId}/signals?search=${search}`
      );

      setSignals([...response.data]);
      setIsLoading(false);
    } catch (e) {
      console.error(e);
      setIsLoading(false);
    }
  };

  const handleScrollView = ({
    layoutMeasurement,
    contentOffset,
    contentSize,
  }) => {
    if (inputSearchValue) {
      return;
    }

    const paddingToBottom = 20;
    const isCloseToBottom =
      layoutMeasurement.height + contentOffset.y >=
      contentSize.height - paddingToBottom;

    if (!isCloseToBottom) {
      return;
    }

    if (!canGetSignals) {
      return;
    }

    setSkip(skip + 1);
  };

  const handleSearchPairs = (value) => {
    setInputSearchValue(value);
    setEffectiveSearchPairs(value);
  };

  useEffect(() => {
    const appStateListener = AppState.addEventListener(
      "change",
      (nextAppState) => {
        setAppState(nextAppState);

        if (nextAppState === "active") {
          setIsLoading(true);
          setSignals(signalsImmutable);
        }
      }
    );

    return () => {
      appStateListener?.remove();
    };
  }, []);

  useEffect(() => {
    setIsLoading(true);
    getSignals(skip);
  }, [skip]);

  useEffect(() => {
    if (!effectiveSearchPairs) {
      setIsLoading(false);

      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      setSignals(signalsImmutable);

      return;
    }

    timeoutRef.current = setTimeout(async () => {
      setIsLoading(true);
      await getSignalsSearch(effectiveSearchPairs);
    }, 700);

    return () => {
      clearTimeout(timeoutRef.current);
    };
  }, [effectiveSearchPairs]);

  useEffect(() => {
    if (!signals.length) return;
    // Está funcionando sem a necessidade de usar o setSignals
    if (signalFromWebsocket) {
      signals.unshift(signalFromWebsocket);
      signalsImmutable.unshift(signalFromWebsocket);
    };
  }, [signalFromWebsocket]);

  return (
    <Wrapper>
      <PageTitle
        key="TitleSignals"
        text={channelName ?? "Noname"}
        goBack="Channels"
      />

      <View style={styles.searchContainer}>  // Adicionado estilo específico para alinhar o InputSearch
        <InputSearch
          key="InputSearchSignals"
          onChangeText={(text) => handleSearchPairs(text)}
          placeholder="Search Pairs"
          value={inputSearchValue}
        />
      </View>

      <ScrollView
        style={styles.scrollViewSignals}
        scrollEventThrottle={400}
        onScroll={({ nativeEvent }) => handleScrollView(nativeEvent)}
      >
        {inputSearchValue && !signals.length && (
          <Text style={styles.emptyState}>No matching signals found.</Text>
        )}

        {signals.map((signal, index) => (
          <SignalsCard
            key={`SignalsCard--${index}`}
            createdAt={signal.createdAt}
            message={signal.messageOriginal}
          />
        ))}
      </ScrollView>

      {isLoading && (
        <View style={styles.containerLoading}>
          <Loading />
        </View>
      )}
    </Wrapper>
  );
};

export default Signals;