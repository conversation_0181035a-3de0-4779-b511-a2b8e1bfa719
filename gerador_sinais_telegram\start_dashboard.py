#!/usr/bin/env python3
"""
Script de inicialização do Dashboard Web CryptoSignals
Instala dependências automaticamente e inicia o dashboard
"""

import subprocess
import sys
import os
import time
from pathlib import Path

def print_colored(text, color=""):
    """Imprime texto colorido"""
    colors = {
        "red": "\033[91m",
        "green": "\033[92m",
        "yellow": "\033[93m",
        "blue": "\033[94m",
        "purple": "\033[95m",
        "cyan": "\033[96m",
        "white": "\033[97m",
        "bold": "\033[1m",
        "end": "\033[0m"
    }
    
    if color in colors:
        print(f"{colors[color]}{text}{colors['end']}")
    else:
        print(text)

def check_python_version():
    """Verifica se a versão do Python é adequada"""
    if sys.version_info < (3, 7):
        print_colored("❌ Python 3.7+ é necessário!", "red")
        print_colored(f"Versão atual: {sys.version}", "yellow")
        return False
    
    print_colored(f"✅ Python {sys.version.split()[0]} detectado", "green")
    return True

def install_dependencies():
    """Instala as dependências necessárias"""
    print_colored("\n📦 Instalando dependências do dashboard...", "blue")
    
    requirements_file = Path("requirements_dashboard.txt")
    
    if not requirements_file.exists():
        print_colored("❌ Arquivo requirements_dashboard.txt não encontrado!", "red")
        return False
    
    try:
        # Instalar dependências
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print_colored("✅ Dependências instaladas com sucesso!", "green")
            return True
        else:
            print_colored("❌ Erro ao instalar dependências:", "red")
            print_colored(result.stderr, "yellow")
            return False
            
    except Exception as e:
        print_colored(f"❌ Erro ao instalar dependências: {e}", "red")
        return False

def check_templates_directory():
    """Verifica se o diretório templates existe"""
    templates_dir = Path("templates")
    
    if not templates_dir.exists():
        print_colored("📁 Criando diretório templates...", "yellow")
        templates_dir.mkdir(exist_ok=True)
    
    dashboard_template = templates_dir / "dashboard.html"
    if not dashboard_template.exists():
        print_colored("❌ Arquivo templates/dashboard.html não encontrado!", "red")
        print_colored("Certifique-se de que o arquivo dashboard.html está no diretório templates/", "yellow")
        return False
    
    print_colored("✅ Template dashboard.html encontrado", "green")
    return True

def start_dashboard():
    """Inicia o dashboard web"""
    print_colored("\n🚀 Iniciando Dashboard Web...", "blue")
    print_colored("=" * 60, "cyan")
    
    try:
        # Importar e executar o dashboard
        import web_dashboard
        web_dashboard.main()
        
    except ImportError as e:
        print_colored(f"❌ Erro ao importar web_dashboard: {e}", "red")
        print_colored("Certifique-se de que o arquivo web_dashboard.py existe", "yellow")
        return False
    except KeyboardInterrupt:
        print_colored("\n🛑 Dashboard interrompido pelo usuário", "yellow")
        return True
    except Exception as e:
        print_colored(f"❌ Erro ao iniciar dashboard: {e}", "red")
        return False

def show_info():
    """Mostra informações sobre o dashboard"""
    print_colored("\n" + "=" * 60, "cyan")
    print_colored("🖥️  CRYPTOSIGNALS - DASHBOARD WEB", "bold")
    print_colored("=" * 60, "cyan")
    print_colored("\n📊 Dashboard de Monitoramento em Tempo Real", "white")
    print_colored("\n🔧 Funcionalidades:", "blue")
    print_colored("  • Monitoramento de serviços em tempo real", "white")
    print_colored("  • Métricas do servidor (CPU, RAM, Disco)", "white")
    print_colored("  • Visualização de logs", "white")
    print_colored("  • Sistema de alertas", "white")
    print_colored("  • Gráficos de performance", "white")
    print_colored("  • Interface responsiva", "white")
    print_colored("\n🌐 Acesso:", "blue")
    print_colored("  • Local: http://localhost:5000", "green")
    print_colored("  • Rede: http://SEU_IP:5000", "green")
    print_colored("\n⚡ Atualizações automáticas a cada 10 segundos", "yellow")
    print_colored("🔄 WebSocket para dados em tempo real", "yellow")
    print_colored("\n" + "=" * 60, "cyan")

def main():
    """Função principal"""
    show_info()
    
    print_colored("\n🔍 Verificando sistema...", "blue")
    
    # Verificar Python
    if not check_python_version():
        return
    
    # Verificar templates
    if not check_templates_directory():
        return
    
    # Perguntar se quer instalar dependências
    print_colored("\n📦 Verificação de dependências:", "blue")
    install_deps = input("Deseja instalar/atualizar as dependências? (y/n): ").strip().lower()
    
    if install_deps in ['y', 'yes', 's', 'sim']:
        if not install_dependencies():
            print_colored("\n❌ Falha na instalação de dependências", "red")
            print_colored("Tente instalar manualmente:", "yellow")
            print_colored("pip install -r requirements_dashboard.txt", "white")
            return
    else:
        print_colored("⚠️ Pulando instalação de dependências", "yellow")
        print_colored("Certifique-se de que as dependências estão instaladas", "yellow")
    
    # Aguardar um momento
    print_colored("\n⏳ Preparando dashboard...", "yellow")
    time.sleep(2)
    
    # Iniciar dashboard
    start_dashboard()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print_colored("\n👋 Encerrando...", "yellow")
    except Exception as e:
        print_colored(f"\n❌ Erro inesperado: {e}", "red")
        print_colored("Verifique se todos os arquivos estão presentes:", "yellow")
        print_colored("  • web_dashboard.py", "white")
        print_colored("  • templates/dashboard.html", "white")
        print_colored("  • requirements_dashboard.txt", "white")
