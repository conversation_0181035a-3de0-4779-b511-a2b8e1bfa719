import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import HomeNavigator from './path_to_HomeNavigator';  // Importando o HomeNavigator
import Login from './path_to_Login';

// Importe os outros componentes de tela que não estão em HomeNavigator
import SignUp from './path_to_SignUp';
import RecoveryAccount from './path_to_RecoveryAccount';
import Premium from './path_to_Premium';
import CompareCripto from './path_to_CompareCripto';
import Rank from './path_to_Rank';
import Channels from './path_to_Channels';
import News from './path_to_News';
import Forum from './path_to_Forum';
import Academy from './path_to_Academy';
import SystemReward from './path_to_SystemReward';
import Support from './path_to_Support';
import User from './path_to_User';

const Stack = createStackNavigator();

const AppNavigator = () => {
  return (
    <Stack.Navigator initialRouteName="Home">
      <Stack.Screen name="Home" component={HomeNavigator} options={{ headerShown: false }} />
      <Stack.Screen name="Login" component={Login} />
      <Stack.Screen name="SignUp" component={SignUp} />
      <Stack.Screen name="RecoveryAccount" component={RecoveryAccount} />
      <Stack.Screen name="Premium" component={Premium} />
      <Stack.Screen name="CompareCripto" component={CompareCripto} />
      <Stack.Screen name="Rank" component={Rank} />
      <Stack.Screen name="Channels" component={Channels} />
      <Stack.Screen name="News" component={News} />
      <Stack.Screen name="Forum" component={Forum} />
      <Stack.Screen name="Academy" component={Academy} />
      <Stack.Screen name="SystemReward" component={SystemReward} />
      <Stack.Screen name="Support" component={Support} />
      <Stack.Screen name="User" component={User} />
      {/* ... outras telas, se houver */}
    </Stack.Navigator>
  );
};

export default AppNavigator;
