{"ast": null, "code": "import color from 'color';\nvar getAndroidCheckedColor = function getAndroidCheckedColor(_ref) {\n  var theme = _ref.theme,\n    customColor = _ref.customColor;\n  if (customColor) {\n    return customColor;\n  }\n  if (theme.isV3) {\n    return theme.colors.primary;\n  }\n  return theme.colors.accent;\n};\nvar getAndroidUncheckedColor = function getAndroidUncheckedColor(_ref2) {\n  var theme = _ref2.theme,\n    customUncheckedColor = _ref2.customUncheckedColor;\n  if (customUncheckedColor) {\n    return customUncheckedColor;\n  }\n  if (theme.isV3) {\n    return theme.colors.onSurfaceVariant;\n  }\n  if (theme.dark) {\n    return color(theme.colors.text).alpha(0.7).rgb().string();\n  }\n  return color(theme.colors.text).alpha(0.54).rgb().string();\n};\nvar getAndroidRippleColor = function getAndroidRippleColor(_ref3) {\n  var theme = _ref3.theme,\n    checkedColor = _ref3.checkedColor,\n    disabled = _ref3.disabled;\n  if (disabled) {\n    if (theme.isV3) {\n      return color(theme.colors.onSurface).alpha(0.16).rgb().string();\n    }\n    return color(theme.colors.text).alpha(0.16).rgb().string();\n  }\n  return color(checkedColor).fade(0.32).rgb().string();\n};\nvar getAndroidControlColor = function getAndroidControlColor(_ref4) {\n  var theme = _ref4.theme,\n    checked = _ref4.checked,\n    disabled = _ref4.disabled,\n    checkedColor = _ref4.checkedColor,\n    uncheckedColor = _ref4.uncheckedColor;\n  if (disabled) {\n    if (theme.isV3) {\n      return theme.colors.onSurfaceDisabled;\n    }\n    return theme.colors.disabled;\n  }\n  if (checked) {\n    return checkedColor;\n  }\n  return uncheckedColor;\n};\nexport var getAndroidSelectionControlColor = function getAndroidSelectionControlColor(_ref5) {\n  var theme = _ref5.theme,\n    disabled = _ref5.disabled,\n    checked = _ref5.checked,\n    customColor = _ref5.customColor,\n    customUncheckedColor = _ref5.customUncheckedColor;\n  var checkedColor = getAndroidCheckedColor({\n    theme: theme,\n    customColor: customColor\n  });\n  var uncheckedColor = getAndroidUncheckedColor({\n    theme: theme,\n    customUncheckedColor: customUncheckedColor\n  });\n  return {\n    rippleColor: getAndroidRippleColor({\n      theme: theme,\n      checkedColor: checkedColor,\n      disabled: disabled\n    }),\n    selectionControlColor: getAndroidControlColor({\n      theme: theme,\n      disabled: disabled,\n      checked: checked,\n      checkedColor: checkedColor,\n      uncheckedColor: uncheckedColor\n    })\n  };\n};\nvar getIOSCheckedColor = function getIOSCheckedColor(_ref6) {\n  var theme = _ref6.theme,\n    disabled = _ref6.disabled,\n    customColor = _ref6.customColor;\n  if (disabled) {\n    if (theme.isV3) {\n      return theme.colors.onSurfaceDisabled;\n    }\n    return theme.colors.disabled;\n  }\n  if (customColor) {\n    return customColor;\n  }\n  if (theme.isV3) {\n    return theme.colors.primary;\n  }\n  return theme.colors.accent;\n};\nvar getIOSRippleColor = function getIOSRippleColor(_ref7) {\n  var theme = _ref7.theme,\n    checkedColor = _ref7.checkedColor,\n    disabled = _ref7.disabled;\n  if (disabled) {\n    if (theme.isV3) {\n      return color(theme.colors.onSurface).alpha(0.16).rgb().string();\n    }\n    return color(theme.colors.text).alpha(0.16).rgb().string();\n  }\n  return color(checkedColor).fade(0.32).rgb().string();\n};\nexport var getSelectionControlIOSColor = function getSelectionControlIOSColor(_ref8) {\n  var theme = _ref8.theme,\n    disabled = _ref8.disabled,\n    customColor = _ref8.customColor;\n  var checkedColor = getIOSCheckedColor({\n    theme: theme,\n    disabled: disabled,\n    customColor: customColor\n  });\n  return {\n    checkedColor: checkedColor,\n    rippleColor: getIOSRippleColor({\n      theme: theme,\n      checkedColor: checkedColor,\n      disabled: disabled\n    })\n  };\n};", "map": {"version": 3, "names": ["color", "getAndroidCheckedColor", "_ref", "theme", "customColor", "isV3", "colors", "primary", "accent", "getAndroidUncheckedColor", "_ref2", "customUncheckedColor", "onSurfaceVariant", "dark", "text", "alpha", "rgb", "string", "getAndroidRippleColor", "_ref3", "checkedColor", "disabled", "onSurface", "fade", "getAndroidControlColor", "_ref4", "checked", "uncheckedColor", "onSurfaceDisabled", "getAndroidSelectionControlColor", "_ref5", "rippleColor", "selectionControlColor", "getIOSCheckedColor", "_ref6", "getIOSRippleColor", "_ref7", "getSelectionControlIOSColor", "_ref8"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\Checkbox\\utils.ts"], "sourcesContent": ["import color from 'color';\n\nimport type { InternalTheme } from '../../types';\n\nconst getAndroidCheckedColor = ({\n  theme,\n  customColor,\n}: {\n  theme: InternalTheme;\n  customColor?: string;\n}) => {\n  if (customColor) {\n    return customColor;\n  }\n\n  if (theme.isV3) {\n    return theme.colors.primary;\n  }\n\n  return theme.colors.accent;\n};\n\nconst getAndroidUncheckedColor = ({\n  theme,\n  customUncheckedColor,\n}: {\n  theme: InternalTheme;\n  customUncheckedColor?: string;\n}) => {\n  if (customUncheckedColor) {\n    return customUncheckedColor;\n  }\n\n  if (theme.isV3) {\n    return theme.colors.onSurfaceVariant;\n  }\n\n  if (theme.dark) {\n    return color(theme.colors.text).alpha(0.7).rgb().string();\n  }\n\n  return color(theme.colors.text).alpha(0.54).rgb().string();\n};\n\nconst getAndroidRippleColor = ({\n  theme,\n  checkedColor,\n  disabled,\n}: {\n  theme: InternalTheme;\n  checkedColor: string;\n  disabled?: boolean;\n}) => {\n  if (disabled) {\n    if (theme.isV3) {\n      return color(theme.colors.onSurface).alpha(0.16).rgb().string();\n    }\n    return color(theme.colors.text).alpha(0.16).rgb().string();\n  }\n\n  return color(checkedColor).fade(0.32).rgb().string();\n};\n\nconst getAndroidControlColor = ({\n  theme,\n  checked,\n  disabled,\n  checkedColor,\n  uncheckedColor,\n}: {\n  theme: InternalTheme;\n  checked: boolean;\n  checkedColor: string;\n  uncheckedColor: string;\n  disabled?: boolean;\n}) => {\n  if (disabled) {\n    if (theme.isV3) {\n      return theme.colors.onSurfaceDisabled;\n    }\n    return theme.colors.disabled;\n  }\n\n  if (checked) {\n    return checkedColor;\n  }\n  return uncheckedColor;\n};\n\nexport const getAndroidSelectionControlColor = ({\n  theme,\n  disabled,\n  checked,\n  customColor,\n  customUncheckedColor,\n}: {\n  theme: InternalTheme;\n  checked: boolean;\n  disabled?: boolean;\n  customColor?: string;\n  customUncheckedColor?: string;\n}) => {\n  const checkedColor = getAndroidCheckedColor({ theme, customColor });\n  const uncheckedColor = getAndroidUncheckedColor({\n    theme,\n    customUncheckedColor,\n  });\n  return {\n    rippleColor: getAndroidRippleColor({ theme, checkedColor, disabled }),\n    selectionControlColor: getAndroidControlColor({\n      theme,\n      disabled,\n      checked,\n      checkedColor,\n      uncheckedColor,\n    }),\n  };\n};\n\nconst getIOSCheckedColor = ({\n  theme,\n  disabled,\n  customColor,\n}: {\n  theme: InternalTheme;\n  customColor?: string;\n  disabled?: boolean;\n}) => {\n  if (disabled) {\n    if (theme.isV3) {\n      return theme.colors.onSurfaceDisabled;\n    }\n    return theme.colors.disabled;\n  }\n\n  if (customColor) {\n    return customColor;\n  }\n\n  if (theme.isV3) {\n    return theme.colors.primary;\n  }\n\n  return theme.colors.accent;\n};\n\nconst getIOSRippleColor = ({\n  theme,\n  checkedColor,\n  disabled,\n}: {\n  theme: InternalTheme;\n  checkedColor: string;\n  disabled?: boolean;\n}) => {\n  if (disabled) {\n    if (theme.isV3) {\n      return color(theme.colors.onSurface).alpha(0.16).rgb().string();\n    }\n    return color(theme.colors.text).alpha(0.16).rgb().string();\n  }\n  return color(checkedColor).fade(0.32).rgb().string();\n};\n\nexport const getSelectionControlIOSColor = ({\n  theme,\n  disabled,\n  customColor,\n}: {\n  theme: InternalTheme;\n  disabled?: boolean;\n  customColor?: string;\n}) => {\n  const checkedColor = getIOSCheckedColor({ theme, disabled, customColor });\n  return {\n    checkedColor,\n    rippleColor: getIOSRippleColor({\n      theme,\n      checkedColor,\n      disabled,\n    }),\n  };\n};\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAIzB,IAAMC,sBAAsB,GAAG,SAAzBA,sBAAsBA,CAAAC,IAAA,EAMtB;EAAA,IALJC,KAAK,GAAAD,IAAA,CAALC,KAAK;IACLC,WAAA,GAAAF,IAAA,CAAAE,WAAA;EAKA,IAAIA,WAAW,EAAE;IACf,OAAOA,WAAW;EACpB;EAEA,IAAID,KAAK,CAACE,IAAI,EAAE;IACd,OAAOF,KAAK,CAACG,MAAM,CAACC,OAAO;EAC7B;EAEA,OAAOJ,KAAK,CAACG,MAAM,CAACE,MAAM;AAC5B,CAAC;AAED,IAAMC,wBAAwB,GAAG,SAA3BA,wBAAwBA,CAAAC,KAAA,EAMxB;EAAA,IALJP,KAAK,GAAAO,KAAA,CAALP,KAAK;IACLQ,oBAAA,GAAAD,KAAA,CAAAC,oBAAA;EAKA,IAAIA,oBAAoB,EAAE;IACxB,OAAOA,oBAAoB;EAC7B;EAEA,IAAIR,KAAK,CAACE,IAAI,EAAE;IACd,OAAOF,KAAK,CAACG,MAAM,CAACM,gBAAgB;EACtC;EAEA,IAAIT,KAAK,CAACU,IAAI,EAAE;IACd,OAAOb,KAAK,CAACG,KAAK,CAACG,MAAM,CAACQ,IAAI,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EAC3D;EAEA,OAAOjB,KAAK,CAACG,KAAK,CAACG,MAAM,CAACQ,IAAI,CAAC,CAACC,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;AAC5D,CAAC;AAED,IAAMC,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAAC,KAAA,EAQrB;EAAA,IAPJhB,KAAK,GAAAgB,KAAA,CAALhB,KAAK;IACLiB,YAAY,GAAAD,KAAA,CAAZC,YAAY;IACZC,QAAA,GAAAF,KAAA,CAAAE,QAAA;EAMA,IAAIA,QAAQ,EAAE;IACZ,IAAIlB,KAAK,CAACE,IAAI,EAAE;MACd,OAAOL,KAAK,CAACG,KAAK,CAACG,MAAM,CAACgB,SAAS,CAAC,CAACP,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;IACjE;IACA,OAAOjB,KAAK,CAACG,KAAK,CAACG,MAAM,CAACQ,IAAI,CAAC,CAACC,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EAC5D;EAEA,OAAOjB,KAAK,CAACoB,YAAY,CAAC,CAACG,IAAI,CAAC,IAAI,CAAC,CAACP,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;AACtD,CAAC;AAED,IAAMO,sBAAsB,GAAG,SAAzBA,sBAAsBA,CAAAC,KAAA,EAYtB;EAAA,IAXJtB,KAAK,GAAAsB,KAAA,CAALtB,KAAK;IACLuB,OAAO,GAAAD,KAAA,CAAPC,OAAO;IACPL,QAAQ,GAAAI,KAAA,CAARJ,QAAQ;IACRD,YAAY,GAAAK,KAAA,CAAZL,YAAY;IACZO,cAAA,GAAAF,KAAA,CAAAE,cAAA;EAQA,IAAIN,QAAQ,EAAE;IACZ,IAAIlB,KAAK,CAACE,IAAI,EAAE;MACd,OAAOF,KAAK,CAACG,MAAM,CAACsB,iBAAiB;IACvC;IACA,OAAOzB,KAAK,CAACG,MAAM,CAACe,QAAQ;EAC9B;EAEA,IAAIK,OAAO,EAAE;IACX,OAAON,YAAY;EACrB;EACA,OAAOO,cAAc;AACvB,CAAC;AAED,OAAO,IAAME,+BAA+B,GAAG,SAAlCA,+BAA+BA,CAAAC,KAAA,EAYtC;EAAA,IAXJ3B,KAAK,GAAA2B,KAAA,CAAL3B,KAAK;IACLkB,QAAQ,GAAAS,KAAA,CAART,QAAQ;IACRK,OAAO,GAAAI,KAAA,CAAPJ,OAAO;IACPtB,WAAW,GAAA0B,KAAA,CAAX1B,WAAW;IACXO,oBAAA,GAAAmB,KAAA,CAAAnB,oBAAA;EAQA,IAAMS,YAAY,GAAGnB,sBAAsB,CAAC;IAAEE,KAAK,EAALA,KAAK;IAAEC,WAAA,EAAAA;EAAY,CAAC,CAAC;EACnE,IAAMuB,cAAc,GAAGlB,wBAAwB,CAAC;IAC9CN,KAAK,EAALA,KAAK;IACLQ,oBAAA,EAAAA;EACF,CAAC,CAAC;EACF,OAAO;IACLoB,WAAW,EAAEb,qBAAqB,CAAC;MAAEf,KAAK,EAALA,KAAK;MAAEiB,YAAY,EAAZA,YAAY;MAAEC,QAAA,EAAAA;IAAS,CAAC,CAAC;IACrEW,qBAAqB,EAAER,sBAAsB,CAAC;MAC5CrB,KAAK,EAALA,KAAK;MACLkB,QAAQ,EAARA,QAAQ;MACRK,OAAO,EAAPA,OAAO;MACPN,YAAY,EAAZA,YAAY;MACZO,cAAA,EAAAA;IACF,CAAC;EACH,CAAC;AACH,CAAC;AAED,IAAMM,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAAC,KAAA,EAQlB;EAAA,IAPJ/B,KAAK,GAAA+B,KAAA,CAAL/B,KAAK;IACLkB,QAAQ,GAAAa,KAAA,CAARb,QAAQ;IACRjB,WAAA,GAAA8B,KAAA,CAAA9B,WAAA;EAMA,IAAIiB,QAAQ,EAAE;IACZ,IAAIlB,KAAK,CAACE,IAAI,EAAE;MACd,OAAOF,KAAK,CAACG,MAAM,CAACsB,iBAAiB;IACvC;IACA,OAAOzB,KAAK,CAACG,MAAM,CAACe,QAAQ;EAC9B;EAEA,IAAIjB,WAAW,EAAE;IACf,OAAOA,WAAW;EACpB;EAEA,IAAID,KAAK,CAACE,IAAI,EAAE;IACd,OAAOF,KAAK,CAACG,MAAM,CAACC,OAAO;EAC7B;EAEA,OAAOJ,KAAK,CAACG,MAAM,CAACE,MAAM;AAC5B,CAAC;AAED,IAAM2B,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAAC,KAAA,EAQjB;EAAA,IAPJjC,KAAK,GAAAiC,KAAA,CAALjC,KAAK;IACLiB,YAAY,GAAAgB,KAAA,CAAZhB,YAAY;IACZC,QAAA,GAAAe,KAAA,CAAAf,QAAA;EAMA,IAAIA,QAAQ,EAAE;IACZ,IAAIlB,KAAK,CAACE,IAAI,EAAE;MACd,OAAOL,KAAK,CAACG,KAAK,CAACG,MAAM,CAACgB,SAAS,CAAC,CAACP,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;IACjE;IACA,OAAOjB,KAAK,CAACG,KAAK,CAACG,MAAM,CAACQ,IAAI,CAAC,CAACC,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EAC5D;EACA,OAAOjB,KAAK,CAACoB,YAAY,CAAC,CAACG,IAAI,CAAC,IAAI,CAAC,CAACP,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;AACtD,CAAC;AAED,OAAO,IAAMoB,2BAA2B,GAAG,SAA9BA,2BAA2BA,CAAAC,KAAA,EAQlC;EAAA,IAPJnC,KAAK,GAAAmC,KAAA,CAALnC,KAAK;IACLkB,QAAQ,GAAAiB,KAAA,CAARjB,QAAQ;IACRjB,WAAA,GAAAkC,KAAA,CAAAlC,WAAA;EAMA,IAAMgB,YAAY,GAAGa,kBAAkB,CAAC;IAAE9B,KAAK,EAALA,KAAK;IAAEkB,QAAQ,EAARA,QAAQ;IAAEjB,WAAA,EAAAA;EAAY,CAAC,CAAC;EACzE,OAAO;IACLgB,YAAY,EAAZA,YAAY;IACZW,WAAW,EAAEI,iBAAiB,CAAC;MAC7BhC,KAAK,EAALA,KAAK;MACLiB,YAAY,EAAZA,YAAY;MACZC,QAAA,EAAAA;IACF,CAAC;EACH,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}