import React from 'react';
import { View, TextInput, TouchableOpacity } from 'react-native';
import IconSearch from '../Icon/Search';
import styles from './styles';

const InputSearch = (props) => {
  return (
    <View style={styles.container}>
      <TextInput
        {...props}
        style={styles.search}
        placeholderTextColor="#7A7A7A"
      />

      <TouchableOpacity style={styles.searchButton}>
        <IconSearch />
      </TouchableOpacity>
    </View>
  )
}

export default InputSearch
