{"ast": null, "code": "import * as React from 'react';\nexport default function useLazyRef(callback) {\n  var lazyRef = React.useRef(undefined);\n  if (lazyRef.current === undefined) {\n    lazyRef.current = callback();\n  }\n  return lazyRef;\n}", "map": {"version": 3, "names": ["React", "useLazyRef", "callback", "lazyRef", "useRef", "undefined", "current"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\utils\\useLazyRef.tsx"], "sourcesContent": ["import * as React from 'react';\n\nexport default function useLazyRef<T>(callback: () => T) {\n  const lazyRef = React.useRef<T | undefined>(undefined);\n\n  if (lazyRef.current === undefined) {\n    lazyRef.current = callback();\n  }\n\n  return lazyRef as React.MutableRefObject<T>;\n}\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,eAAe,SAASC,UAAUA,CAAIC,QAAiB,EAAE;EACvD,IAAMC,OAAO,GAAGH,KAAK,CAACI,MAAM,CAAgBC,SAAS,CAAC;EAEtD,IAAIF,OAAO,CAACG,OAAO,KAAKD,SAAS,EAAE;IACjCF,OAAO,CAACG,OAAO,GAAGJ,QAAQ,CAAC,CAAC;EAC9B;EAEA,OAAOC,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}