{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport React, { useContext, useEffect, useState } from \"react\";\nimport View from \"react-native-web/dist/exports/View\";\nimport Alert from \"react-native-web/dist/exports/Alert\";\nimport ScrollView from \"react-native-web/dist/exports/ScrollView\";\nimport TouchableOpacity from \"react-native-web/dist/exports/TouchableOpacity\";\nimport { Button, Text, ProgressBar } from 'react-native-paper';\nimport Wrapper from \"../../components/Wrapper\";\nimport Card from \"../../components/Card\";\nimport StatCard from \"../../components/StatCard\";\nimport { StoreContext } from \"../../store\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport default function Premium(_ref) {\n  var _ref$route = _ref.route,\n    route = _ref$route === void 0 ? {} : _ref$route;\n  var _useContext = useContext(StoreContext),\n    _useContext2 = _slicedToArray(_useContext, 2),\n    state = _useContext2[0],\n    dispatch = _useContext2[1];\n  var _useState = useState('pro'),\n    _useState2 = _slicedToArray(_useState, 2),\n    selectedPlan = _useState2[0],\n    setSelectedPlan = _useState2[1];\n  var _ref2 = state || {\n      subscription: {\n        subscriptionStatus: false\n      }\n    },\n    subscriptionStatus = _ref2.subscription.subscriptionStatus;\n  var _ref3 = route.params || {\n      accountNotRecovered: false\n    },\n    accountNotRecovered = _ref3.accountNotRecovered;\n  var premiumFeatures = [{\n    icon: '🚀',\n    title: 'Priority Signals',\n    description: 'Get signals 30 seconds before free users',\n    free: false,\n    pro: true,\n    elite: true\n  }, {\n    icon: '📊',\n    title: 'Advanced Analytics',\n    description: 'Detailed performance metrics and insights',\n    free: false,\n    pro: true,\n    elite: true\n  }, {\n    icon: '🎯',\n    title: 'Custom Risk Management',\n    description: 'Personalized risk settings and alerts',\n    free: false,\n    pro: true,\n    elite: true\n  }, {\n    icon: '📱',\n    title: 'Mobile Notifications',\n    description: 'Real-time push notifications',\n    free: true,\n    pro: true,\n    elite: true\n  }, {\n    icon: '💬',\n    title: 'Premium Support',\n    description: '24/7 priority customer support',\n    free: false,\n    pro: false,\n    elite: true\n  }, {\n    icon: '🤖',\n    title: 'Auto Trading',\n    description: 'Automated signal execution',\n    free: false,\n    pro: false,\n    elite: true\n  }, {\n    icon: '📈',\n    title: 'Portfolio Tracking',\n    description: 'Advanced portfolio management tools',\n    free: false,\n    pro: true,\n    elite: true\n  }, {\n    icon: '🔔',\n    title: 'Signal Channels',\n    description: 'Access to premium signal channels',\n    free: '3 channels',\n    pro: '15 channels',\n    elite: 'Unlimited'\n  }];\n  var pricingPlans = [{\n    id: 'free',\n    name: 'Free',\n    price: '$0',\n    period: 'forever',\n    description: 'Perfect for beginners',\n    features: ['3 Signal Channels', 'Basic Notifications', 'Community Support'],\n    color: '#4CAF50',\n    popular: false\n  }, {\n    id: 'pro',\n    name: 'Pro',\n    price: '$29',\n    period: 'month',\n    description: 'Most popular choice',\n    features: ['15 Signal Channels', 'Priority Signals', 'Advanced Analytics', 'Custom Risk Management'],\n    color: '#FECB37',\n    popular: true,\n    discount: '50% OFF'\n  }, {\n    id: 'elite',\n    name: 'Elite',\n    price: '$79',\n    period: 'month',\n    description: 'For serious traders',\n    features: ['Unlimited Channels', 'Auto Trading', 'Premium Support', 'All Pro Features'],\n    color: '#9C27B0',\n    popular: false\n  }];\n  var testimonials = [{\n    name: 'Sarah Chen',\n    role: 'Day Trader',\n    avatar: '👩‍💼',\n    rating: 5,\n    text: 'Premium signals helped me increase my portfolio by 340% in 6 months!'\n  }, {\n    name: 'Mike Rodriguez',\n    role: 'Crypto Investor',\n    avatar: '👨‍💻',\n    rating: 5,\n    text: 'The auto-trading feature is a game changer. I make money while I sleep!'\n  }, {\n    name: 'Alex Thompson',\n    role: 'Professional Trader',\n    avatar: '👨‍💼',\n    rating: 5,\n    text: 'Best ROI I\\'ve ever seen from a trading service. Highly recommended!'\n  }];\n  useEffect(function () {\n    if (accountNotRecovered) {\n      Alert.alert(\"Info\", \"No active subscription for this account.\");\n    }\n  }, [accountNotRecovered]);\n  var handleSubscribe = function handleSubscribe(planId) {\n    var _pricingPlans$find;\n    Alert.alert(\"Subscribe to Premium\", `Subscribe to ${(_pricingPlans$find = pricingPlans.find(function (p) {\n      return p.id === planId;\n    })) == null ? void 0 : _pricingPlans$find.name} plan. This feature will be available soon!`);\n  };\n  var handleManageSubscription = function handleManageSubscription() {\n    Alert.alert(\"Manage Subscription\", \"Subscription management coming soon!\");\n  };\n  if (subscriptionStatus) {\n    return _jsx(Wrapper, {\n      children: _jsxs(ScrollView, {\n        style: {\n          flex: 1\n        },\n        children: [_jsxs(View, {\n          style: {\n            padding: 16,\n            alignItems: 'center'\n          },\n          children: [_jsx(Text, {\n            style: {\n              fontSize: 60,\n              marginBottom: 16\n            },\n            children: \"\\uD83D\\uDC8E\"\n          }), _jsx(Text, {\n            style: {\n              color: '#FECB37',\n              fontSize: 24,\n              fontFamily: 'Poppins_700Bold',\n              textAlign: 'center',\n              marginBottom: 8\n            },\n            children: \"Premium Active\"\n          }), _jsx(Text, {\n            style: {\n              color: '#8a8a8a',\n              fontSize: 14,\n              fontFamily: 'Poppins_400Regular',\n              textAlign: 'center'\n            },\n            children: \"You have access to all premium features\"\n          })]\n        }), _jsxs(View, {\n          style: {\n            paddingHorizontal: 8,\n            marginBottom: 16\n          },\n          children: [_jsx(Text, {\n            style: {\n              color: '#fff',\n              fontSize: 18,\n              fontFamily: 'Poppins_600SemiBold',\n              marginBottom: 12,\n              paddingHorizontal: 8\n            },\n            children: \"Your Premium Benefits\"\n          }), _jsxs(View, {\n            style: {\n              flexDirection: 'row',\n              flexWrap: 'wrap'\n            },\n            children: [_jsx(StatCard, {\n              title: \"Premium Channels\",\n              value: \"15\",\n              subtitle: \"Unlocked\",\n              icon: \"\\uD83D\\uDCFA\"\n            }), _jsx(StatCard, {\n              title: \"Priority Signals\",\n              value: \"847\",\n              subtitle: \"Received\",\n              icon: \"\\uD83D\\uDE80\"\n            }), _jsx(StatCard, {\n              title: \"Success Rate\",\n              value: \"84.2%\",\n              change: \"+5.1%\",\n              changeType: \"positive\",\n              icon: \"\\uD83C\\uDFAF\"\n            }), _jsx(StatCard, {\n              title: \"Savings\",\n              value: \"$2,340\",\n              subtitle: \"This month\",\n              icon: \"\\uD83D\\uDCB0\"\n            })]\n          })]\n        }), _jsxs(View, {\n          style: {\n            paddingHorizontal: 16,\n            marginBottom: 20\n          },\n          children: [_jsx(Text, {\n            style: {\n              color: '#fff',\n              fontSize: 18,\n              fontFamily: 'Poppins_600SemiBold',\n              marginBottom: 12\n            },\n            children: \"Subscription Management\"\n          }), _jsxs(Card, {\n            children: [_jsxs(View, {\n              style: {\n                flexDirection: 'row',\n                justifyContent: 'space-between',\n                alignItems: 'center',\n                marginBottom: 16\n              },\n              children: [_jsxs(View, {\n                children: [_jsx(Text, {\n                  style: {\n                    color: '#fff',\n                    fontSize: 16,\n                    fontFamily: 'Poppins_600SemiBold'\n                  },\n                  children: \"Pro Plan\"\n                }), _jsx(Text, {\n                  style: {\n                    color: '#8a8a8a',\n                    fontSize: 12,\n                    fontFamily: 'Poppins_400Regular'\n                  },\n                  children: \"Next billing: February 15, 2024\"\n                })]\n              }), _jsx(View, {\n                style: {\n                  backgroundColor: '#FECB37',\n                  paddingHorizontal: 8,\n                  paddingVertical: 4,\n                  borderRadius: 4\n                },\n                children: _jsx(Text, {\n                  style: {\n                    color: '#000',\n                    fontSize: 12,\n                    fontFamily: 'Poppins_600SemiBold'\n                  },\n                  children: \"ACTIVE\"\n                })\n              })]\n            }), _jsx(Button, {\n              mode: \"outlined\",\n              onPress: handleManageSubscription,\n              style: {\n                borderColor: '#FECB37'\n              },\n              labelStyle: {\n                color: '#FECB37',\n                fontFamily: 'Poppins_500Medium'\n              },\n              children: \"Manage Subscription\"\n            })]\n          })]\n        })]\n      })\n    });\n  }\n  return _jsx(Wrapper, {\n    children: _jsxs(ScrollView, {\n      style: {\n        flex: 1\n      },\n      children: [_jsxs(View, {\n        style: {\n          padding: 16,\n          alignItems: 'center',\n          backgroundColor: 'linear-gradient(135deg, #FECB37, #FF9800)'\n        },\n        children: [_jsx(Text, {\n          style: {\n            fontSize: 60,\n            marginBottom: 16\n          },\n          children: \"\\uD83D\\uDC8E\"\n        }), _jsx(Text, {\n          style: {\n            color: '#fff',\n            fontSize: 28,\n            fontFamily: 'Poppins_700Bold',\n            textAlign: 'center',\n            marginBottom: 8\n          },\n          children: \"Unlock Premium Trading\"\n        }), _jsx(Text, {\n          style: {\n            color: '#fff',\n            fontSize: 16,\n            fontFamily: 'Poppins_400Regular',\n            textAlign: 'center',\n            marginBottom: 16,\n            opacity: 0.9\n          },\n          children: \"Get priority signals and advanced features\"\n        }), _jsx(View, {\n          style: {\n            backgroundColor: '#F44336',\n            paddingHorizontal: 16,\n            paddingVertical: 8,\n            borderRadius: 20,\n            marginBottom: 16\n          },\n          children: _jsx(Text, {\n            style: {\n              color: '#fff',\n              fontSize: 12,\n              fontFamily: 'Poppins_600SemiBold',\n              textAlign: 'center'\n            },\n            children: \"\\uD83D\\uDD25 LIMITED TIME: 50% OFF FIRST MONTH\"\n          })\n        })]\n      }), _jsxs(View, {\n        style: {\n          paddingHorizontal: 8,\n          marginTop: 16,\n          marginBottom: 16\n        },\n        children: [_jsx(Text, {\n          style: {\n            color: '#fff',\n            fontSize: 18,\n            fontFamily: 'Poppins_600SemiBold',\n            marginBottom: 12,\n            paddingHorizontal: 8,\n            textAlign: 'center'\n          },\n          children: \"Join 50,000+ Successful Traders\"\n        }), _jsxs(View, {\n          style: {\n            flexDirection: 'row',\n            flexWrap: 'wrap'\n          },\n          children: [_jsx(StatCard, {\n            title: \"Average ROI\",\n            value: \"340%\",\n            subtitle: \"Per year\",\n            icon: \"\\uD83D\\uDCC8\"\n          }), _jsx(StatCard, {\n            title: \"Success Rate\",\n            value: \"87.3%\",\n            subtitle: \"Signal accuracy\",\n            icon: \"\\uD83C\\uDFAF\"\n          }), _jsx(StatCard, {\n            title: \"Active Users\",\n            value: \"50K+\",\n            subtitle: \"Worldwide\",\n            icon: \"\\uD83D\\uDC65\"\n          }), _jsx(StatCard, {\n            title: \"Signals Daily\",\n            value: \"150+\",\n            subtitle: \"Premium signals\",\n            icon: \"\\uD83D\\uDCE1\"\n          })]\n        })]\n      }), _jsxs(View, {\n        style: {\n          paddingHorizontal: 16,\n          marginBottom: 16\n        },\n        children: [_jsx(Text, {\n          style: {\n            color: '#fff',\n            fontSize: 20,\n            fontFamily: 'Poppins_700Bold',\n            marginBottom: 16,\n            textAlign: 'center'\n          },\n          children: \"Choose Your Plan\"\n        }), pricingPlans.map(function (plan) {\n          return _jsx(TouchableOpacity, {\n            onPress: function onPress() {\n              return setSelectedPlan(plan.id);\n            },\n            style: {\n              marginBottom: 12\n            },\n            children: _jsxs(Card, {\n              style: {\n                borderColor: selectedPlan === plan.id ? plan.color : 'transparent',\n                borderWidth: selectedPlan === plan.id ? 2 : 0,\n                position: 'relative'\n              },\n              children: [plan.popular && _jsx(View, {\n                style: {\n                  position: 'absolute',\n                  top: -8,\n                  right: 16,\n                  backgroundColor: '#F44336',\n                  paddingHorizontal: 12,\n                  paddingVertical: 4,\n                  borderRadius: 12,\n                  zIndex: 1\n                },\n                children: _jsx(Text, {\n                  style: {\n                    color: '#fff',\n                    fontSize: 10,\n                    fontFamily: 'Poppins_600SemiBold'\n                  },\n                  children: \"MOST POPULAR\"\n                })\n              }), plan.discount && _jsx(View, {\n                style: {\n                  position: 'absolute',\n                  top: -8,\n                  left: 16,\n                  backgroundColor: '#4CAF50',\n                  paddingHorizontal: 12,\n                  paddingVertical: 4,\n                  borderRadius: 12,\n                  zIndex: 1\n                },\n                children: _jsx(Text, {\n                  style: {\n                    color: '#fff',\n                    fontSize: 10,\n                    fontFamily: 'Poppins_600SemiBold'\n                  },\n                  children: plan.discount\n                })\n              }), _jsxs(View, {\n                style: {\n                  flexDirection: 'row',\n                  alignItems: 'center',\n                  marginBottom: 12\n                },\n                children: [_jsxs(View, {\n                  style: {\n                    flex: 1\n                  },\n                  children: [_jsx(Text, {\n                    style: {\n                      color: plan.color,\n                      fontSize: 20,\n                      fontFamily: 'Poppins_700Bold'\n                    },\n                    children: plan.name\n                  }), _jsx(Text, {\n                    style: {\n                      color: '#8a8a8a',\n                      fontSize: 12,\n                      fontFamily: 'Poppins_400Regular'\n                    },\n                    children: plan.description\n                  })]\n                }), _jsx(View, {\n                  style: {\n                    alignItems: 'flex-end'\n                  },\n                  children: _jsxs(View, {\n                    style: {\n                      flexDirection: 'row',\n                      alignItems: 'baseline'\n                    },\n                    children: [_jsx(Text, {\n                      style: {\n                        color: '#fff',\n                        fontSize: 24,\n                        fontFamily: 'Poppins_700Bold'\n                      },\n                      children: plan.price\n                    }), _jsxs(Text, {\n                      style: {\n                        color: '#8a8a8a',\n                        fontSize: 12,\n                        fontFamily: 'Poppins_400Regular',\n                        marginLeft: 4\n                      },\n                      children: [\"/\", plan.period]\n                    })]\n                  })\n                })]\n              }), _jsx(View, {\n                style: {\n                  marginBottom: 16\n                },\n                children: plan.features.map(function (feature, index) {\n                  return _jsxs(View, {\n                    style: {\n                      flexDirection: 'row',\n                      alignItems: 'center',\n                      marginBottom: 4\n                    },\n                    children: [_jsx(Text, {\n                      style: {\n                        color: '#4CAF50',\n                        marginRight: 8\n                      },\n                      children: \"\\u2713\"\n                    }), _jsx(Text, {\n                      style: {\n                        color: '#ccc',\n                        fontSize: 14,\n                        fontFamily: 'Poppins_400Regular'\n                      },\n                      children: feature\n                    })]\n                  }, index);\n                })\n              }), plan.id !== 'free' && _jsx(Button, {\n                mode: selectedPlan === plan.id ? \"contained\" : \"outlined\",\n                onPress: function onPress() {\n                  return handleSubscribe(plan.id);\n                },\n                style: {\n                  backgroundColor: selectedPlan === plan.id ? plan.color : 'transparent',\n                  borderColor: plan.color\n                },\n                labelStyle: {\n                  color: selectedPlan === plan.id ? '#000' : plan.color,\n                  fontFamily: 'Poppins_600SemiBold'\n                },\n                children: selectedPlan === plan.id ? 'Selected Plan' : `Choose ${plan.name}`\n              })]\n            })\n          }, plan.id);\n        })]\n      }), _jsxs(View, {\n        style: {\n          paddingHorizontal: 16,\n          marginBottom: 16\n        },\n        children: [_jsx(Text, {\n          style: {\n            color: '#fff',\n            fontSize: 20,\n            fontFamily: 'Poppins_700Bold',\n            marginBottom: 16,\n            textAlign: 'center'\n          },\n          children: \"Feature Comparison\"\n        }), _jsxs(Card, {\n          children: [_jsxs(View, {\n            style: {\n              flexDirection: 'row',\n              marginBottom: 12\n            },\n            children: [_jsx(View, {\n              style: {\n                flex: 2\n              },\n              children: _jsx(Text, {\n                style: {\n                  color: '#8a8a8a',\n                  fontSize: 12,\n                  fontFamily: 'Poppins_600SemiBold'\n                },\n                children: \"FEATURES\"\n              })\n            }), _jsx(View, {\n              style: {\n                flex: 1,\n                alignItems: 'center'\n              },\n              children: _jsx(Text, {\n                style: {\n                  color: '#4CAF50',\n                  fontSize: 12,\n                  fontFamily: 'Poppins_600SemiBold'\n                },\n                children: \"FREE\"\n              })\n            }), _jsx(View, {\n              style: {\n                flex: 1,\n                alignItems: 'center'\n              },\n              children: _jsx(Text, {\n                style: {\n                  color: '#FECB37',\n                  fontSize: 12,\n                  fontFamily: 'Poppins_600SemiBold'\n                },\n                children: \"PRO\"\n              })\n            }), _jsx(View, {\n              style: {\n                flex: 1,\n                alignItems: 'center'\n              },\n              children: _jsx(Text, {\n                style: {\n                  color: '#9C27B0',\n                  fontSize: 12,\n                  fontFamily: 'Poppins_600SemiBold'\n                },\n                children: \"ELITE\"\n              })\n            })]\n          }), premiumFeatures.map(function (feature, index) {\n            return _jsxs(View, {\n              style: {\n                flexDirection: 'row',\n                alignItems: 'center',\n                paddingVertical: 8,\n                borderTopWidth: index > 0 ? 1 : 0,\n                borderTopColor: '#333'\n              },\n              children: [_jsxs(View, {\n                style: {\n                  flex: 2,\n                  flexDirection: 'row',\n                  alignItems: 'center'\n                },\n                children: [_jsx(Text, {\n                  style: {\n                    fontSize: 16,\n                    marginRight: 8\n                  },\n                  children: feature.icon\n                }), _jsxs(View, {\n                  children: [_jsx(Text, {\n                    style: {\n                      color: '#fff',\n                      fontSize: 14,\n                      fontFamily: 'Poppins_500Medium'\n                    },\n                    children: feature.title\n                  }), _jsx(Text, {\n                    style: {\n                      color: '#8a8a8a',\n                      fontSize: 11,\n                      fontFamily: 'Poppins_400Regular'\n                    },\n                    children: feature.description\n                  })]\n                })]\n              }), _jsx(View, {\n                style: {\n                  flex: 1,\n                  alignItems: 'center'\n                },\n                children: _jsx(Text, {\n                  style: {\n                    color: typeof feature.free === 'boolean' ? feature.free ? '#4CAF50' : '#F44336' : '#8a8a8a',\n                    fontSize: 12,\n                    fontFamily: 'Poppins_500Medium'\n                  },\n                  children: typeof feature.free === 'boolean' ? feature.free ? '✓' : '✗' : feature.free\n                })\n              }), _jsx(View, {\n                style: {\n                  flex: 1,\n                  alignItems: 'center'\n                },\n                children: _jsx(Text, {\n                  style: {\n                    color: typeof feature.pro === 'boolean' ? feature.pro ? '#4CAF50' : '#F44336' : '#8a8a8a',\n                    fontSize: 12,\n                    fontFamily: 'Poppins_500Medium'\n                  },\n                  children: typeof feature.pro === 'boolean' ? feature.pro ? '✓' : '✗' : feature.pro\n                })\n              }), _jsx(View, {\n                style: {\n                  flex: 1,\n                  alignItems: 'center'\n                },\n                children: _jsx(Text, {\n                  style: {\n                    color: typeof feature.elite === 'boolean' ? feature.elite ? '#4CAF50' : '#F44336' : '#8a8a8a',\n                    fontSize: 12,\n                    fontFamily: 'Poppins_500Medium'\n                  },\n                  children: typeof feature.elite === 'boolean' ? feature.elite ? '✓' : '✗' : feature.elite\n                })\n              })]\n            }, index);\n          })]\n        })]\n      }), _jsxs(View, {\n        style: {\n          paddingHorizontal: 16,\n          marginBottom: 16\n        },\n        children: [_jsx(Text, {\n          style: {\n            color: '#fff',\n            fontSize: 20,\n            fontFamily: 'Poppins_700Bold',\n            marginBottom: 16,\n            textAlign: 'center'\n          },\n          children: \"What Our Users Say\"\n        }), _jsx(ScrollView, {\n          horizontal: true,\n          showsHorizontalScrollIndicator: false,\n          children: testimonials.map(function (testimonial, index) {\n            return _jsxs(Card, {\n              style: {\n                marginRight: 12,\n                width: 280\n              },\n              children: [_jsxs(View, {\n                style: {\n                  flexDirection: 'row',\n                  alignItems: 'center',\n                  marginBottom: 12\n                },\n                children: [_jsx(View, {\n                  style: {\n                    backgroundColor: '#333',\n                    borderRadius: 20,\n                    width: 40,\n                    height: 40,\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    marginRight: 12\n                  },\n                  children: _jsx(Text, {\n                    style: {\n                      fontSize: 20\n                    },\n                    children: testimonial.avatar\n                  })\n                }), _jsxs(View, {\n                  style: {\n                    flex: 1\n                  },\n                  children: [_jsx(Text, {\n                    style: {\n                      color: '#fff',\n                      fontSize: 14,\n                      fontFamily: 'Poppins_600SemiBold'\n                    },\n                    children: testimonial.name\n                  }), _jsx(Text, {\n                    style: {\n                      color: '#8a8a8a',\n                      fontSize: 12,\n                      fontFamily: 'Poppins_400Regular'\n                    },\n                    children: testimonial.role\n                  })]\n                }), _jsx(View, {\n                  style: {\n                    flexDirection: 'row'\n                  },\n                  children: _toConsumableArray(Array(testimonial.rating)).map(function (_, i) {\n                    return _jsx(Text, {\n                      style: {\n                        color: '#FECB37',\n                        fontSize: 12\n                      },\n                      children: \"\\u2B50\"\n                    }, i);\n                  })\n                })]\n              }), _jsxs(Text, {\n                style: {\n                  color: '#ccc',\n                  fontSize: 13,\n                  fontFamily: 'Poppins_400Regular',\n                  lineHeight: 18,\n                  fontStyle: 'italic'\n                },\n                children: [\"\\\"\", testimonial.text, \"\\\"\"]\n              })]\n            }, index);\n          })\n        })]\n      }), _jsx(View, {\n        style: {\n          paddingHorizontal: 16,\n          marginBottom: 20\n        },\n        children: _jsxs(Card, {\n          style: {\n            backgroundColor: '#FECB37',\n            padding: 20,\n            alignItems: 'center'\n          },\n          children: [_jsx(Text, {\n            style: {\n              color: '#000',\n              fontSize: 20,\n              fontFamily: 'Poppins_700Bold',\n              textAlign: 'center',\n              marginBottom: 8\n            },\n            children: \"Ready to Start Earning?\"\n          }), _jsx(Text, {\n            style: {\n              color: '#000',\n              fontSize: 14,\n              fontFamily: 'Poppins_400Regular',\n              textAlign: 'center',\n              marginBottom: 16,\n              opacity: 0.8\n            },\n            children: \"Join thousands of successful traders today\"\n          }), _jsx(Button, {\n            mode: \"contained\",\n            onPress: function onPress() {\n              return handleSubscribe(selectedPlan);\n            },\n            style: {\n              backgroundColor: '#000',\n              paddingHorizontal: 20\n            },\n            labelStyle: {\n              color: '#FECB37',\n              fontFamily: 'Poppins_600SemiBold',\n              fontSize: 16\n            },\n            children: \"Start Premium Trial\"\n          }), _jsx(Text, {\n            style: {\n              color: '#000',\n              fontSize: 11,\n              fontFamily: 'Poppins_400Regular',\n              textAlign: 'center',\n              marginTop: 8,\n              opacity: 0.7\n            },\n            children: \"Cancel anytime \\u2022 No hidden fees\"\n          })]\n        })\n      })]\n    })\n  });\n}", "map": {"version": 3, "names": ["React", "useContext", "useEffect", "useState", "View", "<PERSON><PERSON>", "ScrollView", "TouchableOpacity", "<PERSON><PERSON>", "Text", "ProgressBar", "Wrapper", "Card", "StatCard", "StoreContext", "jsx", "_jsx", "jsxs", "_jsxs", "Premium", "_ref", "_ref$route", "route", "_useContext", "_useContext2", "_slicedToArray", "state", "dispatch", "_useState", "_useState2", "<PERSON><PERSON><PERSON>", "setSelectedPlan", "_ref2", "subscription", "subscriptionStatus", "_ref3", "params", "accountNotRecovered", "premiumFeatures", "icon", "title", "description", "free", "pro", "elite", "pricingPlans", "id", "name", "price", "period", "features", "color", "popular", "discount", "testimonials", "role", "avatar", "rating", "text", "alert", "handleSubscribe", "planId", "_pricingPlans$find", "find", "p", "handleManageSubscription", "children", "style", "flex", "padding", "alignItems", "fontSize", "marginBottom", "fontFamily", "textAlign", "paddingHorizontal", "flexDirection", "flexWrap", "value", "subtitle", "change", "changeType", "justifyContent", "backgroundColor", "paddingVertical", "borderRadius", "mode", "onPress", "borderColor", "labelStyle", "opacity", "marginTop", "map", "plan", "borderWidth", "position", "top", "right", "zIndex", "left", "marginLeft", "feature", "index", "marginRight", "borderTopWidth", "borderTopColor", "horizontal", "showsHorizontalScrollIndicator", "testimonial", "width", "height", "_toConsumableArray", "Array", "_", "i", "lineHeight", "fontStyle"], "sources": ["E:/CryptoSignalsApp/src/pages/Premium/index.js"], "sourcesContent": ["import React, { useContext, useEffect, useState } from \"react\";\r\nimport { View, Alert, ScrollView, TouchableOpacity } from \"react-native\";\r\nimport { Button, Text, ProgressBar } from 'react-native-paper';\r\nimport Wrapper from \"../../components/Wrapper\";\r\nimport Card from \"../../components/Card\";\r\nimport StatCard from \"../../components/StatCard\";\r\nimport { StoreContext } from '../../store';\r\n\r\nexport default function Premium({ route = {} }) {\r\n  const [state, dispatch] = useContext(StoreContext);\r\n  const [selectedPlan, setSelectedPlan] = useState('pro');\r\n\r\n  const { subscription: { subscriptionStatus } } = state || { subscription: { subscriptionStatus: false } };\r\n  let { accountNotRecovered } = route.params || { accountNotRecovered: false };\r\n\r\n  // Premium features data\r\n  const premiumFeatures = [\r\n    {\r\n      icon: '🚀',\r\n      title: 'Priority Signals',\r\n      description: 'Get signals 30 seconds before free users',\r\n      free: false,\r\n      pro: true,\r\n      elite: true\r\n    },\r\n    {\r\n      icon: '📊',\r\n      title: 'Advanced Analytics',\r\n      description: 'Detailed performance metrics and insights',\r\n      free: false,\r\n      pro: true,\r\n      elite: true\r\n    },\r\n    {\r\n      icon: '🎯',\r\n      title: 'Custom Risk Management',\r\n      description: 'Personalized risk settings and alerts',\r\n      free: false,\r\n      pro: true,\r\n      elite: true\r\n    },\r\n    {\r\n      icon: '📱',\r\n      title: 'Mobile Notifications',\r\n      description: 'Real-time push notifications',\r\n      free: true,\r\n      pro: true,\r\n      elite: true\r\n    },\r\n    {\r\n      icon: '💬',\r\n      title: 'Premium Support',\r\n      description: '24/7 priority customer support',\r\n      free: false,\r\n      pro: false,\r\n      elite: true\r\n    },\r\n    {\r\n      icon: '🤖',\r\n      title: 'Auto Trading',\r\n      description: 'Automated signal execution',\r\n      free: false,\r\n      pro: false,\r\n      elite: true\r\n    },\r\n    {\r\n      icon: '📈',\r\n      title: 'Portfolio Tracking',\r\n      description: 'Advanced portfolio management tools',\r\n      free: false,\r\n      pro: true,\r\n      elite: true\r\n    },\r\n    {\r\n      icon: '🔔',\r\n      title: 'Signal Channels',\r\n      description: 'Access to premium signal channels',\r\n      free: '3 channels',\r\n      pro: '15 channels',\r\n      elite: 'Unlimited'\r\n    }\r\n  ];\r\n\r\n  // Pricing plans\r\n  const pricingPlans = [\r\n    {\r\n      id: 'free',\r\n      name: 'Free',\r\n      price: '$0',\r\n      period: 'forever',\r\n      description: 'Perfect for beginners',\r\n      features: ['3 Signal Channels', 'Basic Notifications', 'Community Support'],\r\n      color: '#4CAF50',\r\n      popular: false\r\n    },\r\n    {\r\n      id: 'pro',\r\n      name: 'Pro',\r\n      price: '$29',\r\n      period: 'month',\r\n      description: 'Most popular choice',\r\n      features: ['15 Signal Channels', 'Priority Signals', 'Advanced Analytics', 'Custom Risk Management'],\r\n      color: '#FECB37',\r\n      popular: true,\r\n      discount: '50% OFF'\r\n    },\r\n    {\r\n      id: 'elite',\r\n      name: 'Elite',\r\n      price: '$79',\r\n      period: 'month',\r\n      description: 'For serious traders',\r\n      features: ['Unlimited Channels', 'Auto Trading', 'Premium Support', 'All Pro Features'],\r\n      color: '#9C27B0',\r\n      popular: false\r\n    }\r\n  ];\r\n\r\n  // Testimonials\r\n  const testimonials = [\r\n    {\r\n      name: 'Sarah Chen',\r\n      role: 'Day Trader',\r\n      avatar: '👩‍💼',\r\n      rating: 5,\r\n      text: 'Premium signals helped me increase my portfolio by 340% in 6 months!'\r\n    },\r\n    {\r\n      name: 'Mike Rodriguez',\r\n      role: 'Crypto Investor',\r\n      avatar: '👨‍💻',\r\n      rating: 5,\r\n      text: 'The auto-trading feature is a game changer. I make money while I sleep!'\r\n    },\r\n    {\r\n      name: 'Alex Thompson',\r\n      role: 'Professional Trader',\r\n      avatar: '👨‍💼',\r\n      rating: 5,\r\n      text: 'Best ROI I\\'ve ever seen from a trading service. Highly recommended!'\r\n    }\r\n  ];\r\n\r\n  useEffect(() => {\r\n    if (accountNotRecovered) {\r\n      Alert.alert(\"Info\", \"No active subscription for this account.\");\r\n    }\r\n  }, [accountNotRecovered]);\r\n\r\n  const handleSubscribe = (planId) => {\r\n    Alert.alert(\r\n      \"Subscribe to Premium\",\r\n      `Subscribe to ${pricingPlans.find(p => p.id === planId)?.name} plan. This feature will be available soon!`\r\n    );\r\n  };\r\n\r\n  const handleManageSubscription = () => {\r\n    Alert.alert(\"Manage Subscription\", \"Subscription management coming soon!\");\r\n  };\r\n\r\n  if (subscriptionStatus) {\r\n    return (\r\n      <Wrapper>\r\n        <ScrollView style={{ flex: 1 }}>\r\n          {/* Premium Active Header */}\r\n          <View style={{ padding: 16, alignItems: 'center' }}>\r\n            <Text style={{ fontSize: 60, marginBottom: 16 }}>💎</Text>\r\n            <Text style={{\r\n              color: '#FECB37',\r\n              fontSize: 24,\r\n              fontFamily: 'Poppins_700Bold',\r\n              textAlign: 'center',\r\n              marginBottom: 8\r\n            }}>\r\n              Premium Active\r\n            </Text>\r\n            <Text style={{\r\n              color: '#8a8a8a',\r\n              fontSize: 14,\r\n              fontFamily: 'Poppins_400Regular',\r\n              textAlign: 'center'\r\n            }}>\r\n              You have access to all premium features\r\n            </Text>\r\n          </View>\r\n\r\n          {/* Premium Stats */}\r\n          <View style={{ paddingHorizontal: 8, marginBottom: 16 }}>\r\n            <Text style={{\r\n              color: '#fff',\r\n              fontSize: 18,\r\n              fontFamily: 'Poppins_600SemiBold',\r\n              marginBottom: 12,\r\n              paddingHorizontal: 8\r\n            }}>\r\n              Your Premium Benefits\r\n            </Text>\r\n            <View style={{ flexDirection: 'row', flexWrap: 'wrap' }}>\r\n              <StatCard\r\n                title=\"Premium Channels\"\r\n                value=\"15\"\r\n                subtitle=\"Unlocked\"\r\n                icon=\"📺\"\r\n              />\r\n              <StatCard\r\n                title=\"Priority Signals\"\r\n                value=\"847\"\r\n                subtitle=\"Received\"\r\n                icon=\"🚀\"\r\n              />\r\n              <StatCard\r\n                title=\"Success Rate\"\r\n                value=\"84.2%\"\r\n                change=\"+5.1%\"\r\n                changeType=\"positive\"\r\n                icon=\"🎯\"\r\n              />\r\n              <StatCard\r\n                title=\"Savings\"\r\n                value=\"$2,340\"\r\n                subtitle=\"This month\"\r\n                icon=\"💰\"\r\n              />\r\n            </View>\r\n          </View>\r\n\r\n          {/* Subscription Management */}\r\n          <View style={{ paddingHorizontal: 16, marginBottom: 20 }}>\r\n            <Text style={{\r\n              color: '#fff',\r\n              fontSize: 18,\r\n              fontFamily: 'Poppins_600SemiBold',\r\n              marginBottom: 12\r\n            }}>\r\n              Subscription Management\r\n            </Text>\r\n\r\n            <Card>\r\n              <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>\r\n                <View>\r\n                  <Text style={{ color: '#fff', fontSize: 16, fontFamily: 'Poppins_600SemiBold' }}>Pro Plan</Text>\r\n                  <Text style={{ color: '#8a8a8a', fontSize: 12, fontFamily: 'Poppins_400Regular' }}>Next billing: February 15, 2024</Text>\r\n                </View>\r\n                <View style={{\r\n                  backgroundColor: '#FECB37',\r\n                  paddingHorizontal: 8,\r\n                  paddingVertical: 4,\r\n                  borderRadius: 4\r\n                }}>\r\n                  <Text style={{ color: '#000', fontSize: 12, fontFamily: 'Poppins_600SemiBold' }}>ACTIVE</Text>\r\n                </View>\r\n              </View>\r\n\r\n              <Button\r\n                mode=\"outlined\"\r\n                onPress={handleManageSubscription}\r\n                style={{ borderColor: '#FECB37' }}\r\n                labelStyle={{ color: '#FECB37', fontFamily: 'Poppins_500Medium' }}\r\n              >\r\n                Manage Subscription\r\n              </Button>\r\n            </Card>\r\n          </View>\r\n        </ScrollView>\r\n      </Wrapper>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Wrapper>\r\n      <ScrollView style={{ flex: 1 }}>\r\n        {/* Hero Section */}\r\n        <View style={{ padding: 16, alignItems: 'center', backgroundColor: 'linear-gradient(135deg, #FECB37, #FF9800)' }}>\r\n          <Text style={{ fontSize: 60, marginBottom: 16 }}>💎</Text>\r\n          <Text style={{\r\n            color: '#fff',\r\n            fontSize: 28,\r\n            fontFamily: 'Poppins_700Bold',\r\n            textAlign: 'center',\r\n            marginBottom: 8\r\n          }}>\r\n            Unlock Premium Trading\r\n          </Text>\r\n          <Text style={{\r\n            color: '#fff',\r\n            fontSize: 16,\r\n            fontFamily: 'Poppins_400Regular',\r\n            textAlign: 'center',\r\n            marginBottom: 16,\r\n            opacity: 0.9\r\n          }}>\r\n            Get priority signals and advanced features\r\n          </Text>\r\n\r\n          {/* Limited Time Offer */}\r\n          <View style={{\r\n            backgroundColor: '#F44336',\r\n            paddingHorizontal: 16,\r\n            paddingVertical: 8,\r\n            borderRadius: 20,\r\n            marginBottom: 16\r\n          }}>\r\n            <Text style={{\r\n              color: '#fff',\r\n              fontSize: 12,\r\n              fontFamily: 'Poppins_600SemiBold',\r\n              textAlign: 'center'\r\n            }}>\r\n              🔥 LIMITED TIME: 50% OFF FIRST MONTH\r\n            </Text>\r\n          </View>\r\n        </View>\r\n\r\n        {/* Success Stats */}\r\n        <View style={{ paddingHorizontal: 8, marginTop: 16, marginBottom: 16 }}>\r\n          <Text style={{\r\n            color: '#fff',\r\n            fontSize: 18,\r\n            fontFamily: 'Poppins_600SemiBold',\r\n            marginBottom: 12,\r\n            paddingHorizontal: 8,\r\n            textAlign: 'center'\r\n          }}>\r\n            Join 50,000+ Successful Traders\r\n          </Text>\r\n          <View style={{ flexDirection: 'row', flexWrap: 'wrap' }}>\r\n            <StatCard\r\n              title=\"Average ROI\"\r\n              value=\"340%\"\r\n              subtitle=\"Per year\"\r\n              icon=\"📈\"\r\n            />\r\n            <StatCard\r\n              title=\"Success Rate\"\r\n              value=\"87.3%\"\r\n              subtitle=\"Signal accuracy\"\r\n              icon=\"🎯\"\r\n            />\r\n            <StatCard\r\n              title=\"Active Users\"\r\n              value=\"50K+\"\r\n              subtitle=\"Worldwide\"\r\n              icon=\"👥\"\r\n            />\r\n            <StatCard\r\n              title=\"Signals Daily\"\r\n              value=\"150+\"\r\n              subtitle=\"Premium signals\"\r\n              icon=\"📡\"\r\n            />\r\n          </View>\r\n        </View>\r\n\r\n        {/* Pricing Plans */}\r\n        <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>\r\n          <Text style={{\r\n            color: '#fff',\r\n            fontSize: 20,\r\n            fontFamily: 'Poppins_700Bold',\r\n            marginBottom: 16,\r\n            textAlign: 'center'\r\n          }}>\r\n            Choose Your Plan\r\n          </Text>\r\n\r\n          {pricingPlans.map((plan) => (\r\n            <TouchableOpacity\r\n              key={plan.id}\r\n              onPress={() => setSelectedPlan(plan.id)}\r\n              style={{ marginBottom: 12 }}\r\n            >\r\n              <Card style={{\r\n                borderColor: selectedPlan === plan.id ? plan.color : 'transparent',\r\n                borderWidth: selectedPlan === plan.id ? 2 : 0,\r\n                position: 'relative'\r\n              }}>\r\n                {plan.popular && (\r\n                  <View style={{\r\n                    position: 'absolute',\r\n                    top: -8,\r\n                    right: 16,\r\n                    backgroundColor: '#F44336',\r\n                    paddingHorizontal: 12,\r\n                    paddingVertical: 4,\r\n                    borderRadius: 12,\r\n                    zIndex: 1\r\n                  }}>\r\n                    <Text style={{\r\n                      color: '#fff',\r\n                      fontSize: 10,\r\n                      fontFamily: 'Poppins_600SemiBold'\r\n                    }}>\r\n                      MOST POPULAR\r\n                    </Text>\r\n                  </View>\r\n                )}\r\n\r\n                {plan.discount && (\r\n                  <View style={{\r\n                    position: 'absolute',\r\n                    top: -8,\r\n                    left: 16,\r\n                    backgroundColor: '#4CAF50',\r\n                    paddingHorizontal: 12,\r\n                    paddingVertical: 4,\r\n                    borderRadius: 12,\r\n                    zIndex: 1\r\n                  }}>\r\n                    <Text style={{\r\n                      color: '#fff',\r\n                      fontSize: 10,\r\n                      fontFamily: 'Poppins_600SemiBold'\r\n                    }}>\r\n                      {plan.discount}\r\n                    </Text>\r\n                  </View>\r\n                )}\r\n\r\n                <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>\r\n                  <View style={{ flex: 1 }}>\r\n                    <Text style={{\r\n                      color: plan.color,\r\n                      fontSize: 20,\r\n                      fontFamily: 'Poppins_700Bold'\r\n                    }}>\r\n                      {plan.name}\r\n                    </Text>\r\n                    <Text style={{\r\n                      color: '#8a8a8a',\r\n                      fontSize: 12,\r\n                      fontFamily: 'Poppins_400Regular'\r\n                    }}>\r\n                      {plan.description}\r\n                    </Text>\r\n                  </View>\r\n                  <View style={{ alignItems: 'flex-end' }}>\r\n                    <View style={{ flexDirection: 'row', alignItems: 'baseline' }}>\r\n                      <Text style={{\r\n                        color: '#fff',\r\n                        fontSize: 24,\r\n                        fontFamily: 'Poppins_700Bold'\r\n                      }}>\r\n                        {plan.price}\r\n                      </Text>\r\n                      <Text style={{\r\n                        color: '#8a8a8a',\r\n                        fontSize: 12,\r\n                        fontFamily: 'Poppins_400Regular',\r\n                        marginLeft: 4\r\n                      }}>\r\n                        /{plan.period}\r\n                      </Text>\r\n                    </View>\r\n                  </View>\r\n                </View>\r\n\r\n                <View style={{ marginBottom: 16 }}>\r\n                  {plan.features.map((feature, index) => (\r\n                    <View key={index} style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 4 }}>\r\n                      <Text style={{ color: '#4CAF50', marginRight: 8 }}>✓</Text>\r\n                      <Text style={{\r\n                        color: '#ccc',\r\n                        fontSize: 14,\r\n                        fontFamily: 'Poppins_400Regular'\r\n                      }}>\r\n                        {feature}\r\n                      </Text>\r\n                    </View>\r\n                  ))}\r\n                </View>\r\n\r\n                {plan.id !== 'free' && (\r\n                  <Button\r\n                    mode={selectedPlan === plan.id ? \"contained\" : \"outlined\"}\r\n                    onPress={() => handleSubscribe(plan.id)}\r\n                    style={{\r\n                      backgroundColor: selectedPlan === plan.id ? plan.color : 'transparent',\r\n                      borderColor: plan.color\r\n                    }}\r\n                    labelStyle={{\r\n                      color: selectedPlan === plan.id ? '#000' : plan.color,\r\n                      fontFamily: 'Poppins_600SemiBold'\r\n                    }}\r\n                  >\r\n                    {selectedPlan === plan.id ? 'Selected Plan' : `Choose ${plan.name}`}\r\n                  </Button>\r\n                )}\r\n              </Card>\r\n            </TouchableOpacity>\r\n          ))}\r\n        </View>\r\n\r\n        {/* Feature Comparison */}\r\n        <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>\r\n          <Text style={{\r\n            color: '#fff',\r\n            fontSize: 20,\r\n            fontFamily: 'Poppins_700Bold',\r\n            marginBottom: 16,\r\n            textAlign: 'center'\r\n          }}>\r\n            Feature Comparison\r\n          </Text>\r\n\r\n          <Card>\r\n            <View style={{ flexDirection: 'row', marginBottom: 12 }}>\r\n              <View style={{ flex: 2 }}>\r\n                <Text style={{ color: '#8a8a8a', fontSize: 12, fontFamily: 'Poppins_600SemiBold' }}>FEATURES</Text>\r\n              </View>\r\n              <View style={{ flex: 1, alignItems: 'center' }}>\r\n                <Text style={{ color: '#4CAF50', fontSize: 12, fontFamily: 'Poppins_600SemiBold' }}>FREE</Text>\r\n              </View>\r\n              <View style={{ flex: 1, alignItems: 'center' }}>\r\n                <Text style={{ color: '#FECB37', fontSize: 12, fontFamily: 'Poppins_600SemiBold' }}>PRO</Text>\r\n              </View>\r\n              <View style={{ flex: 1, alignItems: 'center' }}>\r\n                <Text style={{ color: '#9C27B0', fontSize: 12, fontFamily: 'Poppins_600SemiBold' }}>ELITE</Text>\r\n              </View>\r\n            </View>\r\n\r\n            {premiumFeatures.map((feature, index) => (\r\n              <View key={index} style={{\r\n                flexDirection: 'row',\r\n                alignItems: 'center',\r\n                paddingVertical: 8,\r\n                borderTopWidth: index > 0 ? 1 : 0,\r\n                borderTopColor: '#333'\r\n              }}>\r\n                <View style={{ flex: 2, flexDirection: 'row', alignItems: 'center' }}>\r\n                  <Text style={{ fontSize: 16, marginRight: 8 }}>{feature.icon}</Text>\r\n                  <View>\r\n                    <Text style={{ color: '#fff', fontSize: 14, fontFamily: 'Poppins_500Medium' }}>\r\n                      {feature.title}\r\n                    </Text>\r\n                    <Text style={{ color: '#8a8a8a', fontSize: 11, fontFamily: 'Poppins_400Regular' }}>\r\n                      {feature.description}\r\n                    </Text>\r\n                  </View>\r\n                </View>\r\n                <View style={{ flex: 1, alignItems: 'center' }}>\r\n                  <Text style={{\r\n                    color: typeof feature.free === 'boolean' ? (feature.free ? '#4CAF50' : '#F44336') : '#8a8a8a',\r\n                    fontSize: 12,\r\n                    fontFamily: 'Poppins_500Medium'\r\n                  }}>\r\n                    {typeof feature.free === 'boolean' ? (feature.free ? '✓' : '✗') : feature.free}\r\n                  </Text>\r\n                </View>\r\n                <View style={{ flex: 1, alignItems: 'center' }}>\r\n                  <Text style={{\r\n                    color: typeof feature.pro === 'boolean' ? (feature.pro ? '#4CAF50' : '#F44336') : '#8a8a8a',\r\n                    fontSize: 12,\r\n                    fontFamily: 'Poppins_500Medium'\r\n                  }}>\r\n                    {typeof feature.pro === 'boolean' ? (feature.pro ? '✓' : '✗') : feature.pro}\r\n                  </Text>\r\n                </View>\r\n                <View style={{ flex: 1, alignItems: 'center' }}>\r\n                  <Text style={{\r\n                    color: typeof feature.elite === 'boolean' ? (feature.elite ? '#4CAF50' : '#F44336') : '#8a8a8a',\r\n                    fontSize: 12,\r\n                    fontFamily: 'Poppins_500Medium'\r\n                  }}>\r\n                    {typeof feature.elite === 'boolean' ? (feature.elite ? '✓' : '✗') : feature.elite}\r\n                  </Text>\r\n                </View>\r\n              </View>\r\n            ))}\r\n          </Card>\r\n        </View>\r\n\r\n        {/* Testimonials */}\r\n        <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>\r\n          <Text style={{\r\n            color: '#fff',\r\n            fontSize: 20,\r\n            fontFamily: 'Poppins_700Bold',\r\n            marginBottom: 16,\r\n            textAlign: 'center'\r\n          }}>\r\n            What Our Users Say\r\n          </Text>\r\n\r\n          <ScrollView horizontal showsHorizontalScrollIndicator={false}>\r\n            {testimonials.map((testimonial, index) => (\r\n              <Card key={index} style={{ marginRight: 12, width: 280 }}>\r\n                <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>\r\n                  <View style={{\r\n                    backgroundColor: '#333',\r\n                    borderRadius: 20,\r\n                    width: 40,\r\n                    height: 40,\r\n                    alignItems: 'center',\r\n                    justifyContent: 'center',\r\n                    marginRight: 12\r\n                  }}>\r\n                    <Text style={{ fontSize: 20 }}>{testimonial.avatar}</Text>\r\n                  </View>\r\n                  <View style={{ flex: 1 }}>\r\n                    <Text style={{\r\n                      color: '#fff',\r\n                      fontSize: 14,\r\n                      fontFamily: 'Poppins_600SemiBold'\r\n                    }}>\r\n                      {testimonial.name}\r\n                    </Text>\r\n                    <Text style={{\r\n                      color: '#8a8a8a',\r\n                      fontSize: 12,\r\n                      fontFamily: 'Poppins_400Regular'\r\n                    }}>\r\n                      {testimonial.role}\r\n                    </Text>\r\n                  </View>\r\n                  <View style={{ flexDirection: 'row' }}>\r\n                    {[...Array(testimonial.rating)].map((_, i) => (\r\n                      <Text key={i} style={{ color: '#FECB37', fontSize: 12 }}>⭐</Text>\r\n                    ))}\r\n                  </View>\r\n                </View>\r\n                <Text style={{\r\n                  color: '#ccc',\r\n                  fontSize: 13,\r\n                  fontFamily: 'Poppins_400Regular',\r\n                  lineHeight: 18,\r\n                  fontStyle: 'italic'\r\n                }}>\r\n                  \"{testimonial.text}\"\r\n                </Text>\r\n              </Card>\r\n            ))}\r\n          </ScrollView>\r\n        </View>\r\n\r\n        {/* Call to Action */}\r\n        <View style={{ paddingHorizontal: 16, marginBottom: 20 }}>\r\n          <Card style={{ backgroundColor: '#FECB37', padding: 20, alignItems: 'center' }}>\r\n            <Text style={{\r\n              color: '#000',\r\n              fontSize: 20,\r\n              fontFamily: 'Poppins_700Bold',\r\n              textAlign: 'center',\r\n              marginBottom: 8\r\n            }}>\r\n              Ready to Start Earning?\r\n            </Text>\r\n            <Text style={{\r\n              color: '#000',\r\n              fontSize: 14,\r\n              fontFamily: 'Poppins_400Regular',\r\n              textAlign: 'center',\r\n              marginBottom: 16,\r\n              opacity: 0.8\r\n            }}>\r\n              Join thousands of successful traders today\r\n            </Text>\r\n            <Button\r\n              mode=\"contained\"\r\n              onPress={() => handleSubscribe(selectedPlan)}\r\n              style={{\r\n                backgroundColor: '#000',\r\n                paddingHorizontal: 20\r\n              }}\r\n              labelStyle={{\r\n                color: '#FECB37',\r\n                fontFamily: 'Poppins_600SemiBold',\r\n                fontSize: 16\r\n              }}\r\n            >\r\n              Start Premium Trial\r\n            </Button>\r\n            <Text style={{\r\n              color: '#000',\r\n              fontSize: 11,\r\n              fontFamily: 'Poppins_400Regular',\r\n              textAlign: 'center',\r\n              marginTop: 8,\r\n              opacity: 0.7\r\n            }}>\r\n              Cancel anytime • No hidden fees\r\n            </Text>\r\n          </Card>\r\n        </View>\r\n      </ScrollView>\r\n    </Wrapper>\r\n  );\r\n}"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,KAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,gBAAA;AAE/D,SAASC,MAAM,EAAEC,IAAI,EAAEC,WAAW,QAAQ,oBAAoB;AAC9D,OAAOC,OAAO;AACd,OAAOC,IAAI;AACX,OAAOC,QAAQ;AACf,SAASC,YAAY;AAAsB,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAE3C,eAAe,SAASC,OAAOA,CAAAC,IAAA,EAAiB;EAAA,IAAAC,UAAA,GAAAD,IAAA,CAAdE,KAAK;IAALA,KAAK,GAAAD,UAAA,cAAG,CAAC,CAAC,GAAAA,UAAA;EAC1C,IAAAE,WAAA,GAA0BtB,UAAU,CAACa,YAAY,CAAC;IAAAU,YAAA,GAAAC,cAAA,CAAAF,WAAA;IAA3CG,KAAK,GAAAF,YAAA;IAAEG,QAAQ,GAAAH,YAAA;EACtB,IAAAI,SAAA,GAAwCzB,QAAQ,CAAC,KAAK,CAAC;IAAA0B,UAAA,GAAAJ,cAAA,CAAAG,SAAA;IAAhDE,YAAY,GAAAD,UAAA;IAAEE,eAAe,GAAAF,UAAA;EAEpC,IAAAG,KAAA,GAAiDN,KAAK,IAAI;MAAEO,YAAY,EAAE;QAAEC,kBAAkB,EAAE;MAAM;IAAE,CAAC;IAAjFA,kBAAkB,GAAAF,KAAA,CAAlCC,YAAY,CAAIC,kBAAkB;EAC1C,IAAAC,KAAA,GAA8Bb,KAAK,CAACc,MAAM,IAAI;MAAEC,mBAAmB,EAAE;IAAM,CAAC;IAAtEA,mBAAmB,GAAAF,KAAA,CAAnBE,mBAAmB;EAGzB,IAAMC,eAAe,GAAG,CACtB;IACEC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,kBAAkB;IACzBC,WAAW,EAAE,0CAA0C;IACvDC,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE;EACT,CAAC,EACD;IACEL,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE,2CAA2C;IACxDC,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE;EACT,CAAC,EACD;IACEL,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,wBAAwB;IAC/BC,WAAW,EAAE,uCAAuC;IACpDC,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE;EACT,CAAC,EACD;IACEL,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,sBAAsB;IAC7BC,WAAW,EAAE,8BAA8B;IAC3CC,IAAI,EAAE,IAAI;IACVC,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE;EACT,CAAC,EACD;IACEL,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,gCAAgC;IAC7CC,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE;EACT,CAAC,EACD;IACEL,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE,4BAA4B;IACzCC,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE;EACT,CAAC,EACD;IACEL,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE,qCAAqC;IAClDC,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE;EACT,CAAC,EACD;IACEL,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,mCAAmC;IAChDC,IAAI,EAAE,YAAY;IAClBC,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC,CACF;EAGD,IAAMC,YAAY,GAAG,CACnB;IACEC,EAAE,EAAE,MAAM;IACVC,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE,IAAI;IACXC,MAAM,EAAE,SAAS;IACjBR,WAAW,EAAE,uBAAuB;IACpCS,QAAQ,EAAE,CAAC,mBAAmB,EAAE,qBAAqB,EAAE,mBAAmB,CAAC;IAC3EC,KAAK,EAAE,SAAS;IAChBC,OAAO,EAAE;EACX,CAAC,EACD;IACEN,EAAE,EAAE,KAAK;IACTC,IAAI,EAAE,KAAK;IACXC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,OAAO;IACfR,WAAW,EAAE,qBAAqB;IAClCS,QAAQ,EAAE,CAAC,oBAAoB,EAAE,kBAAkB,EAAE,oBAAoB,EAAE,wBAAwB,CAAC;IACpGC,KAAK,EAAE,SAAS;IAChBC,OAAO,EAAE,IAAI;IACbC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEP,EAAE,EAAE,OAAO;IACXC,IAAI,EAAE,OAAO;IACbC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,OAAO;IACfR,WAAW,EAAE,qBAAqB;IAClCS,QAAQ,EAAE,CAAC,oBAAoB,EAAE,cAAc,EAAE,iBAAiB,EAAE,kBAAkB,CAAC;IACvFC,KAAK,EAAE,SAAS;IAChBC,OAAO,EAAE;EACX,CAAC,CACF;EAGD,IAAME,YAAY,GAAG,CACnB;IACEP,IAAI,EAAE,YAAY;IAClBQ,IAAI,EAAE,YAAY;IAClBC,MAAM,EAAE,OAAO;IACfC,MAAM,EAAE,CAAC;IACTC,IAAI,EAAE;EACR,CAAC,EACD;IACEX,IAAI,EAAE,gBAAgB;IACtBQ,IAAI,EAAE,iBAAiB;IACvBC,MAAM,EAAE,OAAO;IACfC,MAAM,EAAE,CAAC;IACTC,IAAI,EAAE;EACR,CAAC,EACD;IACEX,IAAI,EAAE,eAAe;IACrBQ,IAAI,EAAE,qBAAqB;IAC3BC,MAAM,EAAE,OAAO;IACfC,MAAM,EAAE,CAAC;IACTC,IAAI,EAAE;EACR,CAAC,CACF;EAEDxD,SAAS,CAAC,YAAM;IACd,IAAImC,mBAAmB,EAAE;MACvBhC,KAAK,CAACsD,KAAK,CAAC,MAAM,EAAE,0CAA0C,CAAC;IACjE;EACF,CAAC,EAAE,CAACtB,mBAAmB,CAAC,CAAC;EAEzB,IAAMuB,eAAe,GAAG,SAAlBA,eAAeA,CAAIC,MAAM,EAAK;IAAA,IAAAC,kBAAA;IAClCzD,KAAK,CAACsD,KAAK,CACT,sBAAsB,EACtB,iBAAAG,kBAAA,GAAgBjB,YAAY,CAACkB,IAAI,CAAC,UAAAC,CAAC;MAAA,OAAIA,CAAC,CAAClB,EAAE,KAAKe,MAAM;IAAA,EAAC,qBAAvCC,kBAAA,CAAyCf,IAAI,6CAC/D,CAAC;EACH,CAAC;EAED,IAAMkB,wBAAwB,GAAG,SAA3BA,wBAAwBA,CAAA,EAAS;IACrC5D,KAAK,CAACsD,KAAK,CAAC,qBAAqB,EAAE,sCAAsC,CAAC;EAC5E,CAAC;EAED,IAAIzB,kBAAkB,EAAE;IACtB,OACElB,IAAA,CAACL,OAAO;MAAAuD,QAAA,EACNhD,KAAA,CAACZ,UAAU;QAAC6D,KAAK,EAAE;UAAEC,IAAI,EAAE;QAAE,CAAE;QAAAF,QAAA,GAE7BhD,KAAA,CAACd,IAAI;UAAC+D,KAAK,EAAE;YAAEE,OAAO,EAAE,EAAE;YAAEC,UAAU,EAAE;UAAS,CAAE;UAAAJ,QAAA,GACjDlD,IAAA,CAACP,IAAI;YAAC0D,KAAK,EAAE;cAAEI,QAAQ,EAAE,EAAE;cAAEC,YAAY,EAAE;YAAG,CAAE;YAAAN,QAAA,EAAC;UAAE,CAAM,CAAC,EAC1DlD,IAAA,CAACP,IAAI;YAAC0D,KAAK,EAAE;cACXhB,KAAK,EAAE,SAAS;cAChBoB,QAAQ,EAAE,EAAE;cACZE,UAAU,EAAE,iBAAiB;cAC7BC,SAAS,EAAE,QAAQ;cACnBF,YAAY,EAAE;YAChB,CAAE;YAAAN,QAAA,EAAC;UAEH,CAAM,CAAC,EACPlD,IAAA,CAACP,IAAI;YAAC0D,KAAK,EAAE;cACXhB,KAAK,EAAE,SAAS;cAChBoB,QAAQ,EAAE,EAAE;cACZE,UAAU,EAAE,oBAAoB;cAChCC,SAAS,EAAE;YACb,CAAE;YAAAR,QAAA,EAAC;UAEH,CAAM,CAAC;QAAA,CACH,CAAC,EAGPhD,KAAA,CAACd,IAAI;UAAC+D,KAAK,EAAE;YAAEQ,iBAAiB,EAAE,CAAC;YAAEH,YAAY,EAAE;UAAG,CAAE;UAAAN,QAAA,GACtDlD,IAAA,CAACP,IAAI;YAAC0D,KAAK,EAAE;cACXhB,KAAK,EAAE,MAAM;cACboB,QAAQ,EAAE,EAAE;cACZE,UAAU,EAAE,qBAAqB;cACjCD,YAAY,EAAE,EAAE;cAChBG,iBAAiB,EAAE;YACrB,CAAE;YAAAT,QAAA,EAAC;UAEH,CAAM,CAAC,EACPhD,KAAA,CAACd,IAAI;YAAC+D,KAAK,EAAE;cAAES,aAAa,EAAE,KAAK;cAAEC,QAAQ,EAAE;YAAO,CAAE;YAAAX,QAAA,GACtDlD,IAAA,CAACH,QAAQ;cACP2B,KAAK,EAAC,kBAAkB;cACxBsC,KAAK,EAAC,IAAI;cACVC,QAAQ,EAAC,UAAU;cACnBxC,IAAI,EAAC;YAAI,CACV,CAAC,EACFvB,IAAA,CAACH,QAAQ;cACP2B,KAAK,EAAC,kBAAkB;cACxBsC,KAAK,EAAC,KAAK;cACXC,QAAQ,EAAC,UAAU;cACnBxC,IAAI,EAAC;YAAI,CACV,CAAC,EACFvB,IAAA,CAACH,QAAQ;cACP2B,KAAK,EAAC,cAAc;cACpBsC,KAAK,EAAC,OAAO;cACbE,MAAM,EAAC,OAAO;cACdC,UAAU,EAAC,UAAU;cACrB1C,IAAI,EAAC;YAAI,CACV,CAAC,EACFvB,IAAA,CAACH,QAAQ;cACP2B,KAAK,EAAC,SAAS;cACfsC,KAAK,EAAC,QAAQ;cACdC,QAAQ,EAAC,YAAY;cACrBxC,IAAI,EAAC;YAAI,CACV,CAAC;UAAA,CACE,CAAC;QAAA,CACH,CAAC,EAGPrB,KAAA,CAACd,IAAI;UAAC+D,KAAK,EAAE;YAAEQ,iBAAiB,EAAE,EAAE;YAAEH,YAAY,EAAE;UAAG,CAAE;UAAAN,QAAA,GACvDlD,IAAA,CAACP,IAAI;YAAC0D,KAAK,EAAE;cACXhB,KAAK,EAAE,MAAM;cACboB,QAAQ,EAAE,EAAE;cACZE,UAAU,EAAE,qBAAqB;cACjCD,YAAY,EAAE;YAChB,CAAE;YAAAN,QAAA,EAAC;UAEH,CAAM,CAAC,EAEPhD,KAAA,CAACN,IAAI;YAAAsD,QAAA,GACHhD,KAAA,CAACd,IAAI;cAAC+D,KAAK,EAAE;gBAAES,aAAa,EAAE,KAAK;gBAAEM,cAAc,EAAE,eAAe;gBAAEZ,UAAU,EAAE,QAAQ;gBAAEE,YAAY,EAAE;cAAG,CAAE;cAAAN,QAAA,GAC7GhD,KAAA,CAACd,IAAI;gBAAA8D,QAAA,GACHlD,IAAA,CAACP,IAAI;kBAAC0D,KAAK,EAAE;oBAAEhB,KAAK,EAAE,MAAM;oBAAEoB,QAAQ,EAAE,EAAE;oBAAEE,UAAU,EAAE;kBAAsB,CAAE;kBAAAP,QAAA,EAAC;gBAAQ,CAAM,CAAC,EAChGlD,IAAA,CAACP,IAAI;kBAAC0D,KAAK,EAAE;oBAAEhB,KAAK,EAAE,SAAS;oBAAEoB,QAAQ,EAAE,EAAE;oBAAEE,UAAU,EAAE;kBAAqB,CAAE;kBAAAP,QAAA,EAAC;gBAA+B,CAAM,CAAC;cAAA,CACrH,CAAC,EACPlD,IAAA,CAACZ,IAAI;gBAAC+D,KAAK,EAAE;kBACXgB,eAAe,EAAE,SAAS;kBAC1BR,iBAAiB,EAAE,CAAC;kBACpBS,eAAe,EAAE,CAAC;kBAClBC,YAAY,EAAE;gBAChB,CAAE;gBAAAnB,QAAA,EACAlD,IAAA,CAACP,IAAI;kBAAC0D,KAAK,EAAE;oBAAEhB,KAAK,EAAE,MAAM;oBAAEoB,QAAQ,EAAE,EAAE;oBAAEE,UAAU,EAAE;kBAAsB,CAAE;kBAAAP,QAAA,EAAC;gBAAM,CAAM;cAAC,CAC1F,CAAC;YAAA,CACH,CAAC,EAEPlD,IAAA,CAACR,MAAM;cACL8E,IAAI,EAAC,UAAU;cACfC,OAAO,EAAEtB,wBAAyB;cAClCE,KAAK,EAAE;gBAAEqB,WAAW,EAAE;cAAU,CAAE;cAClCC,UAAU,EAAE;gBAAEtC,KAAK,EAAE,SAAS;gBAAEsB,UAAU,EAAE;cAAoB,CAAE;cAAAP,QAAA,EACnE;YAED,CAAQ,CAAC;UAAA,CACL,CAAC;QAAA,CACH,CAAC;MAAA,CACG;IAAC,CACN,CAAC;EAEd;EAEA,OACElD,IAAA,CAACL,OAAO;IAAAuD,QAAA,EACNhD,KAAA,CAACZ,UAAU;MAAC6D,KAAK,EAAE;QAAEC,IAAI,EAAE;MAAE,CAAE;MAAAF,QAAA,GAE7BhD,KAAA,CAACd,IAAI;QAAC+D,KAAK,EAAE;UAAEE,OAAO,EAAE,EAAE;UAAEC,UAAU,EAAE,QAAQ;UAAEa,eAAe,EAAE;QAA4C,CAAE;QAAAjB,QAAA,GAC/GlD,IAAA,CAACP,IAAI;UAAC0D,KAAK,EAAE;YAAEI,QAAQ,EAAE,EAAE;YAAEC,YAAY,EAAE;UAAG,CAAE;UAAAN,QAAA,EAAC;QAAE,CAAM,CAAC,EAC1DlD,IAAA,CAACP,IAAI;UAAC0D,KAAK,EAAE;YACXhB,KAAK,EAAE,MAAM;YACboB,QAAQ,EAAE,EAAE;YACZE,UAAU,EAAE,iBAAiB;YAC7BC,SAAS,EAAE,QAAQ;YACnBF,YAAY,EAAE;UAChB,CAAE;UAAAN,QAAA,EAAC;QAEH,CAAM,CAAC,EACPlD,IAAA,CAACP,IAAI;UAAC0D,KAAK,EAAE;YACXhB,KAAK,EAAE,MAAM;YACboB,QAAQ,EAAE,EAAE;YACZE,UAAU,EAAE,oBAAoB;YAChCC,SAAS,EAAE,QAAQ;YACnBF,YAAY,EAAE,EAAE;YAChBkB,OAAO,EAAE;UACX,CAAE;UAAAxB,QAAA,EAAC;QAEH,CAAM,CAAC,EAGPlD,IAAA,CAACZ,IAAI;UAAC+D,KAAK,EAAE;YACXgB,eAAe,EAAE,SAAS;YAC1BR,iBAAiB,EAAE,EAAE;YACrBS,eAAe,EAAE,CAAC;YAClBC,YAAY,EAAE,EAAE;YAChBb,YAAY,EAAE;UAChB,CAAE;UAAAN,QAAA,EACAlD,IAAA,CAACP,IAAI;YAAC0D,KAAK,EAAE;cACXhB,KAAK,EAAE,MAAM;cACboB,QAAQ,EAAE,EAAE;cACZE,UAAU,EAAE,qBAAqB;cACjCC,SAAS,EAAE;YACb,CAAE;YAAAR,QAAA,EAAC;UAEH,CAAM;QAAC,CACH,CAAC;MAAA,CACH,CAAC,EAGPhD,KAAA,CAACd,IAAI;QAAC+D,KAAK,EAAE;UAAEQ,iBAAiB,EAAE,CAAC;UAAEgB,SAAS,EAAE,EAAE;UAAEnB,YAAY,EAAE;QAAG,CAAE;QAAAN,QAAA,GACrElD,IAAA,CAACP,IAAI;UAAC0D,KAAK,EAAE;YACXhB,KAAK,EAAE,MAAM;YACboB,QAAQ,EAAE,EAAE;YACZE,UAAU,EAAE,qBAAqB;YACjCD,YAAY,EAAE,EAAE;YAChBG,iBAAiB,EAAE,CAAC;YACpBD,SAAS,EAAE;UACb,CAAE;UAAAR,QAAA,EAAC;QAEH,CAAM,CAAC,EACPhD,KAAA,CAACd,IAAI;UAAC+D,KAAK,EAAE;YAAES,aAAa,EAAE,KAAK;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAAX,QAAA,GACtDlD,IAAA,CAACH,QAAQ;YACP2B,KAAK,EAAC,aAAa;YACnBsC,KAAK,EAAC,MAAM;YACZC,QAAQ,EAAC,UAAU;YACnBxC,IAAI,EAAC;UAAI,CACV,CAAC,EACFvB,IAAA,CAACH,QAAQ;YACP2B,KAAK,EAAC,cAAc;YACpBsC,KAAK,EAAC,OAAO;YACbC,QAAQ,EAAC,iBAAiB;YAC1BxC,IAAI,EAAC;UAAI,CACV,CAAC,EACFvB,IAAA,CAACH,QAAQ;YACP2B,KAAK,EAAC,cAAc;YACpBsC,KAAK,EAAC,MAAM;YACZC,QAAQ,EAAC,WAAW;YACpBxC,IAAI,EAAC;UAAI,CACV,CAAC,EACFvB,IAAA,CAACH,QAAQ;YACP2B,KAAK,EAAC,eAAe;YACrBsC,KAAK,EAAC,MAAM;YACZC,QAAQ,EAAC,iBAAiB;YAC1BxC,IAAI,EAAC;UAAI,CACV,CAAC;QAAA,CACE,CAAC;MAAA,CACH,CAAC,EAGPrB,KAAA,CAACd,IAAI;QAAC+D,KAAK,EAAE;UAAEQ,iBAAiB,EAAE,EAAE;UAAEH,YAAY,EAAE;QAAG,CAAE;QAAAN,QAAA,GACvDlD,IAAA,CAACP,IAAI;UAAC0D,KAAK,EAAE;YACXhB,KAAK,EAAE,MAAM;YACboB,QAAQ,EAAE,EAAE;YACZE,UAAU,EAAE,iBAAiB;YAC7BD,YAAY,EAAE,EAAE;YAChBE,SAAS,EAAE;UACb,CAAE;UAAAR,QAAA,EAAC;QAEH,CAAM,CAAC,EAENrB,YAAY,CAAC+C,GAAG,CAAC,UAACC,IAAI;UAAA,OACrB7E,IAAA,CAACT,gBAAgB;YAEfgF,OAAO,EAAE,SAATA,OAAOA,CAAA;cAAA,OAAQxD,eAAe,CAAC8D,IAAI,CAAC/C,EAAE,CAAC;YAAA,CAAC;YACxCqB,KAAK,EAAE;cAAEK,YAAY,EAAE;YAAG,CAAE;YAAAN,QAAA,EAE5BhD,KAAA,CAACN,IAAI;cAACuD,KAAK,EAAE;gBACXqB,WAAW,EAAE1D,YAAY,KAAK+D,IAAI,CAAC/C,EAAE,GAAG+C,IAAI,CAAC1C,KAAK,GAAG,aAAa;gBAClE2C,WAAW,EAAEhE,YAAY,KAAK+D,IAAI,CAAC/C,EAAE,GAAG,CAAC,GAAG,CAAC;gBAC7CiD,QAAQ,EAAE;cACZ,CAAE;cAAA7B,QAAA,GACC2B,IAAI,CAACzC,OAAO,IACXpC,IAAA,CAACZ,IAAI;gBAAC+D,KAAK,EAAE;kBACX4B,QAAQ,EAAE,UAAU;kBACpBC,GAAG,EAAE,CAAC,CAAC;kBACPC,KAAK,EAAE,EAAE;kBACTd,eAAe,EAAE,SAAS;kBAC1BR,iBAAiB,EAAE,EAAE;kBACrBS,eAAe,EAAE,CAAC;kBAClBC,YAAY,EAAE,EAAE;kBAChBa,MAAM,EAAE;gBACV,CAAE;gBAAAhC,QAAA,EACAlD,IAAA,CAACP,IAAI;kBAAC0D,KAAK,EAAE;oBACXhB,KAAK,EAAE,MAAM;oBACboB,QAAQ,EAAE,EAAE;oBACZE,UAAU,EAAE;kBACd,CAAE;kBAAAP,QAAA,EAAC;gBAEH,CAAM;cAAC,CACH,CACP,EAEA2B,IAAI,CAACxC,QAAQ,IACZrC,IAAA,CAACZ,IAAI;gBAAC+D,KAAK,EAAE;kBACX4B,QAAQ,EAAE,UAAU;kBACpBC,GAAG,EAAE,CAAC,CAAC;kBACPG,IAAI,EAAE,EAAE;kBACRhB,eAAe,EAAE,SAAS;kBAC1BR,iBAAiB,EAAE,EAAE;kBACrBS,eAAe,EAAE,CAAC;kBAClBC,YAAY,EAAE,EAAE;kBAChBa,MAAM,EAAE;gBACV,CAAE;gBAAAhC,QAAA,EACAlD,IAAA,CAACP,IAAI;kBAAC0D,KAAK,EAAE;oBACXhB,KAAK,EAAE,MAAM;oBACboB,QAAQ,EAAE,EAAE;oBACZE,UAAU,EAAE;kBACd,CAAE;kBAAAP,QAAA,EACC2B,IAAI,CAACxC;gBAAQ,CACV;cAAC,CACH,CACP,EAEDnC,KAAA,CAACd,IAAI;gBAAC+D,KAAK,EAAE;kBAAES,aAAa,EAAE,KAAK;kBAAEN,UAAU,EAAE,QAAQ;kBAAEE,YAAY,EAAE;gBAAG,CAAE;gBAAAN,QAAA,GAC5EhD,KAAA,CAACd,IAAI;kBAAC+D,KAAK,EAAE;oBAAEC,IAAI,EAAE;kBAAE,CAAE;kBAAAF,QAAA,GACvBlD,IAAA,CAACP,IAAI;oBAAC0D,KAAK,EAAE;sBACXhB,KAAK,EAAE0C,IAAI,CAAC1C,KAAK;sBACjBoB,QAAQ,EAAE,EAAE;sBACZE,UAAU,EAAE;oBACd,CAAE;oBAAAP,QAAA,EACC2B,IAAI,CAAC9C;kBAAI,CACN,CAAC,EACP/B,IAAA,CAACP,IAAI;oBAAC0D,KAAK,EAAE;sBACXhB,KAAK,EAAE,SAAS;sBAChBoB,QAAQ,EAAE,EAAE;sBACZE,UAAU,EAAE;oBACd,CAAE;oBAAAP,QAAA,EACC2B,IAAI,CAACpD;kBAAW,CACb,CAAC;gBAAA,CACH,CAAC,EACPzB,IAAA,CAACZ,IAAI;kBAAC+D,KAAK,EAAE;oBAAEG,UAAU,EAAE;kBAAW,CAAE;kBAAAJ,QAAA,EACtChD,KAAA,CAACd,IAAI;oBAAC+D,KAAK,EAAE;sBAAES,aAAa,EAAE,KAAK;sBAAEN,UAAU,EAAE;oBAAW,CAAE;oBAAAJ,QAAA,GAC5DlD,IAAA,CAACP,IAAI;sBAAC0D,KAAK,EAAE;wBACXhB,KAAK,EAAE,MAAM;wBACboB,QAAQ,EAAE,EAAE;wBACZE,UAAU,EAAE;sBACd,CAAE;sBAAAP,QAAA,EACC2B,IAAI,CAAC7C;oBAAK,CACP,CAAC,EACP9B,KAAA,CAACT,IAAI;sBAAC0D,KAAK,EAAE;wBACXhB,KAAK,EAAE,SAAS;wBAChBoB,QAAQ,EAAE,EAAE;wBACZE,UAAU,EAAE,oBAAoB;wBAChC2B,UAAU,EAAE;sBACd,CAAE;sBAAAlC,QAAA,GAAC,GACA,EAAC2B,IAAI,CAAC5C,MAAM;oBAAA,CACT,CAAC;kBAAA,CACH;gBAAC,CACH,CAAC;cAAA,CACH,CAAC,EAEPjC,IAAA,CAACZ,IAAI;gBAAC+D,KAAK,EAAE;kBAAEK,YAAY,EAAE;gBAAG,CAAE;gBAAAN,QAAA,EAC/B2B,IAAI,CAAC3C,QAAQ,CAAC0C,GAAG,CAAC,UAACS,OAAO,EAAEC,KAAK;kBAAA,OAChCpF,KAAA,CAACd,IAAI;oBAAa+D,KAAK,EAAE;sBAAES,aAAa,EAAE,KAAK;sBAAEN,UAAU,EAAE,QAAQ;sBAAEE,YAAY,EAAE;oBAAE,CAAE;oBAAAN,QAAA,GACvFlD,IAAA,CAACP,IAAI;sBAAC0D,KAAK,EAAE;wBAAEhB,KAAK,EAAE,SAAS;wBAAEoD,WAAW,EAAE;sBAAE,CAAE;sBAAArC,QAAA,EAAC;oBAAC,CAAM,CAAC,EAC3DlD,IAAA,CAACP,IAAI;sBAAC0D,KAAK,EAAE;wBACXhB,KAAK,EAAE,MAAM;wBACboB,QAAQ,EAAE,EAAE;wBACZE,UAAU,EAAE;sBACd,CAAE;sBAAAP,QAAA,EACCmC;oBAAO,CACJ,CAAC;kBAAA,GAREC,KASL,CAAC;gBAAA,CACR;cAAC,CACE,CAAC,EAENT,IAAI,CAAC/C,EAAE,KAAK,MAAM,IACjB9B,IAAA,CAACR,MAAM;gBACL8E,IAAI,EAAExD,YAAY,KAAK+D,IAAI,CAAC/C,EAAE,GAAG,WAAW,GAAG,UAAW;gBAC1DyC,OAAO,EAAE,SAATA,OAAOA,CAAA;kBAAA,OAAQ3B,eAAe,CAACiC,IAAI,CAAC/C,EAAE,CAAC;gBAAA,CAAC;gBACxCqB,KAAK,EAAE;kBACLgB,eAAe,EAAErD,YAAY,KAAK+D,IAAI,CAAC/C,EAAE,GAAG+C,IAAI,CAAC1C,KAAK,GAAG,aAAa;kBACtEqC,WAAW,EAAEK,IAAI,CAAC1C;gBACpB,CAAE;gBACFsC,UAAU,EAAE;kBACVtC,KAAK,EAAErB,YAAY,KAAK+D,IAAI,CAAC/C,EAAE,GAAG,MAAM,GAAG+C,IAAI,CAAC1C,KAAK;kBACrDsB,UAAU,EAAE;gBACd,CAAE;gBAAAP,QAAA,EAEDpC,YAAY,KAAK+D,IAAI,CAAC/C,EAAE,GAAG,eAAe,GAAG,UAAU+C,IAAI,CAAC9C,IAAI;cAAE,CAC7D,CACT;YAAA,CACG;UAAC,GAxHF8C,IAAI,CAAC/C,EAyHM,CAAC;QAAA,CACpB,CAAC;MAAA,CACE,CAAC,EAGP5B,KAAA,CAACd,IAAI;QAAC+D,KAAK,EAAE;UAAEQ,iBAAiB,EAAE,EAAE;UAAEH,YAAY,EAAE;QAAG,CAAE;QAAAN,QAAA,GACvDlD,IAAA,CAACP,IAAI;UAAC0D,KAAK,EAAE;YACXhB,KAAK,EAAE,MAAM;YACboB,QAAQ,EAAE,EAAE;YACZE,UAAU,EAAE,iBAAiB;YAC7BD,YAAY,EAAE,EAAE;YAChBE,SAAS,EAAE;UACb,CAAE;UAAAR,QAAA,EAAC;QAEH,CAAM,CAAC,EAEPhD,KAAA,CAACN,IAAI;UAAAsD,QAAA,GACHhD,KAAA,CAACd,IAAI;YAAC+D,KAAK,EAAE;cAAES,aAAa,EAAE,KAAK;cAAEJ,YAAY,EAAE;YAAG,CAAE;YAAAN,QAAA,GACtDlD,IAAA,CAACZ,IAAI;cAAC+D,KAAK,EAAE;gBAAEC,IAAI,EAAE;cAAE,CAAE;cAAAF,QAAA,EACvBlD,IAAA,CAACP,IAAI;gBAAC0D,KAAK,EAAE;kBAAEhB,KAAK,EAAE,SAAS;kBAAEoB,QAAQ,EAAE,EAAE;kBAAEE,UAAU,EAAE;gBAAsB,CAAE;gBAAAP,QAAA,EAAC;cAAQ,CAAM;YAAC,CAC/F,CAAC,EACPlD,IAAA,CAACZ,IAAI;cAAC+D,KAAK,EAAE;gBAAEC,IAAI,EAAE,CAAC;gBAAEE,UAAU,EAAE;cAAS,CAAE;cAAAJ,QAAA,EAC7ClD,IAAA,CAACP,IAAI;gBAAC0D,KAAK,EAAE;kBAAEhB,KAAK,EAAE,SAAS;kBAAEoB,QAAQ,EAAE,EAAE;kBAAEE,UAAU,EAAE;gBAAsB,CAAE;gBAAAP,QAAA,EAAC;cAAI,CAAM;YAAC,CAC3F,CAAC,EACPlD,IAAA,CAACZ,IAAI;cAAC+D,KAAK,EAAE;gBAAEC,IAAI,EAAE,CAAC;gBAAEE,UAAU,EAAE;cAAS,CAAE;cAAAJ,QAAA,EAC7ClD,IAAA,CAACP,IAAI;gBAAC0D,KAAK,EAAE;kBAAEhB,KAAK,EAAE,SAAS;kBAAEoB,QAAQ,EAAE,EAAE;kBAAEE,UAAU,EAAE;gBAAsB,CAAE;gBAAAP,QAAA,EAAC;cAAG,CAAM;YAAC,CAC1F,CAAC,EACPlD,IAAA,CAACZ,IAAI;cAAC+D,KAAK,EAAE;gBAAEC,IAAI,EAAE,CAAC;gBAAEE,UAAU,EAAE;cAAS,CAAE;cAAAJ,QAAA,EAC7ClD,IAAA,CAACP,IAAI;gBAAC0D,KAAK,EAAE;kBAAEhB,KAAK,EAAE,SAAS;kBAAEoB,QAAQ,EAAE,EAAE;kBAAEE,UAAU,EAAE;gBAAsB,CAAE;gBAAAP,QAAA,EAAC;cAAK,CAAM;YAAC,CAC5F,CAAC;UAAA,CACH,CAAC,EAEN5B,eAAe,CAACsD,GAAG,CAAC,UAACS,OAAO,EAAEC,KAAK;YAAA,OAClCpF,KAAA,CAACd,IAAI;cAAa+D,KAAK,EAAE;gBACvBS,aAAa,EAAE,KAAK;gBACpBN,UAAU,EAAE,QAAQ;gBACpBc,eAAe,EAAE,CAAC;gBAClBoB,cAAc,EAAEF,KAAK,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;gBACjCG,cAAc,EAAE;cAClB,CAAE;cAAAvC,QAAA,GACAhD,KAAA,CAACd,IAAI;gBAAC+D,KAAK,EAAE;kBAAEC,IAAI,EAAE,CAAC;kBAAEQ,aAAa,EAAE,KAAK;kBAAEN,UAAU,EAAE;gBAAS,CAAE;gBAAAJ,QAAA,GACnElD,IAAA,CAACP,IAAI;kBAAC0D,KAAK,EAAE;oBAAEI,QAAQ,EAAE,EAAE;oBAAEgC,WAAW,EAAE;kBAAE,CAAE;kBAAArC,QAAA,EAAEmC,OAAO,CAAC9D;gBAAI,CAAO,CAAC,EACpErB,KAAA,CAACd,IAAI;kBAAA8D,QAAA,GACHlD,IAAA,CAACP,IAAI;oBAAC0D,KAAK,EAAE;sBAAEhB,KAAK,EAAE,MAAM;sBAAEoB,QAAQ,EAAE,EAAE;sBAAEE,UAAU,EAAE;oBAAoB,CAAE;oBAAAP,QAAA,EAC3EmC,OAAO,CAAC7D;kBAAK,CACV,CAAC,EACPxB,IAAA,CAACP,IAAI;oBAAC0D,KAAK,EAAE;sBAAEhB,KAAK,EAAE,SAAS;sBAAEoB,QAAQ,EAAE,EAAE;sBAAEE,UAAU,EAAE;oBAAqB,CAAE;oBAAAP,QAAA,EAC/EmC,OAAO,CAAC5D;kBAAW,CAChB,CAAC;gBAAA,CACH,CAAC;cAAA,CACH,CAAC,EACPzB,IAAA,CAACZ,IAAI;gBAAC+D,KAAK,EAAE;kBAAEC,IAAI,EAAE,CAAC;kBAAEE,UAAU,EAAE;gBAAS,CAAE;gBAAAJ,QAAA,EAC7ClD,IAAA,CAACP,IAAI;kBAAC0D,KAAK,EAAE;oBACXhB,KAAK,EAAE,OAAOkD,OAAO,CAAC3D,IAAI,KAAK,SAAS,GAAI2D,OAAO,CAAC3D,IAAI,GAAG,SAAS,GAAG,SAAS,GAAI,SAAS;oBAC7F6B,QAAQ,EAAE,EAAE;oBACZE,UAAU,EAAE;kBACd,CAAE;kBAAAP,QAAA,EACC,OAAOmC,OAAO,CAAC3D,IAAI,KAAK,SAAS,GAAI2D,OAAO,CAAC3D,IAAI,GAAG,GAAG,GAAG,GAAG,GAAI2D,OAAO,CAAC3D;gBAAI,CAC1E;cAAC,CACH,CAAC,EACP1B,IAAA,CAACZ,IAAI;gBAAC+D,KAAK,EAAE;kBAAEC,IAAI,EAAE,CAAC;kBAAEE,UAAU,EAAE;gBAAS,CAAE;gBAAAJ,QAAA,EAC7ClD,IAAA,CAACP,IAAI;kBAAC0D,KAAK,EAAE;oBACXhB,KAAK,EAAE,OAAOkD,OAAO,CAAC1D,GAAG,KAAK,SAAS,GAAI0D,OAAO,CAAC1D,GAAG,GAAG,SAAS,GAAG,SAAS,GAAI,SAAS;oBAC3F4B,QAAQ,EAAE,EAAE;oBACZE,UAAU,EAAE;kBACd,CAAE;kBAAAP,QAAA,EACC,OAAOmC,OAAO,CAAC1D,GAAG,KAAK,SAAS,GAAI0D,OAAO,CAAC1D,GAAG,GAAG,GAAG,GAAG,GAAG,GAAI0D,OAAO,CAAC1D;gBAAG,CACvE;cAAC,CACH,CAAC,EACP3B,IAAA,CAACZ,IAAI;gBAAC+D,KAAK,EAAE;kBAAEC,IAAI,EAAE,CAAC;kBAAEE,UAAU,EAAE;gBAAS,CAAE;gBAAAJ,QAAA,EAC7ClD,IAAA,CAACP,IAAI;kBAAC0D,KAAK,EAAE;oBACXhB,KAAK,EAAE,OAAOkD,OAAO,CAACzD,KAAK,KAAK,SAAS,GAAIyD,OAAO,CAACzD,KAAK,GAAG,SAAS,GAAG,SAAS,GAAI,SAAS;oBAC/F2B,QAAQ,EAAE,EAAE;oBACZE,UAAU,EAAE;kBACd,CAAE;kBAAAP,QAAA,EACC,OAAOmC,OAAO,CAACzD,KAAK,KAAK,SAAS,GAAIyD,OAAO,CAACzD,KAAK,GAAG,GAAG,GAAG,GAAG,GAAIyD,OAAO,CAACzD;gBAAK,CAC7E;cAAC,CACH,CAAC;YAAA,GA5CE0D,KA6CL,CAAC;UAAA,CACR,CAAC;QAAA,CACE,CAAC;MAAA,CACH,CAAC,EAGPpF,KAAA,CAACd,IAAI;QAAC+D,KAAK,EAAE;UAAEQ,iBAAiB,EAAE,EAAE;UAAEH,YAAY,EAAE;QAAG,CAAE;QAAAN,QAAA,GACvDlD,IAAA,CAACP,IAAI;UAAC0D,KAAK,EAAE;YACXhB,KAAK,EAAE,MAAM;YACboB,QAAQ,EAAE,EAAE;YACZE,UAAU,EAAE,iBAAiB;YAC7BD,YAAY,EAAE,EAAE;YAChBE,SAAS,EAAE;UACb,CAAE;UAAAR,QAAA,EAAC;QAEH,CAAM,CAAC,EAEPlD,IAAA,CAACV,UAAU;UAACoG,UAAU;UAACC,8BAA8B,EAAE,KAAM;UAAAzC,QAAA,EAC1DZ,YAAY,CAACsC,GAAG,CAAC,UAACgB,WAAW,EAAEN,KAAK;YAAA,OACnCpF,KAAA,CAACN,IAAI;cAAauD,KAAK,EAAE;gBAAEoC,WAAW,EAAE,EAAE;gBAAEM,KAAK,EAAE;cAAI,CAAE;cAAA3C,QAAA,GACvDhD,KAAA,CAACd,IAAI;gBAAC+D,KAAK,EAAE;kBAAES,aAAa,EAAE,KAAK;kBAAEN,UAAU,EAAE,QAAQ;kBAAEE,YAAY,EAAE;gBAAG,CAAE;gBAAAN,QAAA,GAC5ElD,IAAA,CAACZ,IAAI;kBAAC+D,KAAK,EAAE;oBACXgB,eAAe,EAAE,MAAM;oBACvBE,YAAY,EAAE,EAAE;oBAChBwB,KAAK,EAAE,EAAE;oBACTC,MAAM,EAAE,EAAE;oBACVxC,UAAU,EAAE,QAAQ;oBACpBY,cAAc,EAAE,QAAQ;oBACxBqB,WAAW,EAAE;kBACf,CAAE;kBAAArC,QAAA,EACAlD,IAAA,CAACP,IAAI;oBAAC0D,KAAK,EAAE;sBAAEI,QAAQ,EAAE;oBAAG,CAAE;oBAAAL,QAAA,EAAE0C,WAAW,CAACpD;kBAAM,CAAO;gBAAC,CACtD,CAAC,EACPtC,KAAA,CAACd,IAAI;kBAAC+D,KAAK,EAAE;oBAAEC,IAAI,EAAE;kBAAE,CAAE;kBAAAF,QAAA,GACvBlD,IAAA,CAACP,IAAI;oBAAC0D,KAAK,EAAE;sBACXhB,KAAK,EAAE,MAAM;sBACboB,QAAQ,EAAE,EAAE;sBACZE,UAAU,EAAE;oBACd,CAAE;oBAAAP,QAAA,EACC0C,WAAW,CAAC7D;kBAAI,CACb,CAAC,EACP/B,IAAA,CAACP,IAAI;oBAAC0D,KAAK,EAAE;sBACXhB,KAAK,EAAE,SAAS;sBAChBoB,QAAQ,EAAE,EAAE;sBACZE,UAAU,EAAE;oBACd,CAAE;oBAAAP,QAAA,EACC0C,WAAW,CAACrD;kBAAI,CACb,CAAC;gBAAA,CACH,CAAC,EACPvC,IAAA,CAACZ,IAAI;kBAAC+D,KAAK,EAAE;oBAAES,aAAa,EAAE;kBAAM,CAAE;kBAAAV,QAAA,EACnC6C,kBAAA,CAAIC,KAAK,CAACJ,WAAW,CAACnD,MAAM,CAAC,EAAEmC,GAAG,CAAC,UAACqB,CAAC,EAAEC,CAAC;oBAAA,OACvClG,IAAA,CAACP,IAAI;sBAAS0D,KAAK,EAAE;wBAAEhB,KAAK,EAAE,SAAS;wBAAEoB,QAAQ,EAAE;sBAAG,CAAE;sBAAAL,QAAA,EAAC;oBAAC,GAA/CgD,CAAqD,CAAC;kBAAA,CAClE;gBAAC,CACE,CAAC;cAAA,CACH,CAAC,EACPhG,KAAA,CAACT,IAAI;gBAAC0D,KAAK,EAAE;kBACXhB,KAAK,EAAE,MAAM;kBACboB,QAAQ,EAAE,EAAE;kBACZE,UAAU,EAAE,oBAAoB;kBAChC0C,UAAU,EAAE,EAAE;kBACdC,SAAS,EAAE;gBACb,CAAE;gBAAAlD,QAAA,GAAC,IACA,EAAC0C,WAAW,CAAClD,IAAI,EAAC,IACrB;cAAA,CAAM,CAAC;YAAA,GA3CE4C,KA4CL,CAAC;UAAA,CACR;QAAC,CACQ,CAAC;MAAA,CACT,CAAC,EAGPtF,IAAA,CAACZ,IAAI;QAAC+D,KAAK,EAAE;UAAEQ,iBAAiB,EAAE,EAAE;UAAEH,YAAY,EAAE;QAAG,CAAE;QAAAN,QAAA,EACvDhD,KAAA,CAACN,IAAI;UAACuD,KAAK,EAAE;YAAEgB,eAAe,EAAE,SAAS;YAAEd,OAAO,EAAE,EAAE;YAAEC,UAAU,EAAE;UAAS,CAAE;UAAAJ,QAAA,GAC7ElD,IAAA,CAACP,IAAI;YAAC0D,KAAK,EAAE;cACXhB,KAAK,EAAE,MAAM;cACboB,QAAQ,EAAE,EAAE;cACZE,UAAU,EAAE,iBAAiB;cAC7BC,SAAS,EAAE,QAAQ;cACnBF,YAAY,EAAE;YAChB,CAAE;YAAAN,QAAA,EAAC;UAEH,CAAM,CAAC,EACPlD,IAAA,CAACP,IAAI;YAAC0D,KAAK,EAAE;cACXhB,KAAK,EAAE,MAAM;cACboB,QAAQ,EAAE,EAAE;cACZE,UAAU,EAAE,oBAAoB;cAChCC,SAAS,EAAE,QAAQ;cACnBF,YAAY,EAAE,EAAE;cAChBkB,OAAO,EAAE;YACX,CAAE;YAAAxB,QAAA,EAAC;UAEH,CAAM,CAAC,EACPlD,IAAA,CAACR,MAAM;YACL8E,IAAI,EAAC,WAAW;YAChBC,OAAO,EAAE,SAATA,OAAOA,CAAA;cAAA,OAAQ3B,eAAe,CAAC9B,YAAY,CAAC;YAAA,CAAC;YAC7CqC,KAAK,EAAE;cACLgB,eAAe,EAAE,MAAM;cACvBR,iBAAiB,EAAE;YACrB,CAAE;YACFc,UAAU,EAAE;cACVtC,KAAK,EAAE,SAAS;cAChBsB,UAAU,EAAE,qBAAqB;cACjCF,QAAQ,EAAE;YACZ,CAAE;YAAAL,QAAA,EACH;UAED,CAAQ,CAAC,EACTlD,IAAA,CAACP,IAAI;YAAC0D,KAAK,EAAE;cACXhB,KAAK,EAAE,MAAM;cACboB,QAAQ,EAAE,EAAE;cACZE,UAAU,EAAE,oBAAoB;cAChCC,SAAS,EAAE,QAAQ;cACnBiB,SAAS,EAAE,CAAC;cACZD,OAAO,EAAE;YACX,CAAE;YAAAxB,QAAA,EAAC;UAEH,CAAM,CAAC;QAAA,CACH;MAAC,CACH,CAAC;IAAA,CACG;EAAC,CACN,CAAC;AAEd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}