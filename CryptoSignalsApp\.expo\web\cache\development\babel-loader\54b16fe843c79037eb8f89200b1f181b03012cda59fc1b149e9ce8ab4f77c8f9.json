{"ast": null, "code": "import Easing from \"../../vendor/react-native/Animated/Easing\";\nexport default Easing;", "map": {"version": 3, "names": ["Easing"], "sources": ["C:/Users/<USER>/Desktop/Signals/CryptoSignalsApp/node_modules/react-native-web/dist/exports/Easing/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport Easing from '../../vendor/react-native/Animated/Easing';\nexport default Easing;"], "mappings": "AASA,OAAOA,MAAM;AACb,eAAeA,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}