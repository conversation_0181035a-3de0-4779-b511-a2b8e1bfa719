{"ast": null, "code": "import React from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport TouchableOpacity from \"react-native-web/dist/exports/TouchableOpacity\";\nimport { useNavigation } from '@react-navigation/native';\nimport styles from \"./styles\";\nimport IconArrowLeft from \"../../components/Icon/ArrowLeft\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar PageTitle = function PageTitle(_ref) {\n  var text = _ref.text,\n    goBack = _ref.goBack;\n  var navigation = useNavigation();\n  return _jsxs(View, {\n    style: styles.container,\n    children: [goBack && _jsx(TouchableOpacity, {\n      style: styles.backButton,\n      onPress: function onPress() {\n        return navigation.navigate('Channels');\n      },\n      children: _jsx(IconArrowLeft, {})\n    }), _jsx(Text, {\n      style: styles.title,\n      children: text\n    })]\n  });\n};\nexport default PageTitle;", "map": {"version": 3, "names": ["React", "View", "Text", "TouchableOpacity", "useNavigation", "styles", "IconArrowLeft", "jsx", "_jsx", "jsxs", "_jsxs", "Page<PERSON><PERSON>le", "_ref", "text", "goBack", "navigation", "style", "container", "children", "backButton", "onPress", "navigate", "title"], "sources": ["E:/CryptoSignalsApp/src/components/PageTitle/index.js"], "sourcesContent": ["import React from 'react';\r\nimport { View, Text, TouchableOpacity } from 'react-native';\r\nimport { useNavigation } from '@react-navigation/native';\r\nimport styles from './styles'\r\nimport IconArrowLeft from '../../components/Icon/ArrowLeft'\r\n\r\nconst PageTitle = ({ text, goBack }) => {\r\n  const navigation = useNavigation();\r\n\r\n  return (\r\n    <View style={styles.container}>\r\n      {goBack && (\r\n        <TouchableOpacity style={styles.backButton} onPress={() => navigation.navigate('Channels')}>\r\n          <IconArrowLeft />\r\n        </TouchableOpacity>\r\n      )}\r\n      <Text style={styles.title}>{text}</Text>\r\n    </View>\r\n  )\r\n}\r\n\r\nexport default PageTitle;\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,gBAAA;AAE1B,SAASC,aAAa,QAAQ,0BAA0B;AACxD,OAAOC,MAAM;AACb,OAAOC,aAAa;AAAuC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAE3D,IAAMC,SAAS,GAAG,SAAZA,SAASA,CAAAC,IAAA,EAAyB;EAAA,IAAnBC,IAAI,GAAAD,IAAA,CAAJC,IAAI;IAAEC,MAAM,GAAAF,IAAA,CAANE,MAAM;EAC/B,IAAMC,UAAU,GAAGX,aAAa,CAAC,CAAC;EAElC,OACEM,KAAA,CAACT,IAAI;IAACe,KAAK,EAAEX,MAAM,CAACY,SAAU;IAAAC,QAAA,GAC3BJ,MAAM,IACLN,IAAA,CAACL,gBAAgB;MAACa,KAAK,EAAEX,MAAM,CAACc,UAAW;MAACC,OAAO,EAAE,SAATA,OAAOA,CAAA;QAAA,OAAQL,UAAU,CAACM,QAAQ,CAAC,UAAU,CAAC;MAAA,CAAC;MAAAH,QAAA,EACzFV,IAAA,CAACF,aAAa,IAAE;IAAC,CACD,CACnB,EACDE,IAAA,CAACN,IAAI;MAACc,KAAK,EAAEX,MAAM,CAACiB,KAAM;MAAAJ,QAAA,EAAEL;IAAI,CAAO,CAAC;EAAA,CACpC,CAAC;AAEX,CAAC;AAED,eAAeF,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}