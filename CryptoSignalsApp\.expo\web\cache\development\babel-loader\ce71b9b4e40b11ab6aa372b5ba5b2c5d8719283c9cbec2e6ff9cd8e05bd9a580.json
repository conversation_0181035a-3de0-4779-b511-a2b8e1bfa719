{"ast": null, "code": "export default function getHeaderTitle(options, fallback) {\n  return typeof options.headerTitle === 'string' ? options.headerTitle : options.title !== undefined ? options.title : fallback;\n}", "map": {"version": 3, "names": ["getHeaderTitle", "options", "fallback", "headerTitle", "title", "undefined"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\@react-navigation\\elements\\src\\Header\\getHeaderTitle.tsx"], "sourcesContent": ["import type { HeaderOptions } from '../types';\n\nexport default function getHeaderTitle(\n  options: { title?: string; headerTitle?: HeaderOptions['headerTitle'] },\n  fallback: string\n): string {\n  return typeof options.headerTitle === 'string'\n    ? options.headerTitle\n    : options.title !== undefined\n    ? options.title\n    : fallback;\n}\n"], "mappings": "AAEA,eAAe,SAASA,cAAcA,CACpCC,OAAuE,EACvEC,QAAgB,EACR;EACR,OAAO,OAAOD,OAAO,CAACE,WAAW,KAAK,QAAQ,GAC1CF,OAAO,CAACE,WAAW,GACnBF,OAAO,CAACG,KAAK,KAAKC,SAAS,GAC3BJ,OAAO,CAACG,KAAK,GACbF,QAAQ;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}