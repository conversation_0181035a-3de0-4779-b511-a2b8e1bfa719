{"ast": null, "code": "import View from \"react-native-web/dist/exports/View\";\nvar ScreenStack = View;\nexport default ScreenStack;", "map": {"version": 3, "names": ["ScreenStack", "View"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\react-native-screens\\src\\components\\ScreenStack.web.tsx"], "sourcesContent": ["import { View } from 'react-native';\n\nconst ScreenStack = View;\n\nexport default ScreenStack;\n"], "mappings": ";AAEA,IAAMA,WAAW,GAAGC,IAAI;AAExB,eAAeD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}