import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  BarChart3,
  Signal,
  Settings,
  Activity,
  Home,
  Menu,
  X,
  Wifi,
  WifiOff
} from 'lucide-react';

const Sidebar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [systemStatus, setSystemStatus] = useState('online');
  const location = useLocation();

  const navigation = [
    {
      name: 'Overview',
      href: '/overview',
      icon: Home,
      description: 'System metrics and status'
    },
    {
      name: 'Signals',
      href: '/signals',
      icon: Signal,
      description: 'Trading signals and history'
    },
    {
      name: 'Analytics',
      href: '/analytics',
      icon: BarChart3,
      description: 'Performance analysis'
    },
    {
      name: 'Settings',
      href: '/settings',
      icon: Settings,
      description: 'System configuration'
    },
  ];

  const isActive = (href) => {
    return location.pathname === href || (href === '/overview' && location.pathname === '/');
  };

  return (
    <>
      {/* Mobile Menu Button */}
      <button
        onClick={() => setIsOpen(true)}
        className="md:hidden fixed top-4 left-4 z-50 p-2 bg-gray-900 text-white rounded-lg shadow-lg"
      >
        <Menu size={20} />
      </button>

      {/* Mobile Overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
          onClick={() => setIsOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={`
        fixed md:relative z-50 md:z-auto
        transform ${isOpen ? 'translate-x-0' : '-translate-x-full'} md:translate-x-0
        transition-transform duration-300 ease-in-out
        bg-gray-900 text-white w-64 h-screen flex flex-col
      `}>
        {/* Header */}
        <div className="p-6 border-b border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Activity className="h-8 w-8 text-blue-400 mr-3" />
              <div>
                <h2 className="text-xl font-bold">CryptoSignals</h2>
                <p className="text-xs text-gray-400">Professional Dashboard</p>
              </div>
            </div>
            <button
              onClick={() => setIsOpen(false)}
              className="md:hidden text-gray-400 hover:text-white transition-colors"
            >
              <X size={20} />
            </button>
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-4 space-y-2">
          {navigation.map((item) => {
            const Icon = item.icon;
            return (
              <Link
                key={item.name}
                to={item.href}
                onClick={() => setIsOpen(false)}
                className={`
                  group flex items-center p-3 rounded-lg transition-all duration-200
                  ${isActive(item.href)
                    ? 'bg-blue-600 text-white shadow-lg'
                    : 'text-gray-300 hover:bg-gray-800 hover:text-white'
                  }
                `}
              >
                <Icon className={`h-5 w-5 mr-3 ${
                  isActive(item.href) ? 'text-white' : 'text-gray-400 group-hover:text-white'
                }`} />
                <div className="flex-1">
                  <div className="font-medium">{item.name}</div>
                  <div className="text-xs text-gray-400 group-hover:text-gray-300">
                    {item.description}
                  </div>
                </div>
              </Link>
            );
          })}
        </nav>

        {/* System Status */}
        <div className="p-4 border-t border-gray-700">
          <div className="bg-gray-800 rounded-lg p-4">
            <div className="flex items-center space-x-3 mb-3">
              <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-sm font-medium text-gray-300">System Online</span>
              <Wifi size={16} className="text-green-500" />
            </div>

            <div className="space-y-2 text-xs text-gray-400">
              <div className="flex justify-between">
                <span>Version:</span>
                <span className="text-gray-300">2.0.0</span>
              </div>
              <div className="flex justify-between">
                <span>Strategies:</span>
                <span className="text-green-400">8 Active</span>
              </div>
              <div className="flex justify-between">
                <span>Tokens:</span>
                <span className="text-blue-400">109 Monitored</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Sidebar;
