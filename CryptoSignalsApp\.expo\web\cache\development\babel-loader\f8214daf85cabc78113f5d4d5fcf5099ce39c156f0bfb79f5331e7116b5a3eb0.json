{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport React from \"react\";\nimport { createStackNavigator } from \"@react-navigation/stack\";\nimport { createBottomTabNavigator } from \"@react-navigation/bottom-tabs\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport Channels from \"./pages/Channels\";\nimport Premium from \"./pages/Premium\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar Tab = createBottomTabNavigator();\nvar Stack = createStackNavigator();\nfunction TabNavigator() {\n  var labelStyle = {\n    fontFamily: 'Poppins_500Medium',\n    fontSize: 20\n  };\n  return _jsxs(Tab.Navigator, {\n    screenOptions: function screenOptions(_ref) {\n      var route = _ref.route;\n      return {\n        headerShown: false,\n        tabBarIcon: function tabBarIcon() {\n          return null;\n        },\n        tabBarLabelPosition: 'beside-icon',\n        tabBarStyle: [{\n          backgroundColor: \"#202020\",\n          height: 60,\n          borderTopColor: \"#5d5d5d\"\n        }],\n        tabBarLabel: function tabBarLabel(_ref2) {\n          var focused = _ref2.focused;\n          return focused ? _jsx(Text, {\n            style: _objectSpread(_objectSpread({}, labelStyle), {}, {\n              color: \"#FECB37\"\n            }),\n            children: route.name\n          }) : _jsx(Text, {\n            style: _objectSpread(_objectSpread({}, labelStyle), {}, {\n              color: \"#8a8a8a\"\n            }),\n            children: route.name\n          });\n        }\n      };\n    },\n    children: [_jsx(Tab.Screen, {\n      name: \"Channels\",\n      component: Channels\n    }), _jsx(Tab.Screen, {\n      name: \"Premium\",\n      component: Premium\n    })]\n  });\n}\nexport default function Routes() {\n  return _jsx(TabNavigator, {});\n}", "map": {"version": 3, "names": ["React", "createStackNavigator", "createBottomTabNavigator", "Text", "Channels", "Premium", "jsx", "_jsx", "jsxs", "_jsxs", "Tab", "<PERSON><PERSON>", "TabNavigator", "labelStyle", "fontFamily", "fontSize", "Navigator", "screenOptions", "_ref", "route", "headerShown", "tabBarIcon", "tabBarLabelPosition", "tabBarStyle", "backgroundColor", "height", "borderTopColor", "tabBarLabel", "_ref2", "focused", "style", "_objectSpread", "color", "children", "name", "Screen", "component", "Routes"], "sources": ["E:/CryptoSignalsApp/src/routes.js"], "sourcesContent": ["import React from \"react\";\r\nimport { createStackNavigator } from \"@react-navigation/stack\";\r\nimport { createBottomTabNavigator } from \"@react-navigation/bottom-tabs\";\r\nimport { Text } from \"react-native\";\r\n\r\nimport Channels from \"./pages/Channels\";\r\nimport Premium from \"./pages/Premium\";\r\n\r\nconst Tab = createBottomTabNavigator();\r\nconst Stack = createStackNavigator();\r\n\r\nfunction TabNavigator() {\r\n  const labelStyle = {\r\n    fontFamily: 'Poppins_500Medium',\r\n    fontSize: 20,\r\n  }\r\n  return (\r\n    <Tab.Navigator\r\n      screenOptions={({ route }) => ({\r\n        headerShown: false,\r\n        tabBarIcon: () => null,\r\n        tabBarLabelPosition: 'beside-icon',\r\n        tabBarStyle: [\r\n          {\r\n            backgroundColor: \"#202020\",\r\n            height: 60,\r\n            borderTopColor: \"#5d5d5d\",\r\n          },\r\n        ],\r\n        tabBarLabel: ({ focused }) => {\r\n          return focused ? (\r\n            <Text style={{ ...labelStyle, color: \"#FECB37\" }}>\r\n              {route.name}\r\n            </Text>\r\n          ) : (\r\n            <Text\r\n              style={{ ...labelStyle, color: \"#8a8a8a\" }}\r\n            >\r\n              {route.name}\r\n            </Text>\r\n          );\r\n        },\r\n      })}\r\n    >\r\n      <Tab.Screen name=\"Channels\" component={Channels} />\r\n      <Tab.Screen name=\"Premium\" component={Premium} />\r\n    </Tab.Navigator>\r\n  );\r\n}\r\n\r\nexport default function Routes() {\r\n  return (\r\n    <TabNavigator />\r\n  );\r\n}\r\n\r\n/*\r\nAqui estão as mudanças que fiz:\r\n\r\nIntegrei o Drawer.Navigator diretamente no Routes para evitar redundância.\r\nRemovi a função AppNavigator que não estava sendo utilizada.\r\nA estrutura agora é que temos um Stack.Navigator principal com uma combinação de Drawer.Navigator e\r\n Tab.Navigator para uma melhor organização e navegação.\r\n*/"], "mappings": ";;;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,oBAAoB,QAAQ,yBAAyB;AAC9D,SAASC,wBAAwB,QAAQ,+BAA+B;AAAC,OAAAC,IAAA;AAGzE,OAAOC,QAAQ;AACf,OAAOC,OAAO;AAAwB,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAEtC,IAAMC,GAAG,GAAGR,wBAAwB,CAAC,CAAC;AACtC,IAAMS,KAAK,GAAGV,oBAAoB,CAAC,CAAC;AAEpC,SAASW,YAAYA,CAAA,EAAG;EACtB,IAAMC,UAAU,GAAG;IACjBC,UAAU,EAAE,mBAAmB;IAC/BC,QAAQ,EAAE;EACZ,CAAC;EACD,OACEN,KAAA,CAACC,GAAG,CAACM,SAAS;IACZC,aAAa,EAAE,SAAfA,aAAaA,CAAAC,IAAA;MAAA,IAAKC,KAAK,GAAAD,IAAA,CAALC,KAAK;MAAA,OAAQ;QAC7BC,WAAW,EAAE,KAAK;QAClBC,UAAU,EAAE,SAAZA,UAAUA,CAAA;UAAA,OAAQ,IAAI;QAAA;QACtBC,mBAAmB,EAAE,aAAa;QAClCC,WAAW,EAAE,CACX;UACEC,eAAe,EAAE,SAAS;UAC1BC,MAAM,EAAE,EAAE;UACVC,cAAc,EAAE;QAClB,CAAC,CACF;QACDC,WAAW,EAAE,SAAbA,WAAWA,CAAAC,KAAA,EAAmB;UAAA,IAAdC,OAAO,GAAAD,KAAA,CAAPC,OAAO;UACrB,OAAOA,OAAO,GACZtB,IAAA,CAACJ,IAAI;YAAC2B,KAAK,EAAAC,aAAA,CAAAA,aAAA,KAAOlB,UAAU;cAAEmB,KAAK,EAAE;YAAS,EAAG;YAAAC,QAAA,EAC9Cd,KAAK,CAACe;UAAI,CACP,CAAC,GAEP3B,IAAA,CAACJ,IAAI;YACH2B,KAAK,EAAAC,aAAA,CAAAA,aAAA,KAAOlB,UAAU;cAAEmB,KAAK,EAAE;YAAS,EAAG;YAAAC,QAAA,EAE1Cd,KAAK,CAACe;UAAI,CACP,CACP;QACH;MACF,CAAC;IAAA,CAAE;IAAAD,QAAA,GAEH1B,IAAA,CAACG,GAAG,CAACyB,MAAM;MAACD,IAAI,EAAC,UAAU;MAACE,SAAS,EAAEhC;IAAS,CAAE,CAAC,EACnDG,IAAA,CAACG,GAAG,CAACyB,MAAM;MAACD,IAAI,EAAC,SAAS;MAACE,SAAS,EAAE/B;IAAQ,CAAE,CAAC;EAAA,CACpC,CAAC;AAEpB;AAEA,eAAe,SAASgC,MAAMA,CAAA,EAAG;EAC/B,OACE9B,IAAA,CAACK,YAAY,IAAE,CAAC;AAEpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}