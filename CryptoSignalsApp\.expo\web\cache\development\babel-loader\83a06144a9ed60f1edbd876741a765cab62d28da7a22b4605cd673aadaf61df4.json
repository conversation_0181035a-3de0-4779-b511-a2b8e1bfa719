{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport color from 'color';\nexport var getLeftStyles = function getLeftStyles(alignToTop, description, isV3) {\n  var stylesV3 = {\n    marginRight: 0,\n    marginLeft: 16,\n    alignSelf: alignToTop ? 'flex-start' : 'center'\n  };\n  if (!description) {\n    return _objectSpread(_objectSpread(_objectSpread({}, styles.iconMarginLeft), styles.marginVerticalNone), isV3 && _objectSpread({}, stylesV3));\n  }\n  if (!isV3) {\n    return styles.iconMarginLeft;\n  }\n  return _objectSpread(_objectSpread({}, styles.iconMarginLeft), stylesV3);\n};\nexport var getRightStyles = function getRightStyles(alignToTop, description, isV3) {\n  var stylesV3 = {\n    marginLeft: 16,\n    alignSelf: alignToTop ? 'flex-start' : 'center'\n  };\n  if (!description) {\n    return _objectSpread(_objectSpread(_objectSpread({}, styles.iconMarginRight), styles.marginVerticalNone), isV3 && _objectSpread({}, stylesV3));\n  }\n  if (!isV3) {\n    return styles.iconMarginRight;\n  }\n  return _objectSpread(_objectSpread({}, styles.iconMarginRight), stylesV3);\n};\nvar styles = StyleSheet.create({\n  marginVerticalNone: {\n    marginVertical: 0\n  },\n  iconMarginLeft: {\n    marginLeft: 0,\n    marginRight: 16\n  },\n  iconMarginRight: {\n    marginRight: 0\n  }\n});\nexport var getAccordionColors = function getAccordionColors(_ref) {\n  var theme = _ref.theme,\n    isExpanded = _ref.isExpanded,\n    customRippleColor = _ref.customRippleColor;\n  var _theme$colors;\n  var titleColor = theme.isV3 ? theme.colors.onSurface : color(theme.colors.text).alpha(0.87).rgb().string();\n  var descriptionColor = theme.isV3 ? theme.colors.onSurfaceVariant : color(theme.colors.text).alpha(0.54).rgb().string();\n  var titleTextColor = isExpanded ? (_theme$colors = theme.colors) === null || _theme$colors === void 0 ? void 0 : _theme$colors.primary : titleColor;\n  var rippleColor = customRippleColor || color(titleTextColor).alpha(0.12).rgb().string();\n  return {\n    titleColor: titleColor,\n    descriptionColor: descriptionColor,\n    titleTextColor: titleTextColor,\n    rippleColor: rippleColor\n  };\n};", "map": {"version": 3, "names": ["color", "getLeftStyles", "alignToTop", "description", "isV3", "stylesV3", "marginRight", "marginLeft", "alignSelf", "_objectSpread", "styles", "iconMarginLeft", "marginVerticalNone", "getRightStyles", "iconMarginRight", "StyleSheet", "create", "marginVertical", "getAccordionColors", "_ref", "theme", "isExpanded", "customRippleColor", "_theme$colors", "titleColor", "colors", "onSurface", "text", "alpha", "rgb", "string", "descriptionColor", "onSurfaceVariant", "titleTextColor", "primary", "rippleColor"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\List\\utils.ts"], "sourcesContent": ["import {\n  FlexAlignType,\n  ColorValue,\n  StyleSheet,\n  StyleProp,\n  ViewStyle,\n} from 'react-native';\n\nimport color from 'color';\nimport type { EllipsizeProp, InternalTheme, ThemeProp } from 'src/types';\n\ntype Description =\n  | React.ReactNode\n  | ((props: {\n      selectable: boolean;\n      ellipsizeMode: EllipsizeProp | undefined;\n      color: string;\n      fontSize: number;\n    }) => React.ReactNode);\n\nexport type ListChildProps = {\n  left?: React.ReactNode;\n  right?: React.ReactNode;\n  style?: StyleProp<ViewStyle>;\n  theme?: ThemeProp;\n};\n\nexport type Style = {\n  marginLeft?: number;\n  marginRight?: number;\n  marginVertical?: number;\n  alignSelf?: FlexAlignType;\n};\n\nexport const getLeftStyles = (\n  alignToTop: boolean,\n  description: Description,\n  isV3: boolean\n) => {\n  const stylesV3 = {\n    marginRight: 0,\n    marginLeft: 16,\n    alignSelf: alignToTop ? 'flex-start' : 'center',\n  };\n\n  if (!description) {\n    return {\n      ...styles.iconMarginLeft,\n      ...styles.marginVerticalNone,\n      ...(isV3 && { ...stylesV3 }),\n    };\n  }\n\n  if (!isV3) {\n    return styles.iconMarginLeft;\n  }\n\n  return {\n    ...styles.iconMarginLeft,\n    ...stylesV3,\n  };\n};\n\nexport const getRightStyles = (\n  alignToTop: boolean,\n  description: Description,\n  isV3: boolean\n) => {\n  const stylesV3 = {\n    marginLeft: 16,\n    alignSelf: alignToTop ? 'flex-start' : 'center',\n  };\n\n  if (!description) {\n    return {\n      ...styles.iconMarginRight,\n      ...styles.marginVerticalNone,\n      ...(isV3 && { ...stylesV3 }),\n    };\n  }\n\n  if (!isV3) {\n    return styles.iconMarginRight;\n  }\n\n  return {\n    ...styles.iconMarginRight,\n    ...stylesV3,\n  };\n};\n\nconst styles = StyleSheet.create({\n  marginVerticalNone: { marginVertical: 0 },\n  iconMarginLeft: { marginLeft: 0, marginRight: 16 },\n  iconMarginRight: { marginRight: 0 },\n});\n\nexport const getAccordionColors = ({\n  theme,\n  isExpanded,\n  customRippleColor,\n}: {\n  theme: InternalTheme;\n  isExpanded?: boolean;\n  customRippleColor?: ColorValue;\n}) => {\n  const titleColor = theme.isV3\n    ? theme.colors.onSurface\n    : color(theme.colors.text).alpha(0.87).rgb().string();\n\n  const descriptionColor = theme.isV3\n    ? theme.colors.onSurfaceVariant\n    : color(theme.colors.text).alpha(0.54).rgb().string();\n\n  const titleTextColor = isExpanded ? theme.colors?.primary : titleColor;\n\n  const rippleColor =\n    customRippleColor || color(titleTextColor).alpha(0.12).rgb().string();\n\n  return {\n    titleColor,\n    descriptionColor,\n    titleTextColor,\n    rippleColor,\n  };\n};\n"], "mappings": ";;;;AAQA,OAAOA,KAAK,MAAM,OAAO;AA0BzB,OAAO,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CACxBC,UAAmB,EACnBC,WAAwB,EACxBC,IAAa,EACV;EACH,IAAMC,QAAQ,GAAG;IACfC,WAAW,EAAE,CAAC;IACdC,UAAU,EAAE,EAAE;IACdC,SAAS,EAAEN,UAAU,GAAG,YAAY,GAAG;EACzC,CAAC;EAED,IAAI,CAACC,WAAW,EAAE;IAChB,OAAAM,aAAA,CAAAA,aAAA,CAAAA,aAAA,KACKC,MAAM,CAACC,cAAc,GACrBD,MAAM,CAACE,kBAAkB,GACxBR,IAAI,IAAAK,aAAA,KAASJ,QAAA,CAAU;EAE/B;EAEA,IAAI,CAACD,IAAI,EAAE;IACT,OAAOM,MAAM,CAACC,cAAc;EAC9B;EAEA,OAAAF,aAAA,CAAAA,aAAA,KACKC,MAAM,CAACC,cAAc,GACrBN,QAAA;AAEP,CAAC;AAED,OAAO,IAAMQ,cAAc,GAAG,SAAjBA,cAAcA,CACzBX,UAAmB,EACnBC,WAAwB,EACxBC,IAAa,EACV;EACH,IAAMC,QAAQ,GAAG;IACfE,UAAU,EAAE,EAAE;IACdC,SAAS,EAAEN,UAAU,GAAG,YAAY,GAAG;EACzC,CAAC;EAED,IAAI,CAACC,WAAW,EAAE;IAChB,OAAAM,aAAA,CAAAA,aAAA,CAAAA,aAAA,KACKC,MAAM,CAACI,eAAe,GACtBJ,MAAM,CAACE,kBAAkB,GACxBR,IAAI,IAAAK,aAAA,KAASJ,QAAA,CAAU;EAE/B;EAEA,IAAI,CAACD,IAAI,EAAE;IACT,OAAOM,MAAM,CAACI,eAAe;EAC/B;EAEA,OAAAL,aAAA,CAAAA,aAAA,KACKC,MAAM,CAACI,eAAe,GACtBT,QAAA;AAEP,CAAC;AAED,IAAMK,MAAM,GAAGK,UAAU,CAACC,MAAM,CAAC;EAC/BJ,kBAAkB,EAAE;IAAEK,cAAc,EAAE;EAAE,CAAC;EACzCN,cAAc,EAAE;IAAEJ,UAAU,EAAE,CAAC;IAAED,WAAW,EAAE;EAAG,CAAC;EAClDQ,eAAe,EAAE;IAAER,WAAW,EAAE;EAAE;AACpC,CAAC,CAAC;AAEF,OAAO,IAAMY,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAAC,IAAA,EAQzB;EAAA,IAPJC,KAAK,GAAAD,IAAA,CAALC,KAAK;IACLC,UAAU,GAAAF,IAAA,CAAVE,UAAU;IACVC,iBAAA,GAAAH,IAAA,CAAAG,iBAAA;EAKI,IAAAC,aAAA;EACJ,IAAMC,UAAU,GAAGJ,KAAK,CAAChB,IAAI,GACzBgB,KAAK,CAACK,MAAM,CAACC,SAAS,GACtB1B,KAAK,CAACoB,KAAK,CAACK,MAAM,CAACE,IAAI,CAAC,CAACC,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EAEvD,IAAMC,gBAAgB,GAAGX,KAAK,CAAChB,IAAI,GAC/BgB,KAAK,CAACK,MAAM,CAACO,gBAAgB,GAC7BhC,KAAK,CAACoB,KAAK,CAACK,MAAM,CAACE,IAAI,CAAC,CAACC,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EAEvD,IAAMG,cAAc,GAAGZ,UAAU,IAAAE,aAAA,GAAGH,KAAK,CAACK,MAAM,cAAAF,aAAA,uBAAZA,aAAA,CAAcW,OAAO,GAAGV,UAAU;EAEtE,IAAMW,WAAW,GACfb,iBAAiB,IAAItB,KAAK,CAACiC,cAAc,CAAC,CAACL,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EAEvE,OAAO;IACLN,UAAU,EAAVA,UAAU;IACVO,gBAAgB,EAAhBA,gBAAgB;IAChBE,cAAc,EAAdA,cAAc;IACdE,WAAA,EAAAA;EACF,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}