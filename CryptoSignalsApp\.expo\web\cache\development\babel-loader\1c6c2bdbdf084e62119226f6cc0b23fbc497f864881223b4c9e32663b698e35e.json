{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport { useEffect, useState } from 'react';\nimport { loadAsync } from 'expo-font';\nexport function useFonts(map) {\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    loaded = _useState2[0],\n    setLoaded = _useState2[1];\n  var _useState3 = useState(null),\n    _useState4 = _slicedToArray(_useState3, 2),\n    error = _useState4[0],\n    setError = _useState4[1];\n  useEffect(function () {\n    loadAsync(map).then(function () {\n      return setLoaded(true);\n    }).catch(setError);\n  }, []);\n  return [loaded, error];\n}", "map": {"version": 3, "names": ["useEffect", "useState", "loadAsync", "useFonts", "map", "_useState", "_useState2", "_slicedToArray", "loaded", "setLoaded", "_useState3", "_useState4", "error", "setError", "then", "catch"], "sources": ["C:/Users/<USER>/Desktop/Signals/CryptoSignalsApp/node_modules/@expo-google-fonts/poppins/useFonts.js"], "sourcesContent": ["import { useEffect, useState } from 'react';\n\nimport { loadAsync } from 'expo-font';\n\n/**\n * Load a map of custom fonts to use in textual elements.\n * The map keys are used as font names, and can be used with `fontFamily: <name>;`.\n * It returns a boolean describing if all fonts are loaded.\n *\n * Note, the fonts are not \"reloaded\" when you dynamically change the font map.\n *\n * @see https://docs.expo.io/versions/latest/sdk/font/\n * @example const [loaded, error] = useFonts(...);\n */\nexport function useFonts(map) {\n  let [loaded, setLoaded] = useState(false);\n  let [error, setError] = useState(null);\n\n  useEffect(() => {\n    loadAsync(map)\n      .then(() => setLoaded(true))\n      .catch(setError);\n  }, []);\n\n  return [loaded, error];\n}\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAE3C,SAASC,SAAS,QAAQ,WAAW;AAYrC,OAAO,SAASC,QAAQA,CAACC,GAAG,EAAE;EAC5B,IAAAC,SAAA,GAA0BJ,QAAQ,CAAC,KAAK,CAAC;IAAAK,UAAA,GAAAC,cAAA,CAAAF,SAAA;IAApCG,MAAM,GAAAF,UAAA;IAAEG,SAAS,GAAAH,UAAA;EACtB,IAAAI,UAAA,GAAwBT,QAAQ,CAAC,IAAI,CAAC;IAAAU,UAAA,GAAAJ,cAAA,CAAAG,UAAA;IAAjCE,KAAK,GAAAD,UAAA;IAAEE,QAAQ,GAAAF,UAAA;EAEpBX,SAAS,CAAC,YAAM;IACdE,SAAS,CAACE,GAAG,CAAC,CACXU,IAAI,CAAC;MAAA,OAAML,SAAS,CAAC,IAAI,CAAC;IAAA,EAAC,CAC3BM,KAAK,CAACF,QAAQ,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAEN,OAAO,CAACL,MAAM,EAAEI,KAAK,CAAC;AACxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}