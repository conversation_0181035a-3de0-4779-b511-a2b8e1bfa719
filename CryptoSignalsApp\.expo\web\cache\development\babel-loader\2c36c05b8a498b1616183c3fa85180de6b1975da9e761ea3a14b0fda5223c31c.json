{"ast": null, "code": "export var handlePress = function handlePress(_ref) {\n  var onPress = _ref.onPress,\n    value = _ref.value,\n    onValueChange = _ref.onValueChange,\n    event = _ref.event;\n  if (onPress && onValueChange) {\n    console.warn(`onPress in the scope of RadioButtonGroup will not be executed, use onValueChange instead`);\n  }\n  onValueChange ? onValueChange(value) : onPress === null || onPress === void 0 ? void 0 : onPress(event);\n};\nexport var isChecked = function isChecked(_ref2) {\n  var value = _ref2.value,\n    status = _ref2.status,\n    contextValue = _ref2.contextValue;\n  if (contextValue !== undefined && contextValue !== null) {\n    return contextValue === value ? 'checked' : 'unchecked';\n  } else {\n    return status;\n  }\n};", "map": {"version": 3, "names": ["handlePress", "_ref", "onPress", "value", "onValueChange", "event", "console", "warn", "isChecked", "_ref2", "status", "contextValue", "undefined"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\RadioButton\\utils.ts"], "sourcesContent": ["import type { GestureResponderEvent } from 'react-native';\n\nexport const handlePress = ({\n  onPress,\n  value,\n  onValueChange,\n  event,\n}: {\n  onPress?: (e: GestureResponderEvent) => void;\n  value: string;\n  onValueChange?: (value: string) => void;\n  event: GestureResponderEvent;\n}) => {\n  if (onPress && onValueChange) {\n    console.warn(\n      `onPress in the scope of RadioButtonGroup will not be executed, use onValueChange instead`\n    );\n  }\n\n  onValueChange ? onValueChange(value) : onPress?.(event);\n};\n\nexport const isChecked = ({\n  value,\n  status,\n  contextValue,\n}: {\n  value: string;\n  status?: 'checked' | 'unchecked';\n  contextValue?: string;\n}) => {\n  if (contextValue !== undefined && contextValue !== null) {\n    return contextValue === value ? 'checked' : 'unchecked';\n  } else {\n    return status;\n  }\n};\n"], "mappings": "AAEA,OAAO,IAAMA,WAAW,GAAG,SAAdA,WAAWA,CAAAC,IAAA,EAUlB;EAAA,IATJC,OAAO,GAAAD,IAAA,CAAPC,OAAO;IACPC,KAAK,GAAAF,IAAA,CAALE,KAAK;IACLC,aAAa,GAAAH,IAAA,CAAbG,aAAa;IACbC,KAAA,GAAAJ,IAAA,CAAAI,KAAA;EAOA,IAAIH,OAAO,IAAIE,aAAa,EAAE;IAC5BE,OAAO,CAACC,IAAI,CACV,0FACF,CAAC;EACH;EAEAH,aAAa,GAAGA,aAAa,CAACD,KAAK,CAAC,GAAGD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAGG,KAAK,CAAC;AACzD,CAAC;AAED,OAAO,IAAMG,SAAS,GAAG,SAAZA,SAASA,CAAAC,KAAA,EAQhB;EAAA,IAPJN,KAAK,GAAAM,KAAA,CAALN,KAAK;IACLO,MAAM,GAAAD,KAAA,CAANC,MAAM;IACNC,YAAA,GAAAF,KAAA,CAAAE,YAAA;EAMA,IAAIA,YAAY,KAAKC,SAAS,IAAID,YAAY,KAAK,IAAI,EAAE;IACvD,OAAOA,YAAY,KAAKR,KAAK,GAAG,SAAS,GAAG,WAAW;EACzD,CAAC,MAAM;IACL,OAAOO,MAAM;EACf;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}