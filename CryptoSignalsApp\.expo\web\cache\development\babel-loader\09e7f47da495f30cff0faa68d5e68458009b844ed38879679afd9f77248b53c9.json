{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport { useTheme } from '@react-navigation/native';\nimport * as React from 'react';\nimport StatusBar from \"react-native-web/dist/exports/StatusBar\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nexport default function ModalStatusBarManager(_ref) {\n  var _flattenedStyle$trans, _flattenedStyle$trans2;\n  var dark = _ref.dark,\n    layout = _ref.layout,\n    insets = _ref.insets,\n    style = _ref.style;\n  var _useTheme = useTheme(),\n    darkTheme = _useTheme.dark;\n  var _React$useState = React.useState(true),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    overlapping = _React$useState2[0],\n    setOverlapping = _React$useState2[1];\n  var scale = 1 - 20 / layout.width;\n  var offset = (insets.top - 34) * scale;\n  var flattenedStyle = StyleSheet.flatten(style);\n  var translateY = flattenedStyle === null || flattenedStyle === void 0 ? void 0 : (_flattenedStyle$trans = flattenedStyle.transform) === null || _flattenedStyle$trans === void 0 ? void 0 : (_flattenedStyle$trans2 = _flattenedStyle$trans.find(function (s) {\n    return s.translateY !== undefined;\n  })) === null || _flattenedStyle$trans2 === void 0 ? void 0 : _flattenedStyle$trans2.translateY;\n  React.useEffect(function () {\n    var listener = function listener(_ref2) {\n      var value = _ref2.value;\n      setOverlapping(value < offset);\n    };\n    var sub = translateY === null || translateY === void 0 ? void 0 : translateY.addListener(listener);\n    return function () {\n      return translateY === null || translateY === void 0 ? void 0 : translateY.removeListener(sub);\n    };\n  }, [offset, translateY]);\n  var darkContent = dark != null ? dark : !darkTheme;\n  return React.createElement(StatusBar, {\n    animated: true,\n    barStyle: overlapping && darkContent ? 'dark-content' : 'light-content'\n  });\n}", "map": {"version": 3, "names": ["useTheme", "React", "StatusBar", "StyleSheet", "ModalStatusBarManager", "_ref", "_flattenedStyle$trans", "_flattenedStyle$trans2", "dark", "layout", "insets", "style", "_useTheme", "darkTheme", "_React$useState", "useState", "_React$useState2", "_slicedToArray", "overlapping", "setOverlapping", "scale", "width", "offset", "top", "flattenedStyle", "flatten", "translateY", "transform", "find", "s", "undefined", "useEffect", "listener", "_ref2", "value", "sub", "addListener", "removeListener", "darkContent", "createElement", "animated", "barStyle"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\@react-navigation\\stack\\src\\views\\ModalStatusBarManager.tsx"], "sourcesContent": ["import { useTheme } from '@react-navigation/native';\nimport * as React from 'react';\nimport { StatusBar, StyleSheet } from 'react-native';\nimport type { EdgeInsets } from 'react-native-safe-area-context';\n\nimport type { Layout } from '../types';\n\ntype Props = {\n  dark: boolean | undefined;\n  layout: Layout;\n  insets: EdgeInsets;\n  style: any;\n};\n\nexport default function ModalStatusBarManager({\n  dark,\n  layout,\n  insets,\n  style,\n}: Props) {\n  const { dark: darkTheme } = useTheme();\n  const [overlapping, setOverlapping] = React.useState(true);\n\n  const scale = 1 - 20 / layout.width;\n  const offset = (insets.top - 34) * scale;\n\n  const flattenedStyle = StyleSheet.flatten(style);\n  const translateY = flattenedStyle?.transform?.find(\n    (s: any) => s.translateY !== undefined\n  )?.translateY;\n\n  React.useEffect(() => {\n    const listener = ({ value }: { value: number }) => {\n      setOverlapping(value < offset);\n    };\n\n    const sub = translateY?.addListener(listener);\n\n    return () => translateY?.removeListener(sub);\n  }, [offset, translateY]);\n\n  const darkContent = dark ?? !darkTheme;\n\n  return (\n    <StatusBar\n      animated\n      barStyle={overlapping && darkContent ? 'dark-content' : 'light-content'}\n    />\n  );\n}\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,0BAA0B;AACnD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAAA,OAAAC,SAAA;AAAA,OAAAC,UAAA;AAa9B,eAAe,SAASC,qBAAqBA,CAAAC,IAAA,EAKnC;EAAA,IAAAC,qBAAA,EAAAC,sBAAA;EAAA,IAJRC,IAAI,GAIEH,IAAA,CAJNG,IAAI;IACJC,MAAM,GAGAJ,IAAA,CAHNI,MAAM;IACNC,MAAM,GAEAL,IAAA,CAFNK,MAAM;IACNC,KAAA,GACMN,IAAA,CADNM,KAAA;EAEA,IAAAC,SAAA,GAA4BZ,QAAQ,EAAE;IAAxBa,SAAA,GAAAD,SAAA,CAANJ,IAAI;EACZ,IAAAM,eAAA,GAAsCb,KAAK,CAACc,QAAQ,CAAC,IAAI,CAAC;IAAAC,gBAAA,GAAAC,cAAA,CAAAH,eAAA;IAAnDI,WAAW,GAAAF,gBAAA;IAAEG,cAAc,GAAAH,gBAAA;EAElC,IAAMI,KAAK,GAAG,CAAC,GAAG,EAAE,GAAGX,MAAM,CAACY,KAAK;EACnC,IAAMC,MAAM,GAAG,CAACZ,MAAM,CAACa,GAAG,GAAG,EAAE,IAAIH,KAAK;EAExC,IAAMI,cAAc,GAAGrB,UAAU,CAACsB,OAAO,CAACd,KAAK,CAAC;EAChD,IAAMe,UAAU,GAAGF,cAAc,aAAdA,cAAc,wBAAAlB,qBAAA,GAAdkB,cAAc,CAAEG,SAAS,cAAArB,qBAAA,wBAAAC,sBAAA,GAAzBD,qBAAA,CAA2BsB,IAAI,CAC/C,UAAAC,CAAM;IAAA,OAAKA,CAAC,CAACH,UAAU,KAAKI,SAAS;EAAA,EACvC,cAAAvB,sBAAA,uBAFkBA,sBAAA,CAEhBmB,UAAU;EAEbzB,KAAK,CAAC8B,SAAS,CAAC,YAAM;IACpB,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAGC,KAAA,EAAkC;MAAA,IAA/BC,KAAA,GAA0BD,KAAA,CAA1BC,KAAA;MAClBf,cAAc,CAACe,KAAK,GAAGZ,MAAM,CAAC;IAChC,CAAC;IAED,IAAMa,GAAG,GAAGT,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEU,WAAW,CAACJ,QAAQ,CAAC;IAE7C,OAAO;MAAA,OAAMN,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEW,cAAc,CAACF,GAAG,CAAC;IAAA;EAC9C,CAAC,EAAE,CAACb,MAAM,EAAEI,UAAU,CAAC,CAAC;EAExB,IAAMY,WAAW,GAAG9B,IAAI,WAAJA,IAAI,GAAI,CAACK,SAAS;EAEtC,OACEZ,KAAA,CAAAsC,aAAA,CAACrC,SAAS;IACRsC,QAAQ;IACRC,QAAQ,EAAEvB,WAAW,IAAIoB,WAAW,GAAG,cAAc,GAAG;EAAgB,EACxE;AAEN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}