#!/usr/bin/env python3
import os
import paramiko
import zipfile
from datetime import datetime
import time

# Configurações do servidor
SERVER_HOST = "**************"
SERVER_USER = "root"
SERVER_PASS = "h4*ls:FtJw0e"
PROJECT_DIR = "gerador_sinais_telegram"
REMOTE_PATH = f"/root/{PROJECT_DIR}"

def print_step(message):
    """Imprime uma mensagem formatada para indicar o passo atual"""
    print("\n" + "="*80)
    print(f">>> {message}")
    print("="*80 + "\n")

def create_zip():
    """Cria um arquivo zip com o conteúdo do projeto"""
    print_step("Criando arquivo ZIP do projeto")
    
    # Nome do arquivo zip com timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    zip_filename = f"{PROJECT_DIR}_{timestamp}.zip"
    
    # Lista de arquivos/pastas para ignorar
    ignore_patterns = [
        '__pycache__', 
        '*.pyc', 
        'venv', 
        '*.zip',
        '*.log',
        '*.session',
        '.env'
    ]
    
    def should_ignore(file):
        return any(pattern in file for pattern in ignore_patterns)
    
    with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(PROJECT_DIR):
            # Remover diretórios ignorados
            dirs[:] = [d for d in dirs if not should_ignore(d)]
            
            for file in files:
                if not should_ignore(file):
                    file_path = os.path.join(root, file)
                    arcname = os.path.relpath(file_path, PROJECT_DIR)
                    zipf.write(file_path, arcname)
    
    return zip_filename

def execute_remote_commands(ssh, commands, get_output=False):
    """Executa comandos no servidor remoto"""
    stdin, stdout, stderr = ssh.exec_command(commands)
    if get_output:
        output = stdout.read().decode()
        error = stderr.read().decode()
        return output, error
    else:
        # Aguardar o comando terminar
        stdout.channel.recv_exit_status()

def update_server():
    """Atualiza a aplicação no servidor"""
    try:
        # 1. Criar arquivo ZIP
        zip_filename = create_zip()
        print(f"Arquivo ZIP criado: {zip_filename}")

        # 2. Conectar ao servidor
        print_step("Conectando ao servidor")
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(SERVER_HOST, username=SERVER_USER, password=SERVER_PASS)
        sftp = ssh.open_sftp()

        # 3. Fazer upload do arquivo
        print_step("Enviando arquivo para o servidor")
        remote_zip = f"/root/{zip_filename}"
        sftp.put(zip_filename, remote_zip)

        # 4. Parar o serviço existente
        print_step("Parando o serviço")
        execute_remote_commands(ssh, "systemctl stop gerador_sinais.service")

        # 5. Fazer backup do .env atual
        print_step("Fazendo backup do .env")
        execute_remote_commands(ssh, f"""
            cd {REMOTE_PATH}
            cp .env .env.backup || true
        """)

        # 6. Extrair o novo código
        print_step("Extraindo arquivos")
        execute_remote_commands(ssh, f"""
            cd /root
            rm -rf {PROJECT_DIR}_old
            mv {PROJECT_DIR} {PROJECT_DIR}_old || true
            unzip -o {zip_filename}
            rm {zip_filename}
        """)

        # 7. Restaurar o .env
        print_step("Restaurando .env")
        execute_remote_commands(ssh, f"""
            cd {REMOTE_PATH}
            cp ../{PROJECT_DIR}_old/.env .env || cp .env.example .env
        """)

        # 8. Configurar ambiente virtual
        print_step("Configurando ambiente virtual")
        execute_remote_commands(ssh, f"""
            cd {REMOTE_PATH}
            rm -rf venv
            apt update
            apt install -y python3-full build-essential
            python3 -m venv venv
            source venv/bin/activate
            pip install --upgrade pip setuptools wheel
            pip install -r requirements.txt
        """)

        # 9. Reiniciar o serviço
        print_step("Reiniciando o serviço")
        execute_remote_commands(ssh, """
            systemctl daemon-reload
            systemctl restart gerador_sinais.service
            systemctl enable gerador_sinais.service
        """)

        # 10. Verificar status do serviço
        print_step("Verificando status do serviço")
        output, error = execute_remote_commands(ssh, "systemctl status gerador_sinais.service", get_output=True)
        print("Status do serviço:")
        print(output)
        
        if error:
            print("Erros encontrados:")
            print(error)

        # 11. Limpar arquivos temporários
        print_step("Limpando arquivos temporários")
        os.remove(zip_filename)
        
        # Fechar conexões
        sftp.close()
        ssh.close()

        print_step("Atualização concluída com sucesso!")
        
    except Exception as e:
        print(f"\nERRO: {str(e)}")
        return False

    return True

if __name__ == "__main__":
    print_step("Iniciando processo de atualização")
    success = update_server()
    if success:
        print("\nProcesso de atualização concluído com sucesso!")
    else:
        print("\nOcorreu um erro durante a atualização. Verifique os logs acima.") 