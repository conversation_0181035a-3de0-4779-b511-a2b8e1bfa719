{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"icon\", \"label\", \"background\", \"accessibilityLabel\", \"accessibilityState\", \"color\", \"rippleColor\", \"disabled\", \"onPress\", \"onLongPress\", \"delayLongPress\", \"theme\", \"style\", \"visible\", \"uppercase\", \"testID\", \"animateFrom\", \"extended\", \"iconMode\", \"variant\", \"labelMaxFontSizeMultiplier\", \"hitSlop\"],\n  _excluded2 = [\"backgroundColor\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nimport * as React from 'react';\nimport Animated from \"react-native-web/dist/exports/Animated\";\nimport Easing from \"react-native-web/dist/exports/Easing\";\nimport I18nManager from \"react-native-web/dist/exports/I18nManager\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport ScrollView from \"react-native-web/dist/exports/ScrollView\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport color from 'color';\nimport { getCombinedStyles, getFABColors, getLabelSizeWeb } from \"./utils\";\nimport { useInternalTheme } from \"../../core/theming\";\nimport Icon from \"../Icon\";\nimport Surface from \"../Surface\";\nimport TouchableRipple from \"../TouchableRipple/TouchableRipple\";\nimport AnimatedText from \"../Typography/AnimatedText\";\nvar SIZE = 56;\nvar SCALE = 0.9;\nvar AnimatedFAB = function AnimatedFAB(_ref) {\n  var _ref2, _ref3;\n  var icon = _ref.icon,\n    label = _ref.label,\n    background = _ref.background,\n    _ref$accessibilityLab = _ref.accessibilityLabel,\n    accessibilityLabel = _ref$accessibilityLab === void 0 ? label : _ref$accessibilityLab,\n    accessibilityState = _ref.accessibilityState,\n    customColor = _ref.color,\n    customRippleColor = _ref.rippleColor,\n    disabled = _ref.disabled,\n    onPress = _ref.onPress,\n    onLongPress = _ref.onLongPress,\n    delayLongPress = _ref.delayLongPress,\n    themeOverrides = _ref.theme,\n    style = _ref.style,\n    _ref$visible = _ref.visible,\n    visible = _ref$visible === void 0 ? true : _ref$visible,\n    uppercaseProp = _ref.uppercase,\n    _ref$testID = _ref.testID,\n    testID = _ref$testID === void 0 ? 'animated-fab' : _ref$testID,\n    _ref$animateFrom = _ref.animateFrom,\n    animateFrom = _ref$animateFrom === void 0 ? 'right' : _ref$animateFrom,\n    _ref$extended = _ref.extended,\n    extended = _ref$extended === void 0 ? false : _ref$extended,\n    _ref$iconMode = _ref.iconMode,\n    iconMode = _ref$iconMode === void 0 ? 'dynamic' : _ref$iconMode,\n    _ref$variant = _ref.variant,\n    variant = _ref$variant === void 0 ? 'primary' : _ref$variant,\n    labelMaxFontSizeMultiplier = _ref.labelMaxFontSizeMultiplier,\n    hitSlop = _ref.hitSlop,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var theme = useInternalTheme(themeOverrides);\n  var uppercase = uppercaseProp != null ? uppercaseProp : !theme.isV3;\n  var isIOS = Platform.OS === 'ios';\n  var isWeb = Platform.OS === 'web';\n  var isAnimatedFromRight = animateFrom === 'right';\n  var isIconStatic = iconMode === 'static';\n  var isRTL = I18nManager.isRTL;\n  var labelRef = React.useRef(null);\n  var _React$useRef = React.useRef(new Animated.Value(visible ? 1 : 0)),\n    visibility = _React$useRef.current;\n  var _React$useRef2 = React.useRef(new Animated.Value(0)),\n    animFAB = _React$useRef2.current;\n  var isV3 = theme.isV3,\n    animation = theme.animation;\n  var scale = animation.scale;\n  var labelSize = isWeb ? getLabelSizeWeb(labelRef) : null;\n  var _React$useState = React.useState((_ref2 = labelSize === null || labelSize === void 0 ? void 0 : labelSize.width) != null ? _ref2 : 0),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    textWidth = _React$useState2[0],\n    setTextWidth = _React$useState2[1];\n  var _React$useState3 = React.useState((_ref3 = labelSize === null || labelSize === void 0 ? void 0 : labelSize.height) != null ? _ref3 : 0),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    textHeight = _React$useState4[0],\n    setTextHeight = _React$useState4[1];\n  var borderRadius = SIZE / (isV3 ? 3.5 : 2);\n  React.useEffect(function () {\n    if (!isWeb) {\n      return;\n    }\n    var updateTextSize = function updateTextSize() {\n      if (labelRef.current) {\n        var _labelSize = getLabelSizeWeb(labelRef);\n        if (_labelSize) {\n          var _labelSize$height, _labelSize$width;\n          setTextHeight((_labelSize$height = _labelSize.height) != null ? _labelSize$height : 0);\n          setTextWidth((_labelSize$width = _labelSize.width) != null ? _labelSize$width : 0);\n        }\n      }\n    };\n    updateTextSize();\n    window.addEventListener('resize', updateTextSize);\n    return function () {\n      if (!isWeb) {\n        return;\n      }\n      window.removeEventListener('resize', updateTextSize);\n    };\n  }, [isWeb]);\n  React.useEffect(function () {\n    if (visible) {\n      Animated.timing(visibility, {\n        toValue: 1,\n        duration: 200 * scale,\n        useNativeDriver: true\n      }).start();\n    } else {\n      Animated.timing(visibility, {\n        toValue: 0,\n        duration: 150 * scale,\n        useNativeDriver: true\n      }).start();\n    }\n  }, [visible, scale, visibility]);\n  var _ref4 = StyleSheet.flatten(style) || {},\n    customBackgroundColor = _ref4.backgroundColor,\n    restStyle = _objectWithoutProperties(_ref4, _excluded2);\n  var _getFABColors = getFABColors({\n      theme: theme,\n      variant: variant,\n      disabled: disabled,\n      customColor: customColor,\n      customBackgroundColor: customBackgroundColor\n    }),\n    backgroundColor = _getFABColors.backgroundColor,\n    foregroundColor = _getFABColors.foregroundColor;\n  var rippleColor = customRippleColor || color(foregroundColor).alpha(0.12).rgb().string();\n  var extendedWidth = textWidth + SIZE + borderRadius;\n  var distance = isAnimatedFromRight ? -textWidth - borderRadius : textWidth + borderRadius;\n  React.useEffect(function () {\n    Animated.timing(animFAB, {\n      toValue: !extended ? 0 : distance,\n      duration: 150 * scale,\n      useNativeDriver: true,\n      easing: Easing.linear\n    }).start();\n  }, [animFAB, scale, distance, extended]);\n  var onTextLayout = function onTextLayout(_ref5) {\n    var _ref6, _ref7;\n    var nativeEvent = _ref5.nativeEvent;\n    var _nativeEvent$lines$, _nativeEvent$lines$2;\n    var currentWidth = Math.ceil((_ref6 = (_nativeEvent$lines$ = nativeEvent.lines[0]) === null || _nativeEvent$lines$ === void 0 ? void 0 : _nativeEvent$lines$.width) != null ? _ref6 : 0);\n    var currentHeight = Math.ceil((_ref7 = (_nativeEvent$lines$2 = nativeEvent.lines[0]) === null || _nativeEvent$lines$2 === void 0 ? void 0 : _nativeEvent$lines$2.height) != null ? _ref7 : 0);\n    if (currentWidth !== textWidth || currentHeight !== textHeight) {\n      setTextHeight(currentHeight);\n      if (isIOS) {\n        return setTextWidth(currentWidth - 12);\n      }\n      setTextWidth(currentWidth);\n    }\n  };\n  var propForDirection = function propForDirection(right) {\n    if (isAnimatedFromRight) {\n      return right;\n    }\n    return right.reverse();\n  };\n  var combinedStyles = getCombinedStyles({\n    isAnimatedFromRight: isAnimatedFromRight,\n    isIconStatic: isIconStatic,\n    distance: distance,\n    animFAB: animFAB\n  });\n  var font = isV3 ? theme.fonts.labelLarge : theme.fonts.medium;\n  var textStyle = _objectSpread({\n    color: foregroundColor\n  }, font);\n  var md2Elevation = disabled || !isIOS ? 0 : 6;\n  var md3Elevation = disabled || !isIOS ? 0 : 3;\n  var shadowStyle = isV3 ? styles.v3Shadow : styles.shadow;\n  var baseStyle = [StyleSheet.absoluteFill, disabled ? styles.disabled : shadowStyle];\n  var newAccessibilityState = _objectSpread(_objectSpread({}, accessibilityState), {}, {\n    disabled: disabled\n  });\n  return React.createElement(Surface, _extends({}, rest, {\n    testID: `${testID}-container`,\n    style: [{\n      opacity: visibility,\n      transform: [{\n        scale: visibility\n      }],\n      borderRadius: borderRadius\n    }, !isV3 && {\n      elevation: md2Elevation\n    }, styles.container, restStyle]\n  }, isV3 && {\n    elevation: md3Elevation\n  }, {\n    theme: theme,\n    container: true\n  }), React.createElement(Animated.View, {\n    style: [!isV3 && {\n      transform: [{\n        scaleY: animFAB.interpolate({\n          inputRange: propForDirection([distance, 0]),\n          outputRange: propForDirection([SCALE, 1])\n        })\n      }]\n    }, styles.standard, {\n      borderRadius: borderRadius\n    }]\n  }, React.createElement(View, {\n    style: [StyleSheet.absoluteFill, styles.shadowWrapper]\n  }, React.createElement(Animated.View, {\n    pointerEvents: \"none\",\n    style: [baseStyle, {\n      width: extendedWidth,\n      opacity: animFAB.interpolate({\n        inputRange: propForDirection([distance, 0.9 * distance, 0]),\n        outputRange: propForDirection([1, 0.15, 0])\n      }),\n      borderRadius: borderRadius\n    }],\n    testID: `${testID}-extended-shadow`\n  }), React.createElement(Animated.View, {\n    pointerEvents: \"none\",\n    style: [baseStyle, {\n      opacity: animFAB.interpolate({\n        inputRange: propForDirection([distance, 0.9 * distance, 0]),\n        outputRange: propForDirection([0, 0.85, 1])\n      }),\n      width: SIZE,\n      borderRadius: animFAB.interpolate({\n        inputRange: propForDirection([distance, 0]),\n        outputRange: propForDirection([SIZE / (extendedWidth / SIZE), borderRadius])\n      })\n    }, combinedStyles.absoluteFill],\n    testID: `${testID}-shadow`\n  })), React.createElement(Animated.View, {\n    pointerEvents: \"box-none\",\n    style: [styles.innerWrapper, {\n      borderRadius: borderRadius\n    }]\n  }, React.createElement(Animated.View, {\n    style: [styles.standard, {\n      width: extendedWidth,\n      backgroundColor: backgroundColor,\n      borderRadius: borderRadius\n    }, combinedStyles.innerWrapper]\n  }, React.createElement(TouchableRipple, {\n    borderless: true,\n    background: background,\n    onPress: onPress,\n    onLongPress: onLongPress,\n    delayLongPress: delayLongPress,\n    rippleColor: rippleColor,\n    disabled: disabled,\n    accessibilityLabel: accessibilityLabel,\n    accessibilityRole: \"button\",\n    accessibilityState: newAccessibilityState,\n    testID: testID,\n    style: {\n      borderRadius: borderRadius\n    },\n    theme: theme,\n    hitSlop: hitSlop\n  }, React.createElement(View, {\n    style: [styles.standard, {\n      width: extendedWidth,\n      borderRadius: borderRadius\n    }]\n  }))))), React.createElement(Animated.View, {\n    style: [styles.iconWrapper, combinedStyles.iconWrapper],\n    pointerEvents: \"none\"\n  }, React.createElement(Icon, {\n    source: icon,\n    size: 24,\n    color: foregroundColor,\n    theme: theme\n  })), React.createElement(View, {\n    pointerEvents: \"none\"\n  }, React.createElement(AnimatedText, {\n    ref: isWeb ? labelRef : null,\n    variant: \"labelLarge\",\n    numberOfLines: 1,\n    onTextLayout: isIOS ? onTextLayout : undefined,\n    ellipsizeMode: 'tail',\n    style: [_defineProperty({}, isAnimatedFromRight || isRTL ? 'right' : 'left', isIconStatic ? textWidth - SIZE + borderRadius / (isV3 ? 1 : 2) : borderRadius), {\n      minWidth: textWidth,\n      top: -SIZE / 2 - textHeight / 2,\n      opacity: animFAB.interpolate({\n        inputRange: propForDirection([distance, 0.7 * distance, 0]),\n        outputRange: propForDirection([1, 0, 0])\n      }),\n      transform: [{\n        translateX: animFAB.interpolate({\n          inputRange: propForDirection([distance, 0]),\n          outputRange: propForDirection([0, SIZE])\n        })\n      }]\n    }, styles.label, uppercase && styles.uppercaseLabel, textStyle],\n    theme: theme,\n    testID: `${testID}-text`,\n    maxFontSizeMultiplier: labelMaxFontSizeMultiplier\n  }, label)), !isIOS && React.createElement(ScrollView, {\n    style: styles.textPlaceholderContainer\n  }, React.createElement(AnimatedText, {\n    variant: \"labelLarge\",\n    numberOfLines: 1,\n    onTextLayout: onTextLayout,\n    ellipsizeMode: 'tail',\n    style: [styles.label, uppercase && styles.uppercaseLabel, textStyle],\n    theme: theme\n  }, label)));\n};\nvar styles = StyleSheet.create({\n  standard: {\n    height: SIZE\n  },\n  disabled: {\n    elevation: 0\n  },\n  container: {\n    position: 'absolute',\n    backgroundColor: 'transparent'\n  },\n  innerWrapper: {\n    flexDirection: 'row',\n    overflow: 'hidden'\n  },\n  shadowWrapper: {\n    elevation: 0\n  },\n  shadow: {\n    elevation: 6\n  },\n  v3Shadow: {\n    elevation: 3\n  },\n  iconWrapper: {\n    alignItems: 'center',\n    justifyContent: 'center',\n    position: 'absolute',\n    height: SIZE,\n    width: SIZE\n  },\n  label: {\n    position: 'absolute'\n  },\n  uppercaseLabel: {\n    textTransform: 'uppercase'\n  },\n  textPlaceholderContainer: {\n    height: 0,\n    position: 'absolute'\n  }\n});\nexport default AnimatedFAB;", "map": {"version": 3, "names": ["React", "Animated", "Easing", "I18nManager", "Platform", "ScrollView", "StyleSheet", "View", "color", "getCombinedStyles", "getFABColors", "getLabelSizeWeb", "useInternalTheme", "Icon", "Surface", "TouchableRipple", "AnimatedText", "SIZE", "SCALE", "AnimatedFAB", "_ref", "_ref2", "_ref3", "icon", "label", "background", "_ref$accessibilityLab", "accessibilityLabel", "accessibilityState", "customColor", "customRippleColor", "rippleColor", "disabled", "onPress", "onLongPress", "delayLongPress", "themeOverrides", "theme", "style", "_ref$visible", "visible", "uppercaseProp", "uppercase", "_ref$testID", "testID", "_ref$animateFrom", "animateFrom", "_ref$extended", "extended", "_ref$iconMode", "iconMode", "_ref$variant", "variant", "labelMaxFontSizeMultiplier", "hitSlop", "rest", "_objectWithoutProperties", "_excluded", "isV3", "isIOS", "OS", "isWeb", "isAnimatedFromRight", "isIconStatic", "isRTL", "labelRef", "useRef", "_React$useRef", "Value", "visibility", "current", "_React$useRef2", "animFAB", "animation", "scale", "labelSize", "_React$useState", "useState", "width", "_React$useState2", "_slicedToArray", "textWidth", "setTextWidth", "_React$useState3", "height", "_React$useState4", "textHeight", "setTextHeight", "borderRadius", "useEffect", "updateTextSize", "_labelSize$height", "_labelSize$width", "window", "addEventListener", "removeEventListener", "timing", "toValue", "duration", "useNativeDriver", "start", "_ref4", "flatten", "customBackgroundColor", "backgroundColor", "restStyle", "_excluded2", "_getFABColors", "foregroundColor", "alpha", "rgb", "string", "extendedWidth", "distance", "easing", "linear", "onTextLayout", "_ref5", "_ref6", "_ref7", "nativeEvent", "_nativeEvent$lines$", "_nativeEvent$lines$2", "currentWidth", "Math", "ceil", "lines", "currentHeight", "propForDirection", "right", "reverse", "combinedStyles", "font", "fonts", "labelLarge", "medium", "textStyle", "_objectSpread", "md2Elevation", "md3Elevation", "shadowStyle", "styles", "v3Shadow", "shadow", "baseStyle", "absoluteFill", "newAccessibilityState", "createElement", "_extends", "opacity", "transform", "elevation", "container", "scaleY", "interpolate", "inputRange", "outputRange", "standard", "shadowWrapper", "pointerEvents", "innerWrapper", "borderless", "accessibilityRole", "iconWrapper", "source", "size", "ref", "numberOfLines", "undefined", "ellipsizeMode", "_defineProperty", "min<PERSON><PERSON><PERSON>", "top", "translateX", "uppercase<PERSON>abel", "maxFontSizeMultiplier", "textPlaceholderContainer", "create", "position", "flexDirection", "overflow", "alignItems", "justifyContent", "textTransform"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\FAB\\AnimatedFAB.tsx"], "sourcesContent": ["import * as React from 'react';\nimport type {\n  AccessibilityState,\n  ColorValue,\n  NativeSyntheticEvent,\n  PressableAndroidRippleConfig,\n  TextLayoutEventData,\n} from 'react-native';\nimport {\n  Animated,\n  Easing,\n  GestureResponderEvent,\n  I18nManager,\n  Platform,\n  ScrollView,\n  StyleProp,\n  StyleSheet,\n  View,\n  ViewStyle,\n  Text,\n} from 'react-native';\n\nimport color from 'color';\n\nimport { getCombinedStyles, getFABColors, getLabelSizeWeb } from './utils';\nimport { useInternalTheme } from '../../core/theming';\nimport type { $Omit, $RemoveChildren, ThemeProp } from '../../types';\nimport type { IconSource } from '../Icon';\nimport Icon from '../Icon';\nimport Surface from '../Surface';\nimport TouchableRipple, {\n  Props as TouchableRippleProps,\n} from '../TouchableRipple/TouchableRipple';\nimport AnimatedText from '../Typography/AnimatedText';\n\nexport type AnimatedFABIconMode = 'static' | 'dynamic';\nexport type AnimatedFABAnimateFrom = 'left' | 'right';\n\nexport type Props = $Omit<$RemoveChildren<typeof Surface>, 'mode'> & {\n  /**\n   * Icon to display for the `FAB`.\n   */\n  icon: IconSource;\n  /**\n   * Label for extended `FAB`.\n   */\n  label: string;\n  /**\n   * Make the label text uppercased.\n   */\n  uppercase?: boolean;\n  /**\n   * Type of background drawabale to display the feedback (Android).\n   * https://reactnative.dev/docs/pressable#rippleconfig\n   */\n  background?: PressableAndroidRippleConfig;\n  /**\n   * Accessibility label for the FAB. This is read by the screen reader when the user taps the FAB.\n   * Uses `label` by default if specified.\n   */\n  accessibilityLabel?: string;\n  /**\n   * Accessibility state for the FAB. This is read by the screen reader when the user taps the FAB.\n   */\n  accessibilityState?: AccessibilityState;\n  /**\n   * Custom color for the icon and label of the `FAB`.\n   */\n  color?: string;\n  /**\n   * Color of the ripple effect.\n   */\n  rippleColor?: ColorValue;\n  /**\n   * Whether `FAB` is disabled. A disabled button is greyed out and `onPress` is not called on touch.\n   */\n  disabled?: boolean;\n  /**\n   * Whether `FAB` is currently visible.\n   */\n  visible?: boolean;\n  /**\n   * Function to execute on press.\n   */\n  onPress?: (e: GestureResponderEvent) => void;\n  /**\n   * Function to execute on long press.\n   */\n  onLongPress?: (e: GestureResponderEvent) => void;\n  /**\n   * The number of milliseconds a user must touch the element before executing `onLongPress`.\n   */\n  delayLongPress?: number;\n  /**\n   * Whether icon should be translated to the end of extended `FAB` or be static and stay in the same place. The default value is `dynamic`.\n   */\n  iconMode?: AnimatedFABIconMode;\n  /**\n   * Indicates from which direction animation should be performed. The default value is `right`.\n   */\n  animateFrom?: AnimatedFABAnimateFrom;\n  /**\n   * Whether `FAB` should start animation to extend.\n   */\n  extended: boolean;\n  /**\n   * Specifies the largest possible scale a label font can reach.\n   */\n  labelMaxFontSizeMultiplier?: number;\n  /**\n   * @supported Available in v5.x with theme version 3\n   *\n   * Color mappings variant for combinations of container and icon colors.\n   */\n  variant?: 'primary' | 'secondary' | 'tertiary' | 'surface';\n  style?: Animated.WithAnimatedValue<StyleProp<ViewStyle>>;\n  /**\n   * Sets additional distance outside of element in which a press can be detected.\n   */\n  hitSlop?: TouchableRippleProps['hitSlop'];\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n  /**\n   * TestID used for testing purposes\n   */\n  testID?: string;\n};\n\nconst SIZE = 56;\nconst SCALE = 0.9;\n\n/**\n * An animated, extending horizontally floating action button represents the primary action in an application.\n *\n * ## Usage\n * ```js\n * import React from 'react';\n * import {\n *   StyleProp,\n *   ViewStyle,\n *   Animated,\n *   StyleSheet,\n *   Platform,\n *   ScrollView,\n *   Text,\n *   SafeAreaView,\n *   I18nManager,\n * } from 'react-native';\n * import { AnimatedFAB } from 'react-native-paper';\n *\n * const MyComponent = ({\n *   animatedValue,\n *   visible,\n *   extended,\n *   label,\n *   animateFrom,\n *   style,\n *   iconMode,\n * }) => {\n *   const [isExtended, setIsExtended] = React.useState(true);\n *\n *   const isIOS = Platform.OS === 'ios';\n *\n *   const onScroll = ({ nativeEvent }) => {\n *     const currentScrollPosition =\n *       Math.floor(nativeEvent?.contentOffset?.y) ?? 0;\n *\n *     setIsExtended(currentScrollPosition <= 0);\n *   };\n *\n *   const fabStyle = { [animateFrom]: 16 };\n *\n *   return (\n *     <SafeAreaView style={styles.container}>\n *       <ScrollView onScroll={onScroll}>\n *         {[...new Array(100).keys()].map((_, i) => (\n *           <Text>{i}</Text>\n *         ))}\n *       </ScrollView>\n *       <AnimatedFAB\n *         icon={'plus'}\n *         label={'Label'}\n *         extended={isExtended}\n *         onPress={() => console.log('Pressed')}\n *         visible={visible}\n *         animateFrom={'right'}\n *         iconMode={'static'}\n *         style={[styles.fabStyle, style, fabStyle]}\n *       />\n *     </SafeAreaView>\n *   );\n * };\n *\n * export default MyComponent;\n *\n * const styles = StyleSheet.create({\n *   container: {\n *     flexGrow: 1,\n *   },\n *   fabStyle: {\n *     bottom: 16,\n *     right: 16,\n *     position: 'absolute',\n *   },\n * });\n * ```\n */\nconst AnimatedFAB = ({\n  icon,\n  label,\n  background,\n  accessibilityLabel = label,\n  accessibilityState,\n  color: customColor,\n  rippleColor: customRippleColor,\n  disabled,\n  onPress,\n  onLongPress,\n  delayLongPress,\n  theme: themeOverrides,\n  style,\n  visible = true,\n  uppercase: uppercaseProp,\n  testID = 'animated-fab',\n  animateFrom = 'right',\n  extended = false,\n  iconMode = 'dynamic',\n  variant = 'primary',\n  labelMaxFontSizeMultiplier,\n  hitSlop,\n  ...rest\n}: Props) => {\n  const theme = useInternalTheme(themeOverrides);\n  const uppercase: boolean = uppercaseProp ?? !theme.isV3;\n  const isIOS = Platform.OS === 'ios';\n  const isWeb = Platform.OS === 'web';\n  const isAnimatedFromRight = animateFrom === 'right';\n  const isIconStatic = iconMode === 'static';\n  const { isRTL } = I18nManager;\n  const labelRef = React.useRef<Text & HTMLElement>(null);\n  const { current: visibility } = React.useRef<Animated.Value>(\n    new Animated.Value(visible ? 1 : 0)\n  );\n  const { current: animFAB } = React.useRef<Animated.Value>(\n    new Animated.Value(0)\n  );\n  const { isV3, animation } = theme;\n  const { scale } = animation;\n\n  const labelSize = isWeb ? getLabelSizeWeb(labelRef) : null;\n  const [textWidth, setTextWidth] = React.useState<number>(\n    labelSize?.width ?? 0\n  );\n  const [textHeight, setTextHeight] = React.useState<number>(\n    labelSize?.height ?? 0\n  );\n\n  const borderRadius = SIZE / (isV3 ? 3.5 : 2);\n\n  React.useEffect(() => {\n    if (!isWeb) {\n      return;\n    }\n\n    const updateTextSize = () => {\n      if (labelRef.current) {\n        const labelSize = getLabelSizeWeb(labelRef);\n\n        if (labelSize) {\n          setTextHeight(labelSize.height ?? 0);\n          setTextWidth(labelSize.width ?? 0);\n        }\n      }\n    };\n\n    updateTextSize();\n    window.addEventListener('resize', updateTextSize);\n\n    return () => {\n      if (!isWeb) {\n        return;\n      }\n\n      window.removeEventListener('resize', updateTextSize);\n    };\n  }, [isWeb]);\n\n  React.useEffect(() => {\n    if (visible) {\n      Animated.timing(visibility, {\n        toValue: 1,\n        duration: 200 * scale,\n        useNativeDriver: true,\n      }).start();\n    } else {\n      Animated.timing(visibility, {\n        toValue: 0,\n        duration: 150 * scale,\n        useNativeDriver: true,\n      }).start();\n    }\n  }, [visible, scale, visibility]);\n\n  const { backgroundColor: customBackgroundColor, ...restStyle } =\n    (StyleSheet.flatten(style) || {}) as ViewStyle;\n\n  const { backgroundColor, foregroundColor } = getFABColors({\n    theme,\n    variant,\n    disabled,\n    customColor,\n    customBackgroundColor,\n  });\n\n  const rippleColor =\n    customRippleColor || color(foregroundColor).alpha(0.12).rgb().string();\n\n  const extendedWidth = textWidth + SIZE + borderRadius;\n\n  const distance = isAnimatedFromRight\n    ? -textWidth - borderRadius\n    : textWidth + borderRadius;\n\n  React.useEffect(() => {\n    Animated.timing(animFAB, {\n      toValue: !extended ? 0 : distance,\n      duration: 150 * scale,\n      useNativeDriver: true,\n      easing: Easing.linear,\n    }).start();\n  }, [animFAB, scale, distance, extended]);\n\n  const onTextLayout = ({\n    nativeEvent,\n  }: NativeSyntheticEvent<TextLayoutEventData>) => {\n    const currentWidth = Math.ceil(nativeEvent.lines[0]?.width ?? 0);\n    const currentHeight = Math.ceil(nativeEvent.lines[0]?.height ?? 0);\n\n    if (currentWidth !== textWidth || currentHeight !== textHeight) {\n      setTextHeight(currentHeight);\n\n      if (isIOS) {\n        return setTextWidth(currentWidth - 12);\n      }\n\n      setTextWidth(currentWidth);\n    }\n  };\n\n  const propForDirection = <T,>(right: T[]): T[] => {\n    if (isAnimatedFromRight) {\n      return right;\n    }\n\n    return right.reverse();\n  };\n\n  const combinedStyles = getCombinedStyles({\n    isAnimatedFromRight,\n    isIconStatic,\n    distance,\n    animFAB,\n  });\n\n  const font = isV3 ? theme.fonts.labelLarge : theme.fonts.medium;\n\n  const textStyle = {\n    color: foregroundColor,\n    ...font,\n  };\n\n  const md2Elevation = disabled || !isIOS ? 0 : 6;\n  const md3Elevation = disabled || !isIOS ? 0 : 3;\n\n  const shadowStyle = isV3 ? styles.v3Shadow : styles.shadow;\n  const baseStyle = [\n    StyleSheet.absoluteFill,\n    disabled ? styles.disabled : shadowStyle,\n  ];\n\n  const newAccessibilityState = { ...accessibilityState, disabled };\n\n  return (\n    <Surface\n      {...rest}\n      testID={`${testID}-container`}\n      style={[\n        {\n          opacity: visibility,\n          transform: [\n            {\n              scale: visibility,\n            },\n          ],\n          borderRadius,\n        },\n        !isV3 && {\n          elevation: md2Elevation,\n        },\n        styles.container,\n        restStyle,\n      ]}\n      {...(isV3 && { elevation: md3Elevation })}\n      theme={theme}\n      container\n    >\n      <Animated.View\n        style={[\n          !isV3 && {\n            transform: [\n              {\n                scaleY: animFAB.interpolate({\n                  inputRange: propForDirection([distance, 0]),\n                  outputRange: propForDirection([SCALE, 1]),\n                }),\n              },\n            ],\n          },\n          styles.standard,\n          { borderRadius },\n        ]}\n      >\n        <View style={[StyleSheet.absoluteFill, styles.shadowWrapper]}>\n          <Animated.View\n            pointerEvents=\"none\"\n            style={[\n              baseStyle,\n              {\n                width: extendedWidth,\n                opacity: animFAB.interpolate({\n                  inputRange: propForDirection([distance, 0.9 * distance, 0]),\n                  outputRange: propForDirection([1, 0.15, 0]),\n                }),\n                borderRadius,\n              },\n            ]}\n            testID={`${testID}-extended-shadow`}\n          />\n          <Animated.View\n            pointerEvents=\"none\"\n            style={[\n              baseStyle,\n              {\n                opacity: animFAB.interpolate({\n                  inputRange: propForDirection([distance, 0.9 * distance, 0]),\n                  outputRange: propForDirection([0, 0.85, 1]),\n                }),\n                width: SIZE,\n                borderRadius: animFAB.interpolate({\n                  inputRange: propForDirection([distance, 0]),\n                  outputRange: propForDirection([\n                    SIZE / (extendedWidth / SIZE),\n                    borderRadius,\n                  ]),\n                }),\n              },\n              combinedStyles.absoluteFill,\n            ]}\n            testID={`${testID}-shadow`}\n          />\n        </View>\n        <Animated.View\n          pointerEvents=\"box-none\"\n          style={[styles.innerWrapper, { borderRadius }]}\n        >\n          <Animated.View\n            style={[\n              styles.standard,\n              {\n                width: extendedWidth,\n                backgroundColor,\n                borderRadius,\n              },\n              combinedStyles.innerWrapper,\n            ]}\n          >\n            <TouchableRipple\n              borderless\n              background={background}\n              onPress={onPress}\n              onLongPress={onLongPress}\n              delayLongPress={delayLongPress}\n              rippleColor={rippleColor}\n              disabled={disabled}\n              accessibilityLabel={accessibilityLabel}\n              accessibilityRole=\"button\"\n              accessibilityState={newAccessibilityState}\n              testID={testID}\n              style={{ borderRadius }}\n              theme={theme}\n              hitSlop={hitSlop}\n            >\n              <View\n                style={[\n                  styles.standard,\n                  {\n                    width: extendedWidth,\n                    borderRadius,\n                  },\n                ]}\n              />\n            </TouchableRipple>\n          </Animated.View>\n        </Animated.View>\n      </Animated.View>\n\n      <Animated.View\n        style={[styles.iconWrapper, combinedStyles.iconWrapper]}\n        pointerEvents=\"none\"\n      >\n        <Icon source={icon} size={24} color={foregroundColor} theme={theme} />\n      </Animated.View>\n\n      <View pointerEvents=\"none\">\n        <AnimatedText\n          ref={isWeb ? labelRef : null}\n          variant=\"labelLarge\"\n          numberOfLines={1}\n          onTextLayout={isIOS ? onTextLayout : undefined}\n          ellipsizeMode={'tail'}\n          style={[\n            {\n              [isAnimatedFromRight || isRTL ? 'right' : 'left']: isIconStatic\n                ? textWidth - SIZE + borderRadius / (isV3 ? 1 : 2)\n                : borderRadius,\n            },\n            {\n              minWidth: textWidth,\n              top: -SIZE / 2 - textHeight / 2,\n              opacity: animFAB.interpolate({\n                inputRange: propForDirection([distance, 0.7 * distance, 0]),\n                outputRange: propForDirection([1, 0, 0]),\n              }) as unknown as number,\n              // TODO: check\n              transform: [\n                {\n                  translateX: animFAB.interpolate({\n                    inputRange: propForDirection([distance, 0]),\n                    outputRange: propForDirection([0, SIZE]),\n                  }),\n                },\n              ],\n            },\n            styles.label,\n            uppercase && styles.uppercaseLabel,\n            textStyle,\n          ]}\n          theme={theme}\n          testID={`${testID}-text`}\n          maxFontSizeMultiplier={labelMaxFontSizeMultiplier}\n        >\n          {label}\n        </AnimatedText>\n      </View>\n\n      {!isIOS && (\n        // Method `onTextLayout` on Android returns sizes of text visible on the screen,\n        // however during render the text in `FAB` isn't fully visible. In order to get\n        // proper text measurements there is a need to additionaly render that text, but\n        // wrapped in absolutely positioned `ScrollView` which height is 0.\n        <ScrollView style={styles.textPlaceholderContainer}>\n          <AnimatedText\n            variant=\"labelLarge\"\n            numberOfLines={1}\n            onTextLayout={onTextLayout}\n            ellipsizeMode={'tail'}\n            style={[\n              styles.label,\n              uppercase && styles.uppercaseLabel,\n              textStyle,\n            ]}\n            theme={theme}\n          >\n            {label}\n          </AnimatedText>\n        </ScrollView>\n      )}\n    </Surface>\n  );\n};\n\nconst styles = StyleSheet.create({\n  standard: {\n    height: SIZE,\n  },\n  disabled: {\n    elevation: 0,\n  },\n  // eslint-disable-next-line react-native/no-color-literals\n  container: {\n    position: 'absolute',\n    backgroundColor: 'transparent',\n  },\n  innerWrapper: {\n    flexDirection: 'row',\n    overflow: 'hidden',\n  },\n  shadowWrapper: {\n    elevation: 0,\n  },\n  shadow: {\n    elevation: 6,\n  },\n  v3Shadow: {\n    elevation: 3,\n  },\n  iconWrapper: {\n    alignItems: 'center',\n    justifyContent: 'center',\n    position: 'absolute',\n    height: SIZE,\n    width: SIZE,\n  },\n  label: {\n    position: 'absolute',\n  },\n  uppercaseLabel: {\n    textTransform: 'uppercase',\n  },\n  textPlaceholderContainer: {\n    height: 0,\n    position: 'absolute',\n  },\n});\n\nexport default AnimatedFAB;\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAAA,OAAAC,MAAA;AAAA,OAAAC,WAAA;AAAA,OAAAC,QAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAsB9B,OAAOC,KAAK,MAAM,OAAO;AAEzB,SAASC,iBAAiB,EAAEC,YAAY,EAAEC,eAAe;AACzD,SAASC,gBAAgB;AAGzB,OAAOC,IAAI;AACX,OAAOC,OAAO;AACd,OAAOC,eAAe;AAGtB,OAAOC,YAAY;AAiGnB,IAAMC,IAAI,GAAG,EAAE;AACf,IAAMC,KAAK,GAAG,GAAG;AA8EjB,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAAC,IAAA,EAwBJ;EAAA,IAAAC,KAAA,EAAAC,KAAA;EAAA,IAvBXC,IAAI,GAAAH,IAAA,CAAJG,IAAI;IACJC,KAAK,GAAAJ,IAAA,CAALI,KAAK;IACLC,UAAU,GAAAL,IAAA,CAAVK,UAAU;IAAAC,qBAAA,GAAAN,IAAA,CACVO,kBAAkB;IAAlBA,kBAAkB,GAAAD,qBAAA,cAAGF,KAAK,GAAAE,qBAAA;IAC1BE,kBAAkB,GAAAR,IAAA,CAAlBQ,kBAAkB;IACXC,WAAW,GAAAT,IAAA,CAAlBZ,KAAK;IACQsB,iBAAiB,GAAAV,IAAA,CAA9BW,WAAW;IACXC,QAAQ,GAAAZ,IAAA,CAARY,QAAQ;IACRC,OAAO,GAAAb,IAAA,CAAPa,OAAO;IACPC,WAAW,GAAAd,IAAA,CAAXc,WAAW;IACXC,cAAc,GAAAf,IAAA,CAAde,cAAc;IACPC,cAAc,GAAAhB,IAAA,CAArBiB,KAAK;IACLC,KAAK,GAAAlB,IAAA,CAALkB,KAAK;IAAAC,YAAA,GAAAnB,IAAA,CACLoB,OAAO;IAAPA,OAAO,GAAAD,YAAA,cAAG,IAAI,GAAAA,YAAA;IACHE,aAAa,GAAArB,IAAA,CAAxBsB,SAAS;IAAAC,WAAA,GAAAvB,IAAA,CACTwB,MAAM;IAANA,MAAM,GAAAD,WAAA,cAAG,cAAc,GAAAA,WAAA;IAAAE,gBAAA,GAAAzB,IAAA,CACvB0B,WAAW;IAAXA,WAAW,GAAAD,gBAAA,cAAG,OAAO,GAAAA,gBAAA;IAAAE,aAAA,GAAA3B,IAAA,CACrB4B,QAAQ;IAARA,QAAQ,GAAAD,aAAA,cAAG,KAAK,GAAAA,aAAA;IAAAE,aAAA,GAAA7B,IAAA,CAChB8B,QAAQ;IAARA,QAAQ,GAAAD,aAAA,cAAG,SAAS,GAAAA,aAAA;IAAAE,YAAA,GAAA/B,IAAA,CACpBgC,OAAO;IAAPA,OAAO,GAAAD,YAAA,cAAG,SAAS,GAAAA,YAAA;IACnBE,0BAA0B,GAAAjC,IAAA,CAA1BiC,0BAA0B;IAC1BC,OAAO,GAAAlC,IAAA,CAAPkC,OAAO;IACJC,IAAA,GAAAC,wBAAA,CAAApC,IAAA,EAAAqC,SAAA;EAEH,IAAMpB,KAAK,GAAGzB,gBAAgB,CAACwB,cAAc,CAAC;EAC9C,IAAMM,SAAkB,GAAGD,aAAa,WAAbA,aAAa,GAAI,CAACJ,KAAK,CAACqB,IAAI;EACvD,IAAMC,KAAK,GAAGvD,QAAQ,CAACwD,EAAE,KAAK,KAAK;EACnC,IAAMC,KAAK,GAAGzD,QAAQ,CAACwD,EAAE,KAAK,KAAK;EACnC,IAAME,mBAAmB,GAAGhB,WAAW,KAAK,OAAO;EACnD,IAAMiB,YAAY,GAAGb,QAAQ,KAAK,QAAQ;EAC1C,IAAQc,KAAA,GAAU7D,WAAW,CAArB6D,KAAA;EACR,IAAMC,QAAQ,GAAGjE,KAAK,CAACkE,MAAM,CAAqB,IAAI,CAAC;EACvD,IAAAC,aAAA,GAAgCnE,KAAK,CAACkE,MAAM,CAC1C,IAAIjE,QAAQ,CAACmE,KAAK,CAAC5B,OAAO,GAAG,CAAC,GAAG,CAAC,CACpC,CAAC;IAFgB6B,UAAA,GAAAF,aAAA,CAATG,OAAO;EAGf,IAAAC,cAAA,GAA6BvE,KAAK,CAACkE,MAAM,CACvC,IAAIjE,QAAQ,CAACmE,KAAK,CAAC,CAAC,CACtB,CAAC;IAFgBI,OAAA,GAAAD,cAAA,CAATD,OAAO;EAGf,IAAQZ,IAAI,GAAgBrB,KAAK,CAAzBqB,IAAI;IAAEe,SAAA,GAAcpC,KAAK,CAAnBoC,SAAA;EACd,IAAQC,KAAA,GAAUD,SAAS,CAAnBC,KAAA;EAER,IAAMC,SAAS,GAAGd,KAAK,GAAGlD,eAAe,CAACsD,QAAQ,CAAC,GAAG,IAAI;EAC1D,IAAAW,eAAA,GAAkC5E,KAAK,CAAC6E,QAAQ,EAAAxD,KAAA,GAC9CsD,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEG,KAAK,YAAAzD,KAAA,GAAI,CACtB,CAAC;IAAA0D,gBAAA,GAAAC,cAAA,CAAAJ,eAAA;IAFMK,SAAS,GAAAF,gBAAA;IAAEG,YAAY,GAAAH,gBAAA;EAG9B,IAAAI,gBAAA,GAAoCnF,KAAK,CAAC6E,QAAQ,EAAAvD,KAAA,GAChDqD,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAES,MAAM,YAAA9D,KAAA,GAAI,CACvB,CAAC;IAAA+D,gBAAA,GAAAL,cAAA,CAAAG,gBAAA;IAFMG,UAAU,GAAAD,gBAAA;IAAEE,aAAa,GAAAF,gBAAA;EAIhC,IAAMG,YAAY,GAAGvE,IAAI,IAAIyC,IAAI,GAAG,GAAG,GAAG,CAAC,CAAC;EAE5C1D,KAAK,CAACyF,SAAS,CAAC,YAAM;IACpB,IAAI,CAAC5B,KAAK,EAAE;MACV;IACF;IAEA,IAAM6B,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;MAC3B,IAAIzB,QAAQ,CAACK,OAAO,EAAE;QACpB,IAAMK,UAAS,GAAGhE,eAAe,CAACsD,QAAQ,CAAC;QAE3C,IAAIU,UAAS,EAAE;UAAA,IAAAgB,iBAAA,EAAAC,gBAAA;UACbL,aAAa,EAAAI,iBAAA,GAAChB,UAAS,CAACS,MAAM,YAAAO,iBAAA,GAAI,CAAC,CAAC;UACpCT,YAAY,EAAAU,gBAAA,GAACjB,UAAS,CAACG,KAAK,YAAAc,gBAAA,GAAI,CAAC,CAAC;QACpC;MACF;IACF,CAAC;IAEDF,cAAc,CAAC,CAAC;IAChBG,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEJ,cAAc,CAAC;IAEjD,OAAO,YAAM;MACX,IAAI,CAAC7B,KAAK,EAAE;QACV;MACF;MAEAgC,MAAM,CAACE,mBAAmB,CAAC,QAAQ,EAAEL,cAAc,CAAC;IACtD,CAAC;EACH,CAAC,EAAE,CAAC7B,KAAK,CAAC,CAAC;EAEX7D,KAAK,CAACyF,SAAS,CAAC,YAAM;IACpB,IAAIjD,OAAO,EAAE;MACXvC,QAAQ,CAAC+F,MAAM,CAAC3B,UAAU,EAAE;QAC1B4B,OAAO,EAAE,CAAC;QACVC,QAAQ,EAAE,GAAG,GAAGxB,KAAK;QACrByB,eAAe,EAAE;MACnB,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;IACZ,CAAC,MAAM;MACLnG,QAAQ,CAAC+F,MAAM,CAAC3B,UAAU,EAAE;QAC1B4B,OAAO,EAAE,CAAC;QACVC,QAAQ,EAAE,GAAG,GAAGxB,KAAK;QACrByB,eAAe,EAAE;MACnB,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;IACZ;EACF,CAAC,EAAE,CAAC5D,OAAO,EAAEkC,KAAK,EAAEL,UAAU,CAAC,CAAC;EAEhC,IAAAgC,KAAA,GACG/F,UAAU,CAACgG,OAAO,CAAChE,KAAK,CAAC,IAAI,CAAC,CAAe;IADvBiE,qBAAqB,GAAAF,KAAA,CAAtCG,eAAe;IAA4BC,SAAA,GAAAjD,wBAAA,CAAA6C,KAAA,EAAAK,UAAA;EAGnD,IAAAC,aAAA,GAA6CjG,YAAY,CAAC;MACxD2B,KAAK,EAALA,KAAK;MACLe,OAAO,EAAPA,OAAO;MACPpB,QAAQ,EAARA,QAAQ;MACRH,WAAW,EAAXA,WAAW;MACX0E,qBAAA,EAAAA;IACF,CAAC,CAAC;IANMC,eAAe,GAAAG,aAAA,CAAfH,eAAe;IAAEI,eAAA,GAAAD,aAAA,CAAAC,eAAA;EAQzB,IAAM7E,WAAW,GACfD,iBAAiB,IAAItB,KAAK,CAACoG,eAAe,CAAC,CAACC,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EAExE,IAAMC,aAAa,GAAG/B,SAAS,GAAGhE,IAAI,GAAGuE,YAAY;EAErD,IAAMyB,QAAQ,GAAGnD,mBAAmB,GAChC,CAACmB,SAAS,GAAGO,YAAY,GACzBP,SAAS,GAAGO,YAAY;EAE5BxF,KAAK,CAACyF,SAAS,CAAC,YAAM;IACpBxF,QAAQ,CAAC+F,MAAM,CAACxB,OAAO,EAAE;MACvByB,OAAO,EAAE,CAACjD,QAAQ,GAAG,CAAC,GAAGiE,QAAQ;MACjCf,QAAQ,EAAE,GAAG,GAAGxB,KAAK;MACrByB,eAAe,EAAE,IAAI;MACrBe,MAAM,EAAEhH,MAAM,CAACiH;IACjB,CAAC,CAAC,CAACf,KAAK,CAAC,CAAC;EACZ,CAAC,EAAE,CAAC5B,OAAO,EAAEE,KAAK,EAAEuC,QAAQ,EAAEjE,QAAQ,CAAC,CAAC;EAExC,IAAMoE,YAAY,GAAG,SAAfA,YAAYA,CAAAC,KAAA,EAE+B;IAAA,IAAAC,KAAA,EAAAC,KAAA;IAAA,IAD/CC,WAAA,GAAAH,KAAA,CAAAG,WAAA;IAC+C,IAAAC,mBAAA,EAAAC,oBAAA;IAC/C,IAAMC,YAAY,GAAGC,IAAI,CAACC,IAAI,EAAAP,KAAA,GAAC,CAAAG,mBAAA,GAAAD,WAAW,CAACM,KAAK,CAAC,CAAC,CAAC,cAAAL,mBAAA,uBAApBA,mBAAA,CAAsB3C,KAAK,YAAAwC,KAAA,GAAI,CAAC,CAAC;IAChE,IAAMS,aAAa,GAAGH,IAAI,CAACC,IAAI,EAAAN,KAAA,GAAC,CAAAG,oBAAA,GAAAF,WAAW,CAACM,KAAK,CAAC,CAAC,CAAC,cAAAJ,oBAAA,uBAApBA,oBAAA,CAAsBtC,MAAM,YAAAmC,KAAA,GAAI,CAAC,CAAC;IAElE,IAAII,YAAY,KAAK1C,SAAS,IAAI8C,aAAa,KAAKzC,UAAU,EAAE;MAC9DC,aAAa,CAACwC,aAAa,CAAC;MAE5B,IAAIpE,KAAK,EAAE;QACT,OAAOuB,YAAY,CAACyC,YAAY,GAAG,EAAE,CAAC;MACxC;MAEAzC,YAAY,CAACyC,YAAY,CAAC;IAC5B;EACF,CAAC;EAED,IAAMK,gBAAgB,GAAQ,SAAxBA,gBAAgBA,CAAQC,KAAU,EAAU;IAChD,IAAInE,mBAAmB,EAAE;MACvB,OAAOmE,KAAK;IACd;IAEA,OAAOA,KAAK,CAACC,OAAO,CAAC,CAAC;EACxB,CAAC;EAED,IAAMC,cAAc,GAAG1H,iBAAiB,CAAC;IACvCqD,mBAAmB,EAAnBA,mBAAmB;IACnBC,YAAY,EAAZA,YAAY;IACZkD,QAAQ,EAARA,QAAQ;IACRzC,OAAA,EAAAA;EACF,CAAC,CAAC;EAEF,IAAM4D,IAAI,GAAG1E,IAAI,GAAGrB,KAAK,CAACgG,KAAK,CAACC,UAAU,GAAGjG,KAAK,CAACgG,KAAK,CAACE,MAAM;EAE/D,IAAMC,SAAS,GAAAC,aAAA;IACbjI,KAAK,EAAEoG;EAAe,GACnBwB,IAAA,CACJ;EAED,IAAMM,YAAY,GAAG1G,QAAQ,IAAI,CAAC2B,KAAK,GAAG,CAAC,GAAG,CAAC;EAC/C,IAAMgF,YAAY,GAAG3G,QAAQ,IAAI,CAAC2B,KAAK,GAAG,CAAC,GAAG,CAAC;EAE/C,IAAMiF,WAAW,GAAGlF,IAAI,GAAGmF,MAAM,CAACC,QAAQ,GAAGD,MAAM,CAACE,MAAM;EAC1D,IAAMC,SAAS,GAAG,CAChB1I,UAAU,CAAC2I,YAAY,EACvBjH,QAAQ,GAAG6G,MAAM,CAAC7G,QAAQ,GAAG4G,WAAW,CACzC;EAED,IAAMM,qBAAqB,GAAAT,aAAA,CAAAA,aAAA,KAAQ7G,kBAAkB;IAAEI,QAAA,EAAAA;EAAA,EAAU;EAEjE,OACEhC,KAAA,CAAAmJ,aAAA,CAACrI,OAAO,EAAAsI,QAAA,KACF7F,IAAI;IACRX,MAAM,EAAE,GAAGA,MAAM,YAAa;IAC9BN,KAAK,EAAE,CACL;MACE+G,OAAO,EAAEhF,UAAU;MACnBiF,SAAS,EAAE,CACT;QACE5E,KAAK,EAAEL;MACT,CAAC,CACF;MACDmB,YAAA,EAAAA;IACF,CAAC,EACD,CAAC9B,IAAI,IAAI;MACP6F,SAAS,EAAEb;IACb,CAAC,EACDG,MAAM,CAACW,SAAS,EAChB/C,SAAS;EACT,GACG/C,IAAI,IAAI;IAAE6F,SAAS,EAAEZ;EAAa,CAAC;IACxCtG,KAAK,EAAEA,KAAM;IACbmH,SAAS;EAAA,IAETxJ,KAAA,CAAAmJ,aAAA,CAAClJ,QAAQ,CAACM,IAAI;IACZ+B,KAAK,EAAE,CACL,CAACoB,IAAI,IAAI;MACP4F,SAAS,EAAE,CACT;QACEG,MAAM,EAAEjF,OAAO,CAACkF,WAAW,CAAC;UAC1BC,UAAU,EAAE3B,gBAAgB,CAAC,CAACf,QAAQ,EAAE,CAAC,CAAC,CAAC;UAC3C2C,WAAW,EAAE5B,gBAAgB,CAAC,CAAC9G,KAAK,EAAE,CAAC,CAAC;QAC1C,CAAC;MACH,CAAC;IAEL,CAAC,EACD2H,MAAM,CAACgB,QAAQ,EACf;MAAErE,YAAA,EAAAA;IAAa,CAAC;EAChB,GAEFxF,KAAA,CAAAmJ,aAAA,CAAC5I,IAAI;IAAC+B,KAAK,EAAE,CAAChC,UAAU,CAAC2I,YAAY,EAAEJ,MAAM,CAACiB,aAAa;EAAE,GAC3D9J,KAAA,CAAAmJ,aAAA,CAAClJ,QAAQ,CAACM,IAAI;IACZwJ,aAAa,EAAC,MAAM;IACpBzH,KAAK,EAAE,CACL0G,SAAS,EACT;MACElE,KAAK,EAAEkC,aAAa;MACpBqC,OAAO,EAAE7E,OAAO,CAACkF,WAAW,CAAC;QAC3BC,UAAU,EAAE3B,gBAAgB,CAAC,CAACf,QAAQ,EAAE,GAAG,GAAGA,QAAQ,EAAE,CAAC,CAAC,CAAC;QAC3D2C,WAAW,EAAE5B,gBAAgB,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;MAC5C,CAAC,CAAC;MACFxC,YAAA,EAAAA;IACF,CAAC,CACD;IACF5C,MAAM,EAAE,GAAGA,MAAM;EAAmB,CACrC,CAAC,EACF5C,KAAA,CAAAmJ,aAAA,CAAClJ,QAAQ,CAACM,IAAI;IACZwJ,aAAa,EAAC,MAAM;IACpBzH,KAAK,EAAE,CACL0G,SAAS,EACT;MACEK,OAAO,EAAE7E,OAAO,CAACkF,WAAW,CAAC;QAC3BC,UAAU,EAAE3B,gBAAgB,CAAC,CAACf,QAAQ,EAAE,GAAG,GAAGA,QAAQ,EAAE,CAAC,CAAC,CAAC;QAC3D2C,WAAW,EAAE5B,gBAAgB,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;MAC5C,CAAC,CAAC;MACFlD,KAAK,EAAE7D,IAAI;MACXuE,YAAY,EAAEhB,OAAO,CAACkF,WAAW,CAAC;QAChCC,UAAU,EAAE3B,gBAAgB,CAAC,CAACf,QAAQ,EAAE,CAAC,CAAC,CAAC;QAC3C2C,WAAW,EAAE5B,gBAAgB,CAAC,CAC5B/G,IAAI,IAAI+F,aAAa,GAAG/F,IAAI,CAAC,EAC7BuE,YAAY,CACb;MACH,CAAC;IACH,CAAC,EACD2C,cAAc,CAACc,YAAY,CAC3B;IACFrG,MAAM,EAAE,GAAGA,MAAM;EAAU,CAC5B,CACG,CAAC,EACP5C,KAAA,CAAAmJ,aAAA,CAAClJ,QAAQ,CAACM,IAAI;IACZwJ,aAAa,EAAC,UAAU;IACxBzH,KAAK,EAAE,CAACuG,MAAM,CAACmB,YAAY,EAAE;MAAExE,YAAA,EAAAA;IAAa,CAAC;EAAE,GAE/CxF,KAAA,CAAAmJ,aAAA,CAAClJ,QAAQ,CAACM,IAAI;IACZ+B,KAAK,EAAE,CACLuG,MAAM,CAACgB,QAAQ,EACf;MACE/E,KAAK,EAAEkC,aAAa;MACpBR,eAAe,EAAfA,eAAe;MACfhB,YAAA,EAAAA;IACF,CAAC,EACD2C,cAAc,CAAC6B,YAAY;EAC3B,GAEFhK,KAAA,CAAAmJ,aAAA,CAACpI,eAAe;IACdkJ,UAAU;IACVxI,UAAU,EAAEA,UAAW;IACvBQ,OAAO,EAAEA,OAAQ;IACjBC,WAAW,EAAEA,WAAY;IACzBC,cAAc,EAAEA,cAAe;IAC/BJ,WAAW,EAAEA,WAAY;IACzBC,QAAQ,EAAEA,QAAS;IACnBL,kBAAkB,EAAEA,kBAAmB;IACvCuI,iBAAiB,EAAC,QAAQ;IAC1BtI,kBAAkB,EAAEsH,qBAAsB;IAC1CtG,MAAM,EAAEA,MAAO;IACfN,KAAK,EAAE;MAAEkD,YAAA,EAAAA;IAAa,CAAE;IACxBnD,KAAK,EAAEA,KAAM;IACbiB,OAAO,EAAEA;EAAQ,GAEjBtD,KAAA,CAAAmJ,aAAA,CAAC5I,IAAI;IACH+B,KAAK,EAAE,CACLuG,MAAM,CAACgB,QAAQ,EACf;MACE/E,KAAK,EAAEkC,aAAa;MACpBxB,YAAA,EAAAA;IACF,CAAC;EACD,CACH,CACc,CACJ,CACF,CACF,CAAC,EAEhBxF,KAAA,CAAAmJ,aAAA,CAAClJ,QAAQ,CAACM,IAAI;IACZ+B,KAAK,EAAE,CAACuG,MAAM,CAACsB,WAAW,EAAEhC,cAAc,CAACgC,WAAW,CAAE;IACxDJ,aAAa,EAAC;EAAM,GAEpB/J,KAAA,CAAAmJ,aAAA,CAACtI,IAAI;IAACuJ,MAAM,EAAE7I,IAAK;IAAC8I,IAAI,EAAE,EAAG;IAAC7J,KAAK,EAAEoG,eAAgB;IAACvE,KAAK,EAAEA;EAAM,CAAE,CACxD,CAAC,EAEhBrC,KAAA,CAAAmJ,aAAA,CAAC5I,IAAI;IAACwJ,aAAa,EAAC;EAAM,GACxB/J,KAAA,CAAAmJ,aAAA,CAACnI,YAAY;IACXsJ,GAAG,EAAEzG,KAAK,GAAGI,QAAQ,GAAG,IAAK;IAC7Bb,OAAO,EAAC,YAAY;IACpBmH,aAAa,EAAE,CAAE;IACjBnD,YAAY,EAAEzD,KAAK,GAAGyD,YAAY,GAAGoD,SAAU;IAC/CC,aAAa,EAAE,MAAO;IACtBnI,KAAK,EAAE,CAAAoI,eAAA,KAEF5G,mBAAmB,IAAIE,KAAK,GAAG,OAAO,GAAG,MAAM,EAAGD,YAAY,GAC3DkB,SAAS,GAAGhE,IAAI,GAAGuE,YAAY,IAAI9B,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,GAChD8B,YAAA,GAEN;MACEmF,QAAQ,EAAE1F,SAAS;MACnB2F,GAAG,EAAE,CAAC3J,IAAI,GAAG,CAAC,GAAGqE,UAAU,GAAG,CAAC;MAC/B+D,OAAO,EAAE7E,OAAO,CAACkF,WAAW,CAAC;QAC3BC,UAAU,EAAE3B,gBAAgB,CAAC,CAACf,QAAQ,EAAE,GAAG,GAAGA,QAAQ,EAAE,CAAC,CAAC,CAAC;QAC3D2C,WAAW,EAAE5B,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACzC,CAAC,CAAsB;MAEvBsB,SAAS,EAAE,CACT;QACEuB,UAAU,EAAErG,OAAO,CAACkF,WAAW,CAAC;UAC9BC,UAAU,EAAE3B,gBAAgB,CAAC,CAACf,QAAQ,EAAE,CAAC,CAAC,CAAC;UAC3C2C,WAAW,EAAE5B,gBAAgB,CAAC,CAAC,CAAC,EAAE/G,IAAI,CAAC;QACzC,CAAC;MACH,CAAC;IAEL,CAAC,EACD4H,MAAM,CAACrH,KAAK,EACZkB,SAAS,IAAImG,MAAM,CAACiC,cAAc,EAClCtC,SAAS,CACT;IACFnG,KAAK,EAAEA,KAAM;IACbO,MAAM,EAAE,GAAGA,MAAM,OAAQ;IACzBmI,qBAAqB,EAAE1H;EAA2B,GAEjD7B,KACW,CACV,CAAC,EAEN,CAACmC,KAAK,IAKL3D,KAAA,CAAAmJ,aAAA,CAAC9I,UAAU;IAACiC,KAAK,EAAEuG,MAAM,CAACmC;EAAyB,GACjDhL,KAAA,CAAAmJ,aAAA,CAACnI,YAAY;IACXoC,OAAO,EAAC,YAAY;IACpBmH,aAAa,EAAE,CAAE;IACjBnD,YAAY,EAAEA,YAAa;IAC3BqD,aAAa,EAAE,MAAO;IACtBnI,KAAK,EAAE,CACLuG,MAAM,CAACrH,KAAK,EACZkB,SAAS,IAAImG,MAAM,CAACiC,cAAc,EAClCtC,SAAS,CACT;IACFnG,KAAK,EAAEA;EAAM,GAEZb,KACW,CACJ,CAEP,CAAC;AAEd,CAAC;AAED,IAAMqH,MAAM,GAAGvI,UAAU,CAAC2K,MAAM,CAAC;EAC/BpB,QAAQ,EAAE;IACRzE,MAAM,EAAEnE;EACV,CAAC;EACDe,QAAQ,EAAE;IACRuH,SAAS,EAAE;EACb,CAAC;EAEDC,SAAS,EAAE;IACT0B,QAAQ,EAAE,UAAU;IACpB1E,eAAe,EAAE;EACnB,CAAC;EACDwD,YAAY,EAAE;IACZmB,aAAa,EAAE,KAAK;IACpBC,QAAQ,EAAE;EACZ,CAAC;EACDtB,aAAa,EAAE;IACbP,SAAS,EAAE;EACb,CAAC;EACDR,MAAM,EAAE;IACNQ,SAAS,EAAE;EACb,CAAC;EACDT,QAAQ,EAAE;IACRS,SAAS,EAAE;EACb,CAAC;EACDY,WAAW,EAAE;IACXkB,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBJ,QAAQ,EAAE,UAAU;IACpB9F,MAAM,EAAEnE,IAAI;IACZ6D,KAAK,EAAE7D;EACT,CAAC;EACDO,KAAK,EAAE;IACL0J,QAAQ,EAAE;EACZ,CAAC;EACDJ,cAAc,EAAE;IACdS,aAAa,EAAE;EACjB,CAAC;EACDP,wBAAwB,EAAE;IACxB5F,MAAM,EAAE,CAAC;IACT8F,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC;AAEF,eAAe/J,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}