{"ast": null, "code": "import color from 'color';\nvar getUnderlayColor = function getUnderlayColor(_ref) {\n  var theme = _ref.theme,\n    calculatedRippleColor = _ref.calculatedRippleColor,\n    underlayColor = _ref.underlayColor;\n  if (underlayColor != null) {\n    return underlayColor;\n  }\n  if (theme.isV3) {\n    return color(calculatedRippleColor).rgb().string();\n  }\n  return color(calculatedRippleColor).fade(0.5).rgb().string();\n};\nvar getRippleColor = function getRippleColor(_ref2) {\n  var theme = _ref2.theme,\n    rippleColor = _ref2.rippleColor;\n  if (rippleColor) {\n    return rippleColor;\n  }\n  if (theme.isV3) {\n    return color(theme.colors.onSurface).alpha(0.12).rgb().string();\n  }\n  if (theme.dark) {\n    return color(theme.colors.text).alpha(0.32).rgb().string();\n  }\n  return color(theme.colors.text).alpha(0.2).rgb().string();\n};\nexport var getTouchableRippleColors = function getTouchableRippleColors(_ref3) {\n  var theme = _ref3.theme,\n    rippleColor = _ref3.rippleColor,\n    underlayColor = _ref3.underlayColor;\n  var calculatedRippleColor = getRippleColor({\n    theme: theme,\n    rippleColor: rippleColor\n  });\n  return {\n    calculatedRippleColor: calculatedRippleColor,\n    calculatedUnderlayColor: getUnderlayColor({\n      theme: theme,\n      calculatedRippleColor: calculatedRippleColor,\n      underlayColor: underlayColor\n    })\n  };\n};", "map": {"version": 3, "names": ["color", "getUnderlayColor", "_ref", "theme", "calculatedRippleColor", "underlayColor", "isV3", "rgb", "string", "fade", "getRippleColor", "_ref2", "rippleColor", "colors", "onSurface", "alpha", "dark", "text", "getTouchableRippleColors", "_ref3", "calculatedUnderlayColor"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\TouchableRipple\\utils.ts"], "sourcesContent": ["import type { ColorValue } from 'react-native';\n\nimport color from 'color';\n\nimport type { InternalTheme } from '../../types';\n\nconst getUnderlayColor = ({\n  theme,\n  calculatedRippleColor,\n  underlayColor,\n}: {\n  theme: InternalTheme;\n  calculatedRippleColor: ColorValue;\n  underlayColor?: string;\n}) => {\n  if (underlayColor != null) {\n    return underlayColor;\n  }\n\n  if (theme.isV3) {\n    return color(calculatedRippleColor).rgb().string();\n  }\n\n  return color(calculatedRippleColor).fade(0.5).rgb().string();\n};\n\nconst getRippleColor = ({\n  theme,\n  rippleColor,\n}: {\n  theme: InternalTheme;\n  rippleColor?: ColorValue;\n}) => {\n  if (rippleColor) {\n    return rippleColor;\n  }\n\n  if (theme.isV3) {\n    return color(theme.colors.onSurface).alpha(0.12).rgb().string();\n  }\n\n  if (theme.dark) {\n    return color(theme.colors.text).alpha(0.32).rgb().string();\n  }\n  return color(theme.colors.text).alpha(0.2).rgb().string();\n};\n\nexport const getTouchableRippleColors = ({\n  theme,\n  rippleColor,\n  underlayColor,\n}: {\n  theme: InternalTheme;\n  rippleColor?: ColorValue;\n  underlayColor?: string;\n}) => {\n  const calculatedRippleColor = getRippleColor({ theme, rippleColor });\n  return {\n    calculatedRippleColor,\n    calculatedUnderlayColor: getUnderlayColor({\n      theme,\n      calculatedRippleColor,\n      underlayColor,\n    }),\n  };\n};\n"], "mappings": "AAEA,OAAOA,KAAK,MAAM,OAAO;AAIzB,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAAC,IAAA,EAQhB;EAAA,IAPJC,KAAK,GAAAD,IAAA,CAALC,KAAK;IACLC,qBAAqB,GAAAF,IAAA,CAArBE,qBAAqB;IACrBC,aAAA,GAAAH,IAAA,CAAAG,aAAA;EAMA,IAAIA,aAAa,IAAI,IAAI,EAAE;IACzB,OAAOA,aAAa;EACtB;EAEA,IAAIF,KAAK,CAACG,IAAI,EAAE;IACd,OAAON,KAAK,CAACI,qBAAqB,CAAC,CAACG,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EACpD;EAEA,OAAOR,KAAK,CAACI,qBAAqB,CAAC,CAACK,IAAI,CAAC,GAAG,CAAC,CAACF,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;AAC9D,CAAC;AAED,IAAME,cAAc,GAAG,SAAjBA,cAAcA,CAAAC,KAAA,EAMd;EAAA,IALJR,KAAK,GAAAQ,KAAA,CAALR,KAAK;IACLS,WAAA,GAAAD,KAAA,CAAAC,WAAA;EAKA,IAAIA,WAAW,EAAE;IACf,OAAOA,WAAW;EACpB;EAEA,IAAIT,KAAK,CAACG,IAAI,EAAE;IACd,OAAON,KAAK,CAACG,KAAK,CAACU,MAAM,CAACC,SAAS,CAAC,CAACC,KAAK,CAAC,IAAI,CAAC,CAACR,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EACjE;EAEA,IAAIL,KAAK,CAACa,IAAI,EAAE;IACd,OAAOhB,KAAK,CAACG,KAAK,CAACU,MAAM,CAACI,IAAI,CAAC,CAACF,KAAK,CAAC,IAAI,CAAC,CAACR,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EAC5D;EACA,OAAOR,KAAK,CAACG,KAAK,CAACU,MAAM,CAACI,IAAI,CAAC,CAACF,KAAK,CAAC,GAAG,CAAC,CAACR,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;AAC3D,CAAC;AAED,OAAO,IAAMU,wBAAwB,GAAG,SAA3BA,wBAAwBA,CAAAC,KAAA,EAQ/B;EAAA,IAPJhB,KAAK,GAAAgB,KAAA,CAALhB,KAAK;IACLS,WAAW,GAAAO,KAAA,CAAXP,WAAW;IACXP,aAAA,GAAAc,KAAA,CAAAd,aAAA;EAMA,IAAMD,qBAAqB,GAAGM,cAAc,CAAC;IAAEP,KAAK,EAALA,KAAK;IAAES,WAAA,EAAAA;EAAY,CAAC,CAAC;EACpE,OAAO;IACLR,qBAAqB,EAArBA,qBAAqB;IACrBgB,uBAAuB,EAAEnB,gBAAgB,CAAC;MACxCE,KAAK,EAALA,KAAK;MACLC,qBAAqB,EAArBA,qBAAqB;MACrBC,aAAA,EAAAA;IACF,CAAC;EACH,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}