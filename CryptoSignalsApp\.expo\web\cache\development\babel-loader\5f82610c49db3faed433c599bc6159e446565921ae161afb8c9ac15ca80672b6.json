{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"icon\", \"size\", \"theme\", \"accessibilityLabel\", \"disabled\", \"style\", \"value\", \"status\", \"onPress\", \"rippleColor\"];\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nimport * as React from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport color from 'color';\nimport { ToggleButtonGroupContext } from \"./ToggleButtonGroup\";\nimport { getToggleButtonColor } from \"./utils\";\nimport { useInternalTheme } from \"../../core/theming\";\nimport { black, white } from \"../../styles/themes/v2/colors\";\nimport { forwardRef } from \"../../utils/forwardRef\";\nimport IconButton from \"../IconButton/IconButton\";\nvar ToggleButton = forwardRef(function (_ref, ref) {\n  var icon = _ref.icon,\n    size = _ref.size,\n    themeOverrides = _ref.theme,\n    accessibilityLabel = _ref.accessibilityLabel,\n    disabled = _ref.disabled,\n    style = _ref.style,\n    value = _ref.value,\n    status = _ref.status,\n    _onPress = _ref.onPress,\n    rippleColor = _ref.rippleColor,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var theme = useInternalTheme(themeOverrides);\n  var borderRadius = theme.roundness;\n  return React.createElement(ToggleButtonGroupContext.Consumer, null, function (context) {\n    var checked = context && context.value === value || status === 'checked';\n    var backgroundColor = getToggleButtonColor({\n      theme: theme,\n      checked: checked\n    });\n    var borderColor = theme.isV3 ? theme.colors.outline : color(theme.dark ? white : black).alpha(0.29).rgb().string();\n    return React.createElement(IconButton, _extends({\n      borderless: false,\n      icon: icon,\n      onPress: function onPress(e) {\n        if (_onPress) {\n          _onPress(e);\n        }\n        if (context) {\n          context.onValueChange(!checked ? value : null);\n        }\n      },\n      size: size,\n      accessibilityLabel: accessibilityLabel,\n      accessibilityState: {\n        disabled: disabled,\n        selected: checked\n      },\n      disabled: disabled,\n      style: [styles.content, {\n        backgroundColor: backgroundColor,\n        borderRadius: borderRadius,\n        borderColor: borderColor\n      }, style],\n      ref: ref,\n      theme: theme,\n      rippleColor: rippleColor\n    }, rest));\n  });\n});\nvar styles = StyleSheet.create({\n  content: {\n    width: 42,\n    height: 42,\n    margin: 0\n  }\n});\nexport default ToggleButton;\nexport { ToggleButton };", "map": {"version": 3, "names": ["React", "StyleSheet", "color", "ToggleButtonGroupContext", "getToggleButtonColor", "useInternalTheme", "black", "white", "forwardRef", "IconButton", "ToggleButton", "_ref", "ref", "icon", "size", "themeOverrides", "theme", "accessibilityLabel", "disabled", "style", "value", "status", "onPress", "rippleColor", "rest", "_objectWithoutProperties", "_excluded", "borderRadius", "roundness", "createElement", "Consumer", "context", "checked", "backgroundColor", "borderColor", "isV3", "colors", "outline", "dark", "alpha", "rgb", "string", "_extends", "borderless", "e", "onValueChange", "accessibilityState", "selected", "styles", "content", "create", "width", "height", "margin"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\ToggleButton\\ToggleButton.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  GestureResponderEvent,\n  StyleProp,\n  StyleSheet,\n  ViewStyle,\n  View,\n  Animated,\n  ColorValue,\n} from 'react-native';\n\nimport color from 'color';\n\nimport { ToggleButtonGroupContext } from './ToggleButtonGroup';\nimport { getToggleButtonColor } from './utils';\nimport { useInternalTheme } from '../../core/theming';\nimport { black, white } from '../../styles/themes/v2/colors';\nimport type { ThemeProp } from '../../types';\nimport { forwardRef } from '../../utils/forwardRef';\nimport type { IconSource } from '../Icon';\nimport IconButton from '../IconButton/IconButton';\n\nexport type Props = {\n  /**\n   * Icon to display for the `ToggleButton`.\n   */\n  icon: IconSource;\n  /**\n   * Size of the icon.\n   */\n  size?: number;\n  /**\n   * Custom text color for button.\n   */\n  iconColor?: string;\n  /**\n   * Color of the ripple effect.\n   */\n  rippleColor?: ColorValue;\n  /**\n   * Whether the button is disabled.\n   */\n  disabled?: boolean;\n  /**\n   * Accessibility label for the `ToggleButton`. This is read by the screen reader when the user taps the button.\n   */\n  accessibilityLabel?: string;\n  /**\n   * Function to execute on press.\n   */\n  onPress?: (value?: GestureResponderEvent | string) => void;\n  /**\n   * Value of button.\n   */\n  value?: string;\n  /**\n   * Status of button.\n   */\n  status?: 'checked' | 'unchecked';\n  style?: Animated.WithAnimatedValue<StyleProp<ViewStyle>>;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n  ref?: React.RefObject<View>;\n  /**\n   * testID to be used on tests.\n   */\n  testID?: string;\n};\n\n/**\n * Toggle buttons can be used to group related options. To emphasize groups of related toggle buttons,\n * a group should share a common container.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { ToggleButton } from 'react-native-paper';\n *\n * const ToggleButtonExample = () => {\n *   const [status, setStatus] = React.useState('checked');\n *\n *   const onButtonToggle = value => {\n *     setStatus(status === 'checked' ? 'unchecked' : 'checked');\n *   };\n *\n *   return (\n *     <ToggleButton\n *       icon=\"bluetooth\"\n *       value=\"bluetooth\"\n *       status={status}\n *       onPress={onButtonToggle}\n *     />\n *   );\n * };\n *\n * export default ToggleButtonExample;\n *\n * ```\n */\nconst ToggleButton = forwardRef<View, Props>(\n  (\n    {\n      icon,\n      size,\n      theme: themeOverrides,\n      accessibilityLabel,\n      disabled,\n      style,\n      value,\n      status,\n      onPress,\n      rippleColor,\n      ...rest\n    }: Props,\n    ref\n  ) => {\n    const theme = useInternalTheme(themeOverrides);\n    const borderRadius = theme.roundness;\n\n    return (\n      <ToggleButtonGroupContext.Consumer>\n        {(\n          context: { value: string | null; onValueChange: Function } | null\n        ) => {\n          const checked: boolean | null =\n            (context && context.value === value) || status === 'checked';\n\n          const backgroundColor = getToggleButtonColor({ theme, checked });\n          const borderColor = theme.isV3\n            ? theme.colors.outline\n            : color(theme.dark ? white : black)\n                .alpha(0.29)\n                .rgb()\n                .string();\n\n          return (\n            <IconButton\n              borderless={false}\n              icon={icon}\n              onPress={(e?: GestureResponderEvent | string) => {\n                if (onPress) {\n                  onPress(e);\n                }\n\n                if (context) {\n                  context.onValueChange(!checked ? value : null);\n                }\n              }}\n              size={size}\n              accessibilityLabel={accessibilityLabel}\n              accessibilityState={{ disabled, selected: checked }}\n              disabled={disabled}\n              style={[\n                styles.content,\n                {\n                  backgroundColor,\n                  borderRadius,\n                  borderColor,\n                },\n                style,\n              ]}\n              ref={ref}\n              theme={theme}\n              rippleColor={rippleColor}\n              {...rest}\n            />\n          );\n        }}\n      </ToggleButtonGroupContext.Consumer>\n    );\n  }\n);\n\nconst styles = StyleSheet.create({\n  content: {\n    width: 42,\n    height: 42,\n    margin: 0,\n  },\n});\n\nexport default ToggleButton;\n\n// @component-docs ignore-next-line\nexport { ToggleButton };\n"], "mappings": ";;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,UAAA;AAW9B,OAAOC,KAAK,MAAM,OAAO;AAEzB,SAASC,wBAAwB;AACjC,SAASC,oBAAoB;AAC7B,SAASC,gBAAgB;AACzB,SAASC,KAAK,EAAEC,KAAK;AAErB,SAASC,UAAU;AAEnB,OAAOC,UAAU;AAiFjB,IAAMC,YAAY,GAAGF,UAAU,CAC7B,UAAAG,IAAA,EAcEC,GAAG,EACA;EAAA,IAbDC,IAAI,GAAAF,IAAA,CAAJE,IAAI;IACJC,IAAI,GAAAH,IAAA,CAAJG,IAAI;IACGC,cAAc,GAAAJ,IAAA,CAArBK,KAAK;IACLC,kBAAkB,GAAAN,IAAA,CAAlBM,kBAAkB;IAClBC,QAAQ,GAAAP,IAAA,CAARO,QAAQ;IACRC,KAAK,GAAAR,IAAA,CAALQ,KAAK;IACLC,KAAK,GAAAT,IAAA,CAALS,KAAK;IACLC,MAAM,GAAAV,IAAA,CAANU,MAAM;IACNC,QAAO,GAAAX,IAAA,CAAPW,OAAO;IACPC,WAAW,GAAAZ,IAAA,CAAXY,WAAW;IACRC,IAAA,GAAAC,wBAAA,CAAAd,IAAA,EAAAe,SAAA;EAIL,IAAMV,KAAK,GAAGX,gBAAgB,CAACU,cAAc,CAAC;EAC9C,IAAMY,YAAY,GAAGX,KAAK,CAACY,SAAS;EAEpC,OACE5B,KAAA,CAAA6B,aAAA,CAAC1B,wBAAwB,CAAC2B,QAAQ,QAE9B,UAAAC,OAAiE,EAC9D;IACH,IAAMC,OAAuB,GAC1BD,OAAO,IAAIA,OAAO,CAACX,KAAK,KAAKA,KAAK,IAAKC,MAAM,KAAK,SAAS;IAE9D,IAAMY,eAAe,GAAG7B,oBAAoB,CAAC;MAAEY,KAAK,EAALA,KAAK;MAAEgB,OAAA,EAAAA;IAAQ,CAAC,CAAC;IAChE,IAAME,WAAW,GAAGlB,KAAK,CAACmB,IAAI,GAC1BnB,KAAK,CAACoB,MAAM,CAACC,OAAO,GACpBnC,KAAK,CAACc,KAAK,CAACsB,IAAI,GAAG/B,KAAK,GAAGD,KAAK,CAAC,CAC9BiC,KAAK,CAAC,IAAI,CAAC,CACXC,GAAG,CAAC,CAAC,CACLC,MAAM,CAAC,CAAC;IAEf,OACEzC,KAAA,CAAA6B,aAAA,CAACpB,UAAU,EAAAiC,QAAA;MACTC,UAAU,EAAE,KAAM;MAClB9B,IAAI,EAAEA,IAAK;MACXS,OAAO,EAAG,SAAVA,OAAOA,CAAGsB,CAAkC,EAAK;QAC/C,IAAItB,QAAO,EAAE;UACXA,QAAO,CAACsB,CAAC,CAAC;QACZ;QAEA,IAAIb,OAAO,EAAE;UACXA,OAAO,CAACc,aAAa,CAAC,CAACb,OAAO,GAAGZ,KAAK,GAAG,IAAI,CAAC;QAChD;MACF,CAAE;MACFN,IAAI,EAAEA,IAAK;MACXG,kBAAkB,EAAEA,kBAAmB;MACvC6B,kBAAkB,EAAE;QAAE5B,QAAQ,EAARA,QAAQ;QAAE6B,QAAQ,EAAEf;MAAQ,CAAE;MACpDd,QAAQ,EAAEA,QAAS;MACnBC,KAAK,EAAE,CACL6B,MAAM,CAACC,OAAO,EACd;QACEhB,eAAe,EAAfA,eAAe;QACfN,YAAY,EAAZA,YAAY;QACZO,WAAA,EAAAA;MACF,CAAC,EACDf,KAAK,CACL;MACFP,GAAG,EAAEA,GAAI;MACTI,KAAK,EAAEA,KAAM;MACbO,WAAW,EAAEA;IAAY,GACrBC,IAAI,CACT,CAAC;EAEN,CACiC,CAAC;AAExC,CACF,CAAC;AAED,IAAMwB,MAAM,GAAG/C,UAAU,CAACiD,MAAM,CAAC;EAC/BD,OAAO,EAAE;IACPE,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,MAAM,EAAE;EACV;AACF,CAAC,CAAC;AAEF,eAAe3C,YAAY;AAG3B,SAASA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}