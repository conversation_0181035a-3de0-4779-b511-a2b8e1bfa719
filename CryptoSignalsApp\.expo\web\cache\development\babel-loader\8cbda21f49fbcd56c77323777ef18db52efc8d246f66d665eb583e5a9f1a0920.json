{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nvar _excluded = [\"activeOpacity\", \"children\", \"delayPressIn\", \"delayPressOut\", \"delayLongPress\", \"disabled\", \"focusable\", \"onHideUnderlay\", \"onLongPress\", \"onPress\", \"onPressIn\", \"onPressOut\", \"onShowUnderlay\", \"rejectResponderTermination\", \"style\", \"testOnly_pressed\", \"underlayColor\"];\nimport * as React from 'react';\nimport { useCallback, useMemo, useState, useRef } from 'react';\nimport useMergeRefs from \"../../modules/useMergeRefs\";\nimport usePressEvents from \"../../modules/usePressEvents\";\nimport StyleSheet from \"../StyleSheet\";\nimport View from \"../View\";\nfunction createExtraStyles(activeOpacity, underlayColor) {\n  return {\n    child: {\n      opacity: activeOpacity !== null && activeOpacity !== void 0 ? activeOpacity : 0.85\n    },\n    underlay: {\n      backgroundColor: underlayColor === undefined ? 'black' : underlayColor\n    }\n  };\n}\nfunction hasPressHandler(props) {\n  return props.onPress != null || props.onPressIn != null || props.onPressOut != null || props.onLongPress != null;\n}\nfunction TouchableHighlight(props, forwardedRef) {\n  var activeOpacity = props.activeOpacity,\n    children = props.children,\n    delayPressIn = props.delayPressIn,\n    delayPressOut = props.delayPressOut,\n    delayLongPress = props.delayLongPress,\n    disabled = props.disabled,\n    focusable = props.focusable,\n    onHideUnderlay = props.onHideUnderlay,\n    onLongPress = props.onLongPress,\n    onPress = props.onPress,\n    onPressIn = props.onPressIn,\n    onPressOut = props.onPressOut,\n    onShowUnderlay = props.onShowUnderlay,\n    rejectResponderTermination = props.rejectResponderTermination,\n    style = props.style,\n    testOnly_pressed = props.testOnly_pressed,\n    underlayColor = props.underlayColor,\n    rest = _objectWithoutPropertiesLoose(props, _excluded);\n  var hostRef = useRef(null);\n  var setRef = useMergeRefs(forwardedRef, hostRef);\n  var _useState = useState(testOnly_pressed === true ? createExtraStyles(activeOpacity, underlayColor) : null),\n    extraStyles = _useState[0],\n    setExtraStyles = _useState[1];\n  var showUnderlay = useCallback(function () {\n    if (!hasPressHandler(props)) {\n      return;\n    }\n    setExtraStyles(createExtraStyles(activeOpacity, underlayColor));\n    if (onShowUnderlay != null) {\n      onShowUnderlay();\n    }\n  }, [activeOpacity, onShowUnderlay, props, underlayColor]);\n  var hideUnderlay = useCallback(function () {\n    if (testOnly_pressed === true) {\n      return;\n    }\n    if (hasPressHandler(props)) {\n      setExtraStyles(null);\n      if (onHideUnderlay != null) {\n        onHideUnderlay();\n      }\n    }\n  }, [onHideUnderlay, props, testOnly_pressed]);\n  var pressConfig = useMemo(function () {\n    return {\n      cancelable: !rejectResponderTermination,\n      disabled: disabled,\n      delayLongPress: delayLongPress,\n      delayPressStart: delayPressIn,\n      delayPressEnd: delayPressOut,\n      onLongPress: onLongPress,\n      onPress: onPress,\n      onPressStart: function onPressStart(event) {\n        showUnderlay();\n        if (onPressIn != null) {\n          onPressIn(event);\n        }\n      },\n      onPressEnd: function onPressEnd(event) {\n        hideUnderlay();\n        if (onPressOut != null) {\n          onPressOut(event);\n        }\n      }\n    };\n  }, [delayLongPress, delayPressIn, delayPressOut, disabled, onLongPress, onPress, onPressIn, onPressOut, rejectResponderTermination, showUnderlay, hideUnderlay]);\n  var pressEventHandlers = usePressEvents(hostRef, pressConfig);\n  var child = React.Children.only(children);\n  return React.createElement(View, _extends({}, rest, pressEventHandlers, {\n    accessibilityDisabled: disabled,\n    focusable: !disabled && focusable !== false,\n    pointerEvents: disabled ? 'box-none' : undefined,\n    ref: setRef,\n    style: [styles.root, style, !disabled && styles.actionable, extraStyles && extraStyles.underlay]\n  }), React.cloneElement(child, {\n    style: [child.props.style, extraStyles && extraStyles.child]\n  }));\n}\nvar styles = StyleSheet.create({\n  root: {\n    userSelect: 'none'\n  },\n  actionable: {\n    cursor: 'pointer',\n    touchAction: 'manipulation'\n  }\n});\nvar MemoedTouchableHighlight = React.memo(React.forwardRef(TouchableHighlight));\nMemoedTouchableHighlight.displayName = 'TouchableHighlight';\nexport default MemoedTouchableHighlight;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "useCallback", "useMemo", "useState", "useRef", "useMergeRefs", "usePressEvents", "StyleSheet", "View", "createExtraStyles", "activeOpacity", "underlayColor", "child", "opacity", "underlay", "backgroundColor", "undefined", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "props", "onPress", "onPressIn", "onPressOut", "onLongPress", "TouchableHighlight", "forwardedRef", "children", "delayPressIn", "delayPressOut", "delayLongPress", "disabled", "focusable", "onHideUnderlay", "onShowUnderlay", "rejectResponderTermination", "style", "testOnly_pressed", "rest", "hostRef", "setRef", "_useState", "extraStyles", "setExtraStyles", "showUnderlay", "<PERSON><PERSON><PERSON><PERSON>", "pressConfig", "cancelable", "delayPressStart", "delayPressEnd", "onPressStart", "event", "onPressEnd", "pressEventHandlers", "Children", "only", "createElement", "accessibilityDisabled", "pointerEvents", "ref", "styles", "root", "actionable", "cloneElement", "create", "userSelect", "cursor", "touchAction", "MemoedTouchableHighlight", "memo", "forwardRef", "displayName"], "sources": ["E:/CryptoSignalsApp/node_modules/react-native-web/dist/exports/TouchableHighlight/index.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use client';\n\nimport _extends from \"@babel/runtime/helpers/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nvar _excluded = [\"activeOpacity\", \"children\", \"delayPressIn\", \"delayPressOut\", \"delayLongPress\", \"disabled\", \"focusable\", \"onHideUnderlay\", \"onLongPress\", \"onPress\", \"onPressIn\", \"onPressOut\", \"onShowUnderlay\", \"rejectResponderTermination\", \"style\", \"testOnly_pressed\", \"underlayColor\"];\nimport * as React from 'react';\nimport { useCallback, useMemo, useState, useRef } from 'react';\nimport useMergeRefs from '../../modules/useMergeRefs';\nimport usePressEvents from '../../modules/usePressEvents';\nimport StyleSheet from '../StyleSheet';\nimport View from '../View';\n//import { warnOnce } from '../../modules/warnOnce';\n\nfunction createExtraStyles(activeOpacity, underlayColor) {\n  return {\n    child: {\n      opacity: activeOpacity !== null && activeOpacity !== void 0 ? activeOpacity : 0.85\n    },\n    underlay: {\n      backgroundColor: underlayColor === undefined ? 'black' : underlayColor\n    }\n  };\n}\nfunction hasPressHandler(props) {\n  return props.onPress != null || props.onPressIn != null || props.onPressOut != null || props.onLongPress != null;\n}\n\n/**\n * A wrapper for making views respond properly to touches.\n * On press down, the opacity of the wrapped view is decreased, which allows\n * the underlay color to show through, darkening or tinting the view.\n *\n * The underlay comes from wrapping the child in a new View, which can affect\n * layout, and sometimes cause unwanted visual artifacts if not used correctly,\n * for example if the backgroundColor of the wrapped view isn't explicitly set\n * to an opaque color.\n *\n * TouchableHighlight must have one child (not zero or more than one).\n * If you wish to have several child components, wrap them in a View.\n */\nfunction TouchableHighlight(props, forwardedRef) {\n  /*\n  warnOnce(\n    'TouchableHighlight',\n    'TouchableHighlight is deprecated. Please use Pressable.'\n  );\n  */\n\n  var activeOpacity = props.activeOpacity,\n    children = props.children,\n    delayPressIn = props.delayPressIn,\n    delayPressOut = props.delayPressOut,\n    delayLongPress = props.delayLongPress,\n    disabled = props.disabled,\n    focusable = props.focusable,\n    onHideUnderlay = props.onHideUnderlay,\n    onLongPress = props.onLongPress,\n    onPress = props.onPress,\n    onPressIn = props.onPressIn,\n    onPressOut = props.onPressOut,\n    onShowUnderlay = props.onShowUnderlay,\n    rejectResponderTermination = props.rejectResponderTermination,\n    style = props.style,\n    testOnly_pressed = props.testOnly_pressed,\n    underlayColor = props.underlayColor,\n    rest = _objectWithoutPropertiesLoose(props, _excluded);\n  var hostRef = useRef(null);\n  var setRef = useMergeRefs(forwardedRef, hostRef);\n  var _useState = useState(testOnly_pressed === true ? createExtraStyles(activeOpacity, underlayColor) : null),\n    extraStyles = _useState[0],\n    setExtraStyles = _useState[1];\n  var showUnderlay = useCallback(() => {\n    if (!hasPressHandler(props)) {\n      return;\n    }\n    setExtraStyles(createExtraStyles(activeOpacity, underlayColor));\n    if (onShowUnderlay != null) {\n      onShowUnderlay();\n    }\n  }, [activeOpacity, onShowUnderlay, props, underlayColor]);\n  var hideUnderlay = useCallback(() => {\n    if (testOnly_pressed === true) {\n      return;\n    }\n    if (hasPressHandler(props)) {\n      setExtraStyles(null);\n      if (onHideUnderlay != null) {\n        onHideUnderlay();\n      }\n    }\n  }, [onHideUnderlay, props, testOnly_pressed]);\n  var pressConfig = useMemo(() => ({\n    cancelable: !rejectResponderTermination,\n    disabled,\n    delayLongPress,\n    delayPressStart: delayPressIn,\n    delayPressEnd: delayPressOut,\n    onLongPress,\n    onPress,\n    onPressStart(event) {\n      showUnderlay();\n      if (onPressIn != null) {\n        onPressIn(event);\n      }\n    },\n    onPressEnd(event) {\n      hideUnderlay();\n      if (onPressOut != null) {\n        onPressOut(event);\n      }\n    }\n  }), [delayLongPress, delayPressIn, delayPressOut, disabled, onLongPress, onPress, onPressIn, onPressOut, rejectResponderTermination, showUnderlay, hideUnderlay]);\n  var pressEventHandlers = usePressEvents(hostRef, pressConfig);\n  var child = React.Children.only(children);\n  return /*#__PURE__*/React.createElement(View, _extends({}, rest, pressEventHandlers, {\n    accessibilityDisabled: disabled,\n    focusable: !disabled && focusable !== false,\n    pointerEvents: disabled ? 'box-none' : undefined,\n    ref: setRef,\n    style: [styles.root, style, !disabled && styles.actionable, extraStyles && extraStyles.underlay]\n  }), /*#__PURE__*/React.cloneElement(child, {\n    style: [child.props.style, extraStyles && extraStyles.child]\n  }));\n}\nvar styles = StyleSheet.create({\n  root: {\n    userSelect: 'none'\n  },\n  actionable: {\n    cursor: 'pointer',\n    touchAction: 'manipulation'\n  }\n});\nvar MemoedTouchableHighlight = /*#__PURE__*/React.memo(/*#__PURE__*/React.forwardRef(TouchableHighlight));\nMemoedTouchableHighlight.displayName = 'TouchableHighlight';\nexport default MemoedTouchableHighlight;"], "mappings": "AAUA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,gCAAgC;AACrD,OAAOC,6BAA6B,MAAM,qDAAqD;AAC/F,IAAIC,SAAS,GAAG,CAAC,eAAe,EAAE,UAAU,EAAE,cAAc,EAAE,eAAe,EAAE,gBAAgB,EAAE,UAAU,EAAE,WAAW,EAAE,gBAAgB,EAAE,aAAa,EAAE,SAAS,EAAE,WAAW,EAAE,YAAY,EAAE,gBAAgB,EAAE,4BAA4B,EAAE,OAAO,EAAE,kBAAkB,EAAE,eAAe,CAAC;AAC9R,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,WAAW,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC9D,OAAOC,YAAY;AACnB,OAAOC,cAAc;AACrB,OAAOC,UAAU;AACjB,OAAOC,IAAI;AAGX,SAASC,iBAAiBA,CAACC,aAAa,EAAEC,aAAa,EAAE;EACvD,OAAO;IACLC,KAAK,EAAE;MACLC,OAAO,EAAEH,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAGA,aAAa,GAAG;IAChF,CAAC;IACDI,QAAQ,EAAE;MACRC,eAAe,EAAEJ,aAAa,KAAKK,SAAS,GAAG,OAAO,GAAGL;IAC3D;EACF,CAAC;AACH;AACA,SAASM,eAAeA,CAACC,KAAK,EAAE;EAC9B,OAAOA,KAAK,CAACC,OAAO,IAAI,IAAI,IAAID,KAAK,CAACE,SAAS,IAAI,IAAI,IAAIF,KAAK,CAACG,UAAU,IAAI,IAAI,IAAIH,KAAK,CAACI,WAAW,IAAI,IAAI;AAClH;AAeA,SAASC,kBAAkBA,CAACL,KAAK,EAAEM,YAAY,EAAE;EAQ/C,IAAId,aAAa,GAAGQ,KAAK,CAACR,aAAa;IACrCe,QAAQ,GAAGP,KAAK,CAACO,QAAQ;IACzBC,YAAY,GAAGR,KAAK,CAACQ,YAAY;IACjCC,aAAa,GAAGT,KAAK,CAACS,aAAa;IACnCC,cAAc,GAAGV,KAAK,CAACU,cAAc;IACrCC,QAAQ,GAAGX,KAAK,CAACW,QAAQ;IACzBC,SAAS,GAAGZ,KAAK,CAACY,SAAS;IAC3BC,cAAc,GAAGb,KAAK,CAACa,cAAc;IACrCT,WAAW,GAAGJ,KAAK,CAACI,WAAW;IAC/BH,OAAO,GAAGD,KAAK,CAACC,OAAO;IACvBC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC3BC,UAAU,GAAGH,KAAK,CAACG,UAAU;IAC7BW,cAAc,GAAGd,KAAK,CAACc,cAAc;IACrCC,0BAA0B,GAAGf,KAAK,CAACe,0BAA0B;IAC7DC,KAAK,GAAGhB,KAAK,CAACgB,KAAK;IACnBC,gBAAgB,GAAGjB,KAAK,CAACiB,gBAAgB;IACzCxB,aAAa,GAAGO,KAAK,CAACP,aAAa;IACnCyB,IAAI,GAAGtC,6BAA6B,CAACoB,KAAK,EAAEnB,SAAS,CAAC;EACxD,IAAIsC,OAAO,GAAGjC,MAAM,CAAC,IAAI,CAAC;EAC1B,IAAIkC,MAAM,GAAGjC,YAAY,CAACmB,YAAY,EAAEa,OAAO,CAAC;EAChD,IAAIE,SAAS,GAAGpC,QAAQ,CAACgC,gBAAgB,KAAK,IAAI,GAAG1B,iBAAiB,CAACC,aAAa,EAAEC,aAAa,CAAC,GAAG,IAAI,CAAC;IAC1G6B,WAAW,GAAGD,SAAS,CAAC,CAAC,CAAC;IAC1BE,cAAc,GAAGF,SAAS,CAAC,CAAC,CAAC;EAC/B,IAAIG,YAAY,GAAGzC,WAAW,CAAC,YAAM;IACnC,IAAI,CAACgB,eAAe,CAACC,KAAK,CAAC,EAAE;MAC3B;IACF;IACAuB,cAAc,CAAChC,iBAAiB,CAACC,aAAa,EAAEC,aAAa,CAAC,CAAC;IAC/D,IAAIqB,cAAc,IAAI,IAAI,EAAE;MAC1BA,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAACtB,aAAa,EAAEsB,cAAc,EAAEd,KAAK,EAAEP,aAAa,CAAC,CAAC;EACzD,IAAIgC,YAAY,GAAG1C,WAAW,CAAC,YAAM;IACnC,IAAIkC,gBAAgB,KAAK,IAAI,EAAE;MAC7B;IACF;IACA,IAAIlB,eAAe,CAACC,KAAK,CAAC,EAAE;MAC1BuB,cAAc,CAAC,IAAI,CAAC;MACpB,IAAIV,cAAc,IAAI,IAAI,EAAE;QAC1BA,cAAc,CAAC,CAAC;MAClB;IACF;EACF,CAAC,EAAE,CAACA,cAAc,EAAEb,KAAK,EAAEiB,gBAAgB,CAAC,CAAC;EAC7C,IAAIS,WAAW,GAAG1C,OAAO,CAAC;IAAA,OAAO;MAC/B2C,UAAU,EAAE,CAACZ,0BAA0B;MACvCJ,QAAQ,EAARA,QAAQ;MACRD,cAAc,EAAdA,cAAc;MACdkB,eAAe,EAAEpB,YAAY;MAC7BqB,aAAa,EAAEpB,aAAa;MAC5BL,WAAW,EAAXA,WAAW;MACXH,OAAO,EAAPA,OAAO;MACP6B,YAAY,WAAZA,YAAYA,CAACC,KAAK,EAAE;QAClBP,YAAY,CAAC,CAAC;QACd,IAAItB,SAAS,IAAI,IAAI,EAAE;UACrBA,SAAS,CAAC6B,KAAK,CAAC;QAClB;MACF,CAAC;MACDC,UAAU,WAAVA,UAAUA,CAACD,KAAK,EAAE;QAChBN,YAAY,CAAC,CAAC;QACd,IAAItB,UAAU,IAAI,IAAI,EAAE;UACtBA,UAAU,CAAC4B,KAAK,CAAC;QACnB;MACF;IACF,CAAC;EAAA,CAAC,EAAE,CAACrB,cAAc,EAAEF,YAAY,EAAEC,aAAa,EAAEE,QAAQ,EAAEP,WAAW,EAAEH,OAAO,EAAEC,SAAS,EAAEC,UAAU,EAAEY,0BAA0B,EAAES,YAAY,EAAEC,YAAY,CAAC,CAAC;EACjK,IAAIQ,kBAAkB,GAAG7C,cAAc,CAAC+B,OAAO,EAAEO,WAAW,CAAC;EAC7D,IAAIhC,KAAK,GAAGZ,KAAK,CAACoD,QAAQ,CAACC,IAAI,CAAC5B,QAAQ,CAAC;EACzC,OAAoBzB,KAAK,CAACsD,aAAa,CAAC9C,IAAI,EAAEX,QAAQ,CAAC,CAAC,CAAC,EAAEuC,IAAI,EAAEe,kBAAkB,EAAE;IACnFI,qBAAqB,EAAE1B,QAAQ;IAC/BC,SAAS,EAAE,CAACD,QAAQ,IAAIC,SAAS,KAAK,KAAK;IAC3C0B,aAAa,EAAE3B,QAAQ,GAAG,UAAU,GAAGb,SAAS;IAChDyC,GAAG,EAAEnB,MAAM;IACXJ,KAAK,EAAE,CAACwB,MAAM,CAACC,IAAI,EAAEzB,KAAK,EAAE,CAACL,QAAQ,IAAI6B,MAAM,CAACE,UAAU,EAAEpB,WAAW,IAAIA,WAAW,CAAC1B,QAAQ;EACjG,CAAC,CAAC,EAAed,KAAK,CAAC6D,YAAY,CAACjD,KAAK,EAAE;IACzCsB,KAAK,EAAE,CAACtB,KAAK,CAACM,KAAK,CAACgB,KAAK,EAAEM,WAAW,IAAIA,WAAW,CAAC5B,KAAK;EAC7D,CAAC,CAAC,CAAC;AACL;AACA,IAAI8C,MAAM,GAAGnD,UAAU,CAACuD,MAAM,CAAC;EAC7BH,IAAI,EAAE;IACJI,UAAU,EAAE;EACd,CAAC;EACDH,UAAU,EAAE;IACVI,MAAM,EAAE,SAAS;IACjBC,WAAW,EAAE;EACf;AACF,CAAC,CAAC;AACF,IAAIC,wBAAwB,GAAgBlE,KAAK,CAACmE,IAAI,CAAcnE,KAAK,CAACoE,UAAU,CAAC7C,kBAAkB,CAAC,CAAC;AACzG2C,wBAAwB,CAACG,WAAW,GAAG,oBAAoB;AAC3D,eAAeH,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}