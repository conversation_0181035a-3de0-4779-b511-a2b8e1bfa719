# Documentação dos Serviços - Servidor de Trading

Este repositório contém a documentação e scripts de monitoramento dos serviços rodando em nosso servidor de trading.

**IP do Servidor:** **************  
**Sistema Operacional:** Ubuntu 24.04 LTS

## Credenciais:
<EMAIL>
Tereza-28

ssh root@**************
Senha ssh:
h4*ls:FtJw0e 

## Serviços Ativos

### 1. Gerador de Sinais Telegram (`gerador_sinais.service`)
- **Descrição:** Sistema automatizado de geração de sinais de trading
- **Localização:** `/root/gerador_sinais_telegram/`
- **Arquivo Principal:** `main.py`
- **Logs:** `/root/gerador_sinais_telegram/gerador_sinais.log`
- **Canal Telegram:** SoloLeveling Scalp300%
- **Status:** Serviço ativo e rodando 24/7
- **Funcionalidades:**
  - Análise de mercado em tempo real
  - Geração de sinais de trading
  - Envio automático para canal do Telegram
  - Monitoramento de múltiplos pares de trading

### 2. Sistema de Clientes Streamlit (`sistema-clientes.service`)
- **Descrição:** Interface web para clientes
- **Status:** Serviço ativo e rodando 24/7
- **Acesso:** Via navegador web
- **Tipo:** Aplicação Streamlit

## Ferramentas de Monitoramento

### Monitor de Serviços (`monitor.py`)
Script Python para monitoramento em tempo real dos serviços.

#### Funcionalidades:
1. **Monitoramento Contínuo**
   - Status dos serviços
   - Uso de CPU e memória
   - Estado do servidor

2. **Visualização de Logs**
   - Últimas 50, 100 ou 200 linhas
   - Acompanhamento em tempo real
   - Histórico de eventos

3. **Status do Sistema**
   - Uso de memória
   - Uso de disco
   - Status dos serviços

#### Como Usar o Monitor:

1. Instale as dependências:
```bash
pip install paramiko python-dotenv requests
```

2. Execute o monitor:
```bash
python monitor.py
```

3. Menu de opções:
   - 1: Iniciar monitoramento contínuo
   - 2: Ver logs do Gerador de Sinais
   - 3: Ver status atual
   - 4: Sair

## Manutenção dos Serviços

### Comandos Úteis

```bash
# Status dos serviços
systemctl status gerador_sinais
systemctl status sistema-clientes

# Reiniciar serviços
systemctl restart gerador_sinais
systemctl restart sistema-clientes

# Ver logs
tail -f /root/gerador_sinais_telegram/gerador_sinais.log  # Logs do gerador
journalctl -u sistema-clientes -f  # Logs do sistema de clientes
```

### Backup e Logs
- Logs são mantidos em `/root/gerador_sinais_telegram/gerador_sinais.log`
- Monitor mantém histórico em `monitor_logs/`
- Status atual em `monitor_logs/status.json`

## Segurança e Acesso

- Acesso SSH: `ssh root@**************`
- Porta: 22 (padrão SSH)
- Autenticação: Por senha

## Monitoramento de Recursos

O sistema monitora continuamente:
- Uso de CPU
- Uso de memória
- Espaço em disco
- Status dos serviços
- Logs de aplicação

## Troubleshooting

Em caso de problemas:

1. Verifique os logs:
   ```bash
   tail -f /root/gerador_sinais_telegram/gerador_sinais.log
   ```

2. Verifique o status dos serviços:
   ```bash
   systemctl status gerador_sinais
   systemctl status sistema-clientes
   ```

3. Reinicie os serviços se necessário:
   ```bash
   systemctl restart gerador_sinais
   systemctl restart sistema-clientes
   ```

## Contato e Suporte

Para questões relacionadas à manutenção e suporte dos serviços, entre em contato com a equipe de desenvolvimento.

---

**Nota:** Este documento deve ser mantido atualizado conforme mudanças são feitas no servidor ou nos serviços.

## Caso tudo de errado
Agora temos uma informação importante sobre o problema. O erro `status=203/EXEC` indica que há um problema ao executar o comando especificado no serviço. Especificamente, o sistema não consegue executar o Python através do caminho virtual environment especificado.

O problema parece ser que o script está tentando usar um ambiente virtual (`/root/gerador_sinais_telegram/venv/bin/python`), mas esse ambiente virtual provavelmente não existe ou não foi recriado durante a atualização.

Vamos resolver isso criando um novo ambiente virtual e instalando as dependências:

1. **Crie um novo ambiente virtual**:
   ```
   ssh root@************** "cd /root/gerador_sinais_telegram && python3 -m venv venv && venv/bin/pip install -r requirements.txt"
   ```

2. **Verifique se o ambiente virtual foi criado corretamente**:
   ```
   ssh root@************** "ls -la /root/gerador_sinais_telegram/venv/bin/python"
   ```

3. **Reinicie o serviço**:
   ```
   ssh root@************** "systemctl restart gerador_sinais"
   ```

4. **Verifique o status novamente**:
   ```
   ssh root@************** "systemctl status gerador_sinais"
   ```

O problema é que nosso script de atualização não recriou o ambiente virtual, que é necessário para o serviço funcionar. O serviço está configurado para usar o Python do ambiente virtual, mas como não o recriamos durante a atualização, o serviço não consegue iniciar.

Execute os comandos acima para criar o ambiente virtual, instalar as dependências e reiniciar o serviço. Isso deve resolver o problema.
