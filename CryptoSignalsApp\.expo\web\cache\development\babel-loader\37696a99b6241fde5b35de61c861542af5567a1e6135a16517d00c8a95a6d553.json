{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport * as React from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport { getContentMaxWidth, getMenuItemColor, MAX_WIDTH, MIN_WIDTH } from \"./utils\";\nimport { useInternalTheme } from \"../../core/theming\";\nimport Icon from \"../Icon\";\nimport TouchableRipple from \"../TouchableRipple/TouchableRipple\";\nimport Text from \"../Typography/Text\";\nvar MenuItem = function MenuItem(_ref) {\n  var leadingIcon = _ref.leadingIcon,\n    trailingIcon = _ref.trailingIcon,\n    dense = _ref.dense,\n    title = _ref.title,\n    disabled = _ref.disabled,\n    background = _ref.background,\n    onPress = _ref.onPress,\n    style = _ref.style,\n    containerStyle = _ref.containerStyle,\n    contentStyle = _ref.contentStyle,\n    titleStyle = _ref.titleStyle,\n    customRippleColor = _ref.rippleColor,\n    _ref$testID = _ref.testID,\n    testID = _ref$testID === void 0 ? 'menu-item' : _ref$testID,\n    accessibilityLabel = _ref.accessibilityLabel,\n    accessibilityState = _ref.accessibilityState,\n    themeOverrides = _ref.theme,\n    _ref$titleMaxFontSize = _ref.titleMaxFontSizeMultiplier,\n    titleMaxFontSizeMultiplier = _ref$titleMaxFontSize === void 0 ? 1.5 : _ref$titleMaxFontSize,\n    hitSlop = _ref.hitSlop;\n  var theme = useInternalTheme(themeOverrides);\n  var _getMenuItemColor = getMenuItemColor({\n      theme: theme,\n      disabled: disabled,\n      customRippleColor: customRippleColor\n    }),\n    titleColor = _getMenuItemColor.titleColor,\n    iconColor = _getMenuItemColor.iconColor,\n    rippleColor = _getMenuItemColor.rippleColor;\n  var isV3 = theme.isV3;\n  var containerPadding = isV3 ? 12 : 8;\n  var iconWidth = isV3 ? 24 : 40;\n  var minWidth = MIN_WIDTH - (isV3 ? 12 : 16);\n  var maxWidth = getContentMaxWidth({\n    isV3: isV3,\n    iconWidth: iconWidth,\n    leadingIcon: leadingIcon,\n    trailingIcon: trailingIcon\n  });\n  var titleTextStyle = _objectSpread({\n    color: titleColor\n  }, isV3 ? theme.fonts.bodyLarge : {});\n  var newAccessibilityState = _objectSpread(_objectSpread({}, accessibilityState), {}, {\n    disabled: disabled\n  });\n  return React.createElement(TouchableRipple, {\n    style: [styles.container, {\n      paddingHorizontal: containerPadding\n    }, dense && styles.md3DenseContainer, style],\n    onPress: onPress,\n    disabled: disabled,\n    testID: testID,\n    background: background,\n    accessibilityLabel: accessibilityLabel,\n    accessibilityRole: \"menuitem\",\n    accessibilityState: newAccessibilityState,\n    rippleColor: rippleColor,\n    hitSlop: hitSlop\n  }, React.createElement(View, {\n    style: [styles.row, containerStyle]\n  }, leadingIcon ? React.createElement(View, {\n    style: [!isV3 && styles.item, {\n      width: iconWidth\n    }],\n    pointerEvents: \"box-none\"\n  }, React.createElement(Icon, {\n    source: leadingIcon,\n    size: 24,\n    color: iconColor\n  })) : null, React.createElement(View, {\n    style: [!isV3 && styles.item, styles.content, {\n      minWidth: minWidth,\n      maxWidth: maxWidth\n    }, isV3 && (leadingIcon ? styles.md3LeadingIcon : styles.md3WithoutLeadingIcon), contentStyle],\n    pointerEvents: \"none\"\n  }, React.createElement(Text, {\n    variant: \"bodyLarge\",\n    selectable: false,\n    numberOfLines: 1,\n    testID: `${testID}-title`,\n    style: [!isV3 && styles.title, titleTextStyle, titleStyle],\n    maxFontSizeMultiplier: titleMaxFontSizeMultiplier\n  }, title)), isV3 && trailingIcon ? React.createElement(View, {\n    style: [!isV3 && styles.item, {\n      width: iconWidth\n    }],\n    pointerEvents: \"box-none\"\n  }, React.createElement(Icon, {\n    source: trailingIcon,\n    size: 24,\n    color: iconColor\n  })) : null));\n};\nMenuItem.displayName = 'Menu.Item';\nvar styles = StyleSheet.create({\n  container: {\n    minWidth: MIN_WIDTH,\n    maxWidth: MAX_WIDTH,\n    height: 48,\n    justifyContent: 'center'\n  },\n  md3DenseContainer: {\n    height: 32\n  },\n  row: {\n    flexDirection: 'row'\n  },\n  title: {\n    fontSize: 16\n  },\n  item: {\n    marginHorizontal: 8\n  },\n  content: {\n    justifyContent: 'center'\n  },\n  md3LeadingIcon: {\n    marginLeft: 12\n  },\n  md3WithoutLeadingIcon: {\n    marginLeft: 4\n  }\n});\nexport default MenuItem;", "map": {"version": 3, "names": ["React", "StyleSheet", "View", "getContentMaxWidth", "getMenuItemColor", "MAX_WIDTH", "MIN_WIDTH", "useInternalTheme", "Icon", "TouchableRipple", "Text", "MenuItem", "_ref", "leadingIcon", "trailingIcon", "dense", "title", "disabled", "background", "onPress", "style", "containerStyle", "contentStyle", "titleStyle", "customRippleColor", "rippleColor", "_ref$testID", "testID", "accessibilityLabel", "accessibilityState", "themeOverrides", "theme", "_ref$titleMaxFontSize", "titleMaxFontSizeMultiplier", "hitSlop", "_getMenuItemColor", "titleColor", "iconColor", "isV3", "containerPadding", "iconWidth", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "titleTextStyle", "_objectSpread", "color", "fonts", "bodyLarge", "newAccessibilityState", "createElement", "styles", "container", "paddingHorizontal", "md3DenseContainer", "accessibilityRole", "row", "item", "width", "pointerEvents", "source", "size", "content", "md3LeadingIcon", "md3WithoutLeadingIcon", "variant", "selectable", "numberOfLines", "maxFontSizeMultiplier", "displayName", "create", "height", "justifyContent", "flexDirection", "fontSize", "marginHorizontal", "marginLeft"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\Menu\\MenuItem.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  AccessibilityState,\n  ColorValue,\n  GestureResponderEvent,\n  PressableAndroidRippleConfig,\n  StyleProp,\n  StyleSheet,\n  TextStyle,\n  View,\n  ViewStyle,\n} from 'react-native';\n\nimport {\n  getContentMaxWidth,\n  getMenuItemColor,\n  MAX_WIDTH,\n  MIN_WIDTH,\n} from './utils';\nimport { useInternalTheme } from '../../core/theming';\nimport type { ThemeProp } from '../../types';\nimport Icon, { IconSource } from '../Icon';\nimport TouchableRipple, {\n  Props as TouchableRippleProps,\n} from '../TouchableRipple/TouchableRipple';\nimport Text from '../Typography/Text';\n\nexport type Props = {\n  /**\n   * Title text for the `MenuItem`.\n   */\n  title: React.ReactNode;\n  /**\n   * @renamed Renamed from 'icon' to 'leadingIcon' in v5.x\n   *\n   * Leading icon to display for the `MenuItem`.\n   */\n  leadingIcon?: IconSource;\n  /**\n   * @supported Available in v5.x with theme version 3\n   *\n   * Trailing icon to display for the `MenuItem`.\n   */\n  trailingIcon?: IconSource;\n  /**\n   * Whether the 'item' is disabled. A disabled 'item' is greyed out and `onPress` is not called on touch.\n   */\n  disabled?: boolean;\n  /**\n   * @supported Available in v5.x with theme version 3\n   *\n   * Sets min height with densed layout.\n   */\n  dense?: boolean;\n  /**\n   * Type of background drawabale to display the feedback (Android).\n   * https://reactnative.dev/docs/pressable#rippleconfig\n   */\n  background?: PressableAndroidRippleConfig;\n  /**\n   * Function to execute on press.\n   */\n  onPress?: (e: GestureResponderEvent) => void;\n  /**\n   * Specifies the largest possible scale a title font can reach.\n   */\n  titleMaxFontSizeMultiplier?: number;\n  /**\n   * Style that is passed to the root TouchableRipple container.\n   * @optional\n   */\n  style?: StyleProp<ViewStyle>;\n  /**\n   * Style that is passed to the outermost container that wraps the entire content, including leading and trailing icons and title text.\n   */\n  containerStyle?: StyleProp<ViewStyle>;\n  /**\n   * Style that is passed to the content container, which wraps the title text.\n   */\n  contentStyle?: StyleProp<ViewStyle>;\n  /**\n   * Style that is passed to the Title element.\n   */\n  titleStyle?: StyleProp<TextStyle>;\n  /**\n   * Color of the ripple effect.\n   */\n  rippleColor?: ColorValue;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n  /**\n   * Sets additional distance outside of element in which a press can be detected.\n   */\n  hitSlop?: TouchableRippleProps['hitSlop'];\n  /**\n   * TestID used for testing purposes\n   */\n  testID?: string;\n  /**\n   * Accessibility label for the Touchable. This is read by the screen reader when the user taps the component.\n   */\n  accessibilityLabel?: string;\n  /**\n   * Accessibility state for the Touchable. This is read by the screen reader when the user taps the component.\n   */\n  accessibilityState?: AccessibilityState;\n};\n\n/**\n * A component to show a single list item inside a Menu.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { View } from 'react-native';\n * import { Menu } from 'react-native-paper';\n *\n * const MyComponent = () => (\n *   <View style={{ flex: 1 }}>\n *     <Menu.Item leadingIcon=\"redo\" onPress={() => {}} title=\"Redo\" />\n *     <Menu.Item leadingIcon=\"undo\" onPress={() => {}} title=\"Undo\" />\n *     <Menu.Item leadingIcon=\"content-cut\" onPress={() => {}} title=\"Cut\" disabled />\n *     <Menu.Item leadingIcon=\"content-copy\" onPress={() => {}} title=\"Copy\" disabled />\n *     <Menu.Item leadingIcon=\"content-paste\" onPress={() => {}} title=\"Paste\" />\n *   </View>\n * );\n *\n * export default MyComponent;\n * ```\n */\nconst MenuItem = ({\n  leadingIcon,\n  trailingIcon,\n  dense,\n  title,\n  disabled,\n  background,\n  onPress,\n  style,\n  containerStyle,\n  contentStyle,\n  titleStyle,\n  rippleColor: customRippleColor,\n  testID = 'menu-item',\n  accessibilityLabel,\n  accessibilityState,\n  theme: themeOverrides,\n  titleMaxFontSizeMultiplier = 1.5,\n  hitSlop,\n}: Props) => {\n  const theme = useInternalTheme(themeOverrides);\n  const { titleColor, iconColor, rippleColor } = getMenuItemColor({\n    theme,\n    disabled,\n    customRippleColor,\n  });\n  const { isV3 } = theme;\n\n  const containerPadding = isV3 ? 12 : 8;\n\n  const iconWidth = isV3 ? 24 : 40;\n\n  const minWidth = MIN_WIDTH - (isV3 ? 12 : 16);\n\n  const maxWidth = getContentMaxWidth({\n    isV3,\n    iconWidth,\n    leadingIcon,\n    trailingIcon,\n  });\n\n  const titleTextStyle = {\n    color: titleColor,\n    ...(isV3 ? theme.fonts.bodyLarge : {}),\n  };\n\n  const newAccessibilityState = { ...accessibilityState, disabled };\n\n  return (\n    <TouchableRipple\n      style={[\n        styles.container,\n        { paddingHorizontal: containerPadding },\n        dense && styles.md3DenseContainer,\n        style,\n      ]}\n      onPress={onPress}\n      disabled={disabled}\n      testID={testID}\n      background={background}\n      accessibilityLabel={accessibilityLabel}\n      accessibilityRole=\"menuitem\"\n      accessibilityState={newAccessibilityState}\n      rippleColor={rippleColor}\n      hitSlop={hitSlop}\n    >\n      <View style={[styles.row, containerStyle]}>\n        {leadingIcon ? (\n          <View\n            style={[!isV3 && styles.item, { width: iconWidth }]}\n            pointerEvents=\"box-none\"\n          >\n            <Icon source={leadingIcon} size={24} color={iconColor} />\n          </View>\n        ) : null}\n        <View\n          style={[\n            !isV3 && styles.item,\n            styles.content,\n            { minWidth, maxWidth },\n            isV3 &&\n              (leadingIcon\n                ? styles.md3LeadingIcon\n                : styles.md3WithoutLeadingIcon),\n            contentStyle,\n          ]}\n          pointerEvents=\"none\"\n        >\n          <Text\n            variant=\"bodyLarge\"\n            selectable={false}\n            numberOfLines={1}\n            testID={`${testID}-title`}\n            style={[!isV3 && styles.title, titleTextStyle, titleStyle]}\n            maxFontSizeMultiplier={titleMaxFontSizeMultiplier}\n          >\n            {title}\n          </Text>\n        </View>\n        {isV3 && trailingIcon ? (\n          <View\n            style={[!isV3 && styles.item, { width: iconWidth }]}\n            pointerEvents=\"box-none\"\n          >\n            <Icon source={trailingIcon} size={24} color={iconColor} />\n          </View>\n        ) : null}\n      </View>\n    </TouchableRipple>\n  );\n};\n\nMenuItem.displayName = 'Menu.Item';\n\nconst styles = StyleSheet.create({\n  container: {\n    minWidth: MIN_WIDTH,\n    maxWidth: MAX_WIDTH,\n    height: 48,\n    justifyContent: 'center',\n  },\n  md3DenseContainer: {\n    height: 32,\n  },\n  row: {\n    flexDirection: 'row',\n  },\n  title: {\n    fontSize: 16,\n  },\n  item: {\n    marginHorizontal: 8,\n  },\n  content: {\n    justifyContent: 'center',\n  },\n  md3LeadingIcon: {\n    marginLeft: 12,\n  },\n  md3WithoutLeadingIcon: {\n    marginLeft: 4,\n  },\n});\n\nexport default MenuItem;\n"], "mappings": ";;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAa9B,SACEC,kBAAkB,EAClBC,gBAAgB,EAChBC,SAAS,EACTC,SAAS;AAEX,SAASC,gBAAgB;AAEzB,OAAOC,IAAI;AACX,OAAOC,eAAe;AAGtB,OAAOC,IAAI;AA2GX,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAAC,IAAA,EAmBD;EAAA,IAlBXC,WAAW,GAAAD,IAAA,CAAXC,WAAW;IACXC,YAAY,GAAAF,IAAA,CAAZE,YAAY;IACZC,KAAK,GAAAH,IAAA,CAALG,KAAK;IACLC,KAAK,GAAAJ,IAAA,CAALI,KAAK;IACLC,QAAQ,GAAAL,IAAA,CAARK,QAAQ;IACRC,UAAU,GAAAN,IAAA,CAAVM,UAAU;IACVC,OAAO,GAAAP,IAAA,CAAPO,OAAO;IACPC,KAAK,GAAAR,IAAA,CAALQ,KAAK;IACLC,cAAc,GAAAT,IAAA,CAAdS,cAAc;IACdC,YAAY,GAAAV,IAAA,CAAZU,YAAY;IACZC,UAAU,GAAAX,IAAA,CAAVW,UAAU;IACGC,iBAAiB,GAAAZ,IAAA,CAA9Ba,WAAW;IAAAC,WAAA,GAAAd,IAAA,CACXe,MAAM;IAANA,MAAM,GAAAD,WAAA,cAAG,WAAW,GAAAA,WAAA;IACpBE,kBAAkB,GAAAhB,IAAA,CAAlBgB,kBAAkB;IAClBC,kBAAkB,GAAAjB,IAAA,CAAlBiB,kBAAkB;IACXC,cAAc,GAAAlB,IAAA,CAArBmB,KAAK;IAAAC,qBAAA,GAAApB,IAAA,CACLqB,0BAA0B;IAA1BA,0BAA0B,GAAAD,qBAAA,cAAG,GAAG,GAAAA,qBAAA;IAChCE,OAAA,GAAAtB,IAAA,CAAAsB,OAAA;EAEA,IAAMH,KAAK,GAAGxB,gBAAgB,CAACuB,cAAc,CAAC;EAC9C,IAAAK,iBAAA,GAA+C/B,gBAAgB,CAAC;MAC9D2B,KAAK,EAALA,KAAK;MACLd,QAAQ,EAARA,QAAQ;MACRO,iBAAA,EAAAA;IACF,CAAC,CAAC;IAJMY,UAAU,GAAAD,iBAAA,CAAVC,UAAU;IAAEC,SAAS,GAAAF,iBAAA,CAATE,SAAS;IAAEZ,WAAA,GAAAU,iBAAA,CAAAV,WAAA;EAK/B,IAAQa,IAAA,GAASP,KAAK,CAAdO,IAAA;EAER,IAAMC,gBAAgB,GAAGD,IAAI,GAAG,EAAE,GAAG,CAAC;EAEtC,IAAME,SAAS,GAAGF,IAAI,GAAG,EAAE,GAAG,EAAE;EAEhC,IAAMG,QAAQ,GAAGnC,SAAS,IAAIgC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;EAE7C,IAAMI,QAAQ,GAAGvC,kBAAkB,CAAC;IAClCmC,IAAI,EAAJA,IAAI;IACJE,SAAS,EAATA,SAAS;IACT3B,WAAW,EAAXA,WAAW;IACXC,YAAA,EAAAA;EACF,CAAC,CAAC;EAEF,IAAM6B,cAAc,GAAAC,aAAA;IAClBC,KAAK,EAAET;EAAU,GACbE,IAAI,GAAGP,KAAK,CAACe,KAAK,CAACC,SAAS,GAAG,CAAC,CAAC,CACtC;EAED,IAAMC,qBAAqB,GAAAJ,aAAA,CAAAA,aAAA,KAAQf,kBAAkB;IAAEZ,QAAA,EAAAA;EAAA,EAAU;EAEjE,OACEjB,KAAA,CAAAiD,aAAA,CAACxC,eAAe;IACdW,KAAK,EAAE,CACL8B,MAAM,CAACC,SAAS,EAChB;MAAEC,iBAAiB,EAAEb;IAAiB,CAAC,EACvCxB,KAAK,IAAImC,MAAM,CAACG,iBAAiB,EACjCjC,KAAK,CACL;IACFD,OAAO,EAAEA,OAAQ;IACjBF,QAAQ,EAAEA,QAAS;IACnBU,MAAM,EAAEA,MAAO;IACfT,UAAU,EAAEA,UAAW;IACvBU,kBAAkB,EAAEA,kBAAmB;IACvC0B,iBAAiB,EAAC,UAAU;IAC5BzB,kBAAkB,EAAEmB,qBAAsB;IAC1CvB,WAAW,EAAEA,WAAY;IACzBS,OAAO,EAAEA;EAAQ,GAEjBlC,KAAA,CAAAiD,aAAA,CAAC/C,IAAI;IAACkB,KAAK,EAAE,CAAC8B,MAAM,CAACK,GAAG,EAAElC,cAAc;EAAE,GACvCR,WAAW,GACVb,KAAA,CAAAiD,aAAA,CAAC/C,IAAI;IACHkB,KAAK,EAAE,CAAC,CAACkB,IAAI,IAAIY,MAAM,CAACM,IAAI,EAAE;MAAEC,KAAK,EAAEjB;IAAU,CAAC,CAAE;IACpDkB,aAAa,EAAC;EAAU,GAExB1D,KAAA,CAAAiD,aAAA,CAACzC,IAAI;IAACmD,MAAM,EAAE9C,WAAY;IAAC+C,IAAI,EAAE,EAAG;IAACf,KAAK,EAAER;EAAU,CAAE,CACpD,CAAC,GACL,IAAI,EACRrC,KAAA,CAAAiD,aAAA,CAAC/C,IAAI;IACHkB,KAAK,EAAE,CACL,CAACkB,IAAI,IAAIY,MAAM,CAACM,IAAI,EACpBN,MAAM,CAACW,OAAO,EACd;MAAEpB,QAAQ,EAARA,QAAQ;MAAEC,QAAA,EAAAA;IAAS,CAAC,EACtBJ,IAAI,KACDzB,WAAW,GACRqC,MAAM,CAACY,cAAc,GACrBZ,MAAM,CAACa,qBAAqB,CAAC,EACnCzC,YAAY,CACZ;IACFoC,aAAa,EAAC;EAAM,GAEpB1D,KAAA,CAAAiD,aAAA,CAACvC,IAAI;IACHsD,OAAO,EAAC,WAAW;IACnBC,UAAU,EAAE,KAAM;IAClBC,aAAa,EAAE,CAAE;IACjBvC,MAAM,EAAE,GAAGA,MAAM,QAAS;IAC1BP,KAAK,EAAE,CAAC,CAACkB,IAAI,IAAIY,MAAM,CAAClC,KAAK,EAAE2B,cAAc,EAAEpB,UAAU,CAAE;IAC3D4C,qBAAqB,EAAElC;EAA2B,GAEjDjB,KACG,CACF,CAAC,EACNsB,IAAI,IAAIxB,YAAY,GACnBd,KAAA,CAAAiD,aAAA,CAAC/C,IAAI;IACHkB,KAAK,EAAE,CAAC,CAACkB,IAAI,IAAIY,MAAM,CAACM,IAAI,EAAE;MAAEC,KAAK,EAAEjB;IAAU,CAAC,CAAE;IACpDkB,aAAa,EAAC;EAAU,GAExB1D,KAAA,CAAAiD,aAAA,CAACzC,IAAI;IAACmD,MAAM,EAAE7C,YAAa;IAAC8C,IAAI,EAAE,EAAG;IAACf,KAAK,EAAER;EAAU,CAAE,CACrD,CAAC,GACL,IACA,CACS,CAAC;AAEtB,CAAC;AAED1B,QAAQ,CAACyD,WAAW,GAAG,WAAW;AAElC,IAAMlB,MAAM,GAAGjD,UAAU,CAACoE,MAAM,CAAC;EAC/BlB,SAAS,EAAE;IACTV,QAAQ,EAAEnC,SAAS;IACnBoC,QAAQ,EAAErC,SAAS;IACnBiE,MAAM,EAAE,EAAE;IACVC,cAAc,EAAE;EAClB,CAAC;EACDlB,iBAAiB,EAAE;IACjBiB,MAAM,EAAE;EACV,CAAC;EACDf,GAAG,EAAE;IACHiB,aAAa,EAAE;EACjB,CAAC;EACDxD,KAAK,EAAE;IACLyD,QAAQ,EAAE;EACZ,CAAC;EACDjB,IAAI,EAAE;IACJkB,gBAAgB,EAAE;EACpB,CAAC;EACDb,OAAO,EAAE;IACPU,cAAc,EAAE;EAClB,CAAC;EACDT,cAAc,EAAE;IACda,UAAU,EAAE;EACd,CAAC;EACDZ,qBAAqB,EAAE;IACrBY,UAAU,EAAE;EACd;AACF,CAAC,CAAC;AAEF,eAAehE,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}