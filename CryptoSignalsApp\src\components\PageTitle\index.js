import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import styles from './styles'
import IconArrowLeft from '../../components/Icon/ArrowLeft'

const PageTitle = ({ text, goBack }) => {
  const navigation = useNavigation();

  return (
    <View style={styles.container}>
      {goBack && (
        <TouchableOpacity style={styles.backButton} onPress={() => navigation.navigate('Channels')}>
          <IconArrowLeft />
        </TouchableOpacity>
      )}
      <Text style={styles.title}>{text}</Text>
    </View>
  )
}

export default PageTitle;
