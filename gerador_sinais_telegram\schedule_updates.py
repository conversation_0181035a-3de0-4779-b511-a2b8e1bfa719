import schedule
import time
import subprocess
import logging
import datetime
import os
import sys

# Configurar logging
import io

# Criar um handler de arquivo
file_handler = logging.FileHandler('scheduler.log', encoding='utf-8')

# Criar um handler de console que lida com Unicode
class UnicodeStreamHandler(logging.StreamHandler):
    def __init__(self):
        logging.StreamHandler.__init__(self, io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8'))

    def emit(self, record):
        try:
            msg = self.format(record)
            stream = self.stream
            stream.write(msg + self.terminator)
            self.flush()
        except Exception:
            self.handleError(record)

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        UnicodeStreamHandler(),
        file_handler
    ]
)

logger = logging.getLogger(__name__)

def run_update_script(with_report=False):
    """Executa o script de atualização de resultados"""
    try:
        cmd = [sys.executable, 'update_results.py']
        if with_report:
            cmd.append('--report')

        logger.info(f"Executando: {' '.join(cmd)}")
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True
        )

        stdout, stderr = process.communicate()

        if process.returncode != 0:
            logger.error(f"Erro ao executar script (código {process.returncode}):")
            logger.error(stderr)
        else:
            logger.info("Script executado com sucesso")
            if stdout.strip():
                logger.info(f"Saída: {stdout.strip()}")
    except Exception as e:
        logger.error(f"Erro ao executar script: {e}")

def main():
    """Função principal para agendar tarefas"""
    logger.info("Iniciando agendador de atualizações de resultados")

    # Agendar atualizações a cada 15 minutos
    schedule.every(15).minutes.do(run_update_script)

    # Agendar relatório diário às 23:30
    schedule.every().day.at("23:30").do(run_update_script, with_report=True)

    # Executar imediatamente na inicialização
    run_update_script()

    try:
        while True:
            # Verificar tarefas agendadas
            schedule.run_pending()
            time.sleep(60)  # Verificar a cada minuto
    except KeyboardInterrupt:
        logger.info("Agendador interrompido pelo usuário")
    except Exception as e:
        logger.error(f"Erro no agendador: {e}")
    finally:
        logger.info("Agendador encerrado")

if __name__ == "__main__":
    main()
