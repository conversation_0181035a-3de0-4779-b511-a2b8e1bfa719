{"ast": null, "code": "export {};", "map": {"version": 3, "names": [], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\@react-navigation\\routers\\src\\types.tsx"], "sourcesContent": ["import type * as CommonActions from './CommonActions';\n\nexport type CommonNavigationAction = CommonActions.Action;\n\ntype NavigationRoute<\n  ParamList extends ParamListBase,\n  RouteName extends keyof ParamList\n> = Route<Extract<RouteName, string>, ParamList[RouteName]> & {\n  state?: NavigationState | PartialState<NavigationState>;\n};\n\nexport type NavigationState<ParamList extends ParamListBase = ParamListBase> =\n  Readonly<{\n    /**\n     * Unique key for the navigation state.\n     */\n    key: string;\n    /**\n     * Index of the currently focused route.\n     */\n    index: number;\n    /**\n     * List of valid route names as defined in the screen components.\n     */\n    routeNames: Extract<keyof ParamList, string>[];\n    /**\n     * Alternative entries for history.\n     */\n    history?: unknown[];\n    /**\n     * List of rendered routes.\n     */\n    routes: NavigationRoute<ParamList, keyof ParamList>[];\n    /**\n     * Custom type for the state, whether it's for tab, stack, drawer etc.\n     * During rehydration, the state will be discarded if type doesn't match with router type.\n     * It can also be used to detect the type of the navigator we're dealing with.\n     */\n    type: string;\n    /**\n     * Whether the navigation state has been rehydrated.\n     */\n    stale: false;\n  }>;\n\nexport type InitialState = Readonly<\n  Partial<Omit<NavigationState, 'stale' | 'routes'>> & {\n    routes: (Omit<Route<string>, 'key'> & { state?: InitialState })[];\n  }\n>;\n\nexport type PartialRoute<R extends Route<string>> = Omit<R, 'key'> & {\n  key?: string;\n  state?: PartialState<NavigationState>;\n};\n\nexport type PartialState<State extends NavigationState> = Partial<\n  Omit<State, 'stale' | 'routes'>\n> &\n  Readonly<{\n    stale?: true;\n    routes: PartialRoute<Route<State['routeNames'][number]>>[];\n  }>;\n\nexport type Route<\n  RouteName extends string,\n  Params extends object | undefined = object | undefined\n> = Readonly<{\n  /**\n   * Unique key for the route.\n   */\n  key: string;\n  /**\n   * User-provided name for the route.\n   */\n  name: RouteName;\n  /**\n   * Path associated with the route.\n   * Usually present when the screen was opened from a deep link.\n   */\n  path?: string;\n}> &\n  (undefined extends Params\n    ? Readonly<{\n        /**\n         * Params for this route\n         */\n        params?: Readonly<Params>;\n      }>\n    : Readonly<{\n        /**\n         * Params for this route\n         */\n        params: Readonly<Params>;\n      }>);\n\nexport type ParamListBase = Record<string, object | undefined>;\n\nexport type NavigationAction = Readonly<{\n  /**\n   * Type of the action (e.g. `NAVIGATE`)\n   */\n  type: string;\n  /**\n   * Additional data for the action\n   */\n  payload?: object;\n  /**\n   * Key of the route which dispatched this action.\n   */\n  source?: string;\n  /**\n   * Key of the navigator which should handle this action.\n   */\n  target?: string;\n}>;\n\nexport type ActionCreators<Action extends NavigationAction> = {\n  [key: string]: (...args: any) => Action;\n};\n\nexport type DefaultRouterOptions<RouteName extends string = string> = {\n  /**\n   * Name of the route to focus by on initial render.\n   * If not specified, usually the first route is used.\n   */\n  initialRouteName?: RouteName;\n};\n\nexport type RouterFactory<\n  State extends NavigationState,\n  Action extends NavigationAction,\n  RouterOptions extends DefaultRouterOptions\n> = (options: RouterOptions) => Router<State, Action>;\n\nexport type RouterConfigOptions = {\n  routeNames: string[];\n  routeParamList: ParamListBase;\n  routeGetIdList: Record<\n    string,\n    | ((options: { params?: Record<string, any> }) => string | undefined)\n    | undefined\n  >;\n};\n\nexport type Router<\n  State extends NavigationState,\n  Action extends NavigationAction\n> = {\n  /**\n   * Type of the router. Should match the `type` property in state.\n   * If the type doesn't match, the state will be discarded during rehydration.\n   */\n  type: State['type'];\n\n  /**\n   * Initialize the navigation state.\n   *\n   * @param options.routeNames List of valid route names as defined in the screen components.\n   * @param options.routeParamsList Object containing params for each route.\n   */\n  getInitialState(options: RouterConfigOptions): State;\n\n  /**\n   * Rehydrate the full navigation state from a given partial state.\n   *\n   * @param partialState Navigation state to rehydrate from.\n   * @param options.routeNames List of valid route names as defined in the screen components.\n   * @param options.routeParamsList Object containing params for each route.\n   */\n  getRehydratedState(\n    partialState: PartialState<State> | State,\n    options: RouterConfigOptions\n  ): State;\n\n  /**\n   * Take the current state and updated list of route names, and return a new state.\n   *\n   * @param state State object to update.\n   * @param options.routeNames New list of route names.\n   * @param options.routeParamsList Object containing params for each route.\n   */\n  getStateForRouteNamesChange(\n    state: State,\n    options: RouterConfigOptions & {\n      /**\n       * List of routes whose key has changed even if they still have the same name.\n       * This allows to remove screens declaratively.\n       */\n      routeKeyChanges: string[];\n    }\n  ): State;\n\n  /**\n   * Take the current state and key of a route, and return a new state with the route focused\n   *\n   * @param state State object to apply the action on.\n   * @param key Key of the route to focus.\n   */\n  getStateForRouteFocus(state: State, key: string): State;\n\n  /**\n   * Take the current state and action, and return a new state.\n   * If the action cannot be handled, return `null`.\n   *\n   * @param state State object to apply the action on.\n   * @param action Action object to apply.\n   * @param options.routeNames List of valid route names as defined in the screen components.\n   * @param options.routeParamsList Object containing params for each route.\n   */\n  getStateForAction(\n    state: State,\n    action: Action,\n    options: RouterConfigOptions\n  ): State | PartialState<State> | null;\n\n  /**\n   * Whether the action should also change focus in parent navigator\n   *\n   * @param action Action object to check.\n   */\n  shouldActionChangeFocus(action: NavigationAction): boolean;\n\n  /**\n   * Action creators for the router.\n   */\n  actionCreators?: ActionCreators<Action>;\n};\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}