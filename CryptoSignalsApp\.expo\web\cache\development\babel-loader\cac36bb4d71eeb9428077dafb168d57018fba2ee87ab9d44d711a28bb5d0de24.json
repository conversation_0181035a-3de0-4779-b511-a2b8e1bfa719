{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"onPress\", \"style\", \"children\", \"pointerEvents\", \"theme\"];\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nimport * as React from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport color from 'color';\nimport { useInternalTheme } from \"../../core/theming\";\nimport { black, white } from \"../../styles/themes/v2/colors\";\nimport TouchableRipple from \"../TouchableRipple/TouchableRipple\";\nvar DataTableRow = function DataTableRow(_ref) {\n  var onPress = _ref.onPress,\n    style = _ref.style,\n    children = _ref.children,\n    pointerEvents = _ref.pointerEvents,\n    themeOverrides = _ref.theme,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var theme = useInternalTheme(themeOverrides);\n  var borderBottomColor = theme.isV3 ? theme.colors.surfaceVariant : color(theme.dark ? white : black).alpha(0.12).rgb().string();\n  return React.createElement(TouchableRipple, _extends({}, rest, {\n    onPress: onPress,\n    style: [styles.container, {\n      borderBottomColor: borderBottomColor\n    }, style]\n  }), React.createElement(View, {\n    style: styles.content,\n    pointerEvents: pointerEvents\n  }, children));\n};\nDataTableRow.displayName = 'DataTable.Row';\nvar styles = StyleSheet.create({\n  container: {\n    borderStyle: 'solid',\n    borderBottomWidth: StyleSheet.hairlineWidth,\n    minHeight: 48,\n    paddingHorizontal: 16\n  },\n  content: {\n    flex: 1,\n    flexDirection: 'row'\n  }\n});\nexport default DataTableRow;\nexport { DataTableRow };", "map": {"version": 3, "names": ["React", "StyleSheet", "View", "color", "useInternalTheme", "black", "white", "TouchableRipple", "DataTableRow", "_ref", "onPress", "style", "children", "pointerEvents", "themeOverrides", "theme", "rest", "_objectWithoutProperties", "_excluded", "borderBottomColor", "isV3", "colors", "surfaceVariant", "dark", "alpha", "rgb", "string", "createElement", "_extends", "styles", "container", "content", "displayName", "create", "borderStyle", "borderBottomWidth", "hairlineWidth", "minHeight", "paddingHorizontal", "flex", "flexDirection"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\DataTable\\DataTableRow.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  GestureResponderEvent,\n  StyleProp,\n  StyleSheet,\n  View,\n  ViewProps,\n  ViewStyle,\n} from 'react-native';\n\nimport color from 'color';\n\nimport { useInternalTheme } from '../../core/theming';\nimport { black, white } from '../../styles/themes/v2/colors';\nimport type { $RemoveChildren, ThemeProp } from '../../types';\nimport TouchableRipple from '../TouchableRipple/TouchableRipple';\n\nexport type Props = $RemoveChildren<typeof TouchableRipple> & {\n  /**\n   * Content of the `DataTableRow`.\n   */\n  children: React.ReactNode;\n  /**\n   * Function to execute on press.\n   */\n  onPress?: (e: GestureResponderEvent) => void;\n  style?: StyleProp<ViewStyle>;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n  /**\n   * `pointerEvents` passed to the `View` container, which is wrapping children within `TouchableRipple`.\n   */\n  pointerEvents?: ViewProps['pointerEvents'];\n};\n\n/**\n * A component to show a single row inside of a table.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { DataTable } from 'react-native-paper';\n *\n * const MyComponent = () => (\n *      <DataTable.Row>\n *        <DataTable.Cell numeric>1</DataTable.Cell>\n *        <DataTable.Cell numeric>2</DataTable.Cell>\n *        <DataTable.Cell numeric>3</DataTable.Cell>\n *        <DataTable.Cell numeric>4</DataTable.Cell>\n *      </DataTable.Row>\n * );\n *\n * export default MyComponent;\n * ```\n *\n * @extends TouchableRipple props https://callstack.github.io/react-native-paper/docs/components/TouchableRipple\n */\nconst DataTableRow = ({\n  onPress,\n  style,\n  children,\n  pointerEvents,\n  theme: themeOverrides,\n  ...rest\n}: Props) => {\n  const theme = useInternalTheme(themeOverrides);\n  const borderBottomColor = theme.isV3\n    ? theme.colors.surfaceVariant\n    : color(theme.dark ? white : black)\n        .alpha(0.12)\n        .rgb()\n        .string();\n\n  return (\n    <TouchableRipple\n      {...rest}\n      onPress={onPress}\n      style={[styles.container, { borderBottomColor }, style]}\n    >\n      <View style={styles.content} pointerEvents={pointerEvents}>\n        {children}\n      </View>\n    </TouchableRipple>\n  );\n};\n\nDataTableRow.displayName = 'DataTable.Row';\n\nconst styles = StyleSheet.create({\n  container: {\n    borderStyle: 'solid',\n    borderBottomWidth: StyleSheet.hairlineWidth,\n    minHeight: 48,\n    paddingHorizontal: 16,\n  },\n  content: {\n    flex: 1,\n    flexDirection: 'row',\n  },\n});\n\nexport default DataTableRow;\n\n// @component-docs ignore-next-line\nexport { DataTableRow };\n"], "mappings": ";;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAU9B,OAAOC,KAAK,MAAM,OAAO;AAEzB,SAASC,gBAAgB;AACzB,SAASC,KAAK,EAAEC,KAAK;AAErB,OAAOC,eAAe;AA4CtB,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAAC,IAAA,EAOL;EAAA,IANXC,OAAO,GAAAD,IAAA,CAAPC,OAAO;IACPC,KAAK,GAAAF,IAAA,CAALE,KAAK;IACLC,QAAQ,GAAAH,IAAA,CAARG,QAAQ;IACRC,aAAa,GAAAJ,IAAA,CAAbI,aAAa;IACNC,cAAc,GAAAL,IAAA,CAArBM,KAAK;IACFC,IAAA,GAAAC,wBAAA,CAAAR,IAAA,EAAAS,SAAA;EAEH,IAAMH,KAAK,GAAGX,gBAAgB,CAACU,cAAc,CAAC;EAC9C,IAAMK,iBAAiB,GAAGJ,KAAK,CAACK,IAAI,GAChCL,KAAK,CAACM,MAAM,CAACC,cAAc,GAC3BnB,KAAK,CAACY,KAAK,CAACQ,IAAI,GAAGjB,KAAK,GAAGD,KAAK,CAAC,CAC9BmB,KAAK,CAAC,IAAI,CAAC,CACXC,GAAG,CAAC,CAAC,CACLC,MAAM,CAAC,CAAC;EAEf,OACE1B,KAAA,CAAA2B,aAAA,CAACpB,eAAe,EAAAqB,QAAA,KACVZ,IAAI;IACRN,OAAO,EAAEA,OAAQ;IACjBC,KAAK,EAAE,CAACkB,MAAM,CAACC,SAAS,EAAE;MAAEX,iBAAA,EAAAA;IAAkB,CAAC,EAAER,KAAK;EAAE,IAExDX,KAAA,CAAA2B,aAAA,CAACzB,IAAI;IAACS,KAAK,EAAEkB,MAAM,CAACE,OAAQ;IAAClB,aAAa,EAAEA;EAAc,GACvDD,QACG,CACS,CAAC;AAEtB,CAAC;AAEDJ,YAAY,CAACwB,WAAW,GAAG,eAAe;AAE1C,IAAMH,MAAM,GAAG5B,UAAU,CAACgC,MAAM,CAAC;EAC/BH,SAAS,EAAE;IACTI,WAAW,EAAE,OAAO;IACpBC,iBAAiB,EAAElC,UAAU,CAACmC,aAAa;IAC3CC,SAAS,EAAE,EAAE;IACbC,iBAAiB,EAAE;EACrB,CAAC;EACDP,OAAO,EAAE;IACPQ,IAAI,EAAE,CAAC;IACPC,aAAa,EAAE;EACjB;AACF,CAAC,CAAC;AAEF,eAAehC,YAAY;AAG3B,SAASA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}