{"gerador_sinais": {"status": "active", "details": "● gerador_sinais.service - Gerador de Sinais Telegram\n     Loaded: loaded (/etc/systemd/system/gerador_sinais.service; enabled; preset: enabled)\n     Active: active (running) since Sun 2025-04-13 11:34:29 -03; 2s ago\n   Main PID: 1736806 (python)\n      Tasks: 3 (limit: 4581)\n     Memory: 83.8M (peak: 84.3M)\n        CPU: 1.858s\n     CGroup: /system.slice/gerador_sinais.service\n             └─1736806 /opt/gerador_sinais_telegram/venv/bin/python main.py\n\nApr 13 11:34:31 ubuntu python[1736806]:   File \"/opt/gerador_sinais_telegram/utils/signal_formatter.py\", line 84, in format_breakout_signal\nApr 13 11:34:31 ubuntu python[1736806]:     f\"Entry: {entry_price:.5f}\\n\"\nApr 13 11:34:31 ubuntu python[1736806]:              ^^^^^^^^^^^^^^^^^\nApr 13 11:34:31 ubuntu python[1736806]: ValueError: Unknown format code 'f' for object of type 'str'\nApr 13 11:34:31 ubuntu python[1736806]: 2025-04-13 11:34:31,812 - telethon.network.mtprotosender - INFO - Disconnecting from 149.154.175.57:443/TcpFull...\nApr 13 11:34:31 ubuntu python[1736806]: 2025-04-13 11:34:31,812 - telethon.network.mtprotosender - INFO - Disconnection from 149.154.175.57:443/TcpFull complete!\nApr 13 11:34:31 ubuntu python[1736806]: 2025-04-13 11:34:31,816 - utils.telegram_sender - INFO - Cliente Telegram desconectado\nApr 13 11:34:31 ubuntu python[1736806]: 2025-04-13 11:34:31,816 - __main__ - INFO - Gerador de sinais encerrado.\nApr 13 11:34:32 ubuntu systemd[1]: gerador_sinais.service: Deactivated successfully.\nApr 13 11:34:32 ubuntu systemd[1]: gerador_sinais.service: Consumed 2.111s CPU time, 84.3M memory peak, 0B memory swap peak.", "resource_usage": "", "recent_logs": "ValueError: Unknown format code 'f' for object of type 'str'\n2025-04-13 11:34:31,812 - telethon.network.mtprotosender - INFO - Disconnecting from 149.154.175.57:443/TcpFull...\n2025-04-13 11:34:31,812 - telethon.network.mtprotosender - INFO - Disconnection from 149.154.175.57:443/TcpFull complete!\n2025-04-13 11:34:31,816 - utils.telegram_sender - INFO - Cliente Telegram desconectado\n2025-04-13 11:34:31,816 - __main__ - INFO - Gerador de sinais encerrado.", "timestamp": "2025-04-13T11:34:41.001866"}, "sistema-clientes": {"status": "active", "details": "● sistema-clientes.service - Sistema de Clientes Streamlit\n     Loaded: loaded (/etc/systemd/system/sistema-clientes.service; enabled; preset: enabled)\n     Active: active (running) since Thu 2025-04-03 06:09:13 -03; 1 week 3 days ago\n   Main PID: 763505 (streamlit)\n      Tasks: 9 (limit: 4581)\n     Memory: 159.1M (peak: 328.2M swap: 19.8M swap peak: 81.1M)\n        CPU: 4min 56.217s\n     CGroup: /system.slice/sistema-clientes.service\n             └─763505 /opt/sistema_clientes/venv/bin/python3 /opt/sistema_clientes/venv/bin/streamlit run main.py\n\nApr 13 09:34:03 ubuntu streamlit[763505]: 2025-04-13 09:34:03.175 200 GET /favicon.png (127.0.0.1) 1.12ms\nApr 13 09:48:59 ubuntu streamlit[763505]: 2025-04-13 09:48:59.176 200 GET / (127.0.0.1) 1.87ms\nApr 13 09:54:48 ubuntu streamlit[763505]: 2025-04-13 09:54:48.132 200 GET / (127.0.0.1) 3.67ms\nApr 13 09:56:57 ubuntu streamlit[763505]: 2025-04-13 09:56:57.045 200 GET /.env (127.0.0.1) 2.54ms\nApr 13 09:56:57 ubuntu streamlit[763505]: 2025-04-13 09:56:57.214 200 GET /.git/config (127.0.0.1) 2.56ms\nApr 13 10:04:29 ubuntu streamlit[763505]: 2025-04-13 10:04:29.707 200 GET / (127.0.0.1) 2.78ms\nApr 13 10:27:44 ubuntu streamlit[763505]: 2025-04-13 10:27:44.135 200 GET / (127.0.0.1) 3.09ms\nApr 13 10:36:34 ubuntu streamlit[763505]: 2025-04-13 10:36:34.333 200 GET / (127.0.0.1) 2.75ms\nApr 13 11:09:47 ubuntu streamlit[763505]: 2025-04-13 11:09:47.512 200 GET / (127.0.0.1) 14.34ms\nApr 13 11:13:23 ubuntu streamlit[763505]: 2025-04-13 11:13:23.826 200 GET / (162.216.149.19) 8.79ms", "resource_usage": "%CPU %MEM\n 0.0  3.9", "recent_logs": "Apr 13 10:04:29 ubuntu streamlit[763505]: 2025-04-13 10:04:29.707 200 GET / (127.0.0.1) 2.78ms\nApr 13 10:27:44 ubuntu streamlit[763505]: 2025-04-13 10:27:44.135 200 GET / (127.0.0.1) 3.09ms\nApr 13 10:36:34 ubuntu streamlit[763505]: 2025-04-13 10:36:34.333 200 GET / (127.0.0.1) 2.75ms\nApr 13 11:09:47 ubuntu streamlit[763505]: 2025-04-13 11:09:47.512 200 GET / (127.0.0.1) 14.34ms\nApr 13 11:13:23 ubuntu streamlit[763505]: 2025-04-13 11:13:23.826 200 GET / (162.216.149.19) 8.79ms", "timestamp": "2025-04-13T11:34:50.404042"}, "resources": {"memory": "               total        used        free      shared  buff/cache   available\nMem:            3888        2954         110           8        1073         933\nSwap:            486         486           0\n", "disk": "Filesystem      Size  Used Avail Use% Mounted on\n/dev/md127      389G   30G  344G   8% /\n", "timestamp": "2025-04-13T11:34:51.315695"}}