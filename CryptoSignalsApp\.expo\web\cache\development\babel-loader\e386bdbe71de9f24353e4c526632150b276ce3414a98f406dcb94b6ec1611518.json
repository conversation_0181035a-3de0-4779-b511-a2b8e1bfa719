{"ast": null, "code": "import * as React from 'react';\nexport default React.createContext(undefined);", "map": {"version": 3, "names": ["React", "createContext", "undefined"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\@react-navigation\\stack\\src\\utils\\CardAnimationContext.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport type { StackCardInterpolationProps } from '../types';\n\nexport default React.createContext<StackCardInterpolationProps | undefined>(\n  undefined\n);\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAI9B,eAAeA,KAAK,CAACC,aAAa,CAChCC,SAAS,CACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}