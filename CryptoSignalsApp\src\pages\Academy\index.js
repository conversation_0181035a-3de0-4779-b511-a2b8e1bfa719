import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, ScrollView, Alert } from 'react-native';
import { Button, ProgressBar, Searchbar } from 'react-native-paper';
import PageTitle from '../../components/PageTitle';
import Wrapper from '../../components/Wrapper';
import Card from '../../components/Card';
import StatCard from '../../components/StatCard';
import { SkeletonCard } from '../../components/LoadingSkeleton';

const Academy = ({ navigation }) => {
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [userProgress, setUserProgress] = useState({
    completedCourses: 3,
    totalCourses: 12,
    currentStreak: 7,
    totalHours: 24
  });

  const [courses, setCourses] = useState([
    {
      id: 1,
      title: 'Cryptocurrency Fundamentals',
      description: 'Learn the basics of blockchain technology and cryptocurrencies',
      level: 'Beginner',
      duration: '2 hours',
      lessons: 8,
      progress: 100,
      completed: true,
      icon: '₿',
      category: 'Basics'
    },
    {
      id: 2,
      title: 'Technical Analysis Mastery',
      description: 'Master chart patterns, indicators, and trading strategies',
      level: 'Intermediate',
      duration: '4 hours',
      lessons: 12,
      progress: 65,
      completed: false,
      icon: '📈',
      category: 'Trading'
    },
    {
      id: 3,
      title: 'DeFi Deep Dive',
      description: 'Understand decentralized finance protocols and yield farming',
      level: 'Advanced',
      duration: '3 hours',
      lessons: 10,
      progress: 30,
      completed: false,
      icon: '🏦',
      category: 'DeFi'
    },
    {
      id: 4,
      title: 'Risk Management Strategies',
      description: 'Learn how to protect your capital and manage trading risks',
      level: 'Intermediate',
      duration: '2.5 hours',
      lessons: 9,
      progress: 0,
      completed: false,
      icon: '🛡️',
      category: 'Trading'
    },
    {
      id: 5,
      title: 'NFT Marketplace Guide',
      description: 'Navigate the world of non-fungible tokens and digital collectibles',
      level: 'Beginner',
      duration: '1.5 hours',
      lessons: 6,
      progress: 0,
      completed: false,
      icon: '🎨',
      category: 'NFTs'
    },
    {
      id: 6,
      title: 'Advanced Portfolio Management',
      description: 'Optimize your crypto portfolio with professional strategies',
      level: 'Advanced',
      duration: '3.5 hours',
      lessons: 14,
      progress: 0,
      completed: false,
      icon: '📊',
      category: 'Trading'
    }
  ]);

  useEffect(() => {
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  }, []);

  const filteredCourses = courses.filter(course =>
    course.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    course.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    course.category.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const startCourse = (courseId) => {
    Alert.alert("Course", `Starting course ${courseId}. This feature will be available soon!`);
  };

  const continueCourse = (courseId) => {
    Alert.alert("Course", `Continuing course ${courseId}. This feature will be available soon!`);
  };

  if (loading) {
    return (
      <Wrapper>
        <PageTitle text="Crypto Academy" />
        <ScrollView style={{ padding: 16 }}>
          <SkeletonCard />
          <SkeletonCard />
          <SkeletonCard />
        </ScrollView>
      </Wrapper>
    );
  }

  return (
    <Wrapper>
      <ScrollView style={{ flex: 1 }}>
        {/* Header */}
        <View style={{ padding: 16, paddingBottom: 8 }}>
          <Text style={{
            color: '#fff',
            fontSize: 28,
            fontFamily: 'Poppins_700Bold',
            marginBottom: 4
          }}>
            Crypto Academy 🎓
          </Text>
          <Text style={{
            color: '#8a8a8a',
            fontSize: 14,
            fontFamily: 'Poppins_400Regular'
          }}>
            Master cryptocurrency trading and blockchain technology
          </Text>
        </View>

        {/* Progress Stats */}
        <View style={{ paddingHorizontal: 8, marginBottom: 16 }}>
          <Text style={{
            color: '#fff',
            fontSize: 18,
            fontFamily: 'Poppins_600SemiBold',
            marginBottom: 12,
            paddingHorizontal: 8
          }}>
            Your Progress
          </Text>
          <View style={{ flexDirection: 'row', flexWrap: 'wrap' }}>
            <StatCard
              title="Completed Courses"
              value={`${userProgress.completedCourses}/${userProgress.totalCourses}`}
              subtitle="Keep learning!"
              icon="🏆"
            />
            <StatCard
              title="Learning Streak"
              value={`${userProgress.currentStreak} days`}
              change="+2 days"
              changeType="positive"
              icon="🔥"
            />
            <StatCard
              title="Study Hours"
              value={`${userProgress.totalHours}h`}
              subtitle="This month"
              icon="⏱️"
            />
            <StatCard
              title="Skill Level"
              value="Intermediate"
              subtitle="Keep improving"
              icon="📚"
            />
          </View>
        </View>

        {/* Search Bar */}
        <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>
          <Searchbar
            placeholder="Search courses..."
            onChangeText={setSearchQuery}
            value={searchQuery}
            style={{ backgroundColor: '#2a2a2a', elevation: 0 }}
            inputStyle={{ color: '#fff' }}
            iconColor="#8a8a8a"
            placeholderTextColor="#8a8a8a"
          />
        </View>

        {/* Continue Learning */}
        <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>
          <Text style={{
            color: '#fff',
            fontSize: 18,
            fontFamily: 'Poppins_600SemiBold',
            marginBottom: 12
          }}>
            Continue Learning 📖
          </Text>

          {courses.filter(course => course.progress > 0 && !course.completed).map((course) => (
            <Card key={course.id} style={{ marginBottom: 12 }}>
              <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
                <View style={{
                  backgroundColor: '#333',
                  borderRadius: 8,
                  padding: 8,
                  marginRight: 12,
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <Text style={{ fontSize: 20 }}>{course.icon}</Text>
                </View>
                <View style={{ flex: 1 }}>
                  <Text style={{
                    color: '#fff',
                    fontSize: 16,
                    fontFamily: 'Poppins_600SemiBold',
                    marginBottom: 2
                  }}>
                    {course.title}
                  </Text>
                  <Text style={{
                    color: '#8a8a8a',
                    fontSize: 12,
                    fontFamily: 'Poppins_400Regular'
                  }}>
                    {course.progress}% complete • {course.duration}
                  </Text>
                </View>
              </View>
              <ProgressBar
                progress={course.progress / 100}
                color="#FECB37"
                style={{ height: 6, borderRadius: 3, marginBottom: 12 }}
              />
              <Button
                mode="contained"
                onPress={() => continueCourse(course.id)}
                style={{ backgroundColor: '#FECB37' }}
                labelStyle={{ color: '#000', fontFamily: 'Poppins_500Medium' }}
              >
                Continue Course
              </Button>
            </Card>
          ))}
        </View>

        {/* All Courses */}
        <View style={{ paddingHorizontal: 16, marginBottom: 20 }}>
          <Text style={{
            color: '#fff',
            fontSize: 18,
            fontFamily: 'Poppins_600SemiBold',
            marginBottom: 12
          }}>
            All Courses
          </Text>

          {filteredCourses.map((course) => (
            <Card key={course.id} style={{ marginBottom: 12 }}>
              <View style={{ flexDirection: 'row', alignItems: 'flex-start' }}>
                <View style={{
                  backgroundColor: '#333',
                  borderRadius: 12,
                  padding: 12,
                  marginRight: 12,
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <Text style={{ fontSize: 24 }}>{course.icon}</Text>
                </View>

                <View style={{ flex: 1 }}>
                  <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 4 }}>
                    <View style={{
                      backgroundColor: course.level === 'Beginner' ? '#4CAF50' :
                                     course.level === 'Intermediate' ? '#FECB37' : '#F44336',
                      paddingHorizontal: 6,
                      paddingVertical: 2,
                      borderRadius: 4,
                      marginRight: 8
                    }}>
                      <Text style={{
                        color: course.level === 'Intermediate' ? '#000' : '#fff',
                        fontSize: 10,
                        fontFamily: 'Poppins_600SemiBold'
                      }}>
                        {course.level}
                      </Text>
                    </View>
                    {course.completed && (
                      <Text style={{ fontSize: 12 }}>✅</Text>
                    )}
                  </View>

                  <Text style={{
                    color: '#fff',
                    fontSize: 16,
                    fontFamily: 'Poppins_600SemiBold',
                    marginBottom: 4,
                    lineHeight: 22
                  }}>
                    {course.title}
                  </Text>

                  <Text style={{
                    color: '#ccc',
                    fontSize: 13,
                    fontFamily: 'Poppins_400Regular',
                    marginBottom: 8,
                    lineHeight: 18
                  }}>
                    {course.description}
                  </Text>

                  <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
                    <View>
                      <Text style={{
                        color: '#8a8a8a',
                        fontSize: 11,
                        fontFamily: 'Poppins_400Regular'
                      }}>
                        {course.lessons} lessons • {course.duration}
                      </Text>
                      <Text style={{
                        color: '#8a8a8a',
                        fontSize: 11,
                        fontFamily: 'Poppins_400Regular'
                      }}>
                        Category: {course.category}
                      </Text>
                    </View>
                  </View>

                  {course.progress > 0 && (
                    <View style={{ marginBottom: 8 }}>
                      <ProgressBar
                        progress={course.progress / 100}
                        color="#FECB37"
                        style={{ height: 4, borderRadius: 2 }}
                      />
                    </View>
                  )}

                  <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                    <Button
                      mode={course.completed ? "outlined" : "contained"}
                      onPress={() => course.completed ? Alert.alert("Completed", "You've already completed this course!") :
                                    course.progress > 0 ? continueCourse(course.id) : startCourse(course.id)}
                      style={{
                        flex: 1,
                        backgroundColor: course.completed ? 'transparent' : '#FECB37',
                        borderColor: course.completed ? '#4CAF50' : 'transparent'
                      }}
                      labelStyle={{
                        color: course.completed ? '#4CAF50' : '#000',
                        fontFamily: 'Poppins_500Medium',
                        fontSize: 12
                      }}
                    >
                      {course.completed ? 'Completed' : course.progress > 0 ? 'Continue' : 'Start Course'}
                    </Button>
                  </View>
                </View>
              </View>
            </Card>
          ))}
        </View>
      </ScrollView>
    </Wrapper>
  );
};

export default Academy;