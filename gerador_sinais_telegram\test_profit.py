from utils.signal_formatter import SignalFormatter

print("Testando format_profit_message...")

try:
    message = SignalFormatter.format_profit_message(
        'CAKEUSDT',
        'SHORT', 
        20,
        2.30339,
        40,
        2.35040
    )
    
    print("Mensagem de profit gerada:")
    print("=" * 50)
    print(message)
    print("=" * 50)
    
    if 'CRYPTOSIGNALS PROFESSIONAL' in message:
        print("SUCESSO: Formatacao profissional de profit funcionando!")
    else:
        print("ERRO: Formatacao de profit nao esta correta")
        
except Exception as e:
    print("ERRO:", str(e))
    import traceback
    traceback.print_exc()
