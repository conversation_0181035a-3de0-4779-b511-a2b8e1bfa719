{"ast": null, "code": "'use strict';\n\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _objectSpread from \"@babel/runtime/helpers/objectSpread2\";\nimport _createForOfIteratorHelperLoose from \"@babel/runtime/helpers/createForOfIteratorHelperLoose\";\nimport invariant from 'fbjs/lib/invariant';\nvar ViewabilityHelper = function () {\n  function ViewabilityHelper(config) {\n    _classCallCheck(this, ViewabilityHelper);\n    if (config === void 0) {\n      config = {\n        viewAreaCoveragePercentThreshold: 0\n      };\n    }\n    this._hasInteracted = false;\n    this._timers = new Set();\n    this._viewableIndices = [];\n    this._viewableItems = new Map();\n    this._config = config;\n  }\n  return _createClass(ViewabilityHelper, [{\n    key: \"dispose\",\n    value: function dispose() {\n      this._timers.forEach(clearTimeout);\n    }\n  }, {\n    key: \"computeViewableItems\",\n    value: function computeViewableItems(props, scrollOffset, viewportHeight, getFrameMetrics, renderRange) {\n      var itemCount = props.getItemCount(props.data);\n      var _this$_config = this._config,\n        itemVisiblePercentThreshold = _this$_config.itemVisiblePercentThreshold,\n        viewAreaCoveragePercentThreshold = _this$_config.viewAreaCoveragePercentThreshold;\n      var viewAreaMode = viewAreaCoveragePercentThreshold != null;\n      var viewablePercentThreshold = viewAreaMode ? viewAreaCoveragePercentThreshold : itemVisiblePercentThreshold;\n      invariant(viewablePercentThreshold != null && itemVisiblePercentThreshold != null !== (viewAreaCoveragePercentThreshold != null), 'Must set exactly one of itemVisiblePercentThreshold or viewAreaCoveragePercentThreshold');\n      var viewableIndices = [];\n      if (itemCount === 0) {\n        return viewableIndices;\n      }\n      var firstVisible = -1;\n      var _ref = renderRange || {\n          first: 0,\n          last: itemCount - 1\n        },\n        first = _ref.first,\n        last = _ref.last;\n      if (last >= itemCount) {\n        console.warn('Invalid render range computing viewability ' + JSON.stringify({\n          renderRange: renderRange,\n          itemCount: itemCount\n        }));\n        return [];\n      }\n      for (var idx = first; idx <= last; idx++) {\n        var metrics = getFrameMetrics(idx, props);\n        if (!metrics) {\n          continue;\n        }\n        var top = metrics.offset - scrollOffset;\n        var bottom = top + metrics.length;\n        if (top < viewportHeight && bottom > 0) {\n          firstVisible = idx;\n          if (_isViewable(viewAreaMode, viewablePercentThreshold, top, bottom, viewportHeight, metrics.length)) {\n            viewableIndices.push(idx);\n          }\n        } else if (firstVisible >= 0) {\n          break;\n        }\n      }\n      return viewableIndices;\n    }\n  }, {\n    key: \"onUpdate\",\n    value: function onUpdate(props, scrollOffset, viewportHeight, getFrameMetrics, createViewToken, onViewableItemsChanged, renderRange) {\n      var _this = this;\n      var itemCount = props.getItemCount(props.data);\n      if (this._config.waitForInteraction && !this._hasInteracted || itemCount === 0 || !getFrameMetrics(0, props)) {\n        return;\n      }\n      var viewableIndices = [];\n      if (itemCount) {\n        viewableIndices = this.computeViewableItems(props, scrollOffset, viewportHeight, getFrameMetrics, renderRange);\n      }\n      if (this._viewableIndices.length === viewableIndices.length && this._viewableIndices.every(function (v, ii) {\n        return v === viewableIndices[ii];\n      })) {\n        return;\n      }\n      this._viewableIndices = viewableIndices;\n      if (this._config.minimumViewTime) {\n        var handle = setTimeout(function () {\n          _this._timers.delete(handle);\n          _this._onUpdateSync(props, viewableIndices, onViewableItemsChanged, createViewToken);\n        }, this._config.minimumViewTime);\n        this._timers.add(handle);\n      } else {\n        this._onUpdateSync(props, viewableIndices, onViewableItemsChanged, createViewToken);\n      }\n    }\n  }, {\n    key: \"resetViewableIndices\",\n    value: function resetViewableIndices() {\n      this._viewableIndices = [];\n    }\n  }, {\n    key: \"recordInteraction\",\n    value: function recordInteraction() {\n      this._hasInteracted = true;\n    }\n  }, {\n    key: \"_onUpdateSync\",\n    value: function _onUpdateSync(props, viewableIndicesToCheck, onViewableItemsChanged, createViewToken) {\n      var _this2 = this;\n      viewableIndicesToCheck = viewableIndicesToCheck.filter(function (ii) {\n        return _this2._viewableIndices.includes(ii);\n      });\n      var prevItems = this._viewableItems;\n      var nextItems = new Map(viewableIndicesToCheck.map(function (ii) {\n        var viewable = createViewToken(ii, true, props);\n        return [viewable.key, viewable];\n      }));\n      var changed = [];\n      for (var _iterator = _createForOfIteratorHelperLoose(nextItems), _step; !(_step = _iterator()).done;) {\n        var _step$value = _step.value,\n          key = _step$value[0],\n          viewable = _step$value[1];\n        if (!prevItems.has(key)) {\n          changed.push(viewable);\n        }\n      }\n      for (var _iterator2 = _createForOfIteratorHelperLoose(prevItems), _step2; !(_step2 = _iterator2()).done;) {\n        var _step2$value = _step2.value,\n          _key = _step2$value[0],\n          _viewable = _step2$value[1];\n        if (!nextItems.has(_key)) {\n          changed.push(_objectSpread(_objectSpread({}, _viewable), {}, {\n            isViewable: false\n          }));\n        }\n      }\n      if (changed.length > 0) {\n        this._viewableItems = nextItems;\n        onViewableItemsChanged({\n          viewableItems: Array.from(nextItems.values()),\n          changed: changed,\n          viewabilityConfig: this._config\n        });\n      }\n    }\n  }]);\n}();\nfunction _isViewable(viewAreaMode, viewablePercentThreshold, top, bottom, viewportHeight, itemLength) {\n  if (_isEntirelyVisible(top, bottom, viewportHeight)) {\n    return true;\n  } else {\n    var pixels = _getPixelsVisible(top, bottom, viewportHeight);\n    var percent = 100 * (viewAreaMode ? pixels / viewportHeight : pixels / itemLength);\n    return percent >= viewablePercentThreshold;\n  }\n}\nfunction _getPixelsVisible(top, bottom, viewportHeight) {\n  var visibleHeight = Math.min(bottom, viewportHeight) - Math.max(top, 0);\n  return Math.max(0, visibleHeight);\n}\nfunction _isEntirelyVisible(top, bottom, viewportHeight) {\n  return top >= 0 && bottom <= viewportHeight && bottom > top;\n}\nexport default ViewabilityHelper;", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "_objectSpread", "_createForOfIteratorHelperLoose", "invariant", "ViewabilityHelper", "config", "viewAreaCoveragePercentThreshold", "_hasInteracted", "_timers", "Set", "_viewableIndices", "_viewableItems", "Map", "_config", "key", "value", "dispose", "for<PERSON>ach", "clearTimeout", "computeViewableItems", "props", "scrollOffset", "viewportHeight", "getFrameMetrics", "renderRange", "itemCount", "getItemCount", "data", "_this$_config", "itemVisiblePercentThreshold", "viewAreaMode", "viewablePercentThreshold", "viewableIndices", "firstVisible", "_ref", "first", "last", "console", "warn", "JSON", "stringify", "idx", "metrics", "top", "offset", "bottom", "length", "_isViewable", "push", "onUpdate", "createViewToken", "onViewableItemsChanged", "_this", "waitForInteraction", "every", "v", "ii", "minimumViewTime", "handle", "setTimeout", "delete", "_onUpdateSync", "add", "resetViewableIndices", "recordInteraction", "viewableIndicesToCheck", "_this2", "filter", "includes", "prevItems", "nextItems", "map", "viewable", "changed", "_iterator", "_step", "done", "_step$value", "has", "_iterator2", "_step2", "_step2$value", "_key", "_viewable", "isViewable", "viewableItems", "Array", "from", "values", "viewabilityConfig", "itemLength", "_isEntirelyVisible", "pixels", "_getPixelsVisible", "percent", "visibleHeight", "Math", "min", "max"], "sources": ["E:/CryptoSignalsApp/node_modules/react-native-web/dist/vendor/react-native/ViewabilityHelper/index.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use strict';\n\nimport _objectSpread from \"@babel/runtime/helpers/objectSpread2\";\nimport _createForOfIteratorHelperLoose from \"@babel/runtime/helpers/createForOfIteratorHelperLoose\";\nimport invariant from 'fbjs/lib/invariant';\n/**\n * A Utility class for calculating viewable items based on current metrics like scroll position and\n * layout.\n *\n * An item is said to be in a \"viewable\" state when any of the following\n * is true for longer than `minimumViewTime` milliseconds (after an interaction if `waitForInteraction`\n * is true):\n *\n * - Occupying >= `viewAreaCoveragePercentThreshold` of the view area XOR fraction of the item\n *   visible in the view area >= `itemVisiblePercentThreshold`.\n * - Entirely visible on screen\n */\nclass ViewabilityHelper {\n  constructor(config) {\n    if (config === void 0) {\n      config = {\n        viewAreaCoveragePercentThreshold: 0\n      };\n    }\n    this._hasInteracted = false;\n    this._timers = new Set();\n    this._viewableIndices = [];\n    this._viewableItems = new Map();\n    this._config = config;\n  }\n\n  /**\n   * Cleanup, e.g. on unmount. Clears any pending timers.\n   */\n  dispose() {\n    /* $FlowFixMe[incompatible-call] (>=0.63.0 site=react_native_fb) This\n     * comment suppresses an error found when Flow v0.63 was deployed. To see\n     * the error delete this comment and run Flow. */\n    this._timers.forEach(clearTimeout);\n  }\n\n  /**\n   * Determines which items are viewable based on the current metrics and config.\n   */\n  computeViewableItems(props, scrollOffset, viewportHeight, getFrameMetrics,\n  // Optional optimization to reduce the scan size\n  renderRange) {\n    var itemCount = props.getItemCount(props.data);\n    var _this$_config = this._config,\n      itemVisiblePercentThreshold = _this$_config.itemVisiblePercentThreshold,\n      viewAreaCoveragePercentThreshold = _this$_config.viewAreaCoveragePercentThreshold;\n    var viewAreaMode = viewAreaCoveragePercentThreshold != null;\n    var viewablePercentThreshold = viewAreaMode ? viewAreaCoveragePercentThreshold : itemVisiblePercentThreshold;\n    invariant(viewablePercentThreshold != null && itemVisiblePercentThreshold != null !== (viewAreaCoveragePercentThreshold != null), 'Must set exactly one of itemVisiblePercentThreshold or viewAreaCoveragePercentThreshold');\n    var viewableIndices = [];\n    if (itemCount === 0) {\n      return viewableIndices;\n    }\n    var firstVisible = -1;\n    var _ref = renderRange || {\n        first: 0,\n        last: itemCount - 1\n      },\n      first = _ref.first,\n      last = _ref.last;\n    if (last >= itemCount) {\n      console.warn('Invalid render range computing viewability ' + JSON.stringify({\n        renderRange,\n        itemCount\n      }));\n      return [];\n    }\n    for (var idx = first; idx <= last; idx++) {\n      var metrics = getFrameMetrics(idx, props);\n      if (!metrics) {\n        continue;\n      }\n      var top = metrics.offset - scrollOffset;\n      var bottom = top + metrics.length;\n      if (top < viewportHeight && bottom > 0) {\n        firstVisible = idx;\n        if (_isViewable(viewAreaMode, viewablePercentThreshold, top, bottom, viewportHeight, metrics.length)) {\n          viewableIndices.push(idx);\n        }\n      } else if (firstVisible >= 0) {\n        break;\n      }\n    }\n    return viewableIndices;\n  }\n\n  /**\n   * Figures out which items are viewable and how that has changed from before and calls\n   * `onViewableItemsChanged` as appropriate.\n   */\n  onUpdate(props, scrollOffset, viewportHeight, getFrameMetrics, createViewToken, onViewableItemsChanged,\n  // Optional optimization to reduce the scan size\n  renderRange) {\n    var itemCount = props.getItemCount(props.data);\n    if (this._config.waitForInteraction && !this._hasInteracted || itemCount === 0 || !getFrameMetrics(0, props)) {\n      return;\n    }\n    var viewableIndices = [];\n    if (itemCount) {\n      viewableIndices = this.computeViewableItems(props, scrollOffset, viewportHeight, getFrameMetrics, renderRange);\n    }\n    if (this._viewableIndices.length === viewableIndices.length && this._viewableIndices.every((v, ii) => v === viewableIndices[ii])) {\n      // We might get a lot of scroll events where visibility doesn't change and we don't want to do\n      // extra work in those cases.\n      return;\n    }\n    this._viewableIndices = viewableIndices;\n    if (this._config.minimumViewTime) {\n      var handle = setTimeout(() => {\n        /* $FlowFixMe[incompatible-call] (>=0.63.0 site=react_native_fb) This\n         * comment suppresses an error found when Flow v0.63 was deployed. To\n         * see the error delete this comment and run Flow. */\n        this._timers.delete(handle);\n        this._onUpdateSync(props, viewableIndices, onViewableItemsChanged, createViewToken);\n      }, this._config.minimumViewTime);\n      /* $FlowFixMe[incompatible-call] (>=0.63.0 site=react_native_fb) This\n       * comment suppresses an error found when Flow v0.63 was deployed. To see\n       * the error delete this comment and run Flow. */\n      this._timers.add(handle);\n    } else {\n      this._onUpdateSync(props, viewableIndices, onViewableItemsChanged, createViewToken);\n    }\n  }\n\n  /**\n   * clean-up cached _viewableIndices to evaluate changed items on next update\n   */\n  resetViewableIndices() {\n    this._viewableIndices = [];\n  }\n\n  /**\n   * Records that an interaction has happened even if there has been no scroll.\n   */\n  recordInteraction() {\n    this._hasInteracted = true;\n  }\n  _onUpdateSync(props, viewableIndicesToCheck, onViewableItemsChanged, createViewToken) {\n    // Filter out indices that have gone out of view since this call was scheduled.\n    viewableIndicesToCheck = viewableIndicesToCheck.filter(ii => this._viewableIndices.includes(ii));\n    var prevItems = this._viewableItems;\n    var nextItems = new Map(viewableIndicesToCheck.map(ii => {\n      var viewable = createViewToken(ii, true, props);\n      return [viewable.key, viewable];\n    }));\n    var changed = [];\n    for (var _iterator = _createForOfIteratorHelperLoose(nextItems), _step; !(_step = _iterator()).done;) {\n      var _step$value = _step.value,\n        key = _step$value[0],\n        viewable = _step$value[1];\n      if (!prevItems.has(key)) {\n        changed.push(viewable);\n      }\n    }\n    for (var _iterator2 = _createForOfIteratorHelperLoose(prevItems), _step2; !(_step2 = _iterator2()).done;) {\n      var _step2$value = _step2.value,\n        _key = _step2$value[0],\n        _viewable = _step2$value[1];\n      if (!nextItems.has(_key)) {\n        changed.push(_objectSpread(_objectSpread({}, _viewable), {}, {\n          isViewable: false\n        }));\n      }\n    }\n    if (changed.length > 0) {\n      this._viewableItems = nextItems;\n      onViewableItemsChanged({\n        viewableItems: Array.from(nextItems.values()),\n        changed,\n        viewabilityConfig: this._config\n      });\n    }\n  }\n}\nfunction _isViewable(viewAreaMode, viewablePercentThreshold, top, bottom, viewportHeight, itemLength) {\n  if (_isEntirelyVisible(top, bottom, viewportHeight)) {\n    return true;\n  } else {\n    var pixels = _getPixelsVisible(top, bottom, viewportHeight);\n    var percent = 100 * (viewAreaMode ? pixels / viewportHeight : pixels / itemLength);\n    return percent >= viewablePercentThreshold;\n  }\n}\nfunction _getPixelsVisible(top, bottom, viewportHeight) {\n  var visibleHeight = Math.min(bottom, viewportHeight) - Math.max(top, 0);\n  return Math.max(0, visibleHeight);\n}\nfunction _isEntirelyVisible(top, bottom, viewportHeight) {\n  return top >= 0 && bottom <= viewportHeight && bottom > top;\n}\nexport default ViewabilityHelper;"], "mappings": "AAUA,YAAY;;AAAC,OAAAA,eAAA;AAAA,OAAAC,YAAA;AAEb,OAAOC,aAAa,MAAM,sCAAsC;AAChE,OAAOC,+BAA+B,MAAM,uDAAuD;AACnG,OAAOC,SAAS,MAAM,oBAAoB;AAAC,IAarCC,iBAAiB;EACrB,SAAAA,kBAAYC,MAAM,EAAE;IAAAN,eAAA,OAAAK,iBAAA;IAClB,IAAIC,MAAM,KAAK,KAAK,CAAC,EAAE;MACrBA,MAAM,GAAG;QACPC,gCAAgC,EAAE;MACpC,CAAC;IACH;IACA,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACC,OAAO,GAAG,IAAIC,GAAG,CAAC,CAAC;IACxB,IAAI,CAACC,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACC,cAAc,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC/B,IAAI,CAACC,OAAO,GAAGR,MAAM;EACvB;EAAC,OAAAL,YAAA,CAAAI,iBAAA;IAAAU,GAAA;IAAAC,KAAA,EAKD,SAAAC,OAAOA,CAAA,EAAG;MAIR,IAAI,CAACR,OAAO,CAACS,OAAO,CAACC,YAAY,CAAC;IACpC;EAAC;IAAAJ,GAAA;IAAAC,KAAA,EAKD,SAAAI,oBAAoBA,CAACC,KAAK,EAAEC,YAAY,EAAEC,cAAc,EAAEC,eAAe,EAEzEC,WAAW,EAAE;MACX,IAAIC,SAAS,GAAGL,KAAK,CAACM,YAAY,CAACN,KAAK,CAACO,IAAI,CAAC;MAC9C,IAAIC,aAAa,GAAG,IAAI,CAACf,OAAO;QAC9BgB,2BAA2B,GAAGD,aAAa,CAACC,2BAA2B;QACvEvB,gCAAgC,GAAGsB,aAAa,CAACtB,gCAAgC;MACnF,IAAIwB,YAAY,GAAGxB,gCAAgC,IAAI,IAAI;MAC3D,IAAIyB,wBAAwB,GAAGD,YAAY,GAAGxB,gCAAgC,GAAGuB,2BAA2B;MAC5G1B,SAAS,CAAC4B,wBAAwB,IAAI,IAAI,IAAIF,2BAA2B,IAAI,IAAI,MAAMvB,gCAAgC,IAAI,IAAI,CAAC,EAAE,yFAAyF,CAAC;MAC5N,IAAI0B,eAAe,GAAG,EAAE;MACxB,IAAIP,SAAS,KAAK,CAAC,EAAE;QACnB,OAAOO,eAAe;MACxB;MACA,IAAIC,YAAY,GAAG,CAAC,CAAC;MACrB,IAAIC,IAAI,GAAGV,WAAW,IAAI;UACtBW,KAAK,EAAE,CAAC;UACRC,IAAI,EAAEX,SAAS,GAAG;QACpB,CAAC;QACDU,KAAK,GAAGD,IAAI,CAACC,KAAK;QAClBC,IAAI,GAAGF,IAAI,CAACE,IAAI;MAClB,IAAIA,IAAI,IAAIX,SAAS,EAAE;QACrBY,OAAO,CAACC,IAAI,CAAC,6CAA6C,GAAGC,IAAI,CAACC,SAAS,CAAC;UAC1EhB,WAAW,EAAXA,WAAW;UACXC,SAAS,EAATA;QACF,CAAC,CAAC,CAAC;QACH,OAAO,EAAE;MACX;MACA,KAAK,IAAIgB,GAAG,GAAGN,KAAK,EAAEM,GAAG,IAAIL,IAAI,EAAEK,GAAG,EAAE,EAAE;QACxC,IAAIC,OAAO,GAAGnB,eAAe,CAACkB,GAAG,EAAErB,KAAK,CAAC;QACzC,IAAI,CAACsB,OAAO,EAAE;UACZ;QACF;QACA,IAAIC,GAAG,GAAGD,OAAO,CAACE,MAAM,GAAGvB,YAAY;QACvC,IAAIwB,MAAM,GAAGF,GAAG,GAAGD,OAAO,CAACI,MAAM;QACjC,IAAIH,GAAG,GAAGrB,cAAc,IAAIuB,MAAM,GAAG,CAAC,EAAE;UACtCZ,YAAY,GAAGQ,GAAG;UAClB,IAAIM,WAAW,CAACjB,YAAY,EAAEC,wBAAwB,EAAEY,GAAG,EAAEE,MAAM,EAAEvB,cAAc,EAAEoB,OAAO,CAACI,MAAM,CAAC,EAAE;YACpGd,eAAe,CAACgB,IAAI,CAACP,GAAG,CAAC;UAC3B;QACF,CAAC,MAAM,IAAIR,YAAY,IAAI,CAAC,EAAE;UAC5B;QACF;MACF;MACA,OAAOD,eAAe;IACxB;EAAC;IAAAlB,GAAA;IAAAC,KAAA,EAMD,SAAAkC,QAAQA,CAAC7B,KAAK,EAAEC,YAAY,EAAEC,cAAc,EAAEC,eAAe,EAAE2B,eAAe,EAAEC,sBAAsB,EAEtG3B,WAAW,EAAE;MAAA,IAAA4B,KAAA;MACX,IAAI3B,SAAS,GAAGL,KAAK,CAACM,YAAY,CAACN,KAAK,CAACO,IAAI,CAAC;MAC9C,IAAI,IAAI,CAACd,OAAO,CAACwC,kBAAkB,IAAI,CAAC,IAAI,CAAC9C,cAAc,IAAIkB,SAAS,KAAK,CAAC,IAAI,CAACF,eAAe,CAAC,CAAC,EAAEH,KAAK,CAAC,EAAE;QAC5G;MACF;MACA,IAAIY,eAAe,GAAG,EAAE;MACxB,IAAIP,SAAS,EAAE;QACbO,eAAe,GAAG,IAAI,CAACb,oBAAoB,CAACC,KAAK,EAAEC,YAAY,EAAEC,cAAc,EAAEC,eAAe,EAAEC,WAAW,CAAC;MAChH;MACA,IAAI,IAAI,CAACd,gBAAgB,CAACoC,MAAM,KAAKd,eAAe,CAACc,MAAM,IAAI,IAAI,CAACpC,gBAAgB,CAAC4C,KAAK,CAAC,UAACC,CAAC,EAAEC,EAAE;QAAA,OAAKD,CAAC,KAAKvB,eAAe,CAACwB,EAAE,CAAC;MAAA,EAAC,EAAE;QAGhI;MACF;MACA,IAAI,CAAC9C,gBAAgB,GAAGsB,eAAe;MACvC,IAAI,IAAI,CAACnB,OAAO,CAAC4C,eAAe,EAAE;QAChC,IAAIC,MAAM,GAAGC,UAAU,CAAC,YAAM;UAI5BP,KAAI,CAAC5C,OAAO,CAACoD,MAAM,CAACF,MAAM,CAAC;UAC3BN,KAAI,CAACS,aAAa,CAACzC,KAAK,EAAEY,eAAe,EAAEmB,sBAAsB,EAAED,eAAe,CAAC;QACrF,CAAC,EAAE,IAAI,CAACrC,OAAO,CAAC4C,eAAe,CAAC;QAIhC,IAAI,CAACjD,OAAO,CAACsD,GAAG,CAACJ,MAAM,CAAC;MAC1B,CAAC,MAAM;QACL,IAAI,CAACG,aAAa,CAACzC,KAAK,EAAEY,eAAe,EAAEmB,sBAAsB,EAAED,eAAe,CAAC;MACrF;IACF;EAAC;IAAApC,GAAA;IAAAC,KAAA,EAKD,SAAAgD,oBAAoBA,CAAA,EAAG;MACrB,IAAI,CAACrD,gBAAgB,GAAG,EAAE;IAC5B;EAAC;IAAAI,GAAA;IAAAC,KAAA,EAKD,SAAAiD,iBAAiBA,CAAA,EAAG;MAClB,IAAI,CAACzD,cAAc,GAAG,IAAI;IAC5B;EAAC;IAAAO,GAAA;IAAAC,KAAA,EACD,SAAA8C,aAAaA,CAACzC,KAAK,EAAE6C,sBAAsB,EAAEd,sBAAsB,EAAED,eAAe,EAAE;MAAA,IAAAgB,MAAA;MAEpFD,sBAAsB,GAAGA,sBAAsB,CAACE,MAAM,CAAC,UAAAX,EAAE;QAAA,OAAIU,MAAI,CAACxD,gBAAgB,CAAC0D,QAAQ,CAACZ,EAAE,CAAC;MAAA,EAAC;MAChG,IAAIa,SAAS,GAAG,IAAI,CAAC1D,cAAc;MACnC,IAAI2D,SAAS,GAAG,IAAI1D,GAAG,CAACqD,sBAAsB,CAACM,GAAG,CAAC,UAAAf,EAAE,EAAI;QACvD,IAAIgB,QAAQ,GAAGtB,eAAe,CAACM,EAAE,EAAE,IAAI,EAAEpC,KAAK,CAAC;QAC/C,OAAO,CAACoD,QAAQ,CAAC1D,GAAG,EAAE0D,QAAQ,CAAC;MACjC,CAAC,CAAC,CAAC;MACH,IAAIC,OAAO,GAAG,EAAE;MAChB,KAAK,IAAIC,SAAS,GAAGxE,+BAA+B,CAACoE,SAAS,CAAC,EAAEK,KAAK,EAAE,CAAC,CAACA,KAAK,GAAGD,SAAS,CAAC,CAAC,EAAEE,IAAI,GAAG;QACpG,IAAIC,WAAW,GAAGF,KAAK,CAAC5D,KAAK;UAC3BD,GAAG,GAAG+D,WAAW,CAAC,CAAC,CAAC;UACpBL,QAAQ,GAAGK,WAAW,CAAC,CAAC,CAAC;QAC3B,IAAI,CAACR,SAAS,CAACS,GAAG,CAAChE,GAAG,CAAC,EAAE;UACvB2D,OAAO,CAACzB,IAAI,CAACwB,QAAQ,CAAC;QACxB;MACF;MACA,KAAK,IAAIO,UAAU,GAAG7E,+BAA+B,CAACmE,SAAS,CAAC,EAAEW,MAAM,EAAE,CAAC,CAACA,MAAM,GAAGD,UAAU,CAAC,CAAC,EAAEH,IAAI,GAAG;QACxG,IAAIK,YAAY,GAAGD,MAAM,CAACjE,KAAK;UAC7BmE,IAAI,GAAGD,YAAY,CAAC,CAAC,CAAC;UACtBE,SAAS,GAAGF,YAAY,CAAC,CAAC,CAAC;QAC7B,IAAI,CAACX,SAAS,CAACQ,GAAG,CAACI,IAAI,CAAC,EAAE;UACxBT,OAAO,CAACzB,IAAI,CAAC/C,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEkF,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE;YAC3DC,UAAU,EAAE;UACd,CAAC,CAAC,CAAC;QACL;MACF;MACA,IAAIX,OAAO,CAAC3B,MAAM,GAAG,CAAC,EAAE;QACtB,IAAI,CAACnC,cAAc,GAAG2D,SAAS;QAC/BnB,sBAAsB,CAAC;UACrBkC,aAAa,EAAEC,KAAK,CAACC,IAAI,CAACjB,SAAS,CAACkB,MAAM,CAAC,CAAC,CAAC;UAC7Cf,OAAO,EAAPA,OAAO;UACPgB,iBAAiB,EAAE,IAAI,CAAC5E;QAC1B,CAAC,CAAC;MACJ;IACF;EAAC;AAAA;AAEH,SAASkC,WAAWA,CAACjB,YAAY,EAAEC,wBAAwB,EAAEY,GAAG,EAAEE,MAAM,EAAEvB,cAAc,EAAEoE,UAAU,EAAE;EACpG,IAAIC,kBAAkB,CAAChD,GAAG,EAAEE,MAAM,EAAEvB,cAAc,CAAC,EAAE;IACnD,OAAO,IAAI;EACb,CAAC,MAAM;IACL,IAAIsE,MAAM,GAAGC,iBAAiB,CAAClD,GAAG,EAAEE,MAAM,EAAEvB,cAAc,CAAC;IAC3D,IAAIwE,OAAO,GAAG,GAAG,IAAIhE,YAAY,GAAG8D,MAAM,GAAGtE,cAAc,GAAGsE,MAAM,GAAGF,UAAU,CAAC;IAClF,OAAOI,OAAO,IAAI/D,wBAAwB;EAC5C;AACF;AACA,SAAS8D,iBAAiBA,CAAClD,GAAG,EAAEE,MAAM,EAAEvB,cAAc,EAAE;EACtD,IAAIyE,aAAa,GAAGC,IAAI,CAACC,GAAG,CAACpD,MAAM,EAAEvB,cAAc,CAAC,GAAG0E,IAAI,CAACE,GAAG,CAACvD,GAAG,EAAE,CAAC,CAAC;EACvE,OAAOqD,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEH,aAAa,CAAC;AACnC;AACA,SAASJ,kBAAkBA,CAAChD,GAAG,EAAEE,MAAM,EAAEvB,cAAc,EAAE;EACvD,OAAOqB,GAAG,IAAI,CAAC,IAAIE,MAAM,IAAIvB,cAAc,IAAIuB,MAAM,GAAGF,GAAG;AAC7D;AACA,eAAevC,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}