{"ast": null, "code": "import { CHILD_STATE } from \"./useRouteCache\";\nexport default function getFocusedRouteNameFromRoute(route) {\n  var _route$CHILD_STATE, _state$index;\n  var state = (_route$CHILD_STATE = route[CHILD_STATE]) != null ? _route$CHILD_STATE : route.state;\n  var params = route.params;\n  var routeName = state ? state.routes[(_state$index = state.index) != null ? _state$index : typeof state.type === 'string' && state.type !== 'stack' ? 0 : state.routes.length - 1].name : typeof (params === null || params === void 0 ? void 0 : params.screen) === 'string' ? params.screen : undefined;\n  return routeName;\n}", "map": {"version": 3, "names": ["CHILD_STATE", "getFocusedRouteNameFromRoute", "route", "_route$CHILD_STATE", "_state$index", "state", "params", "routeName", "routes", "index", "type", "length", "name", "screen", "undefined"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\@react-navigation\\core\\src\\getFocusedRouteNameFromRoute.tsx"], "sourcesContent": ["import type { Route } from '@react-navigation/routers';\n\nimport { CHILD_STATE } from './useRouteCache';\n\nexport default function getFocusedRouteNameFromRoute(\n  route: Partial<Route<string>>\n): string | undefined {\n  // @ts-expect-error: this isn't in type definitions coz we want this private\n  const state = route[CHILD_STATE] ?? route.state;\n  const params = route.params as { screen?: unknown } | undefined;\n\n  const routeName = state\n    ? // Get the currently active route name in the nested navigator\n      state.routes[\n        // If we have a partial state without index, for tab/drawer, first screen will be focused one, and last for stack\n        // The type property will only exist for rehydrated state and not for state from deep link\n        state.index ??\n          (typeof state.type === 'string' && state.type !== 'stack'\n            ? 0\n            : state.routes.length - 1)\n      ].name\n    : // If state doesn't exist, we need to default to `screen` param if available\n    typeof params?.screen === 'string'\n    ? params.screen\n    : undefined;\n\n  return routeName;\n}\n"], "mappings": "AAEA,SAASA,WAAW;AAEpB,eAAe,SAASC,4BAA4BA,CAClDC,KAA6B,EACT;EAAA,IAAAC,kBAAA,EAAAC,YAAA;EAEpB,IAAMC,KAAK,IAAAF,kBAAA,GAAGD,KAAK,CAACF,WAAW,CAAC,YAAAG,kBAAA,GAAID,KAAK,CAACG,KAAK;EAC/C,IAAMC,MAAM,GAAGJ,KAAK,CAACI,MAA0C;EAE/D,IAAMC,SAAS,GAAGF,KAAK,GAEnBA,KAAK,CAACG,MAAM,EAAAJ,YAAA,GAGVC,KAAK,CAACI,KAAK,YAAAL,YAAA,GACR,OAAOC,KAAK,CAACK,IAAI,KAAK,QAAQ,IAAIL,KAAK,CAACK,IAAI,KAAK,OAAO,GACrD,CAAC,GACDL,KAAK,CAACG,MAAM,CAACG,MAAM,GAAG,CAAC,CAC9B,CAACC,IAAI,GAER,QAAON,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEO,MAAM,MAAK,QAAQ,GAChCP,MAAM,CAACO,MAAM,GACbC,SAAS;EAEb,OAAOP,SAAS;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}