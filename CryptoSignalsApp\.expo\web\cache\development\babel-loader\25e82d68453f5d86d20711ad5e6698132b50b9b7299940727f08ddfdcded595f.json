{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport { getHeaderTitle, HeaderShownContext } from '@react-navigation/elements';\nimport { StackActions } from '@react-navigation/native';\nimport * as React from 'react';\nimport { useSafeAreaInsets } from 'react-native-safe-area-context';\nimport debounce from \"../../utils/debounce\";\nimport ModalPresentationContext from \"../../utils/ModalPresentationContext\";\nimport HeaderSegment from \"./HeaderSegment\";\nexport default React.memo(function Header(_ref) {\n  var back = _ref.back,\n    layout = _ref.layout,\n    progress = _ref.progress,\n    options = _ref.options,\n    route = _ref.route,\n    navigation = _ref.navigation,\n    styleInterpolator = _ref.styleInterpolator;\n  var insets = useSafeAreaInsets();\n  var previousTitle;\n  if (options.headerBackTitle !== undefined) {\n    previousTitle = options.headerBackTitle;\n  } else if (back) {\n    previousTitle = back.title;\n  }\n  var goBack = React.useCallback(debounce(function () {\n    if (navigation.isFocused() && navigation.canGoBack()) {\n      navigation.dispatch(_objectSpread(_objectSpread({}, StackActions.pop()), {}, {\n        source: route.key\n      }));\n    }\n  }, 50), [navigation, route.key]);\n  var isModal = React.useContext(ModalPresentationContext);\n  var isParentHeaderShown = React.useContext(HeaderShownContext);\n  var statusBarHeight = options.headerStatusBarHeight !== undefined ? options.headerStatusBarHeight : isModal || isParentHeaderShown ? 0 : insets.top;\n  return React.createElement(HeaderSegment, _extends({}, options, {\n    title: getHeaderTitle(options, route.name),\n    progress: progress,\n    layout: layout,\n    modal: isModal,\n    headerBackTitle: options.headerBackTitle !== undefined ? options.headerBackTitle : previousTitle,\n    headerStatusBarHeight: statusBarHeight,\n    onGoBack: back ? goBack : undefined,\n    styleInterpolator: styleInterpolator\n  }));\n});", "map": {"version": 3, "names": ["getHeaderTitle", "HeaderShownContext", "StackActions", "React", "useSafeAreaInsets", "debounce", "ModalPresentationContext", "HeaderSegment", "memo", "Header", "_ref", "back", "layout", "progress", "options", "route", "navigation", "styleInterpolator", "insets", "previousTitle", "headerBackTitle", "undefined", "title", "goBack", "useCallback", "isFocused", "canGoBack", "dispatch", "_objectSpread", "pop", "source", "key", "isModal", "useContext", "isParentHeaderShown", "statusBarHeight", "headerStatusBarHeight", "top", "createElement", "_extends", "name", "modal", "onGoBack"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\@react-navigation\\stack\\src\\views\\Header\\Header.tsx"], "sourcesContent": ["import { getHeaderTitle, HeaderShownContext } from '@react-navigation/elements';\nimport { StackActions } from '@react-navigation/native';\nimport * as React from 'react';\nimport { useSafeAreaInsets } from 'react-native-safe-area-context';\n\nimport type { StackHeaderProps } from '../../types';\nimport debounce from '../../utils/debounce';\nimport ModalPresentationContext from '../../utils/ModalPresentationContext';\nimport HeaderSegment from './HeaderSegment';\n\nexport default React.memo(function Header({\n  back,\n  layout,\n  progress,\n  options,\n  route,\n  navigation,\n  styleInterpolator,\n}: StackHeaderProps) {\n  const insets = useSafeAreaInsets();\n\n  let previousTitle;\n\n  // The label for the left back button shows the title of the previous screen\n  // If a custom label is specified, we use it, otherwise use previous screen's title\n  if (options.headerBackTitle !== undefined) {\n    previousTitle = options.headerBackTitle;\n  } else if (back) {\n    previousTitle = back.title;\n  }\n\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  const goBack = React.useCallback(\n    debounce(() => {\n      if (navigation.isFocused() && navigation.canGoBack()) {\n        navigation.dispatch({\n          ...StackActions.pop(),\n          source: route.key,\n        });\n      }\n    }, 50),\n    [navigation, route.key]\n  );\n\n  const isModal = React.useContext(ModalPresentationContext);\n  const isParentHeaderShown = React.useContext(HeaderShownContext);\n\n  const statusBarHeight =\n    options.headerStatusBarHeight !== undefined\n      ? options.headerStatusBarHeight\n      : isModal || isParentHeaderShown\n      ? 0\n      : insets.top;\n\n  return (\n    <HeaderSegment\n      {...options}\n      title={getHeaderTitle(options, route.name)}\n      progress={progress}\n      layout={layout}\n      modal={isModal}\n      headerBackTitle={\n        options.headerBackTitle !== undefined\n          ? options.headerBackTitle\n          : previousTitle\n      }\n      headerStatusBarHeight={statusBarHeight}\n      onGoBack={back ? goBack : undefined}\n      styleInterpolator={styleInterpolator}\n    />\n  );\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;AAAA,SAASA,cAAc,EAAEC,kBAAkB,QAAQ,4BAA4B;AAC/E,SAASC,YAAY,QAAQ,0BAA0B;AACvD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,iBAAiB,QAAQ,gCAAgC;AAGlE,OAAOC,QAAQ;AACf,OAAOC,wBAAwB;AAC/B,OAAOC,aAAa;AAEpB,eAAeJ,KAAK,CAACK,IAAI,CAAC,SAASC,MAAMA,CAAAC,IAAA,EAQpB;EAAA,IAPnBC,IAAI,GAOaD,IAAA,CAPjBC,IAAI;IACJC,MAAM,GAMWF,IAAA,CANjBE,MAAM;IACNC,QAAQ,GAKSH,IAAA,CALjBG,QAAQ;IACRC,OAAO,GAIUJ,IAAA,CAJjBI,OAAO;IACPC,KAAK,GAGYL,IAAA,CAHjBK,KAAK;IACLC,UAAU,GAEON,IAAA,CAFjBM,UAAU;IACVC,iBAAA,GACiBP,IAAA,CADjBO,iBAAA;EAEA,IAAMC,MAAM,GAAGd,iBAAiB,EAAE;EAElC,IAAIe,aAAa;EAIjB,IAAIL,OAAO,CAACM,eAAe,KAAKC,SAAS,EAAE;IACzCF,aAAa,GAAGL,OAAO,CAACM,eAAe;EACzC,CAAC,MAAM,IAAIT,IAAI,EAAE;IACfQ,aAAa,GAAGR,IAAI,CAACW,KAAK;EAC5B;EAGA,IAAMC,MAAM,GAAGpB,KAAK,CAACqB,WAAW,CAC9BnB,QAAQ,CAAC,YAAM;IACb,IAAIW,UAAU,CAACS,SAAS,EAAE,IAAIT,UAAU,CAACU,SAAS,EAAE,EAAE;MACpDV,UAAU,CAACW,QAAQ,CAAAC,aAAA,CAAAA,aAAA,KACd1B,YAAY,CAAC2B,GAAG,EAAE;QACrBC,MAAM,EAAEf,KAAK,CAACgB;MAAA,EACf,CAAC;IACJ;EACF,CAAC,EAAE,EAAE,CAAC,EACN,CAACf,UAAU,EAAED,KAAK,CAACgB,GAAG,CAAC,CACxB;EAED,IAAMC,OAAO,GAAG7B,KAAK,CAAC8B,UAAU,CAAC3B,wBAAwB,CAAC;EAC1D,IAAM4B,mBAAmB,GAAG/B,KAAK,CAAC8B,UAAU,CAAChC,kBAAkB,CAAC;EAEhE,IAAMkC,eAAe,GACnBrB,OAAO,CAACsB,qBAAqB,KAAKf,SAAS,GACvCP,OAAO,CAACsB,qBAAqB,GAC7BJ,OAAO,IAAIE,mBAAmB,GAC9B,CAAC,GACDhB,MAAM,CAACmB,GAAG;EAEhB,OACElC,KAAA,CAAAmC,aAAA,CAAC/B,aAAa,EAAAgC,QAAA,KACRzB,OAAO;IACXQ,KAAK,EAAEtB,cAAc,CAACc,OAAO,EAAEC,KAAK,CAACyB,IAAI,CAAE;IAC3C3B,QAAQ,EAAEA,QAAS;IACnBD,MAAM,EAAEA,MAAO;IACf6B,KAAK,EAAET,OAAQ;IACfZ,eAAe,EACbN,OAAO,CAACM,eAAe,KAAKC,SAAS,GACjCP,OAAO,CAACM,eAAe,GACvBD,aACL;IACDiB,qBAAqB,EAAED,eAAgB;IACvCO,QAAQ,EAAE/B,IAAI,GAAGY,MAAM,GAAGF,SAAU;IACpCJ,iBAAiB,EAAEA;EAAkB,GACrC;AAEN,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}