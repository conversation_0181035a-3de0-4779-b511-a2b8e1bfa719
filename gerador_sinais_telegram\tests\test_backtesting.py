import unittest
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from unittest.mock import Mock, patch
from utils.backtesting import Backtester

class TestBacktester(unittest.TestCase):
    def setUp(self):
        """Configuração inicial para cada teste"""
        # Mock do cliente Binance
        self.binance_mock = Mock()
        
        # Mock da estratégia
        self.strategy_mock = Mock()
        
        # Criar dados históricos simulados
        dates = pd.date_range(start='2024-01-01', end='2024-01-02', freq='1min')
        self.mock_data = pd.DataFrame({
            'timestamp': dates,
            'open': np.random.normal(100, 1, len(dates)),
            'high': np.random.normal(101, 1, len(dates)),
            'low': np.random.normal(99, 1, len(dates)),
            'close': np.random.normal(100, 1, len(dates)),
            'volume': np.random.normal(1000, 100, len(dates)),
            'close_time': dates + pd.<PERSON><PERSON><PERSON>(minutes=1),
            'quote_volume': np.random.normal(100000, 10000, len(dates)),
            'trades': np.random.randint(100, 1000, len(dates)),
            'taker_buy_base': np.random.normal(500, 50, len(dates)),
            'taker_buy_quote': np.random.normal(50000, 5000, len(dates)),
            'ignore': np.zeros(len(dates))
        })
        
        # Configurar mock para retornar dados simulados
        self.binance_mock.get_historical_klines.return_value = self.mock_data.values.tolist()
        
        # Inicializar backtester
        self.backtester = Backtester(
            binance_client=self.binance_mock,
            strategy=self.strategy_mock,
            initial_capital=1000.0
        )
    
    def test_initialization(self):
        """Testa a inicialização do backtester"""
        self.assertEqual(self.backtester.initial_capital, 1000.0)
        self.assertEqual(self.backtester.current_capital, 1000.0)
        self.assertEqual(len(self.backtester.positions), 0)
        self.assertEqual(len(self.backtester.trades_history), 0)
        self.assertEqual(len(self.backtester.equity_curve), 0)
    
    def test_get_historical_data(self):
        """Testa a obtenção de dados históricos"""
        start_date = datetime(2024, 1, 1)
        end_date = datetime(2024, 1, 2)
        
        df = self.backtester.get_historical_data(
            symbol='BTCUSDT',
            interval='1m',
            start_date=start_date,
            end_date=end_date
        )
        
        self.assertFalse(df.empty)
        self.assertEqual(len(df), len(self.mock_data))
        self.assertTrue(all(col in df.columns for col in ['timestamp', 'open', 'high', 'low', 'close', 'volume']))
    
    def test_execute_trade(self):
        """Testa a execução de um trade"""
        self.backtester.execute_trade(
            symbol='BTCUSDT',
            signal_type='LONG',
            entry_price=100.0,
            stop_loss=95.0,
            take_profit=105.0,
            timestamp=datetime(2024, 1, 1)
        )
        
        self.assertEqual(len(self.backtester.positions), 1)
        self.assertEqual(self.backtester.positions[0]['type'], 'LONG')
        self.assertEqual(self.backtester.positions[0]['entry_price'], 100.0)
        self.assertEqual(self.backtester.positions[0]['position_size'], 10.0)  # 1% do capital inicial
    
    def test_update_positions(self):
        """Testa a atualização de posições"""
        # Abrir uma posição
        self.backtester.execute_trade(
            symbol='BTCUSDT',
            signal_type='LONG',
            entry_price=100.0,
            stop_loss=95.0,
            take_profit=105.0,
            timestamp=datetime(2024, 1, 1)
        )
        
        # Atualizar posição com preço que atinge take profit
        self.backtester.update_positions(105.0, datetime(2024, 1, 1))
        
        self.assertEqual(len(self.backtester.trades_history), 1)
        self.assertEqual(self.backtester.positions[0]['status'], 'closed')
        self.assertEqual(self.backtester.trades_history[0]['reason'], 'take_profit')
    
    def test_calculate_performance_metrics(self):
        """Testa o cálculo de métricas de performance"""
        # Simular alguns trades
        self.backtester.trades_history = [
            {
                'symbol': 'BTCUSDT',
                'type': 'LONG',
                'entry_price': 100.0,
                'exit_price': 105.0,
                'profit_pct': 0.05,
                'profit_amount': 50.0,  # 5% de 1000
                'entry_time': datetime(2024, 1, 1),
                'exit_time': datetime(2024, 1, 2),
                'reason': 'take_profit'
            }
        ]
        
        # Atualizar capital atual
        self.backtester.current_capital = self.backtester.initial_capital + 50.0  # Adicionar lucro
        
        # Simular curva de equity
        self.backtester.equity_curve = [
            {'timestamp': datetime(2024, 1, 1), 'equity': 1000.0},
            {'timestamp': datetime(2024, 1, 2), 'equity': 1050.0}
        ]
        
        metrics = self.backtester.calculate_performance_metrics()
        
        self.assertEqual(metrics['total_trades'], 1)
        self.assertEqual(metrics['win_rate'], 1.0)
        self.assertEqual(metrics['total_return'], 5.0)  # 5% de retorno
    
    def test_calculate_max_drawdown(self):
        """Testa o cálculo do drawdown máximo"""
        equity_values = [1000, 1100, 900, 950, 1200]
        max_dd = self.backtester.calculate_max_drawdown(equity_values)
        
        # O drawdown máximo deve ser (1100 - 900) / 1100 = 18.18%
        self.assertAlmostEqual(max_dd, 18.18, places=2)
    
    def test_run_backtest(self):
        """Testa a execução completa do backtest"""
        # Configurar mock da estratégia para retornar um sinal
        self.strategy_mock.analyze_symbol.return_value = (
            'LONG', 100.0, 95.0, 105.0
        )
        
        start_date = datetime(2024, 1, 1)
        end_date = datetime(2024, 1, 2)
        
        results = self.backtester.run_backtest(
            symbol='BTCUSDT',
            interval='1m',
            start_date=start_date,
            end_date=end_date
        )
        
        self.assertIn('total_trades', results)
        self.assertIn('win_rate', results)
        self.assertIn('profit_factor', results)
        self.assertIn('max_drawdown', results)
        self.assertIn('sharpe_ratio', results)
        self.assertIn('total_return', results)
        self.assertIn('equity_curve', results)
        self.assertIn('trades_history', results)

if __name__ == '__main__':
    unittest.main() 