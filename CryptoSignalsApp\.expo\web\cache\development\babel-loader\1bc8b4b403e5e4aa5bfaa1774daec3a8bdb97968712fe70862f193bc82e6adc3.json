{"ast": null, "code": "import normalizeColor from '@react-native/normalize-colors';\nvar processColor = function processColor(color) {\n  if (color === undefined || color === null) {\n    return color;\n  }\n  var int32Color = normalizeColor(color);\n  if (int32Color === undefined || int32Color === null) {\n    return undefined;\n  }\n  int32Color = (int32Color << 24 | int32Color >>> 8) >>> 0;\n  return int32Color;\n};\nexport default processColor;", "map": {"version": 3, "names": ["normalizeColor", "processColor", "color", "undefined", "int32Color"], "sources": ["C:/Users/<USER>/Desktop/Signals/CryptoSignalsApp/node_modules/react-native-web/dist/exports/processColor/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport normalizeColor from '@react-native/normalize-colors';\nvar processColor = color => {\n  if (color === undefined || color === null) {\n    return color;\n  }\n\n  // convert number and hex\n  var int32Color = normalizeColor(color);\n  if (int32Color === undefined || int32Color === null) {\n    return undefined;\n  }\n  int32Color = (int32Color << 24 | int32Color >>> 8) >>> 0;\n  return int32Color;\n};\nexport default processColor;"], "mappings": "AAUA,OAAOA,cAAc,MAAM,gCAAgC;AAC3D,IAAIC,YAAY,GAAG,SAAfA,YAAYA,CAAGC,KAAK,EAAI;EAC1B,IAAIA,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,IAAI,EAAE;IACzC,OAAOA,KAAK;EACd;EAGA,IAAIE,UAAU,GAAGJ,cAAc,CAACE,KAAK,CAAC;EACtC,IAAIE,UAAU,KAAKD,SAAS,IAAIC,UAAU,KAAK,IAAI,EAAE;IACnD,OAAOD,SAAS;EAClB;EACAC,UAAU,GAAG,CAACA,UAAU,IAAI,EAAE,GAAGA,UAAU,KAAK,CAAC,MAAM,CAAC;EACxD,OAAOA,UAAU;AACnB,CAAC;AACD,eAAeH,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}