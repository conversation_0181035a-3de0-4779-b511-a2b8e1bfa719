import React, { useState } from 'react';
import { View, TextInput, Button, ToastAndroid } from 'react-native';

function Newsletter() {
  const [email, setEmail] = useState('');

  const handleSubscribe = () => {
    if (!email) {
      ToastAndroid.showWithGravity("Please, enter your email address", ToastAndroid.SHORT, ToastAndroid.BOTTOM);
      return;
    }

    // Aqui, você pode fazer uma chamada API para adicionar o email à sua lista de Newsletter ou qualquer outra lógica de back-end necessária.
    
    ToastAndroid.showWithGravity("Subscribed successfully!", ToastAndroid.SHORT, ToastAndroid.BOTTOM);
  };

  return (
    <View style={{ padding: 20 }}>
      <TextInput 
        style={{ height: 40, borderColor: 'gray', borderWidth: 1, padding: 10 }}
        placeholder="Subscribe to our Newsletter"
        value={email}
        onChangeText={setEmail}
      />
      <Button title="Subscribe" onPress={handleSubscribe} />
    </View>
  );
}

export default Newsletter;
