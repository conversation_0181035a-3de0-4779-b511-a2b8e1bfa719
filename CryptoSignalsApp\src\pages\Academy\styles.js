import React from 'react';
import { View, Text, TouchableOpacity, ScrollView, StyleSheet } from 'react-native';
import { AntDesign } from '@expo/vector-icons';
import styles from './styles'; // Importe seu arquivo de estilos.

const Academy = ({ navigation }) => {
  // Função para navegar para a página de um tópico de aprendizado
  const navigateToTopic = (topic) => {
    navigation.navigate('TopicDetail', { topic });
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Academia de Aprendizado</Text>

      <TouchableOpacity onPress={() => navigateToTopic('TradingBasics')} style={styles.topicButton}>
        <Text style={styles.topicText}>Trading Básico</Text>
      </TouchableOpacity>

      <TouchableOpacity onPress={() => navigateToTopic('Cryptocurrencies')} style={styles.topicButton}>
        <Text style={styles.topicText}>Criptomoedas</Text>
      </TouchableOpacity>

      <TouchableOpacity onPress={() => navigateToTopic('TechnicalAnalysis')} style={styles.topicButton}>
        <Text style={styles.topicText}>Análise Técnica</Text>
      </TouchableOpacity>

      {/* Adicione mais botões para outros tópicos de aprendizado aqui */}

      <View style={styles.menuContainer}>
        <TouchableOpacity style={styles.menuButton}>
          {/* Ícone do menu hambúrguer */}
          <AntDesign name="menu-fold" size={24} style={styles.menuIcon} />
        </TouchableOpacity>
        {/* Lista de opções do menu */}
        <View style={styles.menuOptions}>
          <TouchableOpacity>
            <Text style={styles.menuOptionText}>Opção 1</Text>
          </TouchableOpacity>
          <TouchableOpacity>
            <Text style={styles.menuOptionText}>Opção 2</Text>
          </TouchableOpacity>
          <TouchableOpacity>
            <Text style={styles.menuOptionText}>Opção 3</Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
};

export default Academy;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  topicButton: {
    backgroundColor: '#3176c4',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  topicText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  menuContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 32,
  },
  menuButton: {
    backgroundColor: '#3176c4',
    padding: 12,
    borderRadius: 8,
  },
  menuIcon: {
    color: '#fff',
  },
  menuOptions: {
    flexDirection: 'row',
    marginLeft: 16,
  },
  menuOptionText: {
    fontSize: 16,
    marginLeft: 8,
  },
});
