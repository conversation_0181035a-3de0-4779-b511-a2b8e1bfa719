{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport I18nManager from \"react-native-web/dist/exports/I18nManager\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport color from 'color';\nimport { black, white } from \"../../styles/themes/v2/colors\";\nimport getContrastingColor from \"../../utils/getContrastingColor\";\nexport var getCombinedStyles = function getCombinedStyles(_ref) {\n  var isAnimatedFromRight = _ref.isAnimatedFromRight,\n    isIconStatic = _ref.isIconStatic,\n    distance = _ref.distance,\n    animFAB = _ref.animFAB;\n  var isRTL = I18nManager.isRTL;\n  var defaultPositionStyles = {\n    left: -distance,\n    right: undefined\n  };\n  var combinedStyles = {\n    innerWrapper: _objectSpread({}, defaultPositionStyles),\n    iconWrapper: _objectSpread({}, defaultPositionStyles),\n    absoluteFill: {}\n  };\n  var animatedFromRight = isAnimatedFromRight && !isRTL;\n  var animatedFromRightRTL = isAnimatedFromRight && isRTL;\n  var animatedFromLeft = !isAnimatedFromRight && !isRTL;\n  var animatedFromLeftRTL = !isAnimatedFromRight && isRTL;\n  if (animatedFromRight) {\n    combinedStyles.innerWrapper.transform = [{\n      translateX: animFAB.interpolate({\n        inputRange: [distance, 0],\n        outputRange: [distance, 0]\n      })\n    }];\n    combinedStyles.iconWrapper.transform = [{\n      translateX: isIconStatic ? 0 : animFAB\n    }];\n    combinedStyles.absoluteFill.transform = [{\n      translateX: animFAB.interpolate({\n        inputRange: [distance, 0],\n        outputRange: [Math.abs(distance) / 2, Math.abs(distance)]\n      })\n    }];\n  } else if (animatedFromRightRTL) {\n    combinedStyles.iconWrapper.transform = [{\n      translateX: isIconStatic ? 0 : animFAB.interpolate({\n        inputRange: [distance, 0],\n        outputRange: [-distance, 0]\n      })\n    }];\n    combinedStyles.innerWrapper.transform = [{\n      translateX: animFAB.interpolate({\n        inputRange: [distance, 0],\n        outputRange: [-distance, 0]\n      })\n    }];\n    combinedStyles.absoluteFill.transform = [{\n      translateX: animFAB.interpolate({\n        inputRange: [distance, 0],\n        outputRange: [0, distance]\n      })\n    }];\n  } else if (animatedFromLeft) {\n    combinedStyles.iconWrapper.transform = [{\n      translateX: isIconStatic ? distance : animFAB.interpolate({\n        inputRange: [0, distance],\n        outputRange: [distance, distance * 2]\n      })\n    }];\n    combinedStyles.innerWrapper.transform = [{\n      translateX: animFAB\n    }];\n    combinedStyles.absoluteFill.transform = [{\n      translateX: animFAB.interpolate({\n        inputRange: [0, distance],\n        outputRange: [0, Math.abs(distance) / 2]\n      })\n    }];\n  } else if (animatedFromLeftRTL) {\n    combinedStyles.iconWrapper.transform = [{\n      translateX: isIconStatic ? animFAB.interpolate({\n        inputRange: [0, distance],\n        outputRange: [-distance, -distance * 2]\n      }) : -distance\n    }];\n    combinedStyles.innerWrapper.transform = [{\n      translateX: animFAB.interpolate({\n        inputRange: [0, distance],\n        outputRange: [0, -distance]\n      })\n    }];\n    combinedStyles.absoluteFill.transform = [{\n      translateX: animFAB.interpolate({\n        inputRange: [0, distance],\n        outputRange: [0, -distance]\n      })\n    }];\n  }\n  return combinedStyles;\n};\nvar getBackgroundColor = function getBackgroundColor(_ref2) {\n  var theme = _ref2.theme,\n    isVariant = _ref2.isVariant,\n    disabled = _ref2.disabled,\n    customBackgroundColor = _ref2.customBackgroundColor;\n  var _theme$colors;\n  if (customBackgroundColor && !disabled) {\n    return customBackgroundColor;\n  }\n  if (theme.isV3) {\n    if (disabled) {\n      return theme.colors.surfaceDisabled;\n    }\n    if (isVariant('primary')) {\n      return theme.colors.primaryContainer;\n    }\n    if (isVariant('secondary')) {\n      return theme.colors.secondaryContainer;\n    }\n    if (isVariant('tertiary')) {\n      return theme.colors.tertiaryContainer;\n    }\n    if (isVariant('surface')) {\n      return theme.colors.elevation.level3;\n    }\n  }\n  if (disabled) {\n    if (theme.dark) {\n      return color(white).alpha(0.12).rgb().string();\n    }\n    return color(black).alpha(0.12).rgb().string();\n  }\n  return (_theme$colors = theme.colors) === null || _theme$colors === void 0 ? void 0 : _theme$colors.accent;\n};\nvar getForegroundColor = function getForegroundColor(_ref3) {\n  var theme = _ref3.theme,\n    isVariant = _ref3.isVariant,\n    disabled = _ref3.disabled,\n    backgroundColor = _ref3.backgroundColor,\n    customColor = _ref3.customColor;\n  if (typeof customColor !== 'undefined' && !disabled) {\n    return customColor;\n  }\n  if (theme.isV3) {\n    if (disabled) {\n      return theme.colors.onSurfaceDisabled;\n    }\n    if (isVariant('primary')) {\n      return theme.colors.onPrimaryContainer;\n    }\n    if (isVariant('secondary')) {\n      return theme.colors.onSecondaryContainer;\n    }\n    if (isVariant('tertiary')) {\n      return theme.colors.onTertiaryContainer;\n    }\n    if (isVariant('surface')) {\n      return theme.colors.primary;\n    }\n  }\n  if (disabled) {\n    if (theme.dark) {\n      return color(white).alpha(0.32).rgb().string();\n    }\n    return color(black).alpha(0.32).rgb().string();\n  }\n  if (backgroundColor) {\n    return getContrastingColor(backgroundColor || white, white, 'rgba(0, 0, 0, .54)');\n  }\n  return getContrastingColor(white, white, 'rgba(0, 0, 0, .54)');\n};\nexport var getFABColors = function getFABColors(_ref4) {\n  var theme = _ref4.theme,\n    variant = _ref4.variant,\n    disabled = _ref4.disabled,\n    customColor = _ref4.customColor,\n    customBackgroundColor = _ref4.customBackgroundColor,\n    customRippleColor = _ref4.customRippleColor;\n  var isVariant = function isVariant(variantToCompare) {\n    return variant === variantToCompare;\n  };\n  var baseFABColorProps = {\n    theme: theme,\n    isVariant: isVariant,\n    disabled: disabled\n  };\n  var backgroundColor = getBackgroundColor(_objectSpread(_objectSpread({}, baseFABColorProps), {}, {\n    customBackgroundColor: customBackgroundColor\n  }));\n  var foregroundColor = getForegroundColor(_objectSpread(_objectSpread({}, baseFABColorProps), {}, {\n    customColor: customColor,\n    backgroundColor: backgroundColor\n  }));\n  return {\n    backgroundColor: backgroundColor,\n    foregroundColor: foregroundColor,\n    rippleColor: customRippleColor || color(foregroundColor).alpha(0.12).rgb().string()\n  };\n};\nvar getLabelColor = function getLabelColor(_ref5) {\n  var theme = _ref5.theme;\n  if (theme.isV3) {\n    return theme.colors.onSurface;\n  }\n  if (theme.dark) {\n    return theme.colors.text;\n  }\n  return color(theme.colors.text).fade(0.54).rgb().string();\n};\nvar getBackdropColor = function getBackdropColor(_ref6) {\n  var theme = _ref6.theme,\n    customBackdropColor = _ref6.customBackdropColor;\n  var _theme$colors2;\n  if (customBackdropColor) {\n    return customBackdropColor;\n  }\n  if (theme.isV3) {\n    return color(theme.colors.background).alpha(0.95).rgb().string();\n  }\n  return (_theme$colors2 = theme.colors) === null || _theme$colors2 === void 0 ? void 0 : _theme$colors2.backdrop;\n};\nvar getStackedFABBackgroundColor = function getStackedFABBackgroundColor(_ref7) {\n  var theme = _ref7.theme;\n  if (theme.isV3) {\n    return theme.colors.elevation.level3;\n  }\n  return theme.colors.surface;\n};\nexport var getFABGroupColors = function getFABGroupColors(_ref8) {\n  var theme = _ref8.theme,\n    customBackdropColor = _ref8.customBackdropColor;\n  return {\n    labelColor: getLabelColor({\n      theme: theme\n    }),\n    backdropColor: getBackdropColor({\n      theme: theme,\n      customBackdropColor: customBackdropColor\n    }),\n    stackedFABBackgroundColor: getStackedFABBackgroundColor({\n      theme: theme\n    })\n  };\n};\nvar standardSize = {\n  height: 56,\n  width: 56,\n  borderRadius: 28\n};\nvar smallSize = {\n  height: 40,\n  width: 40,\n  borderRadius: 28\n};\nvar v3SmallSize = {\n  height: 40,\n  width: 40\n};\nvar v3MediumSize = {\n  height: 56,\n  width: 56\n};\nvar v3LargeSize = {\n  height: 96,\n  width: 96\n};\nvar getCustomFabSize = function getCustomFabSize(customSize, roundness) {\n  return {\n    height: customSize,\n    width: customSize,\n    borderRadius: roundness === 0 ? 0 : customSize / roundness\n  };\n};\nexport var getFabStyle = function getFabStyle(_ref9) {\n  var size = _ref9.size,\n    theme = _ref9.theme,\n    customSize = _ref9.customSize;\n  var isV3 = theme.isV3,\n    roundness = theme.roundness;\n  if (customSize) return getCustomFabSize(customSize, roundness);\n  if (isV3) {\n    switch (size) {\n      case 'small':\n        return _objectSpread(_objectSpread({}, v3SmallSize), {}, {\n          borderRadius: 3 * roundness\n        });\n      case 'medium':\n        return _objectSpread(_objectSpread({}, v3MediumSize), {}, {\n          borderRadius: 4 * roundness\n        });\n      case 'large':\n        return _objectSpread(_objectSpread({}, v3LargeSize), {}, {\n          borderRadius: 7 * roundness\n        });\n    }\n  }\n  if (size === 'small') {\n    return smallSize;\n  }\n  return standardSize;\n};\nvar extended = {\n  height: 48,\n  paddingHorizontal: 16\n};\nvar v3Extended = {\n  height: 56,\n  borderRadius: 16,\n  paddingHorizontal: 16\n};\nvar getExtendedFabDimensions = function getExtendedFabDimensions(customSize) {\n  return {\n    height: customSize,\n    paddingHorizontal: 16\n  };\n};\nexport var getExtendedFabStyle = function getExtendedFabStyle(_ref0) {\n  var customSize = _ref0.customSize,\n    theme = _ref0.theme;\n  if (customSize) return getExtendedFabDimensions(customSize);\n  var isV3 = theme.isV3;\n  return isV3 ? v3Extended : extended;\n};\nvar cachedContext = null;\nvar getCanvasContext = function getCanvasContext() {\n  if (cachedContext) {\n    return cachedContext;\n  }\n  var canvas = document.createElement('canvas');\n  cachedContext = canvas.getContext('2d');\n  return cachedContext;\n};\nexport var getLabelSizeWeb = function getLabelSizeWeb(ref) {\n  var _metrics$fontBounding, _metrics$fontBounding2;\n  if (Platform.OS !== 'web' || ref.current === null) {\n    return null;\n  }\n  var canvasContext = getCanvasContext();\n  if (!canvasContext) {\n    return null;\n  }\n  var elementStyles = window.getComputedStyle(ref.current);\n  canvasContext.font = elementStyles.font;\n  var metrics = canvasContext.measureText(ref.current.innerText);\n  return {\n    width: metrics.width,\n    height: ((_metrics$fontBounding = metrics.fontBoundingBoxAscent) != null ? _metrics$fontBounding : 0) + ((_metrics$fontBounding2 = metrics.fontBoundingBoxDescent) != null ? _metrics$fontBounding2 : 0)\n  };\n};", "map": {"version": 3, "names": ["color", "black", "white", "getContrastingColor", "getCombinedStyles", "_ref", "isAnimatedFromRight", "isIconStatic", "distance", "animFAB", "isRTL", "I18nManager", "defaultPositionStyles", "left", "right", "undefined", "combinedStyles", "innerWrapper", "_objectSpread", "iconWrapper", "absoluteFill", "animatedFromRight", "animatedFromRightRTL", "animatedFromLeft", "animatedFromLeftRTL", "transform", "translateX", "interpolate", "inputRange", "outputRange", "Math", "abs", "getBackgroundColor", "_ref2", "theme", "isVariant", "disabled", "customBackgroundColor", "_theme$colors", "isV3", "colors", "surfaceDisabled", "primaryContainer", "secondaryContainer", "tertiaryContainer", "elevation", "level3", "dark", "alpha", "rgb", "string", "accent", "getForegroundColor", "_ref3", "backgroundColor", "customColor", "onSurfaceDisabled", "onPrimaryContainer", "onSecondaryContainer", "onTertiaryContainer", "primary", "getFABColors", "_ref4", "variant", "customRippleColor", "variantToCompare", "baseFABColorProps", "foregroundColor", "rippleColor", "getLabelColor", "_ref5", "onSurface", "text", "fade", "getBackdropColor", "_ref6", "customBackdropColor", "_theme$colors2", "background", "backdrop", "getStackedFABBackgroundColor", "_ref7", "surface", "getFABGroupColors", "_ref8", "labelColor", "backdropColor", "stackedFABBackgroundColor", "standardSize", "height", "width", "borderRadius", "smallSize", "v3SmallSize", "v3MediumSize", "v3LargeSize", "getCustomFabSize", "customSize", "roundness", "getFabStyle", "_ref9", "size", "extended", "paddingHorizontal", "v3Extended", "getExtendedFabDimensions", "getExtendedFabStyle", "_ref0", "cachedContext", "getCanvasContext", "canvas", "document", "createElement", "getContext", "getLabelSizeWeb", "ref", "_metrics$fontBounding", "_metrics$fontBounding2", "Platform", "OS", "current", "canvasContext", "elementStyles", "window", "getComputedStyle", "font", "metrics", "measureText", "innerText", "fontBoundingBoxAscent", "fontBoundingBoxDescent"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\FAB\\utils.ts"], "sourcesContent": ["import { MutableRefObject } from 'react';\nimport {\n  Animated,\n  ColorValue,\n  I18nManager,\n  Platform,\n  ViewStyle,\n} from 'react-native';\n\nimport color from 'color';\n\nimport { black, white } from '../../styles/themes/v2/colors';\nimport type { InternalTheme } from '../../types';\nimport getContrastingColor from '../../utils/getContrastingColor';\n\ntype GetCombinedStylesProps = {\n  isAnimatedFromRight: boolean;\n  isIconStatic: boolean;\n  distance: number;\n  animFAB: Animated.Value;\n};\n\ntype CombinedStyles = {\n  innerWrapper: Animated.WithAnimatedValue<ViewStyle>;\n  iconWrapper: Animated.WithAnimatedValue<ViewStyle>;\n  absoluteFill: Animated.WithAnimatedValue<ViewStyle>;\n};\n\ntype Variant = 'primary' | 'secondary' | 'tertiary' | 'surface';\n\ntype BaseProps = {\n  isVariant: (variant: Variant) => boolean;\n  theme: InternalTheme;\n  disabled?: boolean;\n};\n\nexport const getCombinedStyles = ({\n  isAnimatedFromRight,\n  isIconStatic,\n  distance,\n  animFAB,\n}: GetCombinedStylesProps): CombinedStyles => {\n  const { isRTL } = I18nManager;\n\n  const defaultPositionStyles = { left: -distance, right: undefined };\n\n  const combinedStyles: CombinedStyles = {\n    innerWrapper: {\n      ...defaultPositionStyles,\n    },\n    iconWrapper: {\n      ...defaultPositionStyles,\n    },\n    absoluteFill: {},\n  };\n\n  const animatedFromRight = isAnimatedFromRight && !isRTL;\n  const animatedFromRightRTL = isAnimatedFromRight && isRTL;\n  const animatedFromLeft = !isAnimatedFromRight && !isRTL;\n  const animatedFromLeftRTL = !isAnimatedFromRight && isRTL;\n\n  if (animatedFromRight) {\n    combinedStyles.innerWrapper.transform = [\n      {\n        translateX: animFAB.interpolate({\n          inputRange: [distance, 0],\n          outputRange: [distance, 0],\n        }),\n      },\n    ];\n    combinedStyles.iconWrapper.transform = [\n      {\n        translateX: isIconStatic ? 0 : animFAB,\n      },\n    ];\n    combinedStyles.absoluteFill.transform = [\n      {\n        translateX: animFAB.interpolate({\n          inputRange: [distance, 0],\n          outputRange: [Math.abs(distance) / 2, Math.abs(distance)],\n        }),\n      },\n    ];\n  } else if (animatedFromRightRTL) {\n    combinedStyles.iconWrapper.transform = [\n      {\n        translateX: isIconStatic\n          ? 0\n          : animFAB.interpolate({\n              inputRange: [distance, 0],\n              outputRange: [-distance, 0],\n            }),\n      },\n    ];\n    combinedStyles.innerWrapper.transform = [\n      {\n        translateX: animFAB.interpolate({\n          inputRange: [distance, 0],\n          outputRange: [-distance, 0],\n        }),\n      },\n    ];\n    combinedStyles.absoluteFill.transform = [\n      {\n        translateX: animFAB.interpolate({\n          inputRange: [distance, 0],\n          outputRange: [0, distance],\n        }),\n      },\n    ];\n  } else if (animatedFromLeft) {\n    combinedStyles.iconWrapper.transform = [\n      {\n        translateX: isIconStatic\n          ? distance\n          : animFAB.interpolate({\n              inputRange: [0, distance],\n              outputRange: [distance, distance * 2],\n            }),\n      },\n    ];\n    combinedStyles.innerWrapper.transform = [\n      {\n        translateX: animFAB,\n      },\n    ];\n    combinedStyles.absoluteFill.transform = [\n      {\n        translateX: animFAB.interpolate({\n          inputRange: [0, distance],\n          outputRange: [0, Math.abs(distance) / 2],\n        }),\n      },\n    ];\n  } else if (animatedFromLeftRTL) {\n    combinedStyles.iconWrapper.transform = [\n      {\n        translateX: isIconStatic\n          ? animFAB.interpolate({\n              inputRange: [0, distance],\n              outputRange: [-distance, -distance * 2],\n            })\n          : -distance,\n      },\n    ];\n    combinedStyles.innerWrapper.transform = [\n      {\n        translateX: animFAB.interpolate({\n          inputRange: [0, distance],\n          outputRange: [0, -distance],\n        }),\n      },\n    ];\n    combinedStyles.absoluteFill.transform = [\n      {\n        translateX: animFAB.interpolate({\n          inputRange: [0, distance],\n          outputRange: [0, -distance],\n        }),\n      },\n    ];\n  }\n\n  return combinedStyles;\n};\n\nconst getBackgroundColor = ({\n  theme,\n  isVariant,\n  disabled,\n  customBackgroundColor,\n}: BaseProps & { customBackgroundColor?: ColorValue }) => {\n  if (customBackgroundColor && !disabled) {\n    return customBackgroundColor;\n  }\n\n  if (theme.isV3) {\n    if (disabled) {\n      return theme.colors.surfaceDisabled;\n    }\n\n    if (isVariant('primary')) {\n      return theme.colors.primaryContainer;\n    }\n\n    if (isVariant('secondary')) {\n      return theme.colors.secondaryContainer;\n    }\n\n    if (isVariant('tertiary')) {\n      return theme.colors.tertiaryContainer;\n    }\n\n    if (isVariant('surface')) {\n      return theme.colors.elevation.level3;\n    }\n  }\n\n  if (disabled) {\n    if (theme.dark) {\n      return color(white).alpha(0.12).rgb().string();\n    }\n    return color(black).alpha(0.12).rgb().string();\n  }\n\n  //@ts-ignore\n  return theme.colors?.accent;\n};\n\nconst getForegroundColor = ({\n  theme,\n  isVariant,\n  disabled,\n  backgroundColor,\n  customColor,\n}: BaseProps & { backgroundColor: string; customColor?: string }) => {\n  if (typeof customColor !== 'undefined' && !disabled) {\n    return customColor;\n  }\n\n  if (theme.isV3) {\n    if (disabled) {\n      return theme.colors.onSurfaceDisabled;\n    }\n\n    if (isVariant('primary')) {\n      return theme.colors.onPrimaryContainer;\n    }\n\n    if (isVariant('secondary')) {\n      return theme.colors.onSecondaryContainer;\n    }\n\n    if (isVariant('tertiary')) {\n      return theme.colors.onTertiaryContainer;\n    }\n\n    if (isVariant('surface')) {\n      return theme.colors.primary;\n    }\n  }\n\n  if (disabled) {\n    if (theme.dark) {\n      return color(white).alpha(0.32).rgb().string();\n    }\n    return color(black).alpha(0.32).rgb().string();\n  }\n\n  if (backgroundColor) {\n    return getContrastingColor(\n      backgroundColor || white,\n      white,\n      'rgba(0, 0, 0, .54)'\n    );\n  }\n\n  return getContrastingColor(white, white, 'rgba(0, 0, 0, .54)');\n};\n\nexport const getFABColors = ({\n  theme,\n  variant,\n  disabled,\n  customColor,\n  customBackgroundColor,\n  customRippleColor,\n}: {\n  theme: InternalTheme;\n  variant: string;\n  disabled?: boolean;\n  customColor?: string;\n  customBackgroundColor?: ColorValue;\n  customRippleColor?: ColorValue;\n}) => {\n  const isVariant = (variantToCompare: Variant) => {\n    return variant === variantToCompare;\n  };\n\n  const baseFABColorProps = { theme, isVariant, disabled };\n\n  const backgroundColor = getBackgroundColor({\n    ...baseFABColorProps,\n    customBackgroundColor,\n  });\n\n  const foregroundColor = getForegroundColor({\n    ...baseFABColorProps,\n    customColor,\n    backgroundColor,\n  });\n\n  return {\n    backgroundColor,\n    foregroundColor,\n    rippleColor:\n      customRippleColor || color(foregroundColor).alpha(0.12).rgb().string(),\n  };\n};\n\nconst getLabelColor = ({ theme }: { theme: InternalTheme }) => {\n  if (theme.isV3) {\n    return theme.colors.onSurface;\n  }\n\n  if (theme.dark) {\n    return theme.colors.text;\n  }\n\n  return color(theme.colors.text).fade(0.54).rgb().string();\n};\n\nconst getBackdropColor = ({\n  theme,\n  customBackdropColor,\n}: {\n  theme: InternalTheme;\n  customBackdropColor?: string;\n}) => {\n  if (customBackdropColor) {\n    return customBackdropColor;\n  }\n  if (theme.isV3) {\n    return color(theme.colors.background).alpha(0.95).rgb().string();\n  }\n  return theme.colors?.backdrop;\n};\n\nconst getStackedFABBackgroundColor = ({ theme }: { theme: InternalTheme }) => {\n  if (theme.isV3) {\n    return theme.colors.elevation.level3;\n  }\n  return theme.colors.surface;\n};\n\nexport const getFABGroupColors = ({\n  theme,\n  customBackdropColor,\n}: {\n  theme: InternalTheme;\n  customBackdropColor?: string;\n}) => {\n  return {\n    labelColor: getLabelColor({ theme }),\n    backdropColor: getBackdropColor({ theme, customBackdropColor }),\n    stackedFABBackgroundColor: getStackedFABBackgroundColor({ theme }),\n  };\n};\n\nconst standardSize = {\n  height: 56,\n  width: 56,\n  borderRadius: 28,\n};\nconst smallSize = {\n  height: 40,\n  width: 40,\n  borderRadius: 28,\n};\nconst v3SmallSize = {\n  height: 40,\n  width: 40,\n};\nconst v3MediumSize = {\n  height: 56,\n  width: 56,\n};\nconst v3LargeSize = {\n  height: 96,\n  width: 96,\n};\n\nconst getCustomFabSize = (customSize: number, roundness: number) => ({\n  height: customSize,\n  width: customSize,\n  borderRadius: roundness === 0 ? 0 : customSize / roundness,\n});\n\nexport const getFabStyle = ({\n  size,\n  theme,\n  customSize,\n}: {\n  customSize?: number;\n  size: 'small' | 'medium' | 'large';\n  theme: InternalTheme;\n}) => {\n  const { isV3, roundness } = theme;\n\n  if (customSize) return getCustomFabSize(customSize, roundness);\n\n  if (isV3) {\n    switch (size) {\n      case 'small':\n        return { ...v3SmallSize, borderRadius: 3 * roundness };\n      case 'medium':\n        return { ...v3MediumSize, borderRadius: 4 * roundness };\n      case 'large':\n        return { ...v3LargeSize, borderRadius: 7 * roundness };\n    }\n  }\n\n  if (size === 'small') {\n    return smallSize;\n  }\n  return standardSize;\n};\n\nconst extended = {\n  height: 48,\n  paddingHorizontal: 16,\n};\n\nconst v3Extended = {\n  height: 56,\n  borderRadius: 16,\n  paddingHorizontal: 16,\n};\n\nconst getExtendedFabDimensions = (customSize: number) => ({\n  height: customSize,\n  paddingHorizontal: 16,\n});\n\nexport const getExtendedFabStyle = ({\n  customSize,\n  theme,\n}: {\n  customSize?: number;\n  theme: InternalTheme;\n}) => {\n  if (customSize) return getExtendedFabDimensions(customSize);\n\n  const { isV3 } = theme;\n\n  return isV3 ? v3Extended : extended;\n};\n\nlet cachedContext: CanvasRenderingContext2D | null = null;\n\nconst getCanvasContext = () => {\n  if (cachedContext) {\n    return cachedContext;\n  }\n\n  const canvas = document.createElement('canvas');\n  cachedContext = canvas.getContext('2d');\n\n  return cachedContext;\n};\n\nexport const getLabelSizeWeb = (ref: MutableRefObject<HTMLElement | null>) => {\n  if (Platform.OS !== 'web' || ref.current === null) {\n    return null;\n  }\n\n  const canvasContext = getCanvasContext();\n\n  if (!canvasContext) {\n    return null;\n  }\n\n  const elementStyles = window.getComputedStyle(ref.current);\n  canvasContext.font = elementStyles.font;\n\n  const metrics = canvasContext.measureText(ref.current.innerText);\n\n  return {\n    width: metrics.width,\n    height:\n      (metrics.fontBoundingBoxAscent ?? 0) +\n      (metrics.fontBoundingBoxDescent ?? 0),\n  };\n};\n"], "mappings": ";;;;;AASA,OAAOA,KAAK,MAAM,OAAO;AAEzB,SAASC,KAAK,EAAEC,KAAK;AAErB,OAAOC,mBAAmB;AAuB1B,OAAO,IAAMC,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAAC,IAAA,EAKgB;EAAA,IAJ5CC,mBAAmB,GAAAD,IAAA,CAAnBC,mBAAmB;IACnBC,YAAY,GAAAF,IAAA,CAAZE,YAAY;IACZC,QAAQ,GAAAH,IAAA,CAARG,QAAQ;IACRC,OAAA,GAAAJ,IAAA,CAAAI,OAAA;EAEA,IAAQC,KAAA,GAAUC,WAAW,CAArBD,KAAA;EAER,IAAME,qBAAqB,GAAG;IAAEC,IAAI,EAAE,CAACL,QAAQ;IAAEM,KAAK,EAAEC;EAAU,CAAC;EAEnE,IAAMC,cAA8B,GAAG;IACrCC,YAAY,EAAAC,aAAA,KACPN,qBAAA,CACJ;IACDO,WAAW,EAAAD,aAAA,KACNN,qBAAA,CACJ;IACDQ,YAAY,EAAE,CAAC;EACjB,CAAC;EAED,IAAMC,iBAAiB,GAAGf,mBAAmB,IAAI,CAACI,KAAK;EACvD,IAAMY,oBAAoB,GAAGhB,mBAAmB,IAAII,KAAK;EACzD,IAAMa,gBAAgB,GAAG,CAACjB,mBAAmB,IAAI,CAACI,KAAK;EACvD,IAAMc,mBAAmB,GAAG,CAAClB,mBAAmB,IAAII,KAAK;EAEzD,IAAIW,iBAAiB,EAAE;IACrBL,cAAc,CAACC,YAAY,CAACQ,SAAS,GAAG,CACtC;MACEC,UAAU,EAAEjB,OAAO,CAACkB,WAAW,CAAC;QAC9BC,UAAU,EAAE,CAACpB,QAAQ,EAAE,CAAC,CAAC;QACzBqB,WAAW,EAAE,CAACrB,QAAQ,EAAE,CAAC;MAC3B,CAAC;IACH,CAAC,CACF;IACDQ,cAAc,CAACG,WAAW,CAACM,SAAS,GAAG,CACrC;MACEC,UAAU,EAAEnB,YAAY,GAAG,CAAC,GAAGE;IACjC,CAAC,CACF;IACDO,cAAc,CAACI,YAAY,CAACK,SAAS,GAAG,CACtC;MACEC,UAAU,EAAEjB,OAAO,CAACkB,WAAW,CAAC;QAC9BC,UAAU,EAAE,CAACpB,QAAQ,EAAE,CAAC,CAAC;QACzBqB,WAAW,EAAE,CAACC,IAAI,CAACC,GAAG,CAACvB,QAAQ,CAAC,GAAG,CAAC,EAAEsB,IAAI,CAACC,GAAG,CAACvB,QAAQ,CAAC;MAC1D,CAAC;IACH,CAAC,CACF;EACH,CAAC,MAAM,IAAIc,oBAAoB,EAAE;IAC/BN,cAAc,CAACG,WAAW,CAACM,SAAS,GAAG,CACrC;MACEC,UAAU,EAAEnB,YAAY,GACpB,CAAC,GACDE,OAAO,CAACkB,WAAW,CAAC;QAClBC,UAAU,EAAE,CAACpB,QAAQ,EAAE,CAAC,CAAC;QACzBqB,WAAW,EAAE,CAAC,CAACrB,QAAQ,EAAE,CAAC;MAC5B,CAAC;IACP,CAAC,CACF;IACDQ,cAAc,CAACC,YAAY,CAACQ,SAAS,GAAG,CACtC;MACEC,UAAU,EAAEjB,OAAO,CAACkB,WAAW,CAAC;QAC9BC,UAAU,EAAE,CAACpB,QAAQ,EAAE,CAAC,CAAC;QACzBqB,WAAW,EAAE,CAAC,CAACrB,QAAQ,EAAE,CAAC;MAC5B,CAAC;IACH,CAAC,CACF;IACDQ,cAAc,CAACI,YAAY,CAACK,SAAS,GAAG,CACtC;MACEC,UAAU,EAAEjB,OAAO,CAACkB,WAAW,CAAC;QAC9BC,UAAU,EAAE,CAACpB,QAAQ,EAAE,CAAC,CAAC;QACzBqB,WAAW,EAAE,CAAC,CAAC,EAAErB,QAAQ;MAC3B,CAAC;IACH,CAAC,CACF;EACH,CAAC,MAAM,IAAIe,gBAAgB,EAAE;IAC3BP,cAAc,CAACG,WAAW,CAACM,SAAS,GAAG,CACrC;MACEC,UAAU,EAAEnB,YAAY,GACpBC,QAAQ,GACRC,OAAO,CAACkB,WAAW,CAAC;QAClBC,UAAU,EAAE,CAAC,CAAC,EAAEpB,QAAQ,CAAC;QACzBqB,WAAW,EAAE,CAACrB,QAAQ,EAAEA,QAAQ,GAAG,CAAC;MACtC,CAAC;IACP,CAAC,CACF;IACDQ,cAAc,CAACC,YAAY,CAACQ,SAAS,GAAG,CACtC;MACEC,UAAU,EAAEjB;IACd,CAAC,CACF;IACDO,cAAc,CAACI,YAAY,CAACK,SAAS,GAAG,CACtC;MACEC,UAAU,EAAEjB,OAAO,CAACkB,WAAW,CAAC;QAC9BC,UAAU,EAAE,CAAC,CAAC,EAAEpB,QAAQ,CAAC;QACzBqB,WAAW,EAAE,CAAC,CAAC,EAAEC,IAAI,CAACC,GAAG,CAACvB,QAAQ,CAAC,GAAG,CAAC;MACzC,CAAC;IACH,CAAC,CACF;EACH,CAAC,MAAM,IAAIgB,mBAAmB,EAAE;IAC9BR,cAAc,CAACG,WAAW,CAACM,SAAS,GAAG,CACrC;MACEC,UAAU,EAAEnB,YAAY,GACpBE,OAAO,CAACkB,WAAW,CAAC;QAClBC,UAAU,EAAE,CAAC,CAAC,EAAEpB,QAAQ,CAAC;QACzBqB,WAAW,EAAE,CAAC,CAACrB,QAAQ,EAAE,CAACA,QAAQ,GAAG,CAAC;MACxC,CAAC,CAAC,GACF,CAACA;IACP,CAAC,CACF;IACDQ,cAAc,CAACC,YAAY,CAACQ,SAAS,GAAG,CACtC;MACEC,UAAU,EAAEjB,OAAO,CAACkB,WAAW,CAAC;QAC9BC,UAAU,EAAE,CAAC,CAAC,EAAEpB,QAAQ,CAAC;QACzBqB,WAAW,EAAE,CAAC,CAAC,EAAE,CAACrB,QAAQ;MAC5B,CAAC;IACH,CAAC,CACF;IACDQ,cAAc,CAACI,YAAY,CAACK,SAAS,GAAG,CACtC;MACEC,UAAU,EAAEjB,OAAO,CAACkB,WAAW,CAAC;QAC9BC,UAAU,EAAE,CAAC,CAAC,EAAEpB,QAAQ,CAAC;QACzBqB,WAAW,EAAE,CAAC,CAAC,EAAE,CAACrB,QAAQ;MAC5B,CAAC;IACH,CAAC,CACF;EACH;EAEA,OAAOQ,cAAc;AACvB,CAAC;AAED,IAAMgB,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAAC,KAAA,EAKkC;EAAA,IAJxDC,KAAK,GAAAD,KAAA,CAALC,KAAK;IACLC,SAAS,GAAAF,KAAA,CAATE,SAAS;IACTC,QAAQ,GAAAH,KAAA,CAARG,QAAQ;IACRC,qBAAA,GAAAJ,KAAA,CAAAI,qBAAA;EACwD,IAAAC,aAAA;EACxD,IAAID,qBAAqB,IAAI,CAACD,QAAQ,EAAE;IACtC,OAAOC,qBAAqB;EAC9B;EAEA,IAAIH,KAAK,CAACK,IAAI,EAAE;IACd,IAAIH,QAAQ,EAAE;MACZ,OAAOF,KAAK,CAACM,MAAM,CAACC,eAAe;IACrC;IAEA,IAAIN,SAAS,CAAC,SAAS,CAAC,EAAE;MACxB,OAAOD,KAAK,CAACM,MAAM,CAACE,gBAAgB;IACtC;IAEA,IAAIP,SAAS,CAAC,WAAW,CAAC,EAAE;MAC1B,OAAOD,KAAK,CAACM,MAAM,CAACG,kBAAkB;IACxC;IAEA,IAAIR,SAAS,CAAC,UAAU,CAAC,EAAE;MACzB,OAAOD,KAAK,CAACM,MAAM,CAACI,iBAAiB;IACvC;IAEA,IAAIT,SAAS,CAAC,SAAS,CAAC,EAAE;MACxB,OAAOD,KAAK,CAACM,MAAM,CAACK,SAAS,CAACC,MAAM;IACtC;EACF;EAEA,IAAIV,QAAQ,EAAE;IACZ,IAAIF,KAAK,CAACa,IAAI,EAAE;MACd,OAAO/C,KAAK,CAACE,KAAK,CAAC,CAAC8C,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;IAChD;IACA,OAAOlD,KAAK,CAACC,KAAK,CAAC,CAAC+C,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EAChD;EAGA,QAAAZ,aAAA,GAAOJ,KAAK,CAACM,MAAM,cAAAF,aAAA,uBAAZA,aAAA,CAAca,MAAM;AAC7B,CAAC;AAED,IAAMC,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAAC,KAAA,EAM6C;EAAA,IALnEnB,KAAK,GAAAmB,KAAA,CAALnB,KAAK;IACLC,SAAS,GAAAkB,KAAA,CAATlB,SAAS;IACTC,QAAQ,GAAAiB,KAAA,CAARjB,QAAQ;IACRkB,eAAe,GAAAD,KAAA,CAAfC,eAAe;IACfC,WAAA,GAAAF,KAAA,CAAAE,WAAA;EAEA,IAAI,OAAOA,WAAW,KAAK,WAAW,IAAI,CAACnB,QAAQ,EAAE;IACnD,OAAOmB,WAAW;EACpB;EAEA,IAAIrB,KAAK,CAACK,IAAI,EAAE;IACd,IAAIH,QAAQ,EAAE;MACZ,OAAOF,KAAK,CAACM,MAAM,CAACgB,iBAAiB;IACvC;IAEA,IAAIrB,SAAS,CAAC,SAAS,CAAC,EAAE;MACxB,OAAOD,KAAK,CAACM,MAAM,CAACiB,kBAAkB;IACxC;IAEA,IAAItB,SAAS,CAAC,WAAW,CAAC,EAAE;MAC1B,OAAOD,KAAK,CAACM,MAAM,CAACkB,oBAAoB;IAC1C;IAEA,IAAIvB,SAAS,CAAC,UAAU,CAAC,EAAE;MACzB,OAAOD,KAAK,CAACM,MAAM,CAACmB,mBAAmB;IACzC;IAEA,IAAIxB,SAAS,CAAC,SAAS,CAAC,EAAE;MACxB,OAAOD,KAAK,CAACM,MAAM,CAACoB,OAAO;IAC7B;EACF;EAEA,IAAIxB,QAAQ,EAAE;IACZ,IAAIF,KAAK,CAACa,IAAI,EAAE;MACd,OAAO/C,KAAK,CAACE,KAAK,CAAC,CAAC8C,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;IAChD;IACA,OAAOlD,KAAK,CAACC,KAAK,CAAC,CAAC+C,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EAChD;EAEA,IAAII,eAAe,EAAE;IACnB,OAAOnD,mBAAmB,CACxBmD,eAAe,IAAIpD,KAAK,EACxBA,KAAK,EACL,oBACF,CAAC;EACH;EAEA,OAAOC,mBAAmB,CAACD,KAAK,EAAEA,KAAK,EAAE,oBAAoB,CAAC;AAChE,CAAC;AAED,OAAO,IAAM2D,YAAY,GAAG,SAAfA,YAAYA,CAAAC,KAAA,EAcnB;EAAA,IAbJ5B,KAAK,GAAA4B,KAAA,CAAL5B,KAAK;IACL6B,OAAO,GAAAD,KAAA,CAAPC,OAAO;IACP3B,QAAQ,GAAA0B,KAAA,CAAR1B,QAAQ;IACRmB,WAAW,GAAAO,KAAA,CAAXP,WAAW;IACXlB,qBAAqB,GAAAyB,KAAA,CAArBzB,qBAAqB;IACrB2B,iBAAA,GAAAF,KAAA,CAAAE,iBAAA;EASA,IAAM7B,SAAS,GAAI,SAAbA,SAASA,CAAI8B,gBAAyB,EAAK;IAC/C,OAAOF,OAAO,KAAKE,gBAAgB;EACrC,CAAC;EAED,IAAMC,iBAAiB,GAAG;IAAEhC,KAAK,EAALA,KAAK;IAAEC,SAAS,EAATA,SAAS;IAAEC,QAAA,EAAAA;EAAS,CAAC;EAExD,IAAMkB,eAAe,GAAGtB,kBAAkB,CAAAd,aAAA,CAAAA,aAAA,KACrCgD,iBAAiB;IACpB7B,qBAAA,EAAAA;EAAA,EACD,CAAC;EAEF,IAAM8B,eAAe,GAAGf,kBAAkB,CAAAlC,aAAA,CAAAA,aAAA,KACrCgD,iBAAiB;IACpBX,WAAW,EAAXA,WAAW;IACXD,eAAA,EAAAA;EAAA,EACD,CAAC;EAEF,OAAO;IACLA,eAAe,EAAfA,eAAe;IACfa,eAAe,EAAfA,eAAe;IACfC,WAAW,EACTJ,iBAAiB,IAAIhE,KAAK,CAACmE,eAAe,CAAC,CAACnB,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC;EACzE,CAAC;AACH,CAAC;AAED,IAAMmB,aAAa,GAAG,SAAhBA,aAAaA,CAAAC,KAAA,EAA4C;EAAA,IAAtCpC,KAAA,GAAAoC,KAAA,CAAApC,KAAA;EACvB,IAAIA,KAAK,CAACK,IAAI,EAAE;IACd,OAAOL,KAAK,CAACM,MAAM,CAAC+B,SAAS;EAC/B;EAEA,IAAIrC,KAAK,CAACa,IAAI,EAAE;IACd,OAAOb,KAAK,CAACM,MAAM,CAACgC,IAAI;EAC1B;EAEA,OAAOxE,KAAK,CAACkC,KAAK,CAACM,MAAM,CAACgC,IAAI,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAACxB,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;AAC3D,CAAC;AAED,IAAMwB,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAAC,KAAA,EAMhB;EAAA,IALJzC,KAAK,GAAAyC,KAAA,CAALzC,KAAK;IACL0C,mBAAA,GAAAD,KAAA,CAAAC,mBAAA;EAII,IAAAC,cAAA;EACJ,IAAID,mBAAmB,EAAE;IACvB,OAAOA,mBAAmB;EAC5B;EACA,IAAI1C,KAAK,CAACK,IAAI,EAAE;IACd,OAAOvC,KAAK,CAACkC,KAAK,CAACM,MAAM,CAACsC,UAAU,CAAC,CAAC9B,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EAClE;EACA,QAAA2B,cAAA,GAAO3C,KAAK,CAACM,MAAM,cAAAqC,cAAA,uBAAZA,cAAA,CAAcE,QAAQ;AAC/B,CAAC;AAED,IAAMC,4BAA4B,GAAG,SAA/BA,4BAA4BA,CAAAC,KAAA,EAA4C;EAAA,IAAtC/C,KAAA,GAAA+C,KAAA,CAAA/C,KAAA;EACtC,IAAIA,KAAK,CAACK,IAAI,EAAE;IACd,OAAOL,KAAK,CAACM,MAAM,CAACK,SAAS,CAACC,MAAM;EACtC;EACA,OAAOZ,KAAK,CAACM,MAAM,CAAC0C,OAAO;AAC7B,CAAC;AAED,OAAO,IAAMC,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAAC,KAAA,EAMxB;EAAA,IALJlD,KAAK,GAAAkD,KAAA,CAALlD,KAAK;IACL0C,mBAAA,GAAAQ,KAAA,CAAAR,mBAAA;EAKA,OAAO;IACLS,UAAU,EAAEhB,aAAa,CAAC;MAAEnC,KAAA,EAAAA;IAAM,CAAC,CAAC;IACpCoD,aAAa,EAAEZ,gBAAgB,CAAC;MAAExC,KAAK,EAALA,KAAK;MAAE0C,mBAAA,EAAAA;IAAoB,CAAC,CAAC;IAC/DW,yBAAyB,EAAEP,4BAA4B,CAAC;MAAE9C,KAAA,EAAAA;IAAM,CAAC;EACnE,CAAC;AACH,CAAC;AAED,IAAMsD,YAAY,GAAG;EACnBC,MAAM,EAAE,EAAE;EACVC,KAAK,EAAE,EAAE;EACTC,YAAY,EAAE;AAChB,CAAC;AACD,IAAMC,SAAS,GAAG;EAChBH,MAAM,EAAE,EAAE;EACVC,KAAK,EAAE,EAAE;EACTC,YAAY,EAAE;AAChB,CAAC;AACD,IAAME,WAAW,GAAG;EAClBJ,MAAM,EAAE,EAAE;EACVC,KAAK,EAAE;AACT,CAAC;AACD,IAAMI,YAAY,GAAG;EACnBL,MAAM,EAAE,EAAE;EACVC,KAAK,EAAE;AACT,CAAC;AACD,IAAMK,WAAW,GAAG;EAClBN,MAAM,EAAE,EAAE;EACVC,KAAK,EAAE;AACT,CAAC;AAED,IAAMM,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIC,UAAkB,EAAEC,SAAiB;EAAA,OAAM;IACnET,MAAM,EAAEQ,UAAU;IAClBP,KAAK,EAAEO,UAAU;IACjBN,YAAY,EAAEO,SAAS,KAAK,CAAC,GAAG,CAAC,GAAGD,UAAU,GAAGC;EACnD,CAAC;AAAA,CAAC;AAEF,OAAO,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAAC,KAAA,EAQlB;EAAA,IAPJC,IAAI,GAAAD,KAAA,CAAJC,IAAI;IACJnE,KAAK,GAAAkE,KAAA,CAALlE,KAAK;IACL+D,UAAA,GAAAG,KAAA,CAAAH,UAAA;EAMA,IAAQ1D,IAAI,GAAgBL,KAAK,CAAzBK,IAAI;IAAE2D,SAAA,GAAchE,KAAK,CAAnBgE,SAAA;EAEd,IAAID,UAAU,EAAE,OAAOD,gBAAgB,CAACC,UAAU,EAAEC,SAAS,CAAC;EAE9D,IAAI3D,IAAI,EAAE;IACR,QAAQ8D,IAAI;MACV,KAAK,OAAO;QACV,OAAAnF,aAAA,CAAAA,aAAA,KAAY2E,WAAW;UAAEF,YAAY,EAAE,CAAC,GAAGO;QAAA;MAC7C,KAAK,QAAQ;QACX,OAAAhF,aAAA,CAAAA,aAAA,KAAY4E,YAAY;UAAEH,YAAY,EAAE,CAAC,GAAGO;QAAA;MAC9C,KAAK,OAAO;QACV,OAAAhF,aAAA,CAAAA,aAAA,KAAY6E,WAAW;UAAEJ,YAAY,EAAE,CAAC,GAAGO;QAAA;IAC/C;EACF;EAEA,IAAIG,IAAI,KAAK,OAAO,EAAE;IACpB,OAAOT,SAAS;EAClB;EACA,OAAOJ,YAAY;AACrB,CAAC;AAED,IAAMc,QAAQ,GAAG;EACfb,MAAM,EAAE,EAAE;EACVc,iBAAiB,EAAE;AACrB,CAAC;AAED,IAAMC,UAAU,GAAG;EACjBf,MAAM,EAAE,EAAE;EACVE,YAAY,EAAE,EAAE;EAChBY,iBAAiB,EAAE;AACrB,CAAC;AAED,IAAME,wBAAwB,GAAI,SAA5BA,wBAAwBA,CAAIR,UAAkB;EAAA,OAAM;IACxDR,MAAM,EAAEQ,UAAU;IAClBM,iBAAiB,EAAE;EACrB,CAAC;AAAA,CAAC;AAEF,OAAO,IAAMG,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAAC,KAAA,EAM1B;EAAA,IALJV,UAAU,GAAAU,KAAA,CAAVV,UAAU;IACV/D,KAAA,GAAAyE,KAAA,CAAAzE,KAAA;EAKA,IAAI+D,UAAU,EAAE,OAAOQ,wBAAwB,CAACR,UAAU,CAAC;EAE3D,IAAQ1D,IAAA,GAASL,KAAK,CAAdK,IAAA;EAER,OAAOA,IAAI,GAAGiE,UAAU,GAAGF,QAAQ;AACrC,CAAC;AAED,IAAIM,aAA8C,GAAG,IAAI;AAEzD,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA,EAAS;EAC7B,IAAID,aAAa,EAAE;IACjB,OAAOA,aAAa;EACtB;EAEA,IAAME,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EAC/CJ,aAAa,GAAGE,MAAM,CAACG,UAAU,CAAC,IAAI,CAAC;EAEvC,OAAOL,aAAa;AACtB,CAAC;AAED,OAAO,IAAMM,eAAe,GAAI,SAAnBA,eAAeA,CAAIC,GAAyC,EAAK;EAAA,IAAAC,qBAAA,EAAAC,sBAAA;EAC5E,IAAIC,QAAQ,CAACC,EAAE,KAAK,KAAK,IAAIJ,GAAG,CAACK,OAAO,KAAK,IAAI,EAAE;IACjD,OAAO,IAAI;EACb;EAEA,IAAMC,aAAa,GAAGZ,gBAAgB,CAAC,CAAC;EAExC,IAAI,CAACY,aAAa,EAAE;IAClB,OAAO,IAAI;EACb;EAEA,IAAMC,aAAa,GAAGC,MAAM,CAACC,gBAAgB,CAACT,GAAG,CAACK,OAAO,CAAC;EAC1DC,aAAa,CAACI,IAAI,GAAGH,aAAa,CAACG,IAAI;EAEvC,IAAMC,OAAO,GAAGL,aAAa,CAACM,WAAW,CAACZ,GAAG,CAACK,OAAO,CAACQ,SAAS,CAAC;EAEhE,OAAO;IACLtC,KAAK,EAAEoC,OAAO,CAACpC,KAAK;IACpBD,MAAM,EACJ,EAAA2B,qBAAA,GAACU,OAAO,CAACG,qBAAqB,YAAAb,qBAAA,GAAI,CAAC,MAAAC,sBAAA,GAClCS,OAAO,CAACI,sBAAsB,YAAAb,sBAAA,GAAI,CAAC;EACxC,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}