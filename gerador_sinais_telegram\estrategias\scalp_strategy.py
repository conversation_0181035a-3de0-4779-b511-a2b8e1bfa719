import logging
import pandas as pd
import numpy as np
import ta
from config.settings import LEVERAGE, LOOKBACK_DAYS

logger = logging.getLogger(__name__)

class ScalpStrategy:
    def __init__(self, binance_handler):
        self.binance = binance_handler
        self.leverage = LEVERAGE
        self.lookback_days = LOOKBACK_DAYS
        self.profit_percentage = 0.05  # 5% para cálculo dos TPs

    def analyze_symbol(self, symbol):
        """
        Analisa um símbolo para verificar se há sinal de scalping

        Args:
            symbol: Par de trading (ex: 'BTCUSDT')

        Returns:
            tuple: (signal_type, entry_price, stop_loss, take_profit) ou (None, None, None, None) se não houver sinal
        """
        try:
            # Obter dados históricos de 1 minuto
            df = self.binance.get_historical_klines(
                symbol=symbol,
                interval='1m',
                lookback_days=self.lookback_days
            )

            if df.empty:
                logger.warning(f"Sem dados para {symbol}")
                return None, None, None, None

            # Calcular indicadores
            signal = self._calculate_indicators(df)

            if signal in ['LONG', 'SHORT']:
                entry_price = df['close'].iloc[-1]

                # Validar se o preço de entrada é realista
                if not self._validate_price_range(symbol, entry_price):
                    logger.error(f"Preço de entrada irrealista para {symbol}: {entry_price}")
                    return None, None, None, None

                # Usar o cálculo de stop loss baseado em ATR
                stop_loss = self._calculate_stop_loss(entry_price, signal, df)

                # Ajustar o profit_percentage com base na volatilidade (ATR)
                atr = self._calculate_atr(df)
                atr_percentage = atr / entry_price * 100  # ATR em porcentagem

                # Ajustar o profit_percentage com base na volatilidade (mais conservador)
                if atr_percentage < 0.5:  # Baixa volatilidade
                    self.profit_percentage = 0.015  # 1.5% de movimento alvo
                elif atr_percentage < 1.0:  # Média volatilidade
                    self.profit_percentage = 0.025  # 2.5% de movimento alvo
                else:  # Alta volatilidade
                    self.profit_percentage = 0.04  # 4% de movimento alvo

                take_profit = self._calculate_take_profit(entry_price, signal)

                # Validar se os cálculos fazem sentido
                if not self._validate_signal_values(symbol, signal, entry_price, stop_loss, take_profit):
                    return None, None, None, None

                logger.info(f"{symbol} - Sinal de {signal} gerado com entrada em {entry_price}, SL em {stop_loss} (ATR: {atr_percentage:.2f}%), TP alvo: {self.profit_percentage*100:.1f}%")

                # Garantir que todos os valores são float e válidos
                try:
                    return signal, float(entry_price), float(stop_loss), float(take_profit)
                except (ValueError, TypeError) as e:
                    logger.error(f"Erro ao converter valores para float em {symbol}: {e}")
                    return None, None, None, None

            return None, None, None, None

        except Exception as e:
            logger.error(f"Erro ao analisar {symbol} para scalping: {e}")
            return None, None, None, None

    def _calculate_indicators(self, df):
        """Calcula indicadores técnicos e retorna o sinal"""
        # RSI
        df['RSI'] = ta.momentum.rsi(df['close'], window=7)

        # EMAs
        df['EMA_5'] = ta.trend.ema_indicator(df['close'], window=5)
        df['EMA_20'] = ta.trend.ema_indicator(df['close'], window=20)

        # Obter valores mais recentes
        latest_price = df['close'].iloc[-1]
        latest_rsi = df['RSI'].iloc[-1]
        ema5 = df['EMA_5'].iloc[-1]
        ema20 = df['EMA_20'].iloc[-1]

        # Lógica de sinal
        if (latest_rsi < 40) and (ema5 > ema20) and (latest_price < ema20):
            return 'LONG'
        elif (latest_rsi > 60) and (ema5 < ema20) and (latest_price > ema20):
            return 'SHORT'
        else:
            return 'NEUTRO'

    def _calculate_atr(self, df, period=14):
        """Calcula o Average True Range (ATR)"""
        try:
            # Calcular o ATR usando a biblioteca ta
            df['ATR'] = ta.volatility.average_true_range(
                high=df['high'],
                low=df['low'],
                close=df['close'],
                window=period
            )

            # Retornar o valor mais recente do ATR
            return df['ATR'].iloc[-1]
        except Exception as e:
            logger.error(f"Erro ao calcular ATR: {e}")
            # Fallback para um valor padrão baseado em porcentagem
            return df['close'].iloc[-1] * 0.005  # 0.5% do preço atual

    def _calculate_stop_loss(self, entry_price, signal, df=None):
        """
        Calcula o stop loss baseado no ATR ou em porcentagem fixa

        Args:
            entry_price: Preço de entrada
            signal: Tipo de sinal (LONG/SHORT)
            df: DataFrame com dados históricos (opcional)
        """
        try:
            if df is not None:
                # Calcular ATR
                atr = self._calculate_atr(df)

                # Usar 1.5x ATR para o stop loss
                atr_multiplier = 1.5

                if signal == 'LONG':
                    return entry_price - (atr * atr_multiplier)
                else:  # SHORT
                    return entry_price + (atr * atr_multiplier)
            else:
                # Fallback para o método baseado em porcentagem fixa
                if signal == 'LONG':
                    return entry_price * 0.995  # 0.5% abaixo
                else:  # SHORT
                    return entry_price * 1.005  # 0.5% acima
        except Exception as e:
            logger.error(f"Erro ao calcular stop loss baseado em ATR: {e}")
            # Fallback para o método baseado em porcentagem fixa
            if signal == 'LONG':
                return entry_price * 0.995  # 0.5% abaixo
            else:  # SHORT
                return entry_price * 1.005  # 0.5% acima

    def _calculate_take_profit(self, entry_price, signal):
        """Calcula o take profit baseado no tipo de sinal"""
        if signal == 'LONG':
            return entry_price * (1 + self.profit_percentage)
        else:  # SHORT
            return entry_price * (1 - self.profit_percentage)

    def _validate_price_range(self, symbol, price):
        """Valida se um preço está dentro de uma faixa realista para o símbolo"""
        if price <= 0:
            return False

        # Faixas de preços realistas (com margem de segurança)
        price_ranges = {
            'BTC': (10000, 150000),
            'ETH': (500, 10000),
            'BNB': (50, 1000),
            'ADA': (0.1, 5),
            'SOL': (10, 1000),
            'DOT': (1, 50),
            'LINK': (1, 100),
            'MATIC': (0.1, 10),
            'AVAX': (5, 200),
            'ATOM': (1, 100)
        }

        for coin, (min_price, max_price) in price_ranges.items():
            if coin in symbol:
                return min_price <= price <= max_price

        # Para moedas não mapeadas, aceitar uma faixa ampla
        return 0.001 <= price <= 100000

    def _validate_signal_values(self, symbol, signal_type, entry_price, stop_loss, take_profit):
        """Valida se os valores do sinal fazem sentido"""
        try:
            # Validar se todos os valores são positivos
            if entry_price <= 0 or stop_loss <= 0 or take_profit <= 0:
                logger.error(f"Valores inválidos para {symbol}: entry={entry_price}, sl={stop_loss}, tp={take_profit}")
                return False

            # Validar lógica do stop loss
            if signal_type == 'LONG' and stop_loss >= entry_price:
                logger.error(f"Stop loss LONG inválido para {symbol}: {stop_loss} >= {entry_price}")
                return False
            elif signal_type == 'SHORT' and stop_loss <= entry_price:
                logger.error(f"Stop loss SHORT inválido para {symbol}: {stop_loss} <= {entry_price}")
                return False

            # Validar lógica do take profit
            if signal_type == 'LONG' and take_profit <= entry_price:
                logger.error(f"Take profit LONG inválido para {symbol}: {take_profit} <= {entry_price}")
                return False
            elif signal_type == 'SHORT' and take_profit >= entry_price:
                logger.error(f"Take profit SHORT inválido para {symbol}: {take_profit} >= {entry_price}")
                return False

            # Validar se o take profit não é muito agressivo (máximo 10% para scalp)
            if signal_type == 'LONG':
                max_tp = entry_price * 1.10
                if take_profit > max_tp:
                    logger.warning(f"Take profit muito agressivo para {symbol}, ajustando de {take_profit} para {max_tp}")
                    return False
            else:  # SHORT
                min_tp = entry_price * 0.90
                if take_profit < min_tp:
                    logger.warning(f"Take profit muito agressivo para {symbol}, ajustando de {take_profit} para {min_tp}")
                    return False

            return True

        except Exception as e:
            logger.error(f"Erro ao validar valores do sinal para {symbol}: {e}")
            return False