import React, { useState } from 'react';
import { View, Text } from 'react-native';
import styles from './styles';

const CheckButtons = ({ onFiltered }) => {
  const [isActive, setIsActive] = useState({
    all: true,
    free: false,
    premium: false,
    spot: false,
    futures: false
  });
  
  const handleClick = (item) => {
    if (item === 'all') {
      const active = { all: true, free: false, premium: false, spot: false, futures: false }
      setIsActive({ ...active })
      onFiltered(active)
      return
    }

    isActive[item] = !isActive[item]
    isActive.all = false
    allCheckButtonsNotFilled()

    setIsActive({ ...isActive })
    onFiltered(isActive)
  }

  const allCheckButtonsNotFilled = () => {
    if (!isActive.all && !isActive.free && !isActive.premium && !isActive.spot && !isActive.futures) {
      isActive.all = true
    }
  }

  return (
    <View style={styles.container}>
      <Text
        style={[styles.btn, styles.firstBtn, isActive.all ?  styles.btnChecked: null]}
        onPress={() => handleClick('all')}
      >
        All
      </Text>

      <Text
        style={[styles.btn, isActive.free ? styles.btnChecked: null]}
        onPress={() => handleClick('free')}
      >
        Free
      </Text>

      <Text
        style={[styles.btn, isActive.premium ? styles.btnChecked: null]}
        onPress={() => handleClick('premium')}
      >
        Premium
      </Text>

      <Text
        style={[styles.btn, isActive.spot ? styles.btnChecked: null]}
        onPress={() => handleClick('spot')}
      >
        Spot
      </Text>

      <Text
        style={[styles.btn, styles.lastBtn, isActive.futures ? styles.btnChecked: null]}
        onPress={() => handleClick('futures')}
      >
        Futures
      </Text>
    </View>
  )
}

export default CheckButtons;
