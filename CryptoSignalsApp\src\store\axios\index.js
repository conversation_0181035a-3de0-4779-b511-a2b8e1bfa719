import React from 'react';
import axios from 'axios';
import { getApiConfig } from '../../config/api';

export const AxiosContext = React.createContext();

// Obter configuração da API
const apiConfig = getApiConfig();

// Criar instância do axios
const apiClient = axios.create({
  baseURL: apiConfig.BASE_URL,
  timeout: apiConfig.TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Interceptor para tratamento de erros
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error);

    // Se não conseguir conectar, usar dados mock
    if (error.code === 'ECONNREFUSED' || error.code === 'NETWORK_ERROR') {
      console.warn('Usando dados mock devido a erro de conexão');
      return Promise.resolve({ data: getMockData(error.config.url) });
    }

    return Promise.reject(error);
  }
);

// Função para obter dados mock em caso de erro
function getMockData(url) {
  if (url.includes('/channels')) {
    return getMockChannels();
  } else if (url.includes('/signals')) {
    return getMockSignals();
  } else if (url.includes('/push-notifications/token/')) {
    return { permissions: [] };
  }
  return {};
}

function getMockChannels() {
  return [
    {
      id: 1,
      externalId: 'strategy-scalp',
      name: 'CryptoSignals Scalp',
      description: 'Sinais da estratégia Scalp',
      type: 'FUTURES',
      isPremium: false,
      lastSignalAt: new Date().toISOString(),
      totalSignals: 0,
      recentSignals: 0,
      photo: 'https://via.placeholder.com/50x50/4CAF50/FFFFFF?text=S'
    },
    {
      id: 2,
      externalId: 'strategy-breakout',
      name: 'CryptoSignals Breakout',
      description: 'Sinais da estratégia Breakout',
      type: 'FUTURES',
      isPremium: false,
      lastSignalAt: new Date(Date.now() - 3600000).toISOString(),
      totalSignals: 0,
      recentSignals: 0,
      photo: 'https://via.placeholder.com/50x50/4CAF50/FFFFFF?text=B'
    },
    {
      id: 3,
      externalId: 'strategy-swing',
      name: 'CryptoSignals Swing',
      description: 'Sinais da estratégia Swing',
      type: 'SPOT',
      isPremium: false,
      lastSignalAt: new Date(Date.now() - 7200000).toISOString(),
      totalSignals: 0,
      recentSignals: 0,
      photo: 'https://via.placeholder.com/50x50/4CAF50/FFFFFF?text=S'
    },
  ];
}

function getMockSignals() {
  return [
    {
      id: 1,
      symbol: 'BTCUSDT',
      signal_type: 'LONG',
      strategy: 'Scalp',
      entry_price: 45000,
      stop_loss: 44000,
      take_profit_1: 46000,
      status: 'OPEN',
      leverage: 10,
      createdAt: new Date().toISOString(),
      messageOriginal: 'CRYPTOSIGNALS PROFESSIONAL\n===================================\n\nASSET: BTCUSDT\nSTRATEGY: SCALP\nDIRECTION: LONG\nLEVERAGE: 10x\nTIMEFRAME: 1-4H\n\nENTRY ZONE: 45000.00\nSTOP LOSS: 44000.00\n\nTAKE PROFIT LEVELS:\nTP1: 46000.00\n\nRISK MANAGEMENT:\n- Position size: 1-2% of portfolio\n- Risk/Reward: 1:2.5 minimum\n- Strict stop loss adherence\n\n===================================\nCRYPTOSIGNALS PROFESSIONAL'
    }
  ];
}

// API wrapper com fallback para mock
const api = {
  get: async (url, config = {}) => {
    try {
      const response = await apiClient.get(url, config);
      return response;
    } catch (error) {
      console.warn(`Erro na API GET ${url}, usando dados mock:`, error.message);
      return { data: getMockData(url) };
    }
  },

  post: async (url, data = {}, config = {}) => {
    try {
      const response = await apiClient.post(url, data, config);
      return response;
    } catch (error) {
      console.warn(`Erro na API POST ${url}, usando resposta mock:`, error.message);
      return { data: { success: true, message: 'Mock response' } };
    }
  },

  patch: async (url, data = {}, config = {}) => {
    try {
      const response = await apiClient.patch(url, data, config);
      return response;
    } catch (error) {
      console.warn(`Erro na API PATCH ${url}, usando resposta mock:`, error.message);
      return { data: { success: true, message: 'Mock response' } };
    }
  },

  delete: async (url, config = {}) => {
    try {
      const response = await apiClient.delete(url, config);
      return response;
    } catch (error) {
      console.warn(`Erro na API DELETE ${url}, usando resposta mock:`, error.message);
      return { data: { success: true, message: 'Mock response' } };
    }
  }
};

export default function AxiosProvider({ children }) {
  return (
    <AxiosContext.Provider value={[ api ]}>
      {children}
    </AxiosContext.Provider>
  );
}
