import React from 'react';

export const AxiosContext = React.createContext();

// Mock data for channels
const mockChannels = [
  {
    id: 1,
    externalId: 'channel-1',
    description: 'Bitcoin Signals',
    type: 'SPOT',
    isPremium: false,
    lastSignalAt: new Date().toISOString(),
  },
  {
    id: 2,
    externalId: 'channel-2',
    description: 'Ethereum Futures',
    type: 'FUTURES',
    isPremium: true,
    lastSignalAt: new Date(Date.now() - 3600000).toISOString(),
  },
  {
    id: 3,
    externalId: 'channel-3',
    description: 'Altcoin Spot Trading',
    type: 'SPOT',
    isPremium: false,
    lastSignalAt: new Date(Date.now() - 7200000).toISOString(),
  },
];

// Mock API object
const mockApi = {
  get: (url) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        if (url === '/channels') {
          resolve({ data: mockChannels });
        } else if (url.includes('/push-notifications/token/')) {
          resolve({ data: { permissions: [] } });
        } else {
          resolve({ data: {} });
        }
      }, 500); // Simulate network delay
    });
  },
  patch: () => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({ data: {} });
      }, 300);
    });
  },
  post: () => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({ data: {} });
      }, 300);
    });
  },
};

export default function AxiosProvider({ children }) {
  return (
    <AxiosContext.Provider value={[ mockApi ]}>
      {children}
    </AxiosContext.Provider>
  );
}
