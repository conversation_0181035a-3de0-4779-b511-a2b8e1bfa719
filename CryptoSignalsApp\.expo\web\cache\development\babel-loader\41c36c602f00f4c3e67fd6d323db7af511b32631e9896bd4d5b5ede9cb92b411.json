{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"label\", \"size\", \"style\", \"labelStyle\", \"color\", \"theme\", \"maxFontSizeMultiplier\"],\n  _excluded2 = [\"backgroundColor\"];\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nimport * as React from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport useWindowDimensions from \"react-native-web/dist/exports/useWindowDimensions\";\nimport View from \"react-native-web/dist/exports/View\";\nimport { useInternalTheme } from \"../../core/theming\";\nimport { white } from \"../../styles/themes/v2/colors\";\nimport getContrastingColor from \"../../utils/getContrastingColor\";\nimport Text from \"../Typography/Text\";\nvar defaultSize = 64;\nvar AvatarText = function AvatarText(_ref) {\n  var label = _ref.label,\n    _ref$size = _ref.size,\n    size = _ref$size === void 0 ? defaultSize : _ref$size,\n    style = _ref.style,\n    labelStyle = _ref.labelStyle,\n    customColor = _ref.color,\n    themeOverrides = _ref.theme,\n    maxFontSizeMultiplier = _ref.maxFontSizeMultiplier,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var _theme$colors;\n  var theme = useInternalTheme(themeOverrides);\n  var _ref2 = StyleSheet.flatten(style) || {},\n    _ref2$backgroundColor = _ref2.backgroundColor,\n    backgroundColor = _ref2$backgroundColor === void 0 ? (_theme$colors = theme.colors) === null || _theme$colors === void 0 ? void 0 : _theme$colors.primary : _ref2$backgroundColor,\n    restStyle = _objectWithoutProperties(_ref2, _excluded2);\n  var textColor = customColor != null ? customColor : getContrastingColor(backgroundColor, white, 'rgba(0, 0, 0, .54)');\n  var _useWindowDimensions = useWindowDimensions(),\n    fontScale = _useWindowDimensions.fontScale;\n  return React.createElement(View, _extends({\n    style: [{\n      width: size,\n      height: size,\n      borderRadius: size / 2,\n      backgroundColor: backgroundColor\n    }, styles.container, restStyle]\n  }, rest), React.createElement(Text, {\n    style: [styles.text, {\n      color: textColor,\n      fontSize: size / 2,\n      lineHeight: size / fontScale\n    }, labelStyle],\n    numberOfLines: 1,\n    maxFontSizeMultiplier: maxFontSizeMultiplier\n  }, label));\n};\nAvatarText.displayName = 'Avatar.Text';\nvar styles = StyleSheet.create({\n  container: {\n    justifyContent: 'center',\n    alignItems: 'center'\n  },\n  text: {\n    textAlign: 'center',\n    textAlignVertical: 'center'\n  }\n});\nexport default AvatarText;", "map": {"version": 3, "names": ["React", "StyleSheet", "useWindowDimensions", "View", "useInternalTheme", "white", "getContrastingColor", "Text", "defaultSize", "AvatarText", "_ref", "label", "_ref$size", "size", "style", "labelStyle", "customColor", "color", "themeOverrides", "theme", "maxFontSizeMultiplier", "rest", "_objectWithoutProperties", "_excluded", "_theme$colors", "_ref2", "flatten", "_ref2$backgroundColor", "backgroundColor", "colors", "primary", "restStyle", "_excluded2", "textColor", "_useWindowDimensions", "fontScale", "createElement", "_extends", "width", "height", "borderRadius", "styles", "container", "text", "fontSize", "lineHeight", "numberOfLines", "displayName", "create", "justifyContent", "alignItems", "textAlign", "textAlignVertical"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\Avatar\\AvatarText.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  StyleProp,\n  StyleSheet,\n  TextStyle,\n  useWindowDimensions,\n  View,\n  ViewStyle,\n} from 'react-native';\n\nimport { useInternalTheme } from '../../core/theming';\nimport { white } from '../../styles/themes/v2/colors';\nimport type { ThemeProp } from '../../types';\nimport getContrastingColor from '../../utils/getContrastingColor';\nimport Text from '../Typography/Text';\n\nconst defaultSize = 64;\n\nexport type Props = React.ComponentPropsWithRef<typeof View> & {\n  /**\n   * Initials to show as the text in the `Avatar`.\n   */\n  label: string;\n  /**\n   * Size of the avatar.\n   */\n  size?: number;\n  /**\n   * Custom color for the text.\n   */\n  color?: string;\n  /**\n   * Style for text container\n   */\n  style?: StyleProp<ViewStyle>;\n  /**\n   * Style for the title.\n   */\n  labelStyle?: StyleProp<TextStyle>;\n  /**\n   * Specifies the largest possible scale a text font can reach.\n   */\n  maxFontSizeMultiplier?: number;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n};\n\n/**\n * Avatars can be used to represent people in a graphical way.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { Avatar } from 'react-native-paper';\n *\n * const MyComponent = () => (\n *   <Avatar.Text size={24} label=\"XD\" />\n * );\n * ```\n */\nconst AvatarText = ({\n  label,\n  size = defaultSize,\n  style,\n  labelStyle,\n  color: customColor,\n  theme: themeOverrides,\n  maxFontSizeMultiplier,\n  ...rest\n}: Props) => {\n  const theme = useInternalTheme(themeOverrides);\n  const { backgroundColor = theme.colors?.primary, ...restStyle } =\n    StyleSheet.flatten(style) || {};\n  const textColor =\n    customColor ??\n    getContrastingColor(backgroundColor, white, 'rgba(0, 0, 0, .54)');\n  const { fontScale } = useWindowDimensions();\n\n  return (\n    <View\n      style={[\n        {\n          width: size,\n          height: size,\n          borderRadius: size / 2,\n          backgroundColor,\n        },\n        styles.container,\n        restStyle,\n      ]}\n      {...rest}\n    >\n      <Text\n        style={[\n          styles.text,\n          {\n            color: textColor,\n            fontSize: size / 2,\n            lineHeight: size / fontScale,\n          },\n          labelStyle,\n        ]}\n        numberOfLines={1}\n        maxFontSizeMultiplier={maxFontSizeMultiplier}\n      >\n        {label}\n      </Text>\n    </View>\n  );\n};\n\nAvatarText.displayName = 'Avatar.Text';\n\nconst styles = StyleSheet.create({\n  container: {\n    justifyContent: 'center',\n    alignItems: 'center',\n  },\n  text: {\n    textAlign: 'center',\n    textAlignVertical: 'center',\n  },\n});\n\nexport default AvatarText;\n"], "mappings": ";;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,UAAA;AAAA,OAAAC,mBAAA;AAAA,OAAAC,IAAA;AAU9B,SAASC,gBAAgB;AACzB,SAASC,KAAK;AAEd,OAAOC,mBAAmB;AAC1B,OAAOC,IAAI;AAEX,IAAMC,WAAW,GAAG,EAAE;AA8CtB,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAAC,IAAA,EASH;EAAA,IARXC,KAAK,GAAAD,IAAA,CAALC,KAAK;IAAAC,SAAA,GAAAF,IAAA,CACLG,IAAI;IAAJA,IAAI,GAAAD,SAAA,cAAGJ,WAAW,GAAAI,SAAA;IAClBE,KAAK,GAAAJ,IAAA,CAALI,KAAK;IACLC,UAAU,GAAAL,IAAA,CAAVK,UAAU;IACHC,WAAW,GAAAN,IAAA,CAAlBO,KAAK;IACEC,cAAc,GAAAR,IAAA,CAArBS,KAAK;IACLC,qBAAqB,GAAAV,IAAA,CAArBU,qBAAqB;IAClBC,IAAA,GAAAC,wBAAA,CAAAZ,IAAA,EAAAa,SAAA;EACQ,IAAAC,aAAA;EACX,IAAML,KAAK,GAAGf,gBAAgB,CAACc,cAAc,CAAC;EAC9C,IAAAO,KAAA,GACExB,UAAU,CAACyB,OAAO,CAACZ,KAAK,CAAC,IAAI,CAAC,CAAC;IAAAa,qBAAA,GAAAF,KAAA,CADzBG,eAAe;IAAfA,eAAe,GAAAD,qBAAA,eAAAH,aAAA,GAAGL,KAAK,CAACU,MAAM,cAAAL,aAAA,uBAAZA,aAAA,CAAcM,OAAO,GAAAH,qBAAA;IAAKI,SAAA,GAAAT,wBAAA,CAAAG,KAAA,EAAAO,UAAA;EAEpD,IAAMC,SAAS,GACbjB,WAAW,WAAXA,WAAW,GACXV,mBAAmB,CAACsB,eAAe,EAAEvB,KAAK,EAAE,oBAAoB,CAAC;EACnE,IAAA6B,oBAAA,GAAsBhC,mBAAmB,CAAC,CAAC;IAAnCiC,SAAA,GAAAD,oBAAA,CAAAC,SAAA;EAER,OACEnC,KAAA,CAAAoC,aAAA,CAACjC,IAAI,EAAAkC,QAAA;IACHvB,KAAK,EAAE,CACL;MACEwB,KAAK,EAAEzB,IAAI;MACX0B,MAAM,EAAE1B,IAAI;MACZ2B,YAAY,EAAE3B,IAAI,GAAG,CAAC;MACtBe,eAAA,EAAAA;IACF,CAAC,EACDa,MAAM,CAACC,SAAS,EAChBX,SAAS;EACT,GACEV,IAAI,GAERrB,KAAA,CAAAoC,aAAA,CAAC7B,IAAI;IACHO,KAAK,EAAE,CACL2B,MAAM,CAACE,IAAI,EACX;MACE1B,KAAK,EAAEgB,SAAS;MAChBW,QAAQ,EAAE/B,IAAI,GAAG,CAAC;MAClBgC,UAAU,EAAEhC,IAAI,GAAGsB;IACrB,CAAC,EACDpB,UAAU,CACV;IACF+B,aAAa,EAAE,CAAE;IACjB1B,qBAAqB,EAAEA;EAAsB,GAE5CT,KACG,CACF,CAAC;AAEX,CAAC;AAEDF,UAAU,CAACsC,WAAW,GAAG,aAAa;AAEtC,IAAMN,MAAM,GAAGxC,UAAU,CAAC+C,MAAM,CAAC;EAC/BN,SAAS,EAAE;IACTO,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE;EACd,CAAC;EACDP,IAAI,EAAE;IACJQ,SAAS,EAAE,QAAQ;IACnBC,iBAAiB,EAAE;EACrB;AACF,CAAC,CAAC;AAEF,eAAe3C,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}