import os
import sys
import asyncio
import logging
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List
import pandas as pd
import matplotlib.pyplot as plt

# Adicionar diretório raiz ao path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.backtesting import Backtester
from utils.binance_client import BinanceHandler
from estrategias.scalp_strategy import ScalpStrategy
from estrategias.breakout_volume import BreakoutVolumeStrategy
from estrategias.inside_bar import InsideBarStrategy
from estrategias.mfi_strategy import MFIStrategy
from estrategias.swing_strategy import SwingStrategy

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('backtesting.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

class BacktestingRunner:
    def __init__(self):
        self.binance = BinanceHandler()
        self.strategies = {
            'Scalp': ScalpStrategy(self.binance),
            'Breakout': BreakoutVolumeStrategy(self.binance),
            'Inside Bar': InsideBarStrategy(self.binance),
            'MFI': MFIStrategy(self.binance),
            'Swing': SwingStrategy(self.binance)
        }
        
        # Configurações de teste
        self.initial_capital = 10000.0
        self.test_periods = [
            ('1m', 7),    # 7 dias para timeframe de 1 minuto
            ('5m', 30),   # 30 dias para timeframe de 5 minutos
            ('15m', 90),  # 90 dias para timeframe de 15 minutos
            ('1h', 180),  # 180 dias para timeframe de 1 hora
            ('4h', 365)   # 365 dias para timeframe de 4 horas
        ]
        
        # Símbolos para teste
        self.test_symbols = [
            'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'XRPUSDT', 'ADAUSDT',
            'DOGEUSDT', 'DOTUSDT', 'SOLUSDT', 'AVAXUSDT', 'MATICUSDT'
        ]
    
    def plot_equity_curve(self, results: Dict, strategy_name: str, symbol: str, interval: str):
        """
        Plota a curva de equity
        
        Args:
            results: Resultados do backtest
            strategy_name: Nome da estratégia
            symbol: Par de trading
            interval: Timeframe
        """
        if not results['equity_curve']:
            return
            
        df = pd.DataFrame(results['equity_curve'])
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        plt.figure(figsize=(12, 6))
        plt.plot(df['timestamp'], df['equity'])
        plt.title(f'Curva de Equity - {strategy_name} - {symbol} - {interval}')
        plt.xlabel('Data')
        plt.ylabel('Capital')
        plt.grid(True)
        
        # Salvar gráfico
        filename = f'backtesting_results/{strategy_name}_{symbol}_{interval}_equity.png'
        os.makedirs(os.path.dirname(filename), exist_ok=True)
        plt.savefig(filename)
        plt.close()
    
    def save_results(self, results: Dict, strategy_name: str, symbol: str, interval: str):
        """
        Salva os resultados do backtest
        
        Args:
            results: Resultados do backtest
            strategy_name: Nome da estratégia
            symbol: Par de trading
            interval: Timeframe
        """
        # Criar diretório se não existir
        os.makedirs('backtesting_results', exist_ok=True)
        
        # Salvar resultados em CSV
        filename = f'backtesting_results/{strategy_name}_{symbol}_{interval}_results.csv'
        
        # Converter trades history para DataFrame
        trades_df = pd.DataFrame(results['trades_history'])
        if not trades_df.empty:
            trades_df.to_csv(filename, index=False)
        
        # Salvar métricas em um arquivo separado
        metrics = {
            'strategy': strategy_name,
            'symbol': symbol,
            'interval': interval,
            'total_trades': results['total_trades'],
            'win_rate': results['win_rate'],
            'profit_factor': results['profit_factor'],
            'max_drawdown': results['max_drawdown'],
            'sharpe_ratio': results['sharpe_ratio'],
            'total_return': results['total_return']
        }
        
        metrics_df = pd.DataFrame([metrics])
        metrics_file = f'backtesting_results/{strategy_name}_{symbol}_{interval}_metrics.csv'
        metrics_df.to_csv(metrics_file, index=False)
    
    async def run_backtests(self):
        """Executa backtests para todas as estratégias"""
        logger.info("Iniciando backtests...")
        
        # Data final (hoje)
        end_date = datetime.now()
        
        for strategy_name, strategy in self.strategies.items():
            logger.info(f"Testando estratégia: {strategy_name}")
            
            for symbol in self.test_symbols:
                logger.info(f"Testando símbolo: {symbol}")
                
                for interval, days in self.test_periods:
                    logger.info(f"Testando timeframe: {interval} ({days} dias)")
                    
                    try:
                        # Calcular data inicial
                        start_date = end_date - timedelta(days=days)
                        logger.info(f"Período: {start_date} até {end_date}")
                        
                        # Criar backtester
                        backtester = Backtester(
                            binance_client=self.binance,
                            strategy=strategy,
                            initial_capital=self.initial_capital
                        )
                        
                        # Executar backtest
                        results = backtester.run_backtest(
                            symbol=symbol,
                            interval=interval,
                            start_date=start_date,
                            end_date=end_date
                        )
                        
                        # Salvar resultados
                        self.save_results(results, strategy_name, symbol, interval)
                        
                        # Plotar curva de equity
                        self.plot_equity_curve(results, strategy_name, symbol, interval)
                        
                        # Log dos resultados
                        logger.info(f"Resultados para {strategy_name} - {symbol} - {interval}:")
                        logger.info(f"Total de trades: {results['total_trades']}")
                        logger.info(f"Win rate: {results['win_rate']:.2%}")
                        logger.info(f"Profit factor: {results['profit_factor']:.2f}")
                        logger.info(f"Retorno total: {results['total_return']:.2f}%")
                        logger.info(f"Drawdown máximo: {results['max_drawdown']:.2f}%")
                        logger.info(f"Sharpe ratio: {results['sharpe_ratio']:.2f}")
                        
                    except Exception as e:
                        logger.error(f"Erro ao executar backtest para {strategy_name} - {symbol} - {interval}: {e}")
                        continue
                    
                    # Aguardar um pouco entre os testes para não sobrecarregar a API
                    await asyncio.sleep(1)
    
    def generate_summary_report(self):
        """Gera um relatório resumido dos backtests"""
        logger.info("Gerando relatório resumido...")
        
        # Coletar todos os arquivos de métricas
        metrics_files = [f for f in os.listdir('backtesting_results') if f.endswith('_metrics.csv')]
        
        if not metrics_files:
            logger.warning("Nenhum arquivo de métricas encontrado")
            return
        
        # Ler todos os arquivos de métricas
        all_metrics = []
        for file in metrics_files:
            df = pd.read_csv(f'backtesting_results/{file}')
            all_metrics.append(df)
        
        # Combinar todas as métricas
        summary_df = pd.concat(all_metrics, ignore_index=True)
        
        # Calcular médias por estratégia
        strategy_summary = summary_df.groupby('strategy').agg({
            'total_trades': 'mean',
            'win_rate': 'mean',
            'profit_factor': 'mean',
            'max_drawdown': 'mean',
            'sharpe_ratio': 'mean',
            'total_return': 'mean'
        }).round(2)
        
        # Salvar resumo
        strategy_summary.to_csv('backtesting_results/summary_report.csv')
        
        # Log do resumo
        logger.info("\nResumo dos backtests:")
        logger.info(strategy_summary)

async def main():
    runner = BacktestingRunner()
    await runner.run_backtests()
    runner.generate_summary_report()
    logger.info("Backtests concluídos!")

if __name__ == "__main__":
    asyncio.run(main()) 