{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nexport default function fromEntries(entries) {\n  return entries.reduce(function (acc, _ref) {\n    var _ref2 = _slicedToArray(_ref, 2),\n      k = _ref2[0],\n      v = _ref2[1];\n    if (acc.hasOwnProperty(k)) {\n      throw new Error(`A value for key '${k}' already exists in the object.`);\n    }\n    acc[k] = v;\n    return acc;\n  }, {});\n}", "map": {"version": 3, "names": ["fromEntries", "entries", "reduce", "acc", "_ref", "_ref2", "_slicedToArray", "k", "v", "hasOwnProperty", "Error"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\@react-navigation\\core\\src\\fromEntries.tsx"], "sourcesContent": ["// Object.fromEntries is not available in older iOS versions\nexport default function fromEntries<K extends string, V>(\n  entries: (readonly [K, V])[]\n) {\n  return entries.reduce((acc, [k, v]) => {\n    if (acc.hasOwnProperty(k)) {\n      throw new Error(`A value for key '${k}' already exists in the object.`);\n    }\n\n    acc[k] = v;\n    return acc;\n  }, {} as Record<K, V>);\n}\n"], "mappings": ";AACA,eAAe,SAASA,WAAWA,CACjCC,OAA4B,EAC5B;EACA,OAAOA,OAAO,CAACC,MAAM,CAAC,UAACC,GAAG,EAAAC,IAAA,EAAa;IAAA,IAAAC,KAAA,GAAAC,cAAA,CAALF,IAAA;MAALG,CAAC,GAAAF,KAAA;MAAEG,CAAC,GAAAH,KAAA;IAC/B,IAAIF,GAAG,CAACM,cAAc,CAACF,CAAC,CAAC,EAAE;MACzB,MAAM,IAAIG,KAAK,CAAE,oBAAmBH,CAAE,iCAAgC,CAAC;IACzE;IAEAJ,GAAG,CAACI,CAAC,CAAC,GAAGC,CAAC;IACV,OAAOL,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAiB;AACxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}