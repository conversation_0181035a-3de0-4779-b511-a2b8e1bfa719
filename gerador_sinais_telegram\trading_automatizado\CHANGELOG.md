# Changelog do Sistema de Trading Automatizado

## [1.1.0] - 2023-03-14

### Adicionado
- Limite de 4 posições abertas simultaneamente
- Alocação de 10% do capital total por operação
- Parâmetro `--capital_total` para definir o capital total disponível
- Atualização da documentação no README.md
- Novos parâmetros na classe AutoTrader: `capital_total` e `max_posicoes`
- Novos parâmetros na classe TelegramTrader: `capital_total`
- Novos parâmetros na classe ManualTrader: `capital_total`

### Alterado
- Modificada a lógica de abertura de posições para verificar o limite de posições abertas
- Atualizado o cálculo de quantidade para usar 10% do capital total quando fornecido
- Atualizado o arquivo README.md com exemplos de uso do parâmetro `--capital_total`
- Modificado o script `start_trading.py` para aceitar e processar o parâmetro `--capital_total`
- Atualizada a inicialização dos traders para usar o novo parâmetro de capital total
- Melhorada a exibição de logs para mostrar informações sobre o capital total e o limite de posições

### Corrigido
- Ajustada a lógica para garantir que novas posições só sejam abertas quando houver menos de 4 posições ativas

## [1.0.0] - 2023-03-01

### Adicionado
- Implementação inicial do sistema de trading automatizado
- Suporte para sinais via Telegram
- Suporte para operações manuais
- Estratégias de Scalping e Swing Trading
- Modo de simulação para testes sem risco
- Monitoramento contínuo de operações abertas 