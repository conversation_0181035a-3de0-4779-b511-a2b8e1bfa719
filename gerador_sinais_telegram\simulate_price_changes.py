import sqlite3
import sys
import random
import logging
from typing import Dict, List, Optional, Any

# Configurar logging
import io

# Criar um handler de console que lida com Unicode
class UnicodeStreamHandler(logging.StreamHandler):
    def __init__(self):
        logging.StreamHandler.__init__(self, io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8'))

    def emit(self, record):
        try:
            msg = self.format(record)
            stream = self.stream
            stream.write(msg + self.terminator)
            self.flush()
        except Exception:
            self.handleError(record)

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        UnicodeStreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class PriceSimulator:
    """Classe para simular mudanças de preço para testar o sistema de atualização"""

    def __init__(self, db_path="database.sqlite"):
        """
        Inicializa o simulador

        Args:
            db_path (str): Caminho para o banco de dados
        """
        self.db_path = db_path
        self.conn = None
        self.cursor = None
        self.connect_db()

    def connect_db(self):
        """Conecta ao banco de dados"""
        try:
            self.conn = sqlite3.connect(self.db_path)
            self.conn.row_factory = sqlite3.Row
            self.cursor = self.conn.cursor()
            logger.info("Conectado ao banco de dados")
        except Exception as e:
            logger.error(f"Erro ao conectar ao banco de dados: {e}")
            sys.exit(1)

    def get_open_signals(self) -> List[Dict[str, Any]]:
        """Obtém sinais abertos do banco de dados"""
        try:
            self.cursor.execute('''
            SELECT * FROM signals WHERE status = 'OPEN'
            ''')

            rows = self.cursor.fetchall()

            # Converter para lista de dicionários
            signals = [dict(row) for row in rows]

            logger.info(f"Encontrados {len(signals)} sinais abertos")
            return signals
        except Exception as e:
            logger.error(f"Erro ao obter sinais abertos: {e}")
            return []

    def simulate_price_change(self, signal: Dict[str, Any], scenario: str) -> None:
        """
        Simula uma mudança de preço para um sinal

        Args:
            signal (dict): Dados do sinal
            scenario (str): Cenário de simulação (tp, sl, random)
        """
        symbol = signal['symbol']
        entry_price = signal['entry_price']
        signal_type = signal['signal_type']

        # Determinar o novo preço com base no cenário
        if scenario == 'tp':
            # Simular atingir o take profit
            if signal_type == 'LONG':
                # Para LONG, preço sobe para atingir TP
                new_price = signal['take_profit_1'] * 1.01  # Ligeiramente acima do TP
            else:
                # Para SHORT, preço cai para atingir TP
                new_price = signal['take_profit_1'] * 0.99  # Ligeiramente abaixo do TP
        elif scenario == 'sl':
            # Simular atingir o stop loss
            if signal_type == 'LONG':
                # Para LONG, preço cai para atingir SL
                new_price = signal['stop_loss'] * 0.99  # Ligeiramente abaixo do SL
            else:
                # Para SHORT, preço sobe para atingir SL
                new_price = signal['stop_loss'] * 1.01  # Ligeiramente acima do SL
        elif scenario == 'random':
            # Simular uma mudança aleatória de preço
            change = random.uniform(-0.05, 0.05)  # -5% a +5%
            new_price = entry_price * (1 + change)
        else:
            logger.error(f"Cenário desconhecido: {scenario}")
            return

        # Atualizar o preço simulado no arquivo de cache do BinanceHandler
        self._update_price_cache(symbol, new_price)

        # Calcular a variação percentual
        price_diff = ((new_price - entry_price) / entry_price) * 100
        if signal_type == "SHORT":
            price_diff = -price_diff

        logger.info(f"Simulado para {symbol}: Preço de entrada: {entry_price:.5f}, Novo preço: {new_price:.5f}, Variação: {price_diff:.2f}%")

    def _update_price_cache(self, symbol: str, price: float) -> None:
        """
        Atualiza o cache de preços do BinanceHandler

        Args:
            symbol (str): Símbolo do par
            price (float): Novo preço
        """
        try:
            # Criar ou atualizar o arquivo de cache
            cache_file = 'price_cache.txt'

            # Ler cache existente
            cache = {}
            try:
                with open(cache_file, 'r') as f:
                    for line in f:
                        if ':' in line:
                            sym, prc = line.strip().split(':', 1)
                            cache[sym] = float(prc)
            except FileNotFoundError:
                pass

            # Atualizar cache
            cache[symbol] = price

            # Salvar cache atualizado
            with open(cache_file, 'w') as f:
                for sym, prc in cache.items():
                    f.write(f"{sym}:{prc}\n")

            logger.info(f"Cache de preço atualizado para {symbol}: {price:.5f}")
        except Exception as e:
            logger.error(f"Erro ao atualizar cache de preço: {e}")

    def simulate_all_signals(self, scenario: str) -> None:
        """
        Simula mudanças de preço para todos os sinais abertos

        Args:
            scenario (str): Cenário de simulação (tp, sl, random)
        """
        signals = self.get_open_signals()

        if not signals:
            logger.info("Nenhum sinal aberto para simular")
            return

        for signal in signals:
            self.simulate_price_change(signal, scenario)

        logger.info(f"Simulação concluída para {len(signals)} sinais")

    def close(self):
        """Fecha a conexão com o banco de dados"""
        if self.conn:
            self.conn.close()
            logger.info("Conexão com o banco de dados fechada")

def main():
    """Função principal"""
    import argparse

    parser = argparse.ArgumentParser(description='Simula mudanças de preço para testar o sistema de atualização')
    parser.add_argument('scenario', choices=['tp', 'sl', 'random'], help='Cenário de simulação (tp=take profit, sl=stop loss, random=aleatório)')
    parser.add_argument('--signal', type=int, help='ID do sinal específico para simular (opcional)')
    args = parser.parse_args()

    simulator = PriceSimulator()

    try:
        if args.signal:
            # Simular para um sinal específico
            simulator.cursor.execute('SELECT * FROM signals WHERE id = ?', (args.signal,))
            signal = dict(simulator.cursor.fetchone())
            if signal:
                simulator.simulate_price_change(signal, args.scenario)
            else:
                logger.error(f"Sinal com ID {args.signal} não encontrado")
        else:
            # Simular para todos os sinais abertos
            simulator.simulate_all_signals(args.scenario)
    finally:
        simulator.close()

if __name__ == "__main__":
    main()
