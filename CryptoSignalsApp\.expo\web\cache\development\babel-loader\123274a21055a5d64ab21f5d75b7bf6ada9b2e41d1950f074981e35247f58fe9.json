{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport React, { useContext, useRef } from 'react';\nimport { StoreContext } from \"../../../store/index\";\nimport WS from 'react-native-websocket';\nimport { setSignalFromWebsocket } from \"../../../store/actions\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nvar WebSocketWrapper = function WebSocketWrapper() {\n  var _useContext = useContext(StoreContext),\n    _useContext2 = _slicedToArray(_useContext, 2),\n    _ = _useContext2[0],\n    dispatch = _useContext2[1];\n  var ref = useRef();\n  return _jsx(WS, {\n    ref: ref,\n    url: \"ws://mycryptosignals.com:7000\",\n    onOpen: function onOpen() {\n      ref.current.send(JSON.stringify({\n        \"type\": 'client_connected'\n      }));\n    },\n    onMessage: function onMessage(data) {\n      var signal = JSON.parse(data.data);\n      if (signal.type === 'signals_created') {\n        setSignalFromWebsocket(dispatch, signal.content);\n      }\n    },\n    onError: function onError(err) {\n      return console.error('WS ERR:', err);\n    },\n    onClose: function onClose(res) {\n      return console.info('WS CLOSE:', res);\n    },\n    reconnect: true\n  });\n};\nexport default WebSocketWrapper;", "map": {"version": 3, "names": ["React", "useContext", "useRef", "StoreContext", "WS", "setSignalFromWebsocket", "jsx", "_jsx", "WebSocketWrapper", "_useContext", "_useContext2", "_slicedToArray", "_", "dispatch", "ref", "url", "onOpen", "current", "send", "JSON", "stringify", "onMessage", "data", "signal", "parse", "type", "content", "onError", "err", "console", "error", "onClose", "res", "info", "reconnect"], "sources": ["E:/CryptoSignalsApp/src/components/Wrapper/WebSocket/index.js"], "sourcesContent": ["import React, { useContext, useRef } from 'react';\r\nimport { StoreContext } from '../../../store/index'\r\nimport WS from 'react-native-websocket';\r\nimport { setSignalFromWebsocket } from '../../../store/actions'\r\n\r\nconst WebSocketWrapper = () => {\r\n  const [ _, dispatch ] = useContext(StoreContext);\r\n  const ref = useRef();\r\n\r\n  return (\r\n    <WS\r\n      ref={ref}\r\n      url=\"ws://mycryptosignals.com:7000\"\r\n      onOpen={() => {\r\n        ref.current.send(JSON.stringify({\"type\": 'client_connected'}))\r\n      }}\r\n      onMessage={(data) => {\r\n        const signal = JSON.parse(data.data);\r\n        if(signal.type === 'signals_created') {\r\n          setSignalFromWebsocket(dispatch, signal.content)\r\n        }\r\n      }}\r\n      onError={err => console.error('WS ERR:', err)}\r\n      onClose={res => console.info('WS CLOSE:', res)}\r\n      reconnect // Will try to reconnect onClose\r\n    />\r\n  )\r\n}\r\n\r\nexport default WebSocketWrapper\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,UAAU,EAAEC,MAAM,QAAQ,OAAO;AACjD,SAASC,YAAY;AACrB,OAAOC,EAAE,MAAM,wBAAwB;AACvC,SAASC,sBAAsB;AAAgC,SAAAC,GAAA,IAAAC,IAAA;AAE/D,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA,EAAS;EAC7B,IAAAC,WAAA,GAAwBR,UAAU,CAACE,YAAY,CAAC;IAAAO,YAAA,GAAAC,cAAA,CAAAF,WAAA;IAAxCG,CAAC,GAAAF,YAAA;IAAEG,QAAQ,GAAAH,YAAA;EACnB,IAAMI,GAAG,GAAGZ,MAAM,CAAC,CAAC;EAEpB,OACEK,IAAA,CAACH,EAAE;IACDU,GAAG,EAAEA,GAAI;IACTC,GAAG,EAAC,+BAA+B;IACnCC,MAAM,EAAE,SAARA,MAAMA,CAAA,EAAQ;MACZF,GAAG,CAACG,OAAO,CAACC,IAAI,CAACC,IAAI,CAACC,SAAS,CAAC;QAAC,MAAM,EAAE;MAAkB,CAAC,CAAC,CAAC;IAChE,CAAE;IACFC,SAAS,EAAE,SAAXA,SAASA,CAAGC,IAAI,EAAK;MACnB,IAAMC,MAAM,GAAGJ,IAAI,CAACK,KAAK,CAACF,IAAI,CAACA,IAAI,CAAC;MACpC,IAAGC,MAAM,CAACE,IAAI,KAAK,iBAAiB,EAAE;QACpCpB,sBAAsB,CAACQ,QAAQ,EAAEU,MAAM,CAACG,OAAO,CAAC;MAClD;IACF,CAAE;IACFC,OAAO,EAAE,SAATA,OAAOA,CAAEC,GAAG;MAAA,OAAIC,OAAO,CAACC,KAAK,CAAC,SAAS,EAAEF,GAAG,CAAC;IAAA,CAAC;IAC9CG,OAAO,EAAE,SAATA,OAAOA,CAAEC,GAAG;MAAA,OAAIH,OAAO,CAACI,IAAI,CAAC,WAAW,EAAED,GAAG,CAAC;IAAA,CAAC;IAC/CE,SAAS;EAAA,CACV,CAAC;AAEN,CAAC;AAED,eAAe1B,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}