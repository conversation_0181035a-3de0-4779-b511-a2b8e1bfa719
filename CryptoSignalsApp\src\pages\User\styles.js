import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  profileHeader: {
    padding: 20,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  profilePicture: {
    width: 100,
    height: 100,
    borderRadius: 50,
    marginBottom: 10,
  },
  username: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  fullName: {
    fontSize: 16,
    color: '#555',
    marginBottom: 5,
  },
  bio: {
    fontSize: 14,
    color: '#777',
    marginTop: 10,
  },
  privacySwitch: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 20,
  },
  privacyLabel: {
    fontSize: 16,
    marginRight: 10,
  },
  metricsSection: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  badgesSection: {
    padding: 20,
  },
  cancelSubscriptionButton: {
    backgroundColor: '#dc3545',
    padding: 12,
    borderRadius: 4,
    alignItems: 'center',
    margin: 20,
  },
  cancelSubscriptionText: {
    color: '#fff',
    fontSize: 16,
  },
});

export default styles;
