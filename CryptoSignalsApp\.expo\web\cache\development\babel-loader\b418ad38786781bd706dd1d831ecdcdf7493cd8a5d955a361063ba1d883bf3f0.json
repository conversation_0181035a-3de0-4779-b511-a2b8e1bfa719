{"ast": null, "code": "import React from 'react';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport var AxiosContext = React.createContext();\nvar mockChannels = [{\n  id: 1,\n  externalId: 'channel-1',\n  description: 'Bitcoin Signals',\n  type: 'SPOT',\n  isPremium: false,\n  lastSignalAt: new Date().toISOString()\n}, {\n  id: 2,\n  externalId: 'channel-2',\n  description: 'Ethereum Futures',\n  type: 'FUTURES',\n  isPremium: true,\n  lastSignalAt: new Date(Date.now() - 3600000).toISOString()\n}, {\n  id: 3,\n  externalId: 'channel-3',\n  description: 'Altcoin Spot Trading',\n  type: 'SPOT',\n  isPremium: false,\n  lastSignalAt: new Date(Date.now() - 7200000).toISOString()\n}];\nvar mockApi = {\n  get: function get(url) {\n    return new Promise(function (resolve) {\n      setTimeout(function () {\n        if (url === '/channels') {\n          resolve({\n            data: mockChannels\n          });\n        } else if (url.includes('/push-notifications/token/')) {\n          resolve({\n            data: {\n              permissions: []\n            }\n          });\n        } else {\n          resolve({\n            data: {}\n          });\n        }\n      }, 500);\n    });\n  },\n  patch: function patch() {\n    return new Promise(function (resolve) {\n      setTimeout(function () {\n        resolve({\n          data: {}\n        });\n      }, 300);\n    });\n  },\n  post: function post() {\n    return new Promise(function (resolve) {\n      setTimeout(function () {\n        resolve({\n          data: {}\n        });\n      }, 300);\n    });\n  }\n};\nexport default function AxiosProvider(_ref) {\n  var children = _ref.children;\n  return _jsx(AxiosContext.Provider, {\n    value: [mockApi],\n    children: children\n  });\n}", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "AxiosContext", "createContext", "mockChannels", "id", "externalId", "description", "type", "isPremium", "lastSignalAt", "Date", "toISOString", "now", "mockApi", "get", "url", "Promise", "resolve", "setTimeout", "data", "includes", "permissions", "patch", "post", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref", "children", "Provider", "value"], "sources": ["E:/CryptoSignalsApp/src/store/axios/index.js"], "sourcesContent": ["import React from 'react';\r\n\r\nexport const AxiosContext = React.createContext();\r\n\r\n// Mock data for channels\r\nconst mockChannels = [\r\n  {\r\n    id: 1,\r\n    externalId: 'channel-1',\r\n    description: 'Bitcoin Signals',\r\n    type: 'SPOT',\r\n    isPremium: false,\r\n    lastSignalAt: new Date().toISOString(),\r\n  },\r\n  {\r\n    id: 2,\r\n    externalId: 'channel-2',\r\n    description: 'Ethereum Futures',\r\n    type: 'FUTURES',\r\n    isPremium: true,\r\n    lastSignalAt: new Date(Date.now() - 3600000).toISOString(),\r\n  },\r\n  {\r\n    id: 3,\r\n    externalId: 'channel-3',\r\n    description: 'Altcoin Spot Trading',\r\n    type: 'SPOT',\r\n    isPremium: false,\r\n    lastSignalAt: new Date(Date.now() - 7200000).toISOString(),\r\n  },\r\n];\r\n\r\n// Mock API object\r\nconst mockApi = {\r\n  get: (url) => {\r\n    return new Promise((resolve) => {\r\n      setTimeout(() => {\r\n        if (url === '/channels') {\r\n          resolve({ data: mockChannels });\r\n        } else if (url.includes('/push-notifications/token/')) {\r\n          resolve({ data: { permissions: [] } });\r\n        } else {\r\n          resolve({ data: {} });\r\n        }\r\n      }, 500); // Simulate network delay\r\n    });\r\n  },\r\n  patch: () => {\r\n    return new Promise((resolve) => {\r\n      setTimeout(() => {\r\n        resolve({ data: {} });\r\n      }, 300);\r\n    });\r\n  },\r\n  post: () => {\r\n    return new Promise((resolve) => {\r\n      setTimeout(() => {\r\n        resolve({ data: {} });\r\n      }, 300);\r\n    });\r\n  },\r\n};\r\n\r\nexport default function AxiosProvider({ children }) {\r\n  return (\r\n    <AxiosContext.Provider value={[ mockApi ]}>\r\n      {children}\r\n    </AxiosContext.Provider>\r\n  );\r\n}\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAE1B,OAAO,IAAMC,YAAY,GAAGH,KAAK,CAACI,aAAa,CAAC,CAAC;AAGjD,IAAMC,YAAY,GAAG,CACnB;EACEC,EAAE,EAAE,CAAC;EACLC,UAAU,EAAE,WAAW;EACvBC,WAAW,EAAE,iBAAiB;EAC9BC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAE,KAAK;EAChBC,YAAY,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;AACvC,CAAC,EACD;EACEP,EAAE,EAAE,CAAC;EACLC,UAAU,EAAE,WAAW;EACvBC,WAAW,EAAE,kBAAkB;EAC/BC,IAAI,EAAE,SAAS;EACfC,SAAS,EAAE,IAAI;EACfC,YAAY,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACE,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,CAACD,WAAW,CAAC;AAC3D,CAAC,EACD;EACEP,EAAE,EAAE,CAAC;EACLC,UAAU,EAAE,WAAW;EACvBC,WAAW,EAAE,sBAAsB;EACnCC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAE,KAAK;EAChBC,YAAY,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACE,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,CAACD,WAAW,CAAC;AAC3D,CAAC,CACF;AAGD,IAAME,OAAO,GAAG;EACdC,GAAG,EAAE,SAALA,GAAGA,CAAGC,GAAG,EAAK;IACZ,OAAO,IAAIC,OAAO,CAAC,UAACC,OAAO,EAAK;MAC9BC,UAAU,CAAC,YAAM;QACf,IAAIH,GAAG,KAAK,WAAW,EAAE;UACvBE,OAAO,CAAC;YAAEE,IAAI,EAAEhB;UAAa,CAAC,CAAC;QACjC,CAAC,MAAM,IAAIY,GAAG,CAACK,QAAQ,CAAC,4BAA4B,CAAC,EAAE;UACrDH,OAAO,CAAC;YAAEE,IAAI,EAAE;cAAEE,WAAW,EAAE;YAAG;UAAE,CAAC,CAAC;QACxC,CAAC,MAAM;UACLJ,OAAO,CAAC;YAAEE,IAAI,EAAE,CAAC;UAAE,CAAC,CAAC;QACvB;MACF,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;EACJ,CAAC;EACDG,KAAK,EAAE,SAAPA,KAAKA,CAAA,EAAQ;IACX,OAAO,IAAIN,OAAO,CAAC,UAACC,OAAO,EAAK;MAC9BC,UAAU,CAAC,YAAM;QACfD,OAAO,CAAC;UAAEE,IAAI,EAAE,CAAC;QAAE,CAAC,CAAC;MACvB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;EACJ,CAAC;EACDI,IAAI,EAAE,SAANA,IAAIA,CAAA,EAAQ;IACV,OAAO,IAAIP,OAAO,CAAC,UAACC,OAAO,EAAK;MAC9BC,UAAU,CAAC,YAAM;QACfD,OAAO,CAAC;UAAEE,IAAI,EAAE,CAAC;QAAE,CAAC,CAAC;MACvB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;EACJ;AACF,CAAC;AAED,eAAe,SAASK,aAAaA,CAAAC,IAAA,EAAe;EAAA,IAAZC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;EAC9C,OACE1B,IAAA,CAACC,YAAY,CAAC0B,QAAQ;IAACC,KAAK,EAAE,CAAEf,OAAO,CAAG;IAAAa,QAAA,EACvCA;EAAQ,CACY,CAAC;AAE5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}