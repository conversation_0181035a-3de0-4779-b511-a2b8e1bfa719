import asyncio
import logging
import datetime
import sys
import os

# Adicione o diretório raiz ao path para encontrar o módulo imghdr personalizado
sys.path.insert(0, '.')

from telethon import TelegramClient
from telethon.errors import FloodWaitError, ChatWriteForbiddenError
from config.settings import (
    TELEGRAM_API_ID,
    TELEGRAM_API_HASH,
    TELEGRAM_GROUP_ID,
    TELEGRAM_SESSION_FILE,
    TELEGRAM_PHONE
)

logger = logging.getLogger(__name__)

class TelegramSender:
    def __init__(self):
        self.api_id = TELEGRAM_API_ID
        self.api_hash = TELEGRAM_API_HASH
        self.group_id = TELEGRAM_GROUP_ID
        self.session_file = TELEGRAM_SESSION_FILE
        self.client = None
        self.group_entity = None
        self.signals_sent_today = 0
        self.current_day = datetime.datetime.now().date()
        self.last_message_time = 0
        self.message_interval = 5  # Intervalo mínimo entre mensagens (5 segundos)
        self.message_ids = {}  # Dicionário para armazenar IDs de mensagens enviadas

    async def connect(self):
        """Conecta ao Telegram e obtém a entidade do grupo"""
        try:
            self.client = TelegramClient(
                self.session_file,
                self.api_id,
                self.api_hash,
                device_model="Desktop",
                system_version="Windows 11",
                app_version="9.5.1"  # Versão mais recente (maio 2025)
            )

            # Usar o número de telefone do arquivo .env
            if TELEGRAM_PHONE:
                logger.info(f"Usando número de telefone: {TELEGRAM_PHONE}")
                await self.client.start(phone=TELEGRAM_PHONE)
            else:
                # Fallback para o método padrão
                await self.client.start()

            logger.info("Cliente Telegram conectado com sucesso")

            self.group_entity = await self.client.get_entity(self.group_id)
            logger.info(f"Entidade do grupo obtida com sucesso")
            return True
        except Exception as e:
            logger.error(f"Erro ao conectar ao Telegram: {e}")
            return False

    async def disconnect(self):
        """Desconecta do Telegram"""
        if self.client:
            await self.client.disconnect()
            logger.info("Cliente Telegram desconectado")

    def is_trading_hours(self):
        """Verifica se está dentro do horário de trading - OPERAÇÃO 24/7"""
        # Criptomoedas operam 24h, sempre retorna True
        # Mantemos a função para compatibilidade, mas sempre permite envio
        return True

    def reset_daily_counter(self):
        """Reseta o contador diário se necessário"""
        today = datetime.datetime.now().date()
        if today != self.current_day:
            self.signals_sent_today = 0
            self.current_day = today
            logger.info("Contador de sinais diários resetado")

    async def send_message(self, message):
        """Envia uma mensagem para o grupo do Telegram com controle de taxa"""
        self.reset_daily_counter()

        # OPERAÇÃO 24/7 ATIVADA - Removida verificação de horário
        # Criptomoedas operam 24h, mensagens podem ser enviadas a qualquer hora

        # Controle de intervalo entre mensagens
        now = datetime.datetime.now().timestamp()
        elapsed = now - self.last_message_time
        if elapsed < self.message_interval:
            await asyncio.sleep(self.message_interval - elapsed)

        try:
            sent_message = await self.client.send_message(self.group_entity, message)
            self.signals_sent_today += 1
            self.last_message_time = datetime.datetime.now().timestamp()
            logger.info(f"Mensagem enviada com sucesso. Total hoje: {self.signals_sent_today}")

            # Armazenar o ID da mensagem enviada
            message_key = f"msg_{datetime.datetime.now().timestamp()}"
            self.message_ids[message_key] = sent_message.id

            # Limpar mensagens antigas (mais de 24 horas)
            self._clean_old_message_ids()

            return sent_message.id
        except FloodWaitError as e:
            logger.warning(f"Limite de taxa excedido. Aguardando {e.seconds} segundos.")
            await asyncio.sleep(e.seconds)
            return False
        except ChatWriteForbiddenError:
            logger.error("Sem permissão para enviar mensagens no grupo.")
            return False
        except Exception as e:
            logger.error(f"Erro ao enviar mensagem: {e}")
            return False

    async def send_reply_message(self, message, reply_to_msg_id):
        """Envia uma mensagem como resposta a uma mensagem específica"""
        self.reset_daily_counter()

        if not self.is_trading_hours():
            logger.info("Fora do horário de trading. Resposta não enviada.")
            return False

        # Controle de intervalo entre mensagens
        now = datetime.datetime.now().timestamp()
        elapsed = now - self.last_message_time
        if elapsed < self.message_interval:
            await asyncio.sleep(self.message_interval - elapsed)

        try:
            await self.client.send_message(
                entity=self.group_entity,
                message=message,
                reply_to=reply_to_msg_id
            )
            self.signals_sent_today += 1
            self.last_message_time = datetime.datetime.now().timestamp()
            logger.info(f"Resposta enviada com sucesso. Total hoje: {self.signals_sent_today}")
            return True
        except FloodWaitError as e:
            logger.warning(f"Limite de taxa excedido. Aguardando {e.seconds} segundos.")
            await asyncio.sleep(e.seconds)
            return False
        except ChatWriteForbiddenError:
            logger.error("Sem permissão para enviar mensagens no grupo.")
            return False
        except Exception as e:
            logger.error(f"Erro ao enviar resposta: {e}")
            return False

    async def send_photo(self, photo_path, caption=None, reply_to_msg_id=None):
        """Envia uma imagem para o grupo do Telegram com controle de taxa"""
        self.reset_daily_counter()

        # OPERAÇÃO 24/7 ATIVADA - Removida verificação de horário
        # Criptomoedas operam 24h, imagens podem ser enviadas a qualquer hora

        # Controle de intervalo entre mensagens
        now = datetime.datetime.now().timestamp()
        elapsed = now - self.last_message_time
        if elapsed < self.message_interval:
            await asyncio.sleep(self.message_interval - elapsed)

        try:
            await self.client.send_file(
                self.group_entity,
                photo_path,
                caption=caption,
                reply_to=reply_to_msg_id
            )
            self.signals_sent_today += 1
            self.last_message_time = datetime.datetime.now().timestamp()
            logger.info(f"Imagem enviada com sucesso. Total hoje: {self.signals_sent_today}")
            return True
        except FloodWaitError as e:
            logger.warning(f"Limite de taxa excedido. Aguardando {e.seconds} segundos.")
            await asyncio.sleep(e.seconds)
            return False
        except ChatWriteForbiddenError:
            logger.error("Sem permissão para enviar imagens no grupo.")
            return False
        except Exception as e:
            logger.error(f"Erro ao enviar imagem: {e}")
            return False

    def _clean_old_message_ids(self):
        """Limpa IDs de mensagens antigas (mais de 24 horas)"""
        current_time = datetime.datetime.now().timestamp()
        keys_to_remove = []

        for key, _ in self.message_ids.items():
            try:
                # Extrai o timestamp do nome da chave (msg_timestamp)
                timestamp = float(key.split('_')[1])
                if current_time - timestamp > 86400:  # 24 horas em segundos
                    keys_to_remove.append(key)
            except (IndexError, ValueError):
                # Se houver erro ao extrair o timestamp, remove a chave
                keys_to_remove.append(key)

        for key in keys_to_remove:
            self.message_ids.pop(key, None)