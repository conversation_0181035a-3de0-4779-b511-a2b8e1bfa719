import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  container: {
    marginBottom: 0,
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
    justifyContent: 'space-evenly',
  },
  card: {
    backgroundColor: '#0D0D0D',
    marginBottom: 16,
    width: 156,
    height: 74,
    padding: 16,
    borderRadius: 4,
  },
  cardSelected: {
    borderColor: '#FECB37',
    borderWidth: 2,
    borderStyle: 'solid'
  },
  period: {
    color: '#fff',
    fontSize: 12,
    marginBottom: 8,
    fontFamily: 'Poppins_400Regular'
  },
  price: {
    color: '#fff',
    fontSize: 14,
    fontFamily: 'Poppins_700Bold'
  },
})

export default styles;