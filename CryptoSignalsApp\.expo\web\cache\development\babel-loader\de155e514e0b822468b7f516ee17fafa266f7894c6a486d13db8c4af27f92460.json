{"ast": null, "code": "import * as React from 'react';\nvar ModalPresentationContext = React.createContext(false);\nexport default ModalPresentationContext;", "map": {"version": 3, "names": ["React", "ModalPresentationContext", "createContext"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\@react-navigation\\stack\\src\\utils\\ModalPresentationContext.tsx"], "sourcesContent": ["import * as React from 'react';\n\nconst ModalPresentationContext = React.createContext(false);\n\nexport default ModalPresentationContext;\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,IAAMC,wBAAwB,GAAGD,KAAK,CAACE,aAAa,CAAC,KAAK,CAAC;AAE3D,eAAeD,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}