import os
import sys
import asyncio
import logging
import re
import json
from telethon import TelegramClient, events
from telethon.errors import SessionPasswordNeededError

# Importando configurações e utilitários do projeto principal
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.settings import (
    TELEGRAM_API_ID,
    TELEGRAM_API_HASH,
    TELEGRAM_GROUP_ID,
    TELEGRAM_SESSION_FILE
)
from trading_automatizado.auto_trader import AutoTrader

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("trading_automatizado/telegram_trader.log"),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

class TelegramTrader:
    def __init__(self, capital_por_operacao=20, modo_simulacao=True, capital_total=None):
        """
        Inicializa o trader baseado em sinais do Telegram
        
        Args:
            capital_por_operacao: Valor em dólares para cada operação
            modo_simulacao: Se True, apenas simula as operações sem executá-las
            capital_total: Capital total disponível para trading
        """
        self.capital_por_operacao = capital_por_operacao
        self.modo_simulacao = modo_simulacao
        self.capital_total = capital_total
        
        # Inicializa o cliente do Telegram
        self.client = TelegramClient(TELEGRAM_SESSION_FILE, TELEGRAM_API_ID, TELEGRAM_API_HASH)
        
        # Inicializa os traders automáticos para cada estratégia
        self.trader_scalp = AutoTrader(
            capital_por_operacao=capital_por_operacao, 
            modo_simulacao=modo_simulacao, 
            estrategia="scalp",
            capital_total=capital_total,
            max_posicoes=4
        )
        self.trader_swing = AutoTrader(
            capital_por_operacao=capital_por_operacao, 
            modo_simulacao=modo_simulacao, 
            estrategia="swing",
            capital_total=capital_total,
            max_posicoes=4
        )
        
        # Padrões para extrair informações dos sinais
        self.padrao_sinal = re.compile(
            r'(?:SINAL|SIGNAL)\s*(?:DE|:)?\s*(?:COMPRA|VENDA|LONG|SHORT|BUY|SELL)\s*(?:PARA|:)?\s*([A-Z]+/USDT|[A-Z]+USDT)',
            re.IGNORECASE
        )
        self.padrao_tipo = re.compile(r'(?:COMPRA|LONG|BUY|VENDA|SHORT|SELL)', re.IGNORECASE)
        self.padrao_entrada = re.compile(r'(?:ENTRADA|ENTRY)\s*(?::|EM)?\s*([\d.]+)', re.IGNORECASE)
        self.padrao_sl = re.compile(r'(?:STOP\s*LOSS|SL)\s*(?::|EM)?\s*([\d.]+)', re.IGNORECASE)
        self.padrao_tp = re.compile(r'(?:TAKE\s*PROFIT|TP|ALVO)\s*(?::|EM)?\s*([\d.]+)', re.IGNORECASE)
        self.padrao_estrategia = re.compile(r'(?:ESTRATEGIA|STRATEGY|TIPO)\s*(?::|DE)?\s*(SCALP|SWING|SCALPING|DAY\s*TRADE)', re.IGNORECASE)
        self.padrao_timeframe = re.compile(r'(?:TIMEFRAME|TF|TEMPO)\s*(?::|DE)?\s*(1M|5M|15M|30M|1H|4H|1D)', re.IGNORECASE)
        
        # Mapeamento de pares preferenciais por estratégia com base no backtesting
        self.pares_scalp = ['BTCUSDT', 'ETHUSDT', 'BNBUSDT']
        self.pares_swing = ['BTCUSDT', 'ETHUSDT', 'BNBUSDT']
        
        # Inicializa as tarefas de monitoramento
        self.tarefas_monitoramento = []
        
        logger.info(f"TelegramTrader inicializado com capital de ${capital_por_operacao} por operação")
        logger.info(f"Modo de simulação: {'ATIVADO' if modo_simulacao else 'DESATIVADO'}")
    
    def determinar_estrategia(self, mensagem, symbol):
        """
        Determina a estratégia mais adequada com base na mensagem e no símbolo
        
        Args:
            mensagem: Texto da mensagem
            symbol: Par de trading
            
        Returns:
            str: 'scalp' ou 'swing'
        """
        # Verifica se a estratégia está explicitamente mencionada na mensagem
        match_estrategia = self.padrao_estrategia.search(mensagem)
        if match_estrategia:
            estrategia_texto = match_estrategia.group(1).upper()
            if estrategia_texto in ['SCALP', 'SCALPING', 'DAY TRADE']:
                return 'scalp'
            elif estrategia_texto == 'SWING':
                return 'swing'
        
        # Verifica o timeframe mencionado na mensagem
        match_timeframe = self.padrao_timeframe.search(mensagem)
        if match_timeframe:
            timeframe = match_timeframe.group(1).upper()
            if timeframe in ['1M', '5M', '15M', '30M', '1H']:
                return 'scalp'  # Timeframes menores são mais adequados para scalping
            elif timeframe in ['4H', '1D']:
                return 'swing'  # Timeframes maiores são mais adequados para swing
        
        # Com base nos resultados do backtesting, preferimos swing trading
        # pois teve melhor performance geral (retorno de 12.5% vs 8.2%)
        return 'swing'
    
    def extrair_sinal(self, mensagem):
        """
        Extrai informações de sinal de uma mensagem do Telegram
        
        Args:
            mensagem: Texto da mensagem
            
        Returns:
            dict: Informações do sinal ou None se não for um sinal válido
        """
        try:
            # Verifica se a mensagem contém um sinal
            match_sinal = self.padrao_sinal.search(mensagem)
            if not match_sinal:
                return None
            
            # Extrai o símbolo
            symbol = match_sinal.group(1).replace('/', '')
            
            # Extrai o tipo de sinal (LONG ou SHORT)
            match_tipo = self.padrao_tipo.search(mensagem)
            if not match_tipo:
                return None
                
            tipo_texto = match_tipo.group(0).upper()
            if tipo_texto in ['COMPRA', 'LONG', 'BUY']:
                tipo_sinal = 'LONG'
            elif tipo_texto in ['VENDA', 'SHORT', 'SELL']:
                tipo_sinal = 'SHORT'
            else:
                return None
            
            # Extrai preço de entrada
            match_entrada = self.padrao_entrada.search(mensagem)
            if not match_entrada:
                return None
            preco_entrada = float(match_entrada.group(1))
            
            # Extrai stop loss
            match_sl = self.padrao_sl.search(mensagem)
            if match_sl:
                stop_loss = float(match_sl.group(1))
            else:
                stop_loss = None  # Será calculado automaticamente pelo trader
            
            # Extrai take profit
            match_tp = self.padrao_tp.search(mensagem)
            if match_tp:
                take_profit = float(match_tp.group(1))
            else:
                take_profit = None  # Será calculado automaticamente pelo trader
            
            # Determina a estratégia mais adequada
            estrategia = self.determinar_estrategia(mensagem, symbol)
            
            # Cria o dicionário do sinal
            sinal = {
                'symbol': symbol,
                'signal_type': tipo_sinal,
                'entry_price': preco_entrada,
                'stop_loss': stop_loss,
                'take_profit': take_profit,
                'estrategia': estrategia
            }
            
            logger.info(f"Sinal extraído: {json.dumps(sinal)}")
            return sinal
            
        except Exception as e:
            logger.error(f"Erro ao extrair sinal da mensagem: {e}")
            return None
    
    async def processar_mensagem(self, event):
        """
        Processa uma mensagem recebida do Telegram
        
        Args:
            event: Evento de mensagem do Telegram
        """
        try:
            # Obtém o texto da mensagem
            mensagem = event.message.text
            
            # Extrai o sinal da mensagem
            sinal = self.extrair_sinal(mensagem)
            
            if sinal:
                symbol = sinal['symbol']
                tipo_sinal = sinal['signal_type']
                estrategia = sinal['estrategia']
                
                logger.info(f"Sinal detectado: {symbol} - {tipo_sinal} - Estratégia: {estrategia}")
                
                # Seleciona o trader adequado para a estratégia
                trader = self.trader_scalp if estrategia == 'scalp' else self.trader_swing
                
                # Processa o sinal
                resultado = await trader.processar_sinal(sinal)
                
                if resultado:
                    logger.info(f"Ordem executada com sucesso para {symbol} usando estratégia {estrategia}")
                else:
                    logger.warning(f"Falha ao executar ordem para {symbol} usando estratégia {estrategia}")
            
        except Exception as e:
            logger.error(f"Erro ao processar mensagem: {e}")
    
    async def iniciar(self):
        """Inicia o monitoramento do Telegram e o trader automático"""
        try:
            # Inicia o cliente do Telegram
            await self.client.start()
            
            # Verifica se está conectado
            if not await self.client.is_user_authorized():
                logger.error("Não autorizado no Telegram. Verifique as credenciais.")
                return
            
            logger.info("Conectado ao Telegram. Iniciando monitoramento de mensagens.")
            
            # Configura o handler para mensagens
            @self.client.on(events.NewMessage(chats=TELEGRAM_GROUP_ID))
            async def handler_mensagem(event):
                await self.processar_mensagem(event)
            
            # Inicia o monitoramento de operações em paralelo para ambas estratégias
            self.tarefas_monitoramento.append(asyncio.create_task(self.trader_scalp.iniciar()))
            self.tarefas_monitoramento.append(asyncio.create_task(self.trader_swing.iniciar()))
            
            # Mantém o cliente rodando
            await self.client.run_until_disconnected()
            
        except SessionPasswordNeededError:
            logger.error("Autenticação de dois fatores necessária. Configure manualmente a sessão primeiro.")
        except Exception as e:
            logger.error(f"Erro ao iniciar TelegramTrader: {e}")
        finally:
            # Cancela as tarefas de monitoramento
            for tarefa in self.tarefas_monitoramento:
                tarefa.cancel()

async def main():
    """Função principal"""
    trader = TelegramTrader(capital_por_operacao=20, modo_simulacao=True)
    await trader.iniciar()

if __name__ == "__main__":
    asyncio.run(main()) 