{"ast": null, "code": "export function setSubscription(dispatch, payload) {\n  dispatch({\n    type: 'SET_SUBSCRIPTION',\n    payload: payload\n  });\n}\nexport function setSignalFromWebsocket(dispatch, payload) {\n  dispatch({\n    type: 'SET_SIGNAL_FROM_WEBSOCKET',\n    payload: payload\n  });\n}\nexport function removeSubscription(dispatch) {\n  dispatch({\n    type: 'REMOVE_SUBSCRIPTION'\n  });\n}", "map": {"version": 3, "names": ["setSubscription", "dispatch", "payload", "type", "setSignalFromWebsocket", "removeSubscription"], "sources": ["E:/CryptoSignalsApp/src/store/actions.js"], "sourcesContent": ["export function setSubscription(dispatch, payload) {\r\n  dispatch({\r\n    type: 'SET_SUBSCRIPTION',\r\n    payload\r\n  })\r\n}\r\n\r\nexport function setSignalFromWebsocket(dispatch, payload) {\r\n  dispatch({\r\n    type: 'SET_SIGNAL_FROM_WEBSOCKET',\r\n    payload\r\n  })\r\n}\r\n\r\nexport function removeSubscription(dispatch) {\r\n  dispatch({\r\n    type: 'REMOVE_SUBSCRIPTION'\r\n  })\r\n}"], "mappings": "AAAA,OAAO,SAASA,eAAeA,CAACC,QAAQ,EAAEC,OAAO,EAAE;EACjDD,QAAQ,CAAC;IACPE,IAAI,EAAE,kBAAkB;IACxBD,OAAO,EAAPA;EACF,CAAC,CAAC;AACJ;AAEA,OAAO,SAASE,sBAAsBA,CAACH,QAAQ,EAAEC,OAAO,EAAE;EACxDD,QAAQ,CAAC;IACPE,IAAI,EAAE,2BAA2B;IACjCD,OAAO,EAAPA;EACF,CAAC,CAAC;AACJ;AAEA,OAAO,SAASG,kBAAkBA,CAACJ,QAAQ,EAAE;EAC3CA,QAAQ,CAAC;IACPE,IAAI,EAAE;EACR,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}