{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"visible\", \"action\", \"icon\", \"onIconPress\", \"iconAccessibilityLabel\", \"duration\", \"onDismiss\", \"children\", \"elevation\", \"style\", \"wrapperStyle\", \"contentStyle\", \"theme\", \"maxFontSizeMultiplier\", \"rippleColor\", \"testID\"],\n  _excluded2 = [\"style\", \"label\", \"onPress\", \"rippleColor\"];\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nimport * as React from 'react';\nimport Animated from \"react-native-web/dist/exports/Animated\";\nimport Easing from \"react-native-web/dist/exports/Easing\";\nimport I18nManager from \"react-native-web/dist/exports/I18nManager\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport { useSafeAreaInsets } from 'react-native-safe-area-context';\nimport useLatestCallback from 'use-latest-callback';\nimport Button from \"./Button/Button\";\nimport IconButton from \"./IconButton/IconButton\";\nimport MaterialCommunityIcon from \"./MaterialCommunityIcon\";\nimport Surface from \"./Surface\";\nimport Text from \"./Typography/Text\";\nimport { useInternalTheme } from \"../core/theming\";\nvar DURATION_SHORT = 4000;\nvar DURATION_MEDIUM = 7000;\nvar DURATION_LONG = 10000;\nvar Snackbar = function Snackbar(_ref) {\n  var visible = _ref.visible,\n    action = _ref.action,\n    icon = _ref.icon,\n    onIconPress = _ref.onIconPress,\n    _ref$iconAccessibilit = _ref.iconAccessibilityLabel,\n    iconAccessibilityLabel = _ref$iconAccessibilit === void 0 ? 'Close icon' : _ref$iconAccessibilit,\n    _ref$duration = _ref.duration,\n    duration = _ref$duration === void 0 ? DURATION_MEDIUM : _ref$duration,\n    onDismiss = _ref.onDismiss,\n    children = _ref.children,\n    _ref$elevation = _ref.elevation,\n    elevation = _ref$elevation === void 0 ? 2 : _ref$elevation,\n    style = _ref.style,\n    wrapperStyle = _ref.wrapperStyle,\n    contentStyle = _ref.contentStyle,\n    themeOverrides = _ref.theme,\n    maxFontSizeMultiplier = _ref.maxFontSizeMultiplier,\n    rippleColor = _ref.rippleColor,\n    testID = _ref.testID,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var theme = useInternalTheme(themeOverrides);\n  var _useSafeAreaInsets = useSafeAreaInsets(),\n    bottom = _useSafeAreaInsets.bottom,\n    right = _useSafeAreaInsets.right,\n    left = _useSafeAreaInsets.left;\n  var _React$useRef = React.useRef(new Animated.Value(0.0)),\n    opacity = _React$useRef.current;\n  var hideTimeout = React.useRef(undefined);\n  var _React$useState = React.useState(!visible),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    hidden = _React$useState2[0],\n    setHidden = _React$useState2[1];\n  var scale = theme.animation.scale;\n  var animateShow = useLatestCallback(function () {\n    if (hideTimeout.current) clearTimeout(hideTimeout.current);\n    Animated.timing(opacity, {\n      toValue: 1,\n      duration: 200 * scale,\n      easing: Easing.out(Easing.ease),\n      useNativeDriver: true\n    }).start(function (_ref2) {\n      var finished = _ref2.finished;\n      if (finished) {\n        var isInfinity = duration === Number.POSITIVE_INFINITY || duration === Number.NEGATIVE_INFINITY;\n        if (!isInfinity) {\n          hideTimeout.current = setTimeout(onDismiss, duration);\n        }\n      }\n    });\n  });\n  var handleOnVisible = useLatestCallback(function () {\n    setHidden(false);\n  });\n  var handleOnHidden = useLatestCallback(function () {\n    if (hideTimeout.current) {\n      clearTimeout(hideTimeout.current);\n    }\n    Animated.timing(opacity, {\n      toValue: 0,\n      duration: 100 * scale,\n      useNativeDriver: true\n    }).start(function (_ref3) {\n      var finished = _ref3.finished;\n      if (finished) {\n        setHidden(true);\n      }\n    });\n  });\n  React.useEffect(function () {\n    if (!hidden) {\n      animateShow();\n    }\n  }, [animateShow, hidden]);\n  React.useEffect(function () {\n    return function () {\n      if (hideTimeout.current) clearTimeout(hideTimeout.current);\n    };\n  }, []);\n  React.useLayoutEffect(function () {\n    if (visible) {\n      handleOnVisible();\n    } else {\n      handleOnHidden();\n    }\n  }, [visible, handleOnVisible, handleOnHidden]);\n  var colors = theme.colors,\n    roundness = theme.roundness,\n    isV3 = theme.isV3;\n  if (hidden) {\n    return null;\n  }\n  var _ref4 = action || {},\n    actionStyle = _ref4.style,\n    actionLabel = _ref4.label,\n    onPressAction = _ref4.onPress,\n    actionRippleColor = _ref4.rippleColor,\n    actionProps = _objectWithoutProperties(_ref4, _excluded2);\n  var buttonTextColor = isV3 ? colors.inversePrimary : colors.accent;\n  var textColor = isV3 ? colors.inverseOnSurface : colors === null || colors === void 0 ? void 0 : colors.surface;\n  var backgroundColor = isV3 ? colors.inverseSurface : colors === null || colors === void 0 ? void 0 : colors.onSurface;\n  var isIconButton = isV3 && onIconPress;\n  var marginLeft = action ? -12 : -16;\n  var wrapperPaddings = {\n    paddingBottom: bottom,\n    paddingHorizontal: Math.max(left, right)\n  };\n  var renderChildrenWithWrapper = function renderChildrenWithWrapper() {\n    if (typeof children === 'string') {\n      return React.createElement(Text, {\n        variant: \"bodyMedium\",\n        style: [styles.content, {\n          color: textColor\n        }],\n        maxFontSizeMultiplier: maxFontSizeMultiplier\n      }, children);\n    }\n    return React.createElement(View, {\n      style: [styles.content, contentStyle]\n    }, React.createElement(View, null, children));\n  };\n  return React.createElement(View, {\n    pointerEvents: \"box-none\",\n    style: [styles.wrapper, wrapperPaddings, wrapperStyle]\n  }, React.createElement(Surface, _extends({\n    pointerEvents: \"box-none\",\n    accessibilityLiveRegion: \"polite\",\n    theme: theme,\n    style: [!isV3 && styles.elevation, styles.container, {\n      backgroundColor: backgroundColor,\n      borderRadius: roundness,\n      opacity: opacity,\n      transform: [{\n        scale: visible ? opacity.interpolate({\n          inputRange: [0, 1],\n          outputRange: [0.9, 1]\n        }) : 1\n      }]\n    }, style],\n    testID: testID,\n    container: true\n  }, isV3 && {\n    elevation: elevation\n  }, rest), renderChildrenWithWrapper(), (action || isIconButton) && React.createElement(View, {\n    style: [styles.actionsContainer, {\n      marginLeft: marginLeft\n    }]\n  }, action ? React.createElement(Button, _extends({\n    onPress: function onPress(event) {\n      onPressAction === null || onPressAction === void 0 || onPressAction(event);\n      onDismiss();\n    },\n    style: [styles.button, actionStyle],\n    textColor: buttonTextColor,\n    compact: !isV3,\n    mode: \"text\",\n    theme: theme,\n    rippleColor: actionRippleColor\n  }, actionProps), actionLabel) : null, isIconButton ? React.createElement(IconButton, {\n    accessibilityRole: \"button\",\n    borderless: true,\n    onPress: onIconPress,\n    iconColor: theme.colors.inverseOnSurface,\n    rippleColor: rippleColor,\n    theme: theme,\n    icon: icon || function (_ref5) {\n      var size = _ref5.size,\n        color = _ref5.color;\n      return React.createElement(MaterialCommunityIcon, {\n        name: \"close\",\n        color: color,\n        size: size,\n        direction: I18nManager.getConstants().isRTL ? 'rtl' : 'ltr'\n      });\n    },\n    accessibilityLabel: iconAccessibilityLabel,\n    style: styles.icon,\n    testID: `${testID}-icon`\n  }) : null)));\n};\nSnackbar.DURATION_SHORT = DURATION_SHORT;\nSnackbar.DURATION_MEDIUM = DURATION_MEDIUM;\nSnackbar.DURATION_LONG = DURATION_LONG;\nvar styles = StyleSheet.create({\n  wrapper: {\n    position: 'absolute',\n    bottom: 0,\n    width: '100%'\n  },\n  container: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    margin: 8,\n    borderRadius: 4,\n    minHeight: 48\n  },\n  content: {\n    marginHorizontal: 16,\n    marginVertical: 14,\n    flex: 1\n  },\n  actionsContainer: {\n    flexDirection: 'row',\n    justifyContent: 'flex-end',\n    alignItems: 'center',\n    minHeight: 48\n  },\n  button: {\n    marginRight: 8,\n    marginLeft: 4\n  },\n  elevation: {\n    elevation: 6\n  },\n  icon: {\n    width: 40,\n    height: 40,\n    margin: 0\n  }\n});\nexport default Snackbar;", "map": {"version": 3, "names": ["React", "Animated", "Easing", "I18nManager", "StyleSheet", "View", "useSafeAreaInsets", "useLatestCallback", "<PERSON><PERSON>", "IconButton", "MaterialCommunityIcon", "Surface", "Text", "useInternalTheme", "DURATION_SHORT", "DURATION_MEDIUM", "DURATION_LONG", "Snackbar", "_ref", "visible", "action", "icon", "onIconPress", "_ref$iconAccessibilit", "iconAccessibilityLabel", "_ref$duration", "duration", "on<PERSON><PERSON><PERSON>", "children", "_ref$elevation", "elevation", "style", "wrapperStyle", "contentStyle", "themeOverrides", "theme", "maxFontSizeMultiplier", "rippleColor", "testID", "rest", "_objectWithoutProperties", "_excluded", "_useSafeAreaInsets", "bottom", "right", "left", "_React$useRef", "useRef", "Value", "opacity", "current", "hideTimeout", "undefined", "_React$useState", "useState", "_React$useState2", "_slicedToArray", "hidden", "setHidden", "scale", "animation", "animateShow", "clearTimeout", "timing", "toValue", "easing", "out", "ease", "useNativeDriver", "start", "_ref2", "finished", "isInfinity", "Number", "POSITIVE_INFINITY", "NEGATIVE_INFINITY", "setTimeout", "handleOnVisible", "handleOnHidden", "_ref3", "useEffect", "useLayoutEffect", "colors", "roundness", "isV3", "_ref4", "actionStyle", "actionLabel", "label", "onPressAction", "onPress", "actionRippleColor", "actionProps", "_excluded2", "buttonTextColor", "inversePrimary", "accent", "textColor", "inverseOnSurface", "surface", "backgroundColor", "inverseSurface", "onSurface", "isIconButton", "marginLeft", "wrapperPaddings", "paddingBottom", "paddingHorizontal", "Math", "max", "renderChildrenWithWrapper", "createElement", "variant", "styles", "content", "color", "pointerEvents", "wrapper", "_extends", "accessibilityLiveRegion", "container", "borderRadius", "transform", "interpolate", "inputRange", "outputRange", "actionsContainer", "event", "button", "compact", "mode", "accessibilityRole", "borderless", "iconColor", "_ref5", "size", "name", "direction", "getConstants", "isRTL", "accessibilityLabel", "create", "position", "width", "flexDirection", "justifyContent", "margin", "minHeight", "marginHorizontal", "marginVertical", "flex", "alignItems", "marginRight", "height"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\Snackbar.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  Animated,\n  ColorValue,\n  Easing,\n  I18nManager,\n  StyleProp,\n  StyleSheet,\n  View,\n  ViewStyle,\n} from 'react-native';\n\nimport { useSafeAreaInsets } from 'react-native-safe-area-context';\nimport useLatestCallback from 'use-latest-callback';\n\nimport Button from './Button/Button';\nimport type { IconSource } from './Icon';\nimport IconButton from './IconButton/IconButton';\nimport MaterialCommunityIcon from './MaterialCommunityIcon';\nimport Surface from './Surface';\nimport Text from './Typography/Text';\nimport { useInternalTheme } from '../core/theming';\nimport type { $Omit, $RemoveChildren, ThemeProp } from '../types';\n\nexport type Props = $Omit<React.ComponentProps<typeof Surface>, 'mode'> & {\n  /**\n   * Whether the Snackbar is currently visible.\n   */\n  visible: boolean;\n  /**\n   * Label and press callback for the action button. It should contain the following properties:\n   * - `label` - Label of the action button\n   * - `onPress` - Callback that is called when action button is pressed.\n   */\n  action?: $RemoveChildren<typeof Button> & {\n    label: string;\n  };\n  /**\n   * @supported Available in v5.x with theme version 3\n   * Icon to display when `onIconPress` is defined. Default will be `close` icon.\n   */\n  icon?: IconSource;\n  /**\n   * @supported Available in v5.x with theme version 3\n   * Color of the ripple effect.\n   */\n  rippleColor?: ColorValue;\n  /**\n   * @supported Available in v5.x with theme version 3\n   * Function to execute on icon button press. The icon button appears only when this prop is specified.\n   */\n  onIconPress?: () => void;\n  /**\n   * @supported Available in v5.x with theme version 3\n   * Accessibility label for the icon button. This is read by the screen reader when the user taps the button.\n   */\n  iconAccessibilityLabel?: string;\n  /**\n   * The duration for which the Snackbar is shown.\n   */\n  duration?: number;\n  /**\n   * Callback called when Snackbar is dismissed. The `visible` prop needs to be updated when this is called.\n   */\n  onDismiss: () => void;\n  /**\n   * Text content of the Snackbar.\n   */\n  children: React.ReactNode;\n  /**\n   * @supported Available in v5.x with theme version 3\n   * Changes Snackbar shadow and background on iOS and Android.\n   */\n  elevation?: 0 | 1 | 2 | 3 | 4 | 5 | Animated.Value;\n  /**\n   * Specifies the largest possible scale a text font can reach.\n   */\n  maxFontSizeMultiplier?: number;\n  /**\n   * Style for the wrapper of the snackbar\n   */\n  wrapperStyle?: StyleProp<ViewStyle>;\n  /**\n   * Style for the content of the snackbar\n   */\n  contentStyle?: StyleProp<ViewStyle>;\n  style?: Animated.WithAnimatedValue<StyleProp<ViewStyle>>;\n  ref?: React.RefObject<View>;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n  /**\n   * TestID used for testing purposes\n   */\n  testID?: string;\n};\n\nconst DURATION_SHORT = 4000;\nconst DURATION_MEDIUM = 7000;\nconst DURATION_LONG = 10000;\n\n/**\n * Snackbars provide brief feedback about an operation through a message rendered at the bottom of the container in which it's wrapped.\n *\n * Note: To display it as a popup, regardless of the parent's position, wrap it with a `Portal` component – refer to the example in the \"More Examples` section.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { View, StyleSheet } from 'react-native';\n * import { Button, Snackbar } from 'react-native-paper';\n *\n * const MyComponent = () => {\n *   const [visible, setVisible] = React.useState(false);\n *\n *   const onToggleSnackBar = () => setVisible(!visible);\n *\n *   const onDismissSnackBar = () => setVisible(false);\n *\n *   return (\n *     <View style={styles.container}>\n *       <Button onPress={onToggleSnackBar}>{visible ? 'Hide' : 'Show'}</Button>\n *       <Snackbar\n *         visible={visible}\n *         onDismiss={onDismissSnackBar}\n *         action={{\n *           label: 'Undo',\n *           onPress: () => {\n *             // Do something\n *           },\n *         }}>\n *         Hey there! I'm a Snackbar.\n *       </Snackbar>\n *     </View>\n *   );\n * };\n *\n * const styles = StyleSheet.create({\n *   container: {\n *     flex: 1,\n *     justifyContent: 'space-between',\n *   },\n * });\n *\n * export default MyComponent;\n * ```\n */\nconst Snackbar = ({\n  visible,\n  action,\n  icon,\n  onIconPress,\n  iconAccessibilityLabel = 'Close icon',\n  duration = DURATION_MEDIUM,\n  onDismiss,\n  children,\n  elevation = 2,\n  style,\n  wrapperStyle,\n  contentStyle,\n  theme: themeOverrides,\n  maxFontSizeMultiplier,\n  rippleColor,\n  testID,\n  ...rest\n}: Props) => {\n  const theme = useInternalTheme(themeOverrides);\n  const { bottom, right, left } = useSafeAreaInsets();\n\n  const { current: opacity } = React.useRef<Animated.Value>(\n    new Animated.Value(0.0)\n  );\n  const hideTimeout = React.useRef<NodeJS.Timeout | undefined>(undefined);\n\n  const [hidden, setHidden] = React.useState(!visible);\n\n  const { scale } = theme.animation;\n\n  const animateShow = useLatestCallback(() => {\n    if (hideTimeout.current) clearTimeout(hideTimeout.current);\n\n    Animated.timing(opacity, {\n      toValue: 1,\n      duration: 200 * scale,\n      easing: Easing.out(Easing.ease),\n      useNativeDriver: true,\n    }).start(({ finished }) => {\n      if (finished) {\n        const isInfinity =\n          duration === Number.POSITIVE_INFINITY ||\n          duration === Number.NEGATIVE_INFINITY;\n\n        if (!isInfinity) {\n          hideTimeout.current = setTimeout(\n            onDismiss,\n            duration\n          ) as unknown as NodeJS.Timeout;\n        }\n      }\n    });\n  });\n\n  const handleOnVisible = useLatestCallback(() => {\n    // show\n    setHidden(false);\n  });\n\n  const handleOnHidden = useLatestCallback(() => {\n    // hide\n    if (hideTimeout.current) {\n      clearTimeout(hideTimeout.current);\n    }\n\n    Animated.timing(opacity, {\n      toValue: 0,\n      duration: 100 * scale,\n      useNativeDriver: true,\n    }).start(({ finished }) => {\n      if (finished) {\n        setHidden(true);\n      }\n    });\n  });\n\n  React.useEffect(() => {\n    if (!hidden) {\n      animateShow();\n    }\n  }, [animateShow, hidden]);\n\n  React.useEffect(() => {\n    return () => {\n      if (hideTimeout.current) clearTimeout(hideTimeout.current);\n    };\n  }, []);\n\n  React.useLayoutEffect(() => {\n    if (visible) {\n      handleOnVisible();\n    } else {\n      handleOnHidden();\n    }\n  }, [visible, handleOnVisible, handleOnHidden]);\n\n  const { colors, roundness, isV3 } = theme;\n\n  if (hidden) {\n    return null;\n  }\n\n  const {\n    style: actionStyle,\n    label: actionLabel,\n    onPress: onPressAction,\n    rippleColor: actionRippleColor,\n    ...actionProps\n  } = action || {};\n\n  const buttonTextColor = isV3 ? colors.inversePrimary : colors.accent;\n  const textColor = isV3 ? colors.inverseOnSurface : colors?.surface;\n  const backgroundColor = isV3 ? colors.inverseSurface : colors?.onSurface;\n\n  const isIconButton = isV3 && onIconPress;\n\n  const marginLeft = action ? -12 : -16;\n\n  const wrapperPaddings = {\n    paddingBottom: bottom,\n    paddingHorizontal: Math.max(left, right),\n  };\n\n  const renderChildrenWithWrapper = () => {\n    if (typeof children === 'string') {\n      return (\n        <Text\n          variant=\"bodyMedium\"\n          style={[styles.content, { color: textColor }]}\n          maxFontSizeMultiplier={maxFontSizeMultiplier}\n        >\n          {children}\n        </Text>\n      );\n    }\n\n    return (\n      <View style={[styles.content, contentStyle]}>\n        {/* View is added to allow multiple lines support for Text component as children */}\n        <View>{children}</View>\n      </View>\n    );\n  };\n\n  return (\n    <View\n      pointerEvents=\"box-none\"\n      style={[styles.wrapper, wrapperPaddings, wrapperStyle]}\n    >\n      <Surface\n        pointerEvents=\"box-none\"\n        accessibilityLiveRegion=\"polite\"\n        theme={theme}\n        style={[\n          !isV3 && styles.elevation,\n          styles.container,\n          {\n            backgroundColor,\n            borderRadius: roundness,\n            opacity: opacity,\n            transform: [\n              {\n                scale: visible\n                  ? opacity.interpolate({\n                      inputRange: [0, 1],\n                      outputRange: [0.9, 1],\n                    })\n                  : 1,\n              },\n            ],\n          },\n          style,\n        ]}\n        testID={testID}\n        container\n        {...(isV3 && { elevation })}\n        {...rest}\n      >\n        {renderChildrenWithWrapper()}\n        {(action || isIconButton) && (\n          <View style={[styles.actionsContainer, { marginLeft }]}>\n            {action ? (\n              <Button\n                onPress={(event) => {\n                  onPressAction?.(event);\n                  onDismiss();\n                }}\n                style={[styles.button, actionStyle]}\n                textColor={buttonTextColor}\n                compact={!isV3}\n                mode=\"text\"\n                theme={theme}\n                rippleColor={actionRippleColor}\n                {...actionProps}\n              >\n                {actionLabel}\n              </Button>\n            ) : null}\n            {isIconButton ? (\n              <IconButton\n                accessibilityRole=\"button\"\n                borderless\n                onPress={onIconPress}\n                iconColor={theme.colors.inverseOnSurface}\n                rippleColor={rippleColor}\n                theme={theme}\n                icon={\n                  icon ||\n                  (({ size, color }) => {\n                    return (\n                      <MaterialCommunityIcon\n                        name=\"close\"\n                        color={color}\n                        size={size}\n                        direction={\n                          I18nManager.getConstants().isRTL ? 'rtl' : 'ltr'\n                        }\n                      />\n                    );\n                  })\n                }\n                accessibilityLabel={iconAccessibilityLabel}\n                style={styles.icon}\n                testID={`${testID}-icon`}\n              />\n            ) : null}\n          </View>\n        )}\n      </Surface>\n    </View>\n  );\n};\n\n/**\n * Show the Snackbar for a short duration.\n */\nSnackbar.DURATION_SHORT = DURATION_SHORT;\n\n/**\n * Show the Snackbar for a medium duration.\n */\nSnackbar.DURATION_MEDIUM = DURATION_MEDIUM;\n\n/**\n * Show the Snackbar for a long duration.\n */\nSnackbar.DURATION_LONG = DURATION_LONG;\n\nconst styles = StyleSheet.create({\n  wrapper: {\n    position: 'absolute',\n    bottom: 0,\n    width: '100%',\n  },\n  container: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    margin: 8,\n    borderRadius: 4,\n    minHeight: 48,\n  },\n  content: {\n    marginHorizontal: 16,\n    marginVertical: 14,\n    flex: 1,\n  },\n  actionsContainer: {\n    flexDirection: 'row',\n    justifyContent: 'flex-end',\n    alignItems: 'center',\n    minHeight: 48,\n  },\n  button: {\n    marginRight: 8,\n    marginLeft: 4,\n  },\n  elevation: {\n    elevation: 6,\n  },\n  icon: {\n    width: 40,\n    height: 40,\n    margin: 0,\n  },\n});\n\nexport default Snackbar;\n"], "mappings": ";;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAAA,OAAAC,MAAA;AAAA,OAAAC,WAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAY9B,SAASC,iBAAiB,QAAQ,gCAAgC;AAClE,OAAOC,iBAAiB,MAAM,qBAAqB;AAEnD,OAAOC,MAAM;AAEb,OAAOC,UAAU;AACjB,OAAOC,qBAAqB;AAC5B,OAAOC,OAAO;AACd,OAAOC,IAAI;AACX,SAASC,gBAAgB;AA6EzB,IAAMC,cAAc,GAAG,IAAI;AAC3B,IAAMC,eAAe,GAAG,IAAI;AAC5B,IAAMC,aAAa,GAAG,KAAK;AAgD3B,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAAC,IAAA,EAkBD;EAAA,IAjBXC,OAAO,GAAAD,IAAA,CAAPC,OAAO;IACPC,MAAM,GAAAF,IAAA,CAANE,MAAM;IACNC,IAAI,GAAAH,IAAA,CAAJG,IAAI;IACJC,WAAW,GAAAJ,IAAA,CAAXI,WAAW;IAAAC,qBAAA,GAAAL,IAAA,CACXM,sBAAsB;IAAtBA,sBAAsB,GAAAD,qBAAA,cAAG,YAAY,GAAAA,qBAAA;IAAAE,aAAA,GAAAP,IAAA,CACrCQ,QAAQ;IAARA,QAAQ,GAAAD,aAAA,cAAGV,eAAe,GAAAU,aAAA;IAC1BE,SAAS,GAAAT,IAAA,CAATS,SAAS;IACTC,QAAQ,GAAAV,IAAA,CAARU,QAAQ;IAAAC,cAAA,GAAAX,IAAA,CACRY,SAAS;IAATA,SAAS,GAAAD,cAAA,cAAG,CAAC,GAAAA,cAAA;IACbE,KAAK,GAAAb,IAAA,CAALa,KAAK;IACLC,YAAY,GAAAd,IAAA,CAAZc,YAAY;IACZC,YAAY,GAAAf,IAAA,CAAZe,YAAY;IACLC,cAAc,GAAAhB,IAAA,CAArBiB,KAAK;IACLC,qBAAqB,GAAAlB,IAAA,CAArBkB,qBAAqB;IACrBC,WAAW,GAAAnB,IAAA,CAAXmB,WAAW;IACXC,MAAM,GAAApB,IAAA,CAANoB,MAAM;IACHC,IAAA,GAAAC,wBAAA,CAAAtB,IAAA,EAAAuB,SAAA;EAEH,IAAMN,KAAK,GAAGtB,gBAAgB,CAACqB,cAAc,CAAC;EAC9C,IAAAQ,kBAAA,GAAgCpC,iBAAiB,CAAC,CAAC;IAA3CqC,MAAM,GAAAD,kBAAA,CAANC,MAAM;IAAEC,KAAK,GAAAF,kBAAA,CAALE,KAAK;IAAEC,IAAA,GAAAH,kBAAA,CAAAG,IAAA;EAEvB,IAAAC,aAAA,GAA6B9C,KAAK,CAAC+C,MAAM,CACvC,IAAI9C,QAAQ,CAAC+C,KAAK,CAAC,GAAG,CACxB,CAAC;IAFgBC,OAAA,GAAAH,aAAA,CAATI,OAAO;EAGf,IAAMC,WAAW,GAAGnD,KAAK,CAAC+C,MAAM,CAA6BK,SAAS,CAAC;EAEvE,IAAAC,eAAA,GAA4BrD,KAAK,CAACsD,QAAQ,CAAC,CAACnC,OAAO,CAAC;IAAAoC,gBAAA,GAAAC,cAAA,CAAAH,eAAA;IAA7CI,MAAM,GAAAF,gBAAA;IAAEG,SAAS,GAAAH,gBAAA;EAExB,IAAQI,KAAA,GAAUxB,KAAK,CAACyB,SAAS,CAAzBD,KAAA;EAER,IAAME,WAAW,GAAGtD,iBAAiB,CAAC,YAAM;IAC1C,IAAI4C,WAAW,CAACD,OAAO,EAAEY,YAAY,CAACX,WAAW,CAACD,OAAO,CAAC;IAE1DjD,QAAQ,CAAC8D,MAAM,CAACd,OAAO,EAAE;MACvBe,OAAO,EAAE,CAAC;MACVtC,QAAQ,EAAE,GAAG,GAAGiC,KAAK;MACrBM,MAAM,EAAE/D,MAAM,CAACgE,GAAG,CAAChE,MAAM,CAACiE,IAAI,CAAC;MAC/BC,eAAe,EAAE;IACnB,CAAC,CAAC,CAACC,KAAK,CAAC,UAAAC,KAAA,EAAkB;MAAA,IAAfC,QAAA,GAAAD,KAAA,CAAAC,QAAA;MACV,IAAIA,QAAQ,EAAE;QACZ,IAAMC,UAAU,GACd9C,QAAQ,KAAK+C,MAAM,CAACC,iBAAiB,IACrChD,QAAQ,KAAK+C,MAAM,CAACE,iBAAiB;QAEvC,IAAI,CAACH,UAAU,EAAE;UACfrB,WAAW,CAACD,OAAO,GAAG0B,UAAU,CAC9BjD,SAAS,EACTD,QACF,CAA8B;QAChC;MACF;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF,IAAMmD,eAAe,GAAGtE,iBAAiB,CAAC,YAAM;IAE9CmD,SAAS,CAAC,KAAK,CAAC;EAClB,CAAC,CAAC;EAEF,IAAMoB,cAAc,GAAGvE,iBAAiB,CAAC,YAAM;IAE7C,IAAI4C,WAAW,CAACD,OAAO,EAAE;MACvBY,YAAY,CAACX,WAAW,CAACD,OAAO,CAAC;IACnC;IAEAjD,QAAQ,CAAC8D,MAAM,CAACd,OAAO,EAAE;MACvBe,OAAO,EAAE,CAAC;MACVtC,QAAQ,EAAE,GAAG,GAAGiC,KAAK;MACrBS,eAAe,EAAE;IACnB,CAAC,CAAC,CAACC,KAAK,CAAC,UAAAU,KAAA,EAAkB;MAAA,IAAfR,QAAA,GAAAQ,KAAA,CAAAR,QAAA;MACV,IAAIA,QAAQ,EAAE;QACZb,SAAS,CAAC,IAAI,CAAC;MACjB;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF1D,KAAK,CAACgF,SAAS,CAAC,YAAM;IACpB,IAAI,CAACvB,MAAM,EAAE;MACXI,WAAW,CAAC,CAAC;IACf;EACF,CAAC,EAAE,CAACA,WAAW,EAAEJ,MAAM,CAAC,CAAC;EAEzBzD,KAAK,CAACgF,SAAS,CAAC,YAAM;IACpB,OAAO,YAAM;MACX,IAAI7B,WAAW,CAACD,OAAO,EAAEY,YAAY,CAACX,WAAW,CAACD,OAAO,CAAC;IAC5D,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAENlD,KAAK,CAACiF,eAAe,CAAC,YAAM;IAC1B,IAAI9D,OAAO,EAAE;MACX0D,eAAe,CAAC,CAAC;IACnB,CAAC,MAAM;MACLC,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAAC3D,OAAO,EAAE0D,eAAe,EAAEC,cAAc,CAAC,CAAC;EAE9C,IAAQI,MAAM,GAAsB/C,KAAK,CAAjC+C,MAAM;IAAEC,SAAS,GAAWhD,KAAK,CAAzBgD,SAAS;IAAEC,IAAA,GAASjD,KAAK,CAAdiD,IAAA;EAE3B,IAAI3B,MAAM,EAAE;IACV,OAAO,IAAI;EACb;EAEA,IAAA4B,KAAA,GAMIjE,MAAM,IAAI,CAAC,CAAC;IALPkE,WAAW,GAAAD,KAAA,CAAlBtD,KAAK;IACEwD,WAAW,GAAAF,KAAA,CAAlBG,KAAK;IACIC,aAAa,GAAAJ,KAAA,CAAtBK,OAAO;IACMC,iBAAiB,GAAAN,KAAA,CAA9BhD,WAAW;IACRuD,WAAA,GAAApD,wBAAA,CAAA6C,KAAA,EAAAQ,UAAA;EAGL,IAAMC,eAAe,GAAGV,IAAI,GAAGF,MAAM,CAACa,cAAc,GAAGb,MAAM,CAACc,MAAM;EACpE,IAAMC,SAAS,GAAGb,IAAI,GAAGF,MAAM,CAACgB,gBAAgB,GAAGhB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEiB,OAAO;EAClE,IAAMC,eAAe,GAAGhB,IAAI,GAAGF,MAAM,CAACmB,cAAc,GAAGnB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEoB,SAAS;EAExE,IAAMC,YAAY,GAAGnB,IAAI,IAAI9D,WAAW;EAExC,IAAMkF,UAAU,GAAGpF,MAAM,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE;EAErC,IAAMqF,eAAe,GAAG;IACtBC,aAAa,EAAE/D,MAAM;IACrBgE,iBAAiB,EAAEC,IAAI,CAACC,GAAG,CAAChE,IAAI,EAAED,KAAK;EACzC,CAAC;EAED,IAAMkE,yBAAyB,GAAG,SAA5BA,yBAAyBA,CAAA,EAAS;IACtC,IAAI,OAAOlF,QAAQ,KAAK,QAAQ,EAAE;MAChC,OACE5B,KAAA,CAAA+G,aAAA,CAACnG,IAAI;QACHoG,OAAO,EAAC,YAAY;QACpBjF,KAAK,EAAE,CAACkF,MAAM,CAACC,OAAO,EAAE;UAAEC,KAAK,EAAElB;QAAU,CAAC,CAAE;QAC9C7D,qBAAqB,EAAEA;MAAsB,GAE5CR,QACG,CAAC;IAEX;IAEA,OACE5B,KAAA,CAAA+G,aAAA,CAAC1G,IAAI;MAAC0B,KAAK,EAAE,CAACkF,MAAM,CAACC,OAAO,EAAEjF,YAAY;IAAE,GAE1CjC,KAAA,CAAA+G,aAAA,CAAC1G,IAAI,QAAEuB,QAAe,CAClB,CAAC;EAEX,CAAC;EAED,OACE5B,KAAA,CAAA+G,aAAA,CAAC1G,IAAI;IACH+G,aAAa,EAAC,UAAU;IACxBrF,KAAK,EAAE,CAACkF,MAAM,CAACI,OAAO,EAAEZ,eAAe,EAAEzE,YAAY;EAAE,GAEvDhC,KAAA,CAAA+G,aAAA,CAACpG,OAAO,EAAA2G,QAAA;IACNF,aAAa,EAAC,UAAU;IACxBG,uBAAuB,EAAC,QAAQ;IAChCpF,KAAK,EAAEA,KAAM;IACbJ,KAAK,EAAE,CACL,CAACqD,IAAI,IAAI6B,MAAM,CAACnF,SAAS,EACzBmF,MAAM,CAACO,SAAS,EAChB;MACEpB,eAAe,EAAfA,eAAe;MACfqB,YAAY,EAAEtC,SAAS;MACvBlC,OAAO,EAAEA,OAAO;MAChByE,SAAS,EAAE,CACT;QACE/D,KAAK,EAAExC,OAAO,GACV8B,OAAO,CAAC0E,WAAW,CAAC;UAClBC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;UAClBC,WAAW,EAAE,CAAC,GAAG,EAAE,CAAC;QACtB,CAAC,CAAC,GACF;MACN,CAAC;IAEL,CAAC,EACD9F,KAAK,CACL;IACFO,MAAM,EAAEA,MAAO;IACfkF,SAAS;EAAA,GACJpC,IAAI,IAAI;IAAEtD,SAAA,EAAAA;EAAU,CAAC,EACtBS,IAAI,GAEPuE,yBAAyB,CAAC,CAAC,EAC3B,CAAC1F,MAAM,IAAImF,YAAY,KACtBvG,KAAA,CAAA+G,aAAA,CAAC1G,IAAI;IAAC0B,KAAK,EAAE,CAACkF,MAAM,CAACa,gBAAgB,EAAE;MAAEtB,UAAA,EAAAA;IAAW,CAAC;EAAE,GACpDpF,MAAM,GACLpB,KAAA,CAAA+G,aAAA,CAACvG,MAAM,EAAA8G,QAAA;IACL5B,OAAO,EAAG,SAAVA,OAAOA,CAAGqC,KAAK,EAAK;MAClBtC,aAAa,aAAbA,aAAa,eAAbA,aAAa,CAAGsC,KAAK,CAAC;MACtBpG,SAAS,CAAC,CAAC;IACb,CAAE;IACFI,KAAK,EAAE,CAACkF,MAAM,CAACe,MAAM,EAAE1C,WAAW,CAAE;IACpCW,SAAS,EAAEH,eAAgB;IAC3BmC,OAAO,EAAE,CAAC7C,IAAK;IACf8C,IAAI,EAAC,MAAM;IACX/F,KAAK,EAAEA,KAAM;IACbE,WAAW,EAAEsD;EAAkB,GAC3BC,WAAW,GAEdL,WACK,CAAC,GACP,IAAI,EACPgB,YAAY,GACXvG,KAAA,CAAA+G,aAAA,CAACtG,UAAU;IACT0H,iBAAiB,EAAC,QAAQ;IAC1BC,UAAU;IACV1C,OAAO,EAAEpE,WAAY;IACrB+G,SAAS,EAAElG,KAAK,CAAC+C,MAAM,CAACgB,gBAAiB;IACzC7D,WAAW,EAAEA,WAAY;IACzBF,KAAK,EAAEA,KAAM;IACbd,IAAI,EACFA,IAAI,IACH,UAAAiH,KAAA,EAAqB;MAAA,IAAlBC,IAAI,GAAAD,KAAA,CAAJC,IAAI;QAAEpB,KAAA,GAAAmB,KAAA,CAAAnB,KAAA;MACR,OACEnH,KAAA,CAAA+G,aAAA,CAACrG,qBAAqB;QACpB8H,IAAI,EAAC,OAAO;QACZrB,KAAK,EAAEA,KAAM;QACboB,IAAI,EAAEA,IAAK;QACXE,SAAS,EACPtI,WAAW,CAACuI,YAAY,CAAC,CAAC,CAACC,KAAK,GAAG,KAAK,GAAG;MAC5C,CACF,CAAC;IAEN,CACD;IACDC,kBAAkB,EAAEpH,sBAAuB;IAC3CO,KAAK,EAAEkF,MAAM,CAAC5F,IAAK;IACnBiB,MAAM,EAAE,GAAGA,MAAM;EAAQ,CAC1B,CAAC,GACA,IACA,CAED,CACL,CAAC;AAEX,CAAC;AAKDrB,QAAQ,CAACH,cAAc,GAAGA,cAAc;AAKxCG,QAAQ,CAACF,eAAe,GAAGA,eAAe;AAK1CE,QAAQ,CAACD,aAAa,GAAGA,aAAa;AAEtC,IAAMiG,MAAM,GAAG7G,UAAU,CAACyI,MAAM,CAAC;EAC/BxB,OAAO,EAAE;IACPyB,QAAQ,EAAE,UAAU;IACpBnG,MAAM,EAAE,CAAC;IACToG,KAAK,EAAE;EACT,CAAC;EACDvB,SAAS,EAAE;IACTwB,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE,eAAe;IAC/BC,MAAM,EAAE,CAAC;IACTzB,YAAY,EAAE,CAAC;IACf0B,SAAS,EAAE;EACb,CAAC;EACDjC,OAAO,EAAE;IACPkC,gBAAgB,EAAE,EAAE;IACpBC,cAAc,EAAE,EAAE;IAClBC,IAAI,EAAE;EACR,CAAC;EACDxB,gBAAgB,EAAE;IAChBkB,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE,UAAU;IAC1BM,UAAU,EAAE,QAAQ;IACpBJ,SAAS,EAAE;EACb,CAAC;EACDnB,MAAM,EAAE;IACNwB,WAAW,EAAE,CAAC;IACdhD,UAAU,EAAE;EACd,CAAC;EACD1E,SAAS,EAAE;IACTA,SAAS,EAAE;EACb,CAAC;EACDT,IAAI,EAAE;IACJ0H,KAAK,EAAE,EAAE;IACTU,MAAM,EAAE,EAAE;IACVP,MAAM,EAAE;EACV;AACF,CAAC,CAAC;AAEF,eAAejI,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}