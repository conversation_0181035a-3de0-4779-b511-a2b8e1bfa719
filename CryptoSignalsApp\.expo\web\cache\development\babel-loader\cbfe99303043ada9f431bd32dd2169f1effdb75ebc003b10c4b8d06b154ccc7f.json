{"ast": null, "code": "import I18nManager from \"react-native-web/dist/exports/I18nManager\";\nexport default function getInvertedMultiplier(gestureDirection) {\n  switch (gestureDirection) {\n    case 'vertical':\n      return 1;\n    case 'vertical-inverted':\n      return -1;\n    case 'horizontal':\n      return I18nManager.getConstants().isRTL ? -1 : 1;\n    case 'horizontal-inverted':\n      return I18nManager.getConstants().isRTL ? 1 : -1;\n  }\n}", "map": {"version": 3, "names": ["getInvertedMultiplier", "gestureDirection", "I18nManager", "getConstants", "isRTL"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\@react-navigation\\stack\\src\\utils\\getInvertedMultiplier.tsx"], "sourcesContent": ["import { I18nManager } from 'react-native';\n\nimport type { GestureDirection } from '../types';\n\nexport default function getInvertedMultiplier(\n  gestureDirection: GestureDirection\n): 1 | -1 {\n  switch (gestureDirection) {\n    case 'vertical':\n      return 1;\n    case 'vertical-inverted':\n      return -1;\n    case 'horizontal':\n      return I18nManager.getConstants().isRTL ? -1 : 1;\n    case 'horizontal-inverted':\n      return I18nManager.getConstants().isRTL ? 1 : -1;\n  }\n}\n"], "mappings": ";AAIA,eAAe,SAASA,qBAAqBA,CAC3CC,gBAAkC,EAC1B;EACR,QAAQA,gBAAgB;IACtB,KAAK,UAAU;MACb,OAAO,CAAC;IACV,KAAK,mBAAmB;MACtB,OAAO,CAAC,CAAC;IACX,KAAK,YAAY;MACf,OAAOC,WAAW,CAACC,YAAY,EAAE,CAACC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;IAClD,KAAK,qBAAqB;MACxB,OAAOF,WAAW,CAACC,YAAY,EAAE,CAACC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;EAAC;AAEvD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}