{"ast": null, "code": "import BackHandler from \"react-native-web/dist/exports/BackHandler\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nexport var isSearchBarAvailableForCurrentPlatform = ['ios', 'android'].includes(Platform.OS);\nexport function executeNativeBackPress() {\n  BackHandler.exitApp();\n  return true;\n}\nexport var compatibilityFlags = {\n  isNewBackTitleImplementation: true,\n  usesHeaderFlexboxImplementation: true\n};", "map": {"version": 3, "names": ["isSearchBarAvailableForCurrentPlatform", "includes", "Platform", "OS", "executeNativeBackPress", "<PERSON><PERSON><PERSON><PERSON>", "exitApp", "compatibilityFlags", "isNewBackTitleImplementation", "usesHeaderFlexboxImplementation"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\react-native-screens\\src\\utils.ts"], "sourcesContent": ["import { BackHandler, Platform } from 'react-native';\n\nexport const isSearchBarAvailableForCurrentPlatform = [\n  'ios',\n  'android',\n].includes(Platform.OS);\n\nexport function executeNativeBackPress() {\n  // This function invokes the native back press event\n  BackHandler.exitApp();\n  return true;\n}\n\n/**\n * Exposes information useful for downstream navigation library implementers,\n * so they can keep reasonable backward compatibility, if desired.\n *\n * We don't mean for this object to only grow in number of fields, however at the same time\n * we won't be very hasty to reduce it. Expect gradual changes.\n */\nexport const compatibilityFlags = {\n  /**\n   * Because of a bug introduced in https://github.com/software-mansion/react-native-screens/pull/1646\n   * react-native-screens v3.21 changed how header's backTitle handles whitespace strings in https://github.com/software-mansion/react-native-screens/pull/1726\n   * To allow for backwards compatibility in @react-navigation/native-stack we need a way to check if this version or newer is used.\n   * See https://github.com/react-navigation/react-navigation/pull/11423 for more context.\n   */\n  isNewBackTitleImplementation: true,\n\n  /**\n   * With version 4.0.0 the header implementation has been changed. To allow for backward compat\n   * with native-stack@v6 we want to expose a way to check whether the new implementation\n   * is in use or not.\n   *\n   * See:\n   * * https://github.com/software-mansion/react-native-screens/pull/2325\n   * * https://github.com/react-navigation/react-navigation/pull/12125\n   */\n  usesHeaderFlexboxImplementation: true,\n};\n"], "mappings": ";;AAEA,OAAO,IAAMA,sCAAsC,GAAG,CACpD,KAAK,EACL,SAAS,CACV,CAACC,QAAQ,CAACC,QAAQ,CAACC,EAAE,CAAC;AAEvB,OAAO,SAASC,sBAAsBA,CAAA,EAAG;EAEvCC,WAAW,CAACC,OAAO,CAAC,CAAC;EACrB,OAAO,IAAI;AACb;AASA,OAAO,IAAMC,kBAAkB,GAAG;EAOhCC,4BAA4B,EAAE,IAAI;EAWlCC,+BAA+B,EAAE;AACnC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}