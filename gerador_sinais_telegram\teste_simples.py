from utils.signal_formatter import SignalFormatter

# Teste simples da formatação
message = SignalFormatter.format_scalp_signal('CAKEUSDT', 2.35040, {40: 2.30339, 60: 2.27989}, 'SHORT', 20)

print("FORMATACAO TESTADA:")
print("=" * 50)
print(message)
print("=" * 50)

if 'CRYPTOSIGNALS PROFESSIONAL' in message:
    print("SUCESSO: Formatacao profissional funcionando!")
else:
    print("ERRO: Formatacao antiga ainda sendo usada")
