{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"style\", \"mode\", \"edges\"];\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport * as React from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport { useSafeAreaInsets } from \"./SafeAreaContext\";\nvar TOP = 8,\n  RIGHT = 4,\n  BOTTOM = 2,\n  LEFT = 1,\n  ALL = 15;\nvar edgeBitmaskMap = {\n  top: TOP,\n  right: RIGHT,\n  bottom: BOTTOM,\n  left: LEFT\n};\nexport var SafeAreaView = React.forwardRef(function (_ref, ref) {\n  var _ref$style = _ref.style,\n    style = _ref$style === void 0 ? {} : _ref$style,\n    mode = _ref.mode,\n    edges = _ref.edges,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var insets = useSafeAreaInsets();\n  var edgeBitmask = edges != null ? Array.isArray(edges) ? edges.reduce(function (acc, edge) {\n    return acc | edgeBitmaskMap[edge];\n  }, 0) : Object.keys(edges).reduce(function (acc, edge) {\n    return acc | edgeBitmaskMap[edge];\n  }, 0) : ALL;\n  var appliedStyle = React.useMemo(function () {\n    var insetTop = edgeBitmask & TOP ? insets.top : 0;\n    var insetRight = edgeBitmask & RIGHT ? insets.right : 0;\n    var insetBottom = edgeBitmask & BOTTOM ? insets.bottom : 0;\n    var insetLeft = edgeBitmask & LEFT ? insets.left : 0;\n    var flatStyle = StyleSheet.flatten(style);\n    if (mode === 'margin') {\n      var _flatStyle$margin = flatStyle.margin,\n        margin = _flatStyle$margin === void 0 ? 0 : _flatStyle$margin,\n        _flatStyle$marginVert = flatStyle.marginVertical,\n        marginVertical = _flatStyle$marginVert === void 0 ? margin : _flatStyle$marginVert,\n        _flatStyle$marginHori = flatStyle.marginHorizontal,\n        marginHorizontal = _flatStyle$marginHori === void 0 ? margin : _flatStyle$marginHori,\n        _flatStyle$marginTop = flatStyle.marginTop,\n        marginTop = _flatStyle$marginTop === void 0 ? marginVertical : _flatStyle$marginTop,\n        _flatStyle$marginRigh = flatStyle.marginRight,\n        marginRight = _flatStyle$marginRigh === void 0 ? marginHorizontal : _flatStyle$marginRigh,\n        _flatStyle$marginBott = flatStyle.marginBottom,\n        marginBottom = _flatStyle$marginBott === void 0 ? marginVertical : _flatStyle$marginBott,\n        _flatStyle$marginLeft = flatStyle.marginLeft,\n        marginLeft = _flatStyle$marginLeft === void 0 ? marginHorizontal : _flatStyle$marginLeft;\n      var marginStyle = {\n        marginTop: marginTop + insetTop,\n        marginRight: marginRight + insetRight,\n        marginBottom: marginBottom + insetBottom,\n        marginLeft: marginLeft + insetLeft\n      };\n      return [style, marginStyle];\n    } else {\n      var _flatStyle$padding = flatStyle.padding,\n        padding = _flatStyle$padding === void 0 ? 0 : _flatStyle$padding,\n        _flatStyle$paddingVer = flatStyle.paddingVertical,\n        paddingVertical = _flatStyle$paddingVer === void 0 ? padding : _flatStyle$paddingVer,\n        _flatStyle$paddingHor = flatStyle.paddingHorizontal,\n        paddingHorizontal = _flatStyle$paddingHor === void 0 ? padding : _flatStyle$paddingHor,\n        _flatStyle$paddingTop = flatStyle.paddingTop,\n        paddingTop = _flatStyle$paddingTop === void 0 ? paddingVertical : _flatStyle$paddingTop,\n        _flatStyle$paddingRig = flatStyle.paddingRight,\n        paddingRight = _flatStyle$paddingRig === void 0 ? paddingHorizontal : _flatStyle$paddingRig,\n        _flatStyle$paddingBot = flatStyle.paddingBottom,\n        paddingBottom = _flatStyle$paddingBot === void 0 ? paddingVertical : _flatStyle$paddingBot,\n        _flatStyle$paddingLef = flatStyle.paddingLeft,\n        paddingLeft = _flatStyle$paddingLef === void 0 ? paddingHorizontal : _flatStyle$paddingLef;\n      var paddingStyle = {\n        paddingTop: paddingTop + insetTop,\n        paddingRight: paddingRight + insetRight,\n        paddingBottom: paddingBottom + insetBottom,\n        paddingLeft: paddingLeft + insetLeft\n      };\n      return [style, paddingStyle];\n    }\n  }, [style, insets, mode, edgeBitmask]);\n  return React.createElement(View, _extends({\n    style: appliedStyle\n  }, rest, {\n    ref: ref\n  }));\n});", "map": {"version": 3, "names": ["React", "View", "StyleSheet", "useSafeAreaInsets", "TOP", "RIGHT", "BOTTOM", "LEFT", "ALL", "edgeBitmaskMap", "top", "right", "bottom", "left", "SafeAreaView", "forwardRef", "_ref", "ref", "_ref$style", "style", "mode", "edges", "rest", "_objectWithoutProperties", "_excluded", "insets", "edgeBitmask", "Array", "isArray", "reduce", "acc", "edge", "Object", "keys", "appliedStyle", "useMemo", "insetTop", "insetRight", "insetBottom", "insetLeft", "flatStyle", "flatten", "_flatStyle$margin", "margin", "_flatStyle$marginVert", "marginVertical", "_flatStyle$marginHori", "marginHorizontal", "_flatStyle$marginTop", "marginTop", "_flatStyle$marginRigh", "marginRight", "_flatStyle$marginBott", "marginBottom", "_flatStyle$marginLeft", "marginLeft", "marginStyle", "_flatStyle$padding", "padding", "_flatStyle$paddingVer", "paddingVertical", "_flatStyle$paddingHor", "paddingHorizontal", "_flatStyle$paddingTop", "paddingTop", "_flatStyle$paddingRig", "paddingRight", "_flatStyle$paddingBot", "paddingBottom", "_flatStyle$paddingLef", "paddingLeft", "paddingStyle", "createElement", "_extends"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\react-native-safe-area-context\\src\\SafeAreaView.web.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { View, StyleSheet } from 'react-native';\nimport { useSafeAreaInsets } from './SafeAreaContext';\nimport type {\n  Edge,\n  NativeSafeAreaViewInstance,\n  NativeSafeAreaViewProps,\n} from './SafeArea.types';\n\n// prettier-ignore\nconst TOP    = 0b1000,\n      RIGHT  = 0b0100,\n      BOTTOM = 0b0010,\n      LEFT   = 0b0001,\n      ALL    = 0b1111;\n\n/* eslint-disable no-bitwise */\n\nconst edgeBitmaskMap: Record<Edge, number> = {\n  top: TOP,\n  right: RIGHT,\n  bottom: BOTTOM,\n  left: LEFT,\n};\n\nexport const SafeAreaView = React.forwardRef<\n  NativeSafeAreaViewInstance,\n  NativeSafeAreaViewProps\n>(({ style = {}, mode, edges, ...rest }, ref) => {\n  const insets = useSafeAreaInsets();\n\n  const edgeBitmask =\n    edges != null\n      ? Array.isArray(edges)\n        ? edges.reduce(\n            (acc: number, edge: Edge) => acc | edgeBitmaskMap[edge],\n            0,\n          )\n        : Object.keys(edges).reduce(\n            (acc, edge) => acc | edgeBitmaskMap[edge as Edge],\n            0,\n          )\n      : ALL;\n\n  const appliedStyle = React.useMemo(() => {\n    const insetTop = edgeBitmask & TOP ? insets.top : 0;\n    const insetRight = edgeBitmask & RIGHT ? insets.right : 0;\n    const insetBottom = edgeBitmask & BOTTOM ? insets.bottom : 0;\n    const insetLeft = edgeBitmask & LEFT ? insets.left : 0;\n\n    const flatStyle = StyleSheet.flatten(style) as Record<string, number>;\n\n    if (mode === 'margin') {\n      const {\n        margin = 0,\n        marginVertical = margin,\n        marginHorizontal = margin,\n        marginTop = marginVertical,\n        marginRight = marginHorizontal,\n        marginBottom = marginVertical,\n        marginLeft = marginHorizontal,\n      } = flatStyle;\n\n      const marginStyle = {\n        marginTop: marginTop + insetTop,\n        marginRight: marginRight + insetRight,\n        marginBottom: marginBottom + insetBottom,\n        marginLeft: marginLeft + insetLeft,\n      };\n\n      return [style, marginStyle];\n    } else {\n      const {\n        padding = 0,\n        paddingVertical = padding,\n        paddingHorizontal = padding,\n        paddingTop = paddingVertical,\n        paddingRight = paddingHorizontal,\n        paddingBottom = paddingVertical,\n        paddingLeft = paddingHorizontal,\n      } = flatStyle;\n\n      const paddingStyle = {\n        paddingTop: paddingTop + insetTop,\n        paddingRight: paddingRight + insetRight,\n        paddingBottom: paddingBottom + insetBottom,\n        paddingLeft: paddingLeft + insetLeft,\n      };\n\n      return [style, paddingStyle];\n    }\n  }, [style, insets, mode, edgeBitmask]);\n\n  return <View style={appliedStyle} {...rest} ref={ref} />;\n});\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,IAAA;AAAA,OAAAC,UAAA;AAE9B,SAASC,iBAAiB;AAQ1B,IAAMC,GAAG,GAAM,CAAM;EACfC,KAAK,GAAI,CAAM;EACfC,MAAM,GAAG,CAAM;EACfC,IAAI,GAAK,CAAM;EACfC,GAAG,GAAM,EAAM;AAIrB,IAAMC,cAAoC,GAAG;EAC3CC,GAAG,EAAEN,GAAG;EACRO,KAAK,EAAEN,KAAK;EACZO,MAAM,EAAEN,MAAM;EACdO,IAAI,EAAEN;AACR,CAAC;AAED,OAAO,IAAMO,YAAY,GAAGd,KAAK,CAACe,UAAU,CAG1C,UAAAC,IAAA,EAAuCC,GAAG,EAAK;EAAA,IAAAC,UAAA,GAAVF,IAAA,CAAlCG,KAAK;IAALA,KAAK,GAAAD,UAAA,cAAG,CAAC,CAAC,GAAAA,UAAA;IAAEE,IAAI,GAAkBJ,IAAA,CAAtBI,IAAI;IAAEC,KAAK,GAAWL,IAAA,CAAhBK,KAAK;IAAKC,IAAA,GAAAC,wBAAA,CAAMP,IAAA,EAAAQ,SAAA;EACrC,IAAMC,MAAM,GAAGtB,iBAAiB,EAAE;EAElC,IAAMuB,WAAW,GACfL,KAAK,IAAI,IAAI,GACTM,KAAK,CAACC,OAAO,CAACP,KAAK,CAAC,GAClBA,KAAK,CAACQ,MAAM,CACV,UAACC,GAAW,EAAEC,IAAU;IAAA,OAAKD,GAAG,GAAGrB,cAAc,CAACsB,IAAI,CAAC;EAAA,GACvD,CAAC,CACF,GACDC,MAAM,CAACC,IAAI,CAACZ,KAAK,CAAC,CAACQ,MAAM,CACvB,UAACC,GAAG,EAAEC,IAAI;IAAA,OAAKD,GAAG,GAAGrB,cAAc,CAACsB,IAAI,CAAS;EAAA,GACjD,CAAC,CACF,GACHvB,GAAG;EAET,IAAM0B,YAAY,GAAGlC,KAAK,CAACmC,OAAO,CAAC,YAAM;IACvC,IAAMC,QAAQ,GAAGV,WAAW,GAAGtB,GAAG,GAAGqB,MAAM,CAACf,GAAG,GAAG,CAAC;IACnD,IAAM2B,UAAU,GAAGX,WAAW,GAAGrB,KAAK,GAAGoB,MAAM,CAACd,KAAK,GAAG,CAAC;IACzD,IAAM2B,WAAW,GAAGZ,WAAW,GAAGpB,MAAM,GAAGmB,MAAM,CAACb,MAAM,GAAG,CAAC;IAC5D,IAAM2B,SAAS,GAAGb,WAAW,GAAGnB,IAAI,GAAGkB,MAAM,CAACZ,IAAI,GAAG,CAAC;IAEtD,IAAM2B,SAAS,GAAGtC,UAAU,CAACuC,OAAO,CAACtB,KAAK,CAA2B;IAErE,IAAIC,IAAI,KAAK,QAAQ,EAAE;MACrB,IAAAsB,iBAAA,GAQIF,SAAS,CAPXG,MAAM;QAANA,MAAM,GAAAD,iBAAA,cAAG,CAAC,GAAAA,iBAAA;QAAAE,qBAAA,GAORJ,SAAS,CANXK,cAAc;QAAdA,cAAc,GAAAD,qBAAA,cAAGD,MAAM,GAAAC,qBAAA;QAAAE,qBAAA,GAMrBN,SAAS,CALXO,gBAAgB;QAAhBA,gBAAgB,GAAAD,qBAAA,cAAGH,MAAM,GAAAG,qBAAA;QAAAE,oBAAA,GAKvBR,SAAS,CAJXS,SAAS;QAATA,SAAS,GAAAD,oBAAA,cAAGH,cAAc,GAAAG,oBAAA;QAAAE,qBAAA,GAIxBV,SAAS,CAHXW,WAAW;QAAXA,WAAW,GAAAD,qBAAA,cAAGH,gBAAgB,GAAAG,qBAAA;QAAAE,qBAAA,GAG5BZ,SAAS,CAFXa,YAAY;QAAZA,YAAY,GAAAD,qBAAA,cAAGP,cAAc,GAAAO,qBAAA;QAAAE,qBAAA,GAE3Bd,SAAS,CADXe,UAAU;QAAVA,UAAU,GAAAD,qBAAA,cAAGP,gBAAA,GAAAO,qBAAA;MAGf,IAAME,WAAW,GAAG;QAClBP,SAAS,EAAEA,SAAS,GAAGb,QAAQ;QAC/Be,WAAW,EAAEA,WAAW,GAAGd,UAAU;QACrCgB,YAAY,EAAEA,YAAY,GAAGf,WAAW;QACxCiB,UAAU,EAAEA,UAAU,GAAGhB;MAC3B,CAAC;MAED,OAAO,CAACpB,KAAK,EAAEqC,WAAW,CAAC;IAC7B,CAAC,MAAM;MACL,IAAAC,kBAAA,GAQIjB,SAAS,CAPXkB,OAAO;QAAPA,OAAO,GAAAD,kBAAA,cAAG,CAAC,GAAAA,kBAAA;QAAAE,qBAAA,GAOTnB,SAAS,CANXoB,eAAe;QAAfA,eAAe,GAAAD,qBAAA,cAAGD,OAAO,GAAAC,qBAAA;QAAAE,qBAAA,GAMvBrB,SAAS,CALXsB,iBAAiB;QAAjBA,iBAAiB,GAAAD,qBAAA,cAAGH,OAAO,GAAAG,qBAAA;QAAAE,qBAAA,GAKzBvB,SAAS,CAJXwB,UAAU;QAAVA,UAAU,GAAAD,qBAAA,cAAGH,eAAe,GAAAG,qBAAA;QAAAE,qBAAA,GAI1BzB,SAAS,CAHX0B,YAAY;QAAZA,YAAY,GAAAD,qBAAA,cAAGH,iBAAiB,GAAAG,qBAAA;QAAAE,qBAAA,GAG9B3B,SAAS,CAFX4B,aAAa;QAAbA,aAAa,GAAAD,qBAAA,cAAGP,eAAe,GAAAO,qBAAA;QAAAE,qBAAA,GAE7B7B,SAAS,CADX8B,WAAW;QAAXA,WAAW,GAAAD,qBAAA,cAAGP,iBAAA,GAAAO,qBAAA;MAGhB,IAAME,YAAY,GAAG;QACnBP,UAAU,EAAEA,UAAU,GAAG5B,QAAQ;QACjC8B,YAAY,EAAEA,YAAY,GAAG7B,UAAU;QACvC+B,aAAa,EAAEA,aAAa,GAAG9B,WAAW;QAC1CgC,WAAW,EAAEA,WAAW,GAAG/B;MAC7B,CAAC;MAED,OAAO,CAACpB,KAAK,EAAEoD,YAAY,CAAC;IAC9B;EACF,CAAC,EAAE,CAACpD,KAAK,EAAEM,MAAM,EAAEL,IAAI,EAAEM,WAAW,CAAC,CAAC;EAEtC,OAAO1B,KAAA,CAAAwE,aAAA,CAACvE,IAAI,EAAAwE,QAAA;IAACtD,KAAK,EAAEe;EAAa,GAAKZ,IAAI;IAAEL,GAAG,EAAEA;EAAI,GAAG;AAC1D,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}