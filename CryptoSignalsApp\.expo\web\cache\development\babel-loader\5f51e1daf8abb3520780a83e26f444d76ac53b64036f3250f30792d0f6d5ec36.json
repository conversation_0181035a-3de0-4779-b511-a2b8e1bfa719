{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"value\", \"disabled\", \"onValueChange\", \"color\", \"theme\"];\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nimport * as React from 'react';\nimport NativeModules from \"react-native-web/dist/exports/NativeModules\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport NativeSwitch from \"react-native-web/dist/exports/Switch\";\nimport { getSwitchColor } from \"./utils\";\nimport { useInternalTheme } from \"../../core/theming\";\nvar version = NativeModules.PlatformConstants ? NativeModules.PlatformConstants.reactNativeVersion : undefined;\nvar Switch = function Switch(_ref) {\n  var value = _ref.value,\n    disabled = _ref.disabled,\n    onValueChange = _ref.onValueChange,\n    color = _ref.color,\n    themeOverrides = _ref.theme,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var theme = useInternalTheme(themeOverrides);\n  var _getSwitchColor = getSwitchColor({\n      theme: theme,\n      disabled: disabled,\n      value: value,\n      color: color\n    }),\n    checkedColor = _getSwitchColor.checkedColor,\n    onTintColor = _getSwitchColor.onTintColor,\n    thumbTintColor = _getSwitchColor.thumbTintColor;\n  var props = version && version.major === 0 && version.minor <= 56 ? {\n    onTintColor: onTintColor,\n    thumbTintColor: thumbTintColor\n  } : Platform.OS === 'web' ? {\n    activeTrackColor: onTintColor,\n    thumbColor: thumbTintColor,\n    activeThumbColor: checkedColor\n  } : {\n    thumbColor: thumbTintColor,\n    trackColor: {\n      true: onTintColor,\n      false: onTintColor\n    }\n  };\n  return React.createElement(NativeSwitch, _extends({\n    value: value,\n    disabled: disabled,\n    onValueChange: disabled ? undefined : onValueChange\n  }, props, rest));\n};\nexport default Switch;", "map": {"version": 3, "names": ["React", "NativeModules", "Platform", "NativeSwitch", "getSwitchColor", "useInternalTheme", "version", "PlatformConstants", "reactNativeVersion", "undefined", "Switch", "_ref", "value", "disabled", "onValueChange", "color", "themeOverrides", "theme", "rest", "_objectWithoutProperties", "_excluded", "_getSwitchColor", "checkedColor", "onTintColor", "thumbTintColor", "props", "major", "minor", "OS", "activeTrackColor", "thumbColor", "activeThumbColor", "trackColor", "true", "false", "createElement", "_extends"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\Switch\\Switch.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  NativeModules,\n  Platform,\n  StyleProp,\n  Switch as NativeSwitch,\n  ViewStyle,\n} from 'react-native';\n\nimport { getSwitchColor } from './utils';\nimport { useInternalTheme } from '../../core/theming';\nimport type { ThemeProp } from '../../types';\n\nconst version = NativeModules.PlatformConstants\n  ? NativeModules.PlatformConstants.reactNativeVersion\n  : undefined;\n\nexport type Props = React.ComponentPropsWithRef<typeof NativeSwitch> & {\n  /**\n   * Disable toggling the switch.\n   */\n  disabled?: boolean;\n  /**\n   * Value of the switch, true means 'on', false means 'off'.\n   */\n  value?: boolean;\n  /**\n   * Custom color for switch.\n   */\n  color?: string;\n  /**\n   * Callback called with the new value when it changes.\n   */\n  onValueChange?: Function;\n  style?: StyleProp<ViewStyle>;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n};\n\n/**\n * Switch is a visual toggle between two mutually exclusive states — on and off.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { Switch } from 'react-native-paper';\n *\n * const MyComponent = () => {\n *   const [isSwitchOn, setIsSwitchOn] = React.useState(false);\n *\n *   const onToggleSwitch = () => setIsSwitchOn(!isSwitchOn);\n *\n *   return <Switch value={isSwitchOn} onValueChange={onToggleSwitch} />;\n * };\n *\n * export default MyComponent;\n * ```\n */\nconst Switch = ({\n  value,\n  disabled,\n  onValueChange,\n  color,\n  theme: themeOverrides,\n  ...rest\n}: Props) => {\n  const theme = useInternalTheme(themeOverrides);\n  const { checkedColor, onTintColor, thumbTintColor } = getSwitchColor({\n    theme,\n    disabled,\n    value,\n    color,\n  });\n\n  const props =\n    version && version.major === 0 && version.minor <= 56\n      ? {\n          onTintColor,\n          thumbTintColor,\n        }\n      : Platform.OS === 'web'\n      ? {\n          activeTrackColor: onTintColor,\n          thumbColor: thumbTintColor,\n          activeThumbColor: checkedColor,\n        }\n      : {\n          thumbColor: thumbTintColor,\n          trackColor: {\n            true: onTintColor,\n            false: onTintColor,\n          },\n        };\n\n  return (\n    <NativeSwitch\n      value={value}\n      disabled={disabled}\n      onValueChange={disabled ? undefined : onValueChange}\n      {...props}\n      {...rest}\n    />\n  );\n};\n\nexport default Switch;\n"], "mappings": ";;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,aAAA;AAAA,OAAAC,QAAA;AAAA,OAAAC,YAAA;AAS9B,SAASC,cAAc;AACvB,SAASC,gBAAgB;AAGzB,IAAMC,OAAO,GAAGL,aAAa,CAACM,iBAAiB,GAC3CN,aAAa,CAACM,iBAAiB,CAACC,kBAAkB,GAClDC,SAAS;AA6Cb,IAAMC,MAAM,GAAG,SAATA,MAAMA,CAAAC,IAAA,EAOC;EAAA,IANXC,KAAK,GAAAD,IAAA,CAALC,KAAK;IACLC,QAAQ,GAAAF,IAAA,CAARE,QAAQ;IACRC,aAAa,GAAAH,IAAA,CAAbG,aAAa;IACbC,KAAK,GAAAJ,IAAA,CAALI,KAAK;IACEC,cAAc,GAAAL,IAAA,CAArBM,KAAK;IACFC,IAAA,GAAAC,wBAAA,CAAAR,IAAA,EAAAS,SAAA;EAEH,IAAMH,KAAK,GAAGZ,gBAAgB,CAACW,cAAc,CAAC;EAC9C,IAAAK,eAAA,GAAsDjB,cAAc,CAAC;MACnEa,KAAK,EAALA,KAAK;MACLJ,QAAQ,EAARA,QAAQ;MACRD,KAAK,EAALA,KAAK;MACLG,KAAA,EAAAA;IACF,CAAC,CAAC;IALMO,YAAY,GAAAD,eAAA,CAAZC,YAAY;IAAEC,WAAW,GAAAF,eAAA,CAAXE,WAAW;IAAEC,cAAA,GAAAH,eAAA,CAAAG,cAAA;EAOnC,IAAMC,KAAK,GACTnB,OAAO,IAAIA,OAAO,CAACoB,KAAK,KAAK,CAAC,IAAIpB,OAAO,CAACqB,KAAK,IAAI,EAAE,GACjD;IACEJ,WAAW,EAAXA,WAAW;IACXC,cAAA,EAAAA;EACF,CAAC,GACDtB,QAAQ,CAAC0B,EAAE,KAAK,KAAK,GACrB;IACEC,gBAAgB,EAAEN,WAAW;IAC7BO,UAAU,EAAEN,cAAc;IAC1BO,gBAAgB,EAAET;EACpB,CAAC,GACD;IACEQ,UAAU,EAAEN,cAAc;IAC1BQ,UAAU,EAAE;MACVC,IAAI,EAAEV,WAAW;MACjBW,KAAK,EAAEX;IACT;EACF,CAAC;EAEP,OACEvB,KAAA,CAAAmC,aAAA,CAAChC,YAAY,EAAAiC,QAAA;IACXxB,KAAK,EAAEA,KAAM;IACbC,QAAQ,EAAEA,QAAS;IACnBC,aAAa,EAAED,QAAQ,GAAGJ,SAAS,GAAGK;EAAc,GAChDW,KAAK,EACLP,IAAI,CACT,CAAC;AAEN,CAAC;AAED,eAAeR,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}