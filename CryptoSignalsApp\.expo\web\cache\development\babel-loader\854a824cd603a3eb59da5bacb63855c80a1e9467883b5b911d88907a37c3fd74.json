{"ast": null, "code": "function _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nimport * as React from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport { useInternalTheme } from \"../../core/theming\";\nvar DialogActions = function DialogActions(props) {\n  var _useInternalTheme = useInternalTheme(props.theme),\n    isV3 = _useInternalTheme.isV3;\n  var actionsLength = React.Children.toArray(props.children).length;\n  return React.createElement(View, _extends({}, props, {\n    style: [isV3 ? styles.v3Container : styles.container, props.style]\n  }), React.Children.map(props.children, function (child, i) {\n    return React.isValidElement(child) ? React.cloneElement(child, {\n      compact: true,\n      uppercase: !isV3,\n      style: [isV3 && {\n        marginRight: i + 1 === actionsLength ? 0 : 8\n      }, child.props.style]\n    }) : child;\n  }));\n};\nDialogActions.displayName = 'Dialog.Actions';\nvar styles = StyleSheet.create({\n  container: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    justifyContent: 'flex-end',\n    padding: 8\n  },\n  v3Container: {\n    flexDirection: 'row',\n    flexGrow: 1,\n    alignItems: 'center',\n    justifyContent: 'flex-end',\n    paddingBottom: 24,\n    paddingHorizontal: 24\n  }\n});\nexport default DialogActions;", "map": {"version": 3, "names": ["React", "StyleSheet", "View", "useInternalTheme", "DialogActions", "props", "_useInternalTheme", "theme", "isV3", "<PERSON><PERSON><PERSON><PERSON>", "Children", "toArray", "children", "length", "createElement", "_extends", "style", "styles", "v3Container", "container", "map", "child", "i", "isValidElement", "cloneElement", "compact", "uppercase", "marginRight", "displayName", "create", "flexDirection", "alignItems", "justifyContent", "padding", "flexGrow", "paddingBottom", "paddingHorizontal"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\Dialog\\DialogActions.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { StyleProp, StyleSheet, View, ViewStyle } from 'react-native';\n\nimport type { ThemeProp } from 'src/types';\n\nimport { DialogActionChildProps } from './utils';\nimport { useInternalTheme } from '../../core/theming';\n\nexport type Props = React.ComponentPropsWithRef<typeof View> & {\n  /**\n   * Content of the `DialogActions`.\n   */\n  children: React.ReactNode;\n  style?: StyleProp<ViewStyle>;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n};\n\n/**\n * A component to show a list of actions in a Dialog.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { Button, Dialog, Portal } from 'react-native-paper';\n *\n * const MyComponent = () => {\n *   const [visible, setVisible] = React.useState(false);\n *\n *   const hideDialog = () => setVisible(false);\n *\n *   return (\n *     <Portal>\n *       <Dialog visible={visible} onDismiss={hideDialog}>\n *         <Dialog.Actions>\n *           <Button onPress={() => console.log('Cancel')}>Cancel</Button>\n *           <Button onPress={() => console.log('Ok')}>Ok</Button>\n *         </Dialog.Actions>\n *       </Dialog>\n *     </Portal>\n *   );\n * };\n *\n * export default MyComponent;\n * ```\n */\nconst DialogActions = (props: Props) => {\n  const { isV3 } = useInternalTheme(props.theme);\n  const actionsLength = React.Children.toArray(props.children).length;\n\n  return (\n    <View\n      {...props}\n      style={[isV3 ? styles.v3Container : styles.container, props.style]}\n    >\n      {React.Children.map(props.children, (child, i) =>\n        React.isValidElement<DialogActionChildProps>(child)\n          ? React.cloneElement(child, {\n              compact: true,\n              uppercase: !isV3,\n              style: [\n                isV3 && {\n                  marginRight: i + 1 === actionsLength ? 0 : 8,\n                },\n                child.props.style,\n              ],\n            })\n          : child\n      )}\n    </View>\n  );\n};\n\nDialogActions.displayName = 'Dialog.Actions';\n\nconst styles = StyleSheet.create({\n  container: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    justifyContent: 'flex-end',\n    padding: 8,\n  },\n  v3Container: {\n    flexDirection: 'row',\n    flexGrow: 1,\n    alignItems: 'center',\n    justifyContent: 'flex-end',\n    paddingBottom: 24,\n    paddingHorizontal: 24,\n  },\n});\n\nexport default DialogActions;\n"], "mappings": ";;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAM9B,SAASC,gBAAgB;AA0CzB,IAAMC,aAAa,GAAI,SAAjBA,aAAaA,CAAIC,KAAY,EAAK;EACtC,IAAAC,iBAAA,GAAiBH,gBAAgB,CAACE,KAAK,CAACE,KAAK,CAAC;IAAtCC,IAAA,GAAAF,iBAAA,CAAAE,IAAA;EACR,IAAMC,aAAa,GAAGT,KAAK,CAACU,QAAQ,CAACC,OAAO,CAACN,KAAK,CAACO,QAAQ,CAAC,CAACC,MAAM;EAEnE,OACEb,KAAA,CAAAc,aAAA,CAACZ,IAAI,EAAAa,QAAA,KACCV,KAAK;IACTW,KAAK,EAAE,CAACR,IAAI,GAAGS,MAAM,CAACC,WAAW,GAAGD,MAAM,CAACE,SAAS,EAAEd,KAAK,CAACW,KAAK;EAAE,IAElEhB,KAAK,CAACU,QAAQ,CAACU,GAAG,CAACf,KAAK,CAACO,QAAQ,EAAE,UAACS,KAAK,EAAEC,CAAC;IAAA,OAC3CtB,KAAK,CAACuB,cAAc,CAAyBF,KAAK,CAAC,GAC/CrB,KAAK,CAACwB,YAAY,CAACH,KAAK,EAAE;MACxBI,OAAO,EAAE,IAAI;MACbC,SAAS,EAAE,CAAClB,IAAI;MAChBQ,KAAK,EAAE,CACLR,IAAI,IAAI;QACNmB,WAAW,EAAEL,CAAC,GAAG,CAAC,KAAKb,aAAa,GAAG,CAAC,GAAG;MAC7C,CAAC,EACDY,KAAK,CAAChB,KAAK,CAACW,KAAK;IAErB,CAAC,CAAC,GACFK,KACN;EAAA,EACI,CAAC;AAEX,CAAC;AAEDjB,aAAa,CAACwB,WAAW,GAAG,gBAAgB;AAE5C,IAAMX,MAAM,GAAGhB,UAAU,CAAC4B,MAAM,CAAC;EAC/BV,SAAS,EAAE;IACTW,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,UAAU;IAC1BC,OAAO,EAAE;EACX,CAAC;EACDf,WAAW,EAAE;IACXY,aAAa,EAAE,KAAK;IACpBI,QAAQ,EAAE,CAAC;IACXH,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,UAAU;IAC1BG,aAAa,EAAE,EAAE;IACjBC,iBAAiB,EAAE;EACrB;AACF,CAAC,CAAC;AAEF,eAAehC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}