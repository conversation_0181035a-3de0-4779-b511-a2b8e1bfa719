{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"icon\", \"label\", \"active\", \"disabled\", \"theme\", \"rippleColor\", \"style\", \"onPress\", \"background\", \"accessibilityLabel\", \"right\", \"labelMaxFontSizeMultiplier\", \"hitSlop\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport * as React from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport color from 'color';\nimport { useInternalTheme } from \"../../core/theming\";\nimport Icon from \"../Icon\";\nimport TouchableRipple from \"../TouchableRipple/TouchableRipple\";\nimport Text from \"../Typography/Text\";\nvar DrawerItem = function DrawerItem(_ref) {\n  var icon = _ref.icon,\n    label = _ref.label,\n    active = _ref.active,\n    disabled = _ref.disabled,\n    themeOverrides = _ref.theme,\n    customRippleColor = _ref.rippleColor,\n    style = _ref.style,\n    onPress = _ref.onPress,\n    background = _ref.background,\n    accessibilityLabel = _ref.accessibilityLabel,\n    right = _ref.right,\n    labelMaxFontSizeMultiplier = _ref.labelMaxFontSizeMultiplier,\n    hitSlop = _ref.hitSlop,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var theme = useInternalTheme(themeOverrides);\n  var roundness = theme.roundness,\n    isV3 = theme.isV3;\n  var backgroundColor = active ? isV3 ? theme.colors.secondaryContainer : color(theme.colors.primary).alpha(0.12).rgb().string() : undefined;\n  var contentColor = active ? isV3 ? theme.colors.onSecondaryContainer : theme.colors.primary : isV3 ? theme.colors.onSurfaceVariant : color(theme.colors.text).alpha(0.68).rgb().string();\n  var labelMargin = icon ? isV3 ? 12 : 32 : 0;\n  var borderRadius = (isV3 ? 7 : 1) * roundness;\n  var rippleColor = isV3 ? color(contentColor).alpha(0.12).rgb().string() : undefined;\n  var font = isV3 ? theme.fonts.labelLarge : theme.fonts.medium;\n  return React.createElement(View, rest, React.createElement(TouchableRipple, {\n    borderless: true,\n    disabled: disabled,\n    background: background,\n    onPress: onPress,\n    style: [styles.container, {\n      backgroundColor: backgroundColor,\n      borderRadius: borderRadius\n    }, isV3 && styles.v3Container, style],\n    accessibilityRole: \"button\",\n    accessibilityState: {\n      selected: active\n    },\n    accessibilityLabel: accessibilityLabel,\n    rippleColor: customRippleColor || rippleColor,\n    theme: theme,\n    hitSlop: hitSlop\n  }, React.createElement(View, {\n    style: [styles.wrapper, isV3 && styles.v3Wrapper]\n  }, React.createElement(View, {\n    style: styles.content\n  }, icon ? React.createElement(Icon, {\n    source: icon,\n    size: 24,\n    color: contentColor\n  }) : null, React.createElement(Text, {\n    variant: \"labelLarge\",\n    selectable: false,\n    numberOfLines: 1,\n    style: [styles.label, _objectSpread({\n      color: contentColor,\n      marginLeft: labelMargin\n    }, font)],\n    maxFontSizeMultiplier: labelMaxFontSizeMultiplier\n  }, label)), right === null || right === void 0 ? void 0 : right({\n    color: contentColor\n  }))));\n};\nDrawerItem.displayName = 'Drawer.Item';\nvar styles = StyleSheet.create({\n  container: {\n    marginHorizontal: 10,\n    marginVertical: 4\n  },\n  v3Container: {\n    justifyContent: 'center',\n    height: 56,\n    marginLeft: 12,\n    marginRight: 12,\n    marginVertical: 0\n  },\n  wrapper: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    padding: 8\n  },\n  v3Wrapper: {\n    marginLeft: 16,\n    marginRight: 24,\n    padding: 0\n  },\n  content: {\n    flex: 1,\n    flexDirection: 'row',\n    alignItems: 'center'\n  },\n  label: {\n    marginRight: 32\n  }\n});\nexport default DrawerItem;", "map": {"version": 3, "names": ["React", "StyleSheet", "View", "color", "useInternalTheme", "Icon", "TouchableRipple", "Text", "DrawerItem", "_ref", "icon", "label", "active", "disabled", "themeOverrides", "theme", "customRippleColor", "rippleColor", "style", "onPress", "background", "accessibilityLabel", "right", "labelMaxFontSizeMultiplier", "hitSlop", "rest", "_objectWithoutProperties", "_excluded", "roundness", "isV3", "backgroundColor", "colors", "secondaryContainer", "primary", "alpha", "rgb", "string", "undefined", "contentColor", "onSecondaryContainer", "onSurfaceVariant", "text", "labelMargin", "borderRadius", "font", "fonts", "labelLarge", "medium", "createElement", "borderless", "styles", "container", "v3Container", "accessibilityRole", "accessibilityState", "selected", "wrapper", "v3Wrapper", "content", "source", "size", "variant", "selectable", "numberOfLines", "_objectSpread", "marginLeft", "maxFontSizeMultiplier", "displayName", "create", "marginHorizontal", "marginVertical", "justifyContent", "height", "marginRight", "flexDirection", "alignItems", "padding", "flex"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\Drawer\\DrawerItem.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  ColorValue,\n  GestureResponderEvent,\n  PressableAndroidRippleConfig,\n  StyleProp,\n  StyleSheet,\n  View,\n  ViewStyle,\n} from 'react-native';\n\nimport color from 'color';\n\nimport { useInternalTheme } from '../../core/theming';\nimport type { ThemeProp } from '../../types';\nimport Icon, { IconSource } from '../Icon';\nimport TouchableRipple, {\n  Props as TouchableRippleProps,\n} from '../TouchableRipple/TouchableRipple';\nimport Text from '../Typography/Text';\n\nexport type Props = React.ComponentPropsWithRef<typeof View> & {\n  /**\n   * The label text of the item.\n   */\n  label: string;\n  /**\n   * Icon to display for the `DrawerItem`.\n   */\n  icon?: IconSource;\n  /**\n   * Whether to highlight the drawer item as active.\n   */\n  active?: boolean;\n  /**\n   * Whether the item is disabled.\n   */\n  disabled?: boolean;\n  /**\n   * Function to execute on press.\n   */\n  onPress?: (e: GestureResponderEvent) => void;\n  /**\n   * Type of background drawabale to display the feedback (Android).\n   * https://reactnative.dev/docs/pressable#rippleconfig\n   */\n  background?: PressableAndroidRippleConfig;\n  /**\n   * Accessibility label for the button. This is read by the screen reader when the user taps the button.\n   */\n  accessibilityLabel?: string;\n  /**\n   * Callback which returns a React element to display on the right side. For instance a Badge.\n   */\n  right?: (props: { color: string }) => React.ReactNode;\n  /**\n   * Specifies the largest possible scale a label font can reach.\n   */\n  labelMaxFontSizeMultiplier?: number;\n  /**\n   * Color of the ripple effect.\n   */\n  rippleColor?: ColorValue;\n  /**\n   * Sets additional distance outside of element in which a press can be detected.\n   */\n  hitSlop?: TouchableRippleProps['hitSlop'];\n  style?: StyleProp<ViewStyle>;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n};\n\n/**\n * A component used to show an action item with an icon and a label in a navigation drawer.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { Drawer } from 'react-native-paper';\n *\n * const MyComponent = () => (\n *    <Drawer.Item\n *      style={{ backgroundColor: '#64ffda' }}\n *      icon=\"star\"\n *      label=\"First Item\"\n *    />\n * );\n *\n * export default MyComponent;\n * ```\n */\nconst DrawerItem = ({\n  icon,\n  label,\n  active,\n  disabled,\n  theme: themeOverrides,\n  rippleColor: customRippleColor,\n  style,\n  onPress,\n  background,\n  accessibilityLabel,\n  right,\n  labelMaxFontSizeMultiplier,\n  hitSlop,\n  ...rest\n}: Props) => {\n  const theme = useInternalTheme(themeOverrides);\n  const { roundness, isV3 } = theme;\n\n  const backgroundColor = active\n    ? isV3\n      ? theme.colors.secondaryContainer\n      : color(theme.colors.primary).alpha(0.12).rgb().string()\n    : undefined;\n  const contentColor = active\n    ? isV3\n      ? theme.colors.onSecondaryContainer\n      : theme.colors.primary\n    : isV3\n    ? theme.colors.onSurfaceVariant\n    : color(theme.colors.text).alpha(0.68).rgb().string();\n\n  const labelMargin = icon ? (isV3 ? 12 : 32) : 0;\n  const borderRadius = (isV3 ? 7 : 1) * roundness;\n  const rippleColor = isV3\n    ? color(contentColor).alpha(0.12).rgb().string()\n    : undefined;\n  const font = isV3 ? theme.fonts.labelLarge : theme.fonts.medium;\n\n  return (\n    <View {...rest}>\n      <TouchableRipple\n        borderless\n        disabled={disabled}\n        background={background}\n        onPress={onPress}\n        style={[\n          styles.container,\n          { backgroundColor, borderRadius },\n          isV3 && styles.v3Container,\n          style,\n        ]}\n        accessibilityRole=\"button\"\n        accessibilityState={{ selected: active }}\n        accessibilityLabel={accessibilityLabel}\n        rippleColor={customRippleColor || rippleColor}\n        theme={theme}\n        hitSlop={hitSlop}\n      >\n        <View style={[styles.wrapper, isV3 && styles.v3Wrapper]}>\n          <View style={styles.content}>\n            {icon ? (\n              <Icon source={icon} size={24} color={contentColor} />\n            ) : null}\n            <Text\n              variant=\"labelLarge\"\n              selectable={false}\n              numberOfLines={1}\n              style={[\n                styles.label,\n                {\n                  color: contentColor,\n                  marginLeft: labelMargin,\n                  ...font,\n                },\n              ]}\n              maxFontSizeMultiplier={labelMaxFontSizeMultiplier}\n            >\n              {label}\n            </Text>\n          </View>\n\n          {right?.({ color: contentColor })}\n        </View>\n      </TouchableRipple>\n    </View>\n  );\n};\n\nDrawerItem.displayName = 'Drawer.Item';\n\nconst styles = StyleSheet.create({\n  container: {\n    marginHorizontal: 10,\n    marginVertical: 4,\n  },\n  v3Container: {\n    justifyContent: 'center',\n    height: 56,\n    marginLeft: 12,\n    marginRight: 12,\n    marginVertical: 0,\n  },\n  wrapper: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    padding: 8,\n  },\n  v3Wrapper: {\n    marginLeft: 16,\n    marginRight: 24,\n    padding: 0,\n  },\n  content: {\n    flex: 1,\n    flexDirection: 'row',\n    alignItems: 'center',\n  },\n  label: {\n    marginRight: 32,\n  },\n});\n\nexport default DrawerItem;\n"], "mappings": ";;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAW9B,OAAOC,KAAK,MAAM,OAAO;AAEzB,SAASC,gBAAgB;AAEzB,OAAOC,IAAI;AACX,OAAOC,eAAe;AAGtB,OAAOC,IAAI;AA0EX,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAAC,IAAA,EAeH;EAAA,IAdXC,IAAI,GAAAD,IAAA,CAAJC,IAAI;IACJC,KAAK,GAAAF,IAAA,CAALE,KAAK;IACLC,MAAM,GAAAH,IAAA,CAANG,MAAM;IACNC,QAAQ,GAAAJ,IAAA,CAARI,QAAQ;IACDC,cAAc,GAAAL,IAAA,CAArBM,KAAK;IACQC,iBAAiB,GAAAP,IAAA,CAA9BQ,WAAW;IACXC,KAAK,GAAAT,IAAA,CAALS,KAAK;IACLC,OAAO,GAAAV,IAAA,CAAPU,OAAO;IACPC,UAAU,GAAAX,IAAA,CAAVW,UAAU;IACVC,kBAAkB,GAAAZ,IAAA,CAAlBY,kBAAkB;IAClBC,KAAK,GAAAb,IAAA,CAALa,KAAK;IACLC,0BAA0B,GAAAd,IAAA,CAA1Bc,0BAA0B;IAC1BC,OAAO,GAAAf,IAAA,CAAPe,OAAO;IACJC,IAAA,GAAAC,wBAAA,CAAAjB,IAAA,EAAAkB,SAAA;EAEH,IAAMZ,KAAK,GAAGX,gBAAgB,CAACU,cAAc,CAAC;EAC9C,IAAQc,SAAS,GAAWb,KAAK,CAAzBa,SAAS;IAAEC,IAAA,GAASd,KAAK,CAAdc,IAAA;EAEnB,IAAMC,eAAe,GAAGlB,MAAM,GAC1BiB,IAAI,GACFd,KAAK,CAACgB,MAAM,CAACC,kBAAkB,GAC/B7B,KAAK,CAACY,KAAK,CAACgB,MAAM,CAACE,OAAO,CAAC,CAACC,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC,GACxDC,SAAS;EACb,IAAMC,YAAY,GAAG1B,MAAM,GACvBiB,IAAI,GACFd,KAAK,CAACgB,MAAM,CAACQ,oBAAoB,GACjCxB,KAAK,CAACgB,MAAM,CAACE,OAAO,GACtBJ,IAAI,GACJd,KAAK,CAACgB,MAAM,CAACS,gBAAgB,GAC7BrC,KAAK,CAACY,KAAK,CAACgB,MAAM,CAACU,IAAI,CAAC,CAACP,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EAEvD,IAAMM,WAAW,GAAGhC,IAAI,GAAImB,IAAI,GAAG,EAAE,GAAG,EAAE,GAAI,CAAC;EAC/C,IAAMc,YAAY,GAAG,CAACd,IAAI,GAAG,CAAC,GAAG,CAAC,IAAID,SAAS;EAC/C,IAAMX,WAAW,GAAGY,IAAI,GACpB1B,KAAK,CAACmC,YAAY,CAAC,CAACJ,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC,GAC9CC,SAAS;EACb,IAAMO,IAAI,GAAGf,IAAI,GAAGd,KAAK,CAAC8B,KAAK,CAACC,UAAU,GAAG/B,KAAK,CAAC8B,KAAK,CAACE,MAAM;EAE/D,OACE/C,KAAA,CAAAgD,aAAA,CAAC9C,IAAI,EAAKuB,IAAI,EACZzB,KAAA,CAAAgD,aAAA,CAAC1C,eAAe;IACd2C,UAAU;IACVpC,QAAQ,EAAEA,QAAS;IACnBO,UAAU,EAAEA,UAAW;IACvBD,OAAO,EAAEA,OAAQ;IACjBD,KAAK,EAAE,CACLgC,MAAM,CAACC,SAAS,EAChB;MAAErB,eAAe,EAAfA,eAAe;MAAEa,YAAA,EAAAA;IAAa,CAAC,EACjCd,IAAI,IAAIqB,MAAM,CAACE,WAAW,EAC1BlC,KAAK,CACL;IACFmC,iBAAiB,EAAC,QAAQ;IAC1BC,kBAAkB,EAAE;MAAEC,QAAQ,EAAE3C;IAAO,CAAE;IACzCS,kBAAkB,EAAEA,kBAAmB;IACvCJ,WAAW,EAAED,iBAAiB,IAAIC,WAAY;IAC9CF,KAAK,EAAEA,KAAM;IACbS,OAAO,EAAEA;EAAQ,GAEjBxB,KAAA,CAAAgD,aAAA,CAAC9C,IAAI;IAACgB,KAAK,EAAE,CAACgC,MAAM,CAACM,OAAO,EAAE3B,IAAI,IAAIqB,MAAM,CAACO,SAAS;EAAE,GACtDzD,KAAA,CAAAgD,aAAA,CAAC9C,IAAI;IAACgB,KAAK,EAAEgC,MAAM,CAACQ;EAAQ,GACzBhD,IAAI,GACHV,KAAA,CAAAgD,aAAA,CAAC3C,IAAI;IAACsD,MAAM,EAAEjD,IAAK;IAACkD,IAAI,EAAE,EAAG;IAACzD,KAAK,EAAEmC;EAAa,CAAE,CAAC,GACnD,IAAI,EACRtC,KAAA,CAAAgD,aAAA,CAACzC,IAAI;IACHsD,OAAO,EAAC,YAAY;IACpBC,UAAU,EAAE,KAAM;IAClBC,aAAa,EAAE,CAAE;IACjB7C,KAAK,EAAE,CACLgC,MAAM,CAACvC,KAAK,EAAAqD,aAAA;MAEV7D,KAAK,EAAEmC,YAAY;MACnB2B,UAAU,EAAEvB;IAAW,GACpBE,IAAA,EAEL;IACFsB,qBAAqB,EAAE3C;EAA2B,GAEjDZ,KACG,CACF,CAAC,EAENW,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAG;IAAEnB,KAAK,EAAEmC;EAAa,CAAC,CAC5B,CACS,CACb,CAAC;AAEX,CAAC;AAED9B,UAAU,CAAC2D,WAAW,GAAG,aAAa;AAEtC,IAAMjB,MAAM,GAAGjD,UAAU,CAACmE,MAAM,CAAC;EAC/BjB,SAAS,EAAE;IACTkB,gBAAgB,EAAE,EAAE;IACpBC,cAAc,EAAE;EAClB,CAAC;EACDlB,WAAW,EAAE;IACXmB,cAAc,EAAE,QAAQ;IACxBC,MAAM,EAAE,EAAE;IACVP,UAAU,EAAE,EAAE;IACdQ,WAAW,EAAE,EAAE;IACfH,cAAc,EAAE;EAClB,CAAC;EACDd,OAAO,EAAE;IACPkB,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,QAAQ;IACpBC,OAAO,EAAE;EACX,CAAC;EACDnB,SAAS,EAAE;IACTQ,UAAU,EAAE,EAAE;IACdQ,WAAW,EAAE,EAAE;IACfG,OAAO,EAAE;EACX,CAAC;EACDlB,OAAO,EAAE;IACPmB,IAAI,EAAE,CAAC;IACPH,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE;EACd,CAAC;EACDhE,KAAK,EAAE;IACL8D,WAAW,EAAE;EACf;AACF,CAAC,CAAC;AAEF,eAAejE,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}