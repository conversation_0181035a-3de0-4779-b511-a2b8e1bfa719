{"ast": null, "code": "function _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nimport * as React from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport StyledText from \"./StyledText\";\nvar Headline = function Headline(props) {\n  return React.createElement(StyledText, _extends({}, props, {\n    alpha: 0.87,\n    family: \"regular\",\n    style: [styles.text, props.style]\n  }));\n};\nexport default Headline;\nvar styles = StyleSheet.create({\n  text: {\n    fontSize: 24,\n    lineHeight: 32,\n    marginVertical: 2,\n    letterSpacing: 0\n  }\n});", "map": {"version": 3, "names": ["React", "StyleSheet", "StyledText", "Headline", "props", "createElement", "_extends", "alpha", "family", "style", "styles", "text", "create", "fontSize", "lineHeight", "marginVertical", "letterSpacing"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\Typography\\v2\\Headline.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Text, TextStyle, StyleSheet, StyleProp } from 'react-native';\n\nimport StyledText from './StyledText';\n\nexport type Props = React.ComponentProps<typeof Text> & {\n  style?: StyleProp<TextStyle>;\n  children: React.ReactNode;\n};\n\n// @component-group Typography\n\n/**\n * @deprecated Deprecated in v5.x - use `<Text variant=\"headlineSmall\" />` instead.\n * Typography component for showing a headline.\n *\n * <div class=\"screenshots\">\n *   <img src=\"screenshots/headline.png\" />\n * </div>\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { Headline } from 'react-native-paper';\n *\n * const MyComponent = () => (\n *   <Headline>Headline</Headline>\n * );\n *\n * export default MyComponent;\n * ```\n */\nconst Headline = (props: Props) => {\n  return (\n    <StyledText\n      {...props}\n      alpha={0.87}\n      family=\"regular\"\n      style={[styles.text, props.style]}\n    />\n  );\n};\n\nexport default Headline;\n\nconst styles = StyleSheet.create({\n  text: {\n    fontSize: 24,\n    lineHeight: 32,\n    marginVertical: 2,\n    letterSpacing: 0,\n  },\n});\n"], "mappings": ";;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,UAAA;AAG9B,OAAOC,UAAU;AA6BjB,IAAMC,QAAQ,GAAI,SAAZA,QAAQA,CAAIC,KAAY,EAAK;EACjC,OACEJ,KAAA,CAAAK,aAAA,CAACH,UAAU,EAAAI,QAAA,KACLF,KAAK;IACTG,KAAK,EAAE,IAAK;IACZC,MAAM,EAAC,SAAS;IAChBC,KAAK,EAAE,CAACC,MAAM,CAACC,IAAI,EAAEP,KAAK,CAACK,KAAK;EAAE,EACnC,CAAC;AAEN,CAAC;AAED,eAAeN,QAAQ;AAEvB,IAAMO,MAAM,GAAGT,UAAU,CAACW,MAAM,CAAC;EAC/BD,IAAI,EAAE;IACJE,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,EAAE;IACdC,cAAc,EAAE,CAAC;IACjBC,aAAa,EAAE;EACjB;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}