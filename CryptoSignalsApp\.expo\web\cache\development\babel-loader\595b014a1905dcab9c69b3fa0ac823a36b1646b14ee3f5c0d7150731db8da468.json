{"ast": null, "code": "import * as React from 'react';\nimport Keyboard from \"react-native-web/dist/exports/Keyboard\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nexport default function useIsKeyboardShown(_ref) {\n  var onShow = _ref.onShow,\n    onHide = _ref.onHide;\n  React.useEffect(function () {\n    var willShowSubscription;\n    var willHideSubscription;\n    var didShowSubscription;\n    var didHideSubscription;\n    if (Platform.OS === 'ios') {\n      willShowSubscription = Keyboard.addListener('keyboardWillShow', onShow);\n      willHideSubscription = Keyboard.addListener('keyboardWillHide', onHide);\n    } else {\n      didShowSubscription = Keyboard.addListener('keyboardDidShow', onShow);\n      didHideSubscription = Keyboard.addListener('keyboardDidHide', onHide);\n    }\n    return function () {\n      if (Platform.OS === 'ios') {\n        var _willShowSubscription, _willHideSubscription;\n        if ((_willShowSubscription = willShowSubscription) !== null && _willShowSubscription !== void 0 && _willShowSubscription.remove) {\n          willShowSubscription.remove();\n        } else {\n          Keyboard.removeListener('keyboardWillShow', onShow);\n        }\n        if ((_willHideSubscription = willHideSubscription) !== null && _willHideSubscription !== void 0 && _willHideSubscription.remove) {\n          willHideSubscription.remove();\n        } else {\n          Keyboard.removeListener('keyboardWillHide', onHide);\n        }\n      } else {\n        var _didShowSubscription, _didHideSubscription;\n        if ((_didShowSubscription = didShowSubscription) !== null && _didShowSubscription !== void 0 && _didShowSubscription.remove) {\n          didShowSubscription.remove();\n        } else {\n          Keyboard.removeListener('keyboardDidShow', onShow);\n        }\n        if ((_didHideSubscription = didHideSubscription) !== null && _didHideSubscription !== void 0 && _didHideSubscription.remove) {\n          didHideSubscription.remove();\n        } else {\n          Keyboard.removeListener('keyboardDidHide', onHide);\n        }\n      }\n    };\n  }, [onHide, onShow]);\n}", "map": {"version": 3, "names": ["React", "Keyboard", "Platform", "useIsKeyboardShown", "_ref", "onShow", "onHide", "useEffect", "willShowSubscription", "willHideSubscription", "didShowSubscription", "didHideSubscription", "OS", "addListener", "_willShowSubscription", "_willHideSubscription", "remove", "removeListener", "_didShowSubscription", "_didHideSubscription"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\utils\\useIsKeyboardShown.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Keyboard, NativeEventSubscription, Platform } from 'react-native';\n\ntype Props = {\n  onShow: () => void;\n  onHide: () => void;\n};\nexport default function useIsKeyboardShown({ onShow, onHide }: Props) {\n  React.useEffect(() => {\n    let willShowSubscription: NativeEventSubscription | undefined;\n    let willHideSubscription: NativeEventSubscription | undefined;\n    let didShowSubscription: NativeEventSubscription | undefined;\n    let didHideSubscription: NativeEventSubscription | undefined;\n\n    if (Platform.OS === 'ios') {\n      willShowSubscription = Keyboard.addListener('keyboardWillShow', onShow);\n      willHideSubscription = Keyboard.addListener('keyboardWillHide', onHide);\n    } else {\n      didShowSubscription = Keyboard.addListener('keyboardDidShow', onShow);\n      didHideSubscription = Keyboard.addListener('keyboardDidHide', onHide);\n    }\n\n    return () => {\n      if (Platform.OS === 'ios') {\n        if (willShowSubscription?.remove) {\n          willShowSubscription.remove();\n        } else {\n          // @ts-expect-error: We keep deprecated listener remove method for backwards compat with old RN versions\n          Keyboard.removeListener('keyboardWillShow', onShow);\n        }\n\n        if (willHideSubscription?.remove) {\n          willHideSubscription.remove();\n        } else {\n          // @ts-expect-error: We keep deprecated listener remove method for backwards compat with old RN versions\n          Keyboard.removeListener('keyboardWillHide', onHide);\n        }\n      } else {\n        if (didShowSubscription?.remove) {\n          didShowSubscription.remove();\n        } else {\n          // @ts-expect-error: We keep deprecated listener remove method for backwards compat with old RN versions\n          Keyboard.removeListener('keyboardDidShow', onShow);\n        }\n\n        if (didHideSubscription?.remove) {\n          didHideSubscription.remove();\n        } else {\n          // @ts-expect-error: We keep deprecated listener remove method for backwards compat with old RN versions\n          Keyboard.removeListener('keyboardDidHide', onHide);\n        }\n      }\n    };\n  }, [onHide, onShow]);\n}\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAAA,OAAAC,QAAA;AAO9B,eAAe,SAASC,kBAAkBA,CAAAC,IAAA,EAA4B;EAAA,IAAzBC,MAAM,GAAAD,IAAA,CAANC,MAAM;IAAEC,MAAA,GAAAF,IAAA,CAAAE,MAAA;EACnDN,KAAK,CAACO,SAAS,CAAC,YAAM;IACpB,IAAIC,oBAAyD;IAC7D,IAAIC,oBAAyD;IAC7D,IAAIC,mBAAwD;IAC5D,IAAIC,mBAAwD;IAE5D,IAAIT,QAAQ,CAACU,EAAE,KAAK,KAAK,EAAE;MACzBJ,oBAAoB,GAAGP,QAAQ,CAACY,WAAW,CAAC,kBAAkB,EAAER,MAAM,CAAC;MACvEI,oBAAoB,GAAGR,QAAQ,CAACY,WAAW,CAAC,kBAAkB,EAAEP,MAAM,CAAC;IACzE,CAAC,MAAM;MACLI,mBAAmB,GAAGT,QAAQ,CAACY,WAAW,CAAC,iBAAiB,EAAER,MAAM,CAAC;MACrEM,mBAAmB,GAAGV,QAAQ,CAACY,WAAW,CAAC,iBAAiB,EAAEP,MAAM,CAAC;IACvE;IAEA,OAAO,YAAM;MACX,IAAIJ,QAAQ,CAACU,EAAE,KAAK,KAAK,EAAE;QAAA,IAAAE,qBAAA,EAAAC,qBAAA;QACzB,KAAAD,qBAAA,GAAIN,oBAAoB,cAAAM,qBAAA,eAApBA,qBAAA,CAAsBE,MAAM,EAAE;UAChCR,oBAAoB,CAACQ,MAAM,CAAC,CAAC;QAC/B,CAAC,MAAM;UAELf,QAAQ,CAACgB,cAAc,CAAC,kBAAkB,EAAEZ,MAAM,CAAC;QACrD;QAEA,KAAAU,qBAAA,GAAIN,oBAAoB,cAAAM,qBAAA,eAApBA,qBAAA,CAAsBC,MAAM,EAAE;UAChCP,oBAAoB,CAACO,MAAM,CAAC,CAAC;QAC/B,CAAC,MAAM;UAELf,QAAQ,CAACgB,cAAc,CAAC,kBAAkB,EAAEX,MAAM,CAAC;QACrD;MACF,CAAC,MAAM;QAAA,IAAAY,oBAAA,EAAAC,oBAAA;QACL,KAAAD,oBAAA,GAAIR,mBAAmB,cAAAQ,oBAAA,eAAnBA,oBAAA,CAAqBF,MAAM,EAAE;UAC/BN,mBAAmB,CAACM,MAAM,CAAC,CAAC;QAC9B,CAAC,MAAM;UAELf,QAAQ,CAACgB,cAAc,CAAC,iBAAiB,EAAEZ,MAAM,CAAC;QACpD;QAEA,KAAAc,oBAAA,GAAIR,mBAAmB,cAAAQ,oBAAA,eAAnBA,oBAAA,CAAqBH,MAAM,EAAE;UAC/BL,mBAAmB,CAACK,MAAM,CAAC,CAAC;QAC9B,CAAC,MAAM;UAELf,QAAQ,CAACgB,cAAc,CAAC,iBAAiB,EAAEX,MAAM,CAAC;QACpD;MACF;IACF,CAAC;EACH,CAAC,EAAE,CAACA,MAAM,EAAED,MAAM,CAAC,CAAC;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}