{"ast": null, "code": "var DarkTheme = {\n  dark: true,\n  colors: {\n    primary: 'rgb(10, 132, 255)',\n    background: 'rgb(1, 1, 1)',\n    card: 'rgb(18, 18, 18)',\n    text: 'rgb(229, 229, 231)',\n    border: 'rgb(39, 39, 41)',\n    notification: 'rgb(255, 69, 58)'\n  }\n};\nexport default DarkTheme;", "map": {"version": 3, "names": ["DarkTheme", "dark", "colors", "primary", "background", "card", "text", "border", "notification"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\@react-navigation\\native\\src\\theming\\DarkTheme.tsx"], "sourcesContent": ["import type { Theme } from '../types';\n\nconst DarkTheme: Theme = {\n  dark: true,\n  colors: {\n    primary: 'rgb(10, 132, 255)',\n    background: 'rgb(1, 1, 1)',\n    card: 'rgb(18, 18, 18)',\n    text: 'rgb(229, 229, 231)',\n    border: 'rgb(39, 39, 41)',\n    notification: 'rgb(255, 69, 58)',\n  },\n};\n\nexport default DarkTheme;\n"], "mappings": "AAEA,IAAMA,SAAgB,GAAG;EACvBC,IAAI,EAAE,IAAI;EACVC,MAAM,EAAE;IACNC,OAAO,EAAE,mBAAmB;IAC5BC,UAAU,EAAE,cAAc;IAC1BC,IAAI,EAAE,iBAAiB;IACvBC,IAAI,EAAE,oBAAoB;IAC1BC,MAAM,EAAE,iBAAiB;IACzBC,YAAY,EAAE;EAChB;AACF,CAAC;AAED,eAAeR,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}