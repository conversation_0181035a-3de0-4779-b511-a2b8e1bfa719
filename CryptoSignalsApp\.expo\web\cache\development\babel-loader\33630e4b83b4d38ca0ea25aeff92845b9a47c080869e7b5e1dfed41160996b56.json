{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"id\", \"initialRouteName\", \"children\", \"screenListeners\", \"screenOptions\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport { createNavigatorFactory, StackActions, StackRouter, useNavigationBuilder } from '@react-navigation/native';\nimport * as React from 'react';\nimport warnOnce from 'warn-once';\nimport StackView from \"../views/Stack/StackView\";\nfunction StackNavigator(_ref) {\n  var id = _ref.id,\n    initialRouteName = _ref.initialRouteName,\n    children = _ref.children,\n    screenListeners = _ref.screenListeners,\n    screenOptions = _ref.screenOptions,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var mode = rest.mode;\n  warnOnce(mode != null, `Stack Navigator: 'mode=\"${mode}\"' is deprecated. Use 'presentation: \"${mode}\"' in 'screenOptions' instead.\\n\\nSee https://reactnavigation.org/docs/stack-navigator#presentation for more details.`);\n  var headerMode = rest.headerMode;\n  warnOnce(headerMode === 'none', `Stack Navigator: 'headerMode=\"none\"' is deprecated. Use 'headerShown: false' in 'screenOptions' instead.\\n\\nSee https://reactnavigation.org/docs/stack-navigator/#headershown for more details.`);\n  warnOnce(headerMode != null && headerMode !== 'none', `Stack Navigator: 'headerMode' is moved to 'options'. Moved it to 'screenOptions' to keep current behavior.\\n\\nSee https://reactnavigation.org/docs/stack-navigator/#headermode for more details.`);\n  var keyboardHandlingEnabled = rest.keyboardHandlingEnabled;\n  warnOnce(keyboardHandlingEnabled !== undefined, `Stack Navigator: 'keyboardHandlingEnabled' is moved to 'options'. Moved it to 'screenOptions' to keep current behavior.\\n\\nSee https://reactnavigation.org/docs/stack-navigator/#keyboardhandlingenabled for more details.`);\n  var defaultScreenOptions = {\n    presentation: mode,\n    headerShown: headerMode ? headerMode !== 'none' : true,\n    headerMode: headerMode && headerMode !== 'none' ? headerMode : undefined,\n    keyboardHandlingEnabled: keyboardHandlingEnabled\n  };\n  var _useNavigationBuilder = useNavigationBuilder(StackRouter, {\n      id: id,\n      initialRouteName: initialRouteName,\n      children: children,\n      screenListeners: screenListeners,\n      screenOptions: screenOptions,\n      defaultScreenOptions: defaultScreenOptions\n    }),\n    state = _useNavigationBuilder.state,\n    descriptors = _useNavigationBuilder.descriptors,\n    navigation = _useNavigationBuilder.navigation,\n    NavigationContent = _useNavigationBuilder.NavigationContent;\n  React.useEffect(function () {\n    var _navigation$addListen;\n    return ((_navigation$addListen = navigation.addListener) === null || _navigation$addListen === void 0 ? void 0 : _navigation$addListen.call(navigation, 'tabPress', function (e) {\n        var isFocused = navigation.isFocused();\n        requestAnimationFrame(function () {\n          if (state.index > 0 && isFocused && !e.defaultPrevented) {\n            navigation.dispatch(_objectSpread(_objectSpread({}, StackActions.popToTop()), {}, {\n              target: state.key\n            }));\n          }\n        });\n      })\n    );\n  }, [navigation, state.index, state.key]);\n  return React.createElement(NavigationContent, null, React.createElement(StackView, _extends({}, rest, {\n    state: state,\n    descriptors: descriptors,\n    navigation: navigation\n  })));\n}\nexport default createNavigatorFactory(StackNavigator);", "map": {"version": 3, "names": ["createNavigatorFactory", "StackActions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useNavigationBuilder", "React", "warnOnce", "StackView", "StackNavigator", "_ref", "id", "initialRouteName", "children", "screenListeners", "screenOptions", "rest", "_objectWithoutProperties", "_excluded", "mode", "headerMode", "keyboardHandlingEnabled", "undefined", "defaultScreenOptions", "presentation", "headerShown", "_useNavigationBuilder", "state", "descriptors", "navigation", "NavigationContent", "useEffect", "_navigation$addListen", "addListener", "call", "e", "isFocused", "requestAnimationFrame", "index", "defaultPrevented", "dispatch", "_objectSpread", "popToTop", "target", "key", "createElement", "_extends"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\@react-navigation\\stack\\src\\navigators\\createStackNavigator.tsx"], "sourcesContent": ["import {\n  createNavigatorFactory,\n  DefaultNavigatorOptions,\n  EventArg,\n  ParamListBase,\n  StackActionHelpers,\n  StackActions,\n  StackNavigationState,\n  StackRouter,\n  StackRouterOptions,\n  useNavigationBuilder,\n} from '@react-navigation/native';\nimport * as React from 'react';\nimport warnOnce from 'warn-once';\n\nimport type {\n  StackHeaderMode,\n  StackNavigationConfig,\n  StackNavigationEventMap,\n  StackNavigationOptions,\n} from '../types';\nimport StackView from '../views/Stack/StackView';\n\ntype Props = DefaultNavigatorOptions<\n  ParamListBase,\n  StackNavigationState<ParamListBase>,\n  StackNavigationOptions,\n  StackNavigationEventMap\n> &\n  StackRouterOptions &\n  StackNavigationConfig;\n\nfunction StackNavigator({\n  id,\n  initialRouteName,\n  children,\n  screenListeners,\n  screenOptions,\n  ...rest\n}: Props) {\n  // @ts-expect-error: mode is deprecated\n  const mode = rest.mode as 'card' | 'modal' | undefined;\n\n  warnOnce(\n    mode != null,\n    `Stack Navigator: 'mode=\"${mode}\"' is deprecated. Use 'presentation: \"${mode}\"' in 'screenOptions' instead.\\n\\nSee https://reactnavigation.org/docs/stack-navigator#presentation for more details.`\n  );\n\n  // @ts-expect-error: headerMode='none' is deprecated\n  const headerMode = rest.headerMode as StackHeaderMode | 'none' | undefined;\n\n  warnOnce(\n    headerMode === 'none',\n    `Stack Navigator: 'headerMode=\"none\"' is deprecated. Use 'headerShown: false' in 'screenOptions' instead.\\n\\nSee https://reactnavigation.org/docs/stack-navigator/#headershown for more details.`\n  );\n\n  warnOnce(\n    headerMode != null && headerMode !== 'none',\n    `Stack Navigator: 'headerMode' is moved to 'options'. Moved it to 'screenOptions' to keep current behavior.\\n\\nSee https://reactnavigation.org/docs/stack-navigator/#headermode for more details.`\n  );\n\n  // @ts-expect-error: headerMode='none' is deprecated\n  const keyboardHandlingEnabled = rest.keyboardHandlingEnabled;\n\n  warnOnce(\n    keyboardHandlingEnabled !== undefined,\n    `Stack Navigator: 'keyboardHandlingEnabled' is moved to 'options'. Moved it to 'screenOptions' to keep current behavior.\\n\\nSee https://reactnavigation.org/docs/stack-navigator/#keyboardhandlingenabled for more details.`\n  );\n\n  const defaultScreenOptions: StackNavigationOptions = {\n    presentation: mode,\n    headerShown: headerMode ? headerMode !== 'none' : true,\n    headerMode: headerMode && headerMode !== 'none' ? headerMode : undefined,\n    keyboardHandlingEnabled,\n  };\n\n  const { state, descriptors, navigation, NavigationContent } =\n    useNavigationBuilder<\n      StackNavigationState<ParamListBase>,\n      StackRouterOptions,\n      StackActionHelpers<ParamListBase>,\n      StackNavigationOptions,\n      StackNavigationEventMap\n    >(StackRouter, {\n      id,\n      initialRouteName,\n      children,\n      screenListeners,\n      screenOptions,\n      defaultScreenOptions,\n    });\n\n  React.useEffect(\n    () =>\n      // @ts-expect-error: there may not be a tab navigator in parent\n      navigation.addListener?.('tabPress', (e) => {\n        const isFocused = navigation.isFocused();\n\n        // Run the operation in the next frame so we're sure all listeners have been run\n        // This is necessary to know if preventDefault() has been called\n        requestAnimationFrame(() => {\n          if (\n            state.index > 0 &&\n            isFocused &&\n            !(e as unknown as EventArg<'tabPress', true>).defaultPrevented\n          ) {\n            // When user taps on already focused tab and we're inside the tab,\n            // reset the stack to replicate native behaviour\n            navigation.dispatch({\n              ...StackActions.popToTop(),\n              target: state.key,\n            });\n          }\n        });\n      }),\n    [navigation, state.index, state.key]\n  );\n\n  return (\n    <NavigationContent>\n      <StackView\n        {...rest}\n        state={state}\n        descriptors={descriptors}\n        navigation={navigation}\n      />\n    </NavigationContent>\n  );\n}\n\nexport default createNavigatorFactory<\n  StackNavigationState<ParamListBase>,\n  StackNavigationOptions,\n  StackNavigationEventMap,\n  typeof StackNavigator\n>(StackNavigator);\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA,SACEA,sBAAsB,EAKtBC,YAAY,EAEZC,WAAW,EAEXC,oBAAoB,QACf,0BAA0B;AACjC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,QAAQ,MAAM,WAAW;AAQhC,OAAOC,SAAS;AAWhB,SAASC,cAAcA,CAAAC,IAAA,EAOb;EAAA,IANRC,EAAE,GAMID,IAAA,CANNC,EAAE;IACFC,gBAAgB,GAKVF,IAAA,CALNE,gBAAgB;IAChBC,QAAQ,GAIFH,IAAA,CAJNG,QAAQ;IACRC,eAAe,GAGTJ,IAAA,CAHNI,eAAe;IACfC,aAAa,GAEPL,IAAA,CAFNK,aAAa;IACVC,IAAA,GAAAC,wBAAA,CACGP,IAAA,EAAAQ,SAAA;EAEN,IAAMC,IAAI,GAAGH,IAAI,CAACG,IAAoC;EAEtDZ,QAAQ,CACNY,IAAI,IAAI,IAAI,EACX,2BAA0BA,IAAK,yCAAwCA,IAAK,uHAAsH,CACpM;EAGD,IAAMC,UAAU,GAAGJ,IAAI,CAACI,UAAkD;EAE1Eb,QAAQ,CACNa,UAAU,KAAK,MAAM,EACpB,iMAAgM,CAClM;EAEDb,QAAQ,CACNa,UAAU,IAAI,IAAI,IAAIA,UAAU,KAAK,MAAM,EAC1C,kMAAiM,CACnM;EAGD,IAAMC,uBAAuB,GAAGL,IAAI,CAACK,uBAAuB;EAE5Dd,QAAQ,CACNc,uBAAuB,KAAKC,SAAS,EACpC,4NAA2N,CAC7N;EAED,IAAMC,oBAA4C,GAAG;IACnDC,YAAY,EAAEL,IAAI;IAClBM,WAAW,EAAEL,UAAU,GAAGA,UAAU,KAAK,MAAM,GAAG,IAAI;IACtDA,UAAU,EAAEA,UAAU,IAAIA,UAAU,KAAK,MAAM,GAAGA,UAAU,GAAGE,SAAS;IACxED,uBAAA,EAAAA;EACF,CAAC;EAED,IAAAK,qBAAA,GACErB,oBAAoB,CAMlBD,WAAW,EAAE;MACbO,EAAE,EAAFA,EAAE;MACFC,gBAAgB,EAAhBA,gBAAgB;MAChBC,QAAQ,EAARA,QAAQ;MACRC,eAAe,EAAfA,eAAe;MACfC,aAAa,EAAbA,aAAa;MACbQ,oBAAA,EAAAA;IACF,CAAC,CAAC;IAdII,KAAK,GAAAD,qBAAA,CAALC,KAAK;IAAEC,WAAW,GAAAF,qBAAA,CAAXE,WAAW;IAAEC,UAAU,GAAAH,qBAAA,CAAVG,UAAU;IAAEC,iBAAA,GAAAJ,qBAAA,CAAAI,iBAAA;EAgBxCxB,KAAK,CAACyB,SAAS,CACb;IAAA,IAAAC,qBAAA;IAAA,QACE,CAAAA,qBAAA,GACAH,UAAU,CAACI,WAAW,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAAE,IAAA,CAAAL,UAAU,EAAe,UAAU,EAAG,UAAAM,CAAC,EAAK;QAC1C,IAAMC,SAAS,GAAGP,UAAU,CAACO,SAAS,EAAE;QAIxCC,qBAAqB,CAAC,YAAM;UAC1B,IACEV,KAAK,CAACW,KAAK,GAAG,CAAC,IACfF,SAAS,IACT,CAAED,CAAC,CAA2CI,gBAAgB,EAC9D;YAGAV,UAAU,CAACW,QAAQ,CAAAC,aAAA,CAAAA,aAAA,KACdtC,YAAY,CAACuC,QAAQ,EAAE;cAC1BC,MAAM,EAAEhB,KAAK,CAACiB;YAAA,EACf,CAAC;UACJ;QACF,CAAC,CAAC;MACJ,CAAC;IAAA;EAAC,GACJ,CAACf,UAAU,EAAEF,KAAK,CAACW,KAAK,EAAEX,KAAK,CAACiB,GAAG,CAAC,CACrC;EAED,OACEtC,KAAA,CAAAuC,aAAA,CAACf,iBAAiB,QAChBxB,KAAA,CAAAuC,aAAA,CAACrC,SAAS,EAAAsC,QAAA,KACJ9B,IAAI;IACRW,KAAK,EAAEA,KAAM;IACbC,WAAW,EAAEA,WAAY;IACzBC,UAAU,EAAEA;EAAW,GACvB,CACgB;AAExB;AAEA,eAAe3B,sBAAsB,CAKnCO,cAAc,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}