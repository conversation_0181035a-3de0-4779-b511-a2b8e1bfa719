{"ast": null, "code": "import StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nvar styles = StyleSheet.create({\n  container: {\n    backgroundColor: '#202020',\n    minHeight: '100%',\n    padding: 16\n  }\n});\nexport default styles;", "map": {"version": 3, "names": ["styles", "StyleSheet", "create", "container", "backgroundColor", "minHeight", "padding"], "sources": ["C:/Users/<USER>/Desktop/Signals/CryptoSignalsApp/src/components/Wrapper/styles.js"], "sourcesContent": ["import { StyleSheet } from 'react-native';\r\n\r\nconst styles = StyleSheet.create({\r\n  container: {\r\n    backgroundColor: '#202020',\r\n    minHeight: '100%',\r\n    padding: 16,\r\n  }\r\n});\r\n\r\nexport default styles\r\n"], "mappings": ";AAEA,IAAMA,MAAM,GAAGC,UAAU,CAACC,MAAM,CAAC;EAC/BC,SAAS,EAAE;IACTC,eAAe,EAAE,SAAS;IAC1BC,SAAS,EAAE,MAAM;IACjBC,OAAO,EAAE;EACX;AACF,CAAC,CAAC;AAEF,eAAeN,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}