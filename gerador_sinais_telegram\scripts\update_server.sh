#!/bin/bash

# ========================================
# SCRIPT DE ATUALIZAÇÃO AUTOMÁTICA
# CryptoSignals - Servidor de Produção
# ========================================

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configurações do servidor
SERVER_IP="**************"
SERVER_USER="root"
SERVER_PASSWORD="h4*ls:FtJw0e"
PROJECT_PATH="/opt/gerador_sinais_telegram"

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}  CRYPTOSIGNALS - ATUALIZAÇÃO SERVIDOR${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""

# Função para executar comandos no servidor
execute_remote() {
    local command="$1"
    echo -e "${YELLOW}Executando: ${command}${NC}"
    sshpass -p "${SERVER_PASSWORD}" ssh -o StrictHostKeyChecking=no "${SERVER_USER}@${SERVER_IP}" "${command}"
}

# Função para verificar se sshpass está instalado
check_sshpass() {
    if ! command -v sshpass &> /dev/null; then
        echo -e "${RED}❌ sshpass não está instalado!${NC}"
        echo -e "${YELLOW}Instalando sshpass...${NC}"
        
        # Detectar sistema operacional e instalar
        if [[ "$OSTYPE" == "linux-gnu"* ]]; then
            sudo apt-get update && sudo apt-get install -y sshpass
        elif [[ "$OSTYPE" == "darwin"* ]]; then
            brew install hudochenkov/sshpass/sshpass
        elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "win32" ]]; then
            echo -e "${RED}No Windows, use WSL ou instale sshpass manualmente${NC}"
            echo -e "${YELLOW}Alternativa: Use o script manual update_server_manual.bat${NC}"
            exit 1
        fi
    fi
}

# Verificar dependências
echo -e "${BLUE}🔍 Verificando dependências...${NC}"
check_sshpass

# Testar conexão
echo -e "${BLUE}🔗 Testando conexão com servidor...${NC}"
if ! sshpass -p "${SERVER_PASSWORD}" ssh -o StrictHostKeyChecking=no -o ConnectTimeout=10 "${SERVER_USER}@${SERVER_IP}" "echo 'Conexão OK'"; then
    echo -e "${RED}❌ Falha na conexão com o servidor!${NC}"
    echo -e "${YELLOW}Verifique:${NC}"
    echo "  - Conexão com internet"
    echo "  - IP do servidor: ${SERVER_IP}"
    echo "  - Credenciais de acesso"
    exit 1
fi

echo -e "${GREEN}✅ Conexão estabelecida com sucesso!${NC}"
echo ""

# Parar o serviço
echo -e "${BLUE}🛑 Parando serviço CryptoSignals...${NC}"
execute_remote "systemctl stop gerador_sinais.service"

# Navegar para o diretório do projeto
echo -e "${BLUE}📁 Navegando para diretório do projeto...${NC}"
execute_remote "cd ${PROJECT_PATH}"

# Fazer backup das configurações
echo -e "${BLUE}💾 Fazendo backup das configurações...${NC}"
execute_remote "cd ${PROJECT_PATH} && cp .env .env.backup.$(date +%Y%m%d_%H%M%S) 2>/dev/null || echo 'Arquivo .env não encontrado'"

# Atualizar código do repositório
echo -e "${BLUE}📥 Atualizando código do repositório...${NC}"
execute_remote "cd ${PROJECT_PATH} && git stash && git pull origin main"

# Ativar ambiente virtual
echo -e "${BLUE}🐍 Ativando ambiente virtual...${NC}"
execute_remote "cd ${PROJECT_PATH} && source venv/bin/activate"

# Atualizar dependências se necessário
echo -e "${BLUE}📦 Verificando e atualizando dependências...${NC}"
execute_remote "cd ${PROJECT_PATH} && source venv/bin/activate && pip install --upgrade pip"
execute_remote "cd ${PROJECT_PATH} && source venv/bin/activate && pip install pandas-ta finta --upgrade"

# Verificar se o arquivo .env existe
echo -e "${BLUE}⚙️ Verificando configurações...${NC}"
execute_remote "cd ${PROJECT_PATH} && ls -la .env || echo 'ATENÇÃO: Arquivo .env não encontrado!'"

# Reiniciar o serviço
echo -e "${BLUE}🚀 Reiniciando serviço CryptoSignals...${NC}"
execute_remote "systemctl daemon-reload"
execute_remote "systemctl restart gerador_sinais.service"

# Aguardar um momento para o serviço inicializar
echo -e "${YELLOW}⏳ Aguardando inicialização do serviço...${NC}"
sleep 5

# Verificar status do serviço
echo -e "${BLUE}📊 Verificando status do serviço...${NC}"
execute_remote "systemctl status gerador_sinais.service --no-pager -l"

# Verificar logs recentes
echo -e "${BLUE}📋 Verificando logs recentes...${NC}"
execute_remote "journalctl -u gerador_sinais.service --no-pager -l -n 20"

echo ""
echo -e "${GREEN}========================================${NC}"
echo -e "${GREEN}  ✅ ATUALIZAÇÃO CONCLUÍDA!${NC}"
echo -e "${GREEN}========================================${NC}"
echo ""
echo -e "${YELLOW}📋 Comandos úteis para monitoramento:${NC}"
echo ""
echo -e "${BLUE}Ver logs em tempo real:${NC}"
echo "  ssh root@${SERVER_IP}"
echo "  journalctl -u gerador_sinais.service -f"
echo ""
echo -e "${BLUE}Ver status do serviço:${NC}"
echo "  ssh root@${SERVER_IP}"
echo "  systemctl status gerador_sinais.service"
echo ""
echo -e "${BLUE}Reiniciar serviço manualmente:${NC}"
echo "  ssh root@${SERVER_IP}"
echo "  systemctl restart gerador_sinais.service"
echo ""
echo -e "${BLUE}Acessar diretório do projeto:${NC}"
echo "  ssh root@${SERVER_IP}"
echo "  cd ${PROJECT_PATH}"
echo ""

# Perguntar se quer ver logs em tempo real
read -p "Deseja ver os logs em tempo real? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${BLUE}📋 Mostrando logs em tempo real (Ctrl+C para sair)...${NC}"
    sshpass -p "${SERVER_PASSWORD}" ssh -o StrictHostKeyChecking=no "${SERVER_USER}@${SERVER_IP}" "journalctl -u gerador_sinais.service -f"
fi

echo -e "${GREEN}🎉 Script de atualização finalizado!${NC}"
