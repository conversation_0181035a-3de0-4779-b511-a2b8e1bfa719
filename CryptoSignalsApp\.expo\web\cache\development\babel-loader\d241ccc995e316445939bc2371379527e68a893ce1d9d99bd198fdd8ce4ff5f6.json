{"ast": null, "code": "export {};", "map": {"version": 3, "names": [], "sources": ["E:\\CryptoSignalsApp\\node_modules\\@react-navigation\\native\\src\\types.tsx"], "sourcesContent": ["import type {\n  getActionFromState as getActionFromStateDefault,\n  getPathFromState as getPathFromStateDefault,\n  getStateFromPath as getStateFromPathDefault,\n  PathConfigMap,\n  Route,\n} from '@react-navigation/core';\n\nexport type Theme = {\n  dark: boolean;\n  colors: {\n    primary: string;\n    background: string;\n    card: string;\n    text: string;\n    border: string;\n    notification: string;\n  };\n};\n\nexport type LinkingOptions<ParamList extends {}> = {\n  /**\n   * Whether deep link handling should be enabled.\n   * Defaults to true.\n   */\n  enabled?: boolean;\n  /**\n   * The prefixes are stripped from the URL before parsing them.\n   * Usually they are the `scheme` + `host` (e.g. `myapp://chat?user=jane`)\n   *\n   * This is not supported on Web.\n   *\n   * @example\n   * ```js\n   * {\n   *    prefixes: [\n   *      \"myapp://\", // App-specific scheme\n   *      \"https://example.com\", // Prefix for universal links\n   *      \"https://*.example.com\" // Prefix which matches any subdomain\n   *    ]\n   * }\n   * ```\n   */\n  prefixes: string[];\n  /**\n   * Optional function which takes an incoming URL returns a boolean\n   * indicating whether React Navigation should handle it.\n   *\n   * This can be used to disable deep linking for specific URLs.\n   * e.g. URLs used for authentication, and not for deep linking to screens.\n   *\n   * This is not supported on Web.\n   *\n   * @example\n   * ```js\n   * {\n   *   // Filter out URLs used by expo-auth-session\n   *   filter: (url) => !url.includes('+expo-auth-session')\n   * }\n   * ```\n   */\n  filter?: (url: string) => boolean;\n  /**\n   * Config to fine-tune how to parse the path.\n   *\n   * @example\n   * ```js\n   * {\n   *   Chat: {\n   *     path: 'chat/:author/:id',\n   *     parse: { id: Number }\n   *   }\n   * }\n   * ```\n   */\n  config?: {\n    initialRouteName?: keyof ParamList;\n    screens: PathConfigMap<ParamList>;\n  };\n  /**\n   * Custom function to get the initial URL used for linking.\n   * Uses `Linking.getInitialURL()` by default.\n   *\n   * This is not supported on Web.\n   *\n   * @example\n   * ```js\n   * {\n   *    getInitialURL () => Linking.getInitialURL(),\n   * }\n   * ```\n   */\n  getInitialURL?: () =>\n    | string\n    | null\n    | undefined\n    | Promise<string | null | undefined>;\n  /**\n   * Custom function to get subscribe to URL updates.\n   * Uses `Linking.addEventListener('url', callback)` by default.\n   *\n   * This is not supported on Web.\n   *\n   * @example\n   * ```js\n   * {\n   *    subscribe: (listener) => {\n   *      const onReceiveURL = ({ url }) => listener(url);\n   *\n   *      Linking.addEventListener('url', onReceiveURL);\n   *\n   *      return () => Linking.removeEventListener('url', onReceiveURL);\n   *   }\n   * }\n   * ```\n   */\n  subscribe?: (\n    listener: (url: string) => void\n  ) => undefined | void | (() => void);\n  /**\n   * Custom function to parse the URL to a valid navigation state (advanced).\n   */\n  getStateFromPath?: typeof getStateFromPathDefault;\n  /**\n   * Custom function to convert the state object to a valid URL (advanced).\n   * Only applicable on Web.\n   */\n  getPathFromState?: typeof getPathFromStateDefault;\n  /**\n   * Custom function to convert the state object to a valid action (advanced).\n   */\n  getActionFromState?: typeof getActionFromStateDefault;\n};\n\nexport type DocumentTitleOptions = {\n  enabled?: boolean;\n  formatter?: (\n    options: Record<string, any> | undefined,\n    route: Route<string> | undefined\n  ) => string;\n};\n\nexport type ServerContainerRef = {\n  getCurrentOptions(): Record<string, any> | undefined;\n};\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}