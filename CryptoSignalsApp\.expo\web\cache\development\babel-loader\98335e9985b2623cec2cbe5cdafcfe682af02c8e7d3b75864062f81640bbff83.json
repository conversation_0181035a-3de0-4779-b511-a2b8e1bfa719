{"ast": null, "code": "import View from \"react-native-web/dist/exports/View\";\nexport default View;", "map": {"version": 3, "names": ["View"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\react-native-screens\\src\\components\\FullWindowOverlay.web.tsx"], "sourcesContent": ["import { View } from 'react-native';\nimport React, { ReactNode } from 'react';\n\nexport default View as React.ComponentType<{\n  children: ReactNode;\n}>;\n"], "mappings": ";AAGA,eAAeA,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}