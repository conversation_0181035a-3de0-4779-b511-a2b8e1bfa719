{"ast": null, "code": "function _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nimport Image from \"react-native-web/dist/exports/Image\";\nimport View from \"react-native-web/dist/exports/View\";\nimport React from 'react';\nexport var ScreenStackHeaderBackButtonImage = function ScreenStackHeaderBackButtonImage(props) {\n  return React.createElement(View, null, React.createElement(Image, _extends({\n    resizeMode: \"center\",\n    fadeDuration: 0\n  }, props)));\n};\nexport var ScreenStackHeaderRightView = function ScreenStackHeaderRightView(props) {\n  return React.createElement(View, props);\n};\nexport var ScreenStackHeaderLeftView = function ScreenStackHeaderLeftView(props) {\n  return React.createElement(View, props);\n};\nexport var ScreenStackHeaderCenterView = function ScreenStackHeaderCenterView(props) {\n  return React.createElement(View, props);\n};\nexport var ScreenStackHeaderSearchBarView = function ScreenStackHeaderSearchBarView(props) {\n  return React.createElement(View, props);\n};\nexport var ScreenStackHeaderConfig = function ScreenStackHeaderConfig(props) {\n  return React.createElement(View, props);\n};\nexport var ScreenStackHeaderSubview = View;", "map": {"version": 3, "names": ["React", "ScreenStackHeaderBackButtonImage", "props", "createElement", "View", "Image", "_extends", "resizeMode", "fadeDuration", "ScreenStackHeaderRightView", "ScreenStackHeaderLeftView", "ScreenStackHeaderCenterView", "ScreenStackHeaderSearchBarView", "ScreenStackHeaderConfig", "ScreenStackHeaderSubview"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\react-native-screens\\src\\components\\ScreenStackHeaderConfig.web.tsx"], "sourcesContent": ["import { Image, ImageProps, View, ViewProps } from 'react-native';\nimport React from 'react';\nimport {\n  HeaderSubviewTypes,\n  ScreenStackHeaderConfigProps,\n  SearchBarProps,\n} from '../types';\n\nexport const ScreenStackHeaderBackButtonImage = (\n  props: ImageProps,\n): JSX.Element => (\n  <View>\n    <Image resizeMode=\"center\" fadeDuration={0} {...props} />\n  </View>\n);\n\nexport const ScreenStackHeaderRightView = (\n  props: React.PropsWithChildren<ViewProps>,\n): JSX.Element => <View {...props} />;\n\nexport const ScreenStackHeaderLeftView = (\n  props: React.PropsWithChildren<ViewProps>,\n): JSX.Element => <View {...props} />;\n\nexport const ScreenStackHeaderCenterView = (\n  props: React.PropsWithChildren<ViewProps>,\n): JSX.Element => <View {...props} />;\n\nexport const ScreenStackHeaderSearchBarView = (\n  props: React.PropsWithChildren<Omit<SearchBarProps, 'ref'>>,\n): JSX.Element => <View {...props} />;\n\nexport const ScreenStackHeaderConfig = (\n  props: React.PropsWithChildren<ScreenStackHeaderConfigProps>,\n): JSX.Element => <View {...props} />;\n\nexport const ScreenStackHeaderSubview: React.ComponentType<\n  React.PropsWithChildren<ViewProps & { type?: HeaderSubviewTypes }>\n> = View;\n"], "mappings": ";;;;;;;;;;;AACA,OAAOA,KAAK,MAAM,OAAO;AAOzB,OAAO,IAAMC,gCAAgC,GAC3C,SADWA,gCAAgCA,CAC3CC,KAAiB;EAAA,OAEjBF,KAAA,CAAAG,aAAA,CAACC,IAAI,QACHJ,KAAA,CAAAG,aAAA,CAACE,KAAK,EAAAC,QAAA;IAACC,UAAU,EAAC,QAAQ;IAACC,YAAY,EAAE;EAAE,GAAKN,KAAK,CAAG,CACpD,CACP;AAAA;AAED,OAAO,IAAMO,0BAA0B,GACrC,SADWA,0BAA0BA,CACrCP,KAAyC;EAAA,OACzBF,KAAA,CAAAG,aAAA,CAACC,IAAI,EAAKF,KAAQ,CAAC;AAAA;AAErC,OAAO,IAAMQ,yBAAyB,GACpC,SADWA,yBAAyBA,CACpCR,KAAyC;EAAA,OACzBF,KAAA,CAAAG,aAAA,CAACC,IAAI,EAAKF,KAAQ,CAAC;AAAA;AAErC,OAAO,IAAMS,2BAA2B,GACtC,SADWA,2BAA2BA,CACtCT,KAAyC;EAAA,OACzBF,KAAA,CAAAG,aAAA,CAACC,IAAI,EAAKF,KAAQ,CAAC;AAAA;AAErC,OAAO,IAAMU,8BAA8B,GACzC,SADWA,8BAA8BA,CACzCV,KAA2D;EAAA,OAC3CF,KAAA,CAAAG,aAAA,CAACC,IAAI,EAAKF,KAAQ,CAAC;AAAA;AAErC,OAAO,IAAMW,uBAAuB,GAClC,SADWA,uBAAuBA,CAClCX,KAA4D;EAAA,OAC5CF,KAAA,CAAAG,aAAA,CAACC,IAAI,EAAKF,KAAQ,CAAC;AAAA;AAErC,OAAO,IAAMY,wBAEZ,GAAGV,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}