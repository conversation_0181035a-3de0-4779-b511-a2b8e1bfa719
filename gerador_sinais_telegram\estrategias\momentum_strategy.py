#!/usr/bin/env python3
"""
Estratégia de Momentum Avançada
"""

import logging
import pandas as pd
import numpy as np
from typing import Tuple, Optional
import sys
import os

# Adicionar o diretório raiz ao path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from utils.technical_indicators import talib

logger = logging.getLogger(__name__)

class MomentumStrategy:
    def __init__(self, binance_handler):
        self.binance = binance_handler
        self.name = "Momentum"

        # Configurações da estratégia
        self.rsi_period = 14
        self.stoch_k_period = 14
        self.stoch_d_period = 3
        self.williams_r_period = 14
        self.cci_period = 20

        logger.info(f"Estratégia {self.name} inicializada")

    def analyze_symbol(self, symbol: str) -> Tuple[Optional[str], Optional[float], Optional[float], Optional[float]]:
        """
        Analisa um símbolo baseado em indicadores de momentum
        """
        try:
            # Obter dados de 15 minutos para análise de momentum
            data = self.binance.get_historical_klines(symbol, "15m", 1)

            if data.empty or len(data) < 50:
                return None, None, None, None

            # Calcular indicadores de momentum
            rsi_signal = self._analyze_rsi(data)
            stoch_signal = self._analyze_stochastic(data)
            williams_signal = self._analyze_williams_r(data)
            cci_signal = self._analyze_cci(data)
            macd_signal = self._analyze_macd(data)

            # Combinar sinais
            signals = [rsi_signal, stoch_signal, williams_signal, cci_signal, macd_signal]
            long_signals = sum(1 for s in signals if s == 'LONG')
            short_signals = sum(1 for s in signals if s == 'SHORT')

            # Precisamos de pelo menos 3 sinais na mesma direção
            if long_signals >= 3:
                signal_type = 'LONG'
            elif short_signals >= 3:
                signal_type = 'SHORT'
            else:
                return None, None, None, None

            # Obter preço atual
            current_price = self.binance.get_current_price(symbol)
            if not current_price or not self._validate_price_range(symbol, current_price):
                return None, None, None, None

            # Calcular stop loss e take profit
            stop_loss, take_profit = self._calculate_levels(current_price, signal_type, data)

            # Validar se os cálculos fazem sentido
            if not self._validate_signal_values(symbol, signal_type, current_price, stop_loss, take_profit):
                return None, None, None, None

            logger.info(f"{symbol} - Sinal Momentum {signal_type} (L:{long_signals}, S:{short_signals})")
            return signal_type, float(current_price), float(stop_loss), float(take_profit)

        except Exception as e:
            logger.error(f"Erro ao analisar {symbol} com Momentum: {e}")
            return None, None, None, None

    def _analyze_rsi(self, data: pd.DataFrame) -> str:
        """Analisa RSI"""
        try:
            rsi = talib.RSI(data['close'].values, timeperiod=self.rsi_period)
            current_rsi = rsi[-1]

            # RSI divergência e níveis
            if current_rsi < 30:  # Oversold
                return 'LONG'
            elif current_rsi > 70:  # Overbought
                return 'SHORT'
            elif 30 <= current_rsi <= 40:  # Saindo de oversold
                return 'LONG'
            elif 60 <= current_rsi <= 70:  # Entrando em overbought
                return 'SHORT'
            else:
                return 'NEUTRAL'
        except:
            return 'NEUTRAL'

    def _analyze_stochastic(self, data: pd.DataFrame) -> str:
        """Analisa Stochastic Oscillator"""
        try:
            slowk, slowd = talib.STOCH(data['high'].values, data['low'].values, data['close'].values,
                                      fastk_period=self.stoch_k_period, slowk_period=3, slowd_period=self.stoch_d_period)

            current_k = slowk[-1]
            current_d = slowd[-1]

            # Verificar cruzamentos e níveis
            if len(slowk) >= 2:
                prev_k = slowk[-2]
                prev_d = slowd[-2]

                # Cruzamento para cima em área de oversold
                if prev_k <= prev_d and current_k > current_d and current_k < 20:
                    return 'LONG'
                # Cruzamento para baixo em área de overbought
                elif prev_k >= prev_d and current_k < current_d and current_k > 80:
                    return 'SHORT'

            return 'NEUTRAL'
        except:
            return 'NEUTRAL'

    def _analyze_williams_r(self, data: pd.DataFrame) -> str:
        """Analisa Williams %R"""
        try:
            williams_r = talib.WILLR(data['high'].values, data['low'].values, data['close'].values,
                                   timeperiod=self.williams_r_period)
            current_wr = williams_r[-1]

            # Williams %R níveis
            if current_wr < -80:  # Oversold
                return 'LONG'
            elif current_wr > -20:  # Overbought
                return 'SHORT'
            else:
                return 'NEUTRAL'
        except:
            return 'NEUTRAL'

    def _analyze_cci(self, data: pd.DataFrame) -> str:
        """Analisa Commodity Channel Index"""
        try:
            cci = talib.CCI(data['high'].values, data['low'].values, data['close'].values,
                           timeperiod=self.cci_period)
            current_cci = cci[-1]

            # CCI níveis
            if current_cci < -100:  # Oversold
                return 'LONG'
            elif current_cci > 100:  # Overbought
                return 'SHORT'
            else:
                return 'NEUTRAL'
        except:
            return 'NEUTRAL'

    def _analyze_macd(self, data: pd.DataFrame) -> str:
        """Analisa MACD"""
        try:
            macd, signal, hist = talib.MACD(data['close'].values)

            if len(hist) >= 2:
                current_hist = hist[-1]
                prev_hist = hist[-2]

                # Cruzamento do histograma
                if prev_hist <= 0 and current_hist > 0:
                    return 'LONG'
                elif prev_hist >= 0 and current_hist < 0:
                    return 'SHORT'

            return 'NEUTRAL'
        except:
            return 'NEUTRAL'

    def _calculate_levels(self, entry_price: float, signal_type: str, data: pd.DataFrame) -> Tuple[float, float]:
        """Calcula stop loss e take profit baseado na volatilidade"""
        try:
            # Calcular ATR para volatilidade
            atr = talib.ATR(data['high'].values, data['low'].values, data['close'].values, timeperiod=14)
            current_atr = atr[-1]

            # Ajustar níveis baseado na volatilidade
            sl_multiplier = 1.2  # 1.2x ATR para stop loss
            tp_multiplier = 2.0  # 2.0x ATR para take profit

            if signal_type == 'LONG':
                stop_loss = entry_price - (current_atr * sl_multiplier)
                take_profit = entry_price + (current_atr * tp_multiplier)
            else:  # SHORT
                stop_loss = entry_price + (current_atr * sl_multiplier)
                take_profit = entry_price - (current_atr * tp_multiplier)

            return round(stop_loss, 6), round(take_profit, 6)
        except:
            # Fallback para percentuais fixos
            if signal_type == 'LONG':
                stop_loss = entry_price * 0.975  # 2.5% stop loss
                take_profit = entry_price * 1.05  # 5% take profit
            else:
                stop_loss = entry_price * 1.025
                take_profit = entry_price * 0.95

            return round(stop_loss, 6), round(take_profit, 6)

    def _validate_price_range(self, symbol: str, price: float) -> bool:
        """Valida se um preço está dentro de uma faixa realista"""
        if price <= 0:
            return False

        price_ranges = {
            'BTC': (10000, 150000), 'ETH': (500, 10000), 'BNB': (50, 1000),
            'ADA': (0.1, 5), 'SOL': (10, 1000), 'DOT': (1, 50),
            'LINK': (1, 100), 'MATIC': (0.1, 10), 'AVAX': (5, 200)
        }

        for coin, (min_price, max_price) in price_ranges.items():
            if coin in symbol:
                return min_price <= price <= max_price

        return 0.001 <= price <= 100000

    def _validate_signal_values(self, symbol: str, signal_type: str, entry_price: float,
                               stop_loss: float, take_profit: float) -> bool:
        """Valida se os valores do sinal fazem sentido"""
        try:
            if entry_price <= 0 or stop_loss <= 0 or take_profit <= 0:
                return False

            if signal_type == 'LONG':
                if stop_loss >= entry_price or take_profit <= entry_price:
                    return False
                max_tp = entry_price * 1.08  # Máximo 8%
                if take_profit > max_tp:
                    return False
            else:  # SHORT
                if stop_loss <= entry_price or take_profit >= entry_price:
                    return False
                min_tp = entry_price * 0.92  # Mínimo 8%
                if take_profit < min_tp:
                    return False

            return True
        except:
            return False
