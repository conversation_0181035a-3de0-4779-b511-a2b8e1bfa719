# Relatório do Sistema Atual - CryptoSignals

## 1. Visão Geral do Sistema

O CryptoSignals é uma plataforma de geração e envio de sinais de trading para criptomoedas, com foco em operações de curto e médio prazo. O sistema analisa automaticamente o mercado de criptomoedas, identifica oportunidades de trading com base em diferentes estratégias técnicas e envia sinais formatados para um grupo no Telegram.

**Principais componentes:**
- Gerador de sinais automatizado
- Formatador de mensagens para Telegram
- Sistema de monitoramento de resultados
- Controle de duplicação de sinais
- Gerenciamento de atualizações de profit

## 2. Arquitetura do Sistema

O sistema está organizado em uma estrutura modular com os seguintes componentes:

### 2.1 Componentes Principais
- **main.py**: Arquivo principal que coordena todo o fluxo de trabalho (388 linhas)
- **utils/**: Pasta com utilitários e ferramentas auxiliares
- **estrategias/**: Pasta com implementações de diferentes estratégias de trading
- **config/**: Pasta com arquivos de configuração

### 2.2 Módulos Utilitários
- **signal_formatter.py**: Formata os sinais para envio no Telegram
- **signal_controller.py**: Controla duplicação de sinais e atualizações de profit
- **telegram_sender.py**: Gerencia a conexão e envio de mensagens para o Telegram
- **image_processor.py**: Cria imagens para sinais e atualizações de profit
- **binance_client.py**: Interface com a API da Binance para obter dados de mercado

### 2.3 Estratégias Implementadas
- **scalp_strategy.py**: Estratégia de scalping para operações de curto prazo
- **breakout_volume.py**: Estratégia de breakout com confirmação de volume
- **inside_bar.py**: Estratégia baseada no padrão Inside Bar
- **mfi_strategy.py**: Estratégia baseada no indicador Money Flow Index
- **swing_strategy.py**: Estratégia para operações de swing trading

## 3. Funcionalidades Detalhadas

### 3.1 Geração de Sinais

O sistema gera sinais de trading com base em cinco estratégias diferentes:

#### 3.1.1 Scalp Trading
- **Indicadores utilizados**: RSI, EMA 5 e EMA 20
- **Lógica de entrada**: 
  - LONG: RSI < 40, EMA5 > EMA20, preço < EMA20
  - SHORT: RSI > 60, EMA5 < EMA20, preço > EMA20
- **Take Profits**: Quatro níveis (40%, 60%, 80%, 100%)
- **Timeframe**: 1 minuto
- **Validade do sinal**: 4 horas

#### 3.1.2 Breakout com Volume
- **Indicadores utilizados**: Análise de volume e níveis de suporte/resistência
- **Lógica de entrada**: Rompimento de resistência/suporte com aumento de volume
- **Take Profit**: Nível único calculado com base na volatilidade
- **Stop Loss**: Calculado abaixo/acima do ponto de entrada
- **Timeframe**: 15 minutos
- **Validade do sinal**: 6 horas

#### 3.1.3 Inside Bar
- **Padrão gráfico**: Formação de Inside Bar (barra interna)
- **Lógica de entrada**: Rompimento da máxima/mínima da barra-mãe
- **Take Profit**: Nível único baseado na projeção do movimento
- **Stop Loss**: Abaixo/acima da mínima/máxima da barra interna
- **Timeframe**: 15 minutos
- **Validade do sinal**: 8 horas

#### 3.1.4 MFI (Money Flow Index)
- **Indicador principal**: Money Flow Index
- **Lógica de entrada**: Terceiro toque na zona de sobrevenda/sobrecompra
- **Take Profit**: Nível único baseado na volatilidade histórica
- **Stop Loss**: Calculado com base no ATR
- **Timeframe**: 15 minutos
- **Validade do sinal**: 12 horas

#### 3.1.5 Swing Trading
- **Indicadores utilizados**: Cruzamento de EMAs e análise de tendência
- **Lógica de entrada**: Confirmação de cruzamento de EMAs e breakout
- **Take Profit**: Nível único baseado em alvos de Fibonacci
- **Stop Loss**: Calculado com base na volatilidade
- **Timeframe**: 4 horas / Diário
- **Validade do sinal**: 48 horas

### 3.2 Formatação de Sinais

O sistema formata os sinais de forma padronizada para cada tipo de estratégia:

#### 3.2.1 Formato Comum a Todos os Sinais
- Nome do par (ex: BTCUSDT)
- Direção (LONG/SHORT) com emoji (📈/📉)
- Preço de entrada
- Níveis de Take Profit
- Stop Loss recomendado
- Alavancagem sugerida
- Prazo de validade do sinal
- Instruções de uso

#### 3.2.2 Elementos Específicos por Estratégia
- **Scalp**: Múltiplos níveis de Take Profit (40%, 60%, 80%, 100%)
- **Breakout**: Detalhes sobre o volume e resistência/suporte
- **Inside Bar**: Explicação sobre o padrão e como entrar
- **MFI**: Detalhes sobre o indicador e confirmações
- **Swing**: Timeframe recomendado e horizonte de tempo

### 3.3 Controle de Duplicação

O sistema implementa um mecanismo para evitar o envio de sinais duplicados:

- **Critérios de duplicação**:
  - Mesmo par e direção (LONG/SHORT)
  - Intervalo menor que 5 minutos entre sinais
  - Variação de preço menor que 0.5%
- **Armazenamento**: Dicionário em memória com chaves compostas (par_direção)
- **Limpeza automática**: Remoção de sinais antigos após 60 minutos

### 3.4 Monitoramento de Resultados

O sistema monitora continuamente os sinais enviados para verificar se os níveis de profit foram atingidos:

- **Níveis monitorados**: 40%, 60%, 80%, 100% para sinais de scalp
- **Período de monitoramento**: 30 minutos (configurável)
- **Notificações**: Envio de mensagem quando cada nível é atingido
- **Fechamento automático**: Sinal é considerado fechado quando todos os níveis são atingidos ou o tempo expira

### 3.5 Controle de Atualizações de Profit

O sistema controla as atualizações de profit para evitar spam:

- **Critérios para nova atualização**:
  - Pelo menos 5 minutos desde a última atualização
  - Diferença de pelo menos 20% no nível de profit
- **Armazenamento**: Dicionário em memória com o último profit e timestamp
- **Limpeza automática**: Remoção de atualizações antigas após 60 minutos

### 3.6 Envio de Mensagens para Telegram

O sistema gerencia o envio de mensagens para o Telegram com controle de taxa:

- **Autenticação**: Uso de API_ID e API_HASH para autenticação
- **Controle de taxa**: Intervalo mínimo de 5 segundos entre mensagens
- **Horário de operação**: Configurável (padrão: 6h às 21h)
- **Limite diário**: Número máximo de sinais por dia (configurável)
- **Tratamento de erros**: Retry em caso de FloodWaitError

## 4. Configurações do Sistema

O sistema utiliza um arquivo de configuração (.env) com os seguintes parâmetros:

### 4.1 Configurações Gerais
- **MAX_SIGNALS_PER_DAY**: Limite máximo de sinais por dia (padrão: 20)
- **SIGNAL_INTERVAL_SECONDS**: Intervalo entre verificações (padrão: 300 segundos)
- **TRADING_START_HOUR**: Hora de início das operações (padrão: 6)
- **TRADING_END_HOUR**: Hora de término das operações (padrão: 21)
- **SIGNAL_VALIDITY_MINUTES**: Tempo de validade dos sinais (padrão: 30 minutos)

### 4.2 Configurações do Telegram
- **TELEGRAM_API_ID**: ID da API do Telegram
- **TELEGRAM_API_HASH**: Hash da API do Telegram
- **TELEGRAM_GROUP_ID**: ID do grupo para envio dos sinais
- **TELEGRAM_SESSION_FILE**: Arquivo de sessão do Telegram

### 4.3 Configurações da Binance
- **BINANCE_API_KEY**: Chave da API da Binance
- **BINANCE_API_SECRET**: Segredo da API da Binance
- **BINANCE_TESTNET**: Flag para usar testnet (True/False)

### 4.4 Configurações de Estratégias
- **LEVERAGE**: Alavancagem padrão (padrão: 20)
- **LOOKBACK_DAYS**: Dias de histórico para análise (padrão: 1)
- **SCALP_PROFIT_LEVELS**: Níveis de profit para scalp [40, 60, 80, 100]

## 5. Fluxo de Execução

O fluxo de execução do sistema segue os seguintes passos:

1. **Inicialização**:
   - Carregamento das configurações
   - Conexão com a API da Binance
   - Conexão com o cliente do Telegram

2. **Loop Principal**:
   - Verificação do horário de trading
   - Verificação do limite diário de sinais
   - Execução das estratégias de trading
   - Monitoramento de sinais abertos
   - Aguardo do intervalo configurado

3. **Geração de Sinais**:
   - Seleção aleatória de pares para análise
   - Aplicação das estratégias de trading
   - Verificação de duplicação
   - Formatação e envio dos sinais

4. **Monitoramento de Resultados**:
   - Verificação contínua dos preços atuais
   - Cálculo do profit atual
   - Notificação quando níveis são atingidos
   - Fechamento de sinais expirados

5. **Finalização**:
   - Desconexão do cliente do Telegram
   - Registro de logs de encerramento

## 6. Logs e Monitoramento

O sistema mantém logs detalhados de todas as operações:

- **Nível de log**: Configurável (INFO, DEBUG, WARNING, ERROR)
- **Arquivo de log**: gerador_sinais.log
- **Informações registradas**:
  - Sinais gerados e enviados
  - Profits atingidos
  - Erros e exceções
  - Conexões e desconexões
  - Limites atingidos

## 7. Limitações Atuais

O sistema atual possui algumas limitações:

- **Persistência**: Dados armazenados apenas em memória
- **Escalabilidade**: Execução em um único processo
- **Personalização**: Sem filtros personalizados por usuário
- **Feedback**: Sem mecanismo para feedback dos usuários
- **Análise de desempenho**: Sem métricas detalhadas de sucesso dos sinais

## 8. Conclusão

O CryptoSignals é um sistema robusto para geração e envio de sinais de trading para criptomoedas, com suporte a múltiplas estratégias e monitoramento de resultados. O sistema está estruturado de forma modular, permitindo a fácil adição de novas estratégias e funcionalidades.

Com uma base de 20.000 clientes, o sistema demonstra capacidade de escala e confiabilidade, embora existam oportunidades de melhoria em termos de personalização, análise de desempenho e interatividade com os usuários.