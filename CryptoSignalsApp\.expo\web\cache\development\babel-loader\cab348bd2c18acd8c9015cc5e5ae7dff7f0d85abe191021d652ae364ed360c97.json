{"ast": null, "code": "function emptyFunction() {}\nvar BackHandler = {\n  exitApp: emptyFunction,\n  addEventListener: function addEventListener() {\n    console.error('BackHandler is not supported on web and should not be used.');\n    return {\n      remove: emptyFunction\n    };\n  },\n  removeEventListener: emptyFunction\n};\nexport default BackHandler;", "map": {"version": 3, "names": ["emptyFunction", "<PERSON><PERSON><PERSON><PERSON>", "exitApp", "addEventListener", "console", "error", "remove", "removeEventListener"], "sources": ["E:/CryptoSignalsApp/node_modules/react-native-web/dist/exports/BackHandler/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nfunction emptyFunction() {}\nvar BackHandler = {\n  exitApp: emptyFunction,\n  addEventListener() {\n    console.error('BackHandler is not supported on web and should not be used.');\n    return {\n      remove: emptyFunction\n    };\n  },\n  removeEventListener: emptyFunction\n};\nexport default BackHandler;"], "mappings": "AAUA,SAASA,aAAaA,CAAA,EAAG,CAAC;AAC1B,IAAIC,WAAW,GAAG;EAChBC,OAAO,EAAEF,aAAa;EACtBG,gBAAgB,WAAhBA,gBAAgBA,CAAA,EAAG;IACjBC,OAAO,CAACC,KAAK,CAAC,6DAA6D,CAAC;IAC5E,OAAO;MACLC,MAAM,EAAEN;IACV,CAAC;EACH,CAAC;EACDO,mBAAmB,EAAEP;AACvB,CAAC;AACD,eAAeC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}