# Dashboard Web CryptoSignals - Dependências Específicas
# Para instalar: pip install -r requirements_dashboard.txt
# Versão atualizada com paleta de cores CryptoSignals

# Web Framework Core
Flask>=2.3.0
Flask-SocketIO>=5.3.0

# WebSocket Support
python-socketio>=5.8.0
python-engineio>=4.7.0
eventlet>=0.33.0

# SSH Connection for Server Monitoring
paramiko>=3.0.0

# System Monitoring
psutil>=5.9.0

# HTTP Requests
requests>=2.28.0

# Environment Variables
python-dotenv>=1.0.0

# Date/Time Utilities
python-dateutil>=2.8.0

# Logging and Console Output
colorama>=0.4.6
rich>=13.0.0

# Security
cryptography>=41.0.0

# JSON Processing (optional)
simplejson>=3.19.0

# Development Dependencies (optional)
# Uncomment for development
# flask-cors>=4.0.0
# watchdog>=3.0.0
