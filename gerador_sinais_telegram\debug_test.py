import sys
import os

print("Iniciando teste de debug...")

try:
    print("Importando SignalFormatter...")
    from utils.signal_formatter import SignalFormatter
    print("Importacao bem-sucedida!")
    
    print("Testando metodo format_scalp_signal...")
    message = SignalFormatter.format_scalp_signal(
        'CAKEUSDT', 
        2.35040, 
        {40: 2.30339, 60: 2.27989}, 
        'SHORT', 
        20
    )
    
    print("Metodo executado!")
    print("Tipo da mensagem:", type(message))
    
    if message:
        print("Mensagem gerada com sucesso!")
        print("Primeiros 100 caracteres:", message[:100])
        
        if 'CRYPTOSIGNALS PROFESSIONAL' in message:
            print("SUCESSO: Formatacao profissional detectada!")
        elif 'CryptoSignals App' in message:
            print("PROBLEMA: Formatacao antiga detectada!")
        else:
            print("AVISO: Formato nao reconhecido")
    else:
        print("ERRO: Nenhuma mensagem foi gerada")
        
except Exception as e:
    print("ERRO durante execucao:", str(e))
    import traceback
    traceback.print_exc()

print("Teste finalizado.")
