import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#202020',
    padding: 16,
  },
  pageTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FECB37',
    marginBottom: 20,
    textAlign: 'center',
    fontFamily: 'Poppins_600SemiBold',
  },
  loadingText: {
    color: '#fff',
    textAlign: 'center',
    fontSize: 16,
    marginTop: 50,
  },
  newsItem: {
    backgroundColor: '#2a2a2a',
    padding: 16,
    marginBottom: 12,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#FECB37',
  },
  newsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
    fontFamily: 'Poppins_500Medium',
  },
  newsDate: {
    fontSize: 12,
    color: '#8a8a8a',
    marginBottom: 8,
  },
  newsContent: {
    fontSize: 14,
    color: '#ccc',
    lineHeight: 20,
  },
});

export default styles;
