import asyncio
import logging
import datetime
import sys
import os
import argparse
from typing import Dict, List, Optional, Any, Tuple

# Adicionar o diretório raiz ao path para encontrar os módulos
sys.path.insert(0, '.')

from utils.database import DatabaseHandler
from utils.binance_client import BinanceHandler
from utils.telegram_sender import TelegramSender
from utils.signal_formatter import SignalFormatter
from config.settings import SCALP_PROFIT_LEVELS

# Configurar logging
import io
import sys

# Criar um handler de arquivo
file_handler = logging.FileHandler('update_results.log', encoding='utf-8')

# Criar um handler de console que lida com Unicode
class UnicodeStreamHandler(logging.StreamHandler):
    def __init__(self):
        logging.StreamHandler.__init__(self, io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8'))

    def emit(self, record):
        try:
            msg = self.format(record)
            stream = self.stream
            stream.write(msg + self.terminator)
            self.flush()
        except Exception:
            self.handleError(record)

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        UnicodeStreamHandler(),
        file_handler
    ]
)

logger = logging.getLogger(__name__)

class ResultsUpdater:
    """Classe para atualizar resultados de sinais e enviar notificações"""

    def __init__(self, db_path="database.sqlite"):
        """
        Inicializa o atualizador de resultados

        Args:
            db_path (str): Caminho para o banco de dados
        """
        self.db = DatabaseHandler(db_path)
        self.binance = BinanceHandler()
        self.telegram = None  # Será inicializado no método run
        self.formatter = SignalFormatter()

    async def connect_telegram(self):
        """Conecta ao Telegram"""
        self.telegram = TelegramSender()
        connected = await self.telegram.connect()
        if not connected:
            logger.error("Falha ao conectar ao Telegram")
            return False
        return True

    async def update_signal_results(self, signal: Dict[str, Any]) -> Tuple[bool, str, float]:
        """
        Atualiza os resultados de um sinal

        Args:
            signal (dict): Dados do sinal

        Returns:
            tuple: (atualizado, resultado, lucro_percentual)
        """
        symbol = signal['symbol']
        signal_type = signal['signal_type']
        entry_price = signal['entry_price']

        # Obter preço atual
        current_price = self.binance.get_current_price(symbol)
        if current_price is None:
            logger.warning(f"Não foi possível obter o preço atual para {symbol}")
            return False, "UNKNOWN", 0.0

        # Calcular lucro/prejuízo
        price_diff = ((current_price - entry_price) / entry_price) * 100
        if signal_type == "SHORT":
            price_diff = -price_diff

        # Verificar se é um sinal de scalp (com múltiplos níveis de take profit)
        is_scalp = signal['strategy'] == 'Scalp'

        # Determinar resultado com base no preço atual
        result = "UNKNOWN"
        status = signal['status']

        # Se o sinal já está fechado, manter o resultado atual
        if status != 'OPEN':
            return False, signal['result'] or "UNKNOWN", signal['profit_percentage'] or 0.0

        # Para sinais de scalp, verificar níveis de take profit
        if is_scalp:
            # Verificar se atingiu algum nível de take profit
            hit_levels = []
            for level in SCALP_PROFIT_LEVELS:
                if price_diff >= level:
                    hit_levels.append(level)

            if hit_levels:
                highest_level = max(hit_levels)
                # Verificar se este nível já foi notificado
                if not self._is_level_notified(signal['id'], highest_level):
                    await self._notify_profit_hit(signal, current_price, highest_level)
                    result = "WIN"
                    return True, result, price_diff

            # Verificar stop loss para scalp
            if price_diff <= -3.0:  # Assumindo stop loss em -3%
                if not self._is_stop_loss_notified(signal['id']):
                    await self._notify_stop_loss(signal, current_price, price_diff)
                    result = "LOSS"
                    return True, result, price_diff
        else:
            # Para outros tipos de sinais, verificar take profit e stop loss
            take_profit = signal['take_profit_1']
            stop_loss = signal['stop_loss']

            # Calcular percentuais de take profit e stop loss
            tp_percent = ((take_profit - entry_price) / entry_price) * 100
            if signal_type == "SHORT":
                tp_percent = -tp_percent

            sl_percent = ((stop_loss - entry_price) / entry_price) * 100
            if signal_type == "SHORT":
                sl_percent = -sl_percent

            # Verificar se atingiu take profit
            if price_diff >= tp_percent:
                if not self._is_take_profit_notified(signal['id']):
                    await self._notify_take_profit(signal, current_price, price_diff)
                    result = "WIN"
                    return True, result, price_diff

            # Verificar se atingiu stop loss
            if price_diff <= sl_percent:
                if not self._is_stop_loss_notified(signal['id']):
                    await self._notify_stop_loss(signal, current_price, price_diff)
                    result = "LOSS"
                    return True, result, price_diff

        # Se chegou aqui, não houve atualização
        return False, result, price_diff

    def _is_level_notified(self, signal_id: int, level: int) -> bool:
        """Verifica se um nível de profit já foi notificado"""
        updates = self.db.get_signal_updates(signal_id, 'TP_HIT')
        for update in updates:
            if update['profit_level'] == level:
                return True
        return False

    def _is_take_profit_notified(self, signal_id: int) -> bool:
        """Verifica se o take profit já foi notificado"""
        updates = self.db.get_signal_updates(signal_id, 'TP_HIT')
        return len(updates) > 0

    def _is_stop_loss_notified(self, signal_id: int) -> bool:
        """Verifica se o stop loss já foi notificado"""
        updates = self.db.get_signal_updates(signal_id, 'SL_HIT')
        return len(updates) > 0

    async def _notify_profit_hit(self, signal: Dict[str, Any], current_price: float, level: int) -> None:
        """Notifica que um nível de profit foi atingido"""
        # Formatar mensagem
        message = self.formatter.format_profit_message(
            symbol=signal['symbol'],
            signal_type=signal['signal_type'],
            leverage=signal['leverage'],
            current_price=current_price,
            profit_level=level,
            entry_price=signal['entry_price']
        )

        # Enviar mensagem
        reply_message_id = None
        if signal['message_id']:
            reply_message_id = await self.telegram.send_reply_message(message, signal['message_id'])
        else:
            reply_message_id = await self.telegram.send_message(message)

        # Registrar atualização no banco de dados
        update_id = self.db.save_signal_update(
            signal_id=signal['id'],
            update_type='TP_HIT',
            price=current_price,
            profit_level=level,
            message_id=reply_message_id
        )

        # Se for o nível máximo, atualizar o status do sinal
        if level == max(SCALP_PROFIT_LEVELS):
            self.db.update_signal_status(
                signal_id=signal['id'],
                status='TP_HIT',
                result='WIN',
                profit_percentage=level,
                hit_level=level
            )

        logger.info(f"Notificação de profit {level}% enviada para {signal['symbol']} {signal['signal_type']}")

    async def _notify_take_profit(self, signal: Dict[str, Any], current_price: float, price_diff: float) -> None:
        """Notifica que o take profit foi atingido"""
        # Formatar mensagem
        message = self.formatter.format_profit_message(
            symbol=signal['symbol'],
            signal_type=signal['signal_type'],
            leverage=signal['leverage'],
            current_price=current_price,
            profit_level=int(price_diff),
            entry_price=signal['entry_price']
        )

        # Enviar mensagem
        reply_message_id = None
        if signal['message_id']:
            reply_message_id = await self.telegram.send_reply_message(message, signal['message_id'])
        else:
            reply_message_id = await self.telegram.send_message(message)

        # Registrar atualização no banco de dados
        update_id = self.db.save_signal_update(
            signal_id=signal['id'],
            update_type='TP_HIT',
            price=current_price,
            profit_level=int(price_diff),
            message_id=reply_message_id
        )

        # Atualizar o status do sinal
        self.db.update_signal_status(
            signal_id=signal['id'],
            status='TP_HIT',
            result='WIN',
            profit_percentage=price_diff,
            hit_level=100  # Consideramos como 100% do alvo
        )

        logger.info(f"Notificação de take profit enviada para {signal['symbol']} {signal['signal_type']}")

    async def _notify_stop_loss(self, signal: Dict[str, Any], current_price: float, price_diff: float) -> None:
        """Notifica que o stop loss foi atingido"""
        # Usar formatação profissional
        message = self.formatter.format_stop_loss_message(
            symbol=signal['symbol'],
            signal_type=signal['signal_type'],
            leverage=signal['leverage'],
            entry_price=signal['entry_price'],
            current_price=current_price,
            loss_percentage=abs(price_diff)
        )

        # Enviar mensagem
        reply_message_id = None
        if signal['message_id']:
            reply_message_id = await self.telegram.send_reply_message(message, signal['message_id'])
        else:
            reply_message_id = await self.telegram.send_message(message)

        # Registrar atualização no banco de dados
        update_id = self.db.save_signal_update(
            signal_id=signal['id'],
            update_type='SL_HIT',
            price=current_price,
            profit_level=int(price_diff),
            message_id=reply_message_id
        )

        # Atualizar o status do sinal
        self.db.update_signal_status(
            signal_id=signal['id'],
            status='SL_HIT',
            result='LOSS',
            profit_percentage=price_diff
        )

        logger.info(f"Notificação de stop loss enviada para {signal['symbol']} {signal['signal_type']}")

    async def check_expired_signals(self) -> None:
        """Verifica e atualiza sinais expirados"""
        # Obter sinais abertos
        open_signals = self.db.get_open_signals()

        current_time = datetime.datetime.now()
        for signal in open_signals:
            # Converter string para datetime
            created_at = datetime.datetime.fromisoformat(signal['created_at'])

            # Determinar tempo de validade com base na estratégia
            validity_hours = 0.01  # Forçar expiração para teste (10 minutos)

            # Valores normais (comentados para teste)
            # validity_hours = 4  # Padrão
            # if signal['strategy'] == 'Scalp':
            #     validity_hours = 4
            # elif signal['strategy'] == 'Breakout':
            #     validity_hours = 6
            # elif signal['strategy'] == 'Inside Bar':
            #     validity_hours = 8
            # elif signal['strategy'] == 'MFI':
            #     validity_hours = 12
            # elif signal['strategy'] == 'Swing':
            #     validity_hours = 48

            # Verificar se o sinal expirou
            time_elapsed = (current_time - created_at).total_seconds() / 3600  # Em horas
            if time_elapsed > validity_hours:
                # Verificar se já foi notificado como expirado
                updates = self.db.get_signal_updates(signal['id'], 'EXPIRED')
                if not updates:
                    # Atualizar o status do sinal
                    self.db.update_signal_status(
                        signal_id=signal['id'],
                        status='EXPIRED',
                        result='BREAKEVEN'
                    )

                    # Registrar a atualização
                    update_id = self.db.save_signal_update(
                        signal_id=signal['id'],
                        update_type='EXPIRED'
                    )

                    logger.info(f"Sinal {signal['id']} ({signal['symbol']} {signal['signal_type']}) marcado como expirado")

    async def update_all_signals(self) -> None:
        """Atualiza todos os sinais abertos"""
        # Obter sinais abertos
        open_signals = self.db.get_open_signals()

        logger.info(f"Verificando {len(open_signals)} sinais abertos")

        for signal in open_signals:
            try:
                updated, result, profit = await self.update_signal_results(signal)
                if updated:
                    logger.info(f"Sinal {signal['id']} ({signal['symbol']} {signal['signal_type']}) atualizado: {result} ({profit:.2f}%)")
            except Exception as e:
                logger.error(f"Erro ao atualizar sinal {signal['id']}: {e}")

        # Verificar sinais expirados
        await self.check_expired_signals()

    async def generate_daily_report(self) -> None:
        """Gera e envia um relatório diário de desempenho"""
        # Obter estatísticas do dia
        today = datetime.date.today()
        stats = self.db.get_statistics(today, today)

        if not stats:
            logger.info("Sem estatísticas para hoje")
            return

        stat = stats[0]

        # Calcular métricas
        total_signals = stat['signals_sent']
        profitable_signals = stat['successful_signals']
        win_rate = (profitable_signals / max(1, profitable_signals + stat['failed_signals'])) * 100
        avg_profit = stat['average_profit']

        # Usar formatação profissional
        message = self.formatter.format_daily_report(
            total_signals, profitable_signals, win_rate, avg_profit
        )

        # Enviar mensagem
        await self.telegram.send_message(message)
        logger.info("Relatório diário enviado")

    async def run(self, send_report=False):
        """Executa o atualizador de resultados"""
        try:
            # Conectar ao Telegram
            connected = await self.connect_telegram()
            if not connected:
                return

            # Atualizar sinais
            await self.update_all_signals()

            # Enviar relatório diário se solicitado
            if send_report:
                await self.generate_daily_report()

        except Exception as e:
            logger.error(f"Erro ao executar atualizador: {e}")
        finally:
            # Desconectar do Telegram
            if self.telegram:
                await self.telegram.disconnect()

            # Fechar conexão com o banco de dados
            self.db.close()

async def main():
    """Função principal"""
    parser = argparse.ArgumentParser(description='Atualiza resultados de sinais e envia notificações')
    parser.add_argument('--report', action='store_true', help='Envia relatório diário')
    args = parser.parse_args()

    updater = ResultsUpdater()
    await updater.run(send_report=args.report)

if __name__ == "__main__":
    asyncio.run(main())
