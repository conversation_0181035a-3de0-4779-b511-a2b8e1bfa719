<!-- <PERSON><PERSON> apresentação visual com banner moderno -->
<div align="center">
  <img src="logo.png" alt="Gerador de Sinais Telegram" width="200">
  <h1>🚀 Gerador de Sinais Telegram</h1>
  
  [![MIT License](https://img.shields.io/badge/License-MIT-green.svg)](https://choosealicense.com/licenses/mit/)
  [![Python](https://img.shields.io/badge/Python-3.7+-blue.svg)](https://www.python.org/)
  [![SQLite](https://img.shields.io/badge/SQLite-3-blue.svg)](https://www.sqlite.org/)
  [![Telegram](https://img.shields.io/badge/Telegram-API-blue.svg)](https://core.telegram.org/api)
  
  <p>
    <b>Sistema completo para geração, monitoramento e análise de sinais de trading para criptomoedas</b>
  </p>
  
  <p>
    <a href="#-sobre">Sobre</a> •
    <a href="#-funcionalidades">Funcionalidades</a> •
    <a href="#-arquitetura">Arquitetura</a> •
    <a href="#-banco-de-dados">Banco de Dados</a> •
    <a href="#-monitoramento">Monitoramento</a> •
    <a href="#-instalação">Instalação</a> •
    <a href="#-configurações">Configurações</a> •
    <a href="#-simulação">Simulação</a> •
    <a href="#-logs">Logs</a> •
    <a href="#-melhorias">Melhorias</a> •
    <a href="#-contribuição">Contribuição</a>
  </p>
</div>

<br>

## 📋 Sobre

<img align="right" src="https://img.icons8.com/color/96/000000/blockchain-technology.png" width="100">

Sistema avançado para geração, monitoramento e análise de sinais de trading para criptomoedas, com envio automático de atualizações via Telegram e armazenamento em banco de dados SQLite.

<br clear="right"/>

## ✨ Funcionalidades

| Funcionalidade | Descrição |
|----------------|-----------|
| 🔄 **Geração Automática** | Análise técnica para múltiplas estratégias de trading |
| 💾 **Banco SQLite** | Armazenamento completo de sinais, atualizações e estatísticas |
| 📊 **Monitoramento** | Verificação contínua de sinais abertos com detecção de máximas/mínimas |
| ⚙️ **Parâmetros Adaptativos** | Stop loss baseado em ATR e níveis de take profit ajustados à volatilidade |
| 📱 **Notificações** | Atualizações automáticas de take profit, stop loss e expiração |
| 📈 **Relatórios** | Estatísticas diárias e análise de desempenho detalhada |
| 🧩 **Múltiplas Estratégias** | Scalp, Breakout, Inside Bar, MFI e Swing |

<br>

## 🏗️ Arquitetura

<div align="center">
  <img src="https://img.icons8.com/color/96/000000/workflow.png" width="100">
</div>

O sistema é estruturado com os seguintes componentes:

```mermaid
graph TD
    A[main.py] --> B[Gerador de Sinais]
    A --> C[Monitoramento]
    B --> D[Banco de Dados SQLite]
    C --> D
    D --> E[Módulo Telegram]
    B --> F[Estratégias de Trading]
    F -->|Scalp| B
    F -->|Breakout| B
    F -->|Inside Bar| B
    F -->|MFI| B
    F -->|Swing| B
    G[query_database.py] --> D
```

<br>

## 💾 Banco de Dados

<div align="center">
  <img src="https://img.icons8.com/color/96/000000/database-restore.png" width="100">
</div>

O sistema utiliza SQLite para armazenar:

- **📝 Sinais**: Todos os sinais gerados com detalhes completos
- **🔄 Atualizações**: Registros de take profit, stop loss e expiração
- **📊 Estatísticas**: Métricas de desempenho diárias

### 🔍 Comandos para Consulta

```bash
# Listar sinais recentes
python query_database.py signals

# Ver detalhes de um sinal específico
python query_database.py signal <id>

# Ver estatísticas diárias
python query_database.py stats
```

<br>

## 📊 Monitoramento

<div align="center">
  <img src="https://img.icons8.com/color/96/000000/monitoring.png" width="100">
</div>

O sistema monitora automaticamente os sinais abertos:

| Funcionalidade | Descrição |
|----------------|-----------|
| ⏱️ **Verificação Adaptativa** | 1 minuto para scalp, 5 minutos para outros sinais |
| 📈 **Detecção de Máximas/Mínimas** | Análise de candles desde a última verificação |
| 🛑 **Stop Loss Baseado em ATR** | Cálculo dinâmico baseado na volatilidade |
| 🎯 **Take Profit Adaptativo** | Níveis ajustados com base na volatilidade do ativo |
| ⏳ **Expiração Automática** | Tempo de validade variável por estratégia |

### 📈 Níveis de Take Profit para Scalp

<table>
  <tr>
    <th>Nível</th>
    <th>Percentual</th>
    <th>Status</th>
  </tr>
  <tr>
    <td>Nível 1</td>
    <td>25%</td>
    <td>✅</td>
  </tr>
  <tr>
    <td>Nível 2</td>
    <td>50%</td>
    <td>✅</td>
  </tr>
  <tr>
    <td>Nível 3</td>
    <td>75%</td>
    <td>✅</td>
  </tr>
  <tr>
    <td>Nível 4</td>
    <td>100%</td>
    <td>✅</td>
  </tr>
</table>

Os níveis percentuais são aplicados ao alvo de lucro, que é adaptativo com base na volatilidade:

| Volatilidade | Movimento Alvo |
|--------------|----------------|
| 📉 **Baixa** | 2% |
| 📊 **Média** | 3% |
| 📈 **Alta**  | 5% |

<br>

## ⚙️ Configurações

<div align="center">
  <img src="https://img.icons8.com/color/96/000000/settings.png" width="100">
</div>

O sistema utiliza um arquivo `.env` para configuração. Copie o arquivo `.env.example`:

```bash
cp .env.example .env
```

### ⚙️ Principais Configurações

| Configuração | Descrição |
|--------------|-----------|
| `MAX_SIGNALS_PER_DAY` | Limite máximo de sinais por dia |
| `SIGNAL_INTERVAL_SECONDS` | Intervalo entre verificações de novos sinais |
| `TRADING_START_HOUR` e `TRADING_END_HOUR` | Horário de operação do bot |
| `TELEGRAM_API_ID` e `TELEGRAM_API_HASH` | Credenciais da API do Telegram |
| `TELEGRAM_PHONE` | Número de telefone associado à conta do Telegram |
| `TELEGRAM_GROUP_ID` | ID do grupo para envio de sinais |

<br>

## 🚀 Instalação

<div align="center">
  <img src="https://img.icons8.com/color/96/000000/install.png" width="100">
</div>

### Pré-requisitos

- Python 3.7+
- Pip
- Conta Telegram

### Passos para Instalação

1. Clone o repositório:

```bash
git clone https://github.com/seu-usuario/gerador-sinais-telegram.git
cd gerador-sinais-telegram
```

2. Instale as dependências:

```bash
pip install -r requirements.txt
```

3. Configure o arquivo `.env` com suas credenciais

4. Execute o sistema:

```bash
python main.py
```

O sistema executará continuamente, gerando sinais e monitorando resultados.

<br>

## 🧪 Simulação

<div align="center">
  <img src="https://img.icons8.com/color/96/000000/test-tube.png" width="100">
</div>

Para testar o sistema sem depender de movimentos reais de mercado:

```bash
# Simular take profit para um sinal específico
python simulate_price_changes.py tp --signal <id>

# Simular stop loss para um sinal específico
python simulate_price_changes.py sl --signal <id>

# Simular mudanças aleatórias de preço para todos os sinais abertos
python simulate_price_changes.py random
```

<br>

## 📝 Logs

<div align="center">
  <img src="https://img.icons8.com/color/96/000000/log.png" width="100">
</div>

O sistema mantém logs detalhados em `gerador_sinais.log`, incluindo:

- 📝 Sinais gerados e salvos no banco de dados
- 🔄 Atualizações de take profit e stop loss
- ⏳ Expiração de sinais
- ⚠️ Erros e avisos
- 📱 Envio de mensagens e respostas

<br>

## 🔮 Melhorias Futuras

<div align="center">
  <img src="https://img.icons8.com/color/96/000000/idea.png" width="100">
</div>

O sistema está em constante evolução. Algumas melhorias planejadas incluem:

| Melhoria | Descrição |
|----------|-----------|
| 🔄 **Monitoramento em tempo real** | Implementação via websockets |
| 🛑 **Stop loss dinâmico** | Sistema de trailing stop |
| 🔍 **Otimização de ativos** | Filtros de liquidez e volatilidade |
| 📊 **Sistema de backtesting** | Avaliação de estratégias com dados históricos |
| 🔄 **Novas integrações** | Suporte a outras plataformas além do Telegram |

Para mais detalhes, consulte o arquivo `analise_atualizada.md`.

<br>

## 👥 Contribuição

<div align="center">
  <img src="https://img.icons8.com/color/96/000000/conference-call.png" width="100">
</div>

Sinta-se à vontade para contribuir com o projeto através de pull requests ou reportando issues.

1. Faça um Fork do projeto
2. Crie uma Branch para sua Feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a Branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

<br>

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo [LICENSE](LICENSE) para mais detalhes.

---

<div align="center">
  <sub>Desenvolvido com ❤️ para a comunidade de trading</sub>
</div>
