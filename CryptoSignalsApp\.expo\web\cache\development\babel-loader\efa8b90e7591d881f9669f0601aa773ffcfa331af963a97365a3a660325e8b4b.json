{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"disabled\", \"editable\", \"label\", \"error\", \"selectionColor\", \"cursorColor\", \"underlineColor\", \"underlineStyle\", \"activeUnderlineColor\", \"textColor\", \"dense\", \"style\", \"theme\", \"render\", \"multiline\", \"parentState\", \"innerRef\", \"onFocus\", \"forceFocus\", \"onBlur\", \"onChangeText\", \"onLayoutAnimatedText\", \"onLabelTextLayout\", \"onLeftAffixLayoutChange\", \"onRightAffixLayoutChange\", \"onInputLayout\", \"left\", \"right\", \"placeholderTextColor\", \"testID\", \"contentStyle\", \"scaledLabel\"],\n  _excluded2 = [\"fontSize\", \"lineHeight\", \"fontWeight\", \"height\", \"paddingHorizontal\", \"textAlign\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nimport * as React from 'react';\nimport I18nManager from \"react-native-web/dist/exports/I18nManager\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport NativeTextInput from \"react-native-web/dist/exports/TextInput\";\nimport View from \"react-native-web/dist/exports/View\";\nimport Animated from \"react-native-web/dist/exports/Animated\";\nimport { Underline } from \"./Addons/Underline\";\nimport { AdornmentSide, AdornmentType, InputMode } from \"./Adornment/enums\";\nimport TextInputAdornment from \"./Adornment/TextInputAdornment\";\nimport { getAdornmentConfig, getAdornmentStyleAdjustmentForNativeInput } from \"./Adornment/TextInputAdornment\";\nimport { ADORNMENT_SIZE, LABEL_PADDING_TOP_DENSE, LABEL_WIGGLE_X_OFFSET, MAXIMIZED_LABEL_FONT_SIZE, MINIMIZED_LABEL_FONT_SIZE, MINIMIZED_LABEL_Y_OFFSET, MIN_DENSE_HEIGHT, MIN_DENSE_HEIGHT_WL } from \"./constants\";\nimport { adjustPaddingFlat, calculateFlatAffixTopPosition, calculateFlatInputHorizontalPadding, calculateInputHeight, calculateLabelTopPosition, calculatePadding, getConstants, getFlatInputColors } from \"./helpers\";\nimport InputLabel from \"./Label/InputLabel\";\nvar TextInputFlat = function TextInputFlat(_ref) {\n  var _ref$disabled = _ref.disabled,\n    disabled = _ref$disabled === void 0 ? false : _ref$disabled,\n    _ref$editable = _ref.editable,\n    editable = _ref$editable === void 0 ? true : _ref$editable,\n    label = _ref.label,\n    _ref$error = _ref.error,\n    error = _ref$error === void 0 ? false : _ref$error,\n    customSelectionColor = _ref.selectionColor,\n    cursorColor = _ref.cursorColor,\n    underlineColor = _ref.underlineColor,\n    underlineStyle = _ref.underlineStyle,\n    activeUnderlineColor = _ref.activeUnderlineColor,\n    textColor = _ref.textColor,\n    dense = _ref.dense,\n    style = _ref.style,\n    theme = _ref.theme,\n    _ref$render = _ref.render,\n    render = _ref$render === void 0 ? function (props) {\n      return React.createElement(NativeTextInput, props);\n    } : _ref$render,\n    _ref$multiline = _ref.multiline,\n    multiline = _ref$multiline === void 0 ? false : _ref$multiline,\n    parentState = _ref.parentState,\n    innerRef = _ref.innerRef,\n    onFocus = _ref.onFocus,\n    forceFocus = _ref.forceFocus,\n    onBlur = _ref.onBlur,\n    onChangeText = _ref.onChangeText,\n    onLayoutAnimatedText = _ref.onLayoutAnimatedText,\n    onLabelTextLayout = _ref.onLabelTextLayout,\n    onLeftAffixLayoutChange = _ref.onLeftAffixLayoutChange,\n    onRightAffixLayoutChange = _ref.onRightAffixLayoutChange,\n    onInputLayout = _ref.onInputLayout,\n    left = _ref.left,\n    right = _ref.right,\n    placeholderTextColor = _ref.placeholderTextColor,\n    _ref$testID = _ref.testID,\n    testID = _ref$testID === void 0 ? 'text-input-flat' : _ref$testID,\n    contentStyle = _ref.contentStyle,\n    scaledLabel = _ref.scaledLabel,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var isAndroid = Platform.OS === 'android';\n  var colors = theme.colors,\n    isV3 = theme.isV3,\n    roundness = theme.roundness;\n  var font = isV3 ? theme.fonts.bodyLarge : theme.fonts.regular;\n  var hasActiveOutline = parentState.focused || error;\n  var _getConstants = getConstants(isV3),\n    LABEL_PADDING_TOP = _getConstants.LABEL_PADDING_TOP,\n    FLAT_INPUT_OFFSET = _getConstants.FLAT_INPUT_OFFSET,\n    MIN_HEIGHT = _getConstants.MIN_HEIGHT,\n    MIN_WIDTH = _getConstants.MIN_WIDTH;\n  var _ref2 = StyleSheet.flatten(style) || {},\n    fontSizeStyle = _ref2.fontSize,\n    lineHeightStyle = _ref2.lineHeight,\n    fontWeight = _ref2.fontWeight,\n    height = _ref2.height,\n    paddingHorizontal = _ref2.paddingHorizontal,\n    textAlign = _ref2.textAlign,\n    viewStyle = _objectWithoutProperties(_ref2, _excluded2);\n  var fontSize = fontSizeStyle || MAXIMIZED_LABEL_FONT_SIZE;\n  var lineHeight = lineHeightStyle || (Platform.OS === 'web' ? fontSize * 1.2 : undefined);\n  var isPaddingHorizontalPassed = paddingHorizontal !== undefined && typeof paddingHorizontal === 'number';\n  var adornmentConfig = getAdornmentConfig({\n    left: left,\n    right: right\n  });\n  var _calculateFlatInputHo = calculateFlatInputHorizontalPadding({\n      adornmentConfig: adornmentConfig,\n      isV3: isV3\n    }),\n    paddingLeft = _calculateFlatInputHo.paddingLeft,\n    paddingRight = _calculateFlatInputHo.paddingRight;\n  if (isPaddingHorizontalPassed) {\n    paddingLeft = paddingHorizontal;\n    paddingRight = paddingHorizontal;\n  }\n  var leftLayout = parentState.leftLayout,\n    rightLayout = parentState.rightLayout;\n  var rightAffixWidth = right ? rightLayout.width || ADORNMENT_SIZE : ADORNMENT_SIZE;\n  var leftAffixWidth = left ? leftLayout.width || ADORNMENT_SIZE : ADORNMENT_SIZE;\n  var adornmentStyleAdjustmentForNativeInput = getAdornmentStyleAdjustmentForNativeInput({\n    adornmentConfig: adornmentConfig,\n    rightAffixWidth: rightAffixWidth,\n    leftAffixWidth: leftAffixWidth,\n    paddingHorizontal: paddingHorizontal,\n    inputOffset: FLAT_INPUT_OFFSET,\n    mode: InputMode.Flat,\n    isV3: isV3\n  });\n  var _getFlatInputColors = getFlatInputColors({\n      underlineColor: underlineColor,\n      activeUnderlineColor: activeUnderlineColor,\n      customSelectionColor: customSelectionColor,\n      textColor: textColor,\n      disabled: disabled,\n      error: error,\n      theme: theme\n    }),\n    inputTextColor = _getFlatInputColors.inputTextColor,\n    activeColor = _getFlatInputColors.activeColor,\n    underlineColorCustom = _getFlatInputColors.underlineColorCustom,\n    placeholderColor = _getFlatInputColors.placeholderColor,\n    errorColor = _getFlatInputColors.errorColor,\n    backgroundColor = _getFlatInputColors.backgroundColor,\n    selectionColor = _getFlatInputColors.selectionColor;\n  var containerStyle = {\n    backgroundColor: backgroundColor,\n    borderTopLeftRadius: theme.roundness,\n    borderTopRightRadius: theme.roundness\n  };\n  var labelScale = MINIMIZED_LABEL_FONT_SIZE / fontSize;\n  var fontScale = MAXIMIZED_LABEL_FONT_SIZE / fontSize;\n  var labelWidth = parentState.labelLayout.width;\n  var labelHeight = parentState.labelLayout.height;\n  var labelHalfWidth = labelWidth / 2;\n  var labelHalfHeight = labelHeight / 2;\n  var baseLabelTranslateX = (I18nManager.getConstants().isRTL ? 1 : -1) * (labelHalfWidth - labelScale * labelWidth / 2) + (1 - labelScale) * (I18nManager.getConstants().isRTL ? -1 : 1) * paddingLeft;\n  var minInputHeight = dense ? (label ? MIN_DENSE_HEIGHT_WL : MIN_DENSE_HEIGHT) - LABEL_PADDING_TOP_DENSE : MIN_HEIGHT - LABEL_PADDING_TOP;\n  var inputHeight = calculateInputHeight(labelHeight, height, minInputHeight);\n  var topPosition = calculateLabelTopPosition(labelHeight, inputHeight, multiline && height ? 0 : !height ? minInputHeight / 2 : 0);\n  if (height && typeof height !== 'number') {\n    console.warn('Currently we support only numbers in height prop');\n  }\n  var paddingSettings = {\n    height: height ? +height : null,\n    labelHalfHeight: labelHalfHeight,\n    offset: FLAT_INPUT_OFFSET,\n    multiline: multiline ? multiline : null,\n    dense: dense ? dense : null,\n    topPosition: topPosition,\n    fontSize: fontSize,\n    lineHeight: lineHeight,\n    label: label,\n    scale: fontScale,\n    isAndroid: isAndroid,\n    styles: StyleSheet.flatten(dense ? styles.inputFlatDense : styles.inputFlat)\n  };\n  var pad = calculatePadding(paddingSettings);\n  var paddingFlat = adjustPaddingFlat(_objectSpread(_objectSpread({}, paddingSettings), {}, {\n    pad: pad\n  }));\n  var baseLabelTranslateY = -labelHalfHeight - (topPosition + MINIMIZED_LABEL_Y_OFFSET);\n  var _React$useRef = React.useRef([new Animated.Value(0), new Animated.Value(1)]),\n    placeholderOpacityAnims = _React$useRef.current;\n  var placeholderOpacity = hasActiveOutline ? parentState.labeled : placeholderOpacityAnims[parentState.labelLayout.measured ? 1 : 0];\n  var placeholderTextColorBasedOnState = parentState.displayPlaceholder ? placeholderTextColor != null ? placeholderTextColor : placeholderColor : 'transparent';\n  var minHeight = height || (dense ? label ? MIN_DENSE_HEIGHT_WL : MIN_DENSE_HEIGHT : MIN_HEIGHT);\n  var flatHeight = inputHeight + (!height ? dense ? LABEL_PADDING_TOP_DENSE : LABEL_PADDING_TOP : 0);\n  var iconTopPosition = (flatHeight - ADORNMENT_SIZE) / 2;\n  var leftAffixTopPosition = leftLayout.height ? calculateFlatAffixTopPosition(_objectSpread(_objectSpread({\n    height: flatHeight\n  }, paddingFlat), {}, {\n    affixHeight: leftLayout.height\n  })) : null;\n  var rightAffixTopPosition = rightLayout.height ? calculateFlatAffixTopPosition(_objectSpread(_objectSpread({\n    height: flatHeight\n  }, paddingFlat), {}, {\n    affixHeight: rightLayout.height\n  })) : null;\n  var labelProps = {\n    label: label,\n    onLayoutAnimatedText: onLayoutAnimatedText,\n    onLabelTextLayout: onLabelTextLayout,\n    placeholderOpacity: placeholderOpacity,\n    labelError: error,\n    placeholderStyle: styles.placeholder,\n    baseLabelTranslateY: baseLabelTranslateY,\n    baseLabelTranslateX: baseLabelTranslateX,\n    font: font,\n    fontSize: fontSize,\n    lineHeight: lineHeight,\n    fontWeight: fontWeight,\n    labelScale: labelScale,\n    wiggleOffsetX: LABEL_WIGGLE_X_OFFSET,\n    topPosition: topPosition,\n    paddingLeft: isAndroid ? I18nManager.isRTL ? paddingRight : paddingLeft : paddingLeft,\n    paddingRight: isAndroid ? I18nManager.isRTL ? paddingLeft : paddingRight : paddingRight,\n    hasActiveOutline: hasActiveOutline,\n    activeColor: activeColor,\n    placeholderColor: placeholderColor,\n    errorColor: errorColor,\n    roundness: roundness,\n    maxFontSizeMultiplier: rest.maxFontSizeMultiplier,\n    testID: testID,\n    contentStyle: contentStyle,\n    inputContainerLayout: parentState.inputContainerLayout,\n    labelTextLayout: parentState.labelTextLayout,\n    opacity: parentState.value || parentState.focused ? parentState.labelLayout.measured ? 1 : 0 : 1,\n    isV3: isV3\n  };\n  var affixTopPosition = _defineProperty(_defineProperty({}, AdornmentSide.Left, leftAffixTopPosition), AdornmentSide.Right, rightAffixTopPosition);\n  var onAffixChange = _defineProperty(_defineProperty({}, AdornmentSide.Left, onLeftAffixLayoutChange), AdornmentSide.Right, onRightAffixLayoutChange);\n  var adornmentProps = {\n    paddingHorizontal: paddingHorizontal,\n    adornmentConfig: adornmentConfig,\n    forceFocus: forceFocus,\n    topPosition: _defineProperty(_defineProperty({}, AdornmentType.Affix, affixTopPosition), AdornmentType.Icon, iconTopPosition),\n    onAffixChange: onAffixChange,\n    isTextInputFocused: parentState.focused,\n    maxFontSizeMultiplier: rest.maxFontSizeMultiplier,\n    disabled: disabled\n  };\n  if (adornmentConfig.length) {\n    adornmentProps = _objectSpread(_objectSpread({}, adornmentProps), {}, {\n      left: left,\n      right: right,\n      textStyle: _objectSpread(_objectSpread({}, font), {}, {\n        fontSize: fontSize,\n        lineHeight: lineHeight,\n        fontWeight: fontWeight\n      }),\n      visible: parentState.labeled\n    });\n  }\n  return React.createElement(View, {\n    style: [containerStyle, viewStyle]\n  }, React.createElement(Underline, {\n    style: underlineStyle,\n    hasActiveOutline: hasActiveOutline,\n    parentState: parentState,\n    underlineColorCustom: underlineColorCustom,\n    error: error,\n    colors: colors,\n    activeColor: activeColor,\n    theme: theme\n  }), React.createElement(View, {\n    onLayout: onInputLayout,\n    style: [styles.labelContainer, {\n      minHeight: minHeight\n    }]\n  }, !isAndroid && multiline && !!label && !disabled && React.createElement(View, {\n    testID: \"patch-container\",\n    pointerEvents: \"none\",\n    style: [StyleSheet.absoluteFill, dense ? styles.densePatchContainer : styles.patchContainer, {\n      backgroundColor: viewStyle.backgroundColor || containerStyle.backgroundColor,\n      left: paddingLeft,\n      right: paddingRight\n    }]\n  }), label ? React.createElement(InputLabel, _extends({\n    labeled: parentState.labeled,\n    error: parentState.error,\n    focused: parentState.focused,\n    scaledLabel: scaledLabel,\n    wiggle: Boolean(parentState.value && labelProps.labelError),\n    labelLayoutMeasured: parentState.labelLayout.measured,\n    labelLayoutWidth: parentState.labelLayout.width,\n    labelLayoutHeight: parentState.labelLayout.height\n  }, labelProps)) : null, render === null || render === void 0 ? void 0 : render(_objectSpread(_objectSpread({}, rest), {}, {\n    ref: innerRef,\n    onChangeText: onChangeText,\n    placeholder: rest.placeholder,\n    editable: !disabled && editable,\n    selectionColor: selectionColor,\n    cursorColor: typeof cursorColor === 'undefined' ? activeColor : cursorColor,\n    placeholderTextColor: placeholderTextColorBasedOnState,\n    onFocus: onFocus,\n    onBlur: onBlur,\n    underlineColorAndroid: 'transparent',\n    multiline: multiline,\n    style: [styles.input, multiline && height ? {\n      height: flatHeight\n    } : {}, paddingFlat, _objectSpread(_objectSpread({\n      paddingLeft: paddingLeft,\n      paddingRight: paddingRight\n    }, font), {}, {\n      fontSize: fontSize,\n      lineHeight: lineHeight,\n      fontWeight: fontWeight,\n      color: inputTextColor,\n      textAlignVertical: multiline ? 'top' : 'center',\n      textAlign: textAlign ? textAlign : I18nManager.getConstants().isRTL ? 'right' : 'left',\n      minWidth: Math.min(parentState.labelTextLayout.width + 2 * FLAT_INPUT_OFFSET, MIN_WIDTH)\n    }), Platform.OS === 'web' ? {\n      outline: 'none'\n    } : undefined, adornmentStyleAdjustmentForNativeInput, contentStyle],\n    testID: testID\n  }))), React.createElement(TextInputAdornment, adornmentProps));\n};\nexport default TextInputFlat;\nvar styles = StyleSheet.create({\n  placeholder: {\n    position: 'absolute',\n    left: 0\n  },\n  labelContainer: {\n    paddingTop: 0,\n    paddingBottom: 0,\n    flexGrow: 1\n  },\n  input: {\n    margin: 0,\n    flexGrow: 1\n  },\n  inputFlat: {\n    paddingTop: 24,\n    paddingBottom: 4\n  },\n  inputFlatDense: {\n    paddingTop: 22,\n    paddingBottom: 2\n  },\n  patchContainer: {\n    height: 24,\n    zIndex: 2\n  },\n  densePatchContainer: {\n    height: 22,\n    zIndex: 2\n  }\n});", "map": {"version": 3, "names": ["React", "I18nManager", "Platform", "StyleSheet", "NativeTextInput", "View", "Animated", "Underline", "AdornmentSide", "AdornmentType", "InputMode", "TextInputAdornment", "getAdornmentConfig", "getAdornmentStyleAdjustmentForNativeInput", "ADORNMENT_SIZE", "LABEL_PADDING_TOP_DENSE", "LABEL_WIGGLE_X_OFFSET", "MAXIMIZED_LABEL_FONT_SIZE", "MINIMIZED_LABEL_FONT_SIZE", "MINIMIZED_LABEL_Y_OFFSET", "MIN_DENSE_HEIGHT", "MIN_DENSE_HEIGHT_WL", "adjustPaddingFlat", "calculateFlatAffixTopPosition", "calculateFlatInputHorizontalPadding", "calculateInputHeight", "calculateLabelTopPosition", "calculatePadding", "getConstants", "getFlatInputColors", "InputLabel", "TextInputFlat", "_ref", "_ref$disabled", "disabled", "_ref$editable", "editable", "label", "_ref$error", "error", "customSelectionColor", "selectionColor", "cursorColor", "underlineColor", "underlineStyle", "activeUnderlineColor", "textColor", "dense", "style", "theme", "_ref$render", "render", "props", "createElement", "_ref$multiline", "multiline", "parentState", "innerRef", "onFocus", "forceFocus", "onBlur", "onChangeText", "onLayoutAnimatedText", "onLabelTextLayout", "onLeftAffixLayoutChange", "onRightAffixLayoutChange", "onInputLayout", "left", "right", "placeholderTextColor", "_ref$testID", "testID", "contentStyle", "scaledLabel", "rest", "_objectWithoutProperties", "_excluded", "isAndroid", "OS", "colors", "isV3", "roundness", "font", "fonts", "bodyLarge", "regular", "hasActiveOutline", "focused", "_getConstants", "LABEL_PADDING_TOP", "FLAT_INPUT_OFFSET", "MIN_HEIGHT", "MIN_WIDTH", "_ref2", "flatten", "fontSizeStyle", "fontSize", "lineHeightStyle", "lineHeight", "fontWeight", "height", "paddingHorizontal", "textAlign", "viewStyle", "_excluded2", "undefined", "isPaddingHorizontalPassed", "adornmentConfig", "_calculateFlatInputHo", "paddingLeft", "paddingRight", "leftLayout", "rightLayout", "rightAffix<PERSON>idth", "width", "leftAffixWidth", "adornmentStyleAdjustmentForNativeInput", "inputOffset", "mode", "Flat", "_getFlatInputColors", "inputTextColor", "activeColor", "underlineColorCustom", "placeholderColor", "errorColor", "backgroundColor", "containerStyle", "borderTopLeftRadius", "borderTopRightRadius", "labelScale", "fontScale", "labelWidth", "labelLayout", "labelHeight", "labelHalfWidth", "labelHalfHeight", "baseLabelTranslateX", "isRTL", "minInputHeight", "inputHeight", "topPosition", "console", "warn", "paddingSettings", "offset", "scale", "styles", "inputFlatDense", "inputFlat", "pad", "paddingFlat", "_objectSpread", "baseLabelTranslateY", "_React$useRef", "useRef", "Value", "placeholderOpacityAnims", "current", "placeholderOpacity", "labeled", "measured", "placeholderTextColorBasedOnState", "displayPlaceholder", "minHeight", "flatHeight", "iconTopPosition", "leftAffixTopPosition", "affixHeight", "rightAffixTopPosition", "labelProps", "labelError", "placeholder<PERSON><PERSON><PERSON>", "placeholder", "wiggleOffsetX", "maxFontSizeMultiplier", "inputContainerLayout", "labelTextLayout", "opacity", "value", "affixTopPosition", "_defineProperty", "Left", "Right", "onAffixChange", "adornmentProps", "Affix", "Icon", "isTextInputFocused", "length", "textStyle", "visible", "onLayout", "labelContainer", "pointerEvents", "absoluteFill", "densePatchContainer", "patchContainer", "_extends", "wiggle", "Boolean", "labelLayoutMeasured", "labelLayoutWidth", "labelLayoutHeight", "ref", "underlineColorAndroid", "input", "color", "textAlignVertical", "min<PERSON><PERSON><PERSON>", "Math", "min", "outline", "create", "position", "paddingTop", "paddingBottom", "flexGrow", "margin", "zIndex"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\TextInput\\TextInputFlat.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  I18nManager,\n  Platform,\n  StyleSheet,\n  TextInput as NativeTextInput,\n  TextStyle,\n  View,\n  Animated,\n} from 'react-native';\n\nimport { Underline } from './Addons/Underline';\nimport { AdornmentSide, AdornmentType, InputMode } from './Adornment/enums';\nimport TextInputAdornment, {\n  TextInputAdornmentProps,\n} from './Adornment/TextInputAdornment';\nimport {\n  getAdornmentConfig,\n  getAdornmentStyleAdjustmentForNativeInput,\n} from './Adornment/TextInputAdornment';\nimport {\n  ADORNMENT_SIZE,\n  LABEL_PADDING_TOP_DENSE,\n  LABEL_WIGGLE_X_OFFSET,\n  MAXIMIZED_LABEL_FONT_SIZE,\n  MINIMIZED_LABEL_FONT_SIZE,\n  MINIMIZED_LABEL_Y_OFFSET,\n  MIN_DENSE_HEIGHT,\n  MIN_DENSE_HEIGHT_WL,\n} from './constants';\nimport {\n  adjustPaddingFlat,\n  calculateFlatAffixTopPosition,\n  calculateFlatInputHorizontalPadding,\n  calculateInputHeight,\n  calculateLabelTopPosition,\n  calculatePadding,\n  getConstants,\n  getFlatInputColors,\n  Padding,\n} from './helpers';\nimport InputLabel from './Label/InputLabel';\nimport type { ChildTextInputProps, RenderProps } from './types';\n\nconst TextInputFlat = ({\n  disabled = false,\n  editable = true,\n  label,\n  error = false,\n  selectionColor: customSelectionColor,\n  cursorColor,\n  underlineColor,\n  underlineStyle,\n  activeUnderlineColor,\n  textColor,\n  dense,\n  style,\n  theme,\n  render = (props: RenderProps) => <NativeTextInput {...props} />,\n  multiline = false,\n  parentState,\n  innerRef,\n  onFocus,\n  forceFocus,\n  onBlur,\n  onChangeText,\n  onLayoutAnimatedText,\n  onLabelTextLayout,\n  onLeftAffixLayoutChange,\n  onRightAffixLayoutChange,\n  onInputLayout,\n  left,\n  right,\n  placeholderTextColor,\n  testID = 'text-input-flat',\n  contentStyle,\n  scaledLabel,\n  ...rest\n}: ChildTextInputProps) => {\n  const isAndroid = Platform.OS === 'android';\n  const { colors, isV3, roundness } = theme;\n  const font = isV3 ? theme.fonts.bodyLarge : theme.fonts.regular;\n  const hasActiveOutline = parentState.focused || error;\n\n  const { LABEL_PADDING_TOP, FLAT_INPUT_OFFSET, MIN_HEIGHT, MIN_WIDTH } =\n    getConstants(isV3);\n\n  const {\n    fontSize: fontSizeStyle,\n    lineHeight: lineHeightStyle,\n    fontWeight,\n    height,\n    paddingHorizontal,\n    textAlign,\n    ...viewStyle\n  } = (StyleSheet.flatten(style) || {}) as TextStyle;\n  const fontSize = fontSizeStyle || MAXIMIZED_LABEL_FONT_SIZE;\n  const lineHeight =\n    lineHeightStyle || (Platform.OS === 'web' ? fontSize * 1.2 : undefined);\n\n  const isPaddingHorizontalPassed =\n    paddingHorizontal !== undefined && typeof paddingHorizontal === 'number';\n\n  const adornmentConfig = getAdornmentConfig({\n    left,\n    right,\n  });\n\n  let { paddingLeft, paddingRight } = calculateFlatInputHorizontalPadding({\n    adornmentConfig,\n    isV3,\n  });\n\n  if (isPaddingHorizontalPassed) {\n    paddingLeft = paddingHorizontal as number;\n    paddingRight = paddingHorizontal as number;\n  }\n\n  const { leftLayout, rightLayout } = parentState;\n\n  const rightAffixWidth = right\n    ? rightLayout.width || ADORNMENT_SIZE\n    : ADORNMENT_SIZE;\n\n  const leftAffixWidth = left\n    ? leftLayout.width || ADORNMENT_SIZE\n    : ADORNMENT_SIZE;\n\n  const adornmentStyleAdjustmentForNativeInput =\n    getAdornmentStyleAdjustmentForNativeInput({\n      adornmentConfig,\n      rightAffixWidth,\n      leftAffixWidth,\n      paddingHorizontal,\n      inputOffset: FLAT_INPUT_OFFSET,\n      mode: InputMode.Flat,\n      isV3,\n    });\n\n  const {\n    inputTextColor,\n    activeColor,\n    underlineColorCustom,\n    placeholderColor,\n    errorColor,\n    backgroundColor,\n    selectionColor,\n  } = getFlatInputColors({\n    underlineColor,\n    activeUnderlineColor,\n    customSelectionColor,\n    textColor,\n    disabled,\n    error,\n    theme,\n  });\n\n  const containerStyle = {\n    backgroundColor,\n    borderTopLeftRadius: theme.roundness,\n    borderTopRightRadius: theme.roundness,\n  };\n\n  const labelScale = MINIMIZED_LABEL_FONT_SIZE / fontSize;\n  const fontScale = MAXIMIZED_LABEL_FONT_SIZE / fontSize;\n\n  const labelWidth = parentState.labelLayout.width;\n  const labelHeight = parentState.labelLayout.height;\n  const labelHalfWidth = labelWidth / 2;\n  const labelHalfHeight = labelHeight / 2;\n\n  const baseLabelTranslateX =\n    (I18nManager.getConstants().isRTL ? 1 : -1) *\n      (labelHalfWidth - (labelScale * labelWidth) / 2) +\n    (1 - labelScale) *\n      (I18nManager.getConstants().isRTL ? -1 : 1) *\n      paddingLeft;\n\n  const minInputHeight = dense\n    ? (label ? MIN_DENSE_HEIGHT_WL : MIN_DENSE_HEIGHT) - LABEL_PADDING_TOP_DENSE\n    : MIN_HEIGHT - LABEL_PADDING_TOP;\n\n  const inputHeight = calculateInputHeight(labelHeight, height, minInputHeight);\n\n  const topPosition = calculateLabelTopPosition(\n    labelHeight,\n    inputHeight,\n    multiline && height ? 0 : !height ? minInputHeight / 2 : 0\n  );\n\n  if (height && typeof height !== 'number') {\n    // eslint-disable-next-line\n    console.warn('Currently we support only numbers in height prop');\n  }\n\n  const paddingSettings = {\n    height: height ? +height : null,\n    labelHalfHeight,\n    offset: FLAT_INPUT_OFFSET,\n    multiline: multiline ? multiline : null,\n    dense: dense ? dense : null,\n    topPosition,\n    fontSize,\n    lineHeight,\n    label,\n    scale: fontScale,\n    isAndroid,\n    styles: StyleSheet.flatten(\n      dense ? styles.inputFlatDense : styles.inputFlat\n    ) as Padding,\n  };\n\n  const pad = calculatePadding(paddingSettings);\n\n  const paddingFlat = adjustPaddingFlat({\n    ...paddingSettings,\n    pad,\n  });\n\n  const baseLabelTranslateY =\n    -labelHalfHeight - (topPosition + MINIMIZED_LABEL_Y_OFFSET);\n\n  const { current: placeholderOpacityAnims } = React.useRef([\n    new Animated.Value(0),\n    new Animated.Value(1),\n  ]);\n\n  const placeholderOpacity = hasActiveOutline\n    ? parentState.labeled\n    : placeholderOpacityAnims[parentState.labelLayout.measured ? 1 : 0];\n\n  // We don't want to show placeholder if label is displayed, because they overlap.\n  // Before it was done by setting placeholder's value to \" \", but inputs have the same props\n  // what causes broken styles due to: https://github.com/facebook/react-native/issues/48249\n  const placeholderTextColorBasedOnState = parentState.displayPlaceholder\n    ? placeholderTextColor ?? placeholderColor\n    : 'transparent';\n\n  const minHeight =\n    height ||\n    (dense ? (label ? MIN_DENSE_HEIGHT_WL : MIN_DENSE_HEIGHT) : MIN_HEIGHT);\n\n  const flatHeight =\n    inputHeight +\n    (!height ? (dense ? LABEL_PADDING_TOP_DENSE : LABEL_PADDING_TOP) : 0);\n\n  const iconTopPosition = (flatHeight - ADORNMENT_SIZE) / 2;\n\n  const leftAffixTopPosition = leftLayout.height\n    ? calculateFlatAffixTopPosition({\n        height: flatHeight,\n        ...paddingFlat,\n        affixHeight: leftLayout.height,\n      })\n    : null;\n\n  const rightAffixTopPosition = rightLayout.height\n    ? calculateFlatAffixTopPosition({\n        height: flatHeight,\n        ...paddingFlat,\n        affixHeight: rightLayout.height,\n      })\n    : null;\n\n  const labelProps = {\n    label,\n    onLayoutAnimatedText,\n    onLabelTextLayout,\n    placeholderOpacity,\n    labelError: error,\n    placeholderStyle: styles.placeholder,\n    baseLabelTranslateY,\n    baseLabelTranslateX,\n    font,\n    fontSize,\n    lineHeight,\n    fontWeight,\n    labelScale,\n    wiggleOffsetX: LABEL_WIGGLE_X_OFFSET,\n    topPosition,\n    paddingLeft: isAndroid\n      ? I18nManager.isRTL\n        ? paddingRight\n        : paddingLeft\n      : paddingLeft,\n    paddingRight: isAndroid\n      ? I18nManager.isRTL\n        ? paddingLeft\n        : paddingRight\n      : paddingRight,\n    hasActiveOutline,\n    activeColor,\n    placeholderColor,\n    errorColor,\n    roundness,\n    maxFontSizeMultiplier: rest.maxFontSizeMultiplier,\n    testID,\n    contentStyle,\n    inputContainerLayout: parentState.inputContainerLayout,\n    labelTextLayout: parentState.labelTextLayout,\n    opacity:\n      parentState.value || parentState.focused\n        ? parentState.labelLayout.measured\n          ? 1\n          : 0\n        : 1,\n    isV3,\n  };\n\n  const affixTopPosition = {\n    [AdornmentSide.Left]: leftAffixTopPosition,\n    [AdornmentSide.Right]: rightAffixTopPosition,\n  };\n  const onAffixChange = {\n    [AdornmentSide.Left]: onLeftAffixLayoutChange,\n    [AdornmentSide.Right]: onRightAffixLayoutChange,\n  };\n\n  let adornmentProps: TextInputAdornmentProps = {\n    paddingHorizontal,\n    adornmentConfig,\n    forceFocus,\n    topPosition: {\n      [AdornmentType.Affix]: affixTopPosition,\n      [AdornmentType.Icon]: iconTopPosition,\n    },\n    onAffixChange,\n    isTextInputFocused: parentState.focused,\n    maxFontSizeMultiplier: rest.maxFontSizeMultiplier,\n    disabled,\n  };\n  if (adornmentConfig.length) {\n    adornmentProps = {\n      ...adornmentProps,\n      left,\n      right,\n      textStyle: { ...font, fontSize, lineHeight, fontWeight },\n      visible: parentState.labeled,\n    };\n  }\n\n  return (\n    <View style={[containerStyle, viewStyle]}>\n      <Underline\n        style={underlineStyle}\n        hasActiveOutline={hasActiveOutline}\n        parentState={parentState}\n        underlineColorCustom={underlineColorCustom}\n        error={error}\n        colors={colors}\n        activeColor={activeColor}\n        theme={theme}\n      />\n      <View\n        onLayout={onInputLayout}\n        style={[\n          styles.labelContainer,\n          {\n            minHeight,\n          },\n        ]}\n      >\n        {!isAndroid && multiline && !!label && !disabled && (\n          // Workaround for: https://github.com/callstack/react-native-paper/issues/2799\n          // Patch for a multiline TextInput with fixed height, which allow to avoid covering input label with its value.\n          <View\n            testID=\"patch-container\"\n            pointerEvents=\"none\"\n            style={[\n              StyleSheet.absoluteFill,\n              dense ? styles.densePatchContainer : styles.patchContainer,\n              {\n                backgroundColor:\n                  viewStyle.backgroundColor || containerStyle.backgroundColor,\n                left: paddingLeft,\n                right: paddingRight,\n              },\n            ]}\n          />\n        )}\n        {label ? (\n          <InputLabel\n            labeled={parentState.labeled}\n            error={parentState.error}\n            focused={parentState.focused}\n            scaledLabel={scaledLabel}\n            wiggle={Boolean(parentState.value && labelProps.labelError)}\n            labelLayoutMeasured={parentState.labelLayout.measured}\n            labelLayoutWidth={parentState.labelLayout.width}\n            labelLayoutHeight={parentState.labelLayout.height}\n            {...labelProps}\n          />\n        ) : null}\n        {render?.({\n          ...rest,\n          ref: innerRef,\n          onChangeText,\n          placeholder: rest.placeholder,\n          editable: !disabled && editable,\n          selectionColor,\n          cursorColor:\n            typeof cursorColor === 'undefined' ? activeColor : cursorColor,\n          placeholderTextColor: placeholderTextColorBasedOnState,\n          onFocus,\n          onBlur,\n          underlineColorAndroid: 'transparent',\n          multiline,\n          style: [\n            styles.input,\n            multiline && height ? { height: flatHeight } : {},\n            paddingFlat,\n            {\n              paddingLeft,\n              paddingRight,\n              ...font,\n              fontSize,\n              lineHeight,\n              fontWeight,\n              color: inputTextColor,\n              textAlignVertical: multiline ? 'top' : 'center',\n              textAlign: textAlign\n                ? textAlign\n                : I18nManager.getConstants().isRTL\n                ? 'right'\n                : 'left',\n              minWidth: Math.min(\n                parentState.labelTextLayout.width + 2 * FLAT_INPUT_OFFSET,\n                MIN_WIDTH\n              ),\n            },\n            Platform.OS === 'web' ? { outline: 'none' } : undefined,\n            adornmentStyleAdjustmentForNativeInput,\n            contentStyle,\n          ],\n          testID,\n        })}\n      </View>\n      <TextInputAdornment {...adornmentProps} />\n    </View>\n  );\n};\n\nexport default TextInputFlat;\n\nconst styles = StyleSheet.create({\n  placeholder: {\n    position: 'absolute',\n    left: 0,\n  },\n  labelContainer: {\n    paddingTop: 0,\n    paddingBottom: 0,\n    flexGrow: 1,\n  },\n  input: {\n    margin: 0,\n    flexGrow: 1,\n  },\n  inputFlat: {\n    paddingTop: 24,\n    paddingBottom: 4,\n  },\n  inputFlatDense: {\n    paddingTop: 22,\n    paddingBottom: 2,\n  },\n  patchContainer: {\n    height: 24,\n    zIndex: 2,\n  },\n  densePatchContainer: {\n    height: 22,\n    zIndex: 2,\n  },\n});\n"], "mappings": ";;;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,WAAA;AAAA,OAAAC,QAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,eAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,QAAA;AAW9B,SAASC,SAAS;AAClB,SAASC,aAAa,EAAEC,aAAa,EAAEC,SAAS;AAChD,OAAOC,kBAAkB;AAGzB,SACEC,kBAAkB,EAClBC,yCAAyC;AAE3C,SACEC,cAAc,EACdC,uBAAuB,EACvBC,qBAAqB,EACrBC,yBAAyB,EACzBC,yBAAyB,EACzBC,wBAAwB,EACxBC,gBAAgB,EAChBC,mBAAmB;AAErB,SACEC,iBAAiB,EACjBC,6BAA6B,EAC7BC,mCAAmC,EACnCC,oBAAoB,EACpBC,yBAAyB,EACzBC,gBAAgB,EAChBC,YAAY,EACZC,kBAAkB;AAGpB,OAAOC,UAAU;AAGjB,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAAC,IAAA,EAkCQ;EAAA,IAAAC,aAAA,GAAAD,IAAA,CAjCzBE,QAAQ;IAARA,QAAQ,GAAAD,aAAA,cAAG,KAAK,GAAAA,aAAA;IAAAE,aAAA,GAAAH,IAAA,CAChBI,QAAQ;IAARA,QAAQ,GAAAD,aAAA,cAAG,IAAI,GAAAA,aAAA;IACfE,KAAK,GAAAL,IAAA,CAALK,KAAK;IAAAC,UAAA,GAAAN,IAAA,CACLO,KAAK;IAALA,KAAK,GAAAD,UAAA,cAAG,KAAK,GAAAA,UAAA;IACGE,oBAAoB,GAAAR,IAAA,CAApCS,cAAc;IACdC,WAAW,GAAAV,IAAA,CAAXU,WAAW;IACXC,cAAc,GAAAX,IAAA,CAAdW,cAAc;IACdC,cAAc,GAAAZ,IAAA,CAAdY,cAAc;IACdC,oBAAoB,GAAAb,IAAA,CAApBa,oBAAoB;IACpBC,SAAS,GAAAd,IAAA,CAATc,SAAS;IACTC,KAAK,GAAAf,IAAA,CAALe,KAAK;IACLC,KAAK,GAAAhB,IAAA,CAALgB,KAAK;IACLC,KAAK,GAAAjB,IAAA,CAALiB,KAAK;IAAAC,WAAA,GAAAlB,IAAA,CACLmB,MAAM;IAANA,MAAM,GAAAD,WAAA,cAAI,UAAAE,KAAkB;MAAA,OAAKpD,KAAA,CAAAqD,aAAA,CAACjD,eAAe,EAAKgD,KAAQ,CAAC;IAAA,IAAAF,WAAA;IAAAI,cAAA,GAAAtB,IAAA,CAC/DuB,SAAS;IAATA,SAAS,GAAAD,cAAA,cAAG,KAAK,GAAAA,cAAA;IACjBE,WAAW,GAAAxB,IAAA,CAAXwB,WAAW;IACXC,QAAQ,GAAAzB,IAAA,CAARyB,QAAQ;IACRC,OAAO,GAAA1B,IAAA,CAAP0B,OAAO;IACPC,UAAU,GAAA3B,IAAA,CAAV2B,UAAU;IACVC,MAAM,GAAA5B,IAAA,CAAN4B,MAAM;IACNC,YAAY,GAAA7B,IAAA,CAAZ6B,YAAY;IACZC,oBAAoB,GAAA9B,IAAA,CAApB8B,oBAAoB;IACpBC,iBAAiB,GAAA/B,IAAA,CAAjB+B,iBAAiB;IACjBC,uBAAuB,GAAAhC,IAAA,CAAvBgC,uBAAuB;IACvBC,wBAAwB,GAAAjC,IAAA,CAAxBiC,wBAAwB;IACxBC,aAAa,GAAAlC,IAAA,CAAbkC,aAAa;IACbC,IAAI,GAAAnC,IAAA,CAAJmC,IAAI;IACJC,KAAK,GAAApC,IAAA,CAALoC,KAAK;IACLC,oBAAoB,GAAArC,IAAA,CAApBqC,oBAAoB;IAAAC,WAAA,GAAAtC,IAAA,CACpBuC,MAAM;IAANA,MAAM,GAAAD,WAAA,cAAG,iBAAiB,GAAAA,WAAA;IAC1BE,YAAY,GAAAxC,IAAA,CAAZwC,YAAY;IACZC,WAAW,GAAAzC,IAAA,CAAXyC,WAAW;IACRC,IAAA,GAAAC,wBAAA,CAAA3C,IAAA,EAAA4C,SAAA;EAEH,IAAMC,SAAS,GAAG3E,QAAQ,CAAC4E,EAAE,KAAK,SAAS;EAC3C,IAAQC,MAAM,GAAsB9B,KAAK,CAAjC8B,MAAM;IAAEC,IAAI,GAAgB/B,KAAK,CAAzB+B,IAAI;IAAEC,SAAA,GAAchC,KAAK,CAAnBgC,SAAA;EACtB,IAAMC,IAAI,GAAGF,IAAI,GAAG/B,KAAK,CAACkC,KAAK,CAACC,SAAS,GAAGnC,KAAK,CAACkC,KAAK,CAACE,OAAO;EAC/D,IAAMC,gBAAgB,GAAG9B,WAAW,CAAC+B,OAAO,IAAIhD,KAAK;EAErD,IAAAiD,aAAA,GACE5D,YAAY,CAACoD,IAAI,CAAC;IADZS,iBAAiB,GAAAD,aAAA,CAAjBC,iBAAiB;IAAEC,iBAAiB,GAAAF,aAAA,CAAjBE,iBAAiB;IAAEC,UAAU,GAAAH,aAAA,CAAVG,UAAU;IAAEC,SAAA,GAAAJ,aAAA,CAAAI,SAAA;EAG1D,IAAAC,KAAA,GAQK1F,UAAU,CAAC2F,OAAO,CAAC9C,KAAK,CAAC,IAAI,CAAC,CAAe;IAPtC+C,aAAa,GAAAF,KAAA,CAAvBG,QAAQ;IACIC,eAAe,GAAAJ,KAAA,CAA3BK,UAAU;IACVC,UAAU,GAAAN,KAAA,CAAVM,UAAU;IACVC,MAAM,GAAAP,KAAA,CAANO,MAAM;IACNC,iBAAiB,GAAAR,KAAA,CAAjBQ,iBAAiB;IACjBC,SAAS,GAAAT,KAAA,CAATS,SAAS;IACNC,SAAA,GAAA5B,wBAAA,CAAAkB,KAAA,EAAAW,UAAA;EAEL,IAAMR,QAAQ,GAAGD,aAAa,IAAI9E,yBAAyB;EAC3D,IAAMiF,UAAU,GACdD,eAAe,KAAK/F,QAAQ,CAAC4E,EAAE,KAAK,KAAK,GAAGkB,QAAQ,GAAG,GAAG,GAAGS,SAAS,CAAC;EAEzE,IAAMC,yBAAyB,GAC7BL,iBAAiB,KAAKI,SAAS,IAAI,OAAOJ,iBAAiB,KAAK,QAAQ;EAE1E,IAAMM,eAAe,GAAG/F,kBAAkB,CAAC;IACzCuD,IAAI,EAAJA,IAAI;IACJC,KAAA,EAAAA;EACF,CAAC,CAAC;EAEF,IAAAwC,qBAAA,GAAoCpF,mCAAmC,CAAC;MACtEmF,eAAe,EAAfA,eAAe;MACf3B,IAAA,EAAAA;IACF,CAAC,CAAC;IAHI6B,WAAW,GAAAD,qBAAA,CAAXC,WAAW;IAAEC,YAAA,GAAAF,qBAAA,CAAAE,YAAA;EAKnB,IAAIJ,yBAAyB,EAAE;IAC7BG,WAAW,GAAGR,iBAA2B;IACzCS,YAAY,GAAGT,iBAA2B;EAC5C;EAEA,IAAQU,UAAU,GAAkBvD,WAAW,CAAvCuD,UAAU;IAAEC,WAAA,GAAgBxD,WAAW,CAA3BwD,WAAA;EAEpB,IAAMC,eAAe,GAAG7C,KAAK,GACzB4C,WAAW,CAACE,KAAK,IAAIpG,cAAc,GACnCA,cAAc;EAElB,IAAMqG,cAAc,GAAGhD,IAAI,GACvB4C,UAAU,CAACG,KAAK,IAAIpG,cAAc,GAClCA,cAAc;EAElB,IAAMsG,sCAAsC,GAC1CvG,yCAAyC,CAAC;IACxC8F,eAAe,EAAfA,eAAe;IACfM,eAAe,EAAfA,eAAe;IACfE,cAAc,EAAdA,cAAc;IACdd,iBAAiB,EAAjBA,iBAAiB;IACjBgB,WAAW,EAAE3B,iBAAiB;IAC9B4B,IAAI,EAAE5G,SAAS,CAAC6G,IAAI;IACpBvC,IAAA,EAAAA;EACF,CAAC,CAAC;EAEJ,IAAAwC,mBAAA,GAQI3F,kBAAkB,CAAC;MACrBc,cAAc,EAAdA,cAAc;MACdE,oBAAoB,EAApBA,oBAAoB;MACpBL,oBAAoB,EAApBA,oBAAoB;MACpBM,SAAS,EAATA,SAAS;MACTZ,QAAQ,EAARA,QAAQ;MACRK,KAAK,EAALA,KAAK;MACLU,KAAA,EAAAA;IACF,CAAC,CAAC;IAfAwE,cAAc,GAAAD,mBAAA,CAAdC,cAAc;IACdC,WAAW,GAAAF,mBAAA,CAAXE,WAAW;IACXC,oBAAoB,GAAAH,mBAAA,CAApBG,oBAAoB;IACpBC,gBAAgB,GAAAJ,mBAAA,CAAhBI,gBAAgB;IAChBC,UAAU,GAAAL,mBAAA,CAAVK,UAAU;IACVC,eAAe,GAAAN,mBAAA,CAAfM,eAAe;IACfrF,cAAA,GAAA+E,mBAAA,CAAA/E,cAAA;EAWF,IAAMsF,cAAc,GAAG;IACrBD,eAAe,EAAfA,eAAe;IACfE,mBAAmB,EAAE/E,KAAK,CAACgC,SAAS;IACpCgD,oBAAoB,EAAEhF,KAAK,CAACgC;EAC9B,CAAC;EAED,IAAMiD,UAAU,GAAGhH,yBAAyB,GAAG8E,QAAQ;EACvD,IAAMmC,SAAS,GAAGlH,yBAAyB,GAAG+E,QAAQ;EAEtD,IAAMoC,UAAU,GAAG5E,WAAW,CAAC6E,WAAW,CAACnB,KAAK;EAChD,IAAMoB,WAAW,GAAG9E,WAAW,CAAC6E,WAAW,CAACjC,MAAM;EAClD,IAAMmC,cAAc,GAAGH,UAAU,GAAG,CAAC;EACrC,IAAMI,eAAe,GAAGF,WAAW,GAAG,CAAC;EAEvC,IAAMG,mBAAmB,GACvB,CAACxI,WAAW,CAAC2B,YAAY,CAAC,CAAC,CAAC8G,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,KACvCH,cAAc,GAAIL,UAAU,GAAGE,UAAU,GAAI,CAAC,CAAC,GAClD,CAAC,CAAC,GAAGF,UAAU,KACZjI,WAAW,CAAC2B,YAAY,CAAC,CAAC,CAAC8G,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAC3C7B,WAAW;EAEf,IAAM8B,cAAc,GAAG5F,KAAK,GACxB,CAACV,KAAK,GAAGhB,mBAAmB,GAAGD,gBAAgB,IAAIL,uBAAuB,GAC1E4E,UAAU,GAAGF,iBAAiB;EAElC,IAAMmD,WAAW,GAAGnH,oBAAoB,CAAC6G,WAAW,EAAElC,MAAM,EAAEuC,cAAc,CAAC;EAE7E,IAAME,WAAW,GAAGnH,yBAAyB,CAC3C4G,WAAW,EACXM,WAAW,EACXrF,SAAS,IAAI6C,MAAM,GAAG,CAAC,GAAG,CAACA,MAAM,GAAGuC,cAAc,GAAG,CAAC,GAAG,CAC3D,CAAC;EAED,IAAIvC,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;IAExC0C,OAAO,CAACC,IAAI,CAAC,kDAAkD,CAAC;EAClE;EAEA,IAAMC,eAAe,GAAG;IACtB5C,MAAM,EAAEA,MAAM,GAAG,CAACA,MAAM,GAAG,IAAI;IAC/BoC,eAAe,EAAfA,eAAe;IACfS,MAAM,EAAEvD,iBAAiB;IACzBnC,SAAS,EAAEA,SAAS,GAAGA,SAAS,GAAG,IAAI;IACvCR,KAAK,EAAEA,KAAK,GAAGA,KAAK,GAAG,IAAI;IAC3B8F,WAAW,EAAXA,WAAW;IACX7C,QAAQ,EAARA,QAAQ;IACRE,UAAU,EAAVA,UAAU;IACV7D,KAAK,EAALA,KAAK;IACL6G,KAAK,EAAEf,SAAS;IAChBtD,SAAS,EAATA,SAAS;IACTsE,MAAM,EAAEhJ,UAAU,CAAC2F,OAAO,CACxB/C,KAAK,GAAGoG,MAAM,CAACC,cAAc,GAAGD,MAAM,CAACE,SACzC;EACF,CAAC;EAED,IAAMC,GAAG,GAAG3H,gBAAgB,CAACqH,eAAe,CAAC;EAE7C,IAAMO,WAAW,GAAGjI,iBAAiB,CAAAkI,aAAA,CAAAA,aAAA,KAChCR,eAAe;IAClBM,GAAA,EAAAA;EAAA,EACD,CAAC;EAEF,IAAMG,mBAAmB,GACvB,CAACjB,eAAe,IAAIK,WAAW,GAAG1H,wBAAwB,CAAC;EAE7D,IAAAuI,aAAA,GAA6C1J,KAAK,CAAC2J,MAAM,CAAC,CACxD,IAAIrJ,QAAQ,CAACsJ,KAAK,CAAC,CAAC,CAAC,EACrB,IAAItJ,QAAQ,CAACsJ,KAAK,CAAC,CAAC,CAAC,CACtB,CAAC;IAHeC,uBAAA,GAAAH,aAAA,CAATI,OAAO;EAKf,IAAMC,kBAAkB,GAAGzE,gBAAgB,GACvC9B,WAAW,CAACwG,OAAO,GACnBH,uBAAuB,CAACrG,WAAW,CAAC6E,WAAW,CAAC4B,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC;EAKrE,IAAMC,gCAAgC,GAAG1G,WAAW,CAAC2G,kBAAkB,GACnE9F,oBAAoB,WAApBA,oBAAoB,GAAIuD,gBAAgB,GACxC,aAAa;EAEjB,IAAMwC,SAAS,GACbhE,MAAM,KACLrD,KAAK,GAAIV,KAAK,GAAGhB,mBAAmB,GAAGD,gBAAgB,GAAIuE,UAAU,CAAC;EAEzE,IAAM0E,UAAU,GACdzB,WAAW,IACV,CAACxC,MAAM,GAAIrD,KAAK,GAAGhC,uBAAuB,GAAG0E,iBAAiB,GAAI,CAAC,CAAC;EAEvE,IAAM6E,eAAe,GAAG,CAACD,UAAU,GAAGvJ,cAAc,IAAI,CAAC;EAEzD,IAAMyJ,oBAAoB,GAAGxD,UAAU,CAACX,MAAM,GAC1C7E,6BAA6B,CAAAiI,aAAA,CAAAA,aAAA;IAC3BpD,MAAM,EAAEiE;EAAU,GACfd,WAAW;IACdiB,WAAW,EAAEzD,UAAU,CAACX;EAAA,EACzB,CAAC,GACF,IAAI;EAER,IAAMqE,qBAAqB,GAAGzD,WAAW,CAACZ,MAAM,GAC5C7E,6BAA6B,CAAAiI,aAAA,CAAAA,aAAA;IAC3BpD,MAAM,EAAEiE;EAAU,GACfd,WAAW;IACdiB,WAAW,EAAExD,WAAW,CAACZ;EAAA,EAC1B,CAAC,GACF,IAAI;EAER,IAAMsE,UAAU,GAAG;IACjBrI,KAAK,EAALA,KAAK;IACLyB,oBAAoB,EAApBA,oBAAoB;IACpBC,iBAAiB,EAAjBA,iBAAiB;IACjBgG,kBAAkB,EAAlBA,kBAAkB;IAClBY,UAAU,EAAEpI,KAAK;IACjBqI,gBAAgB,EAAEzB,MAAM,CAAC0B,WAAW;IACpCpB,mBAAmB,EAAnBA,mBAAmB;IACnBhB,mBAAmB,EAAnBA,mBAAmB;IACnBvD,IAAI,EAAJA,IAAI;IACJc,QAAQ,EAARA,QAAQ;IACRE,UAAU,EAAVA,UAAU;IACVC,UAAU,EAAVA,UAAU;IACV+B,UAAU,EAAVA,UAAU;IACV4C,aAAa,EAAE9J,qBAAqB;IACpC6H,WAAW,EAAXA,WAAW;IACXhC,WAAW,EAAEhC,SAAS,GAClB5E,WAAW,CAACyI,KAAK,GACf5B,YAAY,GACZD,WAAW,GACbA,WAAW;IACfC,YAAY,EAAEjC,SAAS,GACnB5E,WAAW,CAACyI,KAAK,GACf7B,WAAW,GACXC,YAAY,GACdA,YAAY;IAChBxB,gBAAgB,EAAhBA,gBAAgB;IAChBoC,WAAW,EAAXA,WAAW;IACXE,gBAAgB,EAAhBA,gBAAgB;IAChBC,UAAU,EAAVA,UAAU;IACV5C,SAAS,EAATA,SAAS;IACT8F,qBAAqB,EAAErG,IAAI,CAACqG,qBAAqB;IACjDxG,MAAM,EAANA,MAAM;IACNC,YAAY,EAAZA,YAAY;IACZwG,oBAAoB,EAAExH,WAAW,CAACwH,oBAAoB;IACtDC,eAAe,EAAEzH,WAAW,CAACyH,eAAe;IAC5CC,OAAO,EACL1H,WAAW,CAAC2H,KAAK,IAAI3H,WAAW,CAAC+B,OAAO,GACpC/B,WAAW,CAAC6E,WAAW,CAAC4B,QAAQ,GAC9B,CAAC,GACD,CAAC,GACH,CAAC;IACPjF,IAAA,EAAAA;EACF,CAAC;EAED,IAAMoG,gBAAgB,GAAAC,eAAA,CAAAA,eAAA,KACnB7K,aAAa,CAAC8K,IAAI,EAAGf,oBAAoB,GACzC/J,aAAa,CAAC+K,KAAK,EAAGd,qBAAA,CACxB;EACD,IAAMe,aAAa,GAAAH,eAAA,CAAAA,eAAA,KAChB7K,aAAa,CAAC8K,IAAI,EAAGtH,uBAAuB,GAC5CxD,aAAa,CAAC+K,KAAK,EAAGtH,wBAAA,CACxB;EAED,IAAIwH,cAAuC,GAAG;IAC5CpF,iBAAiB,EAAjBA,iBAAiB;IACjBM,eAAe,EAAfA,eAAe;IACfhD,UAAU,EAAVA,UAAU;IACVkF,WAAW,EAAAwC,eAAA,CAAAA,eAAA,KACR5K,aAAa,CAACiL,KAAK,EAAGN,gBAAgB,GACtC3K,aAAa,CAACkL,IAAI,EAAGrB,eAAA,CACvB;IACDkB,aAAa,EAAbA,aAAa;IACbI,kBAAkB,EAAEpI,WAAW,CAAC+B,OAAO;IACvCwF,qBAAqB,EAAErG,IAAI,CAACqG,qBAAqB;IACjD7I,QAAA,EAAAA;EACF,CAAC;EACD,IAAIyE,eAAe,CAACkF,MAAM,EAAE;IAC1BJ,cAAc,GAAAjC,aAAA,CAAAA,aAAA,KACTiC,cAAc;MACjBtH,IAAI,EAAJA,IAAI;MACJC,KAAK,EAALA,KAAK;MACL0H,SAAS,EAAAtC,aAAA,CAAAA,aAAA,KAAOtE,IAAI;QAAEc,QAAQ,EAARA,QAAQ;QAAEE,UAAU,EAAVA,UAAU;QAAEC,UAAA,EAAAA;MAAA,EAAY;MACxD4F,OAAO,EAAEvI,WAAW,CAACwG;IAAA,EACtB;EACH;EAEA,OACEhK,KAAA,CAAAqD,aAAA,CAAChD,IAAI;IAAC2C,KAAK,EAAE,CAAC+E,cAAc,EAAExB,SAAS;EAAE,GACvCvG,KAAA,CAAAqD,aAAA,CAAC9C,SAAS;IACRyC,KAAK,EAAEJ,cAAe;IACtB0C,gBAAgB,EAAEA,gBAAiB;IACnC9B,WAAW,EAAEA,WAAY;IACzBmE,oBAAoB,EAAEA,oBAAqB;IAC3CpF,KAAK,EAAEA,KAAM;IACbwC,MAAM,EAAEA,MAAO;IACf2C,WAAW,EAAEA,WAAY;IACzBzE,KAAK,EAAEA;EAAM,CACd,CAAC,EACFjD,KAAA,CAAAqD,aAAA,CAAChD,IAAI;IACH2L,QAAQ,EAAE9H,aAAc;IACxBlB,KAAK,EAAE,CACLmG,MAAM,CAAC8C,cAAc,EACrB;MACE7B,SAAA,EAAAA;IACF,CAAC;EACD,GAED,CAACvF,SAAS,IAAItB,SAAS,IAAI,CAAC,CAAClB,KAAK,IAAI,CAACH,QAAQ,IAG9ClC,KAAA,CAAAqD,aAAA,CAAChD,IAAI;IACHkE,MAAM,EAAC,iBAAiB;IACxB2H,aAAa,EAAC,MAAM;IACpBlJ,KAAK,EAAE,CACL7C,UAAU,CAACgM,YAAY,EACvBpJ,KAAK,GAAGoG,MAAM,CAACiD,mBAAmB,GAAGjD,MAAM,CAACkD,cAAc,EAC1D;MACEvE,eAAe,EACbvB,SAAS,CAACuB,eAAe,IAAIC,cAAc,CAACD,eAAe;MAC7D3D,IAAI,EAAE0C,WAAW;MACjBzC,KAAK,EAAE0C;IACT,CAAC;EACD,CACH,CACF,EACAzE,KAAK,GACJrC,KAAA,CAAAqD,aAAA,CAACvB,UAAU,EAAAwK,QAAA;IACTtC,OAAO,EAAExG,WAAW,CAACwG,OAAQ;IAC7BzH,KAAK,EAAEiB,WAAW,CAACjB,KAAM;IACzBgD,OAAO,EAAE/B,WAAW,CAAC+B,OAAQ;IAC7Bd,WAAW,EAAEA,WAAY;IACzB8H,MAAM,EAAEC,OAAO,CAAChJ,WAAW,CAAC2H,KAAK,IAAIT,UAAU,CAACC,UAAU,CAAE;IAC5D8B,mBAAmB,EAAEjJ,WAAW,CAAC6E,WAAW,CAAC4B,QAAS;IACtDyC,gBAAgB,EAAElJ,WAAW,CAAC6E,WAAW,CAACnB,KAAM;IAChDyF,iBAAiB,EAAEnJ,WAAW,CAAC6E,WAAW,CAACjC;EAAO,GAC9CsE,UAAU,CACf,CAAC,GACA,IAAI,EACPvH,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAAqG,aAAA,CAAAA,aAAA,KACF9E,IAAI;IACPkI,GAAG,EAAEnJ,QAAQ;IACbI,YAAY,EAAZA,YAAY;IACZgH,WAAW,EAAEnG,IAAI,CAACmG,WAAW;IAC7BzI,QAAQ,EAAE,CAACF,QAAQ,IAAIE,QAAQ;IAC/BK,cAAc,EAAdA,cAAc;IACdC,WAAW,EACT,OAAOA,WAAW,KAAK,WAAW,GAAGgF,WAAW,GAAGhF,WAAW;IAChE2B,oBAAoB,EAAE6F,gCAAgC;IACtDxG,OAAO,EAAPA,OAAO;IACPE,MAAM,EAANA,MAAM;IACNiJ,qBAAqB,EAAE,aAAa;IACpCtJ,SAAS,EAATA,SAAS;IACTP,KAAK,EAAE,CACLmG,MAAM,CAAC2D,KAAK,EACZvJ,SAAS,IAAI6C,MAAM,GAAG;MAAEA,MAAM,EAAEiE;IAAW,CAAC,GAAG,CAAC,CAAC,EACjDd,WAAW,EAAAC,aAAA,CAAAA,aAAA;MAET3C,WAAW,EAAXA,WAAW;MACXC,YAAY,EAAZA;IAAY,GACT5B,IAAI;MACPc,QAAQ,EAARA,QAAQ;MACRE,UAAU,EAAVA,UAAU;MACVC,UAAU,EAAVA,UAAU;MACV4G,KAAK,EAAEtF,cAAc;MACrBuF,iBAAiB,EAAEzJ,SAAS,GAAG,KAAK,GAAG,QAAQ;MAC/C+C,SAAS,EAAEA,SAAS,GAChBA,SAAS,GACTrG,WAAW,CAAC2B,YAAY,CAAC,CAAC,CAAC8G,KAAK,GAChC,OAAO,GACP,MAAM;MACVuE,QAAQ,EAAEC,IAAI,CAACC,GAAG,CAChB3J,WAAW,CAACyH,eAAe,CAAC/D,KAAK,GAAG,CAAC,GAAGxB,iBAAiB,EACzDE,SACF;IAAA,IAEF1F,QAAQ,CAAC4E,EAAE,KAAK,KAAK,GAAG;MAAEsI,OAAO,EAAE;IAAO,CAAC,GAAG3G,SAAS,EACvDW,sCAAsC,EACtC5C,YAAY,CACb;IACDD,MAAA,EAAAA;EAAA,EACD,CACG,CAAC,EACPvE,KAAA,CAAAqD,aAAA,CAAC1C,kBAAkB,EAAK8K,cAAiB,CACrC,CAAC;AAEX,CAAC;AAED,eAAe1J,aAAa;AAE5B,IAAMoH,MAAM,GAAGhJ,UAAU,CAACkN,MAAM,CAAC;EAC/BxC,WAAW,EAAE;IACXyC,QAAQ,EAAE,UAAU;IACpBnJ,IAAI,EAAE;EACR,CAAC;EACD8H,cAAc,EAAE;IACdsB,UAAU,EAAE,CAAC;IACbC,aAAa,EAAE,CAAC;IAChBC,QAAQ,EAAE;EACZ,CAAC;EACDX,KAAK,EAAE;IACLY,MAAM,EAAE,CAAC;IACTD,QAAQ,EAAE;EACZ,CAAC;EACDpE,SAAS,EAAE;IACTkE,UAAU,EAAE,EAAE;IACdC,aAAa,EAAE;EACjB,CAAC;EACDpE,cAAc,EAAE;IACdmE,UAAU,EAAE,EAAE;IACdC,aAAa,EAAE;EACjB,CAAC;EACDnB,cAAc,EAAE;IACdjG,MAAM,EAAE,EAAE;IACVuH,MAAM,EAAE;EACV,CAAC;EACDvB,mBAAmB,EAAE;IACnBhG,MAAM,EAAE,EAAE;IACVuH,MAAM,EAAE;EACV;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}