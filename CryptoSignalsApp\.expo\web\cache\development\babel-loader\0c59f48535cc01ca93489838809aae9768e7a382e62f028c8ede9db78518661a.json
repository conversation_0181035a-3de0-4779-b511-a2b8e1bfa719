{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"source\", \"color\", \"size\", \"theme\", \"testID\"];\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nimport * as React from 'react';\nimport I18nManager from \"react-native-web/dist/exports/I18nManager\";\nimport Image from \"react-native-web/dist/exports/Image\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport { accessibilityProps } from \"./MaterialCommunityIcon\";\nimport { Consumer as SettingsConsumer } from \"../core/settings\";\nimport { useInternalTheme } from \"../core/theming\";\nvar isImageSource = function isImageSource(source) {\n  return (typeof source === 'object' && source !== null && Object.prototype.hasOwnProperty.call(source, 'uri') && typeof source.uri === 'string' || typeof source === 'number' || Platform.OS === 'web' && typeof source === 'string' && (source.startsWith('data:image') || /\\.(bmp|jpg|jpeg|png|gif|svg)$/.test(source))\n  );\n};\nvar getIconId = function getIconId(source) {\n  if (typeof source === 'object' && source !== null && Object.prototype.hasOwnProperty.call(source, 'uri') && typeof source.uri === 'string') {\n    return source.uri;\n  }\n  return source;\n};\nexport var isValidIcon = function isValidIcon(source) {\n  return typeof source === 'string' || typeof source === 'function' || isImageSource(source);\n};\nexport var isEqualIcon = function isEqualIcon(a, b) {\n  return a === b || getIconId(a) === getIconId(b);\n};\nvar Icon = function Icon(_ref) {\n  var source = _ref.source,\n    color = _ref.color,\n    size = _ref.size,\n    themeOverrides = _ref.theme,\n    testID = _ref.testID,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var theme = useInternalTheme(themeOverrides);\n  var direction = typeof source === 'object' && source.direction && source.source ? source.direction === 'auto' ? I18nManager.getConstants().isRTL ? 'rtl' : 'ltr' : source.direction : null;\n  var s = typeof source === 'object' && source.direction && source.source ? source.source : source;\n  var iconColor = color || (theme.isV3 ? theme.colors.onSurface : theme.colors.text);\n  if (isImageSource(s)) {\n    return React.createElement(Image, _extends({}, rest, {\n      testID: testID,\n      source: s,\n      style: [{\n        transform: [{\n          scaleX: direction === 'rtl' ? -1 : 1\n        }]\n      }, {\n        width: size,\n        height: size,\n        tintColor: color,\n        resizeMode: `contain`\n      }]\n    }, accessibilityProps, {\n      accessibilityIgnoresInvertColors: true\n    }));\n  } else if (typeof s === 'string') {\n    return React.createElement(SettingsConsumer, null, function (_ref2) {\n      var icon = _ref2.icon;\n      return icon === null || icon === void 0 ? void 0 : icon({\n        name: s,\n        color: iconColor,\n        size: size,\n        direction: direction,\n        testID: testID\n      });\n    });\n  } else if (typeof s === 'function') {\n    return s({\n      color: iconColor,\n      size: size,\n      direction: direction,\n      testID: testID\n    });\n  }\n  return null;\n};\nexport default Icon;", "map": {"version": 3, "names": ["React", "I18nManager", "Image", "Platform", "accessibilityProps", "Consumer", "SettingsConsumer", "useInternalTheme", "isImageSource", "source", "Object", "prototype", "hasOwnProperty", "call", "uri", "OS", "startsWith", "test", "getIconId", "isValidIcon", "isEqualIcon", "a", "b", "Icon", "_ref", "color", "size", "themeOverrides", "theme", "testID", "rest", "_objectWithoutProperties", "_excluded", "direction", "getConstants", "isRTL", "s", "iconColor", "isV3", "colors", "onSurface", "text", "createElement", "_extends", "style", "transform", "scaleX", "width", "height", "tintColor", "resizeMode", "accessibilityIgnoresInvertColors", "_ref2", "icon", "name"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\Icon.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  I18nManager,\n  Image,\n  ImageSourcePropType,\n  Platform,\n} from 'react-native';\n\nimport { accessibilityProps } from './MaterialCommunityIcon';\nimport { Consumer as SettingsConsumer } from '../core/settings';\nimport { useInternalTheme } from '../core/theming';\nimport type { ThemeProp } from '../types';\n\ntype IconSourceBase = string | ImageSourcePropType;\n\nexport type IconSource =\n  | IconSourceBase\n  | Readonly<{ source: IconSourceBase; direction: 'rtl' | 'ltr' | 'auto' }>\n  | ((props: IconProps & { color: string }) => React.ReactNode);\n\ntype IconProps = {\n  /**\n   * Size of icon.\n   */\n  size: number;\n  allowFontScaling?: boolean;\n};\n\nconst isImageSource = (source: any) =>\n  // source is an object with uri\n  (typeof source === 'object' &&\n    source !== null &&\n    Object.prototype.hasOwnProperty.call(source, 'uri') &&\n    typeof source.uri === 'string') ||\n  // source is a module, e.g. - require('image')\n  typeof source === 'number' ||\n  // image url on web\n  (Platform.OS === 'web' &&\n    typeof source === 'string' &&\n    (source.startsWith('data:image') ||\n      /\\.(bmp|jpg|jpeg|png|gif|svg)$/.test(source)));\n\nconst getIconId = (source: any) => {\n  if (\n    typeof source === 'object' &&\n    source !== null &&\n    Object.prototype.hasOwnProperty.call(source, 'uri') &&\n    typeof source.uri === 'string'\n  ) {\n    return source.uri;\n  }\n\n  return source;\n};\n\nexport const isValidIcon = (source: any) =>\n  typeof source === 'string' ||\n  typeof source === 'function' ||\n  isImageSource(source);\n\nexport const isEqualIcon = (a: any, b: any) =>\n  a === b || getIconId(a) === getIconId(b);\n\nexport type Props = IconProps & {\n  /**\n   * Icon to display.\n   */\n  source: any;\n  /**\n   * Color of the icon.\n   */\n  color?: string;\n  /**\n   * TestID used for testing purposes\n   */\n  testID?: string;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n};\n\n/**\n * An icon component which renders icon from vector library.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { Icon, MD3Colors } from 'react-native-paper';\n *\n * const MyComponent = () => (\n *   <Icon\n *     source=\"camera\"\n *     color={MD3Colors.error50}\n *     size={20}\n *   />\n * );\n *\n * export default MyComponent;\n * ```\n */\n\nconst Icon = ({\n  source,\n  color,\n  size,\n  theme: themeOverrides,\n  testID,\n  ...rest\n}: Props) => {\n  const theme = useInternalTheme(themeOverrides);\n  const direction =\n    typeof source === 'object' && source.direction && source.source\n      ? source.direction === 'auto'\n        ? I18nManager.getConstants().isRTL\n          ? 'rtl'\n          : 'ltr'\n        : source.direction\n      : null;\n\n  const s =\n    typeof source === 'object' && source.direction && source.source\n      ? source.source\n      : source;\n  const iconColor =\n    color || (theme.isV3 ? theme.colors.onSurface : theme.colors.text);\n\n  if (isImageSource(s)) {\n    return (\n      <Image\n        {...rest}\n        testID={testID}\n        source={s}\n        style={[\n          {\n            transform: [{ scaleX: direction === 'rtl' ? -1 : 1 }],\n          },\n          {\n            width: size,\n            height: size,\n            tintColor: color,\n            resizeMode: `contain`,\n          },\n        ]}\n        {...accessibilityProps}\n        accessibilityIgnoresInvertColors\n      />\n    );\n  } else if (typeof s === 'string') {\n    return (\n      <SettingsConsumer>\n        {({ icon }) => {\n          return icon?.({\n            name: s,\n            color: iconColor,\n            size,\n            direction,\n            testID,\n          });\n        }}\n      </SettingsConsumer>\n    );\n  } else if (typeof s === 'function') {\n    return s({ color: iconColor, size, direction, testID });\n  }\n\n  return null;\n};\n\nexport default Icon;\n"], "mappings": ";;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,WAAA;AAAA,OAAAC,KAAA;AAAA,OAAAC,QAAA;AAQ9B,SAASC,kBAAkB;AAC3B,SAASC,QAAQ,IAAIC,gBAAgB;AACrC,SAASC,gBAAgB;AAkBzB,IAAMC,aAAa,GAAI,SAAjBA,aAAaA,CAAIC,MAAW;EAAA,QAE/B,OAAOA,MAAM,KAAK,QAAQ,IACzBA,MAAM,KAAK,IAAI,IACfC,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,MAAM,EAAE,KAAK,CAAC,IACnD,OAAOA,MAAM,CAACK,GAAG,KAAK,QAAQ,IAEhC,OAAOL,MAAM,KAAK,QAAQ,IAEzBN,QAAQ,CAACY,EAAE,KAAK,KAAK,IACpB,OAAON,MAAM,KAAK,QAAQ,KACzBA,MAAM,CAACO,UAAU,CAAC,YAAY,CAAC,IAC9B,+BAA+B,CAACC,IAAI,CAACR,MAAM,CAAC;EAAE;AAAA;AAEpD,IAAMS,SAAS,GAAI,SAAbA,SAASA,CAAIT,MAAW,EAAK;EACjC,IACE,OAAOA,MAAM,KAAK,QAAQ,IAC1BA,MAAM,KAAK,IAAI,IACfC,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,MAAM,EAAE,KAAK,CAAC,IACnD,OAAOA,MAAM,CAACK,GAAG,KAAK,QAAQ,EAC9B;IACA,OAAOL,MAAM,CAACK,GAAG;EACnB;EAEA,OAAOL,MAAM;AACf,CAAC;AAED,OAAO,IAAMU,WAAW,GAAI,SAAfA,WAAWA,CAAIV,MAAW;EAAA,OACrC,OAAOA,MAAM,KAAK,QAAQ,IAC1B,OAAOA,MAAM,KAAK,UAAU,IAC5BD,aAAa,CAACC,MAAM,CAAC;AAAA;AAEvB,OAAO,IAAMW,WAAW,GAAG,SAAdA,WAAWA,CAAIC,CAAM,EAAEC,CAAM;EAAA,OACxCD,CAAC,KAAKC,CAAC,IAAIJ,SAAS,CAACG,CAAC,CAAC,KAAKH,SAAS,CAACI,CAAC,CAAC;AAAA;AAyC1C,IAAMC,IAAI,GAAG,SAAPA,IAAIA,CAAAC,IAAA,EAOG;EAAA,IANXf,MAAM,GAAAe,IAAA,CAANf,MAAM;IACNgB,KAAK,GAAAD,IAAA,CAALC,KAAK;IACLC,IAAI,GAAAF,IAAA,CAAJE,IAAI;IACGC,cAAc,GAAAH,IAAA,CAArBI,KAAK;IACLC,MAAM,GAAAL,IAAA,CAANK,MAAM;IACHC,IAAA,GAAAC,wBAAA,CAAAP,IAAA,EAAAQ,SAAA;EAEH,IAAMJ,KAAK,GAAGrB,gBAAgB,CAACoB,cAAc,CAAC;EAC9C,IAAMM,SAAS,GACb,OAAOxB,MAAM,KAAK,QAAQ,IAAIA,MAAM,CAACwB,SAAS,IAAIxB,MAAM,CAACA,MAAM,GAC3DA,MAAM,CAACwB,SAAS,KAAK,MAAM,GACzBhC,WAAW,CAACiC,YAAY,CAAC,CAAC,CAACC,KAAK,GAC9B,KAAK,GACL,KAAK,GACP1B,MAAM,CAACwB,SAAS,GAClB,IAAI;EAEV,IAAMG,CAAC,GACL,OAAO3B,MAAM,KAAK,QAAQ,IAAIA,MAAM,CAACwB,SAAS,IAAIxB,MAAM,CAACA,MAAM,GAC3DA,MAAM,CAACA,MAAM,GACbA,MAAM;EACZ,IAAM4B,SAAS,GACbZ,KAAK,KAAKG,KAAK,CAACU,IAAI,GAAGV,KAAK,CAACW,MAAM,CAACC,SAAS,GAAGZ,KAAK,CAACW,MAAM,CAACE,IAAI,CAAC;EAEpE,IAAIjC,aAAa,CAAC4B,CAAC,CAAC,EAAE;IACpB,OACEpC,KAAA,CAAA0C,aAAA,CAACxC,KAAK,EAAAyC,QAAA,KACAb,IAAI;MACRD,MAAM,EAAEA,MAAO;MACfpB,MAAM,EAAE2B,CAAE;MACVQ,KAAK,EAAE,CACL;QACEC,SAAS,EAAE,CAAC;UAAEC,MAAM,EAAEb,SAAS,KAAK,KAAK,GAAG,CAAC,CAAC,GAAG;QAAE,CAAC;MACtD,CAAC,EACD;QACEc,KAAK,EAAErB,IAAI;QACXsB,MAAM,EAAEtB,IAAI;QACZuB,SAAS,EAAExB,KAAK;QAChByB,UAAU,EAAE;MACd,CAAC;IACD,GACE9C,kBAAkB;MACtB+C,gCAAgC;IAAA,EACjC,CAAC;EAEN,CAAC,MAAM,IAAI,OAAOf,CAAC,KAAK,QAAQ,EAAE;IAChC,OACEpC,KAAA,CAAA0C,aAAA,CAACpC,gBAAgB,QACd,UAAA8C,KAAA,EAAc;MAAA,IAAXC,IAAA,GAAAD,KAAA,CAAAC,IAAA;MACF,OAAOA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAG;QACZC,IAAI,EAAElB,CAAC;QACPX,KAAK,EAAEY,SAAS;QAChBX,IAAI,EAAJA,IAAI;QACJO,SAAS,EAATA,SAAS;QACTJ,MAAA,EAAAA;MACF,CAAC,CAAC;IACJ,CACgB,CAAC;EAEvB,CAAC,MAAM,IAAI,OAAOO,CAAC,KAAK,UAAU,EAAE;IAClC,OAAOA,CAAC,CAAC;MAAEX,KAAK,EAAEY,SAAS;MAAEX,IAAI,EAAJA,IAAI;MAAEO,SAAS,EAATA,SAAS;MAAEJ,MAAA,EAAAA;IAAO,CAAC,CAAC;EACzD;EAEA,OAAO,IAAI;AACb,CAAC;AAED,eAAeN,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}