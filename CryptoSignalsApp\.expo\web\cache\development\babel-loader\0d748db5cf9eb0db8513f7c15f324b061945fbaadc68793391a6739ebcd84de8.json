{"ast": null, "code": "export default function memoize(callback) {\n  var previous;\n  var result;\n  return function () {\n    var hasChanged = false;\n    for (var _len = arguments.length, dependencies = new Array(_len), _key = 0; _key < _len; _key++) {\n      dependencies[_key] = arguments[_key];\n    }\n    if (previous) {\n      if (previous.length !== dependencies.length) {\n        hasChanged = true;\n      } else {\n        for (var i = 0; i < previous.length; i++) {\n          if (previous[i] !== dependencies[i]) {\n            hasChanged = true;\n            break;\n          }\n        }\n      }\n    } else {\n      hasChanged = true;\n    }\n    previous = dependencies;\n    if (hasChanged || result === undefined) {\n      result = callback.apply(void 0, dependencies);\n    }\n    return result;\n  };\n}", "map": {"version": 3, "names": ["memoize", "callback", "previous", "result", "has<PERSON><PERSON>ed", "_len", "arguments", "length", "dependencies", "Array", "_key", "i", "undefined", "apply"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\@react-navigation\\stack\\src\\utils\\memoize.tsx"], "sourcesContent": ["export default function memoize<Result, <PERSON><PERSON> extends readonly any[]>(\n  callback: (...deps: Deps) => Result\n) {\n  let previous: Deps | undefined;\n  let result: Result | undefined;\n\n  return (...dependencies: Deps): Result => {\n    let hasChanged = false;\n\n    if (previous) {\n      if (previous.length !== dependencies.length) {\n        hasChanged = true;\n      } else {\n        for (let i = 0; i < previous.length; i++) {\n          if (previous[i] !== dependencies[i]) {\n            hasChanged = true;\n            break;\n          }\n        }\n      }\n    } else {\n      hasChanged = true;\n    }\n\n    previous = dependencies;\n\n    if (hasChanged || result === undefined) {\n      result = callback(...dependencies);\n    }\n\n    return result;\n  };\n}\n"], "mappings": "AAAA,eAAe,SAASA,OAAOA,CAC7BC,QAAmC,EACnC;EACA,IAAIC,QAA0B;EAC9B,IAAIC,MAA0B;EAE9B,OAAO,YAAmC;IACxC,IAAIC,UAAU,GAAG,KAAK;IAAC,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EADdC,YAAY,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;MAAZF,YAAY,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;IAAA;IAGrB,IAAIR,QAAQ,EAAE;MACZ,IAAIA,QAAQ,CAACK,MAAM,KAAKC,YAAY,CAACD,MAAM,EAAE;QAC3CH,UAAU,GAAG,IAAI;MACnB,CAAC,MAAM;QACL,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,QAAQ,CAACK,MAAM,EAAEI,CAAC,EAAE,EAAE;UACxC,IAAIT,QAAQ,CAACS,CAAC,CAAC,KAAKH,YAAY,CAACG,CAAC,CAAC,EAAE;YACnCP,UAAU,GAAG,IAAI;YACjB;UACF;QACF;MACF;IACF,CAAC,MAAM;MACLA,UAAU,GAAG,IAAI;IACnB;IAEAF,QAAQ,GAAGM,YAAY;IAEvB,IAAIJ,UAAU,IAAID,MAAM,KAAKS,SAAS,EAAE;MACtCT,MAAM,GAAGF,QAAQ,CAAAY,KAAA,SAAIL,YAAY,CAAC;IACpC;IAEA,OAAOL,MAAM;EACf,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}