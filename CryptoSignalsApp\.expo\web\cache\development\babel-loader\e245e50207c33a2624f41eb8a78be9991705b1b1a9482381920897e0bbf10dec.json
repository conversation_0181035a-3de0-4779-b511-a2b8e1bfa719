{"ast": null, "code": "import color from 'color';\nimport { black, pinkA400, white } from \"./colors\";\nimport configureFonts from \"../../fonts\";\nexport var MD2LightTheme = {\n  dark: false,\n  roundness: 4,\n  version: 2,\n  isV3: false,\n  colors: {\n    primary: '#6200ee',\n    accent: '#03dac4',\n    background: '#f6f6f6',\n    surface: white,\n    error: '#B00020',\n    text: black,\n    onSurface: '#000000',\n    disabled: color(black).alpha(0.26).rgb().string(),\n    placeholder: color(black).alpha(0.54).rgb().string(),\n    backdrop: color(black).alpha(0.5).rgb().string(),\n    notification: pinkA400,\n    tooltip: 'rgba(28, 27, 31, 1)'\n  },\n  fonts: configureFonts({\n    isV3: false\n  }),\n  animation: {\n    scale: 1.0\n  }\n};", "map": {"version": 3, "names": ["color", "black", "pinkA400", "white", "configure<PERSON>onts", "MD2LightTheme", "dark", "roundness", "version", "isV3", "colors", "primary", "accent", "background", "surface", "error", "text", "onSurface", "disabled", "alpha", "rgb", "string", "placeholder", "backdrop", "notification", "tooltip", "fonts", "animation", "scale"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\styles\\themes\\v2\\LightTheme.tsx"], "sourcesContent": ["import color from 'color';\n\nimport { black, pinkA400, white } from './colors';\nimport type { Fonts, MD2Theme } from '../../../types';\nimport configureFonts from '../../fonts';\n\nexport const MD2LightTheme: MD2Theme = {\n  dark: false,\n  roundness: 4,\n  version: 2,\n  isV3: false,\n  colors: {\n    primary: '#6200ee',\n    accent: '#03dac4',\n    background: '#f6f6f6',\n    surface: white,\n    error: '#B00020',\n    text: black,\n    onSurface: '#000000',\n    disabled: color(black).alpha(0.26).rgb().string(),\n    placeholder: color(black).alpha(0.54).rgb().string(),\n    backdrop: color(black).alpha(0.5).rgb().string(),\n    notification: pinkA400,\n    tooltip: 'rgba(28, 27, 31, 1)',\n  },\n  fonts: configureFonts({ isV3: false }) as Fonts,\n  animation: {\n    scale: 1.0,\n  },\n};\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,SAASC,KAAK,EAAEC,QAAQ,EAAEC,KAAK;AAE/B,OAAOC,cAAc;AAErB,OAAO,IAAMC,aAAuB,GAAG;EACrCC,IAAI,EAAE,KAAK;EACXC,SAAS,EAAE,CAAC;EACZC,OAAO,EAAE,CAAC;EACVC,IAAI,EAAE,KAAK;EACXC,MAAM,EAAE;IACNC,OAAO,EAAE,SAAS;IAClBC,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE,SAAS;IACrBC,OAAO,EAAEX,KAAK;IACdY,KAAK,EAAE,SAAS;IAChBC,IAAI,EAAEf,KAAK;IACXgB,SAAS,EAAE,SAAS;IACpBC,QAAQ,EAAElB,KAAK,CAACC,KAAK,CAAC,CAACkB,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;IACjDC,WAAW,EAAEtB,KAAK,CAACC,KAAK,CAAC,CAACkB,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;IACpDE,QAAQ,EAAEvB,KAAK,CAACC,KAAK,CAAC,CAACkB,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;IAChDG,YAAY,EAAEtB,QAAQ;IACtBuB,OAAO,EAAE;EACX,CAAC;EACDC,KAAK,EAAEtB,cAAc,CAAC;IAAEK,IAAI,EAAE;EAAM,CAAC,CAAU;EAC/CkB,SAAS,EAAE;IACTC,KAAK,EAAE;EACT;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}