import os
import sys
import asyncio
import logging
import argparse
from datetime import datetime

# Adiciona o diretório raiz ao path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Importando os módulos a serem testados
from utils.binance_client import BinanceHandler
from utils.telegram_sender import TelegramSender
from config.settings import (
    BINANCE_API_KEY,
    BINANCE_API_SECRET,
    BINANCE_TESTNET,
    TELEGRAM_API_ID,
    TELEGRAM_API_HASH,
    TELEGRAM_GROUP_ID
)

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('tests/test_connection_real.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

async def test_binance_connection():
    """Testa a conexão real com a Binance"""
    logger.info("Iniciando teste de conexão com a Binance...")
    
    try:
        # Cria uma instância do BinanceHandler
        handler = BinanceHandler(use_mock_data=False)
        
        # Verifica se as credenciais estão configuradas
        if not BINANCE_API_KEY or BINANCE_API_KEY == "your_testnet_api_key":
            logger.error("API Key da Binance não configurada corretamente no arquivo .env")
            return False
        
        if not BINANCE_API_SECRET or BINANCE_API_SECRET == "your_testnet_api_secret":
            logger.error("API Secret da Binance não configurada corretamente no arquivo .env")
            return False
        
        # Testa a sincronização de tempo
        offset = handler.sync_time()
        logger.info(f"Sincronização de tempo com a Binance: offset = {offset} ms")
        
        # Testa a obtenção de dados históricos
        symbols_to_test = ['BTCUSDT', 'ETHUSDT', 'BNBUSDT']
        intervals_to_test = ['1m', '5m', '15m', '1h', '4h']
        
        for symbol in symbols_to_test:
            for interval in intervals_to_test:
                logger.info(f"Obtendo dados históricos para {symbol} em timeframe {interval}...")
                df = handler.get_historical_klines(
                    symbol=symbol,
                    interval=interval,
                    lookback_days=1
                )
                
                if df is None or df.empty:
                    logger.error(f"Falha ao obter dados históricos para {symbol} em timeframe {interval}")
                    return False
                
                logger.info(f"Dados obtidos com sucesso: {len(df)} registros")
                
                # Testa a obtenção do preço atual
                price = handler.get_current_price(symbol)
                if price is None or price <= 0:
                    logger.error(f"Falha ao obter preço atual para {symbol}")
                    return False
                
                logger.info(f"Preço atual de {symbol}: {price}")
        
        logger.info("Teste de conexão com a Binance concluído com sucesso!")
        return True
        
    except Exception as e:
        logger.error(f"Erro ao testar conexão com a Binance: {e}")
        return False

async def test_telegram_connection():
    """Testa a conexão real com o Telegram"""
    logger.info("Iniciando teste de conexão com o Telegram...")
    
    try:
        # Verifica se as credenciais estão configuradas
        if not TELEGRAM_API_ID:
            logger.error("API ID do Telegram não configurada no arquivo .env")
            return False
        
        if not TELEGRAM_API_HASH:
            logger.error("API Hash do Telegram não configurada no arquivo .env")
            return False
        
        if not TELEGRAM_GROUP_ID:
            logger.error("Group ID do Telegram não configurado no arquivo .env")
            return False
        
        # Cria uma instância do TelegramSender
        sender = TelegramSender()
        
        # Conecta ao Telegram
        logger.info("Conectando ao Telegram...")
        await sender.connect()
        
        if not sender.client:
            logger.error("Falha ao conectar ao Telegram")
            return False
        
        # Envia uma mensagem de teste
        test_message = f"🧪 TESTE DE CONEXÃO 🧪\nData/Hora: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}\nEste é um teste automatizado de conexão."
        
        logger.info("Enviando mensagem de teste para o grupo...")
        message_id = await sender.send_message(test_message)
        
        if not message_id:
            logger.error("Falha ao enviar mensagem para o grupo")
            return False
        
        logger.info(f"Mensagem enviada com sucesso! ID: {message_id}")
        
        # Desconecta do Telegram
        await sender.disconnect()
        
        logger.info("Teste de conexão com o Telegram concluído com sucesso!")
        return True
        
    except Exception as e:
        logger.error(f"Erro ao testar conexão com o Telegram: {e}")
        return False

async def main():
    """Função principal"""
    parser = argparse.ArgumentParser(description='Teste de Conexão Real com Binance e Telegram')
    parser.add_argument('--binance', action='store_true', help='Testar apenas a conexão com a Binance')
    parser.add_argument('--telegram', action='store_true', help='Testar apenas a conexão com o Telegram')
    
    args = parser.parse_args()
    
    # Se nenhum argumento for fornecido, testa ambas as conexões
    if not args.binance and not args.telegram:
        args.binance = True
        args.telegram = True
    
    # Testa a conexão com a Binance
    if args.binance:
        binance_result = await test_binance_connection()
        logger.info(f"Resultado do teste de conexão com a Binance: {'SUCESSO' if binance_result else 'FALHA'}")
    
    # Testa a conexão com o Telegram
    if args.telegram:
        telegram_result = await test_telegram_connection()
        logger.info(f"Resultado do teste de conexão com o Telegram: {'SUCESSO' if telegram_result else 'FALHA'}")
    
    # Verifica o resultado geral
    if args.binance and args.telegram:
        if binance_result and telegram_result:
            logger.info("✅ TODOS OS TESTES DE CONEXÃO FORAM BEM-SUCEDIDOS! ✅")
        else:
            logger.error("❌ ALGUNS TESTES DE CONEXÃO FALHARAM! ❌")
    elif args.binance:
        if binance_result:
            logger.info("✅ TESTE DE CONEXÃO COM A BINANCE FOI BEM-SUCEDIDO! ✅")
        else:
            logger.error("❌ TESTE DE CONEXÃO COM A BINANCE FALHOU! ❌")
    elif args.telegram:
        if telegram_result:
            logger.info("✅ TESTE DE CONEXÃO COM O TELEGRAM FOI BEM-SUCEDIDO! ✅")
        else:
            logger.error("❌ TESTE DE CONEXÃO COM O TELEGRAM FALHOU! ❌")

if __name__ == "__main__":
    asyncio.run(main()) 