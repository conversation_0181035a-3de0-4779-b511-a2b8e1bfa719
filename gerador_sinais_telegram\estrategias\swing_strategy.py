import logging
import pandas as pd
import numpy as np
import ta
from config.settings import LEVERAGE

logger = logging.getLogger(__name__)

class SwingStrategy:
    def __init__(self, binance_handler):
        self.binance = binance_handler
        self.leverage = LEVERAGE

    def analyze_symbol(self, symbol):
        """
        Analisa um símbolo para verificar se há sinal de swing trading

        Args:
            symbol: Par de trading (ex: 'BTCUSDT')

        Returns:
            tuple: (signal_type, entry_price, stop_loss, take_profit) ou (None, None, None, None) se não houver sinal
        """
        try:
            # Obter dados históricos
            df = self.binance.get_historical_klines(
                symbol=symbol,
                interval='4h',  # Timeframe padrão para swing
                lookback_days=30  # Mais dias para análise de swing
            )

            if df.empty:
                logger.warning(f"Dados insuficientes para {symbol}")
                return None, None, None, None

            # Verificar condições de entrada
            signal_detected, signal_type, entry_price = self._check_entry_conditions(df)

            if not signal_detected:
                return None, None, None, None

            # Validar se o preço de entrada é realista
            if not self._validate_price_range(symbol, entry_price):
                logger.error(f"Preço de entrada irrealista para {symbol}: {entry_price}")
                return None, None, None, None

            # Calcular stop loss e take profit
            stop_loss = self._calculate_stop_loss(entry_price, signal_type)
            take_profit = self._calculate_take_profit(entry_price, signal_type)

            # Validar se os cálculos fazem sentido
            if not self._validate_signal_values(symbol, signal_type, entry_price, stop_loss, take_profit):
                return None, None, None, None

            logger.info(f"{symbol} - Sinal de Swing Trading {signal_type} gerado. Entrada: {entry_price}, SL: {stop_loss}, TP: {take_profit}")

            # Garantir que todos os valores são float e válidos
            try:
                return signal_type, float(entry_price), float(stop_loss), float(take_profit)
            except (ValueError, TypeError) as e:
                logger.error(f"Erro ao converter valores para float em {symbol}: {e}")
                return None, None, None, None

        except Exception as e:
            logger.error(f"Erro ao analisar {symbol} para swing trading: {e}")
            return None, None, None, None

    def _check_entry_conditions(self, df):
        """
        Verifica as condições de entrada para swing trading:
        - Cruzamento da EMA 8 acima da EMA 21
        - RSI acima de 50
        - Preço acima da EMA 200
        """
        try:
            # Calcular EMAs
            df['EMA_8'] = ta.trend.ema_indicator(df['close'], window=8)
            df['EMA_21'] = ta.trend.ema_indicator(df['close'], window=21)
            df['EMA_200'] = ta.trend.ema_indicator(df['close'], window=200)

            # Calcular RSI
            df['RSI'] = ta.momentum.rsi(df['close'], window=14)

            # Verificar condições no candle mais recente
            current_ema8 = df['EMA_8'].iloc[-1]
            current_ema21 = df['EMA_21'].iloc[-1]
            current_ema200 = df['EMA_200'].iloc[-1]
            current_rsi = df['RSI'].iloc[-1]
            current_price = df['close'].iloc[-1]

            # Verificar cruzamento da EMA 8 acima da EMA 21
            prev_ema8 = df['EMA_8'].iloc[-2]
            prev_ema21 = df['EMA_21'].iloc[-2]

            # Condições para LONG
            long_conditions = (
                prev_ema8 <= prev_ema21 and
                current_ema8 > current_ema21 and
                current_price > current_ema200 and
                current_rsi > 50
            )

            # Condições para SHORT
            short_conditions = (
                prev_ema8 >= prev_ema21 and
                current_ema8 < current_ema21 and
                current_price < current_ema200 and
                current_rsi < 50
            )

            if long_conditions:
                return True, 'LONG', current_price
            elif short_conditions:
                return True, 'SHORT', current_price

            return False, None, None

        except Exception as e:
            logger.error(f"Erro ao verificar condições de entrada para swing trading: {e}")
            return False, None, None

    def _calculate_stop_loss(self, entry_price, signal_type):
        """Calcula o stop loss baseado no tipo de sinal (mais conservador)"""
        if signal_type == 'LONG':
            return entry_price * 0.95  # 5% abaixo (mais conservador)
        else:  # SHORT
            return entry_price * 1.05  # 5% acima (mais conservador)

    def _calculate_take_profit(self, entry_price, signal_type):
        """Calcula o take profit baseado no tipo de sinal (mais conservador)"""
        if signal_type == 'LONG':
            return entry_price * 1.08  # 8% acima (reduzido de 10%)
        else:  # SHORT
            return entry_price * 0.92  # 8% abaixo (reduzido de 10%)

    def _validate_price_range(self, symbol, price):
        """Valida se um preço está dentro de uma faixa realista para o símbolo"""
        if price <= 0:
            return False

        # Faixas de preços realistas (com margem de segurança)
        price_ranges = {
            'BTC': (10000, 150000),
            'ETH': (500, 10000),
            'BNB': (50, 1000),
            'ADA': (0.1, 5),
            'SOL': (10, 1000),
            'DOT': (1, 50),
            'LINK': (1, 100),
            'MATIC': (0.1, 10),
            'AVAX': (5, 200),
            'ATOM': (1, 100)
        }

        for coin, (min_price, max_price) in price_ranges.items():
            if coin in symbol:
                return min_price <= price <= max_price

        # Para moedas não mapeadas, aceitar uma faixa ampla
        return 0.001 <= price <= 100000

    def _validate_signal_values(self, symbol, signal_type, entry_price, stop_loss, take_profit):
        """Valida se os valores do sinal fazem sentido"""
        try:
            # Validar se todos os valores são positivos
            if entry_price <= 0 or stop_loss <= 0 or take_profit <= 0:
                logger.error(f"Valores inválidos para {symbol}: entry={entry_price}, sl={stop_loss}, tp={take_profit}")
                return False

            # Validar lógica do stop loss
            if signal_type == 'LONG' and stop_loss >= entry_price:
                logger.error(f"Stop loss LONG inválido para {symbol}: {stop_loss} >= {entry_price}")
                return False
            elif signal_type == 'SHORT' and stop_loss <= entry_price:
                logger.error(f"Stop loss SHORT inválido para {symbol}: {stop_loss} <= {entry_price}")
                return False

            # Validar lógica do take profit
            if signal_type == 'LONG' and take_profit <= entry_price:
                logger.error(f"Take profit LONG inválido para {symbol}: {take_profit} <= {entry_price}")
                return False
            elif signal_type == 'SHORT' and take_profit >= entry_price:
                logger.error(f"Take profit SHORT inválido para {symbol}: {take_profit} >= {entry_price}")
                return False

            # Validar se o take profit não é muito agressivo (máximo 10% para swing)
            if signal_type == 'LONG':
                max_tp = entry_price * 1.10
                if take_profit > max_tp:
                    logger.warning(f"Take profit muito agressivo para {symbol}, ajustando de {take_profit} para {max_tp}")
                    return False
            else:  # SHORT
                min_tp = entry_price * 0.90
                if take_profit < min_tp:
                    logger.warning(f"Take profit muito agressivo para {symbol}, ajustando de {take_profit} para {min_tp}")
                    return False

            return True

        except Exception as e:
            logger.error(f"Erro ao validar valores do sinal para {symbol}: {e}")
            return False