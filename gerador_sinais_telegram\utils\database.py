import sqlite3
import os
import logging
import datetime
from typing import Dict, List, Tuple, Optional, Any, Union

logger = logging.getLogger(__name__)

class DatabaseHandler:
    """Classe para gerenciar o banco de dados SQLite"""

    def __init__(self, db_path="database.sqlite"):
        """
        Inicializa o manipulador de banco de dados

        Args:
            db_path (str): Caminho para o arquivo do banco de dados
        """
        self.db_path = db_path
        self.conn = None
        self.cursor = None
        self.initialize_db()

    def initialize_db(self):
        """Inicializa o banco de dados e cria as tabelas necessárias se não existirem"""
        try:
            # Verificar se o diretório existe
            db_dir = os.path.dirname(self.db_path)
            if db_dir and not os.path.exists(db_dir):
                os.makedirs(db_dir)

            # Conectar ao banco de dados
            self.conn = sqlite3.connect(self.db_path)
            self.cursor = self.conn.cursor()

            # Criar tabela de sinais
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS signals (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                signal_type TEXT NOT NULL,
                strategy TEXT NOT NULL,
                entry_price REAL NOT NULL,
                stop_loss REAL NOT NULL,
                take_profit_1 REAL NOT NULL,
                take_profit_2 REAL,
                take_profit_3 REAL,
                take_profit_4 REAL,
                leverage INTEGER NOT NULL,
                created_at TIMESTAMP NOT NULL,
                message_id INTEGER,
                status TEXT DEFAULT 'OPEN',
                closed_at TIMESTAMP,
                result TEXT,
                profit_percentage REAL,
                hit_level INTEGER
            )
            ''')

            # Criar tabela de atualizações de sinais
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS signal_updates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                signal_id INTEGER NOT NULL,
                update_type TEXT NOT NULL,
                update_time TIMESTAMP NOT NULL,
                price REAL,
                profit_level INTEGER,
                message_id INTEGER,
                FOREIGN KEY (signal_id) REFERENCES signals (id)
            )
            ''')

            # Criar tabela de estatísticas
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS statistics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                date DATE UNIQUE NOT NULL,
                signals_sent INTEGER DEFAULT 0,
                successful_signals INTEGER DEFAULT 0,
                failed_signals INTEGER DEFAULT 0,
                total_profit REAL DEFAULT 0,
                average_profit REAL DEFAULT 0
            )
            ''')

            self.conn.commit()
            logger.info("Banco de dados inicializado com sucesso")

        except Exception as e:
            logger.error(f"Erro ao inicializar banco de dados: {e}")
            if self.conn:
                self.conn.close()
            raise

    def save_signal(self, signal_data: Dict[str, Any]) -> int:
        """
        Salva um novo sinal no banco de dados

        Args:
            signal_data (dict): Dados do sinal a ser salvo

        Returns:
            int: ID do sinal salvo
        """
        try:
            # Preparar os dados
            now = datetime.datetime.now()

            # Extrair valores do dicionário com valores padrão para campos opcionais
            symbol = signal_data.get('symbol')
            signal_type = signal_data.get('signal_type')
            strategy = signal_data.get('strategy', 'Unknown')
            entry_price = signal_data.get('entry_price')
            stop_loss = signal_data.get('stop_loss')
            leverage = signal_data.get('leverage', 1)
            message_id = signal_data.get('message_id')

            # Processar take profits
            if 'take_profits' in signal_data and isinstance(signal_data['take_profits'], dict):
                take_profits = signal_data['take_profits']
                sorted_levels = sorted(take_profits.keys())

                # Pegar até 4 níveis de take profit
                tp1 = take_profits.get(sorted_levels[0]) if len(sorted_levels) > 0 else None
                tp2 = take_profits.get(sorted_levels[1]) if len(sorted_levels) > 1 else None
                tp3 = take_profits.get(sorted_levels[2]) if len(sorted_levels) > 2 else None
                tp4 = take_profits.get(sorted_levels[3]) if len(sorted_levels) > 3 else None
            else:
                # Se não houver múltiplos take profits, usar o take_profit único
                tp1 = signal_data.get('take_profit')
                tp2 = tp3 = tp4 = None

            # Inserir o sinal
            self.cursor.execute('''
            INSERT INTO signals (
                symbol, signal_type, strategy, entry_price, stop_loss,
                take_profit_1, take_profit_2, take_profit_3, take_profit_4,
                leverage, created_at, message_id, status
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                symbol, signal_type, strategy, entry_price, stop_loss,
                tp1, tp2, tp3, tp4,
                leverage, now, message_id, 'OPEN'
            ))

            self.conn.commit()
            signal_id = self.cursor.lastrowid

            # Atualizar estatísticas diárias
            self._update_daily_stats(now.date())

            logger.info(f"Sinal salvo no banco de dados com ID {signal_id}")
            return signal_id

        except Exception as e:
            logger.error(f"Erro ao salvar sinal no banco de dados: {e}")
            self.conn.rollback()
            return -1

    def update_signal_status(self, signal_id: int, status: str,
                            result: Optional[str] = None,
                            profit_percentage: Optional[float] = None,
                            hit_level: Optional[int] = None) -> bool:
        """
        Atualiza o status de um sinal

        Args:
            signal_id (int): ID do sinal
            status (str): Novo status (OPEN, CLOSED, EXPIRED, TP_HIT, SL_HIT)
            result (str, optional): Resultado do sinal (WIN, LOSS, BREAKEVEN)
            profit_percentage (float, optional): Percentual de lucro/perda
            hit_level (int, optional): Nível de take profit atingido

        Returns:
            bool: True se atualizado com sucesso, False caso contrário
        """
        try:
            now = datetime.datetime.now()

            # Construir a consulta SQL dinamicamente com base nos parâmetros fornecidos
            update_fields = ["status = ?", "closed_at = ?"]
            params = [status, now]

            if result is not None:
                update_fields.append("result = ?")
                params.append(result)

            if profit_percentage is not None:
                update_fields.append("profit_percentage = ?")
                params.append(profit_percentage)

            if hit_level is not None:
                update_fields.append("hit_level = ?")
                params.append(hit_level)

            # Adicionar o ID do sinal aos parâmetros
            params.append(signal_id)

            # Executar a consulta
            self.cursor.execute(f'''
            UPDATE signals SET {", ".join(update_fields)} WHERE id = ?
            ''', params)

            self.conn.commit()

            # Se o sinal foi fechado com resultado, atualizar estatísticas
            if status in ['CLOSED', 'TP_HIT', 'SL_HIT'] and result in ['WIN', 'LOSS']:
                self._update_signal_stats(now.date(), result, profit_percentage)

            logger.info(f"Status do sinal {signal_id} atualizado para {status}")
            return True

        except Exception as e:
            logger.error(f"Erro ao atualizar status do sinal {signal_id}: {e}")
            self.conn.rollback()
            return False

    def save_signal_update(self, signal_id: int, update_type: str,
                          price: Optional[float] = None,
                          profit_level: Optional[int] = None,
                          message_id: Optional[int] = None) -> int:
        """
        Salva uma atualização de sinal (TP atingido, SL atingido, etc.)

        Args:
            signal_id (int): ID do sinal
            update_type (str): Tipo de atualização (TP_HIT, SL_HIT, EXPIRED)
            price (float, optional): Preço atual
            profit_level (int, optional): Nível de take profit atingido
            message_id (int, optional): ID da mensagem de atualização

        Returns:
            int: ID da atualização salva
        """
        try:
            now = datetime.datetime.now()

            self.cursor.execute('''
            INSERT INTO signal_updates (
                signal_id, update_type, update_time, price, profit_level, message_id
            ) VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                signal_id, update_type, now, price, profit_level, message_id
            ))

            self.conn.commit()
            update_id = self.cursor.lastrowid

            logger.info(f"Atualização do sinal {signal_id} salva com ID {update_id}")
            return update_id

        except Exception as e:
            logger.error(f"Erro ao salvar atualização do sinal {signal_id}: {e}")
            self.conn.rollback()
            return -1

    def get_signal_by_id(self, signal_id: int) -> Optional[Dict[str, Any]]:
        """
        Obtém um sinal pelo ID

        Args:
            signal_id (int): ID do sinal

        Returns:
            dict: Dados do sinal ou None se não encontrado
        """
        try:
            self.cursor.execute('''
            SELECT * FROM signals WHERE id = ?
            ''', (signal_id,))

            row = self.cursor.fetchone()
            if not row:
                return None

            # Converter a linha em um dicionário
            columns = [col[0] for col in self.cursor.description]
            return dict(zip(columns, row))

        except Exception as e:
            logger.error(f"Erro ao obter sinal {signal_id}: {e}")
            return None

    def get_signal_by_message_id(self, message_id: int) -> Optional[Dict[str, Any]]:
        """
        Obtém um sinal pelo ID da mensagem do Telegram

        Args:
            message_id (int): ID da mensagem

        Returns:
            dict: Dados do sinal ou None se não encontrado
        """
        try:
            self.cursor.execute('''
            SELECT * FROM signals WHERE message_id = ?
            ''', (message_id,))

            row = self.cursor.fetchone()
            if not row:
                return None

            # Converter a linha em um dicionário
            columns = [col[0] for col in self.cursor.description]
            return dict(zip(columns, row))

        except Exception as e:
            logger.error(f"Erro ao obter sinal com message_id {message_id}: {e}")
            return None

    def get_open_signals(self) -> List[Dict[str, Any]]:
        """
        Obtém todos os sinais abertos

        Returns:
            list: Lista de sinais abertos
        """
        try:
            self.cursor.execute('''
            SELECT * FROM signals WHERE status = 'OPEN'
            ''')

            rows = self.cursor.fetchall()

            # Converter as linhas em dicionários
            columns = [col[0] for col in self.cursor.description]
            return [dict(zip(columns, row)) for row in rows]

        except Exception as e:
            logger.error(f"Erro ao obter sinais abertos: {e}")
            return []

    def _update_daily_stats(self, date: datetime.date) -> None:
        """
        Atualiza as estatísticas diárias

        Args:
            date (datetime.date): Data para atualizar estatísticas
        """
        try:
            # Verificar se já existe um registro para esta data
            self.cursor.execute('''
            SELECT id FROM statistics WHERE date = ?
            ''', (date,))

            row = self.cursor.fetchone()

            if row:
                # Atualizar o registro existente
                self.cursor.execute('''
                UPDATE statistics SET signals_sent = signals_sent + 1 WHERE date = ?
                ''', (date,))
            else:
                # Criar um novo registro
                self.cursor.execute('''
                INSERT INTO statistics (date, signals_sent) VALUES (?, 1)
                ''', (date,))

            self.conn.commit()

        except Exception as e:
            logger.error(f"Erro ao atualizar estatísticas diárias: {e}")
            self.conn.rollback()

    def _update_signal_stats(self, date: datetime.date, result: str, profit_percentage: float) -> None:
        """
        Atualiza as estatísticas de sinais

        Args:
            date (datetime.date): Data para atualizar estatísticas
            result (str): Resultado do sinal (WIN, LOSS)
            profit_percentage (float): Percentual de lucro/perda
        """
        try:
            # Verificar se já existe um registro para esta data
            self.cursor.execute('''
            SELECT id FROM statistics WHERE date = ?
            ''', (date,))

            row = self.cursor.fetchone()

            if not row:
                # Criar um novo registro se não existir
                self.cursor.execute('''
                INSERT INTO statistics (date, signals_sent) VALUES (?, 0)
                ''', (date,))

            # Atualizar estatísticas com base no resultado
            if result == 'WIN':
                self.cursor.execute('''
                UPDATE statistics SET
                    successful_signals = successful_signals + 1,
                    total_profit = total_profit + ?,
                    average_profit = (total_profit + ?) / (successful_signals + failed_signals)
                WHERE date = ?
                ''', (profit_percentage, profit_percentage, date))
            else:  # LOSS
                self.cursor.execute('''
                UPDATE statistics SET
                    failed_signals = failed_signals + 1,
                    total_profit = total_profit + ?,
                    average_profit = (total_profit + ?) / (successful_signals + failed_signals)
                WHERE date = ?
                ''', (profit_percentage, profit_percentage, date))

            self.conn.commit()

        except Exception as e:
            logger.error(f"Erro ao atualizar estatísticas de sinais: {e}")
            self.conn.rollback()

    def get_signal_updates(self, signal_id: int, update_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Obtém atualizações de um sinal

        Args:
            signal_id (int): ID do sinal
            update_type (str, optional): Tipo de atualização (TP_HIT, SL_HIT, EXPIRED)

        Returns:
            list: Lista de atualizações
        """
        try:
            query = "SELECT * FROM signal_updates WHERE signal_id = ?"
            params = [signal_id]

            if update_type:
                query += " AND update_type = ?"
                params.append(update_type)

            query += " ORDER BY update_time DESC"

            self.cursor.execute(query, params)
            rows = self.cursor.fetchall()

            # Converter as linhas em dicionários
            columns = [col[0] for col in self.cursor.description]
            return [dict(zip(columns, row)) for row in rows]

        except Exception as e:
            logger.error(f"Erro ao obter atualizações do sinal {signal_id}: {e}")
            return []

    def get_statistics(self, start_date: Optional[datetime.date] = None,
                      end_date: Optional[datetime.date] = None) -> List[Dict[str, Any]]:
        """
        Obtém estatísticas de sinais

        Args:
            start_date (datetime.date, optional): Data inicial
            end_date (datetime.date, optional): Data final

        Returns:
            list: Lista de estatísticas
        """
        try:
            query = "SELECT * FROM statistics"
            params = []

            if start_date or end_date:
                query += " WHERE "

                if start_date:
                    query += "date >= ?"
                    params.append(start_date)

                    if end_date:
                        query += " AND date <= ?"
                        params.append(end_date)
                elif end_date:
                    query += "date <= ?"
                    params.append(end_date)

            query += " ORDER BY date DESC"

            self.cursor.execute(query, params)
            rows = self.cursor.fetchall()

            # Converter as linhas em dicionários
            columns = [col[0] for col in self.cursor.description]
            return [dict(zip(columns, row)) for row in rows]

        except Exception as e:
            logger.error(f"Erro ao obter estatísticas: {e}")
            return []

    def close(self):
        """Fecha a conexão com o banco de dados"""
        if self.conn:
            self.conn.close()
            logger.info("Conexão com o banco de dados fechada")
