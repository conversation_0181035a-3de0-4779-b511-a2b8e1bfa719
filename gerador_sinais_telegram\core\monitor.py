import paramiko
import time
import datetime
import requests
from pathlib import Path
import json
import os
import threading
import signal
import sys
import re
from collections import defaultdict, deque
from dataclasses import dataclass, asdict
from typing import Dict, List, Optional, Tuple, Any
from dotenv import load_dotenv
import psutil
import subprocess

# Configurações de cores para terminal
class Colors:
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    PURPLE = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    END = '\033[0m'

@dataclass
class ServiceStatus:
    """Classe para representar o status de um serviço"""
    name: str
    status: str
    uptime: str
    cpu_usage: float
    memory_usage: float
    restart_count: int
    last_restart: Optional[str]
    error_count: int
    recent_logs: List[str]
    timestamp: str

@dataclass
class ServerMetrics:
    """Classe para métricas do servidor"""
    cpu_percent: float
    memory_percent: float
    disk_percent: float
    network_io: Dict[str, int]
    load_average: Tuple[float, float, float]
    uptime: str
    timestamp: str

@dataclass
class Alert:
    """Classe para alertas do sistema"""
    level: str  # INFO, WARNING, ERROR, CRITICAL
    service: str
    message: str
    timestamp: str
    resolved: bool = False

class AdvancedServerMonitor:
    def __init__(self):
        # Configurações do servidor
        self.host = "**************"
        self.username = "root"
        self.password = "h4*ls:FtJw0e"

        # Serviços específicos para monitorar
        self.services = {
            "gerador_sinais": {
                "name": "Gerador de Sinais Telegram",
                "log_path": "/opt/gerador_sinais_telegram/gerador_sinais.log",
                "alt_log_path": "/root/gerador_sinais_telegram/gerador_sinais.log",
                "config_path": "/opt/gerador_sinais_telegram",
                "critical": True
            },
            "sistema-clientes": {
                "name": "Sistema de Clientes Streamlit",
                "log_path": None,
                "config_path": None,
                "critical": False
            }
        }

        # Configurar diretórios
        self.log_dir = Path("monitor_logs")
        self.log_dir.mkdir(exist_ok=True)
        self.alerts_dir = self.log_dir / "alerts"
        self.alerts_dir.mkdir(exist_ok=True)

        # Arquivos de dados
        self.status_file = self.log_dir / "status.json"
        self.metrics_file = self.log_dir / "metrics.json"
        self.alerts_file = self.alerts_dir / "alerts.json"

        # Histórico e alertas
        self.previous_status = self.load_status()
        self.metrics_history = deque(maxlen=100)  # Últimas 100 medições
        self.alerts = self.load_alerts()
        self.alert_thresholds = {
            "cpu_critical": 90.0,
            "cpu_warning": 75.0,
            "memory_critical": 90.0,
            "memory_warning": 80.0,
            "disk_critical": 95.0,
            "disk_warning": 85.0,
            "service_restart_threshold": 3  # Máximo de restarts em 1 hora
        }

        # Controle de threading
        self.monitoring_active = False
        self.monitor_thread = None

        # SSH connection pool
        self.ssh_connection = None
        self.last_connection_time = None
        self.connection_timeout = 300  # 5 minutos

    def load_status(self):
        """Carrega o status anterior dos serviços"""
        if self.status_file.exists():
            try:
                with open(self.status_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                return {}
        return {}

    def save_status(self, status):
        """Salva o status atual dos serviços"""
        try:
            with open(self.status_file, 'w', encoding='utf-8') as f:
                json.dump(status, f, indent=4, ensure_ascii=False)
        except Exception as e:
            self.log_event(f"Erro ao salvar status: {e}", "ERROR")

    def load_alerts(self):
        """Carrega alertas anteriores"""
        if self.alerts_file.exists():
            try:
                with open(self.alerts_file, 'r', encoding='utf-8') as f:
                    alerts_data = json.load(f)
                    return [Alert(**alert) for alert in alerts_data]
            except (json.JSONDecodeError, FileNotFoundError):
                return []
        return []

    def save_alerts(self):
        """Salva alertas atuais"""
        try:
            alerts_data = [asdict(alert) for alert in self.alerts]
            with open(self.alerts_file, 'w', encoding='utf-8') as f:
                json.dump(alerts_data, f, indent=4, ensure_ascii=False)
        except Exception as e:
            self.log_event(f"Erro ao salvar alertas: {e}", "ERROR")

    def get_ssh_connection(self):
        """Obtém conexão SSH reutilizável"""
        current_time = time.time()

        # Verificar se a conexão existe e ainda é válida
        if (self.ssh_connection and
            self.last_connection_time and
            current_time - self.last_connection_time < self.connection_timeout):
            try:
                # Testar a conexão
                self.ssh_connection.exec_command("echo test", timeout=5)
                return self.ssh_connection
            except:
                # Conexão inválida, criar nova
                pass

        # Criar nova conexão
        try:
            if self.ssh_connection:
                self.ssh_connection.close()

            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            ssh.connect(
                self.host,
                username=self.username,
                password=self.password,
                timeout=10
            )

            self.ssh_connection = ssh
            self.last_connection_time = current_time
            return ssh

        except Exception as e:
            self.log_event(f"Erro ao conectar SSH: {e}", "ERROR")
            return None

    def create_alert(self, level: str, service: str, message: str):
        """Cria um novo alerta"""
        alert = Alert(
            level=level,
            service=service,
            message=message,
            timestamp=datetime.datetime.now().isoformat()
        )

        # Verificar se já existe um alerta similar não resolvido
        similar_alert = None
        for existing_alert in self.alerts:
            if (existing_alert.service == service and
                existing_alert.message == message and
                not existing_alert.resolved):
                similar_alert = existing_alert
                break

        if not similar_alert:
            self.alerts.append(alert)
            self.save_alerts()
            self.log_event(f"ALERTA {level}: {service} - {message}", level)
            return alert

        return similar_alert

    def log_event(self, message, level="INFO"):
        """Registra eventos no arquivo de log"""
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_file = self.log_dir / f"monitor_{datetime.datetime.now().strftime('%Y%m%d')}.log"

        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(f"{timestamp} - {level} - {message}\n")

        print(f"{timestamp} - {level} - {message}")

    def check_service_status(self, ssh, service):
        """Verifica o status de um serviço específico"""
        try:
            # Executar comando systemctl status
            stdin, stdout, stderr = ssh.exec_command(f"systemctl is-active {service}")
            status = stdout.read().decode().strip()

            # Obter mais detalhes do serviço
            stdin, stdout, stderr = ssh.exec_command(f"systemctl status {service}")
            details = stdout.read().decode().strip()

            # Se o serviço estiver falhando, verificar o arquivo de configuração
            if status != "active":
                stdin, stdout, stderr = ssh.exec_command(f"cat /etc/systemd/system/{service}.service")
                service_config = stdout.read().decode().strip()

                # Verificar se o caminho do ExecStart está correto
                if "ExecStart=/root/gerador_sinais_telegram" in service_config:
                    details += "\n\nAVISO: O caminho do serviço está incorreto no arquivo .service!"
                    details += "\nO caminho deveria ser /opt/gerador_sinais_telegram em vez de /root/gerador_sinais_telegram"
                    details += "\n\nPara corrigir:"
                    details += "\n1. Execute: sudo nano /etc/systemd/system/gerador_sinais.service"
                    details += "\n2. Corrija o caminho no ExecStart"
                    details += "\n3. Execute: sudo systemctl daemon-reload"
                    details += "\n4. Execute: sudo systemctl restart gerador_sinais.service"

            # Obter uso de CPU e memória do serviço
            stdin, stdout, stderr = ssh.exec_command(f"ps -p $(systemctl show -p MainPID {service} | cut -d'=' -f2) -o %cpu,%mem")
            resource_usage = stdout.read().decode().strip()

            # Obter logs específicos do serviço
            if service == "gerador_sinais":
                # Tentar primeiro o caminho correto em /opt
                stdin, stdout, stderr = ssh.exec_command("tail -n 5 /opt/gerador_sinais_telegram/gerador_sinais.log")
                recent_logs = stdout.read().decode().strip()
                if not recent_logs:
                    # Se não encontrar, tentar o caminho em /root
                    stdin, stdout, stderr = ssh.exec_command("tail -n 5 /root/gerador_sinais_telegram/gerador_sinais.log")
                    recent_logs = stdout.read().decode().strip()
                    if recent_logs:
                        print("\nAVISO: O arquivo de log foi encontrado em /root/gerador_sinais_telegram/ mas deveria estar em /opt/gerador_sinais_telegram/")
            else:
                stdin, stdout, stderr = ssh.exec_command(f"journalctl -u {service} -n 5")
                recent_logs = stdout.read().decode().strip()

            return {
                "status": status,
                "details": details,
                "resource_usage": resource_usage,
                "recent_logs": recent_logs,
                "timestamp": datetime.datetime.now().isoformat()
            }
        except Exception as e:
            return {
                "status": "error",
                "details": str(e),
                "timestamp": datetime.datetime.now().isoformat()
            }

    def check_memory_usage(self, ssh):
        """Verifica o uso de memória do servidor"""
        stdin, stdout, stderr = ssh.exec_command("free -m")
        return stdout.read().decode()

    def check_disk_usage(self, ssh):
        """Verifica o uso de disco do servidor"""
        stdin, stdout, stderr = ssh.exec_command("df -h /")
        return stdout.read().decode()

    def monitor(self):
        """Função principal de monitoramento"""
        try:
            # Conectar ao servidor
            print(f"\nTentando conectar ao servidor {self.host}...")
            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            ssh.connect(self.host, username=self.username, password=self.password)
            print("Conexão SSH estabelecida com sucesso")

            # Verificar status dos serviços
            current_status = {}
            for service in self.services:
                status = self.check_service_status(ssh, service)
                current_status[service] = status

                # Verificar mudanças de status
                if (service in self.previous_status and
                    self.previous_status[service]["status"] != status["status"]):
                    self.log_event(
                        f"Mudança de status em {service}: "
                        f"{self.previous_status[service]['status']} -> {status['status']}",
                        "WARNING"
                    )

                # Registrar se o serviço não estiver funcionando
                if status["status"] != "active":
                    # Pegar detalhes adicionais do serviço
                    stdin, stdout, stderr = ssh.exec_command(f"systemctl status {service} | head -n 20")
                    service_details = stdout.read().decode().strip()

                    # Verificar se é apenas uma reinicialização bem-sucedida
                    if "code=exited, status=0/SUCCESS" in service_details:
                        self.log_event(f"Serviço {service} está funcionando normalmente (reiniciando)")
                    else:
                        self.log_event(
                            f"Serviço {service} não está ativo! Status: {status['status']}\n"
                            f"Detalhes do serviço:\n{service_details}",
                            "ERROR"
                        )
                else:
                    self.log_event(f"Serviço {service} está rodando normalmente")
                    if "resource_usage" in status:
                        self.log_event(f"Uso de recursos de {service}: {status['resource_usage']}")

            # Verificar recursos do servidor
            memory = self.check_memory_usage(ssh)
            disk = self.check_disk_usage(ssh)

            # Adicionar informações de recursos ao status
            current_status["resources"] = {
                "memory": memory,
                "disk": disk,
                "timestamp": datetime.datetime.now().isoformat()
            }

            # Salvar status atual
            self.save_status(current_status)
            self.previous_status = current_status

            ssh.close()

        except Exception as e:
            self.log_event(f"Erro ao monitorar servidor: {str(e)}", "ERROR")

    def view_gerador_sinais_logs(self, lines=50):
        """Visualiza os logs do gerador de sinais"""
        try:
            # Conectar ao servidor
            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            print(f"Tentando conectar ao servidor {self.host}...")
            ssh.connect(self.host, username=self.username, password=self.password)
            print("Conexão SSH estabelecida com sucesso")

            # Verificar se o arquivo de log existe
            check_command = f"test -f /root/gerador_sinais_telegram/gerador_sinais.log && echo 'existe' || echo 'nao existe'"
            stdin, stdout, stderr = ssh.exec_command(check_command)
            file_exists = stdout.read().decode().strip()

            # Verificar status do serviço primeiro
            stdin, stdout, stderr = ssh.exec_command("systemctl is-active gerador_sinais")
            service_status = stdout.read().decode().strip()

            if service_status != "active":
                # Se o serviço não estiver ativo, pegar logs do systemd
                print("Serviço não está ativo, obtendo logs do systemd...")
                stdin, stdout, stderr = ssh.exec_command(f"journalctl -u gerador_sinais.service -n {lines}")
                logs = stdout.read().decode().strip()
                return f"Status do serviço: {service_status}\n\nLogs do systemd:\n{logs}"

            if file_exists == 'nao existe':
                # Tentar pegar logs do journalctl mesmo se o arquivo não existir
                print("Arquivo de log não encontrado, obtendo logs do systemd...")
                stdin, stdout, stderr = ssh.exec_command(f"journalctl -u gerador_sinais.service -n {lines}")
                logs = stdout.read().decode().strip()
                return f"Arquivo de log não encontrado. Logs do systemd:\n{logs}"

            # Se o serviço estiver ativo e o arquivo existir, obter logs normalmente
            print("Obtendo logs do arquivo...")
            stdin, stdout, stderr = ssh.exec_command(f"tail -n {lines} /root/gerador_sinais_telegram/gerador_sinais.log")
            logs = stdout.read().decode().strip()
            stderr_output = stderr.read().decode().strip()

            if stderr_output:
                print(f"Erro ao ler logs: {stderr_output}")
                return f"Erro ao ler logs do arquivo: {stderr_output}"

            if not logs:
                return "O arquivo de log está vazio"

            ssh.close()
            return logs

        except paramiko.AuthenticationException:
            return "Erro de autenticação: Verifique as credenciais do servidor"
        except paramiko.SSHException as ssh_error:
            return f"Erro de conexão SSH: {str(ssh_error)}"
        except Exception as e:
            return f"Erro ao obter logs: {str(e)}\nTipo do erro: {type(e).__name__}"

def print_menu():
    """Imprime o menu de opções"""
    print("\n=== Monitor de Serviços ===")
    print("1. Iniciar monitoramento contínuo")
    print("2. Ver logs do Gerador de Sinais")
    print("3. Ver status atual")
    print("4. Sair")
    print("========================")

class AdvancedMonitorInterface:
    """Interface avançada para o monitor"""

    def __init__(self):
        self.monitor = AdvancedServerMonitor()
        self.running = True

    def print_header(self):
        """Imprime cabeçalho colorido"""
        print(f"\n{Colors.CYAN}{Colors.BOLD}{'='*80}{Colors.END}")
        print(f"{Colors.CYAN}{Colors.BOLD}🖥️  CRYPTOSIGNALS - MONITOR AVANÇADO DE SERVIDOR  🖥️{Colors.END}")
        print(f"{Colors.CYAN}{Colors.BOLD}{'='*80}{Colors.END}")

    def print_menu(self):
        """Imprime menu principal melhorado"""
        self.print_header()
        print(f"\n{Colors.BLUE}{Colors.BOLD}📋 MENU PRINCIPAL:{Colors.END}")
        print(f"{Colors.GREEN}1.{Colors.END} 🔄 Monitoramento em tempo real")
        print(f"{Colors.GREEN}2.{Colors.END} 📊 Dashboard de status")
        print(f"{Colors.GREEN}3.{Colors.END} 📋 Visualizar logs")
        print(f"{Colors.GREEN}4.{Colors.END} 🚨 Gerenciar alertas")
        print(f"{Colors.GREEN}5.{Colors.END} 📈 Métricas e estatísticas")
        print(f"{Colors.GREEN}6.{Colors.END} 🔧 Ferramentas de diagnóstico")
        print(f"{Colors.GREEN}7.{Colors.END} ⚙️  Configurações")
        print(f"{Colors.GREEN}8.{Colors.END} ❌ Sair")
        print(f"{Colors.CYAN}{'='*80}{Colors.END}")

    def print_service_status(self, service_name: str, status: dict):
        """Imprime status de serviço formatado"""
        if status['status'] == 'active':
            status_color = Colors.GREEN
            status_icon = "✅"
        elif status['status'] == 'inactive':
            status_color = Colors.YELLOW
            status_icon = "⚠️"
        else:
            status_color = Colors.RED
            status_icon = "❌"

        print(f"{status_icon} {Colors.BOLD}{service_name}{Colors.END}")
        print(f"   Status: {status_color}{status['status']}{Colors.END}")
        if 'resource_usage' in status:
            print(f"   Recursos: {status['resource_usage']}")
        print(f"   Última verificação: {status.get('timestamp', 'N/A')}")

    def show_dashboard(self):
        """Mostra dashboard completo"""
        print(f"\n{Colors.BLUE}{Colors.BOLD}📊 DASHBOARD DE STATUS{Colors.END}")
        print(f"{Colors.CYAN}{'='*60}{Colors.END}")

        # Executar verificação
        ssh = self.monitor.get_ssh_connection()
        if not ssh:
            print(f"{Colors.RED}❌ Erro: Não foi possível conectar ao servidor{Colors.END}")
            return

        # Status dos serviços
        print(f"\n{Colors.YELLOW}{Colors.BOLD}🔧 SERVIÇOS:{Colors.END}")
        for service_id, service_info in self.monitor.services.items():
            status = self.monitor.check_service_status(ssh, service_id)
            self.print_service_status(service_info['name'], status)
            print()

        # Métricas do servidor
        print(f"{Colors.YELLOW}{Colors.BOLD}🖥️  SERVIDOR:{Colors.END}")
        memory = self.monitor.check_memory_usage(ssh)
        disk = self.monitor.check_disk_usage(ssh)

        print(f"💾 Memória:\n{memory}")
        print(f"💿 Disco:\n{disk}")

        # Alertas ativos
        active_alerts = [alert for alert in self.monitor.alerts if not alert.resolved]
        if active_alerts:
            print(f"\n{Colors.RED}{Colors.BOLD}🚨 ALERTAS ATIVOS ({len(active_alerts)}):{Colors.END}")
            for alert in active_alerts[-5:]:  # Últimos 5 alertas
                level_color = Colors.RED if alert.level == "CRITICAL" else Colors.YELLOW
                print(f"   {level_color}• {alert.level}: {alert.service} - {alert.message}{Colors.END}")
        else:
            print(f"\n{Colors.GREEN}✅ Nenhum alerta ativo{Colors.END}")

    def real_time_monitoring(self):
        """Monitoramento em tempo real melhorado"""
        print(f"\n{Colors.BLUE}{Colors.BOLD}🔄 MONITORAMENTO EM TEMPO REAL{Colors.END}")
        print(f"{Colors.CYAN}Pressione Ctrl+C para voltar ao menu{Colors.END}")
        print(f"{Colors.CYAN}{'='*60}{Colors.END}")

        try:
            while True:
                # Limpar tela (funciona no Windows e Linux)
                os.system('cls' if os.name == 'nt' else 'clear')

                # Mostrar timestamp
                now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                print(f"{Colors.CYAN}{Colors.BOLD}🕐 {now}{Colors.END}")

                # Mostrar dashboard
                self.show_dashboard()

                # Aguardar próxima atualização
                print(f"\n{Colors.YELLOW}⏳ Próxima atualização em 30 segundos...{Colors.END}")
                time.sleep(30)

        except KeyboardInterrupt:
            print(f"\n{Colors.GREEN}✅ Monitoramento interrompido{Colors.END}")

def main():
    """Função principal melhorada"""
    interface = AdvancedMonitorInterface()

    # Configurar handler para Ctrl+C
    def signal_handler(sig, frame):
        print(f"\n{Colors.YELLOW}👋 Encerrando monitor...{Colors.END}")
        if interface.monitor.ssh_connection:
            interface.monitor.ssh_connection.close()
        sys.exit(0)

    signal.signal(signal.SIGINT, signal_handler)

    while interface.running:
        try:
            interface.print_menu()
            choice = input(f"\n{Colors.BOLD}Escolha uma opção (1-8): {Colors.END}").strip()

            if choice == "1":
                interface.real_time_monitoring()

            elif choice == "2":
                interface.show_dashboard()
                input(f"\n{Colors.CYAN}Pressione Enter para continuar...{Colors.END}")

            elif choice == "3":
                # Menu de logs (manter funcionalidade existente)
                logs_menu(interface.monitor)

            elif choice == "4":
                # Menu de alertas
                alerts_menu(interface.monitor)

            elif choice == "5":
                # Métricas e estatísticas
                metrics_menu(interface.monitor)

            elif choice == "6":
                # Ferramentas de diagnóstico
                diagnostic_menu(interface.monitor)

            elif choice == "7":
                # Configurações
                settings_menu(interface.monitor)

            elif choice == "8":
                print(f"{Colors.GREEN}👋 Encerrando monitor...{Colors.END}")
                if interface.monitor.ssh_connection:
                    interface.monitor.ssh_connection.close()
                interface.running = False

            else:
                print(f"{Colors.RED}❌ Opção inválida! Escolha entre 1-8.{Colors.END}")
                time.sleep(2)

        except KeyboardInterrupt:
            print(f"\n{Colors.YELLOW}👋 Encerrando monitor...{Colors.END}")
            if interface.monitor.ssh_connection:
                interface.monitor.ssh_connection.close()
            break
        except Exception as e:
            print(f"{Colors.RED}❌ Erro inesperado: {e}{Colors.END}")
            time.sleep(3)

def logs_menu(monitor):
    """Menu de logs melhorado"""
    while True:
        print(f"\n{Colors.BLUE}{Colors.BOLD}📋 VISUALIZAÇÃO DE LOGS{Colors.END}")
        print(f"{Colors.CYAN}{'='*50}{Colors.END}")
        print(f"{Colors.GREEN}1.{Colors.END} 📄 Últimas 50 linhas")
        print(f"{Colors.GREEN}2.{Colors.END} 📄 Últimas 100 linhas")
        print(f"{Colors.GREEN}3.{Colors.END} 📄 Últimas 200 linhas")
        print(f"{Colors.GREEN}4.{Colors.END} 🔄 Acompanhar em tempo real")
        print(f"{Colors.GREEN}5.{Colors.END} 🔍 Buscar por palavra-chave")
        print(f"{Colors.GREEN}6.{Colors.END} ⬅️  Voltar ao menu principal")

        choice = input(f"\n{Colors.BOLD}Escolha uma opção (1-6): {Colors.END}").strip()

        if choice == "1":
            logs = monitor.view_gerador_sinais_logs(50)
            print(f"\n{Colors.YELLOW}📄 Últimas 50 linhas:{Colors.END}")
            print(logs)
        elif choice == "2":
            logs = monitor.view_gerador_sinais_logs(100)
            print(f"\n{Colors.YELLOW}📄 Últimas 100 linhas:{Colors.END}")
            print(logs)
        elif choice == "3":
            logs = monitor.view_gerador_sinais_logs(200)
            print(f"\n{Colors.YELLOW}📄 Últimas 200 linhas:{Colors.END}")
            print(logs)
        elif choice == "4":
            real_time_logs(monitor)
        elif choice == "5":
            search_logs(monitor)
        elif choice == "6":
            break
        else:
            print(f"{Colors.RED}❌ Opção inválida!{Colors.END}")

        if choice in ["1", "2", "3", "5"]:
            input(f"\n{Colors.CYAN}Pressione Enter para continuar...{Colors.END}")

def real_time_logs(monitor):
    """Logs em tempo real melhorado"""
    print(f"\n{Colors.BLUE}{Colors.BOLD}🔄 LOGS EM TEMPO REAL{Colors.END}")
    print(f"{Colors.CYAN}Pressione Ctrl+C para voltar{Colors.END}")
    print(f"{Colors.CYAN}{'='*50}{Colors.END}")

    try:
        ssh = monitor.get_ssh_connection()
        if not ssh:
            print(f"{Colors.RED}❌ Erro de conexão SSH{Colors.END}")
            return

        # Tentar diferentes caminhos de log
        log_paths = [
            "/opt/gerador_sinais_telegram/gerador_sinais.log",
            "/root/gerador_sinais_telegram/gerador_sinais.log"
        ]

        log_path = None
        for path in log_paths:
            _, stdout, _ = ssh.exec_command(f"test -f {path} && echo 'exists'")
            if stdout.read().decode().strip() == 'exists':
                log_path = path
                break

        if log_path:
            print(f"{Colors.GREEN}📄 Monitorando: {log_path}{Colors.END}\n")
            _, stdout, _ = ssh.exec_command(f"tail -f {log_path}")

            while True:
                line = stdout.readline()
                if line:
                    # Colorir logs baseado no nível
                    line = line.strip()
                    if "ERROR" in line:
                        print(f"{Colors.RED}{line}{Colors.END}")
                    elif "WARNING" in line:
                        print(f"{Colors.YELLOW}{line}{Colors.END}")
                    elif "INFO" in line:
                        print(f"{Colors.GREEN}{line}{Colors.END}")
                    else:
                        print(line)
        else:
            print(f"{Colors.YELLOW}⚠️ Arquivo de log não encontrado, usando journalctl{Colors.END}")
            _, stdout, _ = ssh.exec_command("journalctl -u gerador_sinais.service -f")

            while True:
                line = stdout.readline()
                if line:
                    print(line.strip())

    except KeyboardInterrupt:
        print(f"\n{Colors.GREEN}✅ Voltando ao menu de logs{Colors.END}")
    except Exception as e:
        print(f"{Colors.RED}❌ Erro: {e}{Colors.END}")

def search_logs(monitor):
    """Busca em logs"""
    keyword = input(f"{Colors.BOLD}Digite a palavra-chave para buscar: {Colors.END}").strip()
    if not keyword:
        return

    print(f"\n{Colors.BLUE}🔍 Buscando por '{keyword}'...{Colors.END}")

    ssh = monitor.get_ssh_connection()
    if not ssh:
        print(f"{Colors.RED}❌ Erro de conexão SSH{Colors.END}")
        return

    # Buscar nos logs
    _, stdout, _ = ssh.exec_command(f"grep -n '{keyword}' /opt/gerador_sinais_telegram/gerador_sinais.log 2>/dev/null || grep -n '{keyword}' /root/gerador_sinais_telegram/gerador_sinais.log 2>/dev/null || journalctl -u gerador_sinais.service | grep '{keyword}'")

    results = stdout.read().decode().strip()
    if results:
        print(f"\n{Colors.GREEN}📋 Resultados encontrados:{Colors.END}")
        for line in results.split('\n')[:20]:  # Limitar a 20 resultados
            if keyword.lower() in line.lower():
                highlighted = line.replace(keyword, f"{Colors.YELLOW}{Colors.BOLD}{keyword}{Colors.END}")
                print(highlighted)
    else:
        print(f"{Colors.YELLOW}⚠️ Nenhum resultado encontrado para '{keyword}'{Colors.END}")

def alerts_menu(monitor):
    """Menu de alertas"""
    while True:
        print(f"\n{Colors.BLUE}{Colors.BOLD}🚨 GERENCIAMENTO DE ALERTAS{Colors.END}")
        print(f"{Colors.CYAN}{'='*50}{Colors.END}")

        active_alerts = [alert for alert in monitor.alerts if not alert.resolved]
        resolved_alerts = [alert for alert in monitor.alerts if alert.resolved]

        print(f"{Colors.GREEN}1.{Colors.END} 📋 Ver alertas ativos ({len(active_alerts)})")
        print(f"{Colors.GREEN}2.{Colors.END} ✅ Ver alertas resolvidos ({len(resolved_alerts)})")
        print(f"{Colors.GREEN}3.{Colors.END} 🔧 Configurar thresholds")
        print(f"{Colors.GREEN}4.{Colors.END} 🗑️  Limpar alertas antigos")
        print(f"{Colors.GREEN}5.{Colors.END} ⬅️  Voltar ao menu principal")

        choice = input(f"\n{Colors.BOLD}Escolha uma opção (1-5): {Colors.END}").strip()

        if choice == "1":
            show_active_alerts(active_alerts)
        elif choice == "2":
            show_resolved_alerts(resolved_alerts)
        elif choice == "3":
            configure_thresholds(monitor)
        elif choice == "4":
            clear_old_alerts(monitor)
        elif choice == "5":
            break
        else:
            print(f"{Colors.RED}❌ Opção inválida!{Colors.END}")

        if choice != "5":
            input(f"\n{Colors.CYAN}Pressione Enter para continuar...{Colors.END}")

def show_active_alerts(alerts):
    """Mostra alertas ativos"""
    if not alerts:
        print(f"\n{Colors.GREEN}✅ Nenhum alerta ativo{Colors.END}")
        return

    print(f"\n{Colors.RED}{Colors.BOLD}🚨 ALERTAS ATIVOS:{Colors.END}")
    for i, alert in enumerate(alerts, 1):
        level_color = Colors.RED if alert.level == "CRITICAL" else Colors.YELLOW
        print(f"\n{i}. {level_color}{alert.level}{Colors.END} - {alert.service}")
        print(f"   📝 {alert.message}")
        print(f"   🕐 {alert.timestamp}")

def show_resolved_alerts(alerts):
    """Mostra alertas resolvidos"""
    if not alerts:
        print(f"\n{Colors.GREEN}✅ Nenhum alerta resolvido{Colors.END}")
        return

    print(f"\n{Colors.GREEN}{Colors.BOLD}✅ ALERTAS RESOLVIDOS (últimos 10):{Colors.END}")
    for i, alert in enumerate(alerts[-10:], 1):
        print(f"\n{i}. {Colors.GREEN}{alert.level}{Colors.END} - {alert.service}")
        print(f"   📝 {alert.message}")
        print(f"   🕐 {alert.timestamp}")

def configure_thresholds(monitor):
    """Configura thresholds de alertas"""
    print(f"\n{Colors.BLUE}{Colors.BOLD}🔧 CONFIGURAÇÃO DE THRESHOLDS{Colors.END}")
    print(f"{Colors.CYAN}{'='*50}{Colors.END}")

    print(f"\n{Colors.YELLOW}Thresholds atuais:{Colors.END}")
    for key, value in monitor.alert_thresholds.items():
        print(f"  {key}: {value}")

    print(f"\n{Colors.CYAN}Para alterar, digite o nome e novo valor (ex: cpu_warning 80){Colors.END}")
    print(f"{Colors.CYAN}Digite 'voltar' para retornar{Colors.END}")

    while True:
        user_input = input(f"\n{Colors.BOLD}Configuração: {Colors.END}").strip()

        if user_input.lower() == 'voltar':
            break

        try:
            parts = user_input.split()
            if len(parts) == 2:
                key, value = parts
                if key in monitor.alert_thresholds:
                    monitor.alert_thresholds[key] = float(value)
                    print(f"{Colors.GREEN}✅ {key} atualizado para {value}{Colors.END}")
                else:
                    print(f"{Colors.RED}❌ Threshold '{key}' não encontrado{Colors.END}")
            else:
                print(f"{Colors.RED}❌ Formato inválido. Use: nome valor{Colors.END}")
        except ValueError:
            print(f"{Colors.RED}❌ Valor inválido{Colors.END}")

def clear_old_alerts(monitor):
    """Limpa alertas antigos"""
    days = input(f"{Colors.BOLD}Limpar alertas com mais de quantos dias? (padrão: 7): {Colors.END}").strip()

    try:
        days = int(days) if days else 7
        cutoff_date = datetime.datetime.now() - datetime.timedelta(days=days)

        initial_count = len(monitor.alerts)
        monitor.alerts = [
            alert for alert in monitor.alerts
            if datetime.datetime.fromisoformat(alert.timestamp) > cutoff_date
        ]

        removed_count = initial_count - len(monitor.alerts)
        monitor.save_alerts()

        print(f"{Colors.GREEN}✅ {removed_count} alertas removidos{Colors.END}")

    except ValueError:
        print(f"{Colors.RED}❌ Número de dias inválido{Colors.END}")

def metrics_menu(monitor):
    """Menu de métricas"""
    print(f"\n{Colors.BLUE}{Colors.BOLD}📈 MÉTRICAS E ESTATÍSTICAS{Colors.END}")
    print(f"{Colors.CYAN}Em desenvolvimento...{Colors.END}")

def diagnostic_menu(monitor):
    """Menu de diagnóstico"""
    print(f"\n{Colors.BLUE}{Colors.BOLD}🔧 FERRAMENTAS DE DIAGNÓSTICO{Colors.END}")
    print(f"{Colors.CYAN}Em desenvolvimento...{Colors.END}")

def settings_menu(monitor):
    """Menu de configurações"""
    print(f"\n{Colors.BLUE}{Colors.BOLD}⚙️ CONFIGURAÇÕES{Colors.END}")
    print(f"{Colors.CYAN}Em desenvolvimento...{Colors.END}")

if __name__ == "__main__":
    main()