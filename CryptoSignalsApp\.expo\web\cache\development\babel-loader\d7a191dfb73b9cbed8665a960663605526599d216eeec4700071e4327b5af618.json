{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"style\", \"type\", \"visible\", \"theme\", \"onLayout\", \"padding\", \"disabled\"];\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nimport * as React from 'react';\nimport Animated from \"react-native-web/dist/exports/Animated\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport { getTextColor } from \"./utils\";\nimport { useInternalTheme } from \"../../core/theming\";\nimport AnimatedText from \"../Typography/AnimatedText\";\nvar HelperText = function HelperText(_ref) {\n  var style = _ref.style,\n    _ref$type = _ref.type,\n    type = _ref$type === void 0 ? 'info' : _ref$type,\n    _ref$visible = _ref.visible,\n    visible = _ref$visible === void 0 ? true : _ref$visible,\n    themeOverrides = _ref.theme,\n    onLayout = _ref.onLayout,\n    _ref$padding = _ref.padding,\n    padding = _ref$padding === void 0 ? 'normal' : _ref$padding,\n    disabled = _ref.disabled,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var theme = useInternalTheme(themeOverrides);\n  var _React$useRef = React.useRef(new Animated.Value(visible ? 1 : 0)),\n    shown = _React$useRef.current;\n  var _React$useRef2 = React.useRef(0),\n    textHeight = _React$useRef2.current;\n  var scale = theme.animation.scale;\n  var _rest$maxFontSizeMult = rest.maxFontSizeMultiplier,\n    maxFontSizeMultiplier = _rest$maxFontSizeMult === void 0 ? 1.5 : _rest$maxFontSizeMult;\n  React.useEffect(function () {\n    if (visible) {\n      Animated.timing(shown, {\n        toValue: 1,\n        duration: 150 * scale,\n        useNativeDriver: true\n      }).start();\n    } else {\n      Animated.timing(shown, {\n        toValue: 0,\n        duration: 180 * scale,\n        useNativeDriver: true\n      }).start();\n    }\n  }, [visible, scale, shown]);\n  var handleTextLayout = function handleTextLayout(e) {\n    onLayout === null || onLayout === void 0 || onLayout(e);\n    textHeight = e.nativeEvent.layout.height;\n  };\n  var textColor = getTextColor({\n    theme: theme,\n    disabled: disabled,\n    type: type\n  });\n  return React.createElement(AnimatedText, _extends({\n    onLayout: handleTextLayout,\n    style: [styles.text, padding !== 'none' ? styles.padding : {}, {\n      color: textColor,\n      opacity: shown,\n      transform: visible && type === 'error' ? [{\n        translateY: shown.interpolate({\n          inputRange: [0, 1],\n          outputRange: [-textHeight / 2, 0]\n        })\n      }] : []\n    }, style],\n    maxFontSizeMultiplier: maxFontSizeMultiplier\n  }, rest), rest.children);\n};\nvar styles = StyleSheet.create({\n  text: {\n    fontSize: 12,\n    paddingVertical: 4\n  },\n  padding: {\n    paddingHorizontal: 12\n  }\n});\nexport default HelperText;", "map": {"version": 3, "names": ["React", "Animated", "StyleSheet", "getTextColor", "useInternalTheme", "AnimatedText", "HelperText", "_ref", "style", "_ref$type", "type", "_ref$visible", "visible", "themeOverrides", "theme", "onLayout", "_ref$padding", "padding", "disabled", "rest", "_objectWithoutProperties", "_excluded", "_React$useRef", "useRef", "Value", "shown", "current", "_React$useRef2", "textHeight", "scale", "animation", "_rest$maxFontSizeMult", "maxFontSizeMultiplier", "useEffect", "timing", "toValue", "duration", "useNativeDriver", "start", "handleTextLayout", "e", "nativeEvent", "layout", "height", "textColor", "createElement", "_extends", "styles", "text", "color", "opacity", "transform", "translateY", "interpolate", "inputRange", "outputRange", "children", "create", "fontSize", "paddingVertical", "paddingHorizontal"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\HelperText\\HelperText.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  Animated,\n  LayoutChangeEvent,\n  StyleProp,\n  StyleSheet,\n  TextStyle,\n} from 'react-native';\n\nimport { getTextColor } from './utils';\nimport { useInternalTheme } from '../../core/theming';\nimport type { $Omit, ThemeProp } from '../../types';\nimport AnimatedText from '../Typography/AnimatedText';\n\nexport type Props = $Omit<\n  $Omit<React.ComponentPropsWithRef<typeof AnimatedText>, 'padding'>,\n  'type'\n> & {\n  /**\n   * Type of the helper text.\n   */\n  type: 'error' | 'info';\n  /**\n   * Text content of the HelperText.\n   */\n  children: React.ReactNode;\n  /**\n   * Whether to display the helper text.\n   */\n  visible?: boolean;\n  /**\n   * Whether to apply padding to the helper text.\n   */\n  padding?: 'none' | 'normal';\n  /**\n   * Whether the text input tied with helper text is disabled.\n   */\n  disabled?: boolean;\n  style?: StyleProp<TextStyle>;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n  /**\n   * TestID used for testing purposes\n   */\n  testID?: string;\n};\n\n/**\n * Helper text is used in conjuction with input elements to provide additional hints for the user.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { View } from 'react-native';\n * import { HelperText, TextInput } from 'react-native-paper';\n *\n * const MyComponent = () => {\n *   const [text, setText] = React.useState('');\n *\n *    const onChangeText = text => setText(text);\n *\n *   const hasErrors = () => {\n *     return !text.includes('@');\n *   };\n *\n *  return (\n *     <View>\n *       <TextInput label=\"Email\" value={text} onChangeText={onChangeText} />\n *       <HelperText type=\"error\" visible={hasErrors()}>\n *         Email address is invalid!\n *       </HelperText>\n *     </View>\n *   );\n * };\n *\n * export default MyComponent;\n * ```\n */\nconst HelperText = ({\n  style,\n  type = 'info',\n  visible = true,\n  theme: themeOverrides,\n  onLayout,\n  padding = 'normal',\n  disabled,\n  ...rest\n}: Props) => {\n  const theme = useInternalTheme(themeOverrides);\n  const { current: shown } = React.useRef<Animated.Value>(\n    new Animated.Value(visible ? 1 : 0)\n  );\n\n  let { current: textHeight } = React.useRef<number>(0);\n\n  const { scale } = theme.animation;\n\n  const { maxFontSizeMultiplier = 1.5 } = rest;\n\n  React.useEffect(() => {\n    if (visible) {\n      // show text\n      Animated.timing(shown, {\n        toValue: 1,\n        duration: 150 * scale,\n        useNativeDriver: true,\n      }).start();\n    } else {\n      // hide text\n      Animated.timing(shown, {\n        toValue: 0,\n        duration: 180 * scale,\n        useNativeDriver: true,\n      }).start();\n    }\n  }, [visible, scale, shown]);\n\n  const handleTextLayout = (e: LayoutChangeEvent) => {\n    onLayout?.(e);\n    textHeight = e.nativeEvent.layout.height;\n  };\n\n  const textColor = getTextColor({ theme, disabled, type });\n\n  return (\n    <AnimatedText\n      onLayout={handleTextLayout}\n      style={[\n        styles.text,\n        padding !== 'none' ? styles.padding : {},\n        {\n          color: textColor,\n          opacity: shown,\n          transform:\n            visible && type === 'error'\n              ? [\n                  {\n                    translateY: shown.interpolate({\n                      inputRange: [0, 1],\n                      outputRange: [-textHeight / 2, 0],\n                    }),\n                  },\n                ]\n              : [],\n        },\n        style,\n      ]}\n      maxFontSizeMultiplier={maxFontSizeMultiplier}\n      {...rest}\n    >\n      {rest.children}\n    </AnimatedText>\n  );\n};\n\nconst styles = StyleSheet.create({\n  text: {\n    fontSize: 12,\n    paddingVertical: 4,\n  },\n  padding: {\n    paddingHorizontal: 12,\n  },\n});\n\nexport default HelperText;\n"], "mappings": ";;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAAA,OAAAC,UAAA;AAS9B,SAASC,YAAY;AACrB,SAASC,gBAAgB;AAEzB,OAAOC,YAAY;AAoEnB,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAAC,IAAA,EASH;EAAA,IARXC,KAAK,GAAAD,IAAA,CAALC,KAAK;IAAAC,SAAA,GAAAF,IAAA,CACLG,IAAI;IAAJA,IAAI,GAAAD,SAAA,cAAG,MAAM,GAAAA,SAAA;IAAAE,YAAA,GAAAJ,IAAA,CACbK,OAAO;IAAPA,OAAO,GAAAD,YAAA,cAAG,IAAI,GAAAA,YAAA;IACPE,cAAc,GAAAN,IAAA,CAArBO,KAAK;IACLC,QAAQ,GAAAR,IAAA,CAARQ,QAAQ;IAAAC,YAAA,GAAAT,IAAA,CACRU,OAAO;IAAPA,OAAO,GAAAD,YAAA,cAAG,QAAQ,GAAAA,YAAA;IAClBE,QAAQ,GAAAX,IAAA,CAARW,QAAQ;IACLC,IAAA,GAAAC,wBAAA,CAAAb,IAAA,EAAAc,SAAA;EAEH,IAAMP,KAAK,GAAGV,gBAAgB,CAACS,cAAc,CAAC;EAC9C,IAAAS,aAAA,GAA2BtB,KAAK,CAACuB,MAAM,CACrC,IAAItB,QAAQ,CAACuB,KAAK,CAACZ,OAAO,GAAG,CAAC,GAAG,CAAC,CACpC,CAAC;IAFgBa,KAAA,GAAAH,aAAA,CAATI,OAAO;EAIf,IAAAC,cAAA,GAA8B3B,KAAK,CAACuB,MAAM,CAAS,CAAC,CAAC;IAAtCK,UAAA,GAAAD,cAAA,CAATD,OAAO;EAEb,IAAQG,KAAA,GAAUf,KAAK,CAACgB,SAAS,CAAzBD,KAAA;EAER,IAAAE,qBAAA,GAAwCZ,IAAI,CAApCa,qBAAqB;IAArBA,qBAAqB,GAAAD,qBAAA,cAAG,MAAAA,qBAAA;EAEhC/B,KAAK,CAACiC,SAAS,CAAC,YAAM;IACpB,IAAIrB,OAAO,EAAE;MAEXX,QAAQ,CAACiC,MAAM,CAACT,KAAK,EAAE;QACrBU,OAAO,EAAE,CAAC;QACVC,QAAQ,EAAE,GAAG,GAAGP,KAAK;QACrBQ,eAAe,EAAE;MACnB,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;IACZ,CAAC,MAAM;MAELrC,QAAQ,CAACiC,MAAM,CAACT,KAAK,EAAE;QACrBU,OAAO,EAAE,CAAC;QACVC,QAAQ,EAAE,GAAG,GAAGP,KAAK;QACrBQ,eAAe,EAAE;MACnB,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;IACZ;EACF,CAAC,EAAE,CAAC1B,OAAO,EAAEiB,KAAK,EAAEJ,KAAK,CAAC,CAAC;EAE3B,IAAMc,gBAAgB,GAAI,SAApBA,gBAAgBA,CAAIC,CAAoB,EAAK;IACjDzB,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAGyB,CAAC,CAAC;IACbZ,UAAU,GAAGY,CAAC,CAACC,WAAW,CAACC,MAAM,CAACC,MAAM;EAC1C,CAAC;EAED,IAAMC,SAAS,GAAGzC,YAAY,CAAC;IAAEW,KAAK,EAALA,KAAK;IAAEI,QAAQ,EAARA,QAAQ;IAAER,IAAA,EAAAA;EAAK,CAAC,CAAC;EAEzD,OACEV,KAAA,CAAA6C,aAAA,CAACxC,YAAY,EAAAyC,QAAA;IACX/B,QAAQ,EAAEwB,gBAAiB;IAC3B/B,KAAK,EAAE,CACLuC,MAAM,CAACC,IAAI,EACX/B,OAAO,KAAK,MAAM,GAAG8B,MAAM,CAAC9B,OAAO,GAAG,CAAC,CAAC,EACxC;MACEgC,KAAK,EAAEL,SAAS;MAChBM,OAAO,EAAEzB,KAAK;MACd0B,SAAS,EACPvC,OAAO,IAAIF,IAAI,KAAK,OAAO,GACvB,CACE;QACE0C,UAAU,EAAE3B,KAAK,CAAC4B,WAAW,CAAC;UAC5BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;UAClBC,WAAW,EAAE,CAAC,CAAC3B,UAAU,GAAG,CAAC,EAAE,CAAC;QAClC,CAAC;MACH,CAAC,CACF,GACD;IACR,CAAC,EACDpB,KAAK,CACL;IACFwB,qBAAqB,EAAEA;EAAsB,GACzCb,IAAI,GAEPA,IAAI,CAACqC,QACM,CAAC;AAEnB,CAAC;AAED,IAAMT,MAAM,GAAG7C,UAAU,CAACuD,MAAM,CAAC;EAC/BT,IAAI,EAAE;IACJU,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE;EACnB,CAAC;EACD1C,OAAO,EAAE;IACP2C,iBAAiB,EAAE;EACrB;AACF,CAAC,CAAC;AAEF,eAAetD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}