{"ast": null, "code": "import React from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport TouchableOpacity from \"react-native-web/dist/exports/TouchableOpacity\";\nimport { Surface } from 'react-native-paper';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nvar Card = function Card(_ref) {\n  var children = _ref.children,\n    style = _ref.style,\n    onPress = _ref.onPress,\n    _ref$elevation = _ref.elevation,\n    elevation = _ref$elevation === void 0 ? 2 : _ref$elevation,\n    _ref$padding = _ref.padding,\n    padding = _ref$padding === void 0 ? 16 : _ref$padding,\n    _ref$margin = _ref.margin,\n    margin = _ref$margin === void 0 ? 8 : _ref$margin,\n    _ref$backgroundColor = _ref.backgroundColor,\n    backgroundColor = _ref$backgroundColor === void 0 ? '#2a2a2a' : _ref$backgroundColor;\n  var cardStyle = {\n    backgroundColor: backgroundColor,\n    borderRadius: 12,\n    padding: padding,\n    margin: margin,\n    borderWidth: 1,\n    borderColor: '#333',\n    shadowColor: '#000',\n    shadowOffset: {\n      width: 0,\n      height: 2\n    },\n    shadowOpacity: 0.25,\n    shadowRadius: 3.84,\n    elevation: elevation\n  };\n  if (onPress) {\n    return _jsx(TouchableOpacity, {\n      style: cardStyle,\n      onPress: onPress,\n      activeOpacity: 0.8,\n      children: children\n    });\n  }\n  return _jsx(View, {\n    style: [cardStyle, style],\n    children: children\n  });\n};\nexport default Card;", "map": {"version": 3, "names": ["React", "View", "Text", "TouchableOpacity", "Surface", "jsx", "_jsx", "Card", "_ref", "children", "style", "onPress", "_ref$elevation", "elevation", "_ref$padding", "padding", "_ref$margin", "margin", "_ref$backgroundColor", "backgroundColor", "cardStyle", "borderRadius", "borderWidth", "borderColor", "shadowColor", "shadowOffset", "width", "height", "shadowOpacity", "shadowRadius", "activeOpacity"], "sources": ["E:/CryptoSignalsApp/src/components/Card/index.js"], "sourcesContent": ["import React from 'react';\nimport { View, Text, TouchableOpacity } from 'react-native';\nimport { Surface } from 'react-native-paper';\n\nconst Card = ({ \n  children, \n  style, \n  onPress, \n  elevation = 2,\n  padding = 16,\n  margin = 8,\n  backgroundColor = '#2a2a2a'\n}) => {\n  const cardStyle = {\n    backgroundColor,\n    borderRadius: 12,\n    padding,\n    margin,\n    borderWidth: 1,\n    borderColor: '#333',\n    shadowColor: '#000',\n    shadowOffset: {\n      width: 0,\n      height: 2,\n    },\n    shadowOpacity: 0.25,\n    shadowRadius: 3.84,\n    elevation,\n  };\n\n  if (onPress) {\n    return (\n      <TouchableOpacity style={cardStyle} onPress={onPress} activeOpacity={0.8}>\n        {children}\n      </TouchableOpacity>\n    );\n  }\n\n  return (\n    <View style={[cardStyle, style]}>\n      {children}\n    </View>\n  );\n};\n\nexport default Card;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,gBAAA;AAE1B,SAASC,OAAO,QAAQ,oBAAoB;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAE7C,IAAMC,IAAI,GAAG,SAAPA,IAAIA,CAAAC,IAAA,EAQJ;EAAA,IAPJC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;IACRC,KAAK,GAAAF,IAAA,CAALE,KAAK;IACLC,OAAO,GAAAH,IAAA,CAAPG,OAAO;IAAAC,cAAA,GAAAJ,IAAA,CACPK,SAAS;IAATA,SAAS,GAAAD,cAAA,cAAG,CAAC,GAAAA,cAAA;IAAAE,YAAA,GAAAN,IAAA,CACbO,OAAO;IAAPA,OAAO,GAAAD,YAAA,cAAG,EAAE,GAAAA,YAAA;IAAAE,WAAA,GAAAR,IAAA,CACZS,MAAM;IAANA,MAAM,GAAAD,WAAA,cAAG,CAAC,GAAAA,WAAA;IAAAE,oBAAA,GAAAV,IAAA,CACVW,eAAe;IAAfA,eAAe,GAAAD,oBAAA,cAAG,SAAS,GAAAA,oBAAA;EAE3B,IAAME,SAAS,GAAG;IAChBD,eAAe,EAAfA,eAAe;IACfE,YAAY,EAAE,EAAE;IAChBN,OAAO,EAAPA,OAAO;IACPE,MAAM,EAANA,MAAM;IACNK,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE,MAAM;IACnBC,WAAW,EAAE,MAAM;IACnBC,YAAY,EAAE;MACZC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE;IACV,CAAC;IACDC,aAAa,EAAE,IAAI;IACnBC,YAAY,EAAE,IAAI;IAClBhB,SAAS,EAATA;EACF,CAAC;EAED,IAAIF,OAAO,EAAE;IACX,OACEL,IAAA,CAACH,gBAAgB;MAACO,KAAK,EAAEU,SAAU;MAACT,OAAO,EAAEA,OAAQ;MAACmB,aAAa,EAAE,GAAI;MAAArB,QAAA,EACtEA;IAAQ,CACO,CAAC;EAEvB;EAEA,OACEH,IAAA,CAACL,IAAI;IAACS,KAAK,EAAE,CAACU,SAAS,EAAEV,KAAK,CAAE;IAAAD,QAAA,EAC7BA;EAAQ,CACL,CAAC;AAEX,CAAC;AAED,eAAeF,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}