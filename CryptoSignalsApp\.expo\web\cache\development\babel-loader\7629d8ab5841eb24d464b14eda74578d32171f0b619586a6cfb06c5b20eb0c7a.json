{"ast": null, "code": "export { useFonts } from \"./useFonts\";\nexport { default as __metadata__ } from \"./metadata.json\";\nexport var Poppins_100Thin = require(\"./Poppins_100Thin.ttf\");\nexport var Poppins_100Thin_Italic = require(\"./Poppins_100Thin_Italic.ttf\");\nexport var Poppins_200ExtraLight = require(\"./Poppins_200ExtraLight.ttf\");\nexport var Poppins_200ExtraLight_Italic = require(\"./Poppins_200ExtraLight_Italic.ttf\");\nexport var Poppins_300Light = require(\"./Poppins_300Light.ttf\");\nexport var Poppins_300Light_Italic = require(\"./Poppins_300Light_Italic.ttf\");\nexport var Poppins_400Regular = require(\"./Poppins_400Regular.ttf\");\nexport var Poppins_400Regular_Italic = require(\"./Poppins_400Regular_Italic.ttf\");\nexport var Poppins_500Medium = require(\"./Poppins_500Medium.ttf\");\nexport var Poppins_500Medium_Italic = require(\"./Poppins_500Medium_Italic.ttf\");\nexport var Poppins_600SemiBold = require(\"./Poppins_600SemiBold.ttf\");\nexport var Poppins_600SemiBold_Italic = require(\"./Poppins_600SemiBold_Italic.ttf\");\nexport var Poppins_700Bold = require(\"./Poppins_700Bold.ttf\");\nexport var Poppins_700Bold_Italic = require(\"./Poppins_700Bold_Italic.ttf\");\nexport var Poppins_800ExtraBold = require(\"./Poppins_800ExtraBold.ttf\");\nexport var Poppins_800ExtraBold_Italic = require(\"./Poppins_800ExtraBold_Italic.ttf\");\nexport var Poppins_900Black = require(\"./Poppins_900Black.ttf\");\nexport var Poppins_900Black_Italic = require(\"./Poppins_900Black_Italic.ttf\");", "map": {"version": 3, "names": ["useFonts", "default", "__metadata__", "Poppins_100Thin", "require", "Poppins_100Thin_Italic", "Poppins_200ExtraLight", "Poppins_200ExtraLight_Italic", "Poppins_300Light", "Poppins_300Light_Italic", "Poppins_400Regular", "Poppins_400Regular_Italic", "Poppins_500Medium", "Poppins_500Medium_Italic", "Poppins_600SemiBold", "Poppins_600SemiBold_Italic", "Poppins_700Bold", "Poppins_700Bold_Italic", "Poppins_800ExtraBold", "Poppins_800ExtraBold_Italic", "Poppins_900Black", "Poppins_900Black_Italic"], "sources": ["E:/CryptoSignalsApp/node_modules/@expo-google-fonts/poppins/index.js"], "sourcesContent": ["/// Generated by expo-google-fonts/generator\n/// Do not edit by hand unless you know what you are doing\n///\n\nexport { useFonts } from './useFonts';\n\nexport { default as __metadata__ } from './metadata.json';\nexport const Poppins_100Thin = require('./Poppins_100Thin.ttf');\nexport const Poppins_100Thin_Italic = require('./Poppins_100Thin_Italic.ttf');\nexport const Poppins_200ExtraLight = require('./Poppins_200ExtraLight.ttf');\nexport const Poppins_200ExtraLight_Italic = require('./Poppins_200ExtraLight_Italic.ttf');\nexport const Poppins_300Light = require('./Poppins_300Light.ttf');\nexport const Poppins_300Light_Italic = require('./Poppins_300Light_Italic.ttf');\nexport const Poppins_400Regular = require('./Poppins_400Regular.ttf');\nexport const Poppins_400Regular_Italic = require('./Poppins_400Regular_Italic.ttf');\nexport const Poppins_500Medium = require('./Poppins_500Medium.ttf');\nexport const Poppins_500Medium_Italic = require('./Poppins_500Medium_Italic.ttf');\nexport const Poppins_600SemiBold = require('./Poppins_600SemiBold.ttf');\nexport const Poppins_600SemiBold_Italic = require('./Poppins_600SemiBold_Italic.ttf');\nexport const Poppins_700Bold = require('./Poppins_700Bold.ttf');\nexport const Poppins_700Bold_Italic = require('./Poppins_700Bold_Italic.ttf');\nexport const Poppins_800ExtraBold = require('./Poppins_800ExtraBold.ttf');\nexport const Poppins_800ExtraBold_Italic = require('./Poppins_800ExtraBold_Italic.ttf');\nexport const Poppins_900Black = require('./Poppins_900Black.ttf');\nexport const Poppins_900Black_Italic = require('./Poppins_900Black_Italic.ttf');\n"], "mappings": "AAIA,SAASA,QAAQ;AAEjB,SAASC,OAAO,IAAIC,YAAY;AAChC,OAAO,IAAMC,eAAe,GAAGC,OAAO,wBAAwB,CAAC;AAC/D,OAAO,IAAMC,sBAAsB,GAAGD,OAAO,+BAA+B,CAAC;AAC7E,OAAO,IAAME,qBAAqB,GAAGF,OAAO,8BAA8B,CAAC;AAC3E,OAAO,IAAMG,4BAA4B,GAAGH,OAAO,qCAAqC,CAAC;AACzF,OAAO,IAAMI,gBAAgB,GAAGJ,OAAO,yBAAyB,CAAC;AACjE,OAAO,IAAMK,uBAAuB,GAAGL,OAAO,gCAAgC,CAAC;AAC/E,OAAO,IAAMM,kBAAkB,GAAGN,OAAO,2BAA2B,CAAC;AACrE,OAAO,IAAMO,yBAAyB,GAAGP,OAAO,kCAAkC,CAAC;AACnF,OAAO,IAAMQ,iBAAiB,GAAGR,OAAO,0BAA0B,CAAC;AACnE,OAAO,IAAMS,wBAAwB,GAAGT,OAAO,iCAAiC,CAAC;AACjF,OAAO,IAAMU,mBAAmB,GAAGV,OAAO,4BAA4B,CAAC;AACvE,OAAO,IAAMW,0BAA0B,GAAGX,OAAO,mCAAmC,CAAC;AACrF,OAAO,IAAMY,eAAe,GAAGZ,OAAO,wBAAwB,CAAC;AAC/D,OAAO,IAAMa,sBAAsB,GAAGb,OAAO,+BAA+B,CAAC;AAC7E,OAAO,IAAMc,oBAAoB,GAAGd,OAAO,6BAA6B,CAAC;AACzE,OAAO,IAAMe,2BAA2B,GAAGf,OAAO,oCAAoC,CAAC;AACvF,OAAO,IAAMgB,gBAAgB,GAAGhB,OAAO,yBAAyB,CAAC;AACjE,OAAO,IAAMiB,uBAAuB,GAAGjB,OAAO,gCAAgC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}