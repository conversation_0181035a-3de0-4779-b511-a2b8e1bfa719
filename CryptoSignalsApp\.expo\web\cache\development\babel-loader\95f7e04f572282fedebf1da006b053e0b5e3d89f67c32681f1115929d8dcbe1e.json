{"ast": null, "code": "export {};", "map": {"version": 3, "names": [], "sources": ["E:\\CryptoSignalsApp\\node_modules\\react-native-screens\\src\\types.tsx"], "sourcesContent": ["import React from 'react';\nimport {\n  Animated,\n  NativeSyntheticEvent,\n  ViewProps,\n  View,\n  TargetedEvent,\n  TextInputFocusEventData,\n  ColorValue,\n} from 'react-native';\nimport { NativeStackNavigatorProps } from './native-stack/types';\n\nexport type SearchBarCommands = {\n  focus: () => void;\n  blur: () => void;\n  clearText: () => void;\n  toggleCancelButton: (show: boolean) => void;\n  setText: (text: string) => void;\n  cancelSearch: () => void;\n};\n\nexport type BackButtonDisplayMode = 'default' | 'generic' | 'minimal';\nexport type StackPresentationTypes =\n  | 'push'\n  | 'modal'\n  | 'transparentModal'\n  | 'containedModal'\n  | 'containedTransparentModal'\n  | 'fullScreenModal'\n  | 'formSheet';\nexport type StackAnimationTypes =\n  | 'default'\n  | 'fade'\n  | 'fade_from_bottom'\n  | 'flip'\n  | 'none'\n  | 'simple_push'\n  | 'slide_from_bottom'\n  | 'slide_from_right'\n  | 'slide_from_left'\n  | 'ios_from_right'\n  | 'ios_from_left';\nexport type BlurEffectTypes =\n  | 'none'\n  | 'extraLight'\n  | 'light'\n  | 'dark'\n  | 'regular'\n  | 'prominent'\n  | 'systemUltraThinMaterial'\n  | 'systemThinMaterial'\n  | 'systemMaterial'\n  | 'systemThickMaterial'\n  | 'systemChromeMaterial'\n  | 'systemUltraThinMaterialLight'\n  | 'systemThinMaterialLight'\n  | 'systemMaterialLight'\n  | 'systemThickMaterialLight'\n  | 'systemChromeMaterialLight'\n  | 'systemUltraThinMaterialDark'\n  | 'systemThinMaterialDark'\n  | 'systemMaterialDark'\n  | 'systemThickMaterialDark'\n  | 'systemChromeMaterialDark';\nexport type ScreenReplaceTypes = 'push' | 'pop';\nexport type SwipeDirectionTypes = 'vertical' | 'horizontal';\nexport type ScreenOrientationTypes =\n  | 'default'\n  | 'all'\n  | 'portrait'\n  | 'portrait_up'\n  | 'portrait_down'\n  | 'landscape'\n  | 'landscape_left'\n  | 'landscape_right';\nexport type HeaderSubviewTypes =\n  | 'back'\n  | 'right'\n  | 'left'\n  | 'center'\n  | 'searchBar';\n\nexport type HeaderHeightChangeEventType = {\n  headerHeight: number;\n};\n\nexport type TransitionProgressEventType = {\n  progress: number;\n  closing: number;\n  goingForward: number;\n};\n\nexport type GestureResponseDistanceType = {\n  start?: number;\n  end?: number;\n  top?: number;\n  bottom?: number;\n};\n\nexport type SearchBarPlacement = 'automatic' | 'inline' | 'stacked';\n\nexport interface ScreenProps extends ViewProps {\n  active?: 0 | 1 | Animated.AnimatedInterpolation<number>;\n  activityState?: 0 | 1 | 2 | Animated.AnimatedInterpolation<number>;\n  /**\n   * Boolean indicating that the screen should be frozen with `react-freeze`.\n   */\n  shouldFreeze?: boolean;\n  children?: React.ReactNode;\n  /**\n   * Boolean indicating that swipe dismissal should trigger animation provided by `stackAnimation`. Defaults to `false`.\n   *\n   * @platform ios\n   */\n  customAnimationOnSwipe?: boolean;\n  /**\n   * All children screens should have the same value of their \"enabled\" prop as their container.\n   */\n  enabled?: boolean;\n  /**\n   * Internal boolean used to not attach events used only by native-stack. It prevents non native-stack navigators from sending transition progress from their Screen components.\n   */\n  isNativeStack?: boolean;\n  /**\n   * Internal boolean used to detect if current header has large title on iOS.\n   */\n  hasLargeHeader?: boolean;\n  /**\n   * Whether inactive screens should be suspended from re-rendering. Defaults to `false`.\n   * When `enableFreeze()` is run at the top of the application defaults to `true`.\n   */\n  freezeOnBlur?: boolean;\n  /**\n   * Boolean indicating whether the swipe gesture should work on whole screen. Swiping with this option results in the same transition animation as `simple_push` by default.\n   * It can be changed to other custom animations with `customAnimationOnSwipe` prop, but default iOS swipe animation is not achievable due to usage of custom recognizer.\n   * Defaults to `false`.\n   *\n   * @platform ios\n   */\n  fullScreenSwipeEnabled?: boolean;\n  /**\n   * Whether the full screen dismiss gesture has shadow under view during transition.\n   * When enabled, a custom shadow view is added during the transition which tries to mimic the\n   * default iOS shadow. Defaults to `true`.\n   *\n   * This does not affect the behavior of transitions that don't use gestures, enabled by `fullScreenGestureEnabled` prop.\n   *\n   * @platform ios\n   */\n  fullScreenSwipeShadowEnabled?: boolean;\n  /**\n   * Whether you can use gestures to dismiss this screen. Defaults to `true`.\n   *\n   * @platform ios\n   */\n  gestureEnabled?: boolean;\n  /**\n   * Use it to restrict the distance from the edges of screen in which the gesture should be recognized. To be used alongside `fullScreenSwipeEnabled`.\n   *\n   * @platform ios\n   */\n  gestureResponseDistance?: GestureResponseDistanceType;\n  /**\n   * Whether the home indicator should be hidden on this screen. Defaults to `false`.\n   *\n   * @platform ios\n   */\n  homeIndicatorHidden?: boolean;\n  /**\n   * Whether the keyboard should hide when swiping to the previous screen. Defaults to `false`.\n   *\n   * @platform ios\n   */\n  hideKeyboardOnSwipe?: boolean;\n  /**\n   * Boolean indicating whether, when the Android default back button is clicked, the `pop` action should be performed on the native side or on the JS side to be able to prevent it.\n   * Unfortunately the same behavior is not available on iOS since the behavior of native back button cannot be changed there.\n   * Defaults to `false`.\n   *\n   * @platform android\n   */\n  nativeBackButtonDismissalEnabled?: boolean;\n  /**\n   * Sets the navigation bar color. Defaults to initial status bar color.\n   *\n   * @platform android\n   *\n   * @deprecated For all apps targeting Android SDK 35 or above this prop has no effect and is subject to removal in the future.\n   *  For SDK below 35 this works only with specific app setup.\n   *  This props is subject to removal in the future.\n   *  See: https://developer.android.com/reference/android/view/Window#setNavigationBarColor(int)\n   */\n  navigationBarColor?: ColorValue;\n  /**\n   * Boolean indicating whether the content should be visible behind the navigation bar. Defaults to `false`.\n   *\n   * @platform android\n   *\n   * @deprecated For all apps targeting Android SDK 35 or above edge-to-edge is enabled by default.\n   *  We expect that in future SDKs this option will be enforced.\n   *  This prop is subject to removal in the future.\n   *  See: https://developer.android.com/about/versions/15/behavior-changes-15#window-insets\n   */\n  navigationBarTranslucent?: boolean;\n  /**\n   * Sets the visibility of the navigation bar. Defaults to `false`.\n   *\n   * @platform android\n   */\n  navigationBarHidden?: boolean;\n  /**\n   * A callback that gets called when the current screen appears.\n   */\n  onAppear?: (e: NativeSyntheticEvent<TargetedEvent>) => void;\n  onComponentRef?: (view: unknown) => void;\n  /**\n   * A callback that gets called when the current screen disappears.\n   */\n  onDisappear?: (e: NativeSyntheticEvent<TargetedEvent>) => void;\n  /**\n   * A callback that gets called when the current screen is dismissed by hardware back (on Android) or dismiss gesture (swipe back or down).\n   * The callback takes the number of dismissed screens as an argument since iOS 14 native header back button can pop more than 1 screen at a time.\n   */\n  onDismissed?: (e: NativeSyntheticEvent<{ dismissCount: number }>) => void;\n  /**\n   * A callback that gets called when the header height has changed.\n   */\n  onHeaderHeightChange?: (\n    e: NativeSyntheticEvent<HeaderHeightChangeEventType>,\n  ) => void;\n  /**\n   * A callback that gets called after swipe back is canceled.\n   */\n  onGestureCancel?: (e: NativeSyntheticEvent<null>) => void;\n  /**\n   * An internal callback that gets called when the native header back button is clicked on Android and `enableNativeBackButtonDismissal` is set to `false`. It dismises the screen using `navigation.pop()`.\n   *\n   * @platform android\n   */\n  onHeaderBackButtonClicked?: () => void;\n  /**\n   * An internal callback called when screen is dismissed by gesture or by native header back button and `preventNativeDismiss` is set to `true`.\n   *\n   * @platform ios\n   */\n  onNativeDismissCancelled?: (\n    e: NativeSyntheticEvent<{ dismissCount: number }>,\n  ) => void;\n  /**\n   * A callback that gets called when the current screen is in `formSheet` presentation and its detent has changed.\n   */\n  onSheetDetentChanged?: (\n    e: NativeSyntheticEvent<{ index: number; isStable: boolean }>,\n  ) => void;\n  /**\n   * An internal callback called every frame during the transition of screens of `native-stack`, used to feed transition context.\n   */\n  onTransitionProgress?: (\n    e: NativeSyntheticEvent<TransitionProgressEventType>,\n  ) => void;\n  /**\n   * A callback that gets called when the current screen will appear. This is called as soon as the transition begins.\n   */\n  onWillAppear?: (e: NativeSyntheticEvent<TargetedEvent>) => void;\n  /**\n   * A callback that gets called when the current screen will disappear. This is called as soon as the transition begins.\n   */\n  onWillDisappear?: (e: NativeSyntheticEvent<TargetedEvent>) => void;\n  /**\n   * Boolean indicating whether to prevent current screen from being dismissed.\n   * Defaults to `false`.\n   *\n   * @platform ios\n   */\n  preventNativeDismiss?: boolean;\n  ref?: React.Ref<View>;\n  /**\n   * How should the screen replacing another screen animate. Defaults to `pop`.\n   * The following values are currently supported:\n   * - \"push\" – the new screen will perform push animation.\n   * - \"pop\" – the new screen will perform pop animation.\n   */\n  replaceAnimation?: ScreenReplaceTypes;\n  /**\n   * In which orientation should the screen appear.\n   * The following values are currently supported:\n   * - \"default\" - resolves to \"all\" without \"portrait_down\" on iOS. On Android, this lets the system decide the best orientation.\n   * - \"all\" – all orientations are permitted\n   * - \"portrait\" – portrait orientations are permitted\n   * - \"portrait_up\" – right-side portrait orientation is permitted\n   * - \"portrait_down\" – upside-down portrait orientation is permitted\n   * - \"landscape\" – landscape orientations are permitted\n   * - \"landscape_left\" – landscape-left orientation is permitted\n   * - \"landscape_right\" – landscape-right orientation is permitted\n   */\n  screenOrientation?: ScreenOrientationTypes;\n  /**\n   * Describes heights where a sheet can rest.\n   * Works only when `presentation` is set to `formSheet`.\n   *\n   * Heights should be described as fraction (a number from `[0, 1]` interval) of screen height / maximum detent height.\n   * You can pass an array of ascending values each defining allowed sheet detent. iOS accepts any number of detents,\n   * while **Android is limited to three**.\n   *\n   * There is also possibility to specify `fitToContents` literal, which intents to set the sheet height\n   * to the height of its contents. On iOS `fitToContents` currently also includes small padding accounting for bottom inset.\n   *\n   * Please note that the array **must** be sorted in ascending order. This invariant is verified only in developement mode,\n   * where violation results in error.\n   *\n   * **Android is limited to up 3 values in the array** -- any surplus values, beside first three are ignored.\n   *\n   * There are also legacy & **deprecated** options available:\n   *\n   * * 'medium' - corresponds to `[0.5]` detent value, around half of the screen height,\n   * * 'large' - corresponds to `[1.0]` detent value, maximum height,\n   * * 'all' - corresponds to `[0.5, 1.0]` value, the name is deceiving due to compatibility reasons.\n   *\n   * These are provided solely for **temporary** backward compatibility and are destined for removal in future versions.\n   *\n   * Defaults to `[1.0]`.\n   */\n  sheetAllowedDetents?: number[] | 'fitToContents' | 'medium' | 'large' | 'all';\n  /**\n   * Integer value describing elevation of the sheet, impacting shadow on the top edge of the sheet.\n   *\n   * Not dynamic - changing it after the component is rendered won't have an effect.\n   *\n   * Defaults to `24`.\n   *\n   * @platform Android\n   */\n  sheetElevation?: number;\n  /**\n   * Whether the sheet should expand to larger detent when scrolling.\n   * Works only when `stackPresentation` is set to `formSheet`.\n   * Defaults to `true`.\n   *\n   * @platform ios\n   */\n  sheetExpandsWhenScrolledToEdge?: boolean;\n  /**\n   * The corner radius that the sheet will try to render with.\n   * Works only when `stackPresentation` is set to `formSheet`.\n   *\n   * If set to non-negative value it will try to render sheet with provided radius, else it will apply system default.\n   *\n   * If left unset system default is used.\n   *\n   * @platform ios\n   */\n  sheetCornerRadius?: number;\n  /**\n   * Boolean indicating whether the sheet shows a grabber at the top.\n   * Works only when `stackPresentation` is set to `formSheet`.\n   * Defaults to `false`.\n   *\n   * @platform ios\n   */\n  sheetGrabberVisible?: boolean;\n  /**\n   * The largest sheet detent for which a view underneath won't be dimmed.\n   * Works only when `stackPresentation` is set to `formSheet`.\n   *\n   * This prop can be set to an number, which indicates index of detent in `sheetAllowedDetents` array for which\n   * there won't be a dimming view beneath the sheet.\n   *\n   * If the specified index is out of bounds of `sheetAllowedDetents` array, in dev environment mode error will be thrown,\n   * in production the value will be reset to default value.\n   *\n   * Additionaly there are following options available:\n   *\n   * * `none` - there will be dimming view for all detents levels,\n   * * `last` - there won't be a dimming view for any detent level.\n   *\n   * There also legacy & **deprecated** prop values available: `medium`, `large` (don't confuse with `largest`), `all`, which work in tandem with\n   * corresponding legacy prop values for `sheetAllowedDetents` prop.\n   *\n   * These are provided solely for **temporary** backward compatibility and are destined for removal in future versions.\n   *\n   * Defaults to `none`, indicating that the dimming view should be always present.\n   */\n  sheetLargestUndimmedDetentIndex?:\n    | number\n    | 'none'\n    | 'last'\n    | 'medium' // deprecated\n    | 'large' // deprecated\n    | 'all'; // deprecated\n  /**\n   * Index of the detent the sheet should expand to after being opened.\n   * Works only when `stackPresentation` is set to `formSheet`.\n   *\n   * If the specified index is out of bounds of `sheetAllowedDetents` array, in dev environment more error will be thrown,\n   * in production the value will be reset to default value.\n   *\n   * Additionaly there is `last` value available, when set the sheet will expand initially to last (largest) detent.\n   *\n   * Defaults to `0` - which represents first detent in the detents array.\n   */\n  sheetInitialDetentIndex?: number | 'last';\n  /**\n   * How the screen should appear/disappear when pushed or popped at the top of the stack.\n   * The following values are currently supported:\n   * - \"default\" – uses a platform default animation\n   * - \"fade\" – fades screen in or out\n   * - \"fade_from_bottom\" – performs a fade from bottom animation\n   * - \"flip\" – flips the screen, requires stackPresentation: \"modal\" (iOS only)\n   * - \"simple_push\" – performs a default animation, but without native header transition (iOS only)\n   * - `slide_from_bottom` – performs a slide from bottom animation\n   * - \"slide_from_right\" - slide in the new screen from right to left (Android only, resolves to default transition on iOS)\n   * - \"slide_from_left\" - slide in the new screen from left to right\n   * - \"ios_from_right\" - iOS like slide in animation. pushes in the new screen from right to left (Android only, resolves to default transition on iOS)\n   * - \"ios_from_left\" - iOS like slide in animation. pushes in the new screen from left to right (Android only, resolves to default transition on iOS)\n   * - \"none\" – the screen appears/dissapears without an animation\n   */\n  stackAnimation?: StackAnimationTypes;\n  /**\n   * How should the screen be presented.\n   * The following values are currently supported:\n   * - \"push\" – the new screen will be pushed onto a stack which on iOS means that the default animation will be slide from the side, the animation on Android may vary depending on the OS version and theme. Supports nested stack rendering.\n   * - \"modal\" – the new screen will be presented modally. On iOS it'll use `UIModalPresentationStyleAutomatic`. On Android this is equivalent to `push` presentation type. Supports nested stack rendering.\n   * - \"transparentModal\" – the new screen will be presented modally but in addition the second to last screen will remain attached to the stack container such that if the top screen is non opaque the content below can still be seen. If \"modal\" is used instead the below screen will get unmounted as soon as the transition ends.\n   * - \"containedModal\" – will use \"UIModalPresentationCurrentContext\" modal style on iOS and will fallback to \"modal\" on Android.\n   * - \"containedTransparentModal\" – will use \"UIModalPresentationOverCurrentContext\" modal style on iOS and will fallback to \"transparentModal\" on Android.\n   * - \"fullScreenModal\" – will use \"UIModalPresentationFullScreen\" modal style on iOS and will fallback to \"modal\" on Android.\n   * - \"formSheet\" – will use \"UIModalPresentationFormSheet\" modal style on iOS, on Android this will use Material BottomSheetBehaviour. On Android neested stack rendering is not yet supported.\n   */\n  stackPresentation?: StackPresentationTypes;\n  /**\n   * Sets the status bar animation (similar to the `StatusBar` component). Requires enabling (or deleting) `View controller-based status bar appearance` in your Info.plist file on iOS.\n   */\n  statusBarAnimation?: 'none' | 'fade' | 'slide';\n  /**\n   * Sets the status bar color (similar to the `StatusBar` component). Defaults to initial status bar color.\n   *\n   * @platform android\n   *\n   * @deprecated For all apps targeting Android SDK 35 or above this prop has no effect.\n   *  For SDK below 35 this works only with specific app setup.\n   *  This prop is subject to removal in the future.\n   *  See: https://developer.android.com/reference/android/view/Window#setStatusBarColor(int)\n   */\n  statusBarColor?: ColorValue;\n  /**\n   * Whether the status bar should be hidden on this screen. Requires enabling (or deleting) `View controller-based status bar appearance` in your Info.plist file on iOS. Defaults to `false`.\n   */\n  statusBarHidden?: boolean;\n  /**\n   * Sets the status bar color (similar to the `StatusBar` component). Requires enabling (or deleting) `View controller-based status bar appearance` in your Info.plist file on iOS. Defaults to `auto`.\n   */\n  statusBarStyle?: 'inverted' | 'auto' | 'light' | 'dark';\n  /**\n   * Sets the translucency of the status bar. Defaults to `false`.\n   *\n   * @platform android\n   *\n   * @deprecated For all apps targeting Android SDK 35 or above edge-to-edge mode on Android is enabled by default and this point loses relevance.\n   *  It is expected that the edge-to-edge will be enforced in future SDKs: https://developer.android.com/about/versions/15/behavior-changes-15#ux.\n   */\n  statusBarTranslucent?: boolean;\n  /**\n   * Sets the direction in which you should swipe to dismiss the screen.\n   * When using `vertical` option, options `fullScreenSwipeEnabled: true`, `customAnimationOnSwipe: true` and `stackAnimation: 'slide_from_bottom'` are set by default.\n   * The following values are supported:\n   * - `vertical` – dismiss screen vertically\n   * - `horizontal` – dismiss screen horizontally (default)\n   *\n   * @platform ios\n   */\n  swipeDirection?: SwipeDirectionTypes;\n  /**\n   * Changes the duration (in milliseconds) of `slide_from_bottom`, `fade_from_bottom`, `fade` and `simple_push` transitions on iOS. Defaults to `500`.\n   * The duration of `default` and `flip` transitions isn't customizable.\n   *\n   * @platform ios\n   */\n  transitionDuration?: number;\n  /**\n   * Footer component that can be used alongside formSheet stack presentation style.\n   *\n   * This option is provided, because due to implementation details it might be problematic\n   * to implement such layout with JS-only code.\n   *\n   * Please note that this prop is marked as unstable and might be subject of breaking changes,\n   * including removal, in particular when we find solution that will make implementing it with JS\n   * straightforward.\n   *\n   * @platform android\n   */\n  unstable_sheetFooter?: () => React.ReactNode;\n}\n\nexport interface ScreenContainerProps extends ViewProps {\n  children?: React.ReactNode;\n  /**\n   * A prop that gives users an option to switch between using Screens for the navigator (container). All children screens should have the same value of their \"enabled\" prop as their container.\n   */\n  enabled?: boolean;\n  /**\n   * A prop to be used in navigators always showing only one screen (providing only `0` or `2` `activityState` values) for better implementation of `ScreenContainer` on iOS.\n   */\n  hasTwoStates?: boolean;\n}\n\nexport interface GestureDetectorBridge {\n  stackUseEffectCallback: (\n    stackRef: React.MutableRefObject<React.Ref<NativeStackNavigatorProps>>,\n  ) => void;\n}\n\nexport interface ScreenStackProps extends ViewProps, GestureProps {\n  children?: React.ReactNode;\n  /**\n   * A callback that gets called when the current screen finishes its transition.\n   */\n  onFinishTransitioning?: (e: NativeSyntheticEvent<TargetedEvent>) => void;\n  ref?: React.MutableRefObject<React.Ref<View>>;\n}\n\nexport interface ScreenStackHeaderConfigProps extends ViewProps {\n  /**\n   * Whether to show the back button with custom left side of the header.\n   */\n  backButtonInCustomView?: boolean;\n  /**\n   * Controls the color of the navigation header.\n   */\n  backgroundColor?: ColorValue;\n  /**\n   * Title to display in the back button.\n   * @platform ios.\n   */\n  backTitle?: string;\n  /**\n   * Allows for customizing font family to be used for back button title on iOS.\n   * @platform ios\n   */\n  backTitleFontFamily?: string;\n  /**\n   * Allows for customizing font size to be used for back button title on iOS.\n   * @platform ios\n   */\n  backTitleFontSize?: number;\n  /**\n   * Whether the back button title should be visible or not. Defaults to `true`.\n   * @platform ios\n   */\n  backTitleVisible?: boolean;\n  /**\n   * Blur effect to be applied to the header. Works with backgroundColor's alpha < 1.\n   * @platform ios\n   */\n  blurEffect?: BlurEffectTypes;\n  /**\n   * Pass HeaderLeft, HeaderRight and HeaderTitle\n   */\n  children?: React.ReactNode;\n  /**\n   * Controls the color of items rendered on the header. This includes back icon, back text (iOS only) and title text. If you want the title to have different color use titleColor property.\n   */\n  color?: ColorValue;\n  /**\n   * Whether the stack should be in rtl or ltr form.\n   */\n  direction?: 'rtl' | 'ltr';\n  /**\n   * Boolean indicating whether to show the menu on longPress of iOS >= 14 back button.\n   * @platform ios\n   */\n  disableBackButtonMenu?: boolean;\n  /**\n   * How the back button behaves by default (when not customized). Available on iOS>=14, and is used only when none of: `backTitleFontFamily`, `backTitleFontSize`, `disableBackButtonMenu` or `backTitle` is set.\n   * The following values are currently supported (they correspond to https://developer.apple.com/documentation/uikit/uinavigationitembackbuttondisplaymode?language=objc):\n   * - \"default\" – show given back button previous controller title, system generic or just icon based on available space\n   * - \"generic\" – show given system generic or just icon based on available space\n   * - \"minimal\" – show just an icon\n   * @platform ios\n   */\n  backButtonDisplayMode?: BackButtonDisplayMode;\n  /**\n   * When set to true the header will be hidden while the parent Screen is on the top of the stack. The default value is false.\n   */\n  hidden?: boolean;\n  /**\n   * Boolean indicating whether to hide the back button in header.\n   */\n  hideBackButton?: boolean;\n  /**\n   * Boolean indicating whether to hide the elevation shadow or the bottom border on the header.\n   */\n  hideShadow?: boolean;\n  /**\n   * Boolean to set native property to prefer large title header (like in iOS setting).\n   * For large title to collapse on scroll, the content of the screen should be wrapped in a scrollable view such as `ScrollView` or `FlatList`.\n   * If the scrollable area doesn't fill the screen, the large title won't collapse on scroll.\n   * Only supported on iOS.\n   *\n   * @platform ios\n   */\n  largeTitle?: boolean;\n  /**\n   * Controls the color of the navigation header when the edge of any scrollable content reaches the matching edge of the navigation bar.\n   */\n  largeTitleBackgroundColor?: ColorValue;\n  /**\n   * Customize the color to be used for the large title. By default uses the titleColor property.\n   * @platform ios\n   */\n  largeTitleColor?: ColorValue;\n  /**\n   * Customize font family to be used for the large title.\n   * @platform ios\n   */\n  largeTitleFontFamily?: string;\n  /**\n   * Customize the size of the font to be used for the large title.\n   * @platform ios\n   */\n  largeTitleFontSize?: number;\n  /**\n   * Customize the weight of the font to be used for the large title.\n   * @platform ios\n   */\n  largeTitleFontWeight?: string;\n  /**\n   * Boolean that allows for disabling drop shadow under navigation header when the edge of any scrollable content reaches the matching edge of the navigation bar.\n   */\n  largeTitleHideShadow?: boolean;\n  /**\n   * Callback which is executed when screen header is attached\n   */\n  onAttached?: () => void;\n  /**\n   * Callback which is executed when screen header is detached\n   */\n  onDetached?: () => void;\n  /**\n   * String that can be displayed in the header as a fallback for `headerTitle`.\n   */\n  title?: string;\n  /**\n   * Allows for setting text color of the title.\n   */\n  titleColor?: ColorValue;\n  /**\n   * Customize font family to be used for the title.\n   */\n  titleFontFamily?: string;\n  /**\n   * Customize the size of the font to be used for the title.\n   */\n  titleFontSize?: number;\n  /**\n   * Customize the weight of the font to be used for the title.\n   */\n  titleFontWeight?: string;\n  /**\n   * A flag to that lets you opt out of insetting the header. You may want to\n   * set this to `false` if you use an opaque status bar. Defaults to `true`.\n   * Only supported on Android. Insets are always applied on iOS because the\n   * header cannot be opaque.\n   *\n   * @platform android\n   *\n   * @deprecated For apps targeting Android SDK 35 or above edge-to-edge mode is enabled by default\n   *  and it is expected that the edge-to-edge will be enforced in future SDKs - therefore this prop\n   *  loses its relevance and will be removed at some point in the future.\n   */\n  topInsetEnabled?: boolean;\n  /**\n   * Boolean indicating whether the navigation bar is translucent.\n   */\n  translucent?: boolean;\n}\n\nexport interface SearchBarProps {\n  /**\n   * Reference to imperatively modify search bar.\n   *\n   * Currently supported operations are:\n   *\n   * * `focus` - focuses the search bar\n   * * `blur` - removes focus from the search bar\n   * * `clearText` - removes any text present in the search bar input field\n   * * `setText` - sets the search bar's content to given value\n   * * `cancelSearch` - cancel search in search bar.\n   * * `toggleCancelButton` - depending on passed boolean value, hides or shows cancel button (iOS only)\n   */\n  ref?: React.RefObject<SearchBarCommands>;\n\n  /**\n   * The auto-capitalization behavior\n   */\n  autoCapitalize?: 'none' | 'words' | 'sentences' | 'characters';\n  /**\n   * Automatically focuses search bar on mount\n   *\n   * @platform android\n   */\n  autoFocus?: boolean;\n  /**\n   * The search field background color\n   */\n  barTintColor?: ColorValue;\n  /**\n   * The color for the cursor caret and cancel button text.\n   *\n   * @platform ios\n   */\n  tintColor?: ColorValue;\n  /**\n   * The text to be used instead of default `Cancel` button text\n   *\n   * @platform ios\n   */\n  cancelButtonText?: string;\n  /**\n   * Specifies whether the back button should close search bar's text input or not.\n   *\n   * @platform android\n   */\n  disableBackButtonOverride?: boolean;\n  /**\n   * Indicates whether to hide the navigation bar\n   *\n   * @platform ios\n   */\n  hideNavigationBar?: boolean;\n  /**\n   * Indicates whether to hide the search bar when scrolling\n   *\n   * @platform ios\n   */\n  hideWhenScrolling?: boolean;\n\n  /**\n   * Sets type of the input. Defaults to `text`.\n   *\n   * @platform android\n   */\n  inputType?: 'text' | 'phone' | 'number' | 'email';\n  /**\n   * Indicates whether to obscure the underlying content\n   */\n  obscureBackground?: boolean;\n  /**\n   * A callback that gets called when search bar has lost focus\n   */\n  onBlur?: (e: NativeSyntheticEvent<TargetedEvent>) => void;\n  /**\n   * A callback that gets called when the cancel button is pressed\n   *\n   * @platform ios\n   */\n  onCancelButtonPress?: (e: NativeSyntheticEvent<TargetedEvent>) => void;\n\n  /**\n   * A callback that gets called when the text changes. It receives the current text value of the search bar.\n   */\n  onChangeText?: (e: NativeSyntheticEvent<TextInputFocusEventData>) => void;\n\n  /**\n   * A callback that gets called when search bar is closed\n   *\n   * @platform android\n   */\n  onClose?: () => void;\n  /**\n   * A callback that gets called when search bar has received focus\n   */\n  onFocus?: (e: NativeSyntheticEvent<TargetedEvent>) => void;\n  /**\n   * A callback that gets called when search bar is opened\n   *\n   * @platform android\n   */\n  onOpen?: () => void;\n  /**\n   * A callback that gets called when the search button is pressed. It receives the current text value of the search bar.\n   */\n  onSearchButtonPress?: (\n    e: NativeSyntheticEvent<TextInputFocusEventData>,\n  ) => void;\n  /**\n   * Text displayed when search field is empty\n   */\n  placeholder?: string;\n  /**\n   * Position of the search bar\n   *\n   * Supported values:\n   *\n   * * `automatic` - the search bar is placed according to current layout\n   * * `inline` - the search bar is placed on the trailing edge of navigation bar\n   * * `stacked` - the search bar is placed below the other content in navigation bar\n   *\n   * Defaults to `stacked`\n   *\n   * @platform iOS (>= 16.0)\n   */\n  placement?: SearchBarPlacement;\n  /**\n   * The search field text color\n   */\n  textColor?: ColorValue;\n  /**\n   * The search hint text color\n   *\n   * @plaform android\n   */\n  hintTextColor?: ColorValue;\n  /**\n   * The search and close icon color shown in the header\n   *\n   * @plaform android\n   */\n  headerIconColor?: ColorValue;\n  /**\n   * Show the search hint icon when search bar is focused\n   *\n   * @plaform android\n   * @default true\n   */\n  shouldShowHintSearchIcon?: boolean;\n}\n\n/**\n * Custom Screen Transition\n */\n\n/**\n * copy from GestureHandler to avoid strong dependency\n */\nexport type PanGestureHandlerEventPayload = {\n  x: number;\n  y: number;\n  absoluteX: number;\n  absoluteY: number;\n  translationX: number;\n  translationY: number;\n  velocityX: number;\n  velocityY: number;\n};\n\n/**\n * copy from Reanimated to avoid strong dependency\n */\nexport type GoBackGesture =\n  | 'swipeRight'\n  | 'swipeLeft'\n  | 'swipeUp'\n  | 'swipeDown'\n  | 'verticalSwipe'\n  | 'horizontalSwipe'\n  | 'twoDimensionalSwipe';\n\nexport interface MeasuredDimensions {\n  x: number;\n  y: number;\n  width: number;\n  height: number;\n  pageX: number;\n  pageY: number;\n}\n\nexport type AnimatedScreenTransition = {\n  topScreenStyle: (\n    event: PanGestureHandlerEventPayload,\n    screenSize: MeasuredDimensions,\n  ) => Record<string, unknown>;\n  belowTopScreenStyle: (\n    event: PanGestureHandlerEventPayload,\n    screenSize: MeasuredDimensions,\n  ) => Record<string, unknown>;\n};\n\nexport type ScreensRefsHolder = Record<string, React.RefObject<View>>;\n\nexport interface GestureProps {\n  screensRefs?: React.MutableRefObject<ScreensRefsHolder>;\n  currentScreenId?: string;\n  goBackGesture?: GoBackGesture;\n  transitionAnimation?: AnimatedScreenTransition;\n  screenEdgeGesture?: boolean;\n}\n\nexport interface GestureProviderProps extends GestureProps {\n  children?: React.ReactNode;\n  gestureDetectorBridge: React.MutableRefObject<GestureDetectorBridge>;\n}\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}