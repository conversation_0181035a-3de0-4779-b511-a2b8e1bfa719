import React, { useContext, useEffect, useState } from "react";
import { View, Alert, ScrollView, TouchableOpacity } from "react-native";
import { Button, Text, ProgressBar } from 'react-native-paper';
import Wrapper from "../../components/Wrapper";
import Card from "../../components/Card";
import StatCard from "../../components/StatCard";
import { StoreContext } from '../../store';

export default function Premium({ route = {} }) {
  const [state, dispatch] = useContext(StoreContext);
  const [selectedPlan, setSelectedPlan] = useState('pro');

  const { subscription: { subscriptionStatus } } = state || { subscription: { subscriptionStatus: false } };
  let { accountNotRecovered } = route.params || { accountNotRecovered: false };

  // Premium features data
  const premiumFeatures = [
    {
      icon: '🚀',
      title: 'Priority Signals',
      description: 'Get signals 30 seconds before free users',
      free: false,
      pro: true,
      elite: true
    },
    {
      icon: '📊',
      title: 'Advanced Analytics',
      description: 'Detailed performance metrics and insights',
      free: false,
      pro: true,
      elite: true
    },
    {
      icon: '🎯',
      title: 'Custom Risk Management',
      description: 'Personalized risk settings and alerts',
      free: false,
      pro: true,
      elite: true
    },
    {
      icon: '📱',
      title: 'Mobile Notifications',
      description: 'Real-time push notifications',
      free: true,
      pro: true,
      elite: true
    },
    {
      icon: '💬',
      title: 'Premium Support',
      description: '24/7 priority customer support',
      free: false,
      pro: false,
      elite: true
    },
    {
      icon: '🤖',
      title: 'Auto Trading',
      description: 'Automated signal execution',
      free: false,
      pro: false,
      elite: true
    },
    {
      icon: '📈',
      title: 'Portfolio Tracking',
      description: 'Advanced portfolio management tools',
      free: false,
      pro: true,
      elite: true
    },
    {
      icon: '🔔',
      title: 'Signal Channels',
      description: 'Access to premium signal channels',
      free: '3 channels',
      pro: '15 channels',
      elite: 'Unlimited'
    }
  ];

  // Pricing plans
  const pricingPlans = [
    {
      id: 'free',
      name: 'Free',
      price: '$0',
      period: 'forever',
      description: 'Perfect for beginners',
      features: ['3 Signal Channels', 'Basic Notifications', 'Community Support'],
      color: '#4CAF50',
      popular: false
    },
    {
      id: 'pro',
      name: 'Pro',
      price: '$29',
      period: 'month',
      description: 'Most popular choice',
      features: ['15 Signal Channels', 'Priority Signals', 'Advanced Analytics', 'Custom Risk Management'],
      color: '#FECB37',
      popular: true,
      discount: '50% OFF'
    },
    {
      id: 'elite',
      name: 'Elite',
      price: '$79',
      period: 'month',
      description: 'For serious traders',
      features: ['Unlimited Channels', 'Auto Trading', 'Premium Support', 'All Pro Features'],
      color: '#9C27B0',
      popular: false
    }
  ];

  // Testimonials
  const testimonials = [
    {
      name: 'Sarah Chen',
      role: 'Day Trader',
      avatar: '👩‍💼',
      rating: 5,
      text: 'Premium signals helped me increase my portfolio by 340% in 6 months!'
    },
    {
      name: 'Mike Rodriguez',
      role: 'Crypto Investor',
      avatar: '👨‍💻',
      rating: 5,
      text: 'The auto-trading feature is a game changer. I make money while I sleep!'
    },
    {
      name: 'Alex Thompson',
      role: 'Professional Trader',
      avatar: '👨‍💼',
      rating: 5,
      text: 'Best ROI I\'ve ever seen from a trading service. Highly recommended!'
    }
  ];

  useEffect(() => {
    if (accountNotRecovered) {
      Alert.alert("Info", "No active subscription for this account.");
    }
  }, [accountNotRecovered]);

  const handleSubscribe = (planId) => {
    Alert.alert(
      "Subscribe to Premium",
      `Subscribe to ${pricingPlans.find(p => p.id === planId)?.name} plan. This feature will be available soon!`
    );
  };

  const handleManageSubscription = () => {
    Alert.alert("Manage Subscription", "Subscription management coming soon!");
  };

  if (subscriptionStatus) {
    return (
      <Wrapper>
        <ScrollView style={{ flex: 1 }}>
          {/* Premium Active Header */}
          <View style={{ padding: 16, alignItems: 'center' }}>
            <Text style={{ fontSize: 60, marginBottom: 16 }}>💎</Text>
            <Text style={{
              color: '#FECB37',
              fontSize: 24,
              fontFamily: 'Poppins_700Bold',
              textAlign: 'center',
              marginBottom: 8
            }}>
              Premium Active
            </Text>
            <Text style={{
              color: '#8a8a8a',
              fontSize: 14,
              fontFamily: 'Poppins_400Regular',
              textAlign: 'center'
            }}>
              You have access to all premium features
            </Text>
          </View>

          {/* Premium Stats */}
          <View style={{ paddingHorizontal: 8, marginBottom: 16 }}>
            <Text style={{
              color: '#fff',
              fontSize: 18,
              fontFamily: 'Poppins_600SemiBold',
              marginBottom: 12,
              paddingHorizontal: 8
            }}>
              Your Premium Benefits
            </Text>
            <View style={{ flexDirection: 'row', flexWrap: 'wrap' }}>
              <StatCard
                title="Premium Channels"
                value="15"
                subtitle="Unlocked"
                icon="📺"
              />
              <StatCard
                title="Priority Signals"
                value="847"
                subtitle="Received"
                icon="🚀"
              />
              <StatCard
                title="Success Rate"
                value="84.2%"
                change="+5.1%"
                changeType="positive"
                icon="🎯"
              />
              <StatCard
                title="Savings"
                value="$2,340"
                subtitle="This month"
                icon="💰"
              />
            </View>
          </View>

          {/* Subscription Management */}
          <View style={{ paddingHorizontal: 16, marginBottom: 20 }}>
            <Text style={{
              color: '#fff',
              fontSize: 18,
              fontFamily: 'Poppins_600SemiBold',
              marginBottom: 12
            }}>
              Subscription Management
            </Text>

            <Card>
              <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
                <View>
                  <Text style={{ color: '#fff', fontSize: 16, fontFamily: 'Poppins_600SemiBold' }}>Pro Plan</Text>
                  <Text style={{ color: '#8a8a8a', fontSize: 12, fontFamily: 'Poppins_400Regular' }}>Next billing: February 15, 2024</Text>
                </View>
                <View style={{
                  backgroundColor: '#FECB37',
                  paddingHorizontal: 8,
                  paddingVertical: 4,
                  borderRadius: 4
                }}>
                  <Text style={{ color: '#000', fontSize: 12, fontFamily: 'Poppins_600SemiBold' }}>ACTIVE</Text>
                </View>
              </View>

              <Button
                mode="outlined"
                onPress={handleManageSubscription}
                style={{ borderColor: '#FECB37' }}
                labelStyle={{ color: '#FECB37', fontFamily: 'Poppins_500Medium' }}
              >
                Manage Subscription
              </Button>
            </Card>
          </View>
        </ScrollView>
      </Wrapper>
    );
  }

  return (
    <Wrapper>
      <ScrollView style={{ flex: 1 }}>
        {/* Hero Section */}
        <View style={{ padding: 16, alignItems: 'center', backgroundColor: 'linear-gradient(135deg, #FECB37, #FF9800)' }}>
          <Text style={{ fontSize: 60, marginBottom: 16 }}>💎</Text>
          <Text style={{
            color: '#fff',
            fontSize: 28,
            fontFamily: 'Poppins_700Bold',
            textAlign: 'center',
            marginBottom: 8
          }}>
            Unlock Premium Trading
          </Text>
          <Text style={{
            color: '#fff',
            fontSize: 16,
            fontFamily: 'Poppins_400Regular',
            textAlign: 'center',
            marginBottom: 16,
            opacity: 0.9
          }}>
            Get priority signals and advanced features
          </Text>

          {/* Limited Time Offer */}
          <View style={{
            backgroundColor: '#F44336',
            paddingHorizontal: 16,
            paddingVertical: 8,
            borderRadius: 20,
            marginBottom: 16
          }}>
            <Text style={{
              color: '#fff',
              fontSize: 12,
              fontFamily: 'Poppins_600SemiBold',
              textAlign: 'center'
            }}>
              🔥 LIMITED TIME: 50% OFF FIRST MONTH
            </Text>
          </View>
        </View>

        {/* Success Stats */}
        <View style={{ paddingHorizontal: 8, marginTop: 16, marginBottom: 16 }}>
          <Text style={{
            color: '#fff',
            fontSize: 18,
            fontFamily: 'Poppins_600SemiBold',
            marginBottom: 12,
            paddingHorizontal: 8,
            textAlign: 'center'
          }}>
            Join 50,000+ Successful Traders
          </Text>
          <View style={{ flexDirection: 'row', flexWrap: 'wrap' }}>
            <StatCard
              title="Average ROI"
              value="340%"
              subtitle="Per year"
              icon="📈"
            />
            <StatCard
              title="Success Rate"
              value="87.3%"
              subtitle="Signal accuracy"
              icon="🎯"
            />
            <StatCard
              title="Active Users"
              value="50K+"
              subtitle="Worldwide"
              icon="👥"
            />
            <StatCard
              title="Signals Daily"
              value="150+"
              subtitle="Premium signals"
              icon="📡"
            />
          </View>
        </View>

        {/* Pricing Plans */}
        <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>
          <Text style={{
            color: '#fff',
            fontSize: 20,
            fontFamily: 'Poppins_700Bold',
            marginBottom: 16,
            textAlign: 'center'
          }}>
            Choose Your Plan
          </Text>

          {pricingPlans.map((plan) => (
            <TouchableOpacity
              key={plan.id}
              onPress={() => setSelectedPlan(plan.id)}
              style={{ marginBottom: 12 }}
            >
              <Card style={{
                borderColor: selectedPlan === plan.id ? plan.color : 'transparent',
                borderWidth: selectedPlan === plan.id ? 2 : 0,
                position: 'relative'
              }}>
                {plan.popular && (
                  <View style={{
                    position: 'absolute',
                    top: -8,
                    right: 16,
                    backgroundColor: '#F44336',
                    paddingHorizontal: 12,
                    paddingVertical: 4,
                    borderRadius: 12,
                    zIndex: 1
                  }}>
                    <Text style={{
                      color: '#fff',
                      fontSize: 10,
                      fontFamily: 'Poppins_600SemiBold'
                    }}>
                      MOST POPULAR
                    </Text>
                  </View>
                )}

                {plan.discount && (
                  <View style={{
                    position: 'absolute',
                    top: -8,
                    left: 16,
                    backgroundColor: '#4CAF50',
                    paddingHorizontal: 12,
                    paddingVertical: 4,
                    borderRadius: 12,
                    zIndex: 1
                  }}>
                    <Text style={{
                      color: '#fff',
                      fontSize: 10,
                      fontFamily: 'Poppins_600SemiBold'
                    }}>
                      {plan.discount}
                    </Text>
                  </View>
                )}

                <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
                  <View style={{ flex: 1 }}>
                    <Text style={{
                      color: plan.color,
                      fontSize: 20,
                      fontFamily: 'Poppins_700Bold'
                    }}>
                      {plan.name}
                    </Text>
                    <Text style={{
                      color: '#8a8a8a',
                      fontSize: 12,
                      fontFamily: 'Poppins_400Regular'
                    }}>
                      {plan.description}
                    </Text>
                  </View>
                  <View style={{ alignItems: 'flex-end' }}>
                    <View style={{ flexDirection: 'row', alignItems: 'baseline' }}>
                      <Text style={{
                        color: '#fff',
                        fontSize: 24,
                        fontFamily: 'Poppins_700Bold'
                      }}>
                        {plan.price}
                      </Text>
                      <Text style={{
                        color: '#8a8a8a',
                        fontSize: 12,
                        fontFamily: 'Poppins_400Regular',
                        marginLeft: 4
                      }}>
                        /{plan.period}
                      </Text>
                    </View>
                  </View>
                </View>

                <View style={{ marginBottom: 16 }}>
                  {plan.features.map((feature, index) => (
                    <View key={index} style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 4 }}>
                      <Text style={{ color: '#4CAF50', marginRight: 8 }}>✓</Text>
                      <Text style={{
                        color: '#ccc',
                        fontSize: 14,
                        fontFamily: 'Poppins_400Regular'
                      }}>
                        {feature}
                      </Text>
                    </View>
                  ))}
                </View>

                {plan.id !== 'free' && (
                  <Button
                    mode={selectedPlan === plan.id ? "contained" : "outlined"}
                    onPress={() => handleSubscribe(plan.id)}
                    style={{
                      backgroundColor: selectedPlan === plan.id ? plan.color : 'transparent',
                      borderColor: plan.color
                    }}
                    labelStyle={{
                      color: selectedPlan === plan.id ? '#000' : plan.color,
                      fontFamily: 'Poppins_600SemiBold'
                    }}
                  >
                    {selectedPlan === plan.id ? 'Selected Plan' : `Choose ${plan.name}`}
                  </Button>
                )}
              </Card>
            </TouchableOpacity>
          ))}
        </View>

        {/* Feature Comparison */}
        <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>
          <Text style={{
            color: '#fff',
            fontSize: 20,
            fontFamily: 'Poppins_700Bold',
            marginBottom: 16,
            textAlign: 'center'
          }}>
            Feature Comparison
          </Text>

          <Card>
            <View style={{ flexDirection: 'row', marginBottom: 12 }}>
              <View style={{ flex: 2 }}>
                <Text style={{ color: '#8a8a8a', fontSize: 12, fontFamily: 'Poppins_600SemiBold' }}>FEATURES</Text>
              </View>
              <View style={{ flex: 1, alignItems: 'center' }}>
                <Text style={{ color: '#4CAF50', fontSize: 12, fontFamily: 'Poppins_600SemiBold' }}>FREE</Text>
              </View>
              <View style={{ flex: 1, alignItems: 'center' }}>
                <Text style={{ color: '#FECB37', fontSize: 12, fontFamily: 'Poppins_600SemiBold' }}>PRO</Text>
              </View>
              <View style={{ flex: 1, alignItems: 'center' }}>
                <Text style={{ color: '#9C27B0', fontSize: 12, fontFamily: 'Poppins_600SemiBold' }}>ELITE</Text>
              </View>
            </View>

            {premiumFeatures.map((feature, index) => (
              <View key={index} style={{
                flexDirection: 'row',
                alignItems: 'center',
                paddingVertical: 8,
                borderTopWidth: index > 0 ? 1 : 0,
                borderTopColor: '#333'
              }}>
                <View style={{ flex: 2, flexDirection: 'row', alignItems: 'center' }}>
                  <Text style={{ fontSize: 16, marginRight: 8 }}>{feature.icon}</Text>
                  <View>
                    <Text style={{ color: '#fff', fontSize: 14, fontFamily: 'Poppins_500Medium' }}>
                      {feature.title}
                    </Text>
                    <Text style={{ color: '#8a8a8a', fontSize: 11, fontFamily: 'Poppins_400Regular' }}>
                      {feature.description}
                    </Text>
                  </View>
                </View>
                <View style={{ flex: 1, alignItems: 'center' }}>
                  <Text style={{
                    color: typeof feature.free === 'boolean' ? (feature.free ? '#4CAF50' : '#F44336') : '#8a8a8a',
                    fontSize: 12,
                    fontFamily: 'Poppins_500Medium'
                  }}>
                    {typeof feature.free === 'boolean' ? (feature.free ? '✓' : '✗') : feature.free}
                  </Text>
                </View>
                <View style={{ flex: 1, alignItems: 'center' }}>
                  <Text style={{
                    color: typeof feature.pro === 'boolean' ? (feature.pro ? '#4CAF50' : '#F44336') : '#8a8a8a',
                    fontSize: 12,
                    fontFamily: 'Poppins_500Medium'
                  }}>
                    {typeof feature.pro === 'boolean' ? (feature.pro ? '✓' : '✗') : feature.pro}
                  </Text>
                </View>
                <View style={{ flex: 1, alignItems: 'center' }}>
                  <Text style={{
                    color: typeof feature.elite === 'boolean' ? (feature.elite ? '#4CAF50' : '#F44336') : '#8a8a8a',
                    fontSize: 12,
                    fontFamily: 'Poppins_500Medium'
                  }}>
                    {typeof feature.elite === 'boolean' ? (feature.elite ? '✓' : '✗') : feature.elite}
                  </Text>
                </View>
              </View>
            ))}
          </Card>
        </View>

        {/* Testimonials */}
        <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>
          <Text style={{
            color: '#fff',
            fontSize: 20,
            fontFamily: 'Poppins_700Bold',
            marginBottom: 16,
            textAlign: 'center'
          }}>
            What Our Users Say
          </Text>

          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {testimonials.map((testimonial, index) => (
              <Card key={index} style={{ marginRight: 12, width: 280 }}>
                <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
                  <View style={{
                    backgroundColor: '#333',
                    borderRadius: 20,
                    width: 40,
                    height: 40,
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginRight: 12
                  }}>
                    <Text style={{ fontSize: 20 }}>{testimonial.avatar}</Text>
                  </View>
                  <View style={{ flex: 1 }}>
                    <Text style={{
                      color: '#fff',
                      fontSize: 14,
                      fontFamily: 'Poppins_600SemiBold'
                    }}>
                      {testimonial.name}
                    </Text>
                    <Text style={{
                      color: '#8a8a8a',
                      fontSize: 12,
                      fontFamily: 'Poppins_400Regular'
                    }}>
                      {testimonial.role}
                    </Text>
                  </View>
                  <View style={{ flexDirection: 'row' }}>
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Text key={i} style={{ color: '#FECB37', fontSize: 12 }}>⭐</Text>
                    ))}
                  </View>
                </View>
                <Text style={{
                  color: '#ccc',
                  fontSize: 13,
                  fontFamily: 'Poppins_400Regular',
                  lineHeight: 18,
                  fontStyle: 'italic'
                }}>
                  "{testimonial.text}"
                </Text>
              </Card>
            ))}
          </ScrollView>
        </View>

        {/* Call to Action */}
        <View style={{ paddingHorizontal: 16, marginBottom: 20 }}>
          <Card style={{ backgroundColor: '#FECB37', padding: 20, alignItems: 'center' }}>
            <Text style={{
              color: '#000',
              fontSize: 20,
              fontFamily: 'Poppins_700Bold',
              textAlign: 'center',
              marginBottom: 8
            }}>
              Ready to Start Earning?
            </Text>
            <Text style={{
              color: '#000',
              fontSize: 14,
              fontFamily: 'Poppins_400Regular',
              textAlign: 'center',
              marginBottom: 16,
              opacity: 0.8
            }}>
              Join thousands of successful traders today
            </Text>
            <Button
              mode="contained"
              onPress={() => handleSubscribe(selectedPlan)}
              style={{
                backgroundColor: '#000',
                paddingHorizontal: 20
              }}
              labelStyle={{
                color: '#FECB37',
                fontFamily: 'Poppins_600SemiBold',
                fontSize: 16
              }}
            >
              Start Premium Trial
            </Button>
            <Text style={{
              color: '#000',
              fontSize: 11,
              fontFamily: 'Poppins_400Regular',
              textAlign: 'center',
              marginTop: 8,
              opacity: 0.7
            }}>
              Cancel anytime • No hidden fees
            </Text>
          </Card>
        </View>
      </ScrollView>
    </Wrapper>
  );
}