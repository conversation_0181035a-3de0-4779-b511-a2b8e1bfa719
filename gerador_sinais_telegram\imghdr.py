"""
Módulo de compatibilidade para substituir o imghdr removido no Python 3.13
"""

def what(file, h=None):
    """
    Versão simplificada da função what() do imghdr
    Retorna uma string indicando o tipo de imagem ou None
    """
    if h is None:
        if isinstance(file, str):
            with open(file, 'rb') as f:
                h = f.read(32)
        else:
            location = file.tell()
            h = file.read(32)
            file.seek(location)
            
    if h.startswith(b'\xff\xd8'):
        return 'jpeg'
    elif h.startswith(b'\x89PNG\r\n\x1a\n'):
        return 'png'
    elif h.startswith(b'GIF87a') or h.startswith(b'GIF89a'):
        return 'gif'
    elif h.startswith(b'BM'):
        return 'bmp'
    elif h.startswith(b'WEBP'):
        return 'webp'
    return None

# Outras funções que podem ser necessárias
tests = [] 