import React, { useContext, useState } from 'react';
import { <PERSON>, ScrollView, Alert, Linking } from 'react-native';
import { Text, Button, Card, Switch, Divider } from 'react-native-paper';
import { StoreContext } from '../../store';
import Wrapper from '../../components/Wrapper';
import { PAYMENT_CONFIG } from '../../config/api';

export default function Profile() {
  const [state, dispatch] = useContext(StoreContext);
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [darkMode, setDarkMode] = useState(true);

  const { subscription: { subscriptionStatus, subscriptionPeriodEnd } } = state || { 
    subscription: { subscriptionStatus: false, subscriptionPeriodEnd: null } 
  };

  const handleContactSupport = () => {
    Alert.alert(
      "Suporte",
      "Entre em contato conosco:",
      [
        {
          text: "Email",
          onPress: () => Linking.openURL('mailto:<EMAIL>')
        },
        {
          text: "Telegram",
          onPress: () => Linking.openURL('https://t.me/cryptosignals_support')
        },
        {
          text: "Cancelar",
          style: "cancel"
        }
      ]
    );
  };

  const handleAbout = () => {
    Alert.alert(
      "Sobre o CryptoSignals",
      "CryptoSignals Professional\nVersão 2.0.0\n\nSinais de trading profissionais para criptomoedas.\n\nDesenvolvido com ❤️ para traders.",
      [{ text: "OK" }]
    );
  };

  const formatSubscriptionDate = (dateString) => {
    if (!dateString) return 'N/A';
    try {
      return new Date(dateString).toLocaleDateString('pt-BR');
    } catch {
      return 'N/A';
    }
  };

  return (
    <Wrapper>
      <ScrollView style={{ flex: 1, padding: 16 }}>
        {/* Header */}
        <View style={{ marginBottom: 24 }}>
          <Text style={{
            color: '#fff',
            fontSize: 28,
            fontWeight: 'bold',
            marginBottom: 8
          }}>
            Perfil
          </Text>
          <Text style={{
            color: '#8a8a8a',
            fontSize: 14
          }}>
            Gerencie sua conta e configurações
          </Text>
        </View>

        {/* Subscription Status */}
        <Card style={{ marginBottom: 16, backgroundColor: '#2a2a2a' }}>
          <View style={{ padding: 20 }}>
            <Text style={{
              color: '#fff',
              fontSize: 18,
              fontWeight: 'bold',
              marginBottom: 12
            }}>
              Status da Assinatura
            </Text>
            
            <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
              <View style={{
                width: 12,
                height: 12,
                borderRadius: 6,
                backgroundColor: subscriptionStatus ? '#4CAF50' : '#F44336',
                marginRight: 8
              }} />
              <Text style={{
                color: '#fff',
                fontSize: 16,
                fontWeight: '600'
              }}>
                {subscriptionStatus ? 'Premium Ativo' : 'Plano Gratuito'}
              </Text>
            </View>

            {subscriptionStatus && (
              <Text style={{
                color: '#8a8a8a',
                fontSize: 14
              }}>
                Válido até: {formatSubscriptionDate(subscriptionPeriodEnd)}
              </Text>
            )}

            {!subscriptionStatus && (
              <Text style={{
                color: '#8a8a8a',
                fontSize: 14,
                marginBottom: 12
              }}>
                Faça upgrade para acessar sinais premium
              </Text>
            )}
          </View>
        </Card>

        {/* Wallet Information */}
        <Card style={{ marginBottom: 16, backgroundColor: '#2a2a2a' }}>
          <View style={{ padding: 20 }}>
            <Text style={{
              color: '#fff',
              fontSize: 18,
              fontWeight: 'bold',
              marginBottom: 12
            }}>
              Informações de Pagamento
            </Text>
            
            <Text style={{
              color: '#8a8a8a',
              fontSize: 14,
              marginBottom: 8
            }}>
              Wallet para pagamentos USDT:
            </Text>
            
            <Text style={{
              color: '#4CAF50',
              fontSize: 12,
              fontFamily: 'monospace',
              backgroundColor: '#333',
              padding: 8,
              borderRadius: 4
            }}>
              {PAYMENT_CONFIG.USDT_WALLET}
            </Text>
            
            <Text style={{
              color: '#8a8a8a',
              fontSize: 12,
              marginTop: 8
            }}>
              Redes suportadas: BEP20 (BSC) e ERC20 (Ethereum)
            </Text>
          </View>
        </Card>

        {/* Settings */}
        <Card style={{ marginBottom: 16, backgroundColor: '#2a2a2a' }}>
          <View style={{ padding: 20 }}>
            <Text style={{
              color: '#fff',
              fontSize: 18,
              fontWeight: 'bold',
              marginBottom: 16
            }}>
              Configurações
            </Text>

            <View style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: 16
            }}>
              <View>
                <Text style={{ color: '#fff', fontSize: 16 }}>
                  Notificações Push
                </Text>
                <Text style={{ color: '#8a8a8a', fontSize: 12 }}>
                  Receber alertas de novos sinais
                </Text>
              </View>
              <Switch
                value={notificationsEnabled}
                onValueChange={setNotificationsEnabled}
                color="#4CAF50"
              />
            </View>

            <Divider style={{ backgroundColor: '#444', marginVertical: 8 }} />

            <View style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center'
            }}>
              <View>
                <Text style={{ color: '#fff', fontSize: 16 }}>
                  Modo Escuro
                </Text>
                <Text style={{ color: '#8a8a8a', fontSize: 12 }}>
                  Interface em tema escuro
                </Text>
              </View>
              <Switch
                value={darkMode}
                onValueChange={setDarkMode}
                color="#4CAF50"
              />
            </View>
          </View>
        </Card>

        {/* App Information */}
        <Card style={{ marginBottom: 16, backgroundColor: '#2a2a2a' }}>
          <View style={{ padding: 20 }}>
            <Text style={{
              color: '#fff',
              fontSize: 18,
              fontWeight: 'bold',
              marginBottom: 16
            }}>
              Informações do App
            </Text>

            <View style={{ marginBottom: 12 }}>
              <Text style={{ color: '#8a8a8a', fontSize: 14 }}>
                Versão: 2.0.0
              </Text>
            </View>

            <View style={{ marginBottom: 12 }}>
              <Text style={{ color: '#8a8a8a', fontSize: 14 }}>
                Última atualização: Janeiro 2024
              </Text>
            </View>

            <View style={{ marginBottom: 16 }}>
              <Text style={{ color: '#8a8a8a', fontSize: 14 }}>
                Desenvolvido para traders profissionais
              </Text>
            </View>

            <Button
              mode="outlined"
              onPress={handleAbout}
              style={{ borderColor: '#4CAF50', marginBottom: 8 }}
              labelStyle={{ color: '#4CAF50' }}
            >
              Sobre o App
            </Button>

            <Button
              mode="outlined"
              onPress={handleContactSupport}
              style={{ borderColor: '#2196F3' }}
              labelStyle={{ color: '#2196F3' }}
            >
              Contatar Suporte
            </Button>
          </View>
        </Card>

        {/* Footer */}
        <View style={{ 
          alignItems: 'center', 
          paddingVertical: 20,
          marginBottom: 20
        }}>
          <Text style={{
            color: '#666',
            fontSize: 12,
            textAlign: 'center'
          }}>
            CryptoSignals Professional{'\n'}
            Sinais de trading de alta qualidade
          </Text>
        </View>
      </ScrollView>
    </Wrapper>
  );
}
