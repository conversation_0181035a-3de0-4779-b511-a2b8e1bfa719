import React, { useState, useEffect } from 'react';
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  PieChart,
  Calendar,
  RefreshCw,
  Target,
  Activity,
  DollarSign
} from 'lucide-react';
import axios from 'axios';

const Analytics = () => {
  const [analytics, setAnalytics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [timeRange, setTimeRange] = useState('7d');

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/analytics', {
        params: { timeRange }
      });
      setAnalytics(response.data);
      setError(null);
    } catch (err) {
      setError('Erro ao carregar analytics');
      console.error('Erro:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAnalytics();
  }, [timeRange]);

  const timeRanges = [
    { value: '1d', label: '24 Horas' },
    { value: '7d', label: '7 Dias' },
    { value: '30d', label: '30 Dias' },
    { value: '90d', label: '90 Dias' }
  ];

  const MetricCard = ({ title, value, subtitle, icon: Icon, color, trend }) => (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
          {subtitle && (
            <p className="text-sm text-gray-500">{subtitle}</p>
          )}
        </div>
        <div className={`p-3 rounded-full ${color}`}>
          <Icon className="h-6 w-6 text-white" />
        </div>
      </div>
      {trend && (
        <div className="mt-4 flex items-center">
          {trend.direction === 'up' ? (
            <TrendingUp className="h-4 w-4 text-green-500" />
          ) : (
            <TrendingDown className="h-4 w-4 text-red-500" />
          )}
          <span className={`ml-1 text-sm font-medium ${
            trend.direction === 'up' ? 'text-green-600' : 'text-red-600'
          }`}>
            {trend.value}%
          </span>
          <span className="ml-1 text-sm text-gray-500">vs período anterior</span>
        </div>
      )}
    </div>
  );

  const StrategyCard = ({ strategy, data }) => (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900">{strategy}</h3>
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          data.winRate >= 60 ? 'bg-green-100 text-green-800' : 
          data.winRate >= 40 ? 'bg-yellow-100 text-yellow-800' : 
          'bg-red-100 text-red-800'
        }`}>
          {data.winRate}% Win Rate
        </span>
      </div>
      
      <div className="space-y-3">
        <div className="flex justify-between">
          <span className="text-sm text-gray-600">Total de Sinais</span>
          <span className="text-sm font-medium text-gray-900">{data.totalSignals}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-sm text-gray-600">Sinais Vencedores</span>
          <span className="text-sm font-medium text-green-600">{data.winningSignals}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-sm text-gray-600">Profit Médio</span>
          <span className="text-sm font-medium text-gray-900">{data.avgProfit}%</span>
        </div>
        <div className="flex justify-between">
          <span className="text-sm text-gray-600">Melhor Trade</span>
          <span className="text-sm font-medium text-green-600">+{data.bestTrade}%</span>
        </div>
      </div>
      
      {/* Progress Bar */}
      <div className="mt-4">
        <div className="flex justify-between text-sm text-gray-600 mb-1">
          <span>Performance</span>
          <span>{data.winRate}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className={`h-2 rounded-full ${
              data.winRate >= 60 ? 'bg-green-500' : 
              data.winRate >= 40 ? 'bg-yellow-500' : 
              'bg-red-500'
            }`}
            style={{ width: `${data.winRate}%` }}
          ></div>
        </div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin text-blue-500" />
        <span className="ml-2 text-gray-600">Carregando analytics...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6">
        <div className="text-center">
          <BarChart3 className="h-12 w-12 text-red-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-red-800 mb-2">Erro ao carregar analytics</h3>
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={fetchAnalytics}
            className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
          >
            Tentar Novamente
          </button>
        </div>
      </div>
    );
  }

  // Mock data para demonstração
  const mockAnalytics = {
    overview: {
      totalSignals: 247,
      winRate: 68.4,
      avgProfit: 3.2,
      totalProfit: 156.8
    },
    strategies: {
      'Scalp': { totalSignals: 89, winningSignals: 62, winRate: 69.7, avgProfit: 2.1, bestTrade: 8.5 },
      'Breakout': { totalSignals: 45, winningSignals: 31, winRate: 68.9, avgProfit: 4.2, bestTrade: 12.3 },
      'Inside Bar': { totalSignals: 38, winningSignals: 24, winRate: 63.2, avgProfit: 3.8, bestTrade: 9.7 },
      'MFI': { totalSignals: 32, winningSignals: 22, winRate: 68.8, avgProfit: 3.5, bestTrade: 11.2 },
      'Swing': { totalSignals: 28, winningSignals: 19, winRate: 67.9, avgProfit: 5.1, bestTrade: 15.6 },
      'Multi-Source': { totalSignals: 15, winningSignals: 11, winRate: 73.3, avgProfit: 4.8, bestTrade: 13.4 }
    }
  };

  const data = analytics || mockAnalytics;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Analytics</h1>
          <p className="text-gray-600">Análise de performance das estratégias</p>
        </div>
        <div className="flex items-center space-x-3">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            {timeRanges.map(range => (
              <option key={range.value} value={range.value}>
                {range.label}
              </option>
            ))}
          </select>
          <button
            onClick={fetchAnalytics}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Atualizar
          </button>
        </div>
      </div>

      {/* Overview Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Total de Sinais"
          value={data.overview?.totalSignals || 0}
          subtitle={`Últimos ${timeRanges.find(r => r.value === timeRange)?.label.toLowerCase()}`}
          icon={Target}
          color="bg-blue-500"
          trend={{ direction: 'up', value: 12.5 }}
        />
        
        <MetricCard
          title="Win Rate"
          value={`${data.overview?.winRate || 0}%`}
          subtitle="Taxa de acerto geral"
          icon={TrendingUp}
          color="bg-green-500"
          trend={{ direction: 'up', value: 5.2 }}
        />
        
        <MetricCard
          title="Profit Médio"
          value={`${data.overview?.avgProfit || 0}%`}
          subtitle="Por sinal vencedor"
          icon={BarChart3}
          color="bg-purple-500"
          trend={{ direction: 'up', value: 8.1 }}
        />
        
        <MetricCard
          title="Profit Total"
          value={`${data.overview?.totalProfit || 0}%`}
          subtitle="Acumulado no período"
          icon={DollarSign}
          color="bg-yellow-500"
          trend={{ direction: 'up', value: 15.3 }}
        />
      </div>

      {/* Strategy Performance */}
      <div>
        <h2 className="text-xl font-bold text-gray-900 mb-6">Performance por Estratégia</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Object.entries(data.strategies || {}).map(([strategy, strategyData]) => (
            <StrategyCard
              key={strategy}
              strategy={strategy}
              data={strategyData}
            />
          ))}
        </div>
      </div>

      {/* Quick Stats */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Estatísticas Rápidas</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">8</div>
            <div className="text-sm text-gray-600">Estratégias Ativas</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">109</div>
            <div className="text-sm text-gray-600">Tokens Monitorados</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">24/7</div>
            <div className="text-sm text-gray-600">Operação Contínua</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Analytics;
