{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport color from 'color';\nimport { black, white } from \"../../styles/themes/v2/colors\";\nvar DEFAULT_PADDING = 9;\nexport var getSegmentedButtonDensityPadding = function getSegmentedButtonDensityPadding(_ref) {\n  var density = _ref.density;\n  var padding = DEFAULT_PADDING;\n  switch (density) {\n    case 'small':\n      return padding - 2;\n    case 'medium':\n      return padding - 4;\n    case 'high':\n      return padding - 8;\n    default:\n      return padding;\n  }\n};\nexport var getDisabledSegmentedButtonStyle = function getDisabledSegmentedButtonStyle(_ref2) {\n  var theme = _ref2.theme,\n    index = _ref2.index,\n    buttons = _ref2.buttons;\n  var _buttons$index, _buttons;\n  var width = getSegmentedButtonBorderWidth({\n    theme: theme\n  });\n  var isDisabled = (_buttons$index = buttons[index]) === null || _buttons$index === void 0 ? void 0 : _buttons$index.disabled;\n  var isNextDisabled = (_buttons = buttons[index + 1]) === null || _buttons === void 0 ? void 0 : _buttons.disabled;\n  if (!isDisabled && isNextDisabled) {\n    return {\n      borderRightWidth: width\n    };\n  }\n  return {};\n};\nexport var getSegmentedButtonBorderRadius = function getSegmentedButtonBorderRadius(_ref3) {\n  var segment = _ref3.segment,\n    theme = _ref3.theme;\n  if (segment === 'first') {\n    return _objectSpread({\n      borderTopRightRadius: 0,\n      borderBottomRightRadius: 0\n    }, theme.isV3 && {\n      borderEndWidth: 0\n    });\n  } else if (segment === 'last') {\n    return {\n      borderTopLeftRadius: 0,\n      borderBottomLeftRadius: 0\n    };\n  } else {\n    return _objectSpread({\n      borderRadius: 0\n    }, theme.isV3 && {\n      borderEndWidth: 0\n    });\n  }\n};\nvar getSegmentedButtonBackgroundColor = function getSegmentedButtonBackgroundColor(_ref4) {\n  var checked = _ref4.checked,\n    theme = _ref4.theme;\n  if (checked) {\n    if (theme.isV3) {\n      return theme.colors.secondaryContainer;\n    } else {\n      return color(theme.colors.primary).alpha(0.12).rgb().string();\n    }\n  }\n  return 'transparent';\n};\nvar getSegmentedButtonBorderColor = function getSegmentedButtonBorderColor(_ref5) {\n  var theme = _ref5.theme,\n    disabled = _ref5.disabled,\n    checked = _ref5.checked;\n  if (theme.isV3) {\n    if (disabled) {\n      return theme.colors.surfaceDisabled;\n    }\n    return theme.colors.outline;\n  }\n  if (checked) {\n    return theme.colors.primary;\n  }\n  return color(theme.dark ? white : black).alpha(0.29).rgb().string();\n};\nvar getSegmentedButtonBorderWidth = function getSegmentedButtonBorderWidth(_ref6) {\n  var theme = _ref6.theme;\n  if (theme.isV3) {\n    return 1;\n  }\n  return StyleSheet.hairlineWidth;\n};\nvar getSegmentedButtonTextColor = function getSegmentedButtonTextColor(_ref7) {\n  var theme = _ref7.theme,\n    disabled = _ref7.disabled,\n    checked = _ref7.checked,\n    checkedColor = _ref7.checkedColor,\n    uncheckedColor = _ref7.uncheckedColor;\n  if (theme.isV3) {\n    if (disabled) {\n      return theme.colors.onSurfaceDisabled;\n    }\n    if (checked) {\n      return checkedColor != null ? checkedColor : theme.colors.onSecondaryContainer;\n    }\n    return uncheckedColor != null ? uncheckedColor : theme.colors.onSurface;\n  }\n  if (disabled) {\n    return theme.colors.disabled;\n  }\n  return theme.colors.primary;\n};\nexport var getSegmentedButtonColors = function getSegmentedButtonColors(_ref8) {\n  var theme = _ref8.theme,\n    disabled = _ref8.disabled,\n    checked = _ref8.checked,\n    checkedColor = _ref8.checkedColor,\n    uncheckedColor = _ref8.uncheckedColor;\n  var backgroundColor = getSegmentedButtonBackgroundColor({\n    theme: theme,\n    checked: checked\n  });\n  var borderColor = getSegmentedButtonBorderColor({\n    theme: theme,\n    disabled: disabled,\n    checked: checked\n  });\n  var textColor = getSegmentedButtonTextColor({\n    theme: theme,\n    disabled: disabled,\n    checked: checked,\n    checkedColor: checkedColor,\n    uncheckedColor: uncheckedColor\n  });\n  var borderWidth = getSegmentedButtonBorderWidth({\n    theme: theme\n  });\n  return {\n    backgroundColor: backgroundColor,\n    borderColor: borderColor,\n    textColor: textColor,\n    borderWidth: borderWidth\n  };\n};", "map": {"version": 3, "names": ["color", "black", "white", "DEFAULT_PADDING", "getSegmentedButtonDensityPadding", "_ref", "density", "padding", "getDisabledSegmentedButtonStyle", "_ref2", "theme", "index", "buttons", "_buttons$index", "_buttons", "width", "getSegmentedButtonBorderWidth", "isDisabled", "disabled", "isNextDisabled", "borderRightWidth", "getSegmentedButtonBorderRadius", "_ref3", "segment", "_objectSpread", "borderTopRightRadius", "borderBottomRightRadius", "isV3", "borderEndWidth", "borderTopLeftRadius", "borderBottomLeftRadius", "borderRadius", "getSegmentedButtonBackgroundColor", "_ref4", "checked", "colors", "secondaryContainer", "primary", "alpha", "rgb", "string", "getSegmentedButtonBorderColor", "_ref5", "surfaceDisabled", "outline", "dark", "_ref6", "StyleSheet", "hairlineWidth", "getSegmentedButtonTextColor", "_ref7", "checkedColor", "uncheckedColor", "onSurfaceDisabled", "onSecondaryContainer", "onSurface", "getSegmentedButtonColors", "_ref8", "backgroundColor", "borderColor", "textColor", "borderWidth"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\SegmentedButtons\\utils.ts"], "sourcesContent": ["import { StyleSheet, ViewStyle } from 'react-native';\n\nimport color from 'color';\n\nimport { black, white } from '../../styles/themes/v2/colors';\nimport type { InternalTheme } from '../../types';\n\ntype BaseProps = {\n  theme: InternalTheme;\n  disabled?: boolean;\n  checked: boolean;\n};\n\ntype SegmentedButtonProps = {\n  checkedColor?: string;\n  uncheckedColor?: string;\n} & BaseProps;\n\nconst DEFAULT_PADDING = 9;\n\nexport const getSegmentedButtonDensityPadding = ({\n  density,\n}: {\n  density?: 'regular' | 'small' | 'medium' | 'high';\n}) => {\n  let padding = DEFAULT_PADDING;\n\n  switch (density) {\n    case 'small':\n      return padding - 2;\n    case 'medium':\n      return padding - 4;\n    case 'high':\n      return padding - 8;\n    default:\n      return padding;\n  }\n};\n\nexport const getDisabledSegmentedButtonStyle = ({\n  theme,\n  index,\n  buttons,\n}: {\n  theme: InternalTheme;\n  buttons: { disabled?: boolean }[];\n  index: number;\n}): ViewStyle => {\n  const width = getSegmentedButtonBorderWidth({ theme });\n  const isDisabled = buttons[index]?.disabled;\n  const isNextDisabled = buttons[index + 1]?.disabled;\n\n  if (!isDisabled && isNextDisabled) {\n    return {\n      borderRightWidth: width,\n    };\n  }\n  return {};\n};\n\nexport const getSegmentedButtonBorderRadius = ({\n  segment,\n  theme,\n}: {\n  theme: InternalTheme;\n  segment?: 'first' | 'last';\n}): ViewStyle => {\n  if (segment === 'first') {\n    return {\n      borderTopRightRadius: 0,\n      borderBottomRightRadius: 0,\n      ...(theme.isV3 && { borderEndWidth: 0 }),\n    };\n  } else if (segment === 'last') {\n    return {\n      borderTopLeftRadius: 0,\n      borderBottomLeftRadius: 0,\n    };\n  } else {\n    return {\n      borderRadius: 0,\n      ...(theme.isV3 && { borderEndWidth: 0 }),\n    };\n  }\n};\n\nconst getSegmentedButtonBackgroundColor = ({ checked, theme }: BaseProps) => {\n  if (checked) {\n    if (theme.isV3) {\n      return theme.colors.secondaryContainer;\n    } else {\n      return color(theme.colors.primary).alpha(0.12).rgb().string();\n    }\n  }\n  return 'transparent';\n};\n\nconst getSegmentedButtonBorderColor = ({\n  theme,\n  disabled,\n  checked,\n}: BaseProps) => {\n  if (theme.isV3) {\n    if (disabled) {\n      return theme.colors.surfaceDisabled;\n    }\n    return theme.colors.outline;\n  }\n  if (checked) {\n    return theme.colors.primary;\n  }\n\n  return color(theme.dark ? white : black)\n    .alpha(0.29)\n    .rgb()\n    .string();\n};\n\nconst getSegmentedButtonBorderWidth = ({\n  theme,\n}: Omit<BaseProps, 'disabled' | 'checked'>) => {\n  if (theme.isV3) {\n    return 1;\n  }\n\n  return StyleSheet.hairlineWidth;\n};\n\nconst getSegmentedButtonTextColor = ({\n  theme,\n  disabled,\n  checked,\n  checkedColor,\n  uncheckedColor,\n}: SegmentedButtonProps) => {\n  if (theme.isV3) {\n    if (disabled) {\n      return theme.colors.onSurfaceDisabled;\n    }\n    if (checked) {\n      return checkedColor ?? theme.colors.onSecondaryContainer;\n    }\n    return uncheckedColor ?? theme.colors.onSurface;\n  }\n\n  if (disabled) {\n    return theme.colors.disabled;\n  }\n  // Primary color is used for checked state too.\n  return theme.colors.primary;\n};\n\nexport const getSegmentedButtonColors = ({\n  theme,\n  disabled,\n  checked,\n  checkedColor,\n  uncheckedColor,\n}: SegmentedButtonProps) => {\n  const backgroundColor = getSegmentedButtonBackgroundColor({\n    theme,\n    checked,\n  });\n  const borderColor = getSegmentedButtonBorderColor({\n    theme,\n    disabled,\n    checked,\n  });\n  const textColor = getSegmentedButtonTextColor({\n    theme,\n    disabled,\n    checked,\n    checkedColor,\n    uncheckedColor,\n  });\n  const borderWidth = getSegmentedButtonBorderWidth({ theme });\n\n  return { backgroundColor, borderColor, textColor, borderWidth };\n};\n"], "mappings": ";;;;AAEA,OAAOA,KAAK,MAAM,OAAO;AAEzB,SAASC,KAAK,EAAEC,KAAK;AAcrB,IAAMC,eAAe,GAAG,CAAC;AAEzB,OAAO,IAAMC,gCAAgC,GAAG,SAAnCA,gCAAgCA,CAAAC,IAAA,EAIvC;EAAA,IAHJC,OAAA,GAAAD,IAAA,CAAAC,OAAA;EAIA,IAAIC,OAAO,GAAGJ,eAAe;EAE7B,QAAQG,OAAO;IACb,KAAK,OAAO;MACV,OAAOC,OAAO,GAAG,CAAC;IACpB,KAAK,QAAQ;MACX,OAAOA,OAAO,GAAG,CAAC;IACpB,KAAK,MAAM;MACT,OAAOA,OAAO,GAAG,CAAC;IACpB;MACE,OAAOA,OAAO;EAClB;AACF,CAAC;AAED,OAAO,IAAMC,+BAA+B,GAAG,SAAlCA,+BAA+BA,CAAAC,KAAA,EAQ3B;EAAA,IAPfC,KAAK,GAAAD,KAAA,CAALC,KAAK;IACLC,KAAK,GAAAF,KAAA,CAALE,KAAK;IACLC,OAAA,GAAAH,KAAA,CAAAG,OAAA;EAKe,IAAAC,cAAA,EAAAC,QAAA;EACf,IAAMC,KAAK,GAAGC,6BAA6B,CAAC;IAAEN,KAAA,EAAAA;EAAM,CAAC,CAAC;EACtD,IAAMO,UAAU,IAAAJ,cAAA,GAAGD,OAAO,CAACD,KAAK,CAAC,cAAAE,cAAA,uBAAdA,cAAA,CAAgBK,QAAQ;EAC3C,IAAMC,cAAc,IAAAL,QAAA,GAAGF,OAAO,CAACD,KAAK,GAAG,CAAC,CAAC,cAAAG,QAAA,uBAAlBA,QAAA,CAAoBI,QAAQ;EAEnD,IAAI,CAACD,UAAU,IAAIE,cAAc,EAAE;IACjC,OAAO;MACLC,gBAAgB,EAAEL;IACpB,CAAC;EACH;EACA,OAAO,CAAC,CAAC;AACX,CAAC;AAED,OAAO,IAAMM,8BAA8B,GAAG,SAAjCA,8BAA8BA,CAAAC,KAAA,EAM1B;EAAA,IALfC,OAAO,GAAAD,KAAA,CAAPC,OAAO;IACPb,KAAA,GAAAY,KAAA,CAAAZ,KAAA;EAKA,IAAIa,OAAO,KAAK,OAAO,EAAE;IACvB,OAAAC,aAAA;MACEC,oBAAoB,EAAE,CAAC;MACvBC,uBAAuB,EAAE;IAAC,GACtBhB,KAAK,CAACiB,IAAI,IAAI;MAAEC,cAAc,EAAE;IAAE,CAAC;EAE3C,CAAC,MAAM,IAAIL,OAAO,KAAK,MAAM,EAAE;IAC7B,OAAO;MACLM,mBAAmB,EAAE,CAAC;MACtBC,sBAAsB,EAAE;IAC1B,CAAC;EACH,CAAC,MAAM;IACL,OAAAN,aAAA;MACEO,YAAY,EAAE;IAAC,GACXrB,KAAK,CAACiB,IAAI,IAAI;MAAEC,cAAc,EAAE;IAAE,CAAC;EAE3C;AACF,CAAC;AAED,IAAMI,iCAAiC,GAAG,SAApCA,iCAAiCA,CAAAC,KAAA,EAAsC;EAAA,IAAhCC,OAAO,GAAAD,KAAA,CAAPC,OAAO;IAAExB,KAAA,GAAAuB,KAAA,CAAAvB,KAAA;EACpD,IAAIwB,OAAO,EAAE;IACX,IAAIxB,KAAK,CAACiB,IAAI,EAAE;MACd,OAAOjB,KAAK,CAACyB,MAAM,CAACC,kBAAkB;IACxC,CAAC,MAAM;MACL,OAAOpC,KAAK,CAACU,KAAK,CAACyB,MAAM,CAACE,OAAO,CAAC,CAACC,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;IAC/D;EACF;EACA,OAAO,aAAa;AACtB,CAAC;AAED,IAAMC,6BAA6B,GAAG,SAAhCA,6BAA6BA,CAAAC,KAAA,EAIlB;EAAA,IAHfhC,KAAK,GAAAgC,KAAA,CAALhC,KAAK;IACLQ,QAAQ,GAAAwB,KAAA,CAARxB,QAAQ;IACRgB,OAAA,GAAAQ,KAAA,CAAAR,OAAA;EAEA,IAAIxB,KAAK,CAACiB,IAAI,EAAE;IACd,IAAIT,QAAQ,EAAE;MACZ,OAAOR,KAAK,CAACyB,MAAM,CAACQ,eAAe;IACrC;IACA,OAAOjC,KAAK,CAACyB,MAAM,CAACS,OAAO;EAC7B;EACA,IAAIV,OAAO,EAAE;IACX,OAAOxB,KAAK,CAACyB,MAAM,CAACE,OAAO;EAC7B;EAEA,OAAOrC,KAAK,CAACU,KAAK,CAACmC,IAAI,GAAG3C,KAAK,GAAGD,KAAK,CAAC,CACrCqC,KAAK,CAAC,IAAI,CAAC,CACXC,GAAG,CAAC,CAAC,CACLC,MAAM,CAAC,CAAC;AACb,CAAC;AAED,IAAMxB,6BAA6B,GAAG,SAAhCA,6BAA6BA,CAAA8B,KAAA,EAEY;EAAA,IAD7CpC,KAAA,GAAAoC,KAAA,CAAApC,KAAA;EAEA,IAAIA,KAAK,CAACiB,IAAI,EAAE;IACd,OAAO,CAAC;EACV;EAEA,OAAOoB,UAAU,CAACC,aAAa;AACjC,CAAC;AAED,IAAMC,2BAA2B,GAAG,SAA9BA,2BAA2BA,CAAAC,KAAA,EAML;EAAA,IAL1BxC,KAAK,GAAAwC,KAAA,CAALxC,KAAK;IACLQ,QAAQ,GAAAgC,KAAA,CAARhC,QAAQ;IACRgB,OAAO,GAAAgB,KAAA,CAAPhB,OAAO;IACPiB,YAAY,GAAAD,KAAA,CAAZC,YAAY;IACZC,cAAA,GAAAF,KAAA,CAAAE,cAAA;EAEA,IAAI1C,KAAK,CAACiB,IAAI,EAAE;IACd,IAAIT,QAAQ,EAAE;MACZ,OAAOR,KAAK,CAACyB,MAAM,CAACkB,iBAAiB;IACvC;IACA,IAAInB,OAAO,EAAE;MACX,OAAOiB,YAAY,WAAZA,YAAY,GAAIzC,KAAK,CAACyB,MAAM,CAACmB,oBAAoB;IAC1D;IACA,OAAOF,cAAc,WAAdA,cAAc,GAAI1C,KAAK,CAACyB,MAAM,CAACoB,SAAS;EACjD;EAEA,IAAIrC,QAAQ,EAAE;IACZ,OAAOR,KAAK,CAACyB,MAAM,CAACjB,QAAQ;EAC9B;EAEA,OAAOR,KAAK,CAACyB,MAAM,CAACE,OAAO;AAC7B,CAAC;AAED,OAAO,IAAMmB,wBAAwB,GAAG,SAA3BA,wBAAwBA,CAAAC,KAAA,EAMT;EAAA,IAL1B/C,KAAK,GAAA+C,KAAA,CAAL/C,KAAK;IACLQ,QAAQ,GAAAuC,KAAA,CAARvC,QAAQ;IACRgB,OAAO,GAAAuB,KAAA,CAAPvB,OAAO;IACPiB,YAAY,GAAAM,KAAA,CAAZN,YAAY;IACZC,cAAA,GAAAK,KAAA,CAAAL,cAAA;EAEA,IAAMM,eAAe,GAAG1B,iCAAiC,CAAC;IACxDtB,KAAK,EAALA,KAAK;IACLwB,OAAA,EAAAA;EACF,CAAC,CAAC;EACF,IAAMyB,WAAW,GAAGlB,6BAA6B,CAAC;IAChD/B,KAAK,EAALA,KAAK;IACLQ,QAAQ,EAARA,QAAQ;IACRgB,OAAA,EAAAA;EACF,CAAC,CAAC;EACF,IAAM0B,SAAS,GAAGX,2BAA2B,CAAC;IAC5CvC,KAAK,EAALA,KAAK;IACLQ,QAAQ,EAARA,QAAQ;IACRgB,OAAO,EAAPA,OAAO;IACPiB,YAAY,EAAZA,YAAY;IACZC,cAAA,EAAAA;EACF,CAAC,CAAC;EACF,IAAMS,WAAW,GAAG7C,6BAA6B,CAAC;IAAEN,KAAA,EAAAA;EAAM,CAAC,CAAC;EAE5D,OAAO;IAAEgD,eAAe,EAAfA,eAAe;IAAEC,WAAW,EAAXA,WAAW;IAAEC,SAAS,EAATA,SAAS;IAAEC,WAAA,EAAAA;EAAY,CAAC;AACjE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}