{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport { Platform } from 'expo-modules-core';\nimport PixelRatio from \"react-native-web/dist/exports/PixelRatio\";\nfunction getScaledAssetPath(asset) {\n  var scale = AssetSourceResolver.pickScale(asset.scales, PixelRatio.get());\n  var scaleSuffix = scale === 1 ? '' : '@' + scale + 'x';\n  var type = !asset.type ? '' : `.${asset.type}`;\n  if (__DEV__) {\n    return asset.httpServerLocation + '/' + asset.name + scaleSuffix + type;\n  } else {\n    return asset.httpServerLocation.replace(/\\.\\.\\//g, '_') + '/' + asset.name + scaleSuffix + type;\n  }\n}\nvar AssetSourceResolver = function () {\n  function AssetSourceResolver(serverUrl, jsbundleUrl, asset) {\n    _classCallCheck(this, AssetSourceResolver);\n    this.serverUrl = serverUrl || 'https://expo.dev';\n    this.jsbundleUrl = null;\n    this.asset = asset;\n  }\n  return _createClass(AssetSourceResolver, [{\n    key: \"isLoadedFromServer\",\n    value: function isLoadedFromServer() {\n      return true;\n    }\n  }, {\n    key: \"isLoadedFromFileSystem\",\n    value: function isLoadedFromFileSystem() {\n      return false;\n    }\n  }, {\n    key: \"defaultAsset\",\n    value: function defaultAsset() {\n      return this.assetServerURL();\n    }\n  }, {\n    key: \"assetServerURL\",\n    value: function assetServerURL() {\n      var fromUrl = new URL(getScaledAssetPath(this.asset), this.serverUrl);\n      fromUrl.searchParams.set('platform', Platform.OS);\n      fromUrl.searchParams.set('hash', this.asset.hash);\n      return this.fromSource(fromUrl.toString().replace(fromUrl.origin, ''));\n    }\n  }, {\n    key: \"fromSource\",\n    value: function fromSource(source) {\n      var _this$asset$width, _this$asset$height;\n      return {\n        __packager_asset: true,\n        width: (_this$asset$width = this.asset.width) != null ? _this$asset$width : undefined,\n        height: (_this$asset$height = this.asset.height) != null ? _this$asset$height : undefined,\n        uri: source,\n        scale: AssetSourceResolver.pickScale(this.asset.scales, PixelRatio.get())\n      };\n    }\n  }], [{\n    key: \"pickScale\",\n    value: function pickScale(scales, deviceScale) {\n      for (var i = 0; i < scales.length; i++) {\n        if (scales[i] >= deviceScale) {\n          return scales[i];\n        }\n      }\n      return scales[scales.length - 1] || 1;\n    }\n  }]);\n}();\nexport { AssetSourceResolver as default };", "map": {"version": 3, "names": ["Platform", "PixelRatio", "getScaledAssetPath", "asset", "scale", "AssetSourceResolver", "pickScale", "scales", "get", "scaleSuffix", "type", "__DEV__", "httpServerLocation", "name", "replace", "serverUrl", "jsbundleUrl", "_classCallCheck", "_createClass", "key", "value", "isLoadedFromServer", "isLoadedFromFileSystem", "defaultAsset", "assetServerURL", "fromUrl", "URL", "searchParams", "set", "OS", "hash", "fromSource", "toString", "origin", "source", "_this$asset$width", "_this$asset$height", "__packager_asset", "width", "undefined", "height", "uri", "deviceScale", "i", "length", "default"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\expo-asset\\src\\AssetSourceResolver.web.ts"], "sourcesContent": ["import { Platform } from 'expo-modules-core';\nimport { PixelRatio } from 'react-native';\nimport { PackagerAsset } from 'react-native/Libraries/Image/AssetRegistry';\n\nexport type ResolvedAssetSource = {\n  __packager_asset: boolean;\n  width?: number;\n  height?: number;\n  uri: string;\n  scale: number;\n};\n\n// Returns the Metro dev server-specific asset location.\nfunction getScaledAssetPath(asset): string {\n  const scale = AssetSourceResolver.pickScale(asset.scales, PixelRatio.get());\n  const scaleSuffix = scale === 1 ? '' : '@' + scale + 'x';\n  const type = !asset.type ? '' : `.${asset.type}`;\n  if (__DEV__) {\n    return asset.httpServerLocation + '/' + asset.name + scaleSuffix + type;\n  } else {\n    return asset.httpServerLocation.replace(/\\.\\.\\//g, '_') + '/' + asset.name + scaleSuffix + type;\n  }\n}\n\nexport default class AssetSourceResolver {\n  serverUrl: string;\n  // where the jsbundle is being run from\n  // NOTE(EvanBacon): Never defined on web.\n  jsbundleUrl?: string | null;\n  // the asset to resolve\n  asset: PackagerAsset;\n\n  constructor(\n    serverUrl: string | undefined | null,\n    jsbundleUrl: string | undefined | null,\n    asset: PackagerAsset\n  ) {\n    this.serverUrl = serverUrl || 'https://expo.dev';\n    this.jsbundleUrl = null;\n    this.asset = asset;\n  }\n\n  // Always true for web runtimes\n  isLoadedFromServer(): boolean {\n    return true;\n  }\n\n  // Always false for web runtimes\n  isLoadedFromFileSystem(): boolean {\n    return false;\n  }\n\n  defaultAsset(): ResolvedAssetSource {\n    return this.assetServerURL();\n  }\n\n  /**\n   * @returns absolute remote URL for the hosted asset.\n   */\n  assetServerURL(): ResolvedAssetSource {\n    const fromUrl = new URL(getScaledAssetPath(this.asset), this.serverUrl);\n    fromUrl.searchParams.set('platform', Platform.OS);\n    fromUrl.searchParams.set('hash', this.asset.hash);\n    return this.fromSource(\n      // Relative on web\n      fromUrl.toString().replace(fromUrl.origin, '')\n    );\n  }\n\n  fromSource(source: string): ResolvedAssetSource {\n    return {\n      __packager_asset: true,\n      width: this.asset.width ?? undefined,\n      height: this.asset.height ?? undefined,\n      uri: source,\n      scale: AssetSourceResolver.pickScale(this.asset.scales, PixelRatio.get()),\n    };\n  }\n\n  static pickScale(scales: number[], deviceScale: number): number {\n    for (let i = 0; i < scales.length; i++) {\n      if (scales[i] >= deviceScale) {\n        return scales[i];\n      }\n    }\n    return scales[scales.length - 1] || 1;\n  }\n}\n"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,mBAAmB;AAAC,OAAAC,UAAA;AAa7C,SAASC,kBAAkBA,CAACC,KAAK;EAC/B,IAAMC,KAAK,GAAGC,mBAAmB,CAACC,SAAS,CAACH,KAAK,CAACI,MAAM,EAAEN,UAAU,CAACO,GAAG,EAAE,CAAC;EAC3E,IAAMC,WAAW,GAAGL,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,GAAG,GAAGA,KAAK,GAAG,GAAG;EACxD,IAAMM,IAAI,GAAG,CAACP,KAAK,CAACO,IAAI,GAAG,EAAE,GAAG,IAAIP,KAAK,CAACO,IAAI,EAAE;EAChD,IAAIC,OAAO,EAAE;IACX,OAAOR,KAAK,CAACS,kBAAkB,GAAG,GAAG,GAAGT,KAAK,CAACU,IAAI,GAAGJ,WAAW,GAAGC,IAAI;GACxE,MAAM;IACL,OAAOP,KAAK,CAACS,kBAAkB,CAACE,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,GAAG,GAAG,GAAGX,KAAK,CAACU,IAAI,GAAGJ,WAAW,GAAGC,IAAI;;AAEnG;AAAC,IAEoBL,mBAAmB;EAQtC,SAAAA,oBACEU,SAAoC,EACpCC,WAAsC,EACtCb,KAAoB;IAAAc,eAAA,OAAAZ,mBAAA;IAEpB,IAAI,CAACU,SAAS,GAAGA,SAAS,IAAI,kBAAkB;IAChD,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB,IAAI,CAACb,KAAK,GAAGA,KAAK;EACpB;EAAC,OAAAe,YAAA,CAAAb,mBAAA;IAAAc,GAAA;IAAAC,KAAA,EAGD,SAAAC,kBAAkBA,CAAA;MAChB,OAAO,IAAI;IACb;EAAC;IAAAF,GAAA;IAAAC,KAAA,EAGD,SAAAE,sBAAsBA,CAAA;MACpB,OAAO,KAAK;IACd;EAAC;IAAAH,GAAA;IAAAC,KAAA,EAED,SAAAG,YAAYA,CAAA;MACV,OAAO,IAAI,CAACC,cAAc,EAAE;IAC9B;EAAC;IAAAL,GAAA;IAAAC,KAAA,EAKD,SAAAI,cAAcA,CAAA;MACZ,IAAMC,OAAO,GAAG,IAAIC,GAAG,CAACxB,kBAAkB,CAAC,IAAI,CAACC,KAAK,CAAC,EAAE,IAAI,CAACY,SAAS,CAAC;MACvEU,OAAO,CAACE,YAAY,CAACC,GAAG,CAAC,UAAU,EAAE5B,QAAQ,CAAC6B,EAAE,CAAC;MACjDJ,OAAO,CAACE,YAAY,CAACC,GAAG,CAAC,MAAM,EAAE,IAAI,CAACzB,KAAK,CAAC2B,IAAI,CAAC;MACjD,OAAO,IAAI,CAACC,UAAU,CAEpBN,OAAO,CAACO,QAAQ,EAAE,CAAClB,OAAO,CAACW,OAAO,CAACQ,MAAM,EAAE,EAAE,CAAC,CAC/C;IACH;EAAC;IAAAd,GAAA;IAAAC,KAAA,EAED,SAAAW,UAAUA,CAACG,MAAc;MAAA,IAAAC,iBAAA,EAAAC,kBAAA;MACvB,OAAO;QACLC,gBAAgB,EAAE,IAAI;QACtBC,KAAK,GAAAH,iBAAA,GAAE,IAAI,CAAChC,KAAK,CAACmC,KAAK,YAAAH,iBAAA,GAAII,SAAS;QACpCC,MAAM,GAAAJ,kBAAA,GAAE,IAAI,CAACjC,KAAK,CAACqC,MAAM,YAAAJ,kBAAA,GAAIG,SAAS;QACtCE,GAAG,EAAEP,MAAM;QACX9B,KAAK,EAAEC,mBAAmB,CAACC,SAAS,CAAC,IAAI,CAACH,KAAK,CAACI,MAAM,EAAEN,UAAU,CAACO,GAAG,EAAE;OACzE;IACH;EAAC;IAAAW,GAAA;IAAAC,KAAA,EAED,SAAOd,SAASA,CAACC,MAAgB,EAAEmC,WAAmB;MACpD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpC,MAAM,CAACqC,MAAM,EAAED,CAAC,EAAE,EAAE;QACtC,IAAIpC,MAAM,CAACoC,CAAC,CAAC,IAAID,WAAW,EAAE;UAC5B,OAAOnC,MAAM,CAACoC,CAAC,CAAC;;;MAGpB,OAAOpC,MAAM,CAACA,MAAM,CAACqC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC;IACvC;EAAC;AAAA;AAAA,SA9DkBvC,mBAAmB,IAAAwC,OAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}