{"ast": null, "code": "'use strict';\n\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nimport AnimatedValue from \"./AnimatedValue\";\nimport AnimatedWithChildren from \"./AnimatedWithChildren\";\nimport invariant from 'fbjs/lib/invariant';\nvar _uniqueId = 1;\nvar AnimatedValueXY = function (_AnimatedWithChildren) {\n  function AnimatedValueXY(valueIn) {\n    var _this;\n    _classCallCheck(this, AnimatedValueXY);\n    _this = _callSuper(this, AnimatedValueXY);\n    var value = valueIn || {\n      x: 0,\n      y: 0\n    };\n    if (typeof value.x === 'number' && typeof value.y === 'number') {\n      _this.x = new AnimatedValue(value.x);\n      _this.y = new AnimatedValue(value.y);\n    } else {\n      invariant(value.x instanceof AnimatedValue && value.y instanceof AnimatedValue, 'AnimatedValueXY must be initialized with an object of numbers or ' + 'AnimatedValues.');\n      _this.x = value.x;\n      _this.y = value.y;\n    }\n    _this._listeners = {};\n    return _this;\n  }\n  _inherits(AnimatedValueXY, _AnimatedWithChildren);\n  return _createClass(AnimatedValueXY, [{\n    key: \"setValue\",\n    value: function setValue(value) {\n      this.x.setValue(value.x);\n      this.y.setValue(value.y);\n    }\n  }, {\n    key: \"setOffset\",\n    value: function setOffset(offset) {\n      this.x.setOffset(offset.x);\n      this.y.setOffset(offset.y);\n    }\n  }, {\n    key: \"flattenOffset\",\n    value: function flattenOffset() {\n      this.x.flattenOffset();\n      this.y.flattenOffset();\n    }\n  }, {\n    key: \"extractOffset\",\n    value: function extractOffset() {\n      this.x.extractOffset();\n      this.y.extractOffset();\n    }\n  }, {\n    key: \"__getValue\",\n    value: function __getValue() {\n      return {\n        x: this.x.__getValue(),\n        y: this.y.__getValue()\n      };\n    }\n  }, {\n    key: \"resetAnimation\",\n    value: function resetAnimation(callback) {\n      this.x.resetAnimation();\n      this.y.resetAnimation();\n      callback && callback(this.__getValue());\n    }\n  }, {\n    key: \"stopAnimation\",\n    value: function stopAnimation(callback) {\n      this.x.stopAnimation();\n      this.y.stopAnimation();\n      callback && callback(this.__getValue());\n    }\n  }, {\n    key: \"addListener\",\n    value: function addListener(callback) {\n      var _this2 = this;\n      var id = String(_uniqueId++);\n      var jointCallback = function jointCallback(_ref) {\n        var number = _ref.value;\n        callback(_this2.__getValue());\n      };\n      this._listeners[id] = {\n        x: this.x.addListener(jointCallback),\n        y: this.y.addListener(jointCallback)\n      };\n      return id;\n    }\n  }, {\n    key: \"removeListener\",\n    value: function removeListener(id) {\n      this.x.removeListener(this._listeners[id].x);\n      this.y.removeListener(this._listeners[id].y);\n      delete this._listeners[id];\n    }\n  }, {\n    key: \"removeAllListeners\",\n    value: function removeAllListeners() {\n      this.x.removeAllListeners();\n      this.y.removeAllListeners();\n      this._listeners = {};\n    }\n  }, {\n    key: \"getLayout\",\n    value: function getLayout() {\n      return {\n        left: this.x,\n        top: this.y\n      };\n    }\n  }, {\n    key: \"getTranslateTransform\",\n    value: function getTranslateTransform() {\n      return [{\n        translateX: this.x\n      }, {\n        translateY: this.y\n      }];\n    }\n  }]);\n}(AnimatedWithChildren);\nexport default AnimatedValueXY;", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "_possibleConstructorReturn", "_getPrototypeOf", "_inherits", "_callSuper", "t", "o", "e", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "AnimatedValue", "AnimatedWithChildren", "invariant", "_uniqueId", "AnimatedValueXY", "_Animated<PERSON>ith<PERSON><PERSON><PERSON><PERSON>", "valueIn", "_this", "value", "x", "y", "_listeners", "key", "setValue", "setOffset", "offset", "flattenOffset", "extractOffset", "__getValue", "resetAnimation", "callback", "stopAnimation", "addListener", "_this2", "id", "String", "jointCallback", "_ref", "number", "removeListener", "removeAllListeners", "getLayout", "left", "top", "getTranslateTransform", "translateX", "translateY"], "sources": ["E:/CryptoSignalsApp/node_modules/react-native-web/dist/vendor/react-native/Animated/nodes/AnimatedValueXY.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use strict';\n\nimport AnimatedValue from './AnimatedValue';\nimport AnimatedWithChildren from './AnimatedWithChildren';\nimport invariant from 'fbjs/lib/invariant';\nvar _uniqueId = 1;\n\n/**\n * 2D Value for driving 2D animations, such as pan gestures. Almost identical\n * API to normal `Animated.Value`, but multiplexed.\n *\n * See https://reactnative.dev/docs/animatedvaluexy.html\n */\nclass AnimatedValueXY extends AnimatedWithChildren {\n  constructor(valueIn) {\n    super();\n    var value = valueIn || {\n      x: 0,\n      y: 0\n    }; // fixme: shouldn't need `: any`\n    if (typeof value.x === 'number' && typeof value.y === 'number') {\n      this.x = new AnimatedValue(value.x);\n      this.y = new AnimatedValue(value.y);\n    } else {\n      invariant(value.x instanceof AnimatedValue && value.y instanceof AnimatedValue, 'AnimatedValueXY must be initialized with an object of numbers or ' + 'AnimatedValues.');\n      this.x = value.x;\n      this.y = value.y;\n    }\n    this._listeners = {};\n  }\n\n  /**\n   * Directly set the value. This will stop any animations running on the value\n   * and update all the bound properties.\n   *\n   * See https://reactnative.dev/docs/animatedvaluexy.html#setvalue\n   */\n  setValue(value) {\n    this.x.setValue(value.x);\n    this.y.setValue(value.y);\n  }\n\n  /**\n   * Sets an offset that is applied on top of whatever value is set, whether\n   * via `setValue`, an animation, or `Animated.event`. Useful for compensating\n   * things like the start of a pan gesture.\n   *\n   * See https://reactnative.dev/docs/animatedvaluexy.html#setoffset\n   */\n  setOffset(offset) {\n    this.x.setOffset(offset.x);\n    this.y.setOffset(offset.y);\n  }\n\n  /**\n   * Merges the offset value into the base value and resets the offset to zero.\n   * The final output of the value is unchanged.\n   *\n   * See https://reactnative.dev/docs/animatedvaluexy.html#flattenoffset\n   */\n  flattenOffset() {\n    this.x.flattenOffset();\n    this.y.flattenOffset();\n  }\n\n  /**\n   * Sets the offset value to the base value, and resets the base value to\n   * zero. The final output of the value is unchanged.\n   *\n   * See https://reactnative.dev/docs/animatedvaluexy.html#extractoffset\n   */\n  extractOffset() {\n    this.x.extractOffset();\n    this.y.extractOffset();\n  }\n  __getValue() {\n    return {\n      x: this.x.__getValue(),\n      y: this.y.__getValue()\n    };\n  }\n\n  /**\n   * Stops any animation and resets the value to its original.\n   *\n   * See https://reactnative.dev/docs/animatedvaluexy.html#resetanimation\n   */\n  resetAnimation(callback) {\n    this.x.resetAnimation();\n    this.y.resetAnimation();\n    callback && callback(this.__getValue());\n  }\n\n  /**\n   * Stops any running animation or tracking. `callback` is invoked with the\n   * final value after stopping the animation, which is useful for updating\n   * state to match the animation position with layout.\n   *\n   * See https://reactnative.dev/docs/animatedvaluexy.html#stopanimation\n   */\n  stopAnimation(callback) {\n    this.x.stopAnimation();\n    this.y.stopAnimation();\n    callback && callback(this.__getValue());\n  }\n\n  /**\n   * Adds an asynchronous listener to the value so you can observe updates from\n   * animations.  This is useful because there is no way to synchronously read\n   * the value because it might be driven natively.\n   *\n   * Returns a string that serves as an identifier for the listener.\n   *\n   * See https://reactnative.dev/docs/animatedvaluexy.html#addlistener\n   */\n  addListener(callback) {\n    var id = String(_uniqueId++);\n    var jointCallback = _ref => {\n      var number = _ref.value;\n      callback(this.__getValue());\n    };\n    this._listeners[id] = {\n      x: this.x.addListener(jointCallback),\n      y: this.y.addListener(jointCallback)\n    };\n    return id;\n  }\n\n  /**\n   * Unregister a listener. The `id` param shall match the identifier\n   * previously returned by `addListener()`.\n   *\n   * See https://reactnative.dev/docs/animatedvaluexy.html#removelistener\n   */\n  removeListener(id) {\n    this.x.removeListener(this._listeners[id].x);\n    this.y.removeListener(this._listeners[id].y);\n    delete this._listeners[id];\n  }\n\n  /**\n   * Remove all registered listeners.\n   *\n   * See https://reactnative.dev/docs/animatedvaluexy.html#removealllisteners\n   */\n  removeAllListeners() {\n    this.x.removeAllListeners();\n    this.y.removeAllListeners();\n    this._listeners = {};\n  }\n\n  /**\n   * Converts `{x, y}` into `{left, top}` for use in style.\n   *\n   * See https://reactnative.dev/docs/animatedvaluexy.html#getlayout\n   */\n  getLayout() {\n    return {\n      left: this.x,\n      top: this.y\n    };\n  }\n\n  /**\n   * Converts `{x, y}` into a useable translation transform.\n   *\n   * See https://reactnative.dev/docs/animatedvaluexy.html#gettranslatetransform\n   */\n  getTranslateTransform() {\n    return [{\n      translateX: this.x\n    }, {\n      translateY: this.y\n    }];\n  }\n}\nexport default AnimatedValueXY;"], "mappings": "AAUA,YAAY;;AAAC,OAAAA,eAAA;AAAA,OAAAC,YAAA;AAAA,OAAAC,0BAAA;AAAA,OAAAC,eAAA;AAAA,OAAAC,SAAA;AAAA,SAAAC,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,GAAAJ,eAAA,CAAAI,CAAA,GAAAL,0BAAA,CAAAI,CAAA,EAAAG,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAJ,CAAA,EAAAC,CAAA,QAAAL,eAAA,CAAAG,CAAA,EAAAM,WAAA,IAAAL,CAAA,CAAAM,KAAA,CAAAP,CAAA,EAAAE,CAAA;AAAA,SAAAC,0BAAA,cAAAH,CAAA,IAAAQ,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAR,CAAA,aAAAG,yBAAA,YAAAA,0BAAA,aAAAH,CAAA;AAEb,OAAOY,aAAa;AACpB,OAAOC,oBAAoB;AAC3B,OAAOC,SAAS,MAAM,oBAAoB;AAC1C,IAAIC,SAAS,GAAG,CAAC;AAAC,IAQZC,eAAe,aAAAC,qBAAA;EACnB,SAAAD,gBAAYE,OAAO,EAAE;IAAA,IAAAC,KAAA;IAAAzB,eAAA,OAAAsB,eAAA;IACnBG,KAAA,GAAApB,UAAA,OAAAiB,eAAA;IACA,IAAII,KAAK,GAAGF,OAAO,IAAI;MACrBG,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE;IACL,CAAC;IACD,IAAI,OAAOF,KAAK,CAACC,CAAC,KAAK,QAAQ,IAAI,OAAOD,KAAK,CAACE,CAAC,KAAK,QAAQ,EAAE;MAC9DH,KAAA,CAAKE,CAAC,GAAG,IAAIT,aAAa,CAACQ,KAAK,CAACC,CAAC,CAAC;MACnCF,KAAA,CAAKG,CAAC,GAAG,IAAIV,aAAa,CAACQ,KAAK,CAACE,CAAC,CAAC;IACrC,CAAC,MAAM;MACLR,SAAS,CAACM,KAAK,CAACC,CAAC,YAAYT,aAAa,IAAIQ,KAAK,CAACE,CAAC,YAAYV,aAAa,EAAE,mEAAmE,GAAG,iBAAiB,CAAC;MACxKO,KAAA,CAAKE,CAAC,GAAGD,KAAK,CAACC,CAAC;MAChBF,KAAA,CAAKG,CAAC,GAAGF,KAAK,CAACE,CAAC;IAClB;IACAH,KAAA,CAAKI,UAAU,GAAG,CAAC,CAAC;IAAC,OAAAJ,KAAA;EACvB;EAACrB,SAAA,CAAAkB,eAAA,EAAAC,qBAAA;EAAA,OAAAtB,YAAA,CAAAqB,eAAA;IAAAQ,GAAA;IAAAJ,KAAA,EAQD,SAAAK,QAAQA,CAACL,KAAK,EAAE;MACd,IAAI,CAACC,CAAC,CAACI,QAAQ,CAACL,KAAK,CAACC,CAAC,CAAC;MACxB,IAAI,CAACC,CAAC,CAACG,QAAQ,CAACL,KAAK,CAACE,CAAC,CAAC;IAC1B;EAAC;IAAAE,GAAA;IAAAJ,KAAA,EASD,SAAAM,SAASA,CAACC,MAAM,EAAE;MAChB,IAAI,CAACN,CAAC,CAACK,SAAS,CAACC,MAAM,CAACN,CAAC,CAAC;MAC1B,IAAI,CAACC,CAAC,CAACI,SAAS,CAACC,MAAM,CAACL,CAAC,CAAC;IAC5B;EAAC;IAAAE,GAAA;IAAAJ,KAAA,EAQD,SAAAQ,aAAaA,CAAA,EAAG;MACd,IAAI,CAACP,CAAC,CAACO,aAAa,CAAC,CAAC;MACtB,IAAI,CAACN,CAAC,CAACM,aAAa,CAAC,CAAC;IACxB;EAAC;IAAAJ,GAAA;IAAAJ,KAAA,EAQD,SAAAS,aAAaA,CAAA,EAAG;MACd,IAAI,CAACR,CAAC,CAACQ,aAAa,CAAC,CAAC;MACtB,IAAI,CAACP,CAAC,CAACO,aAAa,CAAC,CAAC;IACxB;EAAC;IAAAL,GAAA;IAAAJ,KAAA,EACD,SAAAU,UAAUA,CAAA,EAAG;MACX,OAAO;QACLT,CAAC,EAAE,IAAI,CAACA,CAAC,CAACS,UAAU,CAAC,CAAC;QACtBR,CAAC,EAAE,IAAI,CAACA,CAAC,CAACQ,UAAU,CAAC;MACvB,CAAC;IACH;EAAC;IAAAN,GAAA;IAAAJ,KAAA,EAOD,SAAAW,cAAcA,CAACC,QAAQ,EAAE;MACvB,IAAI,CAACX,CAAC,CAACU,cAAc,CAAC,CAAC;MACvB,IAAI,CAACT,CAAC,CAACS,cAAc,CAAC,CAAC;MACvBC,QAAQ,IAAIA,QAAQ,CAAC,IAAI,CAACF,UAAU,CAAC,CAAC,CAAC;IACzC;EAAC;IAAAN,GAAA;IAAAJ,KAAA,EASD,SAAAa,aAAaA,CAACD,QAAQ,EAAE;MACtB,IAAI,CAACX,CAAC,CAACY,aAAa,CAAC,CAAC;MACtB,IAAI,CAACX,CAAC,CAACW,aAAa,CAAC,CAAC;MACtBD,QAAQ,IAAIA,QAAQ,CAAC,IAAI,CAACF,UAAU,CAAC,CAAC,CAAC;IACzC;EAAC;IAAAN,GAAA;IAAAJ,KAAA,EAWD,SAAAc,WAAWA,CAACF,QAAQ,EAAE;MAAA,IAAAG,MAAA;MACpB,IAAIC,EAAE,GAAGC,MAAM,CAACtB,SAAS,EAAE,CAAC;MAC5B,IAAIuB,aAAa,GAAG,SAAhBA,aAAaA,CAAGC,IAAI,EAAI;QAC1B,IAAIC,MAAM,GAAGD,IAAI,CAACnB,KAAK;QACvBY,QAAQ,CAACG,MAAI,CAACL,UAAU,CAAC,CAAC,CAAC;MAC7B,CAAC;MACD,IAAI,CAACP,UAAU,CAACa,EAAE,CAAC,GAAG;QACpBf,CAAC,EAAE,IAAI,CAACA,CAAC,CAACa,WAAW,CAACI,aAAa,CAAC;QACpChB,CAAC,EAAE,IAAI,CAACA,CAAC,CAACY,WAAW,CAACI,aAAa;MACrC,CAAC;MACD,OAAOF,EAAE;IACX;EAAC;IAAAZ,GAAA;IAAAJ,KAAA,EAQD,SAAAqB,cAAcA,CAACL,EAAE,EAAE;MACjB,IAAI,CAACf,CAAC,CAACoB,cAAc,CAAC,IAAI,CAAClB,UAAU,CAACa,EAAE,CAAC,CAACf,CAAC,CAAC;MAC5C,IAAI,CAACC,CAAC,CAACmB,cAAc,CAAC,IAAI,CAAClB,UAAU,CAACa,EAAE,CAAC,CAACd,CAAC,CAAC;MAC5C,OAAO,IAAI,CAACC,UAAU,CAACa,EAAE,CAAC;IAC5B;EAAC;IAAAZ,GAAA;IAAAJ,KAAA,EAOD,SAAAsB,kBAAkBA,CAAA,EAAG;MACnB,IAAI,CAACrB,CAAC,CAACqB,kBAAkB,CAAC,CAAC;MAC3B,IAAI,CAACpB,CAAC,CAACoB,kBAAkB,CAAC,CAAC;MAC3B,IAAI,CAACnB,UAAU,GAAG,CAAC,CAAC;IACtB;EAAC;IAAAC,GAAA;IAAAJ,KAAA,EAOD,SAAAuB,SAASA,CAAA,EAAG;MACV,OAAO;QACLC,IAAI,EAAE,IAAI,CAACvB,CAAC;QACZwB,GAAG,EAAE,IAAI,CAACvB;MACZ,CAAC;IACH;EAAC;IAAAE,GAAA;IAAAJ,KAAA,EAOD,SAAA0B,qBAAqBA,CAAA,EAAG;MACtB,OAAO,CAAC;QACNC,UAAU,EAAE,IAAI,CAAC1B;MACnB,CAAC,EAAE;QACD2B,UAAU,EAAE,IAAI,CAAC1B;MACnB,CAAC,CAAC;IACJ;EAAC;AAAA,EAjK2BT,oBAAoB;AAmKlD,eAAeG,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}