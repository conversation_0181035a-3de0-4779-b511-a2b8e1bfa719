{"ast": null, "code": "import * as React from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport { useInternalTheme } from \"../../core/theming\";\nimport Icon from \"../Icon\";\nvar DialogIcon = function DialogIcon(_ref) {\n  var _ref$size = _ref.size,\n    size = _ref$size === void 0 ? 24 : _ref$size,\n    color = _ref.color,\n    icon = _ref.icon,\n    themeOverrides = _ref.theme;\n  var theme = useInternalTheme(themeOverrides);\n  if (!theme.isV3) {\n    return null;\n  }\n  var iconColor = color || theme.colors.secondary;\n  return React.createElement(View, {\n    style: styles.wrapper\n  }, React.createElement(Icon, {\n    source: icon,\n    color: iconColor,\n    size: size\n  }));\n};\nDialogIcon.displayName = 'Dialog.Icon';\nvar styles = StyleSheet.create({\n  wrapper: {\n    alignItems: 'center',\n    justifyContent: 'center',\n    paddingTop: 24\n  }\n});\nexport default DialogIcon;\nexport { DialogIcon };", "map": {"version": 3, "names": ["React", "StyleSheet", "View", "useInternalTheme", "Icon", "DialogIcon", "_ref", "_ref$size", "size", "color", "icon", "themeOverrides", "theme", "isV3", "iconColor", "colors", "secondary", "createElement", "style", "styles", "wrapper", "source", "displayName", "create", "alignItems", "justifyContent", "paddingTop"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\Dialog\\DialogIcon.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { StyleSheet, View } from 'react-native';\n\nimport type { ThemeProp } from 'src/types';\n\nimport { useInternalTheme } from '../../core/theming';\nimport Icon, { IconSource } from '../Icon';\n\nexport type Props = {\n  /**\n   *  Custom color for action icon.\n   */\n  color?: string;\n  /**\n   * Name of the icon to show.\n   */\n  icon: IconSource;\n  /**\n   * Optional icon size.\n   */\n  size?: number;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n};\n\n/**\n * @supported Available in v5.x with theme version 3\n * A component to show an icon in a Dialog.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { StyleSheet } from 'react-native';\n * import { Dialog, Portal, Text } from 'react-native-paper';\n *\n * const MyComponent = () => {\n *   const [visible, setVisible] = React.useState(false);\n *\n *   const hideDialog = () => setVisible(false);\n *\n *   return (\n *     <Portal>\n *       <Dialog visible={visible} onDismiss={hideDialog}>\n *         <Dialog.Icon icon=\"alert\" />\n *         <Dialog.Title style={styles.title}>This is a title</Dialog.Title>\n *         <Dialog.Content>\n *           <Text variant=\"bodyMedium\">This is simple dialog</Text>\n *         </Dialog.Content>\n *       </Dialog>\n *     </Portal>\n *   );\n * };\n *\n * const styles = StyleSheet.create({\n *   title: {\n *     textAlign: 'center',\n *   },\n * })\n *\n * export default MyComponent;\n * ```\n */\nconst DialogIcon = ({\n  size = 24,\n  color,\n  icon,\n  theme: themeOverrides,\n}: Props) => {\n  const theme = useInternalTheme(themeOverrides);\n\n  if (!theme.isV3) {\n    return null;\n  }\n\n  //@ts-ignore\n  const iconColor = color || theme.colors.secondary;\n\n  return (\n    <View style={styles.wrapper}>\n      <Icon source={icon} color={iconColor} size={size} />\n    </View>\n  );\n};\n\nDialogIcon.displayName = 'Dialog.Icon';\n\nconst styles = StyleSheet.create({\n  wrapper: {\n    alignItems: 'center',\n    justifyContent: 'center',\n    paddingTop: 24,\n  },\n});\n\nexport default DialogIcon;\n\n// @component-docs ignore-next-line\nexport { DialogIcon };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAK9B,SAASC,gBAAgB;AACzB,OAAOC,IAAI;AA0DX,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAAC,IAAA,EAKH;EAAA,IAAAC,SAAA,GAAAD,IAAA,CAJXE,IAAI;IAAJA,IAAI,GAAAD,SAAA,cAAG,EAAE,GAAAA,SAAA;IACTE,KAAK,GAAAH,IAAA,CAALG,KAAK;IACLC,IAAI,GAAAJ,IAAA,CAAJI,IAAI;IACGC,cAAA,GAAAL,IAAA,CAAPM,KAAK;EAEL,IAAMA,KAAK,GAAGT,gBAAgB,CAACQ,cAAc,CAAC;EAE9C,IAAI,CAACC,KAAK,CAACC,IAAI,EAAE;IACf,OAAO,IAAI;EACb;EAGA,IAAMC,SAAS,GAAGL,KAAK,IAAIG,KAAK,CAACG,MAAM,CAACC,SAAS;EAEjD,OACEhB,KAAA,CAAAiB,aAAA,CAACf,IAAI;IAACgB,KAAK,EAAEC,MAAM,CAACC;EAAQ,GAC1BpB,KAAA,CAAAiB,aAAA,CAACb,IAAI;IAACiB,MAAM,EAAEX,IAAK;IAACD,KAAK,EAAEK,SAAU;IAACN,IAAI,EAAEA;EAAK,CAAE,CAC/C,CAAC;AAEX,CAAC;AAEDH,UAAU,CAACiB,WAAW,GAAG,aAAa;AAEtC,IAAMH,MAAM,GAAGlB,UAAU,CAACsB,MAAM,CAAC;EAC/BH,OAAO,EAAE;IACPI,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE;EACd;AACF,CAAC,CAAC;AAEF,eAAerB,UAAU;AAGzB,SAASA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}