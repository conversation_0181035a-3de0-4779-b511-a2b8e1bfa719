{"ast": null, "code": "'use client';\n\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport UIManager from \"react-native-web/dist/exports/UIManager\";\nexport var isNativePlatformSupported = Platform.OS === 'ios' || Platform.OS === 'android' || Platform.OS === 'windows';\nvar ENABLE_SCREENS = isNativePlatformSupported;\nexport function enableScreens() {\n  var shouldEnableScreens = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n  ENABLE_SCREENS = shouldEnableScreens;\n  if (!isNativePlatformSupported) {\n    return;\n  }\n  if (ENABLE_SCREENS && !UIManager.getViewManagerConfig('RNSScreen')) {\n    console.error(`Screen native module hasn't been linked. Please check the react-native-screens README for more details`);\n  }\n}\nvar ENABLE_FREEZE = false;\nexport function enableFreeze() {\n  var shouldEnableReactFreeze = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n  if (!isNativePlatformSupported) {\n    return;\n  }\n  ENABLE_FREEZE = shouldEnableReactFreeze;\n}\nexport function screensEnabled() {\n  return ENABLE_SCREENS;\n}\nexport function freezeEnabled() {\n  return ENABLE_FREEZE;\n}", "map": {"version": 3, "names": ["Platform", "UIManager", "isNativePlatformSupported", "OS", "ENABLE_SCREENS", "enableScreens", "shouldEnableScreens", "arguments", "length", "undefined", "getViewManagerConfig", "console", "error", "ENABLE_FREEZE", "enableFreeze", "shouldEnableReactFreeze", "screensEnabled", "freezeEnabled"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\react-native-screens\\src\\core.ts"], "sourcesContent": ["'use client';\n\nimport { Platform, UIManager } from 'react-native';\n\nexport const isNativePlatformSupported =\n  Platform.OS === 'ios' ||\n  Platform.OS === 'android' ||\n  Platform.OS === 'windows';\n\nlet ENABLE_SCREENS = isNativePlatformSupported;\n\nexport function enableScreens(shouldEnableScreens = true) {\n  ENABLE_SCREENS = shouldEnableScreens;\n\n  if (!isNativePlatformSupported) {\n    return;\n  }\n\n  if (ENABLE_SCREENS && !UIManager.getViewManagerConfig('RNSScreen')) {\n    console.error(\n      `Screen native module hasn't been linked. Please check the react-native-screens README for more details`,\n    );\n  }\n}\n\nlet ENABLE_FREEZE = false;\n\nexport function enableFreeze(shouldEnableReactFreeze = true) {\n  if (!isNativePlatformSupported) {\n    return;\n  }\n\n  ENABLE_FREEZE = shouldEnableReactFreeze;\n}\n\nexport function screensEnabled() {\n  return ENABLE_SCREENS;\n}\n\nexport function freezeEnabled() {\n  return ENABLE_FREEZE;\n}\n"], "mappings": "AAAA,YAAY;;AAAA,OAAAA,QAAA;AAAA,OAAAC,SAAA;AAIZ,OAAO,IAAMC,yBAAyB,GACpCF,QAAQ,CAACG,EAAE,KAAK,KAAK,IACrBH,QAAQ,CAACG,EAAE,KAAK,SAAS,IACzBH,QAAQ,CAACG,EAAE,KAAK,SAAS;AAE3B,IAAIC,cAAc,GAAGF,yBAAyB;AAE9C,OAAO,SAASG,aAAaA,CAAA,EAA6B;EAAA,IAA5BC,mBAAmB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EACtDH,cAAc,GAAGE,mBAAmB;EAEpC,IAAI,CAACJ,yBAAyB,EAAE;IAC9B;EACF;EAEA,IAAIE,cAAc,IAAI,CAACH,SAAS,CAACS,oBAAoB,CAAC,WAAW,CAAC,EAAE;IAClEC,OAAO,CAACC,KAAK,CACX,wGACF,CAAC;EACH;AACF;AAEA,IAAIC,aAAa,GAAG,KAAK;AAEzB,OAAO,SAASC,YAAYA,CAAA,EAAiC;EAAA,IAAhCC,uBAAuB,GAAAR,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EACzD,IAAI,CAACL,yBAAyB,EAAE;IAC9B;EACF;EAEAW,aAAa,GAAGE,uBAAuB;AACzC;AAEA,OAAO,SAASC,cAAcA,CAAA,EAAG;EAC/B,OAAOZ,cAAc;AACvB;AAEA,OAAO,SAASa,aAAaA,CAAA,EAAG;EAC9B,OAAOJ,aAAa;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}