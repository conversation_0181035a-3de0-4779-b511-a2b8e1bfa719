{"ast": null, "code": "'use strict';\n\nvar colorString = require('color-string');\nvar convert = require('color-convert');\nvar _slice = [].slice;\nvar skippedModels = ['keyword', 'gray', 'hex'];\nvar hashedModelKeys = {};\nObject.keys(convert).forEach(function (model) {\n  hashedModelKeys[_slice.call(convert[model].labels).sort().join('')] = model;\n});\nvar limiters = {};\nfunction Color(obj, model) {\n  if (!(this instanceof Color)) {\n    return new Color(obj, model);\n  }\n  if (model && model in skippedModels) {\n    model = null;\n  }\n  if (model && !(model in convert)) {\n    throw new Error('Unknown model: ' + model);\n  }\n  var i;\n  var channels;\n  if (obj == null) {\n    this.model = 'rgb';\n    this.color = [0, 0, 0];\n    this.valpha = 1;\n  } else if (obj instanceof Color) {\n    this.model = obj.model;\n    this.color = obj.color.slice();\n    this.valpha = obj.valpha;\n  } else if (typeof obj === 'string') {\n    var result = colorString.get(obj);\n    if (result === null) {\n      throw new Error('Unable to parse color from string: ' + obj);\n    }\n    this.model = result.model;\n    channels = convert[this.model].channels;\n    this.color = result.value.slice(0, channels);\n    this.valpha = typeof result.value[channels] === 'number' ? result.value[channels] : 1;\n  } else if (obj.length) {\n    this.model = model || 'rgb';\n    channels = convert[this.model].channels;\n    var newArr = _slice.call(obj, 0, channels);\n    this.color = zeroArray(newArr, channels);\n    this.valpha = typeof obj[channels] === 'number' ? obj[channels] : 1;\n  } else if (typeof obj === 'number') {\n    obj &= 0xFFFFFF;\n    this.model = 'rgb';\n    this.color = [obj >> 16 & 0xFF, obj >> 8 & 0xFF, obj & 0xFF];\n    this.valpha = 1;\n  } else {\n    this.valpha = 1;\n    var keys = Object.keys(obj);\n    if ('alpha' in obj) {\n      keys.splice(keys.indexOf('alpha'), 1);\n      this.valpha = typeof obj.alpha === 'number' ? obj.alpha : 0;\n    }\n    var hashedKeys = keys.sort().join('');\n    if (!(hashedKeys in hashedModelKeys)) {\n      throw new Error('Unable to parse color from object: ' + JSON.stringify(obj));\n    }\n    this.model = hashedModelKeys[hashedKeys];\n    var labels = convert[this.model].labels;\n    var color = [];\n    for (i = 0; i < labels.length; i++) {\n      color.push(obj[labels[i]]);\n    }\n    this.color = zeroArray(color);\n  }\n  if (limiters[this.model]) {\n    channels = convert[this.model].channels;\n    for (i = 0; i < channels; i++) {\n      var limit = limiters[this.model][i];\n      if (limit) {\n        this.color[i] = limit(this.color[i]);\n      }\n    }\n  }\n  this.valpha = Math.max(0, Math.min(1, this.valpha));\n  if (Object.freeze) {\n    Object.freeze(this);\n  }\n}\nColor.prototype = {\n  toString: function toString() {\n    return this.string();\n  },\n  toJSON: function toJSON() {\n    return this[this.model]();\n  },\n  string: function string(places) {\n    var self = this.model in colorString.to ? this : this.rgb();\n    self = self.round(typeof places === 'number' ? places : 1);\n    var args = self.valpha === 1 ? self.color : self.color.concat(this.valpha);\n    return colorString.to[self.model](args);\n  },\n  percentString: function percentString(places) {\n    var self = this.rgb().round(typeof places === 'number' ? places : 1);\n    var args = self.valpha === 1 ? self.color : self.color.concat(this.valpha);\n    return colorString.to.rgb.percent(args);\n  },\n  array: function array() {\n    return this.valpha === 1 ? this.color.slice() : this.color.concat(this.valpha);\n  },\n  object: function object() {\n    var result = {};\n    var channels = convert[this.model].channels;\n    var labels = convert[this.model].labels;\n    for (var i = 0; i < channels; i++) {\n      result[labels[i]] = this.color[i];\n    }\n    if (this.valpha !== 1) {\n      result.alpha = this.valpha;\n    }\n    return result;\n  },\n  unitArray: function unitArray() {\n    var rgb = this.rgb().color;\n    rgb[0] /= 255;\n    rgb[1] /= 255;\n    rgb[2] /= 255;\n    if (this.valpha !== 1) {\n      rgb.push(this.valpha);\n    }\n    return rgb;\n  },\n  unitObject: function unitObject() {\n    var rgb = this.rgb().object();\n    rgb.r /= 255;\n    rgb.g /= 255;\n    rgb.b /= 255;\n    if (this.valpha !== 1) {\n      rgb.alpha = this.valpha;\n    }\n    return rgb;\n  },\n  round: function round(places) {\n    places = Math.max(places || 0, 0);\n    return new Color(this.color.map(roundToPlace(places)).concat(this.valpha), this.model);\n  },\n  alpha: function alpha(val) {\n    if (arguments.length) {\n      return new Color(this.color.concat(Math.max(0, Math.min(1, val))), this.model);\n    }\n    return this.valpha;\n  },\n  red: getset('rgb', 0, maxfn(255)),\n  green: getset('rgb', 1, maxfn(255)),\n  blue: getset('rgb', 2, maxfn(255)),\n  hue: getset(['hsl', 'hsv', 'hsl', 'hwb', 'hcg'], 0, function (val) {\n    return (val % 360 + 360) % 360;\n  }),\n  saturationl: getset('hsl', 1, maxfn(100)),\n  lightness: getset('hsl', 2, maxfn(100)),\n  saturationv: getset('hsv', 1, maxfn(100)),\n  value: getset('hsv', 2, maxfn(100)),\n  chroma: getset('hcg', 1, maxfn(100)),\n  gray: getset('hcg', 2, maxfn(100)),\n  white: getset('hwb', 1, maxfn(100)),\n  wblack: getset('hwb', 2, maxfn(100)),\n  cyan: getset('cmyk', 0, maxfn(100)),\n  magenta: getset('cmyk', 1, maxfn(100)),\n  yellow: getset('cmyk', 2, maxfn(100)),\n  black: getset('cmyk', 3, maxfn(100)),\n  x: getset('xyz', 0, maxfn(100)),\n  y: getset('xyz', 1, maxfn(100)),\n  z: getset('xyz', 2, maxfn(100)),\n  l: getset('lab', 0, maxfn(100)),\n  a: getset('lab', 1),\n  b: getset('lab', 2),\n  keyword: function keyword(val) {\n    if (arguments.length) {\n      return new Color(val);\n    }\n    return convert[this.model].keyword(this.color);\n  },\n  hex: function hex(val) {\n    if (arguments.length) {\n      return new Color(val);\n    }\n    return colorString.to.hex(this.rgb().round().color);\n  },\n  rgbNumber: function rgbNumber() {\n    var rgb = this.rgb().color;\n    return (rgb[0] & 0xFF) << 16 | (rgb[1] & 0xFF) << 8 | rgb[2] & 0xFF;\n  },\n  luminosity: function luminosity() {\n    var rgb = this.rgb().color;\n    var lum = [];\n    for (var i = 0; i < rgb.length; i++) {\n      var chan = rgb[i] / 255;\n      lum[i] = chan <= 0.03928 ? chan / 12.92 : Math.pow((chan + 0.055) / 1.055, 2.4);\n    }\n    return 0.2126 * lum[0] + 0.7152 * lum[1] + 0.0722 * lum[2];\n  },\n  contrast: function contrast(color2) {\n    var lum1 = this.luminosity();\n    var lum2 = color2.luminosity();\n    if (lum1 > lum2) {\n      return (lum1 + 0.05) / (lum2 + 0.05);\n    }\n    return (lum2 + 0.05) / (lum1 + 0.05);\n  },\n  level: function level(color2) {\n    var contrastRatio = this.contrast(color2);\n    if (contrastRatio >= 7.1) {\n      return 'AAA';\n    }\n    return contrastRatio >= 4.5 ? 'AA' : '';\n  },\n  isDark: function isDark() {\n    var rgb = this.rgb().color;\n    var yiq = (rgb[0] * 299 + rgb[1] * 587 + rgb[2] * 114) / 1000;\n    return yiq < 128;\n  },\n  isLight: function isLight() {\n    return !this.isDark();\n  },\n  negate: function negate() {\n    var rgb = this.rgb();\n    for (var i = 0; i < 3; i++) {\n      rgb.color[i] = 255 - rgb.color[i];\n    }\n    return rgb;\n  },\n  lighten: function lighten(ratio) {\n    var hsl = this.hsl();\n    hsl.color[2] += hsl.color[2] * ratio;\n    return hsl;\n  },\n  darken: function darken(ratio) {\n    var hsl = this.hsl();\n    hsl.color[2] -= hsl.color[2] * ratio;\n    return hsl;\n  },\n  saturate: function saturate(ratio) {\n    var hsl = this.hsl();\n    hsl.color[1] += hsl.color[1] * ratio;\n    return hsl;\n  },\n  desaturate: function desaturate(ratio) {\n    var hsl = this.hsl();\n    hsl.color[1] -= hsl.color[1] * ratio;\n    return hsl;\n  },\n  whiten: function whiten(ratio) {\n    var hwb = this.hwb();\n    hwb.color[1] += hwb.color[1] * ratio;\n    return hwb;\n  },\n  blacken: function blacken(ratio) {\n    var hwb = this.hwb();\n    hwb.color[2] += hwb.color[2] * ratio;\n    return hwb;\n  },\n  grayscale: function grayscale() {\n    var rgb = this.rgb().color;\n    var val = rgb[0] * 0.3 + rgb[1] * 0.59 + rgb[2] * 0.11;\n    return Color.rgb(val, val, val);\n  },\n  fade: function fade(ratio) {\n    return this.alpha(this.valpha - this.valpha * ratio);\n  },\n  opaquer: function opaquer(ratio) {\n    return this.alpha(this.valpha + this.valpha * ratio);\n  },\n  rotate: function rotate(degrees) {\n    var hsl = this.hsl();\n    var hue = hsl.color[0];\n    hue = (hue + degrees) % 360;\n    hue = hue < 0 ? 360 + hue : hue;\n    hsl.color[0] = hue;\n    return hsl;\n  },\n  mix: function mix(mixinColor, weight) {\n    if (!mixinColor || !mixinColor.rgb) {\n      throw new Error('Argument to \"mix\" was not a Color instance, but rather an instance of ' + typeof mixinColor);\n    }\n    var color1 = mixinColor.rgb();\n    var color2 = this.rgb();\n    var p = weight === undefined ? 0.5 : weight;\n    var w = 2 * p - 1;\n    var a = color1.alpha() - color2.alpha();\n    var w1 = ((w * a === -1 ? w : (w + a) / (1 + w * a)) + 1) / 2.0;\n    var w2 = 1 - w1;\n    return Color.rgb(w1 * color1.red() + w2 * color2.red(), w1 * color1.green() + w2 * color2.green(), w1 * color1.blue() + w2 * color2.blue(), color1.alpha() * p + color2.alpha() * (1 - p));\n  }\n};\nObject.keys(convert).forEach(function (model) {\n  if (skippedModels.indexOf(model) !== -1) {\n    return;\n  }\n  var channels = convert[model].channels;\n  Color.prototype[model] = function () {\n    if (this.model === model) {\n      return new Color(this);\n    }\n    if (arguments.length) {\n      return new Color(arguments, model);\n    }\n    var newAlpha = typeof arguments[channels] === 'number' ? channels : this.valpha;\n    return new Color(assertArray(convert[this.model][model].raw(this.color)).concat(newAlpha), model);\n  };\n  Color[model] = function (color) {\n    if (typeof color === 'number') {\n      color = zeroArray(_slice.call(arguments), channels);\n    }\n    return new Color(color, model);\n  };\n});\nfunction roundTo(num, places) {\n  return Number(num.toFixed(places));\n}\nfunction roundToPlace(places) {\n  return function (num) {\n    return roundTo(num, places);\n  };\n}\nfunction getset(model, channel, modifier) {\n  model = Array.isArray(model) ? model : [model];\n  model.forEach(function (m) {\n    (limiters[m] || (limiters[m] = []))[channel] = modifier;\n  });\n  model = model[0];\n  return function (val) {\n    var result;\n    if (arguments.length) {\n      if (modifier) {\n        val = modifier(val);\n      }\n      result = this[model]();\n      result.color[channel] = val;\n      return result;\n    }\n    result = this[model]().color[channel];\n    if (modifier) {\n      result = modifier(result);\n    }\n    return result;\n  };\n}\nfunction maxfn(max) {\n  return function (v) {\n    return Math.max(0, Math.min(max, v));\n  };\n}\nfunction assertArray(val) {\n  return Array.isArray(val) ? val : [val];\n}\nfunction zeroArray(arr, length) {\n  for (var i = 0; i < length; i++) {\n    if (typeof arr[i] !== 'number') {\n      arr[i] = 0;\n    }\n  }\n  return arr;\n}\nmodule.exports = Color;", "map": {"version": 3, "names": ["colorString", "require", "convert", "_slice", "slice", "skippedModels", "hashedModelKeys", "Object", "keys", "for<PERSON>ach", "model", "call", "labels", "sort", "join", "limiters", "Color", "obj", "Error", "i", "channels", "color", "valpha", "result", "get", "value", "length", "newArr", "zeroArray", "splice", "indexOf", "alpha", "hashedKeys", "JSON", "stringify", "push", "limit", "Math", "max", "min", "freeze", "prototype", "toString", "string", "toJSON", "places", "self", "to", "rgb", "round", "args", "concat", "percentString", "percent", "array", "object", "unitArray", "unitObject", "r", "g", "b", "map", "roundToPlace", "val", "arguments", "red", "getset", "maxfn", "green", "blue", "hue", "saturationl", "lightness", "saturationv", "chroma", "gray", "white", "wblack", "cyan", "magenta", "yellow", "black", "x", "y", "z", "l", "a", "keyword", "hex", "rgbNumber", "luminosity", "lum", "chan", "pow", "contrast", "color2", "lum1", "lum2", "level", "contrastRatio", "isDark", "yiq", "isLight", "negate", "lighten", "ratio", "hsl", "darken", "saturate", "desaturate", "whiten", "hwb", "blacken", "grayscale", "fade", "opaquer", "rotate", "degrees", "mix", "mixinColor", "weight", "color1", "p", "undefined", "w", "w1", "w2", "newAlpha", "assertArray", "raw", "roundTo", "num", "Number", "toFixed", "channel", "modifier", "Array", "isArray", "m", "v", "arr", "module", "exports"], "sources": ["E:/CryptoSignalsApp/node_modules/react-native-paper/node_modules/color/index.js"], "sourcesContent": ["'use strict';\n\nvar colorString = require('color-string');\nvar convert = require('color-convert');\n\nvar _slice = [].slice;\n\nvar skippedModels = [\n\t// to be honest, I don't really feel like keyword belongs in color convert, but eh.\n\t'keyword',\n\n\t// gray conflicts with some method names, and has its own method defined.\n\t'gray',\n\n\t// shouldn't really be in color-convert either...\n\t'hex'\n];\n\nvar hashedModelKeys = {};\nObject.keys(convert).forEach(function (model) {\n\thashedModelKeys[_slice.call(convert[model].labels).sort().join('')] = model;\n});\n\nvar limiters = {};\n\nfunction Color(obj, model) {\n\tif (!(this instanceof Color)) {\n\t\treturn new Color(obj, model);\n\t}\n\n\tif (model && model in skippedModels) {\n\t\tmodel = null;\n\t}\n\n\tif (model && !(model in convert)) {\n\t\tthrow new Error('Unknown model: ' + model);\n\t}\n\n\tvar i;\n\tvar channels;\n\n\tif (obj == null) { // eslint-disable-line no-eq-null,eqeqeq\n\t\tthis.model = 'rgb';\n\t\tthis.color = [0, 0, 0];\n\t\tthis.valpha = 1;\n\t} else if (obj instanceof Color) {\n\t\tthis.model = obj.model;\n\t\tthis.color = obj.color.slice();\n\t\tthis.valpha = obj.valpha;\n\t} else if (typeof obj === 'string') {\n\t\tvar result = colorString.get(obj);\n\t\tif (result === null) {\n\t\t\tthrow new Error('Unable to parse color from string: ' + obj);\n\t\t}\n\n\t\tthis.model = result.model;\n\t\tchannels = convert[this.model].channels;\n\t\tthis.color = result.value.slice(0, channels);\n\t\tthis.valpha = typeof result.value[channels] === 'number' ? result.value[channels] : 1;\n\t} else if (obj.length) {\n\t\tthis.model = model || 'rgb';\n\t\tchannels = convert[this.model].channels;\n\t\tvar newArr = _slice.call(obj, 0, channels);\n\t\tthis.color = zeroArray(newArr, channels);\n\t\tthis.valpha = typeof obj[channels] === 'number' ? obj[channels] : 1;\n\t} else if (typeof obj === 'number') {\n\t\t// this is always RGB - can be converted later on.\n\t\tobj &= 0xFFFFFF;\n\t\tthis.model = 'rgb';\n\t\tthis.color = [\n\t\t\t(obj >> 16) & 0xFF,\n\t\t\t(obj >> 8) & 0xFF,\n\t\t\tobj & 0xFF\n\t\t];\n\t\tthis.valpha = 1;\n\t} else {\n\t\tthis.valpha = 1;\n\n\t\tvar keys = Object.keys(obj);\n\t\tif ('alpha' in obj) {\n\t\t\tkeys.splice(keys.indexOf('alpha'), 1);\n\t\t\tthis.valpha = typeof obj.alpha === 'number' ? obj.alpha : 0;\n\t\t}\n\n\t\tvar hashedKeys = keys.sort().join('');\n\t\tif (!(hashedKeys in hashedModelKeys)) {\n\t\t\tthrow new Error('Unable to parse color from object: ' + JSON.stringify(obj));\n\t\t}\n\n\t\tthis.model = hashedModelKeys[hashedKeys];\n\n\t\tvar labels = convert[this.model].labels;\n\t\tvar color = [];\n\t\tfor (i = 0; i < labels.length; i++) {\n\t\t\tcolor.push(obj[labels[i]]);\n\t\t}\n\n\t\tthis.color = zeroArray(color);\n\t}\n\n\t// perform limitations (clamping, etc.)\n\tif (limiters[this.model]) {\n\t\tchannels = convert[this.model].channels;\n\t\tfor (i = 0; i < channels; i++) {\n\t\t\tvar limit = limiters[this.model][i];\n\t\t\tif (limit) {\n\t\t\t\tthis.color[i] = limit(this.color[i]);\n\t\t\t}\n\t\t}\n\t}\n\n\tthis.valpha = Math.max(0, Math.min(1, this.valpha));\n\n\tif (Object.freeze) {\n\t\tObject.freeze(this);\n\t}\n}\n\nColor.prototype = {\n\ttoString: function () {\n\t\treturn this.string();\n\t},\n\n\ttoJSON: function () {\n\t\treturn this[this.model]();\n\t},\n\n\tstring: function (places) {\n\t\tvar self = this.model in colorString.to ? this : this.rgb();\n\t\tself = self.round(typeof places === 'number' ? places : 1);\n\t\tvar args = self.valpha === 1 ? self.color : self.color.concat(this.valpha);\n\t\treturn colorString.to[self.model](args);\n\t},\n\n\tpercentString: function (places) {\n\t\tvar self = this.rgb().round(typeof places === 'number' ? places : 1);\n\t\tvar args = self.valpha === 1 ? self.color : self.color.concat(this.valpha);\n\t\treturn colorString.to.rgb.percent(args);\n\t},\n\n\tarray: function () {\n\t\treturn this.valpha === 1 ? this.color.slice() : this.color.concat(this.valpha);\n\t},\n\n\tobject: function () {\n\t\tvar result = {};\n\t\tvar channels = convert[this.model].channels;\n\t\tvar labels = convert[this.model].labels;\n\n\t\tfor (var i = 0; i < channels; i++) {\n\t\t\tresult[labels[i]] = this.color[i];\n\t\t}\n\n\t\tif (this.valpha !== 1) {\n\t\t\tresult.alpha = this.valpha;\n\t\t}\n\n\t\treturn result;\n\t},\n\n\tunitArray: function () {\n\t\tvar rgb = this.rgb().color;\n\t\trgb[0] /= 255;\n\t\trgb[1] /= 255;\n\t\trgb[2] /= 255;\n\n\t\tif (this.valpha !== 1) {\n\t\t\trgb.push(this.valpha);\n\t\t}\n\n\t\treturn rgb;\n\t},\n\n\tunitObject: function () {\n\t\tvar rgb = this.rgb().object();\n\t\trgb.r /= 255;\n\t\trgb.g /= 255;\n\t\trgb.b /= 255;\n\n\t\tif (this.valpha !== 1) {\n\t\t\trgb.alpha = this.valpha;\n\t\t}\n\n\t\treturn rgb;\n\t},\n\n\tround: function (places) {\n\t\tplaces = Math.max(places || 0, 0);\n\t\treturn new Color(this.color.map(roundToPlace(places)).concat(this.valpha), this.model);\n\t},\n\n\talpha: function (val) {\n\t\tif (arguments.length) {\n\t\t\treturn new Color(this.color.concat(Math.max(0, Math.min(1, val))), this.model);\n\t\t}\n\n\t\treturn this.valpha;\n\t},\n\n\t// rgb\n\tred: getset('rgb', 0, maxfn(255)),\n\tgreen: getset('rgb', 1, maxfn(255)),\n\tblue: getset('rgb', 2, maxfn(255)),\n\n\thue: getset(['hsl', 'hsv', 'hsl', 'hwb', 'hcg'], 0, function (val) { return ((val % 360) + 360) % 360; }), // eslint-disable-line brace-style\n\n\tsaturationl: getset('hsl', 1, maxfn(100)),\n\tlightness: getset('hsl', 2, maxfn(100)),\n\n\tsaturationv: getset('hsv', 1, maxfn(100)),\n\tvalue: getset('hsv', 2, maxfn(100)),\n\n\tchroma: getset('hcg', 1, maxfn(100)),\n\tgray: getset('hcg', 2, maxfn(100)),\n\n\twhite: getset('hwb', 1, maxfn(100)),\n\twblack: getset('hwb', 2, maxfn(100)),\n\n\tcyan: getset('cmyk', 0, maxfn(100)),\n\tmagenta: getset('cmyk', 1, maxfn(100)),\n\tyellow: getset('cmyk', 2, maxfn(100)),\n\tblack: getset('cmyk', 3, maxfn(100)),\n\n\tx: getset('xyz', 0, maxfn(100)),\n\ty: getset('xyz', 1, maxfn(100)),\n\tz: getset('xyz', 2, maxfn(100)),\n\n\tl: getset('lab', 0, maxfn(100)),\n\ta: getset('lab', 1),\n\tb: getset('lab', 2),\n\n\tkeyword: function (val) {\n\t\tif (arguments.length) {\n\t\t\treturn new Color(val);\n\t\t}\n\n\t\treturn convert[this.model].keyword(this.color);\n\t},\n\n\thex: function (val) {\n\t\tif (arguments.length) {\n\t\t\treturn new Color(val);\n\t\t}\n\n\t\treturn colorString.to.hex(this.rgb().round().color);\n\t},\n\n\trgbNumber: function () {\n\t\tvar rgb = this.rgb().color;\n\t\treturn ((rgb[0] & 0xFF) << 16) | ((rgb[1] & 0xFF) << 8) | (rgb[2] & 0xFF);\n\t},\n\n\tluminosity: function () {\n\t\t// http://www.w3.org/TR/WCAG20/#relativeluminancedef\n\t\tvar rgb = this.rgb().color;\n\n\t\tvar lum = [];\n\t\tfor (var i = 0; i < rgb.length; i++) {\n\t\t\tvar chan = rgb[i] / 255;\n\t\t\tlum[i] = (chan <= 0.03928) ? chan / 12.92 : Math.pow(((chan + 0.055) / 1.055), 2.4);\n\t\t}\n\n\t\treturn 0.2126 * lum[0] + 0.7152 * lum[1] + 0.0722 * lum[2];\n\t},\n\n\tcontrast: function (color2) {\n\t\t// http://www.w3.org/TR/WCAG20/#contrast-ratiodef\n\t\tvar lum1 = this.luminosity();\n\t\tvar lum2 = color2.luminosity();\n\n\t\tif (lum1 > lum2) {\n\t\t\treturn (lum1 + 0.05) / (lum2 + 0.05);\n\t\t}\n\n\t\treturn (lum2 + 0.05) / (lum1 + 0.05);\n\t},\n\n\tlevel: function (color2) {\n\t\tvar contrastRatio = this.contrast(color2);\n\t\tif (contrastRatio >= 7.1) {\n\t\t\treturn 'AAA';\n\t\t}\n\n\t\treturn (contrastRatio >= 4.5) ? 'AA' : '';\n\t},\n\n\tisDark: function () {\n\t\t// YIQ equation from http://24ways.org/2010/calculating-color-contrast\n\t\tvar rgb = this.rgb().color;\n\t\tvar yiq = (rgb[0] * 299 + rgb[1] * 587 + rgb[2] * 114) / 1000;\n\t\treturn yiq < 128;\n\t},\n\n\tisLight: function () {\n\t\treturn !this.isDark();\n\t},\n\n\tnegate: function () {\n\t\tvar rgb = this.rgb();\n\t\tfor (var i = 0; i < 3; i++) {\n\t\t\trgb.color[i] = 255 - rgb.color[i];\n\t\t}\n\t\treturn rgb;\n\t},\n\n\tlighten: function (ratio) {\n\t\tvar hsl = this.hsl();\n\t\thsl.color[2] += hsl.color[2] * ratio;\n\t\treturn hsl;\n\t},\n\n\tdarken: function (ratio) {\n\t\tvar hsl = this.hsl();\n\t\thsl.color[2] -= hsl.color[2] * ratio;\n\t\treturn hsl;\n\t},\n\n\tsaturate: function (ratio) {\n\t\tvar hsl = this.hsl();\n\t\thsl.color[1] += hsl.color[1] * ratio;\n\t\treturn hsl;\n\t},\n\n\tdesaturate: function (ratio) {\n\t\tvar hsl = this.hsl();\n\t\thsl.color[1] -= hsl.color[1] * ratio;\n\t\treturn hsl;\n\t},\n\n\twhiten: function (ratio) {\n\t\tvar hwb = this.hwb();\n\t\thwb.color[1] += hwb.color[1] * ratio;\n\t\treturn hwb;\n\t},\n\n\tblacken: function (ratio) {\n\t\tvar hwb = this.hwb();\n\t\thwb.color[2] += hwb.color[2] * ratio;\n\t\treturn hwb;\n\t},\n\n\tgrayscale: function () {\n\t\t// http://en.wikipedia.org/wiki/Grayscale#Converting_color_to_grayscale\n\t\tvar rgb = this.rgb().color;\n\t\tvar val = rgb[0] * 0.3 + rgb[1] * 0.59 + rgb[2] * 0.11;\n\t\treturn Color.rgb(val, val, val);\n\t},\n\n\tfade: function (ratio) {\n\t\treturn this.alpha(this.valpha - (this.valpha * ratio));\n\t},\n\n\topaquer: function (ratio) {\n\t\treturn this.alpha(this.valpha + (this.valpha * ratio));\n\t},\n\n\trotate: function (degrees) {\n\t\tvar hsl = this.hsl();\n\t\tvar hue = hsl.color[0];\n\t\thue = (hue + degrees) % 360;\n\t\thue = hue < 0 ? 360 + hue : hue;\n\t\thsl.color[0] = hue;\n\t\treturn hsl;\n\t},\n\n\tmix: function (mixinColor, weight) {\n\t\t// ported from sass implementation in C\n\t\t// https://github.com/sass/libsass/blob/0e6b4a2850092356aa3ece07c6b249f0221caced/functions.cpp#L209\n\t\tif (!mixinColor || !mixinColor.rgb) {\n\t\t\tthrow new Error('Argument to \"mix\" was not a Color instance, but rather an instance of ' + typeof mixinColor);\n\t\t}\n\t\tvar color1 = mixinColor.rgb();\n\t\tvar color2 = this.rgb();\n\t\tvar p = weight === undefined ? 0.5 : weight;\n\n\t\tvar w = 2 * p - 1;\n\t\tvar a = color1.alpha() - color2.alpha();\n\n\t\tvar w1 = (((w * a === -1) ? w : (w + a) / (1 + w * a)) + 1) / 2.0;\n\t\tvar w2 = 1 - w1;\n\n\t\treturn Color.rgb(\n\t\t\t\tw1 * color1.red() + w2 * color2.red(),\n\t\t\t\tw1 * color1.green() + w2 * color2.green(),\n\t\t\t\tw1 * color1.blue() + w2 * color2.blue(),\n\t\t\t\tcolor1.alpha() * p + color2.alpha() * (1 - p));\n\t}\n};\n\n// model conversion methods and static constructors\nObject.keys(convert).forEach(function (model) {\n\tif (skippedModels.indexOf(model) !== -1) {\n\t\treturn;\n\t}\n\n\tvar channels = convert[model].channels;\n\n\t// conversion methods\n\tColor.prototype[model] = function () {\n\t\tif (this.model === model) {\n\t\t\treturn new Color(this);\n\t\t}\n\n\t\tif (arguments.length) {\n\t\t\treturn new Color(arguments, model);\n\t\t}\n\n\t\tvar newAlpha = typeof arguments[channels] === 'number' ? channels : this.valpha;\n\t\treturn new Color(assertArray(convert[this.model][model].raw(this.color)).concat(newAlpha), model);\n\t};\n\n\t// 'static' construction methods\n\tColor[model] = function (color) {\n\t\tif (typeof color === 'number') {\n\t\t\tcolor = zeroArray(_slice.call(arguments), channels);\n\t\t}\n\t\treturn new Color(color, model);\n\t};\n});\n\nfunction roundTo(num, places) {\n\treturn Number(num.toFixed(places));\n}\n\nfunction roundToPlace(places) {\n\treturn function (num) {\n\t\treturn roundTo(num, places);\n\t};\n}\n\nfunction getset(model, channel, modifier) {\n\tmodel = Array.isArray(model) ? model : [model];\n\n\tmodel.forEach(function (m) {\n\t\t(limiters[m] || (limiters[m] = []))[channel] = modifier;\n\t});\n\n\tmodel = model[0];\n\n\treturn function (val) {\n\t\tvar result;\n\n\t\tif (arguments.length) {\n\t\t\tif (modifier) {\n\t\t\t\tval = modifier(val);\n\t\t\t}\n\n\t\t\tresult = this[model]();\n\t\t\tresult.color[channel] = val;\n\t\t\treturn result;\n\t\t}\n\n\t\tresult = this[model]().color[channel];\n\t\tif (modifier) {\n\t\t\tresult = modifier(result);\n\t\t}\n\n\t\treturn result;\n\t};\n}\n\nfunction maxfn(max) {\n\treturn function (v) {\n\t\treturn Math.max(0, Math.min(max, v));\n\t};\n}\n\nfunction assertArray(val) {\n\treturn Array.isArray(val) ? val : [val];\n}\n\nfunction zeroArray(arr, length) {\n\tfor (var i = 0; i < length; i++) {\n\t\tif (typeof arr[i] !== 'number') {\n\t\t\tarr[i] = 0;\n\t\t}\n\t}\n\n\treturn arr;\n}\n\nmodule.exports = Color;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,WAAW,GAAGC,OAAO,CAAC,cAAc,CAAC;AACzC,IAAIC,OAAO,GAAGD,OAAO,CAAC,eAAe,CAAC;AAEtC,IAAIE,MAAM,GAAG,EAAE,CAACC,KAAK;AAErB,IAAIC,aAAa,GAAG,CAEnB,SAAS,EAGT,MAAM,EAGN,KAAK,CACL;AAED,IAAIC,eAAe,GAAG,CAAC,CAAC;AACxBC,MAAM,CAACC,IAAI,CAACN,OAAO,CAAC,CAACO,OAAO,CAAC,UAAUC,KAAK,EAAE;EAC7CJ,eAAe,CAACH,MAAM,CAACQ,IAAI,CAACT,OAAO,CAACQ,KAAK,CAAC,CAACE,MAAM,CAAC,CAACC,IAAI,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAGJ,KAAK;AAC5E,CAAC,CAAC;AAEF,IAAIK,QAAQ,GAAG,CAAC,CAAC;AAEjB,SAASC,KAAKA,CAACC,GAAG,EAAEP,KAAK,EAAE;EAC1B,IAAI,EAAE,IAAI,YAAYM,KAAK,CAAC,EAAE;IAC7B,OAAO,IAAIA,KAAK,CAACC,GAAG,EAAEP,KAAK,CAAC;EAC7B;EAEA,IAAIA,KAAK,IAAIA,KAAK,IAAIL,aAAa,EAAE;IACpCK,KAAK,GAAG,IAAI;EACb;EAEA,IAAIA,KAAK,IAAI,EAAEA,KAAK,IAAIR,OAAO,CAAC,EAAE;IACjC,MAAM,IAAIgB,KAAK,CAAC,iBAAiB,GAAGR,KAAK,CAAC;EAC3C;EAEA,IAAIS,CAAC;EACL,IAAIC,QAAQ;EAEZ,IAAIH,GAAG,IAAI,IAAI,EAAE;IAChB,IAAI,CAACP,KAAK,GAAG,KAAK;IAClB,IAAI,CAACW,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtB,IAAI,CAACC,MAAM,GAAG,CAAC;EAChB,CAAC,MAAM,IAAIL,GAAG,YAAYD,KAAK,EAAE;IAChC,IAAI,CAACN,KAAK,GAAGO,GAAG,CAACP,KAAK;IACtB,IAAI,CAACW,KAAK,GAAGJ,GAAG,CAACI,KAAK,CAACjB,KAAK,CAAC,CAAC;IAC9B,IAAI,CAACkB,MAAM,GAAGL,GAAG,CAACK,MAAM;EACzB,CAAC,MAAM,IAAI,OAAOL,GAAG,KAAK,QAAQ,EAAE;IACnC,IAAIM,MAAM,GAAGvB,WAAW,CAACwB,GAAG,CAACP,GAAG,CAAC;IACjC,IAAIM,MAAM,KAAK,IAAI,EAAE;MACpB,MAAM,IAAIL,KAAK,CAAC,qCAAqC,GAAGD,GAAG,CAAC;IAC7D;IAEA,IAAI,CAACP,KAAK,GAAGa,MAAM,CAACb,KAAK;IACzBU,QAAQ,GAAGlB,OAAO,CAAC,IAAI,CAACQ,KAAK,CAAC,CAACU,QAAQ;IACvC,IAAI,CAACC,KAAK,GAAGE,MAAM,CAACE,KAAK,CAACrB,KAAK,CAAC,CAAC,EAAEgB,QAAQ,CAAC;IAC5C,IAAI,CAACE,MAAM,GAAG,OAAOC,MAAM,CAACE,KAAK,CAACL,QAAQ,CAAC,KAAK,QAAQ,GAAGG,MAAM,CAACE,KAAK,CAACL,QAAQ,CAAC,GAAG,CAAC;EACtF,CAAC,MAAM,IAAIH,GAAG,CAACS,MAAM,EAAE;IACtB,IAAI,CAAChB,KAAK,GAAGA,KAAK,IAAI,KAAK;IAC3BU,QAAQ,GAAGlB,OAAO,CAAC,IAAI,CAACQ,KAAK,CAAC,CAACU,QAAQ;IACvC,IAAIO,MAAM,GAAGxB,MAAM,CAACQ,IAAI,CAACM,GAAG,EAAE,CAAC,EAAEG,QAAQ,CAAC;IAC1C,IAAI,CAACC,KAAK,GAAGO,SAAS,CAACD,MAAM,EAAEP,QAAQ,CAAC;IACxC,IAAI,CAACE,MAAM,GAAG,OAAOL,GAAG,CAACG,QAAQ,CAAC,KAAK,QAAQ,GAAGH,GAAG,CAACG,QAAQ,CAAC,GAAG,CAAC;EACpE,CAAC,MAAM,IAAI,OAAOH,GAAG,KAAK,QAAQ,EAAE;IAEnCA,GAAG,IAAI,QAAQ;IACf,IAAI,CAACP,KAAK,GAAG,KAAK;IAClB,IAAI,CAACW,KAAK,GAAG,CACXJ,GAAG,IAAI,EAAE,GAAI,IAAI,EACjBA,GAAG,IAAI,CAAC,GAAI,IAAI,EACjBA,GAAG,GAAG,IAAI,CACV;IACD,IAAI,CAACK,MAAM,GAAG,CAAC;EAChB,CAAC,MAAM;IACN,IAAI,CAACA,MAAM,GAAG,CAAC;IAEf,IAAId,IAAI,GAAGD,MAAM,CAACC,IAAI,CAACS,GAAG,CAAC;IAC3B,IAAI,OAAO,IAAIA,GAAG,EAAE;MACnBT,IAAI,CAACqB,MAAM,CAACrB,IAAI,CAACsB,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;MACrC,IAAI,CAACR,MAAM,GAAG,OAAOL,GAAG,CAACc,KAAK,KAAK,QAAQ,GAAGd,GAAG,CAACc,KAAK,GAAG,CAAC;IAC5D;IAEA,IAAIC,UAAU,GAAGxB,IAAI,CAACK,IAAI,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;IACrC,IAAI,EAAEkB,UAAU,IAAI1B,eAAe,CAAC,EAAE;MACrC,MAAM,IAAIY,KAAK,CAAC,qCAAqC,GAAGe,IAAI,CAACC,SAAS,CAACjB,GAAG,CAAC,CAAC;IAC7E;IAEA,IAAI,CAACP,KAAK,GAAGJ,eAAe,CAAC0B,UAAU,CAAC;IAExC,IAAIpB,MAAM,GAAGV,OAAO,CAAC,IAAI,CAACQ,KAAK,CAAC,CAACE,MAAM;IACvC,IAAIS,KAAK,GAAG,EAAE;IACd,KAAKF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,MAAM,CAACc,MAAM,EAAEP,CAAC,EAAE,EAAE;MACnCE,KAAK,CAACc,IAAI,CAAClB,GAAG,CAACL,MAAM,CAACO,CAAC,CAAC,CAAC,CAAC;IAC3B;IAEA,IAAI,CAACE,KAAK,GAAGO,SAAS,CAACP,KAAK,CAAC;EAC9B;EAGA,IAAIN,QAAQ,CAAC,IAAI,CAACL,KAAK,CAAC,EAAE;IACzBU,QAAQ,GAAGlB,OAAO,CAAC,IAAI,CAACQ,KAAK,CAAC,CAACU,QAAQ;IACvC,KAAKD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,QAAQ,EAAED,CAAC,EAAE,EAAE;MAC9B,IAAIiB,KAAK,GAAGrB,QAAQ,CAAC,IAAI,CAACL,KAAK,CAAC,CAACS,CAAC,CAAC;MACnC,IAAIiB,KAAK,EAAE;QACV,IAAI,CAACf,KAAK,CAACF,CAAC,CAAC,GAAGiB,KAAK,CAAC,IAAI,CAACf,KAAK,CAACF,CAAC,CAAC,CAAC;MACrC;IACD;EACD;EAEA,IAAI,CAACG,MAAM,GAAGe,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAE,IAAI,CAACjB,MAAM,CAAC,CAAC;EAEnD,IAAIf,MAAM,CAACiC,MAAM,EAAE;IAClBjC,MAAM,CAACiC,MAAM,CAAC,IAAI,CAAC;EACpB;AACD;AAEAxB,KAAK,CAACyB,SAAS,GAAG;EACjBC,QAAQ,EAAE,SAAVA,QAAQA,CAAA,EAAc;IACrB,OAAO,IAAI,CAACC,MAAM,CAAC,CAAC;EACrB,CAAC;EAEDC,MAAM,EAAE,SAARA,MAAMA,CAAA,EAAc;IACnB,OAAO,IAAI,CAAC,IAAI,CAAClC,KAAK,CAAC,CAAC,CAAC;EAC1B,CAAC;EAEDiC,MAAM,EAAE,SAARA,MAAMA,CAAYE,MAAM,EAAE;IACzB,IAAIC,IAAI,GAAG,IAAI,CAACpC,KAAK,IAAIV,WAAW,CAAC+C,EAAE,GAAG,IAAI,GAAG,IAAI,CAACC,GAAG,CAAC,CAAC;IAC3DF,IAAI,GAAGA,IAAI,CAACG,KAAK,CAAC,OAAOJ,MAAM,KAAK,QAAQ,GAAGA,MAAM,GAAG,CAAC,CAAC;IAC1D,IAAIK,IAAI,GAAGJ,IAAI,CAACxB,MAAM,KAAK,CAAC,GAAGwB,IAAI,CAACzB,KAAK,GAAGyB,IAAI,CAACzB,KAAK,CAAC8B,MAAM,CAAC,IAAI,CAAC7B,MAAM,CAAC;IAC1E,OAAOtB,WAAW,CAAC+C,EAAE,CAACD,IAAI,CAACpC,KAAK,CAAC,CAACwC,IAAI,CAAC;EACxC,CAAC;EAEDE,aAAa,EAAE,SAAfA,aAAaA,CAAYP,MAAM,EAAE;IAChC,IAAIC,IAAI,GAAG,IAAI,CAACE,GAAG,CAAC,CAAC,CAACC,KAAK,CAAC,OAAOJ,MAAM,KAAK,QAAQ,GAAGA,MAAM,GAAG,CAAC,CAAC;IACpE,IAAIK,IAAI,GAAGJ,IAAI,CAACxB,MAAM,KAAK,CAAC,GAAGwB,IAAI,CAACzB,KAAK,GAAGyB,IAAI,CAACzB,KAAK,CAAC8B,MAAM,CAAC,IAAI,CAAC7B,MAAM,CAAC;IAC1E,OAAOtB,WAAW,CAAC+C,EAAE,CAACC,GAAG,CAACK,OAAO,CAACH,IAAI,CAAC;EACxC,CAAC;EAEDI,KAAK,EAAE,SAAPA,KAAKA,CAAA,EAAc;IAClB,OAAO,IAAI,CAAChC,MAAM,KAAK,CAAC,GAAG,IAAI,CAACD,KAAK,CAACjB,KAAK,CAAC,CAAC,GAAG,IAAI,CAACiB,KAAK,CAAC8B,MAAM,CAAC,IAAI,CAAC7B,MAAM,CAAC;EAC/E,CAAC;EAEDiC,MAAM,EAAE,SAARA,MAAMA,CAAA,EAAc;IACnB,IAAIhC,MAAM,GAAG,CAAC,CAAC;IACf,IAAIH,QAAQ,GAAGlB,OAAO,CAAC,IAAI,CAACQ,KAAK,CAAC,CAACU,QAAQ;IAC3C,IAAIR,MAAM,GAAGV,OAAO,CAAC,IAAI,CAACQ,KAAK,CAAC,CAACE,MAAM;IAEvC,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,QAAQ,EAAED,CAAC,EAAE,EAAE;MAClCI,MAAM,CAACX,MAAM,CAACO,CAAC,CAAC,CAAC,GAAG,IAAI,CAACE,KAAK,CAACF,CAAC,CAAC;IAClC;IAEA,IAAI,IAAI,CAACG,MAAM,KAAK,CAAC,EAAE;MACtBC,MAAM,CAACQ,KAAK,GAAG,IAAI,CAACT,MAAM;IAC3B;IAEA,OAAOC,MAAM;EACd,CAAC;EAEDiC,SAAS,EAAE,SAAXA,SAASA,CAAA,EAAc;IACtB,IAAIR,GAAG,GAAG,IAAI,CAACA,GAAG,CAAC,CAAC,CAAC3B,KAAK;IAC1B2B,GAAG,CAAC,CAAC,CAAC,IAAI,GAAG;IACbA,GAAG,CAAC,CAAC,CAAC,IAAI,GAAG;IACbA,GAAG,CAAC,CAAC,CAAC,IAAI,GAAG;IAEb,IAAI,IAAI,CAAC1B,MAAM,KAAK,CAAC,EAAE;MACtB0B,GAAG,CAACb,IAAI,CAAC,IAAI,CAACb,MAAM,CAAC;IACtB;IAEA,OAAO0B,GAAG;EACX,CAAC;EAEDS,UAAU,EAAE,SAAZA,UAAUA,CAAA,EAAc;IACvB,IAAIT,GAAG,GAAG,IAAI,CAACA,GAAG,CAAC,CAAC,CAACO,MAAM,CAAC,CAAC;IAC7BP,GAAG,CAACU,CAAC,IAAI,GAAG;IACZV,GAAG,CAACW,CAAC,IAAI,GAAG;IACZX,GAAG,CAACY,CAAC,IAAI,GAAG;IAEZ,IAAI,IAAI,CAACtC,MAAM,KAAK,CAAC,EAAE;MACtB0B,GAAG,CAACjB,KAAK,GAAG,IAAI,CAACT,MAAM;IACxB;IAEA,OAAO0B,GAAG;EACX,CAAC;EAEDC,KAAK,EAAE,SAAPA,KAAKA,CAAYJ,MAAM,EAAE;IACxBA,MAAM,GAAGR,IAAI,CAACC,GAAG,CAACO,MAAM,IAAI,CAAC,EAAE,CAAC,CAAC;IACjC,OAAO,IAAI7B,KAAK,CAAC,IAAI,CAACK,KAAK,CAACwC,GAAG,CAACC,YAAY,CAACjB,MAAM,CAAC,CAAC,CAACM,MAAM,CAAC,IAAI,CAAC7B,MAAM,CAAC,EAAE,IAAI,CAACZ,KAAK,CAAC;EACvF,CAAC;EAEDqB,KAAK,EAAE,SAAPA,KAAKA,CAAYgC,GAAG,EAAE;IACrB,IAAIC,SAAS,CAACtC,MAAM,EAAE;MACrB,OAAO,IAAIV,KAAK,CAAC,IAAI,CAACK,KAAK,CAAC8B,MAAM,CAACd,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEwB,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAACrD,KAAK,CAAC;IAC/E;IAEA,OAAO,IAAI,CAACY,MAAM;EACnB,CAAC;EAGD2C,GAAG,EAAEC,MAAM,CAAC,KAAK,EAAE,CAAC,EAAEC,KAAK,CAAC,GAAG,CAAC,CAAC;EACjCC,KAAK,EAAEF,MAAM,CAAC,KAAK,EAAE,CAAC,EAAEC,KAAK,CAAC,GAAG,CAAC,CAAC;EACnCE,IAAI,EAAEH,MAAM,CAAC,KAAK,EAAE,CAAC,EAAEC,KAAK,CAAC,GAAG,CAAC,CAAC;EAElCG,GAAG,EAAEJ,MAAM,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,EAAE,UAAUH,GAAG,EAAE;IAAE,OAAO,CAAEA,GAAG,GAAG,GAAG,GAAI,GAAG,IAAI,GAAG;EAAE,CAAC,CAAC;EAEzGQ,WAAW,EAAEL,MAAM,CAAC,KAAK,EAAE,CAAC,EAAEC,KAAK,CAAC,GAAG,CAAC,CAAC;EACzCK,SAAS,EAAEN,MAAM,CAAC,KAAK,EAAE,CAAC,EAAEC,KAAK,CAAC,GAAG,CAAC,CAAC;EAEvCM,WAAW,EAAEP,MAAM,CAAC,KAAK,EAAE,CAAC,EAAEC,KAAK,CAAC,GAAG,CAAC,CAAC;EACzC1C,KAAK,EAAEyC,MAAM,CAAC,KAAK,EAAE,CAAC,EAAEC,KAAK,CAAC,GAAG,CAAC,CAAC;EAEnCO,MAAM,EAAER,MAAM,CAAC,KAAK,EAAE,CAAC,EAAEC,KAAK,CAAC,GAAG,CAAC,CAAC;EACpCQ,IAAI,EAAET,MAAM,CAAC,KAAK,EAAE,CAAC,EAAEC,KAAK,CAAC,GAAG,CAAC,CAAC;EAElCS,KAAK,EAAEV,MAAM,CAAC,KAAK,EAAE,CAAC,EAAEC,KAAK,CAAC,GAAG,CAAC,CAAC;EACnCU,MAAM,EAAEX,MAAM,CAAC,KAAK,EAAE,CAAC,EAAEC,KAAK,CAAC,GAAG,CAAC,CAAC;EAEpCW,IAAI,EAAEZ,MAAM,CAAC,MAAM,EAAE,CAAC,EAAEC,KAAK,CAAC,GAAG,CAAC,CAAC;EACnCY,OAAO,EAAEb,MAAM,CAAC,MAAM,EAAE,CAAC,EAAEC,KAAK,CAAC,GAAG,CAAC,CAAC;EACtCa,MAAM,EAAEd,MAAM,CAAC,MAAM,EAAE,CAAC,EAAEC,KAAK,CAAC,GAAG,CAAC,CAAC;EACrCc,KAAK,EAAEf,MAAM,CAAC,MAAM,EAAE,CAAC,EAAEC,KAAK,CAAC,GAAG,CAAC,CAAC;EAEpCe,CAAC,EAAEhB,MAAM,CAAC,KAAK,EAAE,CAAC,EAAEC,KAAK,CAAC,GAAG,CAAC,CAAC;EAC/BgB,CAAC,EAAEjB,MAAM,CAAC,KAAK,EAAE,CAAC,EAAEC,KAAK,CAAC,GAAG,CAAC,CAAC;EAC/BiB,CAAC,EAAElB,MAAM,CAAC,KAAK,EAAE,CAAC,EAAEC,KAAK,CAAC,GAAG,CAAC,CAAC;EAE/BkB,CAAC,EAAEnB,MAAM,CAAC,KAAK,EAAE,CAAC,EAAEC,KAAK,CAAC,GAAG,CAAC,CAAC;EAC/BmB,CAAC,EAAEpB,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;EACnBN,CAAC,EAAEM,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;EAEnBqB,OAAO,EAAE,SAATA,OAAOA,CAAYxB,GAAG,EAAE;IACvB,IAAIC,SAAS,CAACtC,MAAM,EAAE;MACrB,OAAO,IAAIV,KAAK,CAAC+C,GAAG,CAAC;IACtB;IAEA,OAAO7D,OAAO,CAAC,IAAI,CAACQ,KAAK,CAAC,CAAC6E,OAAO,CAAC,IAAI,CAAClE,KAAK,CAAC;EAC/C,CAAC;EAEDmE,GAAG,EAAE,SAALA,GAAGA,CAAYzB,GAAG,EAAE;IACnB,IAAIC,SAAS,CAACtC,MAAM,EAAE;MACrB,OAAO,IAAIV,KAAK,CAAC+C,GAAG,CAAC;IACtB;IAEA,OAAO/D,WAAW,CAAC+C,EAAE,CAACyC,GAAG,CAAC,IAAI,CAACxC,GAAG,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC5B,KAAK,CAAC;EACpD,CAAC;EAEDoE,SAAS,EAAE,SAAXA,SAASA,CAAA,EAAc;IACtB,IAAIzC,GAAG,GAAG,IAAI,CAACA,GAAG,CAAC,CAAC,CAAC3B,KAAK;IAC1B,OAAQ,CAAC2B,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,EAAE,GAAK,CAACA,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,CAAE,GAAIA,GAAG,CAAC,CAAC,CAAC,GAAG,IAAK;EAC1E,CAAC;EAED0C,UAAU,EAAE,SAAZA,UAAUA,CAAA,EAAc;IAEvB,IAAI1C,GAAG,GAAG,IAAI,CAACA,GAAG,CAAC,CAAC,CAAC3B,KAAK;IAE1B,IAAIsE,GAAG,GAAG,EAAE;IACZ,KAAK,IAAIxE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6B,GAAG,CAACtB,MAAM,EAAEP,CAAC,EAAE,EAAE;MACpC,IAAIyE,IAAI,GAAG5C,GAAG,CAAC7B,CAAC,CAAC,GAAG,GAAG;MACvBwE,GAAG,CAACxE,CAAC,CAAC,GAAIyE,IAAI,IAAI,OAAO,GAAIA,IAAI,GAAG,KAAK,GAAGvD,IAAI,CAACwD,GAAG,CAAE,CAACD,IAAI,GAAG,KAAK,IAAI,KAAK,EAAG,GAAG,CAAC;IACpF;IAEA,OAAO,MAAM,GAAGD,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,GAAGA,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,GAAGA,GAAG,CAAC,CAAC,CAAC;EAC3D,CAAC;EAEDG,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,MAAM,EAAE;IAE3B,IAAIC,IAAI,GAAG,IAAI,CAACN,UAAU,CAAC,CAAC;IAC5B,IAAIO,IAAI,GAAGF,MAAM,CAACL,UAAU,CAAC,CAAC;IAE9B,IAAIM,IAAI,GAAGC,IAAI,EAAE;MAChB,OAAO,CAACD,IAAI,GAAG,IAAI,KAAKC,IAAI,GAAG,IAAI,CAAC;IACrC;IAEA,OAAO,CAACA,IAAI,GAAG,IAAI,KAAKD,IAAI,GAAG,IAAI,CAAC;EACrC,CAAC;EAEDE,KAAK,EAAE,SAAPA,KAAKA,CAAYH,MAAM,EAAE;IACxB,IAAII,aAAa,GAAG,IAAI,CAACL,QAAQ,CAACC,MAAM,CAAC;IACzC,IAAII,aAAa,IAAI,GAAG,EAAE;MACzB,OAAO,KAAK;IACb;IAEA,OAAQA,aAAa,IAAI,GAAG,GAAI,IAAI,GAAG,EAAE;EAC1C,CAAC;EAEDC,MAAM,EAAE,SAARA,MAAMA,CAAA,EAAc;IAEnB,IAAIpD,GAAG,GAAG,IAAI,CAACA,GAAG,CAAC,CAAC,CAAC3B,KAAK;IAC1B,IAAIgF,GAAG,GAAG,CAACrD,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGA,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGA,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,IAAI;IAC7D,OAAOqD,GAAG,GAAG,GAAG;EACjB,CAAC;EAEDC,OAAO,EAAE,SAATA,OAAOA,CAAA,EAAc;IACpB,OAAO,CAAC,IAAI,CAACF,MAAM,CAAC,CAAC;EACtB,CAAC;EAEDG,MAAM,EAAE,SAARA,MAAMA,CAAA,EAAc;IACnB,IAAIvD,GAAG,GAAG,IAAI,CAACA,GAAG,CAAC,CAAC;IACpB,KAAK,IAAI7B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC3B6B,GAAG,CAAC3B,KAAK,CAACF,CAAC,CAAC,GAAG,GAAG,GAAG6B,GAAG,CAAC3B,KAAK,CAACF,CAAC,CAAC;IAClC;IACA,OAAO6B,GAAG;EACX,CAAC;EAEDwD,OAAO,EAAE,SAATA,OAAOA,CAAYC,KAAK,EAAE;IACzB,IAAIC,GAAG,GAAG,IAAI,CAACA,GAAG,CAAC,CAAC;IACpBA,GAAG,CAACrF,KAAK,CAAC,CAAC,CAAC,IAAIqF,GAAG,CAACrF,KAAK,CAAC,CAAC,CAAC,GAAGoF,KAAK;IACpC,OAAOC,GAAG;EACX,CAAC;EAEDC,MAAM,EAAE,SAARA,MAAMA,CAAYF,KAAK,EAAE;IACxB,IAAIC,GAAG,GAAG,IAAI,CAACA,GAAG,CAAC,CAAC;IACpBA,GAAG,CAACrF,KAAK,CAAC,CAAC,CAAC,IAAIqF,GAAG,CAACrF,KAAK,CAAC,CAAC,CAAC,GAAGoF,KAAK;IACpC,OAAOC,GAAG;EACX,CAAC;EAEDE,QAAQ,EAAE,SAAVA,QAAQA,CAAYH,KAAK,EAAE;IAC1B,IAAIC,GAAG,GAAG,IAAI,CAACA,GAAG,CAAC,CAAC;IACpBA,GAAG,CAACrF,KAAK,CAAC,CAAC,CAAC,IAAIqF,GAAG,CAACrF,KAAK,CAAC,CAAC,CAAC,GAAGoF,KAAK;IACpC,OAAOC,GAAG;EACX,CAAC;EAEDG,UAAU,EAAE,SAAZA,UAAUA,CAAYJ,KAAK,EAAE;IAC5B,IAAIC,GAAG,GAAG,IAAI,CAACA,GAAG,CAAC,CAAC;IACpBA,GAAG,CAACrF,KAAK,CAAC,CAAC,CAAC,IAAIqF,GAAG,CAACrF,KAAK,CAAC,CAAC,CAAC,GAAGoF,KAAK;IACpC,OAAOC,GAAG;EACX,CAAC;EAEDI,MAAM,EAAE,SAARA,MAAMA,CAAYL,KAAK,EAAE;IACxB,IAAIM,GAAG,GAAG,IAAI,CAACA,GAAG,CAAC,CAAC;IACpBA,GAAG,CAAC1F,KAAK,CAAC,CAAC,CAAC,IAAI0F,GAAG,CAAC1F,KAAK,CAAC,CAAC,CAAC,GAAGoF,KAAK;IACpC,OAAOM,GAAG;EACX,CAAC;EAEDC,OAAO,EAAE,SAATA,OAAOA,CAAYP,KAAK,EAAE;IACzB,IAAIM,GAAG,GAAG,IAAI,CAACA,GAAG,CAAC,CAAC;IACpBA,GAAG,CAAC1F,KAAK,CAAC,CAAC,CAAC,IAAI0F,GAAG,CAAC1F,KAAK,CAAC,CAAC,CAAC,GAAGoF,KAAK;IACpC,OAAOM,GAAG;EACX,CAAC;EAEDE,SAAS,EAAE,SAAXA,SAASA,CAAA,EAAc;IAEtB,IAAIjE,GAAG,GAAG,IAAI,CAACA,GAAG,CAAC,CAAC,CAAC3B,KAAK;IAC1B,IAAI0C,GAAG,GAAGf,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGA,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,GAAGA,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI;IACtD,OAAOhC,KAAK,CAACgC,GAAG,CAACe,GAAG,EAAEA,GAAG,EAAEA,GAAG,CAAC;EAChC,CAAC;EAEDmD,IAAI,EAAE,SAANA,IAAIA,CAAYT,KAAK,EAAE;IACtB,OAAO,IAAI,CAAC1E,KAAK,CAAC,IAAI,CAACT,MAAM,GAAI,IAAI,CAACA,MAAM,GAAGmF,KAAM,CAAC;EACvD,CAAC;EAEDU,OAAO,EAAE,SAATA,OAAOA,CAAYV,KAAK,EAAE;IACzB,OAAO,IAAI,CAAC1E,KAAK,CAAC,IAAI,CAACT,MAAM,GAAI,IAAI,CAACA,MAAM,GAAGmF,KAAM,CAAC;EACvD,CAAC;EAEDW,MAAM,EAAE,SAARA,MAAMA,CAAYC,OAAO,EAAE;IAC1B,IAAIX,GAAG,GAAG,IAAI,CAACA,GAAG,CAAC,CAAC;IACpB,IAAIpC,GAAG,GAAGoC,GAAG,CAACrF,KAAK,CAAC,CAAC,CAAC;IACtBiD,GAAG,GAAG,CAACA,GAAG,GAAG+C,OAAO,IAAI,GAAG;IAC3B/C,GAAG,GAAGA,GAAG,GAAG,CAAC,GAAG,GAAG,GAAGA,GAAG,GAAGA,GAAG;IAC/BoC,GAAG,CAACrF,KAAK,CAAC,CAAC,CAAC,GAAGiD,GAAG;IAClB,OAAOoC,GAAG;EACX,CAAC;EAEDY,GAAG,EAAE,SAALA,GAAGA,CAAYC,UAAU,EAAEC,MAAM,EAAE;IAGlC,IAAI,CAACD,UAAU,IAAI,CAACA,UAAU,CAACvE,GAAG,EAAE;MACnC,MAAM,IAAI9B,KAAK,CAAC,wEAAwE,GAAG,OAAOqG,UAAU,CAAC;IAC9G;IACA,IAAIE,MAAM,GAAGF,UAAU,CAACvE,GAAG,CAAC,CAAC;IAC7B,IAAI+C,MAAM,GAAG,IAAI,CAAC/C,GAAG,CAAC,CAAC;IACvB,IAAI0E,CAAC,GAAGF,MAAM,KAAKG,SAAS,GAAG,GAAG,GAAGH,MAAM;IAE3C,IAAII,CAAC,GAAG,CAAC,GAAGF,CAAC,GAAG,CAAC;IACjB,IAAIpC,CAAC,GAAGmC,MAAM,CAAC1F,KAAK,CAAC,CAAC,GAAGgE,MAAM,CAAChE,KAAK,CAAC,CAAC;IAEvC,IAAI8F,EAAE,GAAG,CAAC,CAAED,CAAC,GAAGtC,CAAC,KAAK,CAAC,CAAC,GAAIsC,CAAC,GAAG,CAACA,CAAC,GAAGtC,CAAC,KAAK,CAAC,GAAGsC,CAAC,GAAGtC,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG;IACjE,IAAIwC,EAAE,GAAG,CAAC,GAAGD,EAAE;IAEf,OAAO7G,KAAK,CAACgC,GAAG,CACd6E,EAAE,GAAGJ,MAAM,CAACxD,GAAG,CAAC,CAAC,GAAG6D,EAAE,GAAG/B,MAAM,CAAC9B,GAAG,CAAC,CAAC,EACrC4D,EAAE,GAAGJ,MAAM,CAACrD,KAAK,CAAC,CAAC,GAAG0D,EAAE,GAAG/B,MAAM,CAAC3B,KAAK,CAAC,CAAC,EACzCyD,EAAE,GAAGJ,MAAM,CAACpD,IAAI,CAAC,CAAC,GAAGyD,EAAE,GAAG/B,MAAM,CAAC1B,IAAI,CAAC,CAAC,EACvCoD,MAAM,CAAC1F,KAAK,CAAC,CAAC,GAAG2F,CAAC,GAAG3B,MAAM,CAAChE,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG2F,CAAC,CAAC,CAAC;EACjD;AACD,CAAC;AAGDnH,MAAM,CAACC,IAAI,CAACN,OAAO,CAAC,CAACO,OAAO,CAAC,UAAUC,KAAK,EAAE;EAC7C,IAAIL,aAAa,CAACyB,OAAO,CAACpB,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;IACxC;EACD;EAEA,IAAIU,QAAQ,GAAGlB,OAAO,CAACQ,KAAK,CAAC,CAACU,QAAQ;EAGtCJ,KAAK,CAACyB,SAAS,CAAC/B,KAAK,CAAC,GAAG,YAAY;IACpC,IAAI,IAAI,CAACA,KAAK,KAAKA,KAAK,EAAE;MACzB,OAAO,IAAIM,KAAK,CAAC,IAAI,CAAC;IACvB;IAEA,IAAIgD,SAAS,CAACtC,MAAM,EAAE;MACrB,OAAO,IAAIV,KAAK,CAACgD,SAAS,EAAEtD,KAAK,CAAC;IACnC;IAEA,IAAIqH,QAAQ,GAAG,OAAO/D,SAAS,CAAC5C,QAAQ,CAAC,KAAK,QAAQ,GAAGA,QAAQ,GAAG,IAAI,CAACE,MAAM;IAC/E,OAAO,IAAIN,KAAK,CAACgH,WAAW,CAAC9H,OAAO,CAAC,IAAI,CAACQ,KAAK,CAAC,CAACA,KAAK,CAAC,CAACuH,GAAG,CAAC,IAAI,CAAC5G,KAAK,CAAC,CAAC,CAAC8B,MAAM,CAAC4E,QAAQ,CAAC,EAAErH,KAAK,CAAC;EAClG,CAAC;EAGDM,KAAK,CAACN,KAAK,CAAC,GAAG,UAAUW,KAAK,EAAE;IAC/B,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC9BA,KAAK,GAAGO,SAAS,CAACzB,MAAM,CAACQ,IAAI,CAACqD,SAAS,CAAC,EAAE5C,QAAQ,CAAC;IACpD;IACA,OAAO,IAAIJ,KAAK,CAACK,KAAK,EAAEX,KAAK,CAAC;EAC/B,CAAC;AACF,CAAC,CAAC;AAEF,SAASwH,OAAOA,CAACC,GAAG,EAAEtF,MAAM,EAAE;EAC7B,OAAOuF,MAAM,CAACD,GAAG,CAACE,OAAO,CAACxF,MAAM,CAAC,CAAC;AACnC;AAEA,SAASiB,YAAYA,CAACjB,MAAM,EAAE;EAC7B,OAAO,UAAUsF,GAAG,EAAE;IACrB,OAAOD,OAAO,CAACC,GAAG,EAAEtF,MAAM,CAAC;EAC5B,CAAC;AACF;AAEA,SAASqB,MAAMA,CAACxD,KAAK,EAAE4H,OAAO,EAAEC,QAAQ,EAAE;EACzC7H,KAAK,GAAG8H,KAAK,CAACC,OAAO,CAAC/H,KAAK,CAAC,GAAGA,KAAK,GAAG,CAACA,KAAK,CAAC;EAE9CA,KAAK,CAACD,OAAO,CAAC,UAAUiI,CAAC,EAAE;IAC1B,CAAC3H,QAAQ,CAAC2H,CAAC,CAAC,KAAK3H,QAAQ,CAAC2H,CAAC,CAAC,GAAG,EAAE,CAAC,EAAEJ,OAAO,CAAC,GAAGC,QAAQ;EACxD,CAAC,CAAC;EAEF7H,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC;EAEhB,OAAO,UAAUqD,GAAG,EAAE;IACrB,IAAIxC,MAAM;IAEV,IAAIyC,SAAS,CAACtC,MAAM,EAAE;MACrB,IAAI6G,QAAQ,EAAE;QACbxE,GAAG,GAAGwE,QAAQ,CAACxE,GAAG,CAAC;MACpB;MAEAxC,MAAM,GAAG,IAAI,CAACb,KAAK,CAAC,CAAC,CAAC;MACtBa,MAAM,CAACF,KAAK,CAACiH,OAAO,CAAC,GAAGvE,GAAG;MAC3B,OAAOxC,MAAM;IACd;IAEAA,MAAM,GAAG,IAAI,CAACb,KAAK,CAAC,CAAC,CAAC,CAACW,KAAK,CAACiH,OAAO,CAAC;IACrC,IAAIC,QAAQ,EAAE;MACbhH,MAAM,GAAGgH,QAAQ,CAAChH,MAAM,CAAC;IAC1B;IAEA,OAAOA,MAAM;EACd,CAAC;AACF;AAEA,SAAS4C,KAAKA,CAAC7B,GAAG,EAAE;EACnB,OAAO,UAAUqG,CAAC,EAAE;IACnB,OAAOtG,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAACD,GAAG,EAAEqG,CAAC,CAAC,CAAC;EACrC,CAAC;AACF;AAEA,SAASX,WAAWA,CAACjE,GAAG,EAAE;EACzB,OAAOyE,KAAK,CAACC,OAAO,CAAC1E,GAAG,CAAC,GAAGA,GAAG,GAAG,CAACA,GAAG,CAAC;AACxC;AAEA,SAASnC,SAASA,CAACgH,GAAG,EAAElH,MAAM,EAAE;EAC/B,KAAK,IAAIP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGO,MAAM,EAAEP,CAAC,EAAE,EAAE;IAChC,IAAI,OAAOyH,GAAG,CAACzH,CAAC,CAAC,KAAK,QAAQ,EAAE;MAC/ByH,GAAG,CAACzH,CAAC,CAAC,GAAG,CAAC;IACX;EACD;EAEA,OAAOyH,GAAG;AACX;AAEAC,MAAM,CAACC,OAAO,GAAG9H,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}