{"ast": null, "code": "import \"./Expo.fx\";\nimport * as Logs from \"./logs/Logs\";\nexport { Logs };\nexport { disableErrorHandling } from \"./errors/ExpoErrorManager\";\nexport { default as registerRootComponent } from \"./launch/registerRootComponent\";\nexport { requireNativeModule } from 'expo-modules-core';", "map": {"version": 3, "names": ["Logs", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "default", "registerRootComponent", "requireNativeModule"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\expo\\src\\Expo.ts"], "sourcesContent": ["import './Expo.fx';\n\nimport * as Logs from './logs/Logs';\n\nexport { Logs };\nexport { disableErrorHandling } from './errors/ExpoErrorManager';\nexport { default as registerRootComponent } from './launch/registerRootComponent';\nexport { requireNativeModule } from 'expo-modules-core';\n"], "mappings": "AAAA;AAEA,OAAO,KAAKA,IAAI;AAEhB,SAASA,IAAI;AACb,SAASC,oBAAoB;AAC7B,SAASC,OAAO,IAAIC,qBAAqB;AACzC,SAASC,mBAAmB,QAAQ,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}