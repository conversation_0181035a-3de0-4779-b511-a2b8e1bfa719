import React, { useState, useEffect } from 'react';
import { 
  Settings as SettingsIcon, 
  Save, 
  RefreshCw, 
  AlertCircle,
  CheckCircle,
  Server,
  Database,
  MessageSquare,
  BarChart3,
  Shield,
  Clock
} from 'lucide-react';
import axios from 'axios';

const Settings = () => {
  const [settings, setSettings] = useState({
    system: {
      max_signals_per_day: 100,
      check_interval: 180,
      operation_mode: '24/7'
    },
    strategies: {
      scalp_enabled: true,
      breakout_enabled: true,
      inside_bar_enabled: true,
      mfi_enabled: true,
      swing_enabled: true,
      multi_source_enabled: true,
      volume_analysis_enabled: true,
      momentum_enabled: true
    },
    telegram: {
      professional_format: true,
      send_updates: true,
      rate_limit: 3
    },
    risk: {
      max_leverage: 20,
      risk_reward_ratio: 2.0,
      max_tp_percentage: 20
    }
  });
  
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [message, setMessage] = useState(null);

  const fetchSettings = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/settings');
      setSettings(response.data);
    } catch (err) {
      console.error('Erro ao carregar configurações:', err);
    } finally {
      setLoading(false);
    }
  };

  const saveSettings = async () => {
    try {
      setSaving(true);
      await axios.post('/api/settings', settings);
      setMessage({ type: 'success', text: 'Configurações salvas com sucesso!' });
      setTimeout(() => setMessage(null), 3000);
    } catch (err) {
      setMessage({ type: 'error', text: 'Erro ao salvar configurações' });
      setTimeout(() => setMessage(null), 3000);
    } finally {
      setSaving(false);
    }
  };

  useEffect(() => {
    fetchSettings();
  }, []);

  const updateSetting = (section, key, value) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [key]: value
      }
    }));
  };

  const SettingCard = ({ title, description, icon: Icon, children }) => (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center mb-4">
        <div className="p-2 bg-blue-100 rounded-lg">
          <Icon className="h-5 w-5 text-blue-600" />
        </div>
        <div className="ml-3">
          <h3 className="text-lg font-medium text-gray-900">{title}</h3>
          <p className="text-sm text-gray-600">{description}</p>
        </div>
      </div>
      {children}
    </div>
  );

  const ToggleSwitch = ({ enabled, onChange, label, description }) => (
    <div className="flex items-center justify-between py-3">
      <div>
        <div className="text-sm font-medium text-gray-900">{label}</div>
        {description && (
          <div className="text-xs text-gray-500">{description}</div>
        )}
      </div>
      <button
        onClick={() => onChange(!enabled)}
        className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
          enabled ? 'bg-blue-600' : 'bg-gray-200'
        }`}
      >
        <span
          className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
            enabled ? 'translate-x-6' : 'translate-x-1'
          }`}
        />
      </button>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Settings</h1>
          <p className="text-gray-600">Configurações do sistema CryptoSignals</p>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={fetchSettings}
            disabled={loading}
            className="flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors disabled:opacity-50"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Recarregar
          </button>
          <button
            onClick={saveSettings}
            disabled={saving}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
          >
            <Save className={`h-4 w-4 mr-2 ${saving ? 'animate-pulse' : ''}`} />
            {saving ? 'Salvando...' : 'Salvar'}
          </button>
        </div>
      </div>

      {/* Message */}
      {message && (
        <div className={`rounded-lg p-4 ${
          message.type === 'success' ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
        }`}>
          <div className="flex">
            {message.type === 'success' ? (
              <CheckCircle className="h-5 w-5 text-green-400" />
            ) : (
              <AlertCircle className="h-5 w-5 text-red-400" />
            )}
            <div className="ml-3">
              <p className={`text-sm font-medium ${
                message.type === 'success' ? 'text-green-800' : 'text-red-800'
              }`}>
                {message.text}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* System Settings */}
      <SettingCard
        title="Configurações do Sistema"
        description="Parâmetros gerais de operação"
        icon={Server}
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Máximo de Sinais por Dia
            </label>
            <input
              type="number"
              value={settings.system.max_signals_per_day}
              onChange={(e) => updateSetting('system', 'max_signals_per_day', parseInt(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              min="1"
              max="1000"
            />
            <p className="text-xs text-gray-500 mt-1">Limite diário de sinais gerados</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Intervalo de Verificação (segundos)
            </label>
            <input
              type="number"
              value={settings.system.check_interval}
              onChange={(e) => updateSetting('system', 'check_interval', parseInt(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              min="60"
              max="3600"
            />
            <p className="text-xs text-gray-500 mt-1">Tempo entre verificações de mercado</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Modo de Operação
            </label>
            <select
              value={settings.system.operation_mode}
              onChange={(e) => updateSetting('system', 'operation_mode', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="24/7">24/7 - Operação Contínua</option>
              <option value="business_hours">Horário Comercial</option>
              <option value="custom">Personalizado</option>
            </select>
          </div>
        </div>
      </SettingCard>

      {/* Strategy Settings */}
      <SettingCard
        title="Estratégias de Trading"
        description="Ativar/desativar estratégias específicas"
        icon={BarChart3}
      >
        <div className="space-y-2">
          <ToggleSwitch
            enabled={settings.strategies.scalp_enabled}
            onChange={(value) => updateSetting('strategies', 'scalp_enabled', value)}
            label="Scalp Strategy"
            description="Operações de curto prazo (1-4h)"
          />
          <ToggleSwitch
            enabled={settings.strategies.breakout_enabled}
            onChange={(value) => updateSetting('strategies', 'breakout_enabled', value)}
            label="Breakout Volume"
            description="Rompimentos com confirmação de volume"
          />
          <ToggleSwitch
            enabled={settings.strategies.inside_bar_enabled}
            onChange={(value) => updateSetting('strategies', 'inside_bar_enabled', value)}
            label="Inside Bar"
            description="Padrão gráfico Inside Bar"
          />
          <ToggleSwitch
            enabled={settings.strategies.mfi_enabled}
            onChange={(value) => updateSetting('strategies', 'mfi_enabled', value)}
            label="MFI Strategy"
            description="Money Flow Index"
          />
          <ToggleSwitch
            enabled={settings.strategies.swing_enabled}
            onChange={(value) => updateSetting('strategies', 'swing_enabled', value)}
            label="Swing Strategy"
            description="Operações de médio prazo"
          />
          <ToggleSwitch
            enabled={settings.strategies.multi_source_enabled}
            onChange={(value) => updateSetting('strategies', 'multi_source_enabled', value)}
            label="Multi-Source Strategy"
            description="Combina múltiplos indicadores"
          />
          <ToggleSwitch
            enabled={settings.strategies.volume_analysis_enabled}
            onChange={(value) => updateSetting('strategies', 'volume_analysis_enabled', value)}
            label="Volume Analysis"
            description="Análise avançada de volume"
          />
          <ToggleSwitch
            enabled={settings.strategies.momentum_enabled}
            onChange={(value) => updateSetting('strategies', 'momentum_enabled', value)}
            label="Momentum Strategy"
            description="Indicadores de momentum"
          />
        </div>
      </SettingCard>

      {/* Telegram Settings */}
      <SettingCard
        title="Configurações do Telegram"
        description="Formatação e envio de mensagens"
        icon={MessageSquare}
      >
        <div className="space-y-4">
          <ToggleSwitch
            enabled={settings.telegram.professional_format}
            onChange={(value) => updateSetting('telegram', 'professional_format', value)}
            label="Formatação Profissional"
            description="Templates clean sem emojis para traders de elite"
          />
          <ToggleSwitch
            enabled={settings.telegram.send_updates}
            onChange={(value) => updateSetting('telegram', 'send_updates', value)}
            label="Enviar Atualizações"
            description="Notificações de profit e status"
          />
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Limite de Taxa (minutos)
            </label>
            <input
              type="number"
              value={settings.telegram.rate_limit}
              onChange={(e) => updateSetting('telegram', 'rate_limit', parseInt(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              min="1"
              max="60"
            />
            <p className="text-xs text-gray-500 mt-1">Intervalo mínimo entre mensagens</p>
          </div>
        </div>
      </SettingCard>

      {/* Risk Management */}
      <SettingCard
        title="Gestão de Risco"
        description="Parâmetros de segurança e risco"
        icon={Shield}
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Alavancagem Máxima
            </label>
            <input
              type="number"
              value={settings.risk.max_leverage}
              onChange={(e) => updateSetting('risk', 'max_leverage', parseInt(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              min="1"
              max="100"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Relação Risk:Reward
            </label>
            <input
              type="number"
              step="0.1"
              value={settings.risk.risk_reward_ratio}
              onChange={(e) => updateSetting('risk', 'risk_reward_ratio', parseFloat(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              min="1.0"
              max="5.0"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Take Profit Máximo (%)
            </label>
            <input
              type="number"
              value={settings.risk.max_tp_percentage}
              onChange={(e) => updateSetting('risk', 'max_tp_percentage', parseInt(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              min="5"
              max="50"
            />
          </div>
        </div>
      </SettingCard>

      {/* System Status */}
      <SettingCard
        title="Status do Sistema"
        description="Informações em tempo real"
        icon={Clock}
      >
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center p-4 bg-green-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600">8</div>
            <div className="text-sm text-gray-600">Estratégias Ativas</div>
          </div>
          <div className="text-center p-4 bg-blue-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">109</div>
            <div className="text-sm text-gray-600">Tokens Monitorados</div>
          </div>
          <div className="text-center p-4 bg-purple-50 rounded-lg">
            <div className="text-2xl font-bold text-purple-600">24/7</div>
            <div className="text-sm text-gray-600">Operação Contínua</div>
          </div>
        </div>
      </SettingCard>
    </div>
  );
};

export default Settings;
