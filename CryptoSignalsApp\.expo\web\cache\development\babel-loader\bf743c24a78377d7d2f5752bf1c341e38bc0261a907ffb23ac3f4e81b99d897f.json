{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"tintColor\", \"style\"];\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport { useTheme } from '@react-navigation/native';\nimport * as React from 'react';\nimport Animated from \"react-native-web/dist/exports/Animated\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nexport default function HeaderTitle(_ref) {\n  var tintColor = _ref.tintColor,\n    style = _ref.style,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var _useTheme = useTheme(),\n    colors = _useTheme.colors;\n  return React.createElement(Animated.Text, _extends({\n    accessibilityRole: \"header\",\n    \"aria-level\": \"1\",\n    numberOfLines: 1\n  }, rest, {\n    style: [styles.title, {\n      color: tintColor === undefined ? colors.text : tintColor\n    }, style]\n  }));\n}\nvar styles = StyleSheet.create({\n  title: Platform.select({\n    ios: {\n      fontSize: 17,\n      fontWeight: '600'\n    },\n    android: {\n      fontSize: 20,\n      fontFamily: 'sans-serif-medium',\n      fontWeight: 'normal'\n    },\n    default: {\n      fontSize: 18,\n      fontWeight: '500'\n    }\n  })\n});", "map": {"version": 3, "names": ["useTheme", "React", "Animated", "Platform", "StyleSheet", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref", "tintColor", "style", "rest", "_objectWithoutProperties", "_excluded", "_useTheme", "colors", "createElement", "Text", "_extends", "accessibilityRole", "numberOfLines", "styles", "title", "color", "undefined", "text", "create", "select", "ios", "fontSize", "fontWeight", "android", "fontFamily", "default"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\@react-navigation\\elements\\src\\Header\\HeaderTitle.tsx"], "sourcesContent": ["import { useTheme } from '@react-navigation/native';\nimport * as React from 'react';\nimport {\n  Animated,\n  Platform,\n  StyleProp,\n  StyleSheet,\n  TextProps,\n  TextStyle,\n} from 'react-native';\n\ntype Props = Omit<TextProps, 'style'> & {\n  tintColor?: string;\n  style?: Animated.WithAnimatedValue<StyleProp<TextStyle>>;\n};\n\nexport default function HeaderTitle({ tintColor, style, ...rest }: Props) {\n  const { colors } = useTheme();\n\n  return (\n    <Animated.Text\n      accessibilityRole=\"header\"\n      aria-level=\"1\"\n      numberOfLines={1}\n      {...rest}\n      style={[\n        styles.title,\n        { color: tintColor === undefined ? colors.text : tintColor },\n        style,\n      ]}\n    />\n  );\n}\n\nconst styles = StyleSheet.create({\n  title: Platform.select({\n    ios: {\n      fontSize: 17,\n      fontWeight: '600',\n    },\n    android: {\n      fontSize: 20,\n      fontFamily: 'sans-serif-medium',\n      fontWeight: 'normal',\n    },\n    default: {\n      fontSize: 18,\n      fontWeight: '500',\n    },\n  }),\n});\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA,SAASA,QAAQ,QAAQ,0BAA0B;AACnD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAAA,OAAAC,QAAA;AAAA,OAAAC,UAAA;AAe9B,eAAe,SAASC,WAAWA,CAAAC,IAAA,EAAuC;EAAA,IAApCC,SAAS,GAAyBD,IAAA,CAAlCC,SAAS;IAAEC,KAAK,GAAkBF,IAAA,CAAvBE,KAAK;IAAKC,IAAA,GAAAC,wBAAA,CAAaJ,IAAA,EAAAK,SAAA;EACtE,IAAAC,SAAA,GAAmBZ,QAAQ,EAAE;IAArBa,MAAA,GAAAD,SAAA,CAAAC,MAAA;EAER,OACEZ,KAAA,CAAAa,aAAA,CAACZ,QAAQ,CAACa,IAAI,EAAAC,QAAA;IACZC,iBAAiB,EAAC,QAAQ;IAC1B,cAAW,GAAG;IACdC,aAAa,EAAE;EAAE,GACbT,IAAI;IACRD,KAAK,EAAE,CACLW,MAAM,CAACC,KAAK,EACZ;MAAEC,KAAK,EAAEd,SAAS,KAAKe,SAAS,GAAGT,MAAM,CAACU,IAAI,GAAGhB;IAAU,CAAC,EAC5DC,KAAK;EACL,GACF;AAEN;AAEA,IAAMW,MAAM,GAAGf,UAAU,CAACoB,MAAM,CAAC;EAC/BJ,KAAK,EAAEjB,QAAQ,CAACsB,MAAM,CAAC;IACrBC,GAAG,EAAE;MACHC,QAAQ,EAAE,EAAE;MACZC,UAAU,EAAE;IACd,CAAC;IACDC,OAAO,EAAE;MACPF,QAAQ,EAAE,EAAE;MACZG,UAAU,EAAE,mBAAmB;MAC/BF,UAAU,EAAE;IACd,CAAC;IACDG,OAAO,EAAE;MACPJ,QAAQ,EAAE,EAAE;MACZC,UAAU,EAAE;IACd;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}