{"ast": null, "code": "import * as React from 'react';\nvar UNINITIALIZED = typeof Symbol === 'function' && typeof Symbol() === 'symbol' ? Symbol() : Object.freeze({});\nexport default function useStable(getInitialValue) {\n  var ref = React.useRef(UNINITIALIZED);\n  if (ref.current === UNINITIALIZED) {\n    ref.current = getInitialValue();\n  }\n  return ref.current;\n}", "map": {"version": 3, "names": ["React", "UNINITIALIZED", "Symbol", "Object", "freeze", "useStable", "getInitialValue", "ref", "useRef", "current"], "sources": ["C:/Users/<USER>/Desktop/Signals/CryptoSignalsApp/node_modules/react-native-web/dist/modules/useStable/index.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport * as React from 'react';\nvar UNINITIALIZED = typeof Symbol === 'function' && typeof Symbol() === 'symbol' ? Symbol() : Object.freeze({});\nexport default function useStable(getInitialValue) {\n  var ref = React.useRef(UNINITIALIZED);\n  if (ref.current === UNINITIALIZED) {\n    ref.current = getInitialValue();\n  }\n  // $FlowFixMe (#64650789) Trouble refining types where `Symbol` is concerned.\n  return ref.current;\n}"], "mappings": "AASA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,IAAIC,aAAa,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAI,OAAOA,MAAM,CAAC,CAAC,KAAK,QAAQ,GAAGA,MAAM,CAAC,CAAC,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC/G,eAAe,SAASC,SAASA,CAACC,eAAe,EAAE;EACjD,IAAIC,GAAG,GAAGP,KAAK,CAACQ,MAAM,CAACP,aAAa,CAAC;EACrC,IAAIM,GAAG,CAACE,OAAO,KAAKR,aAAa,EAAE;IACjCM,GAAG,CAACE,OAAO,GAAGH,eAAe,CAAC,CAAC;EACjC;EAEA,OAAOC,GAAG,CAACE,OAAO;AACpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}