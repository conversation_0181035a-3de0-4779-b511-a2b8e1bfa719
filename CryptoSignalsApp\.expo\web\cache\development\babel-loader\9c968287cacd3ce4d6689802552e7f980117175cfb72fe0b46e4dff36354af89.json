{"ast": null, "code": "import * as React from 'react';\nimport Keyboard from \"react-native-web/dist/exports/Keyboard\";\nimport TextInput from \"react-native-web/dist/exports/TextInput\";\nexport default function useKeyboardManager(isEnabled) {\n  var previouslyFocusedTextInputRef = React.useRef(undefined);\n  var startTimestampRef = React.useRef(0);\n  var keyboardTimeoutRef = React.useRef();\n  var clearKeyboardTimeout = React.useCallback(function () {\n    if (keyboardTimeoutRef.current !== undefined) {\n      clearTimeout(keyboardTimeoutRef.current);\n      keyboardTimeoutRef.current = undefined;\n    }\n  }, []);\n  var onPageChangeStart = React.useCallback(function () {\n    if (!isEnabled()) {\n      return;\n    }\n    clearKeyboardTimeout();\n    var input = TextInput.State.currentlyFocusedInput();\n    input === null || input === void 0 ? void 0 : input.blur();\n    previouslyFocusedTextInputRef.current = input;\n    startTimestampRef.current = Date.now();\n  }, [clearKeyboardTimeout, isEnabled]);\n  var onPageChangeConfirm = React.useCallback(function (force) {\n    if (!isEnabled()) {\n      return;\n    }\n    clearKeyboardTimeout();\n    if (force) {\n      Keyboard.dismiss();\n    } else {\n      var input = previouslyFocusedTextInputRef.current;\n      input === null || input === void 0 ? void 0 : input.blur();\n    }\n    previouslyFocusedTextInputRef.current = undefined;\n  }, [clearKeyboardTimeout, isEnabled]);\n  var onPageChangeCancel = React.useCallback(function () {\n    if (!isEnabled()) {\n      return;\n    }\n    clearKeyboardTimeout();\n    var input = previouslyFocusedTextInputRef.current;\n    if (input) {\n      if (Date.now() - startTimestampRef.current < 100) {\n        keyboardTimeoutRef.current = setTimeout(function () {\n          input === null || input === void 0 ? void 0 : input.focus();\n          previouslyFocusedTextInputRef.current = undefined;\n        }, 100);\n      } else {\n        input === null || input === void 0 ? void 0 : input.focus();\n        previouslyFocusedTextInputRef.current = undefined;\n      }\n    }\n  }, [clearKeyboardTimeout, isEnabled]);\n  React.useEffect(function () {\n    return function () {\n      return clearKeyboardTimeout();\n    };\n  }, [clearKeyboardTimeout]);\n  return {\n    onPageChangeStart: onPageChangeStart,\n    onPageChangeConfirm: onPageChangeConfirm,\n    onPageChangeCancel: onPageChangeCancel\n  };\n}", "map": {"version": 3, "names": ["React", "Keyboard", "TextInput", "useKeyboardManager", "isEnabled", "previouslyFocusedTextInputRef", "useRef", "undefined", "startTimestampRef", "keyboardTimeoutRef", "clearKeyboardTimeout", "useCallback", "current", "clearTimeout", "onPageChangeStart", "input", "State", "currentlyFocusedInput", "blur", "Date", "now", "onPageChangeConfirm", "force", "dismiss", "onPageChangeCancel", "setTimeout", "focus", "useEffect"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\@react-navigation\\stack\\src\\utils\\useKeyboardManager.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { HostComponent, Keyboard, TextInput } from 'react-native';\n\ntype InputRef = React.ElementRef<HostComponent<unknown>> | undefined;\n\nexport default function useKeyboardManager(isEnabled: () => boolean) {\n  // Numeric id of the previously focused text input\n  // When a gesture didn't change the tab, we can restore the focused input with this\n  const previouslyFocusedTextInputRef = React.useRef<InputRef>(undefined);\n  const startTimestampRef = React.useRef<number>(0);\n  const keyboardTimeoutRef = React.useRef<any>();\n\n  const clearKeyboardTimeout = React.useCallback(() => {\n    if (keyboardTimeoutRef.current !== undefined) {\n      clearTimeout(keyboardTimeoutRef.current);\n      keyboardTimeoutRef.current = undefined;\n    }\n  }, []);\n\n  const onPageChangeStart = React.useCallback(() => {\n    if (!isEnabled()) {\n      return;\n    }\n\n    clearKeyboardTimeout();\n\n    const input: InputRef = TextInput.State.currentlyFocusedInput();\n\n    // When a page change begins, blur the currently focused input\n    input?.blur();\n\n    // Store the id of this input so we can refocus it if change was cancelled\n    previouslyFocusedTextInputRef.current = input;\n\n    // Store timestamp for touch start\n    startTimestampRef.current = Date.now();\n  }, [clearKeyboardTimeout, isEnabled]);\n\n  const onPageChangeConfirm = React.useCallback(\n    (force: boolean) => {\n      if (!isEnabled()) {\n        return;\n      }\n\n      clearKeyboardTimeout();\n\n      if (force) {\n        // Always dismiss input, even if we don't have a ref to it\n        // We might not have the ref if onPageChangeStart was never called\n        // This can happen if page change was not from a gesture\n        Keyboard.dismiss();\n      } else {\n        const input = previouslyFocusedTextInputRef.current;\n\n        // Dismiss the keyboard only if an input was a focused before\n        // This makes sure we don't dismiss input on going back and focusing an input\n        input?.blur();\n      }\n\n      // Cleanup the ID on successful page change\n      previouslyFocusedTextInputRef.current = undefined;\n    },\n    [clearKeyboardTimeout, isEnabled]\n  );\n\n  const onPageChangeCancel = React.useCallback(() => {\n    if (!isEnabled()) {\n      return;\n    }\n\n    clearKeyboardTimeout();\n\n    // The page didn't change, we should restore the focus of text input\n    const input = previouslyFocusedTextInputRef.current;\n\n    if (input) {\n      // If the interaction was super short we should make sure keyboard won't hide again.\n\n      // Too fast input refocus will result only in keyboard flashing on screen and hiding right away.\n      // During first ~100ms keyboard will be dismissed no matter what,\n      // so we have to make sure it won't interrupt input refocus logic.\n      // That's why when the interaction is shorter than 100ms we add delay so it won't hide once again.\n      // Subtracting timestamps makes us sure the delay is executed only when needed.\n      if (Date.now() - startTimestampRef.current < 100) {\n        keyboardTimeoutRef.current = setTimeout(() => {\n          input?.focus();\n          previouslyFocusedTextInputRef.current = undefined;\n        }, 100);\n      } else {\n        input?.focus();\n        previouslyFocusedTextInputRef.current = undefined;\n      }\n    }\n  }, [clearKeyboardTimeout, isEnabled]);\n\n  React.useEffect(() => {\n    return () => clearKeyboardTimeout();\n  }, [clearKeyboardTimeout]);\n\n  return {\n    onPageChangeStart,\n    onPageChangeConfirm,\n    onPageChangeCancel,\n  };\n}\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAAA,OAAAC,SAAA;AAK9B,eAAe,SAASC,kBAAkBA,CAACC,SAAwB,EAAE;EAGnE,IAAMC,6BAA6B,GAAGL,KAAK,CAACM,MAAM,CAAWC,SAAS,CAAC;EACvE,IAAMC,iBAAiB,GAAGR,KAAK,CAACM,MAAM,CAAS,CAAC,CAAC;EACjD,IAAMG,kBAAkB,GAAGT,KAAK,CAACM,MAAM,EAAO;EAE9C,IAAMI,oBAAoB,GAAGV,KAAK,CAACW,WAAW,CAAC,YAAM;IACnD,IAAIF,kBAAkB,CAACG,OAAO,KAAKL,SAAS,EAAE;MAC5CM,YAAY,CAACJ,kBAAkB,CAACG,OAAO,CAAC;MACxCH,kBAAkB,CAACG,OAAO,GAAGL,SAAS;IACxC;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,IAAMO,iBAAiB,GAAGd,KAAK,CAACW,WAAW,CAAC,YAAM;IAChD,IAAI,CAACP,SAAS,EAAE,EAAE;MAChB;IACF;IAEAM,oBAAoB,EAAE;IAEtB,IAAMK,KAAe,GAAGb,SAAS,CAACc,KAAK,CAACC,qBAAqB,EAAE;IAG/DF,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEG,IAAI,EAAE;IAGbb,6BAA6B,CAACO,OAAO,GAAGG,KAAK;IAG7CP,iBAAiB,CAACI,OAAO,GAAGO,IAAI,CAACC,GAAG,EAAE;EACxC,CAAC,EAAE,CAACV,oBAAoB,EAAEN,SAAS,CAAC,CAAC;EAErC,IAAMiB,mBAAmB,GAAGrB,KAAK,CAACW,WAAW,CAC1C,UAAAW,KAAc,EAAK;IAClB,IAAI,CAAClB,SAAS,EAAE,EAAE;MAChB;IACF;IAEAM,oBAAoB,EAAE;IAEtB,IAAIY,KAAK,EAAE;MAITrB,QAAQ,CAACsB,OAAO,EAAE;IACpB,CAAC,MAAM;MACL,IAAMR,KAAK,GAAGV,6BAA6B,CAACO,OAAO;MAInDG,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEG,IAAI,EAAE;IACf;IAGAb,6BAA6B,CAACO,OAAO,GAAGL,SAAS;EACnD,CAAC,EACD,CAACG,oBAAoB,EAAEN,SAAS,CAAC,CAClC;EAED,IAAMoB,kBAAkB,GAAGxB,KAAK,CAACW,WAAW,CAAC,YAAM;IACjD,IAAI,CAACP,SAAS,EAAE,EAAE;MAChB;IACF;IAEAM,oBAAoB,EAAE;IAGtB,IAAMK,KAAK,GAAGV,6BAA6B,CAACO,OAAO;IAEnD,IAAIG,KAAK,EAAE;MAQT,IAAII,IAAI,CAACC,GAAG,EAAE,GAAGZ,iBAAiB,CAACI,OAAO,GAAG,GAAG,EAAE;QAChDH,kBAAkB,CAACG,OAAO,GAAGa,UAAU,CAAC,YAAM;UAC5CV,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEW,KAAK,EAAE;UACdrB,6BAA6B,CAACO,OAAO,GAAGL,SAAS;QACnD,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,MAAM;QACLQ,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEW,KAAK,EAAE;QACdrB,6BAA6B,CAACO,OAAO,GAAGL,SAAS;MACnD;IACF;EACF,CAAC,EAAE,CAACG,oBAAoB,EAAEN,SAAS,CAAC,CAAC;EAErCJ,KAAK,CAAC2B,SAAS,CAAC,YAAM;IACpB,OAAO;MAAA,OAAMjB,oBAAoB,EAAE;IAAA;EACrC,CAAC,EAAE,CAACA,oBAAoB,CAAC,CAAC;EAE1B,OAAO;IACLI,iBAAiB,EAAjBA,iBAAiB;IACjBO,mBAAmB,EAAnBA,mBAAmB;IACnBG,kBAAA,EAAAA;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}