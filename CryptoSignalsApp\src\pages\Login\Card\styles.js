import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#fff',
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginRight: 8,
  },
  type: {
    fontSize: 16,
    marginLeft: 8,
  },
  filterButton: {
    marginHorizontal: 5,
    padding: 8,
    borderRadius: 4,
    backgroundColor: '#f5f5f5',
  },
  dFlex: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 32,
  },
  icon: {
    fontSize: 24,
    color: '#000',
  },
  menuOption: {
    marginHorizontal: 5,
  },
});

export default styles;
