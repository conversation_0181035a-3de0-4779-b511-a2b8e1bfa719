import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  row: {
    marginBottom: 16,
  },
  pageTitle: {
    marginBottom: 24
  },
  h1: {
    color: '#fff',
    fontSize: 24,
    textAlign: 'center',
    fontFamily: 'Poppins_500Medium'
  },
  paragraph: {
    color: '#fff',
    fontSize: 14,
    textAlign: 'center',
    fontFamily: 'Poppins_400Regular'
  },
  text: {
    color: '#fff',
    fontSize: 12,
    textAlign: 'center',
    fontFamily: 'Poppins_400Regular'
  },
  inputEmail: {
    backgroundColor: '#fff',
    borderRadius: 4,
    color: '#0D0D0D',
    fontSize: 14,
    padding: 12,
    height: 48,
    fontFamily: 'Poppins_400Regular',
    alignItems: 'center',
    marginBottom: 15
  },
  premiumDates: {
    color: '#fff',
    fontSize: 14,
    fontFamily: 'Poppins_500Medium',
    textAlign: 'center',
  },
  buttonPay: {
    backgroundColor: '#3176c4',
    borderRadius: 4,
    paddingVertical: 6,
    height: 48,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center'
  },
  textButtonPay: {
    color: '#fff',
    fontSize: 14,
    textAlign: 'center',
    fontFamily: 'Poppins_600SemiBold',
    lineHeight: 22,
  }
});

export default styles;
