gerador_sinais_telegram/
├── src/                            # Código fonte principal
│   ├── __init__.py
│   ├── core/                       # Funcionalidades principais
│   │   ├── __init__.py
│   │   ├── signal_generator.py     # Lógica de geração de sinais
│   │   ├── signal_validator.py     # Validação de sinais
│   │   └── signal_monitor.py       # Monitoramento de sinais
│   ├── services/                   # Serviços externos
│   │   ├── __init__.py
│   │   ├── telegram/              # Funcionalidades Telegram
│   │   │   ├── __init__.py
│   │   │   ├── sender.py
│   │   │   └── formatter.py
│   │   └── binance/               # Funcionalidades Binance
│   │       ├── __init__.py
│   │       └── client.py
│   └── utils/                      # Utilitários
│       ├── __init__.py
│       └── helpers.py
├── tests/                          # Testes
│   ├── __init__.py
│   ├── unit/                      # Testes unitários
│   │   ├── __init__.py
│   │   ├── test_signal_formatter.py
│   │   └── test_telegram_sender.py
│   └── integration/               # Testes de integração
│       └── __init__.py
├── config/                        # Configurações
│   ├── __init__.py
│   ├── settings.py
│   └── logging.py
├── docs/                          # Documentação
│   ├── api/
│   ├── guides/
│   └── architecture/
├── scripts/                       # Scripts de utilidade
│   ├── update_script.py
│   └── update_script.sh
├── logs/                         # Diretório para logs
│   └── .gitkeep
├── data/                         # Dados e recursos
│   ├── images/                   # Imagens e recursos visuais
│   └── templates/                # Templates de mensagens
├── .env.example
├── .gitignore
├── main.py                       # Ponto de entrada principal
├── monitor.py                    # Script de monitoramento
├── requirements.txt
└── README.md