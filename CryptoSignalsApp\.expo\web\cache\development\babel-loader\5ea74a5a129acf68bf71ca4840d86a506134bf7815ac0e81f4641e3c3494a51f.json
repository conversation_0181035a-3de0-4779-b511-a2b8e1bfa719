{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"accessibilityLabel\"];\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nimport * as React from 'react';\nimport AppbarAction from \"./AppbarAction\";\nimport AppbarBackIcon from \"./AppbarBackIcon\";\nimport { forwardRef } from \"../../utils/forwardRef\";\nvar AppbarBackAction = forwardRef(function (_ref, ref) {\n  var _ref$accessibilityLab = _ref.accessibilityLabel,\n    accessibilityLabel = _ref$accessibilityLab === void 0 ? 'Back' : _ref$accessibilityLab,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  return React.createElement(AppbarAction, _extends({\n    accessibilityLabel: accessibilityLabel\n  }, rest, {\n    icon: AppbarBackIcon,\n    isLeading: true,\n    ref: ref\n  }));\n});\nAppbarBackAction.displayName = 'Appbar.BackAction';\nexport default AppbarBackAction;\nexport { AppbarBackAction };", "map": {"version": 3, "names": ["React", "AppbarAction", "AppbarBackIcon", "forwardRef", "AppbarBackAction", "_ref", "ref", "_ref$accessibilityLab", "accessibilityLabel", "rest", "_objectWithoutProperties", "_excluded", "createElement", "_extends", "icon", "isLeading", "displayName"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\Appbar\\AppbarBackAction.tsx"], "sourcesContent": ["import * as React from 'react';\nimport type {\n  Animated,\n  GestureResponderEvent,\n  StyleProp,\n  View,\n  ViewStyle,\n} from 'react-native';\n\nimport type { $Omit } from './../../types';\nimport AppbarAction from './AppbarAction';\nimport AppbarBackIcon from './AppbarBackIcon';\nimport { forwardRef } from '../../utils/forwardRef';\n\nexport type Props = $Omit<\n  React.ComponentPropsWithoutRef<typeof AppbarAction>,\n  'icon'\n> & {\n  /**\n   *  Custom color for back icon.\n   */\n  color?: string;\n  /**\n   * Optional icon size.\n   */\n  size?: number;\n  /**\n   * Whether the button is disabled. A disabled button is greyed out and `onPress` is not called on touch.\n   */\n  disabled?: boolean;\n  /**\n   * Accessibility label for the button. This is read by the screen reader when the user taps the button.\n   */\n  accessibilityLabel?: string;\n  /**\n   * Function to execute on press.\n   */\n  onPress?: (e: GestureResponderEvent) => void;\n  style?: Animated.WithAnimatedValue<StyleProp<ViewStyle>>;\n  ref?: React.RefObject<View>;\n};\n\n/**\n * A component used to display a back button in the appbar.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { Appbar } from 'react-native-paper';\n *\n * const MyComponent = () => (\n *     <Appbar.Header>\n *       <Appbar.BackAction onPress={() => {}} />\n *     </Appbar.Header>\n * );\n *\n * export default MyComponent;\n * ```\n */\nconst AppbarBackAction = forwardRef<View, Props>(\n  ({ accessibilityLabel = 'Back', ...rest }: Props, ref) => (\n    <AppbarAction\n      accessibilityLabel={accessibilityLabel}\n      {...rest}\n      icon={AppbarBackIcon}\n      isLeading\n      ref={ref}\n    />\n  )\n);\n\nAppbarBackAction.displayName = 'Appbar.BackAction';\n\nexport default AppbarBackAction;\n\n// @component-docs ignore-next-line\nexport { AppbarBackAction };\n"], "mappings": ";;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAU9B,OAAOC,YAAY;AACnB,OAAOC,cAAc;AACrB,SAASC,UAAU;AA+CnB,IAAMC,gBAAgB,GAAGD,UAAU,CACjC,UAAAE,IAAA,EAAkDC,GAAG;EAAA,IAAAC,qBAAA,GAAAF,IAAA,CAAlDG,kBAAkB;IAAlBA,kBAAkB,GAAAD,qBAAA,cAAG,MAAM,GAAAA,qBAAA;IAAKE,IAAA,GAAAC,wBAAA,CAAAL,IAAA,EAAAM,SAAA;EAAA,OACjCX,KAAA,CAAAY,aAAA,CAACX,YAAY,EAAAY,QAAA;IACXL,kBAAkB,EAAEA;EAAmB,GACnCC,IAAI;IACRK,IAAI,EAAEZ,cAAe;IACrBa,SAAS;IACTT,GAAG,EAAEA;EAAI,EACV,CAEL;AAAA,EAAC;AAEDF,gBAAgB,CAACY,WAAW,GAAG,mBAAmB;AAElD,eAAeZ,gBAAgB;AAG/B,SAASA,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}