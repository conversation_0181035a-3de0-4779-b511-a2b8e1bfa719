{"ast": null, "code": "import View from \"react-native-web/dist/exports/View\";\nvar ScreenContainer = View;\nexport default ScreenContainer;", "map": {"version": 3, "names": ["ScreenContainer", "View"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\react-native-screens\\src\\components\\ScreenContainer.web.tsx"], "sourcesContent": ["import { View } from 'react-native';\n\nconst ScreenContainer = View;\n\nexport default ScreenContainer;\n"], "mappings": ";AAEA,IAAMA,eAAe,GAAGC,IAAI;AAE5B,eAAeD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}