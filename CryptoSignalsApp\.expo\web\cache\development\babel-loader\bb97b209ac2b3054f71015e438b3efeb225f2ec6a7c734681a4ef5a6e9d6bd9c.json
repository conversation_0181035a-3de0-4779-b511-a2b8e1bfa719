{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport React, { useState, useEffect } from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport ScrollView from \"react-native-web/dist/exports/ScrollView\";\nimport TouchableOpacity from \"react-native-web/dist/exports/TouchableOpacity\";\nimport FlatList from \"react-native-web/dist/exports/FlatList\";\nimport { AntDesign } from '@expo/vector-icons';\nimport styles from \"./styles\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar News = function News() {\n  var _useState = useState([]),\n    _useState2 = _slicedToArray(_useState, 2),\n    newsData = _useState2[0],\n    setNewsData = _useState2[1];\n  var _useState3 = useState(true),\n    _useState4 = _slicedToArray(_useState3, 2),\n    loading = _useState4[0],\n    setLoading = _useState4[1];\n  useEffect(function () {\n    var fetchedNews = [{\n      id: '1',\n      title: 'Bitcoin Hits New All-Time High',\n      date: 'August 25, 2023',\n      content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed dapibus.'\n    }, {\n      id: '2',\n      title: 'Ethereum 2.0 Upgrade Announced',\n      date: 'August 23, 2023',\n      content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed dapibus.'\n    }];\n    setNewsData(fetchedNews);\n    setLoading(false);\n  }, []);\n  var renderNewsItem = function renderNewsItem(_ref) {\n    var item = _ref.item;\n    return _jsxs(TouchableOpacity, {\n      style: styles.newsItem,\n      children: [_jsx(Text, {\n        style: styles.newsTitle,\n        children: item.title\n      }), _jsx(Text, {\n        style: styles.newsDate,\n        children: item.date\n      }), _jsx(Text, {\n        style: styles.newsContent,\n        children: item.content\n      })]\n    });\n  };\n  return _jsxs(ScrollView, {\n    style: styles.container,\n    children: [_jsx(Text, {\n      style: styles.pageTitle,\n      children: \"Not\\xEDcias\"\n    }), loading ? _jsx(Text, {\n      style: styles.loadingText,\n      children: \"Carregando not\\xEDcias...\"\n    }) : _jsx(FlatList, {\n      data: newsData,\n      keyExtractor: function keyExtractor(item) {\n        return item.id;\n      },\n      renderItem: renderNewsItem\n    })]\n  });\n};\nexport default News;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "View", "Text", "ScrollView", "TouchableOpacity", "FlatList", "AntDesign", "styles", "jsx", "_jsx", "jsxs", "_jsxs", "News", "_useState", "_useState2", "_slicedToArray", "newsData", "setNewsData", "_useState3", "_useState4", "loading", "setLoading", "fetchedNews", "id", "title", "date", "content", "renderNewsItem", "_ref", "item", "style", "newsItem", "children", "newsTitle", "newsDate", "newsContent", "container", "pageTitle", "loadingText", "data", "keyExtractor", "renderItem"], "sources": ["E:/CryptoSignalsApp/src/pages/News/index.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { View, Text, ScrollView, TouchableOpacity, FlatList } from 'react-native';\r\nimport { AntDesign } from '@expo/vector-icons';\r\nimport styles from './styles';\r\n\r\nconst News = () => {\r\n  const [newsData, setNewsData] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n\r\n  useEffect(() => {\r\n    // Simule a busca de notícias aqui\r\n    const fetchedNews = [\r\n      {\r\n        id: '1',\r\n        title: 'Bitcoin Hits New All-Time High',\r\n        date: 'August 25, 2023',\r\n        content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed dapibus.',\r\n      },\r\n      {\r\n        id: '2',\r\n        title: 'Ethereum 2.0 Upgrade Announced',\r\n        date: 'August 23, 2023',\r\n        content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed dapibus.',\r\n      },\r\n      // Adicione mais notícias aqui\r\n    ];\r\n\r\n    setNewsData(fetchedNews);\r\n    setLoading(false);\r\n  }, []);\r\n\r\n  const renderNewsItem = ({ item }) => (\r\n    <TouchableOpacity style={styles.newsItem}>\r\n      <Text style={styles.newsTitle}>{item.title}</Text>\r\n      <Text style={styles.newsDate}>{item.date}</Text>\r\n      <Text style={styles.newsContent}>{item.content}</Text>\r\n    </TouchableOpacity>\r\n  );\r\n\r\n  return (\r\n    <ScrollView style={styles.container}>\r\n      <Text style={styles.pageTitle}>Notícias</Text>\r\n      {loading ? (\r\n        <Text style={styles.loadingText}>Carregando notícias...</Text>\r\n      ) : (\r\n        <FlatList\r\n          data={newsData}\r\n          keyExtractor={(item) => item.id}\r\n          renderItem={renderNewsItem}\r\n        />\r\n      )}\r\n    </ScrollView>\r\n  );\r\n};\r\n\r\nexport default News;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,gBAAA;AAAA,OAAAC,QAAA;AAEnD,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,OAAOC,MAAM;AAAiB,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAE9B,IAAMC,IAAI,GAAG,SAAPA,IAAIA,CAAA,EAAS;EACjB,IAAAC,SAAA,GAAgCd,QAAQ,CAAC,EAAE,CAAC;IAAAe,UAAA,GAAAC,cAAA,CAAAF,SAAA;IAArCG,QAAQ,GAAAF,UAAA;IAAEG,WAAW,GAAAH,UAAA;EAC5B,IAAAI,UAAA,GAA8BnB,QAAQ,CAAC,IAAI,CAAC;IAAAoB,UAAA,GAAAJ,cAAA,CAAAG,UAAA;IAArCE,OAAO,GAAAD,UAAA;IAAEE,UAAU,GAAAF,UAAA;EAE1BnB,SAAS,CAAC,YAAM;IAEd,IAAMsB,WAAW,GAAG,CAClB;MACEC,EAAE,EAAE,GAAG;MACPC,KAAK,EAAE,gCAAgC;MACvCC,IAAI,EAAE,iBAAiB;MACvBC,OAAO,EAAE;IACX,CAAC,EACD;MACEH,EAAE,EAAE,GAAG;MACPC,KAAK,EAAE,gCAAgC;MACvCC,IAAI,EAAE,iBAAiB;MACvBC,OAAO,EAAE;IACX,CAAC,CAEF;IAEDT,WAAW,CAACK,WAAW,CAAC;IACxBD,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,IAAMM,cAAc,GAAG,SAAjBA,cAAcA,CAAAC,IAAA;IAAA,IAAMC,IAAI,GAAAD,IAAA,CAAJC,IAAI;IAAA,OAC5BlB,KAAA,CAACP,gBAAgB;MAAC0B,KAAK,EAAEvB,MAAM,CAACwB,QAAS;MAAAC,QAAA,GACvCvB,IAAA,CAACP,IAAI;QAAC4B,KAAK,EAAEvB,MAAM,CAAC0B,SAAU;QAAAD,QAAA,EAAEH,IAAI,CAACL;MAAK,CAAO,CAAC,EAClDf,IAAA,CAACP,IAAI;QAAC4B,KAAK,EAAEvB,MAAM,CAAC2B,QAAS;QAAAF,QAAA,EAAEH,IAAI,CAACJ;MAAI,CAAO,CAAC,EAChDhB,IAAA,CAACP,IAAI;QAAC4B,KAAK,EAAEvB,MAAM,CAAC4B,WAAY;QAAAH,QAAA,EAAEH,IAAI,CAACH;MAAO,CAAO,CAAC;IAAA,CACtC,CAAC;EAAA,CACpB;EAED,OACEf,KAAA,CAACR,UAAU;IAAC2B,KAAK,EAAEvB,MAAM,CAAC6B,SAAU;IAAAJ,QAAA,GAClCvB,IAAA,CAACP,IAAI;MAAC4B,KAAK,EAAEvB,MAAM,CAAC8B,SAAU;MAAAL,QAAA,EAAC;IAAQ,CAAM,CAAC,EAC7CZ,OAAO,GACNX,IAAA,CAACP,IAAI;MAAC4B,KAAK,EAAEvB,MAAM,CAAC+B,WAAY;MAAAN,QAAA,EAAC;IAAsB,CAAM,CAAC,GAE9DvB,IAAA,CAACJ,QAAQ;MACPkC,IAAI,EAAEvB,QAAS;MACfwB,YAAY,EAAE,SAAdA,YAAYA,CAAGX,IAAI;QAAA,OAAKA,IAAI,CAACN,EAAE;MAAA,CAAC;MAChCkB,UAAU,EAAEd;IAAe,CAC5B,CACF;EAAA,CACS,CAAC;AAEjB,CAAC;AAED,eAAef,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}