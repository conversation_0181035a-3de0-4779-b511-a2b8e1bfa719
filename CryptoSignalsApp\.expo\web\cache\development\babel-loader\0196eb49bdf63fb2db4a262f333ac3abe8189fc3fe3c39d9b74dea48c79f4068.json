{"ast": null, "code": "'use strict';\n\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nvar DELAY = 'DELAY';\nvar ERROR = 'ERROR';\nvar LONG_PRESS_DETECTED = 'LONG_PRESS_DETECTED';\nvar NOT_RESPONDER = 'NOT_RESPONDER';\nvar RESPONDER_ACTIVE_LONG_PRESS_START = 'RESPONDER_ACTIVE_LONG_PRESS_START';\nvar RESPONDER_ACTIVE_PRESS_START = 'RESPONDER_ACTIVE_PRESS_START';\nvar RESPONDER_INACTIVE_PRESS_START = 'RESPONDER_INACTIVE_PRESS_START';\nvar RESPONDER_GRANT = 'RESPONDER_GRANT';\nvar RESPONDER_RELEASE = 'RESPONDER_RELEASE';\nvar RESPONDER_TERMINATED = 'RESPONDER_TERMINATED';\nvar Transitions = Object.freeze({\n  NOT_RESPONDER: {\n    DELAY: ERROR,\n    RESPONDER_GRANT: RESPONDER_INACTIVE_PRESS_START,\n    RESPONDER_RELEASE: ERROR,\n    RESPONDER_TERMINATED: ERROR,\n    LONG_PRESS_DETECTED: ERROR\n  },\n  RESPONDER_INACTIVE_PRESS_START: {\n    DELAY: RESPONDER_ACTIVE_PRESS_START,\n    RESPONDER_GRANT: ERROR,\n    RESPONDER_RELEASE: NOT_RESPONDER,\n    RESPONDER_TERMINATED: NOT_RESPONDER,\n    LONG_PRESS_DETECTED: ERROR\n  },\n  RESPONDER_ACTIVE_PRESS_START: {\n    DELAY: ERROR,\n    RESPONDER_GRANT: ERROR,\n    RESPONDER_RELEASE: NOT_RESPONDER,\n    RESPONDER_TERMINATED: NOT_RESPONDER,\n    LONG_PRESS_DETECTED: RESPONDER_ACTIVE_LONG_PRESS_START\n  },\n  RESPONDER_ACTIVE_LONG_PRESS_START: {\n    DELAY: ERROR,\n    RESPONDER_GRANT: ERROR,\n    RESPONDER_RELEASE: NOT_RESPONDER,\n    RESPONDER_TERMINATED: NOT_RESPONDER,\n    LONG_PRESS_DETECTED: RESPONDER_ACTIVE_LONG_PRESS_START\n  },\n  ERROR: {\n    DELAY: NOT_RESPONDER,\n    RESPONDER_GRANT: RESPONDER_INACTIVE_PRESS_START,\n    RESPONDER_RELEASE: NOT_RESPONDER,\n    RESPONDER_TERMINATED: NOT_RESPONDER,\n    LONG_PRESS_DETECTED: NOT_RESPONDER\n  }\n});\nvar getElementRole = function getElementRole(element) {\n  return element.getAttribute('role');\n};\nvar getElementType = function getElementType(element) {\n  return element.tagName.toLowerCase();\n};\nvar isActiveSignal = function isActiveSignal(signal) {\n  return signal === RESPONDER_ACTIVE_PRESS_START || signal === RESPONDER_ACTIVE_LONG_PRESS_START;\n};\nvar isButtonRole = function isButtonRole(element) {\n  return getElementRole(element) === 'button';\n};\nvar isPressStartSignal = function isPressStartSignal(signal) {\n  return signal === RESPONDER_INACTIVE_PRESS_START || signal === RESPONDER_ACTIVE_PRESS_START || signal === RESPONDER_ACTIVE_LONG_PRESS_START;\n};\nvar isTerminalSignal = function isTerminalSignal(signal) {\n  return signal === RESPONDER_TERMINATED || signal === RESPONDER_RELEASE;\n};\nvar isValidKeyPress = function isValidKeyPress(event) {\n  var key = event.key,\n    target = event.target;\n  var isSpacebar = key === ' ' || key === 'Spacebar';\n  var isButtonish = getElementType(target) === 'button' || isButtonRole(target);\n  return key === 'Enter' || isSpacebar && isButtonish;\n};\nvar DEFAULT_LONG_PRESS_DELAY_MS = 450;\nvar DEFAULT_PRESS_DELAY_MS = 50;\nvar PressResponder = function () {\n  function PressResponder(config) {\n    _classCallCheck(this, PressResponder);\n    this._eventHandlers = null;\n    this._isPointerTouch = false;\n    this._longPressDelayTimeout = null;\n    this._longPressDispatched = false;\n    this._pressDelayTimeout = null;\n    this._pressOutDelayTimeout = null;\n    this._touchState = NOT_RESPONDER;\n    this._responderElement = null;\n    this.configure(config);\n  }\n  return _createClass(PressResponder, [{\n    key: \"configure\",\n    value: function configure(config) {\n      this._config = config;\n    }\n  }, {\n    key: \"reset\",\n    value: function reset() {\n      this._cancelLongPressDelayTimeout();\n      this._cancelPressDelayTimeout();\n      this._cancelPressOutDelayTimeout();\n    }\n  }, {\n    key: \"getEventHandlers\",\n    value: function getEventHandlers() {\n      if (this._eventHandlers == null) {\n        this._eventHandlers = this._createEventHandlers();\n      }\n      return this._eventHandlers;\n    }\n  }, {\n    key: \"_createEventHandlers\",\n    value: function _createEventHandlers() {\n      var _this = this;\n      var start = function start(event, shouldDelay) {\n        event.persist();\n        _this._cancelPressOutDelayTimeout();\n        _this._longPressDispatched = false;\n        _this._selectionTerminated = false;\n        _this._touchState = NOT_RESPONDER;\n        _this._isPointerTouch = event.nativeEvent.type === 'touchstart';\n        _this._receiveSignal(RESPONDER_GRANT, event);\n        var delayPressStart = normalizeDelay(_this._config.delayPressStart, 0, DEFAULT_PRESS_DELAY_MS);\n        if (shouldDelay !== false && delayPressStart > 0) {\n          _this._pressDelayTimeout = setTimeout(function () {\n            _this._receiveSignal(DELAY, event);\n          }, delayPressStart);\n        } else {\n          _this._receiveSignal(DELAY, event);\n        }\n        var delayLongPress = normalizeDelay(_this._config.delayLongPress, 10, DEFAULT_LONG_PRESS_DELAY_MS);\n        _this._longPressDelayTimeout = setTimeout(function () {\n          _this._handleLongPress(event);\n        }, delayLongPress + delayPressStart);\n      };\n      var end = function end(event) {\n        _this._receiveSignal(RESPONDER_RELEASE, event);\n      };\n      var _keyupHandler = function keyupHandler(event) {\n        var onPress = _this._config.onPress;\n        var target = event.target;\n        if (_this._touchState !== NOT_RESPONDER && isValidKeyPress(event)) {\n          end(event);\n          document.removeEventListener('keyup', _keyupHandler);\n          var role = target.getAttribute('role');\n          var elementType = getElementType(target);\n          var isNativeInteractiveElement = role === 'link' || elementType === 'a' || elementType === 'button' || elementType === 'input' || elementType === 'select' || elementType === 'textarea';\n          var isActiveElement = _this._responderElement === target;\n          if (onPress != null && !isNativeInteractiveElement && isActiveElement) {\n            onPress(event);\n          }\n          _this._responderElement = null;\n        }\n      };\n      return {\n        onStartShouldSetResponder: function onStartShouldSetResponder(event) {\n          var disabled = _this._config.disabled;\n          if (disabled && isButtonRole(event.currentTarget)) {\n            event.stopPropagation();\n          }\n          if (disabled == null) {\n            return true;\n          }\n          return !disabled;\n        },\n        onKeyDown: function onKeyDown(event) {\n          var disabled = _this._config.disabled;\n          var key = event.key,\n            target = event.target;\n          if (!disabled && isValidKeyPress(event)) {\n            if (_this._touchState === NOT_RESPONDER) {\n              start(event, false);\n              _this._responderElement = target;\n              document.addEventListener('keyup', _keyupHandler);\n            }\n            var isSpacebarKey = key === ' ' || key === 'Spacebar';\n            var role = getElementRole(target);\n            var isButtonLikeRole = role === 'button' || role === 'menuitem';\n            if (isSpacebarKey && isButtonLikeRole && getElementType(target) !== 'button') {\n              event.preventDefault();\n            }\n            event.stopPropagation();\n          }\n        },\n        onResponderGrant: function onResponderGrant(event) {\n          return start(event);\n        },\n        onResponderMove: function onResponderMove(event) {\n          if (_this._config.onPressMove != null) {\n            _this._config.onPressMove(event);\n          }\n          var touch = getTouchFromResponderEvent(event);\n          if (_this._touchActivatePosition != null) {\n            var deltaX = _this._touchActivatePosition.pageX - touch.pageX;\n            var deltaY = _this._touchActivatePosition.pageY - touch.pageY;\n            if (Math.hypot(deltaX, deltaY) > 10) {\n              _this._cancelLongPressDelayTimeout();\n            }\n          }\n        },\n        onResponderRelease: function onResponderRelease(event) {\n          return end(event);\n        },\n        onResponderTerminate: function onResponderTerminate(event) {\n          if (event.nativeEvent.type === 'selectionchange') {\n            _this._selectionTerminated = true;\n          }\n          _this._receiveSignal(RESPONDER_TERMINATED, event);\n        },\n        onResponderTerminationRequest: function onResponderTerminationRequest(event) {\n          var _this$_config = _this._config,\n            cancelable = _this$_config.cancelable,\n            disabled = _this$_config.disabled,\n            onLongPress = _this$_config.onLongPress;\n          if (!disabled && onLongPress != null && _this._isPointerTouch && event.nativeEvent.type === 'contextmenu') {\n            return false;\n          }\n          if (cancelable == null) {\n            return true;\n          }\n          return cancelable;\n        },\n        onClick: function onClick(event) {\n          var _this$_config2 = _this._config,\n            disabled = _this$_config2.disabled,\n            onPress = _this$_config2.onPress;\n          if (!disabled) {\n            event.stopPropagation();\n            if (_this._longPressDispatched || _this._selectionTerminated) {\n              event.preventDefault();\n            } else if (onPress != null && event.altKey === false) {\n              onPress(event);\n            }\n          } else {\n            if (isButtonRole(event.currentTarget)) {\n              event.stopPropagation();\n            }\n          }\n        },\n        onContextMenu: function onContextMenu(event) {\n          var _this$_config3 = _this._config,\n            disabled = _this$_config3.disabled,\n            onLongPress = _this$_config3.onLongPress;\n          if (!disabled) {\n            if (onLongPress != null && _this._isPointerTouch && !event.defaultPrevented) {\n              event.preventDefault();\n              event.stopPropagation();\n            }\n          } else {\n            if (isButtonRole(event.currentTarget)) {\n              event.stopPropagation();\n            }\n          }\n        }\n      };\n    }\n  }, {\n    key: \"_receiveSignal\",\n    value: function _receiveSignal(signal, event) {\n      var prevState = this._touchState;\n      var nextState = null;\n      if (Transitions[prevState] != null) {\n        nextState = Transitions[prevState][signal];\n      }\n      if (this._touchState === NOT_RESPONDER && signal === RESPONDER_RELEASE) {\n        return;\n      }\n      if (nextState == null || nextState === ERROR) {\n        console.error(\"PressResponder: Invalid signal \" + signal + \" for state \" + prevState + \" on responder\");\n      } else if (prevState !== nextState) {\n        this._performTransitionSideEffects(prevState, nextState, signal, event);\n        this._touchState = nextState;\n      }\n    }\n  }, {\n    key: \"_performTransitionSideEffects\",\n    value: function _performTransitionSideEffects(prevState, nextState, signal, event) {\n      var _this2 = this;\n      if (isTerminalSignal(signal)) {\n        setTimeout(function () {\n          _this2._isPointerTouch = false;\n        }, 0);\n        this._touchActivatePosition = null;\n        this._cancelLongPressDelayTimeout();\n      }\n      if (isPressStartSignal(prevState) && signal === LONG_PRESS_DETECTED) {\n        var onLongPress = this._config.onLongPress;\n        if (onLongPress != null && event.nativeEvent.key == null) {\n          onLongPress(event);\n          this._longPressDispatched = true;\n        }\n      }\n      var isPrevActive = isActiveSignal(prevState);\n      var isNextActive = isActiveSignal(nextState);\n      if (!isPrevActive && isNextActive) {\n        this._activate(event);\n      } else if (isPrevActive && !isNextActive) {\n        this._deactivate(event);\n      }\n      if (isPressStartSignal(prevState) && signal === RESPONDER_RELEASE) {\n        var _this$_config4 = this._config,\n          _onLongPress = _this$_config4.onLongPress,\n          onPress = _this$_config4.onPress;\n        if (onPress != null) {\n          var isPressCanceledByLongPress = _onLongPress != null && prevState === RESPONDER_ACTIVE_LONG_PRESS_START;\n          if (!isPressCanceledByLongPress) {\n            if (!isNextActive && !isPrevActive) {\n              this._activate(event);\n              this._deactivate(event);\n            }\n          }\n        }\n      }\n      this._cancelPressDelayTimeout();\n    }\n  }, {\n    key: \"_activate\",\n    value: function _activate(event) {\n      var _this$_config5 = this._config,\n        onPressChange = _this$_config5.onPressChange,\n        onPressStart = _this$_config5.onPressStart;\n      var touch = getTouchFromResponderEvent(event);\n      this._touchActivatePosition = {\n        pageX: touch.pageX,\n        pageY: touch.pageY\n      };\n      if (onPressStart != null) {\n        onPressStart(event);\n      }\n      if (onPressChange != null) {\n        onPressChange(true);\n      }\n    }\n  }, {\n    key: \"_deactivate\",\n    value: function _deactivate(event) {\n      var _this$_config6 = this._config,\n        onPressChange = _this$_config6.onPressChange,\n        onPressEnd = _this$_config6.onPressEnd;\n      function end() {\n        if (onPressEnd != null) {\n          onPressEnd(event);\n        }\n        if (onPressChange != null) {\n          onPressChange(false);\n        }\n      }\n      var delayPressEnd = normalizeDelay(this._config.delayPressEnd);\n      if (delayPressEnd > 0) {\n        this._pressOutDelayTimeout = setTimeout(function () {\n          end();\n        }, delayPressEnd);\n      } else {\n        end();\n      }\n    }\n  }, {\n    key: \"_handleLongPress\",\n    value: function _handleLongPress(event) {\n      if (this._touchState === RESPONDER_ACTIVE_PRESS_START || this._touchState === RESPONDER_ACTIVE_LONG_PRESS_START) {\n        this._receiveSignal(LONG_PRESS_DETECTED, event);\n      }\n    }\n  }, {\n    key: \"_cancelLongPressDelayTimeout\",\n    value: function _cancelLongPressDelayTimeout() {\n      if (this._longPressDelayTimeout != null) {\n        clearTimeout(this._longPressDelayTimeout);\n        this._longPressDelayTimeout = null;\n      }\n    }\n  }, {\n    key: \"_cancelPressDelayTimeout\",\n    value: function _cancelPressDelayTimeout() {\n      if (this._pressDelayTimeout != null) {\n        clearTimeout(this._pressDelayTimeout);\n        this._pressDelayTimeout = null;\n      }\n    }\n  }, {\n    key: \"_cancelPressOutDelayTimeout\",\n    value: function _cancelPressOutDelayTimeout() {\n      if (this._pressOutDelayTimeout != null) {\n        clearTimeout(this._pressOutDelayTimeout);\n        this._pressOutDelayTimeout = null;\n      }\n    }\n  }]);\n}();\nexport { PressResponder as default };\nfunction normalizeDelay(delay, min, fallback) {\n  if (min === void 0) {\n    min = 0;\n  }\n  if (fallback === void 0) {\n    fallback = 0;\n  }\n  return Math.max(min, delay !== null && delay !== void 0 ? delay : fallback);\n}\nfunction getTouchFromResponderEvent(event) {\n  var _event$nativeEvent = event.nativeEvent,\n    changedTouches = _event$nativeEvent.changedTouches,\n    touches = _event$nativeEvent.touches;\n  if (touches != null && touches.length > 0) {\n    return touches[0];\n  }\n  if (changedTouches != null && changedTouches.length > 0) {\n    return changedTouches[0];\n  }\n  return event.nativeEvent;\n}", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "DELAY", "ERROR", "LONG_PRESS_DETECTED", "NOT_RESPONDER", "RESPONDER_ACTIVE_LONG_PRESS_START", "RESPONDER_ACTIVE_PRESS_START", "RESPONDER_INACTIVE_PRESS_START", "RESPONDER_GRANT", "RESPONDER_RELEASE", "RESPONDER_TERMINATED", "Transitions", "Object", "freeze", "getElementRole", "element", "getAttribute", "getElementType", "tagName", "toLowerCase", "isActiveSignal", "signal", "isButtonRole", "isPressStartSignal", "isTerminalSignal", "isValidKeyPress", "event", "key", "target", "isSpacebar", "is<PERSON><PERSON><PERSON><PERSON>", "DEFAULT_LONG_PRESS_DELAY_MS", "DEFAULT_PRESS_DELAY_MS", "PressResponder", "config", "_eventHandlers", "_isPointerTouch", "_longPressDelayTimeout", "_longPressDispatched", "_pressDelayTimeout", "_pressOutDelayTimeout", "_touchState", "_responderElement", "configure", "value", "_config", "reset", "_cancelLongPressDelayTimeout", "_cancelPressDelayTimeout", "_cancelPressOutDelayTimeout", "getEventHandlers", "_createEventHandlers", "_this", "start", "<PERSON><PERSON><PERSON><PERSON>", "persist", "_selectionTerminated", "nativeEvent", "type", "_receiveSignal", "delayPressStart", "normalizeDelay", "setTimeout", "delayLongPress", "_handleLongPress", "end", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onPress", "document", "removeEventListener", "role", "elementType", "isNativeInteractiveElement", "isActiveElement", "onStartShouldSetResponder", "disabled", "currentTarget", "stopPropagation", "onKeyDown", "addEventListener", "isSpacebarKey", "isButtonLikeRole", "preventDefault", "onResponderGrant", "onResponderMove", "onPressMove", "touch", "getTouchFromResponderEvent", "_touchActivatePosition", "deltaX", "pageX", "deltaY", "pageY", "Math", "hypot", "onResponderRelease", "onResponderTerminate", "onResponderTerminationRequest", "_this$_config", "cancelable", "onLongPress", "onClick", "_this$_config2", "altKey", "onContextMenu", "_this$_config3", "defaultPrevented", "prevState", "nextState", "console", "error", "_performTransitionSideEffects", "_this2", "isPrevActive", "isNextActive", "_activate", "_deactivate", "_this$_config4", "_onLongPress", "isPressCanceledByLongPress", "_this$_config5", "onPressChange", "onPressStart", "_this$_config6", "onPressEnd", "delayPressEnd", "clearTimeout", "default", "delay", "min", "fallback", "max", "_event$nativeEvent", "changedTouches", "touches", "length"], "sources": ["E:/CryptoSignalsApp/node_modules/react-native-web/dist/modules/usePressEvents/PressResponder.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use strict';\n\nvar DELAY = 'DELAY';\nvar ERROR = 'ERROR';\nvar LONG_PRESS_DETECTED = 'LONG_PRESS_DETECTED';\nvar NOT_RESPONDER = 'NOT_RESPONDER';\nvar RESPONDER_ACTIVE_LONG_PRESS_START = 'RESPONDER_ACTIVE_LONG_PRESS_START';\nvar RESPONDER_ACTIVE_PRESS_START = 'RESPONDER_ACTIVE_PRESS_START';\nvar RESPONDER_INACTIVE_PRESS_START = 'RESPONDER_INACTIVE_PRESS_START';\nvar RESPONDER_GRANT = 'RESPONDER_GRANT';\nvar RESPONDER_RELEASE = 'RESPONDER_RELEASE';\nvar RESPONDER_TERMINATED = 'RESPONDER_TERMINATED';\nvar Transitions = Object.freeze({\n  NOT_RESPONDER: {\n    DELAY: ERROR,\n    RESPONDER_GRANT: RESPONDER_INACTIVE_PRESS_START,\n    RESPONDER_RELEASE: ERROR,\n    RESPONDER_TERMINATED: ERROR,\n    LONG_PRESS_DETECTED: ERROR\n  },\n  RESPONDER_INACTIVE_PRESS_START: {\n    DELAY: RESPONDER_ACTIVE_PRESS_START,\n    RESPONDER_GRANT: ERROR,\n    RESPONDER_RELEASE: NOT_RESPONDER,\n    RESPONDER_TERMINATED: NOT_RESPONDER,\n    LONG_PRESS_DETECTED: ERROR\n  },\n  RESPONDER_ACTIVE_PRESS_START: {\n    DELAY: ERROR,\n    RESPONDER_GRANT: ERROR,\n    RESPONDER_RELEASE: NOT_RESPONDER,\n    RESPONDER_TERMINATED: NOT_RESPONDER,\n    LONG_PRESS_DETECTED: RESPONDER_ACTIVE_LONG_PRESS_START\n  },\n  RESPONDER_ACTIVE_LONG_PRESS_START: {\n    DELAY: ERROR,\n    RESPONDER_GRANT: ERROR,\n    RESPONDER_RELEASE: NOT_RESPONDER,\n    RESPONDER_TERMINATED: NOT_RESPONDER,\n    LONG_PRESS_DETECTED: RESPONDER_ACTIVE_LONG_PRESS_START\n  },\n  ERROR: {\n    DELAY: NOT_RESPONDER,\n    RESPONDER_GRANT: RESPONDER_INACTIVE_PRESS_START,\n    RESPONDER_RELEASE: NOT_RESPONDER,\n    RESPONDER_TERMINATED: NOT_RESPONDER,\n    LONG_PRESS_DETECTED: NOT_RESPONDER\n  }\n});\nvar getElementRole = element => element.getAttribute('role');\nvar getElementType = element => element.tagName.toLowerCase();\nvar isActiveSignal = signal => signal === RESPONDER_ACTIVE_PRESS_START || signal === RESPONDER_ACTIVE_LONG_PRESS_START;\nvar isButtonRole = element => getElementRole(element) === 'button';\nvar isPressStartSignal = signal => signal === RESPONDER_INACTIVE_PRESS_START || signal === RESPONDER_ACTIVE_PRESS_START || signal === RESPONDER_ACTIVE_LONG_PRESS_START;\nvar isTerminalSignal = signal => signal === RESPONDER_TERMINATED || signal === RESPONDER_RELEASE;\nvar isValidKeyPress = event => {\n  var key = event.key,\n    target = event.target;\n  var isSpacebar = key === ' ' || key === 'Spacebar';\n  var isButtonish = getElementType(target) === 'button' || isButtonRole(target);\n  return key === 'Enter' || isSpacebar && isButtonish;\n};\nvar DEFAULT_LONG_PRESS_DELAY_MS = 450; // 500 - 50\nvar DEFAULT_PRESS_DELAY_MS = 50;\n\n/**\n * =========================== PressResponder Tutorial ===========================\n *\n * The `PressResponder` class helps you create press interactions by analyzing the\n * geometry of elements and observing when another responder (e.g. ScrollView)\n * has stolen the touch lock. It offers hooks for your component to provide\n * interaction feedback to the user:\n *\n * - When a press has activated (e.g. highlight an element)\n * - When a press has deactivated (e.g. un-highlight an element)\n * - When a press sould trigger an action, meaning it activated and deactivated\n *   while within the geometry of the element without the lock being stolen.\n *\n * A high quality interaction isn't as simple as you might think. There should\n * be a slight delay before activation. Moving your finger beyond an element's\n * bounds should trigger deactivation, but moving the same finger back within an\n * element's bounds should trigger reactivation.\n *\n * In order to use `PressResponder`, do the following:\n *\n *     const pressResponder = new PressResponder(config);\n *\n * 2. Choose the rendered component who should collect the press events. On that\n *    element, spread `pressability.getEventHandlers()` into its props.\n *\n *    return (\n *      <View {...this.state.pressResponder.getEventHandlers()} />\n *    );\n *\n * 3. Reset `PressResponder` when your component unmounts.\n *\n *    componentWillUnmount() {\n *      this.state.pressResponder.reset();\n *    }\n *\n * ==================== Implementation Details ====================\n *\n * `PressResponder` only assumes that there exists a `HitRect` node. The `PressRect`\n * is an abstract box that is extended beyond the `HitRect`.\n *\n * # Geometry\n *\n *  ┌────────────────────────┐\n *  │  ┌──────────────────┐  │ - Presses start anywhere within `HitRect`.\n *  │  │  ┌────────────┐  │  │\n *  │  │  │ VisualRect │  │  │\n *  │  │  └────────────┘  │  │ - When pressed down for sufficient amount of time\n *  │  │    HitRect       │  │   before letting up, `VisualRect` activates.\n *  │  └──────────────────┘  │\n *  │       Out Region   o   │\n *  └────────────────────│───┘\n *                       └────── When the press is released outside the `HitRect`,\n *                               the responder is NOT eligible for a \"press\".\n *\n * # State Machine\n *\n * ┌───────────────┐ ◀──── RESPONDER_RELEASE\n * │ NOT_RESPONDER │\n * └───┬───────────┘ ◀──── RESPONDER_TERMINATED\n *     │\n *     │ RESPONDER_GRANT (HitRect)\n *     │\n *     ▼\n * ┌─────────────────────┐          ┌───────────────────┐              ┌───────────────────┐\n * │ RESPONDER_INACTIVE_ │  DELAY   │ RESPONDER_ACTIVE_ │  T + DELAY   │ RESPONDER_ACTIVE_ │\n * │ PRESS_START         ├────────▶ │ PRESS_START       ├────────────▶ │ LONG_PRESS_START  │\n * └─────────────────────┘          └───────────────────┘              └───────────────────┘\n *\n * T + DELAY => LONG_PRESS_DELAY + DELAY\n *\n * Not drawn are the side effects of each transition. The most important side\n * effect is the invocation of `onLongPress`. Only when the browser produces a\n * `click` event is `onPress` invoked.\n */\nexport default class PressResponder {\n  constructor(config) {\n    this._eventHandlers = null;\n    this._isPointerTouch = false;\n    this._longPressDelayTimeout = null;\n    this._longPressDispatched = false;\n    this._pressDelayTimeout = null;\n    this._pressOutDelayTimeout = null;\n    this._touchState = NOT_RESPONDER;\n    this._responderElement = null;\n    this.configure(config);\n  }\n  configure(config) {\n    this._config = config;\n  }\n\n  /**\n   * Resets any pending timers. This should be called on unmount.\n   */\n  reset() {\n    this._cancelLongPressDelayTimeout();\n    this._cancelPressDelayTimeout();\n    this._cancelPressOutDelayTimeout();\n  }\n\n  /**\n   * Returns a set of props to spread into the interactive element.\n   */\n  getEventHandlers() {\n    if (this._eventHandlers == null) {\n      this._eventHandlers = this._createEventHandlers();\n    }\n    return this._eventHandlers;\n  }\n  _createEventHandlers() {\n    var start = (event, shouldDelay) => {\n      event.persist();\n      this._cancelPressOutDelayTimeout();\n      this._longPressDispatched = false;\n      this._selectionTerminated = false;\n      this._touchState = NOT_RESPONDER;\n      this._isPointerTouch = event.nativeEvent.type === 'touchstart';\n      this._receiveSignal(RESPONDER_GRANT, event);\n      var delayPressStart = normalizeDelay(this._config.delayPressStart, 0, DEFAULT_PRESS_DELAY_MS);\n      if (shouldDelay !== false && delayPressStart > 0) {\n        this._pressDelayTimeout = setTimeout(() => {\n          this._receiveSignal(DELAY, event);\n        }, delayPressStart);\n      } else {\n        this._receiveSignal(DELAY, event);\n      }\n      var delayLongPress = normalizeDelay(this._config.delayLongPress, 10, DEFAULT_LONG_PRESS_DELAY_MS);\n      this._longPressDelayTimeout = setTimeout(() => {\n        this._handleLongPress(event);\n      }, delayLongPress + delayPressStart);\n    };\n    var end = event => {\n      this._receiveSignal(RESPONDER_RELEASE, event);\n    };\n    var keyupHandler = event => {\n      var onPress = this._config.onPress;\n      var target = event.target;\n      if (this._touchState !== NOT_RESPONDER && isValidKeyPress(event)) {\n        end(event);\n        document.removeEventListener('keyup', keyupHandler);\n        var role = target.getAttribute('role');\n        var elementType = getElementType(target);\n        var isNativeInteractiveElement = role === 'link' || elementType === 'a' || elementType === 'button' || elementType === 'input' || elementType === 'select' || elementType === 'textarea';\n        var isActiveElement = this._responderElement === target;\n        if (onPress != null && !isNativeInteractiveElement && isActiveElement) {\n          onPress(event);\n        }\n        this._responderElement = null;\n      }\n    };\n    return {\n      onStartShouldSetResponder: event => {\n        var disabled = this._config.disabled;\n        if (disabled && isButtonRole(event.currentTarget)) {\n          event.stopPropagation();\n        }\n        if (disabled == null) {\n          return true;\n        }\n        return !disabled;\n      },\n      onKeyDown: event => {\n        var disabled = this._config.disabled;\n        var key = event.key,\n          target = event.target;\n        if (!disabled && isValidKeyPress(event)) {\n          if (this._touchState === NOT_RESPONDER) {\n            start(event, false);\n            this._responderElement = target;\n            // Listen to 'keyup' on document to account for situations where\n            // focus is moved to another element during 'keydown'.\n            document.addEventListener('keyup', keyupHandler);\n          }\n          var isSpacebarKey = key === ' ' || key === 'Spacebar';\n          var role = getElementRole(target);\n          var isButtonLikeRole = role === 'button' || role === 'menuitem';\n          if (isSpacebarKey && isButtonLikeRole && getElementType(target) !== 'button') {\n            // Prevent spacebar scrolling the window if using non-native button\n            event.preventDefault();\n          }\n          event.stopPropagation();\n        }\n      },\n      onResponderGrant: event => start(event),\n      onResponderMove: event => {\n        if (this._config.onPressMove != null) {\n          this._config.onPressMove(event);\n        }\n        var touch = getTouchFromResponderEvent(event);\n        if (this._touchActivatePosition != null) {\n          var deltaX = this._touchActivatePosition.pageX - touch.pageX;\n          var deltaY = this._touchActivatePosition.pageY - touch.pageY;\n          if (Math.hypot(deltaX, deltaY) > 10) {\n            this._cancelLongPressDelayTimeout();\n          }\n        }\n      },\n      onResponderRelease: event => end(event),\n      onResponderTerminate: event => {\n        if (event.nativeEvent.type === 'selectionchange') {\n          this._selectionTerminated = true;\n        }\n        this._receiveSignal(RESPONDER_TERMINATED, event);\n      },\n      onResponderTerminationRequest: event => {\n        var _this$_config = this._config,\n          cancelable = _this$_config.cancelable,\n          disabled = _this$_config.disabled,\n          onLongPress = _this$_config.onLongPress;\n        // If `onLongPress` is provided, don't terminate on `contextmenu` as default\n        // behavior will be prevented for non-mouse pointers.\n        if (!disabled && onLongPress != null && this._isPointerTouch && event.nativeEvent.type === 'contextmenu') {\n          return false;\n        }\n        if (cancelable == null) {\n          return true;\n        }\n        return cancelable;\n      },\n      // NOTE: this diverges from react-native in 3 significant ways:\n      // * The `onPress` callback is not connected to the responder system (the native\n      //  `click` event must be used but is dispatched in many scenarios where no pointers\n      //   are on the screen.) Therefore, it's possible for `onPress` to be called without\n      //   `onPress{Start,End}` being called first.\n      // * The `onPress` callback is only be called on the first ancestor of the native\n      //   `click` target that is using the PressResponder.\n      // * The event's `nativeEvent` is a `MouseEvent` not a `TouchEvent`.\n      onClick: event => {\n        var _this$_config2 = this._config,\n          disabled = _this$_config2.disabled,\n          onPress = _this$_config2.onPress;\n        if (!disabled) {\n          // If long press dispatched, cancel default click behavior.\n          // If the responder terminated because text was selected during the gesture,\n          // cancel the default click behavior.\n          event.stopPropagation();\n          if (this._longPressDispatched || this._selectionTerminated) {\n            event.preventDefault();\n          } else if (onPress != null && event.altKey === false) {\n            onPress(event);\n          }\n        } else {\n          if (isButtonRole(event.currentTarget)) {\n            event.stopPropagation();\n          }\n        }\n      },\n      // If `onLongPress` is provided and a touch pointer is being used, prevent the\n      // default context menu from opening.\n      onContextMenu: event => {\n        var _this$_config3 = this._config,\n          disabled = _this$_config3.disabled,\n          onLongPress = _this$_config3.onLongPress;\n        if (!disabled) {\n          if (onLongPress != null && this._isPointerTouch && !event.defaultPrevented) {\n            event.preventDefault();\n            event.stopPropagation();\n          }\n        } else {\n          if (isButtonRole(event.currentTarget)) {\n            event.stopPropagation();\n          }\n        }\n      }\n    };\n  }\n\n  /**\n   * Receives a state machine signal, performs side effects of the transition\n   * and stores the new state. Validates the transition as well.\n   */\n  _receiveSignal(signal, event) {\n    var prevState = this._touchState;\n    var nextState = null;\n    if (Transitions[prevState] != null) {\n      nextState = Transitions[prevState][signal];\n    }\n    if (this._touchState === NOT_RESPONDER && signal === RESPONDER_RELEASE) {\n      return;\n    }\n    if (nextState == null || nextState === ERROR) {\n      console.error(\"PressResponder: Invalid signal \" + signal + \" for state \" + prevState + \" on responder\");\n    } else if (prevState !== nextState) {\n      this._performTransitionSideEffects(prevState, nextState, signal, event);\n      this._touchState = nextState;\n    }\n  }\n\n  /**\n   * Performs a transition between touchable states and identify any activations\n   * or deactivations (and callback invocations).\n   */\n  _performTransitionSideEffects(prevState, nextState, signal, event) {\n    if (isTerminalSignal(signal)) {\n      // Pressable suppression of contextmenu on windows.\n      // On Windows, the contextmenu is displayed after pointerup.\n      // https://github.com/necolas/react-native-web/issues/2296\n      setTimeout(() => {\n        this._isPointerTouch = false;\n      }, 0);\n      this._touchActivatePosition = null;\n      this._cancelLongPressDelayTimeout();\n    }\n    if (isPressStartSignal(prevState) && signal === LONG_PRESS_DETECTED) {\n      var onLongPress = this._config.onLongPress;\n      // Long press is not supported for keyboards because 'click' can be dispatched\n      // immediately (and multiple times) after 'keydown'.\n      if (onLongPress != null && event.nativeEvent.key == null) {\n        onLongPress(event);\n        this._longPressDispatched = true;\n      }\n    }\n    var isPrevActive = isActiveSignal(prevState);\n    var isNextActive = isActiveSignal(nextState);\n    if (!isPrevActive && isNextActive) {\n      this._activate(event);\n    } else if (isPrevActive && !isNextActive) {\n      this._deactivate(event);\n    }\n    if (isPressStartSignal(prevState) && signal === RESPONDER_RELEASE) {\n      var _this$_config4 = this._config,\n        _onLongPress = _this$_config4.onLongPress,\n        onPress = _this$_config4.onPress;\n      if (onPress != null) {\n        var isPressCanceledByLongPress = _onLongPress != null && prevState === RESPONDER_ACTIVE_LONG_PRESS_START;\n        if (!isPressCanceledByLongPress) {\n          // If we never activated (due to delays), activate and deactivate now.\n          if (!isNextActive && !isPrevActive) {\n            this._activate(event);\n            this._deactivate(event);\n          }\n        }\n      }\n    }\n    this._cancelPressDelayTimeout();\n  }\n  _activate(event) {\n    var _this$_config5 = this._config,\n      onPressChange = _this$_config5.onPressChange,\n      onPressStart = _this$_config5.onPressStart;\n    var touch = getTouchFromResponderEvent(event);\n    this._touchActivatePosition = {\n      pageX: touch.pageX,\n      pageY: touch.pageY\n    };\n    if (onPressStart != null) {\n      onPressStart(event);\n    }\n    if (onPressChange != null) {\n      onPressChange(true);\n    }\n  }\n  _deactivate(event) {\n    var _this$_config6 = this._config,\n      onPressChange = _this$_config6.onPressChange,\n      onPressEnd = _this$_config6.onPressEnd;\n    function end() {\n      if (onPressEnd != null) {\n        onPressEnd(event);\n      }\n      if (onPressChange != null) {\n        onPressChange(false);\n      }\n    }\n    var delayPressEnd = normalizeDelay(this._config.delayPressEnd);\n    if (delayPressEnd > 0) {\n      this._pressOutDelayTimeout = setTimeout(() => {\n        end();\n      }, delayPressEnd);\n    } else {\n      end();\n    }\n  }\n  _handleLongPress(event) {\n    if (this._touchState === RESPONDER_ACTIVE_PRESS_START || this._touchState === RESPONDER_ACTIVE_LONG_PRESS_START) {\n      this._receiveSignal(LONG_PRESS_DETECTED, event);\n    }\n  }\n  _cancelLongPressDelayTimeout() {\n    if (this._longPressDelayTimeout != null) {\n      clearTimeout(this._longPressDelayTimeout);\n      this._longPressDelayTimeout = null;\n    }\n  }\n  _cancelPressDelayTimeout() {\n    if (this._pressDelayTimeout != null) {\n      clearTimeout(this._pressDelayTimeout);\n      this._pressDelayTimeout = null;\n    }\n  }\n  _cancelPressOutDelayTimeout() {\n    if (this._pressOutDelayTimeout != null) {\n      clearTimeout(this._pressOutDelayTimeout);\n      this._pressOutDelayTimeout = null;\n    }\n  }\n}\nfunction normalizeDelay(delay, min, fallback) {\n  if (min === void 0) {\n    min = 0;\n  }\n  if (fallback === void 0) {\n    fallback = 0;\n  }\n  return Math.max(min, delay !== null && delay !== void 0 ? delay : fallback);\n}\nfunction getTouchFromResponderEvent(event) {\n  var _event$nativeEvent = event.nativeEvent,\n    changedTouches = _event$nativeEvent.changedTouches,\n    touches = _event$nativeEvent.touches;\n  if (touches != null && touches.length > 0) {\n    return touches[0];\n  }\n  if (changedTouches != null && changedTouches.length > 0) {\n    return changedTouches[0];\n  }\n  return event.nativeEvent;\n}"], "mappings": "AAUA,YAAY;;AAAC,OAAAA,eAAA;AAAA,OAAAC,YAAA;AAEb,IAAIC,KAAK,GAAG,OAAO;AACnB,IAAIC,KAAK,GAAG,OAAO;AACnB,IAAIC,mBAAmB,GAAG,qBAAqB;AAC/C,IAAIC,aAAa,GAAG,eAAe;AACnC,IAAIC,iCAAiC,GAAG,mCAAmC;AAC3E,IAAIC,4BAA4B,GAAG,8BAA8B;AACjE,IAAIC,8BAA8B,GAAG,gCAAgC;AACrE,IAAIC,eAAe,GAAG,iBAAiB;AACvC,IAAIC,iBAAiB,GAAG,mBAAmB;AAC3C,IAAIC,oBAAoB,GAAG,sBAAsB;AACjD,IAAIC,WAAW,GAAGC,MAAM,CAACC,MAAM,CAAC;EAC9BT,aAAa,EAAE;IACbH,KAAK,EAAEC,KAAK;IACZM,eAAe,EAAED,8BAA8B;IAC/CE,iBAAiB,EAAEP,KAAK;IACxBQ,oBAAoB,EAAER,KAAK;IAC3BC,mBAAmB,EAAED;EACvB,CAAC;EACDK,8BAA8B,EAAE;IAC9BN,KAAK,EAAEK,4BAA4B;IACnCE,eAAe,EAAEN,KAAK;IACtBO,iBAAiB,EAAEL,aAAa;IAChCM,oBAAoB,EAAEN,aAAa;IACnCD,mBAAmB,EAAED;EACvB,CAAC;EACDI,4BAA4B,EAAE;IAC5BL,KAAK,EAAEC,KAAK;IACZM,eAAe,EAAEN,KAAK;IACtBO,iBAAiB,EAAEL,aAAa;IAChCM,oBAAoB,EAAEN,aAAa;IACnCD,mBAAmB,EAAEE;EACvB,CAAC;EACDA,iCAAiC,EAAE;IACjCJ,KAAK,EAAEC,KAAK;IACZM,eAAe,EAAEN,KAAK;IACtBO,iBAAiB,EAAEL,aAAa;IAChCM,oBAAoB,EAAEN,aAAa;IACnCD,mBAAmB,EAAEE;EACvB,CAAC;EACDH,KAAK,EAAE;IACLD,KAAK,EAAEG,aAAa;IACpBI,eAAe,EAAED,8BAA8B;IAC/CE,iBAAiB,EAAEL,aAAa;IAChCM,oBAAoB,EAAEN,aAAa;IACnCD,mBAAmB,EAAEC;EACvB;AACF,CAAC,CAAC;AACF,IAAIU,cAAc,GAAG,SAAjBA,cAAcA,CAAGC,OAAO;EAAA,OAAIA,OAAO,CAACC,YAAY,CAAC,MAAM,CAAC;AAAA;AAC5D,IAAIC,cAAc,GAAG,SAAjBA,cAAcA,CAAGF,OAAO;EAAA,OAAIA,OAAO,CAACG,OAAO,CAACC,WAAW,CAAC,CAAC;AAAA;AAC7D,IAAIC,cAAc,GAAG,SAAjBA,cAAcA,CAAGC,MAAM;EAAA,OAAIA,MAAM,KAAKf,4BAA4B,IAAIe,MAAM,KAAKhB,iCAAiC;AAAA;AACtH,IAAIiB,YAAY,GAAG,SAAfA,YAAYA,CAAGP,OAAO;EAAA,OAAID,cAAc,CAACC,OAAO,CAAC,KAAK,QAAQ;AAAA;AAClE,IAAIQ,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAGF,MAAM;EAAA,OAAIA,MAAM,KAAKd,8BAA8B,IAAIc,MAAM,KAAKf,4BAA4B,IAAIe,MAAM,KAAKhB,iCAAiC;AAAA;AACvK,IAAImB,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAGH,MAAM;EAAA,OAAIA,MAAM,KAAKX,oBAAoB,IAAIW,MAAM,KAAKZ,iBAAiB;AAAA;AAChG,IAAIgB,eAAe,GAAG,SAAlBA,eAAeA,CAAGC,KAAK,EAAI;EAC7B,IAAIC,GAAG,GAAGD,KAAK,CAACC,GAAG;IACjBC,MAAM,GAAGF,KAAK,CAACE,MAAM;EACvB,IAAIC,UAAU,GAAGF,GAAG,KAAK,GAAG,IAAIA,GAAG,KAAK,UAAU;EAClD,IAAIG,WAAW,GAAGb,cAAc,CAACW,MAAM,CAAC,KAAK,QAAQ,IAAIN,YAAY,CAACM,MAAM,CAAC;EAC7E,OAAOD,GAAG,KAAK,OAAO,IAAIE,UAAU,IAAIC,WAAW;AACrD,CAAC;AACD,IAAIC,2BAA2B,GAAG,GAAG;AACrC,IAAIC,sBAAsB,GAAG,EAAE;AAAC,IA4EXC,cAAc;EACjC,SAAAA,eAAYC,MAAM,EAAE;IAAAnC,eAAA,OAAAkC,cAAA;IAClB,IAAI,CAACE,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,sBAAsB,GAAG,IAAI;IAClC,IAAI,CAACC,oBAAoB,GAAG,KAAK;IACjC,IAAI,CAACC,kBAAkB,GAAG,IAAI;IAC9B,IAAI,CAACC,qBAAqB,GAAG,IAAI;IACjC,IAAI,CAACC,WAAW,GAAGrC,aAAa;IAChC,IAAI,CAACsC,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACC,SAAS,CAACT,MAAM,CAAC;EACxB;EAAC,OAAAlC,YAAA,CAAAiC,cAAA;IAAAN,GAAA;IAAAiB,KAAA,EACD,SAAAD,SAASA,CAACT,MAAM,EAAE;MAChB,IAAI,CAACW,OAAO,GAAGX,MAAM;IACvB;EAAC;IAAAP,GAAA;IAAAiB,KAAA,EAKD,SAAAE,KAAKA,CAAA,EAAG;MACN,IAAI,CAACC,4BAA4B,CAAC,CAAC;MACnC,IAAI,CAACC,wBAAwB,CAAC,CAAC;MAC/B,IAAI,CAACC,2BAA2B,CAAC,CAAC;IACpC;EAAC;IAAAtB,GAAA;IAAAiB,KAAA,EAKD,SAAAM,gBAAgBA,CAAA,EAAG;MACjB,IAAI,IAAI,CAACf,cAAc,IAAI,IAAI,EAAE;QAC/B,IAAI,CAACA,cAAc,GAAG,IAAI,CAACgB,oBAAoB,CAAC,CAAC;MACnD;MACA,OAAO,IAAI,CAAChB,cAAc;IAC5B;EAAC;IAAAR,GAAA;IAAAiB,KAAA,EACD,SAAAO,oBAAoBA,CAAA,EAAG;MAAA,IAAAC,KAAA;MACrB,IAAIC,KAAK,GAAG,SAARA,KAAKA,CAAI3B,KAAK,EAAE4B,WAAW,EAAK;QAClC5B,KAAK,CAAC6B,OAAO,CAAC,CAAC;QACfH,KAAI,CAACH,2BAA2B,CAAC,CAAC;QAClCG,KAAI,CAACd,oBAAoB,GAAG,KAAK;QACjCc,KAAI,CAACI,oBAAoB,GAAG,KAAK;QACjCJ,KAAI,CAACX,WAAW,GAAGrC,aAAa;QAChCgD,KAAI,CAAChB,eAAe,GAAGV,KAAK,CAAC+B,WAAW,CAACC,IAAI,KAAK,YAAY;QAC9DN,KAAI,CAACO,cAAc,CAACnD,eAAe,EAAEkB,KAAK,CAAC;QAC3C,IAAIkC,eAAe,GAAGC,cAAc,CAACT,KAAI,CAACP,OAAO,CAACe,eAAe,EAAE,CAAC,EAAE5B,sBAAsB,CAAC;QAC7F,IAAIsB,WAAW,KAAK,KAAK,IAAIM,eAAe,GAAG,CAAC,EAAE;UAChDR,KAAI,CAACb,kBAAkB,GAAGuB,UAAU,CAAC,YAAM;YACzCV,KAAI,CAACO,cAAc,CAAC1D,KAAK,EAAEyB,KAAK,CAAC;UACnC,CAAC,EAAEkC,eAAe,CAAC;QACrB,CAAC,MAAM;UACLR,KAAI,CAACO,cAAc,CAAC1D,KAAK,EAAEyB,KAAK,CAAC;QACnC;QACA,IAAIqC,cAAc,GAAGF,cAAc,CAACT,KAAI,CAACP,OAAO,CAACkB,cAAc,EAAE,EAAE,EAAEhC,2BAA2B,CAAC;QACjGqB,KAAI,CAACf,sBAAsB,GAAGyB,UAAU,CAAC,YAAM;UAC7CV,KAAI,CAACY,gBAAgB,CAACtC,KAAK,CAAC;QAC9B,CAAC,EAAEqC,cAAc,GAAGH,eAAe,CAAC;MACtC,CAAC;MACD,IAAIK,GAAG,GAAG,SAANA,GAAGA,CAAGvC,KAAK,EAAI;QACjB0B,KAAI,CAACO,cAAc,CAAClD,iBAAiB,EAAEiB,KAAK,CAAC;MAC/C,CAAC;MACD,IAAIwC,aAAY,GAAG,SAAfA,YAAYA,CAAGxC,KAAK,EAAI;QAC1B,IAAIyC,OAAO,GAAGf,KAAI,CAACP,OAAO,CAACsB,OAAO;QAClC,IAAIvC,MAAM,GAAGF,KAAK,CAACE,MAAM;QACzB,IAAIwB,KAAI,CAACX,WAAW,KAAKrC,aAAa,IAAIqB,eAAe,CAACC,KAAK,CAAC,EAAE;UAChEuC,GAAG,CAACvC,KAAK,CAAC;UACV0C,QAAQ,CAACC,mBAAmB,CAAC,OAAO,EAAEH,aAAY,CAAC;UACnD,IAAII,IAAI,GAAG1C,MAAM,CAACZ,YAAY,CAAC,MAAM,CAAC;UACtC,IAAIuD,WAAW,GAAGtD,cAAc,CAACW,MAAM,CAAC;UACxC,IAAI4C,0BAA0B,GAAGF,IAAI,KAAK,MAAM,IAAIC,WAAW,KAAK,GAAG,IAAIA,WAAW,KAAK,QAAQ,IAAIA,WAAW,KAAK,OAAO,IAAIA,WAAW,KAAK,QAAQ,IAAIA,WAAW,KAAK,UAAU;UACxL,IAAIE,eAAe,GAAGrB,KAAI,CAACV,iBAAiB,KAAKd,MAAM;UACvD,IAAIuC,OAAO,IAAI,IAAI,IAAI,CAACK,0BAA0B,IAAIC,eAAe,EAAE;YACrEN,OAAO,CAACzC,KAAK,CAAC;UAChB;UACA0B,KAAI,CAACV,iBAAiB,GAAG,IAAI;QAC/B;MACF,CAAC;MACD,OAAO;QACLgC,yBAAyB,EAAE,SAA3BA,yBAAyBA,CAAEhD,KAAK,EAAI;UAClC,IAAIiD,QAAQ,GAAGvB,KAAI,CAACP,OAAO,CAAC8B,QAAQ;UACpC,IAAIA,QAAQ,IAAIrD,YAAY,CAACI,KAAK,CAACkD,aAAa,CAAC,EAAE;YACjDlD,KAAK,CAACmD,eAAe,CAAC,CAAC;UACzB;UACA,IAAIF,QAAQ,IAAI,IAAI,EAAE;YACpB,OAAO,IAAI;UACb;UACA,OAAO,CAACA,QAAQ;QAClB,CAAC;QACDG,SAAS,EAAE,SAAXA,SAASA,CAAEpD,KAAK,EAAI;UAClB,IAAIiD,QAAQ,GAAGvB,KAAI,CAACP,OAAO,CAAC8B,QAAQ;UACpC,IAAIhD,GAAG,GAAGD,KAAK,CAACC,GAAG;YACjBC,MAAM,GAAGF,KAAK,CAACE,MAAM;UACvB,IAAI,CAAC+C,QAAQ,IAAIlD,eAAe,CAACC,KAAK,CAAC,EAAE;YACvC,IAAI0B,KAAI,CAACX,WAAW,KAAKrC,aAAa,EAAE;cACtCiD,KAAK,CAAC3B,KAAK,EAAE,KAAK,CAAC;cACnB0B,KAAI,CAACV,iBAAiB,GAAGd,MAAM;cAG/BwC,QAAQ,CAACW,gBAAgB,CAAC,OAAO,EAAEb,aAAY,CAAC;YAClD;YACA,IAAIc,aAAa,GAAGrD,GAAG,KAAK,GAAG,IAAIA,GAAG,KAAK,UAAU;YACrD,IAAI2C,IAAI,GAAGxD,cAAc,CAACc,MAAM,CAAC;YACjC,IAAIqD,gBAAgB,GAAGX,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,UAAU;YAC/D,IAAIU,aAAa,IAAIC,gBAAgB,IAAIhE,cAAc,CAACW,MAAM,CAAC,KAAK,QAAQ,EAAE;cAE5EF,KAAK,CAACwD,cAAc,CAAC,CAAC;YACxB;YACAxD,KAAK,CAACmD,eAAe,CAAC,CAAC;UACzB;QACF,CAAC;QACDM,gBAAgB,EAAE,SAAlBA,gBAAgBA,CAAEzD,KAAK;UAAA,OAAI2B,KAAK,CAAC3B,KAAK,CAAC;QAAA;QACvC0D,eAAe,EAAE,SAAjBA,eAAeA,CAAE1D,KAAK,EAAI;UACxB,IAAI0B,KAAI,CAACP,OAAO,CAACwC,WAAW,IAAI,IAAI,EAAE;YACpCjC,KAAI,CAACP,OAAO,CAACwC,WAAW,CAAC3D,KAAK,CAAC;UACjC;UACA,IAAI4D,KAAK,GAAGC,0BAA0B,CAAC7D,KAAK,CAAC;UAC7C,IAAI0B,KAAI,CAACoC,sBAAsB,IAAI,IAAI,EAAE;YACvC,IAAIC,MAAM,GAAGrC,KAAI,CAACoC,sBAAsB,CAACE,KAAK,GAAGJ,KAAK,CAACI,KAAK;YAC5D,IAAIC,MAAM,GAAGvC,KAAI,CAACoC,sBAAsB,CAACI,KAAK,GAAGN,KAAK,CAACM,KAAK;YAC5D,IAAIC,IAAI,CAACC,KAAK,CAACL,MAAM,EAAEE,MAAM,CAAC,GAAG,EAAE,EAAE;cACnCvC,KAAI,CAACL,4BAA4B,CAAC,CAAC;YACrC;UACF;QACF,CAAC;QACDgD,kBAAkB,EAAE,SAApBA,kBAAkBA,CAAErE,KAAK;UAAA,OAAIuC,GAAG,CAACvC,KAAK,CAAC;QAAA;QACvCsE,oBAAoB,EAAE,SAAtBA,oBAAoBA,CAAEtE,KAAK,EAAI;UAC7B,IAAIA,KAAK,CAAC+B,WAAW,CAACC,IAAI,KAAK,iBAAiB,EAAE;YAChDN,KAAI,CAACI,oBAAoB,GAAG,IAAI;UAClC;UACAJ,KAAI,CAACO,cAAc,CAACjD,oBAAoB,EAAEgB,KAAK,CAAC;QAClD,CAAC;QACDuE,6BAA6B,EAAE,SAA/BA,6BAA6BA,CAAEvE,KAAK,EAAI;UACtC,IAAIwE,aAAa,GAAG9C,KAAI,CAACP,OAAO;YAC9BsD,UAAU,GAAGD,aAAa,CAACC,UAAU;YACrCxB,QAAQ,GAAGuB,aAAa,CAACvB,QAAQ;YACjCyB,WAAW,GAAGF,aAAa,CAACE,WAAW;UAGzC,IAAI,CAACzB,QAAQ,IAAIyB,WAAW,IAAI,IAAI,IAAIhD,KAAI,CAAChB,eAAe,IAAIV,KAAK,CAAC+B,WAAW,CAACC,IAAI,KAAK,aAAa,EAAE;YACxG,OAAO,KAAK;UACd;UACA,IAAIyC,UAAU,IAAI,IAAI,EAAE;YACtB,OAAO,IAAI;UACb;UACA,OAAOA,UAAU;QACnB,CAAC;QASDE,OAAO,EAAE,SAATA,OAAOA,CAAE3E,KAAK,EAAI;UAChB,IAAI4E,cAAc,GAAGlD,KAAI,CAACP,OAAO;YAC/B8B,QAAQ,GAAG2B,cAAc,CAAC3B,QAAQ;YAClCR,OAAO,GAAGmC,cAAc,CAACnC,OAAO;UAClC,IAAI,CAACQ,QAAQ,EAAE;YAIbjD,KAAK,CAACmD,eAAe,CAAC,CAAC;YACvB,IAAIzB,KAAI,CAACd,oBAAoB,IAAIc,KAAI,CAACI,oBAAoB,EAAE;cAC1D9B,KAAK,CAACwD,cAAc,CAAC,CAAC;YACxB,CAAC,MAAM,IAAIf,OAAO,IAAI,IAAI,IAAIzC,KAAK,CAAC6E,MAAM,KAAK,KAAK,EAAE;cACpDpC,OAAO,CAACzC,KAAK,CAAC;YAChB;UACF,CAAC,MAAM;YACL,IAAIJ,YAAY,CAACI,KAAK,CAACkD,aAAa,CAAC,EAAE;cACrClD,KAAK,CAACmD,eAAe,CAAC,CAAC;YACzB;UACF;QACF,CAAC;QAGD2B,aAAa,EAAE,SAAfA,aAAaA,CAAE9E,KAAK,EAAI;UACtB,IAAI+E,cAAc,GAAGrD,KAAI,CAACP,OAAO;YAC/B8B,QAAQ,GAAG8B,cAAc,CAAC9B,QAAQ;YAClCyB,WAAW,GAAGK,cAAc,CAACL,WAAW;UAC1C,IAAI,CAACzB,QAAQ,EAAE;YACb,IAAIyB,WAAW,IAAI,IAAI,IAAIhD,KAAI,CAAChB,eAAe,IAAI,CAACV,KAAK,CAACgF,gBAAgB,EAAE;cAC1EhF,KAAK,CAACwD,cAAc,CAAC,CAAC;cACtBxD,KAAK,CAACmD,eAAe,CAAC,CAAC;YACzB;UACF,CAAC,MAAM;YACL,IAAIvD,YAAY,CAACI,KAAK,CAACkD,aAAa,CAAC,EAAE;cACrClD,KAAK,CAACmD,eAAe,CAAC,CAAC;YACzB;UACF;QACF;MACF,CAAC;IACH;EAAC;IAAAlD,GAAA;IAAAiB,KAAA,EAMD,SAAAe,cAAcA,CAACtC,MAAM,EAAEK,KAAK,EAAE;MAC5B,IAAIiF,SAAS,GAAG,IAAI,CAAClE,WAAW;MAChC,IAAImE,SAAS,GAAG,IAAI;MACpB,IAAIjG,WAAW,CAACgG,SAAS,CAAC,IAAI,IAAI,EAAE;QAClCC,SAAS,GAAGjG,WAAW,CAACgG,SAAS,CAAC,CAACtF,MAAM,CAAC;MAC5C;MACA,IAAI,IAAI,CAACoB,WAAW,KAAKrC,aAAa,IAAIiB,MAAM,KAAKZ,iBAAiB,EAAE;QACtE;MACF;MACA,IAAImG,SAAS,IAAI,IAAI,IAAIA,SAAS,KAAK1G,KAAK,EAAE;QAC5C2G,OAAO,CAACC,KAAK,CAAC,iCAAiC,GAAGzF,MAAM,GAAG,aAAa,GAAGsF,SAAS,GAAG,eAAe,CAAC;MACzG,CAAC,MAAM,IAAIA,SAAS,KAAKC,SAAS,EAAE;QAClC,IAAI,CAACG,6BAA6B,CAACJ,SAAS,EAAEC,SAAS,EAAEvF,MAAM,EAAEK,KAAK,CAAC;QACvE,IAAI,CAACe,WAAW,GAAGmE,SAAS;MAC9B;IACF;EAAC;IAAAjF,GAAA;IAAAiB,KAAA,EAMD,SAAAmE,6BAA6BA,CAACJ,SAAS,EAAEC,SAAS,EAAEvF,MAAM,EAAEK,KAAK,EAAE;MAAA,IAAAsF,MAAA;MACjE,IAAIxF,gBAAgB,CAACH,MAAM,CAAC,EAAE;QAI5ByC,UAAU,CAAC,YAAM;UACfkD,MAAI,CAAC5E,eAAe,GAAG,KAAK;QAC9B,CAAC,EAAE,CAAC,CAAC;QACL,IAAI,CAACoD,sBAAsB,GAAG,IAAI;QAClC,IAAI,CAACzC,4BAA4B,CAAC,CAAC;MACrC;MACA,IAAIxB,kBAAkB,CAACoF,SAAS,CAAC,IAAItF,MAAM,KAAKlB,mBAAmB,EAAE;QACnE,IAAIiG,WAAW,GAAG,IAAI,CAACvD,OAAO,CAACuD,WAAW;QAG1C,IAAIA,WAAW,IAAI,IAAI,IAAI1E,KAAK,CAAC+B,WAAW,CAAC9B,GAAG,IAAI,IAAI,EAAE;UACxDyE,WAAW,CAAC1E,KAAK,CAAC;UAClB,IAAI,CAACY,oBAAoB,GAAG,IAAI;QAClC;MACF;MACA,IAAI2E,YAAY,GAAG7F,cAAc,CAACuF,SAAS,CAAC;MAC5C,IAAIO,YAAY,GAAG9F,cAAc,CAACwF,SAAS,CAAC;MAC5C,IAAI,CAACK,YAAY,IAAIC,YAAY,EAAE;QACjC,IAAI,CAACC,SAAS,CAACzF,KAAK,CAAC;MACvB,CAAC,MAAM,IAAIuF,YAAY,IAAI,CAACC,YAAY,EAAE;QACxC,IAAI,CAACE,WAAW,CAAC1F,KAAK,CAAC;MACzB;MACA,IAAIH,kBAAkB,CAACoF,SAAS,CAAC,IAAItF,MAAM,KAAKZ,iBAAiB,EAAE;QACjE,IAAI4G,cAAc,GAAG,IAAI,CAACxE,OAAO;UAC/ByE,YAAY,GAAGD,cAAc,CAACjB,WAAW;UACzCjC,OAAO,GAAGkD,cAAc,CAAClD,OAAO;QAClC,IAAIA,OAAO,IAAI,IAAI,EAAE;UACnB,IAAIoD,0BAA0B,GAAGD,YAAY,IAAI,IAAI,IAAIX,SAAS,KAAKtG,iCAAiC;UACxG,IAAI,CAACkH,0BAA0B,EAAE;YAE/B,IAAI,CAACL,YAAY,IAAI,CAACD,YAAY,EAAE;cAClC,IAAI,CAACE,SAAS,CAACzF,KAAK,CAAC;cACrB,IAAI,CAAC0F,WAAW,CAAC1F,KAAK,CAAC;YACzB;UACF;QACF;MACF;MACA,IAAI,CAACsB,wBAAwB,CAAC,CAAC;IACjC;EAAC;IAAArB,GAAA;IAAAiB,KAAA,EACD,SAAAuE,SAASA,CAACzF,KAAK,EAAE;MACf,IAAI8F,cAAc,GAAG,IAAI,CAAC3E,OAAO;QAC/B4E,aAAa,GAAGD,cAAc,CAACC,aAAa;QAC5CC,YAAY,GAAGF,cAAc,CAACE,YAAY;MAC5C,IAAIpC,KAAK,GAAGC,0BAA0B,CAAC7D,KAAK,CAAC;MAC7C,IAAI,CAAC8D,sBAAsB,GAAG;QAC5BE,KAAK,EAAEJ,KAAK,CAACI,KAAK;QAClBE,KAAK,EAAEN,KAAK,CAACM;MACf,CAAC;MACD,IAAI8B,YAAY,IAAI,IAAI,EAAE;QACxBA,YAAY,CAAChG,KAAK,CAAC;MACrB;MACA,IAAI+F,aAAa,IAAI,IAAI,EAAE;QACzBA,aAAa,CAAC,IAAI,CAAC;MACrB;IACF;EAAC;IAAA9F,GAAA;IAAAiB,KAAA,EACD,SAAAwE,WAAWA,CAAC1F,KAAK,EAAE;MACjB,IAAIiG,cAAc,GAAG,IAAI,CAAC9E,OAAO;QAC/B4E,aAAa,GAAGE,cAAc,CAACF,aAAa;QAC5CG,UAAU,GAAGD,cAAc,CAACC,UAAU;MACxC,SAAS3D,GAAGA,CAAA,EAAG;QACb,IAAI2D,UAAU,IAAI,IAAI,EAAE;UACtBA,UAAU,CAAClG,KAAK,CAAC;QACnB;QACA,IAAI+F,aAAa,IAAI,IAAI,EAAE;UACzBA,aAAa,CAAC,KAAK,CAAC;QACtB;MACF;MACA,IAAII,aAAa,GAAGhE,cAAc,CAAC,IAAI,CAAChB,OAAO,CAACgF,aAAa,CAAC;MAC9D,IAAIA,aAAa,GAAG,CAAC,EAAE;QACrB,IAAI,CAACrF,qBAAqB,GAAGsB,UAAU,CAAC,YAAM;UAC5CG,GAAG,CAAC,CAAC;QACP,CAAC,EAAE4D,aAAa,CAAC;MACnB,CAAC,MAAM;QACL5D,GAAG,CAAC,CAAC;MACP;IACF;EAAC;IAAAtC,GAAA;IAAAiB,KAAA,EACD,SAAAoB,gBAAgBA,CAACtC,KAAK,EAAE;MACtB,IAAI,IAAI,CAACe,WAAW,KAAKnC,4BAA4B,IAAI,IAAI,CAACmC,WAAW,KAAKpC,iCAAiC,EAAE;QAC/G,IAAI,CAACsD,cAAc,CAACxD,mBAAmB,EAAEuB,KAAK,CAAC;MACjD;IACF;EAAC;IAAAC,GAAA;IAAAiB,KAAA,EACD,SAAAG,4BAA4BA,CAAA,EAAG;MAC7B,IAAI,IAAI,CAACV,sBAAsB,IAAI,IAAI,EAAE;QACvCyF,YAAY,CAAC,IAAI,CAACzF,sBAAsB,CAAC;QACzC,IAAI,CAACA,sBAAsB,GAAG,IAAI;MACpC;IACF;EAAC;IAAAV,GAAA;IAAAiB,KAAA,EACD,SAAAI,wBAAwBA,CAAA,EAAG;MACzB,IAAI,IAAI,CAACT,kBAAkB,IAAI,IAAI,EAAE;QACnCuF,YAAY,CAAC,IAAI,CAACvF,kBAAkB,CAAC;QACrC,IAAI,CAACA,kBAAkB,GAAG,IAAI;MAChC;IACF;EAAC;IAAAZ,GAAA;IAAAiB,KAAA,EACD,SAAAK,2BAA2BA,CAAA,EAAG;MAC5B,IAAI,IAAI,CAACT,qBAAqB,IAAI,IAAI,EAAE;QACtCsF,YAAY,CAAC,IAAI,CAACtF,qBAAqB,CAAC;QACxC,IAAI,CAACA,qBAAqB,GAAG,IAAI;MACnC;IACF;EAAC;AAAA;AAAA,SAhUkBP,cAAc,IAAA8F,OAAA;AAkUnC,SAASlE,cAAcA,CAACmE,KAAK,EAAEC,GAAG,EAAEC,QAAQ,EAAE;EAC5C,IAAID,GAAG,KAAK,KAAK,CAAC,EAAE;IAClBA,GAAG,GAAG,CAAC;EACT;EACA,IAAIC,QAAQ,KAAK,KAAK,CAAC,EAAE;IACvBA,QAAQ,GAAG,CAAC;EACd;EACA,OAAOrC,IAAI,CAACsC,GAAG,CAACF,GAAG,EAAED,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAGE,QAAQ,CAAC;AAC7E;AACA,SAAS3C,0BAA0BA,CAAC7D,KAAK,EAAE;EACzC,IAAI0G,kBAAkB,GAAG1G,KAAK,CAAC+B,WAAW;IACxC4E,cAAc,GAAGD,kBAAkB,CAACC,cAAc;IAClDC,OAAO,GAAGF,kBAAkB,CAACE,OAAO;EACtC,IAAIA,OAAO,IAAI,IAAI,IAAIA,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;IACzC,OAAOD,OAAO,CAAC,CAAC,CAAC;EACnB;EACA,IAAID,cAAc,IAAI,IAAI,IAAIA,cAAc,CAACE,MAAM,GAAG,CAAC,EAAE;IACvD,OAAOF,cAAc,CAAC,CAAC,CAAC;EAC1B;EACA,OAAO3G,KAAK,CAAC+B,WAAW;AAC1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}