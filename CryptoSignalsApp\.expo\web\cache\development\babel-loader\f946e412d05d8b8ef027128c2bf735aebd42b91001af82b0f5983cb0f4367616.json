{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport React, { useState, useEffect } from 'react';\nimport Appearance from \"react-native-web/dist/exports/Appearance\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport { Provider as PaperProvider, MD3LightTheme as PaperDefaultTheme, MD3DarkTheme as PaperDarkTheme } from 'react-native-paper';\nimport { NavigationContainer, DefaultTheme as NavigationDefaultTheme, DarkTheme as NavigationDarkTheme } from '@react-navigation/native';\nimport { I18nextProvider } from 'react-i18next';\nimport StoreProvider from \"./src/store\";\nimport AxiosProvider from \"./src/store/axios\";\nimport { useFonts, Poppins_400Regular, Poppins_500Medium, Poppins_600SemiBold, Poppins_700Bold } from '@expo-google-fonts/poppins';\nimport Routes from \"./src/routes\";\nimport Loading from \"./src/components/Loading\";\nimport i18n from \"./i18n\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nif (Platform.OS === 'web') {\n  require(\"./web/index.css\");\n}\nvar CustomDefaultTheme = _objectSpread(_objectSpread(_objectSpread({}, NavigationDefaultTheme), PaperDefaultTheme), {}, {\n  colors: _objectSpread(_objectSpread(_objectSpread({}, NavigationDefaultTheme.colors), PaperDefaultTheme.colors), {}, {\n    background: '#202020',\n    primary: '#FECB37',\n    grey: '#0D0D0D'\n  })\n});\nvar CustomDarkTheme = _objectSpread(_objectSpread(_objectSpread({}, NavigationDarkTheme), PaperDarkTheme), {}, {\n  colors: _objectSpread(_objectSpread(_objectSpread({}, NavigationDarkTheme.colors), PaperDarkTheme.colors), {}, {\n    primary: '#FECB37',\n    grey: '#0D0D0D'\n  })\n});\nexport default function App() {\n  var _useState = useState(Appearance.getColorScheme() === 'dark'),\n    _useState2 = _slicedToArray(_useState, 2),\n    isDarkMode = _useState2[0],\n    setIsDarkMode = _useState2[1];\n  var _useFonts = useFonts({\n      Poppins_400Regular: Poppins_400Regular,\n      Poppins_500Medium: Poppins_500Medium,\n      Poppins_600SemiBold: Poppins_600SemiBold,\n      Poppins_700Bold: Poppins_700Bold\n    }),\n    _useFonts2 = _slicedToArray(_useFonts, 1),\n    fontsLoaded = _useFonts2[0];\n  useEffect(function () {\n    var subscription = Appearance.addChangeListener(function (_ref) {\n      var colorScheme = _ref.colorScheme;\n      setIsDarkMode(colorScheme === 'dark');\n    });\n    return function () {\n      return subscription.remove();\n    };\n  }, []);\n  if (!fontsLoaded) {\n    return _jsx(Loading, {});\n  }\n  var theme = isDarkMode ? CustomDarkTheme : CustomDefaultTheme;\n  return _jsx(I18nextProvider, {\n    i18n: i18n,\n    children: _jsx(StoreProvider, {\n      children: _jsx(AxiosProvider, {\n        children: _jsx(PaperProvider, {\n          theme: theme,\n          children: _jsx(NavigationContainer, {\n            theme: theme,\n            children: _jsx(Routes, {})\n          })\n        })\n      })\n    })\n  });\n}", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Appearance", "Platform", "Provider", "PaperProvider", "MD3LightTheme", "PaperDefaultTheme", "MD3DarkTheme", "PaperDarkTheme", "NavigationContainer", "DefaultTheme", "NavigationDefaultTheme", "DarkTheme", "NavigationDarkTheme", "I18nextProvider", "StoreProvider", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useFonts", "Poppins_400Regular", "Poppins_500Medium", "Poppins_600SemiBold", "Poppins_700Bold", "Routes", "Loading", "i18n", "jsx", "_jsx", "OS", "require", "CustomDefaultTheme", "_objectSpread", "colors", "background", "primary", "grey", "CustomDarkTheme", "App", "_useState", "getColorScheme", "_useState2", "_slicedToArray", "isDarkMode", "setIsDarkMode", "_useFonts", "_useFonts2", "fontsLoaded", "subscription", "addChangeListener", "_ref", "colorScheme", "remove", "theme", "children"], "sources": ["E:/CryptoSignalsApp/App.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { Appearance, Platform } from 'react-native';\r\nimport { Provider as PaperProvider, MD3LightTheme as PaperDefaultTheme, MD3DarkTheme as PaperDarkTheme } from 'react-native-paper';\r\nimport { NavigationContainer, DefaultTheme as NavigationDefaultTheme, DarkTheme as NavigationDarkTheme } from '@react-navigation/native';\r\nimport { I18nextProvider } from 'react-i18next';\r\nimport StoreProvider from './src/store';\r\nimport AxiosProvider from './src/store/axios';\r\nimport { useFonts, Poppins_400Regular, Poppins_500Medium, Poppins_600SemiBold, Poppins_700Bold } from '@expo-google-fonts/poppins';\r\nimport Routes from './src/routes';\r\nimport Loading from './src/components/Loading';\r\nimport i18n from './i18n';\r\n\r\n// Import CSS for web\r\nif (Platform.OS === 'web') {\r\n  require('./web/index.css');\r\n}\r\n\r\n\r\n\r\n\r\n\r\nconst CustomDefaultTheme = {\r\n  ...NavigationDefaultTheme,\r\n  ...PaperDefaultTheme,\r\n  colors: {\r\n    ...NavigationDefaultTheme.colors,\r\n    ...PaperDefaultTheme.colors,\r\n    background: '#202020',\r\n    primary: '#FECB37',\r\n    grey: '#0D0D0D',\r\n  }\r\n};\r\n\r\nconst CustomDarkTheme = {\r\n  ...NavigationDarkTheme,\r\n  ...PaperDarkTheme,\r\n  colors: {\r\n    ...NavigationDarkTheme.colors,\r\n    ...PaperDarkTheme.colors,\r\n    primary: '#FECB37',\r\n    grey: '#0D0D0D',\r\n  }\r\n};\r\n\r\nexport default function App() {\r\n  const [isDarkMode, setIsDarkMode] = useState(Appearance.getColorScheme() === 'dark');\r\n\r\n  let [fontsLoaded] = useFonts({\r\n    Poppins_400Regular,\r\n    Poppins_500Medium,\r\n    Poppins_600SemiBold,\r\n    Poppins_700Bold,\r\n  });\r\n\r\n  useEffect(() => {\r\n    const subscription = Appearance.addChangeListener(({ colorScheme }) => {\r\n      setIsDarkMode(colorScheme === 'dark');\r\n    });\r\n\r\n    return () => subscription.remove();\r\n  }, []);\r\n\r\n  if (!fontsLoaded) {\r\n    return <Loading />;\r\n  }\r\n\r\n  const theme = isDarkMode ? CustomDarkTheme : CustomDefaultTheme;\r\n\r\n  return (\r\n    <I18nextProvider i18n={i18n}>\r\n      <StoreProvider>\r\n        <AxiosProvider>\r\n          <PaperProvider theme={theme}>\r\n            <NavigationContainer theme={theme}>\r\n              <Routes />\r\n            </NavigationContainer>\r\n          </PaperProvider>\r\n        </AxiosProvider>\r\n      </StoreProvider>\r\n    </I18nextProvider>\r\n  );\r\n}"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,OAAAC,UAAA;AAAA,OAAAC,QAAA;AAEnD,SAASC,QAAQ,IAAIC,aAAa,EAAEC,aAAa,IAAIC,iBAAiB,EAAEC,YAAY,IAAIC,cAAc,QAAQ,oBAAoB;AAClI,SAASC,mBAAmB,EAAEC,YAAY,IAAIC,sBAAsB,EAAEC,SAAS,IAAIC,mBAAmB,QAAQ,0BAA0B;AACxI,SAASC,eAAe,QAAQ,eAAe;AAC/C,OAAOC,aAAa;AACpB,OAAOC,aAAa;AACpB,SAASC,QAAQ,EAAEC,kBAAkB,EAAEC,iBAAiB,EAAEC,mBAAmB,EAAEC,eAAe,QAAQ,4BAA4B;AAClI,OAAOC,MAAM;AACb,OAAOC,OAAO;AACd,OAAOC,IAAI;AAAe,SAAAC,GAAA,IAAAC,IAAA;AAG1B,IAAIxB,QAAQ,CAACyB,EAAE,KAAK,KAAK,EAAE;EACzBC,OAAO,kBAAkB,CAAC;AAC5B;AAMA,IAAMC,kBAAkB,GAAAC,aAAA,CAAAA,aAAA,CAAAA,aAAA,KACnBnB,sBAAsB,GACtBL,iBAAiB;EACpByB,MAAM,EAAAD,aAAA,CAAAA,aAAA,CAAAA,aAAA,KACDnB,sBAAsB,CAACoB,MAAM,GAC7BzB,iBAAiB,CAACyB,MAAM;IAC3BC,UAAU,EAAE,SAAS;IACrBC,OAAO,EAAE,SAAS;IAClBC,IAAI,EAAE;EAAS;AAChB,EACF;AAED,IAAMC,eAAe,GAAAL,aAAA,CAAAA,aAAA,CAAAA,aAAA,KAChBjB,mBAAmB,GACnBL,cAAc;EACjBuB,MAAM,EAAAD,aAAA,CAAAA,aAAA,CAAAA,aAAA,KACDjB,mBAAmB,CAACkB,MAAM,GAC1BvB,cAAc,CAACuB,MAAM;IACxBE,OAAO,EAAE,SAAS;IAClBC,IAAI,EAAE;EAAS;AAChB,EACF;AAED,eAAe,SAASE,GAAGA,CAAA,EAAG;EAC5B,IAAAC,SAAA,GAAoCtC,QAAQ,CAACE,UAAU,CAACqC,cAAc,CAAC,CAAC,KAAK,MAAM,CAAC;IAAAC,UAAA,GAAAC,cAAA,CAAAH,SAAA;IAA7EI,UAAU,GAAAF,UAAA;IAAEG,aAAa,GAAAH,UAAA;EAEhC,IAAAI,SAAA,GAAoB1B,QAAQ,CAAC;MAC3BC,kBAAkB,EAAlBA,kBAAkB;MAClBC,iBAAiB,EAAjBA,iBAAiB;MACjBC,mBAAmB,EAAnBA,mBAAmB;MACnBC,eAAe,EAAfA;IACF,CAAC,CAAC;IAAAuB,UAAA,GAAAJ,cAAA,CAAAG,SAAA;IALGE,WAAW,GAAAD,UAAA;EAOhB5C,SAAS,CAAC,YAAM;IACd,IAAM8C,YAAY,GAAG7C,UAAU,CAAC8C,iBAAiB,CAAC,UAAAC,IAAA,EAAqB;MAAA,IAAlBC,WAAW,GAAAD,IAAA,CAAXC,WAAW;MAC9DP,aAAa,CAACO,WAAW,KAAK,MAAM,CAAC;IACvC,CAAC,CAAC;IAEF,OAAO;MAAA,OAAMH,YAAY,CAACI,MAAM,CAAC,CAAC;IAAA;EACpC,CAAC,EAAE,EAAE,CAAC;EAEN,IAAI,CAACL,WAAW,EAAE;IAChB,OAAOnB,IAAA,CAACH,OAAO,IAAE,CAAC;EACpB;EAEA,IAAM4B,KAAK,GAAGV,UAAU,GAAGN,eAAe,GAAGN,kBAAkB;EAE/D,OACEH,IAAA,CAACZ,eAAe;IAACU,IAAI,EAAEA,IAAK;IAAA4B,QAAA,EAC1B1B,IAAA,CAACX,aAAa;MAAAqC,QAAA,EACZ1B,IAAA,CAACV,aAAa;QAAAoC,QAAA,EACZ1B,IAAA,CAACtB,aAAa;UAAC+C,KAAK,EAAEA,KAAM;UAAAC,QAAA,EAC1B1B,IAAA,CAACjB,mBAAmB;YAAC0C,KAAK,EAAEA,KAAM;YAAAC,QAAA,EAChC1B,IAAA,CAACJ,MAAM,IAAE;UAAC,CACS;QAAC,CACT;MAAC,CACH;IAAC,CACH;EAAC,CACD,CAAC;AAEtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}