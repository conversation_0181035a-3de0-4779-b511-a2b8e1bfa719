import os
import sys
import asyncio
import logging
import argparse
import json

# Importando configurações e utilitários do projeto principal
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from trading_automatizado.auto_trader import AutoTrader

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("trading_automatizado/manual_trader.log"),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

class ManualTrader:
    def __init__(self, capital_por_operacao=20, modo_simulacao=True, estrategia="swing", capital_total=None):
        """
        Inicializa o trader manual
        
        Args:
            capital_por_operacao: Valor em dólares para cada operação
            modo_simulacao: Se True, apenas simula as operações sem executá-las
            estrategia: Tipo de estratégia ('scalp' ou 'swing')
            capital_total: Capital total disponível para trading
        """
        self.capital_por_operacao = capital_por_operacao
        self.modo_simulacao = modo_simulacao
        self.estrategia = estrategia.lower()
        self.capital_total = capital_total
        
        # Inicializa o trader automático
        self.trader = AutoTrader(
            capital_por_operacao=capital_por_operacao, 
            modo_simulacao=modo_simulacao,
            estrategia=estrategia,
            capital_total=capital_total,
            max_posicoes=4
        )
        
        if capital_total:
            logger.info(f"ManualTrader inicializado com capital total de ${capital_total}")
            logger.info(f"Capital por operação: ${capital_total * 0.1} (10% do capital total)")
        else:
            logger.info(f"ManualTrader inicializado com capital de ${capital_por_operacao} por operação")
            
        logger.info(f"Modo de simulação: {'ATIVADO' if modo_simulacao else 'DESATIVADO'}")
        logger.info(f"Estratégia: {self.estrategia}")
    
    async def executar_operacao(self, symbol, tipo_sinal, preco_entrada, stop_loss=None, take_profit=None):
        """
        Executa uma operação manualmente
        
        Args:
            symbol: Par de trading (ex: 'BTCUSDT')
            tipo_sinal: 'LONG' ou 'SHORT'
            preco_entrada: Preço de entrada
            stop_loss: Preço do stop loss (opcional, será calculado automaticamente se não fornecido)
            take_profit: Preço do take profit (opcional, será calculado automaticamente se não fornecido)
            
        Returns:
            dict: Informações da ordem ou None se falhar
        """
        try:
            # Normaliza o símbolo
            symbol = symbol.upper().replace('/', '')
            
            # Normaliza o tipo de sinal
            tipo_sinal = tipo_sinal.upper()
            if tipo_sinal not in ['LONG', 'SHORT']:
                logger.error(f"Tipo de sinal inválido: {tipo_sinal}. Use 'LONG' ou 'SHORT'.")
                return None
            
            # Calcula stop loss se não fornecido
            if stop_loss is None:
                if tipo_sinal == 'LONG':
                    stop_loss = preco_entrada * 0.99  # 1% abaixo
                else:
                    stop_loss = preco_entrada * 1.01  # 1% acima
            
            # Calcula take profit se não fornecido
            if take_profit is None:
                if tipo_sinal == 'LONG':
                    take_profit = preco_entrada * 1.02  # 2% acima
                else:
                    take_profit = preco_entrada * 0.98  # 2% abaixo
            
            # Cria o sinal
            sinal = {
                'symbol': symbol,
                'signal_type': tipo_sinal,
                'entry_price': preco_entrada,
                'stop_loss': stop_loss,
                'take_profit': take_profit
            }
            
            logger.info(f"Executando operação manual: {json.dumps(sinal)}")
            logger.info(f"Usando estratégia: {self.estrategia}")
            
            # Processa o sinal
            resultado = await self.trader.processar_sinal(sinal)
            
            if resultado:
                logger.info(f"Ordem executada com sucesso para {symbol}")
                return resultado
            else:
                logger.warning(f"Falha ao executar ordem para {symbol}")
                return None
                
        except Exception as e:
            logger.error(f"Erro ao executar operação manual: {e}")
            return None
    
    async def iniciar_monitoramento(self):
        """Inicia o monitoramento de operações"""
        logger.info("Iniciando monitoramento de operações")
        await self.trader.iniciar()

async def main():
    """Função principal"""
    parser = argparse.ArgumentParser(description='Trader Manual para Binance Futures')
    parser.add_argument('--symbol', type=str, required=True, help='Par de trading (ex: BTCUSDT)')
    parser.add_argument('--tipo', type=str, required=True, choices=['LONG', 'SHORT'], help='Tipo de operação')
    parser.add_argument('--preco', type=float, required=True, help='Preço de entrada')
    parser.add_argument('--sl', type=float, help='Stop Loss (opcional)')
    parser.add_argument('--tp', type=float, help='Take Profit (opcional)')
    parser.add_argument('--capital', type=float, default=20, help='Capital por operação em USD (padrão: 20)')
    parser.add_argument('--estrategia', type=str, choices=['scalp', 'swing'], default='swing', 
                        help='Estratégia de trading (padrão: swing)')
    parser.add_argument('--real', action='store_true', help='Modo real (sem simulação)')
    
    args = parser.parse_args()
    
    trader = ManualTrader(
        capital_por_operacao=args.capital, 
        modo_simulacao=not args.real,
        estrategia=args.estrategia
    )
    
    # Executa a operação
    resultado = await trader.executar_operacao(
        symbol=args.symbol,
        tipo_sinal=args.tipo,
        preco_entrada=args.preco,
        stop_loss=args.sl,
        take_profit=args.tp
    )
    
    if resultado:
        print(f"Operação executada com sucesso: {json.dumps(resultado, indent=2)}")
        
        # Inicia o monitoramento
        await trader.iniciar_monitoramento()
    else:
        print("Falha ao executar operação.")

if __name__ == "__main__":
    asyncio.run(main()) 