#!/bin/bash

# Parar o serviço do gerador de sinais
systemctl stop gerador_sinais

# Fazer backup da pasta atual
cd /root
timestamp=$(date +%Y%m%d%H%M%S)
mkdir -p backups
tar -czf backups/gerador_sinais_telegram_backup_$timestamp.tar.gz gerador_sinais_telegram/

# Extrair o novo arquivo zip
unzip -o gerador_sinais_telegram.zip -d gerador_sinais_telegram_temp/

# Mover arquivos importantes da pasta antiga para a nova
cp gerador_sinais_telegram/.env gerador_sinais_telegram_temp/ 2>/dev/null || echo "Arquivo .env não encontrado"
cp gerador_sinais_telegram/session.session gerador_sinais_telegram_temp/ 2>/dev/null || echo "Arquivo session.session não encontrado"
cp gerador_sinais_telegram/session_name.session gerador_sinais_telegram_temp/ 2>/dev/null || echo "Arquivo session_name.session não encontrado"

# Remover a pasta antiga e renomear a nova
rm -rf gerador_sinais_telegram
mv gerador_sinais_telegram_temp gerador_sinais_telegram

# Garantir permissões corretas
chmod -R 755 gerador_sinais_telegram/

# Criar e configurar o ambiente virtual
echo "Criando ambiente virtual..."
cd /root/gerador_sinais_telegram
python3 -m venv venv
source venv/bin/activate
pip install --upgrade pip
pip install -r requirements.txt
deactivate

# Verificar se o ambiente virtual foi criado corretamente
if [ ! -f "/root/gerador_sinais_telegram/venv/bin/python" ]; then
    echo "ERRO: Falha ao criar o ambiente virtual!"
    exit 1
fi

echo "Ambiente virtual criado e configurado com sucesso!"

# Iniciar o serviço novamente
systemctl restart gerador_sinais

# Aguardar alguns segundos para o serviço iniciar
sleep 5

# Verificar o status do serviço
systemctl status gerador_sinais

echo "Atualização concluída com sucesso!" 