import asyncio
import logging
import pytest
import pytest_asyncio
import pandas as pd
import numpy as np
from unittest.mock import AsyncMock, patch, MagicMock, Mock
from datetime import datetime, timedelta

# Importando os módulos a serem testados
from trading_automatizado.auto_trader import AutoTrader
from trading_automatizado.telegram_trader import TelegramTrader
from utils.binance_client import BinanceHandler
from telethon import TelegramClient, events

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('tests/test_trading_integration.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# Fixtures para os testes
@pytest_asyncio.fixture
async def binance_handler():
    """Fixture para criar uma instância mockada do BinanceHandler"""
    with patch('utils.binance_client.Client') as mock_client:
        # Configura o mock do cliente Binance
        mock_client_instance = Mock()
        mock_client.return_value = mock_client_instance
        
        # Cria dados históricos simulados
        dates = pd.date_range(start='2024-01-01', end='2024-01-02', freq='1min')
        mock_data = pd.DataFrame({
            'timestamp': dates,
            'open': np.random.normal(100, 1, len(dates)),
            'high': np.random.normal(101, 1, len(dates)),
            'low': np.random.normal(99, 1, len(dates)),
            'close': np.random.normal(100, 1, len(dates)),
            'volume': np.random.normal(1000, 100, len(dates)),
            'close_time': dates + pd.Timedelta(minutes=1),
            'quote_volume': np.random.normal(100000, 10000, len(dates)),
            'trades': np.random.randint(100, 1000, len(dates)),
            'taker_buy_base': np.random.normal(500, 50, len(dates)),
            'taker_buy_quote': np.random.normal(50000, 5000, len(dates)),
            'ignore': np.zeros(len(dates))
        })
        
        # Configura o mock para retornar dados simulados
        handler = BinanceHandler(use_mock_data=True)
        handler.get_historical_klines = Mock(return_value=mock_data)
        handler.get_current_price = Mock(return_value=100.0)
        
        yield handler

@pytest_asyncio.fixture
async def auto_trader(binance_handler):
    """Fixture para criar uma instância mockada do AutoTrader"""
    with patch('trading_automatizado.auto_trader.BinanceHandler', return_value=binance_handler):
        with patch('trading_automatizado.auto_trader.Client') as mock_client:
            # Configura o mock do cliente Binance
            mock_client_instance = Mock()
            mock_client.return_value = mock_client_instance
            
            # Configura o mock para retornar informações do símbolo
            mock_client_instance.futures_exchange_info.return_value = {
                'symbols': [
                    {
                        'symbol': 'BTCUSDT',
                        'filters': [
                            {
                                'filterType': 'LOT_SIZE',
                                'stepSize': '0.001'
                            }
                        ]
                    }
                ]
            }
            
            # Configura o mock para retornar informações da posição
            mock_client_instance.futures_position_information.return_value = [
                {
                    'symbol': 'BTCUSDT',
                    'positionAmt': '0.0',
                    'entryPrice': '0.0',
                    'markPrice': '100.0',
                    'unRealizedProfit': '0.0',
                    'liquidationPrice': '0.0',
                    'leverage': '20',
                    'maxNotionalValue': '1000000',
                    'marginType': 'isolated',
                    'isolatedMargin': '0.0',
                    'isAutoAddMargin': 'false',
                    'positionSide': 'BOTH',
                    'notional': '0.0',
                    'isolatedWallet': '0.0',
                    'updateTime': 0
                }
            ]
            
            # Configura o mock para retornar informações da ordem
            mock_client_instance.futures_create_order.return_value = {
                'orderId': 12345,
                'symbol': 'BTCUSDT',
                'status': 'NEW',
                'clientOrderId': 'test',
                'price': '0',
                'avgPrice': '0.0',
                'origQty': '0.1',
                'executedQty': '0.0',
                'cumQty': '0.0',
                'cumQuote': '0.0',
                'timeInForce': 'GTC',
                'type': 'MARKET',
                'reduceOnly': False,
                'closePosition': False,
                'side': 'BUY',
                'positionSide': 'BOTH',
                'stopPrice': '0.0',
                'workingType': 'CONTRACT_PRICE',
                'priceProtect': False,
                'origType': 'MARKET',
                'updateTime': 0
            }
            
            trader = AutoTrader(capital_por_operacao=20, modo_simulacao=True, estrategia="swing")
            trader.binance_handler = binance_handler
            
            yield trader

@pytest_asyncio.fixture
async def telegram_trader(auto_trader):
    """Fixture para criar uma instância mockada do TelegramTrader"""
    with patch('trading_automatizado.telegram_trader.TelegramClient') as mock_client:
        with patch('trading_automatizado.telegram_trader.AutoTrader', return_value=auto_trader):
            # Configura o mock do cliente Telegram
            mock_client_instance = AsyncMock()
            mock_client.return_value = mock_client_instance
            mock_client_instance.start = AsyncMock(return_value=True)
            mock_client_instance.is_user_authorized = AsyncMock(return_value=True)
            mock_client_instance.run_until_disconnected = AsyncMock()
            
            trader = TelegramTrader(capital_por_operacao=20, modo_simulacao=True)
            trader.client = mock_client_instance
            trader.trader_scalp = auto_trader
            trader.trader_swing = auto_trader
            
            yield trader

# Testes para o BinanceHandler
@pytest.mark.asyncio
async def test_binance_get_historical_data(binance_handler):
    """Testa a obtenção de dados históricos da Binance"""
    # Obtém dados históricos
    df = binance_handler.get_historical_klines(
        symbol='BTCUSDT',
        interval='1m',
        lookback_days=1
    )
    
    # Verifica se os dados foram retornados corretamente
    assert isinstance(df, pd.DataFrame)
    assert not df.empty
    assert 'open' in df.columns
    assert 'high' in df.columns
    assert 'low' in df.columns
    assert 'close' in df.columns
    assert 'volume' in df.columns
    
    logger.info(f"Dados históricos obtidos com sucesso: {len(df)} registros")

@pytest.mark.asyncio
async def test_binance_get_current_price(binance_handler):
    """Testa a obtenção do preço atual da Binance"""
    # Obtém o preço atual
    price = binance_handler.get_current_price('BTCUSDT')
    
    # Verifica se o preço foi retornado corretamente
    assert isinstance(price, float)
    assert price > 0
    
    logger.info(f"Preço atual obtido com sucesso: {price}")

# Testes para o AutoTrader
@pytest.mark.asyncio
async def test_auto_trader_processar_sinal(auto_trader):
    """Testa o processamento de um sinal pelo AutoTrader"""
    # Cria um sinal de teste
    sinal = {
        'symbol': 'BTCUSDT',
        'signal_type': 'LONG',
        'entry_price': 100.0,
        'stop_loss': 95.0,
        'take_profit': 110.0
    }
    
    # Processa o sinal
    with patch.object(auto_trader, 'executar_ordem', return_value={'orderId': 12345}):
        resultado = await auto_trader.processar_sinal(sinal)
    
    # Verifica se o sinal foi processado corretamente
    assert resultado is not None
    assert 'orderId' in resultado
    assert resultado['orderId'] == 12345
    
    logger.info(f"Sinal processado com sucesso: {resultado}")

@pytest.mark.asyncio
async def test_auto_trader_verificar_volatilidade(auto_trader):
    """Testa a verificação de volatilidade pelo AutoTrader"""
    # Verifica a volatilidade
    resultado = auto_trader.verificar_volatilidade('BTCUSDT')
    
    # Verifica se a volatilidade foi calculada corretamente
    assert isinstance(resultado, bool)
    
    logger.info(f"Verificação de volatilidade concluída: {resultado}")

@pytest.mark.asyncio
async def test_auto_trader_verificar_tendencia(auto_trader):
    """Testa a verificação de tendência pelo AutoTrader"""
    # Verifica a tendência
    resultado = auto_trader.verificar_tendencia('BTCUSDT', 'LONG')
    
    # Verifica se a tendência foi calculada corretamente
    assert isinstance(resultado, bool)
    
    logger.info(f"Verificação de tendência concluída: {resultado}")

@pytest.mark.asyncio
async def test_auto_trader_calcular_quantidade(auto_trader):
    """Testa o cálculo de quantidade pelo AutoTrader"""
    # Calcula a quantidade
    quantidade = auto_trader.calcular_quantidade('BTCUSDT', 100.0)
    
    # Verifica se a quantidade foi calculada corretamente
    assert isinstance(quantidade, float)
    assert quantidade > 0
    
    logger.info(f"Quantidade calculada com sucesso: {quantidade}")

# Testes para o TelegramTrader
@pytest.mark.asyncio
async def test_telegram_trader_extrair_sinal(telegram_trader):
    """Testa a extração de sinal de uma mensagem do Telegram"""
    # Mensagem de teste
    mensagem = """
    SINAL DE COMPRA PARA BTCUSDT
    ENTRADA: 100.0
    STOP LOSS: 95.0
    TAKE PROFIT: 110.0
    ESTRATEGIA: SWING
    """
    
    # Extrai o sinal
    sinal = telegram_trader.extrair_sinal(mensagem)
    
    # Verifica se o sinal foi extraído corretamente
    assert sinal is not None
    assert sinal['symbol'] == 'BTCUSDT'
    assert sinal['signal_type'] == 'LONG'
    assert sinal['entry_price'] == 100.0
    assert sinal['stop_loss'] == 95.0
    assert sinal['take_profit'] == 110.0
    assert sinal['estrategia'] == 'swing'
    
    logger.info(f"Sinal extraído com sucesso: {sinal}")

@pytest.mark.asyncio
async def test_telegram_trader_determinar_estrategia(telegram_trader):
    """Testa a determinação de estratégia pelo TelegramTrader"""
    # Testa com estratégia explícita
    estrategia1 = telegram_trader.determinar_estrategia("ESTRATEGIA: SCALP", "BTCUSDT")
    assert estrategia1 == 'scalp'
    
    # Testa com timeframe
    estrategia2 = telegram_trader.determinar_estrategia("TIMEFRAME: 4H", "BTCUSDT")
    assert estrategia2 == 'swing'
    
    # Testa sem informações
    estrategia3 = telegram_trader.determinar_estrategia("Apenas um texto qualquer", "BTCUSDT")
    assert estrategia3 == 'swing'  # Padrão baseado no backtesting
    
    logger.info("Determinação de estratégia testada com sucesso")

@pytest.mark.asyncio
async def test_telegram_trader_processar_mensagem(telegram_trader):
    """Testa o processamento de mensagem pelo TelegramTrader"""
    # Cria um evento de mensagem simulado
    event = MagicMock()
    event.message.text = """
    SINAL DE COMPRA PARA BTCUSDT
    ENTRADA: 100.0
    STOP LOSS: 95.0
    TAKE PROFIT: 110.0
    """
    
    # Processa a mensagem
    with patch.object(telegram_trader.trader_swing, 'processar_sinal', return_value={'orderId': 12345}):
        await telegram_trader.processar_mensagem(event)
    
    logger.info("Processamento de mensagem testado com sucesso")

# Teste de integração completo
@pytest.mark.asyncio
async def test_integracao_completa(telegram_trader, auto_trader, binance_handler):
    """Testa a integração completa do sistema"""
    # Cria um evento de mensagem simulado
    event = MagicMock()
    event.message.text = """
    SINAL DE COMPRA PARA BTCUSDT
    ENTRADA: 100.0
    STOP LOSS: 95.0
    TAKE PROFIT: 110.0
    ESTRATEGIA: SWING
    """
    
    # Processa a mensagem
    with patch.object(auto_trader, 'executar_ordem', return_value={'orderId': 12345}):
        await telegram_trader.processar_mensagem(event)
    
    logger.info("Teste de integração completo realizado com sucesso")

if __name__ == "__main__":
    pytest.main(["-xvs", "test_trading_integration.py"]) 