{"ast": null, "code": "import StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport Dimensions from \"react-native-web/dist/exports/Dimensions\";\nvar windowHeight = Dimensions.get('window').height;\nvar styles = StyleSheet.create({\n  scrollView: {\n    height: windowHeight - 280\n  },\n  emptyState: {\n    color: '#fff',\n    fontSize: 16,\n    textAlign: 'center',\n    fontFamily: 'Poppins_400Regular',\n    marginTop: 18\n  }\n});\nexport default styles;", "map": {"version": 3, "names": ["windowHeight", "Dimensions", "get", "height", "styles", "StyleSheet", "create", "scrollView", "emptyState", "color", "fontSize", "textAlign", "fontFamily", "marginTop"], "sources": ["E:/CryptoSignalsApp/src/pages/Channels/styles.js"], "sourcesContent": ["import { StyleSheet, Dimensions } from 'react-native';\r\nconst windowHeight = Dimensions.get('window').height;\r\n\r\nconst styles = StyleSheet.create({\r\n  scrollView: {\r\n    height: windowHeight - 280\r\n  },\r\n  emptyState: {\r\n    color: '#fff',\r\n    fontSize: 16,\r\n    textAlign: 'center',\r\n    fontFamily: 'Poppins_400Regular',\r\n    marginTop: 18\r\n  }\r\n})\r\n\r\nexport default styles;"], "mappings": ";;AACA,IAAMA,YAAY,GAAGC,UAAU,CAACC,GAAG,CAAC,QAAQ,CAAC,CAACC,MAAM;AAEpD,IAAMC,MAAM,GAAGC,UAAU,CAACC,MAAM,CAAC;EAC/BC,UAAU,EAAE;IACVJ,MAAM,EAAEH,YAAY,GAAG;EACzB,CAAC;EACDQ,UAAU,EAAE;IACVC,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,QAAQ;IACnBC,UAAU,EAAE,oBAAoB;IAChCC,SAAS,EAAE;EACb;AACF,CAAC,CAAC;AAEF,eAAeT,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}