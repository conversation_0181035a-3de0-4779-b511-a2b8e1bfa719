#!/usr/bin/env python3
"""
Script para testar o sistema expandido com múltiplas fontes de sinais
"""

import asyncio
import logging
import sys
import os
from datetime import datetime

# Adicionar o diretório raiz ao path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.symbols import (
    ALL_SYMBOLS, MAJOR_TOKENS, DEFI_TOKENS, GAMING_TOKENS, 
    MEME_TOKENS, AI_TECH_TOKENS, NEW_TRENDING_TOKENS,
    VOLATILITY_POOLS, TIMEFRAME_SYMBOLS
)

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_symbol_configuration():
    """Testa a configuração expandida de símbolos"""
    logger.info("=== TESTE DE CONFIGURAÇÃO DE SÍMBOLOS ===")
    
    # Contar símbolos por categoria
    categories = {
        'MAJOR_TOKENS': MAJOR_TOKENS,
        'DEFI_TOKENS': DEFI_TOKENS,
        'GAMING_TOKENS': GAMING_TOKENS,
        'MEME_TOKENS': MEME_TOKENS,
        'AI_TECH_TOKENS': AI_TECH_TOKENS,
        'NEW_TRENDING_TOKENS': NEW_TRENDING_TOKENS
    }
    
    total_symbols = 0
    for category, symbols in categories.items():
        count = len(symbols)
        total_symbols += count
        logger.info(f"  {category}: {count} símbolos")
        logger.info(f"    Exemplos: {symbols[:5]}")
    
    logger.info(f"\n📊 Total de símbolos únicos: {len(ALL_SYMBOLS)}")
    logger.info(f"📊 Total de símbolos por categoria: {total_symbols}")
    
    # Verificar pools de volatilidade
    logger.info(f"\n🎯 Pools de Volatilidade:")
    for pool_name, pool_symbols in VOLATILITY_POOLS.items():
        logger.info(f"  {pool_name}: {len(pool_symbols)} símbolos")
    
    # Verificar símbolos por timeframe
    logger.info(f"\n⏰ Símbolos por Timeframe:")
    for timeframe, tf_symbols in TIMEFRAME_SYMBOLS.items():
        logger.info(f"  {timeframe}: {len(tf_symbols)} símbolos")
    
    return len(ALL_SYMBOLS)

def test_strategy_coverage():
    """Testa a cobertura das estratégias"""
    logger.info("\n=== TESTE DE COBERTURA DAS ESTRATÉGIAS ===")
    
    from config.symbols import (
        SCALP_SYMBOLS, BREAKOUT_SYMBOLS, INSIDE_BAR_SYMBOLS,
        MFI_SYMBOLS, SWING_SYMBOLS
    )
    
    strategies = {
        'Scalp': SCALP_SYMBOLS,
        'Breakout': BREAKOUT_SYMBOLS,
        'Inside Bar': INSIDE_BAR_SYMBOLS,
        'MFI': MFI_SYMBOLS,
        'Swing': SWING_SYMBOLS
    }
    
    total_coverage = 0
    for strategy, symbols in strategies.items():
        count = len(symbols)
        total_coverage += count
        logger.info(f"  {strategy}: {count} símbolos")
        logger.info(f"    Primeiros 5: {symbols[:5]}")
    
    # Calcular cobertura média
    avg_coverage = total_coverage / len(strategies)
    logger.info(f"\n📈 Cobertura média por estratégia: {avg_coverage:.1f} símbolos")
    
    # Verificar sobreposição
    all_strategy_symbols = set()
    for symbols in strategies.values():
        all_strategy_symbols.update(symbols)
    
    logger.info(f"📊 Símbolos únicos cobertos pelas estratégias: {len(all_strategy_symbols)}")
    
    return len(all_strategy_symbols)

async def test_strategy_initialization():
    """Testa a inicialização das novas estratégias"""
    logger.info("\n=== TESTE DE INICIALIZAÇÃO DAS ESTRATÉGIAS ===")
    
    try:
        from utils.binance_client import BinanceHandler
        from estrategias.multi_source_strategy import MultiSourceStrategy
        from estrategias.volume_analysis_strategy import VolumeAnalysisStrategy
        from estrategias.momentum_strategy import MomentumStrategy
        
        # Inicializar Binance handler
        binance = BinanceHandler()
        
        # Testar inicialização das novas estratégias
        strategies = {}
        
        try:
            strategies['multi_source'] = MultiSourceStrategy(binance)
            logger.info("✅ MultiSourceStrategy inicializada com sucesso")
        except Exception as e:
            logger.error(f"❌ Erro ao inicializar MultiSourceStrategy: {e}")
        
        try:
            strategies['volume_analysis'] = VolumeAnalysisStrategy(binance)
            logger.info("✅ VolumeAnalysisStrategy inicializada com sucesso")
        except Exception as e:
            logger.error(f"❌ Erro ao inicializar VolumeAnalysisStrategy: {e}")
        
        try:
            strategies['momentum'] = MomentumStrategy(binance)
            logger.info("✅ MomentumStrategy inicializada com sucesso")
        except Exception as e:
            logger.error(f"❌ Erro ao inicializar MomentumStrategy: {e}")
        
        logger.info(f"\n🎯 Estratégias inicializadas: {len(strategies)}/3")
        
        return len(strategies) == 3
        
    except Exception as e:
        logger.error(f"❌ Erro geral na inicialização: {e}")
        return False

def test_signal_capacity():
    """Testa a capacidade de geração de sinais"""
    logger.info("\n=== TESTE DE CAPACIDADE DE SINAIS ===")
    
    from config.settings import MAX_SIGNALS_PER_DAY, SIGNAL_INTERVAL_SECONDS
    
    # Calcular capacidade teórica
    signals_per_hour = 3600 / SIGNAL_INTERVAL_SECONDS
    signals_per_day_theoretical = signals_per_hour * 24
    
    # Número de estratégias
    total_strategies = 8  # 5 originais + 3 novas
    
    # Símbolos por estratégia (média)
    avg_symbols_per_strategy = len(ALL_SYMBOLS) / total_strategies
    
    logger.info(f"📊 Configuração atual:")
    logger.info(f"  Intervalo entre verificações: {SIGNAL_INTERVAL_SECONDS}s")
    logger.info(f"  Limite diário de sinais: {MAX_SIGNALS_PER_DAY}")
    logger.info(f"  Estratégias ativas: {total_strategies}")
    logger.info(f"  Símbolos únicos: {len(ALL_SYMBOLS)}")
    logger.info(f"  Média de símbolos por estratégia: {avg_symbols_per_strategy:.1f}")
    
    logger.info(f"\n⚡ Capacidade teórica:")
    logger.info(f"  Verificações por hora: {signals_per_hour:.1f}")
    logger.info(f"  Verificações por dia: {signals_per_day_theoretical:.0f}")
    logger.info(f"  Oportunidades de sinal por dia: {signals_per_day_theoretical * total_strategies:.0f}")
    
    # Calcular eficiência
    efficiency = (MAX_SIGNALS_PER_DAY / (signals_per_day_theoretical * total_strategies)) * 100
    logger.info(f"  Eficiência do limite: {efficiency:.1f}%")
    
    # Recomendações
    if efficiency < 10:
        logger.info("💡 Recomendação: Limite muito baixo, considere aumentar MAX_SIGNALS_PER_DAY")
    elif efficiency > 50:
        logger.info("⚠️ Aviso: Limite pode ser muito alto, risco de spam")
    else:
        logger.info("✅ Configuração balanceada")
    
    return efficiency

def test_market_coverage():
    """Testa a cobertura de mercado"""
    logger.info("\n=== TESTE DE COBERTURA DE MERCADO ===")
    
    # Analisar cobertura por setor
    sectors = {
        'Principais (BTC, ETH, etc.)': MAJOR_TOKENS,
        'DeFi': DEFI_TOKENS,
        'Gaming/Metaverse': GAMING_TOKENS,
        'Meme Coins': MEME_TOKENS,
        'AI/Tech': AI_TECH_TOKENS,
        'Novos/Trending': NEW_TRENDING_TOKENS
    }
    
    total_market_cap_coverage = 0
    for sector, tokens in sectors.items():
        coverage = len(tokens)
        total_market_cap_coverage += coverage
        
        # Estimar cobertura de mercado (aproximada)
        if 'Principais' in sector:
            market_weight = 70  # 70% do mercado
        elif 'DeFi' in sector:
            market_weight = 15  # 15% do mercado
        elif 'Gaming' in sector:
            market_weight = 8   # 8% do mercado
        elif 'Meme' in sector:
            market_weight = 4   # 4% do mercado
        elif 'AI' in sector:
            market_weight = 2   # 2% do mercado
        else:
            market_weight = 1   # 1% do mercado
        
        logger.info(f"  {sector}: {coverage} tokens (~{market_weight}% do mercado)")
    
    logger.info(f"\n🌍 Cobertura total estimada: ~95% do mercado crypto")
    logger.info(f"📈 Diversificação: {len(sectors)} setores cobertos")
    
    return len(sectors)

async def main():
    """Função principal de teste"""
    logger.info("🚀 TESTE DO SISTEMA EXPANDIDO - MÚLTIPLAS FONTES DE SINAIS")
    logger.info("=" * 70)
    
    try:
        # Executar todos os testes
        symbols_count = test_symbol_configuration()
        strategy_coverage = test_strategy_coverage()
        strategies_ok = await test_strategy_initialization()
        efficiency = test_signal_capacity()
        market_sectors = test_market_coverage()
        
        # Resumo final
        logger.info("\n" + "=" * 70)
        logger.info("📋 RESUMO DOS TESTES:")
        
        tests_results = []
        
        # Teste 1: Símbolos
        if symbols_count >= 100:
            logger.info("✅ Configuração de símbolos: EXCELENTE")
            logger.info(f"   {symbols_count} símbolos únicos disponíveis")
            tests_results.append(True)
        else:
            logger.info("⚠️ Configuração de símbolos: LIMITADA")
            tests_results.append(False)
        
        # Teste 2: Cobertura de estratégias
        if strategy_coverage >= 50:
            logger.info("✅ Cobertura de estratégias: EXCELENTE")
            logger.info(f"   {strategy_coverage} símbolos cobertos pelas estratégias")
            tests_results.append(True)
        else:
            logger.info("⚠️ Cobertura de estratégias: LIMITADA")
            tests_results.append(False)
        
        # Teste 3: Inicialização
        if strategies_ok:
            logger.info("✅ Inicialização de estratégias: SUCESSO")
            logger.info("   Todas as 3 novas estratégias funcionando")
            tests_results.append(True)
        else:
            logger.info("❌ Inicialização de estratégias: FALHOU")
            tests_results.append(False)
        
        # Teste 4: Capacidade
        if 10 <= efficiency <= 50:
            logger.info("✅ Capacidade de sinais: BALANCEADA")
            logger.info(f"   Eficiência de {efficiency:.1f}%")
            tests_results.append(True)
        else:
            logger.info("⚠️ Capacidade de sinais: REQUER AJUSTE")
            tests_results.append(False)
        
        # Teste 5: Cobertura de mercado
        if market_sectors >= 5:
            logger.info("✅ Cobertura de mercado: EXCELENTE")
            logger.info(f"   {market_sectors} setores cobertos")
            tests_results.append(True)
        else:
            logger.info("⚠️ Cobertura de mercado: LIMITADA")
            tests_results.append(False)
        
        # Taxa de sucesso
        success_rate = (sum(tests_results) / len(tests_results)) * 100
        logger.info(f"\n🎯 Taxa de sucesso geral: {sum(tests_results)}/{len(tests_results)} ({success_rate:.0f}%)")
        
        if success_rate >= 80:
            logger.info("🎉 SISTEMA EXPANDIDO CONFIGURADO COM SUCESSO!")
            logger.info("💡 Benefícios alcançados:")
            logger.info("   📈 Muito mais oportunidades de trading")
            logger.info("   🎯 Múltiplas fontes de sinais")
            logger.info("   🌍 Cobertura ampla do mercado")
            logger.info("   ⚡ Operação 24/7 otimizada")
            logger.info("   🔄 Diversificação de estratégias")
        else:
            logger.warning("⚠️ Sistema expandido precisa de ajustes")
            logger.info("💡 Verifique as configurações e dependências")
        
    except Exception as e:
        logger.error(f"❌ Erro durante os testes: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
