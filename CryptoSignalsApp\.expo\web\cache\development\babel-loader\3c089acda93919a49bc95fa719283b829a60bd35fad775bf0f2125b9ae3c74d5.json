{"ast": null, "code": "import { getHeaderTitle, HeaderBackContext } from '@react-navigation/elements';\nimport { NavigationContext, NavigationRouteContext } from '@react-navigation/native';\nimport * as React from 'react';\nimport Animated from \"react-native-web/dist/exports/Animated\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport { forNoAnimation, forSlideLeft, forSlideRight, forSlideUp } from \"../../TransitionConfigs/HeaderStyleInterpolators\";\nimport Header from \"./Header\";\nexport default function HeaderContainer(_ref) {\n  var mode = _ref.mode,\n    scenes = _ref.scenes,\n    layout = _ref.layout,\n    getPreviousScene = _ref.getPreviousScene,\n    getFocusedRoute = _ref.getFocusedRoute,\n    onContentHeightChange = _ref.onContentHeightChange,\n    style = _ref.style;\n  var focusedRoute = getFocusedRoute();\n  var parentHeaderBack = React.useContext(HeaderBackContext);\n  return React.createElement(Animated.View, {\n    pointerEvents: \"box-none\",\n    style: style\n  }, scenes.slice(-3).map(function (scene, i, self) {\n    var _self, _self2;\n    if (mode === 'screen' && i !== self.length - 1 || !scene) {\n      return null;\n    }\n    var _scene$descriptor$opt = scene.descriptor.options,\n      header = _scene$descriptor$opt.header,\n      headerMode = _scene$descriptor$opt.headerMode,\n      _scene$descriptor$opt2 = _scene$descriptor$opt.headerShown,\n      headerShown = _scene$descriptor$opt2 === void 0 ? true : _scene$descriptor$opt2,\n      headerTransparent = _scene$descriptor$opt.headerTransparent,\n      headerStyleInterpolator = _scene$descriptor$opt.headerStyleInterpolator;\n    if (headerMode !== mode || !headerShown) {\n      return null;\n    }\n    var isFocused = focusedRoute.key === scene.descriptor.route.key;\n    var previousScene = getPreviousScene({\n      route: scene.descriptor.route\n    });\n    var headerBack = parentHeaderBack;\n    if (previousScene) {\n      var _previousScene$descri = previousScene.descriptor,\n        options = _previousScene$descri.options,\n        route = _previousScene$descri.route;\n      headerBack = previousScene ? {\n        title: getHeaderTitle(options, route.name)\n      } : parentHeaderBack;\n    }\n    var previousDescriptor = (_self = self[i - 1]) === null || _self === void 0 ? void 0 : _self.descriptor;\n    var nextDescriptor = (_self2 = self[i + 1]) === null || _self2 === void 0 ? void 0 : _self2.descriptor;\n    var _ref2 = (previousDescriptor === null || previousDescriptor === void 0 ? void 0 : previousDescriptor.options) || {},\n      _ref2$headerShown = _ref2.headerShown,\n      previousHeaderShown = _ref2$headerShown === void 0 ? true : _ref2$headerShown,\n      previousHeaderMode = _ref2.headerMode;\n    var nextHeaderlessScene = self.slice(i + 1).find(function (scene) {\n      var _ref3 = (scene === null || scene === void 0 ? void 0 : scene.descriptor.options) || {},\n        _ref3$headerShown = _ref3.headerShown,\n        currentHeaderShown = _ref3$headerShown === void 0 ? true : _ref3$headerShown,\n        currentHeaderMode = _ref3.headerMode;\n      return currentHeaderShown === false || currentHeaderMode === 'screen';\n    });\n    var _ref4 = (nextHeaderlessScene === null || nextHeaderlessScene === void 0 ? void 0 : nextHeaderlessScene.descriptor.options) || {},\n      nextHeaderlessGestureDirection = _ref4.gestureDirection;\n    var isHeaderStatic = (previousHeaderShown === false || previousHeaderMode === 'screen') && !nextDescriptor || nextHeaderlessScene;\n    var props = {\n      layout: layout,\n      back: headerBack,\n      progress: scene.progress,\n      options: scene.descriptor.options,\n      route: scene.descriptor.route,\n      navigation: scene.descriptor.navigation,\n      styleInterpolator: mode === 'float' ? isHeaderStatic ? nextHeaderlessGestureDirection === 'vertical' || nextHeaderlessGestureDirection === 'vertical-inverted' ? forSlideUp : nextHeaderlessGestureDirection === 'horizontal-inverted' ? forSlideRight : forSlideLeft : headerStyleInterpolator : forNoAnimation\n    };\n    return React.createElement(NavigationContext.Provider, {\n      key: scene.descriptor.route.key,\n      value: scene.descriptor.navigation\n    }, React.createElement(NavigationRouteContext.Provider, {\n      value: scene.descriptor.route\n    }, React.createElement(View, {\n      onLayout: onContentHeightChange ? function (e) {\n        var height = e.nativeEvent.layout.height;\n        onContentHeightChange({\n          route: scene.descriptor.route,\n          height: height\n        });\n      } : undefined,\n      pointerEvents: isFocused ? 'box-none' : 'none',\n      accessibilityElementsHidden: !isFocused,\n      importantForAccessibility: isFocused ? 'auto' : 'no-hide-descendants',\n      style: mode === 'float' && !isFocused || headerTransparent ? styles.header : null\n    }, header !== undefined ? header(props) : React.createElement(Header, props))));\n  }));\n}\nvar styles = StyleSheet.create({\n  header: {\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    right: 0\n  }\n});", "map": {"version": 3, "names": ["getHeaderTitle", "HeaderBackContext", "NavigationContext", "NavigationRouteContext", "React", "Animated", "StyleSheet", "View", "forNoAnimation", "forSlideLeft", "forSlideRight", "forSlideUp", "Header", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref", "mode", "scenes", "layout", "getPreviousScene", "getFocusedRoute", "onContentHeightChange", "style", "focusedRoute", "parentHeaderBack", "useContext", "createElement", "pointerEvents", "slice", "map", "scene", "i", "self", "_self", "_self2", "length", "_scene$descriptor$opt", "descriptor", "options", "header", "headerMode", "_scene$descriptor$opt2", "headerShown", "headerTransparent", "headerStyleInterpolator", "isFocused", "key", "route", "previousScene", "headerBack", "_previousScene$descri", "title", "name", "previousDescriptor", "nextDescriptor", "_ref2", "_ref2$headerShown", "previousHeaderShown", "previousHeaderMode", "nextHeaderlessScene", "find", "_ref3", "_ref3$headerShown", "currentHeaderShown", "currentHeaderMode", "_ref4", "nextHeaderlessGestureDirection", "gestureDirection", "isHeaderStatic", "props", "back", "progress", "navigation", "styleInterpolator", "Provider", "value", "onLayout", "e", "height", "nativeEvent", "undefined", "accessibilityElementsHidden", "importantForAccessibility", "styles", "create", "position", "top", "left", "right"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\@react-navigation\\stack\\src\\views\\Header\\HeaderContainer.tsx"], "sourcesContent": ["import { getHeaderTitle, HeaderBackContext } from '@react-navigation/elements';\nimport {\n  NavigationContext,\n  NavigationRouteContext,\n  ParamListBase,\n  Route,\n} from '@react-navigation/native';\nimport * as React from 'react';\nimport { Animated, StyleProp, StyleSheet, View, ViewStyle } from 'react-native';\n\nimport {\n  forNoAnimation,\n  forSlideLeft,\n  forSlideRight,\n  forSlideUp,\n} from '../../TransitionConfigs/HeaderStyleInterpolators';\nimport type {\n  Layout,\n  Scene,\n  StackHeaderMode,\n  StackHeaderProps,\n  StackNavigationProp,\n} from '../../types';\nimport Header from './Header';\n\nexport type Props = {\n  mode: StackHeaderMode;\n  layout: Layout;\n  scenes: (Scene | undefined)[];\n  getPreviousScene: (props: { route: Route<string> }) => Scene | undefined;\n  getFocusedRoute: () => Route<string>;\n  onContentHeightChange?: (props: {\n    route: Route<string>;\n    height: number;\n  }) => void;\n  style?: Animated.WithAnimatedValue<StyleProp<ViewStyle>>;\n};\n\nexport default function HeaderContainer({\n  mode,\n  scenes,\n  layout,\n  getPreviousScene,\n  getFocusedRoute,\n  onContentHeightChange,\n  style,\n}: Props) {\n  const focusedRoute = getFocusedRoute();\n  const parentHeaderBack = React.useContext(HeaderBackContext);\n\n  return (\n    <Animated.View pointerEvents=\"box-none\" style={style}>\n      {scenes.slice(-3).map((scene, i, self) => {\n        if ((mode === 'screen' && i !== self.length - 1) || !scene) {\n          return null;\n        }\n\n        const {\n          header,\n          headerMode,\n          headerShown = true,\n          headerTransparent,\n          headerStyleInterpolator,\n        } = scene.descriptor.options;\n\n        if (headerMode !== mode || !headerShown) {\n          return null;\n        }\n\n        const isFocused = focusedRoute.key === scene.descriptor.route.key;\n        const previousScene = getPreviousScene({\n          route: scene.descriptor.route,\n        });\n\n        let headerBack = parentHeaderBack;\n\n        if (previousScene) {\n          const { options, route } = previousScene.descriptor;\n\n          headerBack = previousScene\n            ? { title: getHeaderTitle(options, route.name) }\n            : parentHeaderBack;\n        }\n\n        // If the screen is next to a headerless screen, we need to make the header appear static\n        // This makes the header look like it's moving with the screen\n        const previousDescriptor = self[i - 1]?.descriptor;\n        const nextDescriptor = self[i + 1]?.descriptor;\n\n        const {\n          headerShown: previousHeaderShown = true,\n          headerMode: previousHeaderMode,\n        } = previousDescriptor?.options || {};\n\n        // If any of the next screens don't have a header or header is part of the screen\n        // Then we need to move this header offscreen so that it doesn't cover it\n        const nextHeaderlessScene = self.slice(i + 1).find((scene) => {\n          const {\n            headerShown: currentHeaderShown = true,\n            headerMode: currentHeaderMode,\n          } = scene?.descriptor.options || {};\n\n          return currentHeaderShown === false || currentHeaderMode === 'screen';\n        });\n\n        const { gestureDirection: nextHeaderlessGestureDirection } =\n          nextHeaderlessScene?.descriptor.options || {};\n\n        const isHeaderStatic =\n          ((previousHeaderShown === false || previousHeaderMode === 'screen') &&\n            // We still need to animate when coming back from next scene\n            // A hacky way to check this is if the next scene exists\n            !nextDescriptor) ||\n          nextHeaderlessScene;\n\n        const props: StackHeaderProps = {\n          layout,\n          back: headerBack,\n          progress: scene.progress,\n          options: scene.descriptor.options,\n          route: scene.descriptor.route,\n          navigation: scene.descriptor\n            .navigation as StackNavigationProp<ParamListBase>,\n          styleInterpolator:\n            mode === 'float'\n              ? isHeaderStatic\n                ? nextHeaderlessGestureDirection === 'vertical' ||\n                  nextHeaderlessGestureDirection === 'vertical-inverted'\n                  ? forSlideUp\n                  : nextHeaderlessGestureDirection === 'horizontal-inverted'\n                  ? forSlideRight\n                  : forSlideLeft\n                : headerStyleInterpolator\n              : forNoAnimation,\n        };\n\n        return (\n          <NavigationContext.Provider\n            key={scene.descriptor.route.key}\n            value={scene.descriptor.navigation}\n          >\n            <NavigationRouteContext.Provider value={scene.descriptor.route}>\n              <View\n                onLayout={\n                  onContentHeightChange\n                    ? (e) => {\n                        const { height } = e.nativeEvent.layout;\n\n                        onContentHeightChange({\n                          route: scene.descriptor.route,\n                          height,\n                        });\n                      }\n                    : undefined\n                }\n                pointerEvents={isFocused ? 'box-none' : 'none'}\n                accessibilityElementsHidden={!isFocused}\n                importantForAccessibility={\n                  isFocused ? 'auto' : 'no-hide-descendants'\n                }\n                style={\n                  // Avoid positioning the focused header absolutely\n                  // Otherwise accessibility tools don't seem to be able to find it\n                  (mode === 'float' && !isFocused) || headerTransparent\n                    ? styles.header\n                    : null\n                }\n              >\n                {header !== undefined ? header(props) : <Header {...props} />}\n              </View>\n            </NavigationRouteContext.Provider>\n          </NavigationContext.Provider>\n        );\n      })}\n    </Animated.View>\n  );\n}\n\nconst styles = StyleSheet.create({\n  header: {\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    right: 0,\n  },\n});\n"], "mappings": "AAAA,SAASA,cAAc,EAAEC,iBAAiB,QAAQ,4BAA4B;AAC9E,SACEC,iBAAiB,EACjBC,sBAAsB,QAGjB,0BAA0B;AACjC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAG9B,SACEC,cAAc,EACdC,YAAY,EACZC,aAAa,EACbC,UAAU;AASZ,OAAOC,MAAM;AAeb,eAAe,SAASC,eAAeA,CAAAC,IAAA,EAQ7B;EAAA,IAPRC,IAAI,GAOED,IAAA,CAPNC,IAAI;IACJC,MAAM,GAMAF,IAAA,CANNE,MAAM;IACNC,MAAM,GAKAH,IAAA,CALNG,MAAM;IACNC,gBAAgB,GAIVJ,IAAA,CAJNI,gBAAgB;IAChBC,eAAe,GAGTL,IAAA,CAHNK,eAAe;IACfC,qBAAqB,GAEfN,IAAA,CAFNM,qBAAqB;IACrBC,KAAA,GACMP,IAAA,CADNO,KAAA;EAEA,IAAMC,YAAY,GAAGH,eAAe,EAAE;EACtC,IAAMI,gBAAgB,GAAGnB,KAAK,CAACoB,UAAU,CAACvB,iBAAiB,CAAC;EAE5D,OACEG,KAAA,CAAAqB,aAAA,CAACpB,QAAQ,CAACE,IAAI;IAACmB,aAAa,EAAC,UAAU;IAACL,KAAK,EAAEA;EAAM,GAClDL,MAAM,CAACW,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,UAACC,KAAK,EAAEC,CAAC,EAAEC,IAAI,EAAK;IAAA,IAAAC,KAAA,EAAAC,MAAA;IACxC,IAAKlB,IAAI,KAAK,QAAQ,IAAIe,CAAC,KAAKC,IAAI,CAACG,MAAM,GAAG,CAAC,IAAK,CAACL,KAAK,EAAE;MAC1D,OAAO,IAAI;IACb;IAEA,IAAAM,qBAAA,GAMIN,KAAK,CAACO,UAAU,CAACC,OAAO;MAL1BC,MAAM,GAAAH,qBAAA,CAANG,MAAM;MACNC,UAAU,GAAAJ,qBAAA,CAAVI,UAAU;MAAAC,sBAAA,GAAAL,qBAAA,CACVM,WAAW;MAAXA,WAAW,GAAAD,sBAAA,cAAG,IAAI,GAAAA,sBAAA;MAClBE,iBAAiB,GAAAP,qBAAA,CAAjBO,iBAAiB;MACjBC,uBAAA,GAAAR,qBAAA,CAAAQ,uBAAA;IAGF,IAAIJ,UAAU,KAAKxB,IAAI,IAAI,CAAC0B,WAAW,EAAE;MACvC,OAAO,IAAI;IACb;IAEA,IAAMG,SAAS,GAAGtB,YAAY,CAACuB,GAAG,KAAKhB,KAAK,CAACO,UAAU,CAACU,KAAK,CAACD,GAAG;IACjE,IAAME,aAAa,GAAG7B,gBAAgB,CAAC;MACrC4B,KAAK,EAAEjB,KAAK,CAACO,UAAU,CAACU;IAC1B,CAAC,CAAC;IAEF,IAAIE,UAAU,GAAGzB,gBAAgB;IAEjC,IAAIwB,aAAa,EAAE;MACjB,IAAAE,qBAAA,GAA2BF,aAAa,CAACX,UAAU;QAA3CC,OAAO,GAAAY,qBAAA,CAAPZ,OAAO;QAAES,KAAA,GAAAG,qBAAA,CAAAH,KAAA;MAEjBE,UAAU,GAAGD,aAAa,GACtB;QAAEG,KAAK,EAAElD,cAAc,CAACqC,OAAO,EAAES,KAAK,CAACK,IAAI;MAAE,CAAC,GAC9C5B,gBAAgB;IACtB;IAIA,IAAM6B,kBAAkB,IAAApB,KAAA,GAAGD,IAAI,CAACD,CAAC,GAAG,CAAC,CAAC,cAAAE,KAAA,uBAAXA,KAAA,CAAaI,UAAU;IAClD,IAAMiB,cAAc,IAAApB,MAAA,GAAGF,IAAI,CAACD,CAAC,GAAG,CAAC,CAAC,cAAAG,MAAA,uBAAXA,MAAA,CAAaG,UAAU;IAE9C,IAAAkB,KAAA,GAGI,CAAAF,kBAAkB,aAAlBA,kBAAkB,uBAAlBA,kBAAkB,CAAEf,OAAO,KAAI,CAAC,CAAC;MAAAkB,iBAAA,GAAAD,KAAA,CAFnCb,WAAW;MAAEe,mBAAmB,GAAAD,iBAAA,cAAG,IAAI,GAAAA,iBAAA;MAC3BE,kBAAA,GAAAH,KAAA,CAAZf,UAAU;IAKZ,IAAMmB,mBAAmB,GAAG3B,IAAI,CAACJ,KAAK,CAACG,CAAC,GAAG,CAAC,CAAC,CAAC6B,IAAI,CAAE,UAAA9B,KAAK,EAAK;MAC5D,IAAA+B,KAAA,GAGI,CAAA/B,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEO,UAAU,CAACC,OAAO,KAAI,CAAC,CAAC;QAAAwB,iBAAA,GAAAD,KAAA,CAFjCnB,WAAW;QAAEqB,kBAAkB,GAAAD,iBAAA,cAAG,IAAI,GAAAA,iBAAA;QAC1BE,iBAAA,GAAAH,KAAA,CAAZrB,UAAU;MAGZ,OAAOuB,kBAAkB,KAAK,KAAK,IAAIC,iBAAiB,KAAK,QAAQ;IACvE,CAAC,CAAC;IAEF,IAAAC,KAAA,GACE,CAAAN,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAEtB,UAAU,CAACC,OAAO,KAAI,CAAC,CAAC;MADrB4B,8BAAA,GAAAD,KAAA,CAAlBE,gBAAgB;IAGxB,IAAMC,cAAc,GACjB,CAACX,mBAAmB,KAAK,KAAK,IAAIC,kBAAkB,KAAK,QAAQ,KAGhE,CAACJ,cAAc,IACjBK,mBAAmB;IAErB,IAAMU,KAAuB,GAAG;MAC9BnD,MAAM,EAANA,MAAM;MACNoD,IAAI,EAAErB,UAAU;MAChBsB,QAAQ,EAAEzC,KAAK,CAACyC,QAAQ;MACxBjC,OAAO,EAAER,KAAK,CAACO,UAAU,CAACC,OAAO;MACjCS,KAAK,EAAEjB,KAAK,CAACO,UAAU,CAACU,KAAK;MAC7ByB,UAAU,EAAE1C,KAAK,CAACO,UAAU,CACzBmC,UAAgD;MACnDC,iBAAiB,EACfzD,IAAI,KAAK,OAAO,GACZoD,cAAc,GACZF,8BAA8B,KAAK,UAAU,IAC7CA,8BAA8B,KAAK,mBAAmB,GACpDtD,UAAU,GACVsD,8BAA8B,KAAK,qBAAqB,GACxDvD,aAAa,GACbD,YAAY,GACdkC,uBAAuB,GACzBnC;IACR,CAAC;IAED,OACEJ,KAAA,CAAAqB,aAAA,CAACvB,iBAAiB,CAACuE,QAAQ;MACzB5B,GAAG,EAAEhB,KAAK,CAACO,UAAU,CAACU,KAAK,CAACD,GAAI;MAChC6B,KAAK,EAAE7C,KAAK,CAACO,UAAU,CAACmC;IAAW,GAEnCnE,KAAA,CAAAqB,aAAA,CAACtB,sBAAsB,CAACsE,QAAQ;MAACC,KAAK,EAAE7C,KAAK,CAACO,UAAU,CAACU;IAAM,GAC7D1C,KAAA,CAAAqB,aAAA,CAAClB,IAAI;MACHoE,QAAQ,EACNvD,qBAAqB,GAChB,UAAAwD,CAAC,EAAK;QACL,IAAQC,MAAA,GAAWD,CAAC,CAACE,WAAW,CAAC7D,MAAM,CAA/B4D,MAAA;QAERzD,qBAAqB,CAAC;UACpB0B,KAAK,EAAEjB,KAAK,CAACO,UAAU,CAACU,KAAK;UAC7B+B,MAAA,EAAAA;QACF,CAAC,CAAC;MACJ,CAAC,GACDE,SACL;MACDrD,aAAa,EAAEkB,SAAS,GAAG,UAAU,GAAG,MAAO;MAC/CoC,2BAA2B,EAAE,CAACpC,SAAU;MACxCqC,yBAAyB,EACvBrC,SAAS,GAAG,MAAM,GAAG,qBACtB;MACDvB,KAAK,EAGFN,IAAI,KAAK,OAAO,IAAI,CAAC6B,SAAS,IAAKF,iBAAiB,GACjDwC,MAAM,CAAC5C,MAAM,GACb;IACL,GAEAA,MAAM,KAAKyC,SAAS,GAAGzC,MAAM,CAAC8B,KAAK,CAAC,GAAGhE,KAAA,CAAAqB,aAAA,CAACb,MAAM,EAAKwD,KAAK,CAAI,CACxD,CACyB,CACP;EAEjC,CAAC,CAAC,CACY;AAEpB;AAEA,IAAMc,MAAM,GAAG5E,UAAU,CAAC6E,MAAM,CAAC;EAC/B7C,MAAM,EAAE;IACN8C,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE;EACT;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}