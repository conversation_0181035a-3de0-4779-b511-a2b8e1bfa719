{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport React, { useState, useEffect } from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport TouchableOpacity from \"react-native-web/dist/exports/TouchableOpacity\";\nimport Image from \"react-native-web/dist/exports/Image\";\nimport Switch from \"react-native-web/dist/exports/Switch\";\nimport ScrollView from \"react-native-web/dist/exports/ScrollView\";\nimport Alert from \"react-native-web/dist/exports/Alert\";\nimport styles from \"./styles\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar Profile = function Profile() {\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    isPublic = _useState2[0],\n    setIsPublic = _useState2[1];\n  var _useState3 = useState({\n      username: '<PERSON><PERSON><PERSON>',\n      fullName: '<PERSON>',\n      bio: 'Crypto enthusiast',\n      profilePicture: 'https://example.com/profile.jpg'\n    }),\n    _useState4 = _slicedToArray(_useState3, 2),\n    userData = _useState4[0],\n    setUserData = _useState4[1];\n  useEffect(function () {}, []);\n  var togglePrivacy = function togglePrivacy() {\n    setIsPublic(function (prevState) {\n      return !prevState;\n    });\n  };\n  var handleCancelSubscription = function handleCancelSubscription() {};\n  var handleEditProfile = function handleEditProfile() {\n    Alert.alert('Edição de Perfil', 'Aqui você implementaria a funcionalidade de edição do perfil.');\n  };\n  var handleChangeProfilePicture = function handleChangeProfilePicture() {\n    Alert.alert('Alterar Foto do Perfil', 'Aqui você implementaria a funcionalidade para trocar a foto do perfil.');\n  };\n  return _jsxs(ScrollView, {\n    style: styles.container,\n    children: [_jsxs(View, {\n      style: styles.profileHeader,\n      children: [_jsx(TouchableOpacity, {\n        onPress: handleChangeProfilePicture,\n        children: _jsx(Image, {\n          source: {\n            uri: userData.profilePicture\n          },\n          style: styles.profilePicture\n        })\n      }), _jsx(Text, {\n        style: styles.username,\n        children: userData.username\n      }), _jsx(Text, {\n        style: styles.fullName,\n        children: userData.fullName\n      }), _jsx(Text, {\n        style: styles.bio,\n        children: userData.bio\n      }), _jsx(TouchableOpacity, {\n        onPress: handleEditProfile,\n        children: _jsx(Text, {\n          children: \"Editar perfil\"\n        })\n      }), _jsxs(View, {\n        style: styles.privacySwitch,\n        children: [_jsxs(Text, {\n          style: styles.privacyLabel,\n          children: [\"Perfil \", isPublic ? 'Público' : 'Privado']\n        }), _jsx(Switch, {\n          value: isPublic,\n          onValueChange: togglePrivacy,\n          thumbColor: isPublic ? '#28a745' : '#dc3545'\n        })]\n      })]\n    }), _jsx(View, {\n      style: styles.metricsSection,\n      children: _jsx(Text, {\n        style: styles.sectionTitle,\n        children: \"M\\xE9tricas de Desempenho\"\n      })\n    }), _jsx(View, {\n      style: styles.badgesSection,\n      children: _jsx(Text, {\n        style: styles.sectionTitle,\n        children: \"Distintivos\"\n      })\n    }), _jsx(TouchableOpacity, {\n      style: styles.cancelSubscriptionButton,\n      onPress: handleCancelSubscription,\n      children: _jsx(Text, {\n        style: styles.cancelSubscriptionText,\n        children: \"Cancelar Assinatura\"\n      })\n    })]\n  });\n};\nexport default Profile;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "View", "Text", "TouchableOpacity", "Image", "Switch", "ScrollView", "<PERSON><PERSON>", "styles", "jsx", "_jsx", "jsxs", "_jsxs", "Profile", "_useState", "_useState2", "_slicedToArray", "isPublic", "setIsPublic", "_useState3", "username", "fullName", "bio", "profilePicture", "_useState4", "userData", "setUserData", "togglePrivacy", "prevState", "handleCancelSubscription", "handleEditProfile", "alert", "handleChangeProfilePicture", "style", "container", "children", "<PERSON><PERSON><PERSON><PERSON>", "onPress", "source", "uri", "privacySwitch", "privacyLabel", "value", "onValueChange", "thumbColor", "metricsSection", "sectionTitle", "badgesSection", "cancelSubscriptionButton", "cancelSubscriptionText"], "sources": ["E:/CryptoSignalsApp/src/pages/User/index.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { View, Text, TouchableOpacity, Image, Switch, ScrollView, Alert } from 'react-native';\r\nimport styles from './styles';\r\n\r\nconst Profile = () => {\r\n  const [isPublic, setIsPublic] = useState(false);\r\n  const [userData, setUserData] = useState({\r\n    username: '<PERSON><PERSON><PERSON>',\r\n    fullName: '<PERSON>',\r\n    bio: 'Crypto enthusiast',\r\n    profilePicture: 'https://example.com/profile.jpg',\r\n  });\r\n\r\n  useEffect(() => {\r\n    // Buscar os dados do usuário do seu banco de dados ou API\r\n    // e atualizar o estado userData com as informações do perfil.\r\n  }, []);\r\n\r\n  const togglePrivacy = () => {\r\n    setIsPublic((prevState) => !prevState);\r\n  };\r\n\r\n  const handleCancelSubscription = () => {\r\n    // Implementação de cancelamento de assinatura\r\n  };\r\n\r\n  const handleEditProfile = () => {\r\n    // Exemplo simplificado usando um alerta\r\n    Alert.alert('Edição de Perfil', 'Aqui você implementaria a funcionalidade de edição do perfil.');\r\n  };\r\n\r\n  const handleChangeProfilePicture = () => {\r\n    // Exemplo simplificado usando um alerta\r\n    Alert.alert('Alterar Foto do Perfil', 'Aqui você implementaria a funcionalidade para trocar a foto do perfil.');\r\n  };\r\n\r\n  return (\r\n    <ScrollView style={styles.container}>\r\n      <View style={styles.profileHeader}>\r\n        <TouchableOpacity onPress={handleChangeProfilePicture}>\r\n          <Image source={{ uri: userData.profilePicture }} style={styles.profilePicture} />\r\n        </TouchableOpacity>\r\n        <Text style={styles.username}>{userData.username}</Text>\r\n        <Text style={styles.fullName}>{userData.fullName}</Text>\r\n        <Text style={styles.bio}>{userData.bio}</Text>\r\n        <TouchableOpacity onPress={handleEditProfile}>\r\n          <Text>Editar perfil</Text>\r\n        </TouchableOpacity>\r\n        <View style={styles.privacySwitch}>\r\n          <Text style={styles.privacyLabel}>Perfil {isPublic ? 'Público' : 'Privado'}</Text>\r\n          <Switch\r\n            value={isPublic}\r\n            onValueChange={togglePrivacy}\r\n            thumbColor={isPublic ? '#28a745' : '#dc3545'}\r\n          />\r\n        </View>\r\n      </View>\r\n\r\n      <View style={styles.metricsSection}>\r\n        <Text style={styles.sectionTitle}>Métricas de Desempenho</Text>\r\n        {/* Exibir métricas de desempenho aqui */}\r\n      </View>\r\n\r\n      <View style={styles.badgesSection}>\r\n        <Text style={styles.sectionTitle}>Distintivos</Text>\r\n        {/* Exibir distintivos aqui */}\r\n      </View>\r\n\r\n      <TouchableOpacity\r\n        style={styles.cancelSubscriptionButton}\r\n        onPress={handleCancelSubscription}\r\n      >\r\n        <Text style={styles.cancelSubscriptionText}>Cancelar Assinatura</Text>\r\n      </TouchableOpacity>\r\n    </ScrollView>\r\n  );\r\n};\r\n\r\nexport default Profile;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,gBAAA;AAAA,OAAAC,KAAA;AAAA,OAAAC,MAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,KAAA;AAEnD,OAAOC,MAAM;AAAiB,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAE9B,IAAMC,OAAO,GAAG,SAAVA,OAAOA,CAAA,EAAS;EACpB,IAAAC,SAAA,GAAgCf,QAAQ,CAAC,KAAK,CAAC;IAAAgB,UAAA,GAAAC,cAAA,CAAAF,SAAA;IAAxCG,QAAQ,GAAAF,UAAA;IAAEG,WAAW,GAAAH,UAAA;EAC5B,IAAAI,UAAA,GAAgCpB,QAAQ,CAAC;MACvCqB,QAAQ,EAAE,SAAS;MACnBC,QAAQ,EAAE,UAAU;MACpBC,GAAG,EAAE,mBAAmB;MACxBC,cAAc,EAAE;IAClB,CAAC,CAAC;IAAAC,UAAA,GAAAR,cAAA,CAAAG,UAAA;IALKM,QAAQ,GAAAD,UAAA;IAAEE,WAAW,GAAAF,UAAA;EAO5BxB,SAAS,CAAC,YAAM,CAGhB,CAAC,EAAE,EAAE,CAAC;EAEN,IAAM2B,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;IAC1BT,WAAW,CAAC,UAACU,SAAS;MAAA,OAAK,CAACA,SAAS;IAAA,EAAC;EACxC,CAAC;EAED,IAAMC,wBAAwB,GAAG,SAA3BA,wBAAwBA,CAAA,EAAS,CAEvC,CAAC;EAED,IAAMC,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAA,EAAS;IAE9BvB,KAAK,CAACwB,KAAK,CAAC,kBAAkB,EAAE,+DAA+D,CAAC;EAClG,CAAC;EAED,IAAMC,0BAA0B,GAAG,SAA7BA,0BAA0BA,CAAA,EAAS;IAEvCzB,KAAK,CAACwB,KAAK,CAAC,wBAAwB,EAAE,wEAAwE,CAAC;EACjH,CAAC;EAED,OACEnB,KAAA,CAACN,UAAU;IAAC2B,KAAK,EAAEzB,MAAM,CAAC0B,SAAU;IAAAC,QAAA,GAClCvB,KAAA,CAACX,IAAI;MAACgC,KAAK,EAAEzB,MAAM,CAAC4B,aAAc;MAAAD,QAAA,GAChCzB,IAAA,CAACP,gBAAgB;QAACkC,OAAO,EAAEL,0BAA2B;QAAAG,QAAA,EACpDzB,IAAA,CAACN,KAAK;UAACkC,MAAM,EAAE;YAAEC,GAAG,EAAEd,QAAQ,CAACF;UAAe,CAAE;UAACU,KAAK,EAAEzB,MAAM,CAACe;QAAe,CAAE;MAAC,CACjE,CAAC,EACnBb,IAAA,CAACR,IAAI;QAAC+B,KAAK,EAAEzB,MAAM,CAACY,QAAS;QAAAe,QAAA,EAAEV,QAAQ,CAACL;MAAQ,CAAO,CAAC,EACxDV,IAAA,CAACR,IAAI;QAAC+B,KAAK,EAAEzB,MAAM,CAACa,QAAS;QAAAc,QAAA,EAAEV,QAAQ,CAACJ;MAAQ,CAAO,CAAC,EACxDX,IAAA,CAACR,IAAI;QAAC+B,KAAK,EAAEzB,MAAM,CAACc,GAAI;QAAAa,QAAA,EAAEV,QAAQ,CAACH;MAAG,CAAO,CAAC,EAC9CZ,IAAA,CAACP,gBAAgB;QAACkC,OAAO,EAAEP,iBAAkB;QAAAK,QAAA,EAC3CzB,IAAA,CAACR,IAAI;UAAAiC,QAAA,EAAC;QAAa,CAAM;MAAC,CACV,CAAC,EACnBvB,KAAA,CAACX,IAAI;QAACgC,KAAK,EAAEzB,MAAM,CAACgC,aAAc;QAAAL,QAAA,GAChCvB,KAAA,CAACV,IAAI;UAAC+B,KAAK,EAAEzB,MAAM,CAACiC,YAAa;UAAAN,QAAA,GAAC,SAAO,EAAClB,QAAQ,GAAG,SAAS,GAAG,SAAS;QAAA,CAAO,CAAC,EAClFP,IAAA,CAACL,MAAM;UACLqC,KAAK,EAAEzB,QAAS;UAChB0B,aAAa,EAAEhB,aAAc;UAC7BiB,UAAU,EAAE3B,QAAQ,GAAG,SAAS,GAAG;QAAU,CAC9C,CAAC;MAAA,CACE,CAAC;IAAA,CACH,CAAC,EAEPP,IAAA,CAACT,IAAI;MAACgC,KAAK,EAAEzB,MAAM,CAACqC,cAAe;MAAAV,QAAA,EACjCzB,IAAA,CAACR,IAAI;QAAC+B,KAAK,EAAEzB,MAAM,CAACsC,YAAa;QAAAX,QAAA,EAAC;MAAsB,CAAM;IAAC,CAE3D,CAAC,EAEPzB,IAAA,CAACT,IAAI;MAACgC,KAAK,EAAEzB,MAAM,CAACuC,aAAc;MAAAZ,QAAA,EAChCzB,IAAA,CAACR,IAAI;QAAC+B,KAAK,EAAEzB,MAAM,CAACsC,YAAa;QAAAX,QAAA,EAAC;MAAW,CAAM;IAAC,CAEhD,CAAC,EAEPzB,IAAA,CAACP,gBAAgB;MACf8B,KAAK,EAAEzB,MAAM,CAACwC,wBAAyB;MACvCX,OAAO,EAAER,wBAAyB;MAAAM,QAAA,EAElCzB,IAAA,CAACR,IAAI;QAAC+B,KAAK,EAAEzB,MAAM,CAACyC,sBAAuB;QAAAd,QAAA,EAAC;MAAmB,CAAM;IAAC,CACtD,CAAC;EAAA,CACT,CAAC;AAEjB,CAAC;AAED,eAAetB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}