#!/usr/bin/env python3
"""
Script para testar a nova formatação profissional dos sinais
"""

import sys
import os

# Adicionar o diretório raiz ao path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.signal_formatter import SignalFormatter

def test_scalp_signal():
    """Testa formatação de sinal scalp profissional"""
    print("=" * 50)
    print("TESTE: SINAL SCALP PROFISSIONAL")
    print("=" * 50)
    
    # Dados de exemplo
    symbol = "BTCUSDT"
    entry_price = 43250.50
    take_profits = {
        40: 43685.25,
        60: 44120.00,
        80: 44554.75,
        100: 44989.50
    }
    signal_type = "LONG"
    leverage = 20
    
    # Gerar sinal formatado
    message = SignalFormatter.format_scalp_signal(
        symbol, entry_price, take_profits, signal_type, leverage
    )
    
    print(message)
    print("\n")

def test_breakout_signal():
    """Testa formatação de sinal breakout profissional"""
    print("=" * 50)
    print("TESTE: SINAL BREAKOUT PROFISSIONAL")
    print("=" * 50)
    
    # Dados de exemplo
    symbol = "ETHUSDT"
    entry_price = 2450.75
    stop_loss = 2400.25
    take_profit = 2550.50
    signal_type = "LONG"
    leverage = 15
    
    # Gerar sinal formatado
    message = SignalFormatter.format_breakout_signal(
        symbol, entry_price, stop_loss, take_profit, leverage, signal_type
    )
    
    print(message)
    print("\n")

def test_inside_bar_signal():
    """Testa formatação de sinal inside bar profissional"""
    print("=" * 50)
    print("TESTE: SINAL INSIDE BAR PROFISSIONAL")
    print("=" * 50)
    
    # Dados de exemplo
    symbol = "BNBUSDT"
    entry_price = 315.25
    stop_loss = 310.50
    take_profit = 325.75
    signal_type = "LONG"
    leverage = 10
    
    # Gerar sinal formatado
    message = SignalFormatter.format_inside_bar_signal(
        symbol, entry_price, stop_loss, take_profit, leverage, signal_type
    )
    
    print(message)
    print("\n")

def test_mfi_signal():
    """Testa formatação de sinal MFI profissional"""
    print("=" * 50)
    print("TESTE: SINAL MFI PROFISSIONAL")
    print("=" * 50)
    
    # Dados de exemplo
    symbol = "ADAUSDT"
    entry_price = 0.4850
    stop_loss = 0.4750
    take_profit = 0.5050
    leverage = 10
    
    # Gerar sinal formatado
    message = SignalFormatter.format_mfi_signal(
        symbol, entry_price, stop_loss, take_profit, leverage
    )
    
    print(message)
    print("\n")

def test_swing_signal():
    """Testa formatação de sinal swing profissional"""
    print("=" * 50)
    print("TESTE: SINAL SWING PROFISSIONAL")
    print("=" * 50)
    
    # Dados de exemplo
    symbol = "SOLUSDT"
    entry_price = 98.50
    stop_loss = 95.25
    take_profit = 105.75
    signal_type = "LONG"
    leverage = 8
    
    # Gerar sinal formatado
    message = SignalFormatter.format_swing_signal(
        symbol, entry_price, stop_loss, take_profit, leverage, signal_type
    )
    
    print(message)
    print("\n")

def test_profit_update():
    """Testa formatação de atualização de profit profissional"""
    print("=" * 50)
    print("TESTE: ATUALIZAÇÃO DE PROFIT PROFISSIONAL")
    print("=" * 50)
    
    # Dados de exemplo
    symbol = "BTCUSDT"
    signal_type = "LONG"
    leverage = 20
    entry_price = 43250.50
    current_price = 44120.00
    profit_percentage = 4.02
    
    # Gerar mensagem formatada
    message = SignalFormatter.format_profit_update(
        symbol, signal_type, leverage, current_price, profit_percentage, entry_price
    )
    
    print(message)
    print("\n")

def test_daily_report():
    """Testa formatação de relatório diário profissional"""
    print("=" * 50)
    print("TESTE: RELATÓRIO DIÁRIO PROFISSIONAL")
    print("=" * 50)
    
    # Dados de exemplo
    total_signals = 47
    profitable_signals = 32
    win_rate = 68.1
    avg_profit = 3.25
    
    # Gerar relatório formatado
    message = SignalFormatter.format_daily_report(
        total_signals, profitable_signals, win_rate, avg_profit
    )
    
    print(message)
    print("\n")

def test_new_strategies():
    """Testa formatação das novas estratégias"""
    print("=" * 50)
    print("TESTE: NOVAS ESTRATÉGIAS PROFISSIONAIS")
    print("=" * 50)
    
    # Dados de exemplo
    symbol = "DOGEUSDT"
    entry_price = 0.08750
    take_profits = {
        40: 0.08925,
        60: 0.09100,
        80: 0.09275,
        100: 0.09450
    }
    signal_type = "LONG"
    
    # Testar Multi-Source
    print("MULTI-SOURCE STRATEGY:")
    message = SignalFormatter.format_multi_source_signal(
        symbol, entry_price, take_profits, signal_type, 20
    )
    print(message)
    print("\n")
    
    # Testar Volume Analysis
    print("VOLUME ANALYSIS STRATEGY:")
    message = SignalFormatter.format_volume_analysis_signal(
        symbol, entry_price, take_profits, signal_type, 15
    )
    print(message)
    print("\n")
    
    # Testar Momentum
    print("MOMENTUM STRATEGY:")
    message = SignalFormatter.format_momentum_signal(
        symbol, entry_price, take_profits, signal_type, 15
    )
    print(message)
    print("\n")

def compare_old_vs_new():
    """Compara formatação antiga vs nova"""
    print("=" * 50)
    print("COMPARAÇÃO: ANTES vs DEPOIS")
    print("=" * 50)
    
    print("ANTES (com emojis e casual):")
    print("━" * 30)
    print("CryptoSignals App ⚡")
    print("")
    print("━━━━━━━━━━━━━━━━━━━━━")
    print("#BTCUSDT • 20x Long 📈")
    print("")
    print("📍 Entry: 43250.50")
    print("🛑 Stop Loss: 42500.00")
    print("")
    print("📊 Take Profits:")
    print("🔹 43685.25 (40%)")
    print("🔹 44120.00 (60%)")
    print("🔹 44554.75 (80%)")
    print("💰 44989.50 (100% profit)")
    print("")
    print("📝 How to use this signal:")
    print("• Enter position near the entry price")
    print("• Set take profits at the levels above")
    print("• Recommended stop loss: 2-3% below entry")
    print("━━━━━━━━━━━━━━━━━━━━━")
    print("CryptoSignals App ⚡")
    
    print("\n" + "=" * 50)
    print("DEPOIS (profissional e clean):")
    print("=" * 50)
    
    # Gerar novo formato
    symbol = "BTCUSDT"
    entry_price = 43250.50
    take_profits = {
        40: 43685.25,
        60: 44120.00,
        80: 44554.75,
        100: 44989.50
    }
    signal_type = "LONG"
    leverage = 20
    
    message = SignalFormatter.format_scalp_signal(
        symbol, entry_price, take_profits, signal_type, leverage
    )
    print(message)

def main():
    """Função principal de teste"""
    print("TESTE DE FORMATAÇÃO PROFISSIONAL - CRYPTOSIGNALS")
    print("=" * 60)
    print("Testando nova formatação clean e profissional")
    print("Focada em traders de elite, sem emojis")
    print("=" * 60)
    print("\n")
    
    # Executar todos os testes
    test_scalp_signal()
    test_breakout_signal()
    test_inside_bar_signal()
    test_mfi_signal()
    test_swing_signal()
    test_profit_update()
    test_daily_report()
    test_new_strategies()
    compare_old_vs_new()
    
    print("\n" + "=" * 60)
    print("RESUMO DOS TESTES:")
    print("=" * 60)
    print("✓ Formatação scalp profissional")
    print("✓ Formatação breakout profissional")
    print("✓ Formatação inside bar profissional")
    print("✓ Formatação MFI profissional")
    print("✓ Formatação swing profissional")
    print("✓ Atualização de profit profissional")
    print("✓ Relatório diário profissional")
    print("✓ Novas estratégias profissionais")
    print("✓ Comparação antes vs depois")
    print("\n")
    print("RESULTADO: FORMATAÇÃO PROFISSIONAL IMPLEMENTADA COM SUCESSO!")
    print("- Removidos todos os emojis")
    print("- Layout clean e técnico")
    print("- Informações organizadas")
    print("- Foco em traders profissionais")
    print("- Consistência em todas as estratégias")

if __name__ == "__main__":
    main()
