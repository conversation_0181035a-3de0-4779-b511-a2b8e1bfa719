{"ast": null, "code": "import StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: '#202020',\n    padding: 16\n  },\n  pageTitle: {\n    fontSize: 24,\n    fontWeight: 'bold',\n    color: '#FECB37',\n    marginBottom: 20,\n    textAlign: 'center',\n    fontFamily: 'Poppins_600SemiBold'\n  },\n  loadingText: {\n    color: '#fff',\n    textAlign: 'center',\n    fontSize: 16,\n    marginTop: 50\n  },\n  newsItem: {\n    backgroundColor: '#2a2a2a',\n    padding: 16,\n    marginBottom: 12,\n    borderRadius: 8,\n    borderLeftWidth: 4,\n    borderLeftColor: '#FECB37'\n  },\n  newsTitle: {\n    fontSize: 18,\n    fontWeight: 'bold',\n    color: '#fff',\n    marginBottom: 8,\n    fontFamily: 'Poppins_500Medium'\n  },\n  newsDate: {\n    fontSize: 12,\n    color: '#8a8a8a',\n    marginBottom: 8\n  },\n  newsContent: {\n    fontSize: 14,\n    color: '#ccc',\n    lineHeight: 20\n  }\n});\nexport default styles;", "map": {"version": 3, "names": ["styles", "StyleSheet", "create", "container", "flex", "backgroundColor", "padding", "pageTitle", "fontSize", "fontWeight", "color", "marginBottom", "textAlign", "fontFamily", "loadingText", "marginTop", "newsItem", "borderRadius", "borderLeftWidth", "borderLeftColor", "newsTitle", "newsDate", "newsContent", "lineHeight"], "sources": ["E:/CryptoSignalsApp/src/pages/News/styles.js"], "sourcesContent": ["import { StyleSheet } from 'react-native';\r\n\r\nconst styles = StyleSheet.create({\r\n  container: {\r\n    flex: 1,\r\n    backgroundColor: '#202020',\r\n    padding: 16,\r\n  },\r\n  pageTitle: {\r\n    fontSize: 24,\r\n    fontWeight: 'bold',\r\n    color: '#FECB37',\r\n    marginBottom: 20,\r\n    textAlign: 'center',\r\n    fontFamily: 'Poppins_600SemiBold',\r\n  },\r\n  loadingText: {\r\n    color: '#fff',\r\n    textAlign: 'center',\r\n    fontSize: 16,\r\n    marginTop: 50,\r\n  },\r\n  newsItem: {\r\n    backgroundColor: '#2a2a2a',\r\n    padding: 16,\r\n    marginBottom: 12,\r\n    borderRadius: 8,\r\n    borderLeftWidth: 4,\r\n    borderLeftColor: '#FECB37',\r\n  },\r\n  newsTitle: {\r\n    fontSize: 18,\r\n    fontWeight: 'bold',\r\n    color: '#fff',\r\n    marginBottom: 8,\r\n    fontFamily: 'Poppins_500Medium',\r\n  },\r\n  newsDate: {\r\n    fontSize: 12,\r\n    color: '#8a8a8a',\r\n    marginBottom: 8,\r\n  },\r\n  newsContent: {\r\n    fontSize: 14,\r\n    color: '#ccc',\r\n    lineHeight: 20,\r\n  },\r\n});\r\n\r\nexport default styles;\r\n"], "mappings": ";AAEA,IAAMA,MAAM,GAAGC,UAAU,CAACC,MAAM,CAAC;EAC/BC,SAAS,EAAE;IACTC,IAAI,EAAE,CAAC;IACPC,eAAe,EAAE,SAAS;IAC1BC,OAAO,EAAE;EACX,CAAC;EACDC,SAAS,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBC,KAAK,EAAE,SAAS;IAChBC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE,QAAQ;IACnBC,UAAU,EAAE;EACd,CAAC;EACDC,WAAW,EAAE;IACXJ,KAAK,EAAE,MAAM;IACbE,SAAS,EAAE,QAAQ;IACnBJ,QAAQ,EAAE,EAAE;IACZO,SAAS,EAAE;EACb,CAAC;EACDC,QAAQ,EAAE;IACRX,eAAe,EAAE,SAAS;IAC1BC,OAAO,EAAE,EAAE;IACXK,YAAY,EAAE,EAAE;IAChBM,YAAY,EAAE,CAAC;IACfC,eAAe,EAAE,CAAC;IAClBC,eAAe,EAAE;EACnB,CAAC;EACDC,SAAS,EAAE;IACTZ,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBC,KAAK,EAAE,MAAM;IACbC,YAAY,EAAE,CAAC;IACfE,UAAU,EAAE;EACd,CAAC;EACDQ,QAAQ,EAAE;IACRb,QAAQ,EAAE,EAAE;IACZE,KAAK,EAAE,SAAS;IAChBC,YAAY,EAAE;EAChB,CAAC;EACDW,WAAW,EAAE;IACXd,QAAQ,EAAE,EAAE;IACZE,KAAK,EAAE,MAAM;IACba,UAAU,EAAE;EACd;AACF,CAAC,CAAC;AAEF,eAAevB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}