#!/usr/bin/env python3
"""
Servidor mínimo para testar a integração
"""

from flask import Flask, jsonify
import sys
import os

app = Flask(__name__)

# Adicionar headers CORS manualmente
@app.after_request
def after_request(response):
    response.headers.add('Access-Control-Allow-Origin', '*')
    response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
    response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')
    return response

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({'status': 'healthy', 'message': 'Servidor funcionando!'})

@app.route('/api/channels', methods=['GET'])
def get_channels():
    """Endpoint para lista de canais/estratégias"""
    channels = [
        {
            'id': 1,
            'externalId': 'strategy-scalp',
            'name': 'CryptoSignals Scalp',
            'description': 'Sinais da estratégia Scalp',
            'type': 'FUTURES',
            'isPremium': False,
            'totalSignals': 0,
            'recentSignals': 0,
            'lastSignalAt': '2024-01-26T10:00:00Z',
            'photo': 'https://via.placeholder.com/50x50/4CAF50/FFFFFF?text=S'
        },
        {
            'id': 2,
            'externalId': 'strategy-swing',
            'name': 'CryptoSignals Swing',
            'description': 'Sinais da estratégia Swing',
            'type': 'SPOT',
            'isPremium': False,
            'totalSignals': 0,
            'recentSignals': 0,
            'lastSignalAt': '2024-01-26T09:30:00Z',
            'photo': 'https://via.placeholder.com/50x50/2196F3/FFFFFF?text=S'
        }
    ]
    return jsonify(channels)

@app.route('/api/channels/<channel_id>/signals', methods=['GET'])
def get_channel_signals(channel_id):
    """Endpoint para sinais de um canal específico"""
    signals = [
        {
            'id': 1,
            'symbol': 'BTCUSDT',
            'signal_type': 'LONG',
            'strategy': 'Scalp',
            'entry_price': 45000.0,
            'stop_loss': 44000.0,
            'take_profit_1': 46000.0,
            'status': 'OPEN',
            'leverage': 10,
            'createdAt': '2024-01-26T10:00:00Z',
            'messageOriginal': 'CRYPTOSIGNALS PROFESSIONAL\n===================================\n\nASSET: BTCUSDT\nSTRATEGY: SCALP\nDIRECTION: LONG\nLEVERAGE: 10x\nTIMEFRAME: 1-4H\n\nENTRY ZONE: 45000.00\nSTOP LOSS: 44000.00\n\nTAKE PROFIT LEVELS:\nTP1: 46000.00\n\nRISK MANAGEMENT:\n- Position size: 1-2% of portfolio\n- Risk/Reward: 1:2.5 minimum\n- Strict stop loss adherence\n\n===================================\nCRYPTOSIGNALS PROFESSIONAL'
        }
    ]
    return jsonify(signals)

if __name__ == '__main__':
    print("🚀 Iniciando servidor mínimo na porta 5000...")
    print("📡 Endpoints disponíveis:")
    print("   - GET /api/health")
    print("   - GET /api/channels") 
    print("   - GET /api/channels/{id}/signals")
    print("\n✅ Servidor pronto!")
    
    try:
        app.run(debug=False, host='0.0.0.0', port=5000)
    except Exception as e:
        print(f"❌ Erro ao iniciar servidor: {e}")
        print("Tentando na porta 5001...")
        app.run(debug=False, host='0.0.0.0', port=5001)
