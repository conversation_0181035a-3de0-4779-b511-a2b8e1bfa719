#!/usr/bin/env python3
"""
Servidor mínimo para testar a integração
"""

from flask import Flask, jsonify, request
import time
import random
from datetime import datetime, timedelta

app = Flask(__name__)

# Adicionar headers CORS manualmente
@app.after_request
def after_request(response):
    response.headers.add('Access-Control-Allow-Origin', '*')
    response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
    response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')
    return response

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({'status': 'healthy', 'message': 'Servidor funcionando!'})

@app.route('/api/channels', methods=['GET'])
def get_channels():
    """Endpoint para lista de canais/estratégias"""
    channels = [
        {
            'id': 1,
            'externalId': 'strategy-scalp',
            'name': 'CryptoSignals Scalp',
            'description': 'Sinais da estratégia Scalp',
            'type': 'FUTURES',
            'isPremium': False,
            'totalSignals': 0,
            'recentSignals': 0,
            'lastSignalAt': '2024-01-26T10:00:00Z',
            'photo': 'https://via.placeholder.com/50x50/4CAF50/FFFFFF?text=S'
        },
        {
            'id': 2,
            'externalId': 'strategy-swing',
            'name': 'CryptoSignals Swing',
            'description': 'Sinais da estratégia Swing',
            'type': 'SPOT',
            'isPremium': False,
            'totalSignals': 0,
            'recentSignals': 0,
            'lastSignalAt': '2024-01-26T09:30:00Z',
            'photo': 'https://via.placeholder.com/50x50/2196F3/FFFFFF?text=S'
        }
    ]
    return jsonify(channels)

@app.route('/api/channels/<channel_id>/signals', methods=['GET'])
def get_channel_signals(channel_id):
    """Endpoint para sinais de um canal específico"""
    signals = [
        {
            'id': 1,
            'symbol': 'BTCUSDT',
            'signal_type': 'LONG',
            'strategy': 'Scalp',
            'entry_price': 45000.0,
            'stop_loss': 44000.0,
            'take_profit_1': 46000.0,
            'status': 'OPEN',
            'leverage': 10,
            'createdAt': '2024-01-26T10:00:00Z',
            'messageOriginal': 'CRYPTOSIGNALS PROFESSIONAL\n===================================\n\nASSET: BTCUSDT\nSTRATEGY: SCALP\nDIRECTION: LONG\nLEVERAGE: 10x\nTIMEFRAME: 1-4H\n\nENTRY ZONE: 45000.00\nSTOP LOSS: 44000.00\n\nTAKE PROFIT LEVELS:\nTP1: 46000.00\n\nRISK MANAGEMENT:\n- Position size: 1-2% of portfolio\n- Risk/Reward: 1:2.5 minimum\n- Strict stop loss adherence\n\n===================================\nCRYPTOSIGNALS PROFESSIONAL'
        }
    ]
    return jsonify(signals)

# Armazenamento temporário para pagamentos (em produção, usar banco de dados)
payments_storage = {}

@app.route('/api/payments', methods=['POST'])
def create_payment():
    """Cria uma nova sessão de pagamento USDT"""
    try:
        data = request.get_json()
        plan_id = data.get('planId')
        user_id = data.get('userId', 'anonymous')

        # Preços dos planos
        plan_prices = {'pro': 29, 'elite': 79}

        if plan_id not in plan_prices:
            return jsonify({'error': 'Plano inválido'}), 400

        # Gerar ID do pagamento
        payment_id = f"pay_{int(time.time())}_{plan_id}"

        # Criar sessão de pagamento
        payment_session = {
            'id': payment_id,
            'planId': plan_id,
            'userId': user_id,
            'amount': plan_prices[plan_id],
            'currency': 'USDT',
            'status': 'pending',
            'walletAddress': '******************************************',
            'createdAt': datetime.now().isoformat(),
            'expiresAt': (datetime.now() + timedelta(minutes=30)).isoformat(),
            'confirmations': 0,
            'requiredConfirmations': 3,
            'transactionHash': None
        }

        payments_storage[payment_id] = payment_session

        return jsonify(payment_session)

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/payments/<payment_id>/status', methods=['GET'])
def get_payment_status(payment_id):
    """Verifica o status de um pagamento"""
    try:
        if payment_id not in payments_storage:
            return jsonify({'error': 'Pagamento não encontrado'}), 404

        payment = payments_storage[payment_id]

        # Verificar se expirou
        if datetime.now() > datetime.fromisoformat(payment['expiresAt']):
            payment['status'] = 'expired'

        # Simular verificação na blockchain (30% de chance de encontrar transação)
        elif payment['status'] == 'pending' and random.random() < 0.3:
            payment['transactionHash'] = '0x' + ''.join(random.choices('0123456789abcdef', k=64))
            payment['confirmations'] = random.randint(1, 5)
            payment['status'] = 'confirming' if payment['confirmations'] < 3 else 'confirmed'

            if payment['status'] == 'confirmed':
                payment['confirmedAt'] = datetime.now().isoformat()

        payments_storage[payment_id] = payment

        return jsonify(payment)

    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    print("🚀 Iniciando servidor mínimo na porta 5000...")
    print("📡 Endpoints disponíveis:")
    print("   - GET /api/health")
    print("   - GET /api/channels")
    print("   - GET /api/channels/{id}/signals")
    print("\n✅ Servidor pronto!")

    try:
        app.run(debug=False, host='0.0.0.0', port=5000)
    except Exception as e:
        print(f"❌ Erro ao iniciar servidor: {e}")
        print("Tentando na porta 5001...")
        app.run(debug=False, host='0.0.0.0', port=5001)
