{"ast": null, "code": "import normalizeColor from \"./compiler/normalizeColor\";\nimport normalizeValueWithProperty from \"./compiler/normalizeValueWithProperty\";\nimport { warnOnce } from \"../../modules/warnOnce\";\nvar emptyObject = {};\nvar defaultOffset = {\n  height: 0,\n  width: 0\n};\nexport var createBoxShadowValue = function createBoxShadowValue(style) {\n  var shadowColor = style.shadowColor,\n    shadowOffset = style.shadowOffset,\n    shadowOpacity = style.shadowOpacity,\n    shadowRadius = style.shadowRadius;\n  var _ref = shadowOffset || defaultOffset,\n    height = _ref.height,\n    width = _ref.width;\n  var offsetX = normalizeValueWithProperty(width);\n  var offsetY = normalizeValueWithProperty(height);\n  var blurRadius = normalizeValueWithProperty(shadowRadius || 0);\n  var color = normalizeColor(shadowColor || 'black', shadowOpacity);\n  if (color != null && offsetX != null && offsetY != null && blurRadius != null) {\n    return offsetX + \" \" + offsetY + \" \" + blurRadius + \" \" + color;\n  }\n};\nexport var createTextShadowValue = function createTextShadowValue(style) {\n  var textShadowColor = style.textShadowColor,\n    textShadowOffset = style.textShadowOffset,\n    textShadowRadius = style.textShadowRadius;\n  var _ref2 = textShadowOffset || defaultOffset,\n    height = _ref2.height,\n    width = _ref2.width;\n  var radius = textShadowRadius || 0;\n  var offsetX = normalizeValueWithProperty(width);\n  var offsetY = normalizeValueWithProperty(height);\n  var blurRadius = normalizeValueWithProperty(radius);\n  var color = normalizeValueWithProperty(textShadowColor, 'textShadowColor');\n  if (color && (height !== 0 || width !== 0 || radius !== 0) && offsetX != null && offsetY != null && blurRadius != null) {\n    return offsetX + \" \" + offsetY + \" \" + blurRadius + \" \" + color;\n  }\n};\nvar mapTransform = function mapTransform(transform) {\n  var type = Object.keys(transform)[0];\n  var value = transform[type];\n  if (type === 'matrix' || type === 'matrix3d') {\n    return type + \"(\" + value.join(',') + \")\";\n  } else {\n    var normalizedValue = normalizeValueWithProperty(value, type);\n    return type + \"(\" + normalizedValue + \")\";\n  }\n};\nexport var createTransformValue = function createTransformValue(value) {\n  return value.map(mapTransform).join(' ');\n};\nvar PROPERTIES_STANDARD = {\n  borderBottomEndRadius: 'borderEndEndRadius',\n  borderBottomStartRadius: 'borderEndStartRadius',\n  borderTopEndRadius: 'borderStartEndRadius',\n  borderTopStartRadius: 'borderStartStartRadius',\n  borderEndColor: 'borderInlineEndColor',\n  borderEndStyle: 'borderInlineEndStyle',\n  borderEndWidth: 'borderInlineEndWidth',\n  borderStartColor: 'borderInlineStartColor',\n  borderStartStyle: 'borderInlineStartStyle',\n  borderStartWidth: 'borderInlineStartWidth',\n  end: 'insetInlineEnd',\n  marginEnd: 'marginInlineEnd',\n  marginHorizontal: 'marginInline',\n  marginStart: 'marginInlineStart',\n  marginVertical: 'marginBlock',\n  paddingEnd: 'paddingInlineEnd',\n  paddingHorizontal: 'paddingInline',\n  paddingStart: 'paddingInlineStart',\n  paddingVertical: 'paddingBlock',\n  start: 'insetInlineStart'\n};\nvar ignoredProps = {\n  elevation: true,\n  overlayColor: true,\n  resizeMode: true,\n  tintColor: true\n};\nexport var preprocess = function preprocess(originalStyle, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var style = originalStyle || emptyObject;\n  var nextStyle = {};\n  if (options.shadow === true, style.shadowColor != null || style.shadowOffset != null || style.shadowOpacity != null || style.shadowRadius != null) {\n    warnOnce('shadowStyles', \"\\\"shadow*\\\" style props are deprecated. Use \\\"boxShadow\\\".\");\n    var boxShadowValue = createBoxShadowValue(style);\n    if (boxShadowValue != null && nextStyle.boxShadow == null) {\n      var boxShadow = style.boxShadow;\n      var value = boxShadow ? boxShadow + \", \" + boxShadowValue : boxShadowValue;\n      nextStyle.boxShadow = value;\n    }\n  }\n  if (options.textShadow === true, style.textShadowColor != null || style.textShadowOffset != null || style.textShadowRadius != null) {\n    warnOnce('textShadowStyles', \"\\\"textShadow*\\\" style props are deprecated. Use \\\"textShadow\\\".\");\n    var textShadowValue = createTextShadowValue(style);\n    if (textShadowValue != null && nextStyle.textShadow == null) {\n      var textShadow = style.textShadow;\n      var _value = textShadow ? textShadow + \", \" + textShadowValue : textShadowValue;\n      nextStyle.textShadow = _value;\n    }\n  }\n  for (var originalProp in style) {\n    if (ignoredProps[originalProp] != null || originalProp === 'shadowColor' || originalProp === 'shadowOffset' || originalProp === 'shadowOpacity' || originalProp === 'shadowRadius' || originalProp === 'textShadowColor' || originalProp === 'textShadowOffset' || originalProp === 'textShadowRadius') {\n      continue;\n    }\n    var originalValue = style[originalProp];\n    var prop = PROPERTIES_STANDARD[originalProp] || originalProp;\n    var _value2 = originalValue;\n    if (!Object.prototype.hasOwnProperty.call(style, originalProp) || prop !== originalProp && style[prop] != null) {\n      continue;\n    }\n    if (prop === 'aspectRatio' && typeof _value2 === 'number') {\n      nextStyle[prop] = _value2.toString();\n    } else if (prop === 'fontVariant') {\n      if (Array.isArray(_value2) && _value2.length > 0) {\n        _value2 = _value2.join(' ');\n      }\n      nextStyle[prop] = _value2;\n    } else if (prop === 'textAlignVertical') {\n      if (style.verticalAlign == null) {\n        nextStyle.verticalAlign = _value2 === 'center' ? 'middle' : _value2;\n      }\n    } else if (prop === 'transform') {\n      if (Array.isArray(_value2)) {\n        _value2 = createTransformValue(_value2);\n      }\n      nextStyle.transform = _value2;\n    } else {\n      nextStyle[prop] = _value2;\n    }\n  }\n  return nextStyle;\n};\nexport default preprocess;", "map": {"version": 3, "names": ["normalizeColor", "normalizeValueWithProperty", "warnOnce", "emptyObject", "defaultOffset", "height", "width", "createBoxShadowValue", "style", "shadowColor", "shadowOffset", "shadowOpacity", "shadowRadius", "_ref", "offsetX", "offsetY", "blurRadius", "color", "createTextShadowValue", "textShadowColor", "textShadowOffset", "textShadowRadius", "_ref2", "radius", "mapTransform", "transform", "type", "Object", "keys", "value", "join", "normalizedValue", "createTransformValue", "map", "PROPERTIES_STANDARD", "borderBottomEndRadius", "borderBottomStartRadius", "borderTopEndRadius", "borderTopStartRadius", "borderEndColor", "borderEndStyle", "borderEndWidth", "borderStartColor", "borderStartStyle", "borderStartWidth", "end", "marginEnd", "marginHorizontal", "marginStart", "marginVertical", "paddingEnd", "paddingHorizontal", "paddingStart", "paddingVertical", "start", "ignoredProps", "elevation", "overlayColor", "resizeMode", "tintColor", "preprocess", "originalStyle", "options", "nextStyle", "shadow", "boxShadowValue", "boxShadow", "textShadow", "textShadowValue", "_value", "originalProp", "originalValue", "prop", "_value2", "prototype", "hasOwnProperty", "call", "toString", "Array", "isArray", "length", "verticalAlign"], "sources": ["E:/CryptoSignalsApp/node_modules/react-native-web/dist/exports/StyleSheet/preprocess.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport normalizeColor from './compiler/normalizeColor';\nimport normalizeValueWithProperty from './compiler/normalizeValueWithProperty';\nimport { warnOnce } from '../../modules/warnOnce';\nvar emptyObject = {};\n\n/**\n * Shadows\n */\n\nvar defaultOffset = {\n  height: 0,\n  width: 0\n};\nexport var createBoxShadowValue = style => {\n  var shadowColor = style.shadowColor,\n    shadowOffset = style.shadowOffset,\n    shadowOpacity = style.shadowOpacity,\n    shadowRadius = style.shadowRadius;\n  var _ref = shadowOffset || defaultOffset,\n    height = _ref.height,\n    width = _ref.width;\n  var offsetX = normalizeValueWithProperty(width);\n  var offsetY = normalizeValueWithProperty(height);\n  var blurRadius = normalizeValueWithProperty(shadowRadius || 0);\n  var color = normalizeColor(shadowColor || 'black', shadowOpacity);\n  if (color != null && offsetX != null && offsetY != null && blurRadius != null) {\n    return offsetX + \" \" + offsetY + \" \" + blurRadius + \" \" + color;\n  }\n};\nexport var createTextShadowValue = style => {\n  var textShadowColor = style.textShadowColor,\n    textShadowOffset = style.textShadowOffset,\n    textShadowRadius = style.textShadowRadius;\n  var _ref2 = textShadowOffset || defaultOffset,\n    height = _ref2.height,\n    width = _ref2.width;\n  var radius = textShadowRadius || 0;\n  var offsetX = normalizeValueWithProperty(width);\n  var offsetY = normalizeValueWithProperty(height);\n  var blurRadius = normalizeValueWithProperty(radius);\n  var color = normalizeValueWithProperty(textShadowColor, 'textShadowColor');\n  if (color && (height !== 0 || width !== 0 || radius !== 0) && offsetX != null && offsetY != null && blurRadius != null) {\n    return offsetX + \" \" + offsetY + \" \" + blurRadius + \" \" + color;\n  }\n};\n\n// { scale: 2 } => 'scale(2)'\n// { translateX: 20 } => 'translateX(20px)'\n// { matrix: [1,2,3,4,5,6] } => 'matrix(1,2,3,4,5,6)'\nvar mapTransform = transform => {\n  var type = Object.keys(transform)[0];\n  var value = transform[type];\n  if (type === 'matrix' || type === 'matrix3d') {\n    return type + \"(\" + value.join(',') + \")\";\n  } else {\n    var normalizedValue = normalizeValueWithProperty(value, type);\n    return type + \"(\" + normalizedValue + \")\";\n  }\n};\nexport var createTransformValue = value => {\n  return value.map(mapTransform).join(' ');\n};\nvar PROPERTIES_STANDARD = {\n  borderBottomEndRadius: 'borderEndEndRadius',\n  borderBottomStartRadius: 'borderEndStartRadius',\n  borderTopEndRadius: 'borderStartEndRadius',\n  borderTopStartRadius: 'borderStartStartRadius',\n  borderEndColor: 'borderInlineEndColor',\n  borderEndStyle: 'borderInlineEndStyle',\n  borderEndWidth: 'borderInlineEndWidth',\n  borderStartColor: 'borderInlineStartColor',\n  borderStartStyle: 'borderInlineStartStyle',\n  borderStartWidth: 'borderInlineStartWidth',\n  end: 'insetInlineEnd',\n  marginEnd: 'marginInlineEnd',\n  marginHorizontal: 'marginInline',\n  marginStart: 'marginInlineStart',\n  marginVertical: 'marginBlock',\n  paddingEnd: 'paddingInlineEnd',\n  paddingHorizontal: 'paddingInline',\n  paddingStart: 'paddingInlineStart',\n  paddingVertical: 'paddingBlock',\n  start: 'insetInlineStart'\n};\nvar ignoredProps = {\n  elevation: true,\n  overlayColor: true,\n  resizeMode: true,\n  tintColor: true\n};\n\n/**\n * Preprocess styles\n */\nexport var preprocess = function preprocess(originalStyle, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var style = originalStyle || emptyObject;\n  var nextStyle = {};\n\n  // Convert shadow styles\n  if (options.shadow === true, style.shadowColor != null || style.shadowOffset != null || style.shadowOpacity != null || style.shadowRadius != null) {\n    warnOnce('shadowStyles', \"\\\"shadow*\\\" style props are deprecated. Use \\\"boxShadow\\\".\");\n    var boxShadowValue = createBoxShadowValue(style);\n    if (boxShadowValue != null && nextStyle.boxShadow == null) {\n      var boxShadow = style.boxShadow;\n      var value = boxShadow ? boxShadow + \", \" + boxShadowValue : boxShadowValue;\n      nextStyle.boxShadow = value;\n    }\n  }\n\n  // Convert text shadow styles\n  if (options.textShadow === true, style.textShadowColor != null || style.textShadowOffset != null || style.textShadowRadius != null) {\n    warnOnce('textShadowStyles', \"\\\"textShadow*\\\" style props are deprecated. Use \\\"textShadow\\\".\");\n    var textShadowValue = createTextShadowValue(style);\n    if (textShadowValue != null && nextStyle.textShadow == null) {\n      var textShadow = style.textShadow;\n      var _value = textShadow ? textShadow + \", \" + textShadowValue : textShadowValue;\n      nextStyle.textShadow = _value;\n    }\n  }\n  for (var originalProp in style) {\n    if (\n    // Ignore some React Native styles\n    ignoredProps[originalProp] != null || originalProp === 'shadowColor' || originalProp === 'shadowOffset' || originalProp === 'shadowOpacity' || originalProp === 'shadowRadius' || originalProp === 'textShadowColor' || originalProp === 'textShadowOffset' || originalProp === 'textShadowRadius') {\n      continue;\n    }\n    var originalValue = style[originalProp];\n    var prop = PROPERTIES_STANDARD[originalProp] || originalProp;\n    var _value2 = originalValue;\n    if (!Object.prototype.hasOwnProperty.call(style, originalProp) || prop !== originalProp && style[prop] != null) {\n      continue;\n    }\n    if (prop === 'aspectRatio' && typeof _value2 === 'number') {\n      nextStyle[prop] = _value2.toString();\n    } else if (prop === 'fontVariant') {\n      if (Array.isArray(_value2) && _value2.length > 0) {\n        /*\n        warnOnce(\n          'fontVariant',\n          '\"fontVariant\" style array value is deprecated. Use space-separated values.'\n        );\n        */\n        _value2 = _value2.join(' ');\n      }\n      nextStyle[prop] = _value2;\n    } else if (prop === 'textAlignVertical') {\n      /*\n      warnOnce(\n        'textAlignVertical',\n        '\"textAlignVertical\" style is deprecated. Use \"verticalAlign\".'\n      );\n      */\n      if (style.verticalAlign == null) {\n        nextStyle.verticalAlign = _value2 === 'center' ? 'middle' : _value2;\n      }\n    } else if (prop === 'transform') {\n      if (Array.isArray(_value2)) {\n        _value2 = createTransformValue(_value2);\n      }\n      nextStyle.transform = _value2;\n    } else {\n      nextStyle[prop] = _value2;\n    }\n  }\n\n  // $FlowIgnore\n  return nextStyle;\n};\nexport default preprocess;"], "mappings": "AASA,OAAOA,cAAc;AACrB,OAAOC,0BAA0B;AACjC,SAASC,QAAQ;AACjB,IAAIC,WAAW,GAAG,CAAC,CAAC;AAMpB,IAAIC,aAAa,GAAG;EAClBC,MAAM,EAAE,CAAC;EACTC,KAAK,EAAE;AACT,CAAC;AACD,OAAO,IAAIC,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAGC,KAAK,EAAI;EACzC,IAAIC,WAAW,GAAGD,KAAK,CAACC,WAAW;IACjCC,YAAY,GAAGF,KAAK,CAACE,YAAY;IACjCC,aAAa,GAAGH,KAAK,CAACG,aAAa;IACnCC,YAAY,GAAGJ,KAAK,CAACI,YAAY;EACnC,IAAIC,IAAI,GAAGH,YAAY,IAAIN,aAAa;IACtCC,MAAM,GAAGQ,IAAI,CAACR,MAAM;IACpBC,KAAK,GAAGO,IAAI,CAACP,KAAK;EACpB,IAAIQ,OAAO,GAAGb,0BAA0B,CAACK,KAAK,CAAC;EAC/C,IAAIS,OAAO,GAAGd,0BAA0B,CAACI,MAAM,CAAC;EAChD,IAAIW,UAAU,GAAGf,0BAA0B,CAACW,YAAY,IAAI,CAAC,CAAC;EAC9D,IAAIK,KAAK,GAAGjB,cAAc,CAACS,WAAW,IAAI,OAAO,EAAEE,aAAa,CAAC;EACjE,IAAIM,KAAK,IAAI,IAAI,IAAIH,OAAO,IAAI,IAAI,IAAIC,OAAO,IAAI,IAAI,IAAIC,UAAU,IAAI,IAAI,EAAE;IAC7E,OAAOF,OAAO,GAAG,GAAG,GAAGC,OAAO,GAAG,GAAG,GAAGC,UAAU,GAAG,GAAG,GAAGC,KAAK;EACjE;AACF,CAAC;AACD,OAAO,IAAIC,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAGV,KAAK,EAAI;EAC1C,IAAIW,eAAe,GAAGX,KAAK,CAACW,eAAe;IACzCC,gBAAgB,GAAGZ,KAAK,CAACY,gBAAgB;IACzCC,gBAAgB,GAAGb,KAAK,CAACa,gBAAgB;EAC3C,IAAIC,KAAK,GAAGF,gBAAgB,IAAIhB,aAAa;IAC3CC,MAAM,GAAGiB,KAAK,CAACjB,MAAM;IACrBC,KAAK,GAAGgB,KAAK,CAAChB,KAAK;EACrB,IAAIiB,MAAM,GAAGF,gBAAgB,IAAI,CAAC;EAClC,IAAIP,OAAO,GAAGb,0BAA0B,CAACK,KAAK,CAAC;EAC/C,IAAIS,OAAO,GAAGd,0BAA0B,CAACI,MAAM,CAAC;EAChD,IAAIW,UAAU,GAAGf,0BAA0B,CAACsB,MAAM,CAAC;EACnD,IAAIN,KAAK,GAAGhB,0BAA0B,CAACkB,eAAe,EAAE,iBAAiB,CAAC;EAC1E,IAAIF,KAAK,KAAKZ,MAAM,KAAK,CAAC,IAAIC,KAAK,KAAK,CAAC,IAAIiB,MAAM,KAAK,CAAC,CAAC,IAAIT,OAAO,IAAI,IAAI,IAAIC,OAAO,IAAI,IAAI,IAAIC,UAAU,IAAI,IAAI,EAAE;IACtH,OAAOF,OAAO,GAAG,GAAG,GAAGC,OAAO,GAAG,GAAG,GAAGC,UAAU,GAAG,GAAG,GAAGC,KAAK;EACjE;AACF,CAAC;AAKD,IAAIO,YAAY,GAAG,SAAfA,YAAYA,CAAGC,SAAS,EAAI;EAC9B,IAAIC,IAAI,GAAGC,MAAM,CAACC,IAAI,CAACH,SAAS,CAAC,CAAC,CAAC,CAAC;EACpC,IAAII,KAAK,GAAGJ,SAAS,CAACC,IAAI,CAAC;EAC3B,IAAIA,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,UAAU,EAAE;IAC5C,OAAOA,IAAI,GAAG,GAAG,GAAGG,KAAK,CAACC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG;EAC3C,CAAC,MAAM;IACL,IAAIC,eAAe,GAAG9B,0BAA0B,CAAC4B,KAAK,EAAEH,IAAI,CAAC;IAC7D,OAAOA,IAAI,GAAG,GAAG,GAAGK,eAAe,GAAG,GAAG;EAC3C;AACF,CAAC;AACD,OAAO,IAAIC,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAGH,KAAK,EAAI;EACzC,OAAOA,KAAK,CAACI,GAAG,CAACT,YAAY,CAAC,CAACM,IAAI,CAAC,GAAG,CAAC;AAC1C,CAAC;AACD,IAAII,mBAAmB,GAAG;EACxBC,qBAAqB,EAAE,oBAAoB;EAC3CC,uBAAuB,EAAE,sBAAsB;EAC/CC,kBAAkB,EAAE,sBAAsB;EAC1CC,oBAAoB,EAAE,wBAAwB;EAC9CC,cAAc,EAAE,sBAAsB;EACtCC,cAAc,EAAE,sBAAsB;EACtCC,cAAc,EAAE,sBAAsB;EACtCC,gBAAgB,EAAE,wBAAwB;EAC1CC,gBAAgB,EAAE,wBAAwB;EAC1CC,gBAAgB,EAAE,wBAAwB;EAC1CC,GAAG,EAAE,gBAAgB;EACrBC,SAAS,EAAE,iBAAiB;EAC5BC,gBAAgB,EAAE,cAAc;EAChCC,WAAW,EAAE,mBAAmB;EAChCC,cAAc,EAAE,aAAa;EAC7BC,UAAU,EAAE,kBAAkB;EAC9BC,iBAAiB,EAAE,eAAe;EAClCC,YAAY,EAAE,oBAAoB;EAClCC,eAAe,EAAE,cAAc;EAC/BC,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,YAAY,GAAG;EACjBC,SAAS,EAAE,IAAI;EACfC,YAAY,EAAE,IAAI;EAClBC,UAAU,EAAE,IAAI;EAChBC,SAAS,EAAE;AACb,CAAC;AAKD,OAAO,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAACC,aAAa,EAAEC,OAAO,EAAE;EAClE,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,IAAItD,KAAK,GAAGqD,aAAa,IAAI1D,WAAW;EACxC,IAAI4D,SAAS,GAAG,CAAC,CAAC;EAGlB,IAAID,OAAO,CAACE,MAAM,KAAK,IAAI,EAAExD,KAAK,CAACC,WAAW,IAAI,IAAI,IAAID,KAAK,CAACE,YAAY,IAAI,IAAI,IAAIF,KAAK,CAACG,aAAa,IAAI,IAAI,IAAIH,KAAK,CAACI,YAAY,IAAI,IAAI,EAAE;IACjJV,QAAQ,CAAC,cAAc,EAAE,4DAA4D,CAAC;IACtF,IAAI+D,cAAc,GAAG1D,oBAAoB,CAACC,KAAK,CAAC;IAChD,IAAIyD,cAAc,IAAI,IAAI,IAAIF,SAAS,CAACG,SAAS,IAAI,IAAI,EAAE;MACzD,IAAIA,SAAS,GAAG1D,KAAK,CAAC0D,SAAS;MAC/B,IAAIrC,KAAK,GAAGqC,SAAS,GAAGA,SAAS,GAAG,IAAI,GAAGD,cAAc,GAAGA,cAAc;MAC1EF,SAAS,CAACG,SAAS,GAAGrC,KAAK;IAC7B;EACF;EAGA,IAAIiC,OAAO,CAACK,UAAU,KAAK,IAAI,EAAE3D,KAAK,CAACW,eAAe,IAAI,IAAI,IAAIX,KAAK,CAACY,gBAAgB,IAAI,IAAI,IAAIZ,KAAK,CAACa,gBAAgB,IAAI,IAAI,EAAE;IAClInB,QAAQ,CAAC,kBAAkB,EAAE,iEAAiE,CAAC;IAC/F,IAAIkE,eAAe,GAAGlD,qBAAqB,CAACV,KAAK,CAAC;IAClD,IAAI4D,eAAe,IAAI,IAAI,IAAIL,SAAS,CAACI,UAAU,IAAI,IAAI,EAAE;MAC3D,IAAIA,UAAU,GAAG3D,KAAK,CAAC2D,UAAU;MACjC,IAAIE,MAAM,GAAGF,UAAU,GAAGA,UAAU,GAAG,IAAI,GAAGC,eAAe,GAAGA,eAAe;MAC/EL,SAAS,CAACI,UAAU,GAAGE,MAAM;IAC/B;EACF;EACA,KAAK,IAAIC,YAAY,IAAI9D,KAAK,EAAE;IAC9B,IAEA+C,YAAY,CAACe,YAAY,CAAC,IAAI,IAAI,IAAIA,YAAY,KAAK,aAAa,IAAIA,YAAY,KAAK,cAAc,IAAIA,YAAY,KAAK,eAAe,IAAIA,YAAY,KAAK,cAAc,IAAIA,YAAY,KAAK,iBAAiB,IAAIA,YAAY,KAAK,kBAAkB,IAAIA,YAAY,KAAK,kBAAkB,EAAE;MAClS;IACF;IACA,IAAIC,aAAa,GAAG/D,KAAK,CAAC8D,YAAY,CAAC;IACvC,IAAIE,IAAI,GAAGtC,mBAAmB,CAACoC,YAAY,CAAC,IAAIA,YAAY;IAC5D,IAAIG,OAAO,GAAGF,aAAa;IAC3B,IAAI,CAAC5C,MAAM,CAAC+C,SAAS,CAACC,cAAc,CAACC,IAAI,CAACpE,KAAK,EAAE8D,YAAY,CAAC,IAAIE,IAAI,KAAKF,YAAY,IAAI9D,KAAK,CAACgE,IAAI,CAAC,IAAI,IAAI,EAAE;MAC9G;IACF;IACA,IAAIA,IAAI,KAAK,aAAa,IAAI,OAAOC,OAAO,KAAK,QAAQ,EAAE;MACzDV,SAAS,CAACS,IAAI,CAAC,GAAGC,OAAO,CAACI,QAAQ,CAAC,CAAC;IACtC,CAAC,MAAM,IAAIL,IAAI,KAAK,aAAa,EAAE;MACjC,IAAIM,KAAK,CAACC,OAAO,CAACN,OAAO,CAAC,IAAIA,OAAO,CAACO,MAAM,GAAG,CAAC,EAAE;QAOhDP,OAAO,GAAGA,OAAO,CAAC3C,IAAI,CAAC,GAAG,CAAC;MAC7B;MACAiC,SAAS,CAACS,IAAI,CAAC,GAAGC,OAAO;IAC3B,CAAC,MAAM,IAAID,IAAI,KAAK,mBAAmB,EAAE;MAOvC,IAAIhE,KAAK,CAACyE,aAAa,IAAI,IAAI,EAAE;QAC/BlB,SAAS,CAACkB,aAAa,GAAGR,OAAO,KAAK,QAAQ,GAAG,QAAQ,GAAGA,OAAO;MACrE;IACF,CAAC,MAAM,IAAID,IAAI,KAAK,WAAW,EAAE;MAC/B,IAAIM,KAAK,CAACC,OAAO,CAACN,OAAO,CAAC,EAAE;QAC1BA,OAAO,GAAGzC,oBAAoB,CAACyC,OAAO,CAAC;MACzC;MACAV,SAAS,CAACtC,SAAS,GAAGgD,OAAO;IAC/B,CAAC,MAAM;MACLV,SAAS,CAACS,IAAI,CAAC,GAAGC,OAAO;IAC3B;EACF;EAGA,OAAOV,SAAS;AAClB,CAAC;AACD,eAAeH,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}