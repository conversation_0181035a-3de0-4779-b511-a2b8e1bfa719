{"ast": null, "code": "import { EventEmitter } from \"../EventEmitter\";\nimport Platform from \"../Platform\";\nimport { CodedError } from \"../errors/CodedError\";\nimport NativeErrorManager from \"./NativeErrorManager\";\nif (__DEV__ && Platform.OS === 'android' && NativeErrorManager) {\n  var onNewException = 'ExpoModulesCoreErrorManager.onNewException';\n  var eventEmitter = new EventEmitter(NativeErrorManager);\n  eventEmitter.addListener(onNewException, function (_ref) {\n    var message = _ref.message;\n    console.error(message);\n  });\n}\nglobalThis.ExpoModulesCore_CodedError = CodedError;", "map": {"version": 3, "names": ["EventEmitter", "Platform", "CodedError", "NativeErrorManager", "__DEV__", "OS", "onNewException", "eventEmitter", "addListener", "_ref", "message", "console", "error", "globalThis", "ExpoModulesCore_CodedError"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\expo-modules-core\\src\\sweet\\setUpErrorManager.fx.ts"], "sourcesContent": ["import { EventEmitter } from '../EventEmitter';\nimport Platform from '../Platform';\nimport { CodedError } from '../errors/CodedError';\nimport NativeErrorManager from './NativeErrorManager';\n\nif (__DEV__ && Platform.OS === 'android' && NativeErrorManager) {\n  const onNewException = 'ExpoModulesCoreErrorManager.onNewException';\n  const eventEmitter = new EventEmitter(NativeErrorManager);\n\n  eventEmitter.addListener(onNewException, ({ message }: { message: string }) => {\n    console.error(message);\n  });\n}\n\n// We have to export `CodedError` via global object to use in later in the C++ code.\nglobalThis.ExpoModulesCore_CodedError = CodedError;\n"], "mappings": "AAAA,SAASA,YAAY;AACrB,OAAOC,QAAQ;AACf,SAASC,UAAU;AACnB,OAAOC,kBAAkB;AAEzB,IAAIC,OAAO,IAAIH,QAAQ,CAACI,EAAE,KAAK,SAAS,IAAIF,kBAAkB,EAAE;EAC9D,IAAMG,cAAc,GAAG,4CAA4C;EACnE,IAAMC,YAAY,GAAG,IAAIP,YAAY,CAACG,kBAAkB,CAAC;EAEzDI,YAAY,CAACC,WAAW,CAACF,cAAc,EAAE,UAAAG,IAAA,EAAqC;IAAA,IAAlCC,OAAO,GAAAD,IAAA,CAAPC,OAAO;IACjDC,OAAO,CAACC,KAAK,CAACF,OAAO,CAAC;EACxB,CAAC,CAAC;;AAIJG,UAAU,CAACC,0BAA0B,GAAGZ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}