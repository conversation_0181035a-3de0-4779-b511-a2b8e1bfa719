#!/usr/bin/env python3
"""
Sistema de Notificações via Webhook
Integração com sistemas externos (Discord, Slack, Teams, etc.)
"""

import json
import logging
import requests
from typing import List, Dict, Optional
from datetime import datetime
import hashlib
import hmac
import time

logger = logging.getLogger(__name__)

class WebhookNotifier:
    """Sistema de notificações via webhook"""

    def __init__(self, config: Dict):
        self.webhooks = config.get('webhooks', [])
        self.enabled = config.get('enabled', False)
        self.timeout = config.get('timeout', 10)
        self.retry_attempts = config.get('retry_attempts', 3)

        if not self.webhooks:
            logger.warning("Nenhum webhook configurado")
            self.enabled = False

    def add_webhook(self, webhook_config: Dict) -> bool:
        """Adiciona novo webhook"""
        try:
            required_fields = ['name', 'url', 'type']
            if not all(field in webhook_config for field in required_fields):
                logger.error("Configuração de webhook inválida")
                return False

            # Verificar se já existe
            for webhook in self.webhooks:
                if webhook['url'] == webhook_config['url']:
                    logger.info("Webhook já existe, atualizando...")
                    webhook.update(webhook_config)
                    return True

            # Adicionar novo webhook
            webhook_config['created_at'] = datetime.now().isoformat()
            self.webhooks.append(webhook_config)

            logger.info(f"Novo webhook adicionado: {webhook_config['name']}")
            return True

        except Exception as e:
            logger.error(f"Erro ao adicionar webhook: {e}")
            return False

    def remove_webhook(self, webhook_name: str) -> bool:
        """Remove webhook"""
        try:
            initial_count = len(self.webhooks)
            self.webhooks = [w for w in self.webhooks if w['name'] != webhook_name]

            removed = initial_count - len(self.webhooks)
            if removed > 0:
                logger.info(f"Webhook removido: {webhook_name}")
                return True
            else:
                logger.warning(f"Webhook não encontrado: {webhook_name}")
                return False

        except Exception as e:
            logger.error(f"Erro ao remover webhook: {e}")
            return False

    def send_signal_webhook(self, signal_data: Dict) -> Dict[str, int]:
        """Envia webhook de novo sinal"""
        if not self.enabled or not self.webhooks:
            return {'sent': 0, 'failed': 0}

        sent_count = 0
        failed_count = 0

        for webhook in self.webhooks:
            try:
                if webhook.get('events', {}).get('signals', True):
                    payload = self._format_signal_payload(signal_data, webhook['type'])
                    success = self._send_webhook(webhook, payload)

                    if success:
                        sent_count += 1
                    else:
                        failed_count += 1

            except Exception as e:
                logger.error(f"Erro ao enviar webhook para {webhook['name']}: {e}")
                failed_count += 1

        return {'sent': sent_count, 'failed': failed_count}

    def send_performance_webhook(self, performance_data: Dict) -> Dict[str, int]:
        """Envia webhook de performance"""
        if not self.enabled or not self.webhooks:
            return {'sent': 0, 'failed': 0}

        sent_count = 0
        failed_count = 0

        for webhook in self.webhooks:
            try:
                if webhook.get('events', {}).get('performance', True):
                    payload = self._format_performance_payload(performance_data, webhook['type'])
                    success = self._send_webhook(webhook, payload)

                    if success:
                        sent_count += 1
                    else:
                        failed_count += 1

            except Exception as e:
                logger.error(f"Erro ao enviar webhook para {webhook['name']}: {e}")
                failed_count += 1

        return {'sent': sent_count, 'failed': failed_count}

    def send_system_webhook(self, message: str, level: str = 'info') -> Dict[str, int]:
        """Envia webhook do sistema"""
        if not self.enabled or not self.webhooks:
            return {'sent': 0, 'failed': 0}

        sent_count = 0
        failed_count = 0

        system_data = {
            'message': message,
            'level': level,
            'timestamp': datetime.now().isoformat(),
            'system': 'CryptoSignals'
        }

        for webhook in self.webhooks:
            try:
                if webhook.get('events', {}).get('system', True):
                    payload = self._format_system_payload(system_data, webhook['type'])
                    success = self._send_webhook(webhook, payload)

                    if success:
                        sent_count += 1
                    else:
                        failed_count += 1

            except Exception as e:
                logger.error(f"Erro ao enviar webhook para {webhook['name']}: {e}")
                failed_count += 1

        return {'sent': sent_count, 'failed': failed_count}

    def _format_signal_payload(self, signal_data: Dict, webhook_type: str) -> Dict:
        """Formata payload para diferentes tipos de webhook"""

        if webhook_type == 'discord':
            return self._format_discord_signal(signal_data)
        elif webhook_type == 'slack':
            return self._format_slack_signal(signal_data)
        elif webhook_type == 'teams':
            return self._format_teams_signal(signal_data)
        else:
            return self._format_generic_signal(signal_data)

    def _format_discord_signal(self, signal_data: Dict) -> Dict:
        """Formata payload para Discord"""
        color = 0x00ff00 if signal_data['signal_type'] == 'LONG' else 0xff0000

        return {
            "embeds": [{
                "title": f"🎯 New {signal_data['signal_type']} Signal",
                "description": f"**{signal_data['symbol']}** - {signal_data['strategy']}",
                "color": color,
                "fields": [
                    {
                        "name": "Entry Price",
                        "value": f"${signal_data['entry_price']:.6f}",
                        "inline": True
                    },
                    {
                        "name": "Stop Loss",
                        "value": f"${signal_data['stop_loss']:.6f}",
                        "inline": True
                    },
                    {
                        "name": "Take Profit",
                        "value": f"${signal_data['take_profit']:.6f}",
                        "inline": True
                    },
                    {
                        "name": "Leverage",
                        "value": f"{signal_data.get('leverage', 'N/A')}x",
                        "inline": True
                    },
                    {
                        "name": "Timeframe",
                        "value": signal_data.get('timeframe', 'N/A'),
                        "inline": True
                    },
                    {
                        "name": "Confidence",
                        "value": f"{signal_data.get('confidence', 'N/A')}%",
                        "inline": True
                    }
                ],
                "footer": {
                    "text": "CryptoSignals Professional v2.0.0"
                },
                "timestamp": datetime.now().isoformat()
            }]
        }

    def _format_slack_signal(self, signal_data: Dict) -> Dict:
        """Formata payload para Slack"""
        color = "good" if signal_data['signal_type'] == 'LONG' else "danger"

        return {
            "attachments": [{
                "color": color,
                "title": f"🎯 New {signal_data['signal_type']} Signal",
                "text": f"*{signal_data['symbol']}* - {signal_data['strategy']}",
                "fields": [
                    {
                        "title": "Entry Price",
                        "value": f"${signal_data['entry_price']:.6f}",
                        "short": True
                    },
                    {
                        "title": "Stop Loss",
                        "value": f"${signal_data['stop_loss']:.6f}",
                        "short": True
                    },
                    {
                        "title": "Take Profit",
                        "value": f"${signal_data['take_profit']:.6f}",
                        "short": True
                    },
                    {
                        "title": "Leverage",
                        "value": f"{signal_data.get('leverage', 'N/A')}x",
                        "short": True
                    }
                ],
                "footer": "CryptoSignals Professional",
                "ts": int(datetime.now().timestamp())
            }]
        }

    def _format_teams_signal(self, signal_data: Dict) -> Dict:
        """Formata payload para Microsoft Teams"""
        return {
            "@type": "MessageCard",
            "@context": "http://schema.org/extensions",
            "themeColor": "0076D7",
            "summary": f"New {signal_data['signal_type']} Signal",
            "sections": [{
                "activityTitle": f"🎯 New {signal_data['signal_type']} Signal",
                "activitySubtitle": f"{signal_data['symbol']} - {signal_data['strategy']}",
                "facts": [
                    {
                        "name": "Entry Price:",
                        "value": f"${signal_data['entry_price']:.6f}"
                    },
                    {
                        "name": "Stop Loss:",
                        "value": f"${signal_data['stop_loss']:.6f}"
                    },
                    {
                        "name": "Take Profit:",
                        "value": f"${signal_data['take_profit']:.6f}"
                    },
                    {
                        "name": "Leverage:",
                        "value": f"{signal_data.get('leverage', 'N/A')}x"
                    }
                ],
                "markdown": True
            }]
        }

    def _format_generic_signal(self, signal_data: Dict) -> Dict:
        """Formata payload genérico"""
        return {
            "event": "new_signal",
            "data": signal_data,
            "timestamp": datetime.now().isoformat(),
            "source": "CryptoSignals"
        }

    def _format_performance_payload(self, performance_data: Dict, webhook_type: str) -> Dict:
        """Formata payload de performance"""
        if webhook_type == 'discord':
            return {
                "embeds": [{
                    "title": "📊 Performance Report",
                    "description": f"**{performance_data.get('period', 'Daily')} Summary**",
                    "color": 0x0099ff,
                    "fields": [
                        {
                            "name": "Total Signals",
                            "value": str(performance_data.get('total_signals', 0)),
                            "inline": True
                        },
                        {
                            "name": "Win Rate",
                            "value": f"{performance_data.get('win_rate', 0)}%",
                            "inline": True
                        },
                        {
                            "name": "Total Profit",
                            "value": f"{performance_data.get('total_profit', 0):.2f}%",
                            "inline": True
                        }
                    ],
                    "timestamp": datetime.now().isoformat()
                }]
            }
        else:
            return {
                "event": "performance_report",
                "data": performance_data,
                "timestamp": datetime.now().isoformat(),
                "source": "CryptoSignals"
            }

    def _format_system_payload(self, system_data: Dict, webhook_type: str) -> Dict:
        """Formata payload do sistema"""
        if webhook_type == 'discord':
            colors = {
                'info': 0x0099ff,
                'warning': 0xffaa00,
                'error': 0xff0000,
                'success': 0x00ff00
            }

            return {
                "embeds": [{
                    "title": f"🔔 System {system_data['level'].title()}",
                    "description": system_data['message'],
                    "color": colors.get(system_data['level'], 0x0099ff),
                    "timestamp": system_data['timestamp']
                }]
            }
        else:
            return {
                "event": "system_notification",
                "data": system_data,
                "timestamp": datetime.now().isoformat(),
                "source": "CryptoSignals"
            }

    def _send_webhook(self, webhook: Dict, payload: Dict) -> bool:
        """Envia webhook com retry"""
        for attempt in range(self.retry_attempts):
            try:
                headers = {'Content-Type': 'application/json'}

                # Adicionar autenticação se configurada
                if webhook.get('secret'):
                    signature = self._generate_signature(payload, webhook['secret'])
                    headers['X-Hub-Signature-256'] = f"sha256={signature}"

                response = requests.post(
                    webhook['url'],
                    json=payload,
                    headers=headers,
                    timeout=self.timeout
                )

                if response.status_code in [200, 201, 202, 204]:
                    logger.debug(f"Webhook enviado com sucesso para {webhook['name']}")
                    return True
                else:
                    logger.warning(f"Webhook falhou para {webhook['name']}: {response.status_code}")

            except requests.exceptions.RequestException as e:
                logger.error(f"Erro de rede no webhook {webhook['name']} (tentativa {attempt + 1}): {e}")

            except Exception as e:
                logger.error(f"Erro inesperado no webhook {webhook['name']}: {e}")

            # Aguardar antes de tentar novamente
            if attempt < self.retry_attempts - 1:
                time.sleep(2 ** attempt)  # Backoff exponencial

        return False

    def _generate_signature(self, payload: Dict, secret: str) -> str:
        """Gera assinatura HMAC para autenticação"""
        payload_bytes = json.dumps(payload, sort_keys=True).encode('utf-8')
        signature = hmac.new(
            secret.encode('utf-8'),
            payload_bytes,
            hashlib.sha256
        ).hexdigest()
        return signature

    def test_webhook(self, webhook_name: str) -> bool:
        """Testa webhook específico"""
        webhook = next((w for w in self.webhooks if w['name'] == webhook_name), None)
        if not webhook:
            logger.error(f"Webhook não encontrado: {webhook_name}")
            return False

        test_payload = {
            "event": "test",
            "message": "CryptoSignals webhook test",
            "timestamp": datetime.now().isoformat(),
            "source": "CryptoSignals"
        }

        return self._send_webhook(webhook, test_payload)

    def get_webhook_count(self) -> int:
        """Retorna número de webhooks configurados"""
        return len(self.webhooks)

    def get_webhooks_status(self) -> List[Dict]:
        """Retorna status de todos os webhooks"""
        status_list = []
        for webhook in self.webhooks:
            status_list.append({
                'name': webhook['name'],
                'type': webhook['type'],
                'url': webhook['url'][:50] + '...' if len(webhook['url']) > 50 else webhook['url'],
                'enabled': webhook.get('enabled', True),
                'events': webhook.get('events', {}),
                'created_at': webhook.get('created_at')
            })
        return status_list
