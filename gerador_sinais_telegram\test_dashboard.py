#!/usr/bin/env python3
"""
Script de teste para verificar se o dashboard está funcionando
"""

import requests
import json
import time

def test_dashboard():
    """Testa se o dashboard está respondendo"""
    
    print("🧪 Testando Dashboard Web...")
    print("=" * 50)
    
    # Testar se o servidor está rodando
    try:
        response = requests.get("http://localhost:5000", timeout=5)
        if response.status_code == 200:
            print("✅ Dashboard está rodando!")
            print(f"   Status Code: {response.status_code}")
            print(f"   Content-Type: {response.headers.get('content-type', 'N/A')}")
        else:
            print(f"⚠️ Dashboard respondeu com status: {response.status_code}")
    except requests.exceptions.ConnectionError:
        print("❌ Dashboard não está rodando ou não está acessível")
        print("   Certifique-se de que 'python web_dashboard.py' está executando")
        return False
    except requests.exceptions.Timeout:
        print("⏰ Timeout ao conectar com o dashboard")
        return False
    except Exception as e:
        print(f"❌ Erro ao testar dashboard: {e}")
        return False
    
    # Testar API de dados
    try:
        print("\n🔍 Testando API de dados...")
        response = requests.get("http://localhost:5000/api/data", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("✅ API de dados funcionando!")
            print(f"   Timestamp: {data.get('timestamp', 'N/A')}")
            print(f"   Serviços: {len(data.get('services', {}))}")
            print(f"   Alertas: {len(data.get('alerts', []))}")
            print(f"   Logs: {len(data.get('logs', []))}")
            
            # Mostrar dados dos serviços
            services = data.get('services', {})
            if services:
                print("\n📊 Status dos Serviços:")
                for service_id, service_data in services.items():
                    status = service_data.get('status', 'unknown')
                    name = service_data.get('name', service_id)
                    print(f"   • {name}: {status}")
            
            # Mostrar métricas do servidor
            metrics = data.get('server_metrics', {})
            if metrics:
                print("\n🖥️ Métricas do Servidor:")
                print(f"   • CPU: {metrics.get('cpu_percent', 0):.1f}%")
                print(f"   • Memória: {metrics.get('memory_percent', 0):.1f}%")
                print(f"   • Disco: {metrics.get('disk_percent', 0):.1f}%")
                print(f"   • Uptime: {metrics.get('uptime', 'N/A')}")
            
        else:
            print(f"⚠️ API retornou status: {response.status_code}")
            print(f"   Response: {response.text[:200]}...")
            
    except Exception as e:
        print(f"❌ Erro ao testar API: {e}")
    
    # Testar API de logs
    try:
        print("\n📋 Testando API de logs...")
        response = requests.get("http://localhost:5000/api/logs/10", timeout=5)
        if response.status_code == 200:
            data = response.json()
            logs = data.get('logs', [])
            print(f"✅ API de logs funcionando! ({len(logs)} logs)")
            
            if logs:
                print("   Últimos logs:")
                for log in logs[-3:]:  # Mostrar últimos 3 logs
                    level = log.get('level', 'INFO')
                    message = log.get('message', '')[:50]
                    timestamp = log.get('timestamp', '')
                    print(f"   • [{level}] {timestamp}: {message}...")
        else:
            print(f"⚠️ API de logs retornou status: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Erro ao testar API de logs: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 Teste concluído!")
    print("\n📊 Para acessar o dashboard:")
    print("   🌐 http://localhost:5000")
    print("   🌐 http://127.0.0.1:5000")
    print("\n🔄 O dashboard atualiza automaticamente a cada 10 segundos")
    print("💡 Use Ctrl+C no terminal do dashboard para parar")
    
    return True

def monitor_dashboard():
    """Monitora o dashboard continuamente"""
    print("\n🔄 Monitoramento contínuo iniciado...")
    print("Pressione Ctrl+C para parar")
    print("-" * 30)
    
    try:
        while True:
            try:
                response = requests.get("http://localhost:5000/api/data", timeout=3)
                if response.status_code == 200:
                    data = response.json()
                    timestamp = data.get('timestamp', '')
                    services_count = len(data.get('services', {}))
                    alerts_count = len(data.get('alerts', []))
                    
                    print(f"✅ {timestamp[:19]} | Serviços: {services_count} | Alertas: {alerts_count}")
                else:
                    print(f"⚠️ {time.strftime('%Y-%m-%d %H:%M:%S')} | Status: {response.status_code}")
                    
            except requests.exceptions.ConnectionError:
                print(f"❌ {time.strftime('%Y-%m-%d %H:%M:%S')} | Dashboard desconectado")
            except Exception as e:
                print(f"❌ {time.strftime('%Y-%m-%d %H:%M:%S')} | Erro: {e}")
            
            time.sleep(5)  # Verificar a cada 5 segundos
            
    except KeyboardInterrupt:
        print("\n🛑 Monitoramento interrompido")

def main():
    """Função principal"""
    print("🧪 TESTE DO DASHBOARD CRYPTOSIGNALS")
    print("=" * 50)
    
    # Teste básico
    if test_dashboard():
        # Perguntar se quer monitorar continuamente
        choice = input("\nDeseja monitorar o dashboard continuamente? (y/n): ").strip().lower()
        if choice in ['y', 'yes', 's', 'sim']:
            monitor_dashboard()
    else:
        print("\n❌ Dashboard não está funcionando corretamente")
        print("\n🔧 Soluções:")
        print("1. Certifique-se de que o dashboard está rodando:")
        print("   python web_dashboard.py")
        print("2. Verifique se a porta 5000 não está em uso")
        print("3. Verifique se as dependências estão instaladas:")
        print("   pip install flask flask-socketio paramiko psutil")

if __name__ == "__main__":
    main()
