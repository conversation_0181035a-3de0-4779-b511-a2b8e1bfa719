const schedule = require('node-schedule');

// Função de atualização para ler mensagens do Telegram e atualizar o aplicativo
function performUpdate() {
    // Aqui, você inserirá o código que lê mensagens do Telegram e atualiza o aplicativo
    console.log('Atualização agendada executada!');
}

// Configure o agendamento de atualizações
function scheduleUpdates() {
    // Defina um cronograma para ler mensagens do Telegram e atualizar o aplicativo
    // Por exemplo: agende para executar todos os dias às 14:30
    schedule.scheduleJob('30 14 * * *', performUpdate);
  
    console.log('Atualizações agendadas para todos os dias às 14:30.');
}

module.exports = {
    scheduleUpdates,
};

  
  //adicionar npm install node-schedule
