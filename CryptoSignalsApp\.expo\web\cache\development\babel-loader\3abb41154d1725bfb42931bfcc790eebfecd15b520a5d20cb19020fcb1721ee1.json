{"ast": null, "code": "import color from 'color';\nexport function getTextColor(_ref) {\n  var theme = _ref.theme,\n    disabled = _ref.disabled,\n    type = _ref.type;\n  var _theme$colors;\n  var colors = theme.colors,\n    dark = theme.dark;\n  if (type === 'error') {\n    return colors === null || colors === void 0 ? void 0 : colors.error;\n  }\n  if (theme.isV3) {\n    if (disabled) {\n      return theme.colors.onSurfaceDisabled;\n    } else {\n      return theme.colors.onSurfaceVariant;\n    }\n  }\n  return color(theme === null || theme === void 0 || (_theme$colors = theme.colors) === null || _theme$colors === void 0 ? void 0 : _theme$colors.text).alpha(dark ? 0.7 : 0.54).rgb().string();\n}", "map": {"version": 3, "names": ["color", "getTextColor", "_ref", "theme", "disabled", "type", "_theme$colors", "colors", "dark", "error", "isV3", "onSurfaceDisabled", "onSurfaceVariant", "text", "alpha", "rgb", "string"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\HelperText\\utils.ts"], "sourcesContent": ["import color from 'color';\n\nimport type { InternalTheme } from '../../types';\n\ntype BaseProps = {\n  theme: InternalTheme;\n  disabled?: boolean;\n  type?: 'error' | 'info';\n};\n\nexport function getTextColor({ theme, disabled, type }: BaseProps) {\n  const { colors, dark } = theme;\n\n  if (type === 'error') {\n    return colors?.error;\n  }\n\n  if (theme.isV3) {\n    if (disabled) {\n      return theme.colors.onSurfaceDisabled;\n    } else {\n      return theme.colors.onSurfaceVariant;\n    }\n  }\n\n  return color(theme?.colors?.text)\n    .alpha(dark ? 0.7 : 0.54)\n    .rgb()\n    .string();\n}\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAUzB,OAAO,SAASC,YAAYA,CAAAC,IAAA,EAAuC;EAAA,IAApCC,KAAK,GAAAD,IAAA,CAALC,KAAK;IAAEC,QAAQ,GAAAF,IAAA,CAARE,QAAQ;IAAEC,IAAA,GAAAH,IAAA,CAAAG,IAAA;EAAmB,IAAAC,aAAA;EACjE,IAAQC,MAAM,GAAWJ,KAAK,CAAtBI,MAAM;IAAEC,IAAA,GAASL,KAAK,CAAdK,IAAA;EAEhB,IAAIH,IAAI,KAAK,OAAO,EAAE;IACpB,OAAOE,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEE,KAAK;EACtB;EAEA,IAAIN,KAAK,CAACO,IAAI,EAAE;IACd,IAAIN,QAAQ,EAAE;MACZ,OAAOD,KAAK,CAACI,MAAM,CAACI,iBAAiB;IACvC,CAAC,MAAM;MACL,OAAOR,KAAK,CAACI,MAAM,CAACK,gBAAgB;IACtC;EACF;EAEA,OAAOZ,KAAK,CAACG,KAAK,aAALA,KAAK,gBAAAG,aAAA,GAALH,KAAK,CAAEI,MAAM,cAAAD,aAAA,uBAAbA,aAAA,CAAeO,IAAI,CAAC,CAC9BC,KAAK,CAACN,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,CACxBO,GAAG,CAAC,CAAC,CACLC,MAAM,CAAC,CAAC;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}