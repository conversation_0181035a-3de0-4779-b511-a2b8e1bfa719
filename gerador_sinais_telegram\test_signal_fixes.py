#!/usr/bin/env python3
"""
Script para testar as correções nos sinais
"""

import asyncio
import logging
import sys
import os

# Adicionar o diretório raiz ao path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.binance_client import BinanceHandler
from utils.signal_formatter import SignalFormatter
from estrategias.breakout_volume import BreakoutVolumeStrategy
from estrategias.scalp_strategy import ScalpStrategy
from estrategias.mfi_strategy import MFIStrategy
from estrategias.inside_bar import InsideBarStrategy
from estrategias.swing_strategy import SwingStrategy

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_price_validation():
    """Testa a validação de preços"""
    logger.info("=== TESTE DE VALIDAÇÃO DE PREÇOS ===")

    binance = BinanceHandler(use_mock_data=True)

    test_symbols = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'SOLUSDT', 'BNBUSDT']

    for symbol in test_symbols:
        price = binance.get_current_price(symbol)
        logger.info(f"{symbol}: Preço = {price}")

        # Testar validação
        is_valid = binance._validate_price(symbol, price)
        logger.info(f"{symbol}: Preço válido = {is_valid}")

        if not is_valid:
            logger.error(f"❌ PREÇO INVÁLIDO DETECTADO: {symbol} = {price}")
        else:
            logger.info(f"✅ Preço válido: {symbol} = {price}")

async def test_signal_formatting():
    """Testa a formatação de sinais"""
    logger.info("\n=== TESTE DE FORMATAÇÃO DE SINAIS ===")

    formatter = SignalFormatter()

    # Teste com preços válidos
    test_cases = [
        {
            'symbol': 'BTCUSDT',
            'entry_price': 50000,
            'take_profits': {40: 51000, 60: 52000, 80: 53000, 100: 54000},
            'signal_type': 'LONG'
        },
        {
            'symbol': 'ETHUSDT',
            'entry_price': 2000,
            'take_profits': {40: 2080, 60: 2160, 80: 2240, 100: 2320},
            'signal_type': 'LONG'
        },
        {
            'symbol': 'ADAUSDT',
            'entry_price': 0.5,
            'take_profits': {40: 0.52, 60: 0.54, 80: 0.56, 100: 0.58},
            'signal_type': 'LONG'
        }
    ]

    for i, test_case in enumerate(test_cases, 1):
        logger.info(f"\n--- Teste {i}: {test_case['symbol']} ---")

        message = formatter.format_scalp_signal(
            symbol=test_case['symbol'],
            entry_price=test_case['entry_price'],
            take_profits=test_case['take_profits'],
            signal_type=test_case['signal_type']
        )

        if message:
            logger.info(f"✅ Sinal formatado com sucesso para {test_case['symbol']}")
            logger.info(f"Mensagem:\n{message}")
        else:
            logger.error(f"❌ Falha ao formatar sinal para {test_case['symbol']}")

async def test_all_strategies():
    """Testa todas as estratégias com validações"""
    logger.info("\n=== TESTE DE TODAS AS ESTRATÉGIAS ===")

    binance = BinanceHandler(use_mock_data=True)

    strategies = {
        'Breakout': BreakoutVolumeStrategy(binance),
        'Scalp': ScalpStrategy(binance),
        'MFI': MFIStrategy(binance),
        'InsideBar': InsideBarStrategy(binance),
        'Swing': SwingStrategy(binance)
    }

    test_symbols = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT']

    for strategy_name, strategy in strategies.items():
        logger.info(f"\n=== TESTANDO ESTRATÉGIA {strategy_name.upper()} ===")

        for symbol in test_symbols:
            logger.info(f"\n--- {strategy_name}: {symbol} ---")

            try:
                signal_type, entry_price, stop_loss, take_profit = strategy.analyze_symbol(symbol)

                if signal_type:
                    logger.info(f"✅ Sinal {strategy_name} gerado para {symbol}:")
                    logger.info(f"  Tipo: {signal_type}")
                    logger.info(f"  Entrada: {entry_price}")
                    logger.info(f"  Stop Loss: {stop_loss}")
                    logger.info(f"  Take Profit: {take_profit}")

                    # Validar se os valores fazem sentido
                    if entry_price <= 0:
                        logger.error(f"❌ Preço de entrada inválido: {entry_price}")
                    elif (signal_type == 'LONG' and stop_loss >= entry_price) or (signal_type == 'SHORT' and stop_loss <= entry_price):
                        logger.error(f"❌ Stop loss inválido: {stop_loss} vs {entry_price}")
                    elif (signal_type == 'LONG' and take_profit <= entry_price) or (signal_type == 'SHORT' and take_profit >= entry_price):
                        logger.error(f"❌ Take profit inválido: {take_profit} vs {entry_price}")
                    else:
                        logger.info(f"✅ Valores do sinal são válidos")

                        # Calcular risk:reward
                        if signal_type == 'LONG':
                            risk = entry_price - stop_loss
                            reward = take_profit - entry_price
                        else:  # SHORT
                            risk = stop_loss - entry_price
                            reward = entry_price - take_profit

                        rr_ratio = reward / risk if risk > 0 else 0
                        logger.info(f"  Risk:Reward = 1:{rr_ratio:.2f}")

                        # Validar se o RR é razoável (entre 1:1 e 1:3)
                        if 1.0 <= rr_ratio <= 3.0:
                            logger.info(f"✅ Risk:Reward razoável")
                        else:
                            logger.warning(f"⚠️ Risk:Reward fora do esperado: {rr_ratio:.2f}")

                else:
                    logger.info(f"ℹ️ Nenhum sinal {strategy_name} gerado para {symbol}")

            except Exception as e:
                logger.error(f"❌ Erro ao testar {strategy_name} em {symbol}: {e}")

async def test_duplicate_detection():
    """Testa a detecção de sinais duplicados"""
    logger.info("\n=== TESTE DE DETECÇÃO DE DUPLICADOS ===")

    # Simular a classe SignalGenerator (apenas a parte de duplicação)
    class MockSignalGenerator:
        def __init__(self):
            self.recent_signals = {}

        def is_signal_duplicate(self, symbol, signal_type, entry_price):
            """Método copiado do main.py com as correções"""
            import datetime

            key = f"{symbol}_{signal_type}"
            current_time = datetime.datetime.now()

            if key in self.recent_signals:
                last_signal = self.recent_signals[key]
                time_diff = current_time - last_signal['timestamp']

                if entry_price <= 0:
                    logger.error(f"Preço de entrada inválido para {symbol}: {entry_price}")
                    return True

                try:
                    price_diff = abs(entry_price - last_signal['entry_price']) / last_signal['entry_price']
                except (ZeroDivisionError, TypeError):
                    logger.error(f"Erro ao calcular diferença de preço para {symbol}")
                    return True

                time_threshold = 600  # 10 minutos
                price_threshold = 0.01  # 1%

                if time_diff.total_seconds() < time_threshold and price_diff < price_threshold:
                    logger.info(f"Sinal duplicado detectado para {symbol}: tempo={time_diff.total_seconds():.0f}s, preço_diff={price_diff:.3f}")
                    return True
            return False

        def register_signal(self, symbol, signal_type, entry_price):
            import datetime
            key = f"{symbol}_{signal_type}"
            self.recent_signals[key] = {
                'timestamp': datetime.datetime.now(),
                'entry_price': entry_price
            }

    generator = MockSignalGenerator()

    # Teste 1: Primeiro sinal (não deve ser duplicado)
    is_dup1 = generator.is_signal_duplicate('BTCUSDT', 'LONG', 50000)
    logger.info(f"Primeiro sinal BTCUSDT: Duplicado = {is_dup1}")
    generator.register_signal('BTCUSDT', 'LONG', 50000)

    # Teste 2: Sinal idêntico imediatamente (deve ser duplicado)
    is_dup2 = generator.is_signal_duplicate('BTCUSDT', 'LONG', 50000)
    logger.info(f"Sinal idêntico BTCUSDT: Duplicado = {is_dup2}")

    # Teste 3: Sinal com preço ligeiramente diferente (deve ser duplicado)
    is_dup3 = generator.is_signal_duplicate('BTCUSDT', 'LONG', 50100)  # 0.2% diferença
    logger.info(f"Sinal similar BTCUSDT (0.2% diff): Duplicado = {is_dup3}")

    # Teste 4: Sinal com preço muito diferente (não deve ser duplicado)
    is_dup4 = generator.is_signal_duplicate('BTCUSDT', 'LONG', 52000)  # 4% diferença
    logger.info(f"Sinal diferente BTCUSDT (4% diff): Duplicado = {is_dup4}")

    # Teste 5: Sinal com preço inválido (deve ser considerado duplicado)
    is_dup5 = generator.is_signal_duplicate('BTCUSDT', 'LONG', -100)
    logger.info(f"Sinal inválido BTCUSDT: Duplicado = {is_dup5}")

async def main():
    """Função principal de teste"""
    logger.info("🚀 INICIANDO TESTES DE CORREÇÃO DOS SINAIS")

    try:
        await test_price_validation()
        await test_signal_formatting()
        await test_all_strategies()
        await test_duplicate_detection()

        logger.info("\n✅ TODOS OS TESTES CONCLUÍDOS!")

    except Exception as e:
        logger.error(f"❌ Erro durante os testes: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
