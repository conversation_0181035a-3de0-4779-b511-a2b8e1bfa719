import React from 'react';
import { View, Image, ToastAndroid } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import styles from './styles';
import { format } from 'date-fns';
import enUSLocale from 'date-fns/locale/en-US';
import IconLocked from '../../../components/Icon/Locked';
import { IconButton, Card as RPCard, Text, Button } from 'react-native-paper';

const Card = ({ channel, paidPremium, isLoadedNotification, notificationPermission, returnNotificationPermission }) => {
  const navigation = useNavigation();
  
  const formattedCreatedAt = format(
    new Date(channel.lastSignalAt),
    'LLL dd, h:mm aa',
    { locale: enUSLocale }
  );

  const handleShowSignals = () => {
    if (!paidPremium && channel.isPremium) {
      navigation.navigate('Premium');
      return;
    }
  
    navigation.navigate('Signals', { channelName: channel.name, channelId: channel.externalId });
  }

  const handleToggleNotification = async (toggleActive) => {
    const params = {
      channelId: channel.externalId,
      isActive: toggleActive,
    };

    returnNotificationPermission(params);

    // Feedback ao usuário
    ToastAndroid.show(
      toggleActive ? 'Notificações ativadas' : 'Notificações desativadas',
      ToastAndroid.SHORT
    );
  }

  return (
    <RPCard onPress={handleShowSignals} style={styles.container}>
      <RPCard.Content style={[styles.dFlex, styles.row]}>
        <Image style={styles.photo} source={{ uri: channel.photo }} />
        <View>
          <Text style={styles.title}>{channel.name}</Text>
          <View style={styles.typeContainer}>
            <Text style={styles.type}>{channel.type}</Text>
            <Text style={styles.dot}></Text>
            {channel.isPremium ? (
              <Text style={styles.type}>Premium</Text>
            ) : (
              <Text style={styles.type}>Free</Text>
            )}
          </View>
        </View>
        <View style={styles.icon}>
          {channel.isPremium && !paidPremium && <IconLocked />}
        </View>
      </RPCard.Content>

      {(!channel.isPremium || paidPremium) && (
        <RPCard.Actions style={styles.actions}>
          <Text style={styles.time}>{formattedCreatedAt}</Text>
          {isLoadedNotification && !notificationPermission.isActive && (
            <Button
              icon="bell-off-outline"
              color="#868686"
              onPress={(e) => {
                e.stopPropagation();
                handleToggleNotification(true);
              }}
            />
          )}
          {isLoadedNotification && notificationPermission.isActive && (
            <Button
              icon="bell-ring-outline"
              color="#A5E1BF"
              onPress={(e) => {
                e.stopPropagation();
                handleToggleNotification(false);
              }}
            />
          )}
        </RPCard.Actions>
      )}
    </RPCard>
  );
}

export default Card;

/*
Troquei o TouchableOpacity que envolvia todo o componente pelo Card do React Native Paper. 
Por consequência, os conteúdos do card foram agrupados no Card.Content e as ações em Card.Actions.
Substituí o Text padrão do React Native pelo Text do React Native Paper.
Em vez de usar TouchableOpacity com IconButton dentro, eu usei o componente Button do React Native Paper 
diretamente com a propriedade icon.
*/