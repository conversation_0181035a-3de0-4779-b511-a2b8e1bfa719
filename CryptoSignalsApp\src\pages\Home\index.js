import React, { useState, useContext, useEffect } from 'react';
import { View, Text, TouchableOpacity, FlatList, Alert, ScrollView, RefreshControl } from 'react-native';
import { Button, useTheme, Searchbar } from 'react-native-paper';
import PageTitle from '../../components/PageTitle';
import Wrapper from '../../components/Wrapper';
import Card from '../../components/Card';
import StatCard from '../../components/StatCard';
import { SkeletonCard } from '../../components/LoadingSkeleton';
import { StoreContext } from '../../store';
import styles from './styles';

const Inicio = ({ navigation, route = {} }) => {
    const { colors } = useTheme();
    const [state, _] = useContext(StoreContext);
    const [isLoading, setIsLoading] = useState(true);
    const [refreshing, setRefreshing] = useState(false);
    const [searchQuery, setSearchQuery] = useState('');

    // Market stats
    const [marketStats, setMarketStats] = useState({
        totalMarketCap: '$2.1T',
        totalVolume: '$89.2B',
        btcDominance: '52.3%',
        activeSignals: '47'
    });

    // Enhanced signals data
    const [signals, setSignals] = useState([
        {
            id: '1',
            type: 'BUY',
            pair: 'BTC/USDT',
            price: '$45,230',
            target: '$47,500',
            stopLoss: '$43,800',
            confidence: 85,
            timeframe: '4H',
            likes: 124,
            comments: 23,
            timestamp: '2 hours ago',
            channel: 'Bitcoin Pro Signals',
            status: 'active'
        },
        {
            id: '2',
            type: 'SELL',
            pair: 'ETH/USDT',
            price: '$3,185',
            target: '$3,050',
            stopLoss: '$3,280',
            confidence: 78,
            timeframe: '1H',
            likes: 89,
            comments: 15,
            timestamp: '45 minutes ago',
            channel: 'Ethereum Signals',
            status: 'active'
        },
        {
            id: '3',
            type: 'BUY',
            pair: 'SOL/USDT',
            price: '$95.40',
            target: '$102.00',
            stopLoss: '$91.20',
            confidence: 92,
            timeframe: '2H',
            likes: 156,
            comments: 31,
            timestamp: '1 hour ago',
            channel: 'Altcoin Masters',
            status: 'profit'
        },
    ]);

    // Trending pairs
    const [trendingPairs, setTrendingPairs] = useState([
        { symbol: 'BTC', price: '$45,230', change: '+2.34%', changeType: 'positive' },
        { symbol: 'ETH', price: '$3,185', change: '+1.87%', changeType: 'positive' },
        { symbol: 'SOL', price: '$95.40', change: '+5.23%', changeType: 'positive' },
        { symbol: 'ADA', price: '$0.485', change: '-0.92%', changeType: 'negative' },
    ]);

    useEffect(() => {
        // Simulate loading
        setTimeout(() => {
            setIsLoading(false);
        }, 1500);
    }, []);

    const onRefresh = () => {
        setRefreshing(true);
        // Simulate refresh
        setTimeout(() => {
            setRefreshing(false);
        }, 1000);
    };

    const handleLike = (id) => {
        const updatedSignals = signals.map((signal) => {
            if (signal.id === id) {
                return { ...signal, likes: signal.likes + 1 };
            }
            return signal;
        });
        setSignals(updatedSignals);
    };

    const handleComment = (id) => {
        Alert.alert("Comments", "Comments feature coming soon!");
    };


    if (isLoading) {
        return (
            <Wrapper>
                <PageTitle text="Dashboard" />
                <ScrollView style={{ padding: 16 }}>
                    <SkeletonCard />
                    <SkeletonCard />
                    <SkeletonCard />
                </ScrollView>
            </Wrapper>
        );
    }

    return (
        <Wrapper>
            <ScrollView
                style={{ flex: 1 }}
                refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
            >
                {/* Header */}
                <View style={{ padding: 16, paddingBottom: 8 }}>
                    <Text style={{
                        color: '#fff',
                        fontSize: 28,
                        fontFamily: 'Poppins_700Bold',
                        marginBottom: 4
                    }}>
                        Dashboard
                    </Text>
                    <Text style={{
                        color: '#8a8a8a',
                        fontSize: 14,
                        fontFamily: 'Poppins_400Regular'
                    }}>
                        Welcome back! Here's your trading overview
                    </Text>
                </View>

                {/* Search Bar */}
                <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>
                    <Searchbar
                        placeholder="Search signals, pairs, channels..."
                        onChangeText={setSearchQuery}
                        value={searchQuery}
                        style={{ backgroundColor: '#2a2a2a', elevation: 0 }}
                        inputStyle={{ color: '#fff' }}
                        iconColor="#8a8a8a"
                        placeholderTextColor="#8a8a8a"
                    />
                </View>

                {/* Market Stats */}
                <View style={{ paddingHorizontal: 8, marginBottom: 16 }}>
                    <Text style={{
                        color: '#fff',
                        fontSize: 18,
                        fontFamily: 'Poppins_600SemiBold',
                        marginBottom: 12,
                        paddingHorizontal: 8
                    }}>
                        Market Overview
                    </Text>
                    <View style={{ flexDirection: 'row', flexWrap: 'wrap' }}>
                        <StatCard
                            title="Market Cap"
                            value={marketStats.totalMarketCap}
                            change="+2.4%"
                            changeType="positive"
                            icon="📊"
                        />
                        <StatCard
                            title="24h Volume"
                            value={marketStats.totalVolume}
                            change="+8.1%"
                            changeType="positive"
                            icon="💰"
                        />
                        <StatCard
                            title="BTC Dominance"
                            value={marketStats.btcDominance}
                            change="-0.3%"
                            changeType="negative"
                            icon="₿"
                        />
                        <StatCard
                            title="Active Signals"
                            value={marketStats.activeSignals}
                            subtitle="Last 24h"
                            icon="📡"
                        />
                    </View>
                </View>

                {/* Trending Pairs */}
                <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>
                    <Text style={{
                        color: '#fff',
                        fontSize: 18,
                        fontFamily: 'Poppins_600SemiBold',
                        marginBottom: 12
                    }}>
                        Trending Now 🔥
                    </Text>
                    <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                        {trendingPairs.map((pair, index) => (
                            <Card key={index} style={{ marginRight: 12, minWidth: 120 }}>
                                <Text style={{
                                    color: '#FECB37',
                                    fontSize: 16,
                                    fontFamily: 'Poppins_600SemiBold',
                                    marginBottom: 4
                                }}>
                                    {pair.symbol}
                                </Text>
                                <Text style={{
                                    color: '#fff',
                                    fontSize: 14,
                                    fontFamily: 'Poppins_500Medium',
                                    marginBottom: 2
                                }}>
                                    {pair.price}
                                </Text>
                                <Text style={{
                                    color: pair.changeType === 'positive' ? '#4CAF50' : '#F44336',
                                    fontSize: 12,
                                    fontFamily: 'Poppins_500Medium'
                                }}>
                                    {pair.change}
                                </Text>
                            </Card>
                        ))}
                    </ScrollView>
                </View>

                {/* Latest Signals */}
                <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>
                    <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 12 }}>
                        <Text style={{
                            color: '#fff',
                            fontSize: 18,
                            fontFamily: 'Poppins_600SemiBold'
                        }}>
                            Latest Signals ⚡
                        </Text>
                        <TouchableOpacity onPress={() => navigation?.navigate('Channels')}>
                            <Text style={{
                                color: '#FECB37',
                                fontSize: 14,
                                fontFamily: 'Poppins_500Medium'
                            }}>
                                View All
                            </Text>
                        </TouchableOpacity>
                    </View>

                    {signals.map((signal) => (
                        <Card key={signal.id} style={{ marginBottom: 12 }}>
                            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: 8 }}>
                                <View style={{ flex: 1 }}>
                                    <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 4 }}>
                                        <View style={{
                                            backgroundColor: signal.type === 'BUY' ? '#4CAF50' : '#F44336',
                                            paddingHorizontal: 8,
                                            paddingVertical: 2,
                                            borderRadius: 4,
                                            marginRight: 8
                                        }}>
                                            <Text style={{
                                                color: '#fff',
                                                fontSize: 10,
                                                fontFamily: 'Poppins_600SemiBold'
                                            }}>
                                                {signal.type}
                                            </Text>
                                        </View>
                                        <Text style={{
                                            color: '#FECB37',
                                            fontSize: 16,
                                            fontFamily: 'Poppins_600SemiBold'
                                        }}>
                                            {signal.pair}
                                        </Text>
                                        <View style={{
                                            backgroundColor: signal.status === 'profit' ? '#4CAF50' : '#FECB37',
                                            paddingHorizontal: 6,
                                            paddingVertical: 2,
                                            borderRadius: 10,
                                            marginLeft: 8
                                        }}>
                                            <Text style={{
                                                color: signal.status === 'profit' ? '#fff' : '#000',
                                                fontSize: 8,
                                                fontFamily: 'Poppins_500Medium'
                                            }}>
                                                {signal.status.toUpperCase()}
                                            </Text>
                                        </View>
                                    </View>
                                    <Text style={{
                                        color: '#8a8a8a',
                                        fontSize: 12,
                                        fontFamily: 'Poppins_400Regular',
                                        marginBottom: 8
                                    }}>
                                        {signal.channel} • {signal.timestamp}
                                    </Text>
                                </View>
                                <View style={{ alignItems: 'flex-end' }}>
                                    <Text style={{
                                        color: '#fff',
                                        fontSize: 14,
                                        fontFamily: 'Poppins_600SemiBold'
                                    }}>
                                        {signal.price}
                                    </Text>
                                    <Text style={{
                                        color: '#8a8a8a',
                                        fontSize: 10,
                                        fontFamily: 'Poppins_400Regular'
                                    }}>
                                        {signal.timeframe}
                                    </Text>
                                </View>
                            </View>

                            <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 12 }}>
                                <View style={{ flex: 1, marginRight: 8 }}>
                                    <Text style={{ color: '#8a8a8a', fontSize: 10, fontFamily: 'Poppins_400Regular' }}>Target</Text>
                                    <Text style={{ color: '#4CAF50', fontSize: 12, fontFamily: 'Poppins_500Medium' }}>{signal.target}</Text>
                                </View>
                                <View style={{ flex: 1, marginRight: 8 }}>
                                    <Text style={{ color: '#8a8a8a', fontSize: 10, fontFamily: 'Poppins_400Regular' }}>Stop Loss</Text>
                                    <Text style={{ color: '#F44336', fontSize: 12, fontFamily: 'Poppins_500Medium' }}>{signal.stopLoss}</Text>
                                </View>
                                <View style={{ flex: 1 }}>
                                    <Text style={{ color: '#8a8a8a', fontSize: 10, fontFamily: 'Poppins_400Regular' }}>Confidence</Text>
                                    <Text style={{ color: '#FECB37', fontSize: 12, fontFamily: 'Poppins_500Medium' }}>{signal.confidence}%</Text>
                                </View>
                            </View>

                            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
                                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                    <TouchableOpacity
                                        onPress={() => handleLike(signal.id)}
                                        style={{ flexDirection: 'row', alignItems: 'center', marginRight: 16 }}
                                    >
                                        <Text style={{ color: '#FECB37', marginRight: 4, fontSize: 14 }}>👍</Text>
                                        <Text style={{ color: '#8a8a8a', fontSize: 12, fontFamily: 'Poppins_400Regular' }}>{signal.likes}</Text>
                                    </TouchableOpacity>
                                    <TouchableOpacity
                                        onPress={() => handleComment(signal.id)}
                                        style={{ flexDirection: 'row', alignItems: 'center' }}
                                    >
                                        <Text style={{ color: '#FECB37', marginRight: 4, fontSize: 14 }}>💬</Text>
                                        <Text style={{ color: '#8a8a8a', fontSize: 12, fontFamily: 'Poppins_400Regular' }}>{signal.comments}</Text>
                                    </TouchableOpacity>
                                </View>
                                <TouchableOpacity>
                                    <Text style={{ color: '#FECB37', fontSize: 12, fontFamily: 'Poppins_500Medium' }}>View Details</Text>
                                </TouchableOpacity>
                            </View>
                        </Card>
                    ))}
                </View>

                {/* Quick Actions */}
                <View style={{ paddingHorizontal: 16, marginBottom: 20 }}>
                    <Text style={{
                        color: '#fff',
                        fontSize: 18,
                        fontFamily: 'Poppins_600SemiBold',
                        marginBottom: 12
                    }}>
                        Quick Actions
                    </Text>
                    <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                        <Button
                            mode="contained"
                            onPress={() => navigation?.navigate('Channels')}
                            style={{ flex: 1, marginRight: 8, backgroundColor: '#FECB37' }}
                            labelStyle={{ color: '#000', fontFamily: 'Poppins_500Medium' }}
                        >
                            View Channels
                        </Button>
                        <Button
                            mode="outlined"
                            onPress={() => navigation?.navigate('Premium')}
                            style={{ flex: 1, marginLeft: 8, borderColor: '#FECB37' }}
                            labelStyle={{ color: '#FECB37', fontFamily: 'Poppins_500Medium' }}
                        >
                            Go Premium
                        </Button>
                    </View>
                </View>
            </ScrollView>
        </Wrapper>
    );
};

export default Inicio;
