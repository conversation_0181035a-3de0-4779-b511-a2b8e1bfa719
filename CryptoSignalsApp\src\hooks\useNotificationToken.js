import { useContext, useEffect, useState } from "react";
import { getSecureStoreItem, setSecureStoreItem } from '../services/secureStore';
import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import { Platform } from 'react-native';
import { StoreContext } from "../store";
import { AxiosContext } from "../store/axios";

Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: false,
    shouldSetBadge: false,
  }),
});

export const useNotificationToken = () => {
  const [state, _] = useContext(StoreContext);
  const { subscription: { subscriptionStatus } } = state;
  const [api] = useContext(AxiosContext);
  const [token, setToken] = useState(null);

  const registerForPushNotificationsAsync = async () => {
    let expoToken = null;

    if (Device.isDevice) {
      const { status: existingStatus } = await Notifications.getPermissionsAsync();

      let finalStatus = existingStatus;
      
      if (existingStatus !== "granted") {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== "granted") {
        alert("Failed to get push token for push notification!");
        return;
      }

        expoToken = (await Notifications.getExpoPushTokenAsync()).data;
    } else {
      alert("Must use physical device for Push Notifications");
    }

    if (Platform.OS === "android") {
      Notifications.setNotificationChannelAsync("default", {
        name: "default",
        importance: Notifications.AndroidImportance.MAX,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: "#FF231F7C",
      });
    }

    return expoToken;
  }

  const sendToken = async (token) => {
    try {
      await api.post('/push-notifications/token', { data: token, isPremium: subscriptionStatus });
    } catch(e) {
      console.error(`Error send token ${token}`, e);
    }
  }

  const getToken = async () => {
    let pushNotificationToken = await getSecureStoreItem('CRYPTOSIGNALS.pushNotification');

    if (!pushNotificationToken) {
      const expoToken = await registerForPushNotificationsAsync();

      if (!expoToken) {
        console.error('token expo não gerado!')
        return;
      }

      await sendToken(expoToken);

      await setSecureStoreItem("CRYPTOSIGNALS.pushNotification", { token: expoToken });

      pushNotificationToken = { token: expoToken }
    }

    setToken(pushNotificationToken.token);
  }

  useEffect(() => {
    getToken();
  }, []);

  return token;
}

export default useNotificationToken;

