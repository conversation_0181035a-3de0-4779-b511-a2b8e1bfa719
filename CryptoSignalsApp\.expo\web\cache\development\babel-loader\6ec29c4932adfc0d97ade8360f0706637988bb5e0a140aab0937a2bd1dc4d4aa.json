{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport React, { useState, useEffect } from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport FlatList from \"react-native-web/dist/exports/FlatList\";\nimport Image from \"react-native-web/dist/exports/Image\";\nimport styles from \"./styles\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar Rank = function Rank() {\n  var _useState = useState([]),\n    _useState2 = _slicedToArray(_useState, 2),\n    rankData = _useState2[0],\n    setRankData = _useState2[1];\n  useEffect(function () {\n    var fetchData = function () {\n      var _ref = _asyncToGenerator(function* () {\n        try {\n          var response = yield fetch('https://api.example.com/rank');\n          var data = yield response.json();\n          setRankData(data);\n        } catch (error) {\n          console.error('Erro ao obter dados de classificação:', error);\n        }\n      });\n      return function fetchData() {\n        return _ref.apply(this, arguments);\n      };\n    }();\n    fetchData();\n  }, []);\n  var renderRankItem = function renderRankItem(_ref2) {\n    var item = _ref2.item,\n      index = _ref2.index;\n    return _jsxs(View, {\n      style: styles.rankItem,\n      children: [_jsx(Text, {\n        style: styles.rank,\n        children: index + 1\n      }), _jsx(Image, {\n        source: {\n          uri: item.profilePicture\n        },\n        style: styles.profilePicture\n      }), _jsxs(View, {\n        style: styles.userInfo,\n        children: [_jsx(Text, {\n          style: styles.username,\n          children: item.username\n        }), _jsx(Text, {\n          style: styles.badgeTitle,\n          children: item.badgeTitle\n        })]\n      }), _jsxs(Text, {\n        style: styles.points,\n        children: [item.points, \" pontos\"]\n      })]\n    });\n  };\n  return _jsxs(View, {\n    style: styles.container,\n    children: [_jsx(Text, {\n      style: styles.title,\n      children: \"Classifica\\xE7\\xE3o dos 50 Melhores\"\n    }), _jsx(FlatList, {\n      data: rankData,\n      keyExtractor: function keyExtractor(item, index) {\n        return index.toString();\n      },\n      renderItem: renderRankItem\n    })]\n  });\n};\nexport default Rank;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "View", "Text", "FlatList", "Image", "styles", "jsx", "_jsx", "jsxs", "_jsxs", "Rank", "_useState", "_useState2", "_slicedToArray", "rankData", "setRankData", "fetchData", "_ref", "_asyncToGenerator", "response", "fetch", "data", "json", "error", "console", "apply", "arguments", "renderRankItem", "_ref2", "item", "index", "style", "rankItem", "children", "rank", "source", "uri", "profilePicture", "userInfo", "username", "badgeTitle", "points", "container", "title", "keyExtractor", "toString", "renderItem"], "sources": ["E:/CryptoSignalsApp/src/pages/Rank/index.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { View, Text, FlatList, Image } from 'react-native';\r\nimport styles from './styles';\r\n\r\nconst Rank = () => {\r\n  const [rankData, setRankData] = useState([]);\r\n\r\n  useEffect(() => {\r\n    // Simule a obtenção dos dados de classificação dos usuários\r\n    const fetchData = async () => {\r\n      try {\r\n        // Faça uma chamada à API ou acesse seu banco de dados para obter os dados de classificação\r\n        // Aqui, estou usando dados fictícios para ilustração\r\n        const response = await fetch('https://api.example.com/rank');\r\n        const data = await response.json();\r\n        setRankData(data);\r\n      } catch (error) {\r\n        console.error('Erro ao obter dados de classificação:', error);\r\n      }\r\n    };\r\n\r\n    fetchData();\r\n  }, []);\r\n\r\n  const renderRankItem = ({ item, index }) => (\r\n    <View style={styles.rankItem}>\r\n      <Text style={styles.rank}>{index + 1}</Text>\r\n      <Image source={{ uri: item.profilePicture }} style={styles.profilePicture} />\r\n      <View style={styles.userInfo}>\r\n        <Text style={styles.username}>{item.username}</Text>\r\n        <Text style={styles.badgeTitle}>{item.badgeTitle}</Text>\r\n      </View>\r\n      <Text style={styles.points}>{item.points} pontos</Text>\r\n    </View>\r\n  );\r\n\r\n  return (\r\n    <View style={styles.container}>\r\n      <Text style={styles.title}>Classificação dos 50 Melhores</Text>\r\n      <FlatList\r\n        data={rankData}\r\n        keyExtractor={(item, index) => index.toString()}\r\n        renderItem={renderRankItem}\r\n      />\r\n    </View>\r\n  );\r\n};\r\n\r\nexport default Rank;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,QAAA;AAAA,OAAAC,KAAA;AAEnD,OAAOC,MAAM;AAAiB,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAE9B,IAAMC,IAAI,GAAG,SAAPA,IAAIA,CAAA,EAAS;EACjB,IAAAC,SAAA,GAAgCZ,QAAQ,CAAC,EAAE,CAAC;IAAAa,UAAA,GAAAC,cAAA,CAAAF,SAAA;IAArCG,QAAQ,GAAAF,UAAA;IAAEG,WAAW,GAAAH,UAAA;EAE5BZ,SAAS,CAAC,YAAM;IAEd,IAAMgB,SAAS;MAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,aAAY;QAC5B,IAAI;UAGF,IAAMC,QAAQ,SAASC,KAAK,CAAC,8BAA8B,CAAC;UAC5D,IAAMC,IAAI,SAASF,QAAQ,CAACG,IAAI,CAAC,CAAC;UAClCP,WAAW,CAACM,IAAI,CAAC;QACnB,CAAC,CAAC,OAAOE,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QAC/D;MACF,CAAC;MAAA,gBAVKP,SAASA,CAAA;QAAA,OAAAC,IAAA,CAAAQ,KAAA,OAAAC,SAAA;MAAA;IAAA,GAUd;IAEDV,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,IAAMW,cAAc,GAAG,SAAjBA,cAAcA,CAAAC,KAAA;IAAA,IAAMC,IAAI,GAAAD,KAAA,CAAJC,IAAI;MAAEC,KAAK,GAAAF,KAAA,CAALE,KAAK;IAAA,OACnCrB,KAAA,CAACR,IAAI;MAAC8B,KAAK,EAAE1B,MAAM,CAAC2B,QAAS;MAAAC,QAAA,GAC3B1B,IAAA,CAACL,IAAI;QAAC6B,KAAK,EAAE1B,MAAM,CAAC6B,IAAK;QAAAD,QAAA,EAAEH,KAAK,GAAG;MAAC,CAAO,CAAC,EAC5CvB,IAAA,CAACH,KAAK;QAAC+B,MAAM,EAAE;UAAEC,GAAG,EAAEP,IAAI,CAACQ;QAAe,CAAE;QAACN,KAAK,EAAE1B,MAAM,CAACgC;MAAe,CAAE,CAAC,EAC7E5B,KAAA,CAACR,IAAI;QAAC8B,KAAK,EAAE1B,MAAM,CAACiC,QAAS;QAAAL,QAAA,GAC3B1B,IAAA,CAACL,IAAI;UAAC6B,KAAK,EAAE1B,MAAM,CAACkC,QAAS;UAAAN,QAAA,EAAEJ,IAAI,CAACU;QAAQ,CAAO,CAAC,EACpDhC,IAAA,CAACL,IAAI;UAAC6B,KAAK,EAAE1B,MAAM,CAACmC,UAAW;UAAAP,QAAA,EAAEJ,IAAI,CAACW;QAAU,CAAO,CAAC;MAAA,CACpD,CAAC,EACP/B,KAAA,CAACP,IAAI;QAAC6B,KAAK,EAAE1B,MAAM,CAACoC,MAAO;QAAAR,QAAA,GAAEJ,IAAI,CAACY,MAAM,EAAC,SAAO;MAAA,CAAM,CAAC;IAAA,CACnD,CAAC;EAAA,CACR;EAED,OACEhC,KAAA,CAACR,IAAI;IAAC8B,KAAK,EAAE1B,MAAM,CAACqC,SAAU;IAAAT,QAAA,GAC5B1B,IAAA,CAACL,IAAI;MAAC6B,KAAK,EAAE1B,MAAM,CAACsC,KAAM;MAAAV,QAAA,EAAC;IAA6B,CAAM,CAAC,EAC/D1B,IAAA,CAACJ,QAAQ;MACPkB,IAAI,EAAEP,QAAS;MACf8B,YAAY,EAAE,SAAdA,YAAYA,CAAGf,IAAI,EAAEC,KAAK;QAAA,OAAKA,KAAK,CAACe,QAAQ,CAAC,CAAC;MAAA,CAAC;MAChDC,UAAU,EAAEnB;IAAe,CAC5B,CAAC;EAAA,CACE,CAAC;AAEX,CAAC;AAED,eAAejB,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}