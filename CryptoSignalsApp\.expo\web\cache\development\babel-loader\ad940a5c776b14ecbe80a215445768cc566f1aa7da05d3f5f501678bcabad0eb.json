{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport React, { useState, useEffect } from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport Image from \"react-native-web/dist/exports/Image\";\nimport Switch from \"react-native-web/dist/exports/Switch\";\nimport ScrollView from \"react-native-web/dist/exports/ScrollView\";\nimport { AntDesign } from '@expo/vector-icons';\nimport styles from \"./styles\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar Profile = function Profile() {\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    isPublic = _useState2[0],\n    setIsPublic = _useState2[1];\n  var _useState3 = useState({\n      username: '<PERSON><PERSON><PERSON>',\n      fullName: '<PERSON>',\n      bio: 'Crypto enthusiast',\n      profilePicture: 'https://example.com/profile.jpg'\n    }),\n    _useState4 = _slicedToArray(_useState3, 2),\n    userData = _useState4[0],\n    setUserData = _useState4[1];\n  var togglePrivacy = function togglePrivacy() {\n    setIsPublic(function (prevState) {\n      return !prevState;\n    });\n  };\n  useEffect(function () {\n    setTimeout(function () {\n      setUserData({\n        username: 'JohnDoe',\n        fullName: 'John Doe',\n        bio: 'Crypto enthusiast',\n        profilePicture: 'https://example.com/profile.jpg'\n      });\n    }, 2000);\n  }, []);\n  return _jsxs(ScrollView, {\n    style: styles.container,\n    children: [_jsxs(View, {\n      style: styles.profileHeader,\n      children: [_jsx(Image, {\n        source: {\n          uri: userData.profilePicture\n        },\n        style: styles.profilePicture\n      }), _jsx(Text, {\n        style: styles.username,\n        children: userData.username\n      }), _jsx(Text, {\n        style: styles.fullName,\n        children: userData.fullName\n      }), _jsx(Text, {\n        style: styles.bio,\n        children: userData.bio\n      }), _jsxs(View, {\n        style: styles.privacySwitch,\n        children: [_jsxs(Text, {\n          style: styles.privacyLabel,\n          children: [\"Perfil \", isPublic ? 'Público' : 'Privado']\n        }), _jsx(Switch, {\n          value: isPublic,\n          onValueChange: togglePrivacy,\n          thumbColor: isPublic ? '#28a745' : '#dc3545'\n        })]\n      })]\n    }), _jsx(View, {\n      style: styles.metricsSection,\n      children: _jsx(Text, {\n        style: styles.sectionTitle,\n        children: \"M\\xE9tricas de Desempenho\"\n      })\n    }), _jsx(View, {\n      style: styles.badgesSection,\n      children: _jsx(Text, {\n        style: styles.sectionTitle,\n        children: \"Distintivos\"\n      })\n    })]\n  });\n};\nexport default Profile;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "View", "Text", "Image", "Switch", "ScrollView", "AntDesign", "styles", "jsx", "_jsx", "jsxs", "_jsxs", "Profile", "_useState", "_useState2", "_slicedToArray", "isPublic", "setIsPublic", "_useState3", "username", "fullName", "bio", "profilePicture", "_useState4", "userData", "setUserData", "togglePrivacy", "prevState", "setTimeout", "style", "container", "children", "<PERSON><PERSON><PERSON><PERSON>", "source", "uri", "privacySwitch", "privacyLabel", "value", "onValueChange", "thumbColor", "metricsSection", "sectionTitle", "badgesSection"], "sources": ["E:/CryptoSignalsApp/src/pages/Rank/styles.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { View, Text, Image, Switch, ScrollView } from 'react-native';\r\nimport { AntDesign } from '@expo/vector-icons';\r\nimport styles from './styles';\r\n\r\nconst Profile = () => {\r\n  const [isPublic, setIsPublic] = useState(false);\r\n  const [userData, setUserData] = useState({\r\n    username: '<PERSON><PERSON><PERSON>',\r\n    fullName: '<PERSON>',\r\n    bio: 'Crypto enthusiast',\r\n    profilePicture: 'https://example.com/profile.jpg',\r\n  });\r\n\r\n  const togglePrivacy = () => {\r\n    setIsPublic((prevState) => !prevState);\r\n  };\r\n\r\n  useEffect(() => {\r\n    // Simulando a obtenção de dados do usuário\r\n    setTimeout(() => {\r\n      setUserData({\r\n        username: '<PERSON><PERSON><PERSON>',\r\n        fullName: '<PERSON>',\r\n        bio: 'Crypto enthusiast',\r\n        profilePicture: 'https://example.com/profile.jpg',\r\n      });\r\n    }, 2000);\r\n  }, []);\r\n\r\n  return (\r\n    <ScrollView style={styles.container}>\r\n      <View style={styles.profileHeader}>\r\n        <Image source={{ uri: userData.profilePicture }} style={styles.profilePicture} />\r\n        <Text style={styles.username}>{userData.username}</Text>\r\n        <Text style={styles.fullName}>{userData.fullName}</Text>\r\n        <Text style={styles.bio}>{userData.bio}</Text>\r\n        <View style={styles.privacySwitch}>\r\n          <Text style={styles.privacyLabel}>Perfil {isPublic ? 'Público' : 'Privado'}</Text>\r\n          <Switch\r\n            value={isPublic}\r\n            onValueChange={togglePrivacy}\r\n            thumbColor={isPublic ? '#28a745' : '#dc3545'}\r\n          />\r\n        </View>\r\n      </View>\r\n\r\n      <View style={styles.metricsSection}>\r\n        <Text style={styles.sectionTitle}>Métricas de Desempenho</Text>\r\n        {/* Exiba as métricas de desempenho aqui */}\r\n      </View>\r\n\r\n      <View style={styles.badgesSection}>\r\n        <Text style={styles.sectionTitle}>Distintivos</Text>\r\n        {/* Exiba os distintivos aqui */}\r\n      </View>\r\n    </ScrollView>\r\n  );\r\n};\r\n\r\nexport default Profile;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,KAAA;AAAA,OAAAC,MAAA;AAAA,OAAAC,UAAA;AAEnD,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,OAAOC,MAAM;AAAiB,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAE9B,IAAMC,OAAO,GAAG,SAAVA,OAAOA,CAAA,EAAS;EACpB,IAAAC,SAAA,GAAgCd,QAAQ,CAAC,KAAK,CAAC;IAAAe,UAAA,GAAAC,cAAA,CAAAF,SAAA;IAAxCG,QAAQ,GAAAF,UAAA;IAAEG,WAAW,GAAAH,UAAA;EAC5B,IAAAI,UAAA,GAAgCnB,QAAQ,CAAC;MACvCoB,QAAQ,EAAE,SAAS;MACnBC,QAAQ,EAAE,UAAU;MACpBC,GAAG,EAAE,mBAAmB;MACxBC,cAAc,EAAE;IAClB,CAAC,CAAC;IAAAC,UAAA,GAAAR,cAAA,CAAAG,UAAA;IALKM,QAAQ,GAAAD,UAAA;IAAEE,WAAW,GAAAF,UAAA;EAO5B,IAAMG,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;IAC1BT,WAAW,CAAC,UAACU,SAAS;MAAA,OAAK,CAACA,SAAS;IAAA,EAAC;EACxC,CAAC;EAED3B,SAAS,CAAC,YAAM;IAEd4B,UAAU,CAAC,YAAM;MACfH,WAAW,CAAC;QACVN,QAAQ,EAAE,SAAS;QACnBC,QAAQ,EAAE,UAAU;QACpBC,GAAG,EAAE,mBAAmB;QACxBC,cAAc,EAAE;MAClB,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,EAAE,EAAE,CAAC;EAEN,OACEX,KAAA,CAACN,UAAU;IAACwB,KAAK,EAAEtB,MAAM,CAACuB,SAAU;IAAAC,QAAA,GAClCpB,KAAA,CAACV,IAAI;MAAC4B,KAAK,EAAEtB,MAAM,CAACyB,aAAc;MAAAD,QAAA,GAChCtB,IAAA,CAACN,KAAK;QAAC8B,MAAM,EAAE;UAAEC,GAAG,EAAEV,QAAQ,CAACF;QAAe,CAAE;QAACO,KAAK,EAAEtB,MAAM,CAACe;MAAe,CAAE,CAAC,EACjFb,IAAA,CAACP,IAAI;QAAC2B,KAAK,EAAEtB,MAAM,CAACY,QAAS;QAAAY,QAAA,EAAEP,QAAQ,CAACL;MAAQ,CAAO,CAAC,EACxDV,IAAA,CAACP,IAAI;QAAC2B,KAAK,EAAEtB,MAAM,CAACa,QAAS;QAAAW,QAAA,EAAEP,QAAQ,CAACJ;MAAQ,CAAO,CAAC,EACxDX,IAAA,CAACP,IAAI;QAAC2B,KAAK,EAAEtB,MAAM,CAACc,GAAI;QAAAU,QAAA,EAAEP,QAAQ,CAACH;MAAG,CAAO,CAAC,EAC9CV,KAAA,CAACV,IAAI;QAAC4B,KAAK,EAAEtB,MAAM,CAAC4B,aAAc;QAAAJ,QAAA,GAChCpB,KAAA,CAACT,IAAI;UAAC2B,KAAK,EAAEtB,MAAM,CAAC6B,YAAa;UAAAL,QAAA,GAAC,SAAO,EAACf,QAAQ,GAAG,SAAS,GAAG,SAAS;QAAA,CAAO,CAAC,EAClFP,IAAA,CAACL,MAAM;UACLiC,KAAK,EAAErB,QAAS;UAChBsB,aAAa,EAAEZ,aAAc;UAC7Ba,UAAU,EAAEvB,QAAQ,GAAG,SAAS,GAAG;QAAU,CAC9C,CAAC;MAAA,CACE,CAAC;IAAA,CACH,CAAC,EAEPP,IAAA,CAACR,IAAI;MAAC4B,KAAK,EAAEtB,MAAM,CAACiC,cAAe;MAAAT,QAAA,EACjCtB,IAAA,CAACP,IAAI;QAAC2B,KAAK,EAAEtB,MAAM,CAACkC,YAAa;QAAAV,QAAA,EAAC;MAAsB,CAAM;IAAC,CAE3D,CAAC,EAEPtB,IAAA,CAACR,IAAI;MAAC4B,KAAK,EAAEtB,MAAM,CAACmC,aAAc;MAAAX,QAAA,EAChCtB,IAAA,CAACP,IAAI;QAAC2B,KAAK,EAAEtB,MAAM,CAACkC,YAAa;QAAAV,QAAA,EAAC;MAAW,CAAM;IAAC,CAEhD,CAAC;EAAA,CACG,CAAC;AAEjB,CAAC;AAED,eAAenB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}