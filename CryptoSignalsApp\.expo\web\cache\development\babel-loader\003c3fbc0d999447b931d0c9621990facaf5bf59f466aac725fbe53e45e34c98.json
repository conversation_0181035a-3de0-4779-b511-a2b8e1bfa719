{"ast": null, "code": "import View from \"react-native-web/dist/exports/View\";\nvar SearchBar = View;\nexport default SearchBar;", "map": {"version": 3, "names": ["SearchBar", "View"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\react-native-screens\\src\\components\\SearchBar.web.tsx"], "sourcesContent": ["import { View } from 'react-native';\n\nconst SearchBar = View;\n\nexport default SearchBar;\n"], "mappings": ";AAEA,IAAMA,SAAS,GAAGC,IAAI;AAEtB,eAAeD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}