import datetime
import logging

logger = logging.getLogger(__name__)

class SignalFormatter:
    """Professional signal formatter for elite traders"""

    @staticmethod
    def format_scalp_signal(symbol, entry_price, take_profits, signal_type, leverage=20):
        """
        Formats a professional scalping signal for elite traders
        """
        try:
            entry_price = float(entry_price)
            if entry_price <= 0:
                raise ValueError(f"Invalid entry price: {entry_price}")
        except (ValueError, TypeError) as e:
            logger.error(f"Error converting entry price for {symbol}: {e}")
            return None

        # Determine decimal places based on price
        decimal_places = 2 if entry_price >= 1 else 6

        # Calculate signal expiry
        current_time = datetime.datetime.now()
        expiry_time = current_time + datetime.timedelta(hours=4)

        # Professional header
        direction = "LONG" if signal_type == "LONG" else "SHORT"

        message = (
            f"CRYPTOSIGNALS PROFESSIONAL\n"
            f"{'=' * 35}\n\n"
            f"ASSET: {symbol}\n"
            f"STRATEGY: SCALP\n"
            f"DIRECTION: {direction}\n"
            f"LEVERAGE: {leverage}x\n"
            f"TIMEFRAME: 1-4H\n\n"
            f"ENTRY ZONE: {entry_price:.{decimal_places}f}\n\n"
            f"TAKE PROFIT LEVELS:\n"
        )

        # Process take profit levels
        tp_count = 1
        for level, price in sorted(take_profits.items()):
            try:
                price = float(price)
                if price <= 0:
                    continue

                # Validate take profit logic
                if signal_type == "LONG" and price <= entry_price:
                    continue
                elif signal_type == "SHORT" and price >= entry_price:
                    continue

                message += f"TP{tp_count}: {price:.{decimal_places}f}\n"
                tp_count += 1

            except (ValueError, TypeError):
                continue

        # Calculate stop loss
        stop_loss_percentage = 0.025  # 2.5%
        if signal_type == "LONG":
            stop_loss_price = entry_price * (1 - stop_loss_percentage)
        else:
            stop_loss_price = entry_price * (1 + stop_loss_percentage)

        # Professional footer
        expiry_str = expiry_time.strftime("%H:%M UTC")

        message += (
            f"\nSTOP LOSS: {stop_loss_price:.{decimal_places}f}\n\n"
            f"RISK MANAGEMENT:\n"
            f"- Position size: 1-2% of portfolio\n"
            f"- Risk/Reward: 1:2 minimum\n"
            f"- Max holding time: 4 hours\n\n"
            f"SIGNAL EXPIRES: {expiry_str}\n"
            f"{'=' * 35}\n"
            f"CRYPTOSIGNALS PROFESSIONAL"
        )

        return message

    @staticmethod
    def format_breakout_signal(symbol, entry_price, stop_loss, take_profit, leverage=10, signal_type="LONG"):
        """
        Formats a professional volume breakout signal
        """
        try:
            entry_price = float(entry_price)
            stop_loss = float(stop_loss)
            take_profit = float(take_profit)
        except (ValueError, TypeError) as e:
            logger.error(f"Error converting prices for {symbol}: {e}")
            return None

        # Determine decimal places
        decimal_places = 2 if entry_price >= 1 else 6

        # Calculate signal expiry
        current_time = datetime.datetime.now()
        expiry_time = current_time + datetime.timedelta(hours=6)
        expiry_str = expiry_time.strftime("%H:%M UTC")

        # Calculate risk/reward levels
        if signal_type == "LONG":
            risk = entry_price - stop_loss
            tp1 = entry_price + (risk * 1.5)
            tp2 = entry_price + (risk * 2.5)
            tp3 = take_profit
        else:
            risk = stop_loss - entry_price
            tp1 = entry_price - (risk * 1.5)
            tp2 = entry_price - (risk * 2.5)
            tp3 = take_profit

        direction = "LONG" if signal_type == "LONG" else "SHORT"

        message = (
            f"CRYPTOSIGNALS PROFESSIONAL\n"
            f"{'=' * 35}\n\n"
            f"ASSET: {symbol}\n"
            f"STRATEGY: VOLUME BREAKOUT\n"
            f"DIRECTION: {direction}\n"
            f"LEVERAGE: {leverage}x\n"
            f"TIMEFRAME: 15M-1H\n\n"
            f"ENTRY ZONE: {entry_price:.{decimal_places}f}\n"
            f"STOP LOSS: {stop_loss:.{decimal_places}f}\n\n"
            f"TAKE PROFIT LEVELS:\n"
            f"TP1: {tp1:.{decimal_places}f}\n"
            f"TP2: {tp2:.{decimal_places}f}\n"
            f"TP3: {tp3:.{decimal_places}f}\n\n"
            f"EXECUTION NOTES:\n"
            f"- Wait for volume confirmation\n"
            f"- Enter on breakout candle close\n"
            f"- Monitor for false breakouts\n\n"
            f"SIGNAL EXPIRES: {expiry_str}\n"
            f"{'=' * 35}\n"
            f"CRYPTOSIGNALS PROFESSIONAL"
        )

        return message

    @staticmethod
    def format_inside_bar_signal(symbol, entry_price, stop_loss, take_profit, leverage=10, signal_type="LONG"):
        """Formats a professional inside bar signal"""
        return SignalFormatter._format_professional_signal(
            symbol, entry_price, stop_loss, take_profit, leverage, signal_type,
            "INSIDE BAR", "1H-4H", 8
        )

    @staticmethod
    def format_mfi_signal(symbol, entry_price, stop_loss, take_profit, leverage=10):
        """Formats a professional MFI signal"""
        return SignalFormatter._format_professional_signal(
            symbol, entry_price, stop_loss, take_profit, leverage, "LONG",
            "MFI REVERSAL", "15M-1H", 12
        )

    @staticmethod
    def format_swing_signal(symbol, entry_price, stop_loss, take_profit, leverage=10, signal_type="LONG"):
        """Formats a professional swing signal"""
        return SignalFormatter._format_professional_signal(
            symbol, entry_price, stop_loss, take_profit, leverage, signal_type,
            "SWING TRADE", "4H-1D", 48
        )

    @staticmethod
    def _format_professional_signal(symbol, entry_price, stop_loss, take_profit, leverage, signal_type, strategy, timeframe, expiry_hours):
        """
        Universal professional signal formatter
        """
        try:
            entry_price = float(entry_price)
            stop_loss = float(stop_loss)
            take_profit = float(take_profit)
        except (ValueError, TypeError) as e:
            logger.error(f"Error converting prices for {symbol}: {e}")
            return None

        # Determine decimal places
        decimal_places = 2 if entry_price >= 1 else 6

        # Calculate signal expiry
        current_time = datetime.datetime.now()
        expiry_time = current_time + datetime.timedelta(hours=expiry_hours)
        expiry_str = expiry_time.strftime("%H:%M UTC")

        # Calculate risk/reward levels
        if signal_type == "LONG":
            risk = entry_price - stop_loss
            tp1 = entry_price + (risk * 1.5)
            tp2 = entry_price + (risk * 2.5)
            tp3 = take_profit
        else:
            risk = stop_loss - entry_price
            tp1 = entry_price - (risk * 1.5)
            tp2 = entry_price - (risk * 2.5)
            tp3 = take_profit

        direction = "LONG" if signal_type == "LONG" else "SHORT"

        message = (
            f"CRYPTOSIGNALS PROFESSIONAL\n"
            f"{'=' * 35}\n\n"
            f"ASSET: {symbol}\n"
            f"STRATEGY: {strategy}\n"
            f"DIRECTION: {direction}\n"
            f"LEVERAGE: {leverage}x\n"
            f"TIMEFRAME: {timeframe}\n\n"
            f"ENTRY ZONE: {entry_price:.{decimal_places}f}\n"
            f"STOP LOSS: {stop_loss:.{decimal_places}f}\n\n"
            f"TAKE PROFIT LEVELS:\n"
            f"TP1: {tp1:.{decimal_places}f}\n"
            f"TP2: {tp2:.{decimal_places}f}\n"
            f"TP3: {tp3:.{decimal_places}f}\n\n"
            f"RISK MANAGEMENT:\n"
            f"- Position size: 1-2% of portfolio\n"
            f"- Risk/Reward: 1:2.5 minimum\n"
            f"- Strict stop loss adherence\n\n"
            f"SIGNAL EXPIRES: {expiry_str}\n"
            f"{'=' * 35}\n"
            f"CRYPTOSIGNALS PROFESSIONAL"
        )

        return message

    # Professional methods for new strategies
    @staticmethod
    def format_multi_source_signal(symbol, entry_price, take_profits, signal_type, leverage=20):
        """Formats a professional multi-source signal"""
        return SignalFormatter.format_scalp_signal(symbol, entry_price, take_profits, signal_type, leverage)

    @staticmethod
    def format_volume_analysis_signal(symbol, entry_price, take_profits, signal_type, leverage=15):
        """Formats a professional volume analysis signal"""
        return SignalFormatter.format_scalp_signal(symbol, entry_price, take_profits, signal_type, leverage)

    @staticmethod
    def format_momentum_signal(symbol, entry_price, take_profits, signal_type, leverage=15):
        """Formats a professional momentum signal"""
        return SignalFormatter.format_scalp_signal(symbol, entry_price, take_profits, signal_type, leverage)

    @staticmethod
    def format_profit_update(symbol, signal_type, leverage, current_price, profit_percentage, entry_price):
        """Formats a professional profit update message"""
        try:
            current_price = float(current_price)
            entry_price = float(entry_price)
        except (ValueError, TypeError):
            return None

        decimal_places = 2 if current_price >= 1 else 6
        direction = "LONG" if signal_type == "LONG" else "SHORT"

        current_time = datetime.datetime.now()
        time_str = current_time.strftime("%H:%M UTC")

        message = (
            f"CRYPTOSIGNALS PROFESSIONAL\n"
            f"{'=' * 35}\n\n"
            f"PROFIT UPDATE\n\n"
            f"ASSET: {symbol}\n"
            f"DIRECTION: {direction}\n"
            f"LEVERAGE: {leverage}x\n\n"
            f"ENTRY PRICE: {entry_price:.{decimal_places}f}\n"
            f"CURRENT PRICE: {current_price:.{decimal_places}f}\n"
            f"PROFIT: +{profit_percentage:.2f}%\n\n"
            f"RECOMMENDATION:\n"
            f"- Consider partial profit taking\n"
            f"- Move stop loss to breakeven\n"
            f"- Monitor for reversal signals\n\n"
            f"UPDATED: {time_str}\n"
            f"{'=' * 35}\n"
            f"CRYPTOSIGNALS PROFESSIONAL"
        )

        return message

    @staticmethod
    def format_daily_report(total_signals, profitable_signals, win_rate, avg_profit):
        """Formats a professional daily performance report"""
        current_time = datetime.datetime.now()
        date_str = current_time.strftime("%Y-%m-%d")

        message = (
            f"CRYPTOSIGNALS PROFESSIONAL\n"
            f"{'=' * 35}\n\n"
            f"DAILY PERFORMANCE REPORT\n"
            f"DATE: {date_str}\n\n"
            f"SIGNALS GENERATED: {total_signals}\n"
            f"PROFITABLE SIGNALS: {profitable_signals}\n"
            f"WIN RATE: {win_rate:.1f}%\n"
            f"AVERAGE PROFIT: {avg_profit:.2f}%\n\n"
            f"MARKET ANALYSIS:\n"
            f"- Multiple timeframes monitored\n"
            f"- 8 strategies active\n"
            f"- 109 assets under surveillance\n"
            f"- 24/7 operation maintained\n\n"
            f"{'=' * 35}\n"
            f"CRYPTOSIGNALS PROFESSIONAL"
        )

        return message

    @staticmethod
    def format_profit_message(symbol, signal_type, leverage, current_price, profit_level, entry_price):
        """Formats a professional profit message for specific levels"""
        try:
            current_price = float(current_price)
            entry_price = float(entry_price)
            profit_level = float(profit_level)
        except (ValueError, TypeError):
            return None

        decimal_places = 2 if current_price >= 1 else 6
        direction = "LONG" if signal_type == "LONG" else "SHORT"

        current_time = datetime.datetime.now()
        time_str = current_time.strftime("%H:%M UTC")

        message = (
            f"CRYPTOSIGNALS PROFESSIONAL\n"
            f"{'=' * 35}\n\n"
            f"PROFIT ALERT\n\n"
            f"ASSET: {symbol}\n"
            f"DIRECTION: {direction}\n"
            f"LEVERAGE: {leverage}x\n\n"
            f"ENTRY PRICE: {entry_price:.{decimal_places}f}\n"
            f"CURRENT PRICE: {current_price:.{decimal_places}f}\n"
            f"PROFIT LEVEL: +{profit_level:.1f}%\n\n"
            f"RECOMMENDATION:\n"
            f"- Consider partial profit taking\n"
            f"- Move stop loss to breakeven\n"
            f"- Monitor for reversal signals\n\n"
            f"UPDATED: {time_str}\n"
            f"{'=' * 35}\n"
            f"CRYPTOSIGNALS PROFESSIONAL"
        )

        return message

    @staticmethod
    def format_stop_loss_message(symbol, signal_type, leverage, entry_price, current_price, loss_percentage):
        """Formats a professional stop loss message"""
        try:
            entry_price = float(entry_price)
            current_price = float(current_price)
            loss_percentage = float(loss_percentage)
        except (ValueError, TypeError):
            return None

        decimal_places = 2 if current_price >= 1 else 6
        direction = "LONG" if signal_type == "LONG" else "SHORT"

        current_time = datetime.datetime.now()
        time_str = current_time.strftime("%H:%M UTC")

        message = (
            f"CRYPTOSIGNALS PROFESSIONAL\n"
            f"{'=' * 35}\n\n"
            f"STOP LOSS ALERT\n\n"
            f"ASSET: {symbol}\n"
            f"DIRECTION: {direction}\n"
            f"LEVERAGE: {leverage}x\n\n"
            f"ENTRY PRICE: {entry_price:.{decimal_places}f}\n"
            f"CURRENT PRICE: {current_price:.{decimal_places}f}\n"
            f"LOSS: -{loss_percentage:.2f}%\n\n"
            f"RISK MANAGEMENT:\n"
            f"- Position should be closed immediately\n"
            f"- Review trade analysis for improvements\n"
            f"- Wait for next high-probability setup\n\n"
            f"UPDATED: {time_str}\n"
            f"{'=' * 35}\n"
            f"CRYPTOSIGNALS PROFESSIONAL"
        )

        return message