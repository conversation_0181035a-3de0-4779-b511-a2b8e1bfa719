{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nimport _toConsumableArray from \"@babel/runtime/helpers/toConsumableArray\";\nvar _excluded = [\"transform\", \"origin\", \"originX\", \"originY\", \"fontFamily\", \"fontSize\", \"fontWeight\", \"fontStyle\", \"style\", \"forwardedRef\", \"gradientTransform\", \"patternTransform\"],\n  _excluded2 = [\"x\", \"y\"];\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport * as React from 'react';\nimport ucE from \"react-native-web/dist/exports/createElement\";\nimport cE from \"react-native-web/dist/exports/createElement\";\nimport SvgTouchableMixin from \"./lib/SvgTouchableMixin\";\nimport { resolve } from \"./lib/resolve\";\nimport { transformsArrayToProps } from \"./lib/extract/extractTransform\";\nvar createElement = cE || ucE;\nvar hasTouchableProperty = function hasTouchableProperty(props) {\n  return props.onPress || props.onPressIn || props.onPressOut || props.onLongPress;\n};\nvar camelCaseToDashed = function camelCaseToDashed(camelCase) {\n  return camelCase.replace(/[A-Z]/g, function (m) {\n    return '-' + m.toLowerCase();\n  });\n};\nfunction stringifyTransformProps(transformProps) {\n  var transformArray = [];\n  if (transformProps.translate != null) {\n    transformArray.push(`translate(${transformProps.translate})`);\n  }\n  if (transformProps.translateX != null || transformProps.translateY != null) {\n    transformArray.push(`translate(${transformProps.translateX || 0}, ${transformProps.translateY || 0})`);\n  }\n  if (transformProps.scale != null) {\n    transformArray.push(`scale(${transformProps.scale})`);\n  }\n  if (transformProps.scaleX != null || transformProps.scaleY != null) {\n    transformArray.push(`scale(${transformProps.scaleX || 1}, ${transformProps.scaleY || 1})`);\n  }\n  if (transformProps.rotation != null) {\n    transformArray.push(`rotate(${transformProps.rotation})`);\n  }\n  if (transformProps.skewX != null) {\n    transformArray.push(`skewX(${transformProps.skewX})`);\n  }\n  if (transformProps.skewY != null) {\n    transformArray.push(`skewY(${transformProps.skewY})`);\n  }\n  return transformArray;\n}\nfunction parseTransformProp(transform, props) {\n  var transformArray = [];\n  props && transformArray.push.apply(transformArray, _toConsumableArray(stringifyTransformProps(props)));\n  if (Array.isArray(transform)) {\n    if (typeof transform[0] === 'number') {\n      transformArray.push(`matrix(${transform.join(' ')})`);\n    } else {\n      var stringifiedProps = transformsArrayToProps(transform);\n      transformArray.push.apply(transformArray, _toConsumableArray(stringifyTransformProps(stringifiedProps)));\n    }\n  } else if (typeof transform === 'string') {\n    transformArray.push(transform);\n  }\n  return transformArray.length ? transformArray.join(' ') : undefined;\n}\nvar prepare = function prepare(self) {\n  var props = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : self.props;\n  var transform = props.transform,\n    origin = props.origin,\n    originX = props.originX,\n    originY = props.originY,\n    fontFamily = props.fontFamily,\n    fontSize = props.fontSize,\n    fontWeight = props.fontWeight,\n    fontStyle = props.fontStyle,\n    style = props.style,\n    forwardedRef = props.forwardedRef,\n    gradientTransform = props.gradientTransform,\n    patternTransform = props.patternTransform,\n    rest = _objectWithoutProperties(props, _excluded);\n  var clean = _objectSpread(_objectSpread({}, hasTouchableProperty(props) ? {\n    onStartShouldSetResponder: self.touchableHandleStartShouldSetResponder,\n    onResponderTerminationRequest: self.touchableHandleResponderTerminationRequest,\n    onResponderGrant: self.touchableHandleResponderGrant,\n    onResponderMove: self.touchableHandleResponderMove,\n    onResponderRelease: self.touchableHandleResponderRelease,\n    onResponderTerminate: self.touchableHandleResponderTerminate\n  } : null), rest);\n  if (origin != null) {\n    clean['transform-origin'] = origin.toString().replace(',', ' ');\n  } else if (originX != null || originY != null) {\n    clean['transform-origin'] = `${originX || 0} ${originY || 0}`;\n  }\n  clean.transform = parseTransformProp(transform, props);\n  clean.gradientTransform = parseTransformProp(gradientTransform);\n  clean.patternTransform = parseTransformProp(patternTransform);\n  clean.ref = function (el) {\n    self.elementRef.current = el;\n    if (typeof forwardedRef === 'function') {\n      forwardedRef(el);\n    } else if (forwardedRef) {\n      forwardedRef.current = el;\n    }\n  };\n  var styles = {};\n  if (fontFamily != null) {\n    styles.fontFamily = fontFamily;\n  }\n  if (fontSize != null) {\n    styles.fontSize = fontSize;\n  }\n  if (fontWeight != null) {\n    styles.fontWeight = fontWeight;\n  }\n  if (fontStyle != null) {\n    styles.fontStyle = fontStyle;\n  }\n  clean.style = resolve(style, styles);\n  return clean;\n};\nvar getBoundingClientRect = function getBoundingClientRect(node) {\n  if (node) {\n    var isElement = node.nodeType === 1;\n    if (isElement && typeof node.getBoundingClientRect === 'function') {\n      return node.getBoundingClientRect();\n    }\n  }\n};\nvar measureLayout = function measureLayout(node, callback) {\n  var relativeNode = node && node.parentNode;\n  if (relativeNode) {\n    setTimeout(function () {\n      var relativeRect = getBoundingClientRect(relativeNode);\n      var _getBoundingClientRec = getBoundingClientRect(node),\n        height = _getBoundingClientRec.height,\n        left = _getBoundingClientRec.left,\n        top = _getBoundingClientRec.top,\n        width = _getBoundingClientRec.width;\n      var x = left - relativeRect.left;\n      var y = top - relativeRect.top;\n      callback(x, y, width, height, left, top);\n    }, 0);\n  }\n};\nfunction remeasure() {\n  var tag = this.state.touchable.responderID;\n  if (tag == null) {\n    return;\n  }\n  measureLayout(tag, this._handleQueryLayout);\n}\nexport var WebShape = function (_React$Component) {\n  function WebShape(props, context) {\n    var _this;\n    _classCallCheck(this, WebShape);\n    _this = _callSuper(this, WebShape, [props, context]);\n    _this.elementRef = React.createRef();\n    _this.lastMergedProps = {};\n    if (hasTouchableProperty(props)) {\n      SvgTouchableMixin(_this);\n    }\n    _this._remeasureMetricsOnActivation = remeasure.bind(_this);\n    return _this;\n  }\n  _inherits(WebShape, _React$Component);\n  return _createClass(WebShape, [{\n    key: \"prepareProps\",\n    value: function prepareProps(props) {\n      return props;\n    }\n  }, {\n    key: \"setNativeProps\",\n    value: function setNativeProps(props) {\n      var merged = Object.assign({}, this.props, this.lastMergedProps, props.style);\n      this.lastMergedProps = merged;\n      var clean = prepare(this, this.prepareProps(merged));\n      var current = this.elementRef.current;\n      if (current) {\n        for (var cleanAttribute of Object.keys(clean)) {\n          var cleanValue = clean[cleanAttribute];\n          switch (cleanAttribute) {\n            case 'ref':\n            case 'children':\n              break;\n            case 'style':\n              for (var partialStyle of [].concat((_clean$style = clean.style) != null ? _clean$style : [])) {\n                var _clean$style;\n                Object.assign(current.style, partialStyle);\n              }\n              break;\n            default:\n              current.setAttribute(camelCaseToDashed(cleanAttribute), cleanValue);\n              break;\n          }\n        }\n      }\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      if (!this.tag) {\n        throw new Error('When extending `WebShape` you need to overwrite either `tag` or `render`!');\n      }\n      this.lastMergedProps = {};\n      return createElement(this.tag, prepare(this, this.prepareProps(this.props)));\n    }\n  }]);\n}(React.Component);\nexport var Circle = function (_WebShape2) {\n  function Circle() {\n    var _this2;\n    _classCallCheck(this, Circle);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this2 = _callSuper(this, Circle, [].concat(args));\n    _this2.tag = 'circle';\n    return _this2;\n  }\n  _inherits(Circle, _WebShape2);\n  return _createClass(Circle);\n}(WebShape);\nexport var ClipPath = function (_WebShape3) {\n  function ClipPath() {\n    var _this3;\n    _classCallCheck(this, ClipPath);\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n    _this3 = _callSuper(this, ClipPath, [].concat(args));\n    _this3.tag = 'clipPath';\n    return _this3;\n  }\n  _inherits(ClipPath, _WebShape3);\n  return _createClass(ClipPath);\n}(WebShape);\nexport var Defs = function (_WebShape4) {\n  function Defs() {\n    var _this4;\n    _classCallCheck(this, Defs);\n    for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n      args[_key3] = arguments[_key3];\n    }\n    _this4 = _callSuper(this, Defs, [].concat(args));\n    _this4.tag = 'defs';\n    return _this4;\n  }\n  _inherits(Defs, _WebShape4);\n  return _createClass(Defs);\n}(WebShape);\nexport var Ellipse = function (_WebShape5) {\n  function Ellipse() {\n    var _this5;\n    _classCallCheck(this, Ellipse);\n    for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n      args[_key4] = arguments[_key4];\n    }\n    _this5 = _callSuper(this, Ellipse, [].concat(args));\n    _this5.tag = 'ellipse';\n    return _this5;\n  }\n  _inherits(Ellipse, _WebShape5);\n  return _createClass(Ellipse);\n}(WebShape);\nexport var G = function (_WebShape6) {\n  function G() {\n    var _this6;\n    _classCallCheck(this, G);\n    for (var _len5 = arguments.length, args = new Array(_len5), _key5 = 0; _key5 < _len5; _key5++) {\n      args[_key5] = arguments[_key5];\n    }\n    _this6 = _callSuper(this, G, [].concat(args));\n    _this6.tag = 'g';\n    return _this6;\n  }\n  _inherits(G, _WebShape6);\n  return _createClass(G, [{\n    key: \"prepareProps\",\n    value: function prepareProps(props) {\n      var x = props.x,\n        y = props.y,\n        rest = _objectWithoutProperties(props, _excluded2);\n      if ((x || y) && !rest.translate) {\n        rest.translate = `${x || 0}, ${y || 0}`;\n      }\n      return rest;\n    }\n  }]);\n}(WebShape);\nexport var Image = function (_WebShape7) {\n  function Image() {\n    var _this7;\n    _classCallCheck(this, Image);\n    for (var _len6 = arguments.length, args = new Array(_len6), _key6 = 0; _key6 < _len6; _key6++) {\n      args[_key6] = arguments[_key6];\n    }\n    _this7 = _callSuper(this, Image, [].concat(args));\n    _this7.tag = 'image';\n    return _this7;\n  }\n  _inherits(Image, _WebShape7);\n  return _createClass(Image);\n}(WebShape);\nexport var Line = function (_WebShape8) {\n  function Line() {\n    var _this8;\n    _classCallCheck(this, Line);\n    for (var _len7 = arguments.length, args = new Array(_len7), _key7 = 0; _key7 < _len7; _key7++) {\n      args[_key7] = arguments[_key7];\n    }\n    _this8 = _callSuper(this, Line, [].concat(args));\n    _this8.tag = 'line';\n    return _this8;\n  }\n  _inherits(Line, _WebShape8);\n  return _createClass(Line);\n}(WebShape);\nexport var LinearGradient = function (_WebShape9) {\n  function LinearGradient() {\n    var _this9;\n    _classCallCheck(this, LinearGradient);\n    for (var _len8 = arguments.length, args = new Array(_len8), _key8 = 0; _key8 < _len8; _key8++) {\n      args[_key8] = arguments[_key8];\n    }\n    _this9 = _callSuper(this, LinearGradient, [].concat(args));\n    _this9.tag = 'linearGradient';\n    return _this9;\n  }\n  _inherits(LinearGradient, _WebShape9);\n  return _createClass(LinearGradient);\n}(WebShape);\nexport var Path = function (_WebShape0) {\n  function Path() {\n    var _this0;\n    _classCallCheck(this, Path);\n    for (var _len9 = arguments.length, args = new Array(_len9), _key9 = 0; _key9 < _len9; _key9++) {\n      args[_key9] = arguments[_key9];\n    }\n    _this0 = _callSuper(this, Path, [].concat(args));\n    _this0.tag = 'path';\n    return _this0;\n  }\n  _inherits(Path, _WebShape0);\n  return _createClass(Path);\n}(WebShape);\nexport var Polygon = function (_WebShape1) {\n  function Polygon() {\n    var _this1;\n    _classCallCheck(this, Polygon);\n    for (var _len0 = arguments.length, args = new Array(_len0), _key0 = 0; _key0 < _len0; _key0++) {\n      args[_key0] = arguments[_key0];\n    }\n    _this1 = _callSuper(this, Polygon, [].concat(args));\n    _this1.tag = 'polygon';\n    return _this1;\n  }\n  _inherits(Polygon, _WebShape1);\n  return _createClass(Polygon);\n}(WebShape);\nexport var Polyline = function (_WebShape10) {\n  function Polyline() {\n    var _this10;\n    _classCallCheck(this, Polyline);\n    for (var _len1 = arguments.length, args = new Array(_len1), _key1 = 0; _key1 < _len1; _key1++) {\n      args[_key1] = arguments[_key1];\n    }\n    _this10 = _callSuper(this, Polyline, [].concat(args));\n    _this10.tag = 'polyline';\n    return _this10;\n  }\n  _inherits(Polyline, _WebShape10);\n  return _createClass(Polyline);\n}(WebShape);\nexport var RadialGradient = function (_WebShape11) {\n  function RadialGradient() {\n    var _this11;\n    _classCallCheck(this, RadialGradient);\n    for (var _len10 = arguments.length, args = new Array(_len10), _key10 = 0; _key10 < _len10; _key10++) {\n      args[_key10] = arguments[_key10];\n    }\n    _this11 = _callSuper(this, RadialGradient, [].concat(args));\n    _this11.tag = 'radialGradient';\n    return _this11;\n  }\n  _inherits(RadialGradient, _WebShape11);\n  return _createClass(RadialGradient);\n}(WebShape);\nexport var Rect = function (_WebShape12) {\n  function Rect() {\n    var _this12;\n    _classCallCheck(this, Rect);\n    for (var _len11 = arguments.length, args = new Array(_len11), _key11 = 0; _key11 < _len11; _key11++) {\n      args[_key11] = arguments[_key11];\n    }\n    _this12 = _callSuper(this, Rect, [].concat(args));\n    _this12.tag = 'rect';\n    return _this12;\n  }\n  _inherits(Rect, _WebShape12);\n  return _createClass(Rect);\n}(WebShape);\nexport var Stop = function (_WebShape13) {\n  function Stop() {\n    var _this13;\n    _classCallCheck(this, Stop);\n    for (var _len12 = arguments.length, args = new Array(_len12), _key12 = 0; _key12 < _len12; _key12++) {\n      args[_key12] = arguments[_key12];\n    }\n    _this13 = _callSuper(this, Stop, [].concat(args));\n    _this13.tag = 'stop';\n    return _this13;\n  }\n  _inherits(Stop, _WebShape13);\n  return _createClass(Stop);\n}(WebShape);\nexport var Svg = function (_WebShape14) {\n  function Svg() {\n    var _this14;\n    _classCallCheck(this, Svg);\n    for (var _len13 = arguments.length, args = new Array(_len13), _key13 = 0; _key13 < _len13; _key13++) {\n      args[_key13] = arguments[_key13];\n    }\n    _this14 = _callSuper(this, Svg, [].concat(args));\n    _this14.tag = 'svg';\n    return _this14;\n  }\n  _inherits(Svg, _WebShape14);\n  return _createClass(Svg);\n}(WebShape);\nexport var Symbol = function (_WebShape15) {\n  function Symbol() {\n    var _this15;\n    _classCallCheck(this, Symbol);\n    for (var _len14 = arguments.length, args = new Array(_len14), _key14 = 0; _key14 < _len14; _key14++) {\n      args[_key14] = arguments[_key14];\n    }\n    _this15 = _callSuper(this, Symbol, [].concat(args));\n    _this15.tag = 'symbol';\n    return _this15;\n  }\n  _inherits(Symbol, _WebShape15);\n  return _createClass(Symbol);\n}(WebShape);\nexport var Text = function (_WebShape16) {\n  function Text() {\n    var _this16;\n    _classCallCheck(this, Text);\n    for (var _len15 = arguments.length, args = new Array(_len15), _key15 = 0; _key15 < _len15; _key15++) {\n      args[_key15] = arguments[_key15];\n    }\n    _this16 = _callSuper(this, Text, [].concat(args));\n    _this16.tag = 'text';\n    return _this16;\n  }\n  _inherits(Text, _WebShape16);\n  return _createClass(Text);\n}(WebShape);\nexport var TSpan = function (_WebShape17) {\n  function TSpan() {\n    var _this17;\n    _classCallCheck(this, TSpan);\n    for (var _len16 = arguments.length, args = new Array(_len16), _key16 = 0; _key16 < _len16; _key16++) {\n      args[_key16] = arguments[_key16];\n    }\n    _this17 = _callSuper(this, TSpan, [].concat(args));\n    _this17.tag = 'tspan';\n    return _this17;\n  }\n  _inherits(TSpan, _WebShape17);\n  return _createClass(TSpan);\n}(WebShape);\nexport var TextPath = function (_WebShape18) {\n  function TextPath() {\n    var _this18;\n    _classCallCheck(this, TextPath);\n    for (var _len17 = arguments.length, args = new Array(_len17), _key17 = 0; _key17 < _len17; _key17++) {\n      args[_key17] = arguments[_key17];\n    }\n    _this18 = _callSuper(this, TextPath, [].concat(args));\n    _this18.tag = 'textPath';\n    return _this18;\n  }\n  _inherits(TextPath, _WebShape18);\n  return _createClass(TextPath);\n}(WebShape);\nexport var Use = function (_WebShape19) {\n  function Use() {\n    var _this19;\n    _classCallCheck(this, Use);\n    for (var _len18 = arguments.length, args = new Array(_len18), _key18 = 0; _key18 < _len18; _key18++) {\n      args[_key18] = arguments[_key18];\n    }\n    _this19 = _callSuper(this, Use, [].concat(args));\n    _this19.tag = 'use';\n    return _this19;\n  }\n  _inherits(Use, _WebShape19);\n  return _createClass(Use);\n}(WebShape);\nexport var Mask = function (_WebShape20) {\n  function Mask() {\n    var _this20;\n    _classCallCheck(this, Mask);\n    for (var _len19 = arguments.length, args = new Array(_len19), _key19 = 0; _key19 < _len19; _key19++) {\n      args[_key19] = arguments[_key19];\n    }\n    _this20 = _callSuper(this, Mask, [].concat(args));\n    _this20.tag = 'mask';\n    return _this20;\n  }\n  _inherits(Mask, _WebShape20);\n  return _createClass(Mask);\n}(WebShape);\nexport var ForeignObject = function (_WebShape21) {\n  function ForeignObject() {\n    var _this21;\n    _classCallCheck(this, ForeignObject);\n    for (var _len20 = arguments.length, args = new Array(_len20), _key20 = 0; _key20 < _len20; _key20++) {\n      args[_key20] = arguments[_key20];\n    }\n    _this21 = _callSuper(this, ForeignObject, [].concat(args));\n    _this21.tag = 'foreignObject';\n    return _this21;\n  }\n  _inherits(ForeignObject, _WebShape21);\n  return _createClass(ForeignObject);\n}(WebShape);\nexport var Marker = function (_WebShape22) {\n  function Marker() {\n    var _this22;\n    _classCallCheck(this, Marker);\n    for (var _len21 = arguments.length, args = new Array(_len21), _key21 = 0; _key21 < _len21; _key21++) {\n      args[_key21] = arguments[_key21];\n    }\n    _this22 = _callSuper(this, Marker, [].concat(args));\n    _this22.tag = 'marker';\n    return _this22;\n  }\n  _inherits(Marker, _WebShape22);\n  return _createClass(Marker);\n}(WebShape);\nexport var Pattern = function (_WebShape23) {\n  function Pattern() {\n    var _this23;\n    _classCallCheck(this, Pattern);\n    for (var _len22 = arguments.length, args = new Array(_len22), _key22 = 0; _key22 < _len22; _key22++) {\n      args[_key22] = arguments[_key22];\n    }\n    _this23 = _callSuper(this, Pattern, [].concat(args));\n    _this23.tag = 'pattern';\n    return _this23;\n  }\n  _inherits(Pattern, _WebShape23);\n  return _createClass(Pattern);\n}(WebShape);\nexport default Svg;", "map": {"version": 3, "names": ["React", "ucE", "cE", "SvgTouchableMixin", "resolve", "transformsArrayToProps", "createElement", "hasTouchableProperty", "props", "onPress", "onPressIn", "onPressOut", "onLongPress", "camelCaseToDashed", "camelCase", "replace", "m", "toLowerCase", "stringifyTransformProps", "transformProps", "transformArray", "translate", "push", "translateX", "translateY", "scale", "scaleX", "scaleY", "rotation", "skewX", "skewY", "parseTransformProp", "transform", "apply", "_toConsumableArray", "Array", "isArray", "join", "stringifiedProps", "length", "undefined", "prepare", "self", "arguments", "origin", "originX", "originY", "fontFamily", "fontSize", "fontWeight", "fontStyle", "style", "forwardedRef", "gradientTransform", "patternTransform", "rest", "_objectWithoutProperties", "_excluded", "clean", "_objectSpread", "onStartShouldSetResponder", "touchableHandleStartShouldSetResponder", "onResponderTerminationRequest", "touchableHandleResponderTerminationRequest", "onResponderGrant", "touchableHandleResponderGrant", "onResponderMove", "touchableHandleResponderMove", "onResponderRelease", "touchableHandleResponderRelease", "onResponderTerminate", "touchableHandleResponderTerminate", "toString", "ref", "el", "elementRef", "current", "styles", "getBoundingClientRect", "node", "isElement", "nodeType", "measureLayout", "callback", "relativeNode", "parentNode", "setTimeout", "relativeRect", "_getBoundingClientRec", "height", "left", "top", "width", "x", "y", "remeasure", "tag", "state", "touchable", "responderID", "_handleQueryLayout", "WebShape", "_React$Component", "context", "_this", "_classCallCheck", "_callSuper", "createRef", "lastMergedProps", "_remeasureMetricsOnActivation", "bind", "_inherits", "_createClass", "key", "value", "prepareProps", "setNativeProps", "merged", "Object", "assign", "cleanAttribute", "keys", "cleanValue", "partialStyle", "concat", "_clean$style", "setAttribute", "render", "Error", "Component", "Circle", "_WebShape2", "_this2", "_len", "args", "_key", "<PERSON><PERSON><PERSON><PERSON>", "_WebShape3", "_this3", "_len2", "_key2", "Defs", "_WebShape4", "_this4", "_len3", "_key3", "Ellipse", "_WebShape5", "_this5", "_len4", "_key4", "G", "_WebShape6", "_this6", "_len5", "_key5", "_excluded2", "Image", "_WebShape7", "_this7", "_len6", "_key6", "Line", "_WebShape8", "_this8", "_len7", "_key7", "LinearGradient", "_WebShape9", "_this9", "_len8", "_key8", "Path", "_WebShape0", "_this0", "_len9", "_key9", "Polygon", "_WebShape1", "_this1", "_len0", "_key0", "Polyline", "_WebShape10", "_this10", "_len1", "_key1", "RadialGrad<PERSON>", "_WebShape11", "_this11", "_len10", "_key10", "Rect", "_WebShape12", "_this12", "_len11", "_key11", "Stop", "_WebShape13", "_this13", "_len12", "_key12", "Svg", "_WebShape14", "_this14", "_len13", "_key13", "Symbol", "_WebShape15", "_this15", "_len14", "_key14", "Text", "_WebShape16", "_this16", "_len15", "_key15", "TSpan", "_WebShape17", "_this17", "_len16", "_key16", "TextPath", "_WebShape18", "_this18", "_len17", "_key17", "Use", "_WebShape19", "_this19", "_len18", "_key18", "Mask", "_WebShape20", "_this20", "_len19", "_key19", "ForeignObject", "_WebShape21", "_this21", "_len20", "_key20", "<PERSON><PERSON>", "_WebShape22", "_this22", "_len21", "_key21", "Pattern", "_WebShape23", "_this23", "_len22", "_key22"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\react-native-svg\\src\\ReactNativeSVG.web.ts"], "sourcesContent": ["import * as React from 'react';\nimport type { GestureResponderEvent, TransformsStyle } from 'react-native';\nimport {\n  // @ts-ignore\n  unstable_createElement as ucE,\n  createElement as cE,\n} from 'react-native';\nimport type {\n  NumberArray,\n  NumberProp,\n  TransformProps,\n} from './lib/extract/types';\nimport SvgTouchableMixin from './lib/SvgTouchableMixin';\nimport { resolve } from './lib/resolve';\nimport { transformsArrayToProps } from './lib/extract/extractTransform';\n\nconst createElement = cE || ucE;\n\ntype BlurEvent = Object;\ntype FocusEvent = Object;\ntype PressEvent = Object;\ntype LayoutEvent = Object;\ntype EdgeInsetsProp = Object;\n\ninterface BaseProps {\n  accessible?: boolean;\n  accessibilityLabel?: string;\n  accessibilityHint?: string;\n  accessibilityIgnoresInvertColors?: boolean;\n  accessibilityRole?: string;\n  accessibilityState?: Object;\n  delayLongPress?: number;\n  delayPressIn?: number;\n  delayPressOut?: number;\n  disabled?: boolean;\n  hitSlop?: EdgeInsetsProp;\n  nativeID?: string;\n  touchSoundDisabled?: boolean;\n  onBlur?: (e: BlurEvent) => void;\n  onFocus?: (e: FocusEvent) => void;\n  onLayout?: (event: LayoutEvent) => object;\n  onLongPress?: (event: PressEvent) => object;\n  onClick?: (event: PressEvent) => object;\n  onPress?: (event: PressEvent) => object;\n  onPressIn?: (event: PressEvent) => object;\n  onPressOut?: (event: PressEvent) => object;\n  pressRetentionOffset?: EdgeInsetsProp;\n  rejectResponderTermination?: boolean;\n\n  transform?: TransformProps['transform'];\n  translate?: NumberArray;\n  translateX?: NumberProp;\n  translateY?: NumberProp;\n  scale?: NumberArray;\n  scaleX?: NumberProp;\n  scaleY?: NumberProp;\n  rotation?: NumberProp;\n  skewX?: NumberProp;\n  skewY?: NumberProp;\n  origin?: NumberArray;\n  originX?: NumberProp;\n  originY?: NumberProp;\n\n  fontStyle?: string;\n  fontWeight?: NumberProp;\n  fontSize?: NumberProp;\n  fontFamily?: string;\n  forwardedRef?:\n    | React.RefCallback<SVGElement>\n    | React.MutableRefObject<SVGElement | null>;\n  style: Iterable<{}>;\n\n  // different tranform props\n  gradientTransform: TransformProps['transform'];\n  patternTransform: TransformProps['transform'];\n}\n\nconst hasTouchableProperty = (props: BaseProps) =>\n  props.onPress || props.onPressIn || props.onPressOut || props.onLongPress;\n\nconst camelCaseToDashed = (camelCase: string) => {\n  return camelCase.replace(/[A-Z]/g, (m) => '-' + m.toLowerCase());\n};\n\nfunction stringifyTransformProps(transformProps: TransformProps) {\n  const transformArray = [];\n  if (transformProps.translate != null) {\n    transformArray.push(`translate(${transformProps.translate})`);\n  }\n  if (transformProps.translateX != null || transformProps.translateY != null) {\n    transformArray.push(\n      `translate(${transformProps.translateX || 0}, ${\n        transformProps.translateY || 0\n      })`,\n    );\n  }\n  if (transformProps.scale != null) {\n    transformArray.push(`scale(${transformProps.scale})`);\n  }\n  if (transformProps.scaleX != null || transformProps.scaleY != null) {\n    transformArray.push(\n      `scale(${transformProps.scaleX || 1}, ${transformProps.scaleY || 1})`,\n    );\n  }\n  // rotation maps to rotate, not to collide with the text rotate attribute (which acts per glyph rather than block)\n  if (transformProps.rotation != null) {\n    transformArray.push(`rotate(${transformProps.rotation})`);\n  }\n  if (transformProps.skewX != null) {\n    transformArray.push(`skewX(${transformProps.skewX})`);\n  }\n  if (transformProps.skewY != null) {\n    transformArray.push(`skewY(${transformProps.skewY})`);\n  }\n  return transformArray;\n}\n\nfunction parseTransformProp(\n  transform: TransformProps['transform'],\n  props?: BaseProps,\n) {\n  const transformArray: string[] = [];\n\n  props && transformArray.push(...stringifyTransformProps(props));\n\n  if (Array.isArray(transform)) {\n    if (typeof transform[0] === 'number') {\n      transformArray.push(`matrix(${transform.join(' ')})`);\n    } else {\n      const stringifiedProps = transformsArrayToProps(\n        transform as TransformsStyle['transform'],\n      );\n      transformArray.push(...stringifyTransformProps(stringifiedProps));\n    }\n  } else if (typeof transform === 'string') {\n    transformArray.push(transform);\n  }\n\n  return transformArray.length ? transformArray.join(' ') : undefined;\n}\n\n/**\n * `react-native-svg` supports additional props that aren't defined in the spec.\n * This function replaces them in a spec conforming manner.\n *\n * @param {WebShape} self Instance given to us.\n * @param {Object?} props Optional overridden props given to us.\n * @returns {Object} Cleaned props object.\n * @private\n */\nconst prepare = <T extends BaseProps>(\n  self: WebShape<T>,\n  props = self.props,\n) => {\n  const {\n    transform,\n    origin,\n    originX,\n    originY,\n    fontFamily,\n    fontSize,\n    fontWeight,\n    fontStyle,\n    style,\n    forwardedRef,\n    gradientTransform,\n    patternTransform,\n    ...rest\n  } = props;\n\n  const clean: {\n    onStartShouldSetResponder?: (e: GestureResponderEvent) => boolean;\n    onResponderMove?: (e: GestureResponderEvent) => void;\n    onResponderGrant?: (e: GestureResponderEvent) => void;\n    onResponderRelease?: (e: GestureResponderEvent) => void;\n    onResponderTerminate?: (e: GestureResponderEvent) => void;\n    onResponderTerminationRequest?: (e: GestureResponderEvent) => boolean;\n    transform?: string;\n    gradientTransform?: string;\n    patternTransform?: string;\n    'transform-origin'?: string;\n    style?: {};\n    ref?: {};\n  } = {\n    ...(hasTouchableProperty(props)\n      ? {\n          onStartShouldSetResponder:\n            self.touchableHandleStartShouldSetResponder,\n          onResponderTerminationRequest:\n            self.touchableHandleResponderTerminationRequest,\n          onResponderGrant: self.touchableHandleResponderGrant,\n          onResponderMove: self.touchableHandleResponderMove,\n          onResponderRelease: self.touchableHandleResponderRelease,\n          onResponderTerminate: self.touchableHandleResponderTerminate,\n        }\n      : null),\n    ...rest,\n  };\n\n  if (origin != null) {\n    clean['transform-origin'] = origin.toString().replace(',', ' ');\n  } else if (originX != null || originY != null) {\n    clean['transform-origin'] = `${originX || 0} ${originY || 0}`;\n  }\n\n  clean.transform = parseTransformProp(transform, props);\n  clean.gradientTransform = parseTransformProp(gradientTransform);\n  clean.patternTransform = parseTransformProp(patternTransform);\n\n  clean.ref = (el: SVGElement | null) => {\n    self.elementRef.current = el;\n    if (typeof forwardedRef === 'function') {\n      forwardedRef(el);\n    } else if (forwardedRef) {\n      forwardedRef.current = el;\n    }\n  };\n\n  const styles: {\n    fontStyle?: string;\n    fontFamily?: string;\n    fontSize?: NumberProp;\n    fontWeight?: NumberProp;\n  } = {};\n\n  if (fontFamily != null) {\n    styles.fontFamily = fontFamily;\n  }\n  if (fontSize != null) {\n    styles.fontSize = fontSize;\n  }\n  if (fontWeight != null) {\n    styles.fontWeight = fontWeight;\n  }\n  if (fontStyle != null) {\n    styles.fontStyle = fontStyle;\n  }\n  clean.style = resolve(style, styles);\n\n  return clean;\n};\n\nconst getBoundingClientRect = (node: SVGElement) => {\n  if (node) {\n    // @ts-ignore\n    const isElement = node.nodeType === 1; /* Node.ELEMENT_NODE */\n    // @ts-ignore\n    if (isElement && typeof node.getBoundingClientRect === 'function') {\n      // @ts-ignore\n      return node.getBoundingClientRect();\n    }\n  }\n};\n\nconst measureLayout = (\n  node: SVGElement,\n  callback: (\n    x: number,\n    y: number,\n    width: number,\n    height: number,\n    left: number,\n    top: number,\n  ) => void,\n) => {\n  // @ts-ignore\n  const relativeNode = node && node.parentNode;\n  if (relativeNode) {\n    setTimeout(() => {\n      // @ts-ignore\n      const relativeRect = getBoundingClientRect(relativeNode);\n      const { height, left, top, width } = getBoundingClientRect(node);\n      const x = left - relativeRect.left;\n      const y = top - relativeRect.top;\n      callback(x, y, width, height, left, top);\n    }, 0);\n  }\n};\n\nfunction remeasure() {\n  // @ts-ignore\n  const tag = this.state.touchable.responderID;\n  if (tag == null) {\n    return;\n  }\n  // @ts-ignore\n  measureLayout(tag, this._handleQueryLayout);\n}\n\nexport class WebShape<\n  P extends BaseProps = BaseProps,\n  C = {},\n> extends React.Component<P, C> {\n  [x: string]: unknown;\n  protected tag?: React.ElementType;\n  protected prepareProps(props: P) {\n    return props;\n  }\n\n  elementRef =\n    React.createRef<SVGElement>() as React.MutableRefObject<SVGElement | null>;\n  lastMergedProps: Partial<P> = {};\n\n  /**\n   * disclaimer: I am not sure why the props are wrapped in a `style` attribute here, but that's how reanimated calls it\n   */\n  setNativeProps(props: { style: P }) {\n    const merged = Object.assign(\n      {},\n      this.props,\n      this.lastMergedProps,\n      props.style,\n    );\n    this.lastMergedProps = merged;\n    const clean = prepare(this, this.prepareProps(merged));\n    const current = this.elementRef.current;\n    if (current) {\n      for (const cleanAttribute of Object.keys(clean)) {\n        const cleanValue = clean[cleanAttribute as keyof typeof clean];\n        switch (cleanAttribute) {\n          case 'ref':\n          case 'children':\n            break;\n          case 'style':\n            // style can be an object here or an array, so we convert it to an array and assign each element\n            for (const partialStyle of ([] as {}[]).concat(clean.style ?? [])) {\n              // @ts-expect-error \"DOM\" is not part of `compilerOptions.lib`\n              Object.assign(current.style, partialStyle);\n            }\n            break;\n          default:\n            // apply all other incoming prop updates as attributes on the node\n            // same logic as in https://github.com/software-mansion/react-native-reanimated/blob/d04720c82f5941532991b235787285d36d717247/src/reanimated2/js-reanimated/index.ts#L38-L39\n            // @ts-expect-error \"DOM\" is not part of `compilerOptions.lib`\n            current.setAttribute(camelCaseToDashed(cleanAttribute), cleanValue);\n            break;\n        }\n      }\n    }\n  }\n\n  _remeasureMetricsOnActivation: () => void;\n  touchableHandleStartShouldSetResponder?: (\n    e: GestureResponderEvent,\n  ) => boolean;\n  touchableHandleResponderMove?: (e: GestureResponderEvent) => void;\n  touchableHandleResponderGrant?: (e: GestureResponderEvent) => void;\n  touchableHandleResponderRelease?: (e: GestureResponderEvent) => void;\n  touchableHandleResponderTerminate?: (e: GestureResponderEvent) => void;\n  touchableHandleResponderTerminationRequest?: (\n    e: GestureResponderEvent,\n  ) => boolean;\n  constructor(props: P, context: C) {\n    super(props, context);\n\n    // Do not attach touchable mixin handlers if SVG element doesn't have a touchable prop\n    if (hasTouchableProperty(props)) {\n      SvgTouchableMixin(this);\n    }\n\n    this._remeasureMetricsOnActivation = remeasure.bind(this);\n  }\n\n  render(): JSX.Element {\n    if (!this.tag) {\n      throw new Error(\n        'When extending `WebShape` you need to overwrite either `tag` or `render`!',\n      );\n    }\n    this.lastMergedProps = {};\n    return createElement(\n      this.tag,\n      prepare(this, this.prepareProps(this.props)),\n    );\n  }\n}\n\nexport class Circle extends WebShape {\n  tag = 'circle' as const;\n}\n\nexport class ClipPath extends WebShape {\n  tag = 'clipPath' as const;\n}\n\nexport class Defs extends WebShape {\n  tag = 'defs' as const;\n}\n\nexport class Ellipse extends WebShape {\n  tag = 'ellipse' as const;\n}\n\nexport class G extends WebShape<\n  BaseProps & {\n    x?: NumberProp;\n    y?: NumberProp;\n    translate?: string;\n  }\n> {\n  tag = 'g' as const;\n  prepareProps(\n    props: BaseProps & {\n      x?: NumberProp;\n      y?: NumberProp;\n      translate?: string;\n    },\n  ) {\n    const { x, y, ...rest } = props;\n\n    if ((x || y) && !rest.translate) {\n      rest.translate = `${x || 0}, ${y || 0}`;\n    }\n\n    return rest;\n  }\n}\n\nexport class Image extends WebShape {\n  tag = 'image' as const;\n}\n\nexport class Line extends WebShape {\n  tag = 'line' as const;\n}\n\nexport class LinearGradient extends WebShape {\n  tag = 'linearGradient' as const;\n}\n\nexport class Path extends WebShape {\n  tag = 'path' as const;\n}\n\nexport class Polygon extends WebShape {\n  tag = 'polygon' as const;\n}\n\nexport class Polyline extends WebShape {\n  tag = 'polyline' as const;\n}\n\nexport class RadialGradient extends WebShape {\n  tag = 'radialGradient' as const;\n}\n\nexport class Rect extends WebShape {\n  tag = 'rect' as const;\n}\n\nexport class Stop extends WebShape {\n  tag = 'stop' as const;\n}\n\nexport class Svg extends WebShape {\n  tag = 'svg' as const;\n}\n\nexport class Symbol extends WebShape {\n  tag = 'symbol' as const;\n}\n\nexport class Text extends WebShape {\n  tag = 'text' as const;\n}\n\nexport class TSpan extends WebShape {\n  tag = 'tspan' as const;\n}\n\nexport class TextPath extends WebShape {\n  tag = 'textPath' as const;\n}\n\nexport class Use extends WebShape {\n  tag = 'use' as const;\n}\n\nexport class Mask extends WebShape {\n  tag = 'mask' as const;\n}\n\nexport class ForeignObject extends WebShape {\n  tag = 'foreignObject' as const;\n}\n\nexport class Marker extends WebShape {\n  tag = 'marker' as const;\n}\n\nexport class Pattern extends WebShape {\n  tag = 'pattern' as const;\n}\n\nexport default Svg;\n"], "mappings": ";;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,GAAA;AAAA,OAAAC,EAAA;AAY9B,OAAOC,iBAAiB;AACxB,SAASC,OAAO;AAChB,SAASC,sBAAsB;AAE/B,IAAMC,aAAa,GAAGJ,EAAE,IAAID,GAAG;AA6D/B,IAAMM,oBAAoB,GAAI,SAAxBA,oBAAoBA,CAAIC,KAAgB;EAAA,OAC5CA,KAAK,CAACC,OAAO,IAAID,KAAK,CAACE,SAAS,IAAIF,KAAK,CAACG,UAAU,IAAIH,KAAK,CAACI,WAAW;AAAA;AAE3E,IAAMC,iBAAiB,GAAI,SAArBA,iBAAiBA,CAAIC,SAAiB,EAAK;EAC/C,OAAOA,SAAS,CAACC,OAAO,CAAC,QAAQ,EAAG,UAAAC,CAAC;IAAA,OAAK,GAAG,GAAGA,CAAC,CAACC,WAAW,EAAE;EAAA,EAAC;AAClE,CAAC;AAED,SAASC,uBAAuBA,CAACC,cAA8B,EAAE;EAC/D,IAAMC,cAAc,GAAG,EAAE;EACzB,IAAID,cAAc,CAACE,SAAS,IAAI,IAAI,EAAE;IACpCD,cAAc,CAACE,IAAI,CAAE,aAAYH,cAAc,CAACE,SAAU,GAAE,CAAC;EAC/D;EACA,IAAIF,cAAc,CAACI,UAAU,IAAI,IAAI,IAAIJ,cAAc,CAACK,UAAU,IAAI,IAAI,EAAE;IAC1EJ,cAAc,CAACE,IAAI,CAChB,aAAYH,cAAc,CAACI,UAAU,IAAI,CAAE,KAC1CJ,cAAc,CAACK,UAAU,IAAI,CAC9B,GAAE,CACJ;EACH;EACA,IAAIL,cAAc,CAACM,KAAK,IAAI,IAAI,EAAE;IAChCL,cAAc,CAACE,IAAI,CAAE,SAAQH,cAAc,CAACM,KAAM,GAAE,CAAC;EACvD;EACA,IAAIN,cAAc,CAACO,MAAM,IAAI,IAAI,IAAIP,cAAc,CAACQ,MAAM,IAAI,IAAI,EAAE;IAClEP,cAAc,CAACE,IAAI,CAChB,SAAQH,cAAc,CAACO,MAAM,IAAI,CAAE,KAAIP,cAAc,CAACQ,MAAM,IAAI,CAAE,GAAE,CACtE;EACH;EAEA,IAAIR,cAAc,CAACS,QAAQ,IAAI,IAAI,EAAE;IACnCR,cAAc,CAACE,IAAI,CAAE,UAASH,cAAc,CAACS,QAAS,GAAE,CAAC;EAC3D;EACA,IAAIT,cAAc,CAACU,KAAK,IAAI,IAAI,EAAE;IAChCT,cAAc,CAACE,IAAI,CAAE,SAAQH,cAAc,CAACU,KAAM,GAAE,CAAC;EACvD;EACA,IAAIV,cAAc,CAACW,KAAK,IAAI,IAAI,EAAE;IAChCV,cAAc,CAACE,IAAI,CAAE,SAAQH,cAAc,CAACW,KAAM,GAAE,CAAC;EACvD;EACA,OAAOV,cAAc;AACvB;AAEA,SAASW,kBAAkBA,CACzBC,SAAsC,EACtCxB,KAAiB,EACjB;EACA,IAAMY,cAAwB,GAAG,EAAE;EAEnCZ,KAAK,IAAIY,cAAc,CAACE,IAAI,CAAAW,KAAA,CAAnBb,cAAc,EAAAc,kBAAA,CAAShB,uBAAuB,CAACV,KAAK,CAAC,EAAC;EAE/D,IAAI2B,KAAK,CAACC,OAAO,CAACJ,SAAS,CAAC,EAAE;IAC5B,IAAI,OAAOA,SAAS,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;MACpCZ,cAAc,CAACE,IAAI,CAAE,UAASU,SAAS,CAACK,IAAI,CAAC,GAAG,CAAE,GAAE,CAAC;IACvD,CAAC,MAAM;MACL,IAAMC,gBAAgB,GAAGjC,sBAAsB,CAC7C2B,SAAS,CACV;MACDZ,cAAc,CAACE,IAAI,CAAAW,KAAA,CAAnBb,cAAc,EAAAc,kBAAA,CAAShB,uBAAuB,CAACoB,gBAAgB,CAAC,EAAC;IACnE;EACF,CAAC,MAAM,IAAI,OAAON,SAAS,KAAK,QAAQ,EAAE;IACxCZ,cAAc,CAACE,IAAI,CAACU,SAAS,CAAC;EAChC;EAEA,OAAOZ,cAAc,CAACmB,MAAM,GAAGnB,cAAc,CAACiB,IAAI,CAAC,GAAG,CAAC,GAAGG,SAAS;AACrE;AAWA,IAAMC,OAAO,GAAG,SAAVA,OAAOA,CACXC,IAAiB,EAEd;EAAA,IADHlC,KAAK,GAAAmC,SAAA,CAAAJ,MAAA,QAAAI,SAAA,QAAAH,SAAA,GAAAG,SAAA,MAAGD,IAAI,CAAClC,KAAK;EAElB,IACEwB,SAAS,GAaPxB,KAAK,CAbPwB,SAAS;IACTY,MAAM,GAYJpC,KAAK,CAZPoC,MAAM;IACNC,OAAO,GAWLrC,KAAK,CAXPqC,OAAO;IACPC,OAAO,GAULtC,KAAK,CAVPsC,OAAO;IACPC,UAAU,GASRvC,KAAK,CATPuC,UAAU;IACVC,QAAQ,GAQNxC,KAAK,CARPwC,QAAQ;IACRC,UAAU,GAORzC,KAAK,CAPPyC,UAAU;IACVC,SAAS,GAMP1C,KAAK,CANP0C,SAAS;IACTC,KAAK,GAKH3C,KAAK,CALP2C,KAAK;IACLC,YAAY,GAIV5C,KAAK,CAJP4C,YAAY;IACZC,iBAAiB,GAGf7C,KAAK,CAHP6C,iBAAiB;IACjBC,gBAAgB,GAEd9C,KAAK,CAFP8C,gBAAgB;IACbC,IAAA,GAAAC,wBAAA,CACDhD,KAAK,EAAAiD,SAAA;EAET,IAAMC,KAaL,GAAAC,aAAA,CAAAA,aAAA,KACKpD,oBAAoB,CAACC,KAAK,CAAC,GAC3B;IACEoD,yBAAyB,EACvBlB,IAAI,CAACmB,sCAAsC;IAC7CC,6BAA6B,EAC3BpB,IAAI,CAACqB,0CAA0C;IACjDC,gBAAgB,EAAEtB,IAAI,CAACuB,6BAA6B;IACpDC,eAAe,EAAExB,IAAI,CAACyB,4BAA4B;IAClDC,kBAAkB,EAAE1B,IAAI,CAAC2B,+BAA+B;IACxDC,oBAAoB,EAAE5B,IAAI,CAAC6B;EAC7B,CAAC,GACD,IAAI,GACLhB,IAAA,CACJ;EAED,IAAIX,MAAM,IAAI,IAAI,EAAE;IAClBc,KAAK,CAAC,kBAAkB,CAAC,GAAGd,MAAM,CAAC4B,QAAQ,EAAE,CAACzD,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;EACjE,CAAC,MAAM,IAAI8B,OAAO,IAAI,IAAI,IAAIC,OAAO,IAAI,IAAI,EAAE;IAC7CY,KAAK,CAAC,kBAAkB,CAAC,GAAI,GAAEb,OAAO,IAAI,CAAE,IAAGC,OAAO,IAAI,CAAE,EAAC;EAC/D;EAEAY,KAAK,CAAC1B,SAAS,GAAGD,kBAAkB,CAACC,SAAS,EAAExB,KAAK,CAAC;EACtDkD,KAAK,CAACL,iBAAiB,GAAGtB,kBAAkB,CAACsB,iBAAiB,CAAC;EAC/DK,KAAK,CAACJ,gBAAgB,GAAGvB,kBAAkB,CAACuB,gBAAgB,CAAC;EAE7DI,KAAK,CAACe,GAAG,GAAI,UAAAC,EAAqB,EAAK;IACrChC,IAAI,CAACiC,UAAU,CAACC,OAAO,GAAGF,EAAE;IAC5B,IAAI,OAAOtB,YAAY,KAAK,UAAU,EAAE;MACtCA,YAAY,CAACsB,EAAE,CAAC;IAClB,CAAC,MAAM,IAAItB,YAAY,EAAE;MACvBA,YAAY,CAACwB,OAAO,GAAGF,EAAE;IAC3B;EACF,CAAC;EAED,IAAMG,MAKL,GAAG,CAAC,CAAC;EAEN,IAAI9B,UAAU,IAAI,IAAI,EAAE;IACtB8B,MAAM,CAAC9B,UAAU,GAAGA,UAAU;EAChC;EACA,IAAIC,QAAQ,IAAI,IAAI,EAAE;IACpB6B,MAAM,CAAC7B,QAAQ,GAAGA,QAAQ;EAC5B;EACA,IAAIC,UAAU,IAAI,IAAI,EAAE;IACtB4B,MAAM,CAAC5B,UAAU,GAAGA,UAAU;EAChC;EACA,IAAIC,SAAS,IAAI,IAAI,EAAE;IACrB2B,MAAM,CAAC3B,SAAS,GAAGA,SAAS;EAC9B;EACAQ,KAAK,CAACP,KAAK,GAAG/C,OAAO,CAAC+C,KAAK,EAAE0B,MAAM,CAAC;EAEpC,OAAOnB,KAAK;AACd,CAAC;AAED,IAAMoB,qBAAqB,GAAI,SAAzBA,qBAAqBA,CAAIC,IAAgB,EAAK;EAClD,IAAIA,IAAI,EAAE;IAER,IAAMC,SAAS,GAAGD,IAAI,CAACE,QAAQ,KAAK,CAAC;IAErC,IAAID,SAAS,IAAI,OAAOD,IAAI,CAACD,qBAAqB,KAAK,UAAU,EAAE;MAEjE,OAAOC,IAAI,CAACD,qBAAqB,EAAE;IACrC;EACF;AACF,CAAC;AAED,IAAMI,aAAa,GAAG,SAAhBA,aAAaA,CACjBH,IAAgB,EAChBI,QAOS,EACN;EAEH,IAAMC,YAAY,GAAGL,IAAI,IAAIA,IAAI,CAACM,UAAU;EAC5C,IAAID,YAAY,EAAE;IAChBE,UAAU,CAAC,YAAM;MAEf,IAAMC,YAAY,GAAGT,qBAAqB,CAACM,YAAY,CAAC;MACxD,IAAAI,qBAAA,GAAqCV,qBAAqB,CAACC,IAAI,CAAC;QAAxDU,MAAM,GAAAD,qBAAA,CAANC,MAAM;QAAEC,IAAI,GAAAF,qBAAA,CAAJE,IAAI;QAAEC,GAAG,GAAAH,qBAAA,CAAHG,GAAG;QAAEC,KAAA,GAAAJ,qBAAA,CAAAI,KAAA;MAC3B,IAAMC,CAAC,GAAGH,IAAI,GAAGH,YAAY,CAACG,IAAI;MAClC,IAAMI,CAAC,GAAGH,GAAG,GAAGJ,YAAY,CAACI,GAAG;MAChCR,QAAQ,CAACU,CAAC,EAAEC,CAAC,EAAEF,KAAK,EAAEH,MAAM,EAAEC,IAAI,EAAEC,GAAG,CAAC;IAC1C,CAAC,EAAE,CAAC,CAAC;EACP;AACF,CAAC;AAED,SAASI,SAASA,CAAA,EAAG;EAEnB,IAAMC,GAAG,GAAG,IAAI,CAACC,KAAK,CAACC,SAAS,CAACC,WAAW;EAC5C,IAAIH,GAAG,IAAI,IAAI,EAAE;IACf;EACF;EAEAd,aAAa,CAACc,GAAG,EAAE,IAAI,CAACI,kBAAkB,CAAC;AAC7C;AAEA,WAAaC,QAAQ,aAAAC,gBAAA;EA+DnB,SAAAD,SAAY7F,KAAQ,EAAE+F,OAAU,EAAE;IAAA,IAAAC,KAAA;IAAAC,eAAA,OAAAJ,QAAA;IAChCG,KAAA,GAAAE,UAAA,OAAAL,QAAA,GAAM7F,KAAK,EAAE+F,OAAO;IAACC,KAAA,CAtDvB7B,UAAU,GACR3E,KAAK,CAAC2G,SAAS,EAAc;IAAAH,KAAA,CAC/BI,eAAe,GAAe,CAAC,CAAC;IAuD9B,IAAIrG,oBAAoB,CAACC,KAAK,CAAC,EAAE;MAC/BL,iBAAiB,CAAAqG,KAAK,CAAC;IACzB;IAEAA,KAAA,CAAKK,6BAA6B,GAAGd,SAAS,CAACe,IAAI,CAAAN,KAAK,CAAC;IAAA,OAAAA,KAAA;EAC3D;EAAAO,SAAA,CAAAV,QAAA,EAAAC,gBAAA;EAAA,OAAAU,YAAA,CAAAX,QAAA;IAAAY,GAAA;IAAAC,KAAA,EAlEU,SAAAC,YAAYA,CAAC3G,KAAQ,EAAE;MAC/B,OAAOA,KAAK;IACd;EAAA;IAAAyG,GAAA;IAAAC,KAAA,EASA,SAAAE,cAAcA,CAAC5G,KAAmB,EAAE;MAClC,IAAM6G,MAAM,GAAGC,MAAM,CAACC,MAAM,CAC1B,CAAC,CAAC,EACF,IAAI,CAAC/G,KAAK,EACV,IAAI,CAACoG,eAAe,EACpBpG,KAAK,CAAC2C,KAAK,CACZ;MACD,IAAI,CAACyD,eAAe,GAAGS,MAAM;MAC7B,IAAM3D,KAAK,GAAGjB,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC0E,YAAY,CAACE,MAAM,CAAC,CAAC;MACtD,IAAMzC,OAAO,GAAG,IAAI,CAACD,UAAU,CAACC,OAAO;MACvC,IAAIA,OAAO,EAAE;QACX,KAAK,IAAM4C,cAAc,IAAIF,MAAM,CAACG,IAAI,CAAC/D,KAAK,CAAC,EAAE;UAC/C,IAAMgE,UAAU,GAAGhE,KAAK,CAAC8D,cAAc,CAAuB;UAC9D,QAAQA,cAAc;YACpB,KAAK,KAAK;YACV,KAAK,UAAU;cACb;YACF,KAAK,OAAO;cAEV,KAAK,IAAMG,YAAY,IAAK,EAAE,CAAUC,MAAM,EAAAC,YAAA,GAACnE,KAAK,CAACP,KAAK,YAAA0E,YAAA,GAAI,EAAE,CAAC,EAAE;gBAAA,IAAAA,YAAA;gBAEjEP,MAAM,CAACC,MAAM,CAAC3C,OAAO,CAACzB,KAAK,EAAEwE,YAAY,CAAC;cAC5C;cACA;YACF;cAIE/C,OAAO,CAACkD,YAAY,CAACjH,iBAAiB,CAAC2G,cAAc,CAAC,EAAEE,UAAU,CAAC;cACnE;UAAM;QAEZ;MACF;IACF;EAAA;IAAAT,GAAA;IAAAC,KAAA,EAwBA,SAAAa,MAAMA,CAAA,EAAgB;MACpB,IAAI,CAAC,IAAI,CAAC/B,GAAG,EAAE;QACb,MAAM,IAAIgC,KAAK,CACb,2EAA2E,CAC5E;MACH;MACA,IAAI,CAACpB,eAAe,GAAG,CAAC,CAAC;MACzB,OAAOtG,aAAa,CAClB,IAAI,CAAC0F,GAAG,EACRvD,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC0E,YAAY,CAAC,IAAI,CAAC3G,KAAK,CAAC,CAAC,CAC7C;IACH;EAAA;AAAA,EAlFQR,KAAK,CAACiI,SAAS;AAqFzB,WAAaC,MAAM,aAAAC,UAAA;EAAA,SAAAD,OAAA;IAAA,IAAAE,MAAA;IAAA3B,eAAA,OAAAyB,MAAA;IAAA,SAAAG,IAAA,GAAA1F,SAAA,CAAAJ,MAAA,EAAA+F,IAAA,OAAAnG,KAAA,CAAAkG,IAAA,GAAAE,IAAA,MAAAA,IAAA,GAAAF,IAAA,EAAAE,IAAA;MAAAD,IAAA,CAAAC,IAAA,IAAA5F,SAAA,CAAA4F,IAAA;IAAA;IAAAH,MAAA,GAAA1B,UAAA,OAAAwB,MAAA,KAAAN,MAAA,CAAAU,IAAA;IAAAF,MAAA,CACjBpC,GAAG,GAAG,QAAQ;IAAA,OAAAoC,MAAA;EAAA;EAAArB,SAAA,CAAAmB,MAAA,EAAAC,UAAA;EAAA,OAAAnB,YAAA,CAAAkB,MAAA;AAAA,EADY7B,QAAQ;AAIpC,WAAamC,QAAQ,aAAAC,UAAA;EAAA,SAAAD,SAAA;IAAA,IAAAE,MAAA;IAAAjC,eAAA,OAAA+B,QAAA;IAAA,SAAAG,KAAA,GAAAhG,SAAA,CAAAJ,MAAA,EAAA+F,IAAA,OAAAnG,KAAA,CAAAwG,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAAN,IAAA,CAAAM,KAAA,IAAAjG,SAAA,CAAAiG,KAAA;IAAA;IAAAF,MAAA,GAAAhC,UAAA,OAAA8B,QAAA,KAAAZ,MAAA,CAAAU,IAAA;IAAAI,MAAA,CACnB1C,GAAG,GAAG,UAAU;IAAA,OAAA0C,MAAA;EAAA;EAAA3B,SAAA,CAAAyB,QAAA,EAAAC,UAAA;EAAA,OAAAzB,YAAA,CAAAwB,QAAA;AAAA,EADYnC,QAAQ;AAItC,WAAawC,IAAI,aAAAC,UAAA;EAAA,SAAAD,KAAA;IAAA,IAAAE,MAAA;IAAAtC,eAAA,OAAAoC,IAAA;IAAA,SAAAG,KAAA,GAAArG,SAAA,CAAAJ,MAAA,EAAA+F,IAAA,OAAAnG,KAAA,CAAA6G,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAAX,IAAA,CAAAW,KAAA,IAAAtG,SAAA,CAAAsG,KAAA;IAAA;IAAAF,MAAA,GAAArC,UAAA,OAAAmC,IAAA,KAAAjB,MAAA,CAAAU,IAAA;IAAAS,MAAA,CACf/C,GAAG,GAAG,MAAM;IAAA,OAAA+C,MAAA;EAAA;EAAAhC,SAAA,CAAA8B,IAAA,EAAAC,UAAA;EAAA,OAAA9B,YAAA,CAAA6B,IAAA;AAAA,EADYxC,QAAQ;AAIlC,WAAa6C,OAAO,aAAAC,UAAA;EAAA,SAAAD,QAAA;IAAA,IAAAE,MAAA;IAAA3C,eAAA,OAAAyC,OAAA;IAAA,SAAAG,KAAA,GAAA1G,SAAA,CAAAJ,MAAA,EAAA+F,IAAA,OAAAnG,KAAA,CAAAkH,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAAhB,IAAA,CAAAgB,KAAA,IAAA3G,SAAA,CAAA2G,KAAA;IAAA;IAAAF,MAAA,GAAA1C,UAAA,OAAAwC,OAAA,KAAAtB,MAAA,CAAAU,IAAA;IAAAc,MAAA,CAClBpD,GAAG,GAAG,SAAS;IAAA,OAAAoD,MAAA;EAAA;EAAArC,SAAA,CAAAmC,OAAA,EAAAC,UAAA;EAAA,OAAAnC,YAAA,CAAAkC,OAAA;AAAA,EADY7C,QAAQ;AAIrC,WAAakD,CAAC,aAAAC,UAAA;EAAA,SAAAD,EAAA;IAAA,IAAAE,MAAA;IAAAhD,eAAA,OAAA8C,CAAA;IAAA,SAAAG,KAAA,GAAA/G,SAAA,CAAAJ,MAAA,EAAA+F,IAAA,OAAAnG,KAAA,CAAAuH,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAArB,IAAA,CAAAqB,KAAA,IAAAhH,SAAA,CAAAgH,KAAA;IAAA;IAAAF,MAAA,GAAA/C,UAAA,OAAA6C,CAAA,KAAA3B,MAAA,CAAAU,IAAA;IAAAmB,MAAA,CAOZzD,GAAG,GAAG,GAAG;IAAA,OAAAyD,MAAA;EAAA;EAAA1C,SAAA,CAAAwC,CAAA,EAAAC,UAAA;EAAA,OAAAxC,YAAA,CAAAuC,CAAA;IAAAtC,GAAA;IAAAC,KAAA,EACT,SAAAC,YAAYA,CACV3G,KAIC,EACD;MACA,IAAQqF,CAAC,GAAiBrF,KAAK,CAAvBqF,CAAC;QAAEC,CAAC,GAActF,KAAK,CAApBsF,CAAC;QAAKvC,IAAA,GAAAC,wBAAA,CAAShD,KAAK,EAAAoJ,UAAA;MAE/B,IAAI,CAAC/D,CAAC,IAAIC,CAAC,KAAK,CAACvC,IAAI,CAAClC,SAAS,EAAE;QAC/BkC,IAAI,CAAClC,SAAS,GAAI,GAAEwE,CAAC,IAAI,CAAE,KAAIC,CAAC,IAAI,CAAE,EAAC;MACzC;MAEA,OAAOvC,IAAI;IACb;EAAA;AAAA,EAtBqB8C,QAAQ;AAyB/B,WAAawD,KAAK,aAAAC,UAAA;EAAA,SAAAD,MAAA;IAAA,IAAAE,MAAA;IAAAtD,eAAA,OAAAoD,KAAA;IAAA,SAAAG,KAAA,GAAArH,SAAA,CAAAJ,MAAA,EAAA+F,IAAA,OAAAnG,KAAA,CAAA6H,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAA3B,IAAA,CAAA2B,KAAA,IAAAtH,SAAA,CAAAsH,KAAA;IAAA;IAAAF,MAAA,GAAArD,UAAA,OAAAmD,KAAA,KAAAjC,MAAA,CAAAU,IAAA;IAAAyB,MAAA,CAChB/D,GAAG,GAAG,OAAO;IAAA,OAAA+D,MAAA;EAAA;EAAAhD,SAAA,CAAA8C,KAAA,EAAAC,UAAA;EAAA,OAAA9C,YAAA,CAAA6C,KAAA;AAAA,EADYxD,QAAQ;AAInC,WAAa6D,IAAI,aAAAC,UAAA;EAAA,SAAAD,KAAA;IAAA,IAAAE,MAAA;IAAA3D,eAAA,OAAAyD,IAAA;IAAA,SAAAG,KAAA,GAAA1H,SAAA,CAAAJ,MAAA,EAAA+F,IAAA,OAAAnG,KAAA,CAAAkI,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAAhC,IAAA,CAAAgC,KAAA,IAAA3H,SAAA,CAAA2H,KAAA;IAAA;IAAAF,MAAA,GAAA1D,UAAA,OAAAwD,IAAA,KAAAtC,MAAA,CAAAU,IAAA;IAAA8B,MAAA,CACfpE,GAAG,GAAG,MAAM;IAAA,OAAAoE,MAAA;EAAA;EAAArD,SAAA,CAAAmD,IAAA,EAAAC,UAAA;EAAA,OAAAnD,YAAA,CAAAkD,IAAA;AAAA,EADY7D,QAAQ;AAIlC,WAAakE,cAAc,aAAAC,UAAA;EAAA,SAAAD,eAAA;IAAA,IAAAE,MAAA;IAAAhE,eAAA,OAAA8D,cAAA;IAAA,SAAAG,KAAA,GAAA/H,SAAA,CAAAJ,MAAA,EAAA+F,IAAA,OAAAnG,KAAA,CAAAuI,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAArC,IAAA,CAAAqC,KAAA,IAAAhI,SAAA,CAAAgI,KAAA;IAAA;IAAAF,MAAA,GAAA/D,UAAA,OAAA6D,cAAA,KAAA3C,MAAA,CAAAU,IAAA;IAAAmC,MAAA,CACzBzE,GAAG,GAAG,gBAAgB;IAAA,OAAAyE,MAAA;EAAA;EAAA1D,SAAA,CAAAwD,cAAA,EAAAC,UAAA;EAAA,OAAAxD,YAAA,CAAAuD,cAAA;AAAA,EADYlE,QAAQ;AAI5C,WAAauE,IAAI,aAAAC,UAAA;EAAA,SAAAD,KAAA;IAAA,IAAAE,MAAA;IAAArE,eAAA,OAAAmE,IAAA;IAAA,SAAAG,KAAA,GAAApI,SAAA,CAAAJ,MAAA,EAAA+F,IAAA,OAAAnG,KAAA,CAAA4I,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAA1C,IAAA,CAAA0C,KAAA,IAAArI,SAAA,CAAAqI,KAAA;IAAA;IAAAF,MAAA,GAAApE,UAAA,OAAAkE,IAAA,KAAAhD,MAAA,CAAAU,IAAA;IAAAwC,MAAA,CACf9E,GAAG,GAAG,MAAM;IAAA,OAAA8E,MAAA;EAAA;EAAA/D,SAAA,CAAA6D,IAAA,EAAAC,UAAA;EAAA,OAAA7D,YAAA,CAAA4D,IAAA;AAAA,EADYvE,QAAQ;AAIlC,WAAa4E,OAAO,aAAAC,UAAA;EAAA,SAAAD,QAAA;IAAA,IAAAE,MAAA;IAAA1E,eAAA,OAAAwE,OAAA;IAAA,SAAAG,KAAA,GAAAzI,SAAA,CAAAJ,MAAA,EAAA+F,IAAA,OAAAnG,KAAA,CAAAiJ,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAA/C,IAAA,CAAA+C,KAAA,IAAA1I,SAAA,CAAA0I,KAAA;IAAA;IAAAF,MAAA,GAAAzE,UAAA,OAAAuE,OAAA,KAAArD,MAAA,CAAAU,IAAA;IAAA6C,MAAA,CAClBnF,GAAG,GAAG,SAAS;IAAA,OAAAmF,MAAA;EAAA;EAAApE,SAAA,CAAAkE,OAAA,EAAAC,UAAA;EAAA,OAAAlE,YAAA,CAAAiE,OAAA;AAAA,EADY5E,QAAQ;AAIrC,WAAaiF,QAAQ,aAAAC,WAAA;EAAA,SAAAD,SAAA;IAAA,IAAAE,OAAA;IAAA/E,eAAA,OAAA6E,QAAA;IAAA,SAAAG,KAAA,GAAA9I,SAAA,CAAAJ,MAAA,EAAA+F,IAAA,OAAAnG,KAAA,CAAAsJ,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAApD,IAAA,CAAAoD,KAAA,IAAA/I,SAAA,CAAA+I,KAAA;IAAA;IAAAF,OAAA,GAAA9E,UAAA,OAAA4E,QAAA,KAAA1D,MAAA,CAAAU,IAAA;IAAAkD,OAAA,CACnBxF,GAAG,GAAG,UAAU;IAAA,OAAAwF,OAAA;EAAA;EAAAzE,SAAA,CAAAuE,QAAA,EAAAC,WAAA;EAAA,OAAAvE,YAAA,CAAAsE,QAAA;AAAA,EADYjF,QAAQ;AAItC,WAAasF,cAAc,aAAAC,WAAA;EAAA,SAAAD,eAAA;IAAA,IAAAE,OAAA;IAAApF,eAAA,OAAAkF,cAAA;IAAA,SAAAG,MAAA,GAAAnJ,SAAA,CAAAJ,MAAA,EAAA+F,IAAA,OAAAnG,KAAA,CAAA2J,MAAA,GAAAC,MAAA,MAAAA,MAAA,GAAAD,MAAA,EAAAC,MAAA;MAAAzD,IAAA,CAAAyD,MAAA,IAAApJ,SAAA,CAAAoJ,MAAA;IAAA;IAAAF,OAAA,GAAAnF,UAAA,OAAAiF,cAAA,KAAA/D,MAAA,CAAAU,IAAA;IAAAuD,OAAA,CACzB7F,GAAG,GAAG,gBAAgB;IAAA,OAAA6F,OAAA;EAAA;EAAA9E,SAAA,CAAA4E,cAAA,EAAAC,WAAA;EAAA,OAAA5E,YAAA,CAAA2E,cAAA;AAAA,EADYtF,QAAQ;AAI5C,WAAa2F,IAAI,aAAAC,WAAA;EAAA,SAAAD,KAAA;IAAA,IAAAE,OAAA;IAAAzF,eAAA,OAAAuF,IAAA;IAAA,SAAAG,MAAA,GAAAxJ,SAAA,CAAAJ,MAAA,EAAA+F,IAAA,OAAAnG,KAAA,CAAAgK,MAAA,GAAAC,MAAA,MAAAA,MAAA,GAAAD,MAAA,EAAAC,MAAA;MAAA9D,IAAA,CAAA8D,MAAA,IAAAzJ,SAAA,CAAAyJ,MAAA;IAAA;IAAAF,OAAA,GAAAxF,UAAA,OAAAsF,IAAA,KAAApE,MAAA,CAAAU,IAAA;IAAA4D,OAAA,CACflG,GAAG,GAAG,MAAM;IAAA,OAAAkG,OAAA;EAAA;EAAAnF,SAAA,CAAAiF,IAAA,EAAAC,WAAA;EAAA,OAAAjF,YAAA,CAAAgF,IAAA;AAAA,EADY3F,QAAQ;AAIlC,WAAagG,IAAI,aAAAC,WAAA;EAAA,SAAAD,KAAA;IAAA,IAAAE,OAAA;IAAA9F,eAAA,OAAA4F,IAAA;IAAA,SAAAG,MAAA,GAAA7J,SAAA,CAAAJ,MAAA,EAAA+F,IAAA,OAAAnG,KAAA,CAAAqK,MAAA,GAAAC,MAAA,MAAAA,MAAA,GAAAD,MAAA,EAAAC,MAAA;MAAAnE,IAAA,CAAAmE,MAAA,IAAA9J,SAAA,CAAA8J,MAAA;IAAA;IAAAF,OAAA,GAAA7F,UAAA,OAAA2F,IAAA,KAAAzE,MAAA,CAAAU,IAAA;IAAAiE,OAAA,CACfvG,GAAG,GAAG,MAAM;IAAA,OAAAuG,OAAA;EAAA;EAAAxF,SAAA,CAAAsF,IAAA,EAAAC,WAAA;EAAA,OAAAtF,YAAA,CAAAqF,IAAA;AAAA,EADYhG,QAAQ;AAIlC,WAAaqG,GAAG,aAAAC,WAAA;EAAA,SAAAD,IAAA;IAAA,IAAAE,OAAA;IAAAnG,eAAA,OAAAiG,GAAA;IAAA,SAAAG,MAAA,GAAAlK,SAAA,CAAAJ,MAAA,EAAA+F,IAAA,OAAAnG,KAAA,CAAA0K,MAAA,GAAAC,MAAA,MAAAA,MAAA,GAAAD,MAAA,EAAAC,MAAA;MAAAxE,IAAA,CAAAwE,MAAA,IAAAnK,SAAA,CAAAmK,MAAA;IAAA;IAAAF,OAAA,GAAAlG,UAAA,OAAAgG,GAAA,KAAA9E,MAAA,CAAAU,IAAA;IAAAsE,OAAA,CACd5G,GAAG,GAAG,KAAK;IAAA,OAAA4G,OAAA;EAAA;EAAA7F,SAAA,CAAA2F,GAAA,EAAAC,WAAA;EAAA,OAAA3F,YAAA,CAAA0F,GAAA;AAAA,EADYrG,QAAQ;AAIjC,WAAa0G,MAAM,aAAAC,WAAA;EAAA,SAAAD,OAAA;IAAA,IAAAE,OAAA;IAAAxG,eAAA,OAAAsG,MAAA;IAAA,SAAAG,MAAA,GAAAvK,SAAA,CAAAJ,MAAA,EAAA+F,IAAA,OAAAnG,KAAA,CAAA+K,MAAA,GAAAC,MAAA,MAAAA,MAAA,GAAAD,MAAA,EAAAC,MAAA;MAAA7E,IAAA,CAAA6E,MAAA,IAAAxK,SAAA,CAAAwK,MAAA;IAAA;IAAAF,OAAA,GAAAvG,UAAA,OAAAqG,MAAA,KAAAnF,MAAA,CAAAU,IAAA;IAAA2E,OAAA,CACjBjH,GAAG,GAAG,QAAQ;IAAA,OAAAiH,OAAA;EAAA;EAAAlG,SAAA,CAAAgG,MAAA,EAAAC,WAAA;EAAA,OAAAhG,YAAA,CAAA+F,MAAA;AAAA,EADY1G,QAAQ;AAIpC,WAAa+G,IAAI,aAAAC,WAAA;EAAA,SAAAD,KAAA;IAAA,IAAAE,OAAA;IAAA7G,eAAA,OAAA2G,IAAA;IAAA,SAAAG,MAAA,GAAA5K,SAAA,CAAAJ,MAAA,EAAA+F,IAAA,OAAAnG,KAAA,CAAAoL,MAAA,GAAAC,MAAA,MAAAA,MAAA,GAAAD,MAAA,EAAAC,MAAA;MAAAlF,IAAA,CAAAkF,MAAA,IAAA7K,SAAA,CAAA6K,MAAA;IAAA;IAAAF,OAAA,GAAA5G,UAAA,OAAA0G,IAAA,KAAAxF,MAAA,CAAAU,IAAA;IAAAgF,OAAA,CACftH,GAAG,GAAG,MAAM;IAAA,OAAAsH,OAAA;EAAA;EAAAvG,SAAA,CAAAqG,IAAA,EAAAC,WAAA;EAAA,OAAArG,YAAA,CAAAoG,IAAA;AAAA,EADY/G,QAAQ;AAIlC,WAAaoH,KAAK,aAAAC,WAAA;EAAA,SAAAD,MAAA;IAAA,IAAAE,OAAA;IAAAlH,eAAA,OAAAgH,KAAA;IAAA,SAAAG,MAAA,GAAAjL,SAAA,CAAAJ,MAAA,EAAA+F,IAAA,OAAAnG,KAAA,CAAAyL,MAAA,GAAAC,MAAA,MAAAA,MAAA,GAAAD,MAAA,EAAAC,MAAA;MAAAvF,IAAA,CAAAuF,MAAA,IAAAlL,SAAA,CAAAkL,MAAA;IAAA;IAAAF,OAAA,GAAAjH,UAAA,OAAA+G,KAAA,KAAA7F,MAAA,CAAAU,IAAA;IAAAqF,OAAA,CAChB3H,GAAG,GAAG,OAAO;IAAA,OAAA2H,OAAA;EAAA;EAAA5G,SAAA,CAAA0G,KAAA,EAAAC,WAAA;EAAA,OAAA1G,YAAA,CAAAyG,KAAA;AAAA,EADYpH,QAAQ;AAInC,WAAayH,QAAQ,aAAAC,WAAA;EAAA,SAAAD,SAAA;IAAA,IAAAE,OAAA;IAAAvH,eAAA,OAAAqH,QAAA;IAAA,SAAAG,MAAA,GAAAtL,SAAA,CAAAJ,MAAA,EAAA+F,IAAA,OAAAnG,KAAA,CAAA8L,MAAA,GAAAC,MAAA,MAAAA,MAAA,GAAAD,MAAA,EAAAC,MAAA;MAAA5F,IAAA,CAAA4F,MAAA,IAAAvL,SAAA,CAAAuL,MAAA;IAAA;IAAAF,OAAA,GAAAtH,UAAA,OAAAoH,QAAA,KAAAlG,MAAA,CAAAU,IAAA;IAAA0F,OAAA,CACnBhI,GAAG,GAAG,UAAU;IAAA,OAAAgI,OAAA;EAAA;EAAAjH,SAAA,CAAA+G,QAAA,EAAAC,WAAA;EAAA,OAAA/G,YAAA,CAAA8G,QAAA;AAAA,EADYzH,QAAQ;AAItC,WAAa8H,GAAG,aAAAC,WAAA;EAAA,SAAAD,IAAA;IAAA,IAAAE,OAAA;IAAA5H,eAAA,OAAA0H,GAAA;IAAA,SAAAG,MAAA,GAAA3L,SAAA,CAAAJ,MAAA,EAAA+F,IAAA,OAAAnG,KAAA,CAAAmM,MAAA,GAAAC,MAAA,MAAAA,MAAA,GAAAD,MAAA,EAAAC,MAAA;MAAAjG,IAAA,CAAAiG,MAAA,IAAA5L,SAAA,CAAA4L,MAAA;IAAA;IAAAF,OAAA,GAAA3H,UAAA,OAAAyH,GAAA,KAAAvG,MAAA,CAAAU,IAAA;IAAA+F,OAAA,CACdrI,GAAG,GAAG,KAAK;IAAA,OAAAqI,OAAA;EAAA;EAAAtH,SAAA,CAAAoH,GAAA,EAAAC,WAAA;EAAA,OAAApH,YAAA,CAAAmH,GAAA;AAAA,EADY9H,QAAQ;AAIjC,WAAamI,IAAI,aAAAC,WAAA;EAAA,SAAAD,KAAA;IAAA,IAAAE,OAAA;IAAAjI,eAAA,OAAA+H,IAAA;IAAA,SAAAG,MAAA,GAAAhM,SAAA,CAAAJ,MAAA,EAAA+F,IAAA,OAAAnG,KAAA,CAAAwM,MAAA,GAAAC,MAAA,MAAAA,MAAA,GAAAD,MAAA,EAAAC,MAAA;MAAAtG,IAAA,CAAAsG,MAAA,IAAAjM,SAAA,CAAAiM,MAAA;IAAA;IAAAF,OAAA,GAAAhI,UAAA,OAAA8H,IAAA,KAAA5G,MAAA,CAAAU,IAAA;IAAAoG,OAAA,CACf1I,GAAG,GAAG,MAAM;IAAA,OAAA0I,OAAA;EAAA;EAAA3H,SAAA,CAAAyH,IAAA,EAAAC,WAAA;EAAA,OAAAzH,YAAA,CAAAwH,IAAA;AAAA,EADYnI,QAAQ;AAIlC,WAAawI,aAAa,aAAAC,WAAA;EAAA,SAAAD,cAAA;IAAA,IAAAE,OAAA;IAAAtI,eAAA,OAAAoI,aAAA;IAAA,SAAAG,MAAA,GAAArM,SAAA,CAAAJ,MAAA,EAAA+F,IAAA,OAAAnG,KAAA,CAAA6M,MAAA,GAAAC,MAAA,MAAAA,MAAA,GAAAD,MAAA,EAAAC,MAAA;MAAA3G,IAAA,CAAA2G,MAAA,IAAAtM,SAAA,CAAAsM,MAAA;IAAA;IAAAF,OAAA,GAAArI,UAAA,OAAAmI,aAAA,KAAAjH,MAAA,CAAAU,IAAA;IAAAyG,OAAA,CACxB/I,GAAG,GAAG,eAAe;IAAA,OAAA+I,OAAA;EAAA;EAAAhI,SAAA,CAAA8H,aAAA,EAAAC,WAAA;EAAA,OAAA9H,YAAA,CAAA6H,aAAA;AAAA,EADYxI,QAAQ;AAI3C,WAAa6I,MAAM,aAAAC,WAAA;EAAA,SAAAD,OAAA;IAAA,IAAAE,OAAA;IAAA3I,eAAA,OAAAyI,MAAA;IAAA,SAAAG,MAAA,GAAA1M,SAAA,CAAAJ,MAAA,EAAA+F,IAAA,OAAAnG,KAAA,CAAAkN,MAAA,GAAAC,MAAA,MAAAA,MAAA,GAAAD,MAAA,EAAAC,MAAA;MAAAhH,IAAA,CAAAgH,MAAA,IAAA3M,SAAA,CAAA2M,MAAA;IAAA;IAAAF,OAAA,GAAA1I,UAAA,OAAAwI,MAAA,KAAAtH,MAAA,CAAAU,IAAA;IAAA8G,OAAA,CACjBpJ,GAAG,GAAG,QAAQ;IAAA,OAAAoJ,OAAA;EAAA;EAAArI,SAAA,CAAAmI,MAAA,EAAAC,WAAA;EAAA,OAAAnI,YAAA,CAAAkI,MAAA;AAAA,EADY7I,QAAQ;AAIpC,WAAakJ,OAAO,aAAAC,WAAA;EAAA,SAAAD,QAAA;IAAA,IAAAE,OAAA;IAAAhJ,eAAA,OAAA8I,OAAA;IAAA,SAAAG,MAAA,GAAA/M,SAAA,CAAAJ,MAAA,EAAA+F,IAAA,OAAAnG,KAAA,CAAAuN,MAAA,GAAAC,MAAA,MAAAA,MAAA,GAAAD,MAAA,EAAAC,MAAA;MAAArH,IAAA,CAAAqH,MAAA,IAAAhN,SAAA,CAAAgN,MAAA;IAAA;IAAAF,OAAA,GAAA/I,UAAA,OAAA6I,OAAA,KAAA3H,MAAA,CAAAU,IAAA;IAAAmH,OAAA,CAClBzJ,GAAG,GAAG,SAAS;IAAA,OAAAyJ,OAAA;EAAA;EAAA1I,SAAA,CAAAwI,OAAA,EAAAC,WAAA;EAAA,OAAAxI,YAAA,CAAAuI,OAAA;AAAA,EADYlJ,QAAQ;AAIrC,eAAeqG,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}