{"ast": null, "code": "var DEG_TO_RAD = Math.PI / 180;\nexport var identity = [1, 0, 0, 1, 0, 0];\nvar a = 1;\nvar b = 0;\nvar c = 0;\nvar d = 1;\nvar tx = 0;\nvar ty = 0;\nvar hasInitialState = true;\nexport function reset() {\n  if (hasInitialState) {\n    return;\n  }\n  a = d = 1;\n  b = c = tx = ty = 0;\n  hasInitialState = true;\n}\nexport function toArray() {\n  if (hasInitialState) {\n    return identity;\n  }\n  return [a, b, c, d, tx, ty];\n}\nexport function append(a2, b2, c2, d2, tx2, ty2) {\n  var change = a2 !== 1 || b2 !== 0 || c2 !== 0 || d2 !== 1;\n  var translate = tx2 !== 0 || ty2 !== 0;\n  if (!change && !translate) {\n    return;\n  }\n  if (hasInitialState) {\n    hasInitialState = false;\n    a = a2;\n    b = b2;\n    c = c2;\n    d = d2;\n    tx = tx2;\n    ty = ty2;\n    return;\n  }\n  var a1 = a;\n  var b1 = b;\n  var c1 = c;\n  var d1 = d;\n  if (change) {\n    a = a1 * a2 + c1 * b2;\n    b = b1 * a2 + d1 * b2;\n    c = a1 * c2 + c1 * d2;\n    d = b1 * c2 + d1 * d2;\n  }\n  if (translate) {\n    tx = a1 * tx2 + c1 * ty2 + tx;\n    ty = b1 * tx2 + d1 * ty2 + ty;\n  }\n}\nexport function appendTransform(x, y, scaleX, scaleY, rotation, skewX, skewY, regX, regY) {\n  if (x === 0 && y === 0 && scaleX === 1 && scaleY === 1 && rotation === 0 && skewX === 0 && skewY === 0 && regX === 0 && regY === 0) {\n    return;\n  }\n  var cos, sin;\n  if (rotation % 360) {\n    var r = rotation * DEG_TO_RAD;\n    cos = Math.cos(r);\n    sin = Math.sin(r);\n  } else {\n    cos = 1;\n    sin = 0;\n  }\n  var a2 = cos * scaleX;\n  var b2 = sin * scaleX;\n  var c2 = -sin * scaleY;\n  var d2 = cos * scaleY;\n  if (skewX || skewY) {\n    var b1 = Math.tan(skewY * DEG_TO_RAD);\n    var c1 = Math.tan(skewX * DEG_TO_RAD);\n    append(a2 + c1 * b2, b1 * a2 + b2, c2 + c1 * d2, b1 * c2 + d2, x, y);\n  } else {\n    append(a2, b2, c2, d2, x, y);\n  }\n  if (regX || regY) {\n    tx -= regX * a + regY * c;\n    ty -= regX * b + regY * d;\n    hasInitialState = false;\n  }\n}", "map": {"version": 3, "names": ["DEG_TO_RAD", "Math", "PI", "identity", "a", "b", "c", "d", "tx", "ty", "hasInitialState", "reset", "toArray", "append", "a2", "b2", "c2", "d2", "tx2", "ty2", "change", "translate", "a1", "b1", "c1", "d1", "appendTransform", "x", "y", "scaleX", "scaleY", "rotation", "skewX", "skewY", "regX", "regY", "cos", "sin", "r", "tan"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\react-native-svg\\src\\lib\\Matrix2D.ts"], "sourcesContent": ["/**\n * based on\n * https://github.com/CreateJS/EaselJS/blob/631cdffb85eff9413dab43b4676f059b4232d291/src/easeljs/geom/Matrix2D.js\n */\nconst DEG_TO_RAD = Math.PI / 180;\n\nexport const identity: [number, number, number, number, number, number] = [\n  1, 0, 0, 1, 0, 0,\n];\n\nlet a = 1;\nlet b = 0;\nlet c = 0;\nlet d = 1;\nlet tx = 0;\nlet ty = 0;\nlet hasInitialState = true;\n\n/**\n * Represents an affine transformation matrix, and provides tools for concatenating transforms.\n *\n * This matrix can be visualized as:\n *\n * \t[ a  c  tx\n * \t  b  d  ty\n * \t  0  0  1  ]\n *\n * Note the locations of b and c.\n **/\n\n/**\n * Reset current matrix to an identity matrix.\n * @method reset\n **/\nexport function reset() {\n  if (hasInitialState) {\n    return;\n  }\n  a = d = 1;\n  b = c = tx = ty = 0;\n  hasInitialState = true;\n}\n\n/**\n * Returns an array with current matrix values.\n * @method toArray\n * @return {Array} an array with current matrix values.\n **/\nexport function toArray(): [number, number, number, number, number, number] {\n  if (hasInitialState) {\n    return identity;\n  }\n  return [a, b, c, d, tx, ty];\n}\n\n/**\n * Appends the specified matrix properties to this matrix. All parameters are required.\n * This is the equivalent of multiplying `(this matrix) * (specified matrix)`.\n * @method append\n * @param {Number} a2\n * @param {Number} b2\n * @param {Number} c2\n * @param {Number} d2\n * @param {Number} tx2\n * @param {Number} ty2\n **/\nexport function append(\n  a2: number,\n  b2: number,\n  c2: number,\n  d2: number,\n  tx2: number,\n  ty2: number,\n) {\n  const change = a2 !== 1 || b2 !== 0 || c2 !== 0 || d2 !== 1;\n  const translate = tx2 !== 0 || ty2 !== 0;\n  if (!change && !translate) {\n    return;\n  }\n  if (hasInitialState) {\n    hasInitialState = false;\n    a = a2;\n    b = b2;\n    c = c2;\n    d = d2;\n    tx = tx2;\n    ty = ty2;\n    return;\n  }\n  const a1 = a;\n  const b1 = b;\n  const c1 = c;\n  const d1 = d;\n  if (change) {\n    a = a1 * a2 + c1 * b2;\n    b = b1 * a2 + d1 * b2;\n    c = a1 * c2 + c1 * d2;\n    d = b1 * c2 + d1 * d2;\n  }\n  if (translate) {\n    tx = a1 * tx2 + c1 * ty2 + tx;\n    ty = b1 * tx2 + d1 * ty2 + ty;\n  }\n}\n\n/**\n * Generates matrix properties from the specified display object transform properties, and appends them to this matrix.\n * For example, you can use this to generate a matrix representing the transformations of a display object:\n *\n * \treset();\n * \tappendTransform(o.x, o.y, o.scaleX, o.scaleY, o.rotation);\n * \tvar matrix = toArray()\n *\n * @method appendTransform\n * @param {Number} x\n * @param {Number} y\n * @param {Number} scaleX\n * @param {Number} scaleY\n * @param {Number} rotation\n * @param {Number} skewX\n * @param {Number} skewY\n * @param {Number} regX Optional.\n * @param {Number} regY Optional.\n **/\nexport function appendTransform(\n  x: number,\n  y: number,\n  scaleX: number,\n  scaleY: number,\n  rotation: number,\n  skewX: number,\n  skewY: number,\n  regX: number,\n  regY: number,\n) {\n  if (\n    x === 0 &&\n    y === 0 &&\n    scaleX === 1 &&\n    scaleY === 1 &&\n    rotation === 0 &&\n    skewX === 0 &&\n    skewY === 0 &&\n    regX === 0 &&\n    regY === 0\n  ) {\n    return;\n  }\n  let cos, sin;\n  if (rotation % 360) {\n    const r = rotation * DEG_TO_RAD;\n    cos = Math.cos(r);\n    sin = Math.sin(r);\n  } else {\n    cos = 1;\n    sin = 0;\n  }\n\n  const a2 = cos * scaleX;\n  const b2 = sin * scaleX;\n  const c2 = -sin * scaleY;\n  const d2 = cos * scaleY;\n\n  if (skewX || skewY) {\n    const b1 = Math.tan(skewY * DEG_TO_RAD);\n    const c1 = Math.tan(skewX * DEG_TO_RAD);\n    append(a2 + c1 * b2, b1 * a2 + b2, c2 + c1 * d2, b1 * c2 + d2, x, y);\n  } else {\n    append(a2, b2, c2, d2, x, y);\n  }\n\n  if (regX || regY) {\n    // append the registration offset:\n    tx -= regX * a + regY * c;\n    ty -= regX * b + regY * d;\n    hasInitialState = false;\n  }\n}\n"], "mappings": "AAIA,IAAMA,UAAU,GAAGC,IAAI,CAACC,EAAE,GAAG,GAAG;AAEhC,OAAO,IAAMC,QAA0D,GAAG,CACxE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CACjB;AAED,IAAIC,CAAC,GAAG,CAAC;AACT,IAAIC,CAAC,GAAG,CAAC;AACT,IAAIC,CAAC,GAAG,CAAC;AACT,IAAIC,CAAC,GAAG,CAAC;AACT,IAAIC,EAAE,GAAG,CAAC;AACV,IAAIC,EAAE,GAAG,CAAC;AACV,IAAIC,eAAe,GAAG,IAAI;AAkB1B,OAAO,SAASC,KAAKA,CAAA,EAAG;EACtB,IAAID,eAAe,EAAE;IACnB;EACF;EACAN,CAAC,GAAGG,CAAC,GAAG,CAAC;EACTF,CAAC,GAAGC,CAAC,GAAGE,EAAE,GAAGC,EAAE,GAAG,CAAC;EACnBC,eAAe,GAAG,IAAI;AACxB;AAOA,OAAO,SAASE,OAAOA,CAAA,EAAqD;EAC1E,IAAIF,eAAe,EAAE;IACnB,OAAOP,QAAQ;EACjB;EACA,OAAO,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,EAAE,EAAEC,EAAE,CAAC;AAC7B;AAaA,OAAO,SAASI,MAAMA,CACpBC,EAAU,EACVC,EAAU,EACVC,EAAU,EACVC,EAAU,EACVC,GAAW,EACXC,GAAW,EACX;EACA,IAAMC,MAAM,GAAGN,EAAE,KAAK,CAAC,IAAIC,EAAE,KAAK,CAAC,IAAIC,EAAE,KAAK,CAAC,IAAIC,EAAE,KAAK,CAAC;EAC3D,IAAMI,SAAS,GAAGH,GAAG,KAAK,CAAC,IAAIC,GAAG,KAAK,CAAC;EACxC,IAAI,CAACC,MAAM,IAAI,CAACC,SAAS,EAAE;IACzB;EACF;EACA,IAAIX,eAAe,EAAE;IACnBA,eAAe,GAAG,KAAK;IACvBN,CAAC,GAAGU,EAAE;IACNT,CAAC,GAAGU,EAAE;IACNT,CAAC,GAAGU,EAAE;IACNT,CAAC,GAAGU,EAAE;IACNT,EAAE,GAAGU,GAAG;IACRT,EAAE,GAAGU,GAAG;IACR;EACF;EACA,IAAMG,EAAE,GAAGlB,CAAC;EACZ,IAAMmB,EAAE,GAAGlB,CAAC;EACZ,IAAMmB,EAAE,GAAGlB,CAAC;EACZ,IAAMmB,EAAE,GAAGlB,CAAC;EACZ,IAAIa,MAAM,EAAE;IACVhB,CAAC,GAAGkB,EAAE,GAAGR,EAAE,GAAGU,EAAE,GAAGT,EAAE;IACrBV,CAAC,GAAGkB,EAAE,GAAGT,EAAE,GAAGW,EAAE,GAAGV,EAAE;IACrBT,CAAC,GAAGgB,EAAE,GAAGN,EAAE,GAAGQ,EAAE,GAAGP,EAAE;IACrBV,CAAC,GAAGgB,EAAE,GAAGP,EAAE,GAAGS,EAAE,GAAGR,EAAE;EACvB;EACA,IAAII,SAAS,EAAE;IACbb,EAAE,GAAGc,EAAE,GAAGJ,GAAG,GAAGM,EAAE,GAAGL,GAAG,GAAGX,EAAE;IAC7BC,EAAE,GAAGc,EAAE,GAAGL,GAAG,GAAGO,EAAE,GAAGN,GAAG,GAAGV,EAAE;EAC/B;AACF;AAqBA,OAAO,SAASiB,eAAeA,CAC7BC,CAAS,EACTC,CAAS,EACTC,MAAc,EACdC,MAAc,EACdC,QAAgB,EAChBC,KAAa,EACbC,KAAa,EACbC,IAAY,EACZC,IAAY,EACZ;EACA,IACER,CAAC,KAAK,CAAC,IACPC,CAAC,KAAK,CAAC,IACPC,MAAM,KAAK,CAAC,IACZC,MAAM,KAAK,CAAC,IACZC,QAAQ,KAAK,CAAC,IACdC,KAAK,KAAK,CAAC,IACXC,KAAK,KAAK,CAAC,IACXC,IAAI,KAAK,CAAC,IACVC,IAAI,KAAK,CAAC,EACV;IACA;EACF;EACA,IAAIC,GAAG,EAAEC,GAAG;EACZ,IAAIN,QAAQ,GAAG,GAAG,EAAE;IAClB,IAAMO,CAAC,GAAGP,QAAQ,GAAG/B,UAAU;IAC/BoC,GAAG,GAAGnC,IAAI,CAACmC,GAAG,CAACE,CAAC,CAAC;IACjBD,GAAG,GAAGpC,IAAI,CAACoC,GAAG,CAACC,CAAC,CAAC;EACnB,CAAC,MAAM;IACLF,GAAG,GAAG,CAAC;IACPC,GAAG,GAAG,CAAC;EACT;EAEA,IAAMvB,EAAE,GAAGsB,GAAG,GAAGP,MAAM;EACvB,IAAMd,EAAE,GAAGsB,GAAG,GAAGR,MAAM;EACvB,IAAMb,EAAE,GAAG,CAACqB,GAAG,GAAGP,MAAM;EACxB,IAAMb,EAAE,GAAGmB,GAAG,GAAGN,MAAM;EAEvB,IAAIE,KAAK,IAAIC,KAAK,EAAE;IAClB,IAAMV,EAAE,GAAGtB,IAAI,CAACsC,GAAG,CAACN,KAAK,GAAGjC,UAAU,CAAC;IACvC,IAAMwB,EAAE,GAAGvB,IAAI,CAACsC,GAAG,CAACP,KAAK,GAAGhC,UAAU,CAAC;IACvCa,MAAM,CAACC,EAAE,GAAGU,EAAE,GAAGT,EAAE,EAAEQ,EAAE,GAAGT,EAAE,GAAGC,EAAE,EAAEC,EAAE,GAAGQ,EAAE,GAAGP,EAAE,EAAEM,EAAE,GAAGP,EAAE,GAAGC,EAAE,EAAEU,CAAC,EAAEC,CAAC,CAAC;EACtE,CAAC,MAAM;IACLf,MAAM,CAACC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEU,CAAC,EAAEC,CAAC,CAAC;EAC9B;EAEA,IAAIM,IAAI,IAAIC,IAAI,EAAE;IAEhB3B,EAAE,IAAI0B,IAAI,GAAG9B,CAAC,GAAG+B,IAAI,GAAG7B,CAAC;IACzBG,EAAE,IAAIyB,IAAI,GAAG7B,CAAC,GAAG8B,IAAI,GAAG5B,CAAC;IACzBG,eAAe,GAAG,KAAK;EACzB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}