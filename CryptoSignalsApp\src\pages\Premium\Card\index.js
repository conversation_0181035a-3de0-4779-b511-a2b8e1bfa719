import React from "react";
import { View, Text, TouchableOpacity } from "react-native";
import styles from "./styles";
import Loading from "../../../components/Loading";

const CardPlan = ({ products, product, returnProductSelected, isLoading }) => {
  const handleSelectedProduct = (productSelected) => {
    const result = productSelected.priceId === product?.priceId
      ? null
      : productSelected;

    returnProductSelected(result);
  }

  const getCardClass = ({priceId}) => product?.priceId === priceId
    ? { ...styles.card, ...styles.cardSelected }
    : styles.card

  return (
    <View style={styles.container}>
      {isLoading && (
        <View style={{height: 164, justifyContent: 'center', alignItems: 'center'}}>
          <Loading />
        </View>
      )}

      {products && products.map((product, index) => (
        <TouchableOpacity
          key={"premiumCard__" + index}
          style={getCardClass(product)}
          onPress={() => handleSelectedProduct(product)}
        >
          <Text style={styles.period}>{product.name}</Text>
          <Text style={styles.price}>
            {String(product.amount).slice(0, -2)} {product.currency}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );
}

export default CardPlan;
