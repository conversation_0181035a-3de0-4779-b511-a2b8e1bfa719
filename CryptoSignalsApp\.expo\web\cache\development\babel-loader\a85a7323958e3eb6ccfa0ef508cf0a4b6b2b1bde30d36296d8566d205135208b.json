{"ast": null, "code": "function _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nimport * as React from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport { useInternalTheme } from \"../../core/theming\";\nvar DialogScrollArea = function DialogScrollArea(props) {\n  var theme = useInternalTheme(props.theme);\n  var borderStyles = {\n    borderColor: theme.isV3 ? theme.colors.surfaceVariant : 'rgba(0, 0, 0, .12)',\n    borderTopWidth: theme.isV3 ? 1 : StyleSheet.hairlineWidth,\n    borderBottomWidth: theme.isV3 ? 1 : StyleSheet.hairlineWidth\n  };\n  return React.createElement(View, _extends({}, props, {\n    style: [styles.container, borderStyles, theme.isV3 && styles.v3Container, props.style]\n  }), props.children);\n};\nDialogScrollArea.displayName = 'Dialog.ScrollArea';\nvar styles = StyleSheet.create({\n  container: {\n    paddingHorizontal: 24,\n    flexGrow: 1,\n    flexShrink: 1\n  },\n  v3Container: {\n    marginBottom: 24\n  }\n});\nexport default DialogScrollArea;", "map": {"version": 3, "names": ["React", "StyleSheet", "View", "useInternalTheme", "DialogScrollArea", "props", "theme", "borderStyles", "borderColor", "isV3", "colors", "surfaceVariant", "borderTopWidth", "hairlineWidth", "borderBottomWidth", "createElement", "_extends", "style", "styles", "container", "v3Container", "children", "displayName", "create", "paddingHorizontal", "flexGrow", "flexShrink", "marginBottom"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\Dialog\\DialogScrollArea.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { StyleProp, StyleSheet, View, ViewStyle } from 'react-native';\n\nimport type { ThemeProp } from 'src/types';\n\nimport { useInternalTheme } from '../../core/theming';\n\nexport type Props = React.ComponentPropsWithRef<typeof View> & {\n  /**\n   * Content of the `DialogScrollArea`.\n   */\n  children: React.ReactNode;\n  style?: StyleProp<ViewStyle>;\n  /**\n   * @optional\n   */\n  theme?: ThemeProp;\n};\n\n/**\n * A component to show a scrollable content in a Dialog. The component only provides appropriate styling.\n * For the scrollable content you can use `ScrollView`, `FlatList` etc. depending on your requirement.\n *\n * ## Usage\n * ```js\n * import * as React from 'react';\n * import { ScrollView } from 'react-native';\n * import { Dialog, Portal, Text } from 'react-native-paper';\n *\n * const MyComponent = () => {\n *   const [visible, setVisible] = React.useState(false);\n *\n *   const hideDialog = () => setVisible(false);\n *\n *   return (\n *     <Portal>\n *       <Dialog visible={visible} onDismiss={hideDialog}>\n *         <Dialog.ScrollArea>\n *           <ScrollView contentContainerStyle={{paddingHorizontal: 24}}>\n *             <Text>This is a scrollable area</Text>\n *           </ScrollView>\n *         </Dialog.ScrollArea>\n *       </Dialog>\n *     </Portal>\n *   );\n * };\n *\n * export default MyComponent;\n * ```\n */\nconst DialogScrollArea = (props: Props) => {\n  const theme = useInternalTheme(props.theme);\n  const borderStyles = {\n    borderColor: theme.isV3\n      ? theme.colors.surfaceVariant\n      : 'rgba(0, 0, 0, .12)',\n    borderTopWidth: theme.isV3 ? 1 : StyleSheet.hairlineWidth,\n    borderBottomWidth: theme.isV3 ? 1 : StyleSheet.hairlineWidth,\n  };\n  return (\n    <View\n      {...props}\n      style={[\n        styles.container,\n        borderStyles,\n        theme.isV3 && styles.v3Container,\n        props.style,\n      ]}\n    >\n      {props.children}\n    </View>\n  );\n};\n\nDialogScrollArea.displayName = 'Dialog.ScrollArea';\n\nconst styles = StyleSheet.create({\n  container: {\n    paddingHorizontal: 24,\n    flexGrow: 1,\n    flexShrink: 1,\n  },\n  v3Container: {\n    marginBottom: 24,\n  },\n});\n\nexport default DialogScrollArea;\n"], "mappings": ";;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAK9B,SAASC,gBAAgB;AA6CzB,IAAMC,gBAAgB,GAAI,SAApBA,gBAAgBA,CAAIC,KAAY,EAAK;EACzC,IAAMC,KAAK,GAAGH,gBAAgB,CAACE,KAAK,CAACC,KAAK,CAAC;EAC3C,IAAMC,YAAY,GAAG;IACnBC,WAAW,EAAEF,KAAK,CAACG,IAAI,GACnBH,KAAK,CAACI,MAAM,CAACC,cAAc,GAC3B,oBAAoB;IACxBC,cAAc,EAAEN,KAAK,CAACG,IAAI,GAAG,CAAC,GAAGR,UAAU,CAACY,aAAa;IACzDC,iBAAiB,EAAER,KAAK,CAACG,IAAI,GAAG,CAAC,GAAGR,UAAU,CAACY;EACjD,CAAC;EACD,OACEb,KAAA,CAAAe,aAAA,CAACb,IAAI,EAAAc,QAAA,KACCX,KAAK;IACTY,KAAK,EAAE,CACLC,MAAM,CAACC,SAAS,EAChBZ,YAAY,EACZD,KAAK,CAACG,IAAI,IAAIS,MAAM,CAACE,WAAW,EAChCf,KAAK,CAACY,KAAK;EACX,IAEDZ,KAAK,CAACgB,QACH,CAAC;AAEX,CAAC;AAEDjB,gBAAgB,CAACkB,WAAW,GAAG,mBAAmB;AAElD,IAAMJ,MAAM,GAAGjB,UAAU,CAACsB,MAAM,CAAC;EAC/BJ,SAAS,EAAE;IACTK,iBAAiB,EAAE,EAAE;IACrBC,QAAQ,EAAE,CAAC;IACXC,UAAU,EAAE;EACd,CAAC;EACDN,WAAW,EAAE;IACXO,YAAY,EAAE;EAChB;AACF,CAAC,CAAC;AAEF,eAAevB,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}