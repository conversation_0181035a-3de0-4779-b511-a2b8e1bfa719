{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport * as React from 'react';\nimport EnsureSingleNavigator from \"./EnsureSingleNavigator\";\nimport NavigationStateContext from \"./NavigationStateContext\";\nimport StaticContainer from \"./StaticContainer\";\nimport useOptionsGetters from \"./useOptionsGetters\";\nexport default function SceneView(_ref) {\n  var screen = _ref.screen,\n    route = _ref.route,\n    navigation = _ref.navigation,\n    routeState = _ref.routeState,\n    getState = _ref.getState,\n    setState = _ref.setState,\n    options = _ref.options,\n    clearOptions = _ref.clearOptions;\n  var navigatorKeyRef = React.useRef();\n  var getKey = React.useCallback(function () {\n    return navigatorKeyRef.current;\n  }, []);\n  var _useOptionsGetters = useOptionsGetters({\n      key: route.key,\n      options: options,\n      navigation: navigation\n    }),\n    addOptionsGetter = _useOptionsGetters.addOptionsGetter;\n  var setKey = React.useCallback(function (key) {\n    navigatorKeyRef.current = key;\n  }, []);\n  var getCurrentState = React.useCallback(function () {\n    var state = getState();\n    var currentRoute = state.routes.find(function (r) {\n      return r.key === route.key;\n    });\n    return currentRoute ? currentRoute.state : undefined;\n  }, [getState, route.key]);\n  var setCurrentState = React.useCallback(function (child) {\n    var state = getState();\n    setState(_objectSpread(_objectSpread({}, state), {}, {\n      routes: state.routes.map(function (r) {\n        return r.key === route.key ? _objectSpread(_objectSpread({}, r), {}, {\n          state: child\n        }) : r;\n      })\n    }));\n  }, [getState, route.key, setState]);\n  var isInitialRef = React.useRef(true);\n  React.useEffect(function () {\n    isInitialRef.current = false;\n  });\n  React.useEffect(function () {\n    return clearOptions;\n  }, []);\n  var getIsInitial = React.useCallback(function () {\n    return isInitialRef.current;\n  }, []);\n  var context = React.useMemo(function () {\n    return {\n      state: routeState,\n      getState: getCurrentState,\n      setState: setCurrentState,\n      getKey: getKey,\n      setKey: setKey,\n      getIsInitial: getIsInitial,\n      addOptionsGetter: addOptionsGetter\n    };\n  }, [routeState, getCurrentState, setCurrentState, getKey, setKey, getIsInitial, addOptionsGetter]);\n  var ScreenComponent = screen.getComponent ? screen.getComponent() : screen.component;\n  return React.createElement(NavigationStateContext.Provider, {\n    value: context\n  }, React.createElement(EnsureSingleNavigator, null, React.createElement(StaticContainer, {\n    name: screen.name,\n    render: ScreenComponent || screen.children,\n    navigation: navigation,\n    route: route\n  }, ScreenComponent !== undefined ? React.createElement(ScreenComponent, {\n    navigation: navigation,\n    route: route\n  }) : screen.children !== undefined ? screen.children({\n    navigation: navigation,\n    route: route\n  }) : null)));\n}", "map": {"version": 3, "names": ["React", "EnsureSingleNavigator", "NavigationStateContext", "StaticContainer", "useOptionsGetters", "SceneView", "_ref", "screen", "route", "navigation", "routeState", "getState", "setState", "options", "clearOptions", "navigator<PERSON><PERSON><PERSON><PERSON>", "useRef", "<PERSON><PERSON><PERSON>", "useCallback", "current", "_useOptionsGetters", "key", "addOptionsGetter", "<PERSON><PERSON><PERSON>", "getCurrentState", "state", "currentRoute", "routes", "find", "r", "undefined", "setCurrentState", "child", "_objectSpread", "map", "isInitialRef", "useEffect", "getIsInitial", "context", "useMemo", "ScreenComponent", "getComponent", "component", "createElement", "Provider", "value", "name", "render", "children"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\@react-navigation\\core\\src\\SceneView.tsx"], "sourcesContent": ["import type {\n  NavigationState,\n  ParamListBase,\n  PartialState,\n  Route,\n} from '@react-navigation/routers';\nimport * as React from 'react';\n\nimport EnsureSingleNavigator from './EnsureSingleNavigator';\nimport NavigationStateContext from './NavigationStateContext';\nimport StaticContainer from './StaticContainer';\nimport type { NavigationProp, RouteConfigComponent } from './types';\nimport useOptionsGetters from './useOptionsGetters';\n\ntype Props<State extends NavigationState, ScreenOptions extends {}> = {\n  screen: RouteConfigComponent<ParamListBase, string> & { name: string };\n  navigation: NavigationProp<\n    ParamListBase,\n    string,\n    string | undefined,\n    State,\n    ScreenOptions\n  >;\n  route: Route<string>;\n  routeState: NavigationState | PartialState<NavigationState> | undefined;\n  getState: () => State;\n  setState: (state: State) => void;\n  options: object;\n  clearOptions: () => void;\n};\n\n/**\n * Component which takes care of rendering the screen for a route.\n * It provides all required contexts and applies optimizations when applicable.\n */\nexport default function SceneView<\n  State extends NavigationState,\n  ScreenOptions extends {}\n>({\n  screen,\n  route,\n  navigation,\n  routeState,\n  getState,\n  setState,\n  options,\n  clearOptions,\n}: Props<State, ScreenOptions>) {\n  const navigatorKeyRef = React.useRef<string | undefined>();\n  const getKey = React.useCallback(() => navigatorKeyRef.current, []);\n\n  const { addOptionsGetter } = useOptionsGetters({\n    key: route.key,\n    options,\n    navigation,\n  });\n\n  const setKey = React.useCallback((key: string) => {\n    navigatorKeyRef.current = key;\n  }, []);\n\n  const getCurrentState = React.useCallback(() => {\n    const state = getState();\n    const currentRoute = state.routes.find((r) => r.key === route.key);\n\n    return currentRoute ? currentRoute.state : undefined;\n  }, [getState, route.key]);\n\n  const setCurrentState = React.useCallback(\n    (child: NavigationState | PartialState<NavigationState> | undefined) => {\n      const state = getState();\n\n      setState({\n        ...state,\n        routes: state.routes.map((r) =>\n          r.key === route.key ? { ...r, state: child } : r\n        ),\n      });\n    },\n    [getState, route.key, setState]\n  );\n\n  const isInitialRef = React.useRef(true);\n\n  React.useEffect(() => {\n    isInitialRef.current = false;\n  });\n\n  // Clear options set by this screen when it is unmounted\n  React.useEffect(() => {\n    return clearOptions;\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  const getIsInitial = React.useCallback(() => isInitialRef.current, []);\n\n  const context = React.useMemo(\n    () => ({\n      state: routeState,\n      getState: getCurrentState,\n      setState: setCurrentState,\n      getKey,\n      setKey,\n      getIsInitial,\n      addOptionsGetter,\n    }),\n    [\n      routeState,\n      getCurrentState,\n      setCurrentState,\n      getKey,\n      setKey,\n      getIsInitial,\n      addOptionsGetter,\n    ]\n  );\n\n  const ScreenComponent = screen.getComponent\n    ? screen.getComponent()\n    : screen.component;\n\n  return (\n    <NavigationStateContext.Provider value={context}>\n      <EnsureSingleNavigator>\n        <StaticContainer\n          name={screen.name}\n          render={ScreenComponent || screen.children}\n          navigation={navigation}\n          route={route}\n        >\n          {ScreenComponent !== undefined ? (\n            <ScreenComponent navigation={navigation} route={route} />\n          ) : screen.children !== undefined ? (\n            screen.children({ navigation, route })\n          ) : null}\n        </StaticContainer>\n      </EnsureSingleNavigator>\n    </NavigationStateContext.Provider>\n  );\n}\n"], "mappings": ";;;AAMA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,OAAOC,qBAAqB;AAC5B,OAAOC,sBAAsB;AAC7B,OAAOC,eAAe;AAEtB,OAAOC,iBAAiB;AAuBxB,eAAe,SAASC,SAASA,CAAAC,IAAA,EAYD;EAAA,IAR9BC,MAAM,GAQsBD,IAAA,CAR5BC,MAAM;IACNC,KAAK,GAOuBF,IAAA,CAP5BE,KAAK;IACLC,UAAU,GAMkBH,IAAA,CAN5BG,UAAU;IACVC,UAAU,GAKkBJ,IAAA,CAL5BI,UAAU;IACVC,QAAQ,GAIoBL,IAAA,CAJ5BK,QAAQ;IACRC,QAAQ,GAGoBN,IAAA,CAH5BM,QAAQ;IACRC,OAAO,GAEqBP,IAAA,CAF5BO,OAAO;IACPC,YAAA,GAC4BR,IAAA,CAD5BQ,YAAA;EAEA,IAAMC,eAAe,GAAGf,KAAK,CAACgB,MAAM,EAAsB;EAC1D,IAAMC,MAAM,GAAGjB,KAAK,CAACkB,WAAW,CAAC;IAAA,OAAMH,eAAe,CAACI,OAAO;EAAA,GAAE,EAAE,CAAC;EAEnE,IAAAC,kBAAA,GAA6BhB,iBAAiB,CAAC;MAC7CiB,GAAG,EAAEb,KAAK,CAACa,GAAG;MACdR,OAAO,EAAPA,OAAO;MACPJ,UAAA,EAAAA;IACF,CAAC,CAAC;IAJMa,gBAAA,GAAAF,kBAAA,CAAAE,gBAAA;EAMR,IAAMC,MAAM,GAAGvB,KAAK,CAACkB,WAAW,CAAE,UAAAG,GAAW,EAAK;IAChDN,eAAe,CAACI,OAAO,GAAGE,GAAG;EAC/B,CAAC,EAAE,EAAE,CAAC;EAEN,IAAMG,eAAe,GAAGxB,KAAK,CAACkB,WAAW,CAAC,YAAM;IAC9C,IAAMO,KAAK,GAAGd,QAAQ,EAAE;IACxB,IAAMe,YAAY,GAAGD,KAAK,CAACE,MAAM,CAACC,IAAI,CAAE,UAAAC,CAAC;MAAA,OAAKA,CAAC,CAACR,GAAG,KAAKb,KAAK,CAACa,GAAG;IAAA,EAAC;IAElE,OAAOK,YAAY,GAAGA,YAAY,CAACD,KAAK,GAAGK,SAAS;EACtD,CAAC,EAAE,CAACnB,QAAQ,EAAEH,KAAK,CAACa,GAAG,CAAC,CAAC;EAEzB,IAAMU,eAAe,GAAG/B,KAAK,CAACkB,WAAW,CACtC,UAAAc,KAAkE,EAAK;IACtE,IAAMP,KAAK,GAAGd,QAAQ,EAAE;IAExBC,QAAQ,CAAAqB,aAAA,CAAAA,aAAA,KACHR,KAAK;MACRE,MAAM,EAAEF,KAAK,CAACE,MAAM,CAACO,GAAG,CAAE,UAAAL,CAAC;QAAA,OACzBA,CAAC,CAACR,GAAG,KAAKb,KAAK,CAACa,GAAG,GAAAY,aAAA,CAAAA,aAAA,KAAQJ,CAAC;UAAEJ,KAAK,EAAEO;QAAA,KAAUH,CAAC;MAAA;IAAA,EAEnD,CAAC;EACJ,CAAC,EACD,CAAClB,QAAQ,EAAEH,KAAK,CAACa,GAAG,EAAET,QAAQ,CAAC,CAChC;EAED,IAAMuB,YAAY,GAAGnC,KAAK,CAACgB,MAAM,CAAC,IAAI,CAAC;EAEvChB,KAAK,CAACoC,SAAS,CAAC,YAAM;IACpBD,YAAY,CAAChB,OAAO,GAAG,KAAK;EAC9B,CAAC,CAAC;EAGFnB,KAAK,CAACoC,SAAS,CAAC,YAAM;IACpB,OAAOtB,YAAY;EAErB,CAAC,EAAE,EAAE,CAAC;EAEN,IAAMuB,YAAY,GAAGrC,KAAK,CAACkB,WAAW,CAAC;IAAA,OAAMiB,YAAY,CAAChB,OAAO;EAAA,GAAE,EAAE,CAAC;EAEtE,IAAMmB,OAAO,GAAGtC,KAAK,CAACuC,OAAO,CAC3B;IAAA,OAAO;MACLd,KAAK,EAAEf,UAAU;MACjBC,QAAQ,EAAEa,eAAe;MACzBZ,QAAQ,EAAEmB,eAAe;MACzBd,MAAM,EAANA,MAAM;MACNM,MAAM,EAANA,MAAM;MACNc,YAAY,EAAZA,YAAY;MACZf,gBAAA,EAAAA;IACF,CAAC;EAAA,CAAC,EACF,CACEZ,UAAU,EACVc,eAAe,EACfO,eAAe,EACfd,MAAM,EACNM,MAAM,EACNc,YAAY,EACZf,gBAAgB,CACjB,CACF;EAED,IAAMkB,eAAe,GAAGjC,MAAM,CAACkC,YAAY,GACvClC,MAAM,CAACkC,YAAY,EAAE,GACrBlC,MAAM,CAACmC,SAAS;EAEpB,OACE1C,KAAA,CAAA2C,aAAA,CAACzC,sBAAsB,CAAC0C,QAAQ;IAACC,KAAK,EAAEP;EAAQ,GAC9CtC,KAAA,CAAA2C,aAAA,CAAC1C,qBAAqB,QACpBD,KAAA,CAAA2C,aAAA,CAACxC,eAAe;IACd2C,IAAI,EAAEvC,MAAM,CAACuC,IAAK;IAClBC,MAAM,EAAEP,eAAe,IAAIjC,MAAM,CAACyC,QAAS;IAC3CvC,UAAU,EAAEA,UAAW;IACvBD,KAAK,EAAEA;EAAM,GAEZgC,eAAe,KAAKV,SAAS,GAC5B9B,KAAA,CAAA2C,aAAA,CAACH,eAAe;IAAC/B,UAAU,EAAEA,UAAW;IAACD,KAAK,EAAEA;EAAM,EAAG,GACvDD,MAAM,CAACyC,QAAQ,KAAKlB,SAAS,GAC/BvB,MAAM,CAACyC,QAAQ,CAAC;IAAEvC,UAAU,EAAVA,UAAU;IAAED,KAAA,EAAAA;EAAM,CAAC,CAAC,GACpC,IAAI,CACQ,CACI,CACQ;AAEtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}