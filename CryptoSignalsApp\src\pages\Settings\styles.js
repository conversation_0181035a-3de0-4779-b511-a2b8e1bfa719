import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  themeToggle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  themeToggleText: {
    fontSize: 16,
    fontWeight: 'bold',  // adicionado negrito para títulos de seções
  },
  privacySwitch: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  privacyLabel: {
    fontSize: 16,
  },
  notificationSwitch: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  notificationLabel: {
    fontSize: 16,
  },
  securitySection: {  // Estilo para a seção "Segurança e Privacidade"
    marginTop: 10,
  },
  linkOption: {  // Estilo para os links "Política de privacidade" e "Termos de serviço"
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  linkOptionText: {
    fontSize: 16,
    color: '#007BFF',  // azul padrão de link
  },
  cancelSubscriptionButton: {
    backgroundColor: '#dc3545',
    padding: 12,
    borderRadius: 4,
    alignItems: 'center',
    marginTop: 16,
    marginHorizontal: 20,
  },
  cancelSubscriptionText: {
    color: '#fff',
    fontSize: 16,
  },
});

export default styles;
