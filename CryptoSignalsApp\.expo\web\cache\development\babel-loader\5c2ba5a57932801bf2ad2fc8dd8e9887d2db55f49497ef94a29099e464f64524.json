{"ast": null, "code": "import color from 'color';\nexport function getTextColor(_ref) {\n  var theme = _ref.theme,\n    disabled = _ref.disabled;\n  var _theme$colors;\n  if (theme.isV3) {\n    if (disabled) {\n      return theme.colors.onSurfaceDisabled;\n    }\n    return theme.colors.onSurfaceVariant;\n  }\n  return color((_theme$colors = theme.colors) === null || _theme$colors === void 0 ? void 0 : _theme$colors.text).alpha(theme.dark ? 0.7 : 0.54).rgb().string();\n}\nexport function getIconColor(_ref2) {\n  var theme = _ref2.theme,\n    isTextInputFocused = _ref2.isTextInputFocused,\n    disabled = _ref2.disabled,\n    customColor = _ref2.customColor;\n  if (typeof customColor === 'function') {\n    return customColor(isTextInputFocused);\n  }\n  if (customColor) {\n    return customColor;\n  }\n  if (!theme.isV3) {\n    return theme.colors.text;\n  }\n  if (disabled) {\n    return theme.colors.onSurfaceDisabled;\n  }\n  return theme.colors.onSurfaceVariant;\n}", "map": {"version": 3, "names": ["color", "getTextColor", "_ref", "theme", "disabled", "_theme$colors", "isV3", "colors", "onSurfaceDisabled", "onSurfaceVariant", "text", "alpha", "dark", "rgb", "string", "getIconColor", "_ref2", "isTextInputFocused", "customColor"], "sources": ["E:\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\components\\TextInput\\Adornment\\utils.ts"], "sourcesContent": ["import color from 'color';\n\nimport type { InternalTheme } from '../../../types';\n\ntype BaseProps = {\n  theme: InternalTheme;\n  disabled?: boolean;\n};\n\nexport function getTextColor({ theme, disabled }: BaseProps) {\n  if (theme.isV3) {\n    if (disabled) {\n      return theme.colors.onSurfaceDisabled;\n    }\n    return theme.colors.onSurfaceVariant;\n  }\n  return color(theme.colors?.text)\n    .alpha(theme.dark ? 0.7 : 0.54)\n    .rgb()\n    .string();\n}\n\nexport function getIconColor({\n  theme,\n  isTextInputFocused,\n  disabled,\n  customColor,\n}: BaseProps & {\n  isTextInputFocused: boolean;\n  customColor?: ((isTextInputFocused: boolean) => string | undefined) | string;\n}) {\n  if (typeof customColor === 'function') {\n    return customColor(isTextInputFocused);\n  }\n  if (customColor) {\n    return customColor;\n  }\n\n  if (!theme.isV3) {\n    return theme.colors.text;\n  }\n\n  if (disabled) {\n    return theme.colors.onSurfaceDisabled;\n  }\n\n  return theme.colors.onSurfaceVariant;\n}\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AASzB,OAAO,SAASC,YAAYA,CAAAC,IAAA,EAAiC;EAAA,IAA9BC,KAAK,GAAAD,IAAA,CAALC,KAAK;IAAEC,QAAA,GAAAF,IAAA,CAAAE,QAAA;EAAuB,IAAAC,aAAA;EAC3D,IAAIF,KAAK,CAACG,IAAI,EAAE;IACd,IAAIF,QAAQ,EAAE;MACZ,OAAOD,KAAK,CAACI,MAAM,CAACC,iBAAiB;IACvC;IACA,OAAOL,KAAK,CAACI,MAAM,CAACE,gBAAgB;EACtC;EACA,OAAOT,KAAK,EAAAK,aAAA,GAACF,KAAK,CAACI,MAAM,cAAAF,aAAA,uBAAZA,aAAA,CAAcK,IAAI,CAAC,CAC7BC,KAAK,CAACR,KAAK,CAACS,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,CAC9BC,GAAG,CAAC,CAAC,CACLC,MAAM,CAAC,CAAC;AACb;AAEA,OAAO,SAASC,YAAYA,CAAAC,KAAA,EAQzB;EAAA,IAPDb,KAAK,GAAAa,KAAA,CAALb,KAAK;IACLc,kBAAkB,GAAAD,KAAA,CAAlBC,kBAAkB;IAClBb,QAAQ,GAAAY,KAAA,CAARZ,QAAQ;IACRc,WAAA,GAAAF,KAAA,CAAAE,WAAA;EAKA,IAAI,OAAOA,WAAW,KAAK,UAAU,EAAE;IACrC,OAAOA,WAAW,CAACD,kBAAkB,CAAC;EACxC;EACA,IAAIC,WAAW,EAAE;IACf,OAAOA,WAAW;EACpB;EAEA,IAAI,CAACf,KAAK,CAACG,IAAI,EAAE;IACf,OAAOH,KAAK,CAACI,MAAM,CAACG,IAAI;EAC1B;EAEA,IAAIN,QAAQ,EAAE;IACZ,OAAOD,KAAK,CAACI,MAAM,CAACC,iBAAiB;EACvC;EAEA,OAAOL,KAAK,CAACI,MAAM,CAACE,gBAAgB;AACtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}