{"ast": null, "code": "'use strict';\nvar NEWTON_ITERATIONS = 4;\nvar NEWTON_MIN_SLOPE = 0.001;\nvar SUBDIVISION_PRECISION = 0.0000001;\nvar SUBDIVISION_MAX_ITERATIONS = 10;\nvar kSplineTableSize = 11;\nvar kSampleStepSize = 1.0 / (kSplineTableSize - 1.0);\nvar float32ArraySupported = typeof Float32Array === 'function';\nfunction A(aA1, aA2) {\n  return 1.0 - 3.0 * aA2 + 3.0 * aA1;\n}\nfunction B(aA1, aA2) {\n  return 3.0 * aA2 - 6.0 * aA1;\n}\nfunction C(aA1) {\n  return 3.0 * aA1;\n}\nfunction calcBezier(aT, aA1, aA2) {\n  return ((A(aA1, aA2) * aT + B(aA1, aA2)) * aT + C(aA1)) * aT;\n}\nfunction getSlope(aT, aA1, aA2) {\n  return 3.0 * A(aA1, aA2) * aT * aT + 2.0 * B(aA1, aA2) * aT + C(aA1);\n}\nfunction binarySubdivide(aX, _aA, _aB, mX1, mX2) {\n  var currentX,\n    currentT,\n    i = 0,\n    aA = _aA,\n    aB = _aB;\n  do {\n    currentT = aA + (aB - aA) / 2.0;\n    currentX = calcBezier(currentT, mX1, mX2) - aX;\n    if (currentX > 0.0) {\n      aB = currentT;\n    } else {\n      aA = currentT;\n    }\n  } while (Math.abs(currentX) > SUBDIVISION_PRECISION && ++i < SUBDIVISION_MAX_ITERATIONS);\n  return currentT;\n}\nfunction newtonRaphsonIterate(aX, _aGuessT, mX1, mX2) {\n  var aGuessT = _aGuessT;\n  for (var i = 0; i < NEWTON_ITERATIONS; ++i) {\n    var currentSlope = getSlope(aGuessT, mX1, mX2);\n    if (currentSlope === 0.0) {\n      return aGuessT;\n    }\n    var currentX = calcBezier(aGuessT, mX1, mX2) - aX;\n    aGuessT -= currentX / currentSlope;\n  }\n  return aGuessT;\n}\nexport default function bezier(mX1, mY1, mX2, mY2) {\n  if (!(mX1 >= 0 && mX1 <= 1 && mX2 >= 0 && mX2 <= 1)) {\n    throw new Error('bezier x values must be in [0, 1] range');\n  }\n  var sampleValues = float32ArraySupported ? new Float32Array(kSplineTableSize) : new Array(kSplineTableSize);\n  if (mX1 !== mY1 || mX2 !== mY2) {\n    for (var i = 0; i < kSplineTableSize; ++i) {\n      sampleValues[i] = calcBezier(i * kSampleStepSize, mX1, mX2);\n    }\n  }\n  function getTForX(aX) {\n    var intervalStart = 0.0;\n    var currentSample = 1;\n    var lastSample = kSplineTableSize - 1;\n    for (; currentSample !== lastSample && sampleValues[currentSample] <= aX; ++currentSample) {\n      intervalStart += kSampleStepSize;\n    }\n    --currentSample;\n    var dist = (aX - sampleValues[currentSample]) / (sampleValues[currentSample + 1] - sampleValues[currentSample]);\n    var guessForT = intervalStart + dist * kSampleStepSize;\n    var initialSlope = getSlope(guessForT, mX1, mX2);\n    if (initialSlope >= NEWTON_MIN_SLOPE) {\n      return newtonRaphsonIterate(aX, guessForT, mX1, mX2);\n    } else if (initialSlope === 0.0) {\n      return guessForT;\n    } else {\n      return binarySubdivide(aX, intervalStart, intervalStart + kSampleStepSize, mX1, mX2);\n    }\n  }\n  return function BezierEasing(x) {\n    if (mX1 === mY1 && mX2 === mY2) {\n      return x;\n    }\n    if (x === 0) {\n      return 0;\n    }\n    if (x === 1) {\n      return 1;\n    }\n    return calcBezier(getTForX(x), mY1, mY2);\n  };\n}\n;", "map": {"version": 3, "names": ["NEWTON_ITERATIONS", "NEWTON_MIN_SLOPE", "SUBDIVISION_PRECISION", "SUBDIVISION_MAX_ITERATIONS", "kSplineTableSize", "kSampleStepSize", "float32ArraySupported", "Float32Array", "A", "aA1", "aA2", "B", "C", "calcBezier", "aT", "getSlope", "binarySubdivide", "aX", "_aA", "_aB", "mX1", "mX2", "currentX", "currentT", "i", "aA", "aB", "Math", "abs", "newtonRaphsonIterate", "_aGuessT", "aGuessT", "currentSlope", "bezier", "mY1", "mY2", "Error", "sampleValues", "Array", "getTForX", "intervalStart", "currentSample", "lastSample", "dist", "guessForT", "initialSlope", "BezierEasing", "x"], "sources": ["E:/CryptoSignalsApp/node_modules/react-native-web/dist/vendor/react-native/Animated/bezier.js"], "sourcesContent": ["/**\n * Portions Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n/**\n * BezierEasing - use bezier curve for transition easing function\n * https://github.com/gre/bezier-easing\n * @copyright 2014-2015 Gaëtan Renaudeau. MIT License.\n */\n\n'use strict';\n\n// These values are established by empiricism with tests (tradeoff: performance VS precision)\nvar NEWTON_ITERATIONS = 4;\nvar NEWTON_MIN_SLOPE = 0.001;\nvar SUBDIVISION_PRECISION = 0.0000001;\nvar SUBDIVISION_MAX_ITERATIONS = 10;\nvar kSplineTableSize = 11;\nvar kSampleStepSize = 1.0 / (kSplineTableSize - 1.0);\nvar float32ArraySupported = typeof Float32Array === 'function';\nfunction A(aA1, aA2) {\n  return 1.0 - 3.0 * aA2 + 3.0 * aA1;\n}\nfunction B(aA1, aA2) {\n  return 3.0 * aA2 - 6.0 * aA1;\n}\nfunction C(aA1) {\n  return 3.0 * aA1;\n}\n\n// Returns x(t) given t, x1, and x2, or y(t) given t, y1, and y2.\nfunction calcBezier(aT, aA1, aA2) {\n  return ((A(aA1, aA2) * aT + B(aA1, aA2)) * aT + C(aA1)) * aT;\n}\n\n// Returns dx/dt given t, x1, and x2, or dy/dt given t, y1, and y2.\nfunction getSlope(aT, aA1, aA2) {\n  return 3.0 * A(aA1, aA2) * aT * aT + 2.0 * B(aA1, aA2) * aT + C(aA1);\n}\nfunction binarySubdivide(aX, _aA, _aB, mX1, mX2) {\n  var currentX,\n    currentT,\n    i = 0,\n    aA = _aA,\n    aB = _aB;\n  do {\n    currentT = aA + (aB - aA) / 2.0;\n    currentX = calcBezier(currentT, mX1, mX2) - aX;\n    if (currentX > 0.0) {\n      aB = currentT;\n    } else {\n      aA = currentT;\n    }\n  } while (Math.abs(currentX) > SUBDIVISION_PRECISION && ++i < SUBDIVISION_MAX_ITERATIONS);\n  return currentT;\n}\nfunction newtonRaphsonIterate(aX, _aGuessT, mX1, mX2) {\n  var aGuessT = _aGuessT;\n  for (var i = 0; i < NEWTON_ITERATIONS; ++i) {\n    var currentSlope = getSlope(aGuessT, mX1, mX2);\n    if (currentSlope === 0.0) {\n      return aGuessT;\n    }\n    var currentX = calcBezier(aGuessT, mX1, mX2) - aX;\n    aGuessT -= currentX / currentSlope;\n  }\n  return aGuessT;\n}\nexport default function bezier(mX1, mY1, mX2, mY2) {\n  if (!(mX1 >= 0 && mX1 <= 1 && mX2 >= 0 && mX2 <= 1)) {\n    throw new Error('bezier x values must be in [0, 1] range');\n  }\n\n  // Precompute samples table\n  var sampleValues = float32ArraySupported ? new Float32Array(kSplineTableSize) : new Array(kSplineTableSize);\n  if (mX1 !== mY1 || mX2 !== mY2) {\n    for (var i = 0; i < kSplineTableSize; ++i) {\n      sampleValues[i] = calcBezier(i * kSampleStepSize, mX1, mX2);\n    }\n  }\n  function getTForX(aX) {\n    var intervalStart = 0.0;\n    var currentSample = 1;\n    var lastSample = kSplineTableSize - 1;\n    for (; currentSample !== lastSample && sampleValues[currentSample] <= aX; ++currentSample) {\n      intervalStart += kSampleStepSize;\n    }\n    --currentSample;\n\n    // Interpolate to provide an initial guess for t\n    var dist = (aX - sampleValues[currentSample]) / (sampleValues[currentSample + 1] - sampleValues[currentSample]);\n    var guessForT = intervalStart + dist * kSampleStepSize;\n    var initialSlope = getSlope(guessForT, mX1, mX2);\n    if (initialSlope >= NEWTON_MIN_SLOPE) {\n      return newtonRaphsonIterate(aX, guessForT, mX1, mX2);\n    } else if (initialSlope === 0.0) {\n      return guessForT;\n    } else {\n      return binarySubdivide(aX, intervalStart, intervalStart + kSampleStepSize, mX1, mX2);\n    }\n  }\n  return function BezierEasing(x) {\n    if (mX1 === mY1 && mX2 === mY2) {\n      return x; // linear\n    }\n    // Because JavaScript number are imprecise, we should guarantee the extremes are right.\n    if (x === 0) {\n      return 0;\n    }\n    if (x === 1) {\n      return 1;\n    }\n    return calcBezier(getTForX(x), mY1, mY2);\n  };\n}\n;"], "mappings": "AAgBA,YAAY;AAGZ,IAAIA,iBAAiB,GAAG,CAAC;AACzB,IAAIC,gBAAgB,GAAG,KAAK;AAC5B,IAAIC,qBAAqB,GAAG,SAAS;AACrC,IAAIC,0BAA0B,GAAG,EAAE;AACnC,IAAIC,gBAAgB,GAAG,EAAE;AACzB,IAAIC,eAAe,GAAG,GAAG,IAAID,gBAAgB,GAAG,GAAG,CAAC;AACpD,IAAIE,qBAAqB,GAAG,OAAOC,YAAY,KAAK,UAAU;AAC9D,SAASC,CAACA,CAACC,GAAG,EAAEC,GAAG,EAAE;EACnB,OAAO,GAAG,GAAG,GAAG,GAAGA,GAAG,GAAG,GAAG,GAAGD,GAAG;AACpC;AACA,SAASE,CAACA,CAACF,GAAG,EAAEC,GAAG,EAAE;EACnB,OAAO,GAAG,GAAGA,GAAG,GAAG,GAAG,GAAGD,GAAG;AAC9B;AACA,SAASG,CAACA,CAACH,GAAG,EAAE;EACd,OAAO,GAAG,GAAGA,GAAG;AAClB;AAGA,SAASI,UAAUA,CAACC,EAAE,EAAEL,GAAG,EAAEC,GAAG,EAAE;EAChC,OAAO,CAAC,CAACF,CAAC,CAACC,GAAG,EAAEC,GAAG,CAAC,GAAGI,EAAE,GAAGH,CAAC,CAACF,GAAG,EAAEC,GAAG,CAAC,IAAII,EAAE,GAAGF,CAAC,CAACH,GAAG,CAAC,IAAIK,EAAE;AAC9D;AAGA,SAASC,QAAQA,CAACD,EAAE,EAAEL,GAAG,EAAEC,GAAG,EAAE;EAC9B,OAAO,GAAG,GAAGF,CAAC,CAACC,GAAG,EAAEC,GAAG,CAAC,GAAGI,EAAE,GAAGA,EAAE,GAAG,GAAG,GAAGH,CAAC,CAACF,GAAG,EAAEC,GAAG,CAAC,GAAGI,EAAE,GAAGF,CAAC,CAACH,GAAG,CAAC;AACtE;AACA,SAASO,eAAeA,CAACC,EAAE,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAE;EAC/C,IAAIC,QAAQ;IACVC,QAAQ;IACRC,CAAC,GAAG,CAAC;IACLC,EAAE,GAAGP,GAAG;IACRQ,EAAE,GAAGP,GAAG;EACV,GAAG;IACDI,QAAQ,GAAGE,EAAE,GAAG,CAACC,EAAE,GAAGD,EAAE,IAAI,GAAG;IAC/BH,QAAQ,GAAGT,UAAU,CAACU,QAAQ,EAAEH,GAAG,EAAEC,GAAG,CAAC,GAAGJ,EAAE;IAC9C,IAAIK,QAAQ,GAAG,GAAG,EAAE;MAClBI,EAAE,GAAGH,QAAQ;IACf,CAAC,MAAM;MACLE,EAAE,GAAGF,QAAQ;IACf;EACF,CAAC,QAAQI,IAAI,CAACC,GAAG,CAACN,QAAQ,CAAC,GAAGpB,qBAAqB,IAAI,EAAEsB,CAAC,GAAGrB,0BAA0B;EACvF,OAAOoB,QAAQ;AACjB;AACA,SAASM,oBAAoBA,CAACZ,EAAE,EAAEa,QAAQ,EAAEV,GAAG,EAAEC,GAAG,EAAE;EACpD,IAAIU,OAAO,GAAGD,QAAQ;EACtB,KAAK,IAAIN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGxB,iBAAiB,EAAE,EAAEwB,CAAC,EAAE;IAC1C,IAAIQ,YAAY,GAAGjB,QAAQ,CAACgB,OAAO,EAAEX,GAAG,EAAEC,GAAG,CAAC;IAC9C,IAAIW,YAAY,KAAK,GAAG,EAAE;MACxB,OAAOD,OAAO;IAChB;IACA,IAAIT,QAAQ,GAAGT,UAAU,CAACkB,OAAO,EAAEX,GAAG,EAAEC,GAAG,CAAC,GAAGJ,EAAE;IACjDc,OAAO,IAAIT,QAAQ,GAAGU,YAAY;EACpC;EACA,OAAOD,OAAO;AAChB;AACA,eAAe,SAASE,MAAMA,CAACb,GAAG,EAAEc,GAAG,EAAEb,GAAG,EAAEc,GAAG,EAAE;EACjD,IAAI,EAAEf,GAAG,IAAI,CAAC,IAAIA,GAAG,IAAI,CAAC,IAAIC,GAAG,IAAI,CAAC,IAAIA,GAAG,IAAI,CAAC,CAAC,EAAE;IACnD,MAAM,IAAIe,KAAK,CAAC,yCAAyC,CAAC;EAC5D;EAGA,IAAIC,YAAY,GAAG/B,qBAAqB,GAAG,IAAIC,YAAY,CAACH,gBAAgB,CAAC,GAAG,IAAIkC,KAAK,CAAClC,gBAAgB,CAAC;EAC3G,IAAIgB,GAAG,KAAKc,GAAG,IAAIb,GAAG,KAAKc,GAAG,EAAE;IAC9B,KAAK,IAAIX,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpB,gBAAgB,EAAE,EAAEoB,CAAC,EAAE;MACzCa,YAAY,CAACb,CAAC,CAAC,GAAGX,UAAU,CAACW,CAAC,GAAGnB,eAAe,EAAEe,GAAG,EAAEC,GAAG,CAAC;IAC7D;EACF;EACA,SAASkB,QAAQA,CAACtB,EAAE,EAAE;IACpB,IAAIuB,aAAa,GAAG,GAAG;IACvB,IAAIC,aAAa,GAAG,CAAC;IACrB,IAAIC,UAAU,GAAGtC,gBAAgB,GAAG,CAAC;IACrC,OAAOqC,aAAa,KAAKC,UAAU,IAAIL,YAAY,CAACI,aAAa,CAAC,IAAIxB,EAAE,EAAE,EAAEwB,aAAa,EAAE;MACzFD,aAAa,IAAInC,eAAe;IAClC;IACA,EAAEoC,aAAa;IAGf,IAAIE,IAAI,GAAG,CAAC1B,EAAE,GAAGoB,YAAY,CAACI,aAAa,CAAC,KAAKJ,YAAY,CAACI,aAAa,GAAG,CAAC,CAAC,GAAGJ,YAAY,CAACI,aAAa,CAAC,CAAC;IAC/G,IAAIG,SAAS,GAAGJ,aAAa,GAAGG,IAAI,GAAGtC,eAAe;IACtD,IAAIwC,YAAY,GAAG9B,QAAQ,CAAC6B,SAAS,EAAExB,GAAG,EAAEC,GAAG,CAAC;IAChD,IAAIwB,YAAY,IAAI5C,gBAAgB,EAAE;MACpC,OAAO4B,oBAAoB,CAACZ,EAAE,EAAE2B,SAAS,EAAExB,GAAG,EAAEC,GAAG,CAAC;IACtD,CAAC,MAAM,IAAIwB,YAAY,KAAK,GAAG,EAAE;MAC/B,OAAOD,SAAS;IAClB,CAAC,MAAM;MACL,OAAO5B,eAAe,CAACC,EAAE,EAAEuB,aAAa,EAAEA,aAAa,GAAGnC,eAAe,EAAEe,GAAG,EAAEC,GAAG,CAAC;IACtF;EACF;EACA,OAAO,SAASyB,YAAYA,CAACC,CAAC,EAAE;IAC9B,IAAI3B,GAAG,KAAKc,GAAG,IAAIb,GAAG,KAAKc,GAAG,EAAE;MAC9B,OAAOY,CAAC;IACV;IAEA,IAAIA,CAAC,KAAK,CAAC,EAAE;MACX,OAAO,CAAC;IACV;IACA,IAAIA,CAAC,KAAK,CAAC,EAAE;MACX,OAAO,CAAC;IACV;IACA,OAAOlC,UAAU,CAAC0B,QAAQ,CAACQ,CAAC,CAAC,EAAEb,GAAG,EAAEC,GAAG,CAAC;EAC1C,CAAC;AACH;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}