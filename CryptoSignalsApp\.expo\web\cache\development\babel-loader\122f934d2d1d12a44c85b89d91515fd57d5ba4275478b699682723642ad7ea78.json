{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport { getHeader<PERSON>itle, HeaderBackContext, HeaderHeightContext, HeaderShownContext } from '@react-navigation/elements';\nimport { useTheme } from '@react-navigation/native';\nimport * as React from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport ModalPresentationContext from \"../../utils/ModalPresentationContext\";\nimport useKeyboardManager from \"../../utils/useKeyboardManager\";\nimport Card from \"./Card\";\nvar EPSILON = 0.1;\nfunction CardContainer(_ref) {\n  var interpolationIndex = _ref.interpolationIndex,\n    index = _ref.index,\n    active = _ref.active,\n    closing = _ref.closing,\n    gesture = _ref.gesture,\n    focused = _ref.focused,\n    modal = _ref.modal,\n    getPreviousScene = _ref.getPreviousScene,\n    getFocusedRoute = _ref.getFocusedRoute,\n    headerDarkContent = _ref.headerDarkContent,\n    hasAbsoluteFloatHeader = _ref.hasAbsoluteFloatHeader,\n    headerHeight = _ref.headerHeight,\n    onHeaderHeightChange = _ref.onHeaderHeightChange,\n    isParentHeaderShown = _ref.isParentHeaderShown,\n    isNextScreenTransparent = _ref.isNextScreenTransparent,\n    detachCurrentScreen = _ref.detachCurrentScreen,\n    layout = _ref.layout,\n    onCloseRoute = _ref.onCloseRoute,\n    onOpenRoute = _ref.onOpenRoute,\n    onGestureCancel = _ref.onGestureCancel,\n    onGestureEnd = _ref.onGestureEnd,\n    onGestureStart = _ref.onGestureStart,\n    onTransitionEnd = _ref.onTransitionEnd,\n    onTransitionStart = _ref.onTransitionStart,\n    renderHeader = _ref.renderHeader,\n    renderScene = _ref.renderScene,\n    safeAreaInsetBottom = _ref.safeAreaInsetBottom,\n    safeAreaInsetLeft = _ref.safeAreaInsetLeft,\n    safeAreaInsetRight = _ref.safeAreaInsetRight,\n    safeAreaInsetTop = _ref.safeAreaInsetTop,\n    scene = _ref.scene;\n  var parentHeaderHeight = React.useContext(HeaderHeightContext);\n  var _useKeyboardManager = useKeyboardManager(React.useCallback(function () {\n      var _scene$descriptor = scene.descriptor,\n        options = _scene$descriptor.options,\n        navigation = _scene$descriptor.navigation;\n      return navigation.isFocused() && options.keyboardHandlingEnabled !== false;\n    }, [scene.descriptor])),\n    onPageChangeStart = _useKeyboardManager.onPageChangeStart,\n    onPageChangeCancel = _useKeyboardManager.onPageChangeCancel,\n    onPageChangeConfirm = _useKeyboardManager.onPageChangeConfirm;\n  var handleOpen = function handleOpen() {\n    var route = scene.descriptor.route;\n    onTransitionEnd({\n      route: route\n    }, false);\n    onOpenRoute({\n      route: route\n    });\n  };\n  var handleClose = function handleClose() {\n    var route = scene.descriptor.route;\n    onTransitionEnd({\n      route: route\n    }, true);\n    onCloseRoute({\n      route: route\n    });\n  };\n  var handleGestureBegin = function handleGestureBegin() {\n    var route = scene.descriptor.route;\n    onPageChangeStart();\n    onGestureStart({\n      route: route\n    });\n  };\n  var handleGestureCanceled = function handleGestureCanceled() {\n    var route = scene.descriptor.route;\n    onPageChangeCancel();\n    onGestureCancel({\n      route: route\n    });\n  };\n  var handleGestureEnd = function handleGestureEnd() {\n    var route = scene.descriptor.route;\n    onGestureEnd({\n      route: route\n    });\n  };\n  var handleTransition = function handleTransition(_ref2) {\n    var closing = _ref2.closing,\n      gesture = _ref2.gesture;\n    var route = scene.descriptor.route;\n    if (!gesture) {\n      onPageChangeConfirm === null || onPageChangeConfirm === void 0 ? void 0 : onPageChangeConfirm(true);\n    } else if (active && closing) {\n      onPageChangeConfirm === null || onPageChangeConfirm === void 0 ? void 0 : onPageChangeConfirm(false);\n    } else {\n      onPageChangeCancel === null || onPageChangeCancel === void 0 ? void 0 : onPageChangeCancel();\n    }\n    onTransitionStart === null || onTransitionStart === void 0 ? void 0 : onTransitionStart({\n      route: route\n    }, closing);\n  };\n  var insets = {\n    top: safeAreaInsetTop,\n    right: safeAreaInsetRight,\n    bottom: safeAreaInsetBottom,\n    left: safeAreaInsetLeft\n  };\n  var _useTheme = useTheme(),\n    colors = _useTheme.colors;\n  var _React$useState = React.useState('box-none'),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    pointerEvents = _React$useState2[0],\n    setPointerEvents = _React$useState2[1];\n  React.useEffect(function () {\n    var _scene$progress$next, _scene$progress$next$;\n    var listener = (_scene$progress$next = scene.progress.next) === null || _scene$progress$next === void 0 ? void 0 : (_scene$progress$next$ = _scene$progress$next.addListener) === null || _scene$progress$next$ === void 0 ? void 0 : _scene$progress$next$.call(_scene$progress$next, function (_ref3) {\n      var value = _ref3.value;\n      setPointerEvents(value <= EPSILON ? 'box-none' : 'none');\n    });\n    return function () {\n      if (listener) {\n        var _scene$progress$next2, _scene$progress$next3;\n        (_scene$progress$next2 = scene.progress.next) === null || _scene$progress$next2 === void 0 ? void 0 : (_scene$progress$next3 = _scene$progress$next2.removeListener) === null || _scene$progress$next3 === void 0 ? void 0 : _scene$progress$next3.call(_scene$progress$next2, listener);\n      }\n    };\n  }, [pointerEvents, scene.progress.next]);\n  var _scene$descriptor$opt = scene.descriptor.options,\n    presentation = _scene$descriptor$opt.presentation,\n    animationEnabled = _scene$descriptor$opt.animationEnabled,\n    cardOverlay = _scene$descriptor$opt.cardOverlay,\n    cardOverlayEnabled = _scene$descriptor$opt.cardOverlayEnabled,\n    cardShadowEnabled = _scene$descriptor$opt.cardShadowEnabled,\n    cardStyle = _scene$descriptor$opt.cardStyle,\n    cardStyleInterpolator = _scene$descriptor$opt.cardStyleInterpolator,\n    gestureDirection = _scene$descriptor$opt.gestureDirection,\n    gestureEnabled = _scene$descriptor$opt.gestureEnabled,\n    gestureResponseDistance = _scene$descriptor$opt.gestureResponseDistance,\n    gestureVelocityImpact = _scene$descriptor$opt.gestureVelocityImpact,\n    headerMode = _scene$descriptor$opt.headerMode,\n    headerShown = _scene$descriptor$opt.headerShown,\n    transitionSpec = _scene$descriptor$opt.transitionSpec;\n  var previousScene = getPreviousScene({\n    route: scene.descriptor.route\n  });\n  var backTitle;\n  if (previousScene) {\n    var _previousScene$descri = previousScene.descriptor,\n      options = _previousScene$descri.options,\n      route = _previousScene$descri.route;\n    backTitle = getHeaderTitle(options, route.name);\n  }\n  var headerBack = React.useMemo(function () {\n    return backTitle !== undefined ? {\n      title: backTitle\n    } : undefined;\n  }, [backTitle]);\n  return React.createElement(Card, {\n    interpolationIndex: interpolationIndex,\n    gestureDirection: gestureDirection,\n    layout: layout,\n    insets: insets,\n    gesture: gesture,\n    current: scene.progress.current,\n    next: scene.progress.next,\n    closing: closing,\n    onOpen: handleOpen,\n    onClose: handleClose,\n    overlay: cardOverlay,\n    overlayEnabled: cardOverlayEnabled,\n    shadowEnabled: cardShadowEnabled,\n    onTransition: handleTransition,\n    onGestureBegin: handleGestureBegin,\n    onGestureCanceled: handleGestureCanceled,\n    onGestureEnd: handleGestureEnd,\n    gestureEnabled: index === 0 ? false : gestureEnabled,\n    gestureResponseDistance: gestureResponseDistance,\n    gestureVelocityImpact: gestureVelocityImpact,\n    transitionSpec: transitionSpec,\n    styleInterpolator: cardStyleInterpolator,\n    accessibilityElementsHidden: !focused,\n    importantForAccessibility: focused ? 'auto' : 'no-hide-descendants',\n    pointerEvents: active ? 'box-none' : pointerEvents,\n    pageOverflowEnabled: headerMode !== 'float' && presentation !== 'modal',\n    headerDarkContent: headerDarkContent,\n    containerStyle: hasAbsoluteFloatHeader && headerMode !== 'screen' ? {\n      marginTop: headerHeight\n    } : null,\n    contentStyle: [{\n      backgroundColor: presentation === 'transparentModal' ? 'transparent' : colors.background\n    }, cardStyle],\n    style: [{\n      overflow: active ? undefined : 'hidden',\n      display: animationEnabled === false && isNextScreenTransparent === false && detachCurrentScreen !== false && !focused ? 'none' : 'flex'\n    }, StyleSheet.absoluteFill]\n  }, React.createElement(View, {\n    style: styles.container\n  }, React.createElement(ModalPresentationContext.Provider, {\n    value: modal\n  }, React.createElement(View, {\n    style: styles.scene\n  }, React.createElement(HeaderBackContext.Provider, {\n    value: headerBack\n  }, React.createElement(HeaderShownContext.Provider, {\n    value: isParentHeaderShown || headerShown !== false\n  }, React.createElement(HeaderHeightContext.Provider, {\n    value: headerShown ? headerHeight : parentHeaderHeight != null ? parentHeaderHeight : 0\n  }, renderScene({\n    route: scene.descriptor.route\n  }))))), headerMode !== 'float' ? renderHeader({\n    mode: 'screen',\n    layout: layout,\n    scenes: [previousScene, scene],\n    getPreviousScene: getPreviousScene,\n    getFocusedRoute: getFocusedRoute,\n    onContentHeightChange: onHeaderHeightChange\n  }) : null)));\n}\nexport default React.memo(CardContainer);\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    flexDirection: 'column-reverse'\n  },\n  scene: {\n    flex: 1\n  }\n});", "map": {"version": 3, "names": ["getHeaderTitle", "HeaderBackContext", "HeaderHeightContext", "HeaderShownContext", "useTheme", "React", "StyleSheet", "View", "ModalPresentationContext", "useKeyboardManager", "Card", "EPSILON", "CardContainer", "_ref", "interpolationIndex", "index", "active", "closing", "gesture", "focused", "modal", "getPreviousScene", "getFocusedRoute", "headerDarkContent", "hasAbsoluteFloatHeader", "headerHeight", "onHeaderHeightChange", "isParentHeaderShown", "isNextScreenTransparent", "detachCurrentScreen", "layout", "onCloseRoute", "onOpenRoute", "onGestureCancel", "onGestureEnd", "onGestureStart", "onTransitionEnd", "onTransitionStart", "renderHeader", "renderScene", "safeAreaInsetBottom", "safeAreaInsetLeft", "safeAreaInsetRight", "safeAreaInsetTop", "scene", "parentHeaderHeight", "useContext", "_useKeyboardManager", "useCallback", "_scene$descriptor", "descriptor", "options", "navigation", "isFocused", "keyboardHandlingEnabled", "onPageChangeStart", "onPageChangeCancel", "onPageChangeConfirm", "handleOpen", "route", "handleClose", "handleGestureBegin", "handleGestureCanceled", "handleGestureEnd", "handleTransition", "_ref2", "insets", "top", "right", "bottom", "left", "_useTheme", "colors", "_React$useState", "useState", "_React$useState2", "_slicedToArray", "pointerEvents", "setPointerEvents", "useEffect", "_scene$progress$next", "_scene$progress$next$", "listener", "progress", "next", "addListener", "call", "_ref3", "value", "_scene$progress$next2", "_scene$progress$next3", "removeListener", "_scene$descriptor$opt", "presentation", "animationEnabled", "cardOverlay", "cardOverlayEnabled", "cardShadowEnabled", "cardStyle", "cardStyleInterpolator", "gestureDirection", "gestureEnabled", "gestureResponseDistance", "gestureVelocityImpact", "headerMode", "headerShown", "transitionSpec", "previousScene", "backTitle", "_previousScene$descri", "name", "headerBack", "useMemo", "undefined", "title", "createElement", "current", "onOpen", "onClose", "overlay", "overlayEnabled", "shadowEnabled", "onTransition", "onGestureBegin", "onGestureCanceled", "styleInterpolator", "accessibilityElementsHidden", "importantForAccessibility", "pageOverflowEnabled", "containerStyle", "marginTop", "contentStyle", "backgroundColor", "background", "style", "overflow", "display", "absoluteFill", "styles", "container", "Provider", "mode", "scenes", "onContentHeightChange", "memo", "create", "flex", "flexDirection"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\@react-navigation\\stack\\src\\views\\Stack\\CardContainer.tsx"], "sourcesContent": ["import {\n  getHead<PERSON><PERSON><PERSON><PERSON>,\n  HeaderBackContext,\n  HeaderHeightContext,\n  HeaderShownContext,\n} from '@react-navigation/elements';\nimport { Route, useTheme } from '@react-navigation/native';\nimport * as React from 'react';\nimport { Animated, StyleSheet, View } from 'react-native';\n\nimport type { Layout, Scene } from '../../types';\nimport ModalPresentationContext from '../../utils/ModalPresentationContext';\nimport useKeyboardManager from '../../utils/useKeyboardManager';\nimport type { Props as HeaderContainerProps } from '../Header/HeaderContainer';\nimport Card from './Card';\n\ntype Props = {\n  interpolationIndex: number;\n  index: number;\n  active: boolean;\n  focused: boolean;\n  closing: boolean;\n  modal: boolean;\n  layout: Layout;\n  gesture: Animated.Value;\n  scene: Scene;\n  headerDarkContent: boolean | undefined;\n  safeAreaInsetTop: number;\n  safeAreaInsetRight: number;\n  safeAreaInsetBottom: number;\n  safeAreaInsetLeft: number;\n  getPreviousScene: (props: { route: Route<string> }) => Scene | undefined;\n  getFocusedRoute: () => Route<string>;\n  renderHeader: (props: HeaderContainerProps) => React.ReactNode;\n  renderScene: (props: { route: Route<string> }) => React.ReactNode;\n  onOpenRoute: (props: { route: Route<string> }) => void;\n  onCloseRoute: (props: { route: Route<string> }) => void;\n  onTransitionStart: (\n    props: { route: Route<string> },\n    closing: boolean\n  ) => void;\n  onTransitionEnd: (props: { route: Route<string> }, closing: boolean) => void;\n  onGestureStart: (props: { route: Route<string> }) => void;\n  onGestureEnd: (props: { route: Route<string> }) => void;\n  onGestureCancel: (props: { route: Route<string> }) => void;\n  hasAbsoluteFloatHeader: boolean;\n  headerHeight: number;\n  onHeaderHeightChange: (props: {\n    route: Route<string>;\n    height: number;\n  }) => void;\n  isParentHeaderShown: boolean;\n  isNextScreenTransparent: boolean;\n  detachCurrentScreen: boolean;\n};\n\nconst EPSILON = 0.1;\n\nfunction CardContainer({\n  interpolationIndex,\n  index,\n  active,\n  closing,\n  gesture,\n  focused,\n  modal,\n  getPreviousScene,\n  getFocusedRoute,\n  headerDarkContent,\n  hasAbsoluteFloatHeader,\n  headerHeight,\n  onHeaderHeightChange,\n  isParentHeaderShown,\n  isNextScreenTransparent,\n  detachCurrentScreen,\n  layout,\n  onCloseRoute,\n  onOpenRoute,\n  onGestureCancel,\n  onGestureEnd,\n  onGestureStart,\n  onTransitionEnd,\n  onTransitionStart,\n  renderHeader,\n  renderScene,\n  safeAreaInsetBottom,\n  safeAreaInsetLeft,\n  safeAreaInsetRight,\n  safeAreaInsetTop,\n  scene,\n}: Props) {\n  const parentHeaderHeight = React.useContext(HeaderHeightContext);\n\n  const { onPageChangeStart, onPageChangeCancel, onPageChangeConfirm } =\n    useKeyboardManager(\n      React.useCallback(() => {\n        const { options, navigation } = scene.descriptor;\n\n        return (\n          navigation.isFocused() && options.keyboardHandlingEnabled !== false\n        );\n      }, [scene.descriptor])\n    );\n\n  const handleOpen = () => {\n    const { route } = scene.descriptor;\n\n    onTransitionEnd({ route }, false);\n    onOpenRoute({ route });\n  };\n\n  const handleClose = () => {\n    const { route } = scene.descriptor;\n\n    onTransitionEnd({ route }, true);\n    onCloseRoute({ route });\n  };\n\n  const handleGestureBegin = () => {\n    const { route } = scene.descriptor;\n\n    onPageChangeStart();\n    onGestureStart({ route });\n  };\n\n  const handleGestureCanceled = () => {\n    const { route } = scene.descriptor;\n\n    onPageChangeCancel();\n    onGestureCancel({ route });\n  };\n\n  const handleGestureEnd = () => {\n    const { route } = scene.descriptor;\n\n    onGestureEnd({ route });\n  };\n\n  const handleTransition = ({\n    closing,\n    gesture,\n  }: {\n    closing: boolean;\n    gesture: boolean;\n  }) => {\n    const { route } = scene.descriptor;\n\n    if (!gesture) {\n      onPageChangeConfirm?.(true);\n    } else if (active && closing) {\n      onPageChangeConfirm?.(false);\n    } else {\n      onPageChangeCancel?.();\n    }\n\n    onTransitionStart?.({ route }, closing);\n  };\n\n  const insets = {\n    top: safeAreaInsetTop,\n    right: safeAreaInsetRight,\n    bottom: safeAreaInsetBottom,\n    left: safeAreaInsetLeft,\n  };\n\n  const { colors } = useTheme();\n\n  const [pointerEvents, setPointerEvents] = React.useState<'box-none' | 'none'>(\n    'box-none'\n  );\n\n  React.useEffect(() => {\n    const listener = scene.progress.next?.addListener?.(\n      ({ value }: { value: number }) => {\n        setPointerEvents(value <= EPSILON ? 'box-none' : 'none');\n      }\n    );\n\n    return () => {\n      if (listener) {\n        scene.progress.next?.removeListener?.(listener);\n      }\n    };\n  }, [pointerEvents, scene.progress.next]);\n\n  const {\n    presentation,\n    animationEnabled,\n    cardOverlay,\n    cardOverlayEnabled,\n    cardShadowEnabled,\n    cardStyle,\n    cardStyleInterpolator,\n    gestureDirection,\n    gestureEnabled,\n    gestureResponseDistance,\n    gestureVelocityImpact,\n    headerMode,\n    headerShown,\n    transitionSpec,\n  } = scene.descriptor.options;\n\n  const previousScene = getPreviousScene({ route: scene.descriptor.route });\n\n  let backTitle: string | undefined;\n\n  if (previousScene) {\n    const { options, route } = previousScene.descriptor;\n\n    backTitle = getHeaderTitle(options, route.name);\n  }\n\n  const headerBack = React.useMemo(\n    () => (backTitle !== undefined ? { title: backTitle } : undefined),\n    [backTitle]\n  );\n\n  return (\n    <Card\n      interpolationIndex={interpolationIndex}\n      gestureDirection={gestureDirection}\n      layout={layout}\n      insets={insets}\n      gesture={gesture}\n      current={scene.progress.current}\n      next={scene.progress.next}\n      closing={closing}\n      onOpen={handleOpen}\n      onClose={handleClose}\n      overlay={cardOverlay}\n      overlayEnabled={cardOverlayEnabled}\n      shadowEnabled={cardShadowEnabled}\n      onTransition={handleTransition}\n      onGestureBegin={handleGestureBegin}\n      onGestureCanceled={handleGestureCanceled}\n      onGestureEnd={handleGestureEnd}\n      gestureEnabled={index === 0 ? false : gestureEnabled}\n      gestureResponseDistance={gestureResponseDistance}\n      gestureVelocityImpact={gestureVelocityImpact}\n      transitionSpec={transitionSpec}\n      styleInterpolator={cardStyleInterpolator}\n      accessibilityElementsHidden={!focused}\n      importantForAccessibility={focused ? 'auto' : 'no-hide-descendants'}\n      pointerEvents={active ? 'box-none' : pointerEvents}\n      pageOverflowEnabled={headerMode !== 'float' && presentation !== 'modal'}\n      headerDarkContent={headerDarkContent}\n      containerStyle={\n        hasAbsoluteFloatHeader && headerMode !== 'screen'\n          ? { marginTop: headerHeight }\n          : null\n      }\n      contentStyle={[\n        {\n          backgroundColor:\n            presentation === 'transparentModal'\n              ? 'transparent'\n              : colors.background,\n        },\n        cardStyle,\n      ]}\n      style={[\n        {\n          // This is necessary to avoid unfocused larger pages increasing scroll area\n          // The issue can be seen on the web when a smaller screen is pushed over a larger one\n          overflow: active ? undefined : 'hidden',\n          display:\n            // Hide unfocused screens when animation isn't enabled\n            // This is also necessary for a11y on web\n            animationEnabled === false &&\n            isNextScreenTransparent === false &&\n            detachCurrentScreen !== false &&\n            !focused\n              ? 'none'\n              : 'flex',\n        },\n        StyleSheet.absoluteFill,\n      ]}\n    >\n      <View style={styles.container}>\n        <ModalPresentationContext.Provider value={modal}>\n          <View style={styles.scene}>\n            <HeaderBackContext.Provider value={headerBack}>\n              <HeaderShownContext.Provider\n                value={isParentHeaderShown || headerShown !== false}\n              >\n                <HeaderHeightContext.Provider\n                  value={headerShown ? headerHeight : parentHeaderHeight ?? 0}\n                >\n                  {renderScene({ route: scene.descriptor.route })}\n                </HeaderHeightContext.Provider>\n              </HeaderShownContext.Provider>\n            </HeaderBackContext.Provider>\n          </View>\n          {headerMode !== 'float'\n            ? renderHeader({\n                mode: 'screen',\n                layout,\n                scenes: [previousScene, scene],\n                getPreviousScene,\n                getFocusedRoute,\n                onContentHeightChange: onHeaderHeightChange,\n              })\n            : null}\n        </ModalPresentationContext.Provider>\n      </View>\n    </Card>\n  );\n}\n\nexport default React.memo(CardContainer);\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    flexDirection: 'column-reverse',\n  },\n  scene: {\n    flex: 1,\n  },\n});\n"], "mappings": ";AAAA,SACEA,cAAc,EACdC,iBAAiB,EACjBC,mBAAmB,EACnBC,kBAAkB,QACb,4BAA4B;AACnC,SAAgBC,QAAQ,QAAQ,0BAA0B;AAC1D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAI9B,OAAOC,wBAAwB;AAC/B,OAAOC,kBAAkB;AAEzB,OAAOC,IAAI;AA0CX,IAAMC,OAAO,GAAG,GAAG;AAEnB,SAASC,aAAaA,CAAAC,IAAA,EAgCZ;EAAA,IA/BRC,kBAAkB,GA+BZD,IAAA,CA/BNC,kBAAkB;IAClBC,KAAK,GA8BCF,IAAA,CA9BNE,KAAK;IACLC,MAAM,GA6BAH,IAAA,CA7BNG,MAAM;IACNC,OAAO,GA4BDJ,IAAA,CA5BNI,OAAO;IACPC,OAAO,GA2BDL,IAAA,CA3BNK,OAAO;IACPC,OAAO,GA0BDN,IAAA,CA1BNM,OAAO;IACPC,KAAK,GAyBCP,IAAA,CAzBNO,KAAK;IACLC,gBAAgB,GAwBVR,IAAA,CAxBNQ,gBAAgB;IAChBC,eAAe,GAuBTT,IAAA,CAvBNS,eAAe;IACfC,iBAAiB,GAsBXV,IAAA,CAtBNU,iBAAiB;IACjBC,sBAAsB,GAqBhBX,IAAA,CArBNW,sBAAsB;IACtBC,YAAY,GAoBNZ,IAAA,CApBNY,YAAY;IACZC,oBAAoB,GAmBdb,IAAA,CAnBNa,oBAAoB;IACpBC,mBAAmB,GAkBbd,IAAA,CAlBNc,mBAAmB;IACnBC,uBAAuB,GAiBjBf,IAAA,CAjBNe,uBAAuB;IACvBC,mBAAmB,GAgBbhB,IAAA,CAhBNgB,mBAAmB;IACnBC,MAAM,GAeAjB,IAAA,CAfNiB,MAAM;IACNC,YAAY,GAcNlB,IAAA,CAdNkB,YAAY;IACZC,WAAW,GAaLnB,IAAA,CAbNmB,WAAW;IACXC,eAAe,GAYTpB,IAAA,CAZNoB,eAAe;IACfC,YAAY,GAWNrB,IAAA,CAXNqB,YAAY;IACZC,cAAc,GAURtB,IAAA,CAVNsB,cAAc;IACdC,eAAe,GASTvB,IAAA,CATNuB,eAAe;IACfC,iBAAiB,GAQXxB,IAAA,CARNwB,iBAAiB;IACjBC,YAAY,GAONzB,IAAA,CAPNyB,YAAY;IACZC,WAAW,GAML1B,IAAA,CANN0B,WAAW;IACXC,mBAAmB,GAKb3B,IAAA,CALN2B,mBAAmB;IACnBC,iBAAiB,GAIX5B,IAAA,CAJN4B,iBAAiB;IACjBC,kBAAkB,GAGZ7B,IAAA,CAHN6B,kBAAkB;IAClBC,gBAAgB,GAEV9B,IAAA,CAFN8B,gBAAgB;IAChBC,KAAA,GACM/B,IAAA,CADN+B,KAAA;EAEA,IAAMC,kBAAkB,GAAGxC,KAAK,CAACyC,UAAU,CAAC5C,mBAAmB,CAAC;EAEhE,IAAA6C,mBAAA,GACEtC,kBAAkB,CAChBJ,KAAK,CAAC2C,WAAW,CAAC,YAAM;MACtB,IAAAC,iBAAA,GAAgCL,KAAK,CAACM,UAAU;QAAxCC,OAAO,GAAAF,iBAAA,CAAPE,OAAO;QAAEC,UAAA,GAAAH,iBAAA,CAAAG,UAAA;MAEjB,OACEA,UAAU,CAACC,SAAS,EAAE,IAAIF,OAAO,CAACG,uBAAuB,KAAK,KAAK;IAEvE,CAAC,EAAE,CAACV,KAAK,CAACM,UAAU,CAAC,CAAC,CACvB;IATKK,iBAAiB,GAAAR,mBAAA,CAAjBQ,iBAAiB;IAAEC,kBAAkB,GAAAT,mBAAA,CAAlBS,kBAAkB;IAAEC,mBAAA,GAAAV,mBAAA,CAAAU,mBAAA;EAW/C,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;IACvB,IAAQC,KAAA,GAAUf,KAAK,CAACM,UAAU,CAA1BS,KAAA;IAERvB,eAAe,CAAC;MAAEuB,KAAA,EAAAA;IAAM,CAAC,EAAE,KAAK,CAAC;IACjC3B,WAAW,CAAC;MAAE2B,KAAA,EAAAA;IAAM,CAAC,CAAC;EACxB,CAAC;EAED,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;IACxB,IAAQD,KAAA,GAAUf,KAAK,CAACM,UAAU,CAA1BS,KAAA;IAERvB,eAAe,CAAC;MAAEuB,KAAA,EAAAA;IAAM,CAAC,EAAE,IAAI,CAAC;IAChC5B,YAAY,CAAC;MAAE4B,KAAA,EAAAA;IAAM,CAAC,CAAC;EACzB,CAAC;EAED,IAAME,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAA,EAAS;IAC/B,IAAQF,KAAA,GAAUf,KAAK,CAACM,UAAU,CAA1BS,KAAA;IAERJ,iBAAiB,EAAE;IACnBpB,cAAc,CAAC;MAAEwB,KAAA,EAAAA;IAAM,CAAC,CAAC;EAC3B,CAAC;EAED,IAAMG,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAA,EAAS;IAClC,IAAQH,KAAA,GAAUf,KAAK,CAACM,UAAU,CAA1BS,KAAA;IAERH,kBAAkB,EAAE;IACpBvB,eAAe,CAAC;MAAE0B,KAAA,EAAAA;IAAM,CAAC,CAAC;EAC5B,CAAC;EAED,IAAMI,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA,EAAS;IAC7B,IAAQJ,KAAA,GAAUf,KAAK,CAACM,UAAU,CAA1BS,KAAA;IAERzB,YAAY,CAAC;MAAEyB,KAAA,EAAAA;IAAM,CAAC,CAAC;EACzB,CAAC;EAED,IAAMK,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAGC,KAAA,EAMnB;IAAA,IALJhD,OAAO,GAKRgD,KAAA,CALChD,OAAO;MACPC,OAAA,GAID+C,KAAA,CAJC/C,OAAA;IAKA,IAAQyC,KAAA,GAAUf,KAAK,CAACM,UAAU,CAA1BS,KAAA;IAER,IAAI,CAACzC,OAAO,EAAE;MACZuC,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAG,IAAI,CAAC;IAC7B,CAAC,MAAM,IAAIzC,MAAM,IAAIC,OAAO,EAAE;MAC5BwC,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAG,KAAK,CAAC;IAC9B,CAAC,MAAM;MACLD,kBAAkB,aAAlBA,kBAAkB,uBAAlBA,kBAAkB,EAAI;IACxB;IAEAnB,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAG;MAAEsB,KAAA,EAAAA;IAAM,CAAC,EAAE1C,OAAO,CAAC;EACzC,CAAC;EAED,IAAMiD,MAAM,GAAG;IACbC,GAAG,EAAExB,gBAAgB;IACrByB,KAAK,EAAE1B,kBAAkB;IACzB2B,MAAM,EAAE7B,mBAAmB;IAC3B8B,IAAI,EAAE7B;EACR,CAAC;EAED,IAAA8B,SAAA,GAAmBnE,QAAQ,EAAE;IAArBoE,MAAA,GAAAD,SAAA,CAAAC,MAAA;EAER,IAAAC,eAAA,GAA0CpE,KAAK,CAACqE,QAAQ,CACtD,UAAU,CACX;IAAAC,gBAAA,GAAAC,cAAA,CAAAH,eAAA;IAFMI,aAAa,GAAAF,gBAAA;IAAEG,gBAAgB,GAAAH,gBAAA;EAItCtE,KAAK,CAAC0E,SAAS,CAAC,YAAM;IAAA,IAAAC,oBAAA,EAAAC,qBAAA;IACpB,IAAMC,QAAQ,IAAAF,oBAAA,GAAGpC,KAAK,CAACuC,QAAQ,CAACC,IAAI,cAAAJ,oBAAA,wBAAAC,qBAAA,GAAnBD,oBAAA,CAAqBK,WAAW,cAAAJ,qBAAA,uBAAhCA,qBAAA,CAAAK,IAAA,CAAAN,oBAAA,EACf,UAAAO,KAAA,EAAkC;MAAA,IAA/BC,KAAA,GAA0BD,KAAA,CAA1BC,KAAA;MACDV,gBAAgB,CAACU,KAAK,IAAI7E,OAAO,GAAG,UAAU,GAAG,MAAM,CAAC;IAC1D,CAAC,CACF;IAED,OAAO,YAAM;MACX,IAAIuE,QAAQ,EAAE;QAAA,IAAAO,qBAAA,EAAAC,qBAAA;QACZ,CAAAD,qBAAA,GAAA7C,KAAK,CAACuC,QAAQ,CAACC,IAAI,cAAAK,qBAAA,wBAAAC,qBAAA,GAAnBD,qBAAA,CAAqBE,cAAc,cAAAD,qBAAA,uBAAnCA,qBAAA,CAAAJ,IAAA,CAAAG,qBAAA,EAAsCP,QAAQ,CAAC;MACjD;IACF,CAAC;EACH,CAAC,EAAE,CAACL,aAAa,EAAEjC,KAAK,CAACuC,QAAQ,CAACC,IAAI,CAAC,CAAC;EAExC,IAAAQ,qBAAA,GAeIhD,KAAK,CAACM,UAAU,CAACC,OAAO;IAd1B0C,YAAY,GAAAD,qBAAA,CAAZC,YAAY;IACZC,gBAAgB,GAAAF,qBAAA,CAAhBE,gBAAgB;IAChBC,WAAW,GAAAH,qBAAA,CAAXG,WAAW;IACXC,kBAAkB,GAAAJ,qBAAA,CAAlBI,kBAAkB;IAClBC,iBAAiB,GAAAL,qBAAA,CAAjBK,iBAAiB;IACjBC,SAAS,GAAAN,qBAAA,CAATM,SAAS;IACTC,qBAAqB,GAAAP,qBAAA,CAArBO,qBAAqB;IACrBC,gBAAgB,GAAAR,qBAAA,CAAhBQ,gBAAgB;IAChBC,cAAc,GAAAT,qBAAA,CAAdS,cAAc;IACdC,uBAAuB,GAAAV,qBAAA,CAAvBU,uBAAuB;IACvBC,qBAAqB,GAAAX,qBAAA,CAArBW,qBAAqB;IACrBC,UAAU,GAAAZ,qBAAA,CAAVY,UAAU;IACVC,WAAW,GAAAb,qBAAA,CAAXa,WAAW;IACXC,cAAA,GAAAd,qBAAA,CAAAc,cAAA;EAGF,IAAMC,aAAa,GAAGtF,gBAAgB,CAAC;IAAEsC,KAAK,EAAEf,KAAK,CAACM,UAAU,CAACS;EAAM,CAAC,CAAC;EAEzE,IAAIiD,SAA6B;EAEjC,IAAID,aAAa,EAAE;IACjB,IAAAE,qBAAA,GAA2BF,aAAa,CAACzD,UAAU;MAA3CC,OAAO,GAAA0D,qBAAA,CAAP1D,OAAO;MAAEQ,KAAA,GAAAkD,qBAAA,CAAAlD,KAAA;IAEjBiD,SAAS,GAAG5G,cAAc,CAACmD,OAAO,EAAEQ,KAAK,CAACmD,IAAI,CAAC;EACjD;EAEA,IAAMC,UAAU,GAAG1G,KAAK,CAAC2G,OAAO,CAC9B;IAAA,OAAOJ,SAAS,KAAKK,SAAS,GAAG;MAAEC,KAAK,EAAEN;IAAU,CAAC,GAAGK,SAAU;EAAA,GAClE,CAACL,SAAS,CAAC,CACZ;EAED,OACEvG,KAAA,CAAA8G,aAAA,CAACzG,IAAI;IACHI,kBAAkB,EAAEA,kBAAmB;IACvCsF,gBAAgB,EAAEA,gBAAiB;IACnCtE,MAAM,EAAEA,MAAO;IACfoC,MAAM,EAAEA,MAAO;IACfhD,OAAO,EAAEA,OAAQ;IACjBkG,OAAO,EAAExE,KAAK,CAACuC,QAAQ,CAACiC,OAAQ;IAChChC,IAAI,EAAExC,KAAK,CAACuC,QAAQ,CAACC,IAAK;IAC1BnE,OAAO,EAAEA,OAAQ;IACjBoG,MAAM,EAAE3D,UAAW;IACnB4D,OAAO,EAAE1D,WAAY;IACrB2D,OAAO,EAAExB,WAAY;IACrByB,cAAc,EAAExB,kBAAmB;IACnCyB,aAAa,EAAExB,iBAAkB;IACjCyB,YAAY,EAAE1D,gBAAiB;IAC/B2D,cAAc,EAAE9D,kBAAmB;IACnC+D,iBAAiB,EAAE9D,qBAAsB;IACzC5B,YAAY,EAAE6B,gBAAiB;IAC/BsC,cAAc,EAAEtF,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGsF,cAAe;IACrDC,uBAAuB,EAAEA,uBAAwB;IACjDC,qBAAqB,EAAEA,qBAAsB;IAC7CG,cAAc,EAAEA,cAAe;IAC/BmB,iBAAiB,EAAE1B,qBAAsB;IACzC2B,2BAA2B,EAAE,CAAC3G,OAAQ;IACtC4G,yBAAyB,EAAE5G,OAAO,GAAG,MAAM,GAAG,qBAAsB;IACpE0D,aAAa,EAAE7D,MAAM,GAAG,UAAU,GAAG6D,aAAc;IACnDmD,mBAAmB,EAAExB,UAAU,KAAK,OAAO,IAAIX,YAAY,KAAK,OAAQ;IACxEtE,iBAAiB,EAAEA,iBAAkB;IACrC0G,cAAc,EACZzG,sBAAsB,IAAIgF,UAAU,KAAK,QAAQ,GAC7C;MAAE0B,SAAS,EAAEzG;IAAa,CAAC,GAC3B,IACL;IACD0G,YAAY,EAAE,CACZ;MACEC,eAAe,EACbvC,YAAY,KAAK,kBAAkB,GAC/B,aAAa,GACbrB,MAAM,CAAC6D;IACf,CAAC,EACDnC,SAAS,CACT;IACFoC,KAAK,EAAE,CACL;MAGEC,QAAQ,EAAEvH,MAAM,GAAGiG,SAAS,GAAG,QAAQ;MACvCuB,OAAO,EAGL1C,gBAAgB,KAAK,KAAK,IAC1BlE,uBAAuB,KAAK,KAAK,IACjCC,mBAAmB,KAAK,KAAK,IAC7B,CAACV,OAAO,GACJ,MAAM,GACN;IACR,CAAC,EACDb,UAAU,CAACmI,YAAY;EACvB,GAEFpI,KAAA,CAAA8G,aAAA,CAAC5G,IAAI;IAAC+H,KAAK,EAAEI,MAAM,CAACC;EAAU,GAC5BtI,KAAA,CAAA8G,aAAA,CAAC3G,wBAAwB,CAACoI,QAAQ;IAACpD,KAAK,EAAEpE;EAAM,GAC9Cf,KAAA,CAAA8G,aAAA,CAAC5G,IAAI;IAAC+H,KAAK,EAAEI,MAAM,CAAC9F;EAAM,GACxBvC,KAAA,CAAA8G,aAAA,CAAClH,iBAAiB,CAAC2I,QAAQ;IAACpD,KAAK,EAAEuB;EAAW,GAC5C1G,KAAA,CAAA8G,aAAA,CAAChH,kBAAkB,CAACyI,QAAQ;IAC1BpD,KAAK,EAAE7D,mBAAmB,IAAI8E,WAAW,KAAK;EAAM,GAEpDpG,KAAA,CAAA8G,aAAA,CAACjH,mBAAmB,CAAC0I,QAAQ;IAC3BpD,KAAK,EAAEiB,WAAW,GAAGhF,YAAY,GAAGoB,kBAAkB,WAAlBA,kBAAkB,GAAI;EAAE,GAE3DN,WAAW,CAAC;IAAEoB,KAAK,EAAEf,KAAK,CAACM,UAAU,CAACS;EAAM,CAAC,CAAC,CAClB,CACH,CACH,CACxB,EACN6C,UAAU,KAAK,OAAO,GACnBlE,YAAY,CAAC;IACXuG,IAAI,EAAE,QAAQ;IACd/G,MAAM,EAANA,MAAM;IACNgH,MAAM,EAAE,CAACnC,aAAa,EAAE/D,KAAK,CAAC;IAC9BvB,gBAAgB,EAAhBA,gBAAgB;IAChBC,eAAe,EAAfA,eAAe;IACfyH,qBAAqB,EAAErH;EACzB,CAAC,CAAC,GACF,IAAI,CAC0B,CAC/B,CACF;AAEX;AAEA,eAAerB,KAAK,CAAC2I,IAAI,CAACpI,aAAa,CAAC;AAExC,IAAM8H,MAAM,GAAGpI,UAAU,CAAC2I,MAAM,CAAC;EAC/BN,SAAS,EAAE;IACTO,IAAI,EAAE,CAAC;IACPC,aAAa,EAAE;EACjB,CAAC;EACDvG,KAAK,EAAE;IACLsG,IAAI,EAAE;EACR;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}