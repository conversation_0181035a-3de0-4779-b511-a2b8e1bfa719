{"ast": null, "code": "if (__DEV__) {\n  if (!('__fbBatchedBridgeConfig' in global)) {\n    Object.defineProperty(global, '__fbBatchedBridgeConfig', {\n      get: function get() {\n        throw new Error(\"Your web project is importing a module from 'react-native' instead of 'react-native-web'. Learn more: https://expo.fyi/fb-batched-bridge-config-web\");\n      }\n    });\n  }\n}", "map": {"version": 3, "names": ["__DEV__", "global", "Object", "defineProperty", "get", "Error"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\expo\\src\\Expo.fx.web.tsx"], "sourcesContent": ["// When users dangerously import a file inside of react-native, it breaks the web alias.\n// This is one of the most common, and cryptic web errors that users encounter.\n// This conditional side-effect provides a more helpful error message for debugging.\n// Use a wrapper `__DEV__` to remove this entire block in production.\nif (__DEV__) {\n  if (\n    // Skip mocking if someone is shimming this value out.\n    !('__fbBatchedBridgeConfig' in global)\n  ) {\n    Object.defineProperty(global, '__fbBatchedBridgeConfig', {\n      get() {\n        throw new Error(\n          \"Your web project is importing a module from 'react-native' instead of 'react-native-web'. Learn more: https://expo.fyi/fb-batched-bridge-config-web\"\n        );\n      },\n    });\n  }\n}\n"], "mappings": "AAIA,IAAIA,OAAO,EAAE;EACX,IAEE,EAAE,yBAAyB,IAAIC,MAAM,CAAC,EACtC;IACAC,MAAM,CAACC,cAAc,CAACF,MAAM,EAAE,yBAAyB,EAAE;MACvDG,GAAG,WAAHA,GAAGA,CAAA;QACD,MAAM,IAAIC,KAAK,CACb,qJAAqJ,CACtJ;MACH;KACD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}