{"ast": null, "code": "var touchableEvents = ['onPress', 'onLongPress', 'onPressIn', 'onPressOut'];\nexport default function hasTouchHandler(touchableEventObject) {\n  return touchableEvents.some(function (event) {\n    return Boolean(touchableEventObject[event]);\n  });\n}", "map": {"version": 3, "names": ["touchableEvents", "has<PERSON>ou<PERSON><PERSON><PERSON><PERSON>", "touchableEventObject", "some", "event", "Boolean"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\utils\\hasTouchHandler.tsx"], "sourcesContent": ["import type { GestureResponderEvent } from 'react-native';\n\nconst touchableEvents = [\n  'onPress',\n  'onLongPress',\n  'onPressIn',\n  'onPressOut',\n] as const;\n\ntype TouchableEventObject = Partial<\n  Record<\n    (typeof touchableEvents)[number],\n    (event: GestureResponderEvent) => void\n  >\n>;\n\nexport default function hasTouchHandler(\n  touchableEventObject: TouchableEventObject\n) {\n  return touchableEvents.some((event) => {\n    return Boolean(touchableEventObject[event]);\n  });\n}\n"], "mappings": "AAEA,IAAMA,eAAe,GAAG,CACtB,SAAS,EACT,aAAa,EACb,WAAW,EACX,YAAY,CACJ;AASV,eAAe,SAASC,eAAeA,CACrCC,oBAA0C,EAC1C;EACA,OAAOF,eAAe,CAACG,IAAI,CAAE,UAAAC,KAAK,EAAK;IACrC,OAAOC,OAAO,CAACH,oBAAoB,CAACE,KAAK,CAAC,CAAC;EAC7C,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}