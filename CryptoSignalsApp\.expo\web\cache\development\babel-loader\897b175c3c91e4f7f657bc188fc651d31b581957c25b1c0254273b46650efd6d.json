{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport React, { useState } from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport styles from \"./styles\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar CheckButtons = function CheckButtons(_ref) {\n  var onFiltered = _ref.onFiltered;\n  var _useState = useState({\n      all: true,\n      free: false,\n      premium: false,\n      spot: false,\n      futures: false\n    }),\n    _useState2 = _slicedToArray(_useState, 2),\n    isActive = _useState2[0],\n    setIsActive = _useState2[1];\n  var handleClick = function handleClick(item) {\n    if (item === 'all') {\n      var active = {\n        all: true,\n        free: false,\n        premium: false,\n        spot: false,\n        futures: false\n      };\n      setIsActive(_objectSpread({}, active));\n      onFiltered(active);\n      return;\n    }\n    isActive[item] = !isActive[item];\n    isActive.all = false;\n    allCheckButtonsNotFilled();\n    setIsActive(_objectSpread({}, isActive));\n    onFiltered(isActive);\n  };\n  var allCheckButtonsNotFilled = function allCheckButtonsNotFilled() {\n    if (!isActive.all && !isActive.free && !isActive.premium && !isActive.spot && !isActive.futures) {\n      isActive.all = true;\n    }\n  };\n  return _jsxs(View, {\n    style: styles.container,\n    children: [_jsx(Text, {\n      style: [styles.btn, styles.firstBtn, isActive.all ? styles.btnChecked : null],\n      onPress: function onPress() {\n        return handleClick('all');\n      },\n      children: \"All\"\n    }), _jsx(Text, {\n      style: [styles.btn, isActive.free ? styles.btnChecked : null],\n      onPress: function onPress() {\n        return handleClick('free');\n      },\n      children: \"Free\"\n    }), _jsx(Text, {\n      style: [styles.btn, isActive.premium ? styles.btnChecked : null],\n      onPress: function onPress() {\n        return handleClick('premium');\n      },\n      children: \"Premium\"\n    }), _jsx(Text, {\n      style: [styles.btn, isActive.spot ? styles.btnChecked : null],\n      onPress: function onPress() {\n        return handleClick('spot');\n      },\n      children: \"Spot\"\n    }), _jsx(Text, {\n      style: [styles.btn, styles.lastBtn, isActive.futures ? styles.btnChecked : null],\n      onPress: function onPress() {\n        return handleClick('futures');\n      },\n      children: \"Futures\"\n    })]\n  });\n};\nexport default CheckButtons;", "map": {"version": 3, "names": ["React", "useState", "View", "Text", "styles", "jsx", "_jsx", "jsxs", "_jsxs", "CheckButtons", "_ref", "onFiltered", "_useState", "all", "free", "premium", "spot", "futures", "_useState2", "_slicedToArray", "isActive", "setIsActive", "handleClick", "item", "active", "_objectSpread", "allCheckButtonsNotFilled", "style", "container", "children", "btn", "firstBtn", "btnChecked", "onPress", "lastBtn"], "sources": ["E:/CryptoSignalsApp/src/components/CheckButtons/index.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { View, Text } from 'react-native';\r\nimport styles from './styles';\r\n\r\nconst CheckButtons = ({ onFiltered }) => {\r\n  const [isActive, setIsActive] = useState({\r\n    all: true,\r\n    free: false,\r\n    premium: false,\r\n    spot: false,\r\n    futures: false\r\n  });\r\n  \r\n  const handleClick = (item) => {\r\n    if (item === 'all') {\r\n      const active = { all: true, free: false, premium: false, spot: false, futures: false }\r\n      setIsActive({ ...active })\r\n      onFiltered(active)\r\n      return\r\n    }\r\n\r\n    isActive[item] = !isActive[item]\r\n    isActive.all = false\r\n    allCheckButtonsNotFilled()\r\n\r\n    setIsActive({ ...isActive })\r\n    onFiltered(isActive)\r\n  }\r\n\r\n  const allCheckButtonsNotFilled = () => {\r\n    if (!isActive.all && !isActive.free && !isActive.premium && !isActive.spot && !isActive.futures) {\r\n      isActive.all = true\r\n    }\r\n  }\r\n\r\n  return (\r\n    <View style={styles.container}>\r\n      <Text\r\n        style={[styles.btn, styles.firstBtn, isActive.all ?  styles.btnChecked: null]}\r\n        onPress={() => handleClick('all')}\r\n      >\r\n        All\r\n      </Text>\r\n\r\n      <Text\r\n        style={[styles.btn, isActive.free ? styles.btnChecked: null]}\r\n        onPress={() => handleClick('free')}\r\n      >\r\n        Free\r\n      </Text>\r\n\r\n      <Text\r\n        style={[styles.btn, isActive.premium ? styles.btnChecked: null]}\r\n        onPress={() => handleClick('premium')}\r\n      >\r\n        Premium\r\n      </Text>\r\n\r\n      <Text\r\n        style={[styles.btn, isActive.spot ? styles.btnChecked: null]}\r\n        onPress={() => handleClick('spot')}\r\n      >\r\n        Spot\r\n      </Text>\r\n\r\n      <Text\r\n        style={[styles.btn, styles.lastBtn, isActive.futures ? styles.btnChecked: null]}\r\n        onPress={() => handleClick('futures')}\r\n      >\r\n        Futures\r\n      </Text>\r\n    </View>\r\n  )\r\n}\r\n\r\nexport default CheckButtons;\r\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,IAAA;AAExC,OAAOC,MAAM;AAAiB,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAE9B,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAAC,IAAA,EAAuB;EAAA,IAAjBC,UAAU,GAAAD,IAAA,CAAVC,UAAU;EAChC,IAAAC,SAAA,GAAgCX,QAAQ,CAAC;MACvCY,GAAG,EAAE,IAAI;MACTC,IAAI,EAAE,KAAK;MACXC,OAAO,EAAE,KAAK;MACdC,IAAI,EAAE,KAAK;MACXC,OAAO,EAAE;IACX,CAAC,CAAC;IAAAC,UAAA,GAAAC,cAAA,CAAAP,SAAA;IANKQ,QAAQ,GAAAF,UAAA;IAAEG,WAAW,GAAAH,UAAA;EAQ5B,IAAMI,WAAW,GAAG,SAAdA,WAAWA,CAAIC,IAAI,EAAK;IAC5B,IAAIA,IAAI,KAAK,KAAK,EAAE;MAClB,IAAMC,MAAM,GAAG;QAAEX,GAAG,EAAE,IAAI;QAAEC,IAAI,EAAE,KAAK;QAAEC,OAAO,EAAE,KAAK;QAAEC,IAAI,EAAE,KAAK;QAAEC,OAAO,EAAE;MAAM,CAAC;MACtFI,WAAW,CAAAI,aAAA,KAAMD,MAAM,CAAE,CAAC;MAC1Bb,UAAU,CAACa,MAAM,CAAC;MAClB;IACF;IAEAJ,QAAQ,CAACG,IAAI,CAAC,GAAG,CAACH,QAAQ,CAACG,IAAI,CAAC;IAChCH,QAAQ,CAACP,GAAG,GAAG,KAAK;IACpBa,wBAAwB,CAAC,CAAC;IAE1BL,WAAW,CAAAI,aAAA,KAAML,QAAQ,CAAE,CAAC;IAC5BT,UAAU,CAACS,QAAQ,CAAC;EACtB,CAAC;EAED,IAAMM,wBAAwB,GAAG,SAA3BA,wBAAwBA,CAAA,EAAS;IACrC,IAAI,CAACN,QAAQ,CAACP,GAAG,IAAI,CAACO,QAAQ,CAACN,IAAI,IAAI,CAACM,QAAQ,CAACL,OAAO,IAAI,CAACK,QAAQ,CAACJ,IAAI,IAAI,CAACI,QAAQ,CAACH,OAAO,EAAE;MAC/FG,QAAQ,CAACP,GAAG,GAAG,IAAI;IACrB;EACF,CAAC;EAED,OACEL,KAAA,CAACN,IAAI;IAACyB,KAAK,EAAEvB,MAAM,CAACwB,SAAU;IAAAC,QAAA,GAC5BvB,IAAA,CAACH,IAAI;MACHwB,KAAK,EAAE,CAACvB,MAAM,CAAC0B,GAAG,EAAE1B,MAAM,CAAC2B,QAAQ,EAAEX,QAAQ,CAACP,GAAG,GAAIT,MAAM,CAAC4B,UAAU,GAAE,IAAI,CAAE;MAC9EC,OAAO,EAAE,SAATA,OAAOA,CAAA;QAAA,OAAQX,WAAW,CAAC,KAAK,CAAC;MAAA,CAAC;MAAAO,QAAA,EACnC;IAED,CAAM,CAAC,EAEPvB,IAAA,CAACH,IAAI;MACHwB,KAAK,EAAE,CAACvB,MAAM,CAAC0B,GAAG,EAAEV,QAAQ,CAACN,IAAI,GAAGV,MAAM,CAAC4B,UAAU,GAAE,IAAI,CAAE;MAC7DC,OAAO,EAAE,SAATA,OAAOA,CAAA;QAAA,OAAQX,WAAW,CAAC,MAAM,CAAC;MAAA,CAAC;MAAAO,QAAA,EACpC;IAED,CAAM,CAAC,EAEPvB,IAAA,CAACH,IAAI;MACHwB,KAAK,EAAE,CAACvB,MAAM,CAAC0B,GAAG,EAAEV,QAAQ,CAACL,OAAO,GAAGX,MAAM,CAAC4B,UAAU,GAAE,IAAI,CAAE;MAChEC,OAAO,EAAE,SAATA,OAAOA,CAAA;QAAA,OAAQX,WAAW,CAAC,SAAS,CAAC;MAAA,CAAC;MAAAO,QAAA,EACvC;IAED,CAAM,CAAC,EAEPvB,IAAA,CAACH,IAAI;MACHwB,KAAK,EAAE,CAACvB,MAAM,CAAC0B,GAAG,EAAEV,QAAQ,CAACJ,IAAI,GAAGZ,MAAM,CAAC4B,UAAU,GAAE,IAAI,CAAE;MAC7DC,OAAO,EAAE,SAATA,OAAOA,CAAA;QAAA,OAAQX,WAAW,CAAC,MAAM,CAAC;MAAA,CAAC;MAAAO,QAAA,EACpC;IAED,CAAM,CAAC,EAEPvB,IAAA,CAACH,IAAI;MACHwB,KAAK,EAAE,CAACvB,MAAM,CAAC0B,GAAG,EAAE1B,MAAM,CAAC8B,OAAO,EAAEd,QAAQ,CAACH,OAAO,GAAGb,MAAM,CAAC4B,UAAU,GAAE,IAAI,CAAE;MAChFC,OAAO,EAAE,SAATA,OAAOA,CAAA;QAAA,OAAQX,WAAW,CAAC,SAAS,CAAC;MAAA,CAAC;MAAAO,QAAA,EACvC;IAED,CAAM,CAAC;EAAA,CACH,CAAC;AAEX,CAAC;AAED,eAAepB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}