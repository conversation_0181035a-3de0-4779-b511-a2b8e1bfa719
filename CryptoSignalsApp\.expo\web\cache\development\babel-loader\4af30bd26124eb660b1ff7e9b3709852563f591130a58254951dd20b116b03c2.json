{"ast": null, "code": "import * as React from 'react';\nimport Dimensions from \"react-native-web/dist/exports/Dimensions\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport { initialWindowMetrics, SafeAreaInsetsContext, SafeAreaProvider } from 'react-native-safe-area-context';\nvar _Dimensions$get = Dimensions.get('window'),\n  _Dimensions$get$width = _Dimensions$get.width,\n  width = _Dimensions$get$width === void 0 ? 0 : _Dimensions$get$width,\n  _Dimensions$get$heigh = _Dimensions$get.height,\n  height = _Dimensions$get$heigh === void 0 ? 0 : _Dimensions$get$heigh;\nvar initialMetrics = Platform.OS === 'web' || initialWindowMetrics == null ? {\n  frame: {\n    x: 0,\n    y: 0,\n    width: width,\n    height: height\n  },\n  insets: {\n    top: 0,\n    left: 0,\n    right: 0,\n    bottom: 0\n  }\n} : initialWindowMetrics;\nexport default function SafeAreaProviderCompat(_ref) {\n  var children = _ref.children,\n    style = _ref.style;\n  return React.createElement(SafeAreaInsetsContext.Consumer, null, function (insets) {\n    if (insets) {\n      return React.createElement(View, {\n        style: [styles.container, style]\n      }, children);\n    }\n    return React.createElement(SafeAreaProvider, {\n      initialMetrics: initialMetrics,\n      style: style\n    }, children);\n  });\n}\nSafeAreaProviderCompat.initialMetrics = initialMetrics;\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1\n  }\n});", "map": {"version": 3, "names": ["React", "Dimensions", "Platform", "StyleSheet", "View", "initialWindowMetrics", "SafeAreaInsetsContext", "SafeAreaProvider", "_Dimensions$get", "get", "_Dimensions$get$width", "width", "_Dimensions$get$heigh", "height", "initialMetrics", "OS", "frame", "x", "y", "insets", "top", "left", "right", "bottom", "SafeAreaProviderCompat", "_ref", "children", "style", "createElement", "Consumer", "styles", "container", "create", "flex"], "sources": ["C:\\Users\\<USER>\\Desktop\\Signals\\CryptoSignalsApp\\node_modules\\react-native-paper\\src\\core\\SafeAreaProviderCompat.tsx"], "sourcesContent": ["/**\n * Ported from @react-navigation https://github.com/react-navigation/react-navigation/blob/main/packages/elements/src/SafeAreaProviderCompat.tsx\n */\nimport * as React from 'react';\nimport {\n  Dimensions,\n  Platform,\n  StyleProp,\n  StyleSheet,\n  View,\n  ViewStyle,\n} from 'react-native';\n\nimport {\n  initialWindowMetrics,\n  SafeAreaInsetsContext,\n  SafeAreaProvider,\n} from 'react-native-safe-area-context';\n\ntype Props = {\n  children: React.ReactNode;\n  style?: StyleProp<ViewStyle>;\n};\n\nconst { width = 0, height = 0 } = Dimensions.get('window');\n\n// To support SSR on web, we need to have empty insets for initial values\n// Otherwise there can be mismatch between SSR and client output\n// We also need to specify empty values to support tests environments\nconst initialMetrics =\n  Platform.OS === 'web' || initialWindowMetrics == null\n    ? {\n        frame: { x: 0, y: 0, width, height },\n        insets: { top: 0, left: 0, right: 0, bottom: 0 },\n      }\n    : initialWindowMetrics;\n\nexport default function SafeAreaProviderCompat({ children, style }: Props) {\n  return (\n    <SafeAreaInsetsContext.Consumer>\n      {(insets) => {\n        if (insets) {\n          // If we already have insets, don't wrap the stack in another safe area provider\n          // This avoids an issue with updates at the cost of potentially incorrect values\n          // https://github.com/react-navigation/react-navigation/issues/174\n          return <View style={[styles.container, style]}>{children}</View>;\n        }\n\n        return (\n          <SafeAreaProvider initialMetrics={initialMetrics} style={style}>\n            {children}\n          </SafeAreaProvider>\n        );\n      }}\n    </SafeAreaInsetsContext.Consumer>\n  );\n}\n\nSafeAreaProviderCompat.initialMetrics = initialMetrics;\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n  },\n});\n"], "mappings": "AAGA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,UAAA;AAAA,OAAAC,QAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAU9B,SACEC,oBAAoB,EACpBC,qBAAqB,EACrBC,gBAAgB,QACX,gCAAgC;AAOvC,IAAAC,eAAA,GAAkCP,UAAU,CAACQ,GAAG,CAAC,QAAQ,CAAC;EAAAC,qBAAA,GAAAF,eAAA,CAAlDG,KAAK;EAALA,KAAK,GAAAD,qBAAA,cAAG,CAAC,GAAAA,qBAAA;EAAAE,qBAAA,GAAAJ,eAAA,CAAEK,MAAM;EAANA,MAAM,GAAAD,qBAAA,cAAG,IAAAA,qBAAA;AAK5B,IAAME,cAAc,GAClBZ,QAAQ,CAACa,EAAE,KAAK,KAAK,IAAIV,oBAAoB,IAAI,IAAI,GACjD;EACEW,KAAK,EAAE;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEP,KAAK,EAALA,KAAK;IAAEE,MAAA,EAAAA;EAAO,CAAC;EACpCM,MAAM,EAAE;IAAEC,GAAG,EAAE,CAAC;IAAEC,IAAI,EAAE,CAAC;IAAEC,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE;AACjD,CAAC,GACDlB,oBAAoB;AAE1B,eAAe,SAASmB,sBAAsBA,CAAAC,IAAA,EAA6B;EAAA,IAA1BC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;IAAEC,KAAA,GAAAF,IAAA,CAAAE,KAAA;EACzD,OACE3B,KAAA,CAAA4B,aAAA,CAACtB,qBAAqB,CAACuB,QAAQ,QAC3B,UAAAV,MAAM,EAAK;IACX,IAAIA,MAAM,EAAE;MAIV,OAAOnB,KAAA,CAAA4B,aAAA,CAACxB,IAAI;QAACuB,KAAK,EAAE,CAACG,MAAM,CAACC,SAAS,EAAEJ,KAAK;MAAE,GAAED,QAAe,CAAC;IAClE;IAEA,OACE1B,KAAA,CAAA4B,aAAA,CAACrB,gBAAgB;MAACO,cAAc,EAAEA,cAAe;MAACa,KAAK,EAAEA;IAAM,GAC5DD,QACe,CAAC;EAEvB,CAC8B,CAAC;AAErC;AAEAF,sBAAsB,CAACV,cAAc,GAAGA,cAAc;AAEtD,IAAMgB,MAAM,GAAG3B,UAAU,CAAC6B,MAAM,CAAC;EAC/BD,SAAS,EAAE;IACTE,IAAI,EAAE;EACR;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}