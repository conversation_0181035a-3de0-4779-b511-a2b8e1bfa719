import { StyleSheet } from 'react-native';

export default StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#1a1a1a',
  },

  headerCard: {
    backgroundColor: '#2a2a2a',
    marginBottom: 16,
    borderRadius: 12,
  },

  header: {
    padding: 20,
    alignItems: 'center',
  },

  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },

  planName: {
    fontSize: 18,
    color: '#FECB37',
    marginBottom: 16,
    fontWeight: '600',
  },

  statusContainer: {
    alignItems: 'center',
  },

  statusChip: {
    backgroundColor: 'transparent',
    marginBottom: 8,
  },

  timer: {
    fontSize: 16,
    color: '#FF9800',
    fontWeight: 'bold',
  },

  networkCard: {
    backgroundColor: '#2a2a2a',
    marginBottom: 16,
    borderRadius: 12,
    padding: 16,
  },

  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 12,
  },

  networkButtons: {
    flexDirection: 'row',
    gap: 12,
  },

  networkButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#444',
    alignItems: 'center',
  },

  networkButtonSelected: {
    borderColor: '#4CAF50',
    backgroundColor: '#4CAF5020',
  },

  networkButtonText: {
    color: '#ccc',
    fontSize: 12,
    textAlign: 'center',
  },

  networkButtonTextSelected: {
    color: '#4CAF50',
    fontWeight: 'bold',
  },

  qrCard: {
    backgroundColor: '#2a2a2a',
    marginBottom: 16,
    borderRadius: 12,
    padding: 16,
  },

  qrContainer: {
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#fff',
    borderRadius: 12,
    marginBottom: 12,
  },

  qrInstructions: {
    color: '#ccc',
    fontSize: 14,
    textAlign: 'center',
  },

  detailsCard: {
    backgroundColor: '#2a2a2a',
    marginBottom: 16,
    borderRadius: 12,
    padding: 16,
  },

  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
    paddingVertical: 4,
  },

  detailLabel: {
    color: '#ccc',
    fontSize: 14,
    flex: 1,
  },

  detailValue: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
    flex: 1,
    textAlign: 'right',
  },

  copyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#333',
    padding: 8,
    borderRadius: 6,
    flex: 2,
    marginLeft: 8,
  },

  addressText: {
    color: '#fff',
    fontSize: 12,
    flex: 1,
    fontFamily: 'monospace',
  },

  copyIcon: {
    marginLeft: 8,
    fontSize: 16,
  },

  paymentId: {
    color: '#4CAF50',
    fontSize: 12,
    fontFamily: 'monospace',
    textDecorationLine: 'underline',
  },

  progressCard: {
    backgroundColor: '#2a2a2a',
    marginBottom: 16,
    borderRadius: 12,
    padding: 16,
  },

  progressBar: {
    height: 8,
    borderRadius: 4,
    marginBottom: 8,
  },

  progressText: {
    color: '#ccc',
    fontSize: 14,
    textAlign: 'center',
  },

  actionButtons: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 16,
  },

  cancelButton: {
    flex: 1,
    borderColor: '#F44336',
  },

  cancelButtonText: {
    color: '#F44336',
  },

  explorerButton: {
    flex: 1,
    backgroundColor: '#2196F3',
  },

  explorerButtonText: {
    color: '#fff',
  },

  instructionsCard: {
    backgroundColor: '#2a2a2a',
    borderRadius: 12,
    padding: 16,
  },

  instructionStep: {
    color: '#ccc',
    fontSize: 14,
    marginBottom: 8,
    lineHeight: 20,
  },

  warningsContainer: {
    marginTop: 16,
    padding: 12,
    backgroundColor: '#FF980020',
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#FF9800',
  },

  warningsTitle: {
    color: '#FF9800',
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 8,
  },

  warningText: {
    color: '#FFB74D',
    fontSize: 12,
    marginBottom: 4,
    lineHeight: 16,
  },
});
