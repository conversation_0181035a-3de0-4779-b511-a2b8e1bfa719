{"ast": null, "code": "import valueParser from 'postcss-value-parser';\nvar invalidShortforms = {\n  background: true,\n  borderBottom: true,\n  borderLeft: true,\n  borderRight: true,\n  borderTop: true,\n  font: true,\n  grid: true,\n  outline: true,\n  textDecoration: true\n};\nvar invalidMultiValueShortforms = {\n  flex: true,\n  margin: true,\n  padding: true,\n  borderColor: true,\n  borderRadius: true,\n  borderStyle: true,\n  borderWidth: true,\n  inset: true,\n  insetBlock: true,\n  insetInline: true,\n  marginBlock: true,\n  marginInline: true,\n  marginHorizontal: true,\n  marginVertical: true,\n  paddingBlock: true,\n  paddingInline: true,\n  paddingHorizontal: true,\n  paddingVertical: true,\n  overflow: true,\n  overscrollBehavior: true,\n  backgroundPosition: true\n};\nfunction error(message) {\n  console.error(message);\n}\nexport function validate(obj) {\n  for (var k in obj) {\n    var prop = k.trim();\n    var value = obj[prop];\n    var isInvalid = false;\n    if (value === null) {\n      continue;\n    }\n    if (typeof value === 'string' && value.indexOf('!important') > -1) {\n      error(\"Invalid style declaration \\\"\" + prop + \":\" + value + \"\\\". Values cannot include \\\"!important\\\"\");\n      isInvalid = true;\n    } else {\n      var suggestion = '';\n      if (prop === 'animation' || prop === 'animationName') {\n        suggestion = 'Did you mean \"animationKeyframes\"?';\n        isInvalid = true;\n      } else if (prop === 'direction') {\n        suggestion = 'Did you mean \"writingDirection\"?';\n        isInvalid = true;\n      } else if (invalidShortforms[prop]) {\n        suggestion = 'Please use long-form properties.';\n        isInvalid = true;\n      } else if (invalidMultiValueShortforms[prop]) {\n        if (typeof value === 'string' && valueParser(value).nodes.length > 1) {\n          suggestion = \"Value is \\\"\" + value + \"\\\" but only single values are supported.\";\n          isInvalid = true;\n        }\n      }\n      if (suggestion !== '') {\n        error(\"Invalid style property of \\\"\" + prop + \"\\\". \" + suggestion);\n      }\n    }\n    if (isInvalid) {\n      delete obj[k];\n    }\n  }\n}", "map": {"version": 3, "names": ["valueParser", "invalidShortforms", "background", "borderBottom", "borderLeft", "borderRight", "borderTop", "font", "grid", "outline", "textDecoration", "invalidMultiValueShortforms", "flex", "margin", "padding", "borderColor", "borderRadius", "borderStyle", "borderWidth", "inset", "insetBlock", "insetInline", "marginBlock", "marginInline", "marginHorizontal", "marginVertical", "paddingBlock", "paddingInline", "paddingHorizontal", "paddingVertical", "overflow", "overscroll<PERSON><PERSON><PERSON><PERSON>", "backgroundPosition", "error", "message", "console", "validate", "obj", "k", "prop", "trim", "value", "isInvalid", "indexOf", "suggestion", "nodes", "length"], "sources": ["E:/CryptoSignalsApp/node_modules/react-native-web/dist/exports/StyleSheet/validate.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport valueParser from 'postcss-value-parser';\nvar invalidShortforms = {\n  background: true,\n  borderBottom: true,\n  borderLeft: true,\n  borderRight: true,\n  borderTop: true,\n  font: true,\n  grid: true,\n  outline: true,\n  textDecoration: true\n};\nvar invalidMultiValueShortforms = {\n  flex: true,\n  margin: true,\n  padding: true,\n  borderColor: true,\n  borderRadius: true,\n  borderStyle: true,\n  borderWidth: true,\n  inset: true,\n  insetBlock: true,\n  insetInline: true,\n  marginBlock: true,\n  marginInline: true,\n  marginHorizontal: true,\n  marginVertical: true,\n  paddingBlock: true,\n  paddingInline: true,\n  paddingHorizontal: true,\n  paddingVertical: true,\n  overflow: true,\n  overscrollBehavior: true,\n  backgroundPosition: true\n};\nfunction error(message) {\n  console.error(message);\n}\nexport function validate(obj) {\n  for (var k in obj) {\n    var prop = k.trim();\n    var value = obj[prop];\n    var isInvalid = false;\n    if (value === null) {\n      continue;\n    }\n    if (typeof value === 'string' && value.indexOf('!important') > -1) {\n      error(\"Invalid style declaration \\\"\" + prop + \":\" + value + \"\\\". Values cannot include \\\"!important\\\"\");\n      isInvalid = true;\n    } else {\n      var suggestion = '';\n      if (prop === 'animation' || prop === 'animationName') {\n        suggestion = 'Did you mean \"animationKeyframes\"?';\n        isInvalid = true;\n      } else if (prop === 'direction') {\n        suggestion = 'Did you mean \"writingDirection\"?';\n        isInvalid = true;\n      } else if (invalidShortforms[prop]) {\n        suggestion = 'Please use long-form properties.';\n        isInvalid = true;\n      } else if (invalidMultiValueShortforms[prop]) {\n        if (typeof value === 'string' && valueParser(value).nodes.length > 1) {\n          suggestion = \"Value is \\\"\" + value + \"\\\" but only single values are supported.\";\n          isInvalid = true;\n        }\n      }\n      if (suggestion !== '') {\n        error(\"Invalid style property of \\\"\" + prop + \"\\\". \" + suggestion);\n      }\n    }\n    if (isInvalid) {\n      delete obj[k];\n    }\n  }\n}"], "mappings": "AASA,OAAOA,WAAW,MAAM,sBAAsB;AAC9C,IAAIC,iBAAiB,GAAG;EACtBC,UAAU,EAAE,IAAI;EAChBC,YAAY,EAAE,IAAI;EAClBC,UAAU,EAAE,IAAI;EAChBC,WAAW,EAAE,IAAI;EACjBC,SAAS,EAAE,IAAI;EACfC,IAAI,EAAE,IAAI;EACVC,IAAI,EAAE,IAAI;EACVC,OAAO,EAAE,IAAI;EACbC,cAAc,EAAE;AAClB,CAAC;AACD,IAAIC,2BAA2B,GAAG;EAChCC,IAAI,EAAE,IAAI;EACVC,MAAM,EAAE,IAAI;EACZC,OAAO,EAAE,IAAI;EACbC,WAAW,EAAE,IAAI;EACjBC,YAAY,EAAE,IAAI;EAClBC,WAAW,EAAE,IAAI;EACjBC,WAAW,EAAE,IAAI;EACjBC,KAAK,EAAE,IAAI;EACXC,UAAU,EAAE,IAAI;EAChBC,WAAW,EAAE,IAAI;EACjBC,WAAW,EAAE,IAAI;EACjBC,YAAY,EAAE,IAAI;EAClBC,gBAAgB,EAAE,IAAI;EACtBC,cAAc,EAAE,IAAI;EACpBC,YAAY,EAAE,IAAI;EAClBC,aAAa,EAAE,IAAI;EACnBC,iBAAiB,EAAE,IAAI;EACvBC,eAAe,EAAE,IAAI;EACrBC,QAAQ,EAAE,IAAI;EACdC,kBAAkB,EAAE,IAAI;EACxBC,kBAAkB,EAAE;AACtB,CAAC;AACD,SAASC,KAAKA,CAACC,OAAO,EAAE;EACtBC,OAAO,CAACF,KAAK,CAACC,OAAO,CAAC;AACxB;AACA,OAAO,SAASE,QAAQA,CAACC,GAAG,EAAE;EAC5B,KAAK,IAAIC,CAAC,IAAID,GAAG,EAAE;IACjB,IAAIE,IAAI,GAAGD,CAAC,CAACE,IAAI,CAAC,CAAC;IACnB,IAAIC,KAAK,GAAGJ,GAAG,CAACE,IAAI,CAAC;IACrB,IAAIG,SAAS,GAAG,KAAK;IACrB,IAAID,KAAK,KAAK,IAAI,EAAE;MAClB;IACF;IACA,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACE,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,EAAE;MACjEV,KAAK,CAAC,8BAA8B,GAAGM,IAAI,GAAG,GAAG,GAAGE,KAAK,GAAG,0CAA0C,CAAC;MACvGC,SAAS,GAAG,IAAI;IAClB,CAAC,MAAM;MACL,IAAIE,UAAU,GAAG,EAAE;MACnB,IAAIL,IAAI,KAAK,WAAW,IAAIA,IAAI,KAAK,eAAe,EAAE;QACpDK,UAAU,GAAG,oCAAoC;QACjDF,SAAS,GAAG,IAAI;MAClB,CAAC,MAAM,IAAIH,IAAI,KAAK,WAAW,EAAE;QAC/BK,UAAU,GAAG,kCAAkC;QAC/CF,SAAS,GAAG,IAAI;MAClB,CAAC,MAAM,IAAIzC,iBAAiB,CAACsC,IAAI,CAAC,EAAE;QAClCK,UAAU,GAAG,kCAAkC;QAC/CF,SAAS,GAAG,IAAI;MAClB,CAAC,MAAM,IAAI/B,2BAA2B,CAAC4B,IAAI,CAAC,EAAE;QAC5C,IAAI,OAAOE,KAAK,KAAK,QAAQ,IAAIzC,WAAW,CAACyC,KAAK,CAAC,CAACI,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;UACpEF,UAAU,GAAG,aAAa,GAAGH,KAAK,GAAG,0CAA0C;UAC/EC,SAAS,GAAG,IAAI;QAClB;MACF;MACA,IAAIE,UAAU,KAAK,EAAE,EAAE;QACrBX,KAAK,CAAC,8BAA8B,GAAGM,IAAI,GAAG,MAAM,GAAGK,UAAU,CAAC;MACpE;IACF;IACA,IAAIF,SAAS,EAAE;MACb,OAAOL,GAAG,CAACC,CAAC,CAAC;IACf;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}